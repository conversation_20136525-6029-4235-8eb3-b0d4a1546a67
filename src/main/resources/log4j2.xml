<?xml version="1.0" encoding="UTF-8"?>
<configuration status="TRACE">
    <!--<configuration static="OFF">-->
    <properties>
        <property name="LEVEL">DEBUG</property>
        <property name="APP_NAME">ows-sas</property>
        <property name="LOG_HOME">logs/${APP_NAME}</property>
        <property name="isThreadContextMapInheritable">true</property>
    </properties>

    <appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="[%d{ABSOLUTE}][%-5p][%-25c][%t] [%X{tracker_id}] [%X{span_id}] %m%n"/>
        </Console>

        <!-- 配置日志输出文件名字     追加读写 -->
        <RollingFile name="RollingFile" fileName="${LOG_HOME}/${APP_NAME}-${LEVEL}.log"
                     filePattern="${LOG_HOME}/${APP_NAME}-${LEVEL}-%d{yyyy-MM-dd}.log" append="true">
            <!-- 输出格式 -->
            <PatternLayout pattern="[%d{ABSOLUTE}][%-5p][%-25c][%t] [%X{tracker_id}] [%X{span_id}] %m%n"/>
            <!-- 设置策略 -->
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingFile>

    </appenders>
    <loggers>

        <!--过滤掉spring一些无用的DEBUG信息-->
        <logger name="org.springframework" level="ERROR"/>
        <logger name="org.apache" level="ERROR"/>
        <logger name="com.netflix" level="ERROR"/>
        <logger name="org.hibernate" level="ERROR"/>
        <logger name="org.mybatis" level="ERROR"/>
        <logger name="springfox.documentation" level="ERROR"/>
        <logger name="tk" level="ERROR"/>
        <logger name="io.lettuce" level="ERROR"/>
        <logger name="jdbc.sqlonly" level="ERROR"/>
        <logger name="jdbc.sqltiming" level="ERROR"/>
        <logger name="jdbc.resultsettable" level="ERROR"/>
        <logger name="jdbc.resultset" level="ERROR"/>
        <logger name="jdbc.connection" level="OFF"/>
        <logger name="jdbc.audit" level="OFF"/>
        <logger name="io.netty" level="ERROR" />
        <logger name="com.zaxxer" level="ERROR" />
        <logger name="log4jdbc.log4j2" level="ERROR" />

        <!-- 为sql跟踪配置一个logger -->
        <!--<logger name="log4jdbc.log4j2" level="INFO" additivity="false">
            <MarkerFilter marker="LOG4JDBC_OTHER" onMatch="DENY" onMismatch="NEUTRAL"/>
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFile"/>
        </logger>-->
        <AsyncLogger name="jdbc.sqlonly" level="ERROR" additivity="false">
            <appender-ref ref="Console"/>
            <AppenderRef ref="RollingFile"/>
        </AsyncLogger>
        <AsyncRoot level="DEBUG">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
        </AsyncRoot>
    </loggers>
</configuration>