<?xml version="1.0" encoding="UTF-8"?>
<!--<configuration status="TRACE">-->
    <configuration static="OFF">
    <properties>
        <property name="LEVEL">DEBUG</property>
        <property name="APP_NAME">ows-sas</property>
        <property name="LOG_HOME">ows-sas/logs</property>
        <property name="isThreadContextMapInheritable">true</property>
    </properties>

    <appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="[%d{ABSOLUTE}][%-5p][%-25c][%t] [%X{tracker_id}] [%X{span_id}] %m%n"/>
        </Console>

        <!-- 配置日志输出文件名字     追加读写 -->
        <!--<RollingFile name="RollingFile" fileName="${LOG_HOME}/${APP_NAME}-${LEVEL}.log"-->
        <!--filePattern="${LOG_HOME}/${APP_NAME}-${LEVEL}-%d{yyyy-MM-dd}.log" append="true">-->
        <!--&lt;!&ndash; 输出格式 &ndash;&gt;-->
        <!--<PatternLayout pattern="[%d{ABSOLUTE}][%-5p][%-25c][%t] [%X{tracker_id}] [%X{span_id}] %m%n"/>-->
        <!--&lt;!&ndash; 设置策略 &ndash;&gt;-->
        <!--<Policies>-->
        <!--&lt;!&ndash; 基于时间的触发策略。该策略主要是完成周期性的log文件封存工作。有两个参数：-->
        <!--interval，integer型，指定两次封存动作之间的时间间隔。单位:以日志的命名精度来确定单位，-->
        <!--比如yyyy-MM-dd-HH 单位为小时，yyyy-MM-dd-HH-mm 单位为分钟-->
        <!--modulate，boolean型，说明是否对封存时间进行调制。若modulate=true，-->
        <!--则封存时间将以0点为边界进行偏移计算。比如，modulate=true，interval=4hours，-->
        <!--那么假设上次封存日志的时间为03:00，则下次封存日志的时间为04:00，-->
        <!--之后的封存时间依次为08:00，12:00，16:00-->
        <!--&ndash;&gt;-->
        <!--<TimeBasedTriggeringPolicy interval="1" modulate="true"/>-->
        <!--</Policies>-->
        <!--</RollingFile>-->

    </appenders>
    <loggers>
        <!--
            这里一定要用 AsyncLogger，不然对主线程影响巨大
            异步模式可能会造成日志顺序有问题，因此最好在业务log中带上日期或者相关的taskId

        <AsyncLogger name="com.aidangqun.log4j2cm.aop.HttpLogAspect" level="INFO" includeLocation="true">
            <AppenderRef ref="ElasticsearchTimeAppender"/>
        </AsyncLogger>

        <AsyncLogger name="com.goodsogood.ows" level="DEBUG" includeLocation="true">
            <AppenderRef ref="OWSElasticsearchTimeAppender"/>
        </AsyncLogger>
-->
        <!--过滤掉spring一些无用的DEBUG信息-->
        <logger name="org.springframework" level="WARN"/>
        <logger name="org.apache" level="ERROR"/>
        <logger name="com.netflix" level="ERROR"/>
        <logger name="org.hibernate" level="ERROR"/>
        <logger name="org.mybatis" level="ERROR"/>
        <logger name="springfox.documentation" level="ERROR"/>
        <logger name="tk" level="ERROR"/>
        <logger name="io.lettuce" level="ERROR"/>
        <logger name="jdbc.sqlonly" level="ERROR"/>
        <logger name="jdbc.sqltiming" level="ERROR"/>
        <logger name="jdbc.resultsettable" level="ERROR"/>
        <logger name="jdbc.resultset" level="ERROR"/>
        <logger name="jdbc.connection" level="OFF"/>
        <logger name="jdbc.audit" level="OFF"/>
        <logger name="io.netty" level="ERROR" />
        <logger name="com.zaxxer" level="ERROR" />
        <logger name="log4jdbc.log4j2" level="DEBUG" />

        <logger name="com.goodsogood.ows" level="DEBUG"/>

        <!-- 为sql跟踪配置一个logger -->
        <AsyncLogger name="jdbc.sqlonly" level="DEBUG" additivity="false">
            <appender-ref ref="Console"/>
        </AsyncLogger>
        <AsyncRoot level="ERROR">
            <AppenderRef ref="Console"/>
        </AsyncRoot>
    </loggers>
</configuration>