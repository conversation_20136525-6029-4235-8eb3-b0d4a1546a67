package com.goodsogood.ows.queue.eval.v2

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.aidangqun.log4j2cm.aop.HttpLogAspect
import com.aidangqun.log4j2cm.helper.LogAspectHelper
import com.goodsogood.ows.common.Constants
import com.goodsogood.ows.common.redisUtil.RedisLockUtil
import com.goodsogood.ows.configuration.EvalV2Config
import com.goodsogood.ows.helper.LogHelper
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.db.eval.v2.OrgScoreEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.model.vo.eval.v2.MetricTaskBean
import com.goodsogood.ows.model.vo.eval.v2.TaskInfo
import com.goodsogood.ows.service.eval.EvalOrgScoreService
import com.goodsogood.ows.service.eval.v2.Strategy
import com.rabbitmq.client.Channel
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.amqp.core.Message
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.amqp.support.AmqpHeaders
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.messaging.handler.annotation.Header
import org.springframework.stereotype.Component
import java.time.Instant
import java.time.ZoneId

/**
 * <AUTHOR>
 * @date 2023/11/27
 * @description class Consumer 考核2.0 消费者
 */
@Component
class Consumer(
    val evalV2Config: EvalV2Config,
    val objectMapper: ObjectMapper,
    val redisTemplate: StringRedisTemplate,
    val evalOrgScoreService: EvalOrgScoreService,
) {
    private val log: Logger = LoggerFactory.getLogger(Consumer::class.java)

    /**
     * 消费方法
     */
    @RabbitListener(
        queues = ["#{evalV2Config.queue}"],
        concurrency = "#{evalV2Config.concurrency}",
        autoStartup = "#{evalV2Config.run}"
    )
    fun consume(@Header(AmqpHeaders.CHANNEL) channel: Channel, message: Message) {
        log.debug("考核2.0消费消息：{}", String(message.body, Charsets.UTF_8))
        val taskBean =
            try {
                objectMapper.readValue(message.body, MetricTaskBean::class.java)
            } catch (e: Exception) {
                log.error("考核2.0消费消息：反序列化发生错误:" + e.localizedMessage, e)
                return
            }
        var hasLock = false
        try {
            // 1. 通过taskId 和subTaskId在Redis中获取对应的任务
            val taskStr = redisTemplate.opsForHash<String, String>().get(
                "${Constants.METRIC_TASK_CACHE_KEY}${taskBean.taskId}",
                taskBean.subTaskId
                    ?: log.error("考核2.0消费消息：taskInfo->${Constants.METRIC_TASK_CACHE_KEY}${taskBean.taskId}获取失败")
            )
            val taskInfo = objectMapper.readValue(taskStr, TaskInfo::class.java)
            // 获取锁
            hasLock = RedisLockUtil.tryGetDistributedLock(
                redisTemplate,
                "${Constants.METRIC_TASK_CACHE_KEY}LOCK_${taskBean.taskId}_${taskBean.subTaskId}",
                "${taskInfo.metricId}_${taskInfo.org?.organizationId}_${taskInfo.unit?.organizationId}",
                3600000
            )
            if (hasLock) {
                // 2. 设置任务状态为进行中
                setTaskStatus(taskBean.taskId ?: "-1", taskBean.subTaskId ?: "-1", MetricTaskBean.TaskStatus.InProgress)
                // 3. 生成策略
                val metric = taskInfo.metric ?: throw Exception("考核2.0消费消息：taskInfo->metric获取失败")
                val year = taskInfo.year ?: throw Exception("考核2.0消费消息：taskInfo->year获取失败")
                val strategy = try {
                    Strategy.createStrategy(metric.clzName ?: "metric_${year}_${metric.metricClassId}_${metric.id}")
                } catch (e: Exception) {
                    log.error("考核2.0消费消息：策略生成失败:" + e.localizedMessage, e)
                    Strategy.createStrategy("defaultStrategy")
                }
                val map = taskInfo.params
                // 4. 计算指标
                strategy?.setTrackId(taskInfo.trackerId ?: taskBean.taskId ?: "")
                val score = strategy?.compute(
                    taskInfo.org ?: throw Exception("考核2.0消费消息：taskInfo->org获取失败"),
                    taskInfo.unit ?: throw Exception("考核2.0消费消息：taskInfo->unit获取失败"),
                    metric,
                    year,
                    taskInfo.trackerId ?: taskBean.taskId ?: "",
                    map ?: mapOf()
                ) ?: 0.0
                // 5. 写入数据库组织得分数据库
                // 设置创建时间，时区为+8时区

                val createTime =
                    Instant.ofEpochMilli(taskInfo.time ?: 0L).atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime()
                evalOrgScoreService.insertSelective(
                    OrgScoreEntity(
                        taskId = taskBean.taskId,
                        year = year,
                        metricClassId = metric.metricClassId,
                        metricId = metric.id,
                        orgId = taskInfo.org!!.organizationId,
                        orgName = taskInfo.org.name,
                        orgLevel = taskInfo.org.orgLevel,
                        unitId = taskInfo.unit!!.organizationId,
                        unitName = taskInfo.unit.name,
                        score = if (metric.scoreType == 2) {
                            score
                        } else {
                            -1.0 * score
                        },
                        createTime = createTime
                    )
                )
                // 6. 设置任务状态为完成
                setTaskStatus(taskBean.taskId ?: "-1", taskBean.subTaskId ?: "-1", MetricTaskBean.TaskStatus.Completed)
            } else {
                log.warn(
                    "考核2.0消费消息：获取锁失败，可能已经有任务在执行了：taskId:{} / subTaskId:{} / metricId:{} / orgId:{}",
                    taskBean.taskId,
                    taskBean.subTaskId,
                    taskInfo.metricId,
                    taskInfo.org?.organizationId
                )
                return
            }
        } catch (e: Exception) {
            // 修改Redis任务状态为取消
            setTaskStatus(taskBean.taskId ?: "-1", taskBean.subTaskId ?: "-1", MetricTaskBean.TaskStatus.Canceled)
            log.error("考核2.0消费消息发生错误:" + e.localizedMessage, e)
        } finally {
            if (hasLock) {
                //手动ACK
                channel.basicAck(message.messageProperties.deliveryTag, false)
            }
        }
    }

    private fun setTaskStatus(taskId: String, subTaskId: String, status: MetricTaskBean.TaskStatus) {
        redisTemplate.opsForHash<String, String>().put(
            "${Constants.METRIC_TASK_STATUS_CACHE_KEY}$taskId",
            subTaskId,
            status.value.toString()
        )
    }
}