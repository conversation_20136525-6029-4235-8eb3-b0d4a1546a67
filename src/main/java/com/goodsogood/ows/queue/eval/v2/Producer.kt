package com.goodsogood.ows.queue.eval.v2

import com.fasterxml.jackson.databind.ObjectMapper
import com.goodsogood.ows.configuration.EvalV2Config
import com.goodsogood.ows.model.vo.eval.v2.MetricTaskBean
import org.slf4j.Logger
import org.springframework.amqp.rabbit.core.RabbitTemplate
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @date 2023/11/27
 * @description class Producer 烟草考核2.0的生产者
 */
@Component
class Producer(
    val evalV2Config: EvalV2Config,
    val rabbitTemplate: RabbitTemplate,
    val objectMapper: ObjectMapper,
) {
    val log: Logger = org.slf4j.LoggerFactory.getLogger(Producer::class.java)

    /**
     * 发送消息
     */
    fun send(message: MetricTaskBean) {
        log.debug("考核2.0发送消息：{}", message)
        rabbitTemplate.convertAndSend(evalV2Config.queue, objectMapper.writeValueAsString(message))
    }
}