package com.goodsogood.ows.queue.dss

import com.aidangqun.log4j2cm.helper.LogAspectHelper
import com.goodsogood.ows.model.mongodb.PartyBranchInfo
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo
import com.goodsogood.ows.model.mongodb.UserInfo
import com.goodsogood.ows.service.dss.DssAsyncBatchService
import com.goodsogood.ows.service.dss.DssAsyncService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.amqp.rabbit.annotation.RabbitHandler
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.amqp.rabbit.core.RabbitTemplate
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.util.stream.Collectors

@Component
class DssConsumer(@Autowired val rabbitTemplate: RabbitTemplate,
                  @Autowired val dssAsyncService: DssAsyncService,
                  @Autowired val dssAsyncBatchService: DssAsyncBatchService
) {

    var log: Logger = LoggerFactory.getLogger(DssConsumer::class.java)

    @Value("\${rabbit-value.dss-user.queue}")
    var userQueue = "dss.queue.user"

    @Value("\${rabbit-value.dss-branch.queue}")
    var branchQueue = "dss.queue.branch"

    @Value("\${rabbit-value.dss-committee.queue}")
    var committeeQueue = "dss.queue.committee"

    @Value("\${rabbit-value.dss-retry.queue}")
    var retryQueue = "dss.queue.retry"

    @RabbitHandler
    @RabbitListener(queues = ["\${rabbit-value.dss-committee.queue}"], concurrency = "10", autoStartup = "\${rabbit-value.dss-committee.run}")
    fun committeeConsumer(infoList: List<PartyCommitteeInfo>) {
        try {
            // 重新设置上下文
            val helper = LogAspectHelper.logAspectHelperBuilder()
            helper.reSetContext(infoList[0].ssLog)
            log.debug("党委消费者接受到消息: -> ${infoList.stream().map(PartyCommitteeInfo::getOrganizationId).collect(
                Collectors.toList()
            )}")
            this.dssAsyncBatchService.saveBatchCommitteeInfo(infoList[0].year, infoList, infoList[0].ssLog.trackerId)
            log.debug("党委消费者成功消费消息: -> ${infoList.stream().map(PartyCommitteeInfo::getOrganizationId).collect(
                Collectors.toList()
            )}")
        } catch (e: Exception) {
            log.error("党委消费消息发生错误...", e)
            this.rabbitTemplate.convertAndSend(retryQueue, infoList)
        }
    }

    @RabbitHandler
    @RabbitListener(queues = ["\${rabbit-value.dss-branch.queue}"], concurrency = "10", autoStartup = "\${rabbit-value.dss-branch.run}")
    fun branchConsumer(infoList: List<PartyBranchInfo>) {
        try {
            // 重新设置上下文
            val helper = LogAspectHelper.logAspectHelperBuilder()
            helper.reSetContext(infoList[0].ssLog)
            log.debug("党支部消费者接收到消息: -> ${infoList.stream().map(PartyBranchInfo::getOrganizationId).collect(
                Collectors.toList()
            )}")
            this.dssAsyncBatchService.saveBatchBranchInfo(infoList[0].year, infoList[0].regionId, infoList, infoList[0].ssLog.trackerId)
            log.debug("党支部消费者成功消费消息: -> ${infoList.stream().map(PartyBranchInfo::getOrganizationId).collect(
                Collectors.toList()
            )}")
        } catch (e: Exception) {
            log.error("党支部消费消息发生错误...", e)
            this.rabbitTemplate.convertAndSend(retryQueue, infoList)
        }
    }

    @RabbitHandler
    @RabbitListener(queues = ["\${rabbit-value.dss-user.queue}"], concurrency = "10", autoStartup = "\${rabbit-value.dss-user.run}")
    fun userConsumer(infoList: List<UserInfo>) {
        try {
            // 重新设置上下文
            val helper = LogAspectHelper.logAspectHelperBuilder()
            helper.reSetContext(infoList[0].ssLog)
            log.debug("人员消费者接收到消息: -> ${infoList.stream().map(UserInfo::getUserId).collect(Collectors.toList())}")
            this.dssAsyncBatchService.saveBatchUserInfo(infoList, infoList[0].year, infoList[0].ssLog.trackerId)
            log.debug("人员消费者成功消费消息: -> ${infoList.stream().map(UserInfo::getUserId).collect(Collectors.toList())}")
        } catch (e: Exception) {
            log.error("消费消息发生错误...", e)
            this.rabbitTemplate.convertAndSend(retryQueue, infoList)
        }
    }

    @RabbitHandler
    @RabbitListener(queues = ["\${rabbit-value.dss-retry.queue}"], concurrency = "20", autoStartup = "\${rabbit-value.dss-retry.run}")
    fun retryConsumer(infoList: List<Any?>) {
        log.debug("重试消费者接收到消息: -> {}", infoList)
        // 重新设置上下文
        val helper = LogAspectHelper.logAspectHelperBuilder()
        infoList.forEach{
            when (it) {
                is UserInfo -> {
                    helper.reSetContext(it.ssLog)
                    this.dssAsyncService.saveUserInfo(it, it.ssLog.trackerId)
                }
                is PartyBranchInfo -> {
                    helper.reSetContext(it.ssLog)
                    this.dssAsyncService.saveBranchInfo(it, it.ssLog.trackerId)
                }
                is PartyCommitteeInfo -> {
                    helper.reSetContext(it.ssLog)
                    this.dssAsyncService.saveCommitteeInfo(it, it.ssLog.trackerId)
                }
                else -> log.error("传入的参数有问题! -> [$it]")
            }
        }
        log.debug("消费成功!")
    }

    @RabbitHandler
    @RabbitListener(queues = ["\${rabbit-value.dss-dead.queue}"], autoStartup = "\${rabbit-value.dss-dead.run}")
    fun deadConsumer(infoList: List<Any?>) {
        log.debug("死信消费者接收到消息: -> {}", infoList)
        infoList.forEach{
            when (it) {
                is UserInfo -> this.dssAsyncService.addErrorList(it.userId, 3)
                is PartyBranchInfo -> this.dssAsyncService.addErrorList(it.organizationId, 2)
                is PartyCommitteeInfo -> this.dssAsyncService.addErrorList(it.organizationId, 1)
                else -> log.error("传入的参数有问题! -> [$it]")
            }
        }
        log.debug("消费成功!")
    }
}