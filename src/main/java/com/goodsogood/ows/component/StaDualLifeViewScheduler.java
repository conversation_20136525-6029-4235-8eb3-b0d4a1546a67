package com.goodsogood.ows.component;

import com.aidangqun.log4j2cm.aop.HttpLogAspect;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.helper.LogHelper;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.service.RegionService;
import com.goodsogood.ows.service.sas.StatisticalUserOrgLifeService;
import com.goodsogood.ows.service.supervise.SuperviseService;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @program: ows-sas
 * @description: 用户双重组织生活统计定时任务
 * @author: chenanshun
 * @create: 2019-04-22 20:00
 **/
@Component
@Log4j2
public class StaDualLifeViewScheduler {

    /**
     * 是否开启定时任务
     */
    @Value("${scheduler.dual-life.run}")
    private boolean runFlag = false;

    private final LogHelper logHelper;
    private final StatisticalUserOrgLifeService statisticalUserOrgLifeService;
    private final RegionService regionService;
    private final OrgService orgService;
    private final SuperviseConfig superviseConfig;
    private final OrgTypeConfig orgTypeConfig;
    private final SuperviseService superviseService;

    @Autowired
    public StaDualLifeViewScheduler(
            LogHelper logHelper,
            StatisticalUserOrgLifeService statisticalUserOrgLifeService,
            RegionService regionService,
            OrgService orgService, SuperviseConfig superviseConfig,
            OrgTypeConfig orgTypeConfig, SuperviseService superviseService) {
        this.logHelper = logHelper;
        this.statisticalUserOrgLifeService = statisticalUserOrgLifeService;
        this.regionService = regionService;
        this.orgService = orgService;
        this.superviseConfig = superviseConfig;
        this.orgTypeConfig = orgTypeConfig;
        this.superviseService = superviseService;
    }

    /**
     * 每天1点组织生活一览表统计
     * 用户双重组织生活统计
     * 0 0 1 * * ?
     */
    @Scheduled(cron = "${scheduler.dual-life.cron}")
    public void sasOrgLifeViewCount() {
        logHelper.reSetContext(null);
        log.debug("sas-scheduler -> {}", runFlag);
        if (!runFlag) {
            log.debug("【SAS】定时器关闭!");
            return;
        }
        log.debug("【SAS】用户双重组织生活统计开始!");
        regionService.getRegions().forEach(regionData -> {
            statisticalUserOrgLifeService.sasUserOrgLifeCount(regionData.getRegionId(), DateTime.now().toDate());
        });
        log.debug("【SAS】用户双重组织生活统计结束!");
    }

    /**
     * 每天1点组织生活一览表统计
     * 用户双重组织生活统计
     * 0 0 1 * * ?
     */
    @Scheduled(cron = "${scheduler.supervise.pull-data}")
    public void sasSupervise() {
        logHelper.reSetContext(null);
        log.debug("sas-supervise -> {}", runFlag);
        if (!runFlag) {
            log.debug("【supervise】定时器关闭!");
            return;
        }
        //先保存各个支部统计数据
        superviseService.saveSuperviseHistory();
        //先删除之前的数据
        superviseService.delSuperviseData();
        log.debug("【supervise】拉取监督预警数据开始!");
        //先摘取支部或者党小组的数据
        List<OrganizationEntity> organizationEntities = orgService.selectAllChildOrgBYOrgAndType(superviseConfig.getRegionId()
                , superviseConfig.getTopOrgId(), orgTypeConfig.getBranch());
        CountDownLatch latch = new CountDownLatch(organizationEntities.size());
        organizationEntities.forEach(item -> superviseService.pullSupervise(item, superviseConfig.getBranch(), latch,HttpLogAspect.getSSLog()));
        // 等待所有线程与完毕
        try {
            log.debug("等待前面执行完毕!");
            latch.await();
            log.debug("拉取党支部执行完成!");
            //拉取党委
            List<OrganizationEntity> organizationEntitiesCommittee = orgService.selectAllChildOrgBYOrgAndType(
                    superviseConfig.getRegionId(), superviseConfig.getTopOrgId(), orgTypeConfig.getCommittee());
            //处理顶级组织是不是包含在内
            orgService.handlerTopOrgInfo(organizationEntitiesCommittee,superviseConfig.getRegionId());
            CountDownLatch latchCommittee = new CountDownLatch(organizationEntitiesCommittee.size());
            organizationEntitiesCommittee.forEach(item -> superviseService.
                    pullSupervise(item, superviseConfig.getCommittee(), latchCommittee, HttpLogAspect.getSSLog()));
            latchCommittee.await();
            log.debug("拉取党委执行执行完成!");
        } catch (Exception e) {
            log.error("程序出现异常: ", e);
        }
    }

}
