package com.goodsogood.ows.component;

import com.goodsogood.ows.common.redisUtil.RedisLockUtil;
import com.goodsogood.ows.configuration.ScoreManagerConfig;
import com.goodsogood.ows.service.score.ScoreAddService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 积分上报到积分中心任务
 */
@Component
@Log4j2
public class ScoreAddScheduler {

    @Value("${scheduler.score-add.run}")
    private boolean runStatus;

    private final ScoreAddService scoreAddService;
    private final ScoreManagerConfig scoreManagerConfig;
    private final StringRedisTemplate stringRedisTemplate;
    private final Errors errors;

    private static final String lock = "ScoreAddScheduler-lock";

    @Autowired
    public ScoreAddScheduler(
                             ScoreManagerConfig scoreManagerConfig,
                             StringRedisTemplate stringRedisTemplate, Errors errors, ScoreAddService scoreAddService) {
        this.scoreAddService = scoreAddService;
        this.scoreManagerConfig = scoreManagerConfig;
        this.stringRedisTemplate = stringRedisTemplate;
        this.errors = errors;
    }

    @Scheduled(cron = "${scheduler.score-add.cron}")
    public void execute() {
        if (!runStatus) {
            log.debug("ScoreAddScheduler->定时任务关闭,结束执行! ");
            return;
        }
        String uuid = UUID.randomUUID().toString();
        try {
            boolean b = RedisLockUtil.tryGetDistributedLock(stringRedisTemplate, lock, uuid, 60 * 1000);
            if (b) {
                //执行任务
                Map<Long, List<Integer>> regions = scoreManagerConfig.getRegions();
                if (CollectionUtils.isEmpty(regions)) {
                    log.debug("ScoreManagerScheduler->未配置区县参数,结束执行!");
                }
                regions.keySet().forEach(scoreAddService::addScoreByScheduler);
            }else {
                log.debug("ScoreAddScheduler->未获取到锁,结束执行!");
            }
        } catch (Exception e) {
            log.error("ScoreAddScheduler->出现异常:[{}]", e.getMessage(), e);
        } finally {
            RedisLockUtil.releaseDistributedLock(stringRedisTemplate, lock, uuid);
        }
    }
}
