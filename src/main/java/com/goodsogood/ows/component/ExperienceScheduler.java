package com.goodsogood.ows.component;

import com.goodsogood.ows.configuration.ExperienceConfig;
import com.goodsogood.ows.service.experience.UserPartyEvalService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

/**
 * 党性体验定时任务
 */
@Component
@Log4j2
public class ExperienceScheduler {

    private final ExperienceConfig experienceConfig;
    private final UserPartyEvalService userPartyEvalService;

    /**  是否开启定时任务 */
    @Value("${scheduler.leader-org-meeting.run}")
    private boolean runStatus;

    public ExperienceScheduler(ExperienceConfig experienceConfig,
                              UserPartyEvalService userPartyEvalService) {
        this.experienceConfig = experienceConfig;
        this.userPartyEvalService = userPartyEvalService;
    }


    /**
     * 每个月的28号1点开始执行
     */
    @Scheduled(cron = "${scheduler.experience-data.create-data}")
    @Transactional(rollbackFor = Exception.class)
    public void createData() {
        // 判断定时任务是否开启
        if(!runStatus) {
            log.info("党性体验-生成数据开始执行定时器关闭!");
            return;
        }
        String batchNumber= UUID.randomUUID().toString().replace("-", "");
        userPartyEvalService.autoExperience(experienceConfig.getRegionId(),null,(byte) 0,batchNumber);
        log.info("党性体验生成数据开始执行结束!");
    }


    /**
     * 每个月的28号11点开始执行
     */
    @Scheduled(cron = "${scheduler.experience-data.calc-star}")
    @Transactional(rollbackFor = Exception.class)
    public void calcStar() {
        // 判断定时任务是否开启
        if(!runStatus) {
            log.info("党性体验-开始计算星级定时器关闭!");
            return;
        }
        userPartyEvalService.calStar(1);
        log.info("党性体验-开始计算星级结束!");
    }


    /**
     * 每个月的倒数第一天，中午12点开始执行
     */
    @Transactional(rollbackFor = Exception.class)
    @Scheduled(cron = "${scheduler.experience-data.calc-click-star}")
    public void calcStarEveryOneMinute() {
        // 判断定时任务是否开启
        if(!runStatus) {
            log.info("每分钟党性体验-开始计算星级定时器关闭!");
            return;
        }
        log.info("每分钟党性体验-开始计算星级定时器开始!");
        userPartyEvalService.calStar(2);
        log.info("每分钟党性体验-开始计算星级结束!");
    }

}
