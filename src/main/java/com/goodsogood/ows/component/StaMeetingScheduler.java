package com.goodsogood.ows.component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.mapper.sas.StatisticalDataRecordMapper;
import com.goodsogood.ows.mapper.sas.StatisticalOrgLifeMapper;
import com.goodsogood.ows.model.db.sas.StatisticalDataRecordEntity;
import com.goodsogood.ows.model.db.sas.StatisticalOrgLifeEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.activity.StaMeetingType;
import com.goodsogood.ows.model.vo.sas.*;
import com.goodsogood.ows.service.meeting.StaMeetingService;
import com.goodsogood.ows.service.sas.StatisticalLeaderOrgLifeService;
import com.goodsogood.ows.service.sas.StatisticalOrgLifeService;
import com.goodsogood.ows.service.sas.StatisticalStorageVariableService;
import com.goodsogood.ows.service.sas.StatisticalTempActivityService;
import com.goodsogood.ows.service.user.OrgPeriodService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: ows-sassta_code
 * @description: 统计组织生活定时任务
 * @author: Mr.LiGuoYong
 * @create: 2019-04-22 20:00
 **/
@Component
@Log4j2
public class StaMeetingScheduler {

    /**  是否开启定时任务 */
    @Value("${scheduler.meeting.run}")
    private boolean runFlag = false;

    private final OrgService orgService;
    private final OrgPeriodService orgPeriodService;
    private final StaMeetingService meetingService;
    private final StatisticalOrgLifeMapper statisticalOrgLifeMapper;
    private final StatisticalDataRecordMapper statisticalDataRecordMapper;
    private final StatisticalOrgLifeService statisticalOrgLifeService;
    private final StatisticalLeaderOrgLifeService statisticalLeaderOrgLifeService;
    private final StatisticalStorageVariableService statisticalStorageVariableService;
    private final StatisticalTempActivityService statisticalTempActivityService;
    private final TogServicesConfig togServicesConfig;
    private final RestTemplate restTemplate;

//    @Value("${supplementOrgInfo.activity-ids}")
    private   String [] activityTypArray;

    @Value("${supplementOrgInfo.sta-start-month}")
    private  String staStartMonth;


    @Autowired
    public StaMeetingScheduler(OrgService orgService, OrgPeriodService orgPeriodService, StaMeetingService meetingService,
                               StatisticalOrgLifeMapper statisticalOrgLifeMapper,
                               StatisticalDataRecordMapper statisticalDataRecordMapper,
                               StatisticalOrgLifeService statisticalOrgLifeService,
                               StatisticalLeaderOrgLifeService statisticalLeaderOrgLifeService,
                               StatisticalStorageVariableService statisticalStorageVariableService,
                               StatisticalTempActivityService statisticalTempActivityService,
                               TogServicesConfig togServicesConfig, RestTemplate restTemplate) {

        this.orgService = orgService;
        this.orgPeriodService = orgPeriodService;
        this.meetingService = meetingService;
        this.statisticalOrgLifeMapper=statisticalOrgLifeMapper;
        this.statisticalDataRecordMapper=statisticalDataRecordMapper;
        this.statisticalOrgLifeService=statisticalOrgLifeService;
        this.statisticalLeaderOrgLifeService=statisticalLeaderOrgLifeService;
        this.statisticalStorageVariableService=statisticalStorageVariableService;
        this.statisticalTempActivityService=statisticalTempActivityService;
        this.togServicesConfig = togServicesConfig;
        this.restTemplate = restTemplate;
    }

    /**
     * 每天1点组织生活统计统半年的数据
     * 0 0 3 * * ?
     */
    @Scheduled(cron = "${scheduler.meeting.cron}")
    public void meetingSta() throws Exception {
        if( ! runFlag ) {
            return;
        }
        long l = System.currentTimeMillis();
        log.info("统计组织生活定时任务开始执行...");
        staMeeting(3L,false);
        log.info("统计组织生活定时任务执行完成... times: {}",(System.currentTimeMillis() - l));

        log.info("每天4点验证数据,刷新组织的支委会创建时间...");
        resetPeriodCreateTime(1L);
        log.info("每天4点验证数据,刷新组织的支委会创建时间,完成... times: {}",(System.currentTimeMillis() - l));

        //完成过后调用考核系统 让考核系统启动定时任务
        String url = String.format("http://%s/eval/init/pullData", togServicesConfig.getEval());
        HttpHeaders headers = new HttpHeaders();
        try {
           String result= RemoteApiHelper.get(restTemplate, url, headers,new TypeReference<Result<String>>(){});
           log.error("调用远程考核服务接口地址-{}, result-{}", url,result);
        } catch (Exception e) {
            log.error("调用远程考核服务接口地址-{}, 错误内容-{}", url,e.getMessage());
        }
    }

    /**
     * 每天23点重新录入组织的创建时间 补录组织信息(包括党小组创建时间,党小组数量,支委会数量)
     * 补录的为全量的信息
     * 0 0 23,2 * * ?
     * 这里23点执行一次(以防这个组织月底最后一天注册，这里进行补录）
     */
    @Scheduled(cron = "${scheduler.meeting.supply-cron}")
    public void supplementOrgInfoScheduler() throws Exception {
        if( ! runFlag ) {
            return;
        }
        //定时任务 只拉取当月数据 因为初始化的时候 已经拉取当时 所有组织的全量信息
        supplementOrgInfo(false);
    }

//    /**
//     * 每天3点验证数据,刷新组织的支委会创建时间
//     * 0 0 3 * * ?
//     */
//    @Scheduled(cron = "${scheduler.meeting.period-cron}")
//    public void resetPeriodCreateTime()  {
//        if( ! runFlag ) {
//            return;
//        }
//        //resetPeriodCreateTime(1L);
//    }


    /**
     * 重置组织的支委会创建时间
     */
    public void resetPeriodCreateTime(Long staTimeCode){
        List<StatisticalOrgLifeEntity> statisticalOrgLifeEntities = statisticalOrgLifeMapper.getPeriodInfo(statisticalLeaderOrgLifeService.getQueryTime(staTimeCode));
        statisticalOrgLifeEntities.forEach(item->{
            try {
                //调用王诺宇那边接口 返回信息
                PeriodForm periodForm = new PeriodForm();
                List<PeriodForm> list = new ArrayList<>();
                periodForm.setOrgId(item.getOrgId());
                periodForm.setPeriodQueryTime( item.getStatisticalDate());
                list.add(periodForm);
                //數據庫格式為2019-01
                List<PeriodFindOrgsResultForm> periodInfo = this.orgPeriodService.getPeriodInfo(list);
                if(!CollectionUtils.isEmpty(periodInfo)) {
                //只查询一条记录
                PeriodFindOrgsResultForm periodFindOrgsResultForm = periodInfo.get(0);
                statisticalOrgLifeMapper.updatePeriodInfoInfo(periodFindOrgsResultForm.getPeriodCreateTime(), item.getOrgLifeId(),periodFindOrgsResultForm.getHasPeriodTag());
            }
        } catch (Exception ex) {
            log.error("重置组织的支委会创建时间失败，ex-{}",ex);
        }
    });

}

    /**
     * 初始化完成过后 操作信息
     * 补录组织信息
     */
    public void supplementOrgInfo(boolean isInit){
        //获取所有组织信息
        List<OrganizationForm> list = this.orgService.findOrgByType(102803L, null, null);
        List<StaMeetingType> staMeetingType = statisticalOrgLifeMapper.getStaMeetingType();
        list.forEach(item->{
            initPartyNumber(item.getOrganizationId(),isInit);
        });
    }


    /**
     * 补录组织信息传入组织id
     */
    public void supplementOrgInfo(boolean isInit,Long orgId){
        log.info("补录orgId-{},开始。。。",orgId);
        //获取所有组织信息
        List<OrganizationForm> list = this.orgService.findOrgByType(null, null, null);
        list = list.stream().filter(item -> (item.getOrganizationId().equals(orgId))).
                collect(Collectors.toList());
        List<StaMeetingType> staMeetingType = statisticalOrgLifeMapper.getStaMeetingType();
        list.forEach(item->{
            //补录组织组织创建时间、支委会数量 这里不操作支委会的信息，resetPeriodCreateTime 这里单独处理
            statisticalOrgLifeService.supplementOrgInfo(item.getOrgCreateTime(),
                    item.getOrganizationId(),item.getPeriodSum(),item.getRegionId() );
            //每月统计党小组数量
            initPartyNumber(item.getOrganizationId(),isInit);
            //包装对象与填充数据
            StatisticalOrgLifeEntity statisticalOrgLifeEntity=setStatisticalOrgLifeEntity(item);
            //填充为0数据
            fillStaData(isInit,statisticalOrgLifeEntity,staMeetingType);
        });
        log.info("补录orgId-{},已经结束。。。",orgId);
    }


    /**
     * 验证是否在当月
     * @param time
     * @param pattern
     * @return
     */
    private static boolean isThisTime(long time,String pattern) {
        Date date = new Date(time);
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        String param = sdf.format(date);//参数时间
        String now = sdf.format(new Date());//当前时间
        return param.equals(now);
    }


    /**
     * 统计组织生活信息信息
     * @param staCode'
     * isSup 是不是 有补充其它数据
     */
    public void staMeeting(Long staCode,boolean isSup){
        //获取所有组织信息
        List<OrganizationForm> list = this.orgService.findOrgByType(102803L, null, null);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> {
                //1:查询5年以内 3:查询半年以内
                List<StatisticsMeetingForm> organizeStaInfo = this.meetingService.getOrganizeStaInfo(item.getOrganizationId(),
                        staCode, 0L, item.getRegionId());
                if (!CollectionUtils.isEmpty(organizeStaInfo)) {
                    for (int i = 0; i < organizeStaInfo.size(); i++) {
                        StatisticalOrgLifeEntity statisticalOrgLifeEntity =
                                setStatisticalOrgLifeEntity(item, organizeStaInfo.get(i));
                        if (null == statisticalOrgLifeEntity) {
                            continue;
                        } else {
                            valMeetingVariety(statisticalOrgLifeEntity, item,isSup);
                        }
                    }
                }
            });
        }
    }

    /**
     * 统计组织生活信息信息
     * @param staCode'
     * isSup 是不是 有补充其它数据
     */
    public void staMeeting(Long staCode,boolean isSup,Long orgId){
        //获取所有组织信息
        List<OrganizationForm> list = this.orgService.findOrgByType(102803L, null, null);
        list = list.stream().filter(item -> item.getOrganizationId().equals(orgId)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> {
                //1:查询5年以内 3:查询半年以内
                List<StatisticsMeetingForm> organizeStaInfo = this.meetingService.getOrganizeStaInfo(item       .getOrganizationId(), staCode, 0L, item.getRegionId());
                if (!CollectionUtils.isEmpty(organizeStaInfo)) {
                    for (int i = 0; i < organizeStaInfo.size(); i++) {
                        StatisticalOrgLifeEntity statisticalOrgLifeEntity =
                                setStatisticalOrgLifeEntity(item, organizeStaInfo.get(i));
                        if (null == statisticalOrgLifeEntity) {
                            continue;
                        } else {
                            valMeetingVariety(statisticalOrgLifeEntity, item,isSup);
                        }
                    }
                }
            });
        }
    }



    /**
     * 验证组织生活变动情况
     */
   @Async("valMeetingExecutor")
   public void  valMeetingVariety(StatisticalOrgLifeEntity statisticalOrgLifeEntity,
                                  OrganizationForm item,boolean isSup){
        try {
            StatisticalOrgLifeEntity  isExistStanEntity = statisticalOrgLifeMapper.
                    selectIsExistStaRecord(statisticalOrgLifeEntity.getOrgId(),
                    statisticalOrgLifeEntity.getActivityTypeId(),statisticalOrgLifeEntity.getStatisticalDate());
            StatisticalDataRecordEntity statisticalDataRecordEntity = new StatisticalDataRecordEntity();
            statisticalDataRecordEntity.setDataType(1);
            //设置区域id
            statisticalOrgLifeEntity.setRegionId(orgService.
                    getById(statisticalOrgLifeEntity.getOrgId()).getRegionId());
            //存在并且统计的数据不一致辞的情况就更新数据
            if (null != isExistStanEntity) {
                //只有存档参与次数 与 新的查询的参与次数不等的情况  才进行更新
                if (!isExistStanEntity.getParticipateNum().equals(statisticalOrgLifeEntity.getParticipateNum())
                    || isSup) {
                    Long orgLifeId = isExistStanEntity.getOrgLifeId();
                    statisticalOrgLifeService.updateMeetingStaInfo(orgLifeId,
                            statisticalOrgLifeEntity.getParticipateNum().longValue(),
                            statisticalOrgLifeEntity.getMeetingStartTime(),
                            statisticalOrgLifeEntity.getMeetingIds());
                }
            } else {
                BeanUtils.copyProperties(statisticalOrgLifeEntity, statisticalDataRecordEntity);
                statisticalOrgLifeMapper.insertOrgLife(statisticalOrgLifeEntity);
                statisticalDataRecordMapper.insert(statisticalDataRecordEntity);
            }
            //验证在当月是否存在组织信息
            List<StatisticalOrgLifeEntity> statisticalOrgLifeEntities =
                    statisticalOrgLifeMapper.selectIsExistStaRecordTheMonth(statisticalOrgLifeEntity.getOrgId());
            if(!CollectionUtils.isEmpty(statisticalOrgLifeEntities)){
                //（目前当月组织关系发生变化进行更新，上月数据不进行更新）
                int result = statisticalOrgLifeService.updateOrgRelationShip(statisticalOrgLifeEntity);
                log.info("更新当月组织关系结果Result-{}-orgId-{}",result,statisticalOrgLifeEntity.getOrgId());
            }
            //补录组织组织创建时间、支委会数量 这里不操作支委会的信息，resetPeriodCreateTime 这里单独处理
            statisticalOrgLifeService.supplementOrgInfo(item.getOrgCreateTime(),
                    item.getOrganizationId(), item.getPeriodSum(), item.getRegionId());

        }catch (Exception ex){
            log.error("验证组织生活变动情况-StaMeetingScheduler-valMeetingVariety-exMsg-{}，" +
                    "ex:{}",ex.getMessage(),ex);
        }
    }

   //这里填充信息,这里是那些没有统计的全部为0
   private void fillStaData(boolean isInit,StatisticalOrgLifeEntity statisticalOrgLifeEntity,List<StaMeetingType> staMeetingType){
       List<String> yearMonthsBetweenTimes = DateUtils.getMonthsBetweenTimes(staStartMonth);
       List<StatisticalOrgLifeEntity> list=new ArrayList<>();
       Long regionId = orgService.getById(statisticalOrgLifeEntity.getOrgId()).getRegionId();
       Optional<StaMeetingType> meetingType = staMeetingType.stream().filter(item -> item.getRegionId().equals(regionId)).findFirst();
       if(meetingType.isPresent()) {
           activityTypArray = meetingType.get().getMeetingTypeIds().split(",");
           if (isInit) {
               for (String item : activityTypArray) {
                   for (int i = 0; i < yearMonthsBetweenTimes.size(); i++) {
                       String yearMonth = yearMonthsBetweenTimes.get(i);
                       StatisticalOrgLifeEntity wrapperStatisticalOrgLife = wrapperStatisticalOrgLife(statisticalOrgLifeEntity, Integer.valueOf(item), yearMonth, regionId);
                       if (null != wrapperStatisticalOrgLife) {
                           list.add(wrapperStatisticalOrgLife);
                       }
                   }
               }
           } else {
               SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
               String currentYearMonth = format.format(new Date());
               for (String item : activityTypArray) {
                   StatisticalOrgLifeEntity wrapperStatisticalOrgLife = wrapperStatisticalOrgLife(statisticalOrgLifeEntity, Integer.valueOf(item), currentYearMonth, regionId);
                   if (null != wrapperStatisticalOrgLife) {
                       list.add(wrapperStatisticalOrgLife);
                   }
               }
           }
       }
       if(!CollectionUtils.isEmpty(list)){
           int result = statisticalOrgLifeMapper.insertList(list);
           log.info("填充信息-fillStaData-result-{}",result);
       }
   }


    /**
     * 初始化党小组时间 ，跟产品确定统计时间是从2019-1月开始
     */
    public void initPartyNumber(Long orgId,boolean isInit) {
        if (isInit) {
            List<String> yearMonthsBetweenTimes = DateUtils.getMonthsBetweenTimes(staStartMonth);
            for (int i = 0; i < yearMonthsBetweenTimes.size(); i++) {
                try {
                    //调用王诺宇那边接口 返回信息
                    List<PeriodForm> list = new ArrayList<>();
                    PeriodForm periodForm = new PeriodForm();
                    periodForm.setOrgId(orgId);
                    periodForm.setPeriodQueryTime(yearMonthsBetweenTimes.get(i));
                    list.add(periodForm);
                    List<PeriodFindOrgsResultForm> periodInfo = this.orgPeriodService.getPeriodInfo(list);
                    PeriodFindOrgsResultForm periodFindOrgsResultForm = periodInfo.get(0);
                    //初始化所有信息 包括当月
                    statisticalStorageVariableService.handlerStorageVariable(periodFindOrgsResultForm.getCreateOrgGroups()==null?0:periodFindOrgsResultForm.getCreateOrgGroups(),
                            orgId ,yearMonthsBetweenTimes.get(i));
                }catch (Exception ex){
                    log.error("初始化党小组时间 ,出错-ex-{}",ex);
                }
            }
        }else {
            try {
                //调用王诺宇那边接口 返回信息
                List<PeriodForm> list = new ArrayList<>();
                PeriodForm periodForm = new PeriodForm();
                periodForm.setOrgId(orgId);
                //得到当月统计时间
                String statisticalDate = DateUtils.dateFormat(new Date(), "yyyy-MM");
                periodForm.setPeriodQueryTime(statisticalDate);
                list.add(periodForm);
                List<PeriodFindOrgsResultForm> periodInfo = this.orgPeriodService.getPeriodInfo(list);
                PeriodFindOrgsResultForm periodFindOrgsResultForm = periodInfo.get(0);
                //操作当月
                int createOrgGroups = periodFindOrgsResultForm.getCreateOrgGroups() == null ? 0 : periodFindOrgsResultForm.getCreateOrgGroups();
                statisticalStorageVariableService.handlerStorageVariable(createOrgGroups, orgId ,statisticalDate);
            }catch (Exception ex){
                log.error("初始化党小组时间 ,当月统计,出错-ex-{}",ex);
            }

        }
    }




    /**
     * 包装填充统计组织生活
     */
   private  StatisticalOrgLifeEntity wrapperStatisticalOrgLife(StatisticalOrgLifeEntity statisticalOrgLifeEntity,
                                                               Integer activityId, String yearMonths,Long regionId){
       Long orgId = statisticalOrgLifeEntity.getOrgId();
       StatisticalOrgLifeEntity isExistStanEntity = statisticalOrgLifeMapper.selectIsExistStaRecord(orgId,activityId,yearMonths);
       String activityName = statisticalTempActivityService.getActivityName(activityId);
       if(activityName==null){
           return null;
       }
       //等于才进行填充数据
       if(isExistStanEntity==null){
           StatisticalOrgLifeEntity statisticalOrgLifeEntityCopy=new StatisticalOrgLifeEntity();
           BeanUtils.copyProperties(statisticalOrgLifeEntity,statisticalOrgLifeEntityCopy);
           statisticalOrgLifeEntityCopy.setStatisticalDate(yearMonths);
           statisticalOrgLifeEntityCopy.setParticipateNum(0);
           statisticalOrgLifeEntityCopy.setActivityTypeId(activityId);
           statisticalOrgLifeEntityCopy.setActivityTypeName(activityName);
           statisticalOrgLifeEntityCopy.setRegionId(regionId);
           statisticalOrgLifeEntityCopy.setStatisticalYear(Integer.valueOf(getYmdStringToMap(yearMonths).get("year")));
           statisticalOrgLifeEntityCopy.setStatisticalMonth(Integer.valueOf(getYmdStringToMap(yearMonths).get("month")));
           return statisticalOrgLifeEntityCopy;
       }else {
           return null;
       }
   }

    /**
     * 封闭统计组织生活类
     * @return
     */
    public  static   StatisticalOrgLifeEntity setStatisticalOrgLifeEntity(OrganizationForm organizationForm ){
        try {
            StatisticalOrgLifeEntity statisticalOrgLifeEntity = new StatisticalOrgLifeEntity();
            statisticalOrgLifeEntity.setOrgName(organizationForm.getName() == null ? "" :
                    organizationForm.getName());
            statisticalOrgLifeEntity.setOrgId(organizationForm.getOrganizationId());
            statisticalOrgLifeEntity.setParentOrgId(organizationForm.getParentId());
            statisticalOrgLifeEntity.setOrgLevel(organizationForm.getOrgLevel());
            statisticalOrgLifeEntity.setIsRetire(organizationForm.getIsRetire());
            statisticalOrgLifeEntity.setOrgTypeId(organizationForm.getOrgTypeChild());
            statisticalOrgLifeEntity.setStatus(1);
            statisticalOrgLifeEntity.setCreateTime(new Date());
            return statisticalOrgLifeEntity;
        }catch (Exception ex){
            return  null;
        }

    }


    /**
     * 封闭统计组织生活类
     * @return
     */
    public  StatisticalOrgLifeEntity setStatisticalOrgLifeEntity(OrganizationForm organizationForm,StatisticsMeetingForm statisticsMeetingForm){
        try {
            StatisticalOrgLifeEntity statisticalOrgLifeEntity = new StatisticalOrgLifeEntity();
            statisticalOrgLifeEntity.setOrgName(organizationForm.getName() == null ? "" : organizationForm.getName());
            //得到组织对应区县id
            statisticalOrgLifeEntity.setRegionId(orgService.getById(organizationForm.getOrganizationId()).getRegionId());
            statisticalOrgLifeEntity.setOrgId(organizationForm.getOrganizationId());
            statisticalOrgLifeEntity.setParentOrgId(organizationForm.getParentId());
            statisticalOrgLifeEntity.setOrgLevel(organizationForm.getOrgLevel());
            statisticalOrgLifeEntity.setIsRetire(organizationForm.getIsRetire());
            statisticalOrgLifeEntity.setOrgTypeId(organizationForm.getOrgTypeChild());
            statisticalOrgLifeEntity.setActivityTypeName(statisticsMeetingForm.getTypeName());
            statisticalOrgLifeEntity.setStatus( 1);
            statisticalOrgLifeEntity.setMeetingStartTime(statisticsMeetingForm.getMeetingStartTime());
            statisticalOrgLifeEntity.setMeetingIds(statisticsMeetingForm.getMeetingIds());
            statisticalOrgLifeEntity.setParticipateNum(statisticsMeetingForm.getCountNum().intValue());
            statisticalOrgLifeEntity.setActivityTypeId(statisticsMeetingForm.getTypeId().intValue());
            statisticalOrgLifeEntity.setStatisticalDate(statisticsMeetingForm.getStaTime());
            statisticalOrgLifeEntity.setStatisticalYear(Integer.valueOf(getYmdStringToMap(statisticsMeetingForm.getStaTime()).get("year")));
            statisticalOrgLifeEntity.setStatisticalMonth(Integer.valueOf(getYmdStringToMap(statisticsMeetingForm.getStaTime()).get("month")));
            statisticalOrgLifeEntity.setCreateTime(new Date());
            return statisticalOrgLifeEntity;
        }catch (Exception ex){
            return  null;
        }

    }


    /**
     * yyyy-MM-dd 转成相应的数组格式
     * @return
     */
    private static   Map<String,String> getYmdStringToMap(String staTime){
        String[] arrayStaTime = staTime.split("-");
        try {
            Map<String, String> map = new HashMap<>();
            map.put("year", arrayStaTime[0]);

            //判断组织年月是0开头
            String firstWord = arrayStaTime[1].substring(0, 1);
            if(firstWord.equals("0")){
                map.put("month", arrayStaTime[1].substring(1));
            }else {
                map.put("month", arrayStaTime[1]);
            }
            return map;
        }catch (Exception ex){
            return  null;
        }
    }

}
