package com.goodsogood.ows.component;

import com.goodsogood.ows.helper.LogHelper;
import com.goodsogood.ows.service.RegionService;
import com.goodsogood.ows.service.sas.StatisticalOrgLifeViewCountService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @program: ows-sas
 * @description: 组织生活一览表统计定时任务
 * @author: chenanshun
 * @create: 2019-04-22 20:00
 */
@Component
@Log4j2
public class StaOrgLifeViewScheduler {

    /**
     * 是否开启定时任务
     */
    @Value("${scheduler.org-life.run}")
    private boolean runFlag = false;

    private final LogHelper logHelper;
    private final StatisticalOrgLifeViewCountService statisticalOrgLifeViewCountService;

    @Autowired
    public StaOrgLifeViewScheduler(
            LogHelper logHelper, StatisticalOrgLifeViewCountService statisticalOrgLifeViewCountService) {
        this.logHelper = logHelper;
        this.statisticalOrgLifeViewCountService = statisticalOrgLifeViewCountService;
    }

    /**
     * 每天5点组织生活一览表统计 在组织生活统计和领导班子统计之后 0 0 5 * * ?
     */
    @Scheduled(cron = "${scheduler.org-life.cron}")
    public void sasOrgLifeViewCount() {
        logHelper.reSetContext(null);
        log.debug("sas-scheduler -> {}", runFlag);
        if (!runFlag) {
            log.debug("【SAS】定时器关闭!");
            return;
        }
        log.debug("【SAS】组织生活一览表统计开始!");
        statisticalOrgLifeViewCountService.sasOrgLifeViewCount();
        log.debug("【SAS】组织生活一览表统计结束!");
    }
}
