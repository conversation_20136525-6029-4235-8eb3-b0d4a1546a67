package com.goodsogood.ows.component;

import com.goodsogood.ows.service.sas.WorkReportService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 拉取工作报告数据
 *
 * <AUTHOR>
 */
@Component
@Log4j2
public class PullWorkReportDataScheduler {

    /**
     * 是否开启定时任务
     */
    @Value("${scheduler.pull-work-report-data.run}")
    private boolean runStatus = true;

    private final WorkReportService workReportService;

    @Autowired
    public PullWorkReportDataScheduler(WorkReportService workReportService) {
        this.workReportService = workReportService;
    }

    @Scheduled(cron = "${scheduler.pull-work-report-data.cron}")
    @Transactional(rollbackFor = Exception.class)
    public void run() {
        if (!runStatus) {
            return;
        }
        log.debug("[{}] -> 拉取工作报告数据定时任务开始！", new Date());

        // 拉取
        this.workReportService.pullBeforeWorkReport();

        log.debug("[{}] -> 拉取工作报告数据定时任务结束！", new Date());
    }
}
