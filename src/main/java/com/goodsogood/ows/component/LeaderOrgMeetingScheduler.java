package com.goodsogood.ows.component;

import com.goodsogood.ows.model.db.sas.TypeEntity;
import com.goodsogood.ows.model.vo.sas.StaMeetingType;
import com.goodsogood.ows.model.vo.sas.TypeAllResultForm;
import com.goodsogood.ows.service.meeting.StaMeetingService;
import com.goodsogood.ows.service.sas.LeaderOrgMeetingFeeService;
import com.goodsogood.ows.service.sas.StatisticalLeaderOrgLifeService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 领导干部组织双重组织生活统计
 * <AUTHOR>
 */
@Component
@Log4j2
public class LeaderOrgMeetingScheduler {

    /**  是否开启定时任务 */
    @Value("${scheduler.leader-org-meeting.run}")
    private boolean runStatus;

    private final StaMeetingService meetingService;
    private final LeaderOrgMeetingFeeService feeService;
    private final StatisticalLeaderOrgLifeService statisticalLeaderOrgLifeService;

    @Autowired
    public LeaderOrgMeetingScheduler(StaMeetingService meetingService, LeaderOrgMeetingFeeService feeService,
                                     StatisticalLeaderOrgLifeService statisticalLeaderOrgLifeService) {
        this.meetingService = meetingService;
        this.feeService = feeService;
        this.statisticalLeaderOrgLifeService=statisticalLeaderOrgLifeService;
    }

    @Scheduled(cron = "${scheduler.leader-org-meeting.cron}")
    @Transactional(rollbackFor = Exception.class)
    public void run() throws Exception {
        // 判断定时任务是否开启
        if(!runStatus) {
            log.debug("【党务看板领导干部组织双重组织生活】定时器关闭!");
            return;
        }
        log.debug("【党务看板领导干部组织双重组织生活】组织生活统计 - start");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            // 获取当前月份
            Date date = new Date();
            // 调用定时任务主方法
            this.feeService.leaderOrgMeetingFeeMain(date, stopWatch, null);

        } catch (Exception e) {
            log.error("时间转换失败, 错误信息", e);
            throw e;
        } finally {
            log.debug("【党务看板领导干部组织双重组织生活】组织生活统计 - end");
        }
    }

    /**
     * 每隔15分钟类型变化
     * @throws Exception
     */
    @Scheduled(cron = "0 0/15 * * * ?")
    public void staMeetingType() throws Exception {
        // 判断定时任务是否开启
        if(!runStatus) {
            log.debug("【统计类型变化】关闭");
            return;
        }
        List<StaMeetingType> staMeetingTypes = new ArrayList<>();
        List<TypeAllResultForm> meetingTypeInfo = this.meetingService.getMeetingTypeInfo();
        meetingTypeInfo.forEach(item->{
            List<TypeEntity> types = item.getTypes();
            types.forEach(itemInner->{
                StaMeetingType staMeetingType=new StaMeetingType();
                staMeetingType.setActivityId(itemInner.getTypeId().intValue());
                staMeetingType.setTypeName(itemInner.getType());
                staMeetingTypes.add(staMeetingType);
            });
        });
        log.debug("【统计类型变化】 - start");
        try {
            if(!CollectionUtils.isEmpty(staMeetingTypes)){
                staMeetingTypes.forEach(item->{
                    statisticalLeaderOrgLifeService.handlerMeetingTypeChange(item);
                });
            }
        }catch (Exception ex){
            log.error("【统计类型变化,发生异常】-{}", ex);
        }finally {
            log.debug("【统计类型变化】 - end");
        }

    }

}
