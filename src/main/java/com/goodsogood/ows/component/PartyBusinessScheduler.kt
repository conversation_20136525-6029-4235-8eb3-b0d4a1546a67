package com.goodsogood.ows.component

import com.goodsogood.ows.configuration.SchedulerConf
import com.goodsogood.ows.service.pbm.PbmWorkDataService
import com.goodsogood.ows.service.pbm.PbmWorkKitService
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.util.*

/**
 *
 * <AUTHOR>
 * @createTime 2022年08月04日 10:17:00
 */
@Component
class PartyBusinessScheduler(private val schedulerConf: SchedulerConf,
                             private val pbmWorkDataService: PbmWorkDataService,
                             private val pbmWorkKitService: PbmWorkKitService
) {

    private val log: Logger = LogManager.getLogger(PartyBusinessScheduler::class.java)

    @Scheduled(cron = "\${scheduler.party-business.cron}")
    fun run() {
        log.debug("党业融合定时任务开始执行")
        if (!schedulerConf.partyBusiness.run) {
            log.debug("党业融合定时任务停止执行")
        }
        // 计算时间 - 获取当前年度和上个月份
        val cal = Calendar.getInstance(Locale.CHINA)
        val year = cal.get(Calendar.YEAR)
        val month = cal.get(Calendar.MONTH)
        schedulerConf.partyBusiness.regionIds.forEach { regionId ->
            log.debug("执行[$regionId]用户党建详情")
            pbmWorkDataService.calculatePbmUserWork(regionId = regionId, year = year, month = month, type = 1)
            log.debug("执行[$regionId]用户业务详情")
            pbmWorkDataService.calculatePbmUserWork(regionId = regionId, year = year, month = month, type = 2)
            log.debug("执行[$regionId]组织党建详情")
            pbmWorkDataService.calculatePbmOrgWork(regionId = regionId, year = year, month = month, type = 1)
            log.debug("执行[$regionId]组织业务详情")
            pbmWorkDataService.calculatePbmOrgWork(regionId = regionId, year = year, month = month, type = 2)
            log.debug("计算[$regionId]全量拟合度")
            pbmWorkKitService.calculatePbmKit(regionId = regionId, year = year, month = month, isCalculateUser = true, type = 0)
            log.debug("计算[$regionId]卷烟营销拟合度")
            pbmWorkKitService.calculatePbmKit(regionId = regionId, year = year, month = month, isCalculateUser = false, type = 1)
            log.debug("计算[$regionId]烟叶生成拟合度")
            pbmWorkKitService.calculatePbmKit(regionId = regionId, year = year, month = month, isCalculateUser = false, type = 2)
            log.debug("计算[$regionId]专卖管理拟合度")
            pbmWorkKitService.calculatePbmKit(regionId = regionId, year = year, month = month, isCalculateUser = false, type = 3)
            log.debug("计算[$regionId]综合管理拟合度")
            pbmWorkKitService.calculatePbmKit(regionId = regionId, year = year, month = month, isCalculateUser = false, type = 4)
        }
        log.debug("党业融合定时任务结束执行")
    }
}