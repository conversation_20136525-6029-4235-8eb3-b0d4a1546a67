package com.goodsogood.ows.component;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.List;

@Component
@Log4j2
public class RegionDataScheduler {

    /**  是否开启定时任务 */
    @Value("${scheduler.region.run}")
    private boolean runStatus;

    @Value("${region-label}")
    private String reigionLabel;

    private final StringRedisTemplate redisTemplate;
    private final SimpleApplicationConfigHelper configHelper;

    @Autowired
    public RegionDataScheduler(StringRedisTemplate redisTemplate, SimpleApplicationConfigHelper configHelper) {
        this.redisTemplate = redisTemplate;
        this.configHelper = configHelper;
    }

    @Scheduled(cron = "${scheduler.region.cron}")
    @Transactional(rollbackFor = ParseException.class)
    public void run(){
        // 判断定时任务是否开启
        if(!runStatus) {
            log.debug("【党务看板党费统计】定时器关闭!");
            return;
        }

        if(!this.redisTemplate.hasKey(Constants.REGION_LIST_KEY) || this.redisTemplate.opsForList().size(Constants.REGION_LIST_KEY) == 0){
            log.debug("【写入区县信息】区县列表不存在，正在写入...");
            Region regions = this.configHelper.getRegions(reigionLabel);
            List<Region.RegionData> regionList = regions.getRegions();
            regionList.forEach(region -> {
                Long regionId = region.getRegionId();
                log.debug("【写入区县信息】写入[{}]...", regionId);
                this.redisTemplate.opsForList().leftPush(Constants.REGION_LIST_KEY, regionId.toString());
            });
            log.debug("【写入区县信息】区县列表不存在，写入完毕...");
        }
    }
}
