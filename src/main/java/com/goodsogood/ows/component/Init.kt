package com.goodsogood.ows.component

import com.fasterxml.jackson.core.json.JsonReadFeature
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule

/**
 *
 * <AUTHOR>
 * @createTime 2022年06月28日 22:21:00
 */
object Init {

    val objectMapper: ObjectMapper =
        ObjectMapper()
            .enable(JsonReadFeature.ALLOW_TRAILING_COMMA.mappedFeature())
            .registerModules(KotlinModule(), ParameterNamesModule(), Jdk8Module(), JavaTimeModule())
            .findAndRegisterModules()
            .also {
                it.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                it.configure(MapperFeature.USE_GETTERS_AS_SETTERS, false)
            }

}