package com.goodsogood.ows.component;

import com.goodsogood.ows.common.ScoreManagerEnum;
import com.goodsogood.ows.common.redisUtil.RedisLockUtil;
import com.goodsogood.ows.configuration.ScoreManagerConfig;
import com.goodsogood.ows.service.scoreManager.OrgScoreManagerService;
import com.goodsogood.ows.service.scoreManager.ScoreManagerHelper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/12/11
 */
@Component
@Log4j2
public class ScoreManagerScheduler {

    @Value("${score-manager.run}")
    private boolean runStatus;

    private final OrgScoreManagerService orgScoreManagerService;
    private final ScoreManagerConfig scoreManagerConfig;
    private final StringRedisTemplate stringRedisTemplate;
    private final Errors errors;
    private final ScoreManagerHelper scoreManagerHelper;

    private static final String lock = "ScoreManagerScheduler-lock";

    @Autowired
    public ScoreManagerScheduler(OrgScoreManagerService orgScoreManagerService,
                                 ScoreManagerConfig scoreManagerConfig,
                                 StringRedisTemplate stringRedisTemplate, Errors errors, ScoreManagerHelper scoreManagerHelper) {
        this.orgScoreManagerService = orgScoreManagerService;
        this.scoreManagerConfig = scoreManagerConfig;
        this.stringRedisTemplate = stringRedisTemplate;
        this.errors = errors;
        this.scoreManagerHelper = scoreManagerHelper;
    }

    @Scheduled(cron = "${score-manager.cron}")
    public void execute() {
        if (!runStatus) {
            log.debug("ScoreManagerScheduler->定时任务关闭,结束执行!");
            return;
        }
        String uuid = UUID.randomUUID().toString();
        try {
            Thread.sleep((new Random().nextInt(10) + 1) * 1000);
            boolean b = RedisLockUtil.tryGetDistributedLock(stringRedisTemplate, lock, uuid, 60 * 1000);
            if (!b) {
                log.debug("ScoreManagerScheduler->未获取到锁,结束执行!");
                return;
            }
            // [{19:[1,2,3,4,6,7,8,9,10,11,12]}]
            Map<Long, List<Integer>> regions = scoreManagerConfig.getRegions();
            if (CollectionUtils.isEmpty(regions)) {
                log.debug("ScoreManagerScheduler->未配置区县参数,结束执行!");
            }
//            //获取当月
//            String month = DateTimeFormatter.ofPattern("yyyy-MM").format(LocalDate.now());
            //获取上月
            String month = DateTimeFormatter.ofPattern("yyyy-MM").format(LocalDate.now().minusMonths(1));
            for (Map.Entry<Long, List<Integer>> entry : regions.entrySet()) {
                Long regionId = entry.getKey();
                List<ScoreManagerEnum> enums = new ArrayList<>(entry.getValue().size());
                for (Integer type : entry.getValue()) {
                    ScoreManagerEnum byTypeId = ScoreManagerEnum.findByTypeId(type);
                    if (null == byTypeId) {
                        scoreManagerHelper.throwMyException("非法配置类型:{}" + type);
                    }
                    // 数据处理类型 1:自身定时任务接口来处理该数据 2:只接存储到积分数据库中
                    if (!byTypeId.getHandleType().equals(1)) {
                        continue;
                    }
                    enums.add(byTypeId);
                }
                /* TODO 发送定时任务开始钉钉消息 */
                orgScoreManagerService.execute(month, regionId, enums);
            }
        } catch (Exception e) {
            log.debug("ScoreManagerScheduler->出现异常: " + e.getMessage(), e);
            /* TODO 发送异常钉钉消息 */
        } finally {
            RedisLockUtil.releaseDistributedLock(stringRedisTemplate, lock, uuid);
        }
    }
}
