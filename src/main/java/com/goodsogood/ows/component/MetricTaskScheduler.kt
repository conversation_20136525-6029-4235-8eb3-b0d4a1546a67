package com.goodsogood.ows.component

import com.goodsogood.ows.configuration.SchedulerConf
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.service.eval.v2.MetricTaskService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpHeaders
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDate

/**
 * 考核2.0 定时任务
 * <AUTHOR>
 * @date 2023/12/9
 * @description class MetricTaskScheduler
 */
@Component
class MetricTaskScheduler(
    private val schedulerConf: SchedulerConf,
    private val metricTaskService: MetricTaskService
) {
    private val log = LoggerFactory.getLogger(MetricTaskScheduler::class.java)

    @Value("\${scheduler.eval-v2.run}")
    private var run: Boolean = false

    @Scheduled(cron = "\${scheduler.eval-v2.cron}")
    fun run() {
        log.debug(
            "考核2.0，开始计算定时任务->{},schedulerConf.evalV2.run:{},run:{}",
            LocalDate.now(), schedulerConf.evalV2.run, run
        )
//        if (!schedulerConf.evalV2.run) return
        if (!run) return
        // 获取当前年份year，如果是1月，那么获取去年
        val year = if (LocalDate.now().monthValue == 1) {
            LocalDate.now().year - 1
        } else {
            LocalDate.now().year
        }
        // 开始计算考核v2.0计算
        metricTaskService.computeMetric(year, HttpHeaders().also {
            /**
             * _cl: SERVER
             * //_gray: 1
             * _hs: 24759666
             * _menu_id:
             * _oid: 3
             * _org_name: %u4E2D%u5171%u91CD%u5E86%u5E02%u70DF%u8349%u4E13%u5356%u5C40%u515A%u7EC4
             * _org_type: 102803
             * _region_id: 19
             * _tk:-0-1-3-121B6YUQVTA
             * _type: 2
             * _uid: 4
             * _un: %u8BB8%u529B%u591A
             */
            it.add(HeaderHelper.OPERATOR_CHANNEL, "SERVER")
            it.add(HeaderHelper.OPERATOR_OID, "3")
            it.add(HeaderHelper.OPERATOR_TYPE, "2")
            it.add(HeaderHelper.OPERATOR_ID, "1")
            it.add(HeaderHelper.OPERATOR_REGION, "19")
        })
    }
}