package com.goodsogood.ows.component;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.redisUtil.Config;
import com.goodsogood.ows.common.redisUtil.RedisLockUtil;
import com.goodsogood.ows.service.sas.PartyFeeService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.UUID;

/**
 *
 * 党费统计-定时任务
 * <AUTHOR>
 */
@Component
@Log4j2
public class OrgPayScheduler {

    /**  是否开启定时任务 */
    @Value("${scheduler.org-pay.run}")
    private boolean runStatus;

    private final PartyFeeService partyFeeService;
    private final StringRedisTemplate redisTemplate;

    @Autowired
    public OrgPayScheduler(PartyFeeService partyFeeService, StringRedisTemplate redisTemplate){
        this.partyFeeService = partyFeeService;
        this.redisTemplate = redisTemplate;
    }
    /**
     * 党费统计定时任务主体
     */
    @Scheduled(cron = "${scheduler.org-pay.cron}")
    public void run(){
        // 判断定时任务是否开启
        if(!runStatus) {
            log.debug("【党务看板党费统计】定时器关闭!");
            return;
        }
        Long regionId = 0L;
        while (true) {
            // 接入Redis锁机制，防止重复新增标签
            String requestId = UUID.randomUUID().toString();
            String lockKey = Constants.REGION_LOCK_KEY;
            try {
                boolean lock = RedisLockUtil.tryGetDistributedLock(redisTemplate, lockKey, requestId, Config.USER_CENTER_LOCK_EXPIRE);
                log.debug("是否拿到锁：lock -> [{}]", lock);
                // 判断是否获取锁
                if (!lock) {
                    lock = RedisLockUtil.tryGetLock(redisTemplate, lockKey, requestId, Config.USER_CENTER_LOCK_EXPIRE);
                }
                log.debug("获取锁时间： requestId -> [{}]", requestId);
                // 执行具体逻辑
                if (lock) {
                    String regionIdStr = this.redisTemplate.opsForList().rightPop(Constants.REGION_LIST_KEY);
                    if (StringUtils.isNotBlank(regionIdStr)) {
                        regionId = Long.valueOf(regionIdStr);
                    } else {
                        break;
                    }
                } else {
                    log.error("长时间没拿到锁 requestId -> [{}]", requestId);
                }
            } catch (Exception e) {
                log.error("【党务看板党费统计】报错, 错误信息", e);
            } finally {
                // 释放锁
                log.debug("执行完毕, 消耗时间 : lockKey -> [{}], requestId -> [{}]", lockKey, requestId);
                if (!StringUtils.isEmpty(lockKey) && !StringUtils.isEmpty(requestId)) {
                    log.debug("执行完毕，释放锁: lockKey -> [{}], requestId -> [{}]", lockKey, requestId);
                    RedisLockUtil.releaseDistributedLock(redisTemplate, lockKey, requestId);
                }
            }
            log.debug("【党务看板党费统计】党费统计 - start");
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            try {
                // 获取当前月份
                Date date = new Date();
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                //得到前一天
                calendar.add(Calendar.DATE, -1);
                Date beforeDate = calendar.getTime();
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM");
                String currentMouth = formatter.format(beforeDate);
                // 调用定时任务主方法
                this.partyFeeService.OrgPayMain(currentMouth, date, false, stopWatch, 0, regionId);
            } catch (ParseException e) {
                log.error("时间转换失败, 错误信息", e);
            } finally {
                log.debug("【党务看板党费统计】党费统计 - end");
            }
        }
    }
}
