package com.goodsogood.ows.component;

import com.goodsogood.ows.service.tbcFusion.TbcBasicsService;
import com.goodsogood.ows.service.tbcFusion.TbcSaveService;
import com.goodsogood.ows.service.tbcFusion.TbcScoreService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * @description: 党业融合大屏定时任务
 * @author: zhangtao
 * @create: 2021-12-15 16:14
 */
@Component
@Log4j2
public class TbcFusionScheduler {

    private final TbcSaveService saveService;
    private final TbcBasicsService tbcBasicsService;
    private final TbcScoreService tbcScoreService;

    /**
     * 是否开启定时任务
     */
    @Value("${scheduler.tbc-fusion.run}")
    private boolean runFlag = false;


    public TbcFusionScheduler(TbcSaveService saveService, TbcBasicsService tbcBasicsService, TbcScoreService tbcScoreService) {
        this.saveService = saveService;
        this.tbcBasicsService = tbcBasicsService;
        this.tbcScoreService = tbcScoreService;
    }


    /**
     * 定时刷新数据缓存
     */
    @Scheduled(cron = "${scheduler.tbc-fusion.cron}")
    public void RefreshCache() {
        log.info("党业融合大屏刷新开始");
        if(!runFlag){
            log.info("党业融合大屏刷新,关闭");
            return;
        }
        try {
            //党支部党树统计信息
            saveService.TbcOrgBasics();
            //党支部基础信息统计
            saveService.TbcBasics();
            //支部堡垒指数统计
            saveService.TbcFortress();
            //支部堡垒指数和党建先锋指数前三统计
            saveService.TbcThreeFortress();
            //支部堡垒指数和党建先锋指数前三统计
            saveService.TbcThreeFortressTOPTen();
            //支部工作情况（散点图）
            saveService.TbcPartyMemberWork();
            //党员”党建+业务”工作情况(柱状图)
            saveService.TbcPartyBranchWord();
            //党员先锋指数(饼状图)
            saveService.TbcPioneerIndex();
            //创新情况统计(词云)
            saveService.TbcInnovate();
            //词云统计标题
            saveService.TbcPioneerTitle();
            //党页拟合度(条形图)
            saveService.TbcMatching();
            //党业地图区
//            saveService.TbcMap();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("刷新数据出错", e);
        }
        log.debug("党业融合大屏刷新结束");
    }

    /**
     * 定时刷新数据缓存
     */
    @Scheduled(cron = "${scheduler.tbc-fusion.basic-info-cron}")
    public void refreshCache() {
        log.info("党业融合(包括党委与人员)计算数据开始");
        if(!runFlag){
            log.info("党业融合(包括党委与人员)计算数据,关闭");
            return;
        }
        try {
            tbcBasicsService.getTbcBaseInfo(1);
            tbcBasicsService.getTbcBaseInfo(2);
        } catch (Exception e) {
            log.error("党业融合(包括党委与人员)计算异常", e);
        }
        log.debug("党业融合(包括党委与人员)计算结束");
    }

    /**
     * 每晚11点清洗数据
     * 清洗 new_owner_id 和 seq_number 两个字段信息
     */
    @Scheduled(cron = "0 0 23 * * ?")
    public void cleaningTbcBaseInfo() {
        log.info("清洗t_tbc_user_info开始");
        if(!runFlag){
            log.info("清洗t_tbc_user_info,关闭");
            return;
        }
        try {
            tbcBasicsService.cleaningTbcBaseInfo();
        } catch (Exception e) {
            log.error("清洗t_tbc_user_info异常", e);
        }
        log.debug("清洗t_tbc_user_info计算结束");
    }


    /**
     * 定时刷新积分数据缓存
     */
    @Scheduled(cron = "${scheduler.tbc-fusion.score-rank-cron}")
    public void scoreRank() {
        log.info("党业融合上月党建和业务积分排行开始");
        if(!runFlag){
            return;
        }
        try {
            //上月
            String yearMonth = DateUtils.getDateByMonths(-1,"yyyy-MM",null);
            tbcScoreService.createPartyRank(86L,3L,yearMonth);
            tbcScoreService.createBusinessRank(86L,3L,yearMonth);
        } catch (Exception e) {
            log.error("党业融合上月党建和业务积分排行异常", e);
        }
        log.debug("党业融合上月党建和业务积分排行结束");
    }
}
