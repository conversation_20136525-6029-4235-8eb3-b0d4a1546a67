package com.goodsogood.ows.component

import org.slf4j.LoggerFactory
import org.springframework.cache.annotation.CacheEvict
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.util.concurrent.TimeUnit

/**
 * <AUTHOR>
 * @date 2023/3/19
 * @description class ClearCacheScheduler 清理缓存的定时器
 */
@Component
class ClearCacheScheduler {
    private val log = LoggerFactory.getLogger(ClearCacheScheduler::class.java)

    @CacheEvict(allEntries = true, value = ["datav-cache"])
    @Scheduled(fixedDelay = 10, initialDelay = 1, timeUnit = TimeUnit.MINUTES)
    fun cleanDatavCache() {
        log.debug("每10分钟清除【datav大屏】缓存数据...")
    }
}