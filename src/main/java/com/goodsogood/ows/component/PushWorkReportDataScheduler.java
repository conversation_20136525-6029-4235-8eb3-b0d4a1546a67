package com.goodsogood.ows.component;

import com.goodsogood.ows.service.sas.PushDataService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 推送工作报告的定时任务
 * <AUTHOR>
 */
@Component
@Log4j2
public class PushWorkReportDataScheduler {

    /**  是否开启定时任务 */
    @Value("${scheduler.push-work-report-data.run}")
    private boolean runStatus = true;

    private final PushDataService pushDataService;

    @Autowired
    public PushWorkReportDataScheduler(PushDataService pushDataService) {
        this.pushDataService = pushDataService;
    }

    @Scheduled(cron = "${scheduler.push-work-report-data.cron}")
    @Transactional(rollbackFor = Exception.class)
    public void run(){
        if (!runStatus) {
            return;
        }
        log.debug("[{}] -> 推送工作报告数据定时任务开始！", new Date());

        // 推送
        this.pushDataService.pushWorkReportData();

        log.debug("[{}] -> 推送工作报告数据定时任务结束！", new Date());
    }
}
