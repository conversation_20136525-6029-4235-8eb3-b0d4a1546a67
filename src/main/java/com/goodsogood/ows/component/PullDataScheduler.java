package com.goodsogood.ows.component;

import com.goodsogood.ows.configuration.PullDataConfig;
import com.goodsogood.ows.service.history.HistoryServices;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 拉取数据
 */
@Component
@Log4j2
public class PullDataScheduler {

    /**  是否开启定时任务 */
    @Value("${scheduler.pull-data.run}")
    private boolean runStatus;

    private final HistoryServices historyServices;
    private final PullDataConfig pullDataConfig;

    public PullDataScheduler(HistoryServices historyServices,
                             PullDataConfig pullDataConfig) {
        this.historyServices = historyServices;
        this.pullDataConfig = pullDataConfig;
    }


    /**
     * 党费统计定时任务主体
     */
    @Scheduled(cron = "${scheduler.pull-data.pull-history}")
    public void run() {
        // 判断定时任务是否开启
        if (!runStatus) {
            log.debug("【每天两点拉取小程序的数据】定时器关闭!");
            return;
        }
        log.debug("【每天两点拉取小程序的数据】开始!");
        pullDataConfig.getHistory().forEach(historyServices::handlerTable);
        log.debug("【每天两点拉取小程序的数据】结束!");
    }
}
