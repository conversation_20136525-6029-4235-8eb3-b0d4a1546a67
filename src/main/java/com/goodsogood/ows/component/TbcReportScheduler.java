package com.goodsogood.ows.component;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.redisUtil.RedisLockUtil;
import com.goodsogood.ows.service.tbc.TbcMeetingLifeService;
import com.goodsogood.ows.service.tbc.TbcPartyOrganService;
import com.goodsogood.ows.service.tbc.TbcPartyUsersService;
import com.goodsogood.ows.service.tbc.TbcPpmdService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/1/5
 * 烟草大屏定时刷新 党员 组织架构 组织生活报告
 * 缓存时间为10分钟  定时任务启动为每8分钟刷新一次
 */
@Component
@Log4j2
public class TbcReportScheduler {
    private final TbcPartyUsersService partyUsersService;
    private final TbcPartyOrganService PartyOrganService;
    private final TbcMeetingLifeService meetingLifeService;
    private final StringRedisTemplate redisTemplate;
    private final TbcPpmdService ppmdService;

    /**
     * 烟草组织id
     */
    private static final Long ORGANIZATION_ID = 4647L;

    /**
     * 是否开启定时任务
     */
    @Value("${scheduler.tbc-report.run}")
    private boolean runFlag = false;

    @Autowired
    public TbcReportScheduler(TbcPartyUsersService partyUsersService,
                              TbcPartyOrganService partyOrganService,
                              TbcMeetingLifeService meetingLifeService,
                              StringRedisTemplate redisTemplate,
                              TbcPpmdService ppmdService) {
        this.partyUsersService = partyUsersService;
        PartyOrganService = partyOrganService;
        this.meetingLifeService = meetingLifeService;
        this.redisTemplate = redisTemplate;
        this.ppmdService = ppmdService;
    }


    /**
     * 烟草党员大屏数据刷新
     */
    @Scheduled(cron = "${scheduler.tbc-report.party-user}")
    public void runOfPartyUser() {
        if (!runFlag) {
            log.debug("烟草大屏定时刷新-烟草党员大屏数据刷新 定时任务关闭------------------------");
            return;
        }
        String lockKey = Constants.TBC_PARTY_USER_ + "LOCK_" + ORGANIZATION_ID;
        String cacheKey = Constants.TBC_PARTY_USER_ + ORGANIZATION_ID;
        String uuid = UUID.randomUUID().toString();
        try {
            //获取redis锁 锁保持时间1分钟
            boolean b = RedisLockUtil.tryGetDistributedLock(redisTemplate, lockKey, uuid, 60 * 1000);
            //如果没有获取到锁 说明其他节点已经获取到了 该节点直接返回
            if (!b) {
                return;
            }
            log.debug("烟草大屏定时刷新-烟草党员大屏数据刷新 开始刷新------------------------");
            //提前删除缓存
            Set<String> keys = redisTemplate.keys(cacheKey + "*");
            if(!CollectionUtils.isEmpty(keys)){
                redisTemplate.delete(keys);
            }
            //查询数据
            partyUsersService.partyUser(ORGANIZATION_ID);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("烟草大屏定时刷新-烟草党员大屏数据刷新 错误:[{}]", e.getMessage());
        } finally {
            //释放锁 没获取到锁的节点调用该方法也没事
            RedisLockUtil.releaseDistributedLock(redisTemplate, lockKey, uuid);
        }
    }

    /**
     * 烟草组织架构大屏数据刷新
     */
    @Scheduled(cron = "${scheduler.tbc-report.party-org}")
    public void runOfPartyOrg() {
        if (!runFlag) {
            log.debug("烟草大屏定时刷新-烟草组织架构大屏数据刷新 定时任务关闭------------------------");
            return;
        }
        String lockKey = Constants.TBC_PARTY_ORG_ + "LOCK_" + ORGANIZATION_ID;
        String cacheKey = Constants.TBC_PARTY_ORG_ + ORGANIZATION_ID;
        String uuid = UUID.randomUUID().toString();
        try {
            boolean b = RedisLockUtil.tryGetDistributedLock(redisTemplate, lockKey, uuid, 60 * 1000);
            if (!b) {
                return;
            }
            log.debug("烟草大屏定时刷新-烟草组织架构大屏数据刷新 开始刷新------------------------");
            //提前删除缓存
            Set<String> keys = redisTemplate.keys(cacheKey + "*");
            if(!CollectionUtils.isEmpty(keys)){
                redisTemplate.delete(keys);
            }
            PartyOrganService.partyOrg(ORGANIZATION_ID);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("烟草大屏定时刷新-烟草组织架构大屏数据刷新 错误:[{}]", e.getMessage());
        } finally {
            RedisLockUtil.releaseDistributedLock(redisTemplate, lockKey, uuid);
        }
    }

    /**
     * 烟草组织生活大屏数据刷新
     */
    @Scheduled(cron = "${scheduler.tbc-report.party-meeting}")
    public void runOfPartyMeeting() {
        if (!runFlag) {
            log.debug("烟草大屏定时刷新-烟草组织生活大屏数据刷新 定时任务关闭------------------------");
            return;
        }
        String lockKey = Constants.TBC_MEETING_LIFE_ + "LOCK_" + ORGANIZATION_ID;
        String cacheKey = Constants.TBC_MEETING_LIFE_ + ORGANIZATION_ID;
        String uuid = UUID.randomUUID().toString();
        try {
            boolean b = RedisLockUtil.tryGetDistributedLock(redisTemplate, lockKey, uuid, 60 * 1000);
            if (!b) {
                return;
            }
            log.debug("烟草大屏定时刷新-烟草组织生活大屏数据刷新 开始刷新------------------------");
            //提前删除缓存
            Set<String> keys = redisTemplate.keys(cacheKey + "*");
            if(!CollectionUtils.isEmpty(keys)){
                redisTemplate.delete(keys);
            }
            meetingLifeService.meetingLife(ORGANIZATION_ID, 3L);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("烟草大屏定时刷新-烟草组织生活大屏数据刷新 错误:[{}]", e.getMessage());
        } finally {
            RedisLockUtil.releaseDistributedLock(redisTemplate, lockKey, uuid);
        }
    }

    /**
     * 烟草党费大屏刷新
     */
    @Scheduled(cron = "${scheduler.tbc-report.party-ppmd}")
    public void runOfPartyPpmd() {
        if (!runFlag) {
            log.debug("烟草大屏定时刷新-烟草党费大屏数据刷新 定时任务关闭------------------------");
            return;
        }
        String lockKey = Constants.TBC_DUES_FORM_ + "LOCK_" + ORGANIZATION_ID;
        String cacheKey = Constants.TBC_DUES_FORM_ + ORGANIZATION_ID;
        String uuid = UUID.randomUUID().toString();
        try {
            boolean b = RedisLockUtil.tryGetDistributedLock(redisTemplate, lockKey, uuid, 60 * 1000);
            if (!b) {
                return;
            }
            log.debug("烟草大屏定时刷新-烟草党费大屏数据刷新 开始刷新------------------------");
            //提前删除缓存
            Set<String> keys = redisTemplate.keys(cacheKey + "*");
            if(!CollectionUtils.isEmpty(keys)){
                redisTemplate.delete(keys);
            }
            ppmdService.getDuesForm(ORGANIZATION_ID, 3L);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("烟草大屏定时刷新-烟草党费大屏数据刷新 错误:[{}]", e.getMessage());
        } finally {
            RedisLockUtil.releaseDistributedLock(redisTemplate, lockKey, uuid);
        }
    }
}

