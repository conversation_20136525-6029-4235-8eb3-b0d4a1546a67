package com.goodsogood.ows.component;

import com.goodsogood.ows.common.redisUtil.RedisLockUtil;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.OperationSystemConfig;
import com.goodsogood.ows.configuration.SchedulerConf;
import com.goodsogood.ows.service.operation.DataDisposeService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;

/**
 * @ClassName : OperationSystemScheduler
 * <AUTHOR> tc
 * @Date: 2022/4/18 10:57
 * @Description : 系统运行报表定时记录数据任务
 */

@Component
@Log4j2
public class OperationSystemScheduler {


    private final StringRedisTemplate redisTemplate;
    private final SchedulerConf schedulerConf;
    private final OperationSystemConfig operationSystemConfig;
    private final SimpleApplicationConfigHelper configHelper;
    private final DataDisposeService dataDisposeService;
    private final String logTxt = "系统运行报表定时记录数据任务";
    @Autowired
    public OperationSystemScheduler(StringRedisTemplate redisTemplate, SchedulerConf schedulerConf, OperationSystemConfig operationSystemConfig, SimpleApplicationConfigHelper configHelper, DataDisposeService dataDisposeService) {
        this.redisTemplate = redisTemplate;
        this.schedulerConf = schedulerConf;
        this.operationSystemConfig = operationSystemConfig;
        this.configHelper = configHelper;
        this.dataDisposeService = dataDisposeService;
    }

    @Scheduled(cron = "${scheduler.operation-system.cron}")
    public void OperationSystemTask() {
        if (!schedulerConf.getOperationSystem().getRunFlag()) {
            return;
        }
        String requestId = UUID.randomUUID().toString();
        try {
            //获取分布式锁
            boolean lock = RedisLockUtil.tryGetDistributedLock(redisTemplate, operationSystemConfig.OPERATION_SYSTEM_TASK_LOCK, requestId, operationSystemConfig.STATISTICS_SCHEDULER_LOCK_EXPIRE);
            if (lock) {
                log.info(logTxt+"开始执行...");
                long l = System.currentTimeMillis();
                //计算统计日期，前一天
                String statsDate = DateUtils.getDateByDays(-1, "yyyy-MM-dd");
                // 获取区县列表
                Region regions = configHelper.getRegions();
                for (Region.RegionData region : regions.getRegions()) {
                    Long regionId = region.getRegionId();
                    CompletableFuture<String> cf1 = CompletableFuture.supplyAsync(() -> dataDisposeService.findVisitRate(regionId,statsDate));
                    CompletableFuture<String> cf2 = CompletableFuture.supplyAsync(() -> dataDisposeService.findVisitDays(regionId,statsDate));
                    CompletableFuture<String> cf3 = CompletableFuture.supplyAsync(() -> dataDisposeService.findClickMenu(regionId,statsDate));
                    CompletableFuture<String> cf4 = CompletableFuture.supplyAsync(() -> dataDisposeService.findVisitNumByInterval(regionId,statsDate));
                    // 并行执行，阻塞main线程直到所有任务执行完成
                    Stream.of(cf1,cf2,cf3,cf4).parallel().map(CompletableFuture::join);
                }
                long le = System.currentTimeMillis();
                log.info(logTxt+" 执行完毕... 消耗时间(ms): {}", (le - l));
            }
        } catch (Exception e) {
            log.error(logTxt+" 报错! ", e);
        } finally {
            RedisLockUtil.releaseDistributedLock(redisTemplate, operationSystemConfig.OPERATION_SYSTEM_TASK_LOCK, requestId);
        }
    }
}
