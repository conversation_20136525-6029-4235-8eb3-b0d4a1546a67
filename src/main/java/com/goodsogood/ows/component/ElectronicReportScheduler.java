package com.goodsogood.ows.component;

import com.goodsogood.ows.service.activity.ActivityService;
import com.goodsogood.ows.service.electronicreport.ElectronicReportServices;
import com.goodsogood.ows.service.electronicreport.OrgReportService;
import com.goodsogood.ows.service.meeting.MeetingReportService;
import com.goodsogood.ows.service.ppmd.PpmdReportService;
import com.goodsogood.ows.service.score.ScoreMasterService;
import com.goodsogood.ows.service.score.ScoreService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * @program: ows-sas
 * @description: 电子党务报告定时任务
 * @author: Mr.<PERSON>
 * @create: 2019-11-27 09:47
 **/
@Component
@Log4j2
public class ElectronicReportScheduler {

    /**  是否开启拉取模块定时任务 */
    @Value("${scheduler.generate-module-report.run}")
    private boolean moduleReportStatus;

    /**  是否开启生成电子报告定时任务 */
    @Value("${scheduler.generate-electronic-report.run}")
    private boolean reportStatus;

    private final ScoreService scoreService;
    private final ActivityService activityService;
    private final ElectronicReportServices createPersonElectronicReport;
    private final PpmdReportService ppmdReportService;
    private final MeetingReportService meetingReportService;
    private final OrgReportService orgReportService;
    private final ScoreMasterService scoreMasterService;

    @Autowired
    public ElectronicReportScheduler(ScoreService scoreService, ActivityService activityService,
                                     ElectronicReportServices createPersonElectronicReport,
                                     PpmdReportService ppmdReportService,
                                     MeetingReportService meetingReportService,
                                     OrgReportService orgReportService,
                                     ScoreMasterService scoreMasterService) {
        this.scoreService = scoreService;
        this.activityService = activityService;
        this.createPersonElectronicReport=createPersonElectronicReport;
        this.ppmdReportService = ppmdReportService;
        this.meetingReportService = meetingReportService;
        this.orgReportService = orgReportService;
        this.scoreMasterService = scoreMasterService;
    }


    /**
     * 抽取业务数据到mongoDB  每周拉取一次
     */
    @Scheduled(cron = "${scheduler.generate-module-report.cron}")
    public void generateModuleReport() {
        // 判断定时任务是否开启
        if(!moduleReportStatus) {
            log.debug("【抽取业务数据到mongoDB】定时器关闭!");
            return;
        }
        Date currentDate = new Date();
        log.debug("【抽取业务数据到mongoDB】定时器开始! 当前时间-> [{}]", currentDate);
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
            String currentTime = format.format(currentDate);
            // 拉取党群活动数据
             this.activityService.buildActivityStaInfo(false);
            // 拉取党费数据
            this.ppmdReportService.generatePpmdReport(currentTime);
            // 拉取组织生活数据
            this.meetingReportService.meetingReport(currentTime);
            // 拉取积分数据
            this.scoreMasterService.runOfScore(currentDate);
        } catch (Exception e) {
            log.error("拉取数据错误...", e);
        } finally {
            log.debug("【抽取业务数据到mongoDB】定时器结束! 当前时间-> [{}]", new Date());
        }
    }

    /**
     * 每个月3号9点生成电子党务报告
     */
    @Scheduled(cron = "${scheduler.generate-electronic-report.cron}")
    public void generateElectronicReport() {
        // 判断定时任务是否开启
        if(!reportStatus) {
            log.debug("【电子党务报告定时任务】定时器关闭!");
            return;
        }
        LocalDateTime before = LocalDateTime.now();
        log.debug("【电子党务报告定时任务】定时器开始! 当前时间-> [{}]", before);
        try {
            // 获取上个月月份
            before = before.minusMonths(1);
            DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyy-MM");
            String queryTime = formatters.format(before);
            // 拉取党群活动数据
            this.activityService.buildActivityStaInfo(queryTime);
            // 拉取党费数据
            this.ppmdReportService.generatePpmdReport(queryTime);
            // 拉取组织生活数据
            this.meetingReportService.meetingReport(queryTime);
            // 拉取积分数据
            this.scoreMasterService.runOfScore(Date.from(before.atZone(ZoneId.of("Asia/Shanghai")).toInstant()));
            // 生成人员党务报表
            this.createPersonElectronicReport.createPersonElectronicReport(false);
            // 生成组织党务报表
            this.orgReportService.runOrgReport(queryTime);
        } catch (Exception e) {
            log.error("拉取数据错误...", e);
        } finally {
            log.debug("【电子党务报告定时任务】定时器关闭! 当前时间-> [{}]", new Date());
        }
    }

}
