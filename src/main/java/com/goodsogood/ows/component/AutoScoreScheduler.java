package com.goodsogood.ows.component;

import com.goodsogood.ows.mapper.rank.SnapshotMapper;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.service.rank.DecisionSupportByMonthService;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
@Log4j2
public class AutoScoreScheduler {
    /**  是否开启自动打分定时任务 */
    @Value("${scheduler.auto-score.run}")
    private boolean autoScoreStatus;

    private final SnapshotMapper snapshotMapper;
    private final DecisionSupportByMonthService decisionSupportByMonthService;

    public AutoScoreScheduler(SnapshotMapper snapshotMapper, DecisionSupportByMonthService decisionSupportByMonthService) {
        this.snapshotMapper = snapshotMapper;
        this.decisionSupportByMonthService = decisionSupportByMonthService;
    }

    @Scheduled(cron = "${scheduler.auto-score.cron}")
    public void generatedLastMonth() {
        if (!autoScoreStatus) { return; }
        int year = new DateTime().getYear();
        int month = new DateTime().getMonthOfYear()-1;
        if (month == 0) { month = 12; year -= 1; }
        int uSize = 500;
        List<Long> userIdList = snapshotMapper.findAllUserSnapshotByYear(year);
        log.debug("人员列表总量为-->{}", userIdList.size());
        String uuid = UUID.randomUUID().toString();
        int uStart = 0, uEnd = uSize;
        while (uStart < userIdList.size()) {
            if (uEnd > userIdList.size()) {
                uEnd = userIdList.size();
            }
            List<Long> subList = Collections.synchronizedList(userIdList.subList(uStart, uEnd));
            log.debug("user-score : subList->{}", subList);
            decisionSupportByMonthService.initialUserScore(subList, year, month, uuid);
            uStart += uSize;
            uEnd += uSize;
        }

        int oSize = 50;
        List<OrgSnapshotEntity> orgSnapshotEntities = snapshotMapper.findAllOrgSnapshotByYear(year);
        List<Long> orgIdList = orgSnapshotEntities.stream().map(OrgSnapshotEntity::getOrgId).collect(Collectors.toList());
        log.debug("组织列表总量为-->{}", orgIdList.size());
        int oStart = 0, oEnd = oSize;
        while (oStart < orgIdList.size()) {
            if (oEnd > orgIdList.size()) {
                oEnd = orgIdList.size();
            }
            List<Long> subList = Collections.synchronizedList(orgIdList.subList(oStart, oEnd));
            log.debug("org-score : subList->{}", subList);
            decisionSupportByMonthService.initialOrgScore(subList, year, month, uuid);
            oStart += oSize;
            oEnd += oSize;
        }
    }
}
