package com.goodsogood.ows.listener;

import com.goodsogood.ows.component.StaMeetingScheduler;
import com.goodsogood.ows.mapper.sas.StatisticalDataRecordMapper;
import com.goodsogood.ows.mapper.sas.StatisticalOrgLifeMapper;
import com.goodsogood.ows.model.db.sas.StatisticalDataRecordEntity;
import com.goodsogood.ows.model.db.sas.StatisticalOrgLifeEntity;
import com.goodsogood.ows.model.vo.sas.OrganizationForm;
import com.goodsogood.ows.model.vo.sas.StatisticsMeetingForm;
import com.goodsogood.ows.service.meeting.StaMeetingService;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: ows-sas
 * @description: 初始化加载内容
 * @author: Mr.LiGuoYong
 * @create: 2019-04-23 13:57
 **/
@Log4j2
@Component
public class InitMeetingListener implements ApplicationListener<ContextRefreshedEvent> {

    //加载标记
    private volatile boolean loadFlag = false;

//    @Value("${listener.runFlag}")
    private final boolean runFlag=true;

    private final OrgService orgService;
    private final StaMeetingService meetingService;
    private final StatisticalOrgLifeMapper statisticalOrgLifeMapper;
    private final StatisticalDataRecordMapper statisticalDataRecordMapper;
    private final StaMeetingScheduler staMeetingScheduler;


    @Autowired
    public InitMeetingListener(OrgService orgService, StaMeetingService meetingService, StatisticalOrgLifeMapper statisticalOrgLifeMapper,
                               StatisticalDataRecordMapper statisticalDataRecordMapper, StaMeetingScheduler staMeetingScheduler) {
        this.orgService = orgService;
        this.meetingService = meetingService;
        this.statisticalOrgLifeMapper=statisticalOrgLifeMapper;
        this.statisticalDataRecordMapper=statisticalDataRecordMapper;
        this.staMeetingScheduler=staMeetingScheduler;
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        try {
            if( runFlag && ! loadFlag ) {
                if( log.isDebugEnabled() ) {
                    log.debug("start load cache...");
                }
                loadFlag = true;
//              staMeetingScheduler.staMeeting(1L);
            }
        } catch ( Exception e ) {
            log.error("load cache error: ", e);
        }
    }

//    /**
//     * 初始化统计信息
//     */
//    private  void  initMeetingStaInfo(){
//        long l = System.currentTimeMillis();
//        log.info("初始化组织生活信息开始执行...");
//        List<StatisticalOrgLifeEntity> listOrgLife = new ArrayList<>();
//        List<StatisticalDataRecordEntity> listDataRecord = new ArrayList<>();
//        //获取所有组织信息
//        List<OrganizationForm> list = this.orgService.findOrgByType(null, null, null);
//        if (!CollectionUtils.isEmpty(list)) {
//            list.forEach(item -> {
//                //查询1年组织生活信息
//                List<StatisticsMeetingForm> organizeStaInfo = this.evalMeetingService.getOrganizeStaInfo(item.getOrganizationId(),1L,0L);
//                if(!CollectionUtils.isEmpty(organizeStaInfo)){
//                    for(int i=0;i<organizeStaInfo.size();i++){
//                        StatisticalOrgLifeEntity statisticalOrgLifeEntity= staMeetingScheduler.setStatisticalOrgLifeEntity(item,organizeStaInfo.get(i));
//                        if(null==statisticalOrgLifeEntity){
//                            continue;
//                        }else {
//                            StatisticalDataRecordEntity statisticalDataRecordEntity=new StatisticalDataRecordEntity();
//                            BeanUtils.copyProperties(statisticalOrgLifeEntity,statisticalDataRecordEntity);
//                            statisticalDataRecordEntity.setDataType(1);
//                            listDataRecord.add(statisticalDataRecordEntity);
//                            listOrgLife.add(statisticalOrgLifeEntity);
//                        }
//                    }
//                }
//            });
//        }
//        if(!CollectionUtils.isEmpty(listOrgLife)) {
//            statisticalOrgLifeMapper.insertList(listOrgLife);
//        }
//        if(!CollectionUtils.isEmpty(listDataRecord)) {
//            statisticalDataRecordMapper.insertList(listDataRecord);
//        }
//        log.info("初始化组织生活信息开始执行完成... times: {}",(System.currentTimeMillis() - l));
//    }
}
