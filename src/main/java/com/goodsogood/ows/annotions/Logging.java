package com.goodsogood.ows.annotions;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2020/12/15
 */
@Retention(value = RetentionPolicy.RUNTIME)
@Target(value = {ElementType.METHOD})
public @interface Logging {

    /**
     * 日志备注
     *
     * @return
     */
    String remark() default "";

    /**
     * 是否强制生成新的method_tracker_id
     *
     * @return
     */
    boolean forceReset() default true;

}
