package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * @program: ows-sas
 * @description: 活动信息配置类
 * @author: <PERSON><PERSON>
 * @create: 2020-06-15 14:19
 **/

@Configuration
@ConfigurationProperties(prefix = "activity-type")
@Data
public class ActivityTypeConfig {

    private Map<String,String> typeConfig;

    /**
     * 辅助决策查询类型
     */
    private String dssTypes = "1,2,3,4,5";

}
