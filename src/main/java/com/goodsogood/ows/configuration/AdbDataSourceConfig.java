package com.goodsogood.ows.configuration;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;

/**
 * ADB数据，获取轻流的数据源（for jdbc Template）
 *
 * <AUTHOR>
 * @date 2023/09/26
 */
@Configuration
public class AdbDataSourceConfig {

    private static final String DATA_SOURCE = "adbDataSource";

    private static final String TRANSACTION_MANAGER = "adbTransactionManager";

    static final String JDBC_TEMPLATE = "adbJdbcTemplate";

    private static final String DATA_PREFIX = "spring.datasource.adb";

    @Bean(name = DATA_SOURCE)
    @ConfigurationProperties(prefix = DATA_PREFIX)
    public DataSource adbDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean(name = TRANSACTION_MANAGER)
    public DataSourceTransactionManager mysqlTransactionManager() {
        return new DataSourceTransactionManager(adbDataSource());
    }

    @Bean(name = JDBC_TEMPLATE)
    public JdbcTemplate adbJdbcTemplate(@Qualifier(DATA_SOURCE) DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}
