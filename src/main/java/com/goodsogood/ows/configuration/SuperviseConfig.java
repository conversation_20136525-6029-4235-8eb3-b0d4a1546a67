package com.goodsogood.ows.configuration;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 烟草监督预警的配置
 */
@Data
@Component
@ConfigurationProperties(prefix = SuperviseConfig.PREFIX)
public class SuperviseConfig {

    public static final String PREFIX = "supervise";

    /**
     * 党委配置那些选项
     */
    private Map<String,Option> committee;


    /**
     * 党支部或党小组 配置那些信息
     */
    private Map<String,Option>  branch;

    /**
     * 配置详情项信息
     */
    private Map<String,Option>  detail;

    /**
     * 党委提示项
     */
    private List<Tips> committeeTips;
    /**
     * 支部提示项
     */
    private List<Tips> branchTips;

    /**
     * 部署区县Id
     */
    private Long regionId;

    /**
     * 发送消息配置
     */
    private Map<String,SendMsg> sendMsg;


    /**
     * 顶级组织Id
     */
    private Long topOrgId;

    @Data
    public static class Tips {
        /**
         * 提示名称
         */
        @JsonProperty(value = "tip_name")
        private String tipName;
        /**
         * 最小
         */
        private Integer min;
        /**
         * 最大
         */
        private Integer max;
    }

    @Data
    public static class SendMsg {
        /**
         * key名称
         */
        private String optionKey;
        /**
         * 对应数据库字段名
         */
        private String optionCol;

        /**
         *  模板id
         */
        private Long templateId;

        /**
         *  是否为变量消息
         */
        private Boolean isVariableMsg;

        /**
         *  1.发送组织管理人员 2.单独发送人员
         */
        private Integer sendObject;

        /**
         *  发送的消息
         */
        private String msgContent;

        /**
         *  是否消息
         */
        private Boolean isSend;
    }

    @Data
    public static class Option {
        /**
         * key名称
         */
        private String optionKey;

        /**
         * 对应数据库字段名
         */
        private String optionCol;

        /**
         *  对应的中文名称
         */
        private String optionName;


        /**
         *  是否显示
         */
        private Boolean isShow;


        /**
         *  实现类名称
         */
        private String implClassName;

        /**
         * 组织生活活动id
         */
        private Integer activityId;

        /**
         * 是否跳转
         */
        private Boolean isLink=false;

        /**
         * 是否参与计算
         */
        private Boolean isCal;

        /**
         * 统计是不是自己
         */
        private Boolean isSelf;

        /**
         * 查询类型 目前 分为 组织 和 人员
         * 组织：org 人员：user
         */
        private String queryType;

        /**
         * 详情内容
         */
        private String content;
    }

}
