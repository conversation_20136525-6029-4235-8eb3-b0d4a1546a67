package com.goodsogood.ows.configuration;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;

/**
 * Volunteer数据源
 * <AUTHOR>
 * @date 2020/11/29
 */
@Configuration
// 使用TK的MapperScan
@MapperScan(basePackages = "com.goodsogood.ows.mapper.volunteer", sqlSessionFactoryRef = VolunteerDataSourceConfig.SQL_SESSION_FACTORY)

public class VolunteerDataSourceConfig {
    private static final String DATA_SOURCE = "volunteerDataSource";

    private static final String TRANSACTION_MANAGER = "volunteerTransactionManager";

    static final String SQL_SESSION_FACTORY = "volunteerSqlSessionFactory";

    private static final String DATA_PREFIX = "spring.datasource.volunteer";

    @Bean(name = DATA_SOURCE)
    @ConfigurationProperties(prefix = DATA_PREFIX)
    public DataSource volunteerDataSource(){
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean(name = TRANSACTION_MANAGER)
    public DataSourceTransactionManager mysqlTransactionManager() {
        return new DataSourceTransactionManager(volunteerDataSource());
    }

    @Bean(name = SQL_SESSION_FACTORY)
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier(DATA_SOURCE) DataSource dataSource)
            throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        return sessionFactory.getObject();
    }
}
