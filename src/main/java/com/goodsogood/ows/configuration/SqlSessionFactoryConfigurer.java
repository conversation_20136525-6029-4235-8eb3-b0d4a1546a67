package com.goodsogood.ows.configuration;

import com.goodsogood.ows.interceptor.DamengSqlInterceptor;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

/**
 * SqlSessionFactory配置器
 * 用于统一配置SQL拦截器到所有数据源
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Component
public class SqlSessionFactoryConfigurer {

    @Autowired
    private DamengSqlInterceptor damengSqlInterceptor;
    
    /**
     * 配置SqlSessionFactory，添加SQL拦截器
     * 
     * @param dataSource 数据源
     * @return 配置好的SqlSessionFactory
     * @throws Exception 配置异常
     */
    public SqlSessionFactory createSqlSessionFactory(DataSource dataSource) throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        
        // 添加SQL拦截器
        sessionFactory.setPlugins(new Interceptor[]{damengSqlInterceptor});
        
        return sessionFactory.getObject();
    }
    
    /**
     * 配置SqlSessionFactory，添加SQL拦截器和其他插件
     * 
     * @param dataSource 数据源
     * @param additionalInterceptors 额外的拦截器
     * @return 配置好的SqlSessionFactory
     * @throws Exception 配置异常
     */
    public SqlSessionFactory createSqlSessionFactory(DataSource dataSource, Interceptor... additionalInterceptors) throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        
        // 合并拦截器
        Interceptor[] allInterceptors = new Interceptor[additionalInterceptors.length + 1];
        allInterceptors[0] = damengSqlInterceptor;
        System.arraycopy(additionalInterceptors, 0, allInterceptors, 1, additionalInterceptors.length);
        
        sessionFactory.setPlugins(allInterceptors);
        
        return sessionFactory.getObject();
    }
}
