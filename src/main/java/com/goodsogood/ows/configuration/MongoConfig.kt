package com.goodsogood.ows.configuration

import com.mongodb.ConnectionString
import com.mongodb.MongoClientSettings
import com.mongodb.MongoCredential
import com.mongodb.client.MongoClient
import com.mongodb.client.MongoClients
import org.apache.commons.lang.StringUtils
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration
import org.springframework.data.mongodb.core.MongoTemplate
import java.net.URLEncoder


/**
 * <AUTHOR>
 * @date 2023/12/1
 * @description class MongoConfig
 */
@Configuration
class MongoConfig(val mongoCollectionNameConfig: MongoCollectionNameConfig) : AbstractMongoClientConfiguration() {
    override fun getDatabaseName(): String {
        return mongoCollectionNameConfig.databaseName!!
    }

    override fun mongoClient(): MongoClient {
        return if (StringUtils.isNotBlank(mongoCollectionNameConfig.userName) && StringUtils.isNotBlank(
                mongoCollectionNameConfig.password
            )
        ) {
            val encodedUsername = URLEncoder.encode(mongoCollectionNameConfig.userName, "UTF-8")
            val encodedPassword = URLEncoder.encode(mongoCollectionNameConfig.password, "UTF-8")
            MongoClients.create(
                MongoClientSettings.builder()
                    .applyConnectionString(ConnectionString("mongodb://${encodedUsername}:${encodedPassword}@${mongoCollectionNameConfig.host}:${mongoCollectionNameConfig.port}/${databaseName}"))
                    .credential(
                        MongoCredential.createCredential(
                            mongoCollectionNameConfig.userName,
                            databaseName,
                            mongoCollectionNameConfig.password.toCharArray()
                        )
                    )
                    .build()
            )
        } else {
            MongoClients.create(
                MongoClientSettings.builder()
                    .applyConnectionString(ConnectionString("mongodb://$mongoCollectionNameConfig.host:$mongoCollectionNameConfig.port/${databaseName}"))
                    .build()
            )
        }
    }

    @Bean
    @Throws(Exception::class)
    fun mongoTemplate(): MongoTemplate {
        return MongoTemplate(mongoClient(), databaseName)
    }
}