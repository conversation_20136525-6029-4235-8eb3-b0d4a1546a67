package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 统计排除的组织IDList
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "exclude-org")
public class ExcludeOrgConfig {

    private List<Long> orgIds;

    public List<Long> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<Long> orgIds) {
        this.orgIds = orgIds;
    }
}
