package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = PullDataConfig.PREFIX)
public class PullDataConfig {

    public static final String PREFIX = "pull-data";

    //拉取小程序的数据
    private List<String> history;

    //小程序的拉取时间配置
    private String historyDate;
}
