package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/21
 * Description:
 */
@Configuration
@ConfigurationProperties(prefix = ScoreMasterIndexProperty.SCORE_INDEX)
@Data
public class ScoreMasterIndexProperty {
    public static final String SCORE_INDEX = "score-mongo";

    /**
     * 每次循环抓取的数据
     */
    private Integer limitSize;

    /**
     * 需要创建的索引
     */
    private List<String> index;

    /**
     * 等待时间
     */
    private Long awaitTime;


    /**
     * 一元捐的主键Id
     */
    private Long donateId;
}
