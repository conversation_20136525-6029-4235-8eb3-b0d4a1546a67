package com.goodsogood.ows.configuration;

import org.apache.arrow.flatbuf.Int;

/**
 * 该数据本应从t_score_rule_explain的id进行获取，但因为辅助决策统计粒度不确定，但业务系统传入doris需要提前处理，因此使用该配置方便后续对其积分最细粒度id进行统计。
 * 后续如果统计粒度一致，可将值设为一致。
 * 该值在t_score_rule_explain的rule_explin_id中需存在，如不存在，需要添加，用以获取score_type以及parent_score_type。
 */
public class DssIndexScoreConstant {
    public static final Integer SALEUSER = 464; //营销的人员分值统计，需求不确定且不提供明确需求
    public static final Integer SALEORG = 465; //营销的组织分值统计，需求不确定且不提供明确需求
    public static final Integer SALEPARTY = 466;//营销的党组分值统计，需求不确定且不提供明确需求
    public static final Integer LEARNBYDATE = 23; //每日一练
    public static final Integer LEARNBYMONTH = 24;//每月一测
    public static final Integer LEARNBYQUARTZ = 25;//每季一考


    public static final Integer MEETING_BIGPARTY_USER = 10;//按时参加支部党员大会-人员
    public static final Integer MEETING_GROUP_LEADER_USER = 11;//党小组组长-人员
    public static final Integer MEETING_GROUP_USER = 12;//按时参加党小组会-人员
    public static final Integer MEETING_LECTURE_USER = 13;//按时参加党课-人员
    public static final Integer MEETING_GROUP_NOLEADER_USER = 14;//党小组组长未按时召开-人员
    public static final Integer MEETING_THEME_USER = 15;//按时参加主题党日-人员
    public static final Integer MEETING_TEACHER_USER = 16;//讲课人-人员
    public static final Integer MEETING_BRANCH_USER = 17;//支委会成员-人员
    public static final Integer MEETING_NOBRANCH_USER = 18;//支委会成员未按时参加支委会-人员


    public static final Integer MEETING_BIGPARTY_ORG = 41;//按时召开支部党员大会-机构
    public static final Integer MEETING_BRANCH_ORG = 42;//按时召开支委会
    public static final Integer MEETING_GROUP_ORG = 43;//按时召开党小组会
    public static final Integer MEETING_LECTURE_ORG = 44;//按时召开党课
    public static final Integer MEETING_THEME_ORG = 45;//按时召开主题党日
    public static final Integer STUDY_NEWS = 47;//学习近平新时代中国特色社会主义思想和新闻报到

    /**
     *
     */
    public static final Integer MEETING_COMMEND_GOOD_USER = 19;//人员创先争优中的奖励部分
    public static final Integer MEETING_COMMEND_BAD_USER = 20;//人员创先争优中的惩罚部分
    public static final Integer MEETING_COMMEND_GOOD_ORG = 21;//人员创先争优中的奖励部分
    public static final Integer MEETING_COMMEND_BAD_ORG = 22;//组织创先争优中的奖励部分

    public static final Integer LOGIN = 1;//登录


    public static final Integer SYSTEM_LOG = 35; //系统访问率
    public static final Integer PERIOD_NOTRANSF = 36;//组织届次到期未换届
    public static final Integer PERIOD_SECTARY = 37;//配齐组织书记
    public static final Integer PERIOD_PUNISH = 38; //有党员受处罚


    /**
     * 党委
     */
    public static final Integer PERIOD_SECTARY_COMMIT = 50; //健全组织机构，配强组织力量 配齐配强书记（副书记），加5个积分
    public static final Integer PERIOD_NOTRANSF_COMMIT = 51; //未督促提醒所属党支部按期开展换届  下属党支部未按期换届，一次扣2分，可累加


    /**
     * 党组
     */
    public static final Integer GROUP_LEADER = 70;// 成立党的建设工作领导小组
    public static final Integer GROUP_PUNISH = 71;// 本单位有受处分
    public static final Integer GROUP_WORKSPACE = 72;// 建立党支部工作联系点制度
    public static final Integer GROUP_BASESPACE = 73;// 建立干部基层联系点制度


    /**
     * doris积分状态对应的 rule_id
     * 以前的类型地址
     * http://wiki.aidangqun.com/project/4?p=538
     */
    public enum DorisRuleId {
        TYPE1(1, PERIOD_NOTRANSF),
        TYPE2(2, PERIOD_SECTARY),
        TYPE4(4, SYSTEM_LOG),
        TYPE5(5, STUDY_NEWS),
        TYPE6(6, PERIOD_SECTARY_COMMIT),
        TYPE7(7, PERIOD_NOTRANSF_COMMIT);
        private final Integer type;
        private final Integer ruleId;

        DorisRuleId(Integer type, Integer ruleId) {
            this.type = type;
            this.ruleId = ruleId;
        }

        //**根据code查询status
        public static Integer getRuleId(int i) {
            DorisRuleId[] ids = DorisRuleId.values();
            for (DorisRuleId map : ids) {
                if (map.type == i) {
                    return map.ruleId;
                }
            }
            return 0;
        }
    }


}
