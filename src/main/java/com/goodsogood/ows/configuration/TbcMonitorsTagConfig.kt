package com.goodsogood.ows.configuration

import com.goodsogood.ows.configuration.TbcMonitorsTagConfig
import lombok.Data
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component

/**
 * 党业融合数据可视化平台配置
 *
 * @author: huangyun
 * @create: 2021/12/03
 */
@Component
@ConfigurationProperties(prefix = TbcMonitorsTagConfig.PREFIX)
class TbcMonitorsTagConfig {
    var target: List<String>? = null

    //党业拟合度占比
    var tbcMatching: CalPercentage? = null

    //党建+业务工作情况 柱状图
    var memberParty: CalPercentage? = null

    /**
     * 排除统计组织
     */
    var excludeOrgIds: String? = null

    class CalPercentage {
        /**
         * 党建计算占比
         */
        var partyIndexProportion: Double? = null

        /**
         * 业务计算占比
         */
        var businessIndexProportion: Double? = null

        /**
         * 创新指标占比
         */
        var InnovationIndexProportion: Double? = null
    }

    //散点图中位数
    var median: Int? = null

    /** 争先创优  */
    var commendPenalizeLevel: MutableMap<Int, List<Int>> = mutableMapOf()

    companion object {
        const val PREFIX = "tbc-monitors-tag"
    }
}