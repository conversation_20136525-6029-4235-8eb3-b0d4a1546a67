package com.goodsogood.ows.configuration;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 一号消息推送配置文件
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = WorkReportMessageConfig.PREFIX)
public class WorkReportMessageConfig {

    public static final String PREFIX = "work-report-message";

    private Integer templateId;

    private SeniorWorkCollect seniorWorkCollect;
    private GeneraWorkCollect generaWorkCollect;
    private UserWorkCollect userWorkCollect;
    private LastMonthWorkCollect lastMonthWorkCollect;
    /**
     * 高级管理员工作报告
     */
    @Data
    public static class SeniorWorkCollect {
        private String titleColor;
        private String content;
        private String contentColor;
        private String timeColor;
        private String orgRank;
        private String userRank;
        private String ppmdCollect;
        private String orgLiftCollect;
        private String orgPeriodCollect;
        private String remarkColor;
        private String linkUrl;
    }

    /**
     * 一般管理员工作报告
     */
    @Data
    public static class GeneraWorkCollect {
        private String titleColor;
        private String birthCollect;
        private String periodCollect;
        private String contentColor;
        private String timeColor;
        private String orgRank;
        private String userRank;
        private String ppmdCollect;
        private String orgLiftCollect;
        private String remarkColor;
        private String linkUrl;
    }

    /**
     * 党员工作报告
     */
    @Data
    public static class UserWorkCollect {
        private String title;
        private String titleColor;
        private String prefaceCollect;
        private String ppmdCollect;
        private String orgLiftCollect;
        private String studyCollect;
        private String creditCollect;
        private String contentColor;
        private String timeColor;
        private String orgRank;
        private String userRank;
        private String remarkColor;
        private String linkUrl;
    }

    /**
     * 上月工作报告（工委管理员）
     */
    @Data
    public static class LastMonthWorkCollect {
        private String title;
        private String titleColor;
        private String orgMemberBirthdayCollect;
        private String orgPeriodInfoCollect;
        private String creditCollect;
        private String contentColor;
        private String timeColor;
        private String orgRank;
        private String remarkColor;
        private String linkUrl;
    }
}
