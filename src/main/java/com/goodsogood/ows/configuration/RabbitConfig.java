package com.goodsogood.ows.configuration;

import org.springframework.amqp.core.*;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


/**
 * 消息队列配置文件
 *
 * <AUTHOR>
 */
@Configuration
public class RabbitConfig implements EnvironmentAware {


    private Environment environment;
    private EvalV2Config evalV2Config;

    public RabbitConfig(EvalV2Config evalV2Config) {
        this.evalV2Config = evalV2Config;
    }

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    /**
     * 辅助决策党委队列
     */
    @Bean("DSS_COMMITTEE_QUEUE")
    public Queue dssCommitteeQueue() {
        return new Queue(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-committee.queue")));
    }

    /**
     * 辅助决策党支部队列
     */
    @Bean("DSS_BRANCH_QUEUE")
    public Queue dssBranchQueue() {
        return new Queue(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-branch.queue")));
    }

    /**
     * 辅助决策人员队列
     */
    @Bean("DSS_USER_QUEUE")
    public Queue dssUserQueue() {
        return new Queue(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-user.queue")));
    }

    /**
     * 辅助决策重试队列
     */
    @Bean("DSS_RETRY_QUEUE")
    public Queue dssRetryQueue() {
        return new Queue(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-retry.queue")),
                true, false, false,
                this.getDeadMap(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-dead.routing-key"))));
    }

    /**
     * 辅助决策死信队列
     */
    @Bean("DSS_DEAD_QUEUE")
    public Queue dssDeadQueue() {
        return new Queue(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-dead.queue")));
    }

    /**
     * 辅助决策党支部交换机
     */
    @Bean("dssCommitteeExchange")
    public TopicExchange dssCommitteeExchange() {
        return new TopicExchange(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-committee.exchange")));
    }

    /**
     * 辅助决策党员交换机
     */
    @Bean("dssBranchExchange")
    public TopicExchange dssBranchExchange() {
        return new TopicExchange(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-branch.exchange")));
    }

    /**
     * 辅助决策人员交换机
     */
    @Bean("dssUserExchange")
    public TopicExchange dssUserExchange() {
        return new TopicExchange(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-user.exchange")));
    }

    /**
     * 辅助决策重试交换机
     */
    @Bean("dssRetryExchange")
    public TopicExchange dssRetryExchange() {
        return new TopicExchange(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-retry.exchange")));
    }

    /**
     * 辅助决策死信交换机
     */
    @Bean("dssDeadExchange")
    public TopicExchange dssDeadExchange() {
        return new TopicExchange(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-dead.exchange")));
    }


    /**
     * 辅助决策党委路由键 把队列与交换机绑定起来
     */
    @Bean("dssCommitteeBinding")
    public Binding dssCommitteeBinding() {
        return BindingBuilder.bind(dssCommitteeQueue()).to(dssCommitteeExchange()).with(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-committee.routing-key")));
    }

    /**
     * 辅助决策党支部路由键 把队列与交换机绑定起来
     */
    @Bean("dssBranchBinding")
    public Binding dssBranchBinding() {
        return BindingBuilder.bind(dssBranchQueue()).to(dssBranchExchange()).with(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-branch.routing-key")));
    }

    /**
     * 辅助决策党员路由键 把队列与交换机绑定起来
     */
    @Bean("dssUserBinding")
    public Binding dssUserBinding() {
        return BindingBuilder.bind(dssUserQueue()).to(dssUserExchange()).with(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-user.routing-key")));
    }

    /**
     * 辅助决策党员路由键 把队列与交换机绑定起来
     */
    @Bean("dssRetryBinding")
    public Binding dssRetryBinding() {
        return BindingBuilder.bind(dssRetryQueue()).to(dssRetryExchange()).with(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-retry.routing-key")));
    }

    /**
     * 辅助决策党员路由键 把队列与交换机绑定起来
     */
    @Bean("dssDeadBinding")
    public Binding dssDeadBinding() {
        return BindingBuilder.bind(dssDeadQueue()).to(dssDeadExchange()).with(Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-dead.routing-key")));
    }

    /**
     * 考核2.0 考核任务队列
     * @return queue
     */
    @Bean
    public Queue evalV2Queue() {
        return new Queue(evalV2Config.getQueue());
    }


    private Map<String, Object> getDeadMap(String routingKey) {
        Map<String, Object> params = new HashMap<>();
        //声明当前队列绑定的死信交换机
        params.put("x-dead-letter-exchange", Objects.requireNonNull(this.environment.getProperty("rabbit-value.dss-dead.exchange")));
        //声明当前队列的死信路由键
        params.put("x-dead-letter-routing-key", routingKey);
        return params;
    }
}
