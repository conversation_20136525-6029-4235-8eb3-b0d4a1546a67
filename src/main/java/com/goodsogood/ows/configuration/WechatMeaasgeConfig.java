package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = WechatMeaasgeConfig.PREFIX)
public class WechatMeaasgeConfig {

    public static final String PREFIX = "wechat-message";

    private String saasUrl;
    private String oldUrl;
    private String projectName;
    private String scope;
    private String version;
    private String routerType;
    private String loginType;
    private String redirect;
}
