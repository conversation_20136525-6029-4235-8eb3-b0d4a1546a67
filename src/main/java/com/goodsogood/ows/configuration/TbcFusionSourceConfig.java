package com.goodsogood.ows.configuration;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;

/**
 * @Author: mengting
 * @Date: 2022/4/13 15:52
 */
@Configuration
@MapperScan(basePackages = "com.goodsogood.ows.mapper.tbcFusion", sqlSessionFactoryRef = UserDataSourceConfig.SQL_SESSION_FACTORY)
public class TbcFusionSourceConfig {
    private static final String DATA_SOURCE = "tbcDataSource";

    private static final String TRANSACTION_MANAGER = "tbcTransactionManager";

    static final String SQL_SESSION_FACTORY = "tbcSqlSessionFactory";

    private static final String DATA_PREFIX = "spring.datasource.tbc";

    @Bean(name = DATA_SOURCE)
    @ConfigurationProperties(prefix = DATA_PREFIX)
    public DataSource tbcDataSource(){
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean(name = TRANSACTION_MANAGER)
    public DataSourceTransactionManager mysqlTransactionManager() {
        return new DataSourceTransactionManager(tbcDataSource());
    }

    @Bean(name = SQL_SESSION_FACTORY)
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier(DATA_SOURCE) DataSource dataSource)
            throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        return sessionFactory.getObject();
    }
}
