package com.goodsogood.ows.configuration

import io.swagger.annotations.ApiOperation
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component

/**
 *
 * <AUTHOR>
 * @date 2023-12-01 14:06:13
 * @description MeetingTypeConfig
 *
 */
@Component
@ConfigurationProperties(prefix = MeetingTypeConfig.PREFIX)
class MeetingTypeConfig {

    companion object {
        const val PREFIX = "meeting-type"
    }

    @get:ApiOperation("党支部党员大会")
    var partyId: Int? = null

    @get:ApiOperation(value = "党党支部委员会会议")
    var committeeId: Int? = null

    @get:ApiOperation(value = "党小组会")
    var groupId: Int? = null

    @get:ApiOperation(value = "党课ID")
    var lessonId: Int? = null

    @get:ApiOperation(value = "主题党日")
    var dayId: Int? = null

    @get:ApiOperation(value = "参加次数")
    var times: Int = 2

    @get:ApiOperation(value = "组织生活会类型ID")
    var meetingTypeId: Int? = null

    @get:ApiOperation(value = "组织生活会类型名称")
    var partyMeeting:Int?=null //党组会

    @get:ApiOperation(value = "组织生活会类型名称")
    var centerStudy:Int?=null //中心组织学习
}