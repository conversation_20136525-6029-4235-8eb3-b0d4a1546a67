package com.goodsogood.ows.configuration

import com.zaxxer.hikari.HikariDataSource
import org.apache.ibatis.session.SqlSessionFactory
import org.mybatis.spring.SqlSessionFactoryBean
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.jdbc.DataSourceBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.jdbc.datasource.DataSourceTransactionManager
import tk.mybatis.spring.annotation.MapperScan
import javax.sql.DataSource

/**
 * task-manager数据源
 * <AUTHOR>
 * @date 2022/3/25
 */
@Configuration
@MapperScan(basePackages = ["com.goodsogood.ows.mapper.task"],
    sqlSessionFactoryRef = TaskManagerConfig.SQL_SESSION_FACTORY)
class TaskManagerConfig {

    companion object {
        const val DATA_SOURCE = "taskDataSource"

        const val TRANSACTION_MANAGER = "taskTransactionManager"

        const val SQL_SESSION_FACTORY = "taskSqlSessionFactory"

        const val DATA_PREFIX = "spring.datasource.task"
    }


    @Bean(name = [DATA_SOURCE])
    @ConfigurationProperties(prefix = DATA_PREFIX)
    fun taskDataSource(): DataSource {
        return DataSourceBuilder.create().type(HikariDataSource::class.java).build()
    }

    @Bean(name = [TRANSACTION_MANAGER])
    fun mysqlTransactionManager(): DataSourceTransactionManager? {
        return DataSourceTransactionManager(taskDataSource())
    }

    @Bean(name = [SQL_SESSION_FACTORY])
    @Throws(Exception::class)
    fun mysqlSqlSessionFactory(@Qualifier(DATA_SOURCE) dataSource: DataSource?): SqlSessionFactory? {
        val sessionFactory = SqlSessionFactoryBean()
        sessionFactory.setDataSource(dataSource)
        return sessionFactory.getObject()
    }
}