package com.goodsogood.ows.configuration

import com.goodsogood.ows.model.antd.*
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component

@Component
@ConfigurationProperties(prefix = AdbQLConfig.PREFIX)
data class AdbQLConfig @JvmOverloads constructor(
    var sqls: Map<String, SqlConfig> = mutableMapOf(),
) {
    companion object {
        const val PREFIX = "adb-ql"

        /**
         * 获取列名
         * @return 列名列表
         */
        fun SqlConfig.columnName(key: String?): String {
            return if (key == null) {
                "未知列名"
            } else {
                params?.find { it[key] != null }?.get(key)?.name ?: key
            }
        }

        /**
         * 格式化日期
         * @param v 值
         * @return 格式化后的值
         */
        fun SqlParam.format(v: Any?): Any {
            if (v == null || v.toString().isEmpty()) {
                return ""
            }
            return when (type) {
                "date_time", "date" -> {
                    when (v) {
                        is String -> {
                            when (format) {
                                "yyyy-MM-dd" -> {
                                    v.toString().substring(0, 10)
                                }

                                "yyyy-MM-dd HH:mm:ss" -> {
                                    v.toString().substring(0, 19)
                                }

                                else -> {
                                    v
                                }
                            }
                        }

                        is java.sql.Timestamp, is java.sql.Date, is java.sql.Time -> {
                            val date = when (v) {
                                is java.sql.Timestamp -> {
                                    java.sql.Date(v.time)
                                }

                                is java.sql.Time -> {
                                    java.sql.Date(v.time)
                                }

                                else -> {
                                    v
                                }
                            }
                            val f = format ?: "yyyy-MM-dd HH:mm:ss"
                            val sdf = java.text.SimpleDateFormat(f)
                            sdf.format(date)
                        }

                        is java.time.LocalDate -> {
                            val f = format ?: "yyyy-MM-dd"
                            val sdf = java.time.format.DateTimeFormatter.ofPattern(f)
                            sdf.format(v)
                        }

                        is java.time.LocalDateTime -> {
                            val f = format ?: "yyyy-MM-dd HH:mm:ss"
                            val sdf = java.time.format.DateTimeFormatter.ofPattern(f)
                            sdf.format(v)
                        }

                        else -> {
                            v
                        }
                    }
                }

                else -> {
                    v
                }
            }
        }

        /**
         * 获取实际的query param 对象
         */
        fun getRealQueryParams(queryParam: QueryParam): AntDesignBase {
            return when (queryParam.type) {
                AntDesignType.Input.toString() -> {
                    Input(
                        name = queryParam.name,
                        param = queryParam.param,
                        index = queryParam.index,
                        type = AntDesignType.Input,
                        placeholder = queryParam.placeholder,
                        text = queryParam.text,
                    )
                }

                AntDesignType.Select.toString() -> {
                    Select(
                        name = queryParam.name,
                        param = queryParam.param,
                        index = queryParam.index,
                        type = AntDesignType.Select,
                        options = queryParam.options,
                        selectDefaultValue = queryParam.selectDefaultValue,
                        target = queryParam.target,
                    )
                }

                AntDesignType.RangePicker.toString() -> {
                    RangePicker(
                        name = queryParam.name,
                        param = queryParam.param,
                        index = queryParam.index,
                        type = AntDesignType.RangePicker,
                        rangePickerDefaultValue = queryParam.rangePickerDefaultValue,
                        dateFormat = queryParam.dateFormat,
                    )
                }

                else -> throw IllegalArgumentException("Unsupported type: ${queryParam.type}")
            }
        }
    }
}

data class SqlConfig @JvmOverloads constructor(
    // 原始sql
    var sql: String? = null,
    // 列映射
    var params: List<Map<String, SqlParam>>? = null,
    // 查询条件列
    var queryParams: List<QueryParam>? = null,
    // 分页sql
    var countSql: String? = null,
)

data class SqlParam @JvmOverloads constructor(
    var name: String? = null,
    var type: String? = null,
    var format: String? = null,
    var width: Int? = null,
    var index: Int? = null,
)

data class QueryParam @JvmOverloads constructor(
    var name: String? = null,
    var type: String? = null,
    var index: Int? = null,
    var target: Int? = null,
    var text: String? = null,
    var selectDefaultValue: String? = null,
    var options: List<Select.Option>? = mutableListOf(),
    var param: String? = null,
    var placeholder: String? = null,
    var dateFormat: String? = "YYYY-MM-DD",
    // 默认值，开始：结束
    var rangePickerDefaultValue: Pair<String?, String?>? = null
)

