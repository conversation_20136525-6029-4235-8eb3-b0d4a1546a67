package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022-04-15 16:32:55
 * @Description TimeIntervalConfig
 */
@Data
@Component
@ConfigurationProperties(prefix = TimeIntervalConfig.PREFIX)
public class TimeIntervalConfig {

    public static final String PREFIX = "time-interval";

    private List<Interval> intervals;

    @Data
    public static class Interval {
        private Integer interval;
        private List<Scope> scopes;
    }

    @Data
    public static class Scope {
        private String first;
        private String second;
    }
}
