package com.goodsogood.ows.configuration;

import com.goodsogood.ows.annotions.Logging;
import com.goodsogood.ows.utils.LoggingUtils;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.function.Supplier;

/**
 * 日志切面
 *
 * <AUTHOR>
 * @date 2020/12/15
 */
@Aspect
@Component
public class LoggingAspect {

    @Pointcut("@annotation(com.goodsogood.ows.annotions.Logging)")
    public void encryptedMessagePoint() {
    }

    @Around("encryptedMessagePoint()")
    public Object around(ProceedingJoinPoint pjd) {
        Object[] args = pjd.getArgs();
        Signature signature = pjd.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method targetMethod = methodSignature.getMethod();
        Logging annotation = targetMethod.getAnnotation(Logging.class);
        String remark = annotation.remark();
        if (StringUtils.isBlank(remark)) {
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append("类[")
                    .append(pjd.getTarget().getClass().getName())
                    .append("],")
                    .append("方法[")
                    .append(methodSignature.getMethod().getName())
                    .append("],");
            remark = stringBuffer.toString();
        }
        Supplier supplier = () -> {
            try {
                return pjd.proceed(args);
            } catch (Throwable throwable) {
                throw new InvokeException(throwable);
            }
        };
        return LoggingUtils.wrap(
                remark,
                supplier,
                annotation.forceReset(),
                args
        );
    }

    @Getter
    public static  class InvokeException extends RuntimeException{

        public final Throwable throwable;

        public InvokeException(Throwable throwable) {
            this.throwable = throwable;
        }
    }

}
