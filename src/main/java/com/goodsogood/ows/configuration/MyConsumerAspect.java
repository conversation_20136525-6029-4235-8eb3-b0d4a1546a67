package com.goodsogood.ows.configuration;

import com.goodsogood.ows.service.scoreManager.ding.DingTalkErrorForm;
import com.goodsogood.ows.service.scoreManager.ding.DingTalkHelper;
import lombok.extern.log4j.Log4j2;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/12/29
 */
@Component
@Aspect
@Log4j2
public class MyConsumerAspect {

    private final DingTalkHelper dingTalkHelper;

    @Autowired
    public MyConsumerAspect(DingTalkHelper dingTalkHelper) {
        this.dingTalkHelper = dingTalkHelper;
    }

    @Pointcut("@annotation(com.goodsogood.ows.annotions.MqConsumerErrorProcess)")
    private void cut() {
    }

    /**
     * 消费错误时抛出
     * 环绕切面
     */
    @Around("cut()")
    public Object advice(ProceedingJoinPoint joinPoint) {
        try {
            return joinPoint.proceed();
        } catch (Throwable e) {
            log.error("消费者错误->" + e.getMessage(), e);
            if (joinPoint instanceof MethodInvocationProceedingJoinPoint point) {
                MethodSignature signature = (MethodSignature) point.getSignature();
                Method method = signature.getMethod();
                DingTalkErrorForm errorForm = new DingTalkErrorForm();
                errorForm.setThrowable(e);
                errorForm.setExecuteArges(point.getArgs().length > 0 ?
                        point.getArgs()[0].toString() : "");
                errorForm.setExecuteMethod(method.toString());
                dingTalkHelper.addErrorMsg(errorForm);
            }
        }
        return null;
    }
}
