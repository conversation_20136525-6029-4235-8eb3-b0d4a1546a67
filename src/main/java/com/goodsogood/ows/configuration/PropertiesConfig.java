package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 消息提醒排除组织
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = PropertiesConfig.PREFIX)
public class PropertiesConfig {

    public static final String PREFIX = "properties";

    /** 1号消息提醒排除的组织 */
    private List<Long> excludeOrg;

    /** 消息推送量 */
    private Integer pushSize;

    /** 微信渠道 */
    private Integer wechatChannel;

    /** 短信渠道 */
    private Integer smsChannel;

    /** 来源 */
    private String source;

    /** 工作报告消息模板 */
    private Integer workReportTemplateId;

}
