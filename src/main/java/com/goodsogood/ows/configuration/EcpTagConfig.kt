package com.goodsogood.ows.configuration

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component

/**
 *
 * <AUTHOR>
 * @Date 2023-03-14 15:42:03
 * @Description EcpTagConfig
 *
 */
@Component
@ConfigurationProperties(prefix = EcpTagConfig.PREFIX)
class EcpTagConfig {

    companion object {
        const val PREFIX = "ecp-tag"
    }

    // 专卖
    var monopolyId: Long? = null

    // 营销
    var marketingId: Long? = null

    // 烟叶
    var tobaccoId: Long? = null

    // 综合
    var synthesisId: Long? = null

    //机关
    var institutionCode: Int? = null

    //专业分公司
    var companyCode: Int? = null

    //两烟产区
    var tobaccoCode: Int? = null

    //纯销区
    var marketingCode: Int? = null

    // 党务干部交流默认得分年份
    var year: Int? = null
}