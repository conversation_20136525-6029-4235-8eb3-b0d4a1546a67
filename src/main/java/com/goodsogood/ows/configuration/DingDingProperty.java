package com.goodsogood.ows.configuration;

import com.goodsogood.ows.common.DingDingConstant;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/24
 * @description class DingdingProperty
 */
@Data
@Configuration
@ConfigurationProperties(prefix = DingDingProperty.PROPERTIES)
public class DingDingProperty {

    /**
     * 使用的是区县名称作为名称，主要是防止多个局点部署时忘了更新节点名称
     */
    @Value("${dingding.server}")
    @NotNull
    private String server;

    protected final static String PROPERTIES = "dd";
    private String pushModel = "{\"msgtype\": \"markdown\",\"markdown\": {\"title\":\"SAS定时任务完成\",\"text\":\"## SAS定时任务完成 \\n > 来自-微服务：["
            + DingDingConstant.KEY_SOURCE
            + "]\\n\\n- trackId：["
            + DingDingConstant.KEY_TRACK_ID
            + "]\\n- 告警时间：["
            + DingDingConstant.KEY_TIME
            + "]\\n- 消息："
            + DingDingConstant.KEY_MSG
            + " \\n\\n"
            + ""
            + "\"},\"at\": {\"atMobiles\": ["
            + ""
            + "],\"isAtAll\": false}}";
    private String accessToken = "802f76bb8758b6e33ff5304139865cf2c470a0920a6f9ff62ceaec11d49e6c04";
    private List<String> atUsers = new ArrayList<>();
}
