package com.goodsogood.ows.configuration

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component

/**
 * 烟草考核2.0的配置
 */
@Component
@ConfigurationProperties(prefix = EvalV2Config.PREFIX)
class EvalV2Config {
    private val log = org.slf4j.LoggerFactory.getLogger(EvalV2Config::class.java)

    companion object {
        const val PREFIX = "eval-v2"
    }
    // ------- mq配置 -------------
    /**
     * 消费者并发数量
     */
    var concurrency = 10

    /**
     * 队列名称
     */
    var queue: String = "eval-v2-strategy.queue"

    /**
     * 开启消费者
     */
    var run = true
    // ---------- 其他配置 -----------
    /**
     * 任务id（党组理论学习中心组学习年度计划、总结 对应的任务id）
     */
    var taskId = 0L
}
