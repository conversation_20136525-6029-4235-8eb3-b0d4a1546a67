package com.goodsogood.ows.configuration

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component

@Component
@ConfigurationProperties(prefix = TagConfig.PREFIX)
class TagConfig {
    companion object {
        const val PREFIX = "tag"
    }

    var readClass:Long?=null //读书班
    var constitution:Long?=null //党章
    var topics:Long?=null //第一议题
    var fusions:Long?=null //党业融合
    var educations:Long?=null //主题教育
}


///**
// * Meeting 类型配置
// */
//@Component
//@ConfigurationProperties(prefix = MeetingTypeConfig.PREFIX)
//class MeetingTypeConfig {
//    companion object {
//        const val PREFIX = "meeting-type"
//    }
//    var partyMeeting:Int?=null //党组会
//    var centerStudy:Int?=null //中心组织学习
//}