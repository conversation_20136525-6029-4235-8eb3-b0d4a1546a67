package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 烟草党性体验
 */
@Data
@Component
@ConfigurationProperties(prefix = ExperienceConfig.PREFIX)
public class ExperienceConfig {

    public static final String PREFIX = "experience";

    /**
     * 部署区县Id
     */
    private Long regionId;

    /**
     * 党委配置那些选项
     */
    private Map<Integer, Option> ruleOption;

    /**
     * 最大星级
     */
    private Integer maxStar;

    /**
     * 联系登录最大天数
     */
    private Integer maxDay;

    /**
     * 活跃程度
     */
    private String startTime;

    /**
     * 联系登录最大天数
     */
    private String endTime;


    private List<StarLevel> starLevels;

    private Quarter quarters;

    /**
     * 党委配置那些选项
     */
    private Map<Integer, OptionRuleTag> ruleRuleTag;

    /**
     * 排除这些项 默认不计算
     */
    private List<Integer> excludeRuleIds;

    @Data
    public static class Option {
        /**
         * 数据库的顶级ruleId
         */
        private Integer ruleId;

        /**
         * 数据库的顶级ruleName
         */
        private String ruleName;

        /**
         * 5星计算规则
         */
        private double fiveStarRule;
        /**
         * 4星计算规则
         */
        private double fourStarRule;
        /**
         * 3星计算规则
         */
        private double threeStarRule;
        /**
         * 2星计算规则
         */
        private double twoStarRule;

        /**
         * 其它规则
         */
        private double otherRule;
    }

    @Data
    public static class OptionRuleTag {
        /**
         * 数据库的顶级ruleId
         */
        private Integer ruleId;

        /**
         * 标签列表
         */
        private List<String> tagName;

        /**
         * 标签第一阶段
         */
        private String tag1;

        /**
         * 标签第二阶段
         */
        private String tag2;

        /**
         * 标签第三阶段
         */
        private String tag3;
    }

    @Data
    public static class StarLevel {

        private Integer star;

        /**
         * 星级对应当月登录总天数最小值
         */
        private Integer mMin;

        /**
         * 星级对应当月登录总天数最大值
         */
        private Integer mMax;

        /**
         * 星级对应当季度登录总天数最小值
         */
        private Integer qMin;

        /**
         * 星级对应当季度登录总天数最大值
         */
        private Integer qMax;

        /**
         * 星级对应当年登录总天数最小值
         */
        private Integer yMin;

        /**
         * 星级对应当年登录总天数最大值
         */
        private Integer yMax;

        /**
         * 最小
         */
        private List<Integer> days;

    }

    @Data
    public static class Quarter {
        private List<Integer> first;
        private List<Integer> second;
        private List<Integer> third;
        private List<Integer> four;
    }

}
