package com.goodsogood.ows.configuration;

import org.jetbrains.annotations.NotNull;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.util.Pair;
import org.springframework.lang.NonNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.StreamSupport;

/**
 * <AUTHOR>
 * @date 2023/3/27
 * @description class CachingApplicationConfiguration cache的配置（使用内存节点） // 最新代码切换的redis缓存（2024/2/2
 */
@Configuration
public class CachingApplicationConfiguration {

    //    @Bean
//    public CacheManager cacheManager() {
//        return new ConcurrentMapCacheManager() {
//            @NotNull
//            @Override
//            protected Cache createConcurrentMapCache(@NotNull String name) {
//                return new ConcurrentMapCollectionHandlingDecoratedCache(super.createConcurrentMapCache(name));
//            }
//        };
//    }
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        return RedisCacheManager.builder(redisConnectionFactory).build();
    }
}

/**
 * class ConcurrentMapCollectionHandlingDecoratedCache extends CollectionHandlingDecoratedCache {
 * <p>
 * protected ConcurrentMapCollectionHandlingDecoratedCache(final Cache cache) {
 * super(cache);
 * }
 *
 * @Override
 * @SuppressWarnings("all") protected boolean areAllKeysPresentInCache(@NotNull Iterable<?> keys) {
 * ConcurrentMap nativeCache = (ConcurrentMap) getNativeCache();
 * return StreamSupport.stream(keys.spliterator(), false).allMatch(nativeCache::containsKey);
 * }
 * }
 * <p>
 * abstract class CollectionHandlingDecoratedCache implements Cache {
 * private final Cache cache;
 * <p>
 * protected CollectionHandlingDecoratedCache(@NotNull Cache cache) {
 * this.cache = cache;
 * }
 * <p>
 * protected Cache getCache() {
 * return this.cache;
 * }
 * @NotNull
 * @Override public String getName() {
 * return getCache().getName();
 * }
 * @NotNull
 * @Override public Object getNativeCache() {
 * return getCache().getNativeCache();
 * }
 * <p>
 * protected abstract boolean areAllKeysPresentInCache(Iterable<?> keys);
 * @SuppressWarnings("unused") protected int sizeOf(Iterable<?> iterable) {
 * return Long.valueOf(StreamSupport.stream(iterable.spliterator(), false).count()).intValue();
 * }
 * <p>
 * protected <T> List<T> toList(Iterable<T> iterable) {
 * return StreamSupport.stream(iterable.spliterator(), false).collect(Collectors.toList());
 * }
 * @Override
 * @SuppressWarnings("all") public ValueWrapper get(Object key) {
 * if (key instanceof Iterable) {
 * Iterable<?> keys = (Iterable<?>) key;
 * if (!areAllKeysPresentInCache(keys)) {
 * return null;
 * }
 * List<Object> values = new ArrayList<>();
 * for (Object singleKey : keys) {
 * values.add(getCache().get(singleKey).get());
 * }
 * return () -> values;
 * }
 * return getCache().get(key);
 * }
 * @Override
 * @SuppressWarnings("unchecked") public <T> T get(@NotNull Object key, Class<T> type) {
 * <p>
 * if (key instanceof Iterable) {
 * return (T) Optional.ofNullable(get(key)).map(Cache.ValueWrapper::get).orElse(null);
 * }
 * <p>
 * return getCache().get(key, type);
 * }
 * @Override
 * @SuppressWarnings("all") public <T> T get(Object key, Callable<T> valueLoader) {
 * return (T) get(key, Object.class);
 * }
 * @Override public void put(@NonNull Object key, Object value) {
 * if (key instanceof Iterable) {
 * pairsFromKeysAndValues(toList((Iterable<?>) key), toList((Iterable<?>) value))
 * .forEach(pair -> getCache().put(pair.getFirst(), pair.getSecond()));
 * } else {
 * getCache().put(key, value);
 * }
 * }
 * @Override public ValueWrapper putIfAbsent(@NotNull Object key, Object value) {
 * if (key instanceof Iterable) {
 * return () -> pairsFromKeysAndValues(toList((Iterable<?>) key), toList((Iterable<?>) value)).stream()
 * .map(pair -> getCache().putIfAbsent(pair.getFirst(), pair.getSecond()))
 * .collect(Collectors.toList());
 * }
 * <p>
 * return getCache().putIfAbsent(key, value);
 * }
 * @Override
 * @SuppressWarnings("all") public void evict(@NotNull Object key) {
 * if (key instanceof Iterable) {
 * StreamSupport.stream(((Iterable) key).spliterator(), false).forEach(getCache()::evict);
 * } else {
 * getCache().evict(key);
 * }
 * }
 * @Override public void clear() {
 * getCache().clear();
 * }
 * <p>
 * private <K, V> List<Pair<K, V>> pairsFromKeysAndValues(List<K> keys, List<V> values) {
 * final int keysSize = keys.size();
 * return IntStream.range(0, keysSize)
 * .mapToObj(index -> Pair.of(keys.get(index), values.get(index)))
 * .collect(Collectors.toList());
 * <p>
 * }
 * <p>
 * }
 */