package com.goodsogood.ows.configuration;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/1/8
 * Description:
 */
@Configuration
public class MyMongoTemplateConfiguration {

    private final MongoCollectionNameConfig mongoCollectionNameConfig;

    public MyMongoTemplateConfiguration(MongoCollectionNameConfig mongoCollectionNameConfig) {
        this.mongoCollectionNameConfig = mongoCollectionNameConfig;
    }

    @Bean
    public MyMongoTemplate createMyMongoTemplate(){
        if(StringUtils.isNotBlank(mongoCollectionNameConfig.getUserName())&&StringUtils.isNotBlank(mongoCollectionNameConfig.getPassword())){
            return new MyMongoTemplate(mongoCollectionNameConfig.getHost(),mongoCollectionNameConfig.getPort(),mongoCollectionNameConfig.getDatabaseName(),mongoCollectionNameConfig.getUserName(),mongoCollectionNameConfig.getPassword());
        }else{
            return new MyMongoTemplate(mongoCollectionNameConfig.getHost(),mongoCollectionNameConfig.getPort(),mongoCollectionNameConfig.getDatabaseName());
        }
    }

}
