package com.goodsogood.ows.configuration;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: mengting
 * @Date: 2022/7/19 10:49
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "learn")
public class LearnScoreConfiguration {
    private LearnVo training;//每日一练
    private LearnVo examinMonth;//每月一测
    private LearnVo examinQuart;//每季一考

    @Data
    public static class LearnVo{
        private Long id;//试卷id
        private String explain;//说明
        private Double score;//分值
        private String remark;
    }


}
