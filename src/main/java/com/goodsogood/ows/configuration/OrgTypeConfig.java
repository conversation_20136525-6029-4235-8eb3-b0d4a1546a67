package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 组织类型
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = OrgTypeConfig.PREFIX)
public class OrgTypeConfig {

    public static final String PREFIX = "org-type";

    //党委
    private List<Integer> communistChild;
    //党总支
    private List<Integer> generalBranchChild;
    //党支部
    private List<Integer> branchChild;
    //党组
    private List<Integer> partyGroup;
    //委员会
    private List<Integer> committee;
    //不统计党组织类型
    private List<Integer> noStatisticsChild;
    //党小组
    private List<Integer> communistGroup;
    //顶级党委
    private List<Integer> topGroup;

    public List<Integer> getBasicAndTopChild(){
        List<Integer> result=getBasicChild();
        result.addAll(topGroup);
        return result;
    }

    /**
     * 包含党支部、党委、党总支
     *
     * @return
     */
    public List<Integer> getBasicChild() {
        List<Integer> basicChild = new ArrayList<>();
        basicChild.addAll(communistChild);
        basicChild.addAll(generalBranchChild);
        basicChild.addAll(branchChild);
        return basicChild;
    }

    /**
     * 包含党委和党总支
     *
     * @return
     */
    public List<Integer> getCommunistAndGeneral() {
        List<Integer> oneLevel = new ArrayList<>();
        oneLevel.addAll(communistChild);
        oneLevel.addAll(generalBranchChild);
        return oneLevel;
    }

    /**
     * 烟草监督预警调用 是否为党委概念
     * 包含 党委/党总支
     * 后面顶级党委 也跟党委/党总支一起计算规则
     * @return
     */
    public boolean checkIsCommunist(Integer typeChild) {
        return communistChild.contains(typeChild)
                || generalBranchChild.contains(typeChild)||topGroup.contains(typeChild);
    }

    /**
     * 烟草监督预警用 是否为党支部概念
     * 包含  党支部/党小组
     *
     * @param typeChild
     * @return
     */
    public boolean checkIsBranch(Integer typeChild) {
        return branchChild.contains(typeChild) || communistGroup.contains(typeChild);
    }

    /**
     * 烟草监督预警用 仅查询是否为党支部
     * 包含  党支部
     *
     * @param typeChild
     * @return
     */
    public boolean checkIsOnlyBranch(Integer typeChild) {
        return branchChild.contains(typeChild);
    }

    /**
     * 烟草监督预警用 是否为党支部概念
     * 包含  党支部/党小组
     *
     * @param typeChild
     * @return
     */
    public boolean checkIsSingleBranch(Integer typeChild) {
        return branchChild.contains(typeChild) ;
    }


    /**
     * 烟草监督预警用 是否为党支部概念
     * 包含  党小组
     *
     * @param typeChild
     * @return
     */
    public boolean checkIsCommunistGroup(Integer typeChild) {
        return communistGroup.contains(typeChild) ;
    }

    /**
     * 党委、党总支、顶级党委
     *
     * @return
     */
    public List<Integer> getCommittee() {
        List<Integer> basicChild = new ArrayList<>();
        basicChild.addAll(communistChild);
        basicChild.addAll(generalBranchChild);
        basicChild.addAll(topGroup);
        return basicChild;
    }

    /**
     * 党支部、党小组
     *
     * @return
     */
    public List<Integer> getBranch() {
        List<Integer> basicChild = new ArrayList<>();
        basicChild.addAll(branchChild);
        basicChild.addAll(communistGroup);
        return basicChild;
    }

    public List<Integer> getCommunistChild() {
        return communistChild;
    }

    public void setCommunistChild(List<Integer> communistChild) {
        this.communistChild = communistChild;
    }

    public List<Integer> getGeneralBranchChild() {
        return generalBranchChild;
    }

    public void setGeneralBranchChild(List<Integer> generalBranchChild) {
        this.generalBranchChild = generalBranchChild;
    }

    public List<Integer> getBranchChild() {
        return branchChild;
    }

    public void setBranchChild(List<Integer> branchChild) {
        this.branchChild = branchChild;
    }

    public List<Integer> getPartyGroup() {
        return partyGroup;
    }

    public void setPartyGroup(List<Integer> partyGroup) {
        this.partyGroup = partyGroup;
    }

    public void setCommittee(List<Integer> committee) {
        this.committee = committee;
    }

    public List<Integer> getNoStatisticsChild() {
        return noStatisticsChild;
    }

    public void setNoStatisticsChild(List<Integer> noStatisticsChild) {
        this.noStatisticsChild = noStatisticsChild;
    }

    public List<Integer> getCommunistGroup() {
        return communistGroup;
    }

    public void setCommunistGroup(List<Integer> communistGroup) {
        this.communistGroup = communistGroup;
    }

    public List<Integer> getTopGroup() {
        return topGroup;
    }

    public void setTopGroup(List<Integer> topGroup) {
        this.topGroup = topGroup;
    }
}
