package com.goodsogood.eps.jackson

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.SerializerProvider
import java.io.IOException
import java.math.RoundingMode
import java.text.DecimalFormat
import kotlin.jvm.Throws


/**
 * <AUTHOR>
 * @date 2020/8/24
 * @description class jackson 序列化成两位（到三位）小数
 */
class CustomDoubleSerialize : JsonSerializer<Double>() {
    private val df = DecimalFormat("#0.00#").also { it.roundingMode = RoundingMode.HALF_UP }

    @Throws(IOException::class, JsonProcessingException::class)
    override fun serialize(
        value: Double?, jgen: JsonGenerator,
        provider: SerializerProvider?
    ) {
        if (value != null) {
            jgen.writeString(df.format(value))
        }
    }

}