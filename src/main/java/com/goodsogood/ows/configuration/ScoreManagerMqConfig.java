package com.goodsogood.ows.configuration;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/12/15
 */
@Configuration
public class ScoreManagerMqConfig {

    /* 创建交换机 */
    @Bean
    public TopicExchange generateTopicExchange() {
        return new TopicExchange(ScoreManagerConfig.SCORE_MANAGER_TOPIC_EXCHANGE_KEY);
    }

    /* 交换机创建完成 */

    /* 创建队列 */
    @Bean
    public Queue generateQueueBaseOrgPeriodExpireNotCreate() {
        return new Queue(ScoreManagerConfig.BASE_ORG_PERIOD_EXPIRE_NOT_CREATE);
    }

    @Bean
    public Queue generateQueueBaseOrgPeriodExistLeader() {
        return new Queue(ScoreManagerConfig.BASE_ORG_PERIOD_EXIST_LEADER);
    }

    @Bean
    public Queue generateQueueBaseOrgPeoplePunish() {
        return new Queue(ScoreManagerConfig.BASE_ORG_PEOPLE_PUNISH);
    }

    @Bean
    public Queue generateQueueBaseOrgPeopleLoginRatio() {
        return new Queue(ScoreManagerConfig.BASE_ORG_PEOPLE_LOGIN_RATIO);
    }

    @Bean
    public Queue generateQueueBaseOrgPeopleNewsRead() {
        return new Queue(ScoreManagerConfig.BASE_ORG_PEOPLE_NEWS_READ);
    }

    @Bean
    public Queue generateQueuePartyOrgExistPeriodLeader() {
        return new Queue(ScoreManagerConfig.PARTY_ORG_EXIST_PERIOD_LEADER);
    }

    @Bean
    public Queue generateQueuePartyOrgPeriodExpireNotCreate() {
        return new Queue(ScoreManagerConfig.PARTY_ORG_PERIOD_EXPIRE_NOT_CREATE);
    }

    @Bean
    public Queue generateQueuePartyOrgPeoplePunish() {
        return new Queue(ScoreManagerConfig.PARTY_ORG_PEOPLE_PUNISH);
    }

    @Bean
    public Queue generateQueuePartyGroupExistLeaderGroup() {
        return new Queue(ScoreManagerConfig.PARTY_GROUP_EXIST_LEADER_GROUP);
    }

    @Bean
    public Queue generateQueuePartyGroupExistPunish() {
        return new Queue(ScoreManagerConfig.PARTY_GROUP_EXIST_PUNISH);
    }

    @Bean
    public Queue generateQueuePartyGroupExistWorkContact() {
        return new Queue(ScoreManagerConfig.PARTY_GROUP_EXIST_WORK_CONTACT);
    }

    @Bean
    public Queue generateQueuePartyGroupExistBaseContact() {
        return new Queue(ScoreManagerConfig.PARTY_GROUP_EXIST_BASE_CONTACT);
    }

    /**
     * 党员每日登录加积分的队列
     */
    @Bean
    public Queue generateQueuePartyUserLogin() {
        return new Queue(ScoreManagerConfig.PARTY_MEMBER_LOGIN);
    }

    /* 队列创建完成 */

    /* 队列关联到交换机 */

    /**
     * 党员每日登录队列绑定到交换机
     * 并监听 ScoreManagerConfig.PARTY_MEMBER_LOGIN
     */
    @Bean
    public Binding bindingExchangePartyUserLogin() {
        return BindingBuilder.bind(generateQueuePartyUserLogin()).
                to(generateTopicExchange()).with(ScoreManagerConfig.PARTY_MEMBER_LOGIN);
    }

    @Bean
    public Binding bindingExchangeBaseOrgPeriodExpireNotCreate() {
        return BindingBuilder.bind(generateQueueBaseOrgPeriodExpireNotCreate()).
                to(generateTopicExchange()).with(ScoreManagerConfig.BASE_ORG_PERIOD_EXPIRE_NOT_CREATE);
    }

    @Bean
    public Binding bindingExchangeBaseOrgPeriodExistLeader() {
        return BindingBuilder.bind(generateQueueBaseOrgPeriodExistLeader()).
                to(generateTopicExchange()).with(ScoreManagerConfig.BASE_ORG_PERIOD_EXIST_LEADER);
    }

    @Bean
    public Binding bindingExchangeBaseOrgPeoplePunish() {
        return BindingBuilder.bind(generateQueueBaseOrgPeoplePunish()).
                to(generateTopicExchange()).with(ScoreManagerConfig.BASE_ORG_PEOPLE_PUNISH);
    }

    @Bean
    public Binding bindingExchangeBaseOrgPeopleLoginRatio() {
        return BindingBuilder.bind(generateQueueBaseOrgPeopleLoginRatio()).
                to(generateTopicExchange()).with(ScoreManagerConfig.BASE_ORG_PEOPLE_LOGIN_RATIO);
    }

    @Bean
    public Binding bindingExchangeBaseOrgPeopleNewsRead() {
        return BindingBuilder.bind(generateQueueBaseOrgPeopleNewsRead()).
                to(generateTopicExchange()).with(ScoreManagerConfig.BASE_ORG_PEOPLE_NEWS_READ);
    }

    @Bean
    public Binding bindingExchangePartyOrgExistPeriodLeader() {
        return BindingBuilder.bind(generateQueuePartyOrgExistPeriodLeader()).
                to(generateTopicExchange()).with(ScoreManagerConfig.PARTY_ORG_EXIST_PERIOD_LEADER);
    }

    @Bean
    public Binding bindingExchangePartyOrgPeriodExpireNotCreate() {
        return BindingBuilder.bind(generateQueuePartyOrgPeriodExpireNotCreate()).
                to(generateTopicExchange()).with(ScoreManagerConfig.PARTY_ORG_PERIOD_EXPIRE_NOT_CREATE);
    }

    @Bean
    public Binding bindingExchangePartyOrgPeoplePunish() {
        return BindingBuilder.bind(generateQueuePartyOrgPeoplePunish()).
                to(generateTopicExchange()).with(ScoreManagerConfig.PARTY_ORG_PEOPLE_PUNISH);
    }

    @Bean
    public Binding bindingExchangePartyGroupExistLeaderGroup() {
        return BindingBuilder.bind(generateQueuePartyGroupExistLeaderGroup()).
                to(generateTopicExchange()).with(ScoreManagerConfig.PARTY_GROUP_EXIST_LEADER_GROUP);
    }

    @Bean
    public Binding bindingExchangePartyGroupExistPunish() {
        return BindingBuilder.bind(generateQueuePartyGroupExistPunish()).
                to(generateTopicExchange()).with(ScoreManagerConfig.PARTY_GROUP_EXIST_PUNISH);
    }

    @Bean
    public Binding bindingExchangePartyGroupExistWorkContact() {
        return BindingBuilder.bind(generateQueuePartyGroupExistWorkContact()).
                to(generateTopicExchange()).with(ScoreManagerConfig.PARTY_GROUP_EXIST_WORK_CONTACT);
    }

    @Bean
    public Binding bindingExchangePartyGroupExistBaseContact() {
        return BindingBuilder.bind(generateQueuePartyGroupExistBaseContact()).
                to(generateTopicExchange()).with(ScoreManagerConfig.PARTY_GROUP_EXIST_BASE_CONTACT);
    }
    /* 队列关联到交换机完成 */
}
