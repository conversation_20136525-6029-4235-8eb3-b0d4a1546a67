package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = PeriodPositionConfig.PREFIX)
public class PeriodPositionConfig {

    public static final String PREFIX = "period-position";

    private List<String> branch;

    private List<String> communist;
}
