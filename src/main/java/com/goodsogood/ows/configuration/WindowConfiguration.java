package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Data
@Component
@ConfigurationProperties(prefix = WindowConfiguration.PREFIX)
public class WindowConfiguration {
    public static final String PREFIX = "window-of-org";
    private Map<Long, TypeStatus> map;


    @Data
    public static class TypeStatus{
        private String typeIds;
        private String status;
    }

}
