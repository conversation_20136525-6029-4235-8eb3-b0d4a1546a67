package com.goodsogood.ows.configuration;

import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@Log4j2
@ConfigurationProperties(prefix = ExcelExportInfoConfig.PREFIX)
@Data
public class ExcelExportInfoConfig {

    public static final String PREFIX = "excel-config";

    /**
     * 文件服务的名称
     */
    private String fileServices;

    /**
     * 项目配置存入excel表格的路径
     */
    private String dirPath;
}
