package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 定时任务配置需要的区县列表
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = RegionListConfig.PREFIX)
public class RegionListConfig {

    public static final String PREFIX = "region-list";

    /**
     * 1号和20号工作提醒
     */
    private List<Long> remind;

    /**
     * 电子党务报告
     */
    private List<Long> report;
}
