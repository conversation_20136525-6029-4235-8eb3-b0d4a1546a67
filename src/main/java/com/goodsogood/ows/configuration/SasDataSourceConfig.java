package com.goodsogood.ows.configuration;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;

/**
 * Sas数据源
 * <AUTHOR>
 * @date 2019/11/13
 */
@Configuration
@MapperScan(basePackages = "com.goodsogood.ows.mapper.sas", sqlSessionFactoryRef = SasDataSourceConfig.SQL_SESSION_FACTORY)
public class SasDataSourceConfig {

    @Autowired
    private SqlSessionFactoryConfigurer sqlSessionFactoryConfigurer;

    private static final String DATA_SOURCE = "sasDataSource";

    private static final String TRANSACTION_MANAGER = "sasTransactionManager";

    static final String SQL_SESSION_FACTORY = "sasSqlSessionFactory";

    private static final String DATA_PREFIX = "spring.datasource.sas";


    @Bean(name = DATA_SOURCE)
    @ConfigurationProperties(prefix = DATA_PREFIX)
    @Primary // 设置为主数据库（必须并唯一）
    public DataSource SasDataSource(){
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean(name = TRANSACTION_MANAGER)
    @Primary // 设置为主数据库（必须并唯一）
    public DataSourceTransactionManager mysqlTransactionManager() {
        return new DataSourceTransactionManager(SasDataSource());
    }

    @Bean(name = SQL_SESSION_FACTORY)
    @Primary // 设置为主数据库（必须并唯一）
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier(DATA_SOURCE) DataSource dataSource)
            throws Exception {
        return sqlSessionFactoryConfigurer.createSqlSessionFactory(dataSource);
    }
}
