package com.goodsogood.ows.configuration;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 模块名称配置
 *
 * <AUTHOR>
 * @create 2019-08-01
 **/
@Component
@ConfigurationProperties(prefix = TogServicesConfig.PREFIX)
@Data
public class TogServicesConfig {

     public static final String PREFIX = "tog-services";

     /** 文件中心 */
     @JsonProperty("file-center")
     private String fileCenter;

     /** 网宣平台 */
     @JsonProperty("cms-plat")
     private String cmsPlat;

     /** 工作流服务 */
     private String workflow;

     /** 活动平台 */
     @JsonProperty("activity-plat")
     private String activityPlat;

     /** 积分中心 */
     @JsonProperty("credit-center")
     private String creditCenter;

     /** 用户中心 */
     @JsonProperty("user-center")
     private String userCenter;

     /** 消息推送中心 */
     @JsonProperty("push-center")
     private String pushCenter;

     /** 组织生活模块 */
     private String meeting;

     /** 党费系统 */
     private String ppmd;

     /**考核模块 */
     @JsonProperty("eval")
     private String eval;
}
