package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @date 08/03/2018
 * @description 读取yml统计相关配置
 */
@Component
@ConfigurationProperties(prefix = "sas.org-life")
@Validated
@Data
public class SasOrgLifeConfig {

    /**
     * 统计数据的最早年份
     */
    private Integer minYear = 2018;

    /**
     * 批量处理数量
     */
    private Integer batchNum = 1000;

    /**
     * 最大条数
     */
    private Integer maxSize = 9999999;
}
