package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 20号工作提醒配置文件
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = WorkRemindMessageConfig.PREFIX)
public class WorkRemindMessageConfig {

    public static final String PREFIX = "work-remind-message";

    private Integer templateId;

    private SeniorWorkRemind seniorWorkRemind;
    private GeneraWorkRemind generaWorkRemind;
    private WorkCommitteeWorkRemind workCommitteeWorkRemind;

    /**
     * 高级管理员工作提醒
     */
    @Data
    public static class SeniorWorkRemind {
        private String title;
        private String titleColor;
        private String ppmdCollect;
        private String orgLiftCollect;
        private String contentColor;
        private String timeColor;
        private String remark;
        private String remarkColor;
        private String linkUrl;
    }

    /**
     * 一般管理员工作提醒
     */
    @Data
    public static class GeneraWorkRemind {
        private String title;
        private String titleColor;
        private String ppmdCollect;
        private String orgLiftCollect;
        private String contentColor;
        private String timeColor;
        private String remark;
        private String remarkColor;
        private String linkUrl;
    }

    /**
     * 本月工作提醒（工委管理员）
     */
    @Data
    public static class WorkCommitteeWorkRemind {
        private String title;
        private String titleColor;
        private String ppmdCollect;
        private String orgLiftCollect;
        private String periodCollect;
        private String contentColor;
        private String timeColor;
        private String remark;
        private String remarkColor;
        private String linkUrl;
    }
}
