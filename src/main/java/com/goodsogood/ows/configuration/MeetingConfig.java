package com.goodsogood.ows.configuration;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 活动类型
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = MeetingConfig.PREFIX)
public class MeetingConfig {
    /** 计算是完成时的时间周期：1.月 */
    public static final int PERIOD_MONTH = 1;
    /** 计算是完成时的时间周期：2.季度 */
    public static final int PERIOD_QUARTER = 2;

    public static final String PREFIX = "meeting";


    /** 各区县对应的typeId */
    private Map<Long, List<MeetingType>> type = new HashMap<>();
    /**
     * 活动类型
     *
     * <p>2 党支部党员大会开展情况
     *
     * <p>3 党支部委员会会议开展情况
     *
     * <p>4 党小组会开展情况
     *
     * <p>5 党课开展情况
     *
     * <p>6主题党日开展情况
     *
     */
    public static final int PARTY_MEMBER = 2;
    public static final int PARTY_COMMITTEE = 3;
    public static final int PARTY_GROUP = 4;
    public static final int PARTY_LECTURE = 5;
    public static final int PARTY_THEME_DAY = 6;

    {
        List<MeetingType> list =
                Arrays.asList(
                        MeetingType.builder().mappingType(PARTY_MEMBER).period(PERIOD_QUARTER).typeId(60L).type("党支部党员大会").build(),
                        MeetingType.builder().mappingType(PARTY_COMMITTEE).period(PERIOD_MONTH).typeId(61L).type("党支部委员会会议").build(),
                        MeetingType.builder().mappingType(PARTY_GROUP).period(PERIOD_MONTH).typeId(62L).type("党小组会").build(),
                        MeetingType.builder().mappingType(PARTY_LECTURE).period(PERIOD_QUARTER).typeId(63L).type("党课").build(),
                        MeetingType.builder().mappingType(PARTY_THEME_DAY).period(PERIOD_MONTH).typeId(64L).type("主题党日").build());
        type.put(3L, list);
        List<MeetingType> listCqyc =
                Arrays.asList(
                        MeetingType.builder().mappingType(PARTY_MEMBER).period(PERIOD_QUARTER).typeId(1L).type("党支部党员大会").build(),
                        MeetingType.builder().mappingType(PARTY_COMMITTEE).period(PERIOD_MONTH).typeId(2L).type("党支部委员会会议").build(),
                        MeetingType.builder().mappingType(PARTY_GROUP).period(PERIOD_MONTH).typeId(3L).type("党小组会").build(),
                        MeetingType.builder().mappingType(PARTY_LECTURE).period(PERIOD_QUARTER).typeId(4L).type("党课").build(),
                        MeetingType.builder().mappingType(PARTY_THEME_DAY).period(PERIOD_MONTH).typeId(5L).type("主题党日").build());
        type.put(86L, listCqyc);
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class MeetingType {

        /**
         * 映射的类型
         *
         * <p>2 党支部党员大会开展情况
         *
         * <p>3 党支部委员会会议开展情况
         *
         * <p>4 党小组会开展情况
         *
         * <p>5 党课开展情况
         *
         * <p>6主题党日开展情况
         *
         */
        private Integer mappingType;
        /** 任务周期 */
        private Integer period;
        /** 区县中实际的类型id */
        private Long typeId;
        /**
         * 类型名称
         */
        private String type;
    }
}
