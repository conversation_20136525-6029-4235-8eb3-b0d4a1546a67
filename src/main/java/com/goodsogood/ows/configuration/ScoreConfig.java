package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: ows-sas
 * @description: 积分分数配置信息
 * @author: Mr.<PERSON>
 * @create: 2020-11-06 09:18
 **/
@Data
@Component
@ConfigurationProperties(prefix = ScoreConfig.PREFIX)
public class ScoreConfig {

    public static final String PREFIX = "auto-score";

    private TotalScore totalScore;

    private OrgScore orgScore;

    private UserScore userScore;

    @Data
    public static class TotalScore {
        private String org;
        private String user;
    }

    /**
     * 组织得分选项
     */
    @Data
    public static class OrgScore {
        private Double parentId;
        private Double name;
        private Double orgTypeChild;
        private Double shortName;
        private Double isRetire;
        private Double orgPhone;
        private Double orgContacts;
        private Double ownerId;
        /**
         * 积分捐赠
         */
        private List<Rule> donate;
        /**
         * 在线学习
         */
        private List<Rule> study;
        /**
         * 志愿活动
         */
        private List<Rule> volunteer;
        /**
         * 组织 基础工作 民主评议结果 所有基层党组织 是否已完成上一年度民主评议结果录入
         */
        private Double lastYearOrgCommentResult;

        /**
         * 组织 基础工作 领导班子成员支部联系点情况 机关党委 是否设置支部联系点（有/无？）
         */
        private Double isCreateLeaderContact;

        /**
         * 组织 基础工作 领导班子成员支部联系点情况 机关党委 支部联系点设置是否合理（联系点与所在支部是否一致）
         */
        private Double checkLeaderContactEqual;
        /**
         * 组织 组织建设 届次换届 所有基层党组织 是否按期换届
         */
        private Double isCreatePeriod;

        /**
         * 织 组织建设 届次换届 党支部 支部人数符合规定（未超过50人或小于3人）
         */
        private Double checkPeriodPerson1;

        /**
         * 组织 组织建设 届次换届 党支部 支委会设置符合规定（超过7人且成立支委会）
         */
        private Double checkPeriodPerson2;

        /**
         * 组织 组织建设 届次换届 机关党委 机关党委书记、专职副书记是否出现空缺
         */
        private Double checkTopOrgLackPosition;

        /**
         * 组织 组织建设 届次换届 机关党委 机关党委书记、专职副书记任职年限是否超过两届
         */
        private Double checkTopOrgRepeatExistPosition;

        private Rule meetingTask;

        /** 组织完成任务得分 */
        private Rule orgLifeScore;

        /** 述职评议分值 */
        private Rule debriefReviewRating1;
        private Rule debriefReviewRating2;
        private Rule debriefReviewRating3;
        private Rule debriefReviewRating4;
        /** 奖励分值 */
        private Rule  orgCommend;
        /** 惩罚扣分值 */
        private Rule  orgPenalize;


        /**
         * 是否完成党费交纳
         */
        private Double ppmdIsFinish;
        /**
         * 是否存在未设置
         */
        private Double ppmdIsUnset;
        /**
         * 党费交齐日期得分
         */
        private List<Rule> ppmdFinishDay;
        /**
         * 组织消费扶贫得分
         */
        private List<Rule> creditPovertyOrder;
        /**
         * 组织积分换书得分
         */
        private List<Rule> creditBookOrder;
    }

    /**
     * 用户得分选项
     */
    @Data
    public static class UserScore {
        private Double name;
        private Double phone;
        private Double certNumber;
        private Double joiningTime;
        private Double education;
        private Double gender;
        private Double politicalType;
        private Double positionCode;
        /**
         * 积分捐赠
         */
        private Rule donate;
        /**
         * 在线学习
         */
        private Rule study;
        /**
         * 志愿活动
         */
        private Rule volunteer;
        /**
         * 党员 领导干部示范带头 讲党课 机关单位领导干部 讲党课次数是否满足规定，每年大于等于1次
         */
        private Double leaderLecturesScore;
        /**
         * 党员 基础工作 民主评议 非离退休党员 是否有有评议结果（按年统计） 1
         */
        private Double lastYearUserCommentResult1;
        /**
         * 党员 基础工作 民主评议 非离退休党员 是否有有评议结果（按年统计） 2
         */
        private Double lastYearUserCommentResult2;
        /**
         * 党员 基础工作 民主评议 非离退休党员 是否有有评议结果（按年统计） 3
         */
        private Double lastYearUserCommentResult3;
        /**
         * 党员 基础工作 民主评议 非离退休党员 是否有有评议结果（按年统计） 4
         */
        private Double lastYearUserCommentResult4;

        /**
         * 互动
         */
        private ActivityRule activity;

        /** 奖励分值 */
        private Rule  userCommend;
        /** 惩罚扣分值 */
        private Rule  userPenalize;

        /**
         * 参加纪实获得分值
         */
        private MeetingRule meeting;

        /**
         * 领导干部双重组织生活
         */
        private MeetingRule dualLife;
        /**
         * 党员是否交纳得分
         */
        private Double ppmdIsFinish;
        /**
         * 党费交齐日期得分
         */
        private List<Rule> ppmdFinishDay;
        /**
         * 用户消费扶贫得分
         */
        private Double povertyOrder;
        /**
         * 用户积分换书得分
         */
        private Double bookOrder;
    }

    /**
     * 规则
     */
    @Data
    public static class Rule {
        /**
         * 最小值
         */
        private Integer min;
        /**
         * 最大值
         */
        private Integer max;
        /**
         * 得分
         */
        private Double score;
    }
    /**
     * 规则
     */
    @Data
    public static class ActivityRule extends Rule {
        /**
         * 互动类型 逗号分割
         */
        private String types;
    }
    /**
     * 纪实规则
     */
    @Data
    public static class MeetingRule extends Rule{
        /**
         * 最小值
         */
        private Integer min;
        /**
         * 得分
         */
        private Double score;
        /**
         * 会议状态
         */
        private String status;
        /**
         * 签到状态
         */
        private String signStatus;
    }
    /**
     * 根据百分比获取规则
     *
     * @param rules      规则集合
     * @param percentage 百分比
     * @return
     */
    public static Double getScoreByRule(List<Rule> rules, Integer percentage) {
        for (Rule rule : rules) {
            if (percentage > rule.min && percentage <= rule.max) {
                return rule.score;
            }
        }
        return 0.0;
    }

    /**
     * @param a 除数
     * @param b 被除数
     * @return
     */
    public static Integer getPercentage(Long a, Long b) {
        Long percentage = a * 100 / b;
        return percentage.intValue();
    }
}
