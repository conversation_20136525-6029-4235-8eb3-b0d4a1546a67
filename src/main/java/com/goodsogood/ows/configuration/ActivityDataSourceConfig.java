package com.goodsogood.ows.configuration;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;

/**
 * Acitvity数据源
 * <AUTHOR>
 * @date 2019/11/13
 */
@Configuration
// 使用TK的MapperScan
@MapperScan(basePackages = "com.goodsogood.ows.mapper.activity", sqlSessionFactoryRef = ActivityDataSourceConfig.SQL_SESSION_FACTORY)
public class ActivityDataSourceConfig {

    @Autowired
    private SqlSessionFactoryConfigurer sqlSessionFactoryConfigurer;

    private static final String DATA_SOURCE = "activityDataSource";

    private static final String TRANSACTION_MANAGER = "activityTransactionManager";

    static final String SQL_SESSION_FACTORY = "activitySqlSessionFactory";

    private static final String DATA_PREFIX = "spring.datasource.activity";

    @Bean(name = DATA_SOURCE)
    @ConfigurationProperties(prefix = DATA_PREFIX)
    public DataSource ActivityDataSource(){
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean(name = TRANSACTION_MANAGER)
    public DataSourceTransactionManager mysqlTransactionManager() {
        return new DataSourceTransactionManager(ActivityDataSource());
    }

    @Bean(name = SQL_SESSION_FACTORY)
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier(DATA_SOURCE) DataSource dataSource)
            throws Exception {
        return sqlSessionFactoryConfigurer.createSqlSessionFactory(dataSource);
    }
}
