package com.goodsogood.ows.configuration;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;

/**
 * @description: 烟草学习系统数据源
 * @author: zhangtao
 * @create: 2022-03-24 10:33
 */
@Configuration
@MapperScan(basePackages = "com.goodsogood.ows.mapper.learn", sqlSessionFactoryRef = LearnDataSourceConfig.SQL_SESSION_FACTORY)
public class LearnDataSourceConfig {

    private static final String DATA_SOURCE = "learnDataSource";

    private static final String TRANSACTION_MANAGER = "learnTransactionManager";

    static final String SQL_SESSION_FACTORY = "learnSqlSessionFactory";

    private static final String DATA_PREFIX = "spring.datasource.learn";

    @Bean(name = DATA_SOURCE)
    @ConfigurationProperties(prefix = DATA_PREFIX)
    public DataSource LearnDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean(name = TRANSACTION_MANAGER)
    public DataSourceTransactionManager mysqlTransactionManager() {
        return new DataSourceTransactionManager(LearnDataSource());
    }

    @Bean(name = SQL_SESSION_FACTORY)
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier(DATA_SOURCE) DataSource dataSource)
            throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        return sessionFactory.getObject();
    }
}