package com.goodsogood.ows.configuration;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("ScoreManagerConfig")
@ConfigurationProperties(prefix = ScoreManagerConfig.PREFIX)
public class ScoreManagerConfig {

    public static final String dingDingPushUrl = "https://oapi.dingtalk.com/robot/send?access_token=%s";

    public static String SCORE_MANAGER_TOPIC_EXCHANGE_KEY = "Score_Manager_Topic_Exchange";
    public static String BASE_ORG_PERIOD_EXPIRE_NOT_CREATE = "BASE_ORG_PERIOD_EXPIRE_NOT_CREATE";
    public static String BASE_ORG_PERIOD_EXIST_LEADER = "BASE_ORG_PERIOD_EXIST_LEADER";
    public static String BASE_ORG_PEOPLE_PUNISH = "BASE_ORG_PEOPLE_PUNISH";
    public static String BASE_ORG_PEOPLE_LOGIN_RATIO = "BASE_ORG_PEOPLE_LOGIN_RATIO";
    public static String BASE_ORG_PEOPLE_NEWS_READ = "BASE_ORG_PEOPLE_NEWS_READ";
    public static String PARTY_ORG_EXIST_PERIOD_LEADER = "PARTY_ORG_EXIST_PERIOD_LEADER";
    public static String PARTY_ORG_PERIOD_EXPIRE_NOT_CREATE = "PARTY_ORG_PERIOD_EXPIRE_NOT_CREATE";
    public static String PARTY_ORG_PEOPLE_PUNISH = "PARTY_ORG_PEOPLE_PUNISH";
    public static String PARTY_GROUP_EXIST_LEADER_GROUP = "PARTY_GROUP_EXIST_LEADER_GROUP";
    public static String PARTY_GROUP_EXIST_PUNISH = "PARTY_GROUP_EXIST_PUNISH";
    public static String PARTY_GROUP_EXIST_WORK_CONTACT = "PARTY_GROUP_EXIST_WORK_CONTACT";
    public static String PARTY_GROUP_EXIST_BASE_CONTACT = "PARTY_GROUP_EXIST_BASE_CONTACT";
    public static String PARTY_MEMBER_LOGIN = "PARTY_MEMBER_LOGIN";


    public static final String PREFIX = "score-manager";

    /**
     * 区分不同环境使用同一mq 相同交换机的问题
     */
    private String exchangesSuffix;

    /**
     * 区县下支持处理积分的项
     */
    private Map<Long, List<Integer>> regions;

    /**
     * 钉钉消息 token
     */
    private String dingTalkAccessKey;

    private String   indexOrgDorisTable;
    private String   indexUserDorisTable;






    public String getDingTalkAccessKey() {
        return dingTalkAccessKey;
    }

    public void setDingTalkAccessKey(String dingTalkAccessKey) {
        this.dingTalkAccessKey = dingTalkAccessKey;
    }

    public String getExchangesSuffix() {
        return exchangesSuffix;
    }

    public void setExchangesSuffix(String exchangesSuffix) {
        this.exchangesSuffix = exchangesSuffix;
        SCORE_MANAGER_TOPIC_EXCHANGE_KEY = SCORE_MANAGER_TOPIC_EXCHANGE_KEY + "_" + exchangesSuffix;
        BASE_ORG_PERIOD_EXPIRE_NOT_CREATE = BASE_ORG_PERIOD_EXPIRE_NOT_CREATE + "_" + exchangesSuffix;
        BASE_ORG_PERIOD_EXIST_LEADER = BASE_ORG_PERIOD_EXIST_LEADER + "_" + exchangesSuffix;
        BASE_ORG_PEOPLE_PUNISH = BASE_ORG_PEOPLE_PUNISH + "_" + exchangesSuffix;
        BASE_ORG_PEOPLE_LOGIN_RATIO = BASE_ORG_PEOPLE_LOGIN_RATIO + "_" + exchangesSuffix;
        BASE_ORG_PEOPLE_NEWS_READ = BASE_ORG_PEOPLE_NEWS_READ + "_" + exchangesSuffix;
        PARTY_ORG_EXIST_PERIOD_LEADER = PARTY_ORG_EXIST_PERIOD_LEADER + "_" + exchangesSuffix;
        PARTY_ORG_PERIOD_EXPIRE_NOT_CREATE = PARTY_ORG_PERIOD_EXPIRE_NOT_CREATE + "_" + exchangesSuffix;
        PARTY_ORG_PEOPLE_PUNISH = PARTY_ORG_PEOPLE_PUNISH + "_" + exchangesSuffix;
        PARTY_GROUP_EXIST_LEADER_GROUP = PARTY_GROUP_EXIST_LEADER_GROUP + "_" + exchangesSuffix;
        PARTY_GROUP_EXIST_PUNISH = PARTY_GROUP_EXIST_PUNISH + "_" + exchangesSuffix;
        PARTY_GROUP_EXIST_WORK_CONTACT = PARTY_GROUP_EXIST_WORK_CONTACT + "_" + exchangesSuffix;
        PARTY_GROUP_EXIST_BASE_CONTACT = PARTY_GROUP_EXIST_BASE_CONTACT + "_" + exchangesSuffix;
        PARTY_MEMBER_LOGIN = PARTY_MEMBER_LOGIN + "_" + exchangesSuffix;
    }

    public Map<Long, List<Integer>> getRegions() {
        return regions;
    }

    public void setRegions(Map<Long, List<Integer>> regions) {
        this.regions = regions;
    }

    public static String getBaseOrgPeriodExpireNotCreate() {
        return BASE_ORG_PERIOD_EXPIRE_NOT_CREATE;
    }

    public static String getBaseOrgPeriodExistLeader() {
        return BASE_ORG_PERIOD_EXIST_LEADER;
    }

    public static String getBaseOrgPeoplePunish() {
        return BASE_ORG_PEOPLE_PUNISH;
    }

    public static String getBaseOrgPeopleLoginRatio() {
        return BASE_ORG_PEOPLE_LOGIN_RATIO;
    }

    public static String getBaseOrgPeopleNewsRead() {
        return BASE_ORG_PEOPLE_NEWS_READ;
    }

    public static String getPartyOrgExistPeriodLeader() {
        return PARTY_ORG_EXIST_PERIOD_LEADER;
    }

    public static String getPartyOrgPeriodExpireNotCreate() {
        return PARTY_ORG_PERIOD_EXPIRE_NOT_CREATE;
    }

    public static String getPartyOrgPeoplePunish() {
        return PARTY_ORG_PEOPLE_PUNISH;
    }

    public static String getPartyGroupExistLeaderGroup() {
        return PARTY_GROUP_EXIST_LEADER_GROUP;
    }

    public static String getPartyGroupExistPunish() {
        return PARTY_GROUP_EXIST_PUNISH;
    }

    public static String getPartyGroupExistWorkContact() {
        return PARTY_GROUP_EXIST_WORK_CONTACT;
    }

    public static String getPartyGroupExistBaseContact() {
        return PARTY_GROUP_EXIST_BASE_CONTACT;
    }

    public static String getPartyMemberLogin() {
        return PARTY_MEMBER_LOGIN;
    }
    public String getIndexOrgDorisTable() {
        return indexOrgDorisTable;
    }

    public void setIndexOrgDorisTable(String indexOrgDorisTable) {
        this.indexOrgDorisTable = indexOrgDorisTable;
    }

    public String getIndexUserDorisTable() {
        return indexUserDorisTable;
    }

    public void setIndexUserDorisTable(String indexUserDorisTable) {
        this.indexUserDorisTable = indexUserDorisTable;
    }
}
