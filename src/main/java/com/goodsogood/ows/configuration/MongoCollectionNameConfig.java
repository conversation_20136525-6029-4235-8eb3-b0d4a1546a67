package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/1/8
 * Description:
 */
@ConfigurationProperties(prefix = MongoCollectionNameConfig.PREFIX)
@Component
public class MongoCollectionNameConfig {
    public static final String PREFIX = "mongo-connection";

    private Map<String, String> collectionNames;

    private String host;

    private Integer port;

    private String databaseName;

    private String userName;

    private String password;

    public Map<String, String> getCollectionNames() {
        return collectionNames;
    }

    public void setCollectionNames(Map<String, String> collectionNames) {
        this.collectionNames = collectionNames;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
