package com.goodsogood.ows.configuration;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;

@Configuration
// 使用TK的MapperScan 小程序学党史数据库
@MapperScan(basePackages = "com.goodsogood.ows.mapper.pull",
        sqlSessionFactoryRef = PullDataSourceConfig.SQL_SESSION_FACTORY)
public class PullDataSourceConfig {

    private static final String DATA_SOURCE = "pullDataSource";

    private static final String TRANSACTION_MANAGER = "pullTransactionManager";

    static final String SQL_SESSION_FACTORY = "pullSqlSessionFactory";

    private static final String DATA_PREFIX = "spring.datasource.pull";

    @Bean(name = DATA_SOURCE)
    @ConfigurationProperties(prefix = DATA_PREFIX)
    public DataSource PullDataSourceConfig(){
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean(name = TRANSACTION_MANAGER)
    public DataSourceTransactionManager mysqlTransactionManager() {
        return new DataSourceTransactionManager(PullDataSourceConfig());
    }

    @Bean(name = SQL_SESSION_FACTORY)
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier(DATA_SOURCE) DataSource dataSource)
            throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        return sessionFactory.getObject();
    }

}
