package com.goodsogood.ows.configuration

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component

/**
 *
 * <AUTHOR>
 * @createTime 2022年06月21日 23:32:00
 */

@Component
@ConfigurationProperties(prefix = PbmWorkDataConfig.PREFIX)
class PbmWorkDataConfig {

    companion object {
        const val PREFIX = "pbm-work-data"
    }

    var ruleIds: MutableList<Long>? = null

    var contentMap = mutableMapOf<Int, String>()

    var workItemIds: MutableList<Long>? = null

    // <key,value> key: workItemId  value 代表选项列表，通过正则表达式进行匹配
    var workDataOption: MutableMap<Int, List<WorkDataConfig>>? = null

    var rankRangeMap: MutableMap<Double, String>? = null

    var meetingMap: FusionMeetingConfiguaration? = null
    // 创先争优赋分值
    var rewardMap: MutableMap<Int, Double>? = null
    // MongoDB Name
    var fusionMongo: String = "PBM-FUSION-BASE"
}

data class FusionMeetingConfiguaration(
    //每项指标涉及的会议类型
    var itemTypeMap: Map<Long, String>? = null,
    //要统计的状态
    var status: String? = null,
    //业务标签 党建业务深度融合标签，多个逗号分隔
    var busiTag: String? = null,

) {
    fun getTypes(item: Long): String? {
        return itemTypeMap!![item]
    }
}

data class WorkDataConfig(

    // 正则表达式
    var regex: String? = null,

    // 显示值
    var value: String? = null
) {

}