package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 地图外显示党组织id
 *
 */
@Data
@Component
@ConfigurationProperties(prefix = OutsideMapConfig.PREFIX)
public class OutsideMapConfig {

    public static final String PREFIX = "outside-map-org-id";

    //党委
    private List<Long> orgId;

    public List<Long> getOrgId() {
        return orgId;
    }

    public void setOrgId(List<Long> orgId) {
        this.orgId = orgId;
    }
}
