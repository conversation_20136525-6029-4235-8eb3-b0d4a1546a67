package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 定时器配置
 *
 * <AUTHOR>
 * @create 2022-04-18
 **/
@Component
@ConfigurationProperties(prefix = SchedulerConf.PREFIX)
public class SchedulerConf {
    public static final String PREFIX = "scheduler";

    private OperationSystem operationSystem;

    private PartyBusiness partyBusiness;

    // 对应配置文件中的 scheduler.eval-v2

    private CommonScheduler evalV2;

    /**
     * 系统运行报告定时任务
     */
    @Data
    public static class OperationSystem {
        private Boolean runFlag;
    }

    /**
     * 系统运行报告定时任务
     */
    public static class PartyBusiness {
        private Boolean run;
        private List<Long> regionIds;

        public Boolean getRun() {
            return run;
        }

        public void setRun(Boolean run) {
            this.run = run;
        }

        public List<Long> getRegionIds() {
            return regionIds;
        }

        public void setRegionIds(List<Long> regionIds) {
            this.regionIds = regionIds;
        }
    }

    // 通用定时器配置类
    public static class CommonScheduler {
        private Boolean run;
        private String cron;

        public Boolean getRun() {
            return run;
        }

        public void setRun(Boolean run) {
            this.run = run;
        }

        public String getCron() {
            return cron;
        }

        public void setCron(String cron) {
            this.cron = cron;
        }
    }


    public OperationSystem getOperationSystem() {
        return operationSystem;
    }

    public void setOperationSystem(OperationSystem operationSystem) {
        this.operationSystem = operationSystem;
    }

    public PartyBusiness getPartyBusiness() {
        return partyBusiness;
    }

    public void setPartyBusiness(PartyBusiness partyBusiness) {
        this.partyBusiness = partyBusiness;
    }

    public CommonScheduler getEvalV2() {
        return evalV2;
    }

    public void setEvalV2(CommonScheduler evalV2) {
        this.evalV2 = evalV2;
    }
}
