package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("OperationSystemConfig")
@ConfigurationProperties(prefix = OperationSystemConfig.PREFIX)
@Data
public class OperationSystemConfig {

    public static final String PREFIX = "operation-system";


    /**
     * 系统运行报表定时记录数据任务锁 锁的过期时间(ms)
     */
    public static final int STATISTICS_SCHEDULER_LOCK_EXPIRE = 1 * 60 * 60 * 1000;

    /**
     * 系统运行报表定时记录数据任务锁
     */
    public static final String OPERATION_SYSTEM_TASK_LOCK = "SAS_OPERATION_SYSTEM_TASK_LOCK";

    /**
     * 访问率 保留小数位数
     */
    private int scale=0;

    /**
     * 集中访问时间段统计，时间间隔
     */
    private int timeInterval=2;
    /**
     * 点击菜单统计功能
     * { channel: "OperationSystem" ,type: "A",menuIds: "1,2,3,4,5" }
     */
    private Map<Long, ClickMenuConf> clickMenu;

    /**
     * 配置需要合并统计的菜单标号
     * KEY: 区县编号
     * VALUE: mergeName合并后名称，mergeList包含的菜单编号集合,逗号间隔
     */
    private Map<Long, List<MenuStatisticsMerge>> clickMenuStatistics = new HashMap();



    @Data
    public static class ClickMenuConf {
        /**
         * click-spy 渠道标识
         */
        private String channel;
        /**
         * click-spy 渠道下分类标识
         */
        private String type;
        /**
         * click-spy  操作对象标识  object字段
         */
        private String menuIds;
    }

    @Data
    public static class MenuStatisticsMerge {
        /**
         * 合并后名称
         */
        private String mergeName;

        /**
         * 包含的菜单编号集合,逗号间隔
         */
        private String mergeList;
    }
}
