package com.goodsogood.ows.configuration;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.codehaus.jackson.map.ser.std.CollectionSerializer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Author: mengting
 * @Date: 2022/4/21 20:38
 */
@Data
@Component
@ConfigurationProperties(prefix = ExperienceRuleTagConfig.PREFIX)
public class ExperienceRuleTagConfig {
    public static final String PREFIX = "experience";
    private Map<Integer, List<String>> ruleTag;

    public List<String> queryRuleTag(Integer ruleId){
        if(CollectionUtils.isNotEmpty(ruleTag.get(ruleId))){
            return ruleTag.get(ruleId);
        }
        return Collections.EMPTY_LIST;
    }
}
