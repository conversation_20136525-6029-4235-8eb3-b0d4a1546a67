package com.goodsogood.ows.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 达梦数据库SQL拦截器
 * 拦截MyBatis执行的SQL，将MySQL语法转换为达梦数据库语法
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Slf4j
@Component
@Intercepts({
    @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class DamengSqlInterceptor implements Interceptor {

    @Autowired
    private MySQLToDamengSqlConverter sqlConverter;
    
    @Autowired
    private SqlInterceptorConfig config;
    
    // 缓存已转换的SQL，避免重复转换
    private final ConcurrentHashMap<String, String> sqlCache = new ConcurrentHashMap<>();
    
    // 需要进行SQL转换的数据源名称集合
    private final Set<String> enabledDataSources = ConcurrentHashMap.newKeySet();
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        try {
            // 检查是否启用拦截器
            if (!config.isEnabled()) {
                return invocation.proceed();
            }
            
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            MetaObject metaObject = SystemMetaObject.forObject(statementHandler);
            
            // 获取MappedStatement
            MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");
            String statementId = mappedStatement.getId();
            
            // 根据statementId判断数据源
            String dataSourceName = extractDataSourceName(statementId);
            
            // 检查当前数据源是否需要进行SQL转换
            if (!shouldConvertSql(dataSourceName)) {
                return invocation.proceed();
            }
            
            // 获取BoundSql
            BoundSql boundSql = statementHandler.getBoundSql();
            String originalSql = boundSql.getSql();
            
            if (originalSql == null || originalSql.trim().isEmpty()) {
                return invocation.proceed();
            }
            
            // 从缓存中获取转换后的SQL，如果没有则进行转换
            String cacheKey = dataSourceName + ":" + originalSql;
            String convertedSql = sqlCache.computeIfAbsent(cacheKey, key -> {
                try {
                    return sqlConverter.convertSql(originalSql, dataSourceName);
                } catch (Exception e) {
                    log.error("Failed to convert SQL for dataSource {}: {}", dataSourceName, originalSql, e);
                    return originalSql; // 转换失败时返回原始SQL
                }
            });
            
            // 如果SQL发生了变化，则更新BoundSql
            if (!originalSql.equals(convertedSql)) {
                // 使用反射修改BoundSql中的SQL
                MetaObject boundSqlMetaObject = SystemMetaObject.forObject(boundSql);
                boundSqlMetaObject.setValue("sql", convertedSql);
                
                log.debug("SQL intercepted and converted for dataSource: {}, statementId: {}", 
                         dataSourceName, statementId);
                log.debug("Original SQL: {}", originalSql);
                log.debug("Converted SQL: {}", convertedSql);
            }
            
        } catch (Exception e) {
            log.error("Error in SQL interceptor", e);
            // 发生异常时继续执行原始流程
        }
        
        return invocation.proceed();
    }
    
    /**
     * 从statementId中提取数据源名称
     * 
     * @param statementId MyBatis的statementId
     * @return 数据源名称
     */
    private String extractDataSourceName(String statementId) {
        if (statementId == null) {
            return "unknown";
        }
        
        // 根据mapper包路径判断数据源
        // 例如: com.goodsogood.ows.mapper.user.UserMapper.selectById -> user
        if (statementId.contains(".mapper.user.")) {
            return "user";
        } else if (statementId.contains(".mapper.sas.")) {
            return "sas";
        } else if (statementId.contains(".mapper.ppmd.")) {
            return "ppmd";
        } else if (statementId.contains(".mapper.meeting.")) {
            return "meeting";
        } else if (statementId.contains(".mapper.activity.")) {
            return "activity";
        } else if (statementId.contains(".mapper.score.")) {
            return "score";
        } else if (statementId.contains(".mapper.eval.")) {
            return "eval";
        } else if (statementId.contains(".mapper.dataworks.")) {
            return "dataworks";
        } else if (statementId.contains(".mapper.rank.")) {
            return "rank";
        } else if (statementId.contains(".mapper.finereport.")) {
            return "finereport";
        } else if (statementId.contains(".mapper.volunteer.")) {
            return "volunteer";
        } else if (statementId.contains(".mapper.history.")) {
            return "history";
        } else if (statementId.contains(".mapper.supervise.")) {
            return "supervise";
        } else if (statementId.contains(".mapper.experience.")) {
            return "experience";
        } else if (statementId.contains(".mapper.pull.")) {
            return "pull";
        } else if (statementId.contains(".mapper.task.")) {
            return "task";
        } else if (statementId.contains(".mapper.learn.")) {
            return "learn";
        } else if (statementId.contains(".mapper.ecp.")) {
            return "ecp";
        }
        
        return "default";
    }
    
    /**
     * 判断是否需要对指定数据源进行SQL转换
     *
     * @param dataSourceName 数据源名称
     * @return 是否需要转换
     */
    private boolean shouldConvertSql(String dataSourceName) {
        return config.isDataSourceEnabled(dataSourceName);
    }
    
    /**
     * 清除SQL缓存
     */
    public void clearSqlCache() {
        sqlCache.clear();
        log.info("SQL cache cleared");
    }
    
    /**
     * 获取缓存大小
     * 
     * @return 缓存大小
     */
    public int getCacheSize() {
        return sqlCache.size();
    }
    
    /**
     * 添加需要转换的数据源
     * 
     * @param dataSourceName 数据源名称
     */
    public void addEnabledDataSource(String dataSourceName) {
        enabledDataSources.add(dataSourceName);
        log.info("Added enabled data source: {}", dataSourceName);
    }
    
    /**
     * 移除需要转换的数据源
     * 
     * @param dataSourceName 数据源名称
     */
    public void removeEnabledDataSource(String dataSourceName) {
        enabledDataSources.remove(dataSourceName);
        log.info("Removed enabled data source: {}", dataSourceName);
    }
    
    /**
     * 获取已启用的数据源列表
     * 
     * @return 数据源名称集合
     */
    public Set<String> getEnabledDataSources() {
        return Set.copyOf(enabledDataSources);
    }
    
    @Override
    public Object plugin(Object target) {
        // 只拦截StatementHandler
        if (target instanceof StatementHandler) {
            return Plugin.wrap(target, this);
        }
        return target;
    }
    
    @Override
    public void setProperties(Properties properties) {
        // 可以通过properties配置拦截器参数
        log.info("DamengSqlInterceptor initialized with properties: {}", properties);
    }
    
    /**
     * 获取拦截器统计信息
     * 
     * @return 统计信息
     */
    public InterceptorStats getStats() {
        return InterceptorStats.builder()
                .cacheSize(sqlCache.size())
                .enabledDataSources(Set.copyOf(enabledDataSources))
                .enabled(config.isEnabled())
                .convertAllDataSources(config.isConvertAllDataSources())
                .build();
    }
    
    /**
     * 拦截器统计信息
     */
    public static class InterceptorStats {
        private final int cacheSize;
        private final Set<String> enabledDataSources;
        private final boolean enabled;
        private final boolean convertAllDataSources;
        
        private InterceptorStats(Builder builder) {
            this.cacheSize = builder.cacheSize;
            this.enabledDataSources = builder.enabledDataSources;
            this.enabled = builder.enabled;
            this.convertAllDataSources = builder.convertAllDataSources;
        }
        
        public static Builder builder() {
            return new Builder();
        }
        
        public int getCacheSize() { return cacheSize; }
        public Set<String> getEnabledDataSources() { return enabledDataSources; }
        public boolean isEnabled() { return enabled; }
        public boolean isConvertAllDataSources() { return convertAllDataSources; }
        
        @Override
        public String toString() {
            return "InterceptorStats{" +
                    "cacheSize=" + cacheSize +
                    ", enabledDataSources=" + enabledDataSources +
                    ", enabled=" + enabled +
                    ", convertAllDataSources=" + convertAllDataSources +
                    '}';
        }
        
        public static class Builder {
            private int cacheSize;
            private Set<String> enabledDataSources;
            private boolean enabled;
            private boolean convertAllDataSources;
            
            public Builder cacheSize(int cacheSize) {
                this.cacheSize = cacheSize;
                return this;
            }
            
            public Builder enabledDataSources(Set<String> enabledDataSources) {
                this.enabledDataSources = enabledDataSources;
                return this;
            }
            
            public Builder enabled(boolean enabled) {
                this.enabled = enabled;
                return this;
            }
            
            public Builder convertAllDataSources(boolean convertAllDataSources) {
                this.convertAllDataSources = convertAllDataSources;
                return this;
            }
            
            public InterceptorStats build() {
                return new InterceptorStats(this);
            }
        }
    }
}
