package com.goodsogood.ows.interceptor;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * SQL拦截器配置类
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@Component
@ConfigurationProperties(prefix = "sql.interceptor")
public class SqlInterceptorConfig {
    
    /**
     * 是否启用SQL拦截器
     */
    private boolean enabled = true;
    
    /**
     * 是否对所有数据源进行SQL转换
     */
    private boolean convertAllDataSources = true;
    
    /**
     * 需要进行SQL转换的数据源名称集合
     * 如果convertAllDataSources为false，则只对此集合中的数据源进行转换
     */
    private Set<String> enabledDataSources = new HashSet<>();
    
    /**
     * 是否启用SQL缓存
     */
    private boolean cacheEnabled = true;
    
    /**
     * SQL缓存最大大小
     */
    private int maxCacheSize = 1000;
    
    /**
     * 是否记录SQL转换日志
     */
    private boolean logEnabled = true;
    
    /**
     * 日志级别：DEBUG, INFO, WARN, ERROR
     */
    private String logLevel = "INFO";
    
    /**
     * 是否启用性能监控
     */
    private boolean performanceMonitorEnabled = false;
    
    /**
     * 性能监控阈值（毫秒）
     */
    private long performanceThreshold = 100;
    
    /**
     * 是否启用SQL验证
     */
    private boolean sqlValidationEnabled = false;
    
    /**
     * 黑名单SQL模式，匹配这些模式的SQL不会被转换
     */
    private Set<String> blacklistPatterns = new HashSet<>();
    
    /**
     * 白名单SQL模式，只有匹配这些模式的SQL才会被转换
     */
    private Set<String> whitelistPatterns = new HashSet<>();
    
    /**
     * 是否启用函数转换
     */
    private boolean functionConversionEnabled = true;
    
    /**
     * 是否启用关键字转换
     */
    private boolean keywordConversionEnabled = true;
    
    /**
     * 是否启用语法转换
     */
    private boolean syntaxConversionEnabled = true;
    
    /**
     * 是否启用LIMIT转换
     */
    private boolean limitConversionEnabled = true;
    
    /**
     * 是否启用日期函数转换
     */
    private boolean dateFunctionConversionEnabled = true;
    
    /**
     * 是否启用字符串函数转换
     */
    private boolean stringFunctionConversionEnabled = true;
    
    /**
     * 是否启用数学函数转换
     */
    private boolean mathFunctionConversionEnabled = true;
    
    /**
     * 是否启用聚合函数转换
     */
    private boolean aggregateFunctionConversionEnabled = true;
    
    /**
     * 是否启用条件函数转换
     */
    private boolean conditionalFunctionConversionEnabled = true;
    
    /**
     * 自定义函数映射
     * key: MySQL函数名, value: 达梦函数名
     */
    private java.util.Map<String, String> customFunctionMapping = new java.util.HashMap<>();
    
    /**
     * 自定义关键字映射
     * key: MySQL关键字, value: 达梦关键字
     */
    private java.util.Map<String, String> customKeywordMapping = new java.util.HashMap<>();
    
    /**
     * 检查指定数据源是否启用SQL转换
     * 
     * @param dataSourceName 数据源名称
     * @return 是否启用
     */
    public boolean isDataSourceEnabled(String dataSourceName) {
        if (!enabled) {
            return false;
        }
        
        if (convertAllDataSources) {
            return true;
        }
        
        return enabledDataSources.contains(dataSourceName);
    }
    
    /**
     * 添加启用的数据源
     * 
     * @param dataSourceName 数据源名称
     */
    public void addEnabledDataSource(String dataSourceName) {
        if (enabledDataSources == null) {
            enabledDataSources = new HashSet<>();
        }
        enabledDataSources.add(dataSourceName);
    }
    
    /**
     * 移除启用的数据源
     * 
     * @param dataSourceName 数据源名称
     */
    public void removeEnabledDataSource(String dataSourceName) {
        if (enabledDataSources != null) {
            enabledDataSources.remove(dataSourceName);
        }
    }
    
    /**
     * 添加黑名单模式
     * 
     * @param pattern SQL模式
     */
    public void addBlacklistPattern(String pattern) {
        if (blacklistPatterns == null) {
            blacklistPatterns = new HashSet<>();
        }
        blacklistPatterns.add(pattern);
    }
    
    /**
     * 添加白名单模式
     * 
     * @param pattern SQL模式
     */
    public void addWhitelistPattern(String pattern) {
        if (whitelistPatterns == null) {
            whitelistPatterns = new HashSet<>();
        }
        whitelistPatterns.add(pattern);
    }
    
    /**
     * 检查SQL是否在黑名单中
     * 
     * @param sql SQL语句
     * @return 是否在黑名单中
     */
    public boolean isBlacklisted(String sql) {
        if (blacklistPatterns == null || blacklistPatterns.isEmpty()) {
            return false;
        }
        
        return blacklistPatterns.stream()
                .anyMatch(pattern -> sql.toLowerCase().contains(pattern.toLowerCase()));
    }
    
    /**
     * 检查SQL是否在白名单中
     * 
     * @param sql SQL语句
     * @return 是否在白名单中
     */
    public boolean isWhitelisted(String sql) {
        if (whitelistPatterns == null || whitelistPatterns.isEmpty()) {
            return true; // 如果没有配置白名单，则认为所有SQL都在白名单中
        }
        
        return whitelistPatterns.stream()
                .anyMatch(pattern -> sql.toLowerCase().contains(pattern.toLowerCase()));
    }
    
    /**
     * 添加自定义函数映射
     * 
     * @param mysqlFunction MySQL函数名
     * @param damengFunction 达梦函数名
     */
    public void addCustomFunctionMapping(String mysqlFunction, String damengFunction) {
        if (customFunctionMapping == null) {
            customFunctionMapping = new java.util.HashMap<>();
        }
        customFunctionMapping.put(mysqlFunction, damengFunction);
    }
    
    /**
     * 添加自定义关键字映射
     * 
     * @param mysqlKeyword MySQL关键字
     * @param damengKeyword 达梦关键字
     */
    public void addCustomKeywordMapping(String mysqlKeyword, String damengKeyword) {
        if (customKeywordMapping == null) {
            customKeywordMapping = new java.util.HashMap<>();
        }
        customKeywordMapping.put(mysqlKeyword, damengKeyword);
    }
    
    /**
     * 获取配置摘要信息
     * 
     * @return 配置摘要
     */
    public String getConfigSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("SqlInterceptorConfig{");
        sb.append("enabled=").append(enabled);
        sb.append(", convertAllDataSources=").append(convertAllDataSources);
        sb.append(", enabledDataSources=").append(enabledDataSources != null ? enabledDataSources.size() : 0);
        sb.append(", cacheEnabled=").append(cacheEnabled);
        sb.append(", maxCacheSize=").append(maxCacheSize);
        sb.append(", logEnabled=").append(logEnabled);
        sb.append(", logLevel='").append(logLevel).append('\'');
        sb.append(", performanceMonitorEnabled=").append(performanceMonitorEnabled);
        sb.append(", functionConversionEnabled=").append(functionConversionEnabled);
        sb.append(", keywordConversionEnabled=").append(keywordConversionEnabled);
        sb.append(", syntaxConversionEnabled=").append(syntaxConversionEnabled);
        sb.append('}');
        return sb.toString();
    }
}
