package com.goodsogood.ows.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MySQL到达梦数据库SQL转换器
 * 处理MySQL语法到达梦数据库语法的转换
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Slf4j
@Component
public class MySQLToDamengSqlConverter {

    @Autowired
    private SqlInterceptorConfig config;

    // MySQL函数到达梦函数的映射
    private static final Map<String, String> FUNCTION_MAPPING = new HashMap<>();

    // MySQL关键字到达梦关键字的映射
    private static final Map<String, String> KEYWORD_MAPPING = new HashMap<>();

    // 需要特殊处理的SQL模式
    private static final Map<Pattern, String> PATTERN_REPLACEMENTS = new HashMap<>();
    
    static {
        initializeFunctionMapping();
        initializeKeywordMapping();
        initializePatternReplacements();
    }
    
    /**
     * 初始化函数映射
     */
    private static void initializeFunctionMapping() {
        // 日期时间函数
        FUNCTION_MAPPING.put("NOW()", "SYSDATE");
        FUNCTION_MAPPING.put("CURDATE()", "TRUNC(SYSDATE)");
        FUNCTION_MAPPING.put("CURTIME()", "TO_CHAR(SYSDATE, 'HH24:MI:SS')");
        FUNCTION_MAPPING.put("UNIX_TIMESTAMP", "EXTRACT(EPOCH FROM TIMESTAMP");
        FUNCTION_MAPPING.put("FROM_UNIXTIME", "TO_TIMESTAMP");
        FUNCTION_MAPPING.put("DATE_FORMAT", "TO_CHAR");
        FUNCTION_MAPPING.put("STR_TO_DATE", "TO_DATE");
        FUNCTION_MAPPING.put("DATEDIFF", "DATEDIFF");
        FUNCTION_MAPPING.put("DATE_ADD", "DATEADD");
        FUNCTION_MAPPING.put("DATE_SUB", "DATEADD");
        FUNCTION_MAPPING.put("YEAR", "EXTRACT(YEAR FROM");
        FUNCTION_MAPPING.put("MONTH", "EXTRACT(MONTH FROM");
        FUNCTION_MAPPING.put("DAY", "EXTRACT(DAY FROM");
        
        // 字符串函数
        FUNCTION_MAPPING.put("CONCAT", "CONCAT");
        FUNCTION_MAPPING.put("LENGTH", "LEN");
        FUNCTION_MAPPING.put("CHAR_LENGTH", "LEN");
        FUNCTION_MAPPING.put("CHARACTER_LENGTH", "LEN");
        FUNCTION_MAPPING.put("SUBSTRING", "SUBSTR");
        FUNCTION_MAPPING.put("MID", "SUBSTR");
        FUNCTION_MAPPING.put("LEFT", "LEFT");
        FUNCTION_MAPPING.put("RIGHT", "RIGHT");
        FUNCTION_MAPPING.put("UPPER", "UPPER");
        FUNCTION_MAPPING.put("LOWER", "LOWER");
        FUNCTION_MAPPING.put("TRIM", "TRIM");
        FUNCTION_MAPPING.put("LTRIM", "LTRIM");
        FUNCTION_MAPPING.put("RTRIM", "RTRIM");
        FUNCTION_MAPPING.put("REPLACE", "REPLACE");
        FUNCTION_MAPPING.put("LOCATE", "INSTR");
        FUNCTION_MAPPING.put("POSITION", "INSTR");
        FUNCTION_MAPPING.put("INSTR", "INSTR");
        
        // 数学函数
        FUNCTION_MAPPING.put("ROUND", "ROUND");
        FUNCTION_MAPPING.put("CEIL", "CEIL");
        FUNCTION_MAPPING.put("CEILING", "CEIL");
        FUNCTION_MAPPING.put("FLOOR", "FLOOR");
        FUNCTION_MAPPING.put("ABS", "ABS");
        FUNCTION_MAPPING.put("MOD", "MOD");
        FUNCTION_MAPPING.put("POWER", "POWER");
        FUNCTION_MAPPING.put("SQRT", "SQRT");
        
        // 聚合函数
        FUNCTION_MAPPING.put("COUNT", "COUNT");
        FUNCTION_MAPPING.put("SUM", "SUM");
        FUNCTION_MAPPING.put("AVG", "AVG");
        FUNCTION_MAPPING.put("MAX", "MAX");
        FUNCTION_MAPPING.put("MIN", "MIN");
        FUNCTION_MAPPING.put("GROUP_CONCAT", "LISTAGG");
        
        // 条件函数
        FUNCTION_MAPPING.put("IF", "CASE WHEN");
        FUNCTION_MAPPING.put("IFNULL", "NVL");
        FUNCTION_MAPPING.put("ISNULL", "NVL");
        FUNCTION_MAPPING.put("COALESCE", "COALESCE");
        FUNCTION_MAPPING.put("NULLIF", "NULLIF");
    }
    
    /**
     * 初始化关键字映射
     */
    private static void initializeKeywordMapping() {
        // 数据类型映射
        KEYWORD_MAPPING.put("AUTO_INCREMENT", "IDENTITY(1,1)");
        KEYWORD_MAPPING.put("TINYINT", "TINYINT");
        KEYWORD_MAPPING.put("SMALLINT", "SMALLINT");
        KEYWORD_MAPPING.put("MEDIUMINT", "INT");
        KEYWORD_MAPPING.put("INT", "INT");
        KEYWORD_MAPPING.put("INTEGER", "INT");
        KEYWORD_MAPPING.put("BIGINT", "BIGINT");
        KEYWORD_MAPPING.put("FLOAT", "FLOAT");
        KEYWORD_MAPPING.put("DOUBLE", "DOUBLE");
        KEYWORD_MAPPING.put("DECIMAL", "DECIMAL");
        KEYWORD_MAPPING.put("NUMERIC", "NUMERIC");
        KEYWORD_MAPPING.put("CHAR", "CHAR");
        KEYWORD_MAPPING.put("VARCHAR", "VARCHAR");
        KEYWORD_MAPPING.put("TEXT", "CLOB");
        KEYWORD_MAPPING.put("MEDIUMTEXT", "CLOB");
        KEYWORD_MAPPING.put("LONGTEXT", "CLOB");
        KEYWORD_MAPPING.put("BLOB", "BLOB");
        KEYWORD_MAPPING.put("MEDIUMBLOB", "BLOB");
        KEYWORD_MAPPING.put("LONGBLOB", "BLOB");
        KEYWORD_MAPPING.put("DATE", "DATE");
        KEYWORD_MAPPING.put("TIME", "TIME");
        KEYWORD_MAPPING.put("DATETIME", "TIMESTAMP");
        KEYWORD_MAPPING.put("TIMESTAMP", "TIMESTAMP");
        
        // 其他关键字
        KEYWORD_MAPPING.put("LIMIT", "ROWNUM <=");
        KEYWORD_MAPPING.put("UNSIGNED", "");
        KEYWORD_MAPPING.put("ZEROFILL", "");
        KEYWORD_MAPPING.put("ENGINE=InnoDB", "");
        KEYWORD_MAPPING.put("ENGINE=MyISAM", "");
        KEYWORD_MAPPING.put("DEFAULT CHARSET=utf8", "");
        KEYWORD_MAPPING.put("DEFAULT CHARSET=utf8mb4", "");
        KEYWORD_MAPPING.put("COLLATE=utf8_general_ci", "");
        KEYWORD_MAPPING.put("COLLATE=utf8mb4_general_ci", "");
    }
    
    /**
     * 初始化模式替换规则
     */
    private static void initializePatternReplacements() {
        // LIMIT语法转换
        PATTERN_REPLACEMENTS.put(
            Pattern.compile("\\s+LIMIT\\s+(\\d+)\\s*$", Pattern.CASE_INSENSITIVE),
            " AND ROWNUM <= $1"
        );
        
        PATTERN_REPLACEMENTS.put(
            Pattern.compile("\\s+LIMIT\\s+(\\d+)\\s*,\\s*(\\d+)\\s*$", Pattern.CASE_INSENSITIVE),
            " AND ROWNUM BETWEEN $1+1 AND $1+$2"
        );
        
        // 反引号处理
        PATTERN_REPLACEMENTS.put(
            Pattern.compile("`([^`]+)`", Pattern.CASE_INSENSITIVE),
            "\"$1\""
        );
        
        // INSERT IGNORE转换
        PATTERN_REPLACEMENTS.put(
            Pattern.compile("INSERT\\s+IGNORE\\s+INTO", Pattern.CASE_INSENSITIVE),
            "INSERT INTO"
        );
        
        // ON DUPLICATE KEY UPDATE转换（简化处理）
        PATTERN_REPLACEMENTS.put(
            Pattern.compile("\\s+ON\\s+DUPLICATE\\s+KEY\\s+UPDATE.*$", Pattern.CASE_INSENSITIVE),
            ""
        );
        
        // REPLACE INTO转换
        PATTERN_REPLACEMENTS.put(
            Pattern.compile("REPLACE\\s+INTO", Pattern.CASE_INSENSITIVE),
            "INSERT INTO"
        );
        
        // 日期函数特殊处理
        PATTERN_REPLACEMENTS.put(
            Pattern.compile("DATE_ADD\\s*\\(\\s*([^,]+)\\s*,\\s*INTERVAL\\s+(\\d+)\\s+(\\w+)\\s*\\)", Pattern.CASE_INSENSITIVE),
            "DATEADD($3, $2, $1)"
        );
        
        PATTERN_REPLACEMENTS.put(
            Pattern.compile("DATE_SUB\\s*\\(\\s*([^,]+)\\s*,\\s*INTERVAL\\s+(\\d+)\\s+(\\w+)\\s*\\)", Pattern.CASE_INSENSITIVE),
            "DATEADD($3, -$2, $1)"
        );
        
        // IF函数转换为CASE WHEN
        PATTERN_REPLACEMENTS.put(
            Pattern.compile("IF\\s*\\(\\s*([^,]+)\\s*,\\s*([^,]+)\\s*,\\s*([^)]+)\\s*\\)", Pattern.CASE_INSENSITIVE),
            "CASE WHEN $1 THEN $2 ELSE $3 END"
        );
    }
    
    /**
     * 转换SQL语句
     *
     * @param originalSql 原始MySQL SQL
     * @return 转换后的达梦SQL
     */
    public String convertSql(String originalSql) {
        return convertSql(originalSql, null);
    }

    /**
     * 转换SQL语句（支持数据源特定配置）
     *
     * @param originalSql 原始MySQL SQL
     * @param dataSourceName 数据源名称
     * @return 转换后的达梦SQL
     */
    public String convertSql(String originalSql, String dataSourceName) {
        if (originalSql == null || originalSql.trim().isEmpty()) {
            return originalSql;
        }

        // 检查是否启用转换
        if (!config.isEnabled()) {
            return originalSql;
        }

        // 检查黑名单
        if (config.isBlacklisted(originalSql)) {
            log.debug("SQL is blacklisted, skipping conversion: {}", originalSql);
            return originalSql;
        }

        // 检查白名单
        if (!config.isWhitelisted(originalSql)) {
            log.debug("SQL is not whitelisted, skipping conversion: {}", originalSql);
            return originalSql;
        }

        try {
            String convertedSql = originalSql;

            // 记录原始SQL
            if (config.isLogEnabled()) {
                log.debug("Original SQL: {}", originalSql);
            }

            // 1. 应用模式替换
            if (config.isSyntaxConversionEnabled()) {
                convertedSql = applyPatternReplacements(convertedSql);
            }

            // 2. 转换函数
            if (config.isFunctionConversionEnabled()) {
                convertedSql = convertFunctions(convertedSql);
            }

            // 3. 转换关键字
            if (config.isKeywordConversionEnabled()) {
                convertedSql = convertKeywords(convertedSql);
            }

            // 4. 处理特殊语法
            convertedSql = handleSpecialSyntax(convertedSql);

            // 记录转换后的SQL
            if (!originalSql.equals(convertedSql) && config.isLogEnabled()) {
                if ("DEBUG".equalsIgnoreCase(config.getLogLevel())) {
                    log.debug("SQL converted from: {} to: {}", originalSql, convertedSql);
                } else if ("INFO".equalsIgnoreCase(config.getLogLevel())) {
                    log.info("SQL converted for dataSource: {}", dataSourceName);
                }
            }

            return convertedSql;

        } catch (Exception e) {
            log.error("Error converting SQL: {}", originalSql, e);
            return originalSql; // 转换失败时返回原始SQL
        }
    }
    
    /**
     * 应用模式替换
     */
    private String applyPatternReplacements(String sql) {
        String result = sql;
        for (Map.Entry<Pattern, String> entry : PATTERN_REPLACEMENTS.entrySet()) {
            Matcher matcher = entry.getKey().matcher(result);
            result = matcher.replaceAll(entry.getValue());
        }
        return result;
    }
    
    /**
     * 转换函数
     */
    private String convertFunctions(String sql) {
        String result = sql;

        // 应用内置函数映射
        if (config.isFunctionConversionEnabled()) {
            for (Map.Entry<String, String> entry : FUNCTION_MAPPING.entrySet()) {
                String mysqlFunc = entry.getKey();
                String damengFunc = entry.getValue();

                // 检查特定类型的函数转换是否启用
                if (shouldConvertFunction(mysqlFunc)) {
                    // 使用正则表达式进行函数替换，确保不会误替换
                    Pattern pattern = Pattern.compile("\\b" + Pattern.quote(mysqlFunc) + "\\b", Pattern.CASE_INSENSITIVE);
                    result = pattern.matcher(result).replaceAll(damengFunc);
                }
            }
        }

        // 应用自定义函数映射
        if (config.getCustomFunctionMapping() != null) {
            for (Map.Entry<String, String> entry : config.getCustomFunctionMapping().entrySet()) {
                String mysqlFunc = entry.getKey();
                String damengFunc = entry.getValue();

                Pattern pattern = Pattern.compile("\\b" + Pattern.quote(mysqlFunc) + "\\b", Pattern.CASE_INSENSITIVE);
                result = pattern.matcher(result).replaceAll(damengFunc);
            }
        }

        return result;
    }

    /**
     * 检查是否应该转换指定函数
     */
    private boolean shouldConvertFunction(String functionName) {
        String upperFunc = functionName.toUpperCase();

        // 日期函数
        if (isDateFunction(upperFunc)) {
            return config.isDateFunctionConversionEnabled();
        }

        // 字符串函数
        if (isStringFunction(upperFunc)) {
            return config.isStringFunctionConversionEnabled();
        }

        // 数学函数
        if (isMathFunction(upperFunc)) {
            return config.isMathFunctionConversionEnabled();
        }

        // 聚合函数
        if (isAggregateFunction(upperFunc)) {
            return config.isAggregateFunctionConversionEnabled();
        }

        // 条件函数
        if (isConditionalFunction(upperFunc)) {
            return config.isConditionalFunctionConversionEnabled();
        }

        return true; // 默认转换
    }

    /**
     * 判断是否为日期函数
     */
    private boolean isDateFunction(String functionName) {
        return functionName.contains("DATE") || functionName.contains("TIME") ||
               functionName.equals("NOW()") || functionName.equals("CURDATE()") ||
               functionName.equals("CURTIME()") || functionName.contains("UNIX_TIMESTAMP") ||
               functionName.equals("YEAR") || functionName.equals("MONTH") || functionName.equals("DAY");
    }

    /**
     * 判断是否为字符串函数
     */
    private boolean isStringFunction(String functionName) {
        return functionName.equals("CONCAT") || functionName.equals("LENGTH") ||
               functionName.equals("SUBSTRING") || functionName.equals("LEFT") ||
               functionName.equals("RIGHT") || functionName.equals("UPPER") ||
               functionName.equals("LOWER") || functionName.equals("TRIM") ||
               functionName.equals("REPLACE") || functionName.equals("LOCATE") ||
               functionName.equals("INSTR") || functionName.contains("CHAR_LENGTH");
    }

    /**
     * 判断是否为数学函数
     */
    private boolean isMathFunction(String functionName) {
        return functionName.equals("ROUND") || functionName.equals("CEIL") ||
               functionName.equals("CEILING") || functionName.equals("FLOOR") ||
               functionName.equals("ABS") || functionName.equals("MOD") ||
               functionName.equals("POWER") || functionName.equals("SQRT");
    }

    /**
     * 判断是否为聚合函数
     */
    private boolean isAggregateFunction(String functionName) {
        return functionName.equals("COUNT") || functionName.equals("SUM") ||
               functionName.equals("AVG") || functionName.equals("MAX") ||
               functionName.equals("MIN") || functionName.equals("GROUP_CONCAT");
    }

    /**
     * 判断是否为条件函数
     */
    private boolean isConditionalFunction(String functionName) {
        return functionName.equals("IF") || functionName.equals("IFNULL") ||
               functionName.equals("ISNULL") || functionName.equals("COALESCE") ||
               functionName.equals("NULLIF");
    }
    
    /**
     * 转换关键字
     */
    private String convertKeywords(String sql) {
        String result = sql;

        // 应用内置关键字映射
        if (config.isKeywordConversionEnabled()) {
            for (Map.Entry<String, String> entry : KEYWORD_MAPPING.entrySet()) {
                String mysqlKeyword = entry.getKey();
                String damengKeyword = entry.getValue();

                // 使用正则表达式进行关键字替换
                Pattern pattern = Pattern.compile("\\b" + Pattern.quote(mysqlKeyword) + "\\b", Pattern.CASE_INSENSITIVE);
                result = pattern.matcher(result).replaceAll(damengKeyword);
            }
        }

        // 应用自定义关键字映射
        if (config.getCustomKeywordMapping() != null) {
            for (Map.Entry<String, String> entry : config.getCustomKeywordMapping().entrySet()) {
                String mysqlKeyword = entry.getKey();
                String damengKeyword = entry.getValue();

                Pattern pattern = Pattern.compile("\\b" + Pattern.quote(mysqlKeyword) + "\\b", Pattern.CASE_INSENSITIVE);
                result = pattern.matcher(result).replaceAll(damengKeyword);
            }
        }

        return result;
    }
    
    /**
     * 处理特殊语法
     */
    private String handleSpecialSyntax(String sql) {
        String result = sql;
        
        // 处理LIMIT语法的复杂情况
        result = handleComplexLimit(result);
        
        // 处理GROUP_CONCAT
        result = handleGroupConcat(result);
        
        // 处理EXTRACT函数的闭合括号
        result = handleExtractFunction(result);
        
        return result;
    }
    
    /**
     * 处理复杂的LIMIT语法
     */
    private String handleComplexLimit(String sql) {
        if (!config.isLimitConversionEnabled()) {
            return sql;
        }

        // 处理带有ORDER BY的LIMIT
        Pattern limitWithOrderBy = Pattern.compile(
            "(.*ORDER\\s+BY\\s+[^)]+)\\s+LIMIT\\s+(\\d+)(?:\\s*,\\s*(\\d+))?\\s*$",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        Matcher matcher = limitWithOrderBy.matcher(sql);
        if (matcher.matches()) {
            String mainQuery = matcher.group(1);
            String offset = matcher.group(2);
            String limit = matcher.group(3);

            if (limit != null) {
                // LIMIT offset, count
                int offsetNum = Integer.parseInt(offset);
                int limitNum = Integer.parseInt(limit);
                return "SELECT * FROM (SELECT ROWNUM rn, t.* FROM (" + mainQuery + ") t WHERE ROWNUM <= " +
                       (offsetNum + limitNum) + ") WHERE rn > " + offsetNum;
            } else {
                // LIMIT count
                return "SELECT * FROM (" + mainQuery + ") WHERE ROWNUM <= " + offset;
            }
        }

        return sql;
    }
    
    /**
     * 处理GROUP_CONCAT函数
     */
    private String handleGroupConcat(String sql) {
        Pattern groupConcatPattern = Pattern.compile(
            "GROUP_CONCAT\\s*\\(\\s*([^)]+)\\s*\\)", 
            Pattern.CASE_INSENSITIVE
        );
        
        Matcher matcher = groupConcatPattern.matcher(sql);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String content = matcher.group(1);
            String replacement = "LISTAGG(" + content + ", ',') WITHIN GROUP (ORDER BY " + content + ")";
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 处理EXTRACT函数的闭合括号
     */
    private String handleExtractFunction(String sql) {
        // 为EXTRACT函数添加缺失的闭合括号
        Pattern extractPattern = Pattern.compile(
            "EXTRACT\\s*\\(\\s*(YEAR|MONTH|DAY)\\s+FROM\\s+([^)]+)", 
            Pattern.CASE_INSENSITIVE
        );
        
        Matcher matcher = extractPattern.matcher(sql);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String unit = matcher.group(1);
            String field = matcher.group(2);
            String replacement = "EXTRACT(" + unit + " FROM " + field + ")";
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
}
