package com.goodsogood.ows.model.vo.experience;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: zhangta<PERSON>
 * @create: 2022-03-18 10:35
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ThoryArmsForm {
    //用户id
    @JsonProperty("user_id")
    @ApiModelProperty(value = "user_id")
    private Long userId;
    //排序的值
    @JsonProperty("value")
    @ApiModelProperty(value = "value")
    private Long value;
    //序号
    @JsonProperty("rownum")
    @ApiModelProperty(value = "rownum")
    private Long rownum;
}
