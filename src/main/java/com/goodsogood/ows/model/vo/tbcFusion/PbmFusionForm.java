package com.goodsogood.ows.model.vo.tbcFusion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData;
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData;
import lombok.Data;

import java.util.List;

@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PbmFusionForm {
    private FusionItemData itemData;
    private List<FusionBaseData> dataList;

    public FusionItemData getItemData() {
        return itemData;
    }

    public void setItemData(FusionItemData itemData) {
        this.itemData = itemData;
    }

    public List<FusionBaseData> getDataList() {
        return dataList;
    }

    public void setDataList(List<FusionBaseData> dataList) {
        this.dataList = dataList;
    }
}
