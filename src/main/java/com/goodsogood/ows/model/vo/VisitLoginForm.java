package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022-04-14 11:36:48
 * @Description VisitLoginForm
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VisitLoginForm {

    @JsonProperty(value = "login_id")
    @ApiModelProperty("登录用户ID")
    private Long loginId;

    @JsonProperty(value = "visit_days")
    @ApiModelProperty("累计访问总天数")
    private Integer visitDays;

    /**
     * 组织下今日首次登录的人员中最晚的那个登录时间
     */
    @JsonProperty(value = "visit_time")
    @ApiModelProperty("登录时间")
    private LocalDateTime visitTime;

}
