package com.goodsogood.ows.model.vo.dataCockpit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PartyFeePaymentVo {

    @ApiModelProperty("本月缴纳党费")
    private Double submittedAmount = 0.0;

    @ApiModelProperty("本月已交人数")
    private Integer submittedMemberNum = 0;

    @ApiModelProperty("本月未交人数")
    private Integer notSubmittedMemberNum = 0;

    @ApiModelProperty("今年本月数据统计")
    private List<MonthData> thisYearMonthData;

    @ApiModelProperty("去年本月数据统计")
    private List<MonthData> lastYearMonthData;


    @JsonInclude(JsonInclude.Include.NON_NULL)
    public class MonthData{
        @ApiModelProperty("天数")
        private Integer day;

        @ApiModelProperty("缴纳党费人数")
        private Integer submittedMemberNum = 0;

        public Integer getDay() {
            return day;
        }

        public void setDay(Integer day) {
            this.day = day;
        }

        public Integer getSubmittedMemberNum() {
            return submittedMemberNum;
        }

        public void setSubmittedMemberNum(Integer submittedMemberNum) {
            this.submittedMemberNum = submittedMemberNum;
        }
    }

    public Double getSubmittedAmount() {
        return submittedAmount;
    }

    public void setSubmittedAmount(Double submittedAmount) {
        this.submittedAmount = submittedAmount;
    }

    public Integer getSubmittedMemberNum() {
        return submittedMemberNum;
    }

    public void setSubmittedMemberNum(Integer submittedMemberNum) {
        this.submittedMemberNum = submittedMemberNum;
    }

    public Integer getNotSubmittedMemberNum() {
        return notSubmittedMemberNum;
    }

    public void setNotSubmittedMemberNum(Integer notSubmittedMemberNum) {
        this.notSubmittedMemberNum = notSubmittedMemberNum;
    }

    public List<MonthData> getThisYearMonthData() {
        return thisYearMonthData;
    }

    public void setThisYearMonthData(List<MonthData> thisYearMonthData) {
        this.thisYearMonthData = thisYearMonthData;
    }

    public List<MonthData> getLastYearMonthData() {
        return lastYearMonthData;
    }

    public void setLastYearMonthData(List<MonthData> lastYearMonthData) {
        this.lastYearMonthData = lastYearMonthData;
    }
}
