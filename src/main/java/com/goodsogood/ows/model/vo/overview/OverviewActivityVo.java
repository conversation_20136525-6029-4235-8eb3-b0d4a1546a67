package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OverviewActivityVo extends  OverviewBaseVo{

    @JsonProperty(value = "list")
    public List<Series> listSeries;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Series {
        /**
         * 活动名称
         */
        @JsonProperty(value = "name")
        private String name;

        /**
         * 1.表示完成 2.未完成
         */
        @JsonProperty(value = "isFinish")
        private Integer is_finish;

        //占比
        private String rate;

        @JsonProperty(value = "period_str")
        private String periodStr;

        /**
         * 完成次数
         */
        public Integer number;


        /**
         * 分配的支部数
         */
        public Integer total;



        /**
         * 1.党支部党员大会 2.党课 3.党支部委员会会议 4.党小组会 5.主题党日 6.上半年组织生活会 7.今年民主生活会8.谈心谈话
         */
        public Integer type;


        /**
         * 支部开展活动结果
         */
        @JsonProperty(value = "result")
        private String result;
    }
}
