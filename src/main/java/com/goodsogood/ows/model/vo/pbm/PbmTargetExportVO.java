package com.goodsogood.ows.model.vo.pbm;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PbmTargetExportVO {

    @ApiModelProperty("人员姓名")
    @Excel(name = "姓名")
    private String userName;


    @ApiModelProperty("手机号")
    @Excel(name = "手机号")
    private String phone;


    @ApiModelProperty("所在单位")
    @Excel(name = "所在单位")
    private String unit;


    @ApiModelProperty("所在部门")
    @Excel(name = "所在部门")
    private String department;


    @ApiModelProperty("是否党员 1-是 2-否")
    @Excel(name = "是否党员 1-是 2-否")
    private String isPartyMember;


    @ApiModelProperty("所在支部")
    @Excel(name = "所在支部")
    private String branch;


    @ApiModelProperty("月度绩效得分")
    @Excel(name = "月度绩效得分")
    private Double score;

    @ApiModelProperty("备注")
    @Excel(name = "备注")
    private String remark;
}
