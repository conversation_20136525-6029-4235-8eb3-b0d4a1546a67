package com.goodsogood.ows.model.vo.operation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.goodsogood.ows.helper.Json.DoubleKeepZeroSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Temporal;
import javax.persistence.Transient;

/**
 * @ClassName : VisitDateResult
 * <AUTHOR> tc
 * @Date: 2022/4/15 10:14
 * @Description : 返回结果类
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VisitDateResult {

    @JsonProperty(value = "region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;

    /**
     * 格式 YYYY-MM-DD
     */
    @JsonProperty(value = "stats_date")
    @ApiModelProperty("统计日期")
    private String statsDate;

    @JsonProperty(value = "user_id")
    @ApiModelProperty("用户编号")
    private Long userId;

    @JsonProperty(value = "user_name")
    @ApiModelProperty("用户名称")
    private String userName;

    @JsonProperty(value = "org_id")
    @ApiModelProperty("组织编号")
    private Long orgId;

    /**
     * 优先组织简称，如果没有就用全称
     */
    @JsonProperty(value = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    /**
     * 每人每天只计算第一次访问
     */
    @JsonProperty(value = "visit_num")
    @ApiModelProperty("访问人数")
    private Integer visitNum = 0;

    /**
     * 访问率
     */
    @JsonProperty(value = "visit_rate")
    @ApiModelProperty("访问率")
    @JsonSerialize(using = DoubleKeepZeroSerializer.class)
    private Double visitRate;

    @JsonProperty(value = "visit_days")
    @ApiModelProperty("人均访问天数")
    @JsonSerialize(using = DoubleKeepZeroSerializer.class)
    private Double visitDays = 0d;

    /**
     * 00:00-01:00 格式 HH-mm
     */
    @JsonProperty(value = "range")
    @ApiModelProperty("时间范围")
    private String range;

    /**
     * 本年最高访问量功能的日期	格式 YYYY-MM-DD
     */
    @JsonProperty(value = "top_date_year")
    @ApiModelProperty("本年访问量最高人次的日期")
    private String topVisitYear;

    @JsonProperty(value = "menu_name_year")
    @ApiModelProperty("本年最高访问量功能的名称")
    private String menuNameYear;

    @JsonProperty(value = "click_num_year")
    @ApiModelProperty("本年访问量最高人次的数")
    private Integer visitNumYear;

    /**
     * 本月最高访问量功能的日期		格式 YYYY-MM-DD
     */
    @JsonProperty(value = "top_date_month")
    @ApiModelProperty("本月访问量最高人次的日期")
    private String topVisitMonth;

    @JsonProperty(value = "menu_name_month")
    @ApiModelProperty("本月最高访问量功能的名称")
    private String menuNameMonth;

    @JsonProperty(value = "click_num_month")
    @ApiModelProperty("本月访问量最高人次的数")
    private Integer visitNumMonth;

    @JsonProperty(value = "menu_name")
    @ApiModelProperty("功能名称")
    private String menuName;

    @JsonProperty(value = "click_num")
    @ApiModelProperty("点击量")
    private Long clickNum;




    /**
     * 组织人数
     */
    @Transient
    private Integer peopleNum;

    /**
     * 最后一次点击或者访问时间
     */
    @Transient
    private String visitTime = "1900-01-01 00:00:00";
}
