package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * @program: ows-sas
 * @description: 组织得分 vo
 * @author: Mr.<PERSON>
 * @create: 2020-11-23 15:11
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgScoreVo {

    private String parentId;

    private String name;

    private String orgTypeChild;

    private String shortName;

    private String isRetire;

    private String orgPhone;

    private String orgContacts;

    private String ownerId;

    private Integer dateMonth;

    private Long orgId;

}
