package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RevisitRankVo {

    @ApiModelProperty("排名")
    @JsonProperty(value = "rank")
    private Integer rank;

    @ApiModelProperty("统计数量")
    @JsonProperty(value = "count_num")
    private Integer countNum;

    @ApiModelProperty("用户姓名")
    @JsonProperty(value = "user_name")
    private String userName;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "own_id")
    private Long ownId;

    @ApiModelProperty("单位名称")
    @JsonProperty(value = "own_name")
    private String ownName;

    @ApiModelProperty("完成率")
    @JsonProperty(value = "rate")
    private Double rate;
}
