package com.goodsogood.ows.model.vo.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Create by FuXiao on 2020/10/27
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ScoreOverviewForm {

    @JsonProperty(value = "org_id")
    @ApiModelProperty(name = "组织id")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty(name = "组织名称")
    private String orgName;

    @JsonProperty(value = "year")
    @ApiModelProperty(name = "年度")
    private Long year;

    @JsonProperty(value = "org_tpye")
    @ApiModelProperty(name = "组织类型")
    private Integer orgType;

    @JsonProperty(value = "score")
    @ApiModelProperty(name = "得分")
    private Double score;
}
