package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用文件上传类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FileCommonForm {
    /**
     * 文件流转状态
     * -1 下载文件发生了错误  1.生成下载文件(开始上传到文件中心) 2.文件中心正在处理上传当中 3.文件已经上传到了oss
     */
    private Integer status;
    /**
     * 是否上传成功
     */
    private boolean isUploadSuc=false;
    /**
     * 随机生成串token
     */
    private String token;
    /**
     * 错误提示信息
     */
    @JsonProperty("err_msg")
    private String errorMsg;
    /**
     * 文件信息
     */
    private FileInfo fileInfo;

    @Data
    public static class FileInfo {
        /**
         * 文件id
         */
        private Long id;
        /**
         * 文件名称
         */
        private String name;
        /**
         * 文件路径
         */
        private String path;
    }

}
