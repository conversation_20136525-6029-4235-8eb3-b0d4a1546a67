package com.goodsogood.ows.model.vo.supervise;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.configuration.SuperviseConfig;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SuperviseTipsForm {

    /**
     * 本组织异常项
     */
    @JsonProperty(value = "self")
    private Integer self;

    /**
     * 下级组织异常项
     */
    @JsonProperty(value = "sub")
    private Integer sub;

    /**
     * 是否能点击 一健预警
     */
    @JsonProperty(value = "is_click")
    private Integer isClick;

    @JsonProperty(value = "tips")
    private List<SuperviseConfig.Tips> listTips;

}
