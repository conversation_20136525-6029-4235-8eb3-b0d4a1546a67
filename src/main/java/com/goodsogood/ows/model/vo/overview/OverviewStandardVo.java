package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OverviewStandardVo  extends OverviewBaseVo{

    @JsonProperty(value = "total")
    public Integer total;

    @JsonProperty(value = "list")
    public List<KeyValVo> listKeyValue;
}
