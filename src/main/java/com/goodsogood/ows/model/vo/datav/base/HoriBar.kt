package com.goodsogood.ows.model.vo.datav.base

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.goodsogood.ows.helper.Json.DoubleKeepTwoSerializer
import java.io.Serializable

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class HoriBar @JvmOverloads constructor(
    // 横向柱状图中每个柱子的类目，即y轴的坐标值。该字段类型与格式须与配置项中y轴的标签类型与格式保持一致。
    var y: String? = null,
    // 横向柱状图中每个柱子的值，即x轴的值。
    @JsonSerialize(using = DoubleKeepTwoSerializer::class)
    var x: Double? = null,
    // （可选）系列值。
    var s: String? = null,
    // 颜色映射对象，默认为"类型A"
    @JsonProperty("colorField")
    var colorField: String? = "类型A",
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 127467930592456403L
    }
}

