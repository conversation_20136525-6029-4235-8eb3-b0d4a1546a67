package com.goodsogood.ows.model.vo.datav

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.goodsogood.ows.model.vo.datav.base.CardFlop
import com.goodsogood.ows.model.vo.datav.base.OtherCardFlop
import java.io.Serializable

/**
 * <AUTHOR>
 * @date 2023/3/29
 * @description class ThreeVO 可视化大屏党业融合 -  三指标
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ThreeVO @JvmOverloads constructor(
    // 党建平均值翻牌器
    var cardFlopAvg0: MutableList<CardFlop> = mutableListOf(),
    // 党建最高值翻牌器
    var cardFlopMax0: MutableList<CardFlop> = mutableListOf(),
    // 党建最低值翻牌器
    var cardFlopMin0: MutableList<CardFlop> = mutableListOf(),

    // 业务平均值翻牌器
    var cardFlopAvg1: MutableList<CardFlop> = mutableListOf(),
    // 业务最高值翻牌器
    var cardFlopMax1: MutableList<CardFlop> = mutableListOf(),
    // 业务最低值翻牌器
    var cardFlopMin1: MutableList<CardFlop> = mutableListOf(),


    // 创新平均值翻牌器
    var cardFlopAvg2: MutableList<CardFlop> = mutableListOf(),
    // 创新最高值翻牌器
    var cardFlopMax2: MutableList<CardFlop> = mutableListOf(),
    // 创新最低值翻牌器
    var cardFlopMin2: MutableList<CardFlop> = mutableListOf(),
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 2407099679069968339L
    }
}
