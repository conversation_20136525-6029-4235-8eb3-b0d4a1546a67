package com.goodsogood.ows.model.vo.user

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

/**
 *
 * <AUTHOR>
 * @createTime 2022年07月13日 13:57:00
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class PbmUserInfo(

    var userId: Long? = null,

    var name: String? = null,

    var politicalType: Int? = null,

    var phoneSecret: String? = null,

    var sequence: Int? = null,

    var title: String? = null,

    var orgId: Long? = null,

    var orgName: String? = null,

    var isEmployee: Int = 0
)
