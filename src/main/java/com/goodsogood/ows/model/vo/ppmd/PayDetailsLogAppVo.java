package com.goodsogood.ows.model.vo.ppmd;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 党支部个人党费交纳记录(移动端)
 *
 * <AUTHOR> tc
 * @date 2019/07/25
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PayDetailsLogAppVo {

    @ApiModelProperty("应交人员编号")
    private Long userId;

    /**
     * 单位 : 元
     */
    @ApiModelProperty("金额")
    private Double payAlready;

    /**
     * 记录类型:1 正常交纳记录、2 免交记录、3 补交记录、4 正常欠交记录 5 少交欠交记录 6 固定金额欠交记录
     */
    @ApiModelProperty("记录类型")
    private Integer payLogType;



}
