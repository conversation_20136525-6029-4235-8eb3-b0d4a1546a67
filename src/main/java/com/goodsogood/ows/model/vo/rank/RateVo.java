package com.goodsogood.ows.model.vo.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.goodsogood.ows.model.db.rank.RateDetailsEntity;
import com.goodsogood.ows.model.db.rank.RateRuleEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * Create by FuXiao on 2020/10/22
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RateVo {

    private RateRuleEntity rateRuleEntity;

    private RateDetailsEntity[] rateDetailsEntities;
}
