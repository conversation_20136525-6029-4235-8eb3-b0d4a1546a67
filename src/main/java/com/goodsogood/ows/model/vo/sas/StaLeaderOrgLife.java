package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;


/**
 * @program: ows-sas
 * @description: 统计领导组织生活信息
 * @author: Mr.<PERSON>
 * @create: 2019-04-24 13:54
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StaLeaderOrgLife {

    @JsonProperty(value = "org_name")
    private String orgName;


    @JsonProperty(value = "activity_type_name")
    private String activityTypeName;


    @JsonProperty(value = "leader_user_id")
    private Long leaderUserId;

    @JsonProperty(value = "leader_name")
    private String leaderName;


    @JsonProperty(value = "activity_type_id")
    private String activityTypeId;

    /**
     * 每个用户对应它的统计
     */
    private StaMonthWrapper listWrapper;

    /**
     * 查询到用户组织生活 命中id 多个以逗号分隔
     */
    @JsonIgnore
    private String ids;

}


