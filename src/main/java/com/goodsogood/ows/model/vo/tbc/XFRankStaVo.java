package com.goodsogood.ows.model.vo.tbc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)  //json 转实例忽略为空
@JsonIgnoreProperties(ignoreUnknown = true) //转json 忽略为空
public class XFRankStaVo {


    //1.卷烟营销党员 2.烟叶生成党员 3.专卖管理党员 4.综合管理党员
    @JsonProperty(value = "seq_number")
    private Integer seqNumber;

    //个人的先锋指数
    @JsonProperty(value = "xf_index")
    private Double xfIndex;


    //本单位人排名
    @JsonProperty(value = "unit_rank")
    private Integer unitRank;

    //本单位党员的总数
    @JsonProperty(value = "unit_total")
    private Integer unitTotal;

    //所属序列百分比
    @JsonProperty(value = "sequence_rank")
    private Double sequenceRank;


    //所属序列百分比
    @JsonProperty(value = "city_rank")
    private Double cityRank;


    //全市排名名次
    @JsonProperty(value = "city_rank_number")
    private Integer cityRankNumber;


    //全市党员的人数
    @JsonProperty(value = "city_number")
    private Integer cityNumber;

}
