package com.goodsogood.ows.model.vo.ppmd;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Create by FuXiao on 2018/12/21
 */
@Data
@ApiModel
public class OrgNumVo {
    @JsonProperty(value = "org_id")
    @ApiModelProperty("组织ID")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    @JsonProperty(value = "num")
    @ApiModelProperty("组织人数")
    private Long num;
}
