package com.goodsogood.ows.model.vo.user

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class UserExpandInfoForm {

    @ApiModelProperty("用户ID")
    var userId: Long? = null

    @ApiModelProperty("用户姓名")
    var name: String? = null

    @ApiModelProperty("性别")
    var gender: Int? = null

    @ApiModelProperty("头像")
    var headUrl: String? = null

    @ApiModelProperty("党内职务")
    var orgPeriodName: String? = null

    @ApiModelProperty("党内职务权重")
    var orgPeriodSeq: Int = 9999

    @ApiModelProperty("入党时间")
    var joiningTime: String? = null

    @ApiModelProperty("党龄")
    var partyAge: String? = null

    @ApiModelProperty("脱敏手机号")
    var phoneSecret: String? = null

    @ApiModelProperty("个人积分")
    var score: Int = 0

    @ApiModelProperty("先锋指数")
    var pioneerScore: Double = 0.0

    constructor()

    constructor(
        userId: Long?,
        name: String?,
        headUrl: String?,
        orgPeriodName: String?,
        partyAge: String?,
        phoneSecret: String?,
        score: Int,
        pioneerScore: Double
    ) {
        this.userId = userId
        this.name = name
        this.headUrl = headUrl
        this.orgPeriodName = orgPeriodName
        this.partyAge = partyAge
        this.phoneSecret = phoneSecret
        this.score = score
        this.pioneerScore = pioneerScore
    }

}

@ApiModel
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class UserPioneerScoreForm {

    @ApiModelProperty("用户ID")
    var userId: Long? = null

    @ApiModelProperty("个人积分")
    var score: Int = 0

    @ApiModelProperty("先锋指数")
    var pioneerScore: Double = 0.0
}

@ApiModel
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class UserPioneerScoreQueryForm {

    @ApiModelProperty("组织ID")
    var orgId: Long? = null

    @ApiModelProperty("用户编号列表")
    var userList: List<Long>? = null

    constructor()

    constructor(orgId: Long?, userList: List<Long>?) {
        this.orgId = orgId
        this.userList = userList
    }

}