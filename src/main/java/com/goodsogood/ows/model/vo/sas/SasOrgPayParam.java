package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SasOrgPayParam {

    @JsonProperty("month1")
    private String month1;

    @JsonProperty("month2")
    private String month2;

    @JsonProperty("month3")
    private String month3;

    @JsonProperty("month4")
    private String month4;

    @JsonProperty("month5")
    private String month5;

    @JsonProperty("month6")
    private String month6;

    @JsonProperty("month7")
    private String month7;

    @JsonProperty("month8")
    private String month8;

    @JsonProperty("month9")
    private String month9;

    @JsonProperty("month10")
    private String month10;

    @JsonProperty("month11")
    private String month11;

    @JsonProperty("month12")
    private String month12;

}
