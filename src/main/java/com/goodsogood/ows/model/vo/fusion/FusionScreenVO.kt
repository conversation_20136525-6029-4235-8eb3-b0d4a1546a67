package com.goodsogood.ows.model.vo.fusion

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

/**
 *
 * <AUTHOR>
 * @createTime 2023年04月06日 10:30:00
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FusionScreenVO(
    // 支部堡垒指数
    var orgFortress: IndexVO = IndexVO(),
    // 党员先锋指数
    var memberPioneer: IndexVO = IndexVO()
) {
    override fun toString(): String {
        return "FusionScreenVO(orgFortress=$orgFortress, memberPioneer=$memberPioneer)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ThreeIndicatorsVO(
    // 组织
    var org: IndicatorsVO = IndicatorsVO(),
    // 党员
    var user: IndicatorsVO = IndicatorsVO()
) {
    override fun toString(): String {
        return "ThreeIndicatorsVO(org=$org, user=$user)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FourFusionVO(
    // 目标
    var target: IndexVO = IndexVO(),
    // 组织
    var org: IndexVO = IndexVO(),
    // 工作
    var work: IndexVO = IndexVO(),
    // 数据
    var data: IndexVO = IndexVO()
) {
    override fun toString(): String {
        return "FusionVO(target=$target, org=$org, work=$work, data=$data)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FusionVO(
    // 目标
    var target: Double = 0.0,
    // 组织
    var org: Double = 0.0,
    // 工作
    var work: Double = 0.0,
    // 数据
    var data: Double = 0.0
) {
    override fun toString(): String {
        return "FusionVO(target=$target, org=$org, work=$work, data=$data)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FusionQualityVO(
    // 党建
    var build: Double = 0.0,
    // 业务
    var business: Double = 0.0
) {
    override fun toString(): String {
        return "BuildAndBusinessVO(build=$build, business=$business)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class IndicatorsVO(
    // 党建
    var build: IndexVO = IndexVO(),
    // 业务
    var business: IndexVO = IndexVO(),
    // 创新
    var innovate: IndexVO = IndexVO()
) {
    override fun toString(): String {
        return "IndicatorsVO(build=$build, business=$business, innovate=$innovate)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class IndexVO(
    var avg: Double = 0.0,
    var max: Double = 0.0,
    var min: Double = 0.0
) {
    override fun toString(): String {
        return "IndexVO(avg=$avg, max=$max, min=$min)"
    }
}

