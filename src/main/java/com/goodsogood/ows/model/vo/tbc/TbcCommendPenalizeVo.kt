package com.goodsogood.ows.model.vo.tbc

/**
 *
 * <AUTHOR>
 * @createTime 2023年03月07日 14:19:00
 */
data class TbcCommendPenalizeVo(

    var type: Int? = null,

    var num: Int = 0,

    var list: MutableList<String> = mutableListOf()
) {
    override fun toString(): String {
        return "TbcCommendPenalizeVo(type=$type,  list=$list)"
    }
}

data class TbcCommendPenalizeOneVo(

    var type: Int? = null,

    var item: String? = null
) {
    override fun toString(): String {
        return "TbcCommendPenalizeVo(type=$type,  item=$item)"
    }
}

data class TbcCommendPenalizeDbForm(

    // 用户ID\组织ID
    var id: Long? = null,
    // 1-用户，2-组织
    var type: Int = 1,
    // 奖励级别 level
    var level: Int? = null,
    // 奖励名称 key
    var name: Int? = null
) {
    override fun toString(): String {
        return "TbcCommendPenalizeDbForm(id=$id, type=$type, name=$name)"
    }
}

data class UserCorpInfo(

    var userId: Long? = null,

    var username: String? = null,

    var corpName: String? = null
) {
    override fun toString(): String {
        return "UserCorpInfo(userId=$userId, username=$username, corpName=$corpName)"
    }
}


/**
 * 党业融合 -指标数据统计
 */
data class TbcPbmVo(

    var avgIndex: Int? = null,

    var minIndex: Int? = null,

    var maxIndex: Int? = null
) {
    override fun toString(): String {
        return "TbcPbmVo(avgIndex=$avgIndex, minIndex=$minIndex, maxIndex=$maxIndex)"
    }
}
