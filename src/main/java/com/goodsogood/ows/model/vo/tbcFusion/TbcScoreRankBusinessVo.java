package com.goodsogood.ows.model.vo.tbcFusion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName : TbcScoreRankBusinessVo
 * <AUTHOR> tc
 * @Date: 2022/6/20 18:48
 * @Description : 业务积分排名信息
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TbcScoreRankBusinessVo {

    @ApiModelProperty("用户编号")
    @JsonProperty(value = "user_id")
    private Long userId;

    @ApiModelProperty("业务积分")
    @JsonProperty(value = "business_score")
    private Long businessScore;

    @ApiModelProperty("本单位同序列员工中排名")
    @JsonProperty(value = "unit_rank")
    private Integer unitRank;

    @ApiModelProperty("本单位同序列员工中人数")
    @JsonProperty(value = "unit_num")
    private Integer unitNum;

    @ApiModelProperty("全市同序列员工中排名")
    @JsonProperty(value = "marketing_rank")
    private Integer marketingRank;

    @ApiModelProperty("全市同序列员工中人数")
    @JsonProperty(value = "marketing_num")
    private Integer marketingNum;

    @ApiModelProperty("全市同序列党员中排名")
    @JsonProperty(value = "all_rank")
    private Integer allRank;

    @ApiModelProperty("全市同序列党员中人数")
    @JsonProperty(value = "all_num")
    private Integer allNum;

}
