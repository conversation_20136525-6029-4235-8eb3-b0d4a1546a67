package com.goodsogood.ows.model.vo.tbc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> ruoyu
 * @date : 2020/12/28
 * 党组织情况主要返回结构
 */
@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class) //自动驼峰命名
@JsonInclude(JsonInclude.Include.NON_NULL)  //json 转实例忽略为空
@JsonIgnoreProperties(ignoreUnknown = true) //转json 忽略为空
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PartyOrganizationForm {

    @ApiModelProperty(value = "组织架构")
    private List<TbcTbcOrganStructureForm> structure;

    @ApiModelProperty(value = "换届信息")
    private TbcPartyOrganizationPeriodForm changePeriod;

    @ApiModelProperty(value = "组织数量")
    private TbcPartyOrganizationPartyNumsForm partyNums;

    @ApiModelProperty(value = "党支部设置情况")
    private List<TbcPartyOrganizationTbcBranchForm> partyBranch;

    @ApiModelProperty(value = "支委会设置内容")
    private TbcPartyOrganizationChangePeriodTagForm changePeriodTag;

    /**
     * 组织架构
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
//    @Builder
    public static class TbcTbcOrganStructureForm extends TbcOrganBaseForm {
//        @ApiModelProperty(value = "组织id")
//        private Long orgId;
//
//        @ApiModelProperty(value = "组织名称")
//        private String orgName;

        @ApiModelProperty(value = "1:是 2:否   如果为1 可以继续向下钻取,数据结构为structure")
        private Integer hasChild;

        @ApiModelProperty(value = "1:是 2:否 是否含有党小组")
        private Integer hasGroup;

        private List<TbcTbcOrganStructureForm> structure;

        @ApiModelProperty(value = "党组织名称")
        private List<String> group;
    }


    /**
     * 换届信息
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class TbcPartyOrganizationPeriodForm {
        @ApiModelProperty(value = "6月内应换届的数量")
        private Integer sixExpireNum;

        @ApiModelProperty(value = "6月内应换届的支部")
        private List<TbcTbcOrganInnerPeriodForm> sixExpireData;

        @ApiModelProperty(value = "本月应换届的支部数量")
        private Integer nowExpireNum;

        @ApiModelProperty(value = "本月应换届的支部")
        private List<TbcTbcOrganInnerPeriodForm> nowExpireData;

        @ApiModelProperty(value = "已到期的支部数量")
        private Integer alreadyExpireNum;

        @ApiModelProperty(value = "已到期的支部")
        private List<TbcTbcOrganInnerPeriodForm> alreadyExpireData;

        @ApiModelProperty(value = "已设置的支部")
        private List<TbcTbcOrganInnerPeriodForm> validData;
    }

    /**
     * 组织数量
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class TbcPartyOrganizationPartyNumsForm {
        @ApiModelProperty(value = "党总支数量")
        private Integer party1;

        @ApiModelProperty(value = "党支部数量")
        private Integer party2;

        @ApiModelProperty(value = "党小组数量")
        private Integer party3;
    }

    /**
     * 党支部设置情况
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
//    @Builder
    public static class TbcPartyOrganizationTbcBranchForm extends TbcOrganBaseForm {
//        @ApiModelProperty(value = "组织id")
//        private Long orgId;
//
//        @ApiModelProperty(value = "组织名称")
//        private String orgName;

        @ApiModelProperty(value = "人数")
        private Integer nums;
    }

    /**
     * 支委会设置内容
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class TbcPartyOrganizationChangePeriodTagForm {
        @ApiModelProperty(value = "应设置支委会数量")
        private Integer shouldTagNum;

        @ApiModelProperty(value = "已设置支委会数量")
        private Integer setTagNum;

        @ApiModelProperty(value = "未设置支委会数量")
        private Integer notTagNum;

        @ApiModelProperty(value = "未设置支委会数量的支部")
        private List<TbcTbcOrganInnerPeriodForm> notTagData;
    }


    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
//    @Builder
    public static class TbcTbcOrganInnerPeriodForm extends TbcOrganBaseForm {
//        @ApiModelProperty(value = "组织架构")
//        private Long orgId;
//
//        @ApiModelProperty(value = "组织名称")
//        private String orgName;

        @ApiModelProperty(value = "人数")
        private Integer nums;

        @ApiModelProperty(value = "当前届次")
        private String nowPeriod;

        @ApiModelProperty(value = "是否创建届次")
        private Integer isCreatePeriod;

        @ApiModelProperty(value = "是否创建支委会标签")
        private Integer isCreatePeriodTag;

        @ApiModelProperty(value = "届次状态 1:本月到期  2:6个月内到期  3:已到期未换届  4:已设置")
//        @JsonIgnore
        private Integer type;
    }
}
