package com.goodsogood.ows.model.vo.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * {@author} TangLilin
 * {@code @date} 2023-03-08 11:41:34
 * {@code @description} StudyForm
 */
@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StudyForm {
    public Study studyScore;
    public Study studyTime;
    public List<StudyMap> trendList;
    public StudyDuration studyDuration;

    @Data
    @ApiModel
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class StudyMap {
        public Integer year = 0;
        public Integer month = 0;
        public Integer score = 0;
    }

    @Data
    @ApiModel
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Study {
        /**
         * 累计最高
         */
        public Integer maxTotal = 0;
        /**
         * 累计平均
         */
        public Integer averTotal = 0;
        /**
         * 年度最高
         */
        public Integer yearMaxTotal = 0;
        /**
         * 年度平均
         */
        public Integer yearAverTotal = 0;
    }

    @Data
    @ApiModel
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class StudyDuration {
        /**
         * 集中学习时长
         */
        public Integer learnTime = 0;
        /**
         * 参与学习人次
         */
        public Integer trainingTime = 0;
        /**
         * 每日一练人数
         */
        public Integer dayTotal = 0;
        /**
         * 每月一测人数
         */
        public Integer monthTotal = 0;
        /**
         * 每季一考人数
         */
        public Integer quarterTotal = 0;
        /**
         * 总党员数
         */
        public Integer partyTotal = 0;
    }

}
