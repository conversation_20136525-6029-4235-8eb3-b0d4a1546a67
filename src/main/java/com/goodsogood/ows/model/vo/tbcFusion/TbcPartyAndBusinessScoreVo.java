package com.goodsogood.ows.model.vo.tbcFusion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName : TbcScoreUserRankVo
 * <AUTHOR> tc
 * @Description : 党建积分和业务信息VO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TbcPartyAndBusinessScoreVo {

    @ApiModelProperty("用户编号")
    @JsonProperty(value = "user_id")
    private Long userId;

    @ApiModelProperty("组织编号")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty("党建积分")
    @JsonProperty(value = "party_score")
    private Long partyScore;

    @ApiModelProperty("业务积分")
    @JsonProperty(value = "business_score")
    private Long businessScore;

    @ApiModelProperty("党建排名")
    @JsonProperty(value = "party_rank")
    private Integer partyRank;

    @ApiModelProperty("党建排名")
    @JsonProperty(value = "business_rank")
    private Integer businessRank;

    @ApiModelProperty("单位数量")
    @JsonProperty(value = "count")
    private Integer count;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Long getPartyScore() {
        return partyScore;
    }

    public void setPartyScore(Long partyScore) {
        this.partyScore = partyScore;
    }

    public Long getBusinessScore() {
        return businessScore;
    }

    public void setBusinessScore(Long businessScore) {
        this.businessScore = businessScore;
    }

    public Integer getPartyRank() {
        return partyRank;
    }

    public void setPartyRank(Integer partyRank) {
        this.partyRank = partyRank;
    }

    public Integer getBusinessRank() {
        return businessRank;
    }

    public void setBusinessRank(Integer businessRank) {
        this.businessRank = businessRank;
    }

}
