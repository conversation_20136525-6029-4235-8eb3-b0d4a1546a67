package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version 2019/9/3 10:07
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EvalMobileDataCollectionForm {

    @ApiModelProperty("考核项目id")
    @JsonProperty(value = "type_id")
    private Integer typeId;

    @ApiModelProperty("考核项目名称")
    @JsonProperty(value = "type_name")
    private String typeName;

    @ApiModelProperty("排序")
    @JsonProperty(value = "level")
    private Integer level;

    @ApiModelProperty("考核周期 1:月,2:季度")
    @JsonProperty(value = "period")
    private Integer period;

    @ApiModelProperty("未完成的组织信息\tperiod=2时,只有一条数据")
    @JsonProperty(value = "data")
    private List<Detail> data;

    @Data
    @ApiModel
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Detail {

        @ApiModelProperty("月份\tperiod=1时有")
        @JsonProperty(value = "month")
        private Integer month;

        @ApiModelProperty("未完成组织数量")
        @JsonProperty(value = "total")
        private Integer total = 0;

        @ApiModelProperty("组织列表")
        @JsonProperty(value = "org_list")
        private List<OrganizationBase> orgList = new ArrayList<>();
    }

}
