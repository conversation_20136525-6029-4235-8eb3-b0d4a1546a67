package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户积分
 * <AUTHOR>
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserScoreInfoVo {

    @ApiModelProperty("用户编号")
    @JsonProperty(value = "user_id")
    private Long userId;

    @ApiModelProperty("用户获得积分")
    @JsonProperty(value = "in_score")
    private Integer inScore;

    @ApiModelProperty("用户消费积分")
    @JsonProperty(value = "out_score")
    private Integer outScore;

    public UserScoreInfoVo(Long userId, Integer inScore, Integer outScore) {
        this.userId = userId;
        this.inScore = inScore;
        this.outScore = outScore;
    }
}
