package com.goodsogood.ows.model.vo.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RankPeriodIsEquleFrom {

    private Long periodId;

    private Date startTime;

    private Date endTime;

    private Long unixStartTime;

    private Long unixEndTime;

    private Long userId;

    private String userName;

    private String positionName;
}
