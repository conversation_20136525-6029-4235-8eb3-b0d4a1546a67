package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Create by FuXiao on 2018/12/21
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActiveUserVo {

    @JsonProperty(value = "user_id")
    @ApiModelProperty("用户ID")
    private Long userId;

    @JsonProperty(value = "user_name")
    @ApiModelProperty("用户名称")
    private String userName;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    @JsonProperty(value = "pay_date")
    @ApiModelProperty("缴纳时间")
    private Date payDate;
}
