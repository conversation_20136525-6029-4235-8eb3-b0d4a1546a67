package com.goodsogood.ows.model.vo.ppmd;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 组织信息返回类
 *
 * <AUTHOR>
 * @date 2018/12/04
 */
@Data
@Builder
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrgInfoVo {

    @JsonProperty(value = "party_id")
    @ApiModelProperty("党委组织ID")
    private Long officeId;

    @JsonProperty(value = "party_name")
    @ApiModelProperty("党委组织名称")
    private String officeName;

    @JsonProperty(value = "org_id")
    @ApiModelProperty("组织ID")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    @JsonProperty(value = "user_count")
    @ApiModelProperty("组织党员数量")
    private Integer userCount;

    /**
     * 是否退休党组织  1-是 2-否
     */
    @JsonProperty(value = "is_retire")
    @ApiModelProperty("是否退休党组织")
    private Integer isRetire;

    /**
     * 组织状态  1-有效 2-禁用
     */
    @JsonProperty(value = "status")
    @ApiModelProperty("组织状态")
    private Integer status;
}
