package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * Auther: ruoyu
 * Date: 19-5-22
 * Description:
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PeriodFindOrgsResultForm {

    @JsonProperty("org_id")
    private Long orgId;

    @JsonProperty("period_create_time")
    private String periodCreateTime;

    @JsonProperty("period_size")
    private Integer periodSize;

    @JsonProperty("create_org_groups")
    private Integer createOrgGroups;

    /**
     * 是否存在以设置支委会标签 1:已设置 2:未设置
     */
    @JsonProperty("has_period_tag")
    private Integer hasPeriodTag;
}
