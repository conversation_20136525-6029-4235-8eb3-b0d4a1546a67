package com.goodsogood.ows.model.vo.user

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.util.*

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class OrgExpandVO {

    @ApiModelProperty("组织ID")
    var orgId: Long? = null

    @ApiModelProperty("组织名称")
    var orgName: String? = null

    @ApiModelProperty("组织类型")
    var orgTypeChild: Int? = null

    @ApiModelProperty("组织创建时间")
    var orgCreateTime: Date? = null

    @ApiModelProperty("党员人数")
    var partyMemberNum: Int = 0

    @ApiModelProperty("党支部数量")
    var branchNum: Int = 0

    @ApiModelProperty("党小组数量")
    var groupNum : Int = 0

    @ApiModelProperty("联系领导")
    var contactLeader: String? = null

    @ApiModelProperty("支部书记")
    var secretary: String? = null

    @ApiModelProperty("届次")
    var orgPeriodValid: String? = null

    @ApiModelProperty("组织层级关系")
    var orgLevel: List<OrgBaseInfo> = mutableListOf()

    /**
     * 经度
     */
    @JsonProperty("longitude")
    var longitude: Double? = null

    /**
     * 纬度
     */
    @JsonProperty("latitude")
    var latitude: Double? = null

    constructor()



    constructor(
        orgId: Long?,
        orgName: String?,
        partyMemberNum: Int?,
        branchNum: Int?,
        groupNum: Int?,
        contactLeader: String?,
        secretary: String?,
        orgPeriodValid: String?,
        orgLevel: List<OrgBaseInfo>?,
        longitude: Double?,
        latitude: Double?
    ) {
        this.orgId = orgId
        this.orgName = orgName
        if (partyMemberNum != null) {
            this.partyMemberNum = partyMemberNum
        }
        if (branchNum != null) {
            this.branchNum = branchNum
        }
        if (groupNum != null) {
            this.groupNum = groupNum
        }
        this.contactLeader = contactLeader
        this.secretary = secretary
        this.orgPeriodValid = orgPeriodValid
        if (orgLevel != null) {
            this.orgLevel = orgLevel
        }
        this.longitude = longitude
        this.latitude = latitude
    }

    constructor(orgId: Long?, orgName: String?, orgTypeChild: Int?, longitude: Double?, latitude: Double?) {
        this.orgId = orgId
        this.orgName = orgName
        this.orgTypeChild = orgTypeChild
        this.longitude = longitude
        this.latitude = latitude
    }

    constructor(
        orgId: Long?,
        orgName: String?,
        orgTypeChild: Int?,
        orgCreateTime: Date?,
        partyMemberNum: Int,
        branchNum: Int,
        groupNum: Int,
        contactLeader: String?,
        secretary: String?,
        orgPeriodValid: String?,
        orgLevel: List<OrgBaseInfo>,
        longitude: Double?,
        latitude: Double?
    ) {
        this.orgId = orgId
        this.orgName = orgName
        this.orgTypeChild = orgTypeChild
        this.orgCreateTime = orgCreateTime
        this.partyMemberNum = partyMemberNum
        this.branchNum = branchNum
        this.groupNum = groupNum
        this.contactLeader = contactLeader
        this.secretary = secretary
        this.orgPeriodValid = orgPeriodValid
        this.orgLevel = orgLevel
        this.longitude = longitude
        this.latitude = latitude
    }


}

class OrgSecretary {
    @ApiModelProperty("书记名称")
    var name: String? = null

    @ApiModelProperty("届次")
    var time: String? = null
}

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
class OrgBaseInfo {

    @ApiModelProperty("组织ID")
    var orgId: Long? = null

    @ApiModelProperty("组织名称")
    var name: String? = null

    constructor()
    constructor(orgId: Long?, name: String?) {
        this.orgId = orgId
        this.name = name
    }

}