package com.goodsogood.ows.model.vo.news;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * User: R
 * Date: 2018/8/13
 * Time: 15:02
 * Created with IntelliJ IDEA.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class NewsAppForm implements Serializable {

    @ApiModelProperty("新闻id")
    @JsonProperty("news_id")
    private Long newsId;

    @ApiModelProperty("新闻长标题")
    private String title;

    @ApiModelProperty("新闻短标题")
    @JsonProperty("sub_title")
    private String subTitle;

    @ApiModelProperty("标签，1：置顶，2：聚焦")
    private String tags;

    @ApiModelProperty("新闻主栏目id")
    @JsonProperty("news_column_id")
    private Long newsColumnId;

    @ApiModelProperty("新闻主栏目名称")
    @JsonProperty("news_column_name")
    private String newsColumnName;

    @ApiModelProperty("新闻副栏目id")
    @JsonProperty("sub_column")
    private String subColumn;

    @ApiModelProperty("新闻来源")
    private String source;

    @ApiModelProperty("新闻摘要")
    private String summary;

    @ApiModelProperty("新闻正文")
    private String content;

    @ApiModelProperty("关键字，逗号分隔")
    private String keywords;

    @ApiModelProperty("序号")
    @JsonProperty("order_number")
    private Long orderNumber;

    @ApiModelProperty("新闻展示类型：1 文字， 2 单图 ，3 三图，4巨幅")
    @JsonProperty("show_type")
    private Integer showType;

    @ApiModelProperty("新闻图片列表")
    @JsonProperty("imgs")
    private List<NewsImgForm> imgs;

    @ApiModelProperty("相关新闻，最多三篇")
    @JsonProperty("relevant")
    private List<NewsAppForm> relevant;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("create_time")
    private Date createTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("update_time")
    private Date updateTime;

    @ApiModelProperty("是否已经转发,只有新闻矩阵包含，1：转发；0：推送 ；-1/null 原创新闻")
    private Integer forward;

    @ApiModelProperty("链接Url")
    @JsonProperty("link_url")
    private String linkUrl;


    @ApiModelProperty("转载")
    @JsonProperty("reprint")
    private Integer reprint;

}
