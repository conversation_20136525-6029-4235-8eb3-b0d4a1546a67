package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.pagehelper.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <p>Description: 组织生活一览表统计 返回对象 </p>
 *
 * <AUTHOR>
 * @version 2019/7/23 16:08
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SasOrgLifeForm {
    @JsonProperty("types")
    @ApiModelProperty(value = "统计类型")
    private List<MeetingTypeForm> types;

    @JsonProperty("list_detail")
    @ApiModelProperty(value = "统计详情")
    private Page<Map<String, Object>> listDetail;

}
