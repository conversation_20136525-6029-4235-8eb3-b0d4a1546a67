package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.LinkedList;
import java.util.List;

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RevisitStitchingVo {

    @JsonProperty(value = "legend")
    private String [] legend={"本月","上月"};

    @JsonProperty(value = "list_series")
    private List<RevisitStitchingVo.Series> listSeries=new LinkedList<>();


    @Data
    public static class Series {
        @JsonProperty(value = "name")
        private String name;

        @JsonProperty(value = "data")
        private List<Integer> data;
    }
}
