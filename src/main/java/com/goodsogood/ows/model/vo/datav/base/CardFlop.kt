package com.goodsogood.ows.model.vo.datav.base

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.goodsogood.ows.helper.Json.DoubleKeepOneSerializer
import com.goodsogood.ows.helper.Json.DoubleKeepTwoSerializer
import java.io.Serializable

/**
 * datav 翻牌器数据结构
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class CardFlop @JvmOverloads constructor(
    // （可选）数字翻牌器的标题内容，不为空时会覆盖样式中的配置。为空时会使用标题 > 标题名配置项中的内容。
    var name: String? = null,
    // 数字翻牌器的具体数值。
//    @JsonSerialize(using = DoubleKeepOneSerializer::class)
    var value: Long? = null,
    // （可选）数字翻牌器的前缀内容，不为空时会覆盖样式中的配置。为空时会使用翻牌器 > 前缀 > 内容配置项中的内容。
    var prefix: String? = null,
    // （可选）数字翻牌器的后缀内容，不为空时会覆盖样式中的配置。为空时会使用翻牌器 > 后缀 > 内容配置项中的内容。
    var suffix: String? = null,
) : Serializable{
    companion object {
        private const val serialVersionUID: Long = -8290391713324047446L
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class OtherCardFlop @JvmOverloads constructor(
    // （可选）数字翻牌器的标题内容，不为空时会覆盖样式中的配置。为空时会使用标题 > 标题名配置项中的内容。
    var name: String? = null,
    // 数字翻牌器的具体数值。
    @JsonSerialize(using = DoubleKeepTwoSerializer::class)
    var value: Double? = null,
    // （可选）数字翻牌器的前缀内容，不为空时会覆盖样式中的配置。为空时会使用翻牌器 > 前缀 > 内容配置项中的内容。
    var prefix: String? = null,
    // （可选）数字翻牌器的后缀内容，不为空时会覆盖样式中的配置。为空时会使用翻牌器 > 后缀 > 内容配置项中的内容。
    var suffix: String? = null,
) : Serializable{
    companion object {
        private const val serialVersionUID: Long = -823331713324047446L
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class OneCardFlop @JvmOverloads constructor(
    // （可选）数字翻牌器的标题内容，不为空时会覆盖样式中的配置。为空时会使用标题 > 标题名配置项中的内容。
    var name: String? = null,
    // 数字翻牌器的具体数值。
    @JsonSerialize(using = DoubleKeepOneSerializer::class)
    var value: Double? = null,
    // （可选）数字翻牌器的前缀内容，不为空时会覆盖样式中的配置。为空时会使用翻牌器 > 前缀 > 内容配置项中的内容。
    var prefix: String? = null,
    // （可选）数字翻牌器的后缀内容，不为空时会覆盖样式中的配置。为空时会使用翻牌器 > 后缀 > 内容配置项中的内容。
    var suffix: String? = null,
) : Serializable{
    companion object {
        private const val serialVersionUID: Long = -823331713324047446L
    }
}