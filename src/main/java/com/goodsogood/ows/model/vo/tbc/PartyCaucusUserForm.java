package com.goodsogood.ows.model.vo.tbc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.github.pagehelper.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class) //自动驼峰命名
@JsonInclude(JsonInclude.Include.NON_NULL)  //json 转实例忽略为空
@JsonIgnoreProperties(ignoreUnknown = true) //转json 忽略为空
public class PartyCaucusUserForm {
    @ApiModelProperty(value = "民族详情")
    private Page<TbcPNationalDetailsFrom> nationNum;

    @ApiModelProperty(value = "年龄阶段详情")
    private Page<TbcAgeForDetailsFrom> ageNum;

    @ApiModelProperty(value = "学历情况")
    private Page<TbcDegreeInDetailsFrom> backdrop;

    /**
     * 民族
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TbcEthnicDistributionOfPartyMembers {
        @ApiModelProperty(value = "optionId")
        private Integer nationId;
        @ApiModelProperty(value = "民族名称")
        private String nationName;
    }

    /**
     * 党员民族详情
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TbcPNationalDetailsFrom {
        @ApiModelProperty(value = "党员ID")
        private Integer memberId;
        @ApiModelProperty(value = "党员姓名")
        private String memberName;
        @ApiModelProperty(value = "所在支部")
        private String orgName;
        @ApiModelProperty(value = "党员民族")
        private String memberNation;
    }

    /**
     * 党员年龄详情
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TbcAgeForDetailsFrom {
        @ApiModelProperty(value = "党员ID")
        private Integer memberId;
        @ApiModelProperty(value = "党员姓名")
        private String memberName;
        @ApiModelProperty(value = "所在支部")
        private String orgName;
        @ApiModelProperty(value = "年龄")
        private Integer age;
    }

    /**
     * 党员学历详情
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TbcDegreeInDetailsFrom {
        @ApiModelProperty(value = "党员ID")
        private Integer memberId;
        @ApiModelProperty(value = "党员姓名")
        private String memberName;
        @ApiModelProperty(value = "所在支部")
        private String orgName;
        @ApiModelProperty(value = "学历")
        private String educationBackground;
    }


}
