package com.goodsogood.ows.model.vo.ppmd;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActiveUserInfo {

    @JsonProperty(value = "user_name")
    @ApiModelProperty("用户名称")
    private String userName;

    @JsonProperty(value = "org_id")
    @ApiModelProperty("组织id")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    @JsonProperty(value = "pay_date")
    @ApiModelProperty("缴纳时间")
    private Date payDate;

    @JsonProperty(value = "short_office_name")
    @ApiModelProperty("所属党委简称")
    private String shortOfficeName;
}
