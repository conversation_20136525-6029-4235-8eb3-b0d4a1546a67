package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 组织类型
 *
 * <AUTHOR>
 * @date 2019/4/22 15:48
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrganizationTypeForm {

    @ApiModelProperty(value = "组织类型key")
    @JsonProperty("op_key")
    private String opKey;

    @ApiModelProperty(value = "组织类型value")
    @JsonProperty("op_value")
    private String opValue;

}
