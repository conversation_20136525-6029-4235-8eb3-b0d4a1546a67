package com.goodsogood.ows.model.vo.ppmd;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 交费基数类
 *
 * @auther Administrator tc
 * @date 2018/10/24
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StatsPartyMemberVo {
    @JsonProperty(value = "user_id")
    @ApiModelProperty("用户编号")
    private Long userId;

    @JsonProperty(value = "user_name")
    @ApiModelProperty("用户名称")
    private String userName;

    @JsonProperty(value = "office_id")
    @ApiModelProperty("机关单位编号")
    private Long officeId;

    @JsonProperty(value = "office_name")
    @ApiModelProperty("机关单位名称")
    private String officeName;

    @JsonProperty(value = "org_id")
    @ApiModelProperty("支部编号")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("支部名称")
    private String orgName;

    @JsonProperty(value = "cert_number_secret")
    @ApiModelProperty("身份证号脱敏文")
    private String certNumberSecret;

    /**
     * 缴纳基数 单位:分
     */
    @JsonProperty(value = "cardinal_number")
    @ApiModelProperty("缴纳基数")
    private Integer cardinalNumber;

    /**
     * 党费规则 1按收入比例 2少交 3免交 4 未设置 5 固定金额
     */
    @JsonProperty(value = "type")
    @ApiModelProperty("党费规则")
    private Integer type;

    /**
     * 交费比例类型:1.正常党员交费比例  2.退休党员交费比例 默认1
     */
    @JsonProperty(value = "ratio_type")
    @ApiModelProperty("交费比例类型")
    private Integer ratioType;

    /**
     * 修改后的党费  单位:分
     */
    @JsonProperty(value = "revised_party_fee")
    @ApiModelProperty("修改后的党费")
    private Integer revisedPartyFee;

    /**
     * 特殊原因编号
     */
    @JsonProperty(value = "reason_id")
    @ApiModelProperty("特殊原因编号")
    private Integer reasonId;

    /**
     * 其他原因描述
     */
    @JsonProperty(value = "reason")
    @ApiModelProperty("其他原因描述")
    private String reason;

    public StatsPartyMemberVo(){
        super();
    }

    public StatsPartyMemberVo(Long userId, String userName, Long officeId, String officeName, Long orgId,
                              String orgName, Integer reasonId, String reason, Integer ratioType) {
        this.userId = userId;
        this.userName = userName;
        this.officeId = officeId;
        this.officeName = officeName;
        this.orgId = orgId;
        this.orgName = orgName;
        this.reasonId = reasonId;
        this.reason = reason;
        this.ratioType = ratioType;
    }
}
