package com.goodsogood.ows.model.vo.datav.base

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.goodsogood.ows.helper.Json.DoubleKeepOneSerializer
import java.io.Serializable

/**
 * <AUTHOR>
 * @date 2023/3/15
 * @description class 地图数据结构 // datav的数据机构本来就是驼峰，所以不加匈牙利转换
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
//@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class MapChart @JvmOverloads constructor(
    //经度
    val lng: Double = 0.0,
    // 纬度
    val lat: Double = 0.0,
    //（可选） 柱高的映射字段
    @JsonSerialize(using = DoubleKeepOneSerializer::class)
    val sizeField: Double? = null,
    // （可选）气泡的名称 / （可选）地区的名称。
    val name: String? = null,
    // （可选）气泡的值。/ （可选）地区的值，与配置面板中填充颜色的最大值、最小值、无数据配置项配合使用，可控制对应区域的颜色。
    val value: String? = null,
    // （可选）气泡的形状映射字段。
    val markerField: String? = null,
    // （可选）气泡的半径大小映射字段。
    @JsonSerialize(using = DoubleKeepOneSerializer::class)
    val radiusField: Double? = null,
    //（可选） 颜色映射字段
    val colorField: String? = null,
    // 标签的高度
    @JsonSerialize(using = DoubleKeepOneSerializer::class)
    val height: Double? = null,
    // 标签的图标样式
    val iconField: String? = null,
    // 标签图标所在的URL地址。(自定义的任意字段支持html标签，可以配合字段映射一同使用)
    val html: String? = null,
    // adcode 区域的adcode编号，可通过DataV.GeoAtlas获取。
    val adcode: String? = null,
    // （可选）地区的纹理形状映射字段。
    val textureShapeField: String? = null,
    // （可选）地区的纹理颜色映射字段。
    val textureColorField: String? = null,

    ) : Serializable {
    companion object {
        private const val serialVersionUID: Long = -3560313583994728639L
    }
}