package com.goodsogood.ows.model.vo.meeting

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModelProperty

/**
 * 市委办公厅大屏 组织换届
 * <AUTHOR>
 * @createTime 2022年08月12日
 */
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonIgnoreProperties(ignoreUnknown = true)
data class MeetingPlanCountVO(
    @ApiModelProperty("会议类型id")
    var typeId: String? = null,

    @ApiModelProperty("会议类型")
    var type: String? = null,

    @ApiModelProperty("开展情况 1:未开展 2:已开展")
    var status: Int? = null
) {
    override fun toString(): String {
        return "MeetingPlanCountVO(typeId=$typeId, type=$type, status=$status)"
    }
}