package com.goodsogood.ows.model.vo.supervise;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OptionForm {

    @JsonProperty(value = "option_key")
    private String optionKey;


    @JsonProperty(value = "option_name")
    private String optionName;

    @JsonProperty(value = "count")
    private Integer count;


    @JsonProperty(value = "is_link")
    private Boolean isLink;

    @JsonProperty(value = "query-type")
    private String queryType;

    @JsonProperty(value = "detail_content")
    private String detailContent;



    @JsonProperty(value = "org_name")
    private String orgName;

    @JsonProperty(value = "org_secretary")
    private String orgSecretary;

    @JsonProperty(value = "create_time")
    private Date createTime;

}
