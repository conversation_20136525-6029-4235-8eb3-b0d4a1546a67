package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EvalLeaderMeeting {

    @ApiModelProperty("领导人ID")
    @JsonProperty(value = "leader_user_id")
    private Long leaderUserId;


    @ApiModelProperty("领导人名字")
    @JsonProperty(value = "leader_name")
    private String leaderName;


    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;


    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;


    @ApiModelProperty("组织类型id")
    @JsonProperty(value = "org_type_id")
    private Integer orgTypeId;

    @ApiModelProperty("你级组织Id")
    @JsonProperty(value = "parent_org_id")
    private Long parentOrgId;

    @ApiModelProperty("组织父级路径")
    @JsonProperty(value = "org_level")
    private String orgLevel;

    @ApiModelProperty("是否离退休党组织 1-是 2-否")
    @JsonProperty(value = "is_retire")
    private Integer isRetire;

    @ApiModelProperty("参与次数")
    @JsonProperty(value = "participate_num")
    private Integer participateNum;

    @ApiModelProperty("统计时间(yyyy-MM)")
    @JsonProperty(value = "statistical_date")
    private String statisticalDate;
}
