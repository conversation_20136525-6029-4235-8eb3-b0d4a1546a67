package com.goodsogood.ows.model.vo.experience;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 党员体检详情信息
 * @Author: mengting
 * @Date: 2022/4/21 11:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrgUserReportVO {
    private Long evalId;
    private Long orgId;
    private Long unitId;
    private String orgName;
    private String unitName;
    private String tag;//过度用，拿来取细项标签
    private Long userId;
    private String userName;
    private Integer star;
    private String phoneSecret;//脱敏手机号
    private String phone;//手机号
    private String type;//综合  理论武装等
    private String item;//综合测评 专项测评
    private String tagAdd;//蓝色标签
    private String tagReduce;//灰色标签
    private String dateMonth;//年月
    private Integer totalStar;

    private List<UserPartyReportVO.TypeStarVO> detail;

    public Long getEvalId() {
        return evalId;
    }

    public void setEvalId(Long evalId) {
        this.evalId = evalId;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getStar() {
        return star;
    }

    public void setStar(Integer star) {
        this.star = star;
    }

    public String getPhoneSecret() {
        return phoneSecret;
    }

    public void setPhoneSecret(String phoneSecret) {
        this.phoneSecret = phoneSecret;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getItem() {
        return item;
    }

    public void setItem(String item) {
        this.item = item;
    }

    public String getTagAdd() {
        return tagAdd;
    }

    public void setTagAdd(String tagAdd) {
        this.tagAdd = tagAdd;
    }

    public String getTagReduce() {
        return tagReduce;
    }

    public void setTagReduce(String tagReduce) {
        this.tagReduce = tagReduce;
    }

    public String getDateMonth() {
        return dateMonth;
    }

    public void setDateMonth(String dateMonth) {
        this.dateMonth = dateMonth;
    }

    public Integer getTotalStar() {
        return totalStar;
    }

    public void setTotalStar(Integer totalStar) {
        this.totalStar = totalStar;
    }

    public List<UserPartyReportVO.TypeStarVO> getDetail() {
        return detail;
    }

    public void setDetail(List<UserPartyReportVO.TypeStarVO> detail) {
        this.detail = detail;
    }
}
