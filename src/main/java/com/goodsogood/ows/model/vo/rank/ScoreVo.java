package com.goodsogood.ows.model.vo.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Create by FuXiao on 2020/10/22
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ScoreVo {
    @JsonProperty(value = "score_rule_id")
    @ApiModelProperty(name = "外键")
    private Long scoreRuleId;

    @JsonProperty(value = "score")
    @ApiModelProperty(name = "得分")
    private Long score;
}
