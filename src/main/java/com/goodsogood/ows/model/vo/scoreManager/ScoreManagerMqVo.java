package com.goodsogood.ows.model.vo.scoreManager;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.common.ScoreManagerEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Transient;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/12/13
 */
@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScoreManagerMqVo {

    /**
     * 需要处理的月份 yyyy-MM
     */
    private String executeTime;

    /**
     * 区县id
     */
    private Long regionId;

    /**
     * 具体处理的类型
     */
    private ScoreManagerEnum managerEnum;

    /**
     * 减少资源消耗 mq时只保存enum typeId
     */
    private Integer typeId;

    /**
     * 需要处理的ids 根据ScoreManagerEnum.idType 判断
     * 可能是orgId,userId,partyGroupId
     */
    private Long id;

    /**
     * 调用方 如果是定时任务这里是-1
     */
    private Long changeUserId = -1L;

    /**
     * 业务处理保存的数据
     */
    private List<TransitScoreManagerVo> handleData;

    @Transient
    private Integer ruleId;

    public void setSingleHandleDefaultData() {
        setSingleHandleData(-1L, "-1", -1D);
    }

    public void setSingleHandleData(Double ratio, String... vars) {
        setSingleHandleData(-1L, "-1", ratio, vars);
    }

    public void setSingleHandleData(Long sourceDataId, String sourceDataIdStr, String... vars) {
        setSingleHandleData(sourceDataId, sourceDataIdStr, null, vars);
    }

    public void setSingleHandleData(Long sourceDataId, String sourceDataIdStr, Double ratio, String... vars) {
        TransitScoreManagerVo transitVo = new TransitScoreManagerVo();
        transitVo.setSourceDataId(sourceDataId);
        transitVo.setSourceDataIdStr(sourceDataIdStr);
        transitVo.setRatio(ratio);
        try {
            transitVo.setVar1(vars[0]);
        } catch (Exception e) {
        }
        try {
            transitVo.setVar2(vars[1]);
        } catch (Exception e) {
        }
        try {
            transitVo.setVar3(vars[2]);
        } catch (Exception e) {
        }
        try {
            transitVo.setVar4(vars[3]);
        } catch (Exception e) {
        }
        try {
            transitVo.setVar5(vars[4]);
        } catch (Exception e) {
        }
        this.handleData = Collections.singletonList(transitVo);
    }

    /**
     * 满足单个业务多次新增封装的参数
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransitScoreManagerVo {
        /**
         * 处理结果的来源依据id
         */
        private Long sourceDataId = -1L;

        /**
         * 处理结果的来源依据id
         */
        private String sourceDataIdStr = "-1";

        /**
         * 冗余字段 业务审查
         */
        private String var1;

        /**
         * 冗余字段 业务审查
         */
        private String var2;

        /**
         * 冗余字段 业务审查
         */
        private String var3;

        /**
         * 冗余字段 业务审查
         */
        private String var4;

        /**
         * 冗余字段 业务审查
         */
        private String var5;

        /**
         * 如果是比率计算 需要填写该值 在addScoreManagerInfo 进行计算
         */
        private Double ratio;
    }
}
