package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.goodsogood.ows.helper.Json.DoubleKeepTwoSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 支部党费个人交纳情况(移动端)返回类
 *
 * @auther Administrator tc
 * @date 2018/11/15
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PayDetailsAppVo {

    @JsonProperty(value = "org_id")
    @ApiModelProperty("组织编号")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    @JsonProperty(value = "user_id")
    @ApiModelProperty("应交人员编号")
    private Long userId;

    @JsonProperty(value = "user_name")
    @ApiModelProperty("应交人员姓名")
    private String userName;

    /**
     * 交纳状态  1已交、2未交、3补交、4欠交
     */
    @JsonProperty(value = "status")
    @ApiModelProperty("交纳状态")
    private Integer status;

    /**
     * 单位 : 元
     */
    @JsonSerialize(using = DoubleKeepTwoSerializer.class)
    @JsonProperty(value = "pay_should")
    @ApiModelProperty("应交金额")
    private Double payShould;

    /**
     *  格式  YYYY-MM-dd HH:mm
     */
    @JsonProperty(value = "last_pay_date")
    @ApiModelProperty("最后一次交纳时间")
    private String lastPayDate;

    /**
     *  党费规则 1按收入比例 2少交 3免交 4新进党员未设置基数
     */
    @JsonProperty(value = "type")
    @ApiModelProperty("党费规则")
    private Integer type;
}
