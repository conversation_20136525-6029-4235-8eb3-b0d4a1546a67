package com.goodsogood.ows.model.vo.tbc;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class TbcBaseVo {

    @ApiModelProperty(value = "组织id")
    private Long orgId;

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty(value = "组织层次关系")
    private String orgLevel;


    @ApiModelProperty(value = "ownerId")
    private Long ownerId;


    @ApiModelProperty(value = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "parentId")
    private Long parentId;


    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "头像")
    private String headUrl;

    @ApiModelProperty(value = "部门名称")
    private String department;


    @ApiModelProperty(value = "职务名称")
    private String position;

    @ApiModelProperty(value = "党内职务")
    private String partyPosition;

    @ApiModelProperty(value = "组织类型")
    private Integer orgTypeChild;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

}
