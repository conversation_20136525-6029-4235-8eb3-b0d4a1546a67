package com.goodsogood.ows.model.vo.dataCockpit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 数据驾驶舱 - 民主评议
 *
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PartyEvaluationVO {

     @ApiModelProperty("党员数量")
     public Integer partyMemberNum = 0;

     @ApiModelProperty("已评数量")
     public Integer completedNum = 0;

     @ApiModelProperty("未评数量")
     public Integer unfinishedNum;

     @ApiModelProperty("评选进度（完成进度）")
     public Double completedProgress;

     @ApiModelProperty("优秀数量")
     public Integer level1Num = 0;

     @ApiModelProperty("优秀比例")
     public Double level1Proportion;

     @ApiModelProperty("合格数量")
     public Integer level2Num = 0;

     @ApiModelProperty("合格比例")
     public Double level2Proportion;

     @ApiModelProperty("基本合格数量")
     public Integer level3Num = 0;

     @ApiModelProperty("基本合格比例")
     public Double level3Proportion;

     @ApiModelProperty("不合格数量")
     public Integer level4Num = 0;

     @ApiModelProperty("不合格比例")
     public Double level4Proportion;

}
