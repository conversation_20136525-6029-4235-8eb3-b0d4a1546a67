package com.goodsogood.ows.model.vo;

import com.goodsogood.ows.model.vo.rank.Month;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.MapUtils;
import lombok.Data;

import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>Description: 得分结果 </p>
 *
 * <AUTHOR>
 * @version 2020/11/23 17:56
 */
@Data
public class ScoreResult {
    /** key: concat(oid/uid,'-',#{item.currentMonth}) */
    private String k;
    /** 分值 */
    private Double score;



    /** 封装返回结果 */
    public static ScoreResultVo getScoreResultVo(
            Integer year, List<Long> idList, List<ScoreResult> scoreList) {
        // 循环scoreList 放入map中 提高效率
        Map<String, Double> scoreMap = new HashMap<>(scoreList.size());
        for (ScoreResult s : scoreList) {
            scoreMap.put(s.getK(), s.getScore());
        }
        // 组装返回结果
        ScoreResultVo scoreResultVo = new ScoreResultVo();
        Map<Long, EnumMap<Month, Double>> score = new HashMap<>();
        scoreResultVo.setScoreResultMap(score);
        for (Long id : idList) {
            EnumMap<Month, Double> enumMap = new EnumMap<>(Month.class);
            score.put(id, enumMap);
            for (Month month : Month.values()) {
                // concat(org_id,'-',#{item.currentMonth})
                String key = id + "-" + DateUtils.dateFormat(year, month.value());
                enumMap.put(month, MapUtils.getDouble(scoreMap, key, 0.0));
            }
        }
        return scoreResultVo;
    }
}
