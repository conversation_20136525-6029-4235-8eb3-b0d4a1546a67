package com.goodsogood.ows.model.vo.dataCockpit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据驾驶舱 - 党员概况(党委)
 *
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PartyMemberVO {

     @ApiModelProperty("党员人数")
     public Integer partyMemberNum = 0;

     @ApiModelProperty("党员占比")
     public Double partyMemberProportion = 0.0;

     @ApiModelProperty("男党员人数")
     public Integer malePartyGroupNum = 0;

     @ApiModelProperty("女党员人数")
     public Integer femalePartyLectureNum = 0;

     @ApiModelProperty("学历维度统计")
     public Education education = new Education();

     @ApiModelProperty("党龄维度统计")
     public PartyStanding partyStanding = new PartyStanding();

     @ApiModelProperty("年龄维度统计")
     public Age age = new Age();

     @Data
     @ApiModel
     @JsonInclude(JsonInclude.Include.NON_NULL)
     @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
     public class Education{

          @ApiModelProperty("研究生及以上")
          public Integer level1 = 0;

          @ApiModelProperty("研究生及以上占比")
          public Double level1Proportion = 0.0;

          @ApiModelProperty("本科")
          public Integer level2 = 0;

          @ApiModelProperty("本科占比")
          public Double level2Proportion = 0.0;

          @ApiModelProperty("高中")
          public Integer level3 = 0;

          @ApiModelProperty("高中占比")
          public Double level3Proportion = 0.0;

          @ApiModelProperty("初中及以下")
          public Integer level4 = 0;

          @ApiModelProperty("初中及以下占比")
          public Double level4Proportion = 0.0;
     }

     @Data
     @ApiModel
     @JsonInclude(JsonInclude.Include.NON_NULL)
     @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
     public class PartyStanding{
          @ApiModelProperty("2012年11月及以后")
          public Integer level1 = 0;

          @ApiModelProperty("2012年11月及以后占比")
          public Double level1Proportion = 0.0;

          @ApiModelProperty("2002年11月-2012年10月")
          public Integer level2 = 0;

          @ApiModelProperty("2002年11月-2012年10月占比")
          public Double level2Proportion = 0.0;

          @ApiModelProperty("1979年1月-2002年10月")
          public Integer level3 = 0;

          @ApiModelProperty("1979年1月-2002年10月占比")
          public Double level3Proportion = 0.0;

          @ApiModelProperty("1976年11月-1978年12月")
          public Integer level4 = 0;

          @ApiModelProperty("1976年11月-1978年12月占比")
          public Double level4Proportion = 0.0;

          @ApiModelProperty("1976年7月6日以前")
          public Integer level5 = 0;

          @ApiModelProperty("1976年7月6日以前占比")
          public Double level5Proportion = 0.0;
     }

     @Data
     @ApiModel
     @JsonInclude(JsonInclude.Include.NON_NULL)
     @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
     public class Age{
          @ApiModelProperty("35岁以下")
          public Integer level1 = 0;

          @ApiModelProperty("35岁以下占比")
          public Double level1Proportion = 0.0;

          @ApiModelProperty("36至50岁")
          public Integer level2 = 0;

          @ApiModelProperty("36至50岁占比")
          public Double level2Proportion = 0.0;

          @ApiModelProperty("51至60岁")
          public Integer level3 = 0;

          @ApiModelProperty("51至60岁占比")
          public Double level3Proportion = 0.0;

          @ApiModelProperty("61岁以上")
          public Integer level4 = 0;

          @ApiModelProperty("61岁以上占比")
          public Double level4Proportion = 0.0;
     }

}
