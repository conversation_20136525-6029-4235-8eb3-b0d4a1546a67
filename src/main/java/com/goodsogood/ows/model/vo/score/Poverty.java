package com.goodsogood.ows.model.vo.score;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.mongodb.ScoreSuperInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class Poverty extends ScoreSuperInfo {

    @ApiModelProperty("t_score_poverty_order.order_id 扶贫商城标识唯一的订单编号")
    private String orderId;

    @ApiModelProperty("t_score_poverty_order.prverty_order_id 主键id")
    private Long povertyOrderId;

    @ApiModelProperty("交易时间")
    private Date tradingTime;

    @ApiModelProperty("已改为计算出来的订单总金额  原扶贫商城推送的订单总金额 单位:分")
    private Long totalPrice;

    @ApiModelProperty("扶贫商城推送的订单总邮费 单位 分")
    private Long logistics;

    @ApiModelProperty("已改为计算出来的捐款金额 原扶贫商城推送过来的捐款金额，单位:分")
    private Long donation;

    @ApiModelProperty("总共有多少种商品")
    private Integer countCommodity;

    @ApiModelProperty("type=1 普通商品有多少种")
    private Integer countCommon;

    @ApiModelProperty("type=1 普通商品有多少件")
    private Integer sizeCommon;

    @ApiModelProperty("type=2 捐款商品有多少种")
    private Integer countDonate;

    @ApiModelProperty("type=2 捐款商品有多少件")
    private Integer sizeDonate;

    @ApiModelProperty("商品信息")
    private List<PovertyCommodity> commoditys;

    @ApiModelProperty("区县id")
    private Long regionId;

}

