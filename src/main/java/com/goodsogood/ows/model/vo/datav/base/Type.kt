package com.goodsogood.ows.model.vo.datav.base

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import java.io.Serializable

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class Type @JvmOverloads constructor(
    // 数据类型/名称
    var typeName: String? = null,
    // 数据索引值，一般是数组的第几个
    var typeIndex: Int? = null,
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 8662265140266161408L
    }
}
