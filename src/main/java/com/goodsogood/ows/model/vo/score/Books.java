package com.goodsogood.ows.model.vo.score;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.mongodb.ScoreSuperInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class Books extends ScoreSuperInfo {

    @ApiModelProperty("t_score_detail_other.score_detail_id 用户积分流水,非学习系统的流水记录 ")
    private Long scoreDetailOtherId;

    @ApiModelProperty("t_score_order order_id 主键订单Id")
    private Long orderId;

    @ApiModelProperty("兑换数量")
    private Long commodityCount;

    @ApiModelProperty("外部系统订单id")
    private String outTradeNo;

    @ApiModelProperty("交易时间")
    private Date consumeTime;

    @ApiModelProperty("产生原因")
    private String remark;

    @ApiModelProperty("商品信息")
    private List<BookCommodity> commoditys;

    @ApiModelProperty("区县id")
    private Long regionId;

}

