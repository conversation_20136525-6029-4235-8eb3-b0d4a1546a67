package com.goodsogood.ows.model.vo.news

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.util.*

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
class NewsListFrom {

    @ApiModelProperty("用户id")
    var userId: Long? = null

    @ApiModelProperty("用户名称")
    var userName: String? = null

    @ApiModelProperty("区县id")
    var regionId: Long? = null

    @ApiModelProperty("用户查看操作")
    var type: Int? = null

    @ApiModelProperty("这个月的每日查看次数")
    var count: Int? = null

    @ApiModelProperty("查看时间")
    var transferTime: Date? = null

    @ApiModelProperty("调用时间别名")
    var date: String? = null

    @ApiModelProperty("用户排名")
    var rank: Int? = null

}