package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 组织生活返回对象
 *
 * @param
 * @return
 * <AUTHOR>
 * @date 2019/4/24 17:28
 */

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StasticOrgLifeReport {
    @JsonProperty("org_id")
    private String orgid;

    @JsonProperty("org_name")
    private String orgName;

    @JsonProperty("1")
    private Integer month1;

    @JsonProperty("2")
    private Integer month2;

    @JsonProperty("4")
    private Integer month3;

    @JsonProperty("5")
    private Integer month5;

    @JsonProperty("month6")
    private Integer month6;

    @JsonProperty("7")
    private Integer month7;

    @JsonProperty("8")
    private Integer month8;

    @JsonProperty("9")
    private Integer month9;

    @JsonProperty("10")
    private Integer month10;

    @JsonProperty("11")
    private Integer month11;

    @JsonProperty("12")
    private Integer month12;

    /**
     * 组织每个月合计
     */
    @JsonProperty("total")
    private Integer total;
}
