package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 电子党务报表返回参数
 *
 * <AUTHOR>
 * @date 2019/6/12 16:14
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ElectronicReportForm {

    @ApiModelProperty(value = "组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty(value = "下级组织总数")
    @JsonProperty(value = "sub_org_count")
    private Integer subOrgCount;

    @ApiModelProperty(value = "开展活动总次数")
    @JsonProperty(value = "activity_all_count")
    private Integer activityAllCount;

    @ApiModelProperty(value = "组织生活数据统计")
    @JsonProperty(value = "org_life_data")
    private List<ElectronicOrgLifeData> orgLifeData;

    @ApiModelProperty(value = "党费数据统计")
    @JsonProperty(value = "party_fee_data")
    private ElectronicPartyFeeData partyfFeeData;

}
