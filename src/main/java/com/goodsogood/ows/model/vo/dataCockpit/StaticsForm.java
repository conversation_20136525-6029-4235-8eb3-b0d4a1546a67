package com.goodsogood.ows.model.vo.dataCockpit;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class StaticsForm {
    private Long commentId;
    private Long orgId;
    private String orgName;
    private Integer year;
    private Integer partyNumber;
    private Integer joinNumber;
    private Integer level1;
    private Integer level2;
    private Integer level3;
    private Integer level4;

}
