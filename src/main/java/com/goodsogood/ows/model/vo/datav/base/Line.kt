package com.goodsogood.ows.model.vo.datav.base

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.goodsogood.ows.helper.Json.DoubleKeepOneSerializer
import java.io.Serializable

/**
 * datav 折线图/区域图/echart折线图等数据
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class Line @JvmOverloads constructor(
    // 折线图中每个圆点的类目，即x轴的坐标值。该字段类型与格式须与配置项中x轴的标签类型与格式保持一致。
    var x: String? = null,
    // 折线图中每个圆点的值，即y轴的值。
    @JsonSerialize(using = DoubleKeepOneSerializer::class)
    var y: Double? = null,
    // （可选）系列值。
    var s: String? = null,
    // 颜色映射对象，默认为"类型A"
    @JsonProperty("colorField")
    var colorField: String? = "类型A",
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = -5874671207993619397L
    }
}
