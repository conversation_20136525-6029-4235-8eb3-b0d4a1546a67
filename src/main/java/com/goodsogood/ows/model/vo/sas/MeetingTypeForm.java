package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 活动类型
 *
 * <AUTHOR>
 * @date 2019/4/22 15:48
 */
@Data
@Builder
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MeetingTypeForm {

    @ApiModelProperty(value = "活动类型ID")
    @JsonProperty("type_id")
    private Long typeId;

    @ApiModelProperty(value = "活动类型名称")
    @JsonProperty("type_name")
    private String typeName;
}
