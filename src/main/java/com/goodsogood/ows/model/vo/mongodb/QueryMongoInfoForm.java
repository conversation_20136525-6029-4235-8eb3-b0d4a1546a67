package com.goodsogood.ows.model.vo.mongodb;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/28
 * Description:
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class QueryMongoInfoForm {

    @ApiModelProperty("数据表")
    @NotBlank(message = "要填collection 大哥")
    private String collection;

    @ApiModelProperty("Class.getClassName")
    @NotBlank(message = "className 也没填")
    private String className;

    @ApiModelProperty("查询类型 1:查询信息  2:查询条数  3:获取index索引")
    @NotNull(message = "type类型呢？我要的类型呢")
    @Range(min = 1, max = 3, message = "是1到3 你又在乱填")
    private Integer type;

    @ApiModelProperty("跳过指定多少条数据")
    @Min(value = 0, message = "你撒子意思，不要乱填skip")
    private Integer skip = 0;

    @ApiModelProperty("最多返回多少条数据 限制最多返回100条数据")
    @Range(min = 1, max = 100,message = "最多返回100条，我  怕  你  遭  不  住")
    private Integer limit = 100;

    @ApiModelProperty("查询条件 key=字段键")
    @Valid
    private Map<String, QueryConditionMap> queryCondition;

    @ApiModelProperty("返回参数集")
    private List<String> queryResultParam;

    @Data
    public static class QueryConditionMap {

        @NotBlank(message = "填command 填command 填command，你看没看文档")
        @Pattern(regexp = "(^is$)|(^gt$)|(^gte$)|(^lt$)|(^lte$)",message = "command写错了,猪")
        @ApiModelProperty("查询命令")
        private String command;

        @NotNull(message = "传了条件 不传value,66666666666666666")
        @ApiModelProperty("值")
        private Object value;

    }

}
