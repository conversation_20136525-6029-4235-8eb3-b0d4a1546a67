package com.goodsogood.ows.model.vo.datav.others

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

/**
 * 参见用户中心 -> com.goodsogood.ows.model.vo.PartyPositionsForm
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
data class PartyPositionsForm @JvmOverloads constructor(
    var total: Int? = null,
    var page: Long? = null,
    var pageSize: Long? = null,
    var numberAddForm: NumberAddForm? = null,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
class NumberAddForm @JvmOverloads constructor(
    val centerNumber: Int? = null,
    val corridorNumber: Double? = null,
    val liveNumber: Int? = null,
    val gardenNumber: Int? = null,
    val vrNumber: Int? = null,
)


@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class HomeResultVo(

    // 地图实体
    var mapResult: List<MapResultVo> = mutableListOf(),

    // 列表实体 key:1:党委 key:2:党总支 key:3:党支部
    var listResult: MutableMap<Int, List<ListResultVo>> = mutableMapOf()
)

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class MapResultVo(
    var orgId: Long? = null,
    var orgName: String? = null,
    var orgType: Int? = null,
    var longitude: Double? = null,
    var latitude: Double? = null
)

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ListResultVo(
    var unitId: Long? = null,
    var unitName: String? = null,
    var unitShortName: String? = null,
    // 1:党委 2:党总支 3:党支部
    var orgType: Int = 0,
    var adcode: MutableSet<String> = mutableSetOf()
)
