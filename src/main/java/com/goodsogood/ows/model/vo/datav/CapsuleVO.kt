package com.goodsogood.ows.model.vo.datav

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import java.io.Serializable

@JsonInclude(JsonInclude.Include.NON_NULL)
//@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class CapsuleVO @JvmOverloads constructor(
    // y轴数据
    @JsonProperty(value = "yaxisData")
    val yAxisData: List<String>? = null,
    // x轴数据
    @JsonProperty(value = "xaxisData")
    val xAxisData: List<String>? = null,
    // 阈值线
    val threshold: String = "0.00"
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 7489343172788260820L
    }
}
