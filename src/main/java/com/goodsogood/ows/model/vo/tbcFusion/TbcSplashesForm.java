package com.goodsogood.ows.model.vo.tbcFusion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 党页先锋指数前%20分析
 *
 * <AUTHOR>
 * @date 2021/12/03
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TbcSplashesForm {

    //  一象限（右上），List<Double> = x，y 坐标
    public List<List<Double>> color1;

    //  二象限（左上）
    public List<List<Double>> color2;

    //  三象限（左下）
    public List<List<Double>> color3;

    // 四象限（右下）
    public List<List<Double>> color4;

    // x轴的 中位数
    @JsonProperty(value = "median_x")
    public Double medianX;

    @JsonProperty(value = "median_y")
    // y轴的 中位数
    public Double medianY;
}
