package com.goodsogood.ows.model.vo.dataCockpit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 数据驾驶舱 - 预警事项
 *
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SuperviseVO {

     @ApiModelProperty("当前预警数")
     public Integer nowSuperviseNum = 0;

     @ApiModelProperty("昨日预警数")
     public Integer beforeDaySuperviseNum = 0;

     @ApiModelProperty("最近预警")
     public List<SuperviseItem> superviseItems;


     @Data
     @ApiModel
     @JsonInclude(JsonInclude.Include.NON_NULL)
     @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
     public class SuperviseItem{

          @ApiModelProperty("预警项目名称")
          public String name;

          @ApiModelProperty("预警时间")
          @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
          public Date date;

     }

}
