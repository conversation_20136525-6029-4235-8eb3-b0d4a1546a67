package com.goodsogood.ows.model.vo.meeting;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @describe 查询组织奖惩信息返回参数
 * @date 2019-12-26
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingOrgCommendPenalizeQueryVO implements java.io.Serializable {

    @Serial
    private static final long serialVersionUID = -3642959209926843397L;
    @ApiModelProperty("主键id")
    @JsonProperty(value = "meeting_org_commend_penalize_id")
    private Long meetingOrgCommendPenalizeId;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty("批准日期")
    @JsonProperty(value = "ratify_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date ratifyTime;

    @ApiModelProperty("奖励或者惩罚类别")
    @JsonProperty(value = "category")
    private Integer category;

    @ApiModelProperty("奖励或者惩罚类别 翻译")
    @JsonProperty(value = "category_value")
    private String categoryValue;

    @ApiModelProperty("奖励或者惩罚级别")
    @JsonProperty(value = "level")
    private Integer level;

    @ApiModelProperty("奖励或者惩罚级别 翻译")
    @JsonProperty(value = "level_value")
    private String levelValue;

    @ApiModelProperty("奖励或者惩罚名称")
    @JsonProperty(value = "name")
    private Integer name;

    @ApiModelProperty("奖惩名称内容")
    @JsonProperty(value = "content")
    private String content;

    @ApiModelProperty("奖惩类型（1:奖励 2:惩罚）")
    @JsonProperty(value = "type")
    private Integer type;

    @ApiModelProperty("奖励或者惩罚名称 列表")
    @JsonProperty(value = "name_op_list")
    private List<Integer> nameOpList;

    @ApiModelProperty("奖励或者惩罚名称 翻译")
    @JsonProperty(value = "name_value")
    private String nameValue;

    @ApiModelProperty("依据说明")
    @JsonProperty(value = "basis_description")
    private String basisDescription;

    @ApiModelProperty("颁奖单位")
    @JsonProperty(value = "award_unit")
    private String awardUnit;

    @ApiModelProperty("相关文件")
    @JsonProperty(value = "related_file")
    private String relatedFile;

    @ApiModelProperty("审核状态 1-待审核、2-审核通过、3-审核不通过")
    @JsonProperty(value = "approval_status")
    private Integer approvalStatus;

    // 大屏用的字段 xxxxx（组织名称），获得xxx级（级别）xxxxx（奖惩名称）
    @JsonProperty(value = "datav_message")
    private String datavMessage;

    public Long getMeetingOrgCommendPenalizeId() {
        return meetingOrgCommendPenalizeId;
    }

    public void setMeetingOrgCommendPenalizeId(Long meetingOrgCommendPenalizeId) {
        this.meetingOrgCommendPenalizeId = meetingOrgCommendPenalizeId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Date getRatifyTime() {
        return ratifyTime;
    }

    public void setRatifyTime(Date ratifyTime) {
        this.ratifyTime = ratifyTime;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public String getCategoryValue() {
        return categoryValue;
    }

    public void setCategoryValue(String categoryValue) {
        this.categoryValue = categoryValue;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getLevelValue() {
        return levelValue;
    }

    public void setLevelValue(String levelValue) {
        this.levelValue = levelValue;
    }

    public Integer getName() {
        return name;
    }

    public void setName(Integer name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<Integer> getNameOpList() {
        return nameOpList;
    }

    public void setNameOpList(List<Integer> nameOpList) {
        this.nameOpList = nameOpList;
    }

    public String getNameValue() {
        return nameValue;
    }

    public void setNameValue(String nameValue) {
        this.nameValue = nameValue;
    }

    public String getBasisDescription() {
        return basisDescription;
    }

    public void setBasisDescription(String basisDescription) {
        this.basisDescription = basisDescription;
    }

    public String getAwardUnit() {
        return awardUnit;
    }

    public void setAwardUnit(String awardUnit) {
        this.awardUnit = awardUnit;
    }

    public String getRelatedFile() {
        return relatedFile;
    }

    public void setRelatedFile(String relatedFile) {
        this.relatedFile = relatedFile;
    }

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getDatavMessage() {
        return datavMessage;
    }

    public void setDatavMessage(String datavMessage) {
        this.datavMessage = datavMessage;
    }
}
