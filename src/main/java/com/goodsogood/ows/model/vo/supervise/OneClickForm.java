package com.goodsogood.ows.model.vo.supervise;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OneClickForm {

    @JsonProperty(value = "type")
    Integer type;


    @JsonProperty(value = "user_ids")
    List<Long> userIds;


    @JsonProperty(value = "choose_org_ids")
    List<Long> chooseOrgIds;


    @JsonProperty(value = "option_keys")
    List<OptionInfo> optionKeys;




    /**
     * 选项卡信息
     */
    @Data
    public static class OptionInfo {

        @JsonProperty(value = "option_key")
        private String optionKey;

        /**
         * org 或者 user
         */
        @JsonProperty(value = "query_type")
        private String queryType;

        /**
         * 统计的数量
         */
        private Integer count;

    }

}
