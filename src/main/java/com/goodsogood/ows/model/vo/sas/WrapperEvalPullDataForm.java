package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @program: ows-sas
 * @description: ${description}
 * @author: Mr.<PERSON>
 * @create: 2019-05-08 16:30
 **/
@Data
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class WrapperEvalPullDataForm<T> {

    /**
     * 有好多页
     */
    private int pages;

    /**
     * 分页的大小
     */
    private int pageSize;


    /**
     * 总计的数据
     */
    private Long total;

    /**
     * 查询的数据
     */
    private List<T>  list;

}
