package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 活动类型
 *
 * <AUTHOR>
 * @date 2019/4/22 15:48
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActivityTypeForm {

    @ApiModelProperty(value = "活动类型ID")
    @JsonProperty("activity_id")
    private String activityId;

    @ApiModelProperty(value = "活动名称")
    @JsonProperty("activity_name")
    private String activityName;
}
