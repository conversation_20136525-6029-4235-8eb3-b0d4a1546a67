package com.goodsogood.ows.model.vo.user

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.goodsogood.ows.model.vo.EthnicNumberInfoVo
import com.goodsogood.ows.model.vo.GetBackdropPagesInfoVo
import com.goodsogood.ows.model.vo.GetPartAgeInfoVo
import com.goodsogood.ows.model.vo.getPartyAgeNumberInfoVo
import io.swagger.annotations.ApiModel
import java.time.LocalDateTime
import java.util.Date

/**
 *
 * <AUTHOR>
 * @createTime 2022年06月15日 11:35:00
 */
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class OrgPeriodResultForm(

    var orgId: Long? = null,

    var name: String? = null,

    var time: String? = null
) {
    override fun toString(): String {
        return "OrgPeriodForm(orgId=$orgId, name=$name, time=$time)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class OrgPeriodShxfResultForm(

    var orgId: Long? = null,

    var name: String? = null,

    var shortName: String? = null,

    var communist: Int = 0,

    var general: Int = 0,

    var branch: Int = 0,
) {
    override fun toString(): String {
        return "OrgPeriodShxfResultForm(orgId=$orgId, name=$name, shortName=$shortName, communist=$communist, general=$general, branch=$branch)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class OrgUserNumberInfo(
    var officialMember: Int = 0, var prepareMember: Int = 0, var man: Int = 0, var woman: Int = 0
) {
    override fun toString(): String {
        return "OrgUserOverviewInfo(officialMember=$officialMember, prepareMember=$prepareMember, man=$man, woman=$woman)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class OrgUserOverviewInfo(
    var officialMember: OverviewInfo = OverviewInfo(),
    var prepareMember: OverviewInfo = OverviewInfo(),
    var man: OverviewInfo = OverviewInfo(),
    var woman: OverviewInfo = OverviewInfo(),
    var total: Int = 0,
    var politicalBirthdayNum: Int = 0,
    var userList: MutableList<UserInfo> = mutableListOf()
) {
    override fun toString(): String {
        return "OrgUserOverviewInfo(officialMember=$officialMember, prepareMember=$prepareMember, man=$man, woman=$woman, total=$total, politicalBirthdayNum=$politicalBirthdayNum, userList=$userList)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class UserInfo(
    var userId: Long? = null,
    var name: String? = null,
    var orgName: String? = null,
    var partyAge: Int = 0,
    var partyAgeStr: String? = null,
    var politicalBirthday: String? = null,
    var gender: String? = null,
    var birthday: String? = null,
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8") var joinTime: LocalDateTime? = null,
    var joiningTimeDate: String? = null,
    var joiningTime: String? = null,
    var education: String? = null,
    //政治面貌
    var officialName: String? = null,
    //人员身份
    var identity: String? = null,
    var position: String? = null

) {
    override fun toString(): String {
        return "UserInfo(userId=$userId, name=$name, orgName=$orgName, politicalBirthday=$politicalBirthday, partyAge=$partyAge)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class OverviewInfo(
    var number: Int = 0, var percent: Int = 0
) {
    override fun toString(): String {
        return "OverviewInfo(number=$number, percent=$percent)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class PartyDistributedForm(
    var partyAge: MutableList<DistributedForm> = mutableListOf(),

    var age: MutableList<DistributedForm> = mutableListOf(),

    var edu: MutableList<DistributedForm> = mutableListOf(),

    var eth: MutableList<DistributedForm> = mutableListOf()
) {
    override fun toString(): String {
        return "PartyDistributedForm(partyAge=$partyAge, age=$age, edu=$edu, eth=$eth)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class DistributedForm(
    var name: String? = null,

    var value: Long = 0,

    var percent: Long = 0,

    var type: Int
) {
    override fun toString(): String {
        return "DistributedForm(name=$name, value=$value, percent=$percent)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class PartyInfoPartyDistributedForm(
    var partyAge: MutableList<PartyAgeInfoDistributedForm> = mutableListOf(),

    var age: MutableList<PartyInfoDistributedForm> = mutableListOf(),

    var edu: MutableList<PartyEducationInfoDistributedForm> = mutableListOf(),

    var eth: MutableList<PartyEthnicInfoDistributedForm> = mutableListOf()
) {
    override fun toString(): String {
        return "PartyInfoPartyDistributedForm(partyAge=$partyAge, age=$age, edu=$edu, eth=$eth)"
    }
}


@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class PartyInfoDistributedForm(
    var name: String? = null,

    var value: MutableList<GetPartAgeInfoVo>? = null,

    var type: Int
) {
    override fun toString(): String {
        return "DistributedForm(name=$name, value=$value)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class PartyEducationInfoDistributedForm(
    var name: String? = null,

    var value: MutableList<GetBackdropPagesInfoVo>? = null,

    var type: Int
) {
    override fun toString(): String {
        return "DistributedForm(name=$name, value=$value)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class PartyAgeInfoDistributedForm(
    var name: String? = null,

    var value: MutableList<getPartyAgeNumberInfoVo>? = null,

    var type: Int
) {
    override fun toString(): String {
        return "DistributedForm(name=$name, value=$value)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class PartyEthnicInfoDistributedForm(
    var name: String? = null,

    var value: MutableList<EthnicNumberInfoVo>? = null,

    var type: Int
) {
    override fun toString(): String {
        return "DistributedForm(name=$name, value=$value)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class ScreenPeriodOrgInfo(

    var committee: PeriodStaInfo? = null,

    var total: PeriodStaInfo? = null,

    var branch: PeriodStaInfo? = null
) {
    override fun toString(): String {
        return "ScreenPeriodOrgInfo(committee=$committee, total=$total, branch=$branch)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class PeriodStaInfo(

    var already: Int = 0,

    var not: Int = 0
) {
    override fun toString(): String {
        return "PeriodStaInfo(already=$already, not=$not)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class AllCountInfo(

    var communistCount: Int = 0, var generalCount: Int = 0, var branchCount: Int = 0, var partyMemberCount: Int = 0
) {
    override fun toString(): String {
        return "AllCountInfo(communistCount=$communistCount, generalCount=$generalCount, branchCount=$branchCount, partyMemberCount=$partyMemberCount)"
    }
}


@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = SnakeCaseStrategy::class)
data class DynamicInfo(
    var dynamicInfo: String? = null, var showTime: String? = null, var createTime: Date? = null
) {
    override fun toString(): String {
        return "PartyDistributedForm(dynamicInfo=$dynamicInfo, time=$showTime,createTime=$createTime)"
    }
}
