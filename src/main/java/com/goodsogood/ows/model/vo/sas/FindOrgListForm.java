package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName FindOrgListForm
 * @description
 * @date 2018-12-11 16:08
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FindOrgListForm {

    @JsonProperty("id_list")
    private List<Long> idList = new ArrayList<>();

    @JsonProperty("is_employee")
    private Integer isEmployee;

    @JsonProperty("is_filter")
    private Integer isFilter;

    @JsonProperty("is_include")
    private Integer isInclude = 2;

    private Long regionId;
}
