package com.goodsogood.ows.model.vo.datav

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.goodsogood.ows.model.vo.datav.base.CardFlop
import com.goodsogood.ows.model.vo.datav.base.Dashboard
import com.goodsogood.ows.model.vo.datav.base.GenericTitle
import com.goodsogood.ows.model.vo.datav.base.OneCardFlop

/**
 * 可视化大屏-党务工作-党费交纳
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class PpmdVO @JvmOverloads constructor(
    // 本月已交党费翻牌器
    var cardFlopMoon: MutableList<OneCardFlop> = mutableListOf(),
    // 本年度已交党费翻牌器
    var cardFlopYear: MutableList<OneCardFlop> = mutableListOf(),
    // 本年度已交党费标题
    var genericTitle: MutableList<GenericTitle> = mutableListOf(),
    // 当月交纳完成度
    var dashboard: MutableList<Dashboard> = mutableListOf(),
)
