package com.goodsogood.ows.model.vo

class UserSeqVO {

    var type: Int? = null

    var useIds: MutableList<Long>? = mutableListOf()

    constructor()

    constructor(type: Int?, useIds: MutableList<Long>?) {
        this.type = type
        this.useIds = useIds
    }

    constructor(type: Int?) {
        this.type = type
    }

    override fun toString(): String {
        return "UserSeqVO(type=$type, useIds=$useIds)"
    }

}

class ItemVO {

    var item: String? = null

    var userId: Long? = null

    constructor()

    constructor(item: String?, userId: Long?) {
        this.item = item
        this.userId = userId
    }

    override fun toString(): String {
        return "ItemVO(item=$item, userId=$userId)"
    }
}

class UserItemVO {

    var item: String? = null

    var type: Int? = null

    var userIds: MutableList<Long?> = mutableListOf()

    constructor()

    constructor(item: String?, type: Int?,userIds: MutableList<Long?>) {
        this.item = item
        this.userIds = userIds
        this.type = type
    }

    override fun toString(): String {
        return "ItemVO(item=$item, userIds=$userIds)"
    }
}