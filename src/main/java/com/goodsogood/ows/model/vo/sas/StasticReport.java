package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @program: ows-sas
 * @description: ${description}
 * @author: Mr<PERSON>
 * @create: 2019-04-23 19:06
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StasticReport {
    @JsonProperty("org_name")
    private String orgName;

    @JsonProperty("month1")
    private Integer month1;

    @JsonProperty("month2")
    private Integer month2;

    @JsonProperty("month3")
    private Integer month3;

    @JsonProperty("month4")
    private Integer month4;

    @JsonProperty("month5")
    private Integer month5;

    @JsonProperty("month6")
    private Integer month6;

    @JsonProperty("month7")
    private Integer month7;

    @JsonProperty("month8")
    private Integer month8;

    @JsonProperty("month9")
    private Integer month9;

    @JsonProperty("month10")
    private Integer month10;

    @JsonProperty("month11")
    private Integer month11;

    @JsonProperty("month12")
    private Integer month12;

    /**
     * 组织每个月合计
     */
    private Integer totalNum;
}
