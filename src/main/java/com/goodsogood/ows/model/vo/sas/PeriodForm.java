package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @program: ows-sas
 * @description: ${description}
 * @author: Mr<PERSON>
 * @create: 2019-05-22 16:57
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PeriodForm {

    @NotNull
    @JsonProperty("org_id")
    private Long orgId;

    @NotNull
    @JsonProperty("period_query_time")
    private String periodQueryTime;
}
