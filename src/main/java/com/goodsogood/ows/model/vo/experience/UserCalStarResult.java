package com.goodsogood.ows.model.vo.experience;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class UserCalStarResult {

    @JsonProperty(value = "rule_id")
    private Long ruleId;

    @ApiModelProperty(value = "date_month")
    private Integer dateMonth;


    @ApiModelProperty(value = "star")
    private Integer star;

    @ApiModelProperty(value = "user_id")
    private Long userId;

}
