package com.goodsogood.ows.model.vo.score;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ReportVo {
    public Integer avgScore;//累计平均积分
    public Integer maxScore;//累计最大积分
    public Integer yearAvg;//年度平均积分
    public Integer yearMax;//年度最大积分


}
