package com.goodsogood.ows.model.vo.tbcFusion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName : TbcScoreRankPartyVo
 * <AUTHOR> tc
 * @Date: 2022/6/20 18:48
 * @Description : 党建积分排名信息
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TbcScoreRankPartyVo {

    @ApiModelProperty("用户编号")
    @JsonProperty(value = "user_id")
    private Long userId;

    @ApiModelProperty("党建积分")
    @JsonProperty(value = "party_score")
    private Long partyScore;

    @ApiModelProperty("本单位党员中排名")
    @JsonProperty(value = "unit_rank")
    private Integer unitRank;

    @ApiModelProperty("本单位党员中人数")
    @JsonProperty(value = "unit_num")
    private Integer unitNum;

    @ApiModelProperty("同序列党员中排名")
    @JsonProperty(value = "marketing_rank")
    private Integer marketingRank;

    @ApiModelProperty("同序列党员中人数")
    @JsonProperty(value = "marketing_num")
    private Integer marketingNum;

    @ApiModelProperty("全市党员中排名")
    @JsonProperty(value = "all_rank")
    private Integer allRank;

    @ApiModelProperty("全市党员中人数")
    @JsonProperty(value = "all_num")
    private Integer allNum;

}
