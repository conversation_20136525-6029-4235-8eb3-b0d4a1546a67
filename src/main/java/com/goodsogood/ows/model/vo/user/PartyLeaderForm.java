package com.goodsogood.ows.model.vo.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023-03-07 16:17:07
 * @Description PartyLeaderForm
 */
@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PartyLeaderForm {
    public Integer partyTotal;
    public Integer deputyTotal;
    public Integer memberTotal;
    public Integer fullTimeTotal;
    public Integer partyTimeTotal;
    public Integer sumTotal;
}
