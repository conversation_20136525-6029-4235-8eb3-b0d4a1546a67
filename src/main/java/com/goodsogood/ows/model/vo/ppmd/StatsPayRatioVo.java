package com.goodsogood.ows.model.vo.ppmd;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 交费比例类
 *
 * @auther Administrator tc
 * @date 2018/10/24
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StatsPayRatioVo {

    /**
     * 计算基数下限 单位:分
     */
    @JsonProperty(value = "lower_limit")
    @ApiModelProperty("计算基数下限")
    private Integer lowerLimit;

    /**
     * 计算基数上限 单位:分
     */
    @JsonProperty(value = "upper_limit")
    @ApiModelProperty("计算基数上限")
    private Integer upperLimit;

    /**
     * 交纳比例  百分数
     */
    @JsonProperty(value = "proportion")
    @ApiModelProperty("交纳比例")
    private Double proportion;

    /**
     * 交费比例类型:1.正常党员交费比例  2.退休党员交费比例  默认 1
     */
    @JsonProperty(value = "ratio_type")
    @ApiModelProperty("交费比例类型")
    private Integer ratioType;
}
