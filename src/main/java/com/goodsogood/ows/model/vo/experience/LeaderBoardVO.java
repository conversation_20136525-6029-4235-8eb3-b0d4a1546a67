package com.goodsogood.ows.model.vo.experience;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.util.Comparator;
import java.util.List;

/**
 * 五星党员排行榜
 * @Author: mengting
 * @Date: 2022/4/22 16:00
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LeaderBoardVO implements Comparable<LeaderBoardVO> {
    private Long orgId;
    private String name;
    private String proportion;//五星占比
    private Double propDouble;
    private List<LeaderBoardVO>  unitBoard;//单位
    private List<LeaderBoardVO> orgBoard;//党支部

    public LeaderBoardVO(Long orgId ,String name, String proportion, Double propDouble) {
        this.orgId = orgId;
        this.name = name;
        this.proportion = proportion;
        this.propDouble = propDouble;
    }


    @Override
    public int compareTo(@NotNull LeaderBoardVO o) {
        if( o.getPropDouble().compareTo(this.propDouble)==0){
            return this.orgId.compareTo(o.getOrgId());
        }else{
            return  o.getPropDouble().compareTo(this.propDouble);
        }
    }
}
