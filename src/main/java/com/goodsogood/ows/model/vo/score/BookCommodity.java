package com.goodsogood.ows.model.vo.score;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * t_score_order_commodity
 */
@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class BookCommodity {

    @ApiModelProperty("t_score_order_commodity 主键id")
    private Long orderCommodityId;

    @ApiModelProperty("商品编号")
    private String commodityId;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("数量")
    private Integer count;

    @ApiModelProperty("订单id 冗余数据 新增mongo时需要去除掉")
    private Long orderId;
}