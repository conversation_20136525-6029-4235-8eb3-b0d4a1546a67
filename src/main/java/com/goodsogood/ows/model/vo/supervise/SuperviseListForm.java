package com.goodsogood.ows.model.vo.supervise;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SuperviseListForm {

    @JsonProperty(value = "current_tab")
    private TabInfo currentTab;

    @JsonProperty(value = "next_tab")
    private TabInfo nextTab;

    @JsonProperty(value = "option_list")
    private List<OptionForm> optionList;

    /**
     * 选项卡信息
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TabInfo {

        @JsonProperty(value = "tab_key")
        private String tabKey;


        @JsonProperty(value = "tab_name")
        private String tabName;

    }

}
