package com.goodsogood.ows.model.vo.report;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.mongodb.report.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 组织电子党务报告实体类
 * @date 2019/11/22
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OrgReportForm implements Serializable {

    @ApiModelProperty("组织ID")
    private Long orgId;

    @ApiModelProperty("区县Id")
    private Long regionId;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("统计时间 yyyy-MM")
    private String startDate;

    @ApiModelProperty("统计时间 yyyy-MM")
    private String endDate;

    @ApiModelProperty("党费情况")
    private PpmdReportInfo ppmdReport;

    @ApiModelProperty("组织生活情况")
    private MeetingReportInfo meetingReport;

    @ApiModelProperty("党群活动情况")
    private ActivityReportInfo activityReport;

    @ApiModelProperty("学习情况")
    private StudyReportInfo studyReport;

    @ApiModelProperty("扶贫情况")
    private DonateReportInfo donateReport;
}
