package com.goodsogood.ows.model.vo.datav

import com.goodsogood.ows.model.vo.datav.base.CardFlop
import com.goodsogood.ows.model.vo.datav.base.ProgressBar

/**
 * <AUTHOR>
 * @date 2023/3/13
 * @description class TaskVO 可视化大屏-党务工作-系统任务
 */
data class TaskVO @JvmOverloads constructor(
    // 翻牌器 - 党建任务、业务任务、创新任务、云上任务、发布任务数、已完成、正在进行、党员参与人次、非党员参与人次
    val cardFlop: Array<List<CardFlop>?> = arrayOfNulls(9),
    // 进度条 - 已完成进度、党员参与人次进度
    val progressBar: Array<List<ProgressBar>?> = arrayOfNulls(2),
) {
    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (other !is TaskVO) return false

        if (!cardFlop.contentEquals(other.cardFlop)) return false
        if (!progressBar.contentEquals(other.progressBar)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = cardFlop.contentHashCode()
        result = 31 * result + progressBar.contentHashCode()
        return result
    }
}