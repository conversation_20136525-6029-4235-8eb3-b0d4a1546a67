package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by FuXiao on 2019/9/26
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MemberResultForm {
    @ApiModelProperty(value = "自然生日党员列表")
    @JsonProperty("birthday_list")
    private List<UserInfoBase> birthdayList = new ArrayList<>();

    @ApiModelProperty(value = "政治生日党员列表")
    @JsonProperty("political_list")
    private List<UserInfoBase> politicalList = new ArrayList<>();
}
