package com.goodsogood.ows.model.vo.tbc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/1/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class) //自动驼峰命名
@JsonInclude(JsonInclude.Include.NON_NULL)  //json 转实例忽略为空
@JsonIgnoreProperties(ignoreUnknown = true) //转json 忽略为空
@AllArgsConstructor
@NoArgsConstructor
public class OrganLifeUserCommentForm extends TbcUserOrganBaseForm {

    @ApiModelProperty(value = "民主评议结果")
    private String commentResult;

    @ApiModelProperty(value = "组织书记")
    private String partyLeader;
}
