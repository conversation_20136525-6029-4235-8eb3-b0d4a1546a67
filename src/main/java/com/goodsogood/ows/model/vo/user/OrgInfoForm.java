package com.goodsogood.ows.model.vo.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 组织单位公用查询模型
 *
 * <AUTHOR>
 * @create 2018-05-08 10:16
 **/
@Data
@ApiModel
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgInfoForm {

     @ApiModelProperty(value = "组织ID")
     @JsonProperty("organization_id")
     private Long organizationId;

     @ApiModelProperty(value = "组织名称")
     @JsonProperty(value = "name")
     private String name;

     @ApiModelProperty(value = "组织简称")
     @JsonProperty(value = "short_name")
     private String shortName;

     @ApiModelProperty(value = "组织类型")
     @JsonProperty(value = "type_name")
     private String typeName;

     @ApiModelProperty(value = "组织类型")
     @JsonProperty(value = "type_child_name")
     private String typeChildName;

     @ApiModelProperty(value = "行业类别")
     @JsonProperty(value = "industry_type_name")
     private String industryTypeName;

     @ApiModelProperty(value = "组织代码")
     @JsonProperty(value = "org_code")
     private String orgCode;

     @ApiModelProperty(value = "上级组织名称")
     @JsonProperty(value = "parent_name")
     private String parentName;

     @ApiModelProperty(value = "组织联系人")
     @JsonProperty(value = "org_contacts")
     private String orgContacts;

     @ApiModelProperty(value = "联系人电话")
     @JsonProperty(value = "org_phone")
     private String orgPhone;

     @ApiModelProperty(value = "组织所属代码")
     @JsonProperty(value = "org_area")
     private String orgArea;

     @ApiModelProperty(value = "组织负责人")
     @JsonProperty(value = "org_leader")
     private String orgLeader;

     @ApiModelProperty(value = "组织负责人电话")
     @JsonProperty(value = "org_leader_phone")
     private String orgLeaderPhone;

     @ApiModelProperty(value = "组织联系地址")
     @JsonProperty(value = "org_address")
     private String orgAddress;

     @ApiModelProperty(value = "邮政编码")
     @JsonProperty(value = "postcode")
     private String postcode;

     @ApiModelProperty(value = "上级组织Id")
     @JsonProperty("parent_id")
     private Long parentId;

     @ApiModelProperty(value = "组织类型")
     @JsonProperty("org_type")
     private Integer orgType;

     @ApiModelProperty(value = "组织类型子级")
     @JsonProperty("org_type_child")
     private Integer orgTypeChild;

     @ApiModelProperty(value = "行业类别")
     @JsonProperty("industry_type")
     private Integer industryType;

     @ApiModelProperty(value = "组织所属行政区域名称")
     @JsonProperty(value = "area_name")
     private String areaName;

     @ApiModelProperty(value = "第三方数据")
     @JsonProperty(value = "third_data_info")
     private String thirdDataInfo;

     @ApiModelProperty(value = "所属组织ID")
     @JsonProperty(value = "owner_id")
     private Long ownerId;

     @ApiModelProperty(value = "所属组织名称")
     @JsonProperty(value = "owner_name")
     private String ownerName;

     @ApiModelProperty(value = "组织所属单位组织类型")
     @JsonProperty("owner_org_type")
     private Integer ownerOrgType;

     @ApiModelProperty(value = "组织所属单位组织类型子级")
     @JsonProperty("owner_org_type_child")
     private Integer ownerOrgTypeChild;

     @ApiModelProperty(value = "父树名称")
     @JsonProperty(value = "parent_tree_name")
     private String parentTreeName;

     @ApiModelProperty(value = "是否是离退休党组织 1-是 2-否")
     @JsonProperty("is_retire")
     private Integer isRetire;

     @ApiModelProperty(value = "所属组织树 1-父树 2-子树")
     @JsonProperty("owner_tree")
     private Integer ownerTree;

     @ApiModelProperty(value = "是否流动党员党组织 1-是 2-否")
     @JsonProperty("is_flow")
     private Integer isFlow;

     @ApiModelProperty(value = "区县ID")
     @JsonProperty(value = "region_id")
     private Long regionId;

     @ApiModelProperty(value = "组织创建时间")
     @JsonProperty(value = "org_create_time")
     private String orgCreateTime;
}

