package com.goodsogood.ows.model.vo.datav

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.goodsogood.ows.model.vo.datav.base.*

/**
 * 可视化大屏-党务工作-组织生活
 * 可视化大屏-党建概况-组织概况
 * 可视化大屏-党建概况-争先创优
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class DataVO @JvmOverloads constructor(
    // 标题
    var title: MutableList<GenericTitle> = mutableListOf(),
    // 副标题
    var subTitle: MutableList<GenericTitle>? = null,
    // 翻牌器
    var cardFlop: MutableList<CardFlop> = mutableListOf(),
    // 进度条 （可选）
    var progressBar: MutableList<ProgressBar>? = null,
    // key value数据
    var keyValue: MutableList<Pair<String, *>>? = null,
    // 仪表盘数据
    var dashboard: MutableList<Dashboard>? = null,
    // 饼图
    var pie: MutableList<Pie>? = null,
    // 多维度饼图
    var multiPie: MutableList<MultidimensionalPie>? = null
) {
    fun addCardFlop(cardFlop: CardFlop): DataVO {
        this.cardFlop.add(cardFlop)
        return this
    }

    fun addTitle(genericTitle: GenericTitle): DataVO {
        this.title.add(genericTitle)
        return this
    }

    /**
     * 添加key value数据
     * !!!线程不安全
     */
    fun addKeyValue(pair: Pair<String, *>): DataVO {
        if (keyValue == null) {
            keyValue = mutableListOf()
        }
        keyValue?.add(pair)
        return this
    }
}
