package com.goodsogood.ows.model.vo.pbm;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PbmOrgTargetUnitVo {

    @ApiModelProperty("单位名称")
    @Excel(name = "单位名称")
    private String orgName;

    @ApiModelProperty("业务绩效得分")
    @Excel(name = "业务绩效得分")
    private Double score;

    @ApiModelProperty("备注")
    @Excel(name = "备注")
    private String remark;
}
