package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import org.joda.time.DateTime

/**
 *
 * <AUTHOR>
 * @createTime 2022年08月12日 11:57:00
 */
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonIgnoreProperties(ignoreUnknown = true)
data class ScreenOrgVo(

    var orgId: Long? = null,

    var orgName: String? = null,

    var orgShortName: String? = null,

    var total: Int? = null
) {
    override fun toString(): String {
        return "OrgVo(orgId=$orgId, orgName=$orgName, orgShortName=$orgShortName, total=$total)"
    }
}

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonIgnoreProperties(ignoreUnknown = true)
data class BranchCommitteeVo(

    var total: Int = 0,

    var already: Int = 0,

    var not: Int = 0
) {
    override fun toString(): String {
        return "BranchCommitteeVo(total=$total, already=$already, not=$not)"
    }
}

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonIgnoreProperties(ignoreUnknown = true)
data class OrgHonorVo(

    var date: String? = null,

    var name: String? = null
) {
    override fun toString(): String {
        return "OrgHonorVo(date=$date, name=$name)"
    }
}

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonIgnoreProperties(ignoreUnknown = true)
data class GetPartAgeInfoVo(

    var name: String? = null,

    var orgName: String? = null,

    var age: Int? = null
) {
    override fun toString(): String {
        return "GetPartAgeInfoVo(name=$name, orgName=$orgName, age=$age)"
    }
}

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonIgnoreProperties(ignoreUnknown = true)
data class GetBackdropPagesInfoVo(

    var name: String? = null,

    var orgName: String? = null,

    var value: String? = null
) {
    override fun toString(): String {
        return "GetBackdropPagesInfo(name=$name, orgName=$orgName, value=$value)"
    }
}

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonIgnoreProperties(ignoreUnknown = true)
data class getPartyAgeNumberInfoVo(

    var name: String? = null,

    var orgName: String? = null,

    var joiningTime: Long? = null
) {
    override fun toString(): String {
        return "getPartyAgeNumberInfoVo(name=$name, orgName=$orgName, joiningTime=$joiningTime)"
    }
}

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonIgnoreProperties(ignoreUnknown = true)
data class EthnicNumberInfoVo(

    var name: String? = null,

    var orgName: String? = null,

    var ethnic: String? = null
) {
    override fun toString(): String {
        return "getPartyAgeNumberInfoVo(name=$name, orgName=$orgName, ethnic=$ethnic)"
    }
}