package com.goodsogood.ows.model.vo.dataCockpit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 换届数据Vo
 *
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserOrgPeriodVO {

     @ApiModelProperty("支部书记名称")
     public String userName;

     @ApiModelProperty("支部书记名称")
     public Date startTime;

     @ApiModelProperty("支部书记名称")
     public Date endTime;

}
