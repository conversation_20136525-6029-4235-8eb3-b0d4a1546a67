package com.goodsogood.ows.model.vo.fusion

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModelProperty

/**
 *
 * <AUTHOR>
 * @createTime 2023年03月17日 10:48:00
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FusionIndexVO(

    @ApiModelProperty("单位ID")
    var unitId: Long? = null,

    @ApiModelProperty("单位名称")
    var unitName: String? = null,

    @ApiModelProperty("融合度")
    var fusionValue: Double = 0.0,

    @ApiModelProperty("本次排名")
    var rank: Int = 0,

    @ApiModelProperty("排名变化 >0上升， <0下降")
    var change: Int = 0,

    @ApiModelProperty("系统均值")
    var avg: Double = 0.0,

    @ApiModelProperty("系统最高")
    var max: Double = 0.0,

    @ApiModelProperty("系统最低")
    var min: Double = 0.0,

    @ApiModelProperty("更新时间")
    var updateTime: String? = null,

    @ApiModelProperty("折线图")
    var line: Map<Int?, MutableList<String>> = mutableMapOf()
) {
    override fun toString(): String {
        return "FusionIndexVO(unitId=$unitId, unitName=$unitName, fusionValue=$fusionValue, rank=$rank, change=$change, avg=$avg, max=$max, min=$min, line=$line)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FusionMapDetail(

    @ApiModelProperty("区域编码")
    var adcode: String? = null,

    @ApiModelProperty("经度")
    var longitude: Double? = null,

    @ApiModelProperty("纬度")
    var latitude: Double? = null,

    @ApiModelProperty("单位ID")
    var unitId: Long? = null,

    @ApiModelProperty("单位名称")
    var unitName: String? = null,

    @ApiModelProperty("单位简称")
    var unitShortName: String? = null,

    @ApiModelProperty("支部数量")
    var branch: Int = 0,

    @ApiModelProperty("党员数量")
    var member: Int = 0,

    @ApiModelProperty("融合度")
    var fusion: Double = 0.0,

    @ApiModelProperty("排名")
    var rank: Int = 0,

    @ApiModelProperty("变化")
    var change: Int = 0
) {
    override fun toString(): String {
        return "FusionMapDetail(adcode=$adcode, longitude=$longitude, latitude=$latitude, unitId=$unitId, unitName=$unitName, unitShortName=$unitShortName, branch=$branch, member=$member, fusion=$fusion, rank=$rank, change=$change)"
    }
}

data class TopOrgDetail(
    var unitId: Long? = null,
    var organizationId: Long? = null,
    var shortName: String? = null,
    var longitude: Double? = null,
    var latitude: Double? = null,
    var adcode: String? = null,
    var branch: Int = 0,
    var member: Int = 0
) {
    override fun toString(): String {
        return "TopOrgDetail(unitId=$unitId, organizationId=$organizationId, shortName=$shortName, longitude=$longitude, latitude=$latitude, adcode=$adcode, branch=$branch, member=$member)"
    }
}
