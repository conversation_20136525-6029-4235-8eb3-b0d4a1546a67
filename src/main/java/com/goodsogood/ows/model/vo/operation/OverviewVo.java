package com.goodsogood.ows.model.vo.operation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.goodsogood.ows.helper.Json.DoubleKeepZeroSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName : OverviewVo
 * <AUTHOR> tc
 * @Date: 2022/4/13 15:27
 * @Description : 数据概览VO类
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OverviewVo {

    @JsonProperty(value = "region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;

    @JsonProperty(value = "user_id")
    @ApiModelProperty("用户编号")
    private Long userId;

    @JsonProperty(value = "user_name")
    @ApiModelProperty("用户名称")
    private String userName;

    @JsonProperty(value = "visit_num")
    @ApiModelProperty("累计访问天数")
    private Integer visitNum;

    @JsonProperty(value = "rank_all")
    @ApiModelProperty("在全市排名")
    private Integer rankAll;

    @JsonProperty(value = "rank_unit")
    @ApiModelProperty("在登录单位排名")
    private Integer rankUnit;

    @JsonProperty(value = "unit_visit")
    @ApiModelProperty("登录单位的今日访问量")
    private Integer unitVisit;

    @JsonProperty(value = "unit_visit_rate")
    @ApiModelProperty("登录单位的今日访问率")
    @JsonSerialize(using = DoubleKeepZeroSerializer.class)
    private Double unitVisitRate;

    @JsonProperty(value = "unit_online")
    @ApiModelProperty("登录单位的当前在线人数")
    private Integer unitOnline;

    @JsonProperty(value = "all_visit")
    @ApiModelProperty("全市今日访问量")
    private Integer allVisit;

    @JsonProperty(value = "all_visit_rate")
    @ApiModelProperty("全市今日访问率")
    @JsonSerialize(using = DoubleKeepZeroSerializer.class)
    private Double allVisitRate;

    @JsonProperty(value = "all_online")
    @ApiModelProperty("全市当前在线人数")
    private Integer allOnline;

    @JsonProperty(value = "blockchain_num")
    @ApiModelProperty("已产生区块链存证")
    private Integer blockchainNum;

    /**
     * 格式  yyyy-MM-dd HH:mm
     */
    @JsonProperty(value = "stats_date")
    @ApiModelProperty("更新时间")
    private String statsDate;

}
