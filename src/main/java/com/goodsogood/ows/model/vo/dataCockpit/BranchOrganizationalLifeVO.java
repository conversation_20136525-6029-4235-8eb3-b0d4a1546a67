package com.goodsogood.ows.model.vo.dataCockpit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据驾驶舱 - 组织生活(党支部)
 *
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BranchOrganizationalLifeVO {

     @ApiModelProperty("本年度开展活动总次数")
     public Integer totalNum = 0;

     @ApiModelProperty("支部党员大会次数")
     public Integer partyCongressNum = 0;

     @ApiModelProperty("支委会次数")
     public Integer branchCommitteeNum = 0;

     @ApiModelProperty("党小组会次数")
     public Integer partyGroupNum = 0;

     @ApiModelProperty("党课次数")
     public Integer partyLectureNum = 0;

     @ApiModelProperty("主题党日次数")
     public Integer themePartyDayNum = 0;

     @ApiModelProperty("支部党员大会完成进度")
     public Double partyCongressProgress;

     @ApiModelProperty("支委会完成进度")
     public Double branchCommitteeProgress;

     @ApiModelProperty("党小组会完成进度")
     public Double partyGroupProgress;

     @ApiModelProperty("党课完成进度")
     public Double partyLectureProgress;

     @ApiModelProperty("主题党日完成进度")
     public Double themePartyDayProgress;

}
