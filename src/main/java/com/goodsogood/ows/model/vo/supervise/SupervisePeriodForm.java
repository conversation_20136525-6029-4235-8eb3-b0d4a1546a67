package com.goodsogood.ows.model.vo.supervise;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SupervisePeriodForm {

    @JsonProperty(value = "org_id")
    private String orgId;

    @JsonProperty(value = "org_name")
    private String orgName;

    @JsonProperty(value = "org_short_name")
    private String orgShortName;

    @JsonProperty(value = "start_time")
    private String startTime;

    @JsonProperty(value = "end_time")
    private String endTime;

    /**
     * 是否设立支委会 1-是，0-否
     */
    @JsonProperty(value = "create_period")
    private Integer createPeriod;

    /**
     * 党员数量
     */
    @JsonProperty(value = "total")
    private Integer total ;


}
