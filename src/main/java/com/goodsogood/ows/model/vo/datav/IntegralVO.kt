package com.goodsogood.ows.model.vo.datav

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.goodsogood.ows.model.vo.datav.base.CardFlop
import com.goodsogood.ows.model.vo.datav.base.Line

/**
 * <AUTHOR>
 * @date 2023/3/13
 * @description class 可视化大屏-党务工作-党员积分
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class IntegralVO @JvmOverloads constructor(
    // 党员积分（累计最高积分、累计平均积分、年度最高积分、年度平均积分）
    var integral: Array<List<CardFlop>?>? = null,
    // 月积分折线图
    var integralNum: MutableList<Line>? = null,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is IntegralVO) return false

        if (integral != null) {
            if (other.integral == null) return false
            if (!integral.contentEquals(other.integral)) return false
        } else if (other.integral != null) return false
        if (integralNum != other.integralNum) return false

        return true
    }

    override fun hashCode(): Int {
        var result = integral?.contentHashCode() ?: 0
        result = 31 * result + (integralNum?.hashCode() ?: 0)
        return result
    }
}