package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KeyValVo {

    public String name;

    public Integer value;

    private String valueStr;

    private Long id;

    public KeyValVo(String name, Integer value, String valueStr, Long id) {
        this.name = name;
        this.value = value;
        this.valueStr = valueStr;
        this.id = id;
    }

    public KeyValVo() {
    }
}
