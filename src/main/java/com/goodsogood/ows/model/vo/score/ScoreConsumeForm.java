package com.goodsogood.ows.model.vo.score;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.log4j.Log4j2;


/**
 * 积分对象
 *
 * <AUTHOR>
 * @create 2021-08-06
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@Log4j2
public class ScoreConsumeForm {
    @ApiModelProperty("区县编号")
    private Long regionId;

    @JsonProperty(value = "user_id")
    @ApiModelProperty("用户id")
    private Long userId;

    @JsonProperty(value = "user_name")
    @ApiModelProperty("用户姓名")
    private String userName;

    @ApiModelProperty(name = "提交数据唯一标识")
    @JsonProperty(value = "token")
    private String token;

    @ApiModelProperty("分数")
    private Long score;

    /**
     * 6 优考试积分 7 新闻学习积分 8 打卡积分 9 答题积分 10 出题积分
     * 11 党费积极交纳积分 12 组织生活积分 13 党建任务积分 14 创新任务积分
     * 15 业务任务积分 16 创先争优积分 17 理论武装积分
     */
    @JsonProperty(value = "score_type")
    @ApiModelProperty("积分类型")
    private Integer scoreType;

    @ApiModelProperty("操作类型：0：新增 1：扣分")
    @JsonProperty(value = "oper_type")
    private Integer operType;

    /**
     * 积分所属的组织编号
     */
    @ApiModelProperty("积分所属的组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 积分组织编号
     */
    @JsonProperty(value = "score_org_id")
    @ApiModelProperty("积分组织编号")
    private Long scoreOrgId;

    /**
     * 说明 放发或扣减组织积分时的说明
     */
    @ApiModelProperty("放发或扣减组织积分时的说明")
    @JsonProperty(value = "explain_txt")
    private String explainTxt;

    /**
     * 新增党组的积分操作
     * scoreOrgType  积分组织类型 1.党树组织  2.党组
     * tc 2021-12-10
     */
    @ApiModelProperty("积分组织类型")
    @JsonProperty(value = "score_org_type")
    private Integer scoreOrgType;

    @ApiModelProperty(name = "消费时间")
    @JsonProperty(value = "consume_time")
    private String consumeTime;

    public ScoreConsumeForm() {
        super();
    }

    public ScoreConsumeForm(Long regionId, Long userId, String token, Long score, Integer scoreType, Integer operType, Long orgId, String explainTxt) {
        this.regionId = regionId;
        this.userId = userId;
        this.token = token;
        this.score = score;
        this.scoreType = scoreType;
        this.operType = operType;
        this.orgId = orgId;
        this.explainTxt = explainTxt;
    }

    public ScoreConsumeForm(Long regionId, String token, Long scoreOrgId, Long score, Integer scoreType, Integer operType, Long orgId, String explainTxt) {
        this.regionId = regionId;
        this.scoreOrgId = scoreOrgId;
        this.token = token;
        this.score = score;
        this.scoreType = scoreType;
        this.operType = operType;
        this.orgId = orgId;
        this.explainTxt = explainTxt;
    }

    public ScoreConsumeForm(Long regionId, String token, Long score, Integer scoreType, Integer operType, Long orgId, String explainTxt, String remark) {
        this.regionId = regionId;
        this.token = token;
        this.score = score;
        this.scoreType = scoreType;
        this.operType = operType;
        this.orgId = orgId;
        this.explainTxt = explainTxt;
        this.remark = remark;
    }
}
