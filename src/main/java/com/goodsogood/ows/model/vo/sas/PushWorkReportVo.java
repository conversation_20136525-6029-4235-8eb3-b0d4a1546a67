package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 高级管理员 上月工作汇总
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PushWorkReportVo {

    /**
     * 接受消息人员ID
     */
    private Long userId;

    /**
     * 接受消息人员的列表
     */
    private List<Long> listUserIds;

    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 区县ID
     */
    private Long regionId;

    /**
     * 接受消息人员名称
     */
    private String userName;

    /**
     * 组织简称
     */
    private String shortName;

    /**
     * 本月生日数量
     */
    private Integer birthDayNum;

    /**
     * 本月生日党员名称
     */
    private List<String> birthPersonName;

    /**
     * 本月政治生日数量
     */
    private Integer politicalNum;

    /**
     * 本月政治生日党员名称
     */
    private List<String> politicalPersonName;

    /**
     * 本月应换届数量
     */
    private Integer thisMonthPeriodNum;

    /**
     * 本月换届党委数量
     */
    private Integer thisMonthPartyNum;

    /**
     * 当前年月
     */
    private String date;

    /**
     * 上一月
     */
    private String lastDate;

    /**
     * 党费总金额
     */
    private Double totalMoney;

    /**
     * 党费未交齐的支部数量
     */
    private Integer oweNum;

    /**
     * 未交党费的党员名称
     */
    private List<String> oweName;

    /**
     * 组织生活开展完成支部数量
     */
    private Integer finishNum;

    /**
     * 组织生活开展未完成支部数量
     */
    private Integer unFinishNum;

    /**
     * 上个月完成换届支部数量
     */
    private Integer beforePeriodNum;

    /**
     * 未完成组织生活类型
     */
    private List<String> unFinishType;

    /**
     * 上月党费交齐支部
     */
    private String firstOrg;

    /**
     * 上月第一位缴纳的党员名称
     */
    private String firstUser;

    /**
     * 到期届次
     */
    private String periodNum;

    /**
     * 推送类型  1-高级管理员, 2-一般管理员, 3-党员
     */
    private Integer pushType;

    /**
     * 支部活动列表
     */
    // private List<StatisticalUserOrgLifeEntity> orgLiftList;

    /**
     * 学习积分
     */
    private Long studyScore;

    /**
     * 参加组织生活次数
     */
    private Integer joinListNum;

    /**
     * 增加积分
     */
    private Integer inScore;

    /**
     * 消耗积分
     */
    private Integer outScore;

}
