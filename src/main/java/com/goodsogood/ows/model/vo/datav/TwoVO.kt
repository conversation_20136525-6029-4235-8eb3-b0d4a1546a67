package com.goodsogood.ows.model.vo.datav

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.goodsogood.ows.model.vo.datav.base.CardFlop
import java.io.Serializable

/**
 * <AUTHOR>
 * @date 2023/3/29
 * @description class TwoVO 可视化大屏党业融合 -  两指数
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class TwoVO @JvmOverloads constructor(
    // 支部堡垒指数
    // 平均值翻牌器
    var cardFlopAvg0: MutableList<CardFlop> = mutableListOf(),
    // 平均值比率
    var avg0: Int = 0,
    // 最大值翻牌器
    var cardFlopMax0: MutableList<CardFlop> = mutableListOf(),
    // 最小值翻牌器
    var cardFlopMin0: MutableList<CardFlop> = mutableListOf(),
    // 支部先锋指数平均值
    // 平均值翻牌器
    var cardFlopAvg1: MutableList<CardFlop> = mutableListOf(),
    // 平均值比率
    var avg1: Int = 0,
    // 最大值翻牌器
    var cardFlopMax1: MutableList<CardFlop> = mutableListOf(),
    // 最小值翻牌器
    var cardFlopMin1: MutableList<CardFlop> = mutableListOf(),
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1654498225978777047L
    }

}
