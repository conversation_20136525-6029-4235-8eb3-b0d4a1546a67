package com.goodsogood.ows.model.vo.dataCockpit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PpmdStsVo {

    @JsonProperty(value = "partyMemberNum")
    @ApiModelProperty("已交/应交人数")
    private Integer partyMemberNum;

    @JsonProperty(value = "partyMemberAmount")
    @ApiModelProperty("已交/应交金额")
    private Double partyMemberAmount;

    public Integer getPartyMemberNum() {
        return partyMemberNum;
    }

    public void setPartyMemberNum(Integer partyMemberNum) {
        this.partyMemberNum = partyMemberNum;
    }

    public Double getPartyMemberAmount() {
        return partyMemberAmount;
    }

    public void setPartyMemberAmount(Double partyMemberAmount) {
        this.partyMemberAmount = partyMemberAmount;
    }
}
