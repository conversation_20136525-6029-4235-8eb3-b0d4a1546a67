package com.goodsogood.ows.model.vo

class MessageVO {

    // 图片地址
    var file: MutableList<String>? = mutableListOf()

    // 展示文字
    var text: String? = null

    // 来源
    var source: String? = null

    // 时间
    var time: String? = null

    var type: Int = 0

    var typeName: String = "未知"

    // 拓展字段
    // 保存打开链接的时需要的字段
    var extend: Map<String, Any>? = mutableMapOf()

    /* 栏目名称 */
    var columnName: String? = null
}