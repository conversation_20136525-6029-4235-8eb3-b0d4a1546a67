package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PeriodPartyCommitteeVo extends OverviewBaseVo {

    //未设置换届信息组织
    @JsonProperty(value = "no_set_period")
    private Integer noSetPeriod;

    //6个月内换届的支部
    @JsonProperty(value = "six_month_set_period")
    private Integer sixMonthSetPeriod;


    //本月应换届的支部
    @JsonProperty(value = "current_month_set_period")
    private Integer currentMonthSetPeriod;

    //已超期的支部
    @JsonProperty(value = "overdue_set_period")
    private Integer overdueSetPeriod;

    /**
     * 统计类型 1.本月 2.6月内 3.超期未换届
     */
    @JsonProperty(value = "sta_type")
    public Integer staType;


    /**
     * 党委数量
     */
    @JsonProperty(value = "committee")
    public Integer committee;

    /**
     * 党总支
     */
    @JsonProperty(value = "total_branch")
    public Integer totalBranch;


    /**
     * 党支部
     */
    @JsonProperty(value = "branch")
    public Integer branch;

}
