package com.goodsogood.ows.model.vo.operation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName : VisitNumVo
 * <AUTHOR> tc
 * @Date: 2022/4/13 16:40
 * @Description : 时间段访问量统计
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VisitNumVo {

    @JsonProperty(value = "region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;

    /**
     * 格式  YYYY-MM-DD
     */
    @JsonProperty(value = "stats_date")
    @ApiModelProperty("统计日期")
    private String statsDate;

    /**
     * 格式 HH-mm  00:00-01:00
     */
    @JsonProperty(value = "range")
    @ApiModelProperty("时间范围")
    private String range;

    /**
     * 统计访问人次
     */
    @JsonProperty(value = "visit_num")
    @ApiModelProperty("访问次数")
    private Integer visitNum;
}
