package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatisticalPartyFeeForm extends SasOrgPayParam {

    @JsonProperty(value = "party_fee_id")
    private Long partyFeeId;


    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;


    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;


    @ApiModelProperty("组织类型")
    @JsonProperty(value = "org_type_id")
    private Integer orgTypeId;


    @ApiModelProperty("你级组织Id")
    @JsonProperty(value = "parent_org_id")
    private Long parentOrgId;


    @ApiModelProperty("组织父级路径")
    @JsonProperty(value = "org_level")
    private String orgLevel;


    @ApiModelProperty("是否离退休党组织 1-是 2-否")
    @JsonProperty(value = "is_retire")
    private Integer isRetire;


    @ApiModelProperty("应交人数（默认为0）")
    @JsonProperty(value = "payable_person_num")
    private Integer payablePersonNum;


    @ApiModelProperty("未交纳人数")
    @JsonProperty(value = "unpaid_person_num")
    private Integer unpaidPersonNum;

    @ApiModelProperty("考核未交纳人数")
    @JsonProperty(value = "eval_unpaid_person_num")
    private Integer evalUnpaidPersonNum;

    @ApiModelProperty("支部成立时间")
    @JsonProperty(value = "org_create_time")
    private Date orgCreateTime;


    @JsonProperty(value = "statistical_year")
    private Integer statisticalYear;


    @ApiModelProperty("统计的月（1-12）")
    @JsonProperty(value = "statistical_month")
    private Integer statisticalMonth;

    @ApiModelProperty("统计时间(yyyy-MM)")
    @JsonProperty(value = "statistical_date")
    private String statisticalDate;


    @ApiModelProperty("逻辑状态:(1-有效, 0-无效)")
    private Integer status;


    @JsonProperty(value = "create_time")
    private Date createTime;


    @JsonProperty(value = "update_time")
    private Date updateTime;
}
