package com.goodsogood.ows.model.vo.meeting;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>Description: 发起活动查询活动任务条件</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 10:27
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Builder
public class MeetingTypeTaskListForm {
    public static final Short TAG_MEETING = 1;
    public static final Short TAG_TASK = 2;
    private Short tag;//请求标记:1、发起活动时查询（查询有效期内的活动任务）；2、任务完成情况页面查询（全部查询）；3、创建任务时校验是否有未结束的任务
    private Long planId;
    private String planName;
    private Long pOrgId;//任务来源 组织
    private Long orgId;//当前组织
    private List<Long> orgIds;//当前用户所属所有组织
    private List<Long> typeIds;
    private Long categoryId;
    private Short status;
    private Short isH5;//是否是移动端查询。0：不是；1：是
    private String type;
    private Date sStartTime;
    private Date eStartTime;
    private Date sEndTime;
    private Date eEndTime;

}
