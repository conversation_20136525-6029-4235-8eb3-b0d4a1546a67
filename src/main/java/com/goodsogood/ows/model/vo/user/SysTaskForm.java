package com.goodsogood.ows.model.vo.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023-03-08 16:11:36
 * @Description TaskForm
 */
@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SysTaskForm {
    /**
     * 党建任务
     */
    private Integer partyTotal = 0;
    /**
     * 创新任务
     */
    private Integer innovationTotal = 0;
    /**
     * 业务任务
     */
    private Integer businessTotal = 0;
    /**
     * 云区任务
     */
    private Integer ecpTotal = 0;
    /**
     * 任务总数
     */
    private Integer total = 0;
    /**
     * 已完成
     */
    public Integer doneTotal = 0;
    /**
     * 正在进行
     */
    public Integer doingTotal = 0;
    /**
     * 党员参与人次
     */
    public Integer partyAttendTotal = 0;
    /**
     * 非党员参与人次
     */
    public Integer otherAttendTotal = 0;
}
