package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.goodsogood.ows.helper.Json.DoubleKeepTwoSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 党费交纳统计结果类
 *
 * @auther Administrator tc
 * @date 2018/10/19
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StatsResultVo {

    @JsonProperty(value = "org_id")
    @ApiModelProperty("组织名称")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    /**
     * 是否考核单位  0 否  1是
     */
    @JsonProperty(value = "examine")
    @ApiModelProperty("是否考核单位")
    private Integer examine;

    /**
     * 统计月份  例如:2018年7月-2018年8月  或者 2018年7月
     */
    @JsonProperty(value = "stats_date")
    @ApiModelProperty("统计月份")
    private String statsDate;

    @JsonProperty(value = "num_total")
    @ApiModelProperty("党员人数")
    private Integer numTotal;

    @JsonProperty(value = "num_should")
    @ApiModelProperty("应交人数")
    private Integer numShould;

    /**
     * 单位 : 元
     */
    @JsonSerialize(using = DoubleKeepTwoSerializer.class)
    @JsonProperty(value = "pay_should")
    @ApiModelProperty("应交金额")
    private Double payShould;

    @JsonProperty(value = "num_already")
    @ApiModelProperty("已交人数")
    private Integer numAlready;

    /**
     * 单位 : 元
     */
    @JsonSerialize(using = DoubleKeepTwoSerializer.class)
    @JsonProperty(value = "pay_already")
    @ApiModelProperty("已交金额")
    private Double payAlready;

    @JsonProperty(value = "num_owing")
    @ApiModelProperty("欠交人数")
    private Integer numOwing;

    /**
     * 单位 : 元
     */
    @JsonSerialize(using = DoubleKeepTwoSerializer.class)
    @JsonProperty(value = "pay_owing")
    @ApiModelProperty("欠交金额")
    private Double payOwing;

    /**
     *  查询月数
     */
    @JsonProperty(value = "month_count")
    @ApiModelProperty("查询月数")
    private Integer monthCount;

    /**
     * 组织类型  1 党委  2 支部
     */
    @JsonProperty(value = "org_type")
    @ApiModelProperty("组织类型")
    private Integer orgType;

}
