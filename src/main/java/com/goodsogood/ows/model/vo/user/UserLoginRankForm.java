package com.goodsogood.ows.model.vo.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户登录排行
 * <AUTHOR>
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserLoginRankForm implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty(value = "orgId")
    private Long orgId;

    @JsonProperty(value = "date")
    private Date date;

    @JsonProperty(value = "month")
    private Integer month;

    @JsonProperty(value = "num")
    private Integer num = 0;
}
