package com.goodsogood.ows.model.vo.tbc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class) //自动驼峰命名
@JsonInclude(JsonInclude.Include.NON_NULL)  //json 转实例忽略为空
@JsonIgnoreProperties(ignoreUnknown = true) //转json 忽略为空
public class PartyCaucusForm {
    @ApiModelProperty(value = "党员人数")
    private TbcPrepareNumberForm caucus;

    @ApiModelProperty(value = "民族分布")
    private List<TbcEthnicDistributionOfPartyMembers> nation;

    @ApiModelProperty(value = "党建视频")
    private TbcVideoAndImgViewForm video;

    @ApiModelProperty(value = "领导成员")
    private List<TbcDetailsOfLeadingMembersForm> leader;

    @ApiModelProperty(value = "性别比例")
    private TbcGenderNumberForm gender;

    @ApiModelProperty(value = "年龄分布")
    private TbcAgeGradesForm age;

    @ApiModelProperty(value = "学历情况")
    private TbcDetailPagesForm backdrop;

    /**
     * 党员人数
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TbcPrepareNumberForm {
        @ApiModelProperty(value = "正式党员人数")
        private Integer officialNumber;
        @ApiModelProperty(value = "预备党员人数")
        private Integer prepareNumber;
        @ApiModelProperty(value = "预备党员人数详情")
        private List<TbcDetailsOfProbationaryPartyMembershipForm> structure;
    }

    /**
     * 党员人数/党员详情
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TbcDetailsOfProbationaryPartyMembershipForm {
        @ApiModelProperty("党员ID")
        private Long memberId;
        @ApiModelProperty(value = "预备党员姓名")
        private String memberName;
        @ApiModelProperty(value = "预备党员所在支部")
        private String memberBranch;
    }

    /**
     * 民族分布
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TbcEthnicDistributionOfPartyMembers {

        @ApiModelProperty(value = "optionId")
        private Integer nationId;

        @ApiModelProperty(value = "民族名称")
        private String nationName;

        @ApiModelProperty(value = "民族人数")
        private Long nationNumber;
    }

    /**
     * 民族分布/民族详情
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TbcDetailsOfEthnicPersonsForm {
        @ApiModelProperty(value = "党员ID")
        private Integer memberId;
        @ApiModelProperty(value = "党员姓名")
        private String memberName;
        @ApiModelProperty(value = "党员民族")
        private String memberNation;
    }

    /**
     * 图片视频展示详情
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TbcVideoAndImgViewForm {
        @ApiModelProperty(value = "文件id")
        private Long fileId;

        @ApiModelProperty(value = "文件url地址")
        private String fileUrl;
    }

    /**
     * 领导成员
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TbcDetailsOfLeadingMembersForm {
        @ApiModelProperty(value = "领导ID")
        private Integer userId;
        @ApiModelProperty(value = "领导姓名")
        private String userName;
        @ApiModelProperty(value = "领导职务")
        private String positionName;
        @ApiModelProperty(value = "领导所在支部")
        private String fromOrgName;
        @ApiModelProperty(value = "领导联系支部")
        private String contactOrgName;
    }

    /**
     * 性别比例
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TbcGenderNumberForm {
        @ApiModelProperty(value = "男人数")
        private Integer manNum;
        @ApiModelProperty(value = "女人数")
        private Integer womanNum;
    }

    /**
     * 年龄分布
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TbcAgeGradesForm {
        @ApiModelProperty(value = "30岁以下人数")
        private Long ageNumber1;
        @ApiModelProperty(value = "30岁~45岁人数")
        private Long ageNumber2;
        @ApiModelProperty(value = "45岁~55岁人数")
        private Long ageNumber3;
        @ApiModelProperty(value = "55岁以上人数")
        private Long ageNumber4;
    }

    /**
     * 学历情况
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TbcDetailPagesForm {
        @ApiModelProperty(value = "研究生人数")
        private Long peopleNumber1;
        private List<TbcEducationBackgroundForm> detailPage1;
        @ApiModelProperty(value = "本科人数")
        private Long peopleNumber2;
        private List<TbcEducationBackgroundForm> detailPage2;
        @ApiModelProperty(value = "专科人数")
        private Long peopleNumber3;
        private List<TbcEducationBackgroundForm> detailPage3;
        @ApiModelProperty(value = "中专人数")
        private Long peopleNumber4;
        private List<TbcEducationBackgroundForm> detailPage4;
        @ApiModelProperty(value = "高中及以下人数")
        private Long peopleNumber5;
        private List<TbcEducationBackgroundForm> detailPage5;
    }

    /**
     * 学历详情
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TbcEducationBackgroundForm {
        @ApiModelProperty(value = "党员ID")
        private Integer memberSeq;
        @ApiModelProperty(value = "党员姓名")
        private Integer memberName;
        @ApiModelProperty(value = "所在支部")
        private Integer memberBranch;
        @ApiModelProperty(value = "学历")
        private Integer educationBackground;
    }
}
