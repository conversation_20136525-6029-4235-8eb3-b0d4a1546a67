package com.goodsogood.ows.model.vo.rank;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Create by FuXiao on 2020/12/9
 */
@Data
@ApiModel
public class CompensateVo {
    @JsonProperty(value = "id")
    @ApiModelProperty("组织id或用户id")
    private Long id;

    @JsonProperty(value = "score_rule_id")
    @ApiModelProperty("规则id")
    private Long scoreRuleId;
}
