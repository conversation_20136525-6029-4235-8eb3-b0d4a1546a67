package com.goodsogood.ows.model.vo.operation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName : VisitRateVo
 * <AUTHOR> tc
 * @Date: 2022/4/13 16:04
 * @Description : 访问率
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VisitRateVo {

    @JsonProperty(value = "region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;

    /**
     * 格式  YYYY-MM-DD
     */
    @JsonProperty(value = "stats_date")
    @ApiModelProperty("统计日期")
    private String statsDate;

    @JsonProperty(value = "parent_org_id")
    @ApiModelProperty("上一级组织编号")
    private Long parentOrgId;

    @JsonProperty(value = "org_id")
    @ApiModelProperty("组织编号")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    @JsonProperty(value = "short_name")
    @ApiModelProperty("组织简称")
    private String shortName;

    @JsonProperty(value = "people_num")
    @ApiModelProperty("组织人数")
    private Integer peopleNum = 0;

    @JsonProperty(value = "visit_num")
    @ApiModelProperty("组织访问人数")
    private Integer visitNum = 0;

    /**
     * 组织下今日首次登录的人员中最晚的那个登录时间
     */
    @JsonProperty(value = "visit_time")
    @ApiModelProperty("登录时间")
    private LocalDateTime visitTime;

    /**
     * 四舍五入保留两位小数-组织内所有人员累计访问天数之和/组织内人员总数
     */
    @JsonProperty(value = "visit_days")
    @ApiModelProperty("人均访问天数")
    public Double visitDays = 0.0;
}
