package com.goodsogood.ows.model.vo.tbcFusion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName : TbcScoreUserRankVo
 * <AUTHOR> tc
 * @Date: 2022/6/20 18:48
 * @Description : 用户积分排行信息VO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TbcScoreUserRankVo {

    @ApiModelProperty("用户编号")
    @JsonProperty(value = "user_id")
    private Long userId;

    @ApiModelProperty("用户名称")
    @JsonProperty(value = "user_name")
    private String userName;

    @ApiModelProperty("组织编号")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty("积分")
    @JsonProperty(value = "score")
    private Long score;

    @ApiModelProperty("排名")
    @JsonProperty(value = "rank")
    private Integer rank;

}
