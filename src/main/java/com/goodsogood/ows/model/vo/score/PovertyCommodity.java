package com.goodsogood.ows.model.vo.score;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PovertyCommodity {

    @ApiModelProperty("t_score_poverty_commodity.poverty_commodity_id 主键Id")
    private Long povertyCommodityId;

    @ApiModelProperty("商品编号")
    private String commodityId;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品标价  单位:分")
    private Integer price;

    @ApiModelProperty("实际售价  单位:分")
    private Integer actualPrice;

    @ApiModelProperty("运费  单位:分")
    private Integer carriage;

    @ApiModelProperty("该商品的销售数量")
    private Integer count;

    @ApiModelProperty("商品类型 1 普通商品，2 捐款商品")
    private Integer type;

    @ApiModelProperty("冗余字段新增mongo时需要去除掉")
    private String orderId;
}