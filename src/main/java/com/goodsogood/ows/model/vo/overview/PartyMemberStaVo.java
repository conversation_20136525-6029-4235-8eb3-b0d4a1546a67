package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class PartyMemberStaVo extends OverviewBaseVo {


    @JsonProperty(value = "formal_party_member")
    private Integer formalPartyMember = 0;

    @JsonProperty(value = "pre_party_member")
    private Integer prePartyMember = 0;


    @JsonProperty(value = "other_party_member")
    private Integer otherPartyMember = 0;


    @JsonProperty(value = "female_member")
    public Integer femaleMember = 0;


    @JsonProperty(value = "male_member")
    public Integer maleMember = 0;


    @JsonProperty(value = "other_gender_member")
    public Integer otherGenderMember = 0;


    @JsonProperty(value = "city_wide_staff")
    public Integer cityWideStaff = 0;

    @JsonProperty(value = "normal_staff")
    private Integer normalStaff = 0;


    @JsonProperty(value = "total")
    public Integer total = 0;
}
