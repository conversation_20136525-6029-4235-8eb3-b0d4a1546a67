package com.goodsogood.ows.model.vo.datav.base

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.goodsogood.ows.helper.Json.DoubleKeepOneSerializer
import java.io.Serializable

/**
 * datav 饼图数据
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class Pie @JvmOverloads constructor(
    var type: String? = "",
    var value: Int = 0,
    var x: String? = "",
    var y: Int = 0,
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = -1136884419394864827L
    }
}

/**
 * datav 多维度饼图 / 胶囊图
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class MultidimensionalPie(
    var x: String = "",
    @JsonSerialize(using = DoubleKeepOneSerializer::class)
    var y: Double = 0.0,
    var s: String?
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1965714149568203216L
    }
}