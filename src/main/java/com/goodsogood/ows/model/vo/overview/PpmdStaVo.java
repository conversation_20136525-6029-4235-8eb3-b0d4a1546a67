package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

public class PpmdStaVo {


    @JsonProperty(value = "pay_num")
    public Integer payNum = 0;


    @JsonProperty(value = "total_num")
    public Integer totalNum = 0;

    @JsonProperty(value = "no_pay_num")
    private Integer noPayNum = 0;


    @JsonProperty(value = "total_amount")
    public Double totalAmount;


    @JsonProperty(value = "legend")
    private String[] legend = {"本月", "上月"};

    @JsonProperty(value = "list_series")
    private List<Series> listSeries;

    public Integer getPayNum() {
        return payNum;
    }

    public void setPayNum(Integer payNum) {
        this.payNum = payNum;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public Integer getNoPayNum() {
        return noPayNum;
    }

    public void setNoPayNum(Integer noPayNum) {
        this.noPayNum = noPayNum;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String[] getLegend() {
        return legend;
    }

    public void setLegend(String[] legend) {
        this.legend = legend;
    }

    public List<Series> getListSeries() {
        return listSeries;
    }

    public void setListSeries(List<Series> listSeries) {
        this.listSeries = listSeries;
    }

    public static class Series {
        @JsonProperty(value = "name")
        private String name;

        @JsonProperty(value = "data")
        private List<Double> data;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<Double> getData() {
            return data;
        }

        public void setData(List<Double> data) {
            this.data = data;
        }
    }

}
