package com.goodsogood.ows.model.vo.tbc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 用户排名Vo
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)  //json 转实例忽略为空
@JsonIgnoreProperties(ignoreUnknown = true) //转json 忽略为空
public class UserRankVo {

    //排名
    @JsonProperty(value = "row")
    private Long row;

    //用户姓名
    @JsonProperty(value = "user_name")
    private String userName;


    //用户id
    @JsonProperty(value = "user_id")
    private Long userId;

    //用户id 多个逗号分隔
    @JsonProperty(value = "user_ids")
    private String userIds;

    //统计时间
    @JsonProperty(value = "sta_month")
    private String staMonth;


    //先峰指数
    @JsonProperty(value = "xianfeng_index")
    private String xianFengIndex="0";

    //堡垒指数
    @JsonProperty(value = "fortress_index")
    private String fortressIndex="0";


    //组织id
    @JsonProperty(value = "org_id")
    private Long orgId;


    //组织id
    @JsonProperty(value = "org_ids")
    private String orgIds;
}