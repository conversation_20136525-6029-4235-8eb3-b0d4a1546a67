package com.goodsogood.ows.model.vo.meeting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 组织生活情况
 * @date 2019/11/21
 */
@Data
public class MeetingForm {

    @ApiModelProperty("组织ID")
    private Long orgId;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("会议ID")
    private Long meetingId;

    @ApiModelProperty("会议名称")
    private String name;

    @ApiModelProperty("会议类型ID")
    private Long typeId;

    @ApiModelProperty("会议类型名称")
    private String typeName;

    @ApiModelProperty("会议召开时间")
    private Date startTime;
}
