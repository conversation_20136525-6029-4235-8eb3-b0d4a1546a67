package com.goodsogood.ows.model.vo.dataCockpit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.vo.overview.OverviewBaseVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataCockpitOverviewActivityVo{

    @JsonProperty(value = "type")
    public Integer type;

    @JsonProperty(value = "name")
    public String name;

    @JsonProperty(value = "value")
    public Integer value;

    @JsonProperty(value = "total")
    public Integer total;
}
