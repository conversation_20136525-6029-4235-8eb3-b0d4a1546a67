package com.goodsogood.ows.model.vo.experience;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class UserCalResult {

    @JsonProperty(value = "rule_id")
    private Long ruleId;

    @ApiModelProperty(value = "rule_name")
    private String ruleName;


    @ApiModelProperty(value = "cal_status")
    private Integer calStatus;

    @ApiModelProperty(value = "star")
    private Integer star;

    @ApiModelProperty(value = "top_rule_id")
    private Integer topRuleId;

    @ApiModelProperty(value = "top_rule_name")
    private String topRuleName;
}
