package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Create by FuXiao on 2018/12/21
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActiveOrgVo implements Comparable<ActiveOrgVo>{

    @JsonProperty(value = "org_id")
    @ApiModelProperty("组织ID")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    @JsonProperty(value = "finish_time")
    @ApiModelProperty("交齐时间")
    private Date finishTime;

    @Override
    public int compareTo(ActiveOrgVo o) {
        return this.finishTime.compareTo(o.finishTime);
    }

}
