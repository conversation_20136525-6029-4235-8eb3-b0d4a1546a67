package com.goodsogood.ows.model.vo.experience;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.db.experience.UserPartyEvalEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * pc端：历史报告+柱状图
 * @Author: mengting
 * @Date: 2022/4/25 14:07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HistoryReportVO {
    private List<UserPartyEvalEntity> report;
    private List<UserPartyEvalEntity.TypeMonthStarForm> pillarChart;
}
