package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;
import java.util.Map;

import javax.persistence.Id;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * t_statistical_org_life 实体类
 *
 * 党支部组织生活
 *
 * <AUTHOR>
 * @create 2019-04-22 15:57
*/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatisticalOrgLifeForm {

	@Id
	@JsonProperty(value = "org_life_id")
	private Long orgLifeId;


	@ApiModelProperty("组织id")
	@JsonProperty(value = "org_id")
	private Long orgId;


	@ApiModelProperty("组织名称")
	@JsonProperty(value = "org_name")
	private String orgName;


	@ApiModelProperty("组织类型id")
	@JsonProperty(value = "org_type_id")
	private Integer orgTypeId;


	@ApiModelProperty("你级组织Id")
	@JsonProperty(value = "parent_org_id")
	private Long parentOrgId;


	@ApiModelProperty("组织父级路径")
	@JsonProperty(value = "org_level")
	private String orgLevel;


	@ApiModelProperty("是否离退休党组织 1-是 2-否")
	@JsonProperty(value = "is_retire")
	private byte isRetire;


	@ApiModelProperty("活动类型id")
	@JsonProperty(value = "activity_type_id")
	private Integer activityTypeId;


	@ApiModelProperty("活动类型名称")
	@JsonProperty(value = "activity_type_name")
	private String activityTypeName;


	@ApiModelProperty("参与次数")
	@JsonProperty(value = "participate_num")
	private Integer participateNum;


	@JsonProperty(value = "statistical_year")
	private String statisticalYear;


	@ApiModelProperty("统计的月（1-12）")
	@JsonProperty(value = "statistical_month")
	private Integer statisticalMonth;

	@ApiModelProperty("统计时间(yyyy-MM)")
	@JsonProperty(value = "statistical_date")
	private String statisticalDate;


	@ApiModelProperty("逻辑状态:(1-有效, 0-无效)")
	private byte status;


	@JsonProperty(value = "create_time")
	private Date createTime;


	@JsonProperty(value = "update_time")
	private Date updateTime;

	@ApiModelProperty("统计总数")
	@JsonProperty(value = "total_data")
	private Map<Integer,Integer> totalData;

}

