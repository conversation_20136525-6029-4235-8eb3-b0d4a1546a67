package com.goodsogood.ows.model.vo.tbc;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class TbcScoreForm {

    /**
     * 积分数量
     */
    private Integer score;

    /**
     * 积分类型
     */
    private Integer scoreType;

    /**
     * 积分大类型
     */
    private Integer parentScoreType;


    /**
     * 标题
     */
    private String title;


    /**
     * 生成时间
     */
    @JsonProperty("create_time")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date createTime;

    /**
     * 提示信息
     */
    private String tips;


    /**
     * 内定
     */
    private String content;
}
