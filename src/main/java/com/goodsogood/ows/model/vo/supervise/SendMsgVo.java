package com.goodsogood.ows.model.vo.supervise;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Collection;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SendMsgVo {

    /**
     * 是否为变量消息
     */
    @JsonProperty("is_variable_msg")
    private boolean isVariableMsg;
    /**
     * 模板id
     */
    @JsonProperty("template_id")
    private Long templateId;


    /**
     * 接受那些人
     */
    @JsonProperty("send_user_ids")
    private Collection<Long> sendUserIds;


    /**
     * 接受那些人
     */
    @JsonProperty("user_infos")
    private Collection<UserInfo> userInfos;

    /**
     * 1 发送组织 2。用户
     *
     *
     */
    @JsonProperty("send_object")
    private Integer sendObject;

    /**
     * 组织id
     */
    @JsonProperty("org_id")
    private Long orgId;

    /**
     * 组织名称
     * 如果是发送组织 orgName 这个参数必有
     */
    @JsonProperty("org_name")
    private String orgName;

    /**
     * 如果是普通消息 些参数有值
     */
    @JsonProperty("content")
    private String content;


    /**
     * 对应的那个选项
     */
    @JsonProperty("option_key")
    private String optionKey;

    @Data
    public static class UserInfo {

        private Long userId;

        private String  userName;

        private Long orgId;
    }


}
