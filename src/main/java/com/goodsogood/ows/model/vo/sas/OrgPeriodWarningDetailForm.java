package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Create by FuXiao on 2019/9/26
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrgPeriodWarningDetailForm {
    @ApiModelProperty(value = "组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty(value = "届次到期时间")
    @JsonProperty(value = "expire_date")
    private String expireDate;

    @ApiModelProperty(value = "党员数量")
    @JsonProperty(value = "abnormal_size")
    private Integer abnormalSize;
}
