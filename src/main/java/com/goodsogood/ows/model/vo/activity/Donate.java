package com.goodsogood.ows.model.vo.activity;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.mongodb.ScoreSuperInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class Donate extends ScoreSuperInfo {

    @ApiModelProperty("t_activity 主键id")
    private Long activityId;

    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("t_donate_user 主键Id")
    private Long donateUserId;

    @ApiModelProperty("捐赠积分")
    private Long donateNum;

    @ApiModelProperty("捐赠时间")
    private Date donateTime;

    @ApiModelProperty("区县id")
    private Long regionId;
}