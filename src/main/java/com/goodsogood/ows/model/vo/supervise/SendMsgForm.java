package com.goodsogood.ows.model.vo.supervise;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 发送监督预警消息提醒
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SendMsgForm {

    @JsonProperty("user_id")
    private Long userId;

    //组织名称
    @JsonProperty("org_name")
    private String orgName;

    //用户名称
    @JsonProperty("user_name")
    private String userName;


    public void setOrgName(String orgName) {
        this.orgName = "【" + orgName + "】";
    }

    public void setUserName(String userName) {
        this.userName = "【" + userName + "】";
    }


}
