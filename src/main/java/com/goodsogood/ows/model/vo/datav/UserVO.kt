package com.goodsogood.ows.model.vo.datav

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.goodsogood.ows.model.vo.datav.base.CardFlop
import com.goodsogood.ows.model.vo.datav.base.Line

/**
 * <AUTHOR>
 * @date 2023/3/10
 * @description 可视化大屏-党建概况-党员概述
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UserVO @JvmOverloads constructor(
    // 占比（翻牌器和仪表盘）
    var rate: DataVO? = null,
    // 性别（三个翻牌器）
    var gender: Array<List<CardFlop>?>? = null,
    // 学历、党龄、年龄、序列（标题、翻牌器、多维度饼图）
    var tab: Array<Array<DataVO?>?>? = null,
    // 党员发展（入党申请人、入党积极分子、发展对象、预备党员）
    var develop: Array<List<CardFlop>?>? = null,
    // 转正党员数折线图
    var becameNum: MutableList<Line>? = null,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is UserVO) return false

        if (rate != other.rate) return false
        if (!gender.contentEquals(other.gender)) return false
        if (!tab.contentEquals(other.tab)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = rate.hashCode()
        result = 31 * result + gender.contentHashCode()
        result = 31 * result + tab.contentHashCode()
        return result
    }
}