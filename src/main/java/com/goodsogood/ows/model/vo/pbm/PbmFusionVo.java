package com.goodsogood.ows.model.vo.pbm;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PbmFusionVo {
    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    private String updateTime;
    private List<String> month;//月份
    private List<String> yearList;//本年度
    private List<String> lastYearList;//上年度
    private List<String> maxList;//最高


    private List<String> minList;//最低

    public PbmFusionVo() {
    }

    public List<String> getMonth() {
        return month;
    }

    public void setMonth(List<String> month) {
        this.month = month;
    }

    public List<String> getYearList() {
        return yearList;
    }

    public void setYearList(List<String> yearList) {
        this.yearList = yearList;
    }

    public List<String> getLastYearList() {
        return lastYearList;
    }

    public void setLastYearList(List<String> lastYearList) {
        this.lastYearList = lastYearList;
    }

    public List<String> getMaxList() {
        return maxList;
    }

    public void setMaxList(List<String> maxList) {
        this.maxList = maxList;
    }

    public List<String> getMinList() {
        return minList;
    }

    public void setMinList(List<String> minList) {
        this.minList = minList;
    }
}
