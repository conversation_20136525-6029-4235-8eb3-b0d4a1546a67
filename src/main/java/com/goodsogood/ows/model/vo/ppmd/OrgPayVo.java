package com.goodsogood.ows.model.vo.ppmd;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Create by FuXiao on 2018/12/21
 */
@Data
@ApiModel
public class OrgPayVo {
    @JsonProperty(value = "num")
    @ApiModelProperty("缴纳人数")
    private Long num;

    @JsonProperty(value = "pay_date")
    @ApiModelProperty("最后缴纳时间")
    private Date payDate;
}
