package com.goodsogood.ows.model.vo.dataCockpit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 数据驾驶舱 - 党建阵地
 *
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PartyBuildingBrandVO {

     @ApiModelProperty("党建阵地数量")
     public Integer partyBuildingBattlefieldNum = 0;

     @ApiModelProperty("党建阵地详情")
     public List<partyBuildingBattlefieldItem> partyBuildingBattlefields;

     @ApiModelProperty("党建品牌数量")
     public Integer PartyBuildingBrandNum = 0;

     @ApiModelProperty("党建品牌详情")
     public List<PartyBuildingBrandItem> partyBuildingBrands;

     @Data
     @ApiModel
     @JsonInclude(JsonInclude.Include.NON_NULL)
     @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
     public class partyBuildingBattlefieldItem {
          @ApiModelProperty("党建阵地图片地址")
          public String partyBuildingBattlefieldImgUrl;

          @ApiModelProperty("党建阵地名称")
          public String partyBuildingBattlefieldName;
     }

     @Data
     @ApiModel
     @JsonInclude(JsonInclude.Include.NON_NULL)
     @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
     public class PartyBuildingBrandItem {
          @ApiModelProperty("党建品牌图片地址")
          public String PartyBuildingBrandImgUrl;

          @ApiModelProperty("党建品牌名称")
          public String PartyBuildingBrandName;
     }

}
