package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 党务统计-组织党费情况
 * <AUTHOR>
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SasOrgPaySituationForm {

    @JsonProperty("org_id")
    @ApiModelProperty("组织ID")
    private Long orgId;

    @JsonProperty("date")
    @ApiModelProperty("查询月份(yyyy-MM)")
    private String date;

    @JsonProperty("pay_able_num")
    @ApiModelProperty("党费应交人数")
    private Integer payAbleNum;

    @JsonProperty("un_pay_num")
    @ApiModelProperty("党费未交人数")
    private Integer unPayNum;

    @JsonProperty("eval_un_pay_num")
    @ApiModelProperty("考核党费未交人数")
    private Integer EvalUnPayNum;
}
