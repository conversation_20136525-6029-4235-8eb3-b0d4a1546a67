package com.goodsogood.ows.model.vo.eval.v2

import java.io.Serial
import java.io.Serializable

/**
 * 烟草考核2.0的指标任务实体，通过mq发送和消费
 */
data class MetricTaskBean @JvmOverloads constructor(
    val taskId: String? = null, // 任务id
    val subTaskId: String? = null, // 子任务id

) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = -8949301967932883420L
    }

    // 任务状态枚举
    enum class TaskStatus(val value: Int) {
        // 任务状态：0. 未开始、1. 进行中、2. 已完成、3. 应错误已取消
        NotStarted(0), InProgress(1), Completed(2), Canceled(3)
    }
}
