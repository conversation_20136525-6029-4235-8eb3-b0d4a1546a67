package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QueryUserInfoForm {

    @ApiModelProperty("用户编号集合")
    @JsonProperty(value = "user_id_list")
    private String userIdList;

    @ApiModelProperty("查询时间")
    @JsonProperty(value = "query_date")
    private String date;
}
