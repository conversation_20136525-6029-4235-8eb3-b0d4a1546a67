package com.goodsogood.ows.model.vo.meeting.experience;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName : PrelectionVo
 * <AUTHOR> tc
 * @Date: 2022/3/17 17:51
 * @Description : 党员讲党课Vo类
 */
@Data
public class PrelectionVo {
    @ApiModelProperty("年份")
    private Integer year;
    @ApiModelProperty("用户编号")
    private Long userId;
    @ApiModelProperty("讲课次数")
    private Integer prelectionNum;
    @ApiModelProperty("排名")
    private Integer ranking;
    @ApiModelProperty("党员总人数参与排名的人数")
    private Integer totalNum;
}
