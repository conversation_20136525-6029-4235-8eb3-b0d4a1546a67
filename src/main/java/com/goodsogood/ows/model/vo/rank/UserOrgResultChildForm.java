package com.goodsogood.ows.model.vo.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.goodsogood.ows.model.vo.sas.UserOrgResultForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserOrgResultChildForm extends UserOrgResultForm {

    @ApiModelProperty("系统级标签，挂靠在人员+组织下")
    private String systemTag;

    @ApiModelProperty("公共标签，挂在-999下")
    private String publicTag;
}
