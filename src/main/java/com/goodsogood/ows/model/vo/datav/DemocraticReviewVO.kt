package com.goodsogood.ows.model.vo.datav

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.goodsogood.ows.model.vo.datav.base.*
import java.io.Serializable

/**
 * 可视化大屏-党务工作-民主评议党员
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class DemocraticReviewVO @JvmOverloads constructor(
    // 轮播饼图数据
    val pie: MutableList<Pie> = mutableListOf(),
    // 仪表盘 （工作完成进度）
    val dashboard: MutableList<Dashboard> = mutableListOf()
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = -7598179313140475358L
    }
}
