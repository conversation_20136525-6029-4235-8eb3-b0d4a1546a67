package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 动态下载列
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DynamicColVo {

    /**
     * 列名显示中文
     */
    private String name;

    /**
     * code 为英文名称
     */
    private String code;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 分组名称
     */
    protected String groupName;
    /**
     * 导出类型 1 是文本 2 是图片,3 是函数,10 是数字 默认是文本
     */
    @JsonIgnore
    private int type=1;

    /**
     * 导出时间设置,如果字段是Date类型则不需要设置 数据库如果是string 类型,这个需要设置这个数据库格式,用以转换时间格式输出
     */
    private String databaseFormat;

    //以下参注解 参与easyPoi链接地址:  http://doc.wupaas.com/docs/easypoi/easypoi-1c0u96flii98v
    private String format;
    private String[] replace;
    private String dict;
    @JsonIgnore
    private boolean hyperlink;
    private Integer fixedIndex;
    private String timezone;
    @JsonIgnore
    private boolean addressList;
    @JsonIgnore
    private Integer imageType;
    @JsonIgnore
    private double width =10;
    @JsonIgnore
    private double height =10;
    @JsonIgnore
    private boolean isWrap;
    @JsonIgnore
    private boolean needMerge;
    @JsonIgnore
    private boolean mergeVertical;
    private int[] mergeRely;
    private String suffix;
    @JsonIgnore
    private boolean isStatistics;
    private String numFormat;
    @JsonIgnore
    private Boolean isColumnHidden;
    private String enumExportField;
    private String desensitizationRule;

    private Boolean isMust=false;

}
