package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * 党费交纳详情参数类
 *
 * @auther Administrator tc
 * @date 2018/10/26
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PayDetailsForm {

    @JsonProperty(value = "org_id")
    @ApiModelProperty("党支部编号")
    @NotNull(message = "{NotNull.stats.orgId}")
    private Long orgId;

    /**
     * 交纳状态   1已交、2未交、3补交、4欠交
     * APP端支持多个状态查询，多个用逗号间隔，PC端不支持
     */
    @JsonProperty(value = "status")
    @ApiModelProperty("交纳状态")
    private String status;

    /**
     * 党费标准   1按收入比例 2少交 3免交 4 未设置 5固定金额
     */
    @JsonProperty(value = "type")
    @ApiModelProperty("党费标准")
    private Integer type;

    /**
     * 查询时间  格式:YYYY-MM
     */
    @JsonProperty(value = "query_date")
    @ApiModelProperty("查询时间")
    @NotBlank(message = "{NotBlank.stats.queryDate}")
    private String queryDate;

    /**
     * 交纳时间升序或降序  1 降序，2 升序
     */
    @JsonProperty(value = "date_order")
    @ApiModelProperty("交纳时间升序或降序")
    private Integer dateOrder;

    /**
     * 查询类型  1 支部党费交纳情况，2 支部交纳榜单
     */
    @JsonProperty(value = "query_type")
    @ApiModelProperty("查询类型")
    private Integer queryType;

    /**
     * 分页大小	默认10
     */
    @JsonProperty(value = "page_size")
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 分页页码	默认1
     */
    @JsonProperty(value = "page_no")
    @ApiModelProperty("分页页码")
    private Integer pageNo;

}
