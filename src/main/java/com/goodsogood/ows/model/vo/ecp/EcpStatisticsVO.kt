package com.goodsogood.ows.model.vo.ecp

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty


/**
 * 烟草党业融合 云上任务参与情况
 * 全市级别
 */
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class EcpStatisticsListVO {

    @ApiModelProperty("总人次")
    var peopleNum: Int? = null

    @ApiModelProperty("党员人数")
    var partyNum: Int? = null

    @ApiModelProperty("党员完成任务人次")
    var partyFinishNum: Int? = null

    @ApiModelProperty("非党员人数")
    var notPartyNum: Int? = null

    @ApiModelProperty("非党员人次")
    var notPartyFinish: Int? = null
}

/**
 * 烟草党业融合 云上任务参与情况
 * 全市级别 列表
 */
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class EcpStatisticsCityVO {

    @ApiModelProperty("单位id")
    var ownerId: Long? = null

    @ApiModelProperty("单位名称")
    var ownerName: String? = null

    @ApiModelProperty("员工参与率")
    var staffRate: String? = null

    @ApiModelProperty("员工参与率")
    var staff: Int? = null

    @ApiModelProperty("党员参与率")
    var partyRate: String? = null

    @ApiModelProperty("党员参与率")
    var party: Int? = null

    @ApiModelProperty("发起任务数")
    var publishTaskNum: Int? = null

    @ApiModelProperty("参与总人次")
    var peopleNum: Int? = null

}

/**
 * 烟草党业融合 云上任务参与情况
 * 本单位级别
 */
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class EcpStatisticsUserVO {

    @ApiModelProperty("全体员工参与率")
    var staffRate: String? = null

    @ApiModelProperty("党员参与率")
    var partyRate: String? = null

    @ApiModelProperty("总人次")
    var peopleNum: Int? = null

    @ApiModelProperty("党员人次")
    var partyPeopleNum: Int? = null

    @ApiModelProperty("非党员人次")
    var notPartyPeopleNum: Int? = null
}

/**
 * 烟草党业融合 云上任务参与情况
 * 本单位级别 人员列表
 */
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class EcpStatisticsUserListVO {

    @ApiModelProperty("党员id")
    var userId: String? = null

    @ApiModelProperty("党员名称")
    var userName: String? = null

    @ApiModelProperty("参与次数")
    var partInNum: Int? = null

    @ApiModelProperty("是否是党员")
    @JsonProperty(value = "is_party")
    var isParty: String? = null
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class EcpOrgDetailForm {

    @ApiModelProperty("标签ID")
    var tagId: Long? = 0

    @ApiModelProperty("数量")
    var total: Int? = 0

    @ApiModelProperty("云上组织名称，逗号分隔")
    var separateUnitName: String? = null
}
