package com.goodsogood.ows.model.vo.supervise;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SuperviseUserForm {
    @JsonProperty(value = "user_id")
    private String userId;


    @JsonProperty(value = "user_name")
    private String userName;


    @JsonProperty(value = "user_phone")
    private String userPhone;

    @JsonProperty(value = "count")
    private Integer count;

    @JsonProperty(value = "org_name")
    private String orgName;

    @JsonProperty(value = "org_secretary")
    private String orgSecretary;

}
