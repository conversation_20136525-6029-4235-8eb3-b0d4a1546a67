package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 党务统计-根据组织ID查询组织党费交纳情况
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SasOrgPayForm {

    @JsonProperty("org_ids")
    @ApiModelProperty(value = "组织IDList")
    private List<Long> orgIds;

    @JsonProperty("date")
    @ApiModelProperty(value = "查询年份")
    private String date;

    @JsonProperty("is_one_month")
    @ApiModelProperty(value = "是否只查询一个月")
    private boolean isOneMonth = false;
}
