package com.goodsogood.ows.model.vo.ppmd;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

/**
 * Create by FuXiao on 2018/12/5
 */
public class OrgForm {
    @JsonProperty(value = "org_id")
    @ApiModelProperty("组织ID")
    private Long orgId;

    @JsonProperty(value = "parent_id")
    @ApiModelProperty("上级组织ID")
    private Long parentId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}
