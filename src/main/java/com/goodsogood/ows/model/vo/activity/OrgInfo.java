package com.goodsogood.ows.model.vo.activity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * @program: ows-sas
 * @description:组织信息 封装组织信息
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2019-11-19 08:31
 **/
@Data
public class OrgInfo {

    @JsonProperty(value = "org_id")
    private Long orgId;

    @JsonProperty(value = "org_name")
    private String orgName;

    @JsonProperty(value = "parent_id")
    private Long parentId;

    @JsonProperty(value = "org_type")
    private Long orgType;

    @JsonProperty(value = "org_type_child")
    private Long orgTypeChild;

    @JsonProperty(value = "org_level")
    private String orgLevel;

    @JsonProperty(value = "org_create_time")
    private Date orgCreateTime;

    @JsonProperty(value = "region_id")
    private Long regionId;

}
