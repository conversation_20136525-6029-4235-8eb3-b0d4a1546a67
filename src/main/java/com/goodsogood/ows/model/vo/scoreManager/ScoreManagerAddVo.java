package com.goodsogood.ows.model.vo.scoreManager;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/12/15
 */
@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScoreManagerAddVo extends ScoreManagerMqVo.TransitScoreManagerVo {

    @NotNull
    private Long regionId;

    @NotNull
    private Integer typeId;

    @NotNull
    private Long changeUser;

    @NotNull
    private String executeTime;

    /**
     *      * 需要处理的ids 根据ScoreManagerEnum.idType 判断
     *      * 可能是orgId,userId,partyGroupId
     */
    @NotNull
    private Long id;

}
