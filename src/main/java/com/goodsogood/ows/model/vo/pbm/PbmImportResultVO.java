package com.goodsogood.ows.model.vo.pbm;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PbmImportResultVO<T> {


    /**
     * 错误列表
     */
    private List<T> listFailedList;


    /**
     * 成功的条数
     */
    private Integer sucCount;

    /**
     * 失败的条数
     */
    private Integer failCount;


    /**
     * 上传错误文件token
     */
    private String token;


    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * -1 表示一直轮询 1 表示处理成功 2.表示失败
     */
    private Integer tips;
}
