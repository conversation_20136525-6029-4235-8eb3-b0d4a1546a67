package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设置信息参数
 *
 * <AUTHOR>
 * @date 2019/4/19 20:07
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StasticConfigListForm {

    @ApiModelProperty(value = "显示活动类型(存入的活动类型类型id,多个以逗号分隔）")
    @JsonProperty("stastic_config_list")
    @NotBlank(message = "{NotNull.sas.configinfo}")
    private String stasticConfigInfoFormList;
}
