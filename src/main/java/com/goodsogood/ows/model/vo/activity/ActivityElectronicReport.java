package com.goodsogood.ows.model.vo.activity;

/**
 * @program: ows-sas
 * @description: ${description}
 * @author: Mr<PERSON>
 * @create: 2019-11-21 15:35
 **/

import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.mongodb.ActivityInfo;
import lombok.Data;

/**
 * 统计结果
 */
@Data
public class ActivityElectronicReport extends ActivityInfo.ParticipantUsers {

    /**
     * 参与活动名称
     */
    @JsonProperty(value = "activity_name")
    private String activityName;


    /**
     * 参与活动类型名称
     */
    @JsonProperty(value = "activity_type_name")
    private String activityTypeName;

}
