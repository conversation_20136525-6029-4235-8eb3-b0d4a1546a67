package com.goodsogood.ows.model.vo.experience;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 党员体检详情筛选条件
 * @Author: mengting
 * @Date: 2022/4/21 22:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReportChooseForm {
    private List<Long> evalIds;
    private Integer evalId;
    private Long regionId;
    private String userName;
    private Integer type = 0  ;//体检维度，默认综合-0  1-理论武装 2-担当作为 3-义务履行 4-业务实绩 5-活跃程度
    private List<Integer> star;//体检结果，支持多选
    private List<String> tag;//体检标签，支持多选
    private Integer orderType = 1;//默认排序方式1：0-综合评级  1-降序  2-升序
    private Long orgId;
    private Integer dateMonth;
    private String dateMonthStr;
    private String phone;//手机号
    private Integer item = 0;//0-综合测评 1-专项测评
    private Integer pageSize = 10;//每页条数
    private Integer pageNum = 1;//当前页



}
