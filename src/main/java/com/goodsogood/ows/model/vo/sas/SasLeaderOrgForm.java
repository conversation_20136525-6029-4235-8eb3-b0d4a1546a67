package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SasLeaderOrgForm {

    @JsonProperty("user_id")
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @JsonProperty("user_name")
    @ApiModelProperty(value = "用户名称")
    private String userName;

    @JsonProperty("org_id")
    @ApiModelProperty(value = "组织ID")
    private Long orgId;

    @JsonProperty("region_id")
    @ApiModelProperty(value = "区域ID")
    private Long regionId;

    @JsonProperty("org_name")
    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @JsonProperty("parent_org_id")
    @ApiModelProperty(value = "上级组织ID")
    private Long parentOrgId;

    @JsonProperty("org_type")
    @ApiModelProperty(value = "组织类型")
    private Integer orgType;

    @JsonProperty("org_level")
    @ApiModelProperty(value = "组织父级路径")
    private String orgLevel;

    @JsonProperty("is_retire")
    @ApiModelProperty(value = "是否退休组织")
    private Integer isRetire;
}
