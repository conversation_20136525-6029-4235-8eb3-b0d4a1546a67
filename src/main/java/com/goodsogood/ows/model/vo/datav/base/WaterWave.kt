package com.goodsogood.ows.model.vo.datav.base

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.goodsogood.ows.helper.Json.DoubleKeepTwoSerializer
import java.io.Serializable

/**
 * 水球图
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class WaterWave(
    @JsonSerialize(using = DoubleKeepTwoSerializer::class)
    val percent: Double
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 8662265123266161401L
    }
}
