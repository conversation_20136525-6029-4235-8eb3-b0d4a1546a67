package com.goodsogood.ows.model.vo.tbcFusion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TbcUserIndexForm extends  TbcPartyIndexForm{


    @ApiModelProperty("用户Id")
    @JsonProperty(value = "user_id")
    private Long UserId;

    @ApiModelProperty("用户Id")
    @JsonProperty(value = "org_name")
    private String OrgName;

}
