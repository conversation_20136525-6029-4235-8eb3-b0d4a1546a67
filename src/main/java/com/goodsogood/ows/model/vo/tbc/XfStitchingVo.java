package com.goodsogood.ows.model.vo.tbc;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 融合概况先锋指数拆线图
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)  //json 转实例忽略为空
@JsonIgnoreProperties(ignoreUnknown = true) //转json 忽略为空
public class XfStitchingVo {

    @JsonIgnore
    String[] strArray={"我的","系统均值","卷烟营销线均值","本单位均值","本单位最高"};
    /**
     * 颜色指示标题
     */
    @JsonProperty(value = "sData")
    private List<String> sData = new ArrayList<>(Arrays.asList(strArray));

    /**
     * X轴横坐标标题
     */
    @JsonProperty(value = "xData")
    private List<String> xData;

    /**
     * 我的
     */
    @JsonProperty(value = "d1")
    private List<String> d1;

    /**
     * 系统均值
     */
    @JsonProperty(value = "d2")
    private List<String> d2;

    /**
     * 序列指标均值（如果用户不存在任何序列此项没有）
     */
    @JsonProperty(value = "d3")
    private List<String> d3;

    /**
     * 本单位均值
     */
    @JsonProperty(value = "d4")
    private List<String> d4;

    /**
     * 本单位最高
     */
    @JsonProperty(value = "d5")
    private List<String> d5;


}
