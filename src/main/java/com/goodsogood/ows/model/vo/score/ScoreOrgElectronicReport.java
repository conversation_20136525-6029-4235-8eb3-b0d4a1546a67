package com.goodsogood.ows.model.vo.score;

import com.goodsogood.ows.model.mongodb.report.UserScoreDonateInfo;
import com.goodsogood.ows.model.mongodb.report.UserScoreStudyInfo;
import lombok.Data;

/**
 * @program: ows-sas
 * @description
 * @author: Mr<PERSON>
 * @create: 2019-11-25 10:21
 **/
@Data
public class ScoreOrgElectronicReport {
    /**
     * 用户学习情况
     */
    private UserScoreStudyInfo userScoreStudyInfo;

    /**
     * 公益扶贫情况
     */
    private UserScoreDonateInfo userScoreDonateInfo;

    /**
     * 部门下面学习积分最多的用户
     */
    private String userName;

}
