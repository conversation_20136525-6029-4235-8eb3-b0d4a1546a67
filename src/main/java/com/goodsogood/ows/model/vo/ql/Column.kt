package com.goodsogood.ows.model.vo.ql

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

/**
 * <AUTHOR>
 * @date 2023/9/26
 * @description class Column
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class Column @JvmOverloads constructor(
    var key: String? = null,
    var name: String? = null,
    var width: Int? = null,
    var index: Int? = null,
)