package com.goodsogood.ows.model.vo.experience;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.arrow.flatbuf.Int;

import java.util.List;

/**
 * @Author: mengting
 * @Date: 2022/3/15 14:39
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserPartyReportVO {
    @ApiModelProperty(value = "eval_id")
    private Long evalId;

    @ApiModelProperty(value = "type")
    private Byte type;

    @ApiModelProperty(value = "avatar")
    private String avatar;

    @ApiModelProperty(value = "user_name")
    private String userName;

    @ApiModelProperty(value = "position")
    private String position;

    @ApiModelProperty(value = "total_star")
    private Integer  totalStar;

    @JsonFormat(pattern = "yyyy.MM.dd", timezone = "GMT+8")
    @JsonProperty("eval_date")
    @ApiModelProperty(value = "eval_date")
    private String evalDate;

    @JsonProperty("type_star")
    @ApiModelProperty(value = "type_star")
    private List<TypeStarVO> typeStar;//雷达图

    @ApiModelProperty(value = "tag_add")
    private String tagAdd;

    @ApiModelProperty(value = "rule")
    private List<RuleVO> rule;

    @ApiModelProperty(value = "plan")
    private List<UserPartyListVO> plan;

    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NoArgsConstructor
    public static class RuleVO {
        private String type;
        private Integer star;
        private String ruleName;//细项名称
        private Integer ruleStar;//细项的星星
        private List<RuleVO> rules;
        public RuleVO(String type, Integer star){
            this.type = type;
            this.star = star;
        }
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class TypeStarVO {
        private Integer ruleId;
        private String type;
        private Integer star;
        private Integer num;//细项数量
        private String tag;//详情标签使用
        private List<TypeStarVO> pieChart;
        public TypeStarVO(Integer star, Integer num){
            this.star = star;
            this.num = num;
        }

        public TypeStarVO(String type, Integer num){
            this.type = type;
            this.num = num;
        }

        public TypeStarVO(String type, List<TypeStarVO> pieChart) {
            this.type = type;
            this.pieChart = pieChart;
        }
    }


}
