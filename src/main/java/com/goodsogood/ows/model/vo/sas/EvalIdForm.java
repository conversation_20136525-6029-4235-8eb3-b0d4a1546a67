package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 考核系统 查询组织信息或领导班子信息
 *
 * <AUTHOR>
 * @create 2019-04-22 16:04
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EvalIdForm {
    private Long orgId;
    private String orgName;
    private Long userId;
    private String userName;
}
