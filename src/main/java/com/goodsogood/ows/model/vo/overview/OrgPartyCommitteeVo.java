package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@ApiModel
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrgPartyCommitteeVo extends OverviewBaseVo {

    @JsonProperty(value = "org_list")
    private List<KeyValVo> list;

    public List<KeyValVo> getList() {
        return list;
    }

    public void setList(List<KeyValVo> list) {
        this.list = list;
    }

}
