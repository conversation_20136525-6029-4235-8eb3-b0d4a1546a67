package com.goodsogood.ows.model.vo.constitution

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModelProperty
import lombok.Data

@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
class ConstitutionCountVO(
    @ApiModelProperty("单位名称")
    var unitName: String? = null,

    @ApiModelProperty("单位id")
    var unitId: Long? = null,

    @ApiModelProperty("组织名称")
    var orgName: String? = null,

    @ApiModelProperty("组织id")
    var orgId: Long? = null,

    @ApiModelProperty("学习次数")
    var times: Int? = null,

    @ApiModelProperty("序号")
    var seq:Int?=null
) {
    override fun toString(): String {
        return "ConstitutionCountVO(unitName=$unitName, unitId=$unitId, orgName=$orgName, orgId=$orgId, times=$times)"
    }
}