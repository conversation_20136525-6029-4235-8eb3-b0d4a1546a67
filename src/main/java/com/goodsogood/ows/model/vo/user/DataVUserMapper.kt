package com.goodsogood.ows.model.vo.user

import com.goodsogood.ows.mapper.MyMapper
import com.goodsogood.ows.model.db.user.UserEntity
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Repository
import java.util.LinkedHashMap
import javax.validation.constraints.NotNull

/**
 * 为datav大屏专门抽出来的mapper
 * <AUTHOR>
 * @date 2024/1/30
 * @description class DataVUserMapper
 */
@Repository
@Mapper
interface DataVUserMapper : MyMapper<UserEntity> {
    /**
     * 获取普通用户、正式党员、预备党员的数据
     */
    @Select(
        """
         <script>
            SELECT
            CASE
                u.political_type 
                WHEN 1 THEN '正式党员' 
                WHEN 5 THEN '预备党员' 
                ELSE '--' END AS name,
              count( DISTINCT u.user_id ) AS value
            FROM
              t_user u 
              LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id
              LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id
            WHERE
              u.`status` = 1 
              AND o.`status` = 1
              AND uoc.is_employee = 1
              AND u.political_type IN ( 1, 5, 17, 18 )
              <foreach item="item" collection="excludeOrgIds" open="" separator=" " close=" ">
                AND o.org_level NOT LIKE CONCAT('%-',#{item},'-%') AND o.organization_id != #{item}
               </foreach>
              AND ( o.organization_id = #{orgId} OR o.org_level LIKE CONCAT('%-',#{orgId},'-%') )
            GROUP BY
              u.political_type
            UNION ALL
            SELECT
             '非党员' AS name,
             count( DISTINCT u.user_id ) AS value
            FROM
              t_user u 
              LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id
              LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id
            WHERE
              u.`status` = 1 
              AND o.`status` = 1
              AND ( u.political_type not IN ( 1, 5, 17, 18 ) or u.political_type is null)
              AND ( o.organization_id = #{cropId} OR o.org_level LIKE CONCAT('%-',#{cropId},'-%') )
        </script>
    """
    )
    fun userPartyMember(
        @Param("orgId") orgId: Long,
        @Param("excludeOrgIds") excludeOrgIds: List<String>,
        @Param("cropId") cropId: Long,
    ): MutableList<Pair<String, Long>>


    /**
     * 干部成员年龄分布
     */
    @Select(
        """
        <script>
        SELECT A.alias as `name`, IF(b.total IS NULL,0,b.total) as `value` FROM (
          SELECT * FROM t_overview_option WHERE type=2 and project_name='overview' 
        ) as A
        LEFT JOIN (
        select `name`,count(DISTINCT userId) as total from (
        SELECT
        (CASE
            WHEN age BETWEEN 1 AND 30 THEN '30岁及以下' 
            WHEN age BETWEEN 31 and 45 THEN '31岁~45岁' 
            WHEN age BETWEEN 46 and 55 THEN '46岁~55岁' 
            WHEN age > 55 THEN '55岁以上'
            ELSE '未知' END) AS `name`,
            u.user_id as userId 
        FROM
          t_user u 
          LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id
          LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id
            left join t_user_org_period_member upm on upm.user_id = u.user_id
        WHERE
          u.`status` = 1 
          AND o.`status` = 1
          AND uoc.is_employee = 1
            AND upm.is_delete = 2
          AND u.political_type IN ( 1, 5, 17, 18 )
          <foreach item="item" collection="excludeOrgIds" open="" separator=" " close=" ">
            AND o.org_level NOT LIKE CONCAT('%-',#{item},'-%') AND o.organization_id != #{item}
           </foreach>
          AND ( o.organization_id = #{orgId} OR o.org_level LIKE CONCAT('%-',#{orgId},'-%') )
           ) tmp group by `name`
        )as B ON A.`name`=B.`name`
        ORDER BY A.order_num
        </script>
    """
    )
    fun userOrgPeriodMemberAgeDistributed(
        @Param("orgId") orgId: Long,
        @Param("excludeOrgIds") excludeOrgIds: List<String>,
    ): List<Pair<String, Long>>

    /**
     * 用户学历分布
     * @param year 入党年份，如果不传，查询所有
     */
    @Select(
        """
        <script>        
        SELECT A.alias as `name`,IF(b.total IS NULL,0,b.total) as `value` FROM (
          SELECT * FROM t_overview_option WHERE type=1 and project_name='overview'
        ) as A
        LEFT JOIN (
        select `name`,sum(total) as total from (
        SELECT
        u.education,
          CASE
            WHEN (u.education = 1005 OR u.education = 103909 OR u.education = 103906 OR u.education = 103907 
            OR u.education = 103908 OR u.education = 103909 OR u.education = 103910 ) THEN '高中生及以下' 
            WHEN (u.education = 103904 OR u.education = 103905 OR u.education = 1004 ) THEN '专科' 
            WHEN (u.education = 1003 OR u.education = 103903) THEN '本科' 
            WHEN (u.education = 103901 OR u.education = 103902) THEN '研究生及以上' 
            ELSE '未知' END AS `name`,
          count(distinct u.user_id) AS total
        FROM
          t_user u 
          LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id
          LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id
          <if test="leader">
            left join t_user_org_period_member upm on upm.user_id = u.user_id
          </if>
        WHERE
          u.`status` = 1 
          AND o.`status` = 1
          AND uoc.is_employee = 1
          <if test="leader">
            AND upm.is_delete = 2
          </if>
          AND u.political_type IN ( 1, 5, 17, 18 )
          <foreach item="item" collection="excludeOrgIds" open="" separator=" " close=" ">
          AND o.org_level NOT LIKE CONCAT('%-',#{item},'-%') AND o.organization_id != #{item}
          </foreach>
          AND ( o.organization_id = #{orgId} OR o.org_level LIKE CONCAT('%-',#{orgId},'-%') )
          <if test="year != null">
          AND year(u.joining_time) = #{year}
          </if>
        GROUP BY
          u.education) temp GROUP BY `name`
        )as B ON A.`name`=B.`name`
        ORDER BY A.order_num
        </script>
    """
    )
    fun userEducation(
        @Param("orgId") orgId: Long,
        @Param("excludeOrgIds") excludeOrgIds: List<String>,
        @Param("year") year: Int?,
        @Param("leader") leader: Boolean = false,
    ): List<Pair<String, Long>>

    /**
     * 用户所属序列分布
     * @param year 入党年份，如果不传，查询所有
     * @param leader 是否查询领导
     */
    @Select(
        """
        <script>
        SELECT
    CASE
        WHEN
            ( u.sequence = 1 ) THEN
                '卷烟营销' 
                WHEN ( u.sequence = 2 ) THEN
                '烟叶生产' 
                WHEN ( u.sequence = 3 ) THEN
                '专卖管理' 
                WHEN ( u.sequence = 4 ) THEN
                '综合管理' ELSE '未知' 
            END AS sequence_name,
            count( DISTINCT u.user_id ) AS total 
        FROM
            t_user u
            LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id
            LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id 
            <if test="leader">
            left join t_user_org_period_member upm on upm.user_id = u.user_id
            </if>
        WHERE
            u.`status` = 1 
            AND o.`status` = 1 
            AND uoc.is_employee = 1 
            AND u.political_type IN ( 1, 5, 17, 18 )
            <if test="leader">
            AND upm.is_delete = 2
            </if>
            <foreach item="item" collection="excludeOrgIds" open="" separator=" " close=" ">
            AND o.org_level NOT LIKE CONCAT('%-',#{item},'-%') AND o.organization_id != #{item}
            </foreach>
            AND ( o.organization_id = #{orgId} OR o.org_level LIKE CONCAT('%-',#{orgId},'-%') )
            <if test="year != null">
            AND year(u.joining_time) = #{year}
            </if>
    GROUP BY
        u.sequence
        </script>
    """
    )
    fun userSequence(
        @Param("orgId") orgId: Long,
        @Param("excludeOrgIds") excludeOrgIds: List<String>,
        @Param("year") year: Int?,
        @Param("leader") leader: Boolean = false,
    ): List<Pair<String, Long>>

    /**
     * 发展党员各阶段情况，返回 ”所属序列-阶段“ 和人数
     * u.sequence,-- 所属序列 1-卷烟私营 2-烟叶生产 3-专卖管理 4-综合管理
     * ud.stage, -- 发展阶段 1-入党申请人 2-积极分子 3-发展对象 4-预备党员 5-成功发展（status = 1）
     * @param unitId 单位id
     * @param excludeOrgIds 排除组织
     * @param year 年份
     */
    @Select(
        """
        <script>
    SELECT
	CONCAT((
		case when u.sequence = 1 then '卷烟私营'
		when u.sequence = 2 then '烟叶生产'
		when u.sequence = 3 then '专卖管理'
		when u.sequence = 4 then '综合管理'
		else '未知'
		end
	),'-',(
		case when ud.stage = 1 then '入党申请人'
		when ud.stage = 2 then '入党积极分子'
		when ud.stage = 3 then '发展对象'
		when ud.stage = 4 then '预备党员'
		when ud.stage = 5 then '成功发展'
		else '未知'
		end
	)) as name,
	count(ud.user_id) as total
FROM
	t_user_develop ud
	LEFT JOIN t_user u ON ud.user_id = u.user_id
	LEFT JOIN t_organization o ON o.organization_id = ud.org_id
	LEFT JOIN t_organization o1 ON o.owner_id = o1.organization_id 
WHERE
	u.`status` = 1 
	AND (( ud.`status` != 0 AND ud.`status` != 2 ) OR ud.`status` IS NULL ) 
    <if test="year != null">
	AND (
		( ud.stage = 1 AND YEAR ( COALESCE ( ud.apply_joining_time, '1900-01-01' )) = #{year} ) 
		OR ( ud.stage = 2 AND YEAR ( COALESCE ( ud.activist_date, '1900-01-01' )) = #{year} ) 
		OR ( ud.stage = 3 AND YEAR ( COALESCE ( ud.develop_date, '1900-01-01' )) = #{year} ) 
		OR ( ud.stage = 4 AND YEAR ( COALESCE ( ud.prepare_date, '1900-01-01' )) = #{year} ) 
		OR ( ud.stage = 5 AND YEAR ( COALESCE ( ud.prepare_date, '1900-01-01' )) = #{year} ) 
	)          
    </if>
    <foreach item="item" collection="excludeOrgIds" open="" separator=" " close=" ">
    AND o.org_level NOT LIKE CONCAT('%-',#{item},'-%') AND o.organization_id != #{item}
    </foreach>
    <if test="unitId != null">
	AND o1.organization_id = #{unitId}
    </if>
	group by u.sequence,ud.stage
        </script>
    """
    )
    fun userDevelopCount(
        @Param("unitId") unitId: Long?,
        @Param("excludeOrgIds") excludeOrgIds: List<String>,
        @Param("year") year: Int?,
    ): List<Pair<String, Long>>

    /**
     * 获取 承诺践诺/责任区/志愿服务/示范岗 数量
     * @param unitId 单位id
     *
     */
    @Select(
        """
        <script>
        SELECT
            '承诺践诺' AS NAME,
            count( 1 ) AS total 
        FROM
            t_user_commitment_practice t 
        <if test="unitId != null">
        WHERE
            t.unit_id = #{unitId}
        </if>
        UNION ALL
        SELECT
            '责任区' AS NAME,
            count( 1 ) AS total 
        FROM
            t_user_mien_response mr 
        WHERE
            mr.organization_id IS NOT NULL 
            <if test="unitId != null">
            AND mr.unit_id = #{unitId}
            </if>
        UNION ALL
        SELECT
            '志愿服务' AS NAME,
            count( 1 ) AS total 
        FROM
            t_user_volunteer_service t 
            <if test="unitId != null">
            WHERE
            t.unit_id = #{unitId}
            </if>
        UNION ALL
        SELECT
            '示范岗' AS NAME,
            count( 1 ) AS total 
        FROM
            t_user_post_demonstration pd
            LEFT JOIN t_organization o1 ON pd.org_id = o1.organization_id
            LEFT JOIN t_organization o2 ON o1.owner_id = o2.organization_id 
        WHERE
            pd.org_id IS NOT NULL 
            AND o2.organization_id IS NOT NULL 
            <if test="unitId != null">
            AND o2.organization_id = #{unitId}
            </if>
        </script>
    """
    )
    fun userPromiseCount(
        @Param("unitId") unitId: Long?,
    ): List<Pair<String, Long>>

    /**
     * 查询连续n年内未发展党员的单位
     * @param regionId
     * @param orgTypeChild
     * @param years
     */
    @Select(
        """
            <script>
            SELECT
            DISTINCT
            o2.organization_id,
            o2.`name`,
            o2.org_type_child,
            o2.org_type
            FROM
            t_organization o
            LEFT JOIN t_user_org_corp uoc ON o.organization_id = uoc.organization_id
            LEFT JOIN t_organization o2 ON o.owner_id = o2.organization_id
            LEFT JOIN t_user u ON uoc.user_id = u.user_id
            WHERE
            o.`status` = 1
            <foreach item="orgId" collection="excludeOrgIds" open="" separator=" " close=" ">
            AND o.org_level NOT LIKE CONCAT('%-',#{orgId},'-%') AND o.organization_id != #{orgId}
            </foreach>
            AND o.org_type_child IN
            <foreach collection="orgTypeChild" item="type" open="(" separator="," close=")">
            #{type}
            </foreach>
            AND uoc.is_employee = 1
            AND YEAR(u.joining_time) IN
            <foreach collection="years" item="year" open="(" separator="," close=")">
            #{year}
            </foreach>
            AND o2.region_id = #{regionId}
            ORDER BY o2.seq
            </script>
                """
    )
    fun getNoDevelopOrgIdsByYears(
        @Param("regionId") regionId: Long,
        @Param("excludeOrgIds") excludeOrgIds: List<String>,
        @Param("orgTypeChild") orgTypeChild: List<Int>,
        @Param("years") years: List<Int>,
    ): List<Long>


    /**
     * 通过单位id，获取n个月前（含本月）的组织领导班子换届数据
     *
     * @param unitId 单位id 可以为空
     * @param month  月份
     */
    @Select("""
            <script>
            SELECT
            	calendar.ym AS ym,
            	COALESCE ( SUM( my_table.total ), 0 ) AS total
            FROM
            	(
            	SELECT
            		DATE_FORMAT( date_sub( CURRENT_DATE (), INTERVAL n MONTH ), '%Y-%m' ) AS ym
            	FROM
            		(
            		SELECT
            			a.N + b.N * 10 AS n
            		FROM
            			( SELECT 0 AS N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 ) AS a
            			CROSS JOIN ( SELECT 0 AS N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 ) AS b
            		) AS numbers
            	) AS calendar
            	LEFT JOIN (
            	SELECT
            		DATE_FORMAT( uop.create_time, '%Y-%m' ) AS ym,
            		COUNT( uop.org_id ) AS total,
            		o.NAME,
            		o.owner_id
            	FROM
            		t_user_org_period uop
            		LEFT JOIN t_organization o ON uop.org_id = o.organization_id
            	WHERE
            		uop.is_delete = 2
            		AND uop.create_time <![CDATA[ > ]]> date_sub( CURRENT_DATE (), INTERVAL #{month} MONTH )
            		AND uop.create_time <![CDATA[ <= ]]> CURRENT_DATE ()
            		AND o.region_id = #{regionId}
            		<if test="unitId != null and unitId != ''">
            		    AND o.owner_id = #{unitId}
            		</if>
                    <if test="excludeOrgIds != null">
                        <foreach item="item" collection="excludeOrgIds" open="" separator=" " close=" ">
                            AND ( o.org_level NOT LIKE CONCAT('%-',#{item},'-%') AND o.organization_id != #{item} )
                            OR uop.org_id != #{item}
                        </foreach>
                    </if>
            	GROUP BY
            		ym,
            		o.owner_id
            	) AS my_table ON calendar.ym = my_table.ym
            WHERE
            	calendar.ym <![CDATA[ > ]]> DATE_FORMAT( date_sub( CURRENT_DATE (), INTERVAL #{month} MONTH ), '%Y-%m' )
            GROUP BY
            	calendar.ym
            ORDER BY
            	calendar.ym
            </script>
            """
    )
    fun getOrgLeaderChange(
        @Param("unitId") unitId: Long?,
        @Param("month") month: @NotNull Int,
        @Param("excludeOrgIds") excludeOrgIds: List<String>?,
        @Param("regionId") regionId: @NotNull Long,
    ): List<Pair<String, Long>>
}