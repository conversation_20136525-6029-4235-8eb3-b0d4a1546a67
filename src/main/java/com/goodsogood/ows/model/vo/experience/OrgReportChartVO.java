package com.goodsogood.ows.model.vo.experience;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.db.experience.UserPartyEvalEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: mengting
 * @Date: 2022/4/21 11:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrgReportChartVO {
    private List<UserPartyReportVO.TypeStarVO>  starPieChart;//星级饼图
    private List<UserPartyReportVO.TypeStarVO> pillarChart;//各维度体检结果-柱状图
    private List<UserPartyReportVO.TypeStarVO> tagPieChart;//标签统计轮播图
}
