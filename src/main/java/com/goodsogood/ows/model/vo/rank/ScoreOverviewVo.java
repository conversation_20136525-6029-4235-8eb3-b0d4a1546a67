package com.goodsogood.ows.model.vo.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Create by FuXiao on 2020/10/23
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ScoreOverviewVo {
    @JsonProperty(value = "user_id")
    @ApiModelProperty(name = "党员id")
    private Long userId;

    @JsonProperty(value = "user_name")
    @ApiModelProperty(name = "党员姓名")
    private String userName;

    @JsonProperty(value = "org_id")
    @ApiModelProperty(name = "所在组织id")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty(name = "所在组织名称")
    private String orgName;

    @JsonProperty(value = "score")
    @ApiModelProperty(name = "得分")
    private Double score;
}
