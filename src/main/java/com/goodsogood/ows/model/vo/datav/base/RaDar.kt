package com.goodsogood.ows.model.vo.datav.base

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import java.io.Serializable

/**
 * datav 雷达图
 * <AUTHOR>
 * @date 2024/2/1
 * @description class RaDar
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class RaDar(
    // 雷达图中的类目，即雷达图的指标名称。
    var t: String? = null,
    // 雷达图中的值，即雷达图的指标值。
    var r: Double? = null,
    // 颜色映射对象，默认为"类型A"
    @JsonProperty("colorField")
    var colorField: String? = "类型A",
) : Serializable{
    companion object {
        private const val serialVersionUID: Long = 8662265140266161401L
    }
}