package com.goodsogood.ows.model.vo.rank;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgWhereResultForm {

    @JsonProperty("org_id")
    private Long orgId;

    @JsonProperty("org_name")
    private String orgName;

    @JsonProperty("short_name")
    private String shortName;

    @JsonProperty("org_type")
    private Integer orgType;

    @JsonProperty("org_type_child")
    private Integer orgTypeChild;

    @JsonProperty("org_type_child_name")
    private String orgTypeChildName;

    @JsonProperty("tag")
    private String tag;

    @JsonProperty("is_top")
    private Integer isTop;
}
