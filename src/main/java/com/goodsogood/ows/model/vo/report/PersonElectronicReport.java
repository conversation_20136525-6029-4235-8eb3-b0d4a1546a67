package com.goodsogood.ows.model.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import com.goodsogood.ows.model.mongodb.report.UserScoreDonateInfo;
import com.goodsogood.ows.model.mongodb.report.UserScoreStudyInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @program: ows-sas
 * @description: 个人电子报告
 * @author: Mr.<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019-11-22 09:50
 **/
@Data
public class PersonElectronicReport {

    @JsonProperty(value = "user_id")
    private Long userId;

    @JsonProperty(value = "user_name")
    private String userName;

    @JsonProperty(value = "org_name")
    private String orgName;


    @JsonProperty(value = "region_id")
    private Long regionId;

    @JsonProperty(value = "statistics_time")
    private String statisticsTime;

    /**
     * 报告创建时间
     */
    @JsonProperty(value = "create_time")
    private Date createTime;
    /**
     * 个人缴纳党费情况
     */
    @JsonProperty(value = "ppmd_pay_info")
    private PpmdPayInfo ppmdPayInfo;


    /**
     * 参加组织生活情况
     */
    @JsonProperty(value = "meeting_join_info")
    private MeetingJoinInfo meetingJoinInfo;

    /**
     * 参加活动情况
     */
    @JsonProperty(value = "activity_join_info")
    private  ActivityJoinInfo activityJoinInfo;

    /**
     * 党员学习情况
     */
    @JsonProperty(value = "study_info")
    private StudyInfo studyInfo;

    /**
     * 党员扶贫情况
     */
    @JsonProperty(value = "donate_info")
    private DonateInfo donateInfo;
    /**
     * 党费统计信息
     */
    @Data
    public static class PpmdPayInfo  {
        /**
         *  是否缴纳党费  1:是 2:否
         */
        @JsonProperty(value = "is_pay")
        private Integer isPay;

        @JsonProperty(value = "pay_number")
        private Double payNumber;

        @JsonProperty(value = "pay_time")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date payTime;

        @ApiModelProperty("缴纳的党费月数")
        @JsonProperty(value = "ppmd_time")
        private List<String> ppmdTime;

        @JsonProperty(value = "pay_fast_list")
        private PayFastUser payFastUsers;

        /**
         * 交费最快用户
         */
        @Data
        public static class PayFastUser  {
            @JsonProperty(value = "user_name")
            private String userName;

            @JsonProperty(value = "pay_time")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
            private Date payTime;
        }
    }

    /**
     * 组织生活参加信息
     */
    @Data
    public static class MeetingJoinInfo {

        @JsonProperty(value = "join_num")
        private Integer joinNum;

        @JsonProperty(value = "meeting_join_list")
        private List<MeetingJoinList> meetingJoinLists;

        @Data
        public static class MeetingJoinList {
            /**
             * 参与时间
             */
            @JsonProperty(value = "join_date")
            private String joinDate;
            /**
             * 组织生活类型List
             */
            @JsonProperty(value = "type_name")
            private List<String> typeName;

            @JsonProperty(value = "org_name")
            private String orgName;
        }
    }




    /**
     * 活动参加信息
     */
    @Data
    public static class ActivityJoinInfo{
        @JsonProperty(value = "join_num")
        private Integer joinNum;

        @JsonProperty(value = "activity_join_list")
        private List<ActivityJoinList> activityJoinList;

        @Data
        public static class ActivityJoinList{
            /**
             * 参与时间
             */
            @JsonProperty(value = "join_date")
            @JsonFormat(pattern="yyyy-MM-dd")
            private Date joinDate;
            /**
             * 活动类型名称
             */
            @JsonProperty(value = "type_name")
            private String typeName;

            /**
             *活动名称
             */
            @JsonProperty(value = "activity_name")
            private String activityName;

        }
    }
    @Data
    public static class StudyInfo{
        /**
         * 总的学习情况
         */
        @JsonProperty(value = "total")
        private UserScoreStudyInfo total;
        /**
         * 查询月学习情况
         */
        @JsonProperty(value = "detail")
        private UserScoreStudyInfo detail;

    }
    @Data
    public static class DonateInfo{
        /**
         * 总计公益/扶贫
         */
        @JsonProperty(value = "total")
        private UserScoreDonateInfo total;
        /**
         * 指定月的公益/扶贫
         */
        @JsonProperty(value = "detail")
        private UserScoreDonateInfo detail;


    }






}
