package com.goodsogood.ows.model.vo.tbc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> huangbinyang
 * @date : 2020.12.28
 * 烟草大屏党费情况返回结构
 */
@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class) //驼峰转下划线
@JsonInclude(JsonInclude.Include.NON_NULL)  //json 转实例忽略为空
@JsonIgnoreProperties(ignoreUnknown = true) //转json 忽略为空
public class OfficeDuesForm {

    @ApiModelProperty("党费交纳情况")
    private StatusForm status;

    @ApiModelProperty("党费交纳趋势")
    private Map<String,List<Node>> trend;

    @ApiModelProperty("支部交纳情况")
    private BranchForm branch;

    @ApiModelProperty("每月党费交纳排名")
    private List<Details> rankList;

    /**
     * 党费交纳情况
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class StatusForm{

        @ApiModelProperty("本年度已缴党费")
        private Double paidYear;

        @ApiModelProperty("本月已缴党费金额")
        private Double paidMonth;

        @ApiModelProperty("本月未缴党费金额")
        private Double unpaidMonth;

        @ApiModelProperty("本月已缴党费人数")
        private Integer numberMonth;

        @ApiModelProperty("本月已交纳人数百分比")
        private String percent;

        @ApiModelProperty("今日交费金额")
        private Double paidDay;

        @ApiModelProperty("今日本人交费人数")
        private Integer self;

        @ApiModelProperty("今日他人代缴人数")
        private Integer other;
    }

    /**
     * 支部交纳情况
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BranchForm{

        @ApiModelProperty("本月已交齐支部数量")
        private Integer numberPaid;

        @ApiModelProperty("本月未交齐支部数量")
        private Integer numberUnpaid;

        @ApiModelProperty("支部列表")
        private List<Paid> list;
    }

    /**
     * 趋势节点
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Node{

        @ApiModelProperty("日期")
        private int day;

        @ApiModelProperty("当日所交党费")
        private Double money;
    }

    /**
     * 支部交费详情
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Paid{

        @ApiModelProperty("支部id")
        private Long branchId;

        @ApiModelProperty("支部简称")
        private String shortName;

        @ApiModelProperty("支部本月已交金额")
        private Double paid;

        @ApiModelProperty("支部本月未交金额")
        private Double unpaid;

        @ApiModelProperty("交齐日期")
        private String date;

        @ApiModelProperty("是否交齐，0：未交齐，1：已交齐")
        private Integer isFinish;

    }

    /**
     * 党员交费详情
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Details {

        @ApiModelProperty("党员姓名")
        private String name;

        @ApiModelProperty("党员所在支部")
        private String of;

        @ApiModelProperty("交费时间")
        private String time;
    }

}
