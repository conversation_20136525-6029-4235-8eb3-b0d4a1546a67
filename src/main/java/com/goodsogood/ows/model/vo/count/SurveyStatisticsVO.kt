package com.goodsogood.ows.model.vo.count

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModelProperty
import lombok.Data
import org.codehaus.jackson.annotate.JsonIgnore
import org.codehaus.jackson.annotate.JsonProperty
import javax.persistence.Column

@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties("interviewConcat")
class SurveyStatisticVO{
    @ApiModelProperty("领导Id")
    var leaderId:String? = null
    
    @ApiModelProperty("领导姓名")
    var leaderName:String? = null
    
    
    @ApiModelProperty("部门id")
    var departmentId:String? = null

    @ApiModelProperty("部门名称")
    var department:String? = null

    @ApiModelProperty("调研方式拼接字符")
    @Transient
    var interviewConcat:String? = null

    @ApiModelProperty("调研客户总数")
    var total:String? = null

    @ApiModelProperty("调研次数")
    var times:String? = null

    @ApiModelProperty("调研方式:1.座谈访谈、2.随机走访、3.问卷调查（轻流）、4.问卷调查、5.专家调查、6.抽样调查、7.统计分析、8.其他")
    var interview:String?=null
    override fun toString(): String {
        return "SurveyStatisticVO(leaderId=$leaderId, leaderName=$leaderName, departmentId=$departmentId, department=$department, interviewConcat=$interviewConcat, total=$total, times=$times, interview=$interview)"
    }


}