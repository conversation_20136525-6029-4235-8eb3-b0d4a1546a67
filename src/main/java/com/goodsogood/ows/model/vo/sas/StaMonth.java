package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.persistence.Transient;

/**
 * @program: ows-sas
 * @description: 公共类名称
 * @author: 统计月份
 * @create: 2019-04-24 14:00
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StaMonth {

        @Transient
        @JsonProperty("month1")
        private Integer month1;

        @Transient
        @JsonProperty("month2")
        private Integer month2;

        @Transient
        @JsonProperty("month3")
        private Integer month3;

        @Transient
        @JsonProperty("month4")
        private Integer month4;

        @Transient
        @JsonProperty("month5")
        private Integer month5;

        @Transient
        @JsonProperty("month6")
        private Integer month6;

        @Transient
        @JsonProperty("month7")
        private Integer month7;

        @Transient
        @JsonProperty("month8")
        private Integer month8;

        @Transient
        @JsonProperty("month9")
        private Integer month9;

        @Transient
        @JsonProperty("month10")
        private Integer month10;

        @Transient
        @JsonProperty("month11")
        private Integer month11;

        @Transient
        @JsonProperty("month12")
        private Integer month12;

        /**
         * 组织每个月合计
         */
        @Transient
        private Integer total;

        /**
         * 组织每个月合计
         */
        @Transient
        @JsonProperty("activity_type_id")
        private Integer activityTypeId;

        @Transient
        @JsonProperty("activity_type_name")
        private String activityTypeName;

}
