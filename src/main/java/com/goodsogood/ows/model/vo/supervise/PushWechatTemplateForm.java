package com.goodsogood.ows.model.vo.supervise;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.Set;

/**
 * <p>Description: 微信推送接口参数</p>
 *
 * <AUTHOR>
 * @date 2019-05-14 10:57
 * @since 1.0.4
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class PushWechatTemplateForm {

    @JsonProperty("data")
    private String data;

    @ApiModelProperty("模板id")
    @JsonProperty("template_id")
    private Long templateId;

    @ApiModelProperty("渠道类型 1:短信 2:微信 3:主题推送 4:钉钉")
    @JsonProperty("channel_type")
    private Byte channelType;

    @ApiModelProperty("是否推送指定组织及其子组织 0:否 1:是")
    @JsonProperty("is_current_org")
    private Integer isCurrentOrg;

    @ApiModelProperty("业务系统的application.name，例如纪实 meeting")
    @JsonProperty("source")
    private String source;

    @ApiModelProperty("推送类型,0-即时推送，1-定时推送，此处传0即可")
    @JsonProperty("push_type")
    private Integer pushType;


    @ApiModelProperty("需要推送的组织id 如果不是全量推送org_ids，user_ids不能同时为空")
    @JsonProperty("org_ids")
    private Set<Long> orgIds;


    @ApiModelProperty("需要推送的用户id 如果不是全量推送org_ids，user_ids不能同时为空")
    @JsonProperty("user_ids")
    private Set<Long> userIds;

    @ApiModelProperty("推送时间,定时推送时不能为空")
    @JsonProperty("push_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pushTime;

    @ApiModelProperty("是否外链 0-否，1-是，此处传1即可")
    @JsonProperty("is_link_out")
    private Integer linkOut;

    @ApiModelProperty("外链地址,支持占位符{openId},自动替换为真实的openId,支持占位符{oid},自动替换为公总号对应的oid")
    @JsonProperty("link_url")
    private String linkUrl;

    @ApiModelProperty("是否全量推送 0-否，1-是")
    @JsonProperty("is_full")
    private Integer full;


    @ApiModelProperty("推送公众号关联的组织id")
    @JsonProperty("org_id")
    private Long orgId;

}
