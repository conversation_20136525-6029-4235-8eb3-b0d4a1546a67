package com.goodsogood.ows.model.vo.datav.base

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.goodsogood.ows.helper.Json.DoubleKeepZeroSerializer
import java.io.Serializable

/**
 * datav 仪表盘
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class Dashboard @JvmOverloads constructor(
    // 仪表盘中刻度的最小值。
    var min: Long? = 0,
    // 仪表盘中刻度的最大值。
    var max: Long? = 1,
    // 仪表盘中心文本的内容。
    val content: String? = null,
    // 仪表盘的指标值。
    val value: String? = null,
    // 仪表盘中指标比例的百分比值，配置数据需要为0到100之间的数值。
    @JsonSerialize(using = DoubleKeepZeroSerializer::class)
    val percent: Double? = null,
): Serializable {
    companion object {
        private const val serialVersionUID: Long = -4828672523408571759L
    }
}
