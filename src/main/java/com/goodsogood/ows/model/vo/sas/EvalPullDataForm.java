package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;


/**
 * @program: ows-sas
 * @description: 拉取组织生活封装类型
 * @author: Mr.<PERSON><PERSON>
 * @create: 2019-05-08 13:57
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EvalPullDataForm {

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("组织类型id")
    @JsonProperty(value = "org_type_id")
    @Column(name = "org_type_id")
    private Long orgTypeId;

    @ApiModelProperty("组织创建时间")
    @JsonProperty(value = "org_create_time")
    @Column(name = "org_create_time")
    private Date orgCreateTime;


    /**
     * 每个月存在多个会议时间
     */
    @JsonProperty(value = "meeting_start_time")
    @Column(name = "meeting_start_time")
    private String meetingStartTime;


    /**
     * 记录会议的id
     */
    @JsonProperty(value = "meeting_ids")
    @Column(name = "meeting_ids")
    private String meetingIds;


    /**
     * 这里查询t_statistical_storage_variable 并且返回一个json字条串
     */
    @ApiModelProperty("党小组数量")
    @JsonProperty(value = "party_group_num")
    @Column(name = "party_group_num")
    private String partyGroupNum;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;



    @ApiModelProperty("是否离退休党组织 1-是 2-否")
    @JsonProperty(value = "is_retire")
    @Column(name = "is_retire")
    private Integer isRetire;


    @ApiModelProperty("活动类型id")
    @JsonProperty(value = "activity_type_id")
    @Column(name = "activity_type_id")
    private Integer activityTypeId;



    @ApiModelProperty("活动类型名称")
    @JsonProperty(value = "activity_type_name")
    @Column(name = "activity_type_name")
    private String activityTypeName;


    @ApiModelProperty("参与次数")
    @JsonProperty(value = "participate_num")
    @Column(name = "participate_num")
    private Integer participateNum;


    @JsonProperty(value = "statistical_year")
    @Column(name = "statistical_year")
    private Integer statisticalYear;

    @ApiModelProperty("统计的月（1-12）")
    @JsonProperty(value = "statistical_month")
    @Column(name = "statistical_month")
    private Integer statisticalMonth;

    @ApiModelProperty("统计时间(yyyy-MM)")
    @JsonProperty(value = "statistical_date")
    @Column(name = "statistical_date")
    private String statisticalDate;


    @ApiModelProperty("逻辑状态:(1-有效, 0-无效)")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty(value = "支委会数量")
    @JsonProperty("period_sum")
    private Integer periodSum;

    /**
     * 是否存在以设置支委会标签 1:已设置 2:未设置
     */
    @JsonProperty("has_period_tag")
    private Integer hasPeriodTag;

}
