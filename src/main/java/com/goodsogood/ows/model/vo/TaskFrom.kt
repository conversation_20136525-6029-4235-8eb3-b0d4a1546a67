package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModelProperty
import lombok.Data


@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class TaskFrom {
    @ApiModelProperty("排名")
    val rank: Int? = null

    @ApiModelProperty("用户id")
    var userId: Long? = null

    @ApiModelProperty("分数")
    val commentNum: Int? = null

    @ApiModelProperty("五星次数")
    val numberOfStars: Int? = null
}


@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
class EcpTaskStaFrom {

    @ApiModelProperty("累计完成")
    @JsonProperty(value = "cumulative_completion")
    var cumulativeCompletion: Int? = null

    @ApiModelProperty("本单位人均")
    @JsonProperty(value = "unit_average")
    var unitAverage: Int? = null

    @ApiModelProperty("系统人均")
    @JsonProperty(value = "system_average")
    var systemAverage: Int? = null

}


@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
class EcpTaskListForm {

    @ApiModelProperty("任务id")
    @JsonProperty(value = "task_id")
    var taskId: Long? = null

    @ApiModelProperty("任务名称")
    @JsonProperty(value = "task_name")
    var taskName: String? = null


    @ApiModelProperty("完成数量")
    @JsonProperty(value = "number")
    var number: Int? = null


    @ApiModelProperty("是不是审核员")
    @JsonProperty(value = "verify")
    var verify: Int? = null

    @ApiModelProperty("操作状态")
    @JsonProperty(value = "operate_status")
    var operateStatus: Int? = null


    @ApiModelProperty("接收范围（1：组织，2：人员）")
    @JsonProperty(value = "access_range")
    var accessRange: Int? = null

    @ApiModelProperty("任务验证")
    @JsonProperty(value = "status")
    var status: Int? = null

    @JsonProperty(value = "access_id")
    var accessId: Long? = null

    @JsonProperty(value = "task_access_id")
    var taskAccessId: Long? = null

    @ApiModelProperty("开始时间")
    @JsonProperty(value = "begin_time")
    var beginTime: String? = null

    @ApiModelProperty("结束时间")
    @JsonProperty(value = "end_time")
    var endTime: String? = null
}

/**
 * 云区动态记录
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
class EcpRecordVo {

    @ApiModelProperty("单位名称")
    @JsonProperty(value = "unit_name")
    var unitName: String? = null

    @ApiModelProperty("部门")
    @JsonProperty(value = "department")
    var department: String? = null

    @ApiModelProperty("用户名称")
    @JsonProperty(value = "name")
    var name: String? = null

    @ApiModelProperty("类型1.加入云上组织2.完成云上任务")
    @JsonProperty(value = "type")
    var type: Int? = null

    @ApiModelProperty("任务id")
    @JsonProperty(value = "task_id")
    var taskId: Int? = null

    @ApiModelProperty("ecpOrgId")
    @JsonProperty(value = "ecp_org_id")
    var ecpOrgId: Long? = null

    @ApiModelProperty("显示内容")
    @JsonProperty(value = "content")
    var content: String? = null

    @ApiModelProperty("内容json，用于前端解析跳转")
    @JsonProperty(value = "content_detail")
    var contentDetail: String? = null
}