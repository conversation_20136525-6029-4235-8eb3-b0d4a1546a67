package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * Create by  on 2018/12/25
 * <AUTHOR>
 */
@Data
@ApiModel
public class ActivePaymentVo {
    @JsonProperty(value = "active_users")
    @ApiModelProperty("用户名称")
    private ActiveUserVo activeUsers;

    @JsonProperty(value = "active_orgs")
    @ApiModelProperty("用户名称")
    private ActiveOrgVo activeOrgs;
}
