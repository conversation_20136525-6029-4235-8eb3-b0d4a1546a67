package com.goodsogood.ows.model.vo.meeting;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Description: 活动类型</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 10:27
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CategoryListForm {

    @ApiModelProperty("id")
    @JsonProperty(value = "category_id")
    private Long categoryId;

    @JsonProperty("category")
    @ApiModelProperty(value = "类别")
    private String category;
}
