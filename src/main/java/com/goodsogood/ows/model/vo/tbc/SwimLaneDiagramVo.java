package com.goodsogood.ows.model.vo.tbc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 泳道图Vo
 */
@Data
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)  //json 转实例忽略为空
@JsonIgnoreProperties(ignoreUnknown = true) //转json 忽略为空
public class SwimLaneDiagramVo {

    //分公司名称
    @JsonProperty(value = "list_name")
    private List<String> listName;

    //统计结果
    @JsonProperty(value = "sta_result")
    private List<DetailResult> detailResult;

    //全体党员平均值
    @JsonProperty(value = "all_party_avg")
    private Double allPartyAvg;

    //非党员平均值
    @JsonProperty(value = "non_party_avg")
    private Double nonPartyAvg;

    //系统最高
    @JsonProperty(value = "system_top")
    private Integer systemTop;

    //系统最高分公司
    @JsonProperty(value = "system_top_company")
    private String systemTopCompany;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Accessors(chain = true)
    public static class DetailResult {
        //最高人员信息
        @JsonProperty(value = "top_infos")
        private List<TopUserInfo> topIfoS;

        //统计结果
        @JsonProperty(value = "branch_averages")
        private List<BranchAverage> branchAverages;

    }


    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Accessors(chain = true)
    public static class TopUserInfo {
        //姓名
        @JsonProperty(value = "name")
        private String name;

        //手机号（脱敏）
        @JsonProperty(value = "phone")
        private String phone;

        //正式党员、预备党员、非党员
        @JsonProperty(value = "politics_name")
        private String politicsName;

        //所属分公司
        @JsonProperty(value = "unit_name")
        private String unitName;

        //最高的值
        @JsonProperty(value = "value")
        private Integer value;

    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Accessors(chain = true)
    public static class BranchAverage {
        //1.党员 2.非党员
        @JsonProperty(value = "politics_type")
        private Integer politicsType;

        //人数
        @JsonProperty(value = "number")
        private Integer number;

        //均值
        @JsonProperty(value = "value")
        private Double value;
    }

    public SwimLaneDiagramVo() {
    }

    public SwimLaneDiagramVo(Double allPartyAvg, Double nonPartyAvg) {
        this.allPartyAvg = allPartyAvg;
        this.nonPartyAvg = nonPartyAvg;
    }
}
