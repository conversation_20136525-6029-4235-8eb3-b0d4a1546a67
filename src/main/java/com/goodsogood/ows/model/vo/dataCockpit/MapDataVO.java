package com.goodsogood.ows.model.vo.dataCockpit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * 数据驾驶舱 - 地图数据
 *
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MapDataVO {

     @ApiModelProperty("是否最后一级 0-否 1-是")
     public Integer isLast;

     @ApiModelProperty("下级党组织列表")
     public List<ResultOrg> resultOrgs;

     @Data
     @ApiModel
     @JsonInclude(JsonInclude.Include.NON_NULL)
     @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
     public class ResultOrg{
          @ApiModelProperty("组织id")
          private Long organizationId;

          @ApiModelProperty("组织名称")
          private String name;

          @ApiModelProperty("组织类型")
          private Integer orgTypeChild;

          @ApiModelProperty("组织类型Str")
          private String orgTypeChildStr;

          @ApiModelProperty(value = "经度")
          private Double longitude;

          @ApiModelProperty(value = "纬度")
          private Double latitude;

          @ApiModelProperty("组织类别 1-党委 2-党支部 3-其他")
          private Integer orgType;

          @ApiModelProperty(value = "是否地图外显示 0-否 1-是")
          private Integer isOutsideMap;
     }

}
