package com.goodsogood.ows.model.vo.score;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 参加情况
 *
 * <AUTHOR>
 * @date 2020/11/30
 */
@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class JoinVO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 参与次数
     */
    private Integer joinTotal;

    /**
     * 积分用户id
     */
    private Long scoreUserId;

    /**
     * 积分
     */
    private Integer score;
}
