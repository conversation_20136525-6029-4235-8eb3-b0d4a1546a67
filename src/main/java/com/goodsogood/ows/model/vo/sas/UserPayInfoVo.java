package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserPayInfoVo {

    @ApiModelProperty("用户编号")
    @JsonProperty(value = "user_id")
    private Long userId;

    @ApiModelProperty("类型  1 已交、免交   2 未交、欠交")
    private Integer type;

    @ApiModelProperty("已交或者欠交金额")
    private Double money;
}
