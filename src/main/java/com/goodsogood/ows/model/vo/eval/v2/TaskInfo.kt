package com.goodsogood.ows.model.vo.eval.v2

import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase

/**
 * 任务封装对象
 */
data class TaskInfo @JvmOverloads constructor(
    val year: Int? = null,
    val taskId: String? = null,
    val metricId: Long? = null,
    val metricClassId: Long? = null,
    val metric: MetricEntity? = null,
    val unit: OrganizationBase? = null,
    val org: OrganizationBase? = null,
    val time: Long? = null,
    val trackerId: String? = null,
    val params: MutableMap<String, Any>? = null,
)
