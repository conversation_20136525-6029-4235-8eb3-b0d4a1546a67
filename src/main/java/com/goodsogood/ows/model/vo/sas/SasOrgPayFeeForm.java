package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.util.Date;

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SasOrgPayFeeForm {

    @JsonProperty(value = "party_fee_id")
    private Long partyFeeId;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty("区县id")
    @JsonProperty(value = "region_id")
    private Long regionId;

    @ApiModelProperty("组织类型")
    @JsonProperty(value = "org_type_id")
    private Integer orgTypeId;

    @ApiModelProperty("是否离退休党组织 1-是 2-否")
    @JsonProperty(value = "is_retire")
    private Integer isRetire;


    @ApiModelProperty("应交人数（默认为0）")
    @JsonProperty(value = "payable_person_num")
    private Integer payablePersonNum;


    @ApiModelProperty("未交纳人数")
    @JsonProperty(value = "unpaid_person_num")
    private Integer unpaidPersonNum;


    @JsonProperty(value = "statistical_year")
    private Integer statisticalYear;


    @ApiModelProperty("统计的月（1-12）")
    @JsonProperty(value = "statistical_month")
    private Integer statisticalMonth;

    @ApiModelProperty("统计时间(yyyy-MM)")
    @JsonProperty(value = "statistical_date")
    private String statisticalDate;

    @Transient
    @ApiModelProperty(value = "查询的开始日期")
    private Date startTime;

    @Transient
    @ApiModelProperty(value = "查询的结束日期")
    private Date endTime;

    @ApiModelProperty(value = "月份字符串")
    @JsonProperty("date_str")
    private String dateStr;

    @Transient
    @ApiModelProperty(value = "需要查询的列名")
    private String payFeeParam;

    @Transient
    @ApiModelProperty(value = "展示的组织类型")
    private String showOrgType;
}
