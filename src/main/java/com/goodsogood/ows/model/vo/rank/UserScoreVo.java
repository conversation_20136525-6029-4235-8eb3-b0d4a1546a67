package com.goodsogood.ows.model.vo.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Create by FuXiao on 2020/10/22
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserScoreVo {
    @JsonProperty(value = "user_id")
    @ApiModelProperty(name = "用户id")
    private Long userId;

    @JsonProperty(value = "user_name")
    @ApiModelProperty(name = "姓名")
    private String userName;

    @JsonProperty(value = "org_id")
    @ApiModelProperty(name = "所在组织id")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty(name = "所在组织名称")
    private String orgName;

    @JsonProperty(value = "top_name")
    @ApiModelProperty(name = "类型名称")
    private String topName;

    @JsonProperty(value = "year")
    @ApiModelProperty(name = "年度")
    private Long year;

    @JsonProperty(value = "create_user_id")
    @ApiModelProperty(name = "录入人id")
    private Long createUserId;

    @JsonProperty(value = "create_user_name")
    @ApiModelProperty(name = "录入人名称")
    private String createUserName;

    @JsonProperty(value = "scoreVos")
    @ApiModelProperty(name = "分数虚类")
    private ScoreVo[] scoreVos;

}
