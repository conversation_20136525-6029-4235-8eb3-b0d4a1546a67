package com.goodsogood.ows.model.vo.eval.v2

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer
import java.time.LocalDateTime

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class TopPriorityUnitStatisticsVO(
    // 关联的单位id
    var unitOrgId: Long? = null,
    // 关联的单位name
    var unitOrgName: String? = null,
    // 关联的单位简称
    var unitShortName: String? = null,
    // 所有已学议题数
    var priorityNum: Int = 0,
    // 党组会学习数量
    var partyGroupNum: Int = 0,
    //  中心组学习数量
    var leadingGroupNum: Int = 0,
    // 专题读书班数量
    var specialReadingNum: Int = 0,
    // 	正常已学议题数
    var completedPriorityNum: Int = 0,
    // 	逾期学习议题数
    var latePriorityNum: Int = 0,
    // 已学议题数据
    var meetings: List<TopPriorityUnitVO>? = null,
    // 单位排序
    var seq:Int? = 0,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class TopPriorityUnitVO(
    // 关联的组织id
    var associatedOrg: Long? = null,
    // 关联的单位name
    // @Field("associated_org_name")
    var associatedName: String? = null,
    var associatedUnitOrgId: Long? = null,
    // | unit_org_name | string | 20 | Y | 单位名称|登录的时候会获得|
    var associatedUnitOrgName: String? = null,
    // | unit_short_name | string | 20 | Y | 单位简称|登录的时候会获得|
    // 学习组织所属单位(简称)
    var associatedUnitShortName: String? = null,

    // 第一议题id
    var topPriorityId: String? = null,
    // 第一议题标题
    var title: String? = null,

    // meeting相关
    // 会议id
    var meetingId: Long? = null,
    // 会议标题
    var meetingTitle: String? = null,
    // 会议时间
    @get: JsonFormat
        (shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer::class)
    @JsonDeserialize(using = LocalDateTimeDeserializer::class)
    var meetingTime: LocalDateTime? = null,
    // 会议类型id
    var meetingTypes: String? = null,
    // 会议类型名称
    var meetingTypeNames: String? = null,
    // 第一议题学习间隔时间
    var diffDays: Long? = null,
)