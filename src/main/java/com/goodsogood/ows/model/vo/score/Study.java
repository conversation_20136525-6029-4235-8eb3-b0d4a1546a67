package com.goodsogood.ows.model.vo.score;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.mongodb.ScoreSuperInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * t_score_detail oper_type=0 and score_type=4
 */
@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class Study extends ScoreSuperInfo {

    @ApiModelProperty("积分记录")
    private Long score;

    @ApiModelProperty("t_score_detail 积分流水Id ")
    private Long scoreDetailId;

    @ApiModelProperty("产生的原因")
    private String remark;

    @ApiModelProperty("消费时间")
    private Date consumeTime;

    @ApiModelProperty("区县id")
    private Long regionId;
}