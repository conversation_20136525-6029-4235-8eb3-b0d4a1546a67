package com.goodsogood.ows.model.vo.meeting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议人员记录
 * @date 2019/11/21
 */
@Data
public class MeetingUserForm {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户姓名")
    private String userName;

    @ApiModelProperty("签到状态 1：已签到（默认） 2：未签到 3：因公请假 4：因私请假 5: 缺席")
    private Integer signStatus;

    @ApiModelProperty("参会人员与列席人员标识 1：记录人员，2：主持人，3：参与人员 ，4：列席人员")
    private String tag;
}
