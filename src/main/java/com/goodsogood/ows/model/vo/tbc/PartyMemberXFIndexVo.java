package com.goodsogood.ows.model.vo.tbc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)  //json 转实例忽略为空
@JsonIgnoreProperties(ignoreUnknown = true) //转json 忽略为空
public class PartyMemberXFIndexVo {

    //本单位均值
    @JsonProperty(value = "unit_avg")
    private Double unitAvg;

    //本单位最高
    @JsonProperty(value = "unit_max")
    private Double unitMax;

    //系统平均
    @JsonProperty(value = "system_average")
    private Double systemAverage;

    //系统最高
    @JsonProperty(value = "system_max")
    private Double systemMax;

    //堡垒指标列表
    @JsonProperty(value = "list")
    private List<XianFengIndex> list;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Accessors(chain = true)
    public static class XianFengIndex {

        //排名（指数相同排名相同）
        @JsonProperty(value = "rank")
        private Integer rank;

        //党员姓名
        @JsonProperty(value = "name")
        private String name;

        //支部名称
        @JsonProperty(value = "org_name")
        private String orgName;

        //先锋指数
        @JsonProperty(value = "xianfeng_index")
        private Double xainFengIndex;

        //党建积分
        @JsonProperty(value = "party_index")
        private Double partyIndex;

        //业务积分
        @JsonProperty(value = "business_index")
        private Double businessIndex;

        //创新积分
        @JsonProperty(value = "innovation_index")
        private Double innovationIndex;
    }

}
