package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
public class OrgPartyBranchVo extends OverviewBaseVo {

    @JsonProperty(value = "org_name")
    private String orgName;


    @JsonProperty(value = "list")
    private List<String> partyGroupName;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public List<String> getPartyGroupName() {
        return partyGroupName;
    }

    public void setPartyGroupName(List<String> partyGroupName) {
        this.partyGroupName = partyGroupName;
    }
}
