package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 条件查询
 *
 * <AUTHOR>
 * @date 2019/6/12 17:31
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ElectronicConditionForm {

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    @NotNull(message = "{NotNull.sas.org.id}")
    private Long orgId;

    @ApiModelProperty("区县id")
    @JsonProperty(value = "region_id")
    private Long regionId;

    @ApiModelProperty("查询年份")
    @JsonProperty(value = "year")
    @NotNull(message = "{NotNull.sas.month}")
    private Integer year;

    @ApiModelProperty("查询月份")
    @JsonProperty(value = "month")
    @NotNull(message = "{NotNull.sas.year}")
    private String month;

    @ApiModelProperty(value = "是否过滤设置展示的组织类型,默认需要过滤")
    @JsonProperty("org_type_id_flag")
    private Boolean orgTypeIdFlag=true;

}
