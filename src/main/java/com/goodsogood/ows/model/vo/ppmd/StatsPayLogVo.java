package com.goodsogood.ows.model.vo.ppmd;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 交费记录类
 *
 * @auther Administrator tc
 * @date 2018/10/24
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StatsPayLogVo {

    @JsonProperty(value = "org_id")
    @ApiModelProperty("支部编号")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("支部名称")
    private String orgName;

    @JsonProperty(value = "office_id")
    @ApiModelProperty("机关单位编号")
    private Long officeId;

    @JsonProperty(value = "office_name")
    @ApiModelProperty("机关单位名称")
    private String officeName;

    @JsonProperty(value = "user_id")
    @ApiModelProperty("用户编号")
    private Long userId;

    @JsonProperty(value = "user_name")
    @ApiModelProperty("用户名称")
    private String userName;

    /**
     * 缴费方式 1：银联支付   2：微信支付
     */
    @JsonProperty(value = "pay_method")
    @ApiModelProperty("缴费方式")
    private Integer payMethod;

    /**
     * 已交金额 单位:分
     */
    @JsonProperty(value = "pay_already")
    @ApiModelProperty("已交金额")
    private Integer payAlready;

    /**
     * 记录类型:1 正常交纳记录、2 免交记录、3 补交记录、4 正常欠交记录 5 少交欠交记录、6 固定金额欠交记录
     */
    @JsonProperty(value = "pay_log_type")
    @ApiModelProperty("记录类型")
    private Integer payLogType;

    /**
     * 交费比例类型:1.正常党员交费比例  2.退休党员交费比例  默认 1
     */
    @JsonProperty(value = "ratio_type")
    @ApiModelProperty("交费比例类型")
    private Integer ratioType;

    /**
     * 缴费类型，1：自己缴费 2：他人代缴  3:代人缴费
     */
    @JsonProperty(value = "pay_type")
    @ApiModelProperty("缴费类型")
    private Integer payType;
}
