package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 电子报表党费统计
 *
 * <AUTHOR>
 * @date 2019/6/12 16:30
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ElectronicPartyFeeData {

    @ApiModelProperty("应交人数（默认为0）")
    @JsonProperty(value = "payable_person_num")
    private Integer payablePersonNum;


    @ApiModelProperty("未交纳人数")
    @JsonProperty(value = "unpaid_person_num")
    private Integer unpaidPersonNum;

    @ApiModelProperty(value = "党费缴纳完成的比例")
    @JsonProperty(value = "proportion")
    private BigDecimal proportion;
}
