package com.goodsogood.ows.model.vo.datav

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.goodsogood.ows.model.vo.datav.base.CardFlop
import com.goodsogood.ows.model.vo.datav.base.Pie
import java.io.Serializable

/**
 * <AUTHOR>
 * @date 2023/3/29
 * @description class OneVO 可视化大屏党业融合 -  一云区
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class OneVO @JvmOverloads constructor(
    // 云上任务翻牌器
    var cardFlopTask: MutableList<CardFlop> = mutableListOf(),
    // 云上组织翻牌器
    var cardFlopOrg: MutableList<CardFlop> = mutableListOf(),
    // 参与人数翻牌器
    var cardFlopPeople: MutableList<CardFlop> = mutableListOf(),
    // 参与人次翻牌器
    var cardFlopNum: MutableList<CardFlop> = mutableListOf(),
    // 参与人次饼图
    var pie: MutableList<Pie>? = null,
    // 党员人数翻牌器
    var cardFlopParty: MutableList<CardFlop> = mutableListOf(),
    // 非党员人数翻牌器
    var cardFlopNotParty: MutableList<CardFlop> = mutableListOf(),
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = -5533833553370756378L
    }
}