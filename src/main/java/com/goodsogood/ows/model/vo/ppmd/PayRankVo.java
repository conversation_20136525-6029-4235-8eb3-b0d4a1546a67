package com.goodsogood.ows.model.vo.ppmd;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * @program: ows-sas
 * @description: 单位完成交纳党费排名
 * @author: Mr.<PERSON>
 * @create: 2020-08-12 15:42
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PayRankVo {

    @JsonProperty(value = "org_id")
    private Long orgId;

    @JsonProperty(value = "org_name")
    private String orgName;

    @JsonProperty(value = "finish_time")
    private Date finishTime;

}
