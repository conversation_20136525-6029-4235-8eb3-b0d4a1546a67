package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 党组织生活统计查询条件
 *
 * <AUTHOR>
 * @date 2019/4/23 14:41
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SasOrgLifeConditionForm {

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    @NotNull(message = "{NotNull.sas.org.id}")
    private Long orgId;

    @ApiModelProperty("区县id")
    @JsonProperty(value = "region_id")
    private Long regionId;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty("活动类型id,当前显示的活动")
    @JsonProperty(value = "activity_type_id")
    private Integer activityTypeId;

    @ApiModelProperty("活动类型id,当前显示的活动(多个逗号分割）")
    @JsonProperty(value = "activity_type_ids")
    private String activityTypeIds;

    @ApiModelProperty("时间查询类型,1-按年，2-按半年，3-按季度，4-按月份")
    @JsonProperty(value = "time_type")
    @NotNull(message = "{NotNull.sas.time.type}")
    private Integer timeType;

    @ApiModelProperty("年份")
    @JsonProperty(value = "year")
    @NotNull(message = "{NotNull.sas.year}")
    private Integer year;

    @ApiModelProperty("查询时间,1-上半年，2-下半年，3-第一季度，4-第二季度，5-第三季度，6-第四季度")
    @JsonProperty(value = "time")
    private Integer time;

    @ApiModelProperty("开始月份")
    @JsonProperty(value = "start_month")
    private Integer startMonth;

    @ApiModelProperty("结束月份")
    @JsonProperty(value = "end_month")
    private Integer endMonth;

    @ApiModelProperty(value = "页码")
    @JsonProperty("page_no")
    private Integer pageNo = 1;

    @ApiModelProperty(value = "分页大小")
    @JsonProperty("page_size")
    @Range(min = 1, max = 50, message = "每页显示条数1-50")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "查询的开始日期")
    @JsonProperty("start_time")
    private Date startTime;

    @ApiModelProperty(value = "查询的结束日期")
    @JsonProperty("end_time")
    private Date endTime;

    @ApiModelProperty(value = "月份字符串")
    @JsonProperty("date_str")
    private String dateStr;

    @ApiModelProperty(value = "是否离退休组织1-是 2-否")
    @JsonProperty("is_retire")
    private Integer isRetire;

    @ApiModelProperty(value = "活动类型:{'activity_id':1,'activity_name':'办公室'}")
    @JsonProperty("activity_type_json")
    private String activityTypeJson;

    @Transient
    @ApiModelProperty(value = "展示的组织类型")
    private String showOrgType;

    @ApiModelProperty(value = "是否过滤设置展示的组织类型,默认需要过滤")
    @JsonProperty("org_type_id_flag")
    private Boolean orgTypeIdFlag=true;

    @ApiModelProperty(value = "是否过滤参与次数大于0的数据，用于电子党务报告")
    @JsonProperty("participate_flag")
    private Boolean participateFlag=false;


    @ApiModelProperty(value = "是否开启只查询机关党委的限制")
    @JsonProperty("query_limit")
    private Integer  queryLimit;



}
