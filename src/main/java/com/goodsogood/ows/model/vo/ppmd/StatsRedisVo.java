package com.goodsogood.ows.model.vo.ppmd;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统计redis类
 *
 * @auther Administrator tc
 * @date 2018/10/24
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StatsRedisVo {

    /**
     * 当统计的维度为党支部时，记录党支部所属的机关单位
     */
    @JsonProperty(value = "parent_org_id")
    @ApiModelProperty("党支部所属党委编号")
    private Long parentOrgId;

    @JsonProperty(value = "org_id")
    @ApiModelProperty("党委或者支部编号")
    private Long id;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("党委或者支部名称")
    private String name;

    /**
     * 是否考核单位 0 否  1是
     */
    @JsonProperty(value = "examine")
    @ApiModelProperty("是否考核单位")
    private Integer examine;

    @JsonProperty(value = "stats_date")
    @ApiModelProperty("统计月份")
    private String statsDate;

    @JsonProperty(value = "num_total")
    @ApiModelProperty("党员人数")
    private Integer numTotal;

    @JsonProperty(value = "num_should")
    @ApiModelProperty("应交人数")
    private Integer numShould;

    @JsonProperty(value = "pay_should")
    @ApiModelProperty("应交金额")
    private Long payShould;

    @JsonProperty(value = "num_already")
    @ApiModelProperty("已交人数")
    private Integer numAlready;

    @JsonProperty(value = "pay_already")
    @ApiModelProperty("已交金额")
    private Long payAlready;

    @JsonProperty(value = "num_owing")
    @ApiModelProperty("欠交人数")
    private Integer numOwing;

    @JsonProperty(value = "pay_owing")
    @ApiModelProperty("欠交金额")
    private Long payOwing;

    /**
     * 组织类型   1 党委  2 支部
     *  在党委维度的报表里提供给前端判断跳转
     *  tc 2018.12.05
     */
    @JsonProperty(value = "org_type")
    @ApiModelProperty("组织类型")
    private Integer orgType;
}
