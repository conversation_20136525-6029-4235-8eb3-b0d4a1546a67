package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 组织
 *
 * <AUTHOR>
 * @date 2018-04-02
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrganizationForm {


    @JsonProperty("org_id")
    @ApiModelProperty(value = "组织ID")
    private Long organizationId;

    @JsonProperty("parent_id")
    @ApiModelProperty(value = "父类ID")
    private Long parentId;

    @JsonProperty("region_id")
    @ApiModelProperty(value = "区县Id")
    private Long regionId;

    @ApiModelProperty(value = "组织名称")
    private String name;

    @JsonProperty("org_type")
    @ApiModelProperty(value = "组织类型")
    private Integer orgType;

    @JsonProperty("org_type_child")
    @ApiModelProperty(value = "组织类型下级")
    private Integer orgTypeChild;

    @JsonProperty("org_code")
    @ApiModelProperty(value = "组织代码")
    private String orgCode;

    @JsonProperty("org_phone")
    private String orgPhone;

    @JsonProperty("org_leader")
    private String orgLeader;

    @JsonProperty("org_leader_phone")
    private String orgLeaderPhone;

    @JsonProperty("org_unique_code")
    private String orgUniqueCode;

    @JsonProperty("org_area")
    private String orgArea;

    private String postcode;

    @JsonProperty("org_address")
    private String orgAddress;

    /**
     * 状态 1-启用 2-禁用 3-不可修改
     */
    private Integer status;

    @JsonProperty("seq")
    private Integer seq;

    @JsonProperty("child_org_num")
    private Integer childOrgNum;

    @JsonProperty("company_num")
    private Integer companyNum;

    @JsonProperty("user_num")
    private Integer userNum;

    @JsonProperty("work_num")
    private Integer workNum;

    @JsonProperty("org_level")
    private String orgLevel;

    @JsonProperty("last_change_user")
    private Long lastChangeUser;

    @JsonProperty("create_time")
    private Date createTime;

    @JsonProperty("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "行业类别")
    @JsonProperty("industry_type")
    private Integer industryType;

    @ApiModelProperty(value = "是否激活 1-已激活 2-未激活")
    private Integer activate;

    @Transient
    @JsonProperty("oid")
    private Long id;

    @Transient
    @JsonProperty("org_type_name")
    private String orgTypeName;

    @JsonProperty("org_contacts")
    private String orgContacts;

    @ApiModelProperty(value = "组织序列号")
    @JsonProperty("id")
    private String orgId;

    @ApiModelProperty(value = "上级组织序列号")
    @JsonProperty("org_pid")
    private String orgPid;

    @ApiModelProperty(value = "所属组织ID")
    @JsonProperty("owner_id")
    private Long ownerId;

    @ApiModelProperty(value = "第三方数据")
    @JsonProperty("third_data_info")
    private String thirdDataInfo;

    @ApiModelProperty(value = "第三方数据来源 1-12371")
    @JsonProperty("third_source")
    private Integer thirdSource;

    @ApiModelProperty(value = "所属组织树 1-父树 2-子树")
    @JsonProperty("owner_tree")
    private Integer ownerTree;

    @ApiModelProperty(value = "是否离退休党组织 1-是 2-否")
    @JsonProperty("is_retire")
    private Integer isRetire;

    @JsonProperty("org_create_time")
    @ApiModelProperty(value = "组织建立时间")
    private Date orgCreateTime;

    @ApiModelProperty(value = "支委会数量")
    @JsonProperty("period_sum")
    private Integer periodSum;

    @ApiModelProperty(value = "党小组数量")
    @JsonProperty("group_sum")
    private Integer groupSum;


    @ApiModelProperty(value = "党小组列表")
    @JsonProperty("org_groups")
    private List<OrgGroupForm> orgGroups;

}
