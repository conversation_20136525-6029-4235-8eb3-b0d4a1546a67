package com.goodsogood.ows.model.vo.datav.base

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import java.io.Serializable

/**
 * datav 进度条
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ProgressBar @JvmOverloads constructor(
    // 进度条的具体数值，图中百分比值为value值/sum值。
    var value: Long? = 0,
    // 进度条的总值，数据面板中的sum值内容优先级高于配置面板内的总值。
    var sum: Long? = 0,
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 8662265140266161408L
    }
}

