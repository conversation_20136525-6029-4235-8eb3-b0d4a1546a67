package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @program: ows-sas
 * @description: 用户得分配置
 * @author: Mr.<PERSON>
 * @create: 2020-11-23 14:17
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserScoreVo {

    private Integer name;

    private Integer phone;

    private Integer certNumber;

    private Integer  education;

    private Integer  joiningTime;

    private Integer  gender;

    private Integer   politicalType;

    private Integer    positionCode;

    private Integer    totalCount;

    private Integer    dateMonth;

    private Long    orgId;

}
