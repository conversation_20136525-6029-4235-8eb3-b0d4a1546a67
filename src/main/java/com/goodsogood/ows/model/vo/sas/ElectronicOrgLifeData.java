package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

import javax.persistence.Column;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 电子报表组织生活统计
 *
 * <AUTHOR>
 * @date 2019/6/12 16:29
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ElectronicOrgLifeData {

    @ApiModelProperty("活动类型id")
    @JsonProperty(value = "activity_type_id")
    @Column(name = "activity_type_id")
    private Integer activityTypeId;

    @ApiModelProperty("活动类型名称")
    @JsonProperty(value = "activity_type_name")
    @Column(name = "activity_type_name")
    private String activityTypeName;

    @ApiModelProperty("参与次数")
    @JsonProperty(value = "participate_num")
    private Integer participateNum;

    @ApiModelProperty(value = "总的组织数量")
    @JsonProperty(value = "all_org_count")
    private Integer allOrgCount;

    @ApiModelProperty(value = "开展活动的组织数量")
    @JsonProperty(value = "participate_org_count")
    private Integer participateOrgCount;

    @ApiModelProperty(value = "开展活动有党小组的组织数量")
    @JsonProperty(value = "has_party")
    private Integer hasParty;

    @ApiModelProperty(value = "没有党小组的组织数量")
    @JsonProperty(value = "not_has_party")
    private Integer notHasParty;

    @ApiModelProperty(value = "开展活动有支委会的组织数量")
    @JsonProperty(value = "participate_has_period")
    private Integer participateHasPeriod;

    @ApiModelProperty(value = "没有支委会的组织数量")
    @JsonProperty(value = "not_has_period")
    private Integer notHasPeriod;


    @ApiModelProperty(value = "开展活动次数组织所完成的比例")
    @JsonProperty(value = "proportion")
    private BigDecimal proportion;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty(value = "下级组织总数")
    @JsonProperty(value = "sub_org_count")
    private Integer subOrgCount;

    @ApiModelProperty(value = "开展活动总次数")
    @JsonProperty(value = "activity_all_count")
    private Integer activityAllCount;
}
