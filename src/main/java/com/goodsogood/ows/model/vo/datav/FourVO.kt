package com.goodsogood.ows.model.vo.datav

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.goodsogood.ows.model.vo.datav.base.CardFlop
import com.goodsogood.ows.model.vo.datav.base.GenericTitle
import java.io.Serializable

/**
 * <AUTHOR>
 * @date 2023/3/30
 * @description class FourVO 党业融合 -  四融合
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FourVO @JvmOverloads constructor(
    // 题目
    var genericTitle: List<GenericTitle>? = null,
    // 最高得分
    var cardFlopHeight: List<CardFlop>? = null,
    // 平均得分
    var cardFlopAvg: List<CardFlop>? = null,
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 3644147942288897093L
    }

}