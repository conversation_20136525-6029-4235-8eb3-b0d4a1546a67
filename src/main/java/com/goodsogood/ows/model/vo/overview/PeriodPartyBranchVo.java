package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PeriodPartyBranchVo extends OverviewBaseVo {

    @JsonProperty(value = "period_info")
    private String periodInfo;

    @JsonProperty(value = "list_leader")
    private List<LeaderInfo> list;


    @Data
    public static class LeaderInfo {
        @JsonProperty(value = "name")
        private String name;

        @JsonProperty(value = "position")
        private String position;
    }

}

