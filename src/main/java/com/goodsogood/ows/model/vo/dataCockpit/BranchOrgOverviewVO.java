package com.goodsogood.ows.model.vo.dataCockpit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 数据驾驶舱 - 组织概况(党委)
 *
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BranchOrgOverviewVO {

     @ApiModelProperty("党员数量")
     public Integer partyMemberNum = 0;

     @ApiModelProperty("组织届次开始时间")
     @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
     public Date periodStartTime;

     @ApiModelProperty("组织届次结束时间")
     @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
     public Date periodEndTime;

     @ApiModelProperty("书记")
     public String partyBranchSecretary;

     @ApiModelProperty("其他成员")
     public String otherMember;

}
