package com.goodsogood.ows.model.vo.tbc;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> ruoyu
 * @date : 2020/12/28
 * 组织视图
 */
@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class) //自动驼峰命名
@JsonInclude(JsonInclude.Include.NON_NULL)  //json 转实例忽略为空
@JsonIgnoreProperties(ignoreUnknown = true) //转json 忽略为空
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrgTabViewVo {

    //1.党委或者党总支 2.党支部
    private Integer type;

    //组织名称
    @JsonProperty(value = "org_name")
    private String orgName;

    //堡垒指数
    @JsonProperty(value = "fortress_index")
    private Double fortressIndex;

    //负数为下降 正数为上升 0 没有变化
    @JsonProperty(value = "rank_go")
    private Integer rankGo;

    //当月的
    @JsonProperty(value = "current_month_rank")
    private Integer currentRank;

    //支部平均值
    @JsonProperty(value = "avg_index")
    private Integer avgIndex;

    //三个指标块 第一块数据
    @JsonProperty(value = "list_index_total")
    private List<TbcBaseIndexForm> lisTbcBaseIndex;

    //党建指标历史  如果是党支部有此参数 第二块数据
    @JsonProperty(value = "list_index_history")
    List<List<Integer>> listIndexHistory;


    //党支部堡垒平均值 如果是党委有些参数 第二块数据
    @JsonProperty(value = "org_index_avg")
    TbcOrgIndexVo orgIndexVo;

    //党先锋指数 第三块数据
    @JsonProperty(value = "user_index_avg")
    TbcOrgUserAvgScoreVo userAvgScoreVo;

    /**
     * 支部堡垒平均值
     */
    @Data
    public static class TbcOrgIndexVo {
        //支部指数平均值
        @JsonProperty(value = "avg_org_index")
        private Integer avgOrgIndex;

        //支部指数平均值
        @JsonProperty(value = "list_org_info")
        private List<TbcOrgInfoVo> listOrgInfo;

        //支部的总数 用于前端小于3条 做判断
        @JsonProperty(value = "org_count")
        private Integer orgCount;

        @Data
        public static class TbcOrgInfoVo {
            @ApiModelProperty(value = "支部名称")
            @JsonProperty(value = "org_name")
            private String orgName;

            @ApiModelProperty(value = "支部id")
            @JsonProperty(value = "org_id")
            private Long orgId;

            @ApiModelProperty(value = "堡垒指数")
            @JsonProperty(value = "fortress_index")
            private Integer fortressIndex;

        }
    }

    /**
     * 支部堡垒平均值
     */
    @Data
    public static class TbcOrgUserAvgScoreVo {

        @ApiModelProperty(value = "本组织平均得分")
        @JsonProperty(value = "org_user_avg_score")
        private Integer orgUserAvgScore=0;

        @ApiModelProperty(value = "全市平均得分")
        @JsonProperty(value = "all_user_avg_score")
        private Integer allUserAvgScore=0;

        @ApiModelProperty(value = "支部用户数量")
        @JsonProperty(value = "org_user_count")
        private Integer orgUserCount=0;

        @ApiModelProperty(value = "用户记录")
        @JsonProperty(value = "list_user")
        private List<UserScore> listUser;


        @Data
        public static class UserScore{
            @ApiModelProperty(value = "用户id")
            @JsonProperty(value = "user_id")
            private Long userId;


            @ApiModelProperty(value = "用户姓名")
            @JsonProperty(value = "user_name")
            private String userName;


            @ApiModelProperty(value = "用户组织名称")
            @JsonProperty(value = "user_org_name")
            private String userOrgName;



            @ApiModelProperty(value = "先锋指数")
            @JsonProperty(value = "xiangfeng_index")
            private Double xiangFengIndex;
        }

    }

}
