package com.goodsogood.ows.model.vo.operation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName : VisitRateVo
 * <AUTHOR> tc
 * @Date: 2022/4/13 16:04
 * @Description : 人员累计访问天数
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VisitDaysVo {

    @JsonProperty(value = "region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;

    /**
     * 格式  YYYY-MM-DD
     */
    @JsonProperty(value = "stats_date")
    @ApiModelProperty("统计日期")
    private String statsDate;

    @JsonProperty(value = "user_id")
    @ApiModelProperty("用户编号")
    private Long userId;

    @JsonProperty(value = "user_name")
    @ApiModelProperty("用户名称")
    private String userName;

    @JsonProperty(value = "org_id")
    @ApiModelProperty("组织编号")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    @JsonProperty(value = "org_short_name")
    @ApiModelProperty("组织简称")
    private String shortName;

    @JsonProperty(value = "visit_days")
    @ApiModelProperty("累计访问天数")
    public Integer visitDays = 0;

    @JsonProperty(value = "visit_time")
    @ApiModelProperty("当日登录时间")
    private LocalDateTime visitTime;
}
