package com.goodsogood.ows.model.vo.tbcFusion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 党员"党建+业务"工作情况
 * <AUTHOR>
 * @date 2021/12/03
 */
@Data
//@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TbcPartyMemberWorkForm {
    @ApiModelProperty("业务指标")
    private List<Integer> d1=Arrays.asList(0,0,0,0);

    @ApiModelProperty("党建指标")
    private List<Integer> d2=Arrays.asList(0,0,0,0);

    @ApiModelProperty("创新指标")
    private List<Integer> d3=Arrays.asList(0,0,0,0);

    @ApiModelProperty("先锋指数")
    private List<Integer> d4=Arrays.asList(0,0,0,0);

    @ApiModelProperty("颜色指示标题")
    @JsonProperty(value = "sData")
    private List<String> sData;

    @ApiModelProperty("第一行横坐标标题")
    @JsonProperty(value = "xData")
    private List<String> xData;
}
