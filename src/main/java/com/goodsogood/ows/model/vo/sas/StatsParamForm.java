package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 党费交纳统计参数类
 *
 * @auther Administrator tc
 * @date 2018/10/19
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StatsParamForm {

    @JsonProperty(value = "parent_org_id")
    @ApiModelProperty("党支部所属机关单位编号")
    private Long parentOrgId;

    @JsonProperty(value = "org_id")
    @ApiModelProperty("组织编号")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    /**
     * 是否考核单位  0 否  1是
     */
    @JsonProperty(value = "examine")
    @ApiModelProperty("是否考核单位")
    private Integer examine;

    /**
     * 查询开始时间  格式:YYYY-MM
     */
    @JsonProperty(value = "start_date")
    @ApiModelProperty("查询开始时间")
    @NotBlank(message = "{NotBlank.stats.startDate}")
    private String startDate;

    /**
     * 查询结束时间  格式:YYYY-MM
     */
    @JsonProperty(value = "end_date")
    @ApiModelProperty("查询结束时间")
    @NotBlank(message = "{NotBlank.stats.endDate}")
    private String endDate;

    /**
     *  是否统计人次  0 否 1是   默认 1
     */
    @JsonProperty(value = "person_time")
    @ApiModelProperty("是否统计人次")
    private Integer personTime;
}
