package com.goodsogood.ows.model.vo.ql

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.goodsogood.ows.model.antd.AntDesignType
import org.slf4j.LoggerFactory

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class QueryForm @JvmOverloads constructor(
    // 查询条件的对应的列名，多个列用逗号分割
    var param: String? = null,
    // 查询控件类型 Input、Select、RangePicker
    var type: String? = null,
    // 查询条件的值
    var values: List<String>? = null,
) {
//    private val log = LoggerFactory.getLogger(this::class.java)

    companion object {
        fun QueryForm.checkValues(): Boolean {
            values?.let {
                val myType = enumValues<AntDesignType>().find { typeValue -> typeValue.key == type } ?: return false
                return when (myType) {
                    AntDesignType.Input -> it.size == 1
                    AntDesignType.Select -> it.isNotEmpty()
                    AntDesignType.RangePicker -> it.size == 2
                }
            }
            return false
        }

        fun QueryForm.createCondition(): Map<String, Any> {
            val myType = enumValues<AntDesignType>().find { typeValue -> typeValue.key == type } ?: return mapOf()
            val params = param?.split(",") ?: return mapOf()
            return mutableMapOf<String, Any>().also {
                params.forEach { p ->
                    if (p.isNotBlank()) {
                        when (myType) {
                            AntDesignType.Input -> it[p] = values!![0]// mapOf(p to values!![0])
                            AntDesignType.Select -> it[p] = values!!
                            AntDesignType.RangePicker -> it[p] = Pair(values!![0], values!![1])
                        }
                    }
                }
            }
        }
    }
}
