package com.goodsogood.ows.model.vo.pbm;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;


@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PbmTargetVO {

    @ApiModelProperty("人员姓名")
    @Excel(name = "姓名（必填）")
    private String userName;


    @ApiModelProperty("手机号")
    @Excel(name = "手机号（必填）")
    private String phone;


    @ApiModelProperty("所在单位")
    private String unit;

    @ApiModelProperty("时间")
    @Excel(name = "年-月（必填）")
    private String time;

    @ApiModelProperty("所在部门")
    private String department;


    @ApiModelProperty("是否党员 1-是 2-否")
    private Integer isPartyMember;


    @ApiModelProperty("所在支部")
    private String branch;


    @ApiModelProperty("月度绩效得分")
    @Excel(name = "月度绩效得分（必填）")
    private Double score;

    @ApiModelProperty("备注")
    @Excel(name = "备注")
    private String remark;


    @ApiModelProperty("提示信息")
    private String tips;
}
