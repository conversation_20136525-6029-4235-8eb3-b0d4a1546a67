package com.goodsogood.ows.model.vo.operation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName : ClickMenuVo
 * <AUTHOR> tc
 * @Date: 2022/4/13 16:33
 * @Description : 功能点击
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClickMenuVo {

    @JsonProperty(value = "region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;

    /**
     * 格式  YYYY-MM-DD
     */
    @JsonProperty(value = "stats_date")
    @ApiModelProperty("统计日期")
    private String statsDate;

    @JsonProperty(value = "menu_id")
    @ApiModelProperty("菜单编号")
    private String menuId;

    @JsonProperty(value = "click_num")
    @ApiModelProperty("点击次数")
    private Long clickNum=0L;

    @JsonProperty(value = "last_click_time")
    @ApiModelProperty("当日最后一次点击时间")
    private LocalDateTime lastClickTime;
}
