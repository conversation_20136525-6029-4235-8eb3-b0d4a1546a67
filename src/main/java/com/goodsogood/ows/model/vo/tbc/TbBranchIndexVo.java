package com.goodsogood.ows.model.vo.tbc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class) //自动驼峰命名
@JsonInclude(JsonInclude.Include.NON_NULL)  //json 转实例忽略为空
@JsonIgnoreProperties(ignoreUnknown = true) //转json 忽略为空
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TbBranchIndexVo {

    /**
     * 党建计算占比
     */
    private Double partyIndex;


    /**
     * 业务计算占比
     */
    private Double businessIndex;


    /**
     * 创新指标占比
     */
    private Double InnovationIndex;



    /**
     * 党员先峰指数
     */
    private Double xiangFengIndex;


    /**
     * 支部堡垒指数
     */
    private Double fortressIndex;


    /**
     * 统计的年月
     */
    private String staMonth;
}
