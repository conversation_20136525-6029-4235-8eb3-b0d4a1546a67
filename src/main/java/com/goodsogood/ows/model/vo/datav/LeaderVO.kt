package com.goodsogood.ows.model.vo.datav

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.goodsogood.ows.helper.Json.DoubleKeepZeroSerializer
import com.goodsogood.ows.model.vo.datav.base.CardFlop

/**
 * <AUTHOR>
 * @date 2023/3/13
 * @description 党务干部对象
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class LeaderVO @JvmOverloads constructor(
    // 专职和兼职数据的进度条表格
    var fullParty: List<FullOrPartyVO>? = null,
    // 书记、副书记、委员 三个翻牌器
    var position: Array<List<CardFlop>?>? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is LeaderVO) return false

        if (fullParty != other.fullParty) return false
        if (position != null) {
            if (other.position == null) return false
            if (!position.contentEquals(other.position)) return false
        } else if (other.position != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = fullParty?.hashCode() ?: 0
        result = 31 * result + (position?.contentHashCode() ?: 0)
        return result
    }
}

/**
 * 专职或兼职数据图形（进度条表格）
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FullOrPartyVO @JvmOverloads constructor(
    @JsonProperty("city")
    var key: String? = null,
    @JsonProperty("proportion")
    @JsonSerialize(using = DoubleKeepZeroSerializer::class)
    var value: Double? = null,
)