package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OverviewBrandVo extends OverviewBaseVo{

    /**
     * 品牌的数量
     */
    @JsonProperty(value = "branch")
    private Long branch;

    /**
     * 阵地数量
     */
    @JsonProperty(value = "front")
    private Long front;

    /**
     * 组织名称
     */
    @JsonProperty(value = "org_name")
    private String  orgName;

    /**
     * 品牌LOGO
     */
    @JsonProperty(value = "branch_logo")
    private String  branchLogo;

    /**
     * 品牌名称
     */
    @JsonProperty(value = "branch_name")
    private String  branchName;

    /**
     * 品牌信息
     */
    @JsonProperty(value = "branch_infos")
    private List<BranchInfo> branchInfos;


    @Data
    public static class BranchInfo {
        @JsonProperty(value = "name")
        private String name;

        @JsonProperty(value = "count")
        private String count;
    }


}
