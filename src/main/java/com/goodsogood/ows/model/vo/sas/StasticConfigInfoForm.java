package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotBlank;

import java.util.List;

import javax.persistence.GeneratedValue;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 党务看板统计
 *
 * <AUTHOR>
 * @date 2019/4/19 11:12
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StasticConfigInfoForm {

    private static final long serialVersionUID = -5270513824712876596L;
    @ApiModelProperty(value = "设置信息记录ID")
    @JsonProperty("config_info_id")
    @GeneratedValue(generator = "JDBC")
    private Long configInfoId;

    @ApiModelProperty(value = "统计类型1.党支部组织生活统计 2.领导干部双重组织生活统计 3.党费交纳完成情况统计")
    @JsonProperty("statistical_type")
    @NotNull(message = "{NotNull.sas.statisticalType}")
    private Integer statisticalType;

    @ApiModelProperty(value = "显示组织类型(存入的组织类型类型id,多个以逗号分隔）")
    @NotBlank(message = "{NotNull.sas.showOrganizationTypes}")
    @JsonProperty("show_organization_types")
    private String showOrganizationTypes;

    @ApiModelProperty(value = "全部组织类型(存入的组织类型类型id,多个以逗号分隔）")
    @JsonProperty("all_organization_types")
    private String allOrganizationTypes;

    @ApiModelProperty(value = "显示活动类型(存入的活动类型类型id,多个以逗号分隔）")
    @JsonProperty("show_activity_types")
    private String showActivityTypes;

    @ApiModelProperty(value = "全部活动类型(存入的活动类型类型id,多个以逗号分隔）")
    @JsonProperty("all_activity_types")
    private String allActivityTypes;

    @ApiModelProperty(value = "组织类型名称")
    @JsonProperty("show_organization_type_list")
    private List<OrganizationTypeForm> showOrganizationTypeList;

    @ApiModelProperty(value = "活动类型名称")
    @JsonProperty("show_activity_type_list")
    private List<ActivityTypeForm> showActivityTypeList;

}
