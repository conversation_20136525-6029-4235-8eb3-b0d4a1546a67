package com.goodsogood.ows.model.vo.activity;

import lombok.Data;

/**
 * @program: ows-sas
 * @description: ${description}
 * @author: Mr.<PERSON>
 * @create: 2019-11-22 16:47
 **/
@Data
public class ActivityOrgElectronicReport {
    /**
     * 活动名称
     */
     String typeName;
    /**
     * 活动条数
     */
     Integer participantCount;

    public ActivityOrgElectronicReport(String typeName, Integer participantCount) {
        this.typeName=typeName;
        this.participantCount=participantCount;
    }

    public ActivityOrgElectronicReport() {

    }

    public ActivityOrgElectronicReport(String typeName) {
        this.typeName=typeName;
    }
}
