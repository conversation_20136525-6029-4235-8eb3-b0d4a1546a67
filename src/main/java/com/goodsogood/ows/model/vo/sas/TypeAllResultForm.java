package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.sas.TypeEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Description: 活动类型</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 10:27
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TypeAllResultForm {

    @ApiModelProperty("类别id")
    @JsonProperty(value = "category_id")
    private Long categoryId;

    @ApiModelProperty("类别")
    @JsonProperty(value = "category")
    private String category;

    @ApiModelProperty("类型")
    @JsonProperty(value = "types")
    private List<TypeEntity> types;
}
