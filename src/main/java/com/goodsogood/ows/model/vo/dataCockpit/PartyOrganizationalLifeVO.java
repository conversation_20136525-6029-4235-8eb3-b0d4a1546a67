package com.goodsogood.ows.model.vo.dataCockpit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据驾驶舱 - 组织生活(党委)
 *
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PartyOrganizationalLifeVO {

     @ApiModelProperty("本年度开展活动总次数")
     public Integer totalNum = 0;

     @ApiModelProperty("支部党员大会次数")
     public Integer partyCongressNum = 0;

     @ApiModelProperty("支委会次数")
     public Integer branchCommitteeNum = 0;

     @ApiModelProperty("党小组会次数")
     public Integer partyGroupNum = 0;

     @ApiModelProperty("党课次数")
     public Integer partyLectureNum = 0;

     @ApiModelProperty("主题党日次数")
     public Integer themePartyDayNum = 0;

     @ApiModelProperty("中心组学习次数")
     public Integer centralGroupLearningNum = 0;

     @ApiModelProperty("支部党员大会占比")
     public Double partyCongressProportion;

     @ApiModelProperty("支委会占比")
     public Double branchCommitteeProportion;

     @ApiModelProperty("党小组会占比")
     public Double partyGroupProportion;

     @ApiModelProperty("党课占比")
     public Double partyLectureProportion;

     @ApiModelProperty("主题党日占比")
     public Double themePartyDayProportion;

     @ApiModelProperty("中心组学习占比")
     public Double centralGroupLearningProportion;
}
