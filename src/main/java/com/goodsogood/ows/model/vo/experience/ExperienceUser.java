package com.goodsogood.ows.model.vo.experience;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ExperienceUser {

    @ApiModelProperty("user_id")
    @JsonProperty(value = "user_id")
    private Long userId;

    @JsonProperty("name")
    @ApiModelProperty(value = "name")
    private String name;


    @JsonProperty("avatar")
    @ApiModelProperty(value = "头像")
    private String avatar;

    @JsonProperty("t_user中的头像")
    @ApiModelProperty(value = "头像")
    private String avatarUser;


    @JsonProperty("organization_id")
    @ApiModelProperty(value = "organizationId")
    private Long organizationId;



    @JsonProperty("org_name")
    @ApiModelProperty(value = "组织名称")
    private String orgName;


    @JsonProperty("org_level")
    @ApiModelProperty(value = "组织层次关系")
    private String orgLevel;


    @JsonProperty("position")
    @ApiModelProperty(value = "职务")
    private String position;
}
