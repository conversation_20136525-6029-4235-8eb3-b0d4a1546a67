package com.goodsogood.ows.model.vo.report;

import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;

/**
 * @program: ows-sas
 * @description: ${description}
 * @author: Mr<PERSON><PERSON><PERSON>
 * @create: 2019-11-22 10:35
 **/

public class DemoTest {
    public static void main(String[] args) throws Exception {
        // 创建任务 T2 的 FutureTask
        FutureTask<String> ft3= new FutureTask<>(new T3Task());
        // 创建任务 T2 的 FutureTask
        FutureTask<String> ft2= new FutureTask<>(new T2Task(ft3));
        // 创建任务 T1 的 FutureTask
        FutureTask<String> ft1= new FutureTask<>(new T1Task(ft2));
        // 线程 T2 执行任务 ft2
        Thread T3 = new Thread(ft3);
        T3.start();
        // 线程 T1 执行任务 ft1
        Thread T1 = new Thread(ft1);
        T1.start();
        // 线程 T2 执行任务 ft2
        Thread T2 = new Thread(ft2);
        T2.start();
        // 等待线程 T1 执行结果
        System.out.println(ft1.get());
    }

    // T1Task 需要执行的任务：
    // 洗水壶、烧开水、泡茶
    public static class T1Task implements Callable<String> {
        FutureTask<String> ft2;

        // T1 任务需要 T2 任务的 FutureTask
        T1Task(FutureTask<String> ft2) {
            this.ft2 = ft2;
        }

        @Override
        public String call() throws Exception {
            System.out.println("T1: 洗水壶...");
            System.out.println("T1: 烧开水...");
            // 获取 T2 线程的茶叶
            String tf = ft2.get();
            System.out.println("T1: 拿到茶叶:" + tf);
            System.out.println("T1: 泡茶...");
            return " 上茶:" + tf;
        }
    }

    // T2Task 需要执行的任务:
    // 洗茶壶、洗茶杯、拿茶叶
    public static class T2Task implements Callable<String> {
        FutureTask<String> ft3;

        // T1 任务需要 T2 任务的 FutureTask
        T2Task(FutureTask<String> ft3) {
            this.ft3 = ft3;
        }

        @Override
       public String call() throws Exception {
            // 获取 T2 线程的茶叶
            String tf = ft3.get();
            System.out.println("T2: 洗茶壶...");
            System.out.println("T2: 洗茶杯...");
            System.out.println("T2: 拿茶叶...");
            return " 龙井 ";
        }
    }


    // T2Task 需要执行的任务:
    // 等待朋友的到来
    public static class T3Task implements Callable<String> {
        @Override
        public String call() {
            System.out.println("等待朋友的到来...");
            return "朋友已经到来";
        }
    }

}
