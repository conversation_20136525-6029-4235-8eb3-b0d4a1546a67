package com.goodsogood.ows.model.vo.tbc;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> ruoyu
 * @date : 2020/12/28
 * 烟草组织生活实现类
 */
@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class) //自动驼峰命名
@JsonInclude(JsonInclude.Include.NON_NULL)  //json 转实例忽略为空
@JsonIgnoreProperties(ignoreUnknown = true) //转json 忽略为空
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrganizationLifeForm {

    @ApiModelProperty(value = "党建动态")
    private List<OrganizationLifePartyInfoForm> partyInfo;

    @ApiModelProperty(value = "民主评议党员情况")
    private List<OrganizationLifeCommentForm> userComment;

    @ApiModelProperty(value = "述职评议情况")
    private List<OrganizationLifeCommentForm> orgComment;

    @ApiModelProperty(value = "组织生活开展情况")
    private OrganizationLifeMeetingActivityForm meetingActivity;

    @ApiModelProperty(value = "领导成员双重组织生活本季度")
    private List<OrganizationLifeLeaderMeetingActivityForm> leaderMeetingActivity;

    @ApiModelProperty(value = "三会一课完成情况")
    private List<OrganizationLifeCenterMeetingActivityForm> centerMeetingActivity;

    @ApiModelProperty(value = "主题党日完成情况")
    private List<OrganizationLifeCenterPartyActivityForm> centerPartyActivity;


    /**
     * 党建动态
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class OrganizationLifePartyInfoForm {
        @ApiModelProperty(value = "新闻标题名称")
        private String title;
    }

    /**
     * 民主评议党员情况/述职评议情况
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class OrganizationLifeCommentForm {
        @ApiModelProperty(value = "评议级别")
        private String name;

        @ApiModelProperty(value = "数量")
        private Integer nums;
    }

    /**
     * 组织生活开展情况
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class OrganizationLifeMeetingActivityForm {
        @ApiModelProperty(value = "本年共开展支部活动次数")
        private Integer total;

        @ApiModelProperty(value = "会议开展类型数据")
        private List<OrganizationLifeInnerMeetingActivityForm> list;
    }

    /**
     * 组织生活开展情况:会议开展类型数据
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class OrganizationLifeInnerMeetingActivityForm {

        @ApiModelProperty(value = "类型Id")
        private Integer typeId;

        @ApiModelProperty(value = "会议类型")
        private String typeName;

        @ApiModelProperty(value = "已开展会议次数")
        private Integer alreadyMeetingNum;

        @ApiModelProperty(value = "应完成组织数")
        private Integer shouldDoneNum;

        @ApiModelProperty(value = "已完成组织数")
        private Integer alreadyDoneNum;

        @ApiModelProperty(value = "未完成组织数")
        private Integer undoneNum;

        @ApiModelProperty(value = "未完成组织列表")
        private List<OrganizationLifeInnerUndoenMeetingActivityForm> undoneInfo;
    }

    /**
     * 组织生活开展情况:未完成组织列表
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class OrganizationLifeInnerUndoenMeetingActivityForm {
        @ApiModelProperty(value = "组织id")
        private Long orgId;

        @ApiModelProperty(value = "组织名称")
        private String orgName;

        @ApiModelProperty(value = "完成状态")
        private Integer completeStatus;
    }

    /**
     * 领导成员双重组织生活本季度
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class OrganizationLifeLeaderMeetingActivityForm {
        @JsonIgnore
        @ApiModelProperty(value = "会议id")
        private Long meetingId;

        @ApiModelProperty(value = "用户id")
        private Long userId;

        @ApiModelProperty(value = "用户名称")
        private String name;

        @ApiModelProperty(value = "组织id")
        private Long orgId;

        @ApiModelProperty(value = "组织名称")
        private String orgName;

        @ApiModelProperty(value = "参与次数")
        private Integer num;
    }

    /**
     * 三会一课完成情况
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class OrganizationLifeCenterMeetingActivityForm {
        @ApiModelProperty(value = "是否完成")
        private Integer isDone;

        @ApiModelProperty(value = "组织id")
        private Long orgId;

        @ApiModelProperty(value = "组织名称")
        private String orgName;

        @ApiModelProperty(value = "完成情况")
        private List<MeetingDoneInfo> info;
    }

    /**
     * 三会一课组织完成详情
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class MeetingDoneInfo {
        @JsonIgnore
        @ApiModelProperty(value = "组织id")
        private Long organizationId;

        @ApiModelProperty(value = "任务名称")
        private String taskName;

        @ApiModelProperty(value = "统计时间范围 开始时间")
        private String startTime;

        @ApiModelProperty(value = "统计时间范围 结束时间")
        private String endTime;

        @ApiModelProperty(value = "完成状态 1:已完成  2:未完成")
        private Integer status;
    }

    /**
     * 主题党日完成情况
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class OrganizationLifeCenterPartyActivityForm {
        @ApiModelProperty(value = "组织id")
        private Long orgId;

        @ApiModelProperty(value = "组织名称")
        private String orgName;

        @ApiModelProperty(value = "是否完成")
        private Integer isDone;
    }
}
