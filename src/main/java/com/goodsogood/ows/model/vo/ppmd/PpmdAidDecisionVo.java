package com.goodsogood.ows.model.vo.ppmd;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 辅助决策党费模块
 *
 * <AUTHOR> tc
 * @date 2020/11/9
 */
@Data
public class PpmdAidDecisionVo {
    @ApiModelProperty("交费完成数量")
    private Integer finish;
    @ApiModelProperty("交费未完成数量")
    private Integer unfinish;
    @ApiModelProperty("应交人数")
    private Integer numShould;
    @ApiModelProperty("已交人数")
    private Integer numAlready;
    @ApiModelProperty("应交金额")
    private Double payShould;
    @ApiModelProperty("已交金额")
    private Double payAlready;
    @ApiModelProperty("交纳日期")
    private String payDay;
    @ApiModelProperty("本党委排名")
    private Integer rank;
    @ApiModelProperty("本年总共交纳党费")
    /**
     * 单位 ：元，为了避免科学计数法，使用字符串类型
     */
    private String curYear;
}
