package com.goodsogood.ows.model.vo.datav.base

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import java.io.Serializable

/**
 * datav 通用标题
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class GenericTitle @JvmOverloads constructor(
    // （可选）标题的内容，配置后会覆盖标题名配置项的内容。为空时，会读取标题名配置项的内容进行显示。
    var value: String? = "",
    // （可选）标题跳转的超链接地址，配置后会覆盖超链接配置项的URL。
    var url: String? = null,
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 6927467930592456403L
    }
}
