package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 组织人员列表查询
 *
 * <AUTHOR>
 * @date 2018-06-11
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrgUserQueryForm {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("组织ID")
    @NotNull(message = "{NotNull.user.org.id}")
    private Long orgId;

    @ApiModelProperty("姓名")
    @Length(max = 30,message = "{Length.org.user.name}")
    private String name;

    @ApiModelProperty("证件号码")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$|^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{2}[0-9Xx]$",message = "{Pattern.login.certNumber}")
    private String certNumber;

    @ApiModelProperty("电话")
    @Pattern(regexp = "^1\\d{10}$",message = "{Pattern.user.phone}")
    private String phone;

    @ApiModelProperty("性别")
    private String gender;//1-男 2-女 3-未知性别   //多个用逗号分隔

    @ApiModelProperty("政治面貌")
    private String politicalType; //多个用逗号分隔

    @ApiModelProperty("技术等级")
    private String jobGrade; //多个用逗号分隔

    @ApiModelProperty("职务")
    @Length(max = 30,message = "{Length.org.user.position}")
    private String position;

    @ApiModelProperty("党组织")
    private String communist;  //多个用逗号分隔

    @ApiModelProperty("团组织")
    private String youthLeague;  //多个用逗号分隔

    @ApiModelProperty("工会组织")
    private String unionMember;  //多个用逗号分隔

    @ApiModelProperty("妇女组织")
    private String womenLeague; //多个用逗号分隔

    @ApiModelProperty("证件类别")
    private String certType;   //多个用逗号分隔

    @ApiModelProperty("户籍类型")
    private String censusType; //多个用逗号分隔

    @ApiModelProperty("学历")
    private String education; //多个用逗号分隔

    @ApiModelProperty("籍贯省/直辖市")
    private Integer nativeProvince;

    @ApiModelProperty("籍贯市/区")
    private Integer nativeCity;

    @ApiModelProperty("民族")
    private String ethnic; //多个用逗号分隔

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("是否包含下级")
    private Integer onlyCurrentOrg;

    @ApiModelProperty("页码")
    private Integer page = 1;

    @ApiModelProperty("每页数量")
    private Integer pagesize = 10;

}
