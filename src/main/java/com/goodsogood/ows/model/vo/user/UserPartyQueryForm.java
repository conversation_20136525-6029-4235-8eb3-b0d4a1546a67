package com.goodsogood.ows.model.vo.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.common.bean.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName UserPartyQueryForm
 * @description
 * @date 2019-07-23 9:47
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserPartyQueryForm {

    @ApiModelProperty("组织ID")
    @JsonProperty("org_id")
    private Long orgId;

    @ApiModelProperty("是否包含离退休组织 1:包含（默认）；2：不包含；3：仅包含")
    @JsonProperty("include_retire")
    private Integer includeRetire = 1;

    @ApiModelProperty("是否包含下级 1:包含（默认）；2：不包含；3：仅包含")
    @JsonProperty("include_level")
    private Integer includeLevel = 1;

    @ApiModelProperty("是否包含所有人 1-是 2-否，默认2查询所有党员")
    @JsonProperty("include_all")
    private Integer includeAll = 2;

    @ApiModelProperty("是否包含删除状态信息 1-是 2-否，默认1")
    @JsonProperty("filter_status")
    private Integer filterStatus = 1;

    private  PageBean page;

}
