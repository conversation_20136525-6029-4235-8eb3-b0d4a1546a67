package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 党组织生活统计查询条件
 *
 * <AUTHOR>
 * @date 2019/4/23 14:41
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvalIdRequestForm {

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    @NotNull(message = "{NotNull.sas.org.id}")
    private Long orgId;

    @ApiModelProperty("名称")
    @JsonProperty(value = "name")
    private String name;

    @ApiModelProperty("年份")
    @JsonProperty(value = "year")
    @NotNull(message = "{NotNull.sas.year}")
    private Integer year;

    @ApiModelProperty("月份")
    @JsonProperty(value = "time")
    private Integer time;

    @ApiModelProperty("2 非离退休")
    @JsonProperty(value = "is_retire")
    private Integer isRetire;

}
