package com.goodsogood.ows.model.vo.experience;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: mengting
 * @Date: 2022/4/21 10:54
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrgTimesVO {
    private Integer yearTotalTimes = 0;//本年累计体检次数
    private Integer yearSystemTimes ;//当年系统测评次数、报告数、锤炼计划分数
    private Integer yearSelfTimes;//当年手动测评次数
    private Integer yearSelfPersons = 0;//本年手动测评人数
    private Integer yearPlanTimes;//锤炼计划分数
    private Integer yearSystemPersons;//系统测评人数-年


    private Integer monthTimes = 0;//本月体检次数
    private Integer monthSelfTimes = 0;//本月自主体检次数
    private Integer monthSystemTimes;//指定月份系统测评次数、报告数、锤炼计划分数
    private  Integer monthSelfPersons = 0;//本月手动测评人数
    private Integer monthPlanTimes;//月度锤炼计划分数
    private Integer monthSystemPersons;//系统测评人数-月



}
