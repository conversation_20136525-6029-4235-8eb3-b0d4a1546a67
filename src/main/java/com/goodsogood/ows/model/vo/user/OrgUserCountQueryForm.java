package com.goodsogood.ows.model.vo.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OrgUserCountQueryForm
 * @description
 * @date 2019-07-02 10:39
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgUserCountQueryForm {

    @JsonProperty(value = "id_list")
    private List<Long> idList;

    @JsonProperty(value = "is_employee")
    private Integer isEmployee = 1;

    @JsonProperty(value = "political_type")
    private String politicalType = "1,5,17,18";
}
