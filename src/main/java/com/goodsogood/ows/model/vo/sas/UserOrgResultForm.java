package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName UserOrgResultForm
 * @description
 * @date 2018-10-25 10:53
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserOrgResultForm {

    @ApiModelProperty("用户ID")
    @JsonProperty("user_id")
    private Long userId;

    @ApiModelProperty("用户姓名")
    @JsonProperty("name")
    private String name;

    @ApiModelProperty("组织ID")
    @JsonProperty("org_id")
    private Long orgId;

    @ApiModelProperty("组织名称")
    @JsonProperty("org_name")
    private String orgName;

    @ApiModelProperty("组织类型")
    @JsonProperty("org_type")
    private Integer orgType;

    @ApiModelProperty(value = "脱敏证件号码")
    @JsonProperty("cert_number")
    private String certNumber;

    @ApiModelProperty(value = "脱敏证件号码")
    @JsonProperty("cert_number_secret")
    private String certNumberSecret;

    @ApiModelProperty("脱敏用户手机号")
    @JsonProperty("phone_secret")
    private String phoneSecret;

    @ApiModelProperty(value = "脱敏用户手机号")
    @JsonProperty("phone")
    private String phone;

    @ApiModelProperty(value = "群团职务")
    @JsonProperty("position")
    private String position;

    @ApiModelProperty("党组织")
    @JsonProperty("communist")
    private Integer communist;

    @ApiModelProperty("团组织")
    @JsonProperty("youth_league")
    private Integer youthLeague;

    @ApiModelProperty("工会组织")
    @JsonProperty("union_member")
    private Integer unionMember;

    @ApiModelProperty("妇女组织")
    @JsonProperty("women_league")
    private Integer womenLeague;

    @ApiModelProperty(value = "是否显示移交管理员按钮")
    @JsonProperty("appoint_able")
    private Integer appointAble;

    @ApiModelProperty(value = "政治面貌")
    @JsonProperty("political")
    private String political;

    @ApiModelProperty(value = "标签列表，用逗号隔开")
    @JsonProperty("tag")
    private String tag;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "学历")
    private String education;

    @ApiModelProperty(value = "民族")
    private String ethnic;

    @ApiModelProperty(value = "入党时间")
    private String joiningTime;

    @ApiModelProperty(value = "转正时间")
    private String becomeTime;

    @ApiModelProperty(value = "籍贯")
    private String nativePlace;
}
