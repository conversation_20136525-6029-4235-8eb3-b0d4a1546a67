package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.goodsogood.ows.model.db.user.OrgUserActivityForm;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.data.annotation.Id;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * @program: ows-sas
 * @description: ${description}
 * @author: Mr.<PERSON>
 * @create: 2019-05-13 11:45
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgGroupEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "org_group_id")
    private Long orgGroupId;

    //组织id
    @Column(name = "org_id")
    private Long orgId;

    //党小组名称
    @Column(name = "org_group_name")
    private String orgGroupName;

    //删除标志（1-表示未删除，2-已删除）
    @Column(name = "is_delete")
    private Integer isDelete;

    //创建时间（创建时候前端选择的时间） yyyy-MM-dd
    @Column(name = "create_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;

    @Transient
    private List<OrgUserActivityForm> users;
}
