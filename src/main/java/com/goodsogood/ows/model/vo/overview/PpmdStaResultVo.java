package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PpmdStaResultVo {

    @JsonProperty(value = "pay_date")
    @ApiModelProperty("支付时间")
    private Integer payDate;



    @JsonProperty(value = "pay_already")
    @ApiModelProperty("已经付款总金额")
    private Double payAlready;

    public Integer getPayDate() {
        return payDate;
    }

    public void setPayDate(Integer payDate) {
        this.payDate = payDate;
    }

    public Double getPayAlready() {
        return payAlready;
    }

    public void setPayAlready(Double payAlready) {
        this.payAlready = payAlready;
    }
}
