package com.goodsogood.ows.model.vo.tbcFusion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TbcPartyIndexForm {

    @ApiModelProperty("党支部orgId")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty("党建指标")
    @JsonProperty(value = "party_index")
    private Double partyIndex=0.0;


    @ApiModelProperty("业务指标")
    @JsonProperty(value = "business_index")
    private Double businessIndex=0.0;


    @ApiModelProperty("创新指标")
    @JsonProperty(value = "innovation_index")
    private Double innovationIndex=0.0;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;


    @ApiModelProperty("堡垒指数")
    @JsonProperty(value = "fortress_index")
    private Double fortressIndex=0.0;

    @ApiModelProperty("党员先锋指数")
    @JsonProperty(value = "party_members_number")
    private Double partyMembersNumber=0.0;

    @ApiModelProperty("对数")
    @JsonProperty(value = "logarithm")
    private Double logarithm=0.0;

    @ApiModelProperty("拟合度")
    @JsonProperty(value = "like_index")
    private Double likeIndex;
}
