package com.goodsogood.ows.model.vo.tbcFusion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 创新情况统计
 * <AUTHOR>
 * @date 2021/12/03
 */
@Data
//@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TbcInnovateForm {
    @ApiModelProperty("字符标题")
    private String name;

    @ApiModelProperty("字体大小")
    private String value;

    @ApiModelProperty("字体颜色")
    private Integer colorField;
}
