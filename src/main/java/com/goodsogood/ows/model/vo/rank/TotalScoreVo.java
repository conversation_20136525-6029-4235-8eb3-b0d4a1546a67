package com.goodsogood.ows.model.vo.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Create by FuXiao on 2020/11/19
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TotalScoreVo {
    @JsonProperty(value = "id")
    @ApiModelProperty(name = "用户id或组织id")
    private Long id;

    @JsonProperty(value = "score")
    @ApiModelProperty(name = "总分")
    private Integer score;
}
