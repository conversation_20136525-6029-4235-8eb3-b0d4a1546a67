package com.goodsogood.ows.model.vo.news;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/10/12
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class NewsImgForm {

    @ApiModelProperty("新闻图片id")
    @JsonProperty("news_img_id")
    private Long newsImgId;

    @ApiModelProperty("新闻id")
    @JsonProperty("news_id")
    private Long newsId;

    @ApiModelProperty("图片url")
    private String url;

    @ApiModelProperty("展示类型： 2 单图 ，3 三图，4巨幅，5聚焦图")
    private Integer type;
}
