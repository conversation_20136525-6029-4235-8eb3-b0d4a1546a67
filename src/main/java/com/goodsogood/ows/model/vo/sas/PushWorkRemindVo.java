package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 高级管理员 本月工作提醒
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PushWorkRemindVo {

    /**
     * 接受消息人员ID
     */
    private Long userId;

    /**
     * 接受消息的管理信息
     */
    private List<Long> listUserIds;

    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 区县ID
     */
    private Long regionId;

    /**
     * 接受消息人员名称
     */
    private String userName;

    /**
     * 组织简称
     */
    private String shortName;

    /**
     * 当前年月
     */
    private String date;

    /**
     * 党费未交齐的支部/党员数量
     */
    private Integer oweNum;

    /**
     * 组织换届数量
     */
    private Integer periodNum;

    /**
     * 组织生活开展完成支部数量
     */
    private List<OrgLiftRemindVo> remindList;

    /**
     * 未完成组织生活类型
     */
    private List<String> unFinishType;

    /**
     * 推送类型  1-高级管理员, 2-一般管理员
     */
    private Integer pushType;

}
