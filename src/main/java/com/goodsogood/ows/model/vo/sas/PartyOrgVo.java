package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 上级党委
 * <AUTHOR>
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PartyOrgVo {

    @JsonProperty(value = "org_id")
    @ApiModelProperty("组织编号")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    @JsonProperty(value = "party_id")
    @ApiModelProperty("上级党委编号")
    private Long partyId;

    @JsonProperty(value = "party_name")
    @ApiModelProperty("上级党委名称")
    private String partyName;

    @ApiModelProperty("上级党委组织简称")
    @JsonProperty(value = "party_short_name")
    private String partyShortName;

    @ApiModelProperty("所属行政单位ID")
    @JsonProperty("owner_id")
    private Long ownerId;
}
