package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName PartyResultForm
 * @description
 * @date 2018-11-15 9:56
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PartyResultForm {

    @ApiModelProperty("组织ID")
    @JsonProperty("org_id")
    private Long orgId;

    @ApiModelProperty("组织ID")
    @JsonProperty("org_name")
    private String orgName;

    @ApiModelProperty("上级党委组织ID")
    @JsonProperty("party_id")
    private Long partyId;

    @ApiModelProperty("上级党委组织名称")
    @JsonProperty("party_name")
    private String partyName;

    @ApiModelProperty("上级党委组织简称")
    @JsonProperty(value = "party_short_name")
    private String partyShortName;

}
