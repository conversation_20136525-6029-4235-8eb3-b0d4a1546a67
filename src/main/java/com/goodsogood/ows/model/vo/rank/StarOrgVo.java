package com.goodsogood.ows.model.vo.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * Create by FuXiao on 2020/11/7
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StarOrgVo {
    @JsonProperty(value = "org_id")
    @ApiModelProperty(name = "组织id")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @ApiModelProperty(name = "组织名称")
    private String orgName;

    @JsonProperty(value = "map")
    @ApiModelProperty(name = "星级人数map")
    private Map<String, Integer> map;
}
