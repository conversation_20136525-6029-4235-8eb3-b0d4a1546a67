package com.goodsogood.ows.model.vo.tbc;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserTabViewVo {

    //用户姓名
    @JsonProperty(value = "user_name")
    private String userName;


    //用户头像
    @JsonProperty(value = "header_url")
    private String headerUrl;


    //组织名称
    @JsonProperty(value = "org_name")
    private String orgName;

    //部门
    @JsonProperty(value = "department")
    private String department;


    //职务
    @JsonProperty(value = "position")
    private String position;


    @ApiModelProperty(value = "党内职务")
    @JsonProperty(value = "party_position")
    private String partyPosition;

    //负数为下降 正数为上升 0 没有变化
    @JsonProperty(value = "rank_go")
    private Integer rankGo;

    //全市排名
    @JsonProperty(value = "city_rank")
    private Integer cityRank;

    //全市平均值
    @JsonProperty(value = "avg_index")
    private Integer avgIndex;

    //单位排名
    @JsonProperty(value = "unit_rank")
    private Integer unitRank;

    //个人先锋指数
    @JsonProperty(value = "xian_feng_index")
    private String xianFengIndwx;

    //支部排名
    @JsonProperty(value = "branch_rank")
    private Integer branchRank;

    //党建先峰指数趋势图
    @JsonProperty(value = "list_index_history")
    List<List<Integer>> listIndexHistory;

    //党员历史记录
    @JsonProperty(value = "person_history")
    List<List<Integer>> personHistory;

    //三个指标块 第一块数据
    @JsonProperty(value = "list_index_total")
    private List<TbcBaseIndexForm> lisTbcBaseIndex;


    //用户指数月度统计
    @JsonProperty(value = "user_index_month")
    private  List<UserIndex> userIndexMonth;


    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Accessors(chain = true)
    public static class UserIndex {

        //指数名称
        @JsonProperty(value = "index_name")
        private String indexName;

        //指数名称
        @JsonProperty(value = "index")
        private Double index;

        //幅度
        @JsonProperty(value = "amplitude")
        private Double amplitude;

    }

}
