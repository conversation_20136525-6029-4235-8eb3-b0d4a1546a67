package com.goodsogood.ows.model.vo.meeting;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class YearMeetingForm {

    @ApiModelProperty("组织生活次数")
    @JsonProperty("total")
    private Integer total;

    @ApiModelProperty("党员大会次数")
    @JsonProperty("party_member")
    private Integer partyMember;

    @ApiModelProperty("党支部委员会会议")
    @JsonProperty("party_committee")
    private Integer partyCommittee;

    @ApiModelProperty("党小组会")
    @JsonProperty("party_group")
    private Integer partyGroup;

    @ApiModelProperty("党课")
    @JsonProperty("party_lecture")
    private Integer partyLecture;

    @ApiModelProperty("主题党日")
    @JsonProperty("party_theme_day")
    private Integer partyThemeDay;
}
