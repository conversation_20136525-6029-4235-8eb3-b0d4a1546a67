package com.goodsogood.ows.model.vo.datav

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.goodsogood.ows.model.vo.datav.base.CardFlop

/**
 * <AUTHOR>
 * @date 2023/3/13
 * @description class 可视化大屏-党务工作-学习教育
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class StudyVO @JvmOverloads constructor(
    // 学习强国 -4个翻牌器 （累计最高积分、累计平均积分、年度最高积分、年度平均积分）、一个Line
    val powerful: MutableList<Any> = mutableListOf(),
    // 网络学院-累计最高学时、累计平均学时、年度最高学时、年度平均学时
    val online: Array<List<CardFlop>?> = arrayOfNulls(4),
    // 数值党建-集中学习时长/h，参与学习人次、每日一练、每月一测、每季一考
    val tobacco: Array<List<CardFlop>?> = arrayOfNulls(5),
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is StudyVO) return false

        if (powerful != other.powerful) return false
        if (!online.contentEquals(other.online)) return false
        if (!tobacco.contentEquals(other.tobacco)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = powerful.hashCode()
        result = 31 * result + online.contentHashCode()
        result = 31 * result + tobacco.contentHashCode()
        return result
    }
}