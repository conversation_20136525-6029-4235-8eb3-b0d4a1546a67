package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_statistical_storage_variable 实体类
 *
 * <AUTHOR>
 * @create 2019-05-16 08:39
*/
@Data
@ApiModel
@Table(name = "t_statistical_storage_variable")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatisticalStorageVariableEntity {
	
	@Id
	@JsonProperty(value = "storage_val_id")
	@Column(name = "storage_val_id")
	private Long storageValId;

	@ApiModelProperty("统计日期（yyyy-MM)")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;
	
	@ApiModelProperty("统计日期（yyyy-MM)")
	@JsonProperty(value = "statistical_date")
	@Column(name = "statistical_date")
	private String statisticalDate;
	
	
	@ApiModelProperty("统计的值")
	@JsonProperty(value = "storage_val")
	@Column(name = "storage_val")
	private Long storageVal;
	
	
	@ApiModelProperty("1.党小组变量")
	@JsonProperty(value = "storage_type")
	@Column(name = "storage_type")
	private Integer storageType;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("更新时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
}

