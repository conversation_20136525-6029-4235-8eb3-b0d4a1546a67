package com.goodsogood.ows.model.vo.sas;

import com.goodsogood.ows.push.template.wechat.WorkNoticeTemplate;
import com.goodsogood.ows.push.variable.AbstractVariableMessagePushRequest;
import lombok.Data;

import java.util.List;

/**
 * 消息推送实体类
 * <AUTHOR>
 */
@Data
public class WorkReportPushDataForm extends AbstractVariableMessagePushRequest<WorkNoticeTemplate.WorkNotice> {

    private List<WorkNoticeTemplate.WorkNotice> data;

    @Override
    public List<WorkNoticeTemplate.WorkNotice> getData() {
        return this.data;
    }

    public void setData(List<WorkNoticeTemplate.WorkNotice> data) {
        this.data = data;
    }

}
