package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * t_statistical_org_life_view 实体类
 * <p>
 * 组织生活一浏览表
 *
 * <AUTHOR>
 * @create 2019-07-23 15:15
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatisticalOrgLifeViewForm {

    public static final long TYPE_DUAL_ORG_LIFE = -1L;

    public static final String TYPE_DUAL_ORG_LIFE_NAME = "双重组织生活";

    @ApiModelProperty("id")
    @JsonProperty(value = "org_life_view_id")
    private Long orgLifeViewId;


    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;


    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;


    @ApiModelProperty("活动类型id")
    @JsonProperty(value = "type_id")
    private Long typeId;

    @ApiModelProperty("统计类型：1 组织举办组织生活次数 2 双重组织生活次数")
    @JsonProperty(value = "sas_type")
    private Integer sasType;


    @ApiModelProperty("组织排序权重")
    @JsonProperty(value = "org_seq")
    private Integer orgSeq;

    @ApiModelProperty("类型名称")
    @JsonProperty(value = "type_name")
    private String typeName;


    @ApiModelProperty("举办次数")
    @JsonProperty(value = "participate_num")
    private Integer participateNum;


    @ApiModelProperty("统计的年")
    @JsonProperty(value = "statistical_year")
    private Integer statisticalYear;


    @ApiModelProperty("统计的月（1-12）")
    @JsonProperty(value = "statistical_month")
    private Integer statisticalMonth;

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    private Date createTime;

    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    private Date updateTime;

}

