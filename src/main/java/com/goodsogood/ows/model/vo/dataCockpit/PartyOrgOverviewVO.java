package com.goodsogood.ows.model.vo.dataCockpit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据驾驶舱 - 组织概况(党委)
 *
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PartyOrgOverviewVO {

     @ApiModelProperty("党组织数量")
     public Integer partyOrganizationNum = 0;

     @ApiModelProperty("党委数量")
     public Integer partyCommitteeNum = 0;

     @ApiModelProperty("党总支数量")
     public Integer partyGeneralBranchNum = 0;

     @ApiModelProperty("党支部数量")
     public Integer partyBranchNum = 0;

     @ApiModelProperty("行政单位数量")
     public Integer administrativeUnitNum = 0;

     @ApiModelProperty("党组数量")
     public Integer partyGroupNum = 0;

     @ApiModelProperty("党小组数量")
     public Integer communistGroupNum = 0;

     @ApiModelProperty("班子成员数量")
     public Integer teamMembersNum = 0;

     @ApiModelProperty("党支部书记数量")
     public Integer partyBranchSecretaryNum = 0;

     @ApiModelProperty("党员数量")
     public Integer partyMemberNum = 0;
}
