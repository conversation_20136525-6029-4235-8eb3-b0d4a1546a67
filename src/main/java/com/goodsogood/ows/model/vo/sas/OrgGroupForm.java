package com.goodsogood.ows.model.vo.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgGroupForm {

    @ApiModelProperty("党小组ID")
    @JsonProperty("org_group_id")
    private Long orgGroupId;

    @ApiModelProperty("党小组所在组织ID")
    @JsonProperty("org_id")
    private Long orgId;

    @ApiModelProperty("党小组名称")
    @JsonProperty("org_group_name")
    private String orgGroupName;

    @ApiModelProperty("是否删除")
    @JsonProperty("is_delete")
    private Integer isDelete;

    @ApiModelProperty("创建时间")
    @JsonProperty("create_date")
    private Date createDate;

}
