package com.goodsogood.ows.model.vo.overview;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 组织关系转接
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrgRelationshipVo extends OverviewBaseVo {

//    @JsonProperty(value = "list")
//    private List<KeyValVo> list;


    @JsonProperty(value = "list")
    private List<RelationshipInfo> list;

    @Data
    public static class RelationshipInfo {
        /**
         * 统计类型 1.本月 2.本年
         */
        @JsonProperty(value = "sta_type")
        private Integer staType;

        @ApiModelProperty(value = "转入")
        @JsonProperty(value = "transfer_in")
        private Integer transferIn;

        @ApiModelProperty(value = "转出")
        @JsonProperty(value = "transfer_out")
        private Integer transferOut;
    }


    @Data
    public static class RelationshipNumber{
        /**
         * 转入党员数量
         */
        private Integer inNum;

        /**
         * 转出党员的数量
         */
        private Integer outNum;

    }
}
