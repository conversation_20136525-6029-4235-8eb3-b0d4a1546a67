package com.goodsogood.ows.model.vo.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.EnumMap;
import java.util.Map;

/**
 * Create by FuXiao on 2020/11/23
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ScoreResultVo {
    @JsonProperty(value = "score_result_map")
    @ApiModelProperty(name = "key表示人员id或组织id,value表示1~12月的得分map")
    private Map<Long, EnumMap<Month, Double>> scoreResultMap;
}
