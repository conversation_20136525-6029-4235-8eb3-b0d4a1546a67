package com.goodsogood.ows.model.mongodb;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 组织生活统计MongoDB模型
 * @date 2019/11/19
 */
@Data
@ApiModel
//@Document(collection = "MEETING_REPORT")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@CompoundIndexes({
        @CompoundIndex(name = "meeting_org_idx", def = "{'orgId': 1, 'statsDate': 1}"),
        @CompoundIndex(name = "meeting_idx", def = "{'meetingId': 1}")
})
public class MeetingInfo implements Serializable {

    @ApiModelProperty("组织ID")
    private Long orgId;

    @ApiModelProperty("区县ID")
    private Long regionId;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("父级ID")
    private Long parentId;

    @ApiModelProperty("组织类型")
    private Integer orgType;

    @ApiModelProperty("组织类型下级")
    private Integer orgTypeChild;

    @ApiModelProperty("组织层级关系")
    private String orgLevel;

    @ApiModelProperty("组织创建时间")
    private Date orgCreateTime;

    @ApiModelProperty("会议ID")
    private Long meetingId;

    @ApiModelProperty("会议名称")
    private String meetingName;

    @ApiModelProperty("召开会议时间")
    private Date meetingDate;

    @ApiModelProperty("会议类型")
    private List<MeetingType> meetingType;

    @ApiModelProperty("参与会议人员列表")
    private List<User> userList;

    @ApiModelProperty("统计时间 yyyy-mm")
    private String statsDate;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 会议类型
     * <AUTHOR>
     * @date 2019/11/19
     * @return
     */
    @Data
    @ApiModel
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public class MeetingType{

        @ApiModelProperty("类型ID")
        private Long id;

        @ApiModelProperty("类型名称")
        private String name;
    }

    /**
     * 参会人员信息
     * <AUTHOR>
     * @date 2019/11/19
     * @return
     */
    @Data
    @ApiModel
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public class User{

        @ApiModelProperty("用户ID")
        private Long userId;

        @ApiModelProperty("用户姓名")
        private String userName;

        @ApiModelProperty("签到类型 1：已签到（默认） 2：未签到 3：因公请假 4：因私请假 5: 缺席")
        private Integer signStatus;

        @ApiModelProperty("标识 1：记录人员，2：主持人，3：参与人员 ，4：列席人员")
        private List<Integer> tag;
    }
}
