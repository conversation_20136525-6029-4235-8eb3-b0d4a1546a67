package com.goodsogood.ows.model.mongodb.report;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/11/22
 * Description: 用于组装返回mongo score相关 个人电子党务报告数据
 */
@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UserScoreStudyReport {

    @ApiModelProperty(name = "总计学习情况")
    private UserScoreStudyInfo studyTotal;

    @ApiModelProperty(name = "指定月的学习情况")
    private UserScoreStudyInfo studyDetail;

    @ApiModelProperty(name = "总计公益/扶贫")
    private UserScoreDonateInfo donateTotal;

    @ApiModelProperty(name = "指定月的公益/扶贫")
    private UserScoreDonateInfo donateDetail;

    public static UserScoreStudyReport getInitEntity() {
        UserScoreStudyReport userScoreStudyReport = new UserScoreStudyReport();
        userScoreStudyReport.setDonateDetail(UserScoreDonateInfo.getInitEntity());
        userScoreStudyReport.setDonateTotal(UserScoreDonateInfo.getInitEntity());
        userScoreStudyReport.setStudyDetail(UserScoreStudyInfo.getInitEntity());
        userScoreStudyReport.setStudyTotal(UserScoreStudyInfo.getInitEntity());
        return userScoreStudyReport;
    }

}

