package com.goodsogood.ows.model.mongodb.user;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ThirdToken {

    @ApiModelProperty(value = "	第三方登录类型")
    @JsonProperty("th_type")
    private Integer thType;

    @ApiModelProperty(value = "	区域ID")
    @JsonProperty("region_id")
    private Long regionId;

    @ApiModelProperty(value = "	所属组织ID")
    private Long oId;

    @ApiModelProperty(value = "	第三方token")
    @JsonProperty("th_token")
    private String thToken;

    @ApiModelProperty(value = "第三方头像地址")
    private String head;

    @ApiModelProperty(value = "第三方昵称	")
    @JsonProperty(value = "nick_name")
    private String nickName;
}

