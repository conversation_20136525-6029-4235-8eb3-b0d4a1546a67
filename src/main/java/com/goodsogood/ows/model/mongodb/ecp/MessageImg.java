package com.goodsogood.ows.model.mongodb.ecp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 帖子图片实体类
 *
 * <AUTHOR>
 * @date 2021/5/6
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MessageImg {

    @ApiModelProperty("帖子图片id")
    private String messageImgId;

    @ApiModelProperty("帖子图片url")
    private String messageImgUrl;

    @ApiModelProperty("帖子图片缩略图url")
    private String imgThumbnailUrl;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date updateTime;
}
