package com.goodsogood.ows.model.mongodb;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.model.mongodb.dss.*;
import com.goodsogood.ows.model.mongodb.dss.MeetingInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 党委详情
 * <AUTHOR>
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PartyCommitteeInfo extends DssBaseInfo {

    private static final long serialVersionUID = -6961802099543964144L;

    /** 数据更新时间 年.月.日 */
    @ApiModelProperty("数据更新时间")
    private String updateTime;

    @ApiModelProperty("组织Id")
    private Long organizationId;

    /** 区县ID */
    @ApiModelProperty("区域ID")
    private Long regionId;

    /** 当前数据年份 */
    @ApiModelProperty("当前数据年份")
    private Integer year;

    /** 数据创建时间 */
    @ApiModelProperty("数据生成时间")
    private Date createTime;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("组织简称")
    private String orgShortName;

    @ApiModelProperty("组织成层级关系")
    private String orgLevel;

    @ApiModelProperty("组织类型")
    private Integer orgTypeChild;

    @ApiModelProperty("党组织届次信息")
    private OrgPeriodInfo orgPeriodInfo;

    @ApiModelProperty("领导班子成员列表")
    private List<LeaderInfo> leaderList = new ArrayList<>();

    @ApiModelProperty("下级组织数量")
    private Integer secPartyBranchTotal;

    @ApiModelProperty("下级组织列表")
    private List<DssOrgChild> orgList = new ArrayList<>();

    @ApiModelProperty("综合评分")
    private Double sumScore;

    @ApiModelProperty("星级")
    private Integer star;

    @ApiModelProperty("年度得分趋势")
    private List<MapObject> annualScoreMap = new ArrayList<>();

    @ApiModelProperty("雷达图")
    private List<RadarChartMap> radarChartMap = new ArrayList<>();

    @ApiModelProperty("自动打分全市排名")
    private ScoreRankingBase scoreRanking;

    /** 党组织类别 */
    @ApiModelProperty("党组织类别")
    private List<PieObject> partyOrgCategory = new ArrayList<>();

    /** 党组织类型 */
    @ApiModelProperty("党组织类型")
    private List<PieObject> partyOrgType = new ArrayList<>();

    /** 党员数据概览 */
    @ApiModelProperty("党员数据概览")
    private List<PartyMemberInfo> partyMemberInfo = new ArrayList<>();

    @ApiModelProperty("组织生活情况")
    private MeetingInfo orgLifeInfo;

    /** 党费交纳情况 */
    @ApiModelProperty("党费交纳情况")
    private PayInfo payInfo;

    @ApiModelProperty("学习情况")
    private StudyInfo studyInfo;

    @ApiModelProperty("评分情况饼图")
    private List<GrandMap> grandMap = new ArrayList<>();

    @ApiModelProperty("消费扶贫情况")
    private ConsumeInfo consumeInfo;

    @ApiModelProperty("志愿者服务")
    private VolunteerInfo volunteerInfo;

    @ApiModelProperty("用户活跃度")
    private OrgUserActive userActive;

    @ApiModelProperty("日志上下文 -> 便于查找日志")
    private LogAspectHelper.SSLog ssLog;

    public LogAspectHelper.SSLog getSsLog() {
        return ssLog;
    }

    public void setSsLog(LogAspectHelper.SSLog ssLog) {
        this.ssLog = ssLog;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel;
    }

    public Integer getOrgTypeChild() {
        return orgTypeChild;
    }

    public void setOrgTypeChild(Integer orgTypeChild) {
        this.orgTypeChild = orgTypeChild;
    }

    public OrgPeriodInfo getOrgPeriodInfo() {
        return orgPeriodInfo;
    }

    public void setOrgPeriodInfo(OrgPeriodInfo orgPeriodInfo) {
        this.orgPeriodInfo = orgPeriodInfo;
    }

    public List<LeaderInfo> getLeaderList() {
        return leaderList;
    }

    public void setLeaderList(List<LeaderInfo> leaderList) {
        this.leaderList = leaderList;
    }

    public Integer getSecPartyBranchTotal() {
        return secPartyBranchTotal;
    }

    public void setSecPartyBranchTotal(Integer secPartyBranchTotal) {
        this.secPartyBranchTotal = secPartyBranchTotal;
    }

    public List<DssOrgChild> getOrgList() {
        return orgList;
    }

    public void setOrgList(List<DssOrgChild> orgList) {
        this.orgList = orgList;
    }

    public Double getSumScore() {
        return sumScore;
    }

    public void setSumScore(Double sumScore) {
        this.sumScore = sumScore;
    }

    public Integer getStar() {
        return star;
    }

    public void setStar(Integer star) {
        this.star = star;
    }

    public List<MapObject> getAnnualScoreMap() {
        return annualScoreMap;
    }

    public void setAnnualScoreMap(List<MapObject> annualScoreMap) {
        this.annualScoreMap = annualScoreMap;
    }

    public List<RadarChartMap> getRadarChartMap() {
        return radarChartMap;
    }

    public void setRadarChartMap(List<RadarChartMap> radarChartMap) {
        this.radarChartMap = radarChartMap;
    }

    public List<PieObject> getPartyOrgCategory() {
        return partyOrgCategory;
    }

    public void setPartyOrgCategory(List<PieObject> partyOrgCategory) {
        this.partyOrgCategory = partyOrgCategory;
    }

    public List<PieObject> getPartyOrgType() {
        return partyOrgType;
    }

    public void setPartyOrgType(List<PieObject> partyOrgType) {
        this.partyOrgType = partyOrgType;
    }

    public List<PartyMemberInfo> getPartyMemberInfo() {
        return partyMemberInfo;
    }

    public void setPartyMemberInfo(List<PartyMemberInfo> partyMemberInfo) {
        this.partyMemberInfo = partyMemberInfo;
    }

    public MeetingInfo getOrgLifeInfo() {
        return orgLifeInfo;
    }

    public void setOrgLifeInfo(MeetingInfo orgLifeInfo) {
        this.orgLifeInfo = orgLifeInfo;
    }

    public PayInfo getPayInfo() {
        return payInfo;
    }

    public void setPayInfo(PayInfo payInfo) {
        this.payInfo = payInfo;
    }

    public StudyInfo getStudyInfo() {
        return studyInfo;
    }

    public void setStudyInfo(StudyInfo studyInfo) {
        this.studyInfo = studyInfo;
    }

    public List<GrandMap> getGrandMap() {
        return grandMap;
    }

    public void setGrandMap(List<GrandMap> grandMap) {
        this.grandMap = grandMap;
    }

    public ConsumeInfo getConsumeInfo() {
        return consumeInfo;
    }

    public void setConsumeInfo(ConsumeInfo consumeInfo) {
        this.consumeInfo = consumeInfo;
    }

    public VolunteerInfo getVolunteerInfo() {
        return volunteerInfo;
    }

    public void setVolunteerInfo(VolunteerInfo volunteerInfo) {
        this.volunteerInfo = volunteerInfo;
    }

    public OrgUserActive getUserActive() {
        return userActive;
    }

    public void setUserActive(OrgUserActive userActive) {
        this.userActive = userActive;
    }

    public String getOrgShortName() {
        return orgShortName;
    }

    public void setOrgShortName(String orgShortName) {
        this.orgShortName = orgShortName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        PartyCommitteeInfo that = (PartyCommitteeInfo) o;
        return Objects.equals(organizationId, that.organizationId) &&
                Objects.equals(regionId, that.regionId) &&
                Objects.equals(year, that.year);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), organizationId, regionId, year);
    }
}
