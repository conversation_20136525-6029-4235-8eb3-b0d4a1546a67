package com.goodsogood.ows.model.mongodb;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.mongodb.report.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 组织数据统计
 * @date 2019/11/28
 */
@Data
@ApiModel
//@Document(collection = "ORG_REPORT_INFO")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@CompoundIndexes({
        @CompoundIndex(name = "org_report_idx", def = "{'orgId': 1, 'statsYear': 1}", background = true)
})
public class OrgReportInfo implements Serializable {

    @ApiModelProperty("组织ID")
    private Long orgId;


    @ApiModelProperty("组织ID")
    private Long regionId;


    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("报告列表-每月一条")
    private List<ReportInfo> reportInfoList;

    @ApiModelProperty("统计年份 yyyy")
    private Integer statsYear;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public class ReportInfo implements Comparable<ReportInfo> {

        @ApiModelProperty("统计时间(月份)MM")
        private Integer statsMonth;

        @ApiModelProperty("党费情况")
        private PpmdReportInfo ppmdReport;

        @ApiModelProperty("组织生活情况")
        private MeetingReportInfo meetingReport;

        @ApiModelProperty("党群活动情况")
        private ActivityReportInfo activityReport;

        @ApiModelProperty("学习情况")
        private StudyReportInfo studyReport;

        @ApiModelProperty("扶贫情况")
        private DonateReportInfo donateReport;

        @Override
        public int compareTo(ReportInfo o) {
            //升序
            return this.statsMonth - o.statsMonth;
        }
    }
}
