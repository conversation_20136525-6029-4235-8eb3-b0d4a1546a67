package com.goodsogood.ows.model.mongodb.pbm

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer
import io.swagger.annotations.ApiModelProperty
import org.springframework.data.mongodb.core.index.CompoundIndexes
import java.time.LocalDateTime

/**
 *
 * <AUTHOR>
 * @createTime 2022年06月25日 10:35:00
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
@CompoundIndexes()
data class UserWorkDetailInfo @JvmOverloads constructor(
    @ApiModelProperty("用户ID")
    var userId: Long? = null,

    @ApiModelProperty("区域ID")
    var regionId: Long? = null,

    @ApiModelProperty("指标ID")
    var workItemId: Long? = null,

    @ApiModelProperty("指标名称")
    var workItemName: String? = null,

    @ApiModelProperty("考核周期 1-周，2-月度，3-季度，4-年度，5-累计")
    var cycle: Int? = null,

    @ApiModelProperty("考核标准")
    var criterion: String? = null,

    @ApiModelProperty("年")
    var year: Int? = null,

    @ApiModelProperty("月")
    var month: Int? = null,

    @ApiModelProperty("周")
    var week: Int? = null,

    @ApiModelProperty("计算排名的值")
    var rankValue: Long? = null,

    @ApiModelProperty("工作结果描述")
    var workResult: String? = null,

    @ApiModelProperty("结果比较")
    var resultCompare: String? = null,

    @ApiModelProperty("备注")
    var remark: String? = null,

    @ApiModelProperty("类型 1-党建 2-业务")
    var type: Int? = null,

    @ApiModelProperty("创建时间")
    @get: JsonFormat
        (shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer::class)
    @JsonDeserialize(using = LocalDateTimeDeserializer::class)
    var createTime: LocalDateTime? = null,

    @ApiModelProperty("创建人")
    var createUser: Long? = null,

    @ApiModelProperty("更新时间")
    @get: JsonFormat
        (shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer::class)
    @JsonDeserialize(using = LocalDateTimeDeserializer::class)
    var updateTime: LocalDateTime? = null,

    @ApiModelProperty("最后修改人")
    var lastChangeUser: Long? = null


) {

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is UserWorkDetailInfo) return false

        if (userId != other.userId) return false
        if (regionId != other.regionId) return false
        if (workItemId != other.workItemId) return false
        if (workItemName != other.workItemName) return false
        if (cycle != other.cycle) return false
        if (criterion != other.criterion) return false
        if (year != other.year) return false
        if (month != other.month) return false
        if (week != other.week) return false
        if (rankValue != other.rankValue) return false
        if (workResult != other.workResult) return false
        if (resultCompare != other.resultCompare) return false
        if (remark != other.remark) return false
        if (type != other.type) return false
        if (createTime != other.createTime) return false
        if (createUser != other.createUser) return false
        if (updateTime != other.updateTime) return false
        if (lastChangeUser != other.lastChangeUser) return false

        return true
    }

    override fun hashCode(): Int {
        var result = userId?.hashCode() ?: 0
        result = 31 * result + (regionId?.hashCode() ?: 0)
        result = 31 * result + (workItemId?.hashCode() ?: 0)
        result = 31 * result + (workItemName?.hashCode() ?: 0)
        result = 31 * result + (cycle ?: 0)
        result = 31 * result + (criterion?.hashCode() ?: 0)
        result = 31 * result + (year ?: 0)
        result = 31 * result + (month ?: 0)
        result = 31 * result + (week ?: 0)
        result = 31 * result + (rankValue?.hashCode() ?: 0)
        result = 31 * result + (workResult?.hashCode() ?: 0)
        result = 31 * result + (resultCompare?.hashCode() ?: 0)
        result = 31 * result + (remark?.hashCode() ?: 0)
        result = 31 * result + (type ?: 0)
        result = 31 * result + (createTime?.hashCode() ?: 0)
        result = 31 * result + (createUser?.hashCode() ?: 0)
        result = 31 * result + (updateTime?.hashCode() ?: 0)
        result = 31 * result + (lastChangeUser?.hashCode() ?: 0)
        return result
    }


    override fun toString(): String {
        return "UserWorkDetailInfo(userId=$userId, regionId=$regionId, workItemId=$workItemId, workItemName=$workItemName, cycle=$cycle, criterion=$criterion, year=$year, month=$month, week=$week, rankValue=$rankValue, workResult=$workResult, resultCompare=$resultCompare, remark=$remark, type=$type, createTime=$createTime, createUser=$createUser, updateTime=$updateTime, lastChangeUser=$lastChangeUser)"
    }

}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class OrgWorkDetailInfo @JvmOverloads constructor(

    @ApiModelProperty("区域ID")
    var regionId: Long? = null,

    @ApiModelProperty("指标ID")
    var workItemId: Long? = null,

    @ApiModelProperty("指标名称")
    var workItemName: String? = null,

    @ApiModelProperty("考核周期 1-周，2-月度，3-季度，4-年度，5-累计")
    var cycle: Int? = null,

    @ApiModelProperty("考核标准")
    var criterion: String? = null,

    @ApiModelProperty("年")
    var year: Int? = null,

    @ApiModelProperty("月")
    var month: Int? = null,

    @ApiModelProperty("周")
    var week: Int? = null,

    @ApiModelProperty("计算排名的值")
    var rankValue: Long? = null,

    @ApiModelProperty("工作结果描述")
    var workResult: String? = null,

    @ApiModelProperty("结果比较")
    var resultCompare: String? = null,

    @ApiModelProperty("备注")
    var remark: String? = null,

    @ApiModelProperty("类型 1-党建 2-业务")
    var type: Int? = null,

    @ApiModelProperty("创建时间")
    @get: JsonFormat
        (shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer::class)
    @JsonDeserialize(using = LocalDateTimeDeserializer::class)
    var createTime: LocalDateTime? = null,

    @ApiModelProperty("创建人")
    var createUser: Long? = null,

    @ApiModelProperty("更新时间")
    @get: JsonFormat
        (shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer::class)
    @JsonDeserialize(using = LocalDateTimeDeserializer::class)
    var updateTime: LocalDateTime? = null,

    @ApiModelProperty("最后修改人")
    var lastChangeUser: Long? = null,

    @ApiModelProperty("组织ID") var orgId: Long? = null
) {

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is OrgWorkDetailInfo) return false

        if (regionId != other.regionId) return false
        if (workItemId != other.workItemId) return false
        if (workItemName != other.workItemName) return false
        if (cycle != other.cycle) return false
        if (criterion != other.criterion) return false
        if (year != other.year) return false
        if (month != other.month) return false
        if (week != other.week) return false
        if (rankValue != other.rankValue) return false
        if (workResult != other.workResult) return false
        if (resultCompare != other.resultCompare) return false
        if (remark != other.remark) return false
        if (type != other.type) return false
        if (createTime != other.createTime) return false
        if (createUser != other.createUser) return false
        if (updateTime != other.updateTime) return false
        if (lastChangeUser != other.lastChangeUser) return false
        if (orgId != other.orgId) return false

        return true
    }

    override fun hashCode(): Int {
        var result = regionId?.hashCode() ?: 0
        result = 31 * result + (workItemId?.hashCode() ?: 0)
        result = 31 * result + (workItemName?.hashCode() ?: 0)
        result = 31 * result + (cycle ?: 0)
        result = 31 * result + (criterion?.hashCode() ?: 0)
        result = 31 * result + (year ?: 0)
        result = 31 * result + (month ?: 0)
        result = 31 * result + (week ?: 0)
        result = 31 * result + (rankValue?.hashCode() ?: 0)
        result = 31 * result + (workResult?.hashCode() ?: 0)
        result = 31 * result + (resultCompare?.hashCode() ?: 0)
        result = 31 * result + (remark?.hashCode() ?: 0)
        result = 31 * result + (type ?: 0)
        result = 31 * result + (createTime?.hashCode() ?: 0)
        result = 31 * result + (createUser?.hashCode() ?: 0)
        result = 31 * result + (updateTime?.hashCode() ?: 0)
        result = 31 * result + (lastChangeUser?.hashCode() ?: 0)
        result = 31 * result + (orgId?.hashCode() ?: 0)
        return result
    }

    override fun toString(): String {
        return "OrgWorkDetailInfo(regionId=$regionId, workItemId=$workItemId, workItemName=$workItemName, cycle=$cycle, criterion=$criterion, year=$year, month=$month, week=$week, rankValue=$rankValue, workResult=$workResult, resultCompare=$resultCompare, remark=$remark, type=$type, createTime=$createTime, createUser=$createUser, updateTime=$updateTime, lastChangeUser=$lastChangeUser, orgId=$orgId)"
    }

}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class BusinessForm @JvmOverloads constructor(
    var calResult: Double? = null,
    var explainStr: String? = null,
    var rankNum: Int? = null
) {
    override fun toString(): String {
        return "OrgBusinessForm(calResult=$calResult, explainStr=$explainStr, rankNum=$rankNum)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ScatterPlotForm @JvmOverloads constructor(
    var xAxis: MutableList<Long>? = mutableListOf(),
    var yAxis: MutableList<Long>? = mutableListOf(),
    var median: MutableList<Long>? = mutableListOf(),
    var point: MutableList<PointForm>? = mutableListOf()
) {
    override fun toString(): String {
        return "ScatterPlotForm(xAxis=$xAxis, yAxis=$yAxis, median=$median, point=$point)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class PointForm @JvmOverloads constructor(
    var name: String? = null,
    var phone: String? = null,
    var org: String? = null,
    var value: MutableList<Long>? = mutableListOf()
) {
    override fun toString(): String {
        return "PointForm(name=$name, phone=$phone, org=$org, value=$value)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FitScatterPlotForm @JvmOverloads constructor(
    var name: String? = null,
    var phone: String? = null,
    var nhd: Double? = null,
    var chartData: MutableList<Double>? = mutableListOf()
) {
    override fun toString(): String {
        return "FitScatterPlotForm(name=$name, phone=$phone, nhd=$nhd, chartData=$chartData)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UnitScatterPlotForm @JvmOverloads constructor(
    @ApiModelProperty("1-拟合度 2-党建工作 3-业务工作")
    var type: Int? = null,
    var sefValue: Double? = null,
    var rankNum: Int? = null,
    var avgNum: Double? = null,
    var maxNum: Double? = null,
    var sef: Double? = null
) {
    override fun toString(): String {
        return "UnitScatterPlotForm(type=$type, sefValue=$sefValue, rankNum=$rankNum, avgNum=$avgNum, maxNum=$maxNum)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ColumnarChartForm @JvmOverloads constructor(
    var areaName: String? = null,
    var num: Double? = null
) {
    override fun toString(): String {
        return "ColunmnarChartForm(areaName=$areaName, num=$num)"
    }
}


@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FindUnitMixScatterVO @JvmOverloads constructor(
    var unitName: String? = null, // 单位名称
    var nhd: Double? = null, // 拟合度
    var chartData: MutableList<Double>? = mutableListOf() // 	[党建工作积分, 业务工作积分]
) {
    override fun toString(): String {
        return "FindUnitMixScatterVO(unitName=$unitName, nhd=$nhd, chartData=$chartData)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FindUnitMixLineBarVO @JvmOverloads constructor(
    var xData: MutableList<String>? = mutableListOf(), // x轴
    var p: MutableList<Double?>? = mutableListOf(), // 党务工作
    var b: MutableList<Double?>? = mutableListOf(), // 业务工作
    var k: MutableList<Double?>? = mutableListOf(), // 拟合度
) {
    override fun toString(): String {
        return "FindUnitMixLineBarVO(xData=$xData, p=$p, b=$b, k=$k)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ScatterListForm @JvmOverloads constructor(
    var userName: String? = null,
    var unitName: String? = null,
    var title: String? = null
) {
    override fun toString(): String {
        return "scatterListForm(userName=$userName, unitName=$unitName, title=$title)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class GetUserKitResponse(

    @ApiModelProperty("拟合度")
    var kit: Double? = null,
    @ApiModelProperty("本单位党员人数")
    var unitTotal: Int? = null,
    @ApiModelProperty("本单位排名")
    var unitRank: Int? = null,
    @ApiModelProperty("序列党员人数")
    var sequenceTotal: Int? = null,
    @ApiModelProperty("序列排名")
    var sequenceRank: Int? = null,
    @ApiModelProperty("X轴")
    var xAxis: MutableList<String>? = mutableListOf(),
    @ApiModelProperty("我的")
    var d1: MutableList<Double?> = mutableListOf(),
    @ApiModelProperty("卷烟营销平均")
    var d2: MutableList<Double?> = mutableListOf(),
    @ApiModelProperty("本单位平均")
    var d3: MutableList<Double?> = mutableListOf(),
) {
    override fun toString(): String {
        return "GetUserKitResponse(kit=$kit, unitTotal=$unitTotal, unitRank=$unitRank, sequenceTotal=$sequenceTotal, sequenceRank=$sequenceRank, xAxis=$xAxis, d1=$d1, d2=$d2, d3=$d3)"
    }
}



