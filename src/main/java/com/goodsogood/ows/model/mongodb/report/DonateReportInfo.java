package com.goodsogood.ows.model.mongodb.report;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 组织电子党务报表-扶贫公益情况
 * @date 2019/11/28
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class DonateReportInfo implements Serializable {

    @ApiModelProperty(name = "参与一元捐次数")
    private Integer donateNum = 0;

    @ApiModelProperty(name = "捐助积分")
    private Long donateScore = 0L;

    @ApiModelProperty(name = "多少人购买了扶贫产品")
    private Integer povertyPersonNum = 0;

    @ApiModelProperty(name = "在消费扶贫商城交易单数")
    private Integer povertyNum = 0;

    @ApiModelProperty(name = "交易金额")
    private Double povertyAmount = 0.0;

    @ApiModelProperty(name = "购买公益宝贝件数")
    private Integer charityNum = 0;

    @ApiModelProperty(name = "购买公益宝贝金额")
    private Double charityAmount = 0.0;

}
