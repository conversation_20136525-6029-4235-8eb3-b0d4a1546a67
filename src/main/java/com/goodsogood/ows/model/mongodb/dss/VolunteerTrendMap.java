/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Auto-generated: 2020-11-05 19:49:56
 * 志愿者开展趋势图
 * <AUTHOR>
 */
@Data
@ApiModel
public class VolunteerTrendMap extends DssBaseInfo {

    private static final long serialVersionUID = -556550058840600341L;

    @ApiModelProperty("志愿者项目")
    private List<Integer> project;

    @ApiModelProperty("送服务")
    private List<Integer> service;

    @ApiModelProperty("求助接单")
    private List<Integer> help;

}