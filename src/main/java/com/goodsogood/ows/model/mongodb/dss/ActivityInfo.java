package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 积分参加列表
 * <AUTHOR>
 */
@Data
@ApiModel
public class ActivityInfo extends DssBaseInfo {

    private static final long serialVersionUID = -9189625271324605767L;

    @ApiModelProperty("参加党群活动次数")
    private Integer joinNum;

    @ApiModelProperty("参加党群活动列表")
    private List<ActivityJoinList> activityJoinList = new ArrayList<>();
}
