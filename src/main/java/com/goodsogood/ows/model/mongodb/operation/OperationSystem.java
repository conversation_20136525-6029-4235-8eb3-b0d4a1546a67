package com.goodsogood.ows.model.mongodb.operation;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.index.TextIndexed;

import java.util.Date;

/**
 * @ClassName : OperationSystem
 * <AUTHOR> tc
 * @Date: 2022/4/18 15:31
 * @Description : 系统运行报告功能访问记录
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OperationSystem {

    /**
     * 区县id
     */
    @JsonProperty("region_id")
    private Long regionId;

    /**
     * 用户id
     */
    @Indexed
    @JsonProperty("user_id")
    private String userId;
    /**
     * 用户名称
     */
    @JsonProperty("user_name")
    private String userName;
    /**
     * 组织id
     */
    @JsonProperty("org_id")
    private String orgId;
    /**
     * 组织名称
     */
    @JsonProperty("org_name")
    private String orgName;

    /**
     * 渠道名称 OperationSystem
     */
    private String channal;

    /**
     * 操作类型 A 功能访问
     */
    @TextIndexed
    private String type;
    /**
     * 操作对象（暂指消息id）
     */
    private String object;
    /**
     * 数量（默认为1，用于购买或金额等的累加统计）
     */
    private Double amount;
    /**
     * 调用时间
     */
    @JsonProperty("transfer_time")
    private Date transferTime;
}
