package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * Auto-generated: 2020-11-05 19:49:56
 *
 * <AUTHOR>
 */
@Data
@SuperBuilder(toBuilder = true)
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class DssOrg implements Serializable {

    private static final long serialVersionUID = 4642014001725133593L;

    /**
     * 组织ID
     */
    @ApiModelProperty("组织ID")
    private Long orgId;

    /**
     * 组织名称
     */
    @ApiModelProperty("组织名称")
    private String name;

    /**
     * 组织简称
     */
    @ApiModelProperty("组织简称")
    private String shortName;
}
