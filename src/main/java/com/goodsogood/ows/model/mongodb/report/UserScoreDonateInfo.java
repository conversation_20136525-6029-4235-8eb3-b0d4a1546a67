package com.goodsogood.ows.model.mongodb.report;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/11/22
 * Description: 公益/扶贫
 */
@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UserScoreDonateInfo {

    @ApiModelProperty(name = "参与一元捐次数")
    private Integer donateNum;

    @ApiModelProperty(name = "捐助积分")
    private Long donateScore;

    @ApiModelProperty(name = "多少人购买了扶贫产品")
    private Integer povertyPersonNum;

    @ApiModelProperty(name = "在消费扶贫商城交易单数")
    private Integer povertyNum;

    @ApiModelProperty(name = "交易金额")
    private Double povertyMoney;

    @ApiModelProperty(name = "购买公益宝贝件数")
    private Integer charityNum;

    @ApiModelProperty(name = "购买公益宝贝金额")
    private Double charityMoney;

    public Integer getDonateNum() {
        if(donateNum==null){
            return 0;
        }
        return donateNum;
    }

    public Long getDonateScore() {
        if(donateScore==null){
            return 0L;
        }
        return donateScore;
    }

    public Integer getPovertyPersonNum() {
        if(povertyPersonNum==null){
            return 0;
        }
        return povertyPersonNum;
    }

    public Integer getPovertyNum() {
        if(povertyNum==null){
            return 0;
        }
        return povertyNum;
    }

    public Double getPovertyMoney() {
        if(povertyMoney==null){
            return 0.00;
        }
        return povertyMoney;
    }

    public Integer getCharityNum() {
        if(charityNum==null){
            return 0;
        }
        return charityNum;
    }

    public Double getCharityMoney() {
        if(charityMoney==null){
            return 0.00;
        }
        return charityMoney;
    }

    public static UserScoreDonateInfo getInitEntity() {
        UserScoreDonateInfo userScoreDonateInfo = new UserScoreDonateInfo();
        userScoreDonateInfo.setDonateNum(0);
        userScoreDonateInfo.setDonateScore(0L);
        userScoreDonateInfo.setPovertyNum(0);
        userScoreDonateInfo.setPovertyMoney(0.00);
        userScoreDonateInfo.setPovertyPersonNum(0);
        userScoreDonateInfo.setCharityNum(0);
        userScoreDonateInfo.setCharityMoney(0.00);
        return userScoreDonateInfo;
    }

}
