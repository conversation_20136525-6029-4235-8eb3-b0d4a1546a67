package com.goodsogood.ows.model.mongodb.operation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName : ClickMenuVo
 * <AUTHOR> tc
 * @Date: 2022/4/13 16:33
 * @Description : 功能点击
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClickMenu {

    @JsonProperty(value = "region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;

    /**
     * 格式  YYYY-MM-DD
     */
    @JsonProperty(value = "stats_date")
    @ApiModelProperty("统计日期")
    private String statsDate;

    @JsonProperty(value = "menu_name")
    @ApiModelProperty("功能名称")
    private String menuName;

    @JsonProperty(value = "click_num")
    @ApiModelProperty("点击次数")
    private Long clickNum=0L;

    //格式: YYYY-MM-DD HH:mm:ss
    @JsonProperty(value = "last_click_time")
    @ApiModelProperty("当日最后一次点击时间")
    private String lastClickTime;
}
