/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


/**
 * Auto-generated: 2020-11-05 19:49:56
 * 评分情况饼图
 * <AUTHOR>
 */
@Data
@ApiModel
public class GrandMap extends PieObject{

    private static final long serialVersionUID = -5996451616305106681L;

    /** 组织列表 */
    @ApiModelProperty("组织列表")
    private List<DssOrgChild> orgList = new ArrayList<>();
}