package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Auto-generated: 2020-11-05 19:49:56
 * 组织用户活跃度
 * <AUTHOR>
 */
@Data
@ApiModel
public class OrgUserActive extends UserActive {

    private static final long serialVersionUID = -7960008035939199923L;

    @ApiModelProperty("本年度访问量最大的一天")
    private MaxDayVisit maxDayVisit;

    @ApiModelProperty("本年度访问量最大的一个月")
    private MaxMonthVisit maxMonthVisit;
}
