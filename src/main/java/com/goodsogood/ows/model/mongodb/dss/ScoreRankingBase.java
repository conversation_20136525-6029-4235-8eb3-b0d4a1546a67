package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 自动打分排名基础类
 * <AUTHOR>
 */
@Data
@ApiModel
public class ScoreRankingBase extends DssBaseInfo {

	private static final long serialVersionUID = -2988092004584820910L;

	@ApiModelProperty("排名 - 名次")
	private List<Integer> ranking = new ArrayList<>();

	@ApiModelProperty("排名 - 百分比")
	private List<Double> percent = new ArrayList<>();
}
