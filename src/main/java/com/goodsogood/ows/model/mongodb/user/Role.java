package com.goodsogood.ows.model.mongodb.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Role {

    @ApiModelProperty(value = "角色ID")
    @JsonProperty(value = "role_id")
    private Long roleId;

    @ApiModelProperty(value = "角色名称")
    @JsonProperty(value = "role_name")
    private String roleName;

    @ApiModelProperty(value = "角色类型")
    @JsonProperty(value = "role_type")
    private Integer roleType;

    @ApiModelProperty(value = "权限所属 1-PC端 2-微信端")
    private Integer belong;

    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "区县ID")
    @JsonProperty("region_id")
    private Long regionId;
}
