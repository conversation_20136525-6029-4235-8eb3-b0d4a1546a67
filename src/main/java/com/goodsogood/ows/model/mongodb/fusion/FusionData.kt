package com.goodsogood.ows.model.mongodb.fusion

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty


/**
 * 党业融合
 */
@ApiModel("党业融合")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FusionData(
    @ApiModelProperty("单位ID")
    var unitId: Long? = null,

    @ApiModelProperty("单位名称")
    var unitName: String? = null,

    @ApiModelProperty("年份")
    var year: Int? = null,

    @ApiModelProperty("月份")
    var month: Int? = null,

    @ApiModelProperty("融合度")
    var fusion: Double = 0.0,

    @ApiModelProperty("排名")
    var rank: Int = 0,

    @ApiModelProperty("上个月的浮动")
    var change: Int = 0,

    @ApiModelProperty("数据更新时间")
    var updateTime: Long? = null
) {
    override fun toString(): String {
        return "FusionData(unitId=$unitId, unitName=$unitName, year=$year, month=$month, fusion=$fusion, rank=$rank, updateTime=$updateTime)"
    }
}

/**
 * 党业融合指标得分
 *
 * <AUTHOR>
 * @createTime 2023年03月10日 17:27:00
 */
@ApiModel("党业融合指标得分")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FusionItemData(
    @ApiModelProperty("单位ID")
    var unitId: Long? = null,
    @ApiModelProperty("单位名称")
    var unitName: String? = null,
    @ApiModelProperty("年份")
    var year: Int? = null,
    @ApiModelProperty("月份")
    var month: Int? = null,
    @ApiModelProperty("指标ID")
    var itemId: Long? = null,
    /** 指标得分（根据规则计算得分，用于计算融合度，不能为空） */
    @ApiModelProperty("指标得分 - 根据规则计算得分 - 计算为融合度")
    var score: Double = 0.0,
    /** 为该指标赋分（可能为平均值、考核得分、综合等，可为空） */
    @ApiModelProperty("指标赋分 - 单项计算得分")
    var itemScore: Double? = null,
    /**需要才存，可为空 */
    @ApiModelProperty("最高分")
    var maxScore: Double? = null,
    /**需要才存，可为空 */
    @ApiModelProperty("党员平均分")
    var partyAvgScore: Double? = null,
    /**需要才存，可为空 */
    @ApiModelProperty("非党员平均分")
    var noPartyAvgScore: Double? = null,
    /** 根据情况，需要需要排名，请填入排名 */
    @ApiModelProperty("排名")
    var rank: Int? = null,
    /** 作为展示使用 */
    @ApiModelProperty("结果")
    var result: String? = null,
    @ApiModelProperty("数据更新时间")
    var updateTime: Long? = null
) {
    override fun toString(): String {
        return "FusionUnitData(unitId=$unitId, unitName=$unitName, year=$year, month=$month, itemId=$itemId, score=$score, itemScore=$itemScore, rank=$rank, result=$result, updateTime=$updateTime)"
    }
}

