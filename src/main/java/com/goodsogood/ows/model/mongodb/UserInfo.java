package com.goodsogood.ows.model.mongodb;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.model.mongodb.dss.*;
import com.goodsogood.ows.model.mongodb.dss.ActivityInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 *  党员情况
 * <AUTHOR>
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserInfo extends DssBaseInfo {

    private static final long serialVersionUID = -2037728855127188455L;

    /** 数据更新时间 年.月.日 */
    @ApiModelProperty("数据更新时间")
    private String updateTime;

    /** 区县ID */
    @ApiModelProperty("区域ID")
    private Long regionId;

    /** 当前数据年份 */
    @ApiModelProperty("当前数据年份")
    private Integer year;

    /** 数据创建时间 */
    @ApiModelProperty("数据生成时间")
    private Date createTime;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户姓名")
    private String name;

    @ApiModelProperty("综合得分")
    private Double compositeScore;

    @ApiModelProperty("星级")
    private Integer star;

    @ApiModelProperty("党内职务")
    private String partyPosition;

    @ApiModelProperty("加密手机号")
    private String phoneSecret;

    @ApiModelProperty("政治面貌")
    private String politicalType;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("学历")
    private String education;

    @ApiModelProperty("年龄区间")
    private Integer[] ageSection;

    @ApiModelProperty("入党时间")
    private String joinPartyTime;

    @ApiModelProperty("岗位")
    private String position;

    @ApiModelProperty("籍贯")
    private String nativePlace;

    @ApiModelProperty("所属支部")
    private DssOrg branch;

    @ApiModelProperty("所属党委")
    private DssOrg company;

    @ApiModelProperty("标签")
    private String tag;

    @ApiModelProperty("奖惩信息")
    private List<String> bonusPenaltyInfo = new ArrayList<>();

    @ApiModelProperty("得分雷达图")
    private List<RadarChartMap> radarChartMap = new ArrayList<>();

    @ApiModelProperty("年度得分趋势")
    private List<MapObject> annualScoreMap = new ArrayList<>();

    @ApiModelProperty("自动打分排行")
    private ScoreRanking scoreRanking;

    @ApiModelProperty("组织生活概览")
    private UserMeetingInfo orgLifeInfo;

    @ApiModelProperty("党费概览")
    private UserPayInfo payInfo;

    @ApiModelProperty("学习情况概览")
    private UserStudyInfo studyInfo;

    @ApiModelProperty("党群活动情况概览")
    private ActivityInfo activityInfo;

    @ApiModelProperty("消费扶贫")
    private UserDonateInfo donateInfo;

    @ApiModelProperty("志愿服务")
    private UserVolunteer volunteer;

    @ApiModelProperty("用户活跃度")
    private UserActive userActive;

    @ApiModelProperty("日志上下文 -> 便于查找日志")
    private LogAspectHelper.SSLog ssLog;


    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getCompositeScore() {
        return compositeScore;
    }

    public void setCompositeScore(Double compositeScore) {
        this.compositeScore = compositeScore;
    }

    public Integer getStar() {
        return star;
    }

    public void setStar(Integer star) {
        this.star = star;
    }

    public List<MapObject> getAnnualScoreMap() {
        return annualScoreMap;
    }

    public void setAnnualScoreMap(List<MapObject> annualScoreMap) {
        this.annualScoreMap = annualScoreMap;
    }

    public String getPartyPosition() {
        return partyPosition;
    }

    public void setPartyPosition(String partyPosition) {
        this.partyPosition = partyPosition;
    }

    public String getPoliticalType() {
        return politicalType;
    }

    public void setPoliticalType(String politicalType) {
        this.politicalType = politicalType;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public Integer[] getAgeSection() {
        return ageSection;
    }

    public void setAgeSection(Integer[] ageSection) {
        this.ageSection = ageSection;
    }

    public String getJoinPartyTime() {
        return joinPartyTime;
    }

    public void setJoinPartyTime(String joinPartyTime) {
        this.joinPartyTime = joinPartyTime;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getNativePlace() {
        return nativePlace;
    }

    public void setNativePlace(String nativePlace) {
        this.nativePlace = nativePlace;
    }

    public DssOrg getBranch() {
        return branch;
    }

    public void setBranch(DssOrg branch) {
        this.branch = branch;
    }

    public DssOrg getCompany() {
        return company;
    }

    public void setCompany(DssOrg company) {
        this.company = company;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public List<String> getBonusPenaltyInfo() {
        return bonusPenaltyInfo;
    }

    public void setBonusPenaltyInfo(List<String> bonusPenaltyInfo) {
        this.bonusPenaltyInfo = bonusPenaltyInfo;
    }

    public List<RadarChartMap> getRadarChartMap() {
        return radarChartMap;
    }

    public void setRadarChartMap(List<RadarChartMap> radarChartMap) {
        this.radarChartMap = radarChartMap;
    }

    public UserMeetingInfo getOrgLifeInfo() {
        return orgLifeInfo;
    }

    public void setOrgLifeInfo(UserMeetingInfo orgLifeInfo) {
        this.orgLifeInfo = orgLifeInfo;
    }

    public UserPayInfo getPayInfo() {
        return payInfo;
    }

    public void setPayInfo(UserPayInfo payInfo) {
        this.payInfo = payInfo;
    }

    public UserStudyInfo getStudyInfo() {
        return studyInfo;
    }

    public void setStudyInfo(UserStudyInfo studyInfo) {
        this.studyInfo = studyInfo;
    }

    public ActivityInfo getActivityInfo() {
        return activityInfo;
    }

    public void setActivityInfo(ActivityInfo activityInfo) {
        this.activityInfo = activityInfo;
    }

    public UserDonateInfo getDonateInfo() {
        return donateInfo;
    }

    public void setDonateInfo(UserDonateInfo donateInfo) {
        this.donateInfo = donateInfo;
    }

    public UserVolunteer getVolunteer() {
        return volunteer;
    }

    public void setVolunteer(UserVolunteer volunteer) {
        this.volunteer = volunteer;
    }

    public UserActive getUserActive() {
        return userActive;
    }

    public void setUserActive(UserActive userActive) {
        this.userActive = userActive;
    }

    public LogAspectHelper.SSLog getSsLog() {
        return ssLog;
    }

    public void setSsLog(LogAspectHelper.SSLog ssLog) {
        this.ssLog = ssLog;
    }

    public String getPhoneSecret() {
        return phoneSecret;
    }

    public void setPhoneSecret(String phoneSecret) {
        this.phoneSecret = phoneSecret;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        UserInfo userInfo = (UserInfo) o;
        return Objects.equals(regionId, userInfo.regionId) &&
                Objects.equals(year, userInfo.year) &&
                Objects.equals(userId, userInfo.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), regionId, year, userId);
    }

    public boolean equals(@NotNull UserInfo info) {
        return this.getUserId().equals(info.getUserId()) && this.getYear().equals(info.getYear());
    }
}
