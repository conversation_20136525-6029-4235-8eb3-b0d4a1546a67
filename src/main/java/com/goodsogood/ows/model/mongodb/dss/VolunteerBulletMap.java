/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Auto-generated: 2020-11-05 19:49:56
 * 志愿者进度条
 * <AUTHOR>
 */
@Data
@ApiModel
public class VolunteerBulletMap extends DssBaseInfo{

    private static final long serialVersionUID = -3585040885919203569L;

    @ApiModelProperty("进度条名称")
    private String title;

    @ApiModelProperty("当前进度")
    private Integer ranges;

    @ApiModelProperty("总进度")
    private Integer measures;

}