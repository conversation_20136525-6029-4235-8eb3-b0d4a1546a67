package com.goodsogood.ows.model.mongodb;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.List;

/**
 * @ClassName : partyPositions
 * <AUTHOR> tc
 * @Date: 2021/9/7 16:16
 * @Description : 党建阵地
 */
@Data
@ApiModel("党建阵地")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PartyPositions implements Serializable {

    @ApiModelProperty(value = "阵地编号")
    @JsonProperty(value = "party_position_id")
    private String partyPositionId;

    @ApiModelProperty(value = "阵地名称")
    @JsonProperty(value = "party_position_name")
    private String partyPositionName;

    @ApiModelProperty(value = "所属区县编号")
    @JsonProperty(value = "region_id")
    private Long regionId;

    @ApiModelProperty(value = "所属组织编号")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty(value = "可容纳人数")
    @JsonProperty(value = "galleryful")
    private Integer galleryful;

    @ApiModelProperty(value = "阵地分类")
    @JsonProperty(value = "classify")
    private String classify;

    @ApiModelProperty(value = "省编码")
    @JsonProperty(value = "province_code")
    private Long provinceCode;

    @ApiModelProperty(value = "省名称")
    @JsonProperty(value = "province")
    private String province;

    @ApiModelProperty(value = "市编码")
    @JsonProperty(value = "city_code")
    private Long cityCode;

    @ApiModelProperty(value = "市名称")
    @JsonProperty(value = "city")
    private String city;

    @ApiModelProperty(value = "区编码")
    @JsonProperty(value = "area_code")
    private Long areaCode;

    @ApiModelProperty(value = "区名称")
    @JsonProperty(value = "area")
    private String area;

    @ApiModelProperty(value = "街道编码")
    @JsonProperty(value = "street_code")
    private Long streetCode;

    @ApiModelProperty(value = "街道名称")
    @JsonProperty(value = "street")
    private String street;

    @ApiModelProperty(value = "详细地址")
    @JsonProperty(value = "address")
    private String address;

    @ApiModelProperty(value = "备注")
    @JsonProperty(value = "remark")
    private String remark;

    @ApiModelProperty(value = "阵地图片")
    @JsonProperty(value = "pictures")
    private List<PositionFile> pictures;

    @ApiModelProperty(value = "阵地RV")
    @JsonProperty(value = "vr")
    private List<PositionFile> vr;

    @ApiModelProperty(value = "最有一次修改时间")
    @JsonProperty(value = "last_update_time")
    private String lastUpdateTime;

    @ApiModelProperty(value = "最有一次修改用户")
    @JsonProperty(value = "last_update_user")
    private Long lastUpdateUser;


    //是否存在图片文件 0否 1是  默认0
    @Transient
    private Integer haveVr=0;

    //推优状态：-1:取消审核  0:审核失败   1.待审核  2.审核成功  空：原始态
    private Integer status;
}
