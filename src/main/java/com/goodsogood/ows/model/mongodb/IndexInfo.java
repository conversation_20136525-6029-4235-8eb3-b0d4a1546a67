package com.goodsogood.ows.model.mongodb;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.model.mongodb.dss.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Auto-generated: 2020-11-05 19:49:56
 * 首页对象
 * <AUTHOR>
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class IndexInfo extends DssBaseInfo {

    private static final long serialVersionUID = 4729583291995129895L;

    /** 数据更新时间 年.月.日 */
    @ApiModelProperty("数据更新时间")
    private String updateTime;

    @ApiModelProperty("顶级党组织ID")
    private Long rootId;

    /** 区县ID */
    @ApiModelProperty("区域ID")
    private Long regionId;

    /** 当前数据年份 */
    @ApiModelProperty("当前数据年份")
    private Integer year;

    /** 数据创建时间 */
    @ApiModelProperty("数据生成时间")
    private Date createTime;

    /** 评分情况 */
    @ApiModelProperty("评分情况")
    private List<IndexGrandMap> grandMap = new ArrayList<>();

    /** 党组织类别 */
    @ApiModelProperty("党组织类别")
    private List<PieObject> partyOrgCategory = new ArrayList<>();

    /** 党组织类型 */
    @ApiModelProperty("党组织类型")
    private List<PieObject> partyOrgType = new ArrayList<>();

    /** 党员数据概览 */
    @ApiModelProperty("党员数据概览")
    private List<PartyMemberInfo> partyMemberInfo = new ArrayList<>();

    /** 组织生活 */
    @ApiModelProperty("组织生活")
    private OrgLifeInfo orgLifeInfo;

    /** 党费交纳情况 */
    @ApiModelProperty("党费交纳情况")
    private PayInfo payInfo;

    /** 学习数据 */
    @ApiModelProperty("学习数据")
    private StudyInfo studyInfo;

    /** 考核情况 */
    @ApiModelProperty("考核情况")
    private List<GrandMap> evalInfo = new ArrayList<>();

    /** 消费扶贫 */
    @ApiModelProperty("消费扶贫")
    private ConsumeInfo consumeInfo;

    /** 志愿者服务数据 */
    @ApiModelProperty("志愿者服务数据")
    private VolunteerInfo volunteerInfo;

    /** 用户活跃度 */
    @ApiModelProperty("用户活跃度")
    private OrgUserActive userActive;

    private LogAspectHelper.SSLog ssLog;

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Long getRootId() {
        return rootId;
    }

    public void setRootId(Long rootId) {
        this.rootId = rootId;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public List<IndexGrandMap> getGrandMap() {
        return grandMap;
    }

    public void setGrandMap(List<IndexGrandMap> grandMap) {
        this.grandMap = grandMap;
    }

    public List<PieObject> getPartyOrgCategory() {
        return partyOrgCategory;
    }

    public void setPartyOrgCategory(List<PieObject> partyOrgCategory) {
        this.partyOrgCategory = partyOrgCategory;
    }

    public List<PieObject> getPartyOrgType() {
        return partyOrgType;
    }

    public void setPartyOrgType(List<PieObject> partyOrgType) {
        this.partyOrgType = partyOrgType;
    }

    public List<PartyMemberInfo> getPartyMemberInfo() {
        return partyMemberInfo;
    }

    public void setPartyMemberInfo(List<PartyMemberInfo> partyMemberInfo) {
        this.partyMemberInfo = partyMemberInfo;
    }

    public OrgLifeInfo getOrgLifeInfo() {
        return orgLifeInfo;
    }

    public void setOrgLifeInfo(OrgLifeInfo orgLifeInfo) {
        this.orgLifeInfo = orgLifeInfo;
    }

    public PayInfo getPayInfo() {
        return payInfo;
    }

    public void setPayInfo(PayInfo payInfo) {
        this.payInfo = payInfo;
    }

    public StudyInfo getStudyInfo() {
        return studyInfo;
    }

    public void setStudyInfo(StudyInfo studyInfo) {
        this.studyInfo = studyInfo;
    }

    public List<GrandMap> getEvalInfo() {
        return evalInfo;
    }

    public void setEvalInfo(List<GrandMap> evalInfo) {
        this.evalInfo = evalInfo;
    }

    public ConsumeInfo getConsumeInfo() {
        return consumeInfo;
    }

    public void setConsumeInfo(ConsumeInfo consumeInfo) {
        this.consumeInfo = consumeInfo;
    }

    public VolunteerInfo getVolunteerInfo() {
        return volunteerInfo;
    }

    public void setVolunteerInfo(VolunteerInfo volunteerInfo) {
        this.volunteerInfo = volunteerInfo;
    }

    public OrgUserActive getUserActive() {
        return userActive;
    }

    public void setUserActive(OrgUserActive userActive) {
        this.userActive = userActive;
    }

    public LogAspectHelper.SSLog getSsLog() {
        return ssLog;
    }

    public void setSsLog(LogAspectHelper.SSLog ssLog) {
        this.ssLog = ssLog;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        IndexInfo indexInfo = (IndexInfo) o;
        return Objects.equals(rootId, indexInfo.rootId) &&
                Objects.equals(regionId, indexInfo.regionId) &&
                Objects.equals(year, indexInfo.year);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), rootId, regionId, year);
    }
}