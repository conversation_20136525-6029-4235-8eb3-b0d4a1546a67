package com.goodsogood.ows.model.mongodb;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName : PositionFile
 * <AUTHOR> tc
 * @Date: 2021/9/8 14:18
 * @Description : 党建阵地文件
 */

@ApiModel("党建阵地文件")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PositionFile implements Serializable {

    @ApiModelProperty(value = "文件id")
    @JsonProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "文件名称")
    @JsonProperty(value = "name")
    private String name;

    @ApiModelProperty(value = "文件路径")
    @JsonProperty(value = "path")
    private String path;

    @ApiModelProperty(value = "文件原名称")
    @JsonProperty(value = "file_name")
    private String fileName;

    @ApiModelProperty(value = "文件大小")
    @JsonProperty(value = "size")
    private Integer size;
}
