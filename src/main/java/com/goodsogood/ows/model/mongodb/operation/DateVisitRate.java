package com.goodsogood.ows.model.mongodb.operation;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName : DateVisitRate
 * <AUTHOR> tc
 * @Date: 2022/4/15 15:15
 * @Description : 按日期统计访问率
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DateVisitRate {

    @JsonProperty(value = "region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;

    /**
     * 格式 YYYY-MM-DD
     */
    @JsonProperty(value = "stats_date")
    @ApiModelProperty("统计日期")
    private String statsDate;

    /**
     * 每人每天只计算第一次访问
     */
    @JsonProperty(value = "visit_num")
    @ApiModelProperty("访问人数")
    private Integer visitNum;

    @JsonProperty(value = "people_num")
    @ApiModelProperty("总人数")
    private Integer peopleNum;

    /**
     * 访问率
     */
    @JsonProperty(value = "visit_rate")
    @ApiModelProperty("访问率")
    private String visitRate;
}
