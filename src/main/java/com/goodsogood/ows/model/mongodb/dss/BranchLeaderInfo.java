package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 党支部领导班子
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class BranchLeaderInfo extends DssUser {

    private static final long serialVersionUID = 6699347729454390837L;

    @ApiModelProperty("领导干部类型 1 - 领导干部, 2 - 联系本支部的领导")
    private Integer type;
}
