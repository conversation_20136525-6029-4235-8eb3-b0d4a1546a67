package com.goodsogood.ows.model.mongodb;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.user.VideoAndPictureVO;
import com.goodsogood.ows.model.vo.user.VrVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/*
党建品牌实体
 */
@Document("PartyBrand") // 这里因为是固定的查询，写死doc的名称
public class PartyBrandBase {
    /**
     * 组织id
     */
    @JsonProperty(value = "org_id")
    private Long orgId;

    /**
     * 组织名称
     */
    @JsonProperty(value = "org_name")
    private String orgName;

    /**
     * logo
     */
    private PositionFile logo;

    /**
     * 品牌名称
     */
    @JsonProperty(value = "brand_name")
    private String brandName;

    /**
     * 释义
     */
    private String paraphrase;

    /**
     * 推优状态
     * -1:取消审核 0:审核失败 1.待审核 2.审核成功 空：原始态
     */
    private Integer status;

    /**
     * 标签
     */
    private List<String> tags;

    /**
     * 视频列表
     */
    @JsonProperty(value = "video_list")
    private VideoAndPictureVO videoList;

    /**
     * 图片列表
     */
    @JsonProperty(value = "picture_list")
    private VideoAndPictureVO pictureList;

    /**
     * 党建阵地vr-支部之窗
     */
    @JsonProperty(value = "vr_list")
    private VrVo vrList;

    public PartyBrandBase() {
    }

    public PartyBrandBase(Long orgId, String orgName, PositionFile logo, String brandName, String paraphrase, Integer status, List<String> tags, VideoAndPictureVO videoList, VideoAndPictureVO pictureList, VrVo vrList) {
        this.orgId = orgId;
        this.orgName = orgName;
        this.logo = logo;
        this.brandName = brandName;
        this.paraphrase = paraphrase;
        this.status = status;
        this.tags = tags;
        this.videoList = videoList;
        this.pictureList = pictureList;
        this.vrList = vrList;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public PositionFile getLogo() {
        return logo;
    }

    public void setLogo(PositionFile logo) {
        this.logo = logo;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getParaphrase() {
        return paraphrase;
    }

    public void setParaphrase(String paraphrase) {
        this.paraphrase = paraphrase;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public VideoAndPictureVO getVideoList() {
        return videoList;
    }

    public void setVideoList(VideoAndPictureVO videoList) {
        this.videoList = videoList;
    }

    public VideoAndPictureVO getPictureList() {
        return pictureList;
    }

    public void setPictureList(VideoAndPictureVO pictureList) {
        this.pictureList = pictureList;
    }

    public VrVo getVrList() {
        return vrList;
    }

    public void setVrList(VrVo vrList) {
        this.vrList = vrList;
    }
}
