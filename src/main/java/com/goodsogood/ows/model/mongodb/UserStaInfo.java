package com.goodsogood.ows.model.mongodb;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.vo.report.PersonElectronicReport;
import lombok.Data;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * @program: ows-sas
 * @description: 以用户为单位出报告 包括用户电子党务报告
 * @author: Mr.<PERSON>
 * @create: 2019-11-26 13:48
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@CompoundIndexes({
        @CompoundIndex(name = "user_id", def = "{'userId': -1}"),
        @CompoundIndex(name = "person_electronic_report.statistics_time", def = "{'personElectronicReport.statisticsTime': -1}"),
        @CompoundIndex(name = "person_electronic_report.user_id", def = "{'personElectronicReport.userId': -1}")
})
//@Document(collection="UserStaInfo")
public class UserStaInfo {

    @JsonProperty(value = "user_id")
    private Long userId;

    @JsonProperty(value = "region_id")
    private Long regionId;

    @JsonProperty(value = "person_electronic_report")
    private List<PersonElectronicReport> personElectronicReport;

}
