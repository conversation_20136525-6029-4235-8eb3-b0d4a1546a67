package com.goodsogood.ows.model.mongodb.report;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 组织电子党务报表-学习情况
 * @date 2019/11/28
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class StudyReportInfo implements Serializable {

    @ApiModelProperty("学习人数")
    private Integer personNum = 0;

    @ApiModelProperty("学习积分")
    private Long score = 0L;

    @ApiModelProperty("兑换书数量")
    private Long bookNum = 0L;

    @ApiModelProperty("学习积分排名第一")
    private String firstName;
}
