package com.goodsogood.ows.model.mongodb;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.vo.activity.OrgInfo;
import lombok.Data;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @program: ows-sas
 * @description: 活动报表
 * @author: Mr.<PERSON>
 * @create: 2019-11-19 08:41
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@CompoundIndexes({
        @CompoundIndex(name = "activity_id", def = "{'activityId': -1}"),
        @CompoundIndex(name = "org_id", def = "{'orgId': -1}"),
        @CompoundIndex(name = "participant_users.user_id", def = "{'participantUsers.userId': -1}"),
        @CompoundIndex(name = "participant_users.org_id", def = "{'participantUsers.orgId': -1}"),
        @CompoundIndex(name = "participant_users.participationDate", def = "{'participantUsers.participationDate': -1}"),
        @CompoundIndex(name = "win_users.user_id", def = "{'winUsers.userId': -1}")
})
//@Document(collection = "Activity")
public class ActivityInfo extends OrgInfo {

    private String id;

    /**
     * 活动id
     */
    @JsonProperty(value = "activity_id")
    private Long activityId;
    /**
     * 活动名称
     */
    @JsonProperty(value = "activity_name")
    private String activityName;

    /**
     * 活动类型
     */
    @JsonProperty(value = "activity_type")
    private Long activityType;

    /**
     * 活动类型名称
     */
    @JsonProperty(value = "activity_type_name")
    private String activityTypeName;

    /**
     * 活动创建时间
     */
    @JsonProperty(value = "activity_create_time")
    private Date activityCreatTime;

    /**
     * 与当前活动的用户表(t_activity_user)最大主键Id
     */
    @JsonProperty(value = "activity_user_id")
    private Long activityUserId;

    /**
     * 参与人数 是对participantUsers 字段冗余
     */
    @JsonProperty(value = "participant_num")
    private Integer participantNum;
    /**
     * 参与人员详情信息
     */
    @JsonProperty(value = "participant_users")
    private List<ParticipantUsers> participantUsers;

    /**
     * 活动获奖人数
     */
    @JsonProperty(value = "win_users_num")
    private Integer winUsersNum;

    /**
     * 参与活动获奖名单
     */
    @JsonProperty(value = "win_users")
    private List<WinUsers> winUsers;


    @Data
    public static class ParticipantUsers implements Serializable {
        private static final long serialVersionUID = 5912584617321272843L;

        @JsonProperty(value = "user_id")
        private Long userId;
        /**
         * 查询时间 格式yyyyMM
         */
        @JsonProperty(value = "participation_date")
        private Integer participationDate;

        /**
         * 具体参与的时间
         */
        @JsonProperty(value = "participation_time")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date participationTime;

        /**
         * 关联组织id
         */
        @JsonProperty(value = "org_id")
        private Long orgId;

        /**
         * 上级组织
         */
        @JsonProperty(value = "org_level")
        private String orgLevel;
    }

    @Data
    public static class WinUsers implements Serializable {

        private static final long serialVersionUID = 5912584617321272845L;

        @JsonProperty(value = "user_id")
        private Long userId;

        /**
         * 获取积分总数量
         */
        @JsonProperty(value = "score")
        private Integer score;

        /**
         * 获得实物礼品总数量
         */
        @JsonProperty(value = "real_thing")
        private Integer realThing;

        /**
         * 获取实物礼品名称,多个礼品逗号分隔
         */
        @JsonProperty(value = "real_things_detail")
        private String realThingsDetail;

        /**
         * 获取现金总数量
         */
        @JsonProperty(value = "cash")
        private Double cash;
    }

}
