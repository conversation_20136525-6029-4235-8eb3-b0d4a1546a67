/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Auto-generated: 2020-11-05 19:49:56
 * 党员概况
 * <AUTHOR>
 * @website
 */
@Data
@ApiModel
public class PartyMemberInfo extends DssBaseInfo{

    private static final long serialVersionUID = -8625934452867524272L;

    /** 饼图名称 */
    @ApiModelProperty("饼图名称")
    private String type;

    /** 饼图名称 */
    @ApiModelProperty("排序")
    @JsonIgnore
    private Integer order;

    /** 饼图数据 */
    @ApiModelProperty("饼图数据")
    private List<PieObject> value;

}