package com.goodsogood.ows.model.mongodb.report;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.mongodb.OrgReportInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 组织电子党务报表-党群活动
 * @date 2019/11/28
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ActivityReportInfo implements Serializable {

    @ApiModelProperty("参与活动人数")
    private Integer personNum = 0;

    @ApiModelProperty("党群活动类型维度统计列表")
    public List<Type> typeList;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public class Type implements Serializable{

        @ApiModelProperty("组织生活类型ID")
        public Long id;

        @ApiModelProperty("组织生活类型")
        public String type;

        @ApiModelProperty("开展次数")
        public Integer num = 0;
    }
}
