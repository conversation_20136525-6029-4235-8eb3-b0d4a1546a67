package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 志愿者服务情况
 * <AUTHOR>
 */
@Data
@ApiModel
public class UserVolunteer extends DssBaseInfo {

    private static final long serialVersionUID = -2474645954107008434L;

    @ApiModelProperty("参与次数")
    private Integer joinNum;

    @ApiModelProperty("服务时长")
    private Double serviceTime;

    @ApiModelProperty("志愿积分")
    private Integer volunteerScore;

    @ApiModelProperty("守时程度")
    private Double punctualGrade;

    @ApiModelProperty("服务态度")
    private Double attitudeGrade;

    @ApiModelProperty("专业水平")
    private Double professionalGrade;

}
