/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Auto-generated: 2020-11-05 19:49:56
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder(toBuilder = true)
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class DssOrgChild extends DssOrg {

    private static final long serialVersionUID = -6031492338368719486L;
    /** 书记 */
    @ApiModelProperty("书记")
    private String secretary;

    /** 组织类型 1-党委/党总支, 2-党支部 */
    @ApiModelProperty("组织类型")
    private Integer orgType;

    /** 综合得分 */
    @ApiModelProperty("综合得分")
    private Double grand;
}