package com.goodsogood.ows.model.mongodb;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.model.mongodb.dss.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 党支部详情
 * <AUTHOR>
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PartyBranchInfo extends DssBaseInfo{

    private static final long serialVersionUID = 4862766958462602338L;

    /** 数据更新时间 年.月.日 */
    @ApiModelProperty("数据更新时间")
    private String updateTime;

    /** 区县ID */
    @ApiModelProperty("区域ID")
    private Long regionId;

    /** 当前数据年份 */
    @ApiModelProperty("当前数据年份")
    private Integer year;

    /** 数据创建时间 */
    @ApiModelProperty("数据生成时间")
    private Date createTime;

    @ApiModelProperty("组织Id")
    private Long organizationId;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("组织简称")
    private String orgShortName;

    @ApiModelProperty("组织成层级关系")
    private String orgLevel;

    @ApiModelProperty("组织类型")
    private Integer orgTypeChild;

    @ApiModelProperty("党组织届次信息")
    private OrgPeriodInfo orgPeriodInfo;

    @ApiModelProperty("所属党委")
    private DssOrg company;

    @ApiModelProperty("组织下党员人数")
    private Integer partyMemberTotal;

    @ApiModelProperty("组织下党员列表")
    private List<DssUserChild> userList = new ArrayList<>();

    @ApiModelProperty("领导干部列表")
    private List<BranchLeaderInfo> leaderList = new ArrayList<>();

    @ApiModelProperty("综合得分")
    private Double sumScore;

    @ApiModelProperty("星级")
    private Integer star;

    @ApiModelProperty("雷达图")
    private List<RadarChartMap> radarChartMap = new ArrayList<>();

    @ApiModelProperty("年度得分趋势")
    private List<MapObject> annualScoreMap = new ArrayList<>();

    @ApiModelProperty("自动打分排行")
    private ScoreRanking scoreRanking;

    @ApiModelProperty("党员数据概况")
    private List<PartyMemberInfo> partyMemberInfo = new ArrayList<>();

    @ApiModelProperty("组织生活概况")
    private BranchMeetingInfo orgLifeInfo;

    @ApiModelProperty("党费交纳概况")
    private PayInfo payInfo;

    @ApiModelProperty("学习情况")
    private StudyInfo studyInfo;

    @ApiModelProperty("评分概览")
    private List<UserGrandMap> grandMap = new ArrayList<>();

    @ApiModelProperty("消费扶贫情况")
    private ConsumeInfo consumeInfo;

    @ApiModelProperty("消费扶贫情况")
    private OrgUserActive userActive;

    @ApiModelProperty("日志上下文 -> 便于查找日志")
    private LogAspectHelper.SSLog ssLog;

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel;
    }

    public Integer getOrgTypeChild() {
        return orgTypeChild;
    }

    public void setOrgTypeChild(Integer orgTypeChild) {
        this.orgTypeChild = orgTypeChild;
    }

    public OrgPeriodInfo getOrgPeriodInfo() {
        return orgPeriodInfo;
    }

    public void setOrgPeriodInfo(OrgPeriodInfo orgPeriodInfo) {
        this.orgPeriodInfo = orgPeriodInfo;
    }

    public DssOrg getCompany() {
        return company;
    }

    public void setCompany(DssOrg company) {
        this.company = company;
    }

    public Integer getPartyMemberTotal() {
        return partyMemberTotal;
    }

    public void setPartyMemberTotal(Integer partyMemberTotal) {
        this.partyMemberTotal = partyMemberTotal;
    }

    public List<DssUserChild> getUserList() {
        return userList;
    }

    public void setUserList(List<DssUserChild> userList) {
        this.userList = userList;
    }

    public List<BranchLeaderInfo> getLeaderList() {
        return leaderList;
    }

    public void setLeaderList(List<BranchLeaderInfo> leaderList) {
        this.leaderList = leaderList;
    }

    public Double getSumScore() {
        return sumScore;
    }

    public void setSumScore(Double sumScore) {
        this.sumScore = sumScore;
    }

    public Integer getStar() {
        return star;
    }

    public void setStar(Integer star) {
        this.star = star;
    }

    public List<RadarChartMap> getRadarChartMap() {
        return radarChartMap;
    }

    public void setRadarChartMap(List<RadarChartMap> radarChartMap) {
        this.radarChartMap = radarChartMap;
    }

    public List<MapObject> getAnnualScoreMap() {
        return annualScoreMap;
    }

    public void setAnnualScoreMap(List<MapObject> annualScoreMap) {
        this.annualScoreMap = annualScoreMap;
    }

    public List<PartyMemberInfo> getPartyMemberInfo() {
        return partyMemberInfo;
    }

    public void setPartyMemberInfo(List<PartyMemberInfo> partyMemberInfo) {
        this.partyMemberInfo = partyMemberInfo;
    }

    public BranchMeetingInfo getOrgLifeInfo() {
        return orgLifeInfo;
    }

    public void setOrgLifeInfo(BranchMeetingInfo orgLifeInfo) {
        this.orgLifeInfo = orgLifeInfo;
    }

    public PayInfo getPayInfo() {
        return payInfo;
    }

    public void setPayInfo(PayInfo payInfo) {
        this.payInfo = payInfo;
    }

    public StudyInfo getStudyInfo() {
        return studyInfo;
    }

    public void setStudyInfo(StudyInfo studyInfo) {
        this.studyInfo = studyInfo;
    }

    public List<UserGrandMap> getGrandMap() {
        return grandMap;
    }

    public void setGrandMap(List<UserGrandMap> grandMap) {
        this.grandMap = grandMap;
    }

    public ConsumeInfo getConsumeInfo() {
        return consumeInfo;
    }

    public void setConsumeInfo(ConsumeInfo consumeInfo) {
        this.consumeInfo = consumeInfo;
    }

    public OrgUserActive getUserActive() {
        return userActive;
    }

    public void setUserActive(OrgUserActive userActive) {
        this.userActive = userActive;
    }

    public LogAspectHelper.SSLog getSsLog() {
        return ssLog;
    }

    public void setSsLog(LogAspectHelper.SSLog ssLog) {
        this.ssLog = ssLog;
    }

    public String getOrgShortName() {
        return orgShortName;
    }

    public void setOrgShortName(String orgShortName) {
        this.orgShortName = orgShortName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        PartyBranchInfo that = (PartyBranchInfo) o;
        return Objects.equals(regionId, that.regionId) &&
                Objects.equals(year, that.year) &&
                Objects.equals(organizationId, that.organizationId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), regionId, year, organizationId);
    }
}
