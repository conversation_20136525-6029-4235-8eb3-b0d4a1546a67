package com.goodsogood.ows.model.mongodb;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.vo.activity.Donate;
import com.goodsogood.ows.model.vo.score.Books;
import com.goodsogood.ows.model.vo.score.Poverty;
import com.goodsogood.ows.model.vo.score.Study;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-11-19 11
 * score mongo 模型
 */
@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
//@Document(collection="scoreInfo")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ScoreInfo implements Serializable {

    private static final long serialVersionUID = -783943054107632612L;
    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("组织id")
    private Long orgId;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("上级组织id")
    private Long parentId;

    @ApiModelProperty("所属组织类型")
    private Integer orgType;

    @ApiModelProperty("所属组织详细类型")
    private Integer orgTypeChild;

    @ApiModelProperty("上级组织链")
    private String orgLevel;

    @ApiModelProperty("组织建立时间")
    private Date orgCreateTime;

    @ApiModelProperty("该数据所属时间")
    private String queryTime;

    @ApiModelProperty("该数据创建时间")
    private Date createTime;

    @ApiModelProperty("学习积分")
    private List<Study> studys;

    @ApiModelProperty("积分换书")
    private List<Books> books;

    @ApiModelProperty("一元捐")
    private List<Donate> donates;

    @ApiModelProperty("扶贫商城")
    private List<Poverty> povertys;

}