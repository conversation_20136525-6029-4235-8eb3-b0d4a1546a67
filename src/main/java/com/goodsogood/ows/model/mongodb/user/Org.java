package com.goodsogood.ows.model.mongodb.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 组织信息-Mongo
 *
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Org extends MongoBasicBean {

    private static final long serialVersionUID = 2996195075704013950L;

    @ApiModelProperty(value = "组织ID")
    @JsonProperty(value = "org_id")
    private Long organizationId;

    @ApiModelProperty(value = "父组织ID")
    @JsonProperty("parent_id")
    private Long parentId;

    @ApiModelProperty(value = "区县ID")
    @JsonProperty("region_id")
    private Long regionId;

    @ApiModelProperty(value = "组织名称")
    @JsonProperty("name")
    private String name;

    @ApiModelProperty(value = "组织简称")
    @JsonProperty("short_name")
    private String shortName;

    @ApiModelProperty(value = "组织类型")
    @JsonProperty("org_type")
    private Integer orgType;

    @ApiModelProperty(value = "组织类型-下级")
    @JsonProperty("org_type_child")
    private Integer orgTypeChild;

    @ApiModelProperty(value = "组织代码")
    @JsonProperty("org_code")
    private String orgCode;

    @ApiModelProperty(value = "组织联系电话")
    @JsonProperty("org_phone")
    private String orgPhone;

    @ApiModelProperty(value = "组织负责人")
    @JsonProperty("org_leader")
    private String orgLeader;

    @ApiModelProperty(value = "组织负责人电话")
    @JsonProperty("org_leader_phone")
    private String orgLeaderPhone;

    @ApiModelProperty(value = "组织所属行政区域")
    @JsonProperty("org_area")
    private String orgArea;

    @ApiModelProperty(value = "邮政编码")
    private String postcode;

    @ApiModelProperty(value = "组织联系方式")
    @JsonProperty("org_address")
    private String orgAddress;

    @ApiModelProperty(value = "管理员电话")
    @JsonProperty("manager_phone")
    private String managerPhone;

    @ApiModelProperty(value = "行业类别")
    @JsonProperty("industry_type")
    private Integer industryType;

    @ApiModelProperty(value = "区域")
    @JsonProperty("area_code")
    private String areaCode;

    @ApiModelProperty(value = "组织标识码")
    @JsonProperty("org_unique_code")
    private String orgUniqueCode;

    @ApiModelProperty(value = "组织联系人	")
    @JsonProperty("org_contacts")
    private String orgContacts;

    @ApiModelProperty(value = "顺序")
    @JsonProperty("seq")
    private Integer seq;

    @ApiModelProperty(value = "组织数量 下级组织")
    @JsonProperty("child_org_num")
    private Integer childOrgNum;

    @ApiModelProperty(value = "组织数量 组织单位")
    @JsonProperty("company_num")
    private Integer companyNum;

    @ApiModelProperty(value = "组织人员数量")
    @JsonProperty("user_num")
    private Integer userNum;

    @ApiModelProperty(value = "组织人员数量")
    @JsonProperty("party_member_num")
    private Integer partyMemberNum;

    @ApiModelProperty(value = "组织职工数量")
    @JsonProperty("work_num")
    private Integer workNum;

    @ApiModelProperty(value = "组织层级关系")
    @JsonProperty("org_level")
    private String orgLevel;

    @ApiModelProperty(value = "组织树父级路径")
    @JsonProperty("org_create_time")
    private String orgCreateTime;

    @ApiModelProperty(value = "是否激活 1-已激活 2-未激活")
    private Integer activate;

    @ApiModelProperty(value = "所属组织ID")
    @JsonProperty("owner_id")
    private Long ownerId;

    @ApiModelProperty(value = "所属组织树 1-父树 2-子树")
    @JsonProperty("owner_tree")
    private Integer ownerTree;

    @ApiModelProperty(value = "是否离退休党组织 1-是 2-否")
    @JsonProperty("is_retire")
    private Integer isRetire;

    @ApiModelProperty(value = "是否流动党员党组织 1-是 2-否")
    @JsonProperty("is_flow")
    private Integer isFlow;

    @ApiModelProperty(value = "组织关联单位是否与上级组织保持一致 1-是 2-否")
    @JsonProperty("is_consistent")
    private Integer isConsistent;

    @ApiModelProperty(value = "组织书记")
    @JsonProperty("party_leader")
    private String partyLeader;

    @ApiModelProperty(value = "标签列表")
    @JsonProperty("tag_list")
    private List<Tag> tagList;

    /** 状态 1-启用 2-禁用 3-不可修改 */
    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("create_time")
    private String createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonProperty("update_time")
    private String updateTime;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    @JsonProperty("longitude")
    private Double longitude;

    /** 纬度 */
    @ApiModelProperty(value = "纬度")
    @JsonProperty("latitude")
    private Double latitude;

    @JsonProperty("last_change_user")
    private Long lastChangeUser;

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public Integer getOrgTypeChild() {
        return orgTypeChild;
    }

    public void setOrgTypeChild(Integer orgTypeChild) {
        this.orgTypeChild = orgTypeChild;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgPhone() {
        return orgPhone;
    }

    public void setOrgPhone(String orgPhone) {
        this.orgPhone = orgPhone;
    }

    public String getOrgLeader() {
        return orgLeader;
    }

    public void setOrgLeader(String orgLeader) {
        this.orgLeader = orgLeader;
    }

    public String getOrgLeaderPhone() {
        return orgLeaderPhone;
    }

    public void setOrgLeaderPhone(String orgLeaderPhone) {
        this.orgLeaderPhone = orgLeaderPhone;
    }

    public String getOrgArea() {
        return orgArea;
    }

    public void setOrgArea(String orgArea) {
        this.orgArea = orgArea;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getOrgAddress() {
        return orgAddress;
    }

    public void setOrgAddress(String orgAddress) {
        this.orgAddress = orgAddress;
    }

    public String getManagerPhone() {
        return managerPhone;
    }

    public void setManagerPhone(String managerPhone) {
        this.managerPhone = managerPhone;
    }

    public Integer getIndustryType() {
        return industryType;
    }

    public void setIndustryType(Integer industryType) {
        this.industryType = industryType;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getOrgUniqueCode() {
        return orgUniqueCode;
    }

    public void setOrgUniqueCode(String orgUniqueCode) {
        this.orgUniqueCode = orgUniqueCode;
    }

    public String getOrgContacts() {
        return orgContacts;
    }

    public void setOrgContacts(String orgContacts) {
        this.orgContacts = orgContacts;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public Integer getChildOrgNum() {
        return childOrgNum;
    }

    public void setChildOrgNum(Integer childOrgNum) {
        this.childOrgNum = childOrgNum;
    }

    public Integer getCompanyNum() {
        return companyNum;
    }

    public void setCompanyNum(Integer companyNum) {
        this.companyNum = companyNum;
    }

    public Integer getUserNum() {
        return userNum;
    }

    public void setUserNum(Integer userNum) {
        this.userNum = userNum;
    }

    public Integer getPartyMemberNum() {
        return partyMemberNum;
    }

    public void setPartyMemberNum(Integer partyMemberNum) {
        this.partyMemberNum = partyMemberNum;
    }

    public Integer getWorkNum() {
        return workNum;
    }

    public void setWorkNum(Integer workNum) {
        this.workNum = workNum;
    }

    public String getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel;
    }

    public String getOrgCreateTime() {
        return orgCreateTime;
    }

    public void setOrgCreateTime(String orgCreateTime) {
        this.orgCreateTime = orgCreateTime;
    }

    public Integer getActivate() {
        return activate;
    }

    public void setActivate(Integer activate) {
        this.activate = activate;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Integer getOwnerTree() {
        return ownerTree;
    }

    public void setOwnerTree(Integer ownerTree) {
        this.ownerTree = ownerTree;
    }

    public Integer getIsRetire() {
        return isRetire;
    }

    public void setIsRetire(Integer isRetire) {
        this.isRetire = isRetire;
    }

    public Integer getIsFlow() {
        return isFlow;
    }

    public void setIsFlow(Integer isFlow) {
        this.isFlow = isFlow;
    }

    public Integer getIsConsistent() {
        return isConsistent;
    }

    public void setIsConsistent(Integer isConsistent) {
        this.isConsistent = isConsistent;
    }

    public String getPartyLeader() {
        return partyLeader;
    }

    public void setPartyLeader(String partyLeader) {
        this.partyLeader = partyLeader;
    }

    public List<Tag> getTagList() {
        return tagList;
    }

    public void setTagList(List<Tag> tagList) {
        this.tagList = tagList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Long getLastChangeUser() {
        return lastChangeUser;
    }

    public void setLastChangeUser(Long lastChangeUser) {
        this.lastChangeUser = lastChangeUser;
    }
}
