package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 参加的组织生活列表
 * <AUTHOR>
 */
@Data
@ApiModel
public class MeetingDevelopInfo extends DssBaseInfo{

    private static final long serialVersionUID = -5707763257693462146L;

    @ApiModelProperty("组织生活召开的日期")
    private String date;

    @ApiModelProperty("开展组织生活的组织名称")
    private String orgName;

    @ApiModelProperty("组织生活类型")
    private String type;

    @ApiModelProperty("typeId")
    private Long typeId;
}
