/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Auto-generated: 2020-11-05 19:49:56
 * 用户活跃度
 * <AUTHOR>
 */
@Data
@ApiModel
public class UserActive extends DssBaseInfo{

    private static final long serialVersionUID = -7738886731986870187L;

    @ApiModelProperty("本年访问次数")
    private Integer yearVisitNum;

    @ApiModelProperty("年度月均访问量")
    private Integer averageMonthVisitNum;

    @ApiModelProperty("最常访问模块 -> 词云图")
    private List<PieObject> visitModule = new ArrayList<>();
}