package com.goodsogood.ows.model.mongodb.user

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import io.swagger.annotations.ApiModelProperty
import java.io.Serializable

/**
 * 动态字段 mongo
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
open class MongoBasicBean : Serializable {
    @ApiModelProperty(value = "动态字段集合")
    var fields: Map<String, Any> = HashMap()

    companion object {
        private const val serialVersionUID = -8351426189973141584L
    }
}