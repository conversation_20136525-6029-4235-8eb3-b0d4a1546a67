/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Auto-generated: 2020-11-05 19:49:56
 * 组织生活数据
 * <AUTHOR>
 * @website
 */
@Data
@ApiModel
public class OrgLifeInfo extends MeetingInfo{

    private static final long serialVersionUID = 6094967167929250905L;

    /** 年度共开展支部组织生活次数 党委没有 */
    @ApiModelProperty("年度共开展支部组织生活次数")
    private Integer meetingNum;

    /** 党员大会平均召开次数 党委没有 */
    @ApiModelProperty("党员大会平均召开次数")
    private Integer partyMemberMeeting;

    /** 党支部委员会会议平均召开次数 */
    @ApiModelProperty("党支部委员会会议平均召开次数")
    private Integer PartyCommitteeMeeting;

    /** 主题党日平均召开次数 */
    @ApiModelProperty("主题党日平均召开次数")
    private Integer partyThemeMeeting;
}