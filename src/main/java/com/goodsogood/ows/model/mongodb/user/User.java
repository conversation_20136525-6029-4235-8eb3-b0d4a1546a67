package com.goodsogood.ows.model.mongodb.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.mongodb.user.MongoBasicBean;
import com.goodsogood.ows.model.mongodb.user.Tag;
import com.goodsogood.ows.model.mongodb.user.ThirdToken;
import com.goodsogood.ows.model.mongodb.user.UserOrg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import java.util.List;

/**
 * 用户信息-Mongo
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class User extends MongoBasicBean {

    private static final long serialVersionUID = -3365873411391084756L;

    @ApiModelProperty(value = "MongoDB 主键")
    private String _id;

    @ApiModelProperty(value = "用户ID")
    @JsonProperty(value = "user_id")
    private Long userId;

    @ApiModelProperty(value = "第三方ID")
    @JsonProperty(value = "third_id")
    private String thirdId;

    @ApiModelProperty(value = "登录名")
    @JsonProperty(value = "login_name")
    private String loginName;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "用户名")
    private String name;

    @ApiModelProperty(value = "座机号码")
    private String tel;

    @ApiModelProperty(value = "用户手机号-密文")
    private String phone;

    @ApiModelProperty(value = "脱敏用户手机号")
    @JsonProperty("phone_secret")
    private String phoneSecret;

    @ApiModelProperty(value = "Email")
    private String email;

    @ApiModelProperty(value = "证件类型")
    @JsonProperty("cert_type")
    private Integer certType;

    @ApiModelProperty(value = "户籍类型")
    @JsonProperty("census_type")
    private Integer censusType;

    @ApiModelProperty(value = "证件号码")
    @JsonProperty("cert_number")
    private String certNumber;

    @ApiModelProperty(value = "脱敏证件号码")
    @JsonProperty("cert_number_secret")
    private String certNumberSecret;

    @ApiModelProperty(value = "性别")
    private Integer gender;

    @ApiModelProperty(value = "出生日期")
    private String birthday;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "学历")
    private Integer education;

    @ApiModelProperty(value = "民族")
    private Integer ethnic;

    @ApiModelProperty(value = "籍贯省/直辖市")
    @JsonProperty("native_province")
    private Integer nativeProvince;

    @ApiModelProperty(value = "籍贯市/区")
    @JsonProperty("native_city")
    private Integer nativeCity;

    @ApiModelProperty(value = "籍贯")
    @JsonProperty("birthplace")
    private String birthPlace;

    @ApiModelProperty(value = "政治面貌")
    @JsonProperty("political_type")
    private Integer politicalType;

    @ApiModelProperty(value = "婚姻")
    private Integer marriage;

    @ApiModelProperty(value = "用户序列号")
    private String sourceId;

    @ApiModelProperty(value = "是否实名认证 1、已认证 2、未认证")
    @JsonProperty("is_verify")
    private Integer isVerify;

    @ApiModelProperty("是否退休 1-是 2-否")
    @JsonProperty("is_retire")
    private Integer isRetire;

    @ApiModelProperty("是否允许更新 1-是 2-否")
    @JsonProperty("is_edit")
    private Integer isEdit;

    @ApiModelProperty("是否失联 1-是 2-否")
    @JsonProperty("is_lose")
    private Integer isLose;

    @ApiModelProperty("是否流动党员党组织 1-是 2-否")
    @JsonProperty("is_flow")
    private Integer isFlow;

    @ApiModelProperty("用户类型 1-标准用户 2-游客 3-志愿者")
    @JsonProperty("user_type")
    private Integer userType;

    @ApiModelProperty(value = "住址")
    private String address;

    @ApiModelProperty(value = "公共标签列表")
    @JsonProperty("tag_list")
    private List<Tag> tagList;

    @ApiModelProperty("用户类型 1-标准用户 2-游客 3-志愿者")
    @JsonProperty("org_list")
    private List<UserOrg> orgList;

    @ApiModelProperty("第三方token列表")
    @JsonProperty("third_list")
    private List<ThirdToken> thirdList;

    @ApiModelProperty(value = "用户状态")
    private Integer status;

    @ApiModelProperty("入党日期")
    @JsonProperty("joining_time")
    private String joiningTime;

    @ApiModelProperty(value = "转正时间")
    @JsonProperty("joining_positive_time")
    private String joiningPositiveTime;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("create_time")
    private Integer createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonProperty("update_time")
    private Integer updateTime;

    @ApiModelProperty(value = "最后更新人")
    @JsonProperty("last_change_user")
    private Long lastChangeUser;

    @ApiModelProperty("离开类型1045")
    @JsonProperty("leave_type")
    private Integer leaveType;

    @ApiModelProperty("新增党员方式")
    @JsonProperty("join_type")
    private Integer joinType;

    @ApiModelProperty("工号")
    @JsonProperty("job_number")
    private String jobNumber;

    @ApiModelProperty("手写签名")
    @JsonProperty("hand_sign")
    private String handSign;

    @ApiModelProperty("照片")
    @JsonProperty("head_url")
    private String headUrl;

    @ApiModelProperty("用户自定义标签")
    @JsonProperty("tags")
    private String tags;

    @ApiModelProperty("职位")
    private String title;

    @ApiModelProperty("所属序列")
    @Column(name = "sequence")
    private Integer sequence;

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getHandSign() {
        return handSign;
    }

    public void setHandSign(String handSign) {
        this.handSign = handSign;
    }

    public String getHeadUrl() {
        return headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhoneSecret() {
        return phoneSecret;
    }

    public void setPhoneSecret(String phoneSecret) {
        this.phoneSecret = phoneSecret;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getCertType() {
        return certType;
    }

    public void setCertType(Integer certType) {
        this.certType = certType;
    }

    public Integer getCensusType() {
        return censusType;
    }

    public void setCensusType(Integer censusType) {
        this.censusType = censusType;
    }

    public String getCertNumber() {
        return certNumber;
    }

    public void setCertNumber(String certNumber) {
        this.certNumber = certNumber;
    }

    public String getCertNumberSecret() {
        return certNumberSecret;
    }

    public void setCertNumberSecret(String certNumberSecret) {
        this.certNumberSecret = certNumberSecret;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getEducation() {
        return education;
    }

    public void setEducation(Integer education) {
        this.education = education;
    }

    public Integer getEthnic() {
        return ethnic;
    }

    public void setEthnic(Integer ethnic) {
        this.ethnic = ethnic;
    }

    public Integer getNativeProvince() {
        return nativeProvince;
    }

    public void setNativeProvince(Integer nativeProvince) {
        this.nativeProvince = nativeProvince;
    }

    public Integer getNativeCity() {
        return nativeCity;
    }

    public void setNativeCity(Integer nativeCity) {
        this.nativeCity = nativeCity;
    }

    public String getBirthPlace() {
        return birthPlace;
    }

    public void setBirthPlace(String birthPlace) {
        this.birthPlace = birthPlace;
    }

    public Integer getPoliticalType() {
        return politicalType;
    }

    public void setPoliticalType(Integer politicalType) {
        this.politicalType = politicalType;
    }

    public Integer getMarriage() {
        return marriage;
    }

    public void setMarriage(Integer marriage) {
        this.marriage = marriage;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public Integer getIsVerify() {
        return isVerify;
    }

    public void setIsVerify(Integer isVerify) {
        this.isVerify = isVerify;
    }

    public Integer getIsRetire() {
        return isRetire;
    }

    public void setIsRetire(Integer isRetire) {
        this.isRetire = isRetire;
    }

    public Integer getIsEdit() {
        return isEdit;
    }

    public void setIsEdit(Integer isEdit) {
        this.isEdit = isEdit;
    }

    public Integer getIsLose() {
        return isLose;
    }

    public void setIsLose(Integer isLose) {
        this.isLose = isLose;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public List<Tag> getTagList() {
        return tagList;
    }

    public void setTagList(List<Tag> tagList) {
        this.tagList = tagList;
    }

    public List<UserOrg> getOrgList() {
        return orgList;
    }

    public void setOrgList(List<UserOrg> orgList) {
        this.orgList = orgList;
    }

    public List<ThirdToken> getThirdList() {
        return thirdList;
    }

    public void setThirdList(List<ThirdToken> thirdList) {
        this.thirdList = thirdList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getJoiningTime() {
        return joiningTime;
    }

    public void setJoiningTime(String joiningTime) {
        this.joiningTime = joiningTime;
    }

    public String getJoiningPositiveTime() {
        return joiningPositiveTime;
    }

    public void setJoiningPositiveTime(String joiningPositiveTime) {
        this.joiningPositiveTime = joiningPositiveTime;
    }

    public Integer getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Integer updateTime) {
        this.updateTime = updateTime;
    }

    public Long getLastChangeUser() {
        return lastChangeUser;
    }

    public void setLastChangeUser(Long lastChangeUser) {
        this.lastChangeUser = lastChangeUser;
    }

    public Integer getLeaveType() {
        return leaveType;
    }

    public void setLeaveType(Integer leaveType) {
        this.leaveType = leaveType;
    }

    public Integer getJoinType() {
        return joinType;
    }

    public void setJoinType(Integer joinType) {
        this.joinType = joinType;
    }

    public String getThirdId() {
        return thirdId;
    }

    public void setThirdId(String thirdId) {
        this.thirdId = thirdId;
    }

    public Integer getIsFlow() {
        return isFlow;
    }

    public void setIsFlow(Integer isFlow) {
        this.isFlow = isFlow;
    }
}
