package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Auto-generated: 2020-11-05 19:49:56
 * 评分情况饼图
 * <AUTHOR>
 */
@Data
@ApiModel
public class UserGrandMap extends PieObject{

    private static final long serialVersionUID = 8720673854494603176L;

    /** 党员列表 */
    @ApiModelProperty("党员列表")
    private List<DssUserChild> userList = new ArrayList<>();
}
