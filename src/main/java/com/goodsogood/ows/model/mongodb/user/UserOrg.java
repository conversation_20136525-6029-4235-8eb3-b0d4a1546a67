package com.goodsogood.ows.model.mongodb.user;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserOrg {

    @ApiModelProperty(value = "组织ID")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty(value = "组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty(value = "组织类型")
    @JsonProperty("org_type")
    private Integer orgType;

    @ApiModelProperty(value = "组织类型-下级")
    @JsonProperty("org_type_child")
    private Integer orgTypeChild;

    @ApiModelProperty(value = "组织层级关系")
    @JsonProperty("org_level")
    private String orgLevel;

    @ApiModelProperty(value = "原组织ID")
    @JsonProperty("old_org_id")
    private Long oldOrgId;

    @ApiModelProperty(value = "原组织名称")
    @JsonProperty("old_org_name")
    private String oldOrgName;

    @ApiModelProperty(value = "区域ID")
    @JsonProperty(value = "region_id")
    private Long regionId;

    @ApiModelProperty(value = "职务")
    private String position;

    @ApiModelProperty(value = "职务")
    @JsonProperty(value = "position_code")
    private Long positionCode;

    @ApiModelProperty("是否是当前组织员工 1-是 0-否 2:为新增领导班子时新增")
    @JsonProperty("is_employee")
    private Integer isEmployee;

    @ApiModelProperty(value = "进入支部时间")
    @JsonProperty("join_party_time")
    private String joinPartyTime;

    @ApiModelProperty(value = "进入支部类型 1-市内转入 2-市外转入")
    @JsonProperty("join_party_type")
    private Integer joinPartyType;

    @ApiModelProperty("技术等级")
    @JsonProperty("job_grade")
    private Integer jobGrade;

    @ApiModelProperty("党组织")
    @JsonProperty("communist")
    private Integer communist;

    @ApiModelProperty("团组织")
    @JsonProperty("youth_league")
    private Integer youthLeague;

    @ApiModelProperty("工会组织")
    @JsonProperty("union_member")
    private Integer unionMember;

    @ApiModelProperty("妇女组织")
    @JsonProperty("women_league")
    private Integer womenLeague;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("create_time")
    private Long createTime;

    @ApiModelProperty(value = "角色列表")
    @JsonProperty("role_list")
    private List<Role> roleList;

    @ApiModelProperty(value = "系统业务标签列表")
    @JsonProperty("tag_list")
    private List<Tag> tagList;

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public Integer getOrgTypeChild() {
        return orgTypeChild;
    }

    public void setOrgTypeChild(Integer orgTypeChild) {
        this.orgTypeChild = orgTypeChild;
    }

    public String getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel;
    }

    public Long getOldOrgId() {
        return oldOrgId;
    }

    public void setOldOrgId(Long oldOrgId) {
        this.oldOrgId = oldOrgId;
    }

    public String getOldOrgName() {
        return oldOrgName;
    }

    public void setOldOrgName(String oldOrgName) {
        this.oldOrgName = oldOrgName;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Long getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(Long positionCode) {
        this.positionCode = positionCode;
    }

    public Integer getIsEmployee() {
        return isEmployee;
    }

    public void setIsEmployee(Integer isEmployee) {
        this.isEmployee = isEmployee;
    }

    public String getJoinPartyTime() {
        return joinPartyTime;
    }

    public void setJoinPartyTime(String joinPartyTime) {
        this.joinPartyTime = joinPartyTime;
    }

    public Integer getJoinPartyType() {
        return joinPartyType;
    }

    public void setJoinPartyType(Integer joinPartyType) {
        this.joinPartyType = joinPartyType;
    }

    public Integer getJobGrade() {
        return jobGrade;
    }

    public void setJobGrade(Integer jobGrade) {
        this.jobGrade = jobGrade;
    }

    public Integer getCommunist() {
        return communist;
    }

    public void setCommunist(Integer communist) {
        this.communist = communist;
    }

    public Integer getYouthLeague() {
        return youthLeague;
    }

    public void setYouthLeague(Integer youthLeague) {
        this.youthLeague = youthLeague;
    }

    public Integer getUnionMember() {
        return unionMember;
    }

    public void setUnionMember(Integer unionMember) {
        this.unionMember = unionMember;
    }

    public Integer getWomenLeague() {
        return womenLeague;
    }

    public void setWomenLeague(Integer womenLeague) {
        this.womenLeague = womenLeague;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public List<Role> getRoleList() {
        return roleList;
    }

    public void setRoleList(List<Role> roleList) {
        this.roleList = roleList;
    }

    public List<Tag> getTagList() {
        return tagList;
    }

    public void setTagList(List<Tag> tagList) {
        this.tagList = tagList;
    }
}
