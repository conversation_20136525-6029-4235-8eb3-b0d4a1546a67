package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 党组织届次信息
 * <AUTHOR>
 */
@Data
@ApiModel
public class OrgPeriodInfo extends DssBaseInfo{

    private static final long serialVersionUID = -6902165416479350991L;

    @ApiModelProperty("书记")
    private List<DssUser> partyLeader;

    @ApiModelProperty("副书记")
    private List<DssUser> partyLeaderSec;

    @ApiModelProperty("其他成员")
    private List<DssUser> leaderOtherGroups;

    @ApiModelProperty("届次有效期")
    private String validTime;

    @ApiModelProperty("组织类型中文")
    private String orgType;

}