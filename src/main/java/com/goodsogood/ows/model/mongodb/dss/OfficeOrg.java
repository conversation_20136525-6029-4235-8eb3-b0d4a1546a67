package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 首页机关党委数据
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder(toBuilder = true)
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class OfficeOrg extends DssOrgChild {

	private static final long serialVersionUID = 5760828335322250022L;
	/** 加入排序 */
	@ApiModelProperty("年度得分列表")
	private List<YearGrand> yearGrandList;

	@ApiModelProperty("坐标")
	private Double[] coordinate;

	@ApiModelProperty("详细地址")
	private String address;
}
