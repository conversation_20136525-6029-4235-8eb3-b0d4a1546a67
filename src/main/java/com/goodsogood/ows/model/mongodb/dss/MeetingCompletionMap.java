/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Auto-generated: 2020-11-05 19:49:56
 * 组织生活完成度
 * <AUTHOR>
 */
@Data
@ApiModel
public class MeetingCompletionMap extends DssBaseInfo{

    private static final long serialVersionUID = 994460009308369363L;

    /** 已完成 */
    @ApiModelProperty("已完成")
    private List<Integer> complete = new ArrayList<>();

    /** 未完成 */
    @ApiModelProperty("未完成")
    private List<Integer> incomplete = new ArrayList<>();
}