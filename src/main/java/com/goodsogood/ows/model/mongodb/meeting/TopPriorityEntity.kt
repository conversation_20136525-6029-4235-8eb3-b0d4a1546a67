package com.goodsogood.ows.model.mongodb.meeting

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer
import com.goodsogood.ows.configuration.MongoCollectionNameConfig4Region
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import org.hibernate.validator.constraints.Length
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.stereotype.Component
import java.io.Serial
import java.io.Serializable
import java.time.LocalDate
import java.time.LocalDateTime
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotNull
import javax.validation.constraints.Pattern

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Document("#{@TopPriorityRepositoryCustomImpl.getCollectionName()}")
data class TopPriorityEntity @JvmOverloads constructor(
    // 标题
    @Indexed
    @get:NotBlank(message = "标题不能为空")
    @get:Length(min = 1, max = 200, message = "标题长度为1-200个字")
    var title: String? = null,
    // 摘要
    @Indexed
    @get:Length(min = 1, max = 3000, message = "摘要长度为1-3000个字")
    var summary: String? = null,
    // 内容
    @Indexed
    @get:Length(min = 1, max = 5000, message = "内容长度为1-5000个字")
    var content: String? = null,

    @get:Length(min = 1, max = 3000, message = "来源长度为1-3000个字")
    var source: String? = null,

    // 外链,长度 1~200个字，通过正则表达式验证需要 http:// 或者 https://开头
    @Indexed
    @get:Length(min = 1, max = 200, message = "外链长度为1-200个字")
    @get:Pattern(regexp = "^(http|https)://.*", message = "外链格式不正确")
    // @Field("ex_link")
    var exLink: String? = null,

    // 附件
    var files: MutableList<MeetingFileForm>? = mutableListOf(),
    // 是否关联 1：是，0：否 (查询的时候，需要替换成 本组织是否已经关联	1：是，0：否)
    var associated: Int? = 0,
//    // 关联的组织id
//    var associatedOrgIds: MutableList<Long> = mutableListOf(),
//    // 关联的会议id
//    var meetings: MutableList<Long> = mutableListOf(),
//    // 关联会议时间学习时间 yyyy-MM-dd
//    var meetingTimes: MutableList<String> = mutableListOf(),

    var meetings: MutableList<TopPriorityMeeting>? = mutableListOf(),
    // 媒体发布时间
    @Indexed
    @get: JsonFormat
        (shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateSerializer::class)
    @JsonDeserialize(using = LocalDateDeserializer::class)
    @get:NotNull(message = "媒体发布时间不能为空")
    // @Field("source_time")
    var sourceTime: LocalDate? = null,
    // 创建人id
    // @Field("user_id")
    var userId: Long? = null,
    // 创建人
    // @Field("user_name")
    var userName: String? = null,
    // 创建组织id
    // @Field("org_id")
    var orgId: Long? = null,
    // 创建组织名称
    // @Field("org_name")
    var orgName: String? = null,
    // 单位id|登录的时候会获得
    @get:NotNull(message = "单位id不能为空")
    var unitOrgId: Long? = null,
    // | unit_org_name | string | 20 | Y | 单位名称|登录的时候会获得|
    @get:NotNull(message = "单位名称不能为空")
    var unitOrgName: String? = null,
    // | unit_short_name | string | 20 | Y | 单位简称|登录的时候会获得|
    var unitShortName: String? = null,
    // 创建时间
    @get: JsonFormat
        (shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Indexed
    @JsonSerialize(using = LocalDateTimeSerializer::class)
    @JsonDeserialize(using = LocalDateTimeDeserializer::class)
    // @Field("create_time")
    var createTime: LocalDateTime? = null,
    // 修改时间
    @get: JsonFormat
        (shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Indexed
    @JsonSerialize(using = LocalDateTimeSerializer::class)
    @JsonDeserialize(using = LocalDateTimeDeserializer::class)
    // @Field("update_time")
    var updateTime: LocalDateTime? = null,
    @Id
    // @Field("top_priority_id")
    var topPriorityId: String? = null,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class TopPriorityMeeting @JvmOverloads constructor(
    // 关联的组织id
    // @Field("associated_org")
    var associatedOrg: Long? = null,
    // 关联的单位name
    // @Field("associated_org_name")
    var associatedName: String? = null,
    var associatedUnitOrgId: Long? = null,
    // | unit_org_name | string | 20 | Y | 单位名称|登录的时候会获得|
    var associatedUnitOrgName: String? = null,
    // | unit_short_name | string | 20 | Y | 单位简称|登录的时候会获得|
    // 学习组织所属单位(简称)
    var associatedUnitShortName: String? = null,
    // 关联的会议id
    // @Field("meeting_id")
    var meetingId: Long? = null,
    // 关联的会议title
    // @Field("meeting_title")
    var meetingTitle: String? = null,
    // 关联会议时间学习时间 yyyy-MM-dd
    @Indexed
    @get: JsonFormat
        (shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateSerializer::class)
    @JsonDeserialize(using = LocalDateDeserializer::class)
    var meetingTime: LocalDate? = null,
    // 关联会议类型(多个)
    // @Field("meeting_types")
    var meetingTypes: String? = null,
    // 关联会议类型名称
    // @Field("meeting_type_names")
    var meetingTypeNames: String? = null,

    // 间隔时间（day）
    var diffDays: Int? = null,
)

@Component("TopPriorityRepositoryCustomImpl")
class TopPriorityRepositoryCustomImpl(val mongoCollectionNameConfig4Region: MongoCollectionNameConfig4Region) :
    ConfigRepositoryCustom {
    override var collectionName: String? = null
        get() = TopPriorityRepositoryCustomImpl.collectionName + "-" + mongoCollectionNameConfig4Region.region

    companion object {
        private const val collectionName = ConfigRepositoryCustom.topPriority
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class MeetingFileForm @JvmOverloads constructor(
    @ApiModelProperty("文件ID")
    @JsonProperty(value = "file_id")
    var fileId: Long? = null,
    /**
     * [com.goodsogood.ows.common.FileSourceEnum]
     */
    @ApiModelProperty("文件来源 详情见上面枚举类")
    var source: Int? = null,

    @ApiModelProperty("附件名称")
    var name: String? = null,

    @ApiModelProperty("附件路径")
    var path: String? = null,

    @ApiModelProperty("文件原名称")
    @JsonProperty(value = "file_name")
    var fileName: String? = null,

    @ApiModelProperty("文件大小（byte）")
    var size: Long? = null,
) : Serializable {

    override fun toString(): String {
        return "MeetingFileForm(fileId=$fileId, source=$source, name=$name, path=$path, fileName=$fileName, size=$size)"
    }

    companion object {
        @Serial
        private const val serialVersionUID: Long = 2460847685239702917L
    }
}
