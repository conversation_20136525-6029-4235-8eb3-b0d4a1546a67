package com.goodsogood.ows.model.mongodb.fusion

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.io.Serializable

/**
 * 党业融合父类
 * <AUTHOR>
 * @createTime 2023年03月10日 17:27:00
 */
@ApiModel("党业融合基础详情父类")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
open class FusionBaseData : Serializable {

    @ApiModelProperty("单位ID")
    var unitId: Long? = null

    @ApiModelProperty("单位名称")
    var unitName: String? = null

    @ApiModelProperty("指标ID")
    var itemId: Long? = null

    @ApiModelProperty("年份")
    var year: Int? = null

    @ApiModelProperty("月份")
    var month: Int? = null

    @ApiModelProperty("数据更新时间")
    var updateTime: Long? = null

    companion object {
        private const val serialVersionUID: Long = 1874089644701334908L
    }
}

/**
 * 创先争优详情
 * <AUTHOR>
 */
@ApiModel("创先争优详情")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class ExcellentDetail : FusionBaseData() {
    @ApiModelProperty("ID")
    var sourceId: Long? = null

    @ApiModelProperty("姓名")
    var sourceName: String? = null

    @ApiModelProperty("手机号")
    var phone: String? = null

    @ApiModelProperty("奖励ID")
    var rewardId: Long? = null

    @ApiModelProperty("奖励名称")
    var rewardName: String? = null

    @ApiModelProperty("奖励级别")
    var rewardLevel: String? = null

    @ApiModelProperty("赋分")
    var score: Double = 0.0

    companion object {
        private const val serialVersionUID: Long = 2316693115536867679L
    }
}

/**
 * 云上组织建设
 * <AUTHOR>
 */
@ApiModel("云上组织建设")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class EcpOrgDetail : FusionBaseData() {
    @ApiModelProperty("单位分类code")
    var unitCategoryCode: Int? = null

    @ApiModelProperty("单位分类名称")
    var unitCategoryName: String? = null

    @ApiModelProperty("专卖数量")
    var monopolyNum: Int = 0

    @ApiModelProperty("专卖云区组织名称")
    var monopolyName: String? = null
    var monopolyViewName: String? = null

    @ApiModelProperty("营销数量")
    var marketingNum: Int = 0

    @ApiModelProperty("营销云区组织名称")
    var marketingName: String? = null
    var marketingViewName: String? = null

    @ApiModelProperty("综合数量")
    var synthesisNum: Int = 0

    @ApiModelProperty("综合云区组织名称")
    var synthesisName: String? = null
    var synthesisViewName: String? = null

    @ApiModelProperty("烟草数量")
    var tobaccoNum: Int = 0

    @ApiModelProperty("烟叶云区组织名称")
    var tobaccoName: String? = null
    var tobaccoViewName: String? = null

    @ApiModelProperty("赋分")
    var score: Double = 0.0

    companion object {
        private const val serialVersionUID: Long = 6820112867808208529L
    }
}

/**
 * 党务干部交流
 */
@ApiModel("党务干部交流")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class MemberChangeDetail : FusionBaseData() {
    @ApiModelProperty("交流方向")
    var changeDirection: String? = null

    @ApiModelProperty("党员ID")
    var userId: Long? = null

    @ApiModelProperty("党员姓名")
    var userName: String? = null

    @ApiModelProperty("党员手机号")
    var phone: String? = null

    @ApiModelProperty("详情")
    var detail: String? = null

    @ApiModelProperty("去掉 专职党务工作者 标签，是否完成 1-是 0-否")
    var removeTagDone: Int = 0

    @ApiModelProperty("新增 专职党务工作者 标签，是否完成 1-是 0-否")
    var addTagDone: Int = 0

    companion object {
        private const val serialVersionUID: Long = -1764301504520683560L
    }
}

/**
 * 党组责任落实
 * 领导小组履职
 * 理论学习情况
 * 主题党日活动
 */
@ApiModel("党组责任落实、领导小组履职、理论学习情况、主题党日活动")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class MeetingDetail : FusionBaseData() {
    @ApiModelProperty("会议ID")
    var meetingId: Long? = null

    @ApiModelProperty("会议名称")
    var meetingName: String? = null

    @ApiModelProperty("会议类型")
    var meetingType: String? = null

    @ApiModelProperty("会议时间")
    var meetingDate: String? = null

    @ApiModelProperty("是否与业务相关")
    var hasRelevant: Boolean? = null

    @ApiModelProperty("临时")
    var tag: String? = null

    companion object {
        private const val serialVersionUID: Long = -9081299027564925806L
    }
}

/**
 * 领导小组履职
 */
@ApiModel("领导小组履职")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class LeaderDutyDetail : FusionBaseData() {
    @ApiModelProperty("会议ID")
    var meetingId: Long? = null

    @ApiModelProperty("会议名称")
    var meetingName: String? = null

    @ApiModelProperty("会议时间")
    var meetingDate: String? = null

    @ApiModelProperty("是否与业务相关")
    var hasRelevant: Boolean = false

    @ApiModelProperty("临时")
    var tag: String? = null

    companion object {
        private const val serialVersionUID: Long = 3901399514431274782L
    }
}

/**
 * 双重组织生活
 */
@ApiModel("双重组织生活")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class DualLifeDetail(
    @ApiModelProperty("领导人员ID") var userId: Long? = null,
    @ApiModelProperty("领导人员姓名") var userName: String?? = null,
    @ApiModelProperty("参加次数") var joinNum: Int = 0,
    @ApiModelProperty("是否满足要求") var hasSatisfy: Boolean = false,
    @ApiModelProperty("临时") var orgId: Long? = null,

    ) : FusionBaseData() {
    companion object {
        private const val serialVersionUID: Long = -535141124310572190L
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as DualLifeDetail
        if (userId != other.userId) return false
        if (orgId != other.orgId) return false
        return true
    }

    override fun hashCode(): Int {
        var result = userId?.hashCode() ?: 0
//        result = 31 * result + (userName?.hashCode() ?: 0)
//        result = 31 * result + joinNum
//        result = 31 * result + hasSatisfy.hashCode()
        result = 31 * result + (orgId?.hashCode() ?: 0)
        return result
    }

}

/**
 * 领导联系基层
 */
@ApiModel("领导联系基层")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class LeadersContactDetail (
    @ApiModelProperty("领导人员ID")
    var userId: Long? = null,

    @ApiModelProperty("领导人员姓名")
    var userName: String? = null,

    @ApiModelProperty("联系支部")
    var contactOrg: String? = null,

    var orgId: String? = null,

    @ApiModelProperty("参加次数")
    var joinNum: Int = 0,

    @ApiModelProperty("是否满足要求")
    var hasSatisfy: Boolean = false,
    var meetingId: Long? = null)
    : FusionBaseData() {
    companion object {
        private const val serialVersionUID: Long = 2873054684306855244L
    }


}

/**
 * 云区参与情况
 */
@ApiModel("云区参与情况")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class EcpJoinDetail : FusionBaseData() {
    @ApiModelProperty("用户ID")
    var userId: Long? = null

    @ApiModelProperty("参加次数")
    var joinNum: Int = 0

    companion object {
        private const val serialVersionUID: Long = 8155195365515606006L
    }

}

/**
 * 云区活动情况
 */
@ApiModel("云区活动情况")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class EcpActivityDetail : FusionBaseData() {
    @ApiModelProperty("云上组织ID")
    var orgId: String? = null

    @ApiModelProperty("云上组织名称")
    var orgName: String? = null

    @ApiModelProperty("本月发布任务数")
    var taskNum: Int = 0

    @ApiModelProperty("平均发布任务数")
    var averageTaskNum: Double = 0.0

    companion object {
        private const val serialVersionUID: Long = 1144169657182649779L
    }
}

/**
 * 系统使用情况
 */
@ApiModel("系统使用情况")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class SystemUsageDetail : FusionBaseData() {
    @ApiModelProperty("人员ID")
    var userId: Long? = null

    @ApiModelProperty("人员姓名")
    var userName: String? = null

    @ApiModelProperty("所在支部ID")
    var orgId: String? = null

    @ApiModelProperty("所在支部名称")
    var orgName: String? = null

    @ApiModelProperty("积分")
    var score: Double = 0.0

    companion object {
        private const val serialVersionUID: Long = 3162760885484665943L
    }
}

/**
 * 目标基类
 */
@ApiModel("目标基类")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
open class TargetBase : FusionBaseData() {

    @ApiModelProperty("单位id")
    var orgId: String? = null

    @ApiModelProperty("积分")
    var itemScore: Double = 0.0

    companion object {
        private const val serialVersionUID: Long = 3165320885484665943L
    }
}

/**
 * 党建考核情况
 */
@ApiModel("党建考核情况")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class PartyBuildTarget : TargetBase() {
    companion object {
        private const val serialVersionUID: Long = 4165320885484665943L
    }
}


/**
 * 业务考核情况
 */
@ApiModel("业务考核情况")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class BusinessTarget : TargetBase() {
    companion object {
        private const val serialVersionUID: Long = 4165320885484665943L
    }
}


/**
 * 干群满意情况
 */
@ApiModel("干群满意情况")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class SatisfiedTarget : TargetBase() {
    companion object {
        private const val serialVersionUID: Long = 4165320885484665943L
    }
}

/**
 * 党群绩效对比
 */
@ApiModel("党群绩效对比")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class PartyGroupCompareTarget : FusionBaseData() {
    @ApiModelProperty("月度绩效得分")
    var partyMemberAvgScore: Double = 0.0


    @ApiModelProperty("月度绩效得分")
    var notPartyMemberAvgScore: Double = 0.0
}


/**
 * 党群绩效对比
 */
@ApiModel("党群绩效对比")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class PartyGroupCompareTargetDetail : FusionBaseData() {
    @ApiModelProperty("序号")
    var num: Int? = null

    @ApiModelProperty("用户姓名")
    var userName: String? = null

    @ApiModelProperty("手机号")
    var phone: String? = null

    @ApiModelProperty("所在部门")
    var department: String? = null

    @ApiModelProperty("是否党员")
    var isPartyMember: Int? = null


    @ApiModelProperty("所在支部")
    var branch: String? = null

    @ApiModelProperty("月度绩效得分")
    var itemScore: Double = 0.0
}