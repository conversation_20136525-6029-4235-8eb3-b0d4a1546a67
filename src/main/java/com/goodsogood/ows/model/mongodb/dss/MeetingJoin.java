package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 组织生活
 * <AUTHOR>
 */
@Data
@ApiModel
public class MeetingJoin extends DssBaseInfo {

    private static final long serialVersionUID = -3974112155615383040L;

    @ApiModelProperty("组织生活名称")
    private String name;

    @ApiModelProperty("组织生活数据列表")
    private List<Integer> data;

}
