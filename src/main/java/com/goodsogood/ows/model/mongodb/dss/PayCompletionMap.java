/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Auto-generated: 2020-11-05 19:49:56
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class PayCompletionMap extends DssBaseInfo{

    private static final long serialVersionUID = -4452065279112372602L;

    @ApiModelProperty("已交齐数量")
    private List<Integer> complete = new ArrayList<>();

    @ApiModelProperty("未交齐数量")
    private List<Integer> incomplete = new ArrayList<>();
}