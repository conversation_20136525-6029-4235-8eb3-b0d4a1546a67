/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.goodsogood.eps.jackson.CustomDoubleSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Auto-generated: 2020-11-05 19:49:56
 * 消费扶贫情况
 * <AUTHOR>
 */
@Data
@ApiModel
public class ConsumeInfo extends DssBaseInfo{

    private static final long serialVersionUID = 2198859018465541264L;

    @ApiModelProperty("累计共购买单数")
    private Integer buyTotal;

    @ApiModelProperty("累计共消费金额")
    @JsonSerialize(using = CustomDoubleSerialize.class)
    private Double payTotal;

    @ApiModelProperty("累计参与人数")
    private Integer userTotal;

    @ApiModelProperty("累计配捐金额")
    private Double donateTotal;

    @ApiModelProperty("本年度共购买单数")
    private Integer yearBuyTotal;

    @ApiModelProperty("本年度共消费金额")
    @JsonSerialize(using = CustomDoubleSerialize.class)
    private Double yearPayTotal;

    @ApiModelProperty("本年度参与人数")
    private Integer yearUserTotal;

    @ApiModelProperty("本年度配捐金额")
    @JsonSerialize(using = CustomDoubleSerialize.class)
    private Double yearDonateTotal;

    @ApiModelProperty("消费扶贫饼状图")
    private List<PieObject> consumeMap = new ArrayList<>();

}