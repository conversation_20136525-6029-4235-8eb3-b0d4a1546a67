/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Auto-generated: 2020-11-05 19:49:56
 * 每月1号党费交纳趋势图
 * <AUTHOR>
 */
@Data
@ApiModel
public class PayTrendMap extends DssBaseInfo{

    private static final long serialVersionUID = 4338386445057175099L;

    @ApiModelProperty("人数百分比")
    private List<Double> person = new ArrayList<>();

    @ApiModelProperty("金额百分比")
    private List<Double> money = new ArrayList<>();

}