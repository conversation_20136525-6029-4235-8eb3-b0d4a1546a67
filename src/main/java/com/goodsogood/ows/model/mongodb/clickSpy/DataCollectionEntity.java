package com.goodsogood.ows.model.mongodb.clickSpy;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
/**
 * Create by FuXiao on 2019/10/16
 */
@Data
public class DataCollectionEntity implements Serializable {

    /**
     * 用户id
     */
    @JsonProperty("user_id")
    private String userId;
    /**
     * 操作类型 A点击 B点赞 C取消赞
     */
    private String type;
    /**
     * 操作对象（暂指消息id）
     */
    private String object;
    /**
     * 数量（默认为1，用于购买或金额等的累加统计）
     */
    private Double amount;
    /**
     * 调用时间
     */
    @JsonProperty("transfer_time")
    private Date transferTime;
    /**
     * 接收时间
     */
    @JsonProperty("receive_time")
    private Date receiveTime;
    /**
     * 渠道名称
     */
    private String channal;
    /**
     * uuid
     */
    private String uuid;
    /**
     * 操作对象前缀
     */
    private String prefix;
    /**
     * 分享者id
     */
    @JsonProperty("sharer_id")
    private String sharerId;
    /**
     * 区县id
     */
    @JsonProperty("region_id")
    private Long regionId;
}
