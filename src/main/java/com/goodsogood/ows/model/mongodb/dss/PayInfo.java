/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Auto-generated: 2020-11-05 19:49:56
 * 党费交纳数据
 * <AUTHOR>
 */
@Data
@ApiModel
public class PayInfo extends DssBaseInfo {

    private static final long serialVersionUID = 4152498560766943564L;

    /** 年度交纳党费数据 为了不变为科学计数法，修改类型为string*/
    @ApiModelProperty("年度交纳党费数据")
    private String curYear;

    /** 党费交纳完成度 */
    @ApiModelProperty("党费交纳完成度")
    private PayCompletionMap payCompletionMap;

    /** 党费交纳趋势图 */
    @ApiModelProperty("党费交纳趋势图")
    private PayTrendMap payTrendMap;
}