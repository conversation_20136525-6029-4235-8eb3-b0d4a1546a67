package com.goodsogood.ows.model.mongodb.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 标签 - Mongo
 *
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Tag {

    @ApiModelProperty(value = "标签ID")
    @JsonProperty(value = "tag_id")
    private Long tagId;

    @ApiModelProperty(value = "标签名称")
    @JsonProperty(value = "tag_name")
    private String tagName;

    @ApiModelProperty(value = "区县ID")
    @JsonProperty(value = "region_id")
    private Long regionId;

    @ApiModelProperty(value = "标签类型")
    @JsonProperty(value = "tag_type")
    private Integer tagType;
}