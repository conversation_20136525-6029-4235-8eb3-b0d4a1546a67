package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户学习数据
 * <AUTHOR>
 */
@Data
@ApiModel
public class UserStudyInfo extends DssBaseInfo{

    private static final long serialVersionUID = 667011655429773289L;

    @ApiModelProperty("累计在七一书院参加学习次数")
    private Integer studyNum;

    @ApiModelProperty("累计获得学分")
    private Integer studyInScore;

    @ApiModelProperty("累计积分兑换图书册书")
    private Integer book;

    @ApiModelProperty("累计使用积分")
    private Integer studyOutScore;

    @ApiModelProperty("一元捐累计捐赠积分")
    private Integer donateScore;
}
