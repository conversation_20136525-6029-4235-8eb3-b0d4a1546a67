package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 人员党费交纳情况
 * <AUTHOR>
 */
@Data
@ApiModel
public class UserPayInfo extends DssBaseInfo{

    private static final long serialVersionUID = -2984791492959156503L;

    @ApiModelProperty("党费标准")
    private Double standard;

    @ApiModelProperty("该年度党费交纳趋势图")
    private List<PayInitiative> trend = new ArrayList<>();
}
