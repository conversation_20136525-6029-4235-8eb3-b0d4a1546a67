package com.goodsogood.ows.model.mongodb.dss;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.goodsogood.eps.jackson.CustomDoubleSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户消费扶贫
 * <AUTHOR>
 */
@Data
@ApiModel
public class UserDonateInfo extends DssBaseInfo {

    private static final long serialVersionUID = -1353192902318182433L;

    @ApiModelProperty("累计本年度共购买订单数")
    private Integer orderNum;

    @ApiModelProperty("累计本年度共消费金额")
    @JsonSerialize(using = CustomDoubleSerialize.class)
    private Double amount;

    @ApiModelProperty("消费喜好")
    private List<PieObject> preference = new ArrayList<>();
}
