package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> ruoyu
 * @date : 2020/12/2
 */
@Data
public class OrgBaseLeaderInfo extends BranchLeaderInfo {


    private static final long serialVersionUID = -741414558297470510L;

    @ApiModelProperty("职务")
    private String position;

    @ApiModelProperty("是否单位负责人 1:是 0:否")
    private Integer isHead;

    @ApiModelProperty("所属查询的组织")
    private Long fromOrg;

}
