package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 人员参与组织生活详情
 * <AUTHOR>
 */
@Data
@ApiModel
public class UserMeetingInfo extends BranchMeetingInfo{

    private static final long serialVersionUID = -2317374349729741990L;

    @ApiModelProperty("组织生活列表")
    private List<MonthMeetingDevelopInfo> meetingMonth = new ArrayList<>();
}
