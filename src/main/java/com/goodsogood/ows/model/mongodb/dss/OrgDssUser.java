package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> ruoyu
 * @date : 2020/12/2
 */
@Data
@ApiModel
public class OrgDssUser extends DssUser {

    private static final long serialVersionUID = -716267982523640413L;

    @ApiModelProperty("所属组织id")
    private Long orgId;

    @ApiModelProperty("党内职务类型 1.书记 2.副书记 3.其他成员")
    private Integer type;

    @ApiModelProperty("党内职务")
    private String position;

    @ApiModelProperty("有效期")
    private String validDate;
}
