package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 组织生活情况 - 党支部
 * <AUTHOR>
 */
@Data
@ApiModel
public class BranchMeetingInfo extends DssBaseInfo{

    private static final long serialVersionUID = 2812213657701524749L;

    @ApiModelProperty("本年度共参与组织生活次数")
    private Integer meetingNum;

    @ApiModelProperty("组织生活参与情况")
    private List<MeetingJoin> meetingJoin = new ArrayList<>();
}
