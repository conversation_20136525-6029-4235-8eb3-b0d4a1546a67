package com.goodsogood.ows.model.mongodb;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 党费缴纳模型
 * @date 2019/11/19
 */
@Data
@ApiModel
//@Document(collection = "PPMD_REPORT")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@CompoundIndexes({
        @CompoundIndex(name = "ppmd_org_idx", def = "{'orgId': 1, 'statsDate': 1}")
})
public class PpmdPayInfo implements Serializable {

    @ApiModelProperty("组织ID")
    private Long orgId;

    @ApiModelProperty("区县ID")
    private Long regionId;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("父级ID")
    private Long parentId;

    @ApiModelProperty("组织类型")
    private Integer orgType;

    @ApiModelProperty("组织类型下级")
    private Integer orgTypeChild;

    @ApiModelProperty("组织层级关系")
    private String orgLevel;

    @ApiModelProperty("组织创建时间")
    private Date orgCreateTime;

    @ApiModelProperty("组织下人员缴纳党费情况")
    private List<User> userList;

    @ApiModelProperty("本党委第一缴纳党员")
    private FirstUser firstUser;

    @ApiModelProperty("本党委第一交齐支部")
    private FirstOrg firstOrg;

    @ApiModelProperty("统计时间 yyyy-mm")
    private String statsDate;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;


    /**
     * 本党委第一缴纳党员
     * <AUTHOR>
     * @date 2019/11/19
     * @return
     */
    @Data
    @ApiModel
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public class FirstUser{

        @ApiModelProperty("用户ID")
        private Long userId;

        @ApiModelProperty("用户姓名")
        private String userName;

        @ApiModelProperty("缴纳时间")
        private Date payTime;
    }

    /**
     * 本党委第一交齐支部
     * <AUTHOR>
     * @date 2019/11/19
     * @return
     */
    @Data
    @ApiModel
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public class FirstOrg{

        @ApiModelProperty("组织ID")
        private Long orgId;

        @ApiModelProperty("组织名称")
        private String orgName;

        @ApiModelProperty("缴纳时间")
        private Date payTime;
    }

    /**
     * 用户缴纳党费信息
     * <AUTHOR>
     * @date 2019/11/19
     * @return
     */
    @Data
    @ApiModel
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public class User{

        @ApiModelProperty("用户ID")
        private Long userId;

        @ApiModelProperty("用户姓名")
        private String userName;

        @ApiModelProperty("党费缴纳情况")
        private int status;

        @ApiModelProperty("应缴纳党费金额")
        private Double ppmdAmount;

        @ApiModelProperty("已缴纳党费金额")
        private Double payAmount;

        @ApiModelProperty("缴纳党费时间")
        private Date payDate;

        @ApiModelProperty("党费的月份")
        private List<String> ppmdDate;
    }
}
