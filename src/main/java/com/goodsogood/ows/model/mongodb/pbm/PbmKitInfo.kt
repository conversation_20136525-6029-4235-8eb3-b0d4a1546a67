package com.goodsogood.ows.model.mongodb.pbm

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModelProperty
import java.time.LocalDateTime

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class PbmUserKitInfo @JvmOverloads constructor(
    @ApiModelProperty("区域ID") var regionId: Long? = 0,
    @ApiModelProperty("时间 yyyy-MM") var date: String? = null,
    @ApiModelProperty("单月变动业务积分") var business: Long = 0,
    @ApiModelProperty("单月变动党建积分") var partyBuild: Long = 0,
    @ApiModelProperty("累计业务积分") var businessTotal: Long = 0,
    @ApiModelProperty("累计党建积分") var partyBuildTotal: Long = 0,
    @ApiModelProperty("累计业务积分-对数") var businessTotalLog: Double = 0.0,
    @ApiModelProperty("累计党建积分-对数") var partyBuildTotalLog: Double = 0.0,
    @ApiModelProperty("拟合度") var goodnessOfKit: Double = 0.0,
    @ApiModelProperty("创建时间") var createTime: LocalDateTime? = null,
    @ApiModelProperty("更新时间") var updateTime: LocalDateTime? = null,

    @ApiModelProperty("人员ID")
    var userId: Long? = 0,

    @ApiModelProperty("人员姓名")
    var userName: String? = null,

    @ApiModelProperty("脱敏手机号")
    var phone: String? = null,

    @ApiModelProperty("政治面貌")
    var politicalType: Int? = 0,

    @ApiModelProperty("所属序列")
    var sequence: Int? = 0,

    @ApiModelProperty("所属组织ID")
    var orgId: Long? = 0,

    @ApiModelProperty("所属组织名称")
    var orgName: String? = null,

    @ApiModelProperty("所属单位ID")
    var unitId: Long? = 0,

    @ApiModelProperty("所属单位名称")
    var unitName: String? = null,

    @ApiModelProperty("职位")
    var title: String? = null,

    @ApiModelProperty("本单位拟合度排序")
    var selfRank: PbmKitChild = PbmKitChild(),

    @ApiModelProperty("所属序列拟合度排序")
    var sequenceRank: PbmKitChild = PbmKitChild()

) {

    override fun toString(): String {
        return "PbmUserKitInfo(regionId=$regionId, date=$date, business=$business, partyBuild=$partyBuild, businessTotal=$businessTotal, partyBuildTotal=$partyBuildTotal, businessTotalLog=$businessTotalLog, partyBuildTotalLog=$partyBuildTotalLog, goodnessOfKit=$goodnessOfKit, createTime=$createTime, updateTime=$updateTime, userId=$userId, userName=$userName, phone=$phone, politicalType=$politicalType, sequence=$sequence, orgId=$orgId, orgName=$orgName, unitId=$unitId, unitName=$unitName)"
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is PbmUserKitInfo) return false

        if (regionId != other.regionId) return false
        if (date != other.date) return false
        if (business != other.business) return false
        if (partyBuild != other.partyBuild) return false
        if (businessTotal != other.businessTotal) return false
        if (partyBuildTotal != other.partyBuildTotal) return false
        if (businessTotalLog != other.businessTotalLog) return false
        if (partyBuildTotalLog != other.partyBuildTotalLog) return false
        if (goodnessOfKit != other.goodnessOfKit) return false
        if (createTime != other.createTime) return false
        if (updateTime != other.updateTime) return false
        if (userId != other.userId) return false
        if (userName != other.userName) return false
        if (phone != other.phone) return false
        if (politicalType != other.politicalType) return false
        if (sequence != other.sequence) return false
        if (orgId != other.orgId) return false
        if (orgName != other.orgName) return false
        if (unitId != other.unitId) return false
        if (unitName != other.unitName) return false
        if (title != other.title) return false
        if (selfRank != other.selfRank) return false
        if (sequenceRank != other.sequenceRank) return false

        return true
    }

    override fun hashCode(): Int {
        var result = regionId?.hashCode() ?: 0
        result = 31 * result + (date?.hashCode() ?: 0)
        result = 31 * result + business.hashCode()
        result = 31 * result + partyBuild.hashCode()
        result = 31 * result + businessTotal.hashCode()
        result = 31 * result + partyBuildTotal.hashCode()
        result = 31 * result + businessTotalLog.hashCode()
        result = 31 * result + partyBuildTotalLog.hashCode()
        result = 31 * result + goodnessOfKit.hashCode()
        result = 31 * result + (createTime?.hashCode() ?: 0)
        result = 31 * result + (updateTime?.hashCode() ?: 0)
        result = 31 * result + (userId?.hashCode() ?: 0)
        result = 31 * result + (userName?.hashCode() ?: 0)
        result = 31 * result + (phone?.hashCode() ?: 0)
        result = 31 * result + (politicalType ?: 0)
        result = 31 * result + (sequence ?: 0)
        result = 31 * result + (orgId?.hashCode() ?: 0)
        result = 31 * result + (orgName?.hashCode() ?: 0)
        result = 31 * result + (unitId?.hashCode() ?: 0)
        result = 31 * result + (unitName?.hashCode() ?: 0)
        result = 31 * result + (title?.hashCode() ?: 0)
        result = 31 * result + selfRank.hashCode()
        result = 31 * result + sequenceRank.hashCode()
        return result
    }

}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
class PbmUnitKitInfo @JvmOverloads constructor(
    @ApiModelProperty("区域ID") var regionId: Long? = 0,
    @ApiModelProperty("时间 yyyy-MM") var date: String? = null,
    @ApiModelProperty("单月变动业务积分") var business: Long = 0,
    @ApiModelProperty("单月变动党建积分") var partyBuild: Long = 0,
    @ApiModelProperty("累计业务积分") var businessTotal: Long = 0,
    @ApiModelProperty("累计党建积分") var partyBuildTotal: Long = 0,
    @ApiModelProperty("累计业务积分-对数") var businessTotalLog: Double = 0.0,
    @ApiModelProperty("累计党建积分-对数") var partyBuildTotalLog: Double = 0.0,
    @ApiModelProperty("拟合度") var goodnessOfKit: Double = 0.0,
    @ApiModelProperty("创建时间") var createTime: LocalDateTime? = null,
    @ApiModelProperty("更新时间") var updateTime: LocalDateTime? = null,

    @ApiModelProperty("单位ID")
    var unitId: Long? = 0,

    @ApiModelProperty("单位名称")
    var unitName: String? = null,

    @ApiModelProperty("排名")
    var rank: PbmKitChild = PbmKitChild(),

    @ApiModelProperty("平均值")
    var avg: PbmKitChild = PbmKitChild(),

    @ApiModelProperty("最高值")
    var high: PbmKitChild = PbmKitChild(),

    @ApiModelProperty("本单位自建")
    var self: PbmKitChild = PbmKitChild()
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is PbmUnitKitInfo) return false

        if (regionId != other.regionId) return false
        if (date != other.date) return false
        if (business != other.business) return false
        if (partyBuild != other.partyBuild) return false
        if (businessTotal != other.businessTotal) return false
        if (partyBuildTotal != other.partyBuildTotal) return false
        if (businessTotalLog != other.businessTotalLog) return false
        if (partyBuildTotalLog != other.partyBuildTotalLog) return false
        if (goodnessOfKit != other.goodnessOfKit) return false
        if (createTime != other.createTime) return false
        if (updateTime != other.updateTime) return false
        if (unitId != other.unitId) return false
        if (unitName != other.unitName) return false
        if (rank != other.rank) return false
        if (avg != other.avg) return false
        if (high != other.high) return false
        if (self != other.self) return false

        return true
    }

    override fun hashCode(): Int {
        var result = regionId?.hashCode() ?: 0
        result = 31 * result + (date?.hashCode() ?: 0)
        result = 31 * result + business.hashCode()
        result = 31 * result + partyBuild.hashCode()
        result = 31 * result + businessTotal.hashCode()
        result = 31 * result + partyBuildTotal.hashCode()
        result = 31 * result + businessTotalLog.hashCode()
        result = 31 * result + partyBuildTotalLog.hashCode()
        result = 31 * result + goodnessOfKit.hashCode()
        result = 31 * result + (createTime?.hashCode() ?: 0)
        result = 31 * result + (updateTime?.hashCode() ?: 0)
        result = 31 * result + (unitId?.hashCode() ?: 0)
        result = 31 * result + (unitName?.hashCode() ?: 0)
        result = 31 * result + rank.hashCode()
        result = 31 * result + avg.hashCode()
        result = 31 * result + high.hashCode()
        result = 31 * result + self.hashCode()
        return result
    }

    override fun toString(): String {
        return "PbmUnitKitInfo(regionId=$regionId, date=$date, business=$business, partyBuild=$partyBuild, businessTotal=$businessTotal, partyBuildTotal=$partyBuildTotal, businessTotalLog=$businessTotalLog, partyBuildTotalLog=$partyBuildTotalLog, goodnessOfKit=$goodnessOfKit, createTime=$createTime, updateTime=$updateTime, unitId=$unitId, unitName=$unitName, rank=$rank, avg=$avg, high=$high, self=$self)"
    }

}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class PbmOrgUserKitInfo @JvmOverloads constructor(

    @ApiModelProperty("单位ID")
    var unitId: Long? = 0,

    @ApiModelProperty("单位名称")
    var unitName: String? = null,

    @ApiModelProperty("区域ID")
    var regionId: Long? = 0,

    @ApiModelProperty("时间 yyyy-MM")
    var date: String? = null,

    @ApiModelProperty("类型 0 -> 全体，1 -> 卷烟营销，2 -> 烟叶生成，3 -> 专卖管理，4 -> 综合管理")
    var type: Int? = 0,

    @ApiModelProperty("本单位当前业务线的业务积分平均值")
    var selfBusinessAvg: ScoreInfo = ScoreInfo(),

    @ApiModelProperty("本单位当前业务线的党建积分平均值")
    var selfPartyAvg: ScoreInfo = ScoreInfo(),

    @ApiModelProperty("本单位当前业务线的拟合度平均值")
    var selfKitAvg: ScoreInfo = ScoreInfo(),

    @ApiModelProperty("当前业务线的业务积分平均值")
    var allBusinessAvg: ScoreInfo = ScoreInfo(),

    @ApiModelProperty("当前业务线的拟合度平均值")
    var allKitAvg: ScoreInfo = ScoreInfo(),

    @ApiModelProperty("当前业务线的党员业务积分极值")
    var businessExtreme: Extremum = Extremum(),

    @ApiModelProperty("当前业务线的党员党建积分极值")
    var partyBuildExtreme: Extremum = Extremum(),

    @ApiModelProperty("当前业务线的和党员中位数")
    var allPartyMedian: PbmKitChild = PbmKitChild(),

    @ApiModelProperty("全体非党员中位数 暂不需要")
    var allNonPartyMedian: PbmKitChild = PbmKitChild(),

    @ApiModelProperty("本单位最高业务积分人")
    var maxUserKitInfo: MutableList<PbmUserKitInfo> = mutableListOf(),

    @ApiModelProperty("本单位最高党建积分人")
    var maxUserPartyInfo: MutableList<PbmUserKitInfo> = mutableListOf(),

    @ApiModelProperty("创建时间")
    var createTime: LocalDateTime? = null,

    @ApiModelProperty("更新时间")
    var updateTime: LocalDateTime? = null

)

data class PbmKitChild @JvmOverloads constructor(

    @ApiModelProperty("业务")
    var b: Double? = 0.0,

    @ApiModelProperty("党建")
    var p: Double? = 0.0,

    @ApiModelProperty("拟合度")
    var k: Double? = 0.0
)

data class ScoreInfo @JvmOverloads constructor(

    @ApiModelProperty("党员")
    var be: Double? = 0.0,

    @ApiModelProperty("非党员")
    var non: Double? = 0.0,

    @ApiModelProperty("党员数量")
    var beNum: Int? = 0,

    @ApiModelProperty("非党员数量")
    var nonNum: Int? = 0
)

data class Extremum @JvmOverloads constructor(

    @ApiModelProperty("最小值")
    var min: Long? = 0,

    @ApiModelProperty("最大值")
    var max: Long? = 0
)

data class RankEntity(

    var id: Long? = null,

    var score: Double? = null
)