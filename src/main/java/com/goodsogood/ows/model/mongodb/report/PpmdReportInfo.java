package com.goodsogood.ows.model.mongodb.report;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 组织电子党务报表-党费
 * @date 2019/11/28
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PpmdReportInfo implements Serializable {

    @ApiModelProperty("交纳党费的人数")
    private Integer payNum = 0;

    @ApiModelProperty("总共交纳多少党费")
    private Double payAmount = 0.0;

    @ApiModelProperty("最先交纳齐的党支部")
    private String firstOrg;

    @ApiModelProperty("最先交纳的党员")
    private String firstUser;
}
