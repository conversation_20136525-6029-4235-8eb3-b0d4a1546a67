package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 学习情况趋势
 *
 * <AUTHOR>
 * @date 2020/11/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class StudySituationInfo extends DssBaseInfo{

    private static final long serialVersionUID = 3757206728577468094L;

    @ApiModelProperty("小时")
    private Integer time;

    @ApiModelProperty("总人数")
    private Integer total;

}
