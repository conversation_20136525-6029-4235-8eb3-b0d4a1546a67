package com.goodsogood.ows.model.mongodb.report;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/11/22
 * Description:
 */
@Data
@ApiModel
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UserScoreStudyInfo {

    @ApiModelProperty(name = "学习次数")
    private Integer studyNum;

    @ApiModelProperty(name = "获得学分")
    private Long receiveScoreNum;

    @ApiModelProperty(name = "兑换图书")
    private Long exchangeBookNum;

    public Integer getStudyNum() {
        if(studyNum==null){
            return 0;
        }
        return studyNum;
    }

    public Long getReceiveScoreNum() {
        if(receiveScoreNum==null){
            return 0L;
        }
        return receiveScoreNum;
    }

    public Long getExchangeBookNum() {
        if(exchangeBookNum==null){
            return 0L;
        }
        return exchangeBookNum;
    }

    public static UserScoreStudyInfo getInitEntity() {
        UserScoreStudyInfo userScoreStudyInfo=new UserScoreStudyInfo();
        userScoreStudyInfo.setStudyNum(0);
        userScoreStudyInfo.setReceiveScoreNum(0L);
        userScoreStudyInfo.setExchangeBookNum(0L);
        return userScoreStudyInfo;

    }

}
