/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Auto-generated: 2020-11-05 19:49:56
 * 学习数据
 * <AUTHOR>
 */
@Data
@ApiModel
public class StudyInfo extends DssBaseInfo{

    private static final long serialVersionUID = -3872993009676911412L;

    @ApiModelProperty("累积学习人次")
    private Integer totalMember;

    @ApiModelProperty("累积获得学习积分")
    private Integer totalScore;

    @ApiModelProperty("累积兑换图书")
    private Integer totalBook;

    @ApiModelProperty("累积兑换积分")
    private Integer totalExchangeScore;

    @ApiModelProperty("本年学习人次")
    private Integer curStudyTimes;

    @ApiModelProperty("本年学习积分")
    private Integer curStudyScore;

    @ApiModelProperty("累积捐赠积分(一元捐)")
    private Integer donateScore;

    @ApiModelProperty("学习情况趋势图")
    private List<Integer> studyTrendMap = new ArrayList<>();
}