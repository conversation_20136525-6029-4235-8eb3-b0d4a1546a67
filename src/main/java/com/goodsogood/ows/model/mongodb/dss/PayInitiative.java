package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 党费交纳趋势图
 * <AUTHOR>
 */
@Data
@ApiModel
public class PayInitiative extends DssBaseInfo{

    private static final long serialVersionUID = 7033697458180268711L;

    /**
     * 在本党委内的缴纳排名
     */

    @ApiModelProperty("月份")
    private Integer month;

    @ApiModelProperty("交纳党费日期")
    private Integer day;

    @ApiModelProperty("排名百分比")
    private Double ranking;

    @ApiModelProperty("排名")
    private Integer limit;
}
