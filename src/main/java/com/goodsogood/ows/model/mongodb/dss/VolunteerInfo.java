/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Auto-generated: 2020-11-05 19:49:56
 * 志愿者统计数据
 * <AUTHOR>
 */
@Data
@ApiModel
public class VolunteerInfo extends DssBaseInfo{

    private static final long serialVersionUID = -1703066108013983687L;

    @ApiModelProperty("志愿者团体数")
    private Integer volunteerTeamTotal;

    @ApiModelProperty("志愿者数")
    private Integer volunteerUserTotal;

    @ApiModelProperty("开展志愿服务")
    private Integer volunteerServiceTotal;

    @ApiModelProperty("开展送服务")
    private Integer volunteerSupportServiceTotal;

    @ApiModelProperty("求助接单")
    private Integer recourseTotal;

    @ApiModelProperty("志愿者参与")
    private Integer volunteerJoinTotal;

    @ApiModelProperty("志愿者进度条")
    private List<VolunteerBulletMap> volunteerBulletMap = new ArrayList<>();

    @ApiModelProperty("志愿者开展趋势图")
    private VolunteerTrendMap volunteerTrendMap;

}