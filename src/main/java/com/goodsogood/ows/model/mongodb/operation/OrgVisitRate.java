package com.goodsogood.ows.model.mongodb.operation;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName : OrgVisitRate
 * <AUTHOR> tc
 * @Date: 2022/4/15 15:49
 * @Description : 按组织统计访问率
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrgVisitRate {
    @JsonProperty(value = "region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;

    /**
     * 格式 YYYY-MM-DD
     */
    @JsonProperty(value = "stats_date")
    @ApiModelProperty("统计日期")
    private String statsDate;

    @JsonProperty(value = "parent_org_id")
    @ApiModelProperty("上一级组织编号")
    private Long parentOrgId;

    @JsonProperty(value = "org_id")
    @ApiModelProperty("组织编号")
    private Long orgId;

    /**
     * 组织名称
     */
    @JsonProperty(value = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    /**
     * 组织简称
     */
    @JsonProperty(value = "short_name")
    @ApiModelProperty("组织简称")
    private String shortName;

    /**
     * 每人每天只计算第一次访问
     */
    @JsonProperty(value = "visit_num")
    @ApiModelProperty("访问人数")
    private Integer visitNum;

    @JsonProperty(value = "people_num")
    @ApiModelProperty("总人数")
    private Integer peopleNum;

    /**
     * 访问率
     */
    @JsonProperty(value = "visit_rate")
    @ApiModelProperty("访问率")
    private String visitRate;

    /**
     * 四舍五入保留两位小数-组织内所有人员累计访问天数之和/组织内人员总数
     */
    @JsonProperty(value = "visit_days")
    @ApiModelProperty("人均访问天数")
    public String visitDays;

    /**
     * 组织下今日首次登录的人员中最晚的那个登录时间
     */
    @JsonProperty(value = "visit_time")
    @ApiModelProperty("登录时间")
    private String visitTime;
}
