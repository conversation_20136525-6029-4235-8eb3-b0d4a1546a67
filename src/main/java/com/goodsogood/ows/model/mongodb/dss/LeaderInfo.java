package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 领导信息
 * <AUTHOR>
 */
@Data
@ApiModel
public class LeaderInfo extends DssUser{

    private static final long serialVersionUID = -7529343983068097344L;

    @ApiModelProperty("职务")
    private String position;

    @ApiModelProperty("是否是负责人 1-是, 0-否")
    private Integer isHead;
}
