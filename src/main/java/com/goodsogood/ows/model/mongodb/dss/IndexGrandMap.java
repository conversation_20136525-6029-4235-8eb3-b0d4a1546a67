/**
  * Copyright 2020 json.cn 
  */
package com.goodsogood.ows.model.mongodb.dss;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


/**
 * Auto-generated: 2020-11-05 19:49:56
 * 评分情况饼图
 * <AUTHOR>
 */
@Data
@ApiModel
public class IndexGrandMap extends PieObject{

    private static final long serialVersionUID = 436687533986700026L;

    /** 组织列表 */
    @ApiModelProperty("组织列表")
    private List<OfficeOrg> orgList = new ArrayList<>();
}