package com.goodsogood.ows.model.mongodb.dss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * 年度得分
 * <AUTHOR>
 */
@Data
@SuperBuilder(toBuilder = true)
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class YearGrand implements Serializable {

	private static final long serialVersionUID = 105025812509509345L;

	/** n年份 */
	@ApiModelProperty("年份")
	private Integer year;

	/** 综合得分 */
	@ApiModelProperty("综合得分")
	private Double grand;
}
