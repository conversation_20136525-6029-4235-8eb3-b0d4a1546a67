package com.goodsogood.ows.model.mongodb.report;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 组织电子党务报表-组织生活记录
 * @date 2019/11/28
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MeetingReportInfo implements Serializable {

    @ApiModelProperty("党组织信息")
    private OrgNum orgNum;

    @ApiModelProperty("组织生活信息")
    private MeetingNum meetingNum;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public class OrgNum implements Serializable{

        @ApiModelProperty("党组织数量")
        private Integer CommunistNum = 0;

        @ApiModelProperty("党委数量")
        private Integer partyNum = 0;

        @ApiModelProperty("党支部数量")
        private Integer branchNum = 0;

        @ApiModelProperty("其他组织数量")
        private Integer otherNum = 0;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public class MeetingNum implements Serializable{

        @ApiModelProperty("组织生活开展次数")
        public Integer meetingCount = 0;

        @ApiModelProperty("组织生活类型开展次数")
        public List<Type> typeList;

        @Data
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
        public class Type implements Serializable{

            @ApiModelProperty("组织生活类型ID")
            public Long id;

            @ApiModelProperty("组织生活类型")
            public String type;

            @ApiModelProperty("开展次数")
            public Integer num = 0;
        }
    }
}
