package com.goodsogood.ows.model.mongodb.ecp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

/**
 * 用户首页(基本)信息vo
 *
 * <AUTHOR>
 * @data 2021/4/29
 */
@Data
@ApiModel
@Document(collection = "messages")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HomeMessageForm {

    private static final long serialVersionUID = -1500461191890651303L;

    @ApiModelProperty("用户帖子审核状态")
    public Integer checkStatus;

    @ApiModelProperty("帖子id")
    @Field("_id")
    public String messagesId;

    @ApiModelProperty("文案内容")
    //@Field("content")
    public String content;

    @ApiModelProperty("图片url数组")
    //@Field("message_img")
    public List<MessageImg> messageImg;

    @ApiModelProperty("视频url")
    //@Field("message_video")
    public List<MessageVideo> messageVideo;

    @ApiModelProperty("审核变动时间用于排序")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    public Date changeTime;
}
