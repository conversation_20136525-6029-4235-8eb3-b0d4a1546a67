package com.goodsogood.ows.model.enumpackage;


/**
 * @description: 组织账号类型枚举
 * @author: Mr.<PERSON>
 * @create: 2019-03-14 10:47
 **/

public enum OrganizeAccountTypeEnum {

    SPECIAL_ACCOUNT(1,"专户"),
    ORGANIZE_ACCOUNT(2,"组织账户"),
    RMB_ACCOUNT(3,"RMB账户");


    private int key;
    private String desc;

    OrganizeAccountTypeEnum(int key, String desc) {
        this.key = key;
        this.desc = desc;

    }


    public int getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}