package com.goodsogood.ows.model.antd

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

/**
 * antd框架代码的RangePicker控件
 * 添加了一些特别的属性
 */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class RangePicker(
    override var name: String?,
    override var param: String?,
    override var index: Int?,
    override var type: AntDesignType? = AntDesignType.RangePicker,
    var dateFormat: String? = "YYYY-MM-DD",
    // 默认值，开始：结束
    @JsonProperty("defaultValue")
    var rangePickerDefaultValue: Pair<String?, String?>? = null
) : AntDesignBase
