package com.goodsogood.ows.model.antd

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

/**
 * antd框架代码的select控件
 * 添加了一些特别的属性
 */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Select(
    override var name: String?,
    override var param: String?,
    override var index: Int?,
    override var type: AntDesignType? = AntDesignType.Select,
    // 默认值（option的value）
    @JsonProperty("defaultValue")
    var selectDefaultValue: String? = null,
    // 选项
    var options: List<Option>? = listOf(),
    // 目标字段index 这里如果有target字段，需要在选定的时候把选定的值对应到target对应字段(index)的param中
    var target: Int?,
) : AntDesignBase {
    data class Option(
        // 选项值
        var value: String? = null,
        // 选项文本
        var label: String? = null,
        // 是否禁用
        var disabled: Boolean = false,
    )
}
