package com.goodsogood.ows.model.antd

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

/**
 * antd框架代码的input控件
 * 添加了一些特别的属性
 */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Input(
    override var name: String?,
    override var param: String?,
    override var index: Int?,
    // 输入框
    override var type: AntDesignType? = AntDesignType.Input,
    // 文本
    var text: String? = null,
    // 输入框提示词
    var placeholder: String? = null,
) : AntDesignBase
