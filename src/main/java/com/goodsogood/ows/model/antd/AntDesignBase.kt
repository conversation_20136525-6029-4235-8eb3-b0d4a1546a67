package com.goodsogood.ows.model.antd

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

/**
 * <AUTHOR>
 * @date 2023/11/23
 * @description class AntDesignBase 基础信息
 */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
interface AntDesignBase {
    // 字段名称
    var name: String?

    // 类名（对应的查询列名称,多个用逗号分割，如："col01,col30"）
    var param: String?

    // 条件顺序
    var index: Int?

    // 控件类型
    var type: AntDesignType?

    fun getType(): String = type.toString()
}


// 控件类型枚举
enum class AntDesignType(val key: String) {

    // 输入框
    Input("Input"),

    // 下拉列表框
    Select("Select"),

    // 日期选择框
    RangePicker("RangePicker");

    override fun toString(): String = key
}
