package com.goodsogood.ows.model.db.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotBlank;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * t_type 实体类
 *
 * <AUTHOR>
 * @create 2018-10-19 16:21
 */
@Data
@ApiModel
@Table(name = "t_type")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TypeEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "type_id")
    @Column(name = "type_id")
    private Long typeId;


    @ApiModelProperty("所属类别id")
    @JsonProperty(value = "category_id")
    @Column(name = "category_id")
    @NotNull(message = "{NotNull.category.categoryId}")
    private Long categoryId;


    @ApiModelProperty("创建人所属组织id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;


    @ApiModelProperty("创建人所属组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;


    @ApiModelProperty("类型")
    @Column(name = "type")
    @NotBlank(message = "{NotBlank.type.type}")
    @Length(max = 50,message = "{Length.type.type}")
    private String type;


    @ApiModelProperty("类别。冗余")
    @Column(name = "category")
    private String category;


    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private Date createTime;


    @ApiModelProperty("创建人id")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    private Long createUser;


    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    private Date updateTime;


    @ApiModelProperty("最后更新人id")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    private Long lastChangeUser;

}

