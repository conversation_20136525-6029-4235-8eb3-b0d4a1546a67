package com.goodsogood.ows.model.db.tbcFusion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * @Author: mengting
 * @Date: 2022/4/6 13:48
 */
@Data
@ApiModel
@Table(name = "t_party_business_rule")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PartyBusinessRuleEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer ruleId;


    private String name;


    private String rule;


    private Short period;


    private Integer objectType;


    private Short calType;

    private String explainStr;


    private LocalDateTime createTime;


    private LocalDateTime updateTime;
}
