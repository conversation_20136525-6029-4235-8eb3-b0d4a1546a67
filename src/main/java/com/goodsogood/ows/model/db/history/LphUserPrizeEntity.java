package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_user_prize 实体类
 *
 * 用户奖品流水表 
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_user_prize")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphUserPrizeEntity   extends  BaseEntity{
	
//	@Id
	@ApiModelProperty("主键id")
	@JsonProperty(value = "user_prize_id")
	@Column(name = "user_prize_id")
	private Long userPrizeId;
	
	
	@ApiModelProperty("区县id")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Integer regionId;
	
	
	@ApiModelProperty("用户唯一id")
	@JsonProperty(value = "open_id")
	@Column(name = "open_id")
	private String openId;
	
	
	@ApiModelProperty("奖品商品id")
	@JsonProperty(value = "prize_id")
	@Column(name = "prize_id")
	private Long prizeId;
	
	
	@ApiModelProperty("中奖流水下标主键id")
	@JsonProperty(value = "award_id")
	@Column(name = "award_id")
	private Long awardId;
	
	
	@ApiModelProperty("奖品名字")
	@JsonProperty(value = "prize_name")
	@Column(name = "prize_name")
	private String prizeName;
	
	
	@ApiModelProperty("奖品图片")
	@JsonProperty(value = "prize_url")
	@Column(name = "prize_url")
	private String prizeUrl;
	
	
	@ApiModelProperty("用户中奖状态 0(否) : 1:中奖")
	@Column(name = "type")
	private Byte type;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
}

