package com.goodsogood.ows.model.db.meeting

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import javax.persistence.*
import javax.validation.constraints.NotNull
import javax.validation.constraints.Size

@Entity
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Table(name = "t_meeting_work_points")
class MeetingWorkPointEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "work_points_id", nullable = false)
    var id: Long? = null

    @Column(name = "org_id")
    var orgId: Long? = null

    @get:NotNull(message = "单位id不能为空")
    @Column(name = "owner_id", nullable = false)
    var ownerId: Long? = null

    @get:NotNull(message = "单位名称不能为空")
    @get:Size(min = 1, max = 120, message = "单位名称长度为1-120个字")
    @Column(name = "owner_name", nullable = false, length = 120)
    var ownerName: String? = null

    // 文件名称
    @get:Size(max = 100, message = "文件名称长度为1-100个字")
    @Column(name = "file_name", length = 500)
    var fileName: String? = null

    // 上下半年 1.上半年。2.下半年
    @Column(name = "half_year")
    var halfYear: Int? = null

    // 年度
    @Column(name = "year")
    var year: Int? = null

    // 文号
    @get:Size(max = 100, message = "文号长度为1-100个字")
    @Column(name = "doc_code")
    var docCode: String? = null

    // 发文时间
    @get: JsonFormat
        (shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateSerializer::class)
    @JsonDeserialize(using = LocalDateDeserializer::class)
    @Column(name = "publish_time")
    var publishTime: LocalDate? = null

    // 附件
    @Column(name = "attachment")
    var attachment: String? = null

    @Column(name = "create_user", nullable = false)
    var createUser: Long? = null

    @get: JsonFormat
        (shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer::class)
    @JsonDeserialize(using = LocalDateTimeDeserializer::class)
    @Column(name = "create_time", nullable = false)
    var createTime: LocalDateTime? = null

    @Column(name = "update_user")
    var updateUser: Long? = null

    @get: JsonFormat
        (shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer::class)
    @JsonDeserialize(using = LocalDateTimeDeserializer::class)
    @Column(name = "update_time")
    var updateTime: LocalDateTime? = null
}