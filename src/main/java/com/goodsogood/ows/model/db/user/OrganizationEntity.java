package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 组织
 *
 * <AUTHOR>
 * @date 2018-04-02
 */
@Data
@ApiModel
@Table(name = "t_organization")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrganizationEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "organization_id")
    @JsonProperty("org_id")
    private Long organizationId;

    @Column(name = "region_id")
    @JsonProperty("region_id")
    private Long regionId;

    @JsonProperty("parent_id")
    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "name")
    private String name;

    @Column(name = "short_name")
    @JsonProperty("short_name")
    private String shortName;

    @Column(name = "org_type")
    @JsonProperty("org_type")
    private Integer orgType;

    @Column(name = "org_type_child")
    @JsonProperty("org_type_child")
    private Integer orgTypeChild;

    @Column(name = "org_create_time")
    @JsonProperty("org_create_time")
    private Date orgCreateTime;

    @Column(name = "org_code")
    @JsonProperty("org_code")
    private String orgCode;

    @Column(name = "org_phone")
    @JsonProperty("org_phone")
    private String orgPhone;

    @Column(name = "org_leader")
    @JsonProperty("org_leader")
    private String orgLeader;

    @Column(name = "org_leader_phone")
    @JsonProperty("org_leader_phone")
    private String orgLeaderPhone;

    @Column(name = "org_unique_code")
    @JsonProperty("org_unique_code")
    private String orgUniqueCode;

    @Column(name = "org_area")
    @JsonProperty("org_area")
    private String orgArea;

    @Column(name = "postcode")
    private String postcode;

    @JsonProperty("org_address")
    @Column(name = "org_address")
    private String orgAddress;

    /**
     * 状态 1-启用 2-禁用 3-不可修改
     */
    @Column(name = "status")
    private Integer status;

    @JsonProperty("seq")
    @Column(name = "seq")
    private Integer seq;

    @Column(name = "child_org_num")
    @JsonProperty("child_org_num")
    private Integer childOrgNum;

    @Column(name = "company_num")
    @JsonProperty("company_num")
    private Integer companyNum;

    @Column(name = "user_num")
    @JsonProperty("user_num")
    private Integer userNum;

    @Column(name = "work_num")
    @JsonProperty("work_num")
    private Integer workNum;

    @Column(name = "org_level")
    @JsonProperty("org_level")
    private String orgLevel;

    @Column(name = "last_change_user")
    @JsonProperty("last_change_user")
    private Long lastChangeUser;

    @Column(name = "create_time")
    @JsonProperty("create_time")
    private Date createTime;

    @Column(name = "update_time")
    @JsonProperty("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "行业类别")
    @Column(name = "industry_type")
    @JsonProperty("industry_type")
    private Integer industryType;

    @ApiModelProperty(value = "是否激活 1-已激活 2-未激活")
    @Column(name = "activate")
    private Integer activate;

    @Transient
    @JsonProperty("oid")
    private Long id;

    @Transient
    @JsonProperty("org_type_name")
    private String orgTypeName;

    @Column(name = "org_contacts")
    @JsonProperty("org_contacts")
    private String orgContacts;

    @ApiModelProperty(value = "组织序列号")
    @Column(name = "org_id")
    @JsonProperty("id")
    private String orgId;

    @ApiModelProperty(value = "上级组织序列号")
    @Column(name = "org_pid")
    @JsonProperty("org_pid")
    private String orgPid;

    @ApiModelProperty(value = "所属组织ID")
    @JsonProperty("owner_id")
    @Column(name = "owner_id")
    private Long ownerId;

    @ApiModelProperty(value = "第三方数据")
    @JsonProperty("third_data_info")
    @Column(name = "third_data_info")
    private String thirdDataInfo;

    @ApiModelProperty(value = "第三方数据来源 1-12371")
    @JsonProperty("third_source")
    @Column(name = "third_source")
    private Integer thirdSource;

    @ApiModelProperty(value = "所属组织树 1-父树 2-子树")
    @JsonProperty("owner_tree")
    @Column(name = "owner_tree")
    private Integer ownerTree;

    @ApiModelProperty(value = "是否离退休党组织 1-是 2-否")
    @JsonProperty("is_retire")
    @Column(name = "is_retire")
    private Integer isRetire;

    @ApiModelProperty(value = "是否流动党员党组织 1-是 2-否")
    @JsonProperty("is_flow")
    @Column(name = "is_flow")
    private Integer isFlow;

    @ApiModelProperty(value = "组织关联单位是否与上级组织保持一致 1-是 2-否")
    @JsonProperty("is_consistent")
    @Column(name = "is_consistent")
    private Integer isConsistent;

    @JsonProperty("adcode")
    @Column(name = "adcode")
    private String adcode;

    @ApiModelProperty(value = "经度")
    @JsonProperty("longitude")
    @Column(name = "longitude")
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    @JsonProperty("latitude")
    @Column(name = "latitude")
    private Double latitude;

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public Integer getOrgTypeChild() {
        return orgTypeChild;
    }

    public void setOrgTypeChild(Integer orgTypeChild) {
        this.orgTypeChild = orgTypeChild;
    }

    public Date getOrgCreateTime() {
        return orgCreateTime;
    }

    public void setOrgCreateTime(Date orgCreateTime) {
        this.orgCreateTime = orgCreateTime;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgPhone() {
        return orgPhone;
    }

    public void setOrgPhone(String orgPhone) {
        this.orgPhone = orgPhone;
    }

    public String getOrgLeader() {
        return orgLeader;
    }

    public void setOrgLeader(String orgLeader) {
        this.orgLeader = orgLeader;
    }

    public String getOrgLeaderPhone() {
        return orgLeaderPhone;
    }

    public void setOrgLeaderPhone(String orgLeaderPhone) {
        this.orgLeaderPhone = orgLeaderPhone;
    }

    public String getOrgUniqueCode() {
        return orgUniqueCode;
    }

    public void setOrgUniqueCode(String orgUniqueCode) {
        this.orgUniqueCode = orgUniqueCode;
    }

    public String getOrgArea() {
        return orgArea;
    }

    public void setOrgArea(String orgArea) {
        this.orgArea = orgArea;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getOrgAddress() {
        return orgAddress;
    }

    public void setOrgAddress(String orgAddress) {
        this.orgAddress = orgAddress;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public Integer getChildOrgNum() {
        return childOrgNum;
    }

    public void setChildOrgNum(Integer childOrgNum) {
        this.childOrgNum = childOrgNum;
    }

    public Integer getCompanyNum() {
        return companyNum;
    }

    public void setCompanyNum(Integer companyNum) {
        this.companyNum = companyNum;
    }

    public Integer getUserNum() {
        return userNum;
    }

    public void setUserNum(Integer userNum) {
        this.userNum = userNum;
    }

    public Integer getWorkNum() {
        return workNum;
    }

    public void setWorkNum(Integer workNum) {
        this.workNum = workNum;
    }

    public String getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel;
    }

    public Long getLastChangeUser() {
        return lastChangeUser;
    }

    public void setLastChangeUser(Long lastChangeUser) {
        this.lastChangeUser = lastChangeUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIndustryType() {
        return industryType;
    }

    public void setIndustryType(Integer industryType) {
        this.industryType = industryType;
    }

    public Integer getActivate() {
        return activate;
    }

    public void setActivate(Integer activate) {
        this.activate = activate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgTypeName() {
        return orgTypeName;
    }

    public void setOrgTypeName(String orgTypeName) {
        this.orgTypeName = orgTypeName;
    }

    public String getOrgContacts() {
        return orgContacts;
    }

    public void setOrgContacts(String orgContacts) {
        this.orgContacts = orgContacts;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgPid() {
        return orgPid;
    }

    public void setOrgPid(String orgPid) {
        this.orgPid = orgPid;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getThirdDataInfo() {
        return thirdDataInfo;
    }

    public void setThirdDataInfo(String thirdDataInfo) {
        this.thirdDataInfo = thirdDataInfo;
    }

    public Integer getThirdSource() {
        return thirdSource;
    }

    public void setThirdSource(Integer thirdSource) {
        this.thirdSource = thirdSource;
    }

    public Integer getOwnerTree() {
        return ownerTree;
    }

    public void setOwnerTree(Integer ownerTree) {
        this.ownerTree = ownerTree;
    }

    public Integer getIsRetire() {
        return isRetire;
    }

    public void setIsRetire(Integer isRetire) {
        this.isRetire = isRetire;
    }

    public Integer getIsFlow() {
        return isFlow;
    }

    public void setIsFlow(Integer isFlow) {
        this.isFlow = isFlow;
    }

    public Integer getIsConsistent() {
        return isConsistent;
    }

    public void setIsConsistent(Integer isConsistent) {
        this.isConsistent = isConsistent;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }
}