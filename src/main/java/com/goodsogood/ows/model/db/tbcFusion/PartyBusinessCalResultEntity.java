package com.goodsogood.ows.model.db.tbcFusion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * @Author: mengting
 * @Date: 2022/4/13 13:33
 */
@Data
@ApiModel
@Table(name="t_party_business_cal_result")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PartyBusinessCalResultEntity {

    @Id
    @Column(name = "result_id")
    private String resultId;

    @Column(name = "data_id")
    private String dataId;

    @Column(name = "rule_id")
    private Integer ruleId;

    @Column(name = "region_id")
    private Long regionId;

    @Column(name = "year")
    private Integer year;

    @Column(name = "object_type")
    private Integer objectType;

    @Column(name = "odps_object")
    private String odpsObject;

    @Column(name = "cal_object")
    private String calObject;

    @Column(name = "cal_date")
    private Integer calDate;

    @Column(name = "cal_result")
    private Double calResult;

    @Column(name = "score")
    private Double score;

    @Column(name = "rank_num")
    private Integer rankNum;

    @Column(name = "explain_str")
    private String explainStr;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;
}
