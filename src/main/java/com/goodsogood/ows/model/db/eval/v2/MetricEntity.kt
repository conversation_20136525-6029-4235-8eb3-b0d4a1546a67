package com.goodsogood.ows.model.db.eval.v2

import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer
import java.time.LocalDateTime
import javax.persistence.*
import javax.validation.constraints.NotNull
import javax.validation.constraints.Size

@Entity
@Table(name = "t_eval_metric")
open class MetricEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "metric_id", nullable = false)
    open var id: Long? = null

    @NotNull
    @Column(name = "metric_class_id", nullable = false)
    open var metricClassId: Long? = null

    @NotNull
    @Column(name = "level", nullable = false)
    open var level: Int? = null

    @Size(max = 400)
    @NotNull
    @Column(name = "name", nullable = false, length = 400)
    open var name: String? = null

    @Size(max = 2000)
    @Column(name = "summary", length = 2000)
    open var summary: String? = null

    @Column(name = "type")
    open var type: Byte? = null

    @NotNull
    @Column(name = "year", nullable = false)
    open var year: Int? = null

    @NotNull
    @Column(name = "enabled", nullable = false)
    open var enabled: Byte? = null

    @Column(name = "score_limit", precision = 24, scale = 6)
    open var scoreLimit: Double? = null

    @NotNull
    @Column(name = "score", nullable = false, precision = 24, scale = 6)
    open var score: Double? = null

    @Column(name = "score_type")
    open var scoreType:Int? = null

    @Size(max = 100)
    @Column(name = "clz_name", length = 100)
    open var clzName: String? = null

    @NotNull
    @Column(name = "include_sub_org", nullable = false)
    open var includeSubOrg: Byte? = null

    @NotNull
    @Column(name = "create_user", nullable = false)
    open var createUser: Long? = null

    @NotNull
    @Column(name = "create_time", nullable = false)
    @JsonSerialize(using = LocalDateTimeSerializer::class)
    @JsonDeserialize(using = LocalDateTimeDeserializer::class)
    open var createTime: LocalDateTime? = null

    @NotNull
    @Column(name = "last_change_user", nullable = false)
    open var lastChangeUser: Long? = null

    @NotNull
    @Column(name = "update_time", nullable = false)
    @JsonSerialize(using = LocalDateTimeSerializer::class)
    @JsonDeserialize(using = LocalDateTimeDeserializer::class)
    open var updateTime: LocalDateTime? = null
}