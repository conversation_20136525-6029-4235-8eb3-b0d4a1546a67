package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_user_line_status 实体类
 *
 * 用户线路总表状态 
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_user_line_status")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphUserLineStatusEntity   extends  BaseEntity{
	
//	@Id
	@ApiModelProperty("用户线路总表主键")
	@JsonProperty(value = "sum_up_id")
	@Column(name = "sum_up_id")
	private Long sumUpId;
	
	
	@ApiModelProperty("总线路id")
	@JsonProperty(value = "circuit_id")
	@Column(name = "circuit_id")
	private Long circuitId;
	
	
	@ApiModelProperty("区县ID")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Integer regionId;
	
	
	@ApiModelProperty("用户openId")
	@JsonProperty(value = "open_id")
	@Column(name = "open_id")
	private String openId;
	
	
	@ApiModelProperty("线路完成数 0:进行状态(未完成) 1+:完成次数")
	@JsonProperty(value = "complete_line")
	@Column(name = "complete_line")
	private Integer completeLine;
	
	
	@ApiModelProperty("当前线路完成的关卡数")
	@JsonProperty(value = "complete_num")
	@Column(name = "complete_num")
	private Integer completeNum;
	
	
	@ApiModelProperty("当前线路最新一天通关的次数")
	@JsonProperty(value = "today_pass")
	@Column(name = "today_pass")
	private Integer todayPass;
	
	
	@ApiModelProperty("用户该线路最新闯关的时间 与 today_pass 结合")
	@JsonProperty(value = "today_pass_time")
	@Column(name = "today_pass_time")
	private Date todayPassTime;
	
	
	@ApiModelProperty("当前线路最后一次完成的id")
	@JsonProperty(value = "last_complete_circuit_detail_id")
	@Column(name = "last_complete_circuit_detail_id")
	private Long lastCompleteCircuitDetailId;
	
	
	@ApiModelProperty("当前线路下次开始的Id")
	@JsonProperty(value = "begin_circuit_detail_id")
	@Column(name = "begin_circuit_detail_id")
	private Long beginCircuitDetailId;
	
	
	@ApiModelProperty("首次完成时间")
	@JsonProperty(value = "first_complete_time")
	@Column(name = "first_complete_time")
	private Date firstCompleteTime;
	
	
	@ApiModelProperty("最后完成时间")
	@JsonProperty(value = "last_complete_time")
	@Column(name = "last_complete_time")
	private Date lastCompleteTime;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("修改时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
}

