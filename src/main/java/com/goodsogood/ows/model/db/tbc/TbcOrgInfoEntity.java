package com.goodsogood.ows.model.db.tbc;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_tbc_org_info 实体类
 *
 * 党业融合组织信息\r\n 
 *
 * <AUTHOR>
 * @create 2021-12-27 17:19
*/
@Data
@ApiModel
@Table(name = "t_tbc_org_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TbcOrgInfoEntity {
	
	@Id
	@JsonProperty(value = "tbc_org_info_id")
	@Column(name = "tbc_org_info_id")
	private Long tbcOrgInfoId;
	
	
	@ApiModelProperty("组织类型")
	@JsonProperty(value = "org_type_child")
	@Column(name = "org_type_child")
	private Integer orgTypeChild;
	
	
	@ApiModelProperty("组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;
	
	
	@ApiModelProperty("组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;
	
	
	@ApiModelProperty("组织名称")
	@JsonProperty(value = "org_level")
	@Column(name = "org_level")
	private String orgLevel;
	
	
	@JsonProperty(value = "owner_id")
	@Column(name = "owner_id")
	private Long ownerId;
	
	
	@ApiModelProperty("党建指标")
	@JsonProperty(value = "party_index")
	@Column(name = "party_index")
	private Double partyIndex=0.0;

    @ApiModelProperty("党建积分")
    @JsonProperty(value = "party_score")
    @Column(name = "party_score")
    private Double partyScore=0.0;
	
	
	@ApiModelProperty("业务指标")
	@JsonProperty(value = "business_index")
	@Column(name = "business_index")
	private Double businessIndex=0.0;

    @ApiModelProperty("业务积分")
    @JsonProperty(value = "business_score")
    @Column(name = "business_score")
    private Double businessScore=0.0;
	
	@ApiModelProperty("创建指标")
	@JsonProperty(value = "innovation_index")
	@Column(name = "innovation_index")
	private Double innovationIndex=0.0;

    @ApiModelProperty("创建积分")
    @JsonProperty(value = "innovation_score")
    @Column(name = "innovation_score")
    private Double innovationScore=0.0;
	
	
	@ApiModelProperty("堡垒指标")
	@JsonProperty(value = "fortress_index")
	@Column(name = "fortress_index")
	private Double fortressIndex=0.0;

    @ApiModelProperty("堡垒对数指标")
    @JsonProperty(value = "log_fortress_index")
    @Column(name = "log_fortress_index")
    private Double logFortressIndex=0.0;
	
	@ApiModelProperty("统计时间（202110或者202101)")
	@JsonProperty(value = "sta_month")
	@Column(name = "sta_month")
	private String staMonth;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("更新时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;
	
}

