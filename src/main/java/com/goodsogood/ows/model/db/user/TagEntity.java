package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 标签entity
 *
 * <AUTHOR>
 * @date 2018-03-23
 */
@Data
@ApiModel
@Table(name = "t_tag")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TagEntity {

     @Id
     @ApiModelProperty(value = "标签ID")
     @Column(name = "tag_id")
     @JsonProperty("tag_id")
     @GeneratedValue(generator = "JDBC")
     private Long tagId;

     @ApiModelProperty(value = "父标签ID")
     @Column(name = "parent_id")
     @JsonProperty("parent_id")
     private Long parentId;

     @ApiModelProperty(value = "标签名称")
     private String name;

     @ApiModelProperty(value = "状态")
     private Integer status;

     @ApiModelProperty(value = "标签类型")
     @Column(name = "tag_type")
     @JsonProperty("tag_type")
     private Integer tagType;

     @ApiModelProperty(value = "权重值")
     private Integer seq;

     @ApiModelProperty(value = "单位ID")
     @Column(name = "corporation_id")
     private Long corporationId;

     @ApiModelProperty(value = "组织ID")
     @Column(name = "organization_id")
     private Long organizationId;

     @ApiModelProperty(value = "区县ID")
     @Column(name = "region_id")
     @JsonProperty("region_id")
     private Long regionId;

     @ApiModelProperty(value = "类型")
     @Column(name = "org_type")
     private Integer orgType;

     @ApiModelProperty(value = "最后修改人")
     @Column(name = "last_change_user")
     private Long lastChangeUser;

     @ApiModelProperty(value = "标签所属/对象")
     @Column(name = "belong")
     private Integer belong;

     @ApiModelProperty(value = "创建时间")
     @Column(name = "create_time")
     private Date createTime;

     @ApiModelProperty(value = "更新时间")
     @Column(name = "update_time")
     private Date updateTime;

}
