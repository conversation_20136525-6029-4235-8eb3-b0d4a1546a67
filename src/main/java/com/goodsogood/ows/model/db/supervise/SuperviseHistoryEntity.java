package com.goodsogood.ows.model.db.supervise;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_supervise_history 实体类
 *
*/
@ApiModel
@Table(name = "t_supervise_history")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SuperviseHistoryEntity {
	
	@Id
	@JsonProperty(value = "t_supervise_history_id")
	@Column(name = "t_supervise_history_id")
	private Long superviseHistoryId;
	
	
	@ApiModelProperty("统计时间")
	@JsonProperty(value = "date")
	@Column(name = "date")
	private Date date;

	@ApiModelProperty("自身异常数量")
    @Column(name = "self")
    @JsonProperty("self")
    private Integer self;
	
	
	@ApiModelProperty("下级（党委是党支部 党支部是党员）异常数量")
	@JsonProperty(value = "sub")
	@Column(name = "sub")
	private Integer sub;

	@ApiModelProperty("组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Long getSuperviseHistoryId() {
		return superviseHistoryId;
	}

	public void setSuperviseHistoryId(Long superviseHistoryId) {
		this.superviseHistoryId = superviseHistoryId;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Integer getSelf() {
		return self;
	}

	public void setSelf(Integer self) {
		this.self = self;
	}

	public Integer getSub() {
		return sub;
	}

	public void setSub(Integer sub) {
		this.sub = sub;
	}
}

