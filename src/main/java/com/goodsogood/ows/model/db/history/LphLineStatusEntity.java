package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_line_status 实体类
 *
 * 区县线路总表状态 
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_line_status")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphLineStatusEntity  extends  BaseEntity{
	
//	@Id
	@ApiModelProperty("区县线路总表状态主键")
	@JsonProperty(value = "line_id")
	@Column(name = "line_id")
	private Long lineId;
	
	
	@ApiModelProperty("区县ID")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Integer regionId;
	
	
	@ApiModelProperty("线路总表ID")
	@JsonProperty(value = "circuit_id")
	@Column(name = "circuit_id")
	private Long circuitId;
	
	
	@ApiModelProperty("参与用户人数总值")
	@JsonProperty(value = "people_number")
	@Column(name = "people_number")
	private Integer peopleNumber;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("修改时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
}

