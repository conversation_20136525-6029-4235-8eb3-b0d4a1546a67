package com.goodsogood.ows.model.db.sas;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_statistical_temp_activity 实体类
 *
 * <AUTHOR>
 * @create 2019-05-05 09:05
*/
@Data
@ApiModel
@Table(name = "t_statistical_temp_activity")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatisticalTempActivityEntity {
	
	@Id
	@Column(name = "id")
	private Integer id;
	
	
	@JsonProperty(value = "activity_id")
	@Column(name = "activity_id")
	private Integer activityId;
	
	
	@JsonProperty(value = "type_name")
	@Column(name = "type_name")
	private String typeName;
	
}

