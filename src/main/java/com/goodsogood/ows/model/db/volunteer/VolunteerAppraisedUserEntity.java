package com.goodsogood.ows.model.db.volunteer;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * t_volunteer_appraised_user
 *
 * <p>志愿者评价
 *
 * <AUTHOR>
 * @date 2020-08-17 09:54
 */
@Data
@ApiModel
@Table(name = "t_volunteer_appraised_user")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VolunteerAppraisedUserEntity {

	@Id
	@GeneratedValue(generator = "JDBC")
	@ApiModelProperty("id")
	@JsonProperty(value = "appraised_user_id")
	@Column(name = "appraised_user_id")
	private Long appraisedUserId;

	@ApiModelProperty("招募id。对应评价的项目-岗位")
	@JsonProperty(value = "recruit_id")
	@Column(name = "recruit_id")
	private Long recruitId;

	@ApiModelProperty("志愿者id（冗余）")
	@JsonProperty(value = "volunteer_user_id")
	@Column(name = "volunteer_user_id")
	private Long volunteerUserId;

	@ApiModelProperty("审批意见（100个字）")
	@Column(name = "content")
	private String content;

	@ApiModelProperty("守时程度（0-5）默认0")
	@JsonProperty(value = "punctuality_index")
	@Column(name = "punctuality_index")
	private Integer punctualityIndex;

	@ApiModelProperty("服务态度（0-5）。默认0")
	@JsonProperty(value = "attitude_index")
	@Column(name = "attitude_index")
	private Integer attitudeIndex;

	@ApiModelProperty("专业水平（0-5）默认 0")
	@JsonProperty(value = "professional_index")
	@Column(name = "professional_index")
	private Integer professionalIndex;

	@ApiModelProperty("逻辑删除。0 正常 1 删除")
	@JsonProperty(value = "is_del")
	@Column(name = "is_del")
	private Integer isDel;

	@ApiModelProperty("标记位：1.管理员评价 2.未到岗标记")
	@JsonProperty(value = "add_tag")
	@Column(name = "add_tag")
	private Integer addTag;

	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date createTime;

	@ApiModelProperty("创建用户")
	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;

	@ApiModelProperty("更新时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;

	@ApiModelProperty("最后更新用户")
	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;
}
