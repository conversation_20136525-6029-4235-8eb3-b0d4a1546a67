package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 数据字典entity
 *
 * <AUTHOR>
 * @date 2018-03-29
 */
@Data
@ApiModel
@Table(name = "t_option")
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OptionEntity {

    @Id
    @ApiModelProperty("字典code")
    private String code;

    @ApiModelProperty("字典key")
    @JsonProperty("op_key")
    private Integer opKey;

    @ApiModelProperty("字典value")
    @JsonProperty("op_value")
    private String opValue;

    @ApiModelProperty("排序值")
    @JsonProperty("seq")
    private Integer seq;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getOpKey() {
        return opKey;
    }

    public void setOpKey(Integer opKey) {
        this.opKey = opKey;
    }

    public String getOpValue() {
        return opValue;
    }

    public void setOpValue(String opValue) {
        this.opValue = opValue;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }
}
