package com.goodsogood.ows.model.db.experience;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 *
 * t_user_party_eval_rule 实体类
 *
 * 评分项目 
 *
 * <AUTHOR>
 * @create 2022-03-11 10:52
*/
@Data
@ApiModel
@Table(name = "t_user_party_eval_rule")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserPartyEvalRuleEntity {
	
	@Id
	@JsonProperty(value = "rule_id")
	@Column(name = "rule_id")
	private Integer ruleId;
	
	
	@JsonProperty(value = "rule_name")
	@Column(name = "rule_name")
	private String ruleName;
	
	
	@ApiModelProperty("没有上级为0")
	@JsonProperty(value = "parent_id")
	@Column(name = "parent_id")
	private Integer parentId;
	
	
	@ApiModelProperty("考核对象：0：全体党员  1：管理员  2：党组成员  3：党员领导干部  4：支部书记  5：组织管理员（党委、党总支、党支部、党小组）")
	@JsonProperty(value = "rule_target")
	@Column(name = "rule_target")
	private Byte ruleTarget;
	
	
	@ApiModelProperty("考核周期：1:每月  2：每年  3：每季度 4：所有  5:一年同期")
	@JsonProperty(value = "rule_period")
	@Column(name = "rule_period")
	private Byte rulePeriod;
	
	
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Long regionId;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private LocalDateTime createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private LocalDateTime updateTime;

	@JsonProperty(value = "is_show")
	@Column(name = "is_show")
	private Integer isShow;
	
}

