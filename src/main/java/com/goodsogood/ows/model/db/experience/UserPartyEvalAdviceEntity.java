package com.goodsogood.ows.model.db.experience;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 *
 * t_user_party_eval_advice 实体类
 *
 * 意见建议 
 *
 * <AUTHOR>
 * @create 2022-03-11 10:52
*/
@Data
@ApiModel
@Table(name = "t_user_party_eval_advice")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserPartyEvalAdviceEntity {
	
	@Id
	@JsonProperty(value = "advice_id")
	@Column(name = "advice_id")
	private Integer adviceId;
	
	
	@JsonProperty(value = "advice_name")
	@Column(name = "advice_name")
	private String adviceName;
	
	
	@ApiModelProperty("细项id")
	@JsonProperty(value = "rule_id")
	@Column(name = "rule_id")
	private Integer ruleId;


	@ApiModelProperty("大项id")
	@JsonProperty(value = "rule_pid")
	@Column(name = "rule_pid")
	private Integer rulePid;
	
	@ApiModelProperty("参数类型")
	@JsonProperty(value = "param_type")
	@Column(name = "param_type")
	private Byte paramType;


	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Long regionId;
	
	
	@ApiModelProperty("1:默认(未达五星默认报告)  ")
	@JsonProperty(value = "default_status")
	@Column(name = "default_status")
	private Byte defaultStatus;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private LocalDateTime createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private LocalDateTime updateTime;
	
}

