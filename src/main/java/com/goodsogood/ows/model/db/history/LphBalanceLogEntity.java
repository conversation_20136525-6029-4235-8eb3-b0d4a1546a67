package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_balance_log 实体类
 *
 * 每日闯关币总余额记录表 
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_balance_log")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphBalanceLogEntity {

//	@Id
	@Column(name = "id")
	private Long id;
	
	
	@ApiModelProperty("余额")
	@Column(name = "balance")
	private Long balance;
	
	
	@ApiModelProperty("区县id")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Integer regionId;
	
	
	@ApiModelProperty("记录日期")
	@JsonProperty(value = "log_time")
	@Column(name = "log_time")
	private Date logTime;
	
}

