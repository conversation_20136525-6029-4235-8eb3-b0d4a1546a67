package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 积分详情实体
 * <AUTHOR>
 * @create 2018-06-09 15:08
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_score_detail")
public class ScoreDetailEntity {

     @Id
     @Column(name = "score_detail_id")
     @ApiModelProperty(name = "id")
     private Long scoreDetailId;

     @Column(name = "score_user_id")
     @ApiModelProperty(name = "积分用户用id")
     private Long scoreUserId;

     @Column(name = "score")
     @ApiModelProperty(name = "积分")
     private Long score;

     @Column(name = "token")
     @ApiModelProperty(name = "单次提交唯一标识")
     private String token;

     @Column(name = "trade_no")
     @ApiModelProperty(name = "交易流水号")
     private String tradeNo;

     @Column(name = "out_trade_no")
     @ApiModelProperty(name = "第三方系统交易流水号")
     private String outTradeNo;

     @Column(name = "app_id")
     @ApiModelProperty(name = "第三方系统id")
     private Long appId;

     @Column(name = "oper_type")
     @ApiModelProperty(name = "操作类型：0：添加；1：扣分;2调整积分")
     private Byte operType;

     @Column(name = "score_type")
     @ApiModelProperty(name = "积分类型")
     private Byte scoreType;

     @Column(name = "score_type_name")
     @ApiModelProperty(name = "积分类型名称")
     private String scoreTypeName;

     @Column(name = "create_time")
     @ApiModelProperty(name = "创建时间")
     private Date createTime;

     @Column(name = "update_time")
     @ApiModelProperty(name = "更新时间")
     private Date updateTime;

     @Column(name = "remark")
     @ApiModelProperty(name = "备注")
     private String remark;

     @Column(name = "consume_time")
     @ApiModelProperty(name = "消费时间")
     private Date consumeTime;

     @ApiModelProperty("组织Id")
     @JsonProperty(value = "org_id")
     @Column(name = "org_id")
     private Long orgId;

     @ApiModelProperty("操作rmb数量")
     @Column(name = "rmb")
     private BigDecimal rmb;


}
