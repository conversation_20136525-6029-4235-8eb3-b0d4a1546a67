package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 积分规则说明表
 * t_score_rule_explain 实体类
 * <AUTHOR>
*/
@Data
@ApiModel
@Table(name = "t_score_rule_explain_new")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ScoreRuleExplainNewEntity {

	@Id
	@ApiModelProperty(name = "id")
	@JsonProperty(value = "rule_explain_id")
	@Column(name = "rule_explain_id")
	private Long ruleExplainId;


	/**
	 * 积分对象   1 党员,2 党支部 3 党总支 4 党委 5 党组
	 */
	@ApiModelProperty("积分对象")
	@JsonProperty(value = "score_object")
	@Column(name = "score_object")
	private Integer scoreObject;

	/**
	 * 积分类型  1.党建指标 2.业务指标 3.创新指标
	 */
	@ApiModelProperty("积分类型")
	@JsonProperty(value = "parent_score_type")
	@Column(name = "parent_score_type")
	private Integer parentScoreType;

	/**
	 * 积分类型文本
	 */
	@ApiModelProperty("积分类型文本")
	@JsonProperty(value = "parent_score_type_name")
	@Column(name = "parent_score_type_name")
	private String parentScoreTypeName;


	/**
	 * 积分类别  1.党建指标 2.业务指标 3.创新指标
	 */
	@ApiModelProperty("积分类型")
	@JsonProperty(value = "score_type")
	@Column(name = "score_type")
	private Integer scoreType;

	/**
	 * 积分类别文本
	 */
	@ApiModelProperty("积分类型文本")
	@JsonProperty(value = "score_type_name")
	@Column(name = "score_type_name")
	private String scoreTypeName;

	@ApiModelProperty("分值")
	@JsonProperty(value = "score")
	@Column(name = "score")
	private Double score;

	@Transient
	@ApiModelProperty("处理后的分值")
	private String scoreNew;


	@ApiModelProperty("规则说明")
	@Column(name = "rule_explain")
	private String ruleExplain;

	@ApiModelProperty("规则")
	private String rule;

	@ApiModelProperty("规则简要说明")
	private String shortExplain;

	@ApiModelProperty("1-每日  2-每月  3-每季度  4-每年")
	private Short type;

	@ApiModelProperty("分值上限")
	private Integer maxScore;

	@Transient
	private Integer scoreTotal;

	private Long ruleId;

}

