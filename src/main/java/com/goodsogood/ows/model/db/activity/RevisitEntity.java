package com.goodsogood.ows.model.db.activity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_revisit 实体类
 *
 * 重温入党志愿书和入党誓词 
 *
 * <AUTHOR>
 * @create 2021-09-15 16:41
*/
@Data
@ApiModel
@Table(name = "t_revisit")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RevisitEntity {
	
	@Id
	@JsonProperty(value = "revisit_id")
	@Column(name = "revisit_id")
	private Long revisitId;
	
	
	@ApiModelProperty("任务id")
	@JsonProperty(value = "task_id")
	@Column(name = "task_id")
	private String taskId;

    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;


    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    private String  orgLevel;

	@JsonProperty(value = "user_id")
	@Column(name = "user_id")
	private Long userId;
	
	
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Long regionId;
	
	
	@ApiModelProperty("思想感悟")
	@Column(name = "sentiment")
	private String sentiment;
	
	
	@ApiModelProperty("1.入党誓词 2.入党志愿书")
	@Column(name = "type")
	private Integer type;
	
	
	@ApiModelProperty("状态 1.显示 2.不显示")
	@Column(name = "status")
	private Integer status;
	
	
	@ApiModelProperty("季度 ,int (类型表示) ,例如:202103")
	@Column(name = "quarterly")
	private Integer quarterly;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
	
	@ApiModelProperty("回调任务系统时间")
	@JsonProperty(value = "callback_task_time")
	@Column(name = "callback_task_time")
	private Date callbackTaskTime;

	
}

