package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * @Author: tc
 * @Description 积分订单数据表
 * @Date 20:16 2018/7/31
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_score_order")
public class ScoreOrderEntity extends ScoreOrderSupperEntity{
     @Id
     @Column(name = "order_id")
     @GeneratedValue(generator = "JDBC")
     @ApiModelProperty(name = "id")
     private Long orderId;
}
