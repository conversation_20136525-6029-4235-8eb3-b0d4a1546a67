package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.time.LocalDateTime;

@Data
@ApiModel
@Table(name = "t_score_mid_detail")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ScoreMidDetailEntity {
    @Id
    private Long midDetailId;
    private Long userId;
    private Long scoreUserId;
    private String userName;
    private Long orgId;
    private String orgName;
    private Long unitId;
    private String unitName;
    private Long parentId;
    private String orgLevel;
    private Integer dataYear;
    private String dataMonth;
    private Integer score;//月度总积分
    private Integer unitScore;//月度单位积分
    private Integer usualScore;//月度通用积分
    private Integer type;//1-月度积分 2-年度积分  3-总积分
    private LocalDateTime createTime;
    @Transient
    private Long totalScore;
    @Transient
    private Integer monthRank;//系统月度排名-全市通用积分
    @Transient
    private Integer yearRank;//系统年度排名-全市通用积分
    @Transient
    private Integer monthUnitRank;//系统月度排名-单位自主积分
    @Transient
    private Integer yearUnitRank;//系统年度排名-单位自主积分
    @Transient
    private Integer rank;
    @Transient
    private Integer unitRank;
}
