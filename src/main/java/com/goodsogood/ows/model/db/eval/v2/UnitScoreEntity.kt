package com.goodsogood.ows.model.db.eval.v2

import org.springframework.data.util.ProxyUtils
import java.time.LocalDateTime
import javax.persistence.*
import javax.validation.constraints.NotNull
import javax.validation.constraints.Size
import kotlin.jvm.Transient

@Entity
@Table(name = "t_eval_v2_unit_score")
data class UnitScoreEntity(
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "v2_unit_score_id", nullable = false)
    var id: Long? = null,

    @NotNull
    @Column(name = "year", nullable = false)
    var year: Int? = null,

    @NotNull
    @Column(name = "metric_class_id", nullable = false)
    var metricClassId: Long? = null,

    @NotNull
    @Column(name = "metric_id", nullable = false)
    var metricId: Long? = null,

    @Transient
    var metricName: String? = null,

    @NotNull
    @Column(name = "org_id", nullable = false)
    var orgId: Long? = null,

    @Size(max = 200)
    @Column(name = "org_name", length = 200)
    var orgName: String? = null,

    @Size(max = 50)
    @NotNull
    @Column(name = "org_level", nullable = false, length = 50)
    var orgLevel: String? = null,

    @NotNull
    @Column(name = "unit_id", nullable = false)
    var unitId: Long? = null,

    @Size(max = 200)
    @Column(name = "unit_name", length = 200)
    var unitName: String? = null,

    @Column(name = "sys_score", precision = 24, scale = 6)
    var sysScore: Double? = null,

    @Column(name = "assmt_score", precision = 24, scale = 6)
    var assmtScore: Double? = null,

    @Column(name = "party_score", precision = 24, scale = 6)
    var partyScore: Double? = null,

    @NotNull
    @Column(name = "final_score", nullable = false, precision = 24, scale = 6)
    var finalScore: Double? = null,

    @Column(name = "modified_flag")
    var modifiedFlag: Int? = null,

    @Size(max = 900)
    @Column(name = "remark", length = 900)
    var remark: String? = null,

    @NotNull
    @Column(name = "create_time", nullable = false)
    var createTime: LocalDateTime? = null,

    @NotNull
    @Column(name = "last_change_user", nullable = false)
    var lastChangeUser: Long? = null,

    @NotNull
    @Column(name = "update_time", nullable = false)
    var updateTime: LocalDateTime? = null,
) {
    final override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || ProxyUtils.getUserClass(this) != ProxyUtils.getUserClass(
                other
            )
        ) return false
        other as UnitScoreEntity

        return id != null && id == other.id
    }

    final override fun hashCode(): Int = javaClass.hashCode()

    @Override
    override fun toString(): String {
        return this::class.simpleName + "(id = $id , year = $year , metricClassId = $metricClassId , metricId = $metricId , orgId = $orgId , orgName = $orgName , orgLevel = $orgLevel , unitId = $unitId , unitName = $unitName , sysScore = $sysScore , assmtScore = $assmtScore , partyScore = $partyScore , finalScore = $finalScore , modifiedFlag = $modifiedFlag , remark = $remark , createTime = $createTime , lastChangeUser = $lastChangeUser , updateTime = $updateTime )"
    }
}