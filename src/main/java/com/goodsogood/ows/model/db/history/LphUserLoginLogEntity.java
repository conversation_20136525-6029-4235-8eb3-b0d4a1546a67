package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_user_login_log 实体类
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_user_login_log")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphUserLoginLogEntity   extends  BaseEntity{
	
//	@Id
	@ApiModelProperty("主键")
	@JsonProperty(value = "log_id")
	@Column(name = "log_id")
	private Long logId;
	
	
	@ApiModelProperty("小程序唯一标识")
	@JsonProperty(value = "open_id")
	@Column(name = "open_id")
	private String openId;
	
	
	@ApiModelProperty("区县id")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Integer regionId;
	
	
	@ApiModelProperty("ip地址")
	@Column(name = "ip")
	private String ip;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
}

