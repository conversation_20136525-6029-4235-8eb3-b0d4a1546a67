package com.goodsogood.ows.model.db.task;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "t_task_access")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TaskAccessEntity {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "task_access_id")
    @ApiModelProperty("主键")
    private Long taskAccessId;

    /**
     * 任务id
     */
    @Column(name = "task_id")
    @ApiModelProperty("任务id")
    private Long taskId;

    /**
     * 接收组织id
     */
    @Column(name = "access_org_id")
    @ApiModelProperty("接收组织id")
    private Long accessOrgId;

    /**
     * 接收用户id
     */
    @Column(name = "access_user_id")
    @ApiModelProperty("接收用户id")
    private Long accessUserId;

    /**
     * 接收用户组织id
     */
    @Column(name = "access_user_org_id")
    @ApiModelProperty("接收用户组织id")
    private Long accessUserOrgId;

    /**
     * 接收范围（1：组织，2：人员）
     */
    @Column(name = "access_range")
    @ApiModelProperty("接收范围（1：组织，2：人员）")
    private Integer accessRange;

    /**
     * 操作状态（1：待填报，2：草稿，3：已提交，4：已转派，5：已撤回，6：已退回，7：未通过，8：已通过，9：逾期未提交）
     */
    @Column(name = "operate_status")
    @ApiModelProperty("操作状态（1：待填报，2：草稿，3：已提交，4：已转派，5：已撤回，6：已退回，7：未通过，8：已通过，9：逾期未提交）")
    private Integer operateStatus;

    /**
     * 评分-100分制
     */
    @Column(name = "assess_score")
    @ApiModelProperty("评分-100分制")
    private Integer assessScore;

    /**
     * 评价内容
     */
    @Column(name = "assess_content")
    @ApiModelProperty("评价内容")
    private String assessContent;

    /**
     * 审核人id
     */
    @Column(name = "verify_user_id")
    @ApiModelProperty("最终审核人id")
    private Long verifyUserId;

    /**
     * 审核组织id
     */
    @Column(name = "verify_org_id")
    @ApiModelProperty("审核组织id")
    private Long verifyOrgId;

    /**
     * 是否已读（0：否，1：是）
     */
    @Column(name = "is_read")
    @ApiModelProperty("是否已读（0：否，1：是）")
    private Integer isRead;

    /**
     * 是否被删除（0：否，1：是）
     */
    @Column(name = "is_del")
    @ApiModelProperty("是否被删除（0：否，1：是）")
    private Integer isDel;

    /**
     * 是否已经推送了任务提醒
     */
    @Column(name = "is_remind_type")
    @ApiModelProperty("是否已经推送了任务提醒: 1:提醒完毕")
    private Integer isRemindType;

    /**
     * 今日是否推送 1: 是  0: 否 (自定义提醒时间用)
     */
    @Column(name = "repeat_push_type")
    @ApiModelProperty("今日是否推送 1: 是  0: 否 (自定义提醒时间用)")
    private Integer repeatPushType;

    /**
     * 派发时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("派发时间")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /**
     * 最后修改人
     */
    @Column(name = "last_change_user")
    @ApiModelProperty("最后修改人")
    private Long lastChangeUser;

}
