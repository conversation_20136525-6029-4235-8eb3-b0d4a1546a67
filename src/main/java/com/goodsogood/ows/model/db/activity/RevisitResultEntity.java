package com.goodsogood.ows.model.db.activity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_revisit_result 实体类
 *
 * 重温入党志愿书和入党誓词 
 *
 * <AUTHOR>
 * @create 2021-09-15 16:41
*/
@Data
@ApiModel
@Table(name = "t_revisit_result")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RevisitResultEntity {
	
	@Id
	@JsonProperty(value = "revisit_result_id")
	@Column(name = "revisit_result_id")
	private Long revisitResultId;
	
	
	@ApiModelProperty("感悟id")
	@JsonProperty(value = "revisit_id")
	@Column(name = "revisit_id")
	private Long revisitId;


    @ApiModelProperty("任务id")
    @JsonProperty(value = "task_id")
    @Column(name = "task_id")
    private String taskId;

    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;


    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;

    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    private String  orgLevel;

	@JsonProperty(value = "user_id")
	@Column(name = "user_id")
	private Long userId;


    @ApiModelProperty(value = "单位id")
    @JsonProperty(value = "own_Id")
    @Column(name = "own_Id")
    private Long ownId;

    @ApiModelProperty(value = "单位名称")
    @JsonProperty(value = "own_name")
    @Column(name = "own_name")
    private Long ownName;

    @JsonProperty(value = "org_type_child")
    @Column(name = "org_type_child")
    private Integer  orgTypeChild;
	
	@ApiModelProperty("1.入党誓词浏览 2.入党志愿书浏览 3.入党誓词填写 4.入党志愿书填写")
	@Column(name = "type")
	private Integer type;
	
	
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Long regionId;
	
	
	@ApiModelProperty("发送积分的唯一标识")
	@Column(name = "uuid")
	private String uuid;
	
	
	@ApiModelProperty("发送积分")
	@Column(name = "score")
	private Integer score;
	
	
	@ApiModelProperty("状态 1.显示 2.不显示")
	@Column(name = "status")
	private Integer status;
	
	
	@ApiModelProperty("季度 ,int (类型表示) ,例如:20213")
	@Column(name = "quarterly")
	private Integer quarterly;
	
	
	@ApiModelProperty("备注")
	@Column(name = "remark")
	private String remark;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;


    @ApiModelProperty("回调任务系统时间")
    @JsonProperty(value = "callback_task_time")
    @Column(name = "callback_task_time")
    private Date callbackTaskTime;


}

