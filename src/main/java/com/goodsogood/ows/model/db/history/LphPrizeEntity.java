package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_prize 实体类
 *
 * 奖品详情表 
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_prize")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphPrizeEntity   extends  BaseEntity{
	
//	@Id
	@ApiModelProperty("奖品主键")
	@JsonProperty(value = "prize_id")
	@Column(name = "prize_id")
	private Long prizeId;
	
	
	@ApiModelProperty("奖品编号")
	@Column(name = "mark")
	private Integer mark;
	
	
	@ApiModelProperty("区县id组")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Integer regionId;
	
	
	@ApiModelProperty("奖品类型(积分:0 , 实物:1")
	@JsonProperty(value = "prize_type")
	@Column(name = "prize_type")
	private Byte prizeType;
	
	
	@ApiModelProperty("奖品名称")
	@JsonProperty(value = "prize_name")
	@Column(name = "prize_name")
	private String prizeName;
	
	
	@ApiModelProperty("当前奖品偏移量")
	@JsonProperty(value = "now_offset")
	@Column(name = "now_offset")
	private Long nowOffset;
	
	
	@ApiModelProperty("奖品图标")
	@JsonProperty(value = "prize_url")
	@Column(name = "prize_url")
	private String prizeUrl;
	
	
	@ApiModelProperty("奖励积分")
	@JsonProperty(value = "score_num")
	@Column(name = "score_num")
	private Long scoreNum;
	
	
	@ApiModelProperty("奖品数量")
	@JsonProperty(value = "prize_number")
	@Column(name = "prize_number")
	private Integer prizeNumber;
	
	
	@ApiModelProperty("参与人数")
	@JsonProperty(value = "participants_num")
	@Column(name = "participants_num")
	private Integer participantsNum;
	
	
	@ApiModelProperty("已经抽中的数量")
	@JsonProperty(value = "pt_num")
	@Column(name = "pt_num")
	private Integer ptNum;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("修改时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
}

