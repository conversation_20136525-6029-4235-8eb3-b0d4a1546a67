package com.goodsogood.ows.model.db.sas;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@ApiModel
@Table(name = "t_pbm_target")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PbmTargetEntity {


    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "pbm_target_id")
    @Column(name = "pbm_target_id")
    private Long pbmTargetId;


    @ApiModelProperty("单位id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("人员id")
    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    private Long userId;

    @ApiModelProperty("人员姓名")
    @JsonProperty(value = "user_name")
    @Column(name = "user_name")
    private String userName;


    @ApiModelProperty("手机号")
    @JsonProperty(value = "phone")
    @Column(name = "phone")
    private String phone;


    @ApiModelProperty("所在单位")
    @JsonProperty(value = "unit")
    @Column(name = "unit")
    private String unit;

    @JsonProperty(value = "unit_id")
    @Column(name = "unit_id")
    private String unitId;


    @ApiModelProperty("所在部门")
    @JsonProperty(value = "department")
    @Column(name = "department")
    private String department;

    @JsonProperty(value = "department_id")
    @Column(name = "department_id")
    private String departmentId;


    @ApiModelProperty("是否党员 1-是 2-否")
    @JsonProperty(value = "is_party_member")
    @Column(name = "is_party_member")
    private Integer isPartyMember;

    @JsonProperty(value = "branch_id")
    @Column(name = "branch_id")
    private Long branchId;

    @ApiModelProperty("所在支部")
    @JsonProperty(value = "branch")
    @Column(name = "branch")
    private String branch;


    @ApiModelProperty("月度绩效得分")
    @JsonProperty(value = "score")
    @Column(name = "score")
    private Double score;

    @ApiModelProperty("备注")
    @JsonProperty(value = "remark")
    @Column(name = "remark")
    private String remark;

    @ApiModelProperty("时间")
    @JsonProperty(value = "time")
    @Column(name = "time")
    private String time;

    private Long lastChangeUser;

    private Date createTime;

    private Date updateTime;

}
