package com.goodsogood.ows.model.db.eval;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 统计实体类属性
 * <p>
 * 扣分统计
 *
 * <AUTHOR>
 * @create 2019-03-11 10:28
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EvalCountBaseInfo {

    @ApiModelProperty("目标考核组织")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;


    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;


    @ApiModelProperty("年份")
    @Column(name = "year")
    private Integer year;


    @ApiModelProperty("逾期扣分")
    @Column(name = "overdue")
    private Float overdue = 0f;


    @ApiModelProperty("查处扣分")
    @Column(name = "examine")
    private Float examine = 0f;


    @ApiModelProperty("审核扣分")
    @Column(name = "check_deduct")
    @JsonProperty(value = "check")
    private Float checkDeduct = 0f;


    @ApiModelProperty("数据采集扣分")
    @Column(name = "collect")
    private Float collect = 0f;


    @ApiModelProperty("累计扣分")
    @Column(name = "total")
    private Float total = 0f;


    @ApiModelProperty("最终扣分")
    @Column(name = "real_deduct")
    @JsonProperty(value = "real")
    private Float realDeduct = 0f;

    @ApiModelProperty("能否查看组织扣分记录 1：能查看 2：不能查看")
    @JsonProperty(value = "can_find")
    @Transient
    private Integer canFind = 1;

    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 3.0.0新增
     */
    @ApiModelProperty("区县id")
    @JsonProperty("region_id")
    @Column(name = "region_id")
    private Long regionId;

}

