package com.goodsogood.ows.model.db.activity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_activity 实体类
 *
 * 活动表，可能会按照管理员操作组织进行拆分 
 *
 * <AUTHOR>
 * @create 2019-11-19 14:00
*/
@Data
@ApiModel
@Table(name = "t_activity")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActivityEntity {
	
	@Id
	@ApiModelProperty("活动id")
	@JsonProperty(value = "activity_id")
	@Column(name = "activity_id")
	private Long activityId;
	
	
	@ApiModelProperty("活动长标题")
	@Column(name = "title")
	private String title;
	
	
	@ApiModelProperty("活动短标题")
	@JsonProperty(value = "sub_title")
	@Column(name = "sub_title")
	private String subTitle;
	
	
	@ApiModelProperty("模板名称相同，用于下级评上级的问卷详情回显")
	@JsonProperty(value = "template_name")
	@Column(name = "template_name")
	private Long templateName;
	
	
	@ApiModelProperty("自定义标签的id，用逗号分割")
	@Column(name = "tags")
	private String tags;
	
	
	@ApiModelProperty("活动所属栏目（网宣平台的栏目id）")
	@JsonProperty(value = "column_id")
	@Column(name = "column_id")
	private Long columnId;
	
	
	@ApiModelProperty("参加人员范围：1.开放活动，2.指定参加人员名单，3.指定参加组织人员")
	@JsonProperty(value = "limit_user_scope")
	@Column(name = "limit_user_scope")
	private Byte limitUserScope;
	
	
	@ApiModelProperty("参加人员限制：0.不限制，1.有限制")
	@JsonProperty(value = "limit_user")
	@Column(name = "limit_user")
	private Byte limitUser;
	
	
	@ApiModelProperty("是否可见：0.所有人员可见，1.参加人员可见")
	@JsonProperty(value = "limit_public")
	@Column(name = "limit_public")
	private Byte limitPublic;
	
	
	@ApiModelProperty("部门id")
	@JsonProperty(value = "department_id")
	@Column(name = "department_id")
	private Long departmentId;
	
	
	@ApiModelProperty("活动组织部门（发起单位）名称")
	@JsonProperty(value = "department_name")
	@Column(name = "department_name")
	private String departmentName;
	
	
	@ApiModelProperty("所属组织或者企业id")
	@JsonProperty(value = "organization_id")
	@Column(name = "organization_id")
	private Long organizationId;
	
	
	@ApiModelProperty("单位/组织名称")
	@JsonProperty(value = "organization_name")
	@Column(name = "organization_name")
	private String organizationName;
	
	
	@ApiModelProperty("1.单位，2.组织")
	@JsonProperty(value = "o_type")
	@Column(name = "o_type")
	private Byte oType;
	
	
	@ApiModelProperty("活动通用介绍")
	@Column(name = "description")
	private String description;
	
	
	@ApiModelProperty("活动app通用介绍")
	@JsonProperty(value = "description_app")
	@Column(name = "description_app")
	private String descriptionApp;
	
	
	@ApiModelProperty("活动类型 1:投票；2:问卷调查；3:有奖竞答；4:线下活动；5:积分捐赠")
	@Column(name = "type")
	private Byte type;
	
	
	@ApiModelProperty("活动开始时间")
	@JsonProperty(value = "start_time")
	@Column(name = "start_time")
	private Date startTime;
	
	
	@ApiModelProperty("活动结束时间")
	@JsonProperty(value = "end_time")
	@Column(name = "end_time")
	private Date endTime;
	
	
	@ApiModelProperty("活动的奖励获奖概率（%），奖励的类型随机，如果其他奖品都没了，就肯定给积分；参与就获得奖励，那么设置成100")
	@JsonProperty(value = "hit_rate")
	@Column(name = "hit_rate")
	private Float hitRate;
	
	
	@ApiModelProperty("是否需要登录，1:是，0:否")
	@JsonProperty(value = "need_login")
	@Column(name = "need_login")
	private Byte needLogin;
	
	
	@ApiModelProperty("restriction of user。是否开启账户限制，0:否,1:是")
	@Column(name = "rouser")
	private Byte rouser;
	
	
	@ApiModelProperty("限制用户参与次数")
	@JsonProperty(value = "rouser_time")
	@Column(name = "rouser_time")
	private Short rouserTime;
	
	
	@ApiModelProperty("restriction of ip;是否开启ip限制，0:否，1:是")
	@Column(name = "roip")
	private Byte roip;
	
	
	@ApiModelProperty("限制ip参与次数")
	@JsonProperty(value = "roip_time")
	@Column(name = "roip_time")
	private Short roipTime;
	
	
	@ApiModelProperty("restriction of call phone;是否开启手机限制，0:否，1:是")
	@Column(name = "rophone")
	private Byte rophone;
	
	
	@ApiModelProperty("限制设备参与次数")
	@JsonProperty(value = "rophone_time")
	@Column(name = "rophone_time")
	private Short rophoneTime;
	
	
	@ApiModelProperty("花费积分")
	@Column(name = "cost")
	private Integer cost;
	
	
	@ApiModelProperty("状态，1:正常;2:审批中,3:审批不通过")
	@Column(name = "status")
	private Byte status;
	
	
	@ApiModelProperty("是否需要审批,0:不需要，1：需要")
	@JsonProperty(value = "must_approve")
	@Column(name = "must_approve")
	private Byte mustApprove;
	
	
	@ApiModelProperty("是否加入活动矩阵，1.是，0/null.否")
	@Column(name = "matrix")
	private Byte matrix;
	
	
	@ApiModelProperty("推送时间，冗余")
	@JsonProperty(value = "push_time")
	@Column(name = "push_time")
	private Date pushTime;
	
	
	@ApiModelProperty("对应的审批流程类型id")
	@JsonProperty(value = "workflow_id")
	@Column(name = "workflow_id")
	private Long workflowId;
	
	
	@ApiModelProperty("实际的审批任务流的id")
	@JsonProperty(value = "workflow_task_id")
	@Column(name = "workflow_task_id")
	private Long workflowTaskId;
	
	
	@JsonProperty(value = "workflow_name")
	@Column(name = "workflow_name")
	private String workflowName;
	
	
	@ApiModelProperty("是否删除0.否，1.是")
	@Column(name = "isdel")
	private Byte isdel;
	
	
	@ApiModelProperty("创建人")
	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("修改时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
	
	@ApiModelProperty("最后修改人")
	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;
	
}

