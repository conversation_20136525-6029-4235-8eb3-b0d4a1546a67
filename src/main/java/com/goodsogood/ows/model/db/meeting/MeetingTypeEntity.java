package com.goodsogood.ows.model.db.meeting;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

/**
 *
 * t_meeting_type 实体类
 *
 * 活动与活动类型关联表
 *
 * <AUTHOR>
 * @create 2018-10-26 11:40
*/
@Data
@ApiModel
@Table(name = "t_meeting_type")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingTypeEntity {
	
	@Id
	@GeneratedValue(generator = "JDBC")
	@ApiModelProperty("id")
	@JsonProperty(value = "meeting_type_id")
	@Column(name = "meeting_type_id")
	private Long meetingTypeId;


    @ApiModelProperty("活动id")
	@JsonProperty(value = "meeting_id")
	@Column(name = "meeting_id")
	private Long meetingId;
	
	
	@ApiModelProperty("任务id")
	@JsonProperty(value = "meeting_task_id")
	@Column(name = "meeting_task_id")
	@NotNull(message = "{NotNull.type.meetingTaskId}")
	private Long meetingTaskId;

    @ApiModelProperty("组织生活id")
	@JsonProperty(value = "meeting_plan_id")
	@Transient
	private Long meetingPlanId;
	
	@ApiModelProperty("类型id")
	@JsonProperty(value = "type_id")
	@Column(name = "type_id")
	private Long typeId;
	
	
	@ApiModelProperty("所属类别id。冗余")
	@JsonProperty(value = "category_id")
	@Column(name = "category_id")
	private Long categoryId;
	
	
	@ApiModelProperty("类型。冗余")
	@Column(name = "type")
	private String type;
	
	
	@ApiModelProperty("类别。冗余")
	@Column(name = "category")
	private String category;

	@ApiModelProperty("是否需要签到。0：不需要；1：需要")
	@JsonProperty(value = "is_sign_in")
	@Transient
	private Short isSignIn;

	/**
	 * @version 2019年9月19日 11:12:19
	 */
	@ApiModelProperty("人员选择规则类型：-1 未指定 1.必须选择党小组 2.必须选择支委会届次 3.必须选择组织所有成员")
	@JsonProperty(value = "code")
	@Transient
	private Integer code;
}

