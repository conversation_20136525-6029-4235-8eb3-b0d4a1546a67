package com.goodsogood.ows.model.db.tbcFusion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * @Author: mengting
 * @Date: 2022/5/25 13:55
 */
@Data
@ApiModel
@Table(name = "t_crm_mcs_visit_overview")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CrmMcsVisitOverviewEntity {

    @ApiModelProperty("主键标识")
    @Id
    private Long id ;


    @ApiModelProperty("月份yyyy-MM")
    private String visitMonth;


    @ApiModelProperty("市公司标识 FK:UC_UU_ORGINFO.ORG_UUID")
    private String corpUuid;


    @ApiModelProperty("区县单位标识 FK:UC_UU_ORGINFO.ORG_UUID")
    private String countyUuid;


    @ApiModelProperty("实地拜访覆盖率")
    private  Double onSitePer;


    @ApiModelProperty("拜访完成率")
    private Double  visitCompletedPer;

    @ApiModelProperty("拜访到位率")
    private Double  visitInPlacePer;


    @ApiModelProperty("平均拜访出勤率")
    private Double avgAttendance;



    @ApiModelProperty("标准策略完成率")
    private  Double index13;

    @ApiModelProperty("临时策略完成率")
    private  Double index23;

    @ApiModelProperty("预约策略完成率")
    private  Double index33;


    @ApiModelProperty("预警策略完成率")
    private  Double index43;


    @ApiModelProperty("客户评价-满意率 评价得分(星)")
    private  Double evaluationSatisfactionRate;


    @ApiModelProperty("营销该表修改时间")
    private LocalDateTime updateTime;


    @ApiModelProperty("营销该表创建时间")
    private LocalDateTime createTime;


    @ApiModelProperty("年月-分区字段")
    @Id
    private String ds;

    @ApiModelProperty("年月-分区字段")
    private LocalDateTime myCreateTime;


    @ApiModelProperty("营销人员")
    private String salerDeptUuid;


    @ApiModelProperty("营销人员uuid")
    private String personUuid;


    @ApiModelProperty("手机号")
    private String masterMobile;

    @ApiModelProperty("加密手机号")
    private String phoneSecret;

}
