package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * Auther: ruoyu
 * Date: 2019/10/9
 * Description:
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrgUserActivityForm {

    @JsonProperty("user_id")
    private Long userId;

    @JsonProperty("user_name")
    private String userName;

    @JsonProperty("org_id")
    private Long orgId;

    @JsonProperty("org_name")
    private String orgName;

    @JsonProperty("phone")
    private String phone;

    @JsonProperty("cert_number")
    private String certNumber;

    @JsonProperty("org_type")
    private Integer orgType;

}
