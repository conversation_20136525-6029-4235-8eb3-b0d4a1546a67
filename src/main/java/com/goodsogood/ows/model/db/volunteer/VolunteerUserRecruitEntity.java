package com.goodsogood.ows.model.db.volunteer;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/13
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_volunteer_user_recruit")
public class VolunteerUserRecruitEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "recruit_id")
    @Column(name = "recruit_id")
    @ApiModelProperty(name = "招募id")
    private Long recruitId;

    @JsonProperty(value = "volunteer_team_id")
    @Column(name = "volunteer_team_id")
    @ApiModelProperty(name = "团体id")
    private Long volunteerTeamId;

    @JsonProperty(value = "volunteer_user_id")
    @Column(name = "volunteer_user_id")
    @ApiModelProperty(name = "志愿者id")
    private Long volunteerUserId;

    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    @ApiModelProperty(name = "党建系统用户id")
    private Long userId;

    @JsonProperty(value = "project_id")
    @Column(name = "project_id")
    @ApiModelProperty(name = "志愿项目id")
    private Long projectId;

    @JsonProperty(value = "project_task_id")
    @Column(name = "project_task_id")
    @ApiModelProperty(name = "志愿项目岗位id")
    private Long projectTaskId;

    @ApiModelProperty(name = "姓名")
    private String name;

    @JsonProperty(value = "nick_name")
    @Column(name = "nick_name")
    @ApiModelProperty(name = "昵称")
    private String nickName;

    @JsonProperty(value = "volunteer_number")
    @Column(name = "volunteer_number")
    @ApiModelProperty(name = "志愿者编号")
    private String volunteerNumber;

    @ApiModelProperty(name = "手机号（密文）")
    private String phone;

    @JsonProperty(value = "phone_secret")
    @Column(name = "phone_secret")
    @ApiModelProperty(name = "手机号（脱敏）")
    private String phoneSecret;

    @ApiModelProperty(name = "性别 1-男 2-女 3-未知")
    private Integer gender;

    @ApiModelProperty(name = "年龄")
    private Integer age;

    @JsonProperty(value = "service_district")
    @Column(name = "service_district")
    @ApiModelProperty(name = "服务区域-区")
    private Integer serviceDistrict;

    @JsonProperty(value = "service_district_name")
    @Column(name = "service_district_name")
    @ApiModelProperty(name = "区名称")
    private String serviceDistrictName;

    @ApiModelProperty(name = "状态 0-未处理，1-已同意，2-已拒绝")
    private Integer status;

    @JsonProperty(value = "join_way")
    @Column(name = "join_way")
    @ApiModelProperty(name = "加入方式 0-邀请加入，1-申请加入")
    private Integer joinWay;

    @JsonProperty(value = "is_admin")
    @Column(name = "is_admin")
    @ApiModelProperty(name = "是否管理员 0-否，1-是")
    private Integer isAdmin;

    @JsonProperty(value = "appraised_status")
    @Column(name = "appraised_status")
    @ApiModelProperty(name = "评价状态：0 未评价 （默认）；1 已评价")
    private Integer appraisedStatus;

    @JsonProperty(value = "reported_status")
    @Column(name = "reported_status")
    @ApiModelProperty(name = "到岗状态：0 未到岗；1 已到岗（默认）")
    private Integer reportedStatus;

    @JsonProperty(value = "is_del")
    @Column(name = "is_del")
    @ApiModelProperty(name = "是否删除 0-否，1-是")
    private Integer isDel;

    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    @ApiModelProperty(name = "创建人")
    private Long createUser;

    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    @ApiModelProperty(name = "创建时间")
    private Date createTime;

    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    @ApiModelProperty(name = "最后修改人")
    private Long lastChangeUser;

    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    @ApiModelProperty(name = "修改时间")
    private Date updateTime;

    @JsonProperty(value = "region_id")
    @Column(name = "region_id")
    @ApiModelProperty(name = "服务所属区县编号")
    private Long regionId;

//    @JsonProperty(value = "org_id")
//    @Column(name = "org_id")
//    @ApiModelProperty(name = "用户所属组织id")
//    private Long orgId;
//
//    @JsonProperty(value = "org_name")
//    @Column(name = "org_name")
//    @ApiModelProperty(name = "用户所属组织名称")
//    private String orgName;
}