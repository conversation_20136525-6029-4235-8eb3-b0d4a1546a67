package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2019/11/20
 * @Description:
 **/
@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Table(name = "t_user_org_period_member")
public class OrgPeriodMemberEntity extends BaseAbstractEntity {

    @Id
    @ApiModelProperty(value = "主键")
    @Column(name = "period_member_id")
    @GeneratedValue(generator = "JDBC")
    private Long periodMemberId;
    
    //关联主表id
    @Range(min = 1, message = "{Range.id}")
    @Column(name = "period_id")
    private Long periodId;
    //用户id
    @Range(min = 1, max = 99999999999L, message = "{Range.id}")
    @Column(name = "user_id")
    private Long userId;
    //'用户名称'
    @NotBlank(message = "{NotBlank.user.name}")
    @Column(name = "user_name")
    private String userName;
    //职务　id
   /* @Range(min = 1, max = 9999999999L, message = "{Range.code}")
    private int positionCode;*/
    @ApiModelProperty("职务")
    @Length(min = 1, max = 100, message = "{Length.leader.position}")
    @Column(name = "position_name")
    private String positionName;
    /*@Range(max = 9999999999L, message = "{Range.code}")
    private Integer gradeId;*/
    @ApiModelProperty("职级")
    @Length(max = 100, message = "{Length.leader.position}")
    @Column(name = "grade_name")
    private String gradeName;

    @ApiModelProperty("成员类型（1,党组织委员会成员，2纪律检查委员成员，3党务办公室成员）默认1")
    @Range(min = 1, max = 3, message = "{Range.OrgPeriodMemberAddForm.type}")
    @Column(name = "type")
    private Byte type;

    @Column(name = "is_delete")
    private Short isDelete;
}
