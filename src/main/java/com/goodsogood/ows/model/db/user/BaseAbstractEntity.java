package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * @program: ows-user-center
 * @description: 数据库基本信息抽象类
 * @author: taiqian.Luo
 * @create: 2019-04-09 13:58
 **/
@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public abstract class BaseAbstractEntity {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;

    //创建用户id
    @Column(name = "create_user")
    private Long createUser;

    //创建用户id
    @Column(name = "last_change_user")
    private Long lastChangeUser;

}
