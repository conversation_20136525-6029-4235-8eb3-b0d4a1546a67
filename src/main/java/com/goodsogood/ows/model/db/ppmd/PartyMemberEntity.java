package com.goodsogood.ows.model.db.ppmd;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.goodsogood.ows.helper.Json.DoubleKeepTwoSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Transient;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * Create by FuXiao on 2018/10/24
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_ppmd_party_member")
public class PartyMemberEntity {
    @Id
    @JsonProperty(value = "party_member_id")
    @Column(name = "party_member_id")
    @ApiModelProperty(name = "主键")
    private Long partyMemberId;

    @JsonProperty(value = "reason_id")
    @Column(name = "reason_id")
    @ApiModelProperty(name = "关联的原因描述id")
    private Long reasonId;

    @JsonProperty(value = "user_name")
    @Column(name = "user_name")
    @ApiModelProperty(name = "姓名")
    private String userName;

    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    @ApiModelProperty(name = "用户id")
    private Long userId;

    @JsonProperty(value = "office_id")
    @Column(name = "office_id")
    @ApiModelProperty(name = "机关单位编号")
    private Long officeId;

    @JsonProperty(value = "office_name")
    @Column(name = "office_name")
    @ApiModelProperty(name = "机关单位名称")
    private String officeName;

    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    @ApiModelProperty(name = "支部id")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    @ApiModelProperty(name = "支部名称")
    private String orgName;

    @JsonProperty(value = "cert_number")
    @Column(name = "cert_number")
    @ApiModelProperty(name = "身份证号密文")
    private String certNumber;

    @JsonProperty(value = "cert_number_secret")
    @Column(name = "cert_number_secret")
    @ApiModelProperty(name = "身份证号脱敏文")
    private String certNumberSecret;

    @JsonProperty(value = "cardinal_number")
    @Column(name = "cardinal_number")
    @JsonSerialize(using = DoubleKeepTwoSerializer.class)
    @ApiModelProperty(name = "缴纳基数 单位分")
    private Double cardinalNumber;

    @Column(name = "type")
    @ApiModelProperty(name = "党费规则 1按收入比例 2少交 3免交")
    @NotNull(message = "{NotBlank.stats.type}")
    private Integer type;

    @Column(name = "reason")
    @ApiModelProperty(name = "其他选项时，原因文本")
    private String reason;

    @JsonProperty(value = "revised_party_fee")
    @Column(name = "revised_party_fee")
    @JsonSerialize(using = DoubleKeepTwoSerializer.class)
    @ApiModelProperty(name = "修改后的党费  单位:分")
    private Double revisedPartyFee;

    @JsonProperty(value = "start_time")
    @Column(name = "start_time")
    @ApiModelProperty(name = "开始时间")
    @NotNull(message = "{NotBlank.stats.startTime}")
    private Date startTime;

    @JsonProperty(value = "end_time")
    @Column(name = "end_time")
    @ApiModelProperty(name = "结束时间")
    private Date endTime;

    @JsonProperty(value = "ratio_type")
    @Column(name = "ratio_type")
    @ApiModelProperty(name = "交费比例类型:1.正常党员交费比例  2.退休党员交费比例")
    private Integer ratioType;

    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    @ApiModelProperty(name = "结束时间")
    private Date createTime;

    @JsonProperty(value = "is_import")
    @Column(name = "is_import")
    @ApiModelProperty(name = "是否为导入的交费标准   0 否 1 是")
    private Integer isImport;

    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    @ApiModelProperty(name = "创建人")
    private Long createUser;

    @JsonProperty(value = "resource_id")
    @Column(name = "resource_id")
    @ApiModelProperty(name = "资源id")
    private Long resourceId;

    @JsonProperty(value = "resource_path")
    @Column(name = "resource_path")
    @ApiModelProperty(name = "资源路径")
    private String resourcePath;

    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    @ApiModelProperty(name = "最后修改人")
    private Long lastChangeUser;

    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    @ApiModelProperty(name = "最后修改时间")
    private Date updateTime;

    @Transient
    private Double proportion;

    @Transient
    private Double partyFee;
}
