package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * Auther: ruoyu
 * Date: 19-4-9
 * Description: 组织领导班子表
 */
@Data
@ApiModel
@Table(name = "t_user_org_leader")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrgLeaderEntity {

    @Id
    @ApiModelProperty(value = "主键")
    @Column(name = "leader_id")
    @GeneratedValue(generator = "JDBC")
    private Long leaderId;

    @ApiModelProperty(value = "组织id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty(value = "用户id")
    @Column(name = "user_id")
    private Integer userId;

    @ApiModelProperty(value = "用户名称")
    @Column(name = "user_name")
    private String userName;

    @ApiModelProperty(value = "职务")
    @Column(name="position")
    private String position;

//    @ApiModelProperty(value = "职务id")
//    @Column(name = "position_code")
//    private Integer positionCode;
//
//    @ApiModelProperty(value = "职务名称")
//    @Column(name = "position_name")
//    private String positionName;
//
//    @ApiModelProperty(value = "职级id")
//    @Column(name = "grade_id")
//    private Integer gradeId;
//
//    @ApiModelProperty(value = "职级名称")
//    @Column(name = "grade_name")
//    private String gradeName;

    @ApiModelProperty(value = "任职开始时间")
    @Column(name = "work_date")
    private Date workDate;

    @ApiModelProperty(value = "是否领导干部　1:是　2:否")
    @Column(name = "is_leader")
    private Integer isLeader;

    @ApiModelProperty(value = "是否单位负责人 1:是　2：否")
    @Column(name = "is_head")
    private Integer isHead;

//    @ApiModelProperty(value = "行政单位组织id")
//    @Column(name = "xzdw_org_id")
//    private Integer xzdwOrgId;

    @ApiModelProperty(value = "逻辑删除 1:是　2:否")
    @Column(name = "is_delete")
    private Integer isDelete;

    @ApiModelProperty(value = "创建用户id")
    @Column(name = "create_user")
    private Long createUser;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "create_time")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    @Column(name = "update_time")
    private Date updateTime;

    @ApiModelProperty(value = "最后修改用户")
    @Column(name = "last_change_user")
    private Long last_change_user;

}
