package com.goodsogood.ows.model.db.experience;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 *
 * t_user_party_eval 实体类
 *
 * 考核结果-综合评价 
 *
 * <AUTHOR>
 * @create 2022-03-11 10:52
*/
@Data
@ApiModel
@Table(name = "t_user_party_eval")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserPartyEvalEntity {

	@Id
	@JsonProperty(value = "eval_id")
	@Column(name = "eval_id")
	private Long evalId;


	@JsonProperty(value = "user_id")
	@Column(name = "user_id")
	private Long userId;


	@JsonProperty(value = "name")
	@Column(name = "user_name")
	private String userName;


	@ApiModelProperty("职务")
	@Column(name = "position")
	private String position;

    @ApiModelProperty("头像")
    @Column(name = "avatar")
    private String avatar;


	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;


	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;


	@JsonProperty(value = "org_level")
	@Column(name = "org_level")
	private String orgLevel;


	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Long regionId;


	@ApiModelProperty("年月格式202201")
	@JsonProperty(value = "date_month")
	@Column(name = "date_month")
	private Integer dateMonth;


	@ApiModelProperty("体检时间")
	@JsonProperty(value = "experience_date")
	@Column(name = "eval_date")
	private String evalDate;

    @ApiModelProperty("生成批次号")
    @JsonProperty(value = "batch_number")
    @Column(name = "batch_number")
    private String batchNumber;

	@ApiModelProperty("测评方式：1:自动测评  2：手动测评")
	@Column(name = "type")
	private Byte type;


	@ApiModelProperty("综合评级-星星")
	@JsonProperty(value = "experience_start")
	@Column(name = "total_star")
	private Integer totalStar;


	@JsonProperty(value = "task_id")
	@Column(name = "task_id")
	private String taskId;


	@ApiModelProperty("完成状态 0：任务已发起   1：完成")
	@JsonProperty(value = "cal_status")
	@Column(name = "cal_status")
	private Byte calStatus;


	@ApiModelProperty("逗号分割，本次拥有的标签名")
	@JsonProperty(value = "tag_add")
	@Column(name = "tag_add")
	private String tagAdd;


	@ApiModelProperty("本次置灰的标签名--多个逗号分割")
	@JsonProperty(value = "tag_reduce")
	@Column(name = "tag_reduce")
	private String tagReduce;


	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private LocalDateTime createTime;


	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private LocalDateTime updateTime;


    @JsonProperty(value = "experience_result")
    @Transient
    private List<Integer> experienceResult;

	@JsonProperty(value = "rule_name")
	@Transient
	private String ruleName;


	@JsonProperty(value = "type_month_star")
	@Transient
	private List<TypeMonthStarForm> typeMonthStar;

	@Data
	@JsonInclude(JsonInclude.Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class TypeMonthStarForm{
		@JsonProperty("type_name")
		private String typeName;
		@JsonProperty("month_star")
		private List<MonthStarVO>  monthStar;
	}

	@Data
	@JsonInclude(JsonInclude.Include.NON_NULL)
	@JsonIgnoreProperties(ignoreUnknown = true)
	@AllArgsConstructor
	public static class MonthStarVO{
		private String month;
		private Integer star;
	}
}

