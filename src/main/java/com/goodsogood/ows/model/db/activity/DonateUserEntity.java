package com.goodsogood.ows.model.db.activity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 捐赠用户
 *
 * <AUTHOR>
 * @date 2019/1/8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Table(name = "t_donate_user")
@ApiModel
public class DonateUserEntity implements Serializable {

    @Id
    @Column(name = "donate_user_id")
    @JsonProperty("donate_user_id")
    @GeneratedValue(generator = "JDBC")
    private Long donateUserId;

    @Column(name = "activity_user_id")
    @JsonProperty("activity_user_id")
    private Long activityUserId;

    @Column(name = "activity_id")
    @JsonProperty("activity_id")
    @ApiModelProperty("活动id")
    private Long activityId;

    @Column(name = "user_id")
    @JsonProperty("user_id")
    @ApiModelProperty("用户id")
    private Long userId;

    @Column(name = "user_name")
    @JsonProperty("user_name")
    @ApiModelProperty("用户昵称")
    private String userName;

    @ApiModelProperty("用户头像")
    private String head;

    @ApiModelProperty("手机号码密文")
    private String phone;

    @Column(name = "phone_secret")
    @JsonProperty("phone_secret")
    @ApiModelProperty("手机号码脱敏")
    private String phoneSecret;

    @Column(name = "donate_num")
    @JsonProperty("donate_num")
    @ApiModelProperty("捐赠分数")
    private Long donateNum;

    @Transient
    @ApiModelProperty("排名")
    private Long rank;

    @Column(name = "org_id")
    @JsonProperty("org_id")
    @ApiModelProperty("捐赠者组织id")
    private Long orgId;

    @Column(name = "org_name")
    @JsonProperty("org_name")
    @ApiModelProperty("捐赠者组织")
    private String orgName;

    @Column(name = "last_org_id")
    @JsonProperty("last_org_id")
    @ApiModelProperty("上级组织id（市直机关下的第一层级组织）")
    private Long lastOrgId;

    @Column(name = "last_org_name")
    @JsonProperty("last_org_name")
    @ApiModelProperty("上级组织名称（市直机关下的第一层级组织）")
    private String lastOrgName;

    @Column(name = "donate_time")
    @JsonProperty("donate_time")
    @ApiModelProperty("捐赠时间")
    private Date donateTime;

    @Column(name = "is_clean")
    @JsonProperty("is_clean")
    @ApiModelProperty("数据是否被清洗过，0-未清洗，1-已清洗，2-有多个组织无法清洗，3-清洗失败")
    private Integer isClean;
}
