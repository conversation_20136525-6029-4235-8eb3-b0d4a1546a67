package com.goodsogood.ows.model.db.user

import com.fasterxml.jackson.annotation.JsonInclude
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.util.Date
import javax.persistence.Column
import javax.persistence.Id
import javax.persistence.Table

/**
 *
 * <AUTHOR>
 * @createTime 2023年02月28日 09:53:00
 */
@ApiModel
@Table(name = "t_user_login_statistics")
@JsonInclude(JsonInclude.Include.NON_NULL)
data class LoginStatisticsEntity(

    @Id
    @ApiModelProperty(value = "统计ID")
    @Column(name = "statistics_id")
    var statisticsId: Long? = null,

    @ApiModelProperty(value = "用户ID")
    @Column(name = "user_id")
    var userId: Long? = null,

    @ApiModelProperty(value = "登录天数")
    @Column(name = "login_day")
    var loginDay: Int? = null,

    @ApiModelProperty(value = "更新时间")
    @Column(name = "update_time")
    var updateTime: Date? = null
) {
    override fun toString(): String {
        return "LoginStatisticsEntity(statisticsId=$statisticsId, userId=$userId, loginDay=$loginDay, updateTime=$updateTime)"
    }
}