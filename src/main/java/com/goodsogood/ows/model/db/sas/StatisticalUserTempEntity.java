package com.goodsogood.ows.model.db.sas;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * t_statistical_user_temp 实体类
 * <p>
 * 双重组织生活用户临时表
 *
 * <AUTHOR>
 * @create 2019-07-26 17:42
 */
@Data
@ApiModel
@Table(name = "t_statistical_user_temp")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatisticalUserTempEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "statistical_user_temp_id")
    @Column(name = "statistical_user_temp_id")
    private Long statisticalUserTempId;


    @ApiModelProperty("用户ID")
    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    private Long userId;


    @ApiModelProperty("用户名字")
    @JsonProperty(value = "user_name")
    @Column(name = "user_name")
    private String userName;

    @ApiModelProperty("用户状态 1-正常 2-删除")
    @JsonProperty(value = "user_status")
    @Column(name = "user_status")
    private Integer userStatus;

    @ApiModelProperty("是否是党员。1 是 2否")
    @JsonProperty(value = "is_party_member")
    @Column(name = "is_party_member")
    private Integer isPartyMember;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("区县Id")
    @JsonProperty(value = "region_id")
    @Column(name = "region_id")
    private Long regionId;


    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;

    @ApiModelProperty("组织状态 1-正常 2-删除")
    @JsonProperty(value = "org_status")
    @Column(name = "org_status")
    private Integer orgStatus;

    @ApiModelProperty("组织类型-党组织102803")
    @JsonProperty(value = "org_type")
    @Column(name = "org_type")
    private Integer orgType;


    @ApiModelProperty("组织具体类型-党支部10280302")
    @JsonProperty(value = "org_type_child")
    @Column(name = "org_type_child")
    private Integer orgTypeChild;

    @ApiModelProperty("上级组织Id")
    @JsonProperty(value = "parent_id")
    @Column(name = "parent_id")
    private Long parentId;


    @ApiModelProperty("组织父级路径")
    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    private String orgLevel;


    @ApiModelProperty("是否离退休党组织 1-是 2-否")
    @JsonProperty(value = "is_retire")
    @Column(name = "is_retire")
    private Integer isRetire;


    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private Date createTime;

    @JsonProperty(value = "user_create_time")
    @Column(name = "user_create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date userCreateTime;

    @JsonProperty(value = "user_update_time")
    @Column(name = "user_update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date userUpdateTime;

    @JsonProperty(value = "org_create_time")
    @Column(name = "org_create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orgCreateTime;

    @JsonProperty(value = "org_update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "org_update_time")
    private Date orgUpdateTime;
}

