package com.goodsogood.ows.model.db.experience;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 *
 * t_user_party_eval_result 实体类
 *
 * 大项考核结果 
 *
 * <AUTHOR>
 * @create 2022-03-11 10:52
*/
@Data
@ApiModel
@Table(name = "t_user_party_eval_result")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserPartyEvalResultEntity {
	
	@Id
	@JsonProperty(value = "result_id")
	@Column(name = "result_id")
	private Long resultId;
	
	
	@JsonProperty(value = "rule_id")
	@Column(name = "rule_id")
	private Integer ruleId;
	
	
	@JsonProperty(value = "eval_id")
	@Column(name = "eval_id")
	private Long evalId;
	
	
	@ApiModelProperty("冗余查询")
	@JsonProperty(value = "task_id")
	@Column(name = "task_id")
	private String taskId;
	
	
	@JsonProperty(value = "rule_name")
	@Column(name = "rule_name")
	private String ruleName;
	
	
	@Column(name = "star")
	private Integer star;
	
	
	@ApiModelProperty("意见建议")
	@JsonProperty(value = "advice_id")
	@Column(name = "advice_id")
	private Integer adviceId;

	@ApiModelProperty("参数值")
	@JsonProperty(value = "param_value")
	@Column(name = "param_value")
	private String paramValue;


}

