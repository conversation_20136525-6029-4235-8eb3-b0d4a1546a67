package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * @program: ows-user-center
 * @description: 党小组实体类
 * @author: taiqian.Luo
 * @create: 2019-04-09 11:42
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgGroupVo {

    @Column(name = "org_group_id")
    private Long orgGroupId;

    //组织id
    @Column(name = "org_id")
    private Long orgId;

    /**
     * 3.0.0 新增 区域id
     */
    @Column(name = "region_id")
    private Long regionId;

    //党小组名称
    @Column(name = "org_group_name")
    private String orgGroupName;

    //删除标志（1-表示未删除，2-已删除）
    @Column(name = "is_delete")
    private Integer isDelete;

    //创建时间（创建时候前端选择的时间） yyyy-MM-dd
    @Column(name = "create_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;

    // 关联组织ID
    @Column(name = "link_org_id")
    private Long linkOrgId;



}
