package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 用户实体
 *
 * <AUTHOR>
 * @date 2018-03-21
 */
@Data
@ApiModel
@Table(name = "t_user")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserEntity {

    @ApiModelProperty(value = "id")
    @Min(value = 1, message = "{Min.user.id}")
    @NotNull(message = "{NotBlank.user.id}")
    @Id
    @Column(name = "user_id")
    @JsonProperty("user_id")
    @GeneratedValue(generator = "JDBC")
    private Long userId;

    @ApiModelProperty(value = "用户组信息")
    @Column(name = "group_info")
    @JsonProperty("group_info")
    private String groupInfo;

    @ApiModelProperty(value = "用户名")
    @NotNull(message = "{NotBlank.user.name}")
    private String name;

    @ApiModelProperty(value = "密码")
    private String password;

    @NotNull(message = "{NotBlank.user.phone}")
    @ApiModelProperty(value = "用户手机号")
    private String phone;

    @ApiModelProperty(value = "脱敏用户手机号")
    @Column(name = "phone_secret")
    @JsonProperty("phone_secret")
    private String phoneSecret;

    @ApiModelProperty(value = "用户状态")
    private Integer status;

    @ApiModelProperty(value = "证件类型")
    @NotNull(message = "{NotBlank.user.certType}")
    @Column(name = "cert_type")
    @JsonProperty("cert_type")
    private Integer certType;


    @ApiModelProperty(value = "证件号码")
    @NotNull(message = "{NotBlank.user.certNumber}")
    @Column(name = "cert_number")
    @JsonProperty("cert_number")
    private String certNumber;

    @ApiModelProperty(value = "脱敏证件号码")
    @Column(name = "cert_number_secret")
    @JsonProperty("cert_number_secret")
    private String certNumberSecret;

    @ApiModelProperty(value = "职务")
    @Column(name = "position")
    private String position;

    @ApiModelProperty(value = "Email")
    private String email;

    @ApiModelProperty(value = "入职日期")
    @Column(name = "entry_date")
    @JsonProperty("entry_date")
    private String entryDate;

    @ApiModelProperty(value = "性别")
    private Integer gender;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "户籍类型")
    @Column(name = "census_type")
    @JsonProperty("census_type")
    private Integer censusType;

    @ApiModelProperty(value = "国籍")
    private Integer nationality;

    @ApiModelProperty(value = "籍贯省/直辖市")
    @Column(name = "native_province")
    @JsonProperty("native_province")
    private Integer nativeProvince;

    @ApiModelProperty(value = "籍贯市/区")
    @Column(name = "native_city")
    @JsonProperty("native_city")
    private Integer nativeCity;

    @ApiModelProperty(value = "婚姻")
    private Integer marriage;

    @ApiModelProperty(value = "学历")
    private Integer education;

    @ApiModelProperty(value = "政治面貌")
    @Column(name = "political_type")
    @JsonProperty("political_type")
    private Integer politicalType;

    @ApiModelProperty(value = "是否实名认证")
    @Column(name = "is_verify")
    @JsonProperty("is_verify")
    private Integer isVerify;

    @ApiModelProperty(value = "民族")
    private Integer ethnic;

    @ApiModelProperty(value = "最后修改人")
    @Column(name = "last_change_user")
    @JsonProperty("last_change_user")
    private Long lastChangeUser;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "create_time")
    @JsonProperty("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @Column(name = "update_time")
    @JsonProperty("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "用户序列号")
    private String id;

    @ApiModelProperty(value = "出生日期")
    private String birthday;

    @ApiModelProperty("籍贯")
    @JsonProperty("birthplace")
    @Column(name = "birthplace")
    private String birthPlace;

    @ApiModelProperty("入党日期")
    @JsonProperty("joining_time")
    @Column(name = "joining_time")
    private String joiningTime;

    @ApiModelProperty("是否退休 1-是 2-否")
    @JsonProperty("is_retire")
    @Column(name = "is_retire")
    private Integer isRetire;

    @ApiModelProperty("是否允许更新 1-是 2-否")
    @JsonProperty("is_edit")
    @Column(name = "is_edit")
    private Integer isEdit;

    @ApiModelProperty("是否失联 1-是 2-否")
    @JsonProperty("is_lose")
    @Column(name = "is_lose")
    private Integer isLose;

    @ApiModelProperty("是否是游客 1-是 2-否")
    @JsonProperty("is_visitor")
    @Column(name = "is_visitor")
    private Integer isVisitor;

    @ApiModelProperty("是否流动党员 1-是 2-否")
    @JsonProperty("is_flow")
    @Column(name = "is_flow")
    private Integer isFlow;


    @ApiModelProperty("所在地区层级关系")
    @JsonProperty("native_level")
    @Column(name = "native_level")
    private String nativeLevel;

    @ApiModelProperty("联系方式-密文")
    @JsonProperty("contact_number")
    @Column(name = "contact_number")
    private String contactNumber;

    @ApiModelProperty("联系方式-脱敏")
    @JsonProperty("contact_number_secret")
    @Column(name = "contact_number_secret")
    private String contactNumberSecret;

    @ApiModelProperty("详细地址")
    @JsonProperty("address")
    @Column(name = "address")
    private String address;

    @ApiModelProperty("工作单位")
    @JsonProperty("work_unit")
    @Column(name = "work_unit")
    private String workUnit;

    @ApiModelProperty("离开类型1045")
    @JsonProperty("leave_type")
    @Column(name = "leave_type")
    private Integer leaveType;

    @ApiModelProperty("新增党员方式")
    @JsonProperty("join_type")
    @Column(name = "join_type")
    private Integer joinType;

    @ApiModelProperty("工号")
    @JsonProperty("job_number")
    @Column(name = "job_number")
    private String jobNumber;

    @ApiModelProperty("数据记录HMAC值")
    @JsonProperty("hmac")
    @Column(name = "hmac")
    private String hmac;

    @ApiModelProperty("头像地址")
    @JsonProperty("head_url")
    @Column(name = "head_url")
    private String headUrl;

    @ApiModelProperty("手写签名")
    @JsonProperty("hand_sign")
    @Column(name = "hand_sign")
    private String handSign;

    @ApiModelProperty("用户自定义标签")
    @JsonProperty("tags")
    @Column(name = "tags")
    private String tags;

    @ApiModelProperty("职位")
    @Column(name = "title")
    private String title;

    @ApiModelProperty("所属序列")
    @Column(name = "sequence")
    private Integer sequence;

    @ApiModelProperty("组织ID")
    @Transient
    private Long orgId;

    @ApiModelProperty("组织名称")
    @Transient
    private String orgName;

    @ApiModelProperty("组织简称")
    @Transient
    private String shortName;

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getGroupInfo() {
        return groupInfo;
    }

    public void setGroupInfo(String groupInfo) {
        this.groupInfo = groupInfo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhoneSecret() {
        return phoneSecret;
    }

    public void setPhoneSecret(String phoneSecret) {
        this.phoneSecret = phoneSecret;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCertType() {
        return certType;
    }

    public void setCertType(Integer certType) {
        this.certType = certType;
    }

    public String getCertNumber() {
        return certNumber;
    }

    public void setCertNumber(String certNumber) {
        this.certNumber = certNumber;
    }

    public String getCertNumberSecret() {
        return certNumberSecret;
    }

    public void setCertNumberSecret(String certNumberSecret) {
        this.certNumberSecret = certNumberSecret;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(String entryDate) {
        this.entryDate = entryDate;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getCensusType() {
        return censusType;
    }

    public void setCensusType(Integer censusType) {
        this.censusType = censusType;
    }

    public Integer getNationality() {
        return nationality;
    }

    public void setNationality(Integer nationality) {
        this.nationality = nationality;
    }

    public Integer getNativeProvince() {
        return nativeProvince;
    }

    public void setNativeProvince(Integer nativeProvince) {
        this.nativeProvince = nativeProvince;
    }

    public Integer getNativeCity() {
        return nativeCity;
    }

    public void setNativeCity(Integer nativeCity) {
        this.nativeCity = nativeCity;
    }

    public Integer getMarriage() {
        return marriage;
    }

    public void setMarriage(Integer marriage) {
        this.marriage = marriage;
    }

    public Integer getEducation() {
        return education;
    }

    public void setEducation(Integer education) {
        this.education = education;
    }

    public Integer getPoliticalType() {
        return politicalType;
    }

    public void setPoliticalType(Integer politicalType) {
        this.politicalType = politicalType;
    }

    public Integer getIsVerify() {
        return isVerify;
    }

    public void setIsVerify(Integer isVerify) {
        this.isVerify = isVerify;
    }

    public Integer getEthnic() {
        return ethnic;
    }

    public void setEthnic(Integer ethnic) {
        this.ethnic = ethnic;
    }

    public Long getLastChangeUser() {
        return lastChangeUser;
    }

    public void setLastChangeUser(Long lastChangeUser) {
        this.lastChangeUser = lastChangeUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getBirthPlace() {
        return birthPlace;
    }

    public void setBirthPlace(String birthPlace) {
        this.birthPlace = birthPlace;
    }

    public String getJoiningTime() {
        return joiningTime;
    }

    public void setJoiningTime(String joiningTime) {
        this.joiningTime = joiningTime;
    }

    public Integer getIsRetire() {
        return isRetire;
    }

    public void setIsRetire(Integer isRetire) {
        this.isRetire = isRetire;
    }

    public Integer getIsEdit() {
        return isEdit;
    }

    public void setIsEdit(Integer isEdit) {
        this.isEdit = isEdit;
    }

    public Integer getIsLose() {
        return isLose;
    }

    public void setIsLose(Integer isLose) {
        this.isLose = isLose;
    }

    public Integer getIsVisitor() {
        return isVisitor;
    }

    public void setIsVisitor(Integer isVisitor) {
        this.isVisitor = isVisitor;
    }

    public Integer getIsFlow() {
        return isFlow;
    }

    public void setIsFlow(Integer isFlow) {
        this.isFlow = isFlow;
    }

    public String getNativeLevel() {
        return nativeLevel;
    }

    public void setNativeLevel(String nativeLevel) {
        this.nativeLevel = nativeLevel;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getContactNumberSecret() {
        return contactNumberSecret;
    }

    public void setContactNumberSecret(String contactNumberSecret) {
        this.contactNumberSecret = contactNumberSecret;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getWorkUnit() {
        return workUnit;
    }

    public void setWorkUnit(String workUnit) {
        this.workUnit = workUnit;
    }

    public Integer getLeaveType() {
        return leaveType;
    }

    public void setLeaveType(Integer leaveType) {
        this.leaveType = leaveType;
    }

    public Integer getJoinType() {
        return joinType;
    }

    public void setJoinType(Integer joinType) {
        this.joinType = joinType;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public String getHmac() {
        return hmac;
    }

    public void setHmac(String hmac) {
        this.hmac = hmac;
    }

    public String getHeadUrl() {
        return headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl;
    }

    public String getHandSign() {
        return handSign;
    }

    public void setHandSign(String handSign) {
        this.handSign = handSign;
    }
}