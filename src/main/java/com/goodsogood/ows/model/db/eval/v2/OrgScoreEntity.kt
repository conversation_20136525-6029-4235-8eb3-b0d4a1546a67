package com.goodsogood.ows.model.db.eval.v2

import org.springframework.data.util.ProxyUtils
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDateTime
import javax.persistence.*
import javax.validation.constraints.NotNull
import javax.validation.constraints.Size

@Entity
@Table(name = "t_eval_v2_org_score")
data class OrgScoreEntity @JvmOverloads constructor(
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "v2_org_score_id", nullable = false)
    var id: Long? = null,

    @Size(max = 32)
    @Column(name = "task_id", length = 32)
    var taskId: String? = null,

    @NotNull
    @Column(name = "year", nullable = false)
    var year: Int? = null,

    @NotNull
    @Column(name = "metric_class_id", nullable = false)
    var metricClassId: Long? = null,

    @NotNull
    @Column(name = "metric_id", nullable = false)
    var metricId: Long? = null,

    @NotNull
    @Column(name = "org_id", nullable = false)
    var orgId: Long? = null,

    @Size(max = 200)
    @Column(name = "org_name", length = 200)
    var orgName: String? = null,

    @Size(max = 50)
    @NotNull
    @Column(name = "org_level", nullable = false, length = 50)
    var orgLevel: String? = null,

    @NotNull
    @Column(name = "unit_id", nullable = false)
    var unitId: Long? = null,

    @Size(max = 200)
    @Column(name = "unit_name", length = 200)
    var unitName: String? = null,

    @NotNull
    @Column(name = "score", nullable = false, precision = 24, scale = 6)
    var score: Double? = null,

    @Size(max = 900)
    @Column(name = "remark", length = 900)
    var remark: String? = null,

    @NotNull
    @Column(name = "create_time", nullable = false)
    var createTime: LocalDateTime? = null,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || ProxyUtils.getUserClass(this) != ProxyUtils.getUserClass(
                other
            )
        ) return false
        other as OrgScoreEntity

        return id != null && id == other.id
    }

    override fun hashCode(): Int = javaClass.hashCode()

    @Override
    override fun toString(): String {
        return this::class.simpleName + "(id = $id , taskId = $taskId , year = $year , metricClassId = $metricClassId , metricId = $metricId , orgId = $orgId , orgName = $orgName , orgLevel = $orgLevel , unitId = $unitId , unitName = $unitName , score = $score , remark = $remark , createTime = $createTime )"
    }
}