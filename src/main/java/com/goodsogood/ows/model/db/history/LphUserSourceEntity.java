package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 *
 * t_lph_user_source 实体类
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_user_source")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphUserSourceEntity  extends  BaseEntity{
	
//	@Id
	@Column(name = "day")
	private String day;
	
	
	@JsonProperty(value = "source_name")
	@Column(name = "source_name")
	private String sourceName;
	
	
	@JsonProperty(value = "user_num")
	@Column(name = "user_num")
	private Integer userNum;
	
	
	@JsonProperty(value = "active_num")
	@Column(name = "active_num")
	private Integer activeNum;
	
	
	@JsonProperty(value = "open_num")
	@Column(name = "open_num")
	private Integer openNum;
	
	
	@JsonProperty(value = "in_num")
	@Column(name = "in_num")
	private Integer inNum;
	
	
	@Column(name = "times")
	private String times;
	
}

