package com.goodsogood.ows.model.db.sas;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_statistical_user_org_life 实体类
 *
 * 用户双重组织生活 
 *
 * <AUTHOR>
 * @create 2019-07-23 17:05
*/
@Data
@ApiModel
@Table(name = "t_statistical_user_org_life")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatisticalUserOrgLifeEntity {
	
	@Id
	@GeneratedValue(generator = "JDBC")
	@JsonProperty(value = "user_org_life_id")
	@Column(name = "user_org_life_id")
	private Long userOrgLifeId;
	
	
	@ApiModelProperty("用户ID")
	@JsonProperty(value = "user_id")
	@Column(name = "user_id")
	private Long userId;

	@ApiModelProperty("活动类型id")
	@JsonProperty(value = "type_id")
	@Column(name = "type_id")
	private Long typeId;
	
	
	@ApiModelProperty("活动类型名称")
	@JsonProperty(value = "type_name")
	@Column(name = "type_name")
	private String typeName;
	
	
	@ApiModelProperty("参与次数")
	@JsonProperty(value = "participate_num")
	@Column(name = "participate_num")
	private Integer participateNum;

	@JsonProperty(value = "statistical_year")
	@Column(name = "statistical_year")
	private Integer statisticalYear;
	
	
	@ApiModelProperty("统计的月（1-12）")
	@JsonProperty(value = "statistical_month")
	@Column(name = "statistical_month")
	private Integer statisticalMonth;

	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;

	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;

	@ApiModelProperty("区县Id")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Long regionId;
}

