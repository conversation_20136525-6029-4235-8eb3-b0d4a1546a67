package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/6/23
 * Description: 志愿者主表
 */
@Data
@ApiModel
@Table(name = "t_volunteer_user")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VolunteerUserEntity {

    /**
     * 志愿者用户id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "volunteer_user_id")
    @JsonProperty("volunteer_user_id")
    private Long volunteerUserId;

    /**
     * 用户名
     */
    @Column(name = "name")
    @JsonProperty("name")
    private String name;

    /**
     * 昵称
     */
    @Column(name = "nick_name")
    @JsonProperty("nick_name")
    private String nickName;

    /**
     * 志愿者编号
     */
    @Column(name = "volunteer_number")
    @JsonProperty("volunteer_number")
    private String volunteerNumber;

    /**
     * 密码
     */
    @Column(name = "password")
    @JsonProperty("password")
    private String password;

    /**
     * 注册或新增记录的手机号码-密文
     */
    @Column(name = "register_phone")
    @JsonProperty("register_phone")
    private String registerPhone;

    /**
     * 注册人电话-脱敏文
     */
    @Column(name = "register_phone_secret")
    @JsonProperty("register_phone_secret")
    private String registerPhoneSecret;

    /**
     * 联系手机号码(二次开发冗余)
     */
    @Column(name = "contact_phone")
    @JsonProperty("contact_phone")
    private String contactPhone;

    /**
     * 1:正常 2:未激活(手机号码激活)
     */
    @Column(name = "status")
    @JsonProperty("status")
    private Integer status;

    /**
     * 国家编号 option 1012
     */
    @Column(name = "country")
    @JsonProperty("country")
    private Integer country;

    /**
     * 政治面貌 option 1033
     */
    @Column(name = "political_type")
    @JsonProperty("political_type")
    private Integer politicalType;

    /**
     * 民族 option 1004
     */
    @Column(name = "ethnic")
    @JsonProperty("ethnic")
    private Integer ethnic;

    /**
     * 居住省 code
     */
    @Column(name = "native_province")
    @JsonProperty("native_province")
    private Integer nativeProvince;

    /**
     * 居住区域-市
     */
    @Column(name = "native_city")
    @JsonProperty("native_city")
    private Integer nativeCity;

    /**
     * 居住区域-区
     */
    @Column(name = "native_district")
    @JsonProperty("native_district")
    private Integer nativeDistrict;

    /**
     * 学历 option 1039
     */
    @Column(name = "education")
    @JsonProperty("education")
    private Integer education;

    /**
     * 从业状况 option 1040
     */
    @Column(name = "employment")
    @JsonProperty("employment")
    private Integer employment;

    /**
     * 单位名称
     */
    @Column(name = "org_name")
    @JsonProperty("org_name")
    private String orgName;

    /**
     * 服务区域-省
     */
    @Column(name = "service_province")
    @JsonProperty("service_province")
    private Integer serviceProvince;

    /**
     * 服务区域-市
     */
    @Column(name = "service_city")
    @JsonProperty("service_city")
    private Integer serviceCity;

    /**
     * 服务区域-区
     */
    @Column(name = "service_district")
    @JsonProperty("service_district")
    private Integer serviceDistrict;

    /**
     * 个人特长
     */
    @Column(name = "skill")
    @JsonProperty("skill")
    private String skill;

    /**
     * 服务经历
     */
    @Column(name = "service_experience")
    @JsonProperty("service_experience")
    private String serviceExperience;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @JsonProperty("user_id")
    private Long userId;

    /**
     * 创建人 -999 自己注册
     */
    @Column(name = "create_user")
    @JsonProperty("create_user")
    private Long createUser;

    /**
     * 最后修改人
     */
    @Column(name = "last_change_user")
    @JsonProperty("last_change_user")
    private Long lastChangeUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @JsonProperty("update_time")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonProperty("create_time")
    private Date createTime;

    /**
     * 需要补全信息 0否 1是
     */
    @Column(name = "need_complete")
    @JsonProperty("need_complete")
    private Integer needComplete;

}
