package com.goodsogood.ows.model.db.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.List;

/**
 * Create by FuXiao on 2020/10/19
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_rank_column")
public class ColumnEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "column_id")
    @Column(name = "column_id")
    @ApiModelProperty(name = "项id")
    private Long columnId;

    @JsonProperty(value = "top_id")
    @Column(name = "top_id")
    @ApiModelProperty(name = "外键")
    private Long topId;

    @JsonProperty(value = "column_name")
    @Column(name = "column_name")
    @ApiModelProperty(name = "项名称")
    private String columnName;

    @JsonProperty(value = "upper_limit")
    @Column(name = "upper_limit")
    @ApiModelProperty(name = "分值上限")
    private Long upperLimit;

    @JsonProperty(value = "single_choice")
    @Column(name = "single_choice")
    @ApiModelProperty(name = "是否单项选择 1是0否")
    private Long singleChoice;

    @JsonProperty(value = "parent_id")
    @Column(name = "parent_id")
    @ApiModelProperty(name = "父级项id")
    private Long parentId;

    @JsonProperty(value = "level")
    @Column(name = "level")
    @ApiModelProperty(name = "层级 只有1、2、3层")
    private Integer level;

    @Transient
    @JsonProperty(value = "children")
    @ApiModelProperty(name = "子类型")
    private List<ColumnEntity> children;

    @Transient
    @JsonProperty(value = "childRule")
    @ApiModelProperty(name = "子规则")
    private List<ScoreRuleEntity> childRule;
}
