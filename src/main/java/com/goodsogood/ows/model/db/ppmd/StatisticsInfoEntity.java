package com.goodsogood.ows.model.db.ppmd;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *   统计信息表实体类
 *  tc 2018.12.05
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_ppmd_statistics_info")
public class StatisticsInfoEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "statistics_info_id")
    @Column(name = "statistics_info_id")
    @ApiModelProperty(name = "主键编号")
    private Long statisticsInfoId;

    @JsonProperty(value = "parent_org_id")
    @Column(name = "parent_org_id")
    @ApiModelProperty(name = "党支部所属党委编号")
    private Long parentOrgId;

    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    @ApiModelProperty(name = "党委或者支部编号")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    @ApiModelProperty(name = "党委或者支部名称")
    private String orgName;

    /**
     * 是否考核单位 0 否 1是
     */
    @JsonProperty(value = "examine")
    @Column(name = "examine")
    @ApiModelProperty(name = "是否考核单位")
    private Integer examine;

    @JsonProperty(value = "stats_date")
    @Column(name = "stats_date")
    @ApiModelProperty(name = "统计月份")
    private String statsDate;

    @JsonProperty(value = "num_total")
    @Column(name = "num_total")
    @ApiModelProperty(name = "党员人数")
    private Integer numTotal;

    @JsonProperty(value = "num_should")
    @Column(name = "num_should")
    @ApiModelProperty(name = "应交人数")
    private Integer numShould;

    /**
     * 应交金额 单位分
     */
    @JsonProperty(value = "pay_should")
    @Column(name = "pay_should")
    @ApiModelProperty(name = "应交金额")
    private Long payShould;

    @JsonProperty(value = "num_already")
    @Column(name = "num_already")
    @ApiModelProperty(name = "已交人数")
    private Integer numAlready;

    /**
     * 已交金额 单位分
     */
    @JsonProperty(value = "pay_already")
    @Column(name = "pay_already")
    @ApiModelProperty(name = "已交金额")
    private Long payAlready;

    @JsonProperty(value = "num_owing")
    @Column(name = "num_owing")
    @ApiModelProperty(name = "欠交人数")
    private Integer numOwing;

    /**
     * 欠交金额 单位分
     */
    @JsonProperty(value = "pay_owing")
    @Column(name = "pay_owing")
    @ApiModelProperty(name = "欠交金额")
    private Long payOwing;

    /**
     * 组织类型 1 党委 2 支部
     */
    @JsonProperty(value = "org_type")
    @Column(name = "org_type")
    @ApiModelProperty(name = "组织类型")
    private Integer orgType;

    /**
     * 统计维度类型  1 党委维度  2 支部维度
     */
    @JsonProperty(value = "statistics_type")
    @Column(name = "statistics_type")
    @ApiModelProperty(name = "统计维度类型")
    private Integer statisticsType;

    @JsonProperty(value = "redis_key")
    @Column(name = "redis_key")
    @ApiModelProperty(name = "统计的redisKey")
    private String redisKey;

    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    @ApiModelProperty(name = "创建时间")
    private Date createTime;

}
