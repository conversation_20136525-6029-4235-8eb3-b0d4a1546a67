package com.goodsogood.ows.model.db.volunteer;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 评分信息
 * <AUTHOR>
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppraiseInfo {

    @ApiModelProperty("志愿用户id")
    private Long volunteerUserId;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("守时程度")
    private Double punctuality;

    @ApiModelProperty("服务态度")
    private Double attitude;

    @ApiModelProperty("专业水平")
    private Double professional;

    @ApiModelProperty("志愿积分")
    private Integer score;

    @ApiModelProperty("服务时长")
    private Integer time;

    @ApiModelProperty("服务次数")
    private Integer serviceNumber;
}
