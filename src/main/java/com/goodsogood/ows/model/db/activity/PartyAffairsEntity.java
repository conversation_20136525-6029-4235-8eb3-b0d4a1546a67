package com.goodsogood.ows.model.db.activity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_party_affairs 实体类
 *
 * 党务公开主表 
 *
 * <AUTHOR>
 * @create 2021-11-19 16:47
*/
@Data
@ApiModel
@Table(name = "t_party_affairs")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PartyAffairsEntity {
	
	@Id
	@JsonProperty(value = "party_affairs_id")
	@Column(name = "party_affairs_id")
	private Long partyAffairsId;
	
	
	@ApiModelProperty("组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;
	
	
	@ApiModelProperty("组织层级关系")
	@JsonProperty(value = "org_level")
	@Column(name = "org_level")
	private String orgLevel;
	
	
	@ApiModelProperty("1.公示公告 2.单位动态 3.计划总结 4.领导分工 5、党费交纳 6.其它")
	@Column(name = "type")
	private Integer type;
	
	
	@ApiModelProperty("1.本单位 2、全市")
	@Column(name = "scope")
	private Integer scope;
	
	
	@ApiModelProperty("标题")
	@Column(name = "title")
	private String title;
	
	
	@Column(name = "content")
	private String content;
	
	
	@ApiModelProperty("图片链接 多个以逗号分开")
	@JsonProperty(value = "img_url")
	@Column(name = "img_url")
	private String imgUrl;
	
	
	@ApiModelProperty("1.通过 2.待审核 3.不通过 4.草稿 5.已删除")
	@Column(name = "status")
	private Integer status;
	
	
	@ApiModelProperty("公示开始时间")
	@JsonProperty(value = "start_time")
	@Column(name = "start_time")
	private Date startTime;
	
	
	@ApiModelProperty("公示结束时间")
	@JsonProperty(value = "end_time")
	@Column(name = "end_time")
	private Date endTime;
	
	
	@ApiModelProperty("审核内容")
	@JsonProperty(value = "audit_content")
	@Column(name = "audit_content")
	private String auditContent;
	
	
	@ApiModelProperty("审核时间")
	@JsonProperty(value = "audit_time")
	@Column(name = "audit_time")
	private Date auditTime;
	
	
	@ApiModelProperty("审核用户id")
	@JsonProperty(value = "audit_user_id")
	@Column(name = "audit_user_id")
	private Long auditUserId;


    @ApiModelProperty("待审核用户ids,多个逗号分隔")
    @JsonProperty(value = "wait_audit_user_ids")
    @Column(name = "wait_audit_user_ids")
    private String waitAuditUserIds;
	
	
	@JsonProperty(value = "submit_date")
    @JsonFormat(pattern="yyyy-MM-dd")
    @Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
}

