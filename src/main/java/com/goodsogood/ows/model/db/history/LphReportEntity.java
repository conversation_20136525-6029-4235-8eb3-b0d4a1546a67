package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_report 实体类
 *
 * 报表 
 *
 * <AUTHOR>
 * @create 2021-05-27 14:12
*/
@Data
@ApiModel
@Table(name = "t_lph_report")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphReportEntity {
	
	@Id
	@ApiModelProperty("主键")
	@JsonProperty(value = "report_id")
	@Column(name = "report_id")
	private Long reportId;
	
	
	@ApiModelProperty("小程序openid")
	@JsonProperty(value = "open_id")
	@Column(name = "open_id")
	private String openId;
	
	
	@ApiModelProperty("公众号open_id")
	@JsonProperty(value = "third_open_id")
	@Column(name = "third_open_id")
	private String thirdOpenId;
	
	
	@ApiModelProperty("总积分")
	@JsonProperty(value = "score_num")
	@Column(name = "score_num")
	private Integer scoreNum;
	
	
	@ApiModelProperty("通关数")
	@JsonProperty(value = "pass_total")
	@Column(name = "pass_total")
	private Integer passTotal;
	
	
	@ApiModelProperty("抽奖得分")
	@JsonProperty(value = "prize_score_num")
	@Column(name = "prize_score_num")
	private Integer prizeScoreNum;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
}

