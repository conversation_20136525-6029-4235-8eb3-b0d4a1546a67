package com.goodsogood.ows.model.db.meeting;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 *
 * t_meeting_task 实体类
 *
 * <AUTHOR>
 * @create 2018-10-24 09:42
*/
@Data
@ApiModel
@Table(name = "t_meeting_task")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingTaskEntity {
	
	@Id
	@GeneratedValue(generator = "JDBC")
	@ApiModelProperty("id")
	@JsonProperty(value = "meeting_task_id")
	@Column(name = "meeting_task_id")
	private Long meetingTaskId;


    @ApiModelProperty("组织生活id")
	@JsonProperty(value = "meeting_plan_id")
	@Column(name = "meeting_plan_id")
	private Long meetingPlanId;


    @ApiModelProperty("活动举办要求id")
	@JsonProperty(value = "meeting_require_id")
	@Column(name = "meeting_require_id")
	private Long meetingRequireId;
	
	
	@ApiModelProperty("类型id")
	@JsonProperty(value = "type_id")
	@Column(name = "type_id")
	private Long typeId;
	
	
	@ApiModelProperty("所属类别id。冗余")
	@JsonProperty(value = "category_id")
	@Column(name = "category_id")
	private Long categoryId;
	
	
	@ApiModelProperty("执行组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;


    @ApiModelProperty("活动所属组织生活的派发组织id")
	@JsonProperty(value = "p_org_id")
	@Column(name = "p_org_id")
	private Long pOrgId;
	
	
	@ApiModelProperty("类型。冗余")
	@Column(name = "type")
	private String type;
	
	
	@ApiModelProperty("类别。冗余")
	@Column(name = "category")
	private String category;


    @ApiModelProperty("所属组织生活名称。冗余")
	@Column(name = "name")
	private String name;


    @ApiModelProperty("举办次数。")
	@JsonProperty(value = "meeting_num")
	@Column(name = "meeting_num")
	private Integer meetingNum;

    @ApiModelProperty("要求举办次数。")
	@JsonProperty(value = "meeting_req_num")
	@Transient
	private Integer meetingReqNum;
	
	@ApiModelProperty("状态（：1未完成 2：已完成）")
	@Column(name = "status")
	private Short status;

	@ApiModelProperty("状态（：1未完成 2：已完成 3:已逾期）")
	@Transient
	private Short statusForm;

	@ApiModelProperty("开始时间")
	@JsonProperty(value = "start_time")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@Column(name = "start_time")
	private Date startTime;


	@ApiModelProperty("结束时间")
	@JsonProperty(value = "end_time")
	@Column(name = "end_time")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date endTime;
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("创建人id")
	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;
	
	
	@ApiModelProperty("更新时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
	
	@ApiModelProperty("最后更新人id")
	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;


	@ApiModelProperty("未执行扣分。")
	@Transient
	private Float deduct;


	@ApiModelProperty("是否需要签到。0：不需要；1：需要")
	@JsonProperty(value = "is_sign_in")
	@Transient
	private Short isSignIn;


	@ApiModelProperty("是否需要填写决议 0：不需要；1：需要")
	@JsonProperty(value = "is_w_resolution")
	@Transient
	private Short isWResolution;

	@ApiModelProperty("状态对应的说明")
	@JsonProperty(value = "status_remark")
	@Transient
	private String statusRemark;


	/**
	 * @version 2019年9月19日 11:12:19
	 */
	@ApiModelProperty("人员选择规则类型：-1 未指定 1.必须选择党小组 2.必须选择支委会届次 3.必须选择组织所有成员")
	@JsonProperty(value = "code")
	@Transient
	private Integer code;


	@ApiModelProperty("是否需要填写讲课人 0 否(默认)；1 是")
	@JsonProperty(value = "has_lecturer")
	@Transient
	private Integer hasLecturer;

	@ApiModelProperty("是否需要填写讲课标题 0 否（默认）；1 是")
	@JsonProperty(value = "has_lecture_title")
	@Transient
	private Integer hasLectureTitle;
}

