package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_user_information 实体类
 *
 * 个人信息表 
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_user_information")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphUserInformationEntity  extends  BaseEntity{
	
//	@Id
	@JsonProperty(value = "user_information_id")
	@Column(name = "user_information_id")
	private Long userInformationId;
	
	
	@ApiModelProperty("区县id")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Integer regionId;
	
	
	@JsonProperty(value = "open_id")
	@Column(name = "open_id")
	private String openId;
	
	
	@ApiModelProperty("当前余额闯关币")
	@JsonProperty(value = "balance_num")
	@Column(name = "balance_num")
	private Integer balanceNum;
	
	
	@ApiModelProperty("获得的积分")
	@JsonProperty(value = "score_num")
	@Column(name = "score_num")
	private Long scoreNum;
	
	
	@ApiModelProperty("今日通关数")
	@JsonProperty(value = "today_pass")
	@Column(name = "today_pass")
	private Integer todayPass;
	
	
	@ApiModelProperty("今日通关时间")
	@JsonProperty(value = "today_pass_time")
	@Column(name = "today_pass_time")
	private Date todayPassTime;
	
	
	@ApiModelProperty("选择总线路的id")
	@JsonProperty(value = "circuit_id")
	@Column(name = "circuit_id")
	private Long circuitId;
	
	
	@ApiModelProperty("总获得抽奖次数")
	@JsonProperty(value = "total_lottery_num")
	@Column(name = "total_lottery_num")
	private Integer totalLotteryNum;
	
	
	@ApiModelProperty("当前已抽奖次数")
	@JsonProperty(value = "now_lottery_num")
	@Column(name = "now_lottery_num")
	private Integer nowLotteryNum;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("更新时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
}

