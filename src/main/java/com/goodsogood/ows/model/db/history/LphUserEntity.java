package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_user 实体类
 *
 * 用户 
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_user")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphUserEntity  extends  BaseEntity{
	
//	@Id
	@ApiModelProperty("主键")
	@JsonProperty(value = "user_id")
	@Column(name = "user_id")
	private Long userId;
	
	
	@ApiModelProperty("用户在当前小程序的openid")
	@JsonProperty(value = "open_id")
	@Column(name = "open_id")
	private String openId;
	
	
	@ApiModelProperty("头像地址")
	@JsonProperty(value = "avatar_url")
	@Column(name = "avatar_url")
	private String avatarUrl;
	
	
	@ApiModelProperty("昵称")
	@JsonProperty(value = "nick_name")
	@Column(name = "nick_name")
	private String nickName;
	
	
	@ApiModelProperty("区县id")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Integer regionId;
	
	
	@ApiModelProperty("组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;
	
	
	@ApiModelProperty("组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;
	
	
	@ApiModelProperty("第三方用户id（党建系统）")
	@JsonProperty(value = "third_user_id")
	@Column(name = "third_user_id")
	private Long thirdUserId;
	
	
	@ApiModelProperty("第三方用户openid（党建系统）")
	@JsonProperty(value = "third_open_id")
	@Column(name = "third_open_id")
	private String thirdOpenId;
	
	
	@ApiModelProperty("是否有用户关系 0-否，1-是")
	@JsonProperty(value = "is_relation")
	@Column(name = "is_relation")
	private Byte isRelation;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("当前用户是第几位参与")
	@JsonProperty(value = "join_rank")
	@Column(name = "join_rank")
	private Integer joinRank;
	
	
	@ApiModelProperty("下一次增加抽奖次数时需要达到的影响力")
	@JsonProperty(value = "target_influence")
	@Column(name = "target_influence")
	private Integer targetInfluence;
	
	
	@ApiModelProperty("更新时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
}

