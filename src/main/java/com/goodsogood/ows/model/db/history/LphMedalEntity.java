package com.goodsogood.ows.model.db.history;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_medal 实体类
 *
 * 用户奖章 
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_medal")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphMedalEntity  extends  BaseEntity {
	
//	@Id
	@JsonProperty(value = "medal_id")
	@Column(name = "medal_id")
	private Long medalId;
	
	
	@JsonProperty(value = "open_id")
	@Column(name = "open_id")
	private String openId;
	
	
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Integer regionId;
	
	
	@ApiModelProperty("线路id")
	@JsonProperty(value = "circuit_id")
	@Column(name = "circuit_id")
	private Long circuitId;
	
	
	@ApiModelProperty("总线路关数id")
	@JsonProperty(value = "circuit_detail_id")
	@Column(name = "circuit_detail_id")
	private Long circuitDetailId;
	
	
	@ApiModelProperty("图片地址")
	@JsonProperty(value = "medal_url")
	@Column(name = "medal_url")
	private String medalUrl;
	
	
	@ApiModelProperty("奖章名称")
	@JsonProperty(value = "medal_name")
	@Column(name = "medal_name")
	private String medalName;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
}

