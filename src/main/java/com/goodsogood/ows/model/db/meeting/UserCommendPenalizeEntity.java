package com.goodsogood.ows.model.db.meeting;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 党员奖惩实体类
 * @date 2019/12/27
 */
@Data
@ApiModel
@Table(name = "t_meeting_user_commend_penalize")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserCommendPenalizeEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "meeting_user_commend_penalize_id")
    @Column(name = "meeting_user_commend_penalize_id")
    private Long meetingUserCommendPenalizeId;

    @ApiModelProperty("党员用户ID")
    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    private Long userId;

    @ApiModelProperty("党员用户姓名")
    @JsonProperty(value = "user_name")
    @Column(name = "user_name")
    private String userName;

    @ApiModelProperty("党员用户身份证")
    @JsonProperty(value = "cert_number")
    @Column(name = "cert_number")
    private String certNumber;

    @ApiModelProperty("党员所属组织ID")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("党员所属组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;

    @ApiModelProperty("党员所属组织层级关系")
    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    private String orgLevel;

    @ApiModelProperty("生效日期")
    @JsonProperty(value = "effective_time")
    @Column(name = "effective_time")
    private String effectiveTime;

    @ApiModelProperty("奖惩类别")
    @JsonProperty(value = "category")
    @Column(name = "category")
    private Integer category;

    @ApiModelProperty("奖惩级别")
    @JsonProperty(value = "level")
    @Column(name = "level")
    private Integer level;

    // 当级别为其他时，奖惩内容不能为空
    @ApiModelProperty("奖惩内容")
    @JsonProperty(value = "content")
    private String content;

    @ApiModelProperty("奖惩名称")
    @JsonProperty(value = "name")
    @Column(name = "name")
    private Integer name;

    @ApiModelProperty("奖惩原因")
    @JsonProperty(value = "reason")
    @Column(name = "reason")
    private String reason;

    @ApiModelProperty("奖励类型(1-及时性表彰、2-定期集中性表彰)")
    @JsonProperty(value = "reward_type")
    @Column(name = "reward_type")
    private Integer rewardType;

    @ApiModelProperty("奖惩类型（1:奖励 2:惩罚）")
    @JsonProperty(value = "type")
    @Column(name = "type")
    private Integer type;

    @ApiModelProperty("受奖批准机关名称")
    @JsonProperty(value = "office_name")
    @Column(name = "office_name")
    private String officeName;

    @ApiModelProperty("受奖批准机关级别")
    @JsonProperty(value = "office_level")
    @Column(name = "office_level")
    private Integer officeLevel;

    @ApiModelProperty("颁奖单位")
    @JsonProperty(value = "award_unit")
    @Column(name = "award_unit")
    private String awardUnit;

    @ApiModelProperty("相关文件")
    @JsonProperty(value = "related_file")
    @Column(name = "related_file")
    private String relatedFile;

    @ApiModelProperty("依据说明")
    @JsonProperty(value = "basis_description")
    @Column(name = "basis_description")
    private String basisDescription;

    @ApiModelProperty("审核状态 1-待审核、2-审核通过、3-审核不通过")
    @JsonProperty(value = "approval_status")
    @Column(name = "approval_status")
    private Integer approvalStatus;

    @ApiModelProperty("状态")
    @JsonProperty(value = "status")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    private Long createUser;

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private Date createTime;

    @ApiModelProperty("最后修改人")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    private Long lastChangeUser;

    @ApiModelProperty("修改时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "region_id")
    @JsonProperty("region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;

    @Column(name = "workflow_task_id")
    @JsonProperty("workflow_task_id")
    @ApiModelProperty("审核流ID")
    private Long workflowTaskId;

    @Column(name = "source_type")
    @JsonProperty("source_type")
    @ApiModelProperty("来源类型：1-本系统，2-外部系统")
    private Integer sourceType;

    @Column(name = "source_id")
    @JsonProperty("source_id")
    @ApiModelProperty("来源Id")
    private String sourceId;


    public Long getMeetingUserCommendPenalizeId() {
        return meetingUserCommendPenalizeId;
    }

    public void setMeetingUserCommendPenalizeId(Long meetingUserCommendPenalizeId) {
        this.meetingUserCommendPenalizeId = meetingUserCommendPenalizeId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCertNumber() {
        return certNumber;
    }

    public void setCertNumber(String certNumber) {
        this.certNumber = certNumber;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getName() {
        return name;
    }

    public void setName(Integer name) {
        this.name = name;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getRewardType() {
        return rewardType;
    }

    public void setRewardType(Integer rewardType) {
        this.rewardType = rewardType;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public Integer getOfficeLevel() {
        return officeLevel;
    }

    public void setOfficeLevel(Integer officeLevel) {
        this.officeLevel = officeLevel;
    }

    public String getAwardUnit() {
        return awardUnit;
    }

    public void setAwardUnit(String awardUnit) {
        this.awardUnit = awardUnit;
    }

    public String getRelatedFile() {
        return relatedFile;
    }

    public void setRelatedFile(String relatedFile) {
        this.relatedFile = relatedFile;
    }

    public String getBasisDescription() {
        return basisDescription;
    }

    public void setBasisDescription(String basisDescription) {
        this.basisDescription = basisDescription;
    }

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getLastChangeUser() {
        return lastChangeUser;
    }

    public void setLastChangeUser(Long lastChangeUser) {
        this.lastChangeUser = lastChangeUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Long getWorkflowTaskId() {
        return workflowTaskId;
    }

    public void setWorkflowTaskId(Long workflowTaskId) {
        this.workflowTaskId = workflowTaskId;
    }
}
