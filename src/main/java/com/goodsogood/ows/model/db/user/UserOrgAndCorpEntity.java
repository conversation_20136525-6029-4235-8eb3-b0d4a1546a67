package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 用户组织单位entity
 *
 * <AUTHOR>
 * @date 2018-04-02
 */
@Data
@ApiModel
@Table(name = "t_user_org_corp")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserOrgAndCorpEntity {

     @ApiModelProperty(value = "主键ID")
     @Id
     @Column(name = "user_org_corp_id")
     @JsonProperty("user_org_corp_id")
     @GeneratedValue(generator = "JDBC")
     private Long userOrgCorpId;

     @ApiModelProperty(value = "用户ID")
     @Column(name = "user_id")
     private Long userId;

     @ApiModelProperty(value = "单位ID")
     @Column(name = "corporation_id")
     private Long corporationId;

     @ApiModelProperty(value = "组织ID")
     @Column(name = "organization_id")
     private Long organizationId;

     @ApiModelProperty(value = "积分")
     @Column(name = "score")
     private int score;

     @ApiModelProperty(value = "标签ID")
     @JsonProperty("tag_id")
     private String tagId;

     @ApiModelProperty(value = "工号")
     @Column(name = "job_number")
     private String jobNumber;

     @ApiModelProperty(value = "工作岗位")
     @Column(name = "position")
     private String position;

     @ApiModelProperty(value = "工作岗位code")
     @Column(name = "position_code")
     private String positionCode;

     @ApiModelProperty(value = "Email")
     private String email;

     @ApiModelProperty(value = "入职日期")
     @Column(name = "entry_date")
     private String entryDate;

//    @ApiModelProperty(value = "技术等级")
//    @Column(name = "technical_grade")
//    private Integer technicalGrade;
//
//    @ApiModelProperty(value = "会籍变化类型")
//    @Column(name = "org_change_type")
//    private Integer orgChangeType;
//
//    @ApiModelProperty(value = "会籍变化原因")
//    @Column(name = "org_change_reason")
//    private Integer orgChangeReason;
//
//    @ApiModelProperty(value = "会籍变化日期")
//    @Column(name = "org_change_date")
//    private Date orgChangeDate;

//    @ApiModelProperty(value = "就业状态")
//    @Column(name = "work_status")
//    private Integer workStatus;

     @ApiModelProperty("技术等级")
     @Column(name = "job_grade")
     private Integer jobGrade;

     @ApiModelProperty("党组织")
     @Column(name = "communist")
     private Integer communist;

     @ApiModelProperty("团组织")
     @Column(name = "youth_league")
     private Integer youthLeague;

     @ApiModelProperty("工会组织")
     @Column(name = "union_member")
     private Integer unionMember;

     @ApiModelProperty("妇女组织")
     @Column(name = "women_league")
     private Integer womenLeague;

     @ApiModelProperty(value = "最后修改人")
     @Column(name = "last_change_user")
     private Long lastChangeUser;

     @ApiModelProperty(value = "创建时间")
     @Column(name = "create_time")
     private Date createTime;

     @ApiModelProperty(value = "更新时间")
     @Column(name = "update_time")
     private Date updateTime;

     @ApiModelProperty("是否是当前组织员工 1-是 0-否 2:为新增领导班子时新增")
     @Column(name = "is_employee")
     private Integer isEmployee;

     @ApiModelProperty(value = "进入支部时间")
     @Column(name = "join_party_time")
     private Date joinPartyTime;

     @ApiModelProperty(value = "区县ID")
     @Column(name = "region_id")
     @JsonProperty("region_id")
     private Long regionId;
}
