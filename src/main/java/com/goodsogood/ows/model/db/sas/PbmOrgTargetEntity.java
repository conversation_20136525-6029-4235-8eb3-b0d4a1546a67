package com.goodsogood.ows.model.db.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@ApiModel
@Table(name = "t_pbm_org_target")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PbmOrgTargetEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "pbm_target_org_id")
    @Column(name = "pbm_target_org_id")
    private Long pbmTargetOrgId;


    @ApiModelProperty("单位id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("单位名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;


    @ApiModelProperty("业务绩效得分")
    @JsonProperty(value = "target_score")
    @Column(name = "target_score")
    private Double targetScore;


    @ApiModelProperty("业务绩效备注")
    @JsonProperty(value = "target_remark")
    @Column(name = "target_remark")
    private String targetRemark;


    @ApiModelProperty("干群满意度得分")
    @JsonProperty(value = "satisfied_score")
    @Column(name = "satisfied_score")
    private Double satisfiedScore;


    @ApiModelProperty("干群满意度备注")
    @JsonProperty(value = "satisfied_remark")
    @Column(name = "satisfied_remark")
    private String satisfiedRemark;


    @ApiModelProperty("党建年度考核得分")
    @JsonProperty(value = "examine_score")
    @Column(name = "examine_score")
    private Double examineScore;


    @ApiModelProperty("党建年度考核备注")
    @JsonProperty(value = "examine_remark")
    @Column(name = "examine_remark")
    private String examineRemark;

    @ApiModelProperty("时间")
    @JsonProperty(value = "time")
    @Column(name = "time")
    private String time;

    private Long lastChangeUser;

    private Date createTime;

    private Date updateTime;

}
