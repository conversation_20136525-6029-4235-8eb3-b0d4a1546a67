package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
* @Author: tc
* @Description 积分订单和退款数据父类
* @Date 16:16 2019/5/30
*/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
public class ScoreOrderSupperEntity {

     @Column(name = "app_id")
     @ApiModelProperty(name = "应用编号")
     protected Long appId;

     @Column(name = "trade_no")
     @ApiModelProperty(name = "积分系统交易流水号")
     protected String tradeNo;

     @Column(name = "out_trade_no")
     @ApiModelProperty(name = "第三方系统交易流水号")
     protected String outTradeNo;

     @Column(name = "sub_out_trade_no")
     @ApiModelProperty(name = "融合商城子订单号")
     protected String subOutTradeNo;

     /**
      * 订单数据时  子订单卖给用户的积分（不含邮费）
      * 退款数据时  退用户的积分(含邮费)
      */
     @Column(name = "score_total")
     @ApiModelProperty(name = "子订单积分(不含邮费)")
     protected Long scoreTotal;

     /**
      * 订单数据时  子订单金额 （含运费 ， 单位分， RMB）
      * 退款数据时  子订单退款金额（含运费 ， 单位分 ， RMB）
      */
     @Column(name = "amount_total")
     @ApiModelProperty(name = "子订单总金额(分)")
     protected Long amountTotal;

     /**
      * 退款数据时  退款运费
      */
     @Column(name = "postage")
     @ApiModelProperty(name = "子订单邮费(分)")
     protected Long postage;

     /**
      *  订单数据时  0 失败   1 成功  2 处理中
      *  退款数据时  只有1 已退款
      */
     @Column(name = "status")
     @ApiModelProperty(name = "状态")
     protected Integer status;

     @Column(name = "buyer_id")
     @ApiModelProperty(name = "购买人Id")
     protected Long buyerId;

     @Column(name = "buyer_name")
     @ApiModelProperty(name = "购买人名称")
     protected String buyerName;

     @Column(name = "buyer_phone")
     @ApiModelProperty(name = "购买人电话")
     protected String buyerPhone;

     @Column(name = "buyer_phone_secret")
     @ApiModelProperty(name = "购买人电话脱敏文本")
     protected String buyerPhoneSecret;

     @Column(name = "buyer_org")
     @ApiModelProperty(name = "购买人组织编号")
     protected String buyerOrg;

     @Column(name = "buyer_org_name")
     @ApiModelProperty(name = "购买者组织名称")
     protected String buyerOrgName;

     @Column(name = "buyer_dept")
     @ApiModelProperty(name = "购买人单位编号")
     protected String buyerDept;

     @Column(name = "buyer_dept_name")
     @ApiModelProperty(name = "购买人单位名称")
     protected String buyerDeptName;

     @Column(name = "order_user_name")
     @ApiModelProperty(name = "收货人姓名")
     protected String orderUserName;

     @Column(name = "order_user_phone")
     @ApiModelProperty(name = "收货人电话")
     protected String orderUserPhone;

     @Column(name = "order_user_phone_secret")
     @ApiModelProperty(name = "收货人手机号码脱敏文本")
     protected String orderUserPhoneSecret;

     @Column(name = "order_address")
     @ApiModelProperty(name = "收货人手机号码脱敏文本")
     protected String orderAddress;

     @Column(name = "deal_date")
     @ApiModelProperty(name = "交易时间")
     protected String dealDate;

     @Column(name = "supplier_id")
     @ApiModelProperty(name = "供应商编号")
     protected Long supplierId;

     @Column(name = "supplier_name")
     @ApiModelProperty(name = "供应商名称")
     protected String supplierName;

     @Column(name = "memo")
     @ApiModelProperty(name = "备注")
     protected String memo;

     @Column(name = "create_time")
     @ApiModelProperty(name = "创建时间")
     protected Date createTime;

     @Column(name = "update_time")
     @ApiModelProperty(name = "更新时间")
     protected Date updateTime;

}
