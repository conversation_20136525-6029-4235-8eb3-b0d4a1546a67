package com.goodsogood.ows.model.db.task;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * TaskThirdEntity
 *
 * <AUTHOR>
 * @Date 2021-09-17 11:09
 */
@Data
@Table(name = "t_task_third")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TaskThirdEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "task_third_id")
    @ApiModelProperty("主键")
    private Long taskThirdId;

    @Column(name = "task_id")
    @ApiModelProperty("任务id")
    private Long taskId;

    @Column(name = "source_id")
    @ApiModelProperty("第三方任务id")
    private String sourceId;

    @Column(name = "union_id")
    @ApiModelProperty("第三方任务创建人id")
    private String unionId;

    @Column(name = "callback")
    @ApiModelProperty("是否需要回调确认结束。（0：否，1：是）")
    private Integer callback;

    @Column(name = "remark")
    @ApiModelProperty("备用信息")
    private String remark;

    /**
     * 派发时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /**
     * 最后修改人
     */
    @Column(name = "last_change_user")
    @ApiModelProperty("最后修改人")
    private Long lastChangeUser;


}
