package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * t_branch_highlight 实体类
 *
 * <AUTHOR>
 * @create 2021-09-03 11:02
 */
@Data
@ApiModel
@Table(name = "t_branch_highlight")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BranchHighlightEntity {

    @ApiModelProperty("视频封面")
    @Column(name = "cover")
    private String cover;

    @ApiModelProperty("标题")
    @Column(name = "`title`")
    private String title;

    @Id
    @JsonProperty(value = "branch_highlight_id")
    @Column(name = "branch_highlight_id")
    private Long branchHighlightId;


    @ApiModelProperty("1.支部风采 2.支部发展史")
    @Column(name = "type")
    @JsonIgnore
    private Integer type;


    @JsonProperty(value = "region_id")
    @Column(name = "region_id")
    @JsonIgnore
    private Long regionId;


    @ApiModelProperty("组织Id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;


    @ApiModelProperty("简介")
    @Column(name = "`desc`")
    private String desc;


    @ApiModelProperty("图片或者视频链接地址，多个以逗号分隔")
    @Column(name = "url")
    private String url;


    @ApiModelProperty("支部发展史获取时间")
    @JsonProperty(value = "node_time")
    @Column(name = "node_time")
    private String nodeTime;


    @ApiModelProperty("添加记录用户id")
    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    @JsonIgnore
    private Long userId;


    @ApiModelProperty("状态 1.可用 2.不可用")
    @Column(name = "status")
    @JsonIgnore
    private Integer status;


    @JsonProperty(value = "create_time")
    @JsonIgnore
    @Column(name = "create_time")
    private Date createTime;


    @JsonProperty(value = "update_time")
    @JsonIgnore
    @Column(name = "update_time")
    private Date updateTime;



}