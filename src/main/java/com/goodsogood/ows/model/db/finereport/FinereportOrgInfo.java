package com.goodsogood.ows.model.db.finereport;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_finereport_org_info 实体类
 *
 * 党委下面成员的基本情况 
 *
 * <AUTHOR>
 * @create 2020-08-06 11:14
*/
@Data
@Table(name = "t_finereport_org_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FinereportOrgInfo {

    @Id
    @JsonProperty(value = "org_info_id")
    @Column(name = "org_info_id")
    private Integer orgInfoId=0;


    //组织id
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId=0L;

    @JsonProperty(value = "seq")
    @Column(name = "seq")
    private Integer seq=0;

    @JsonProperty(value = "sta_flag")
    @Column(name = "sta_flag")
    private Integer staFlag=0;


    //区域id
    @JsonProperty(value = "region_id")
    @Column(name = "region_id")
    private Integer regionId=0;


    //党委名称
    @JsonProperty(value = "party_name")
    @Column(name = "party_name")
    private String partyName="";


    //党委简称
    @JsonProperty(value = "party_short_name")
    @Column(name = "party_short_name")
    private String partyShortName="";


    //基层党组织总数
    @JsonProperty(value = "organization_total")
    @Column(name = "organization_total")
    private Integer organizationTotal=0;


    //党委数
    @JsonProperty(value = "period_num")
    @Column(name = "period_num")
    private Integer periodNum=0;


    //党总支数
    @JsonProperty(value = "party_branch_total")
    @Column(name = "party_branch_total")
    private Integer partyBranchTotal=0;


    //党支部数
    @JsonProperty(value = "party_branch_number")
    @Column(name = "party_branch_number")
    private Integer partyBranchNumber=0;


    //机关党组织数量
    @JsonProperty(value = "party_org_number")
    @Column(name = "party_org_number")
    private Integer partyOrgNumber=0;


    //事业单位党组织数量
    @JsonProperty(value = "career_org_number")
    @Column(name = "career_org_number")
    private Integer careerOrgNumber=0;


    //企业党组织数量
    @JsonProperty(value = "company_org_number")
    @Column(name = "company_org_number")
    private Integer companyOrgNumber=0;


    //社会党组织数量
    @JsonProperty(value = "society_org_number")
    @Column(name = "society_org_number")
    private Integer societyOrgNumber=0;


    //社会党组织数量
    @JsonProperty(value = "other_org_number")
    @Column(name = "other_org_number")
    private Integer otherOrgNumber=0;



    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private Date createTime;


    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    private Date updateTime;


    //("统计年份")
    @JsonProperty(value = "sta_year")
    @Column(name = "sta_year")
    private String staYear;

}

