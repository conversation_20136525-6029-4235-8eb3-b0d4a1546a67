package com.goodsogood.ows.model.db.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * Create by FuXiao on 2020/10/19
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_rank_rate_rule")
public class RateRuleEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "rate_rule_id")
    @Column(name = "rate_rule_id")
    @ApiModelProperty(name = "评级规则id")
    private Long rateRuleId;

    @JsonProperty(value = "rule_name")
    @Column(name = "rule_name")
    @ApiModelProperty(name = "评级规则名称")
    private String ruleName;

    @JsonProperty(value = "year")
    @Column(name = "year")
    @ApiModelProperty(name = "评级年度")
    private Long year;

    @JsonProperty(value = "deadline")
    @Column(name = "deadline")
    @ApiModelProperty(name = "填报截止时间")
    private Date deadline;

    @JsonProperty(value = "status")
    @Column(name = "status")
    @ApiModelProperty(name = "1正常 2已截止")
    private Integer status;

    @JsonProperty(value = "type")
    @Column(name = "type")
    @ApiModelProperty(name = "类型 1用户 2组织")
    private Integer type;
}
