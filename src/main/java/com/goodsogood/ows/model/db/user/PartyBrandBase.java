package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
/*
党建品牌实体
 */
public class PartyBrandBase {
    /**
     * 组织id
     */
    @JsonProperty(value = "org_id")
    private Long orgId;

    /**
     * 组织名称
     */
    @JsonProperty(value = "org_name")
    private String orgName;

    /**
     * logo
     */
    private PositionFile logo;

    /**
     * 品牌名称
     */
    @JsonProperty(value = "brand_name")
    private String brandName;

    /**
     * 释义
     */
    private String paraphrase;

    /**
     * 标签
     */
    private List<String> tags;

    /**
     * 视频列表
     */
    @JsonProperty(value = "video_list")
    private VideoAndPictureVO videoList;

    /**
     * 图片列表
     */
    @JsonProperty(value = "picture_list")
    private VideoAndPictureVO pictureList;
}
