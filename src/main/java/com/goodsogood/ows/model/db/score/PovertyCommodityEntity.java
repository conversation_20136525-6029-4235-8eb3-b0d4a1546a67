package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
* @Author: tc
* @Description 消费扶贫商品信息实体类
* @Date 15:14 2019/10/25
*/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@Table(name = "t_score_poverty_commodity")
public class PovertyCommodityEntity {

    @Id
    @Column(name = "poverty_commodity_id")
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty(name = "id")
    private Long id;

    /**
     *  渠道标志
     */
    @Column(name = "channel")
    @ApiModelProperty("渠道标志")
    private String channel;

    @Column(name = "order_id")
    @ApiModelProperty("所属订单编号")
    private String orderId;

    @Column(name = "recommend_type")
    @ApiModelProperty("专区编号")
    private String recommendType;

    @Column(name = "commodity_id")
    @ApiModelProperty("商品编号")
    private String commodityId;

    @Column(name = "commodity_name")
    @ApiModelProperty("商品名称")
    private String commodityName;

    /**
     * 商品一级分类
     */
    @Column(name = "classify")
    @ApiModelProperty("分类")
    private String classify;

    /**
     * 商品二级分类
     */
    @Column(name = "sub_class")
    @ApiModelProperty("子类")
    private String subClass;

    /**
     * 商品定价 单位:分
     */
    @Column(name = "price")
    @ApiModelProperty("商品定价")
    private Integer price;

    /**
     * 实际售价 单位:分
     */
    @Column(name = "actual_price")
    @ApiModelProperty("实际售价")
    private Integer actualPrice;

    /**
     * 运费  单位:分
     */
    @Column(name = "carriage")
    @ApiModelProperty("运费")
    private Integer carriage;

    /**
     * 销售数量
     */
    @Column(name = "count")
    @ApiModelProperty("销售数量")
    private Integer count;

    /**
     * 商品规格、单位   盒，件，箱，只等
     */
    @Column(name = "standard")
    @ApiModelProperty("商品规格")
    private String standard;

    /**
     * 所属区县
     */
    @Column(name = "area")
    @ApiModelProperty("所属区县")
    private String area;

    /**
     * 商户名称
     */
    @Column(name = "company")
    @ApiModelProperty("商户名称")
    private String company;

    /**
     * 商品类型 1：普通商品   2：一元捐商品
     */
    @Column(name = "type")
    @ApiModelProperty("商品类型")
    private Integer type;

    /**
     * 此商品捐赠金额  单位:分 默认为0
     */
    @Column(name = "donation_money")
    @ApiModelProperty("此商品捐赠金额")
    private Integer donationMoney = 0;


    public PovertyCommodityEntity() {
        super();
    }

    public PovertyCommodityEntity(String channel, String orderId, String recommendType, String commodityId, String commodityName, String classify, String subClass, Integer price, Integer actualPrice, Integer carriage, Integer count, String standard, String area, String company, Integer type, Integer donationMoney) {
        this.channel = channel;
        this.orderId = orderId;
        this.recommendType = recommendType;
        this.commodityId = commodityId;
        this.commodityName = commodityName;
        this.classify = classify;
        this.subClass = subClass;
        this.price = price;
        this.actualPrice = actualPrice;
        this.carriage = carriage;
        this.count = count;
        this.standard = standard;
        this.area = area;
        this.company = company;
        this.type = type;
        this.donationMoney = donationMoney;
    }
}
