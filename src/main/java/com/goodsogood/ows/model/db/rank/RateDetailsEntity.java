package com.goodsogood.ows.model.db.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Create by FuXiao on 2020/10/19
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_rank_rate_details")
public class RateDetailsEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "rate_details_id")
    @Column(name = "rate_details_id")
    @ApiModelProperty(name = "主键")
    private Long rateDetailsId;

    @JsonProperty(value = "rate_rule_id")
    @Column(name = "rate_rule_id")
    @ApiModelProperty(name = "外键")
    private Long rateRuleId;

    @JsonProperty(value = "sort")
    @Column(name = "sort")
    @ApiModelProperty(name = "排序")
    private Long sort;

    @JsonProperty(value = "rate_name")
    @Column(name = "rate_name")
    @ApiModelProperty(name = "等级名称")
    private String rateName;

    @JsonProperty(value = "compare_type")
    @Column(name = "compare_type")
    @ApiModelProperty(name = "比较类型 1大于 2小于 3大于等于 4小于等于 5等于")
    private Integer compareType;

    @JsonProperty(value = "compare_content")
    @Column(name = "compare_content")
    @ApiModelProperty(name = "比较内容 1平均分 2排名 3第一名 4一票否决")
    private Integer compareContent;

    @JsonProperty(value = "coefficient")
    @Column(name = "coefficient")
    @ApiModelProperty(name = "系数 例如0.7")
    private Double coefficient;
}
