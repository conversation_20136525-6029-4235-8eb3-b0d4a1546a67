package com.goodsogood.ows.model.db.meeting;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 组织生活会实体
 *
 * <AUTHOR>
 * @date 2021.11.23
 */

@ApiModel
@Table(name = "t_meeting_org_life")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrgLifeEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "life_id")
    @ApiModelProperty("主键")
    private Long lifeId;

    /**
     * 会议名称
     */
    @Column(name = "title")
    @ApiModelProperty("会议名称")
    private String title;

    /**
     * 年度
     */
    @Column(name = "years")
    @ApiModelProperty("年度")
    private Integer years;

    /**
     * 活动id
     */
    @Column(name = "meeting_id")
    @ApiModelProperty("活动id")
    private Long meetingId;

    /**
     * 区县id
     */
    @Column(name = "region_id")
    @ApiModelProperty("区县id")
    private Long regionId;

    /**
     * 所属组织id
     */
    @Column(name = "org_id")
    @ApiModelProperty("所属组织id")
    private Long orgId;

    /**
     * 所属组织名称
     */
    @Column(name = "org_name")
    @ApiModelProperty("所属组织名称")
    private String orgName;

    /**
     * 所属组织层级
     */
    @Column(name = "org_level")
    @ApiModelProperty("所属组织层级")
    private String orgLevel;


    /**
     * 1：新建，2：保存了准备草稿，3：已结束，4：保存会后梳理草稿，5：已上报或归档
     */
    @Column(name = "status")
    @ApiModelProperty("1：新建，2：保存了准备草稿，3：已结束，4：保存会后梳理草稿，5：已上报或归档")
    private Integer status;

    /**
     * 是否删除 0：否，1：是
     */
    @Column(name = "is_del")
    @ApiModelProperty("是否删除 0：否，1：是")
    private Integer isDel;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * create_user
     */
    @Column(name = "create_user")
    @ApiModelProperty("create_user")
    private Long createUser;

    /**
     * last_change_user
     */
    @Column(name = "last_change_user")
    @ApiModelProperty("last_change_user")
    private Long lastChangeUser;

    public Long getLifeId() {
        return lifeId;
    }

    public void setLifeId(Long lifeId) {
        this.lifeId = lifeId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getYears() {
        return years;
    }

    public void setYears(Integer years) {
        this.years = years;
    }

    public Long getMeetingId() {
        return meetingId;
    }

    public void setMeetingId(Long meetingId) {
        this.meetingId = meetingId;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getLastChangeUser() {
        return lastChangeUser;
    }

    public void setLastChangeUser(Long lastChangeUser) {
        this.lastChangeUser = lastChangeUser;
    }
}
