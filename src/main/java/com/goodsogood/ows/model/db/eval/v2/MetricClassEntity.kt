package com.goodsogood.ows.model.db.eval.v2

import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDateTime
import javax.persistence.*
import javax.validation.constraints.NotNull
import javax.validation.constraints.Size

@Entity
@Table(name = "t_eval_metric_class")
open class MetricClassEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "metric_class_id", nullable = false)
    open var id: Long? = null

    @NotNull
    @Column(name = "level", nullable = false)
    open var level: Int? = null

    @Size(max = 400)
    @NotNull
    @Column(name = "name", nullable = false, length = 400)
    open var name: String? = null

    @Size(max = 2000)
    @Column(name = "summary", length = 2000)
    open var summary: String? = null

    @NotNull
    @Column(name = "score_limit", nullable = false, precision = 24, scale = 6)
    open var scoreLimit: Double? = null

    @NotNull
    @Column(name = "year", nullable = false)
    open var year: Int? = null

    @NotNull
    @Column(name = "create_user", nullable = false)
    open var createUser: Long? = null

    @NotNull
    @Column(name = "create_time", nullable = false)
    open var createTime: LocalDateTime? = null

    @NotNull
    @Column(name = "last_change_user", nullable = false)
    open var lastChangeUser: Long? = null

    @NotNull
    @Column(name = "update_time", nullable = false)
    open var updateTime: LocalDateTime? = null
}