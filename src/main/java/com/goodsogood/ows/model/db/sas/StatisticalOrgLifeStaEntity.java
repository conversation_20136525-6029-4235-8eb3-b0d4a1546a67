package com.goodsogood.ows.model.db.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 *
 * t_statistical_org_life 实体类
 * 党支部组织生活
 * 这里用户数据操作
 * <AUTHOR>
 * @create 2019-04-22 15:57
*/
@Data
@ApiModel
@Table(name = "t_statistical_org_life")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatisticalOrgLifeStaEntity  {

	@Id
	@JsonProperty(value = "org_life_id")
	@Column(name = "org_life_id")
	private Long orgLifeId;


	@ApiModelProperty("组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;


	@ApiModelProperty("组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;


	@ApiModelProperty("组织类型id")
	@JsonProperty(value = "org_type_id")
	@Column(name = "org_type_id")
	private Integer orgTypeId;


	@ApiModelProperty("你级组织Id")
	@JsonProperty(value = "parent_org_id")
	@Column(name = "parent_org_id")
	private Long parentOrgId;


	@ApiModelProperty("组织父级路径")
	@JsonProperty(value = "org_level")
	@Column(name = "org_level")
	private String orgLevel;


	@ApiModelProperty("是否离退休党组织 1-是 2-否")
	@JsonProperty(value = "is_retire")
	@Column(name = "is_retire")
	private Integer isRetire;


	@ApiModelProperty("活动类型id")
	@JsonProperty(value = "activity_type_id")
	@Column(name = "activity_type_id")
	private Integer activityTypeId;

	@Transient
	@ApiModelProperty("活动类型id,当前显示的活动(多个逗号分割）")
	@JsonProperty(value = "activity_type_ids")
	private String activityTypeIds;


	@ApiModelProperty("活动类型名称")
	@JsonProperty(value = "activity_type_name")
	@Column(name = "activity_type_name")
	private String activityTypeName;


	@ApiModelProperty("参与次数")
	@JsonProperty(value = "participate_num")
	@Column(name = "participate_num")
	private Integer participateNum;


	@JsonProperty(value = "statistical_year")
	@Column(name = "statistical_year")
	private Integer statisticalYear;

	@ApiModelProperty("统计的月（1-12）")
	@JsonProperty(value = "statistical_month")
	@Column(name = "statistical_month")
	private Integer statisticalMonth;

	@ApiModelProperty("统计时间(yyyy-MM)")
	@JsonProperty(value = "statistical_date")
	@Column(name = "statistical_date")
	private String statisticalDate;


	@ApiModelProperty("逻辑状态:(1-有效, 0-无效)")
	@Column(name = "status")
	private Integer status;


	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;


	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;

	@Transient
	@ApiModelProperty(value = "查询的月份")
	private String findMonths;

	@Transient
	@ApiModelProperty(value = "查询的开始日期")
	private Date startTime;

	@Transient
	@ApiModelProperty(value = "查询的结束日期")
	private Date endTime;

	@Transient
	@ApiModelProperty(value = "月份字符串")
	private String dateStr;

	@Transient
	@ApiModelProperty(value = "统计所有次数")
	private Integer countParticipate;

	@Transient
	@ApiModelProperty(value = "统计总月数参数")
	private String totalParam;

	@Transient
	@ApiModelProperty(value = "需要查询的列名")
	private String rowParam;


	@Transient
	@ApiModelProperty(value = "展示的组织类型")
	private String showOrgType;



}

