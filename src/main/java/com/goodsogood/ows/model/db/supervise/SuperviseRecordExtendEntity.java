package com.goodsogood.ows.model.db.supervise;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_supervise_record_extend 实体类
 *
 * 一键督办记录表 
 *
 * <AUTHOR>
 * @create 2022-01-24 14:16
*/
@Data
@ApiModel
@Table(name = "t_supervise_record_extend")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SuperviseRecordExtendEntity {
	
	@Id
	@JsonProperty(value = "supervise_record_extend_id")
	@Column(name = "supervise_record_extend_id")
	private Long superviseRecordExtendId;
	
	
	@ApiModelProperty("1 发送组织 2.用户")
	@JsonProperty(value = "send_object")
	@Column(name = "send_object")
	private Integer sendObject;
	
	
	@ApiModelProperty("模板id")
	@JsonProperty(value = "template_id")
	@Column(name = "template_id")
	private Integer templateId;
	
	
	@ApiModelProperty("用户id")
	@JsonProperty(value = "user_id")
	@Column(name = "user_id")
	private Long userId;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;
	
	
	@ApiModelProperty("选项key")
	@JsonProperty(value = "option_key")
	@Column(name = "option_key")
	private String optionKey;
	
	
	@ApiModelProperty("记录编号（按照时间格式yyyyMMdd）")
	@JsonProperty(value = "record_num")
	@Column(name = "record_num")
	private Integer recordNum;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
}

