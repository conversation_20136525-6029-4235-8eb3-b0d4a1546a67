package com.goodsogood.ows.model.db.experience;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * t_user_party_eval_detail 实体类
 * <p>
 * 考核结果细项
 *
 * <AUTHOR>
 * @create 2022-03-11 10:52
 */
@Data
@ApiModel
@Table(name = "t_user_party_eval_detail")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserPartyEvalDetailEntity {

    @Id
    @JsonProperty(value = "detail_id")
    @Column(name = "detail_id")
    private Long detailId;


    @JsonProperty(value = "rule_id")
    @Column(name = "rule_id")
    private Integer ruleId;


    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    private Long userId;


    @JsonProperty(value = "region_id")
    @Column(name = "region_id")
    private Long regionId;


    @ApiModelProperty("年月格式202201")
    @JsonProperty(value = "date_month")
    @Column(name = "date_month")
    private Integer dateMonth;


    @JsonProperty(value = "task_id")
    @Column(name = "task_id")
    private String taskId;


    @ApiModelProperty("星级")
    @Column(name = "star")
    private Integer star;


    @ApiModelProperty("锤炼计划id")
    @JsonProperty(value = "plan_id")
    @Column(name = "plan_id")
    private Integer planId;


    @ApiModelProperty("1:本月，你连续在数智党建平台学习教育模块学习的天数\n" +
            "2:你主动参与完成了云上党支部任务次数\n" +
            "3:“党建任务”5星评价次数\n" +
            "4:“业务任务”5星评价次数\n" +
            "5:“创新任务”5星评价次数\n" +
            "6:你最近一次参加党小组会的日期------xxxx年xx日\n" +
            "7:本月你登录了数智党建平台的次数\n" +
            "8:（晚上11点-凌晨4点）还在工作的时间---xxxx年xx月xx日 hh:mm\n" +
            "9:本年度主动讲党课次数\n" +
            "10:在数智党建平台“学习教育”模块学习时长五星的最低时长\n" +
            "11:《学习强国》五星的最低学分\n" +
            "12:学习《中国烟草网络学院》五星的最低学时\n" +
            "13:积极主动讲党课五星最低次数")
    @JsonProperty(value = "param_type")
    @Column(name = "param_type")
    private Byte paramType;


    @JsonProperty(value = "param_value")
    @Column(name = "param_value")
    private String paramValue;


    @ApiModelProperty("完成状态 0：未完成   1：完成")
    @JsonProperty(value = "cal_status")
    @Column(name = "cal_status")
    private Byte calStatus;


    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private LocalDateTime createTime;


    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    public Long getDetailId() {
        return detailId;
    }

    public void setDetailId(Long detailId) {
        this.detailId = detailId;
    }

    public Integer getRuleId() {
        return ruleId;
    }

    public void setRuleId(Integer ruleId) {
        this.ruleId = ruleId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Integer getDateMonth() {
        return dateMonth;
    }

    public void setDateMonth(Integer dateMonth) {
        this.dateMonth = dateMonth;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Integer getStar() {
        return star;
    }

    public void setStar(Integer star) {
        this.star = star;
    }

    public Integer getPlanId() {
        return planId;
    }

    public void setPlanId(Integer planId) {
        this.planId = planId;
    }

    public Byte getParamType() {
        return paramType;
    }

    public void setParamType(Byte paramType) {
        this.paramType = paramType;
    }

    public String getParamValue() {
        return paramValue;
    }

    public void setParamValue(String paramValue) {
        this.paramValue = paramValue;
    }

    public Byte getCalStatus() {
        return calStatus;
    }

    public void setCalStatus(Byte calStatus) {
        this.calStatus = calStatus;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}

