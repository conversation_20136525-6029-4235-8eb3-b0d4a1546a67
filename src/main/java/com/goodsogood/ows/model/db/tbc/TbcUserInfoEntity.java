package com.goodsogood.ows.model.db.tbc;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_tbc_user_info 实体类
 *
 * 党业融合组织信息\r\n 
 *
 * <AUTHOR>
 * @create 2021-12-27 17:19
*/
@Data
@ApiModel
@Table(name = "t_tbc_user_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TbcUserInfoEntity {
	
	@Id
	@JsonProperty(value = "tbc_user_info_id")
	@Column(name = "tbc_user_info_id")
	private Long tbcUserInfoId;
	
	
	@ApiModelProperty("组织id")
	@JsonProperty(value = "user_id")
	@Column(name = "user_id")
	private Long userId;


    @ApiModelProperty("用户头像")
    @JsonProperty(value = "head_url")
    @Column(name = "head_url")
    private String headUrl;
	
	@ApiModelProperty("用户名称")
	@JsonProperty(value = "user_name")
	@Column(name = "user_name")
	private String userName;
	
	
	@ApiModelProperty("部门")
	@Column(name = "department")
	private String department;
	
	
	@ApiModelProperty("职务")
	@Column(name = "position")
	private String position;

    @ApiModelProperty("旧的ownId")
    @JsonProperty(value = "owner_id")
    @Column(name = "owner_id")
    private Long ownerId;

    @ApiModelProperty("新的ownId")
    @JsonProperty(value = "new_owner_id")
    @Column(name = "new_owner_id")
    private Long newOwnerId;
	
	@ApiModelProperty("组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;

    @ApiModelProperty("组织层级关系")
    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    private String orgLevel;
	
	
	@ApiModelProperty("用户所在组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;
	
	
	@ApiModelProperty("党建指标")
	@JsonProperty(value = "party_index")
	@Column(name = "party_index")
	private Double partyIndex=0.0;

    @ApiModelProperty("党建积分")
    @JsonProperty(value = "party_score")
    @Column(name = "party_score")
    private Double partyScore=0.0;
	
	@ApiModelProperty("业务指标")
	@JsonProperty(value = "business_index")
	@Column(name = "business_index")
	private Double businessIndex=0.0;

    @ApiModelProperty("业务积分")
    @JsonProperty(value = "business_score")
    @Column(name = "business_score")
    private Double businessScore=0.0;
	
	
	@ApiModelProperty("创建指标")
	@JsonProperty(value = "innovation_index")
	@Column(name = "innovation_index")
	private Double innovationIndex=0.0;


    @ApiModelProperty("创建积分")
    @JsonProperty(value = "innovation_score")
    @Column(name = "innovation_score")
    private Double innovationScore=0.0;
	
	@ApiModelProperty("先峰指标")
	@JsonProperty(value = "xianfeng_index")
	@Column(name = "xianfeng_index")
	private Double xianfengIndex=0.0;

    @ApiModelProperty("先峰指标")
    @JsonProperty(value = "log_xianfeng_index")
    @Column(name = "log_xianfeng_index")
    private Double logXianfengIndex=0.0;
	
	
	@ApiModelProperty("统计时间（202110或者202101)")
	@JsonProperty(value = "sta_month")
	@Column(name = "sta_month")
	private String staMonth;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("更新时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "党内职务")
    @JsonProperty(value = "party_position")
    @Column(name = "party_position")
    private String partyPosition;


    @ApiModelProperty("所属序列编号")
    @JsonProperty(value = "seq_number")
    @Column(name = "seq_number")
    private Integer seqNumber;

	
}

