package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_circuit_detail 实体类
 *
 * 线路详情表 
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_circuit_detail")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphCircuitDetailEntity  extends  BaseEntity{
	
//	@Id
	@JsonProperty(value = "circuit_detail_id")
	@Column(name = "circuit_detail_id")
	private Long circuitDetailId;
	
	
	@ApiModelProperty("线路总表主键id")
	@JsonProperty(value = "circuit_id")
	@Column(name = "circuit_id")
	private Long circuitId;
	
	
	@ApiModelProperty("编号")
	@Column(name = "level")
	private Integer level;
	
	
	@Column(name = "content")
	private String content;
	
	
	@ApiModelProperty("发放积分数量")
	@JsonProperty(value = "give_score")
	@Column(name = "give_score")
	private Long giveScore;
	
	
	@JsonProperty(value = "medal_name")
	@Column(name = "medal_name")
	private String medalName;
	
	
	@ApiModelProperty("奖章")
	@JsonProperty(value = "medal_url")
	@Column(name = "medal_url")
	private String medalUrl;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
}

