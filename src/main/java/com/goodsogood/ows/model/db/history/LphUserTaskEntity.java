package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_user_task 实体类
 *
 * 用户任务流水表 
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_user_task")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphUserTaskEntity  extends  BaseEntity{
	
//	@Id
	@ApiModelProperty("自增主键")
	@JsonProperty(value = "task_id")
	@Column(name = "task_id")
	private Long taskId;
	
	
	@ApiModelProperty("openId")
	@JsonProperty(value = "open_id")
	@Column(name = "open_id")
	private String openId;
	
	
	@ApiModelProperty("区县id")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Integer regionId;
	
	
	@ApiModelProperty("任务类型 0：签到，1：5k步，2：分享，3：邀请，4：抽奖")
	@Column(name = "type")
	private Byte type;
	
	
	@ApiModelProperty("备注")
	@Column(name = "remark")
	private String remark;
	
	
	@ApiModelProperty("已完成次数")
	@JsonProperty(value = "complete_num")
	@Column(name = "complete_num")
	private Integer completeNum;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
}

