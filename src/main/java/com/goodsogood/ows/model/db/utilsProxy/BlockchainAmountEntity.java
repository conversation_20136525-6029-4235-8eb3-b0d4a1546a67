package com.goodsogood.ows.model.db.utilsProxy;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

/**
 * 区块链统计
 * <AUTHOR> tc
 * @date 2022/04/19
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Table(name = "t_utils_proxy_statistics")
public class BlockchainAmountEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "statistics_id")
    private Long statisticsId;

    /**
     * 区县编号
     */
    @Column(name = "region_id")
    private Long regionId;

    /**
     * 区块链上链方法名称
     */
    @Column(name = "method_name")
    private String methodName;

    /**
     * 区块链上链统计次数
     */
    @Column(name = "amount")
    private Long amount;

    /**
     * 最后修改时间
     */
    @Column(name = "update_date")
    private LocalDate updateDate;

    public BlockchainAmountEntity(Long regionId, String methodName, Long amount, LocalDate updateDate) {
        this.regionId = regionId;
        this.methodName = methodName;
        this.amount = amount;
        this.updateDate = updateDate;
    }
}
