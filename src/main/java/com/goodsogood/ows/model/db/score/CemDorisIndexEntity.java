package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.data.annotation.Id;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Table;

@Data
@ApiModel
@Table(name = "t_cem_doris_index")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CemDorisIndexEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long ruleId;

    private String name;

    private String detail;

    private Integer scoreType;

    private String scoreTypeName;

    private Integer parentScoreType;

    private String parentTypeName;

    private String remark;

    private Integer object;//1-党员 2-党组织  3-党组

    private Integer status;//0-启用  1-未启用


}