package com.goodsogood.ows.model.db.tbcFusion;

/**
 * @Author: mengting
 * @Date: 2022/4/18 14:43
 */

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-04-18
 * 营销系统单位名称与本系统单位id对应关系表
 */

@Data
@ApiModel
@Table(name = "t_party_county_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PartyCountyInfoEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    private String countyName;//营销系统区县名称


    //本系统名称
    private String orgName;


    //单位id
    private Long orgId;


    private LocalDateTime createTime;


    private LocalDateTime updateTime;


}
