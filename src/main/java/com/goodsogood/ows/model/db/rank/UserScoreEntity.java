package com.goodsogood.ows.model.db.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Create by FuXiao on 2020/10/19
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_rank_user_score")
public class UserScoreEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "user_score_id")
    @Column(name = "user_score_id")
    @ApiModelProperty(name = "主键")
    private Long userScoreId;

    @JsonProperty(value = "score_rule_id")
    @Column(name = "score_rule_id")
    @ApiModelProperty(name = "外键")
    private Long scoreRuleId;

    @JsonProperty(value = "score_rule_name")
    @Column(name = "score_rule_name")
    @ApiModelProperty(name = "对应规则名称")
    private String scoreRuleName;

    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    @ApiModelProperty(name = "用户id")
    private Long userId;

    @JsonProperty(value = "user_name")
    @Column(name = "user_name")
    @ApiModelProperty(name = "姓名")
    private String userName;

    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    @ApiModelProperty(name = "所在组织id")
    private Long orgId;

    @JsonProperty(value = "time")
    @Column(name = "time")
    @ApiModelProperty(name = "时间")
    private String time;

    @JsonProperty(value = "score")
    @Column(name = "score")
    @ApiModelProperty(name = "得分")
    private Double score;

    @JsonProperty(value = "percentage")
    @Column(name = "percentage")
    @ApiModelProperty(name = "计算系数 例如30%用0.3表示")
    private Double percentage;

    @JsonProperty(value = "top_id")
    @Column(name = "top_id")
    @ApiModelProperty(name = "类型id")
    private Long topId;
}
