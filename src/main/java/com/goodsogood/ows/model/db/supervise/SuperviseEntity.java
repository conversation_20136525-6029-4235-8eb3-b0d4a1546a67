package com.goodsogood.ows.model.db.supervise;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_supervise 实体类
 *
 * <AUTHOR>
 * @create 2021-10-13 17:24
*/
@ApiModel
@Table(name = "t_supervise")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SuperviseEntity {
	
	@Id
	@JsonProperty(value = "supervise_id")
	@Column(name = "supervise_id")
	private Long superviseId;
	
	
	@ApiModelProperty("组织类型")
	@JsonProperty(value = "org_type")
	@Column(name = "org_type")
	private Integer orgType;

    @Column(name = "org_type_child")
    @JsonProperty("org_type_child")
    private Integer orgTypeChild;
	
	
	@ApiModelProperty("组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;

    @ApiModelProperty("父级组织id")
    @JsonProperty(value = "parent_id")
    @Column(name = "parent_id")
    private Long parentId;
	
	@ApiModelProperty("组织层级关系")
	@JsonProperty(value = "org_level")
	@Column(name = "org_level")
	private String orgLevel;
	
	
	@ApiModelProperty("组织书记,多个逗号分隔")
	@JsonProperty(value = "org_secretary")
	@Column(name = "org_secretary")
	private String orgSecretary;

    @ApiModelProperty("组织书记,多个逗号分隔")
    @JsonProperty(value = "org_secretary_id")
    @Column(name = "org_secretary_id")
    private String orgSecretaryId;
	
	
	@ApiModelProperty("组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;
	
	
	@ApiModelProperty("区县id")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Long regionId;

	
	
	@ApiModelProperty("组织信息不完整")
	@JsonProperty(value = "option_1")
	@Column(name = "option_1")
	private Integer option1;
	
	
	@ApiModelProperty("组织逾期未换届")
	@JsonProperty(value = "option_2")
	@Column(name = "option_2")
	private Integer option2;
	
	
	@ApiModelProperty("党支部正式党员7名（含）以上，未设立支委会")
	@JsonProperty(value = "option_3")
	@Column(name = "option_3")
	private Integer option3;
	
	
	@ApiModelProperty("未配齐组织书记")
	@JsonProperty(value = "option_4")
	@Column(name = "option_4")
	private Integer option4;
	
	
	@ApiModelProperty("连续2个月未开展支部委员会")
	@JsonProperty(value = "option_5")
	@Column(name = "option_5")
	private Integer option5;
	
	
	@ApiModelProperty("连续2个月未开展党小组会")
	@JsonProperty(value = "option_6")
	@Column(name = "option_6")
	private Integer option6;
	
	
	@ApiModelProperty("连续2个月未开展主题党日")
	@JsonProperty(value = "option_7")
	@Column(name = "option_7")
	private Integer option7;
	
	
	@ApiModelProperty("连续2个季度未开展支部党员大会")
	@JsonProperty(value = "option_8")
	@Column(name = "option_8")
	private Integer option8;
	
	
	@ApiModelProperty("连续2个季度未开展党课")
	@JsonProperty(value = "option_9")
	@Column(name = "option_9")
	private Integer option9;
	
	
	@ApiModelProperty("党支部党员人数20人以上，未划分党小组")
	@JsonProperty(value = "option_10")
	@Column(name = "option_10")
	private Integer option10;
	
	
	@ApiModelProperty("连续2个月未参加支部委员会的党员")
	@JsonProperty(value = "option_11")
	@Column(name = "option_11")
	private Integer option11;
	
	
	@ApiModelProperty("连续2个月未参加党小组会的党员")
	@JsonProperty(value = "option_12")
	@Column(name = "option_12")
	private Integer option12;
	
	
	@ApiModelProperty("连续2个月未参加主题党日的党员")
	@JsonProperty(value = "option_13")
	@Column(name = "option_13")
	private Integer option13;
	
	
	@ApiModelProperty("连续2个季度未参加支部党员大会的党员")
	@JsonProperty(value = "option_14")
	@Column(name = "option_14")
	private Integer option14;
	
	
	@ApiModelProperty("连续2个季度未参加党课党员")
	@JsonProperty(value = "option_15")
	@Column(name = "option_15")
	private Integer option15;
	
	
	@ApiModelProperty("连续5个月未交纳党费的党员")
	@JsonProperty(value = "option_16")
	@Column(name = "option_16")
	private Integer option16;
	
	
	@ApiModelProperty("连续6个月未交纳党费的党员")
	@JsonProperty(value = "option_17")
	@Column(name = "option_17")
	private Integer option17;
	
	
	@ApiModelProperty("连续2个季度未完成重温入党誓词的党员")
	@JsonProperty(value = "option_18")
	@Column(name = "option_18")
	private Integer option18;
	
	
	@ApiModelProperty("连续2个季度未完成重温入党志愿书的党员")
	@JsonProperty(value = "option_19")
	@Column(name = "option_19")
	private Integer option19;
	
	
	@ApiModelProperty("未配齐组织书记")
	@JsonProperty(value = "option_20")
	@Column(name = "option_20")
	private Integer option20;
	
	
	@ApiModelProperty("党支部党员人数20人以上，未划分党小组的")
	@JsonProperty(value = "option_21")
	@Column(name = "option_21")
	private Integer option21;
	
	
	@ApiModelProperty("组织生活异常的党支部")
	@JsonProperty(value = "option_22")
	@Column(name = "option_22")
	private Integer option22;
	
	
	@ApiModelProperty("连续2个季度重温入党誓词完成率低于50%的党支部")
	@JsonProperty(value = "option_23")
	@Column(name = "option_23")
	private Integer option23;
	
	
	@ApiModelProperty("连续2个季度重温入党志愿书完成率低于50%的党支部")
	@JsonProperty(value = "option_24")
	@Column(name = "option_24")
	private Integer option24;
	
	
	@ApiModelProperty("有党员连续5个月未交纳党费的党支部")
	@JsonProperty(value = "option_25")
	@Column(name = "option_25")
	private Integer option25;
	
	
	@ApiModelProperty("有党员连续6个月未交纳党费的党支部")
	@JsonProperty(value = "option_26")
	@Column(name = "option_26")
	private Integer option26;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;

    /**
     * 是否预警
     */
    @JsonProperty(value = "is_supervise")
    @Column(name = "is_supervise")
	private Integer isSupervise=0;

	public Long getSuperviseId() {
		return superviseId;
	}

	public void setSuperviseId(Long superviseId) {
		this.superviseId = superviseId;
	}

	public Integer getOrgType() {
		return orgType;
	}

	public void setOrgType(Integer orgType) {
		this.orgType = orgType;
	}

	public Integer getOrgTypeChild() {
		return orgTypeChild;
	}

	public void setOrgTypeChild(Integer orgTypeChild) {
		this.orgTypeChild = orgTypeChild;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public String getOrgLevel() {
		return orgLevel;
	}

	public void setOrgLevel(String orgLevel) {
		this.orgLevel = orgLevel;
	}

	public String getOrgSecretary() {
		return orgSecretary;
	}

	public void setOrgSecretary(String orgSecretary) {
		this.orgSecretary = orgSecretary;
	}

	public String getOrgSecretaryId() {
		return orgSecretaryId;
	}

	public void setOrgSecretaryId(String orgSecretaryId) {
		this.orgSecretaryId = orgSecretaryId;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public Long getRegionId() {
		return regionId;
	}

	public void setRegionId(Long regionId) {
		this.regionId = regionId;
	}

	public Integer getOption1() {
		return option1;
	}

	public void setOption1(Integer option1) {
		this.option1 = option1;
	}

	public Integer getOption2() {
		return option2;
	}

	public void setOption2(Integer option2) {
		this.option2 = option2;
	}

	public Integer getOption3() {
		return option3;
	}

	public void setOption3(Integer option3) {
		this.option3 = option3;
	}

	public Integer getOption4() {
		return option4;
	}

	public void setOption4(Integer option4) {
		this.option4 = option4;
	}

	public Integer getOption5() {
		return option5;
	}

	public void setOption5(Integer option5) {
		this.option5 = option5;
	}

	public Integer getOption6() {
		return option6;
	}

	public void setOption6(Integer option6) {
		this.option6 = option6;
	}

	public Integer getOption7() {
		return option7;
	}

	public void setOption7(Integer option7) {
		this.option7 = option7;
	}

	public Integer getOption8() {
		return option8;
	}

	public void setOption8(Integer option8) {
		this.option8 = option8;
	}

	public Integer getOption9() {
		return option9;
	}

	public void setOption9(Integer option9) {
		this.option9 = option9;
	}

	public Integer getOption10() {
		return option10;
	}

	public void setOption10(Integer option10) {
		this.option10 = option10;
	}

	public Integer getOption11() {
		return option11;
	}

	public void setOption11(Integer option11) {
		this.option11 = option11;
	}

	public Integer getOption12() {
		return option12;
	}

	public void setOption12(Integer option12) {
		this.option12 = option12;
	}

	public Integer getOption13() {
		return option13;
	}

	public void setOption13(Integer option13) {
		this.option13 = option13;
	}

	public Integer getOption14() {
		return option14;
	}

	public void setOption14(Integer option14) {
		this.option14 = option14;
	}

	public Integer getOption15() {
		return option15;
	}

	public void setOption15(Integer option15) {
		this.option15 = option15;
	}

	public Integer getOption16() {
		return option16;
	}

	public void setOption16(Integer option16) {
		this.option16 = option16;
	}

	public Integer getOption17() {
		return option17;
	}

	public void setOption17(Integer option17) {
		this.option17 = option17;
	}

	public Integer getOption18() {
		return option18;
	}

	public void setOption18(Integer option18) {
		this.option18 = option18;
	}

	public Integer getOption19() {
		return option19;
	}

	public void setOption19(Integer option19) {
		this.option19 = option19;
	}

	public Integer getOption20() {
		return option20;
	}

	public void setOption20(Integer option20) {
		this.option20 = option20;
	}

	public Integer getOption21() {
		return option21;
	}

	public void setOption21(Integer option21) {
		this.option21 = option21;
	}

	public Integer getOption22() {
		return option22;
	}

	public void setOption22(Integer option22) {
		this.option22 = option22;
	}

	public Integer getOption23() {
		return option23;
	}

	public void setOption23(Integer option23) {
		this.option23 = option23;
	}

	public Integer getOption24() {
		return option24;
	}

	public void setOption24(Integer option24) {
		this.option24 = option24;
	}

	public Integer getOption25() {
		return option25;
	}

	public void setOption25(Integer option25) {
		this.option25 = option25;
	}

	public Integer getOption26() {
		return option26;
	}

	public void setOption26(Integer option26) {
		this.option26 = option26;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getIsSupervise() {
		return isSupervise;
	}

	public void setIsSupervise(Integer isSupervise) {
		this.isSupervise = isSupervise;
	}
}

