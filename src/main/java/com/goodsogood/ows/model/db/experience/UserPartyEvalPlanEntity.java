package com.goodsogood.ows.model.db.experience;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 *
 * t_user_party_eval_plan 实体类
 *
 * 锤炼计划 
 *
 * <AUTHOR>
 * @create 2022-03-11 10:52
*/
@Data
@ApiModel
@Table(name = "t_user_party_eval_plan")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserPartyEvalPlanEntity {
	
	@Id
	@ApiModelProperty("锤炼计划id，因可能多个rule对应一个plan,为方便distinct，设置id")
	@JsonProperty(value = "plan_id")
	@Column(name = "plan_id")
	private Integer planId;
	
	
	@ApiModelProperty("锤炼计划")
	@Column(name = "plan")
	private String plan;
	
	
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Integer regionId;
	
	
	@ApiModelProperty("对应的大项规则")
	@JsonProperty(value = "rule_parent_id")
	@Column(name = "rule_parent_id")
	private Integer ruleParentId;
	
	
	@ApiModelProperty("对应的细项规则")
	@JsonProperty(value = "rule_id")
	@Column(name = "rule_id")
	private Integer ruleId;

	@ApiModelProperty("对应的参数type")
	@JsonProperty(value = "rule_id")
	@Column(name = "param_type")
	private Byte paramType;
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private LocalDateTime createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private LocalDateTime updateTime;
	
}

