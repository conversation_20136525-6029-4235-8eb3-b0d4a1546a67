package com.goodsogood.ows.model.db.task;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 任务流水表
 * @date 2021、09/13
 */
@Data
@ApiModel
@Table(name = "t_task_log")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskLogEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 日志主键id
     */
    @Id
    @ApiModelProperty("日志主键id")
    private Long taskLogId;

    /**
     * 接收表id
     */
    @ApiModelProperty("接收表id")
    @Column(name = "task_access_id")
    private Long taskAccessId;

    /**
     * 执行描述
     */
    @ApiModelProperty("执行描述")
    @Column(name = "details_content")
    private String detailsContent;

    /**
     * 1：填报任务 2：审核任务
     */
    @ApiModelProperty("1：填报任务 2：审核任务")
    @Column(name = "operator_type")
    private Integer operatorType;

    /**
     * 1：自行办理 2：转派组织 3：转派人员
     */
    @ApiModelProperty("1：自行办理 2：转派组织 3：转派人员")
    @Column(name = "operator_status")
    private Integer operatorStatus;

    /**
     * 转派目标json（根据处理方式指定目标）
     */
    @ApiModelProperty("转派目标json（根据处理方式指定目标）")
    @Column(name = "operator_unit_json")
    private String operatorUnitJson;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    @Column(name = "user_name")
    private String userName;

    /**
     * 支部id
     */
    @ApiModelProperty("支部id")
    @Column(name = "org_id")
    private Long orgId;

    /**
     * 支部名称
     */
    @ApiModelProperty("支部名称")
    @Column(name = "org_name")
    private String orgName;

    /**
     * 任务操作记录状态: 填报—（1：待填报 2：草稿 3：已提交 4：已转派 5：已撤回） 审核—（6：已退回 7：未通过 8：已通过 ） 9：逾期
     */
    @ApiModelProperty("任务操作记录状态: 填报—（1：待填报 2：草稿 3：已提交 4：已转派 5：已撤回） 审核—（6：已退回 7：未通过 8：已通过 ） 9：逾期")
    @Column(name = "task_log_status")
    private Integer taskLogStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @Column(name = "create_time")
    private Date createTime;


}
