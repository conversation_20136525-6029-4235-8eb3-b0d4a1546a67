package com.goodsogood.ows.model.db.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Create by FuXiao on 2020/10/19
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_rank_score_rule")
public class ScoreRuleEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "score_rule_id")
    @Column(name = "score_rule_id")
    @ApiModelProperty(name = "评分规则id")
    private Long scoreRuleId;

    @JsonProperty(value = "column_id")
    @Column(name = "column_id")
    @ApiModelProperty(name = "外键")
    private Long columnId;

    @JsonProperty(value = "rule_description")
    @Column(name = "rule_description")
    @ApiModelProperty(name = "规则描述")
    private String ruleDescription;

    @JsonProperty(value = "score_rule")
    @Column(name = "score_rule")
    @ApiModelProperty(name = "打分逻辑 1加分 2减分 3清零")
    private Integer scoreRule;

    @JsonProperty(value = "score")
    @Column(name = "score")
    @ApiModelProperty(name = "分值 如果是区间则用1-100表示")
    private String score;

    @JsonProperty(value = "is_range")
    @Column(name = "is_range")
    @ApiModelProperty(name = "是否范围打分 1是 0否")
    private Integer isRange;

    @JsonProperty(value = "default_rule")
    @Column(name = "default_rule")
    @ApiModelProperty(name = "是否是默认规则 1是 0否")
    private Integer defaultRule;
}
