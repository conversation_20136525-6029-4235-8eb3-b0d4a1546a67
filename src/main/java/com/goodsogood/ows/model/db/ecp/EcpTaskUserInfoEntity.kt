package com.goodsogood.ows.model.db.ecp

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModelProperty
import lombok.Data
import java.util.*
import javax.persistence.Column
import javax.persistence.GeneratedValue
import javax.persistence.Id
import javax.persistence.Table


@Data
@Table(name = "t_ecp_task_user_info")
@JsonNaming(PropertyNamingStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
class EcpTaskUserInfoEntity {

    /**
     * 云区参与任务统计表主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("云区参与任务统计表主键")
    @Column(name = "ecp_user_info_id")
    var ecpUserInfoId: Long? = null

    /**
     * 区县id
     */
    @ApiModelProperty("区县id")
    @Column(name = "region_id")
    var regionId: Long? = null

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    @Column(name = "user_id")
    var userId: Long? = null

    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    @Column(name = "user_name")
    var userName: String? = null

    /**
     * 用户所属党组织id
     */
    @ApiModelProperty("用户所属党组织id")
    @Column(name = "org_id")
    var orgId: Long? = null

    /**
     * 用户所属党组织名称
     */
    @ApiModelProperty("用户所属党组织名称")
    @Column(name = "org_name")
    var orgName: String? = null

    /**
     * 所属单位id
     */
    @ApiModelProperty("所属单位id")
    @Column(name = "owner_id")
    var ownerId: Long? = null

    /**
     * 所属单位名称
     */
    @ApiModelProperty("所属单位名称")
    @Column(name = "owner_name")
    var ownerName: String? = null

    /**
     * 用户完成云区任务次数
     */
    @ApiModelProperty("用户完成云区任务次数")
    @Column(name = "finish_num")
    var finishNum: Int? = null

    /**
     * 是否是党员 0:否 1:是
     */
    @ApiModelProperty("是否是党员 0:否 1:是")
    @Column(name = "is_party")
    var isParty: Int? = null

    /**
     * 用户发起 云区 任务数
     */
    @ApiModelProperty("用户发起 云区 任务数")
    @Column(name = "publish_task_num")
    var publishTaskNum: Int? = null

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @Column(name = "create_time")
    var createTime: Date? = null

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @Column(name = "update_time")
    var updateTime: Date? = null

    companion object {
        const val serialVersionUID = 1L
    }
}