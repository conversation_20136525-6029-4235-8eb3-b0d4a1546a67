package com.goodsogood.ows.model.db.user

import lombok.Data
import java.io.Serializable
import java.util.*
import javax.persistence.*

/**
 * @description 党组管理
 * <AUTHOR>
 * @date 2021-09-07
 */
@Entity
@Data
@Table(name = "t_party_group")
class PartyGroupEntity : Serializable {
    @Id
    @GeneratedValue(generator = "JDBC")
    /**
     * group_id
     */
    @Column(name = "group_id")
    var groupId: Long? = null

    /**
     * 此组织id 为单位id
     */
    @Column(name = "org_id")
    var orgId: Long? = null

    /**
     * region_id
     */
    @Column(name = "region_id")
    var regionId: Long? = null

    /**
     * 党组名称
     */
    @Column(name = "name")
    var name: String? = null

    /**
     * 1:有效 0:无效
     */
    @Column(name = "status")
    var status: Int? = null

    /**
     * 创建人
     */
    @Column(name = "create_user")
    var createUser: Long? = null

    /**
     * 创建时当前用户所属组织id
     */
    @Column(name = "create_org")
    var createOrg: Long? = null

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    var createTime: Date? = null

    /**
     * change_user
     */
    @Column(name = "change_user")
    var changeUser: Long? = null

    /**
     * change_org
     */
    @Column(name = "change_org")
    var changeOrg: Long? = null

    /**
     * change_time
     */
    @Column(name = "change_time")
    var changeTime: Date? = null

    companion object {
        private const val serialVersionUID = 1L
    }
}