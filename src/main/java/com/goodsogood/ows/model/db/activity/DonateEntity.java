package com.goodsogood.ows.model.db.activity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * Auther: ruoyu
 * Date: 2019/1/8
 * Description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Table(name = "t_donate")
public class DonateEntity {

    @ApiModelProperty(value = "活动id")
    @JsonProperty("activity_id")
    @Column(name = "activity_id")
    private Long activityId;

    @ApiModelProperty(value = "捐赠方式 1:自由捐赠 2:定额捐赠")
    @JsonProperty("donate_type")
    @Column(name = "donate_type")
    private Integer donateType;

    @ApiModelProperty(value = "定额捐赠时 设置的捐赠数额")
    @JsonProperty("donate_num")
    @Column(name = "donate_num")
    private Long donateNum;

    @ApiModelProperty(value = "外链开关 1：打开 0：关闭")
    @JsonProperty("is_open")
    @Column(name = "is_open")
    private Integer isOpen;

    @ApiModelProperty(value = "链接名称")
    @JsonProperty("link_name")
    @Column(name = "link_name")
    private String linkName;

    @ApiModelProperty(value = "外链类型 1：图片 2：链接")
    @JsonProperty("link_type")
    @Column(name = "link_type")
    private Integer linkType;

    @ApiModelProperty(value = "链接地址")
    @JsonProperty("link_str")
    @Column(name = "link_str")
    private String linkStr;

    @ApiModelProperty(value = "快捷输入选项")
    @JsonProperty("quick_str")
    @Column(name = "quick_str")
    private String quickStr;

    @JsonProperty("count_people")
    @ApiModelProperty(value = "参与人数")
    private Integer countPeople;

    @JsonProperty("score")
    @ApiModelProperty(value = "捐赠总积分")
    private Long score;

    @ApiModelProperty(value = "创建人id")
    @JsonProperty("create_user")
    @Column(name = "create_user")
    private Long createUser;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("create_time")
    @Column(name = "create_time")
    private Date createTime;

    @ApiModelProperty(value = "最近修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("update_time")
    @Column(name = "update_time")
    private Date updateTime;

    @ApiModelProperty(value = "最近修改人id")
    @JsonProperty("last_change_user")
    @Column(name = "last_change_user")
    private Long lastChangeUser;

}
