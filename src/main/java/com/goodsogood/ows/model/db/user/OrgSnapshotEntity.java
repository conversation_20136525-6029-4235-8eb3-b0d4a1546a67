package com.goodsogood.ows.model.db.user;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-11-03 18:15
 * @since 3.0.1
 **/
@Table(name = "t_org_snapshot")
@Data
public class OrgSnapshotEntity {

     @Id
     @GeneratedValue(generator = "JDBC")
     @Column(name = "org_snapshot_id")
     private Long orgSnapshotId;
     @Column(name = "year")
     private Integer year;
     @Column(name = "month")
     private Integer month;
     @Column(name = "date_month")
     private String dateMonth;
     @Column(name = "org_id")
     private Long orgId;
     @Column(name = "org_pid")
     private Long orgPid;
     @Column(name = "org_name")
     private String orgName;
     @Column(name = "org_short_name")
     private String orgShortName;
     @Column(name = "party_leader")
     private String partyLeader;
     @Column(name = "address")
     private String address;
     /**
      * 经度
      */
     @Column(name = "longitude")
     private Double longitude;
     /**
      * 纬度
      */
     @Column(name = "latitude")
     private Double latitude;
     @Column(name = "contacts")
     private String contacts;
     @Column(name = "contacts_phone")
     private String contactsPhone;
     @Column(name = "build_time")
     private String buildTime;
     @Column(name = "owner_id")
     private Long ownerId;
     @Column(name = "is_retire")
     private Integer isRetire;
     @Column(name = "is_flow")
     private Integer isFlow;
     @Column(name = "org_type")
     private Integer orgType;
     @Column(name = "org_type_child")
     private Integer orgTypeChild;
     @Column(name = "org_type_child_old")
     private Integer orgTypeChildOld;
     @Column(name = "org_level")
     private String orgLevel;
     @Column(name = "org_old_pid")
     private Long orgOldPid;
     @Column(name = "org_old_level")
     private String orgOldLevel;
     @Column(name = "region_id")
     private Long regionId;
     @Column(name = "status")
     private Integer status;
     @Column(name = "status_old")
     private Integer statusOld;
     @Column(name = "create_time")
     private Date createTime;
}
