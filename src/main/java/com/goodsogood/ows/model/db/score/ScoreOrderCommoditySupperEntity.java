package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
* @Author: tc
* @Description 积分订单商品和退款商品父类
* @Date 16:16 2019/5/30
*/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
public class ScoreOrderCommoditySupperEntity {

     @Column(name = "commodity_id")
     @ApiModelProperty(name = "商品编号")
     protected String commodityId;

     @Column(name = "commodity_name")
     @ApiModelProperty(name = "商品名称")
     protected String commodityName;

     @Column(name = "category_two")
     @ApiModelProperty(name = "商品二级分类编号")
     protected String categoryTwo;

     @Column(name = "category_two_txt")
     @ApiModelProperty(name = "二级分类文本")
     protected String categoryTwoTxt;

     @Column(name = "category_three")
     @ApiModelProperty(name = "商品三级分类编号")
     protected String categoryThree;

     @Column(name = "category_three_txt")
     @ApiModelProperty(name = "三级分类文本")
     protected String categoryThreeTxt;

     /**
      * 商品市场指导价格（单位分 ， RMB）
      */
     @Column(name = "mall_price")
     @ApiModelProperty(name = "商品市场指导价格")
     protected Long mallPrice;

     @Column(name = "cost_amount")
     @ApiModelProperty(name = "商品成本金额(分)")
     protected Long costAmount;

     /**
      * 退款商品数据时  退款给渠道的金额
      */
     @Column(name = "sale_amount")
     @ApiModelProperty(name = "商品销售金额(分)")
     protected Long saleAmount;

     /**
      * 退款商品数据时  退款给用户的积分
      */
     @Column(name = "sale_score")
     @ApiModelProperty(name = "商品销售积分价格")
     protected Long saleScore;

     @Column(name = "count")
     @ApiModelProperty(name = "数量")
     protected Integer count;

     @Column(name = "cost_total")
     @ApiModelProperty(name = "采购成本小计(分)")
     protected Long costTotal;

     @Column(name = "sale_total")
     @ApiModelProperty(name = "售价小计(分)")
     protected Long saleTotal;

}
