package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_user_level 实体类
 *
 * 闯关答题流水表 
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_user_level")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphUserLevelEntity   extends  BaseEntity{
	
//	@Id
	@ApiModelProperty("自增主键")
	@JsonProperty(value = "level_id")
	@Column(name = "level_id")
	private Long levelId;
	
	
	@ApiModelProperty("openId")
	@JsonProperty(value = "open_id")
	@Column(name = "open_id")
	private String openId;
	
	
	@ApiModelProperty("区县id")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Integer regionId;
	
	
	@ApiModelProperty("获得的积分")
	@JsonProperty(value = "score_num")
	@Column(name = "score_num")
	private Long scoreNum;
	
	
	@ApiModelProperty("是否调用成功 0:未推送 1:推送成功 2:推送失败 3:回调成功")
	@JsonProperty(value = "is_push")
	@Column(name = "is_push")
	private Byte isPush;
	
	
	@ApiModelProperty("调用积分中心的token")
	@Column(name = "token")
	private String token;
	
	
	@ApiModelProperty("线路总表id")
	@JsonProperty(value = "circuit_id")
	@Column(name = "circuit_id")
	private Long circuitId;
	
	
	@ApiModelProperty("线路详情id")
	@JsonProperty(value = "circuit_detail_id")
	@Column(name = "circuit_detail_id")
	private Long circuitDetailId;
	
	
	@ApiModelProperty("答题状态 0：true，1：false")
	@Column(name = "status")
	private Byte status;
	
	
	@JsonProperty(value = "medal_name")
	@Column(name = "medal_name")
	private String medalName;
	
	
	@ApiModelProperty("奖章地址")
	@JsonProperty(value = "medal_url")
	@Column(name = "medal_url")
	private String medalUrl;

	
	@ApiModelProperty("修改时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
}

