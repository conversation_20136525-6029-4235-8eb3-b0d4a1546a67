package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
* @Author: tc
* @Description 消费扶贫商品信息类
* @Date 15:14 2019/10/21
*/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@Table(name = "t_score_poverty_order")
public class PovertyOrderEntity {
    @Id
    @Column(name = "prverty_order_id")
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty(name = "id")
    private Long id;

    /**
     * 每次推送生成一次唯一的推送数据标识，当推送成功后，会回调通知巴味渝珍该批次数据推送成功
     */
    @Column(name = "token")
    @ApiModelProperty("唯一标识")
    private String token;

    /**
     *  用户信息加密字符串
     */
    @Column(name = "info")
    @ApiModelProperty("用户信息加密字符串")
    private String info;

    /**
     *  渠道标志
     */
    @Column(name = "channel")
    @ApiModelProperty("渠道标志")
    private String channel;

    /**
     *  用户标识openid
     */
    @Column(name = "openid")
    @ApiModelProperty("用户标识")
    private String openid;

    /**
     * 订单编号  巴味渝珍标识唯一的订单编号
     */
    @Column(name = "order_id")
    @ApiModelProperty("订单编号")
    private String orderId;

    @Column(name = "oid")
    @ApiModelProperty("当前登录公众号所属组织")
    private Long oid;

    @Column(name = "org_id")
    @ApiModelProperty("组织id")
    private Long orgId;

    @Column(name = "org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    @Column(name = "user_id")
    @ApiModelProperty("用户id")
    private Long userId;

    @Column(name = "user_name")
    @ApiModelProperty("用户姓名")
    private String userName;

    @Column(name = "party_org_id")
    @ApiModelProperty("所属党委id")
    private Long partyOrgId;

    @Column(name = "party_org_name")
    @ApiModelProperty("所属党委名称")
    private String partyOrgName;

    @Column(name = "unit_org_id")
    @ApiModelProperty("所属单位id")
    private Long unitOrgId;

    @Column(name = "unit_org_name")
    @ApiModelProperty("所属单位名称")
    private String unitOrgName;

    /**
     * 扶贫商城推送过来的订单总金额，单位:分
     */
    @Column(name = "total_price")
    @ApiModelProperty("订单总金额")
    private Integer totalPrice;

    /**
     * 扶贫商城推送过来的订单总邮费，单位:分
     */
    @Column(name = "logistics")
    @ApiModelProperty("订单总邮费")
    private Integer logistics;

    /**
     * 扶贫商城推送过来的捐款金额，单位:分
     */
    @Column(name = "donation")
    @ApiModelProperty("捐款金额")
    private Integer donation;

    /**
     * 计算出来的捐款笔数
     */
    @Column(name = "donation_count")
    @ApiModelProperty("计算出来的捐款笔数")
    private Integer donationCount;

    /**
     * 计算出来的捐款金额，单位:分
     */
    @Column(name = "calculate_donation")
    @ApiModelProperty("计算的捐款金额")
    private Integer calculateDonation;

    /**
     * 计算出来的订单总金额(商品单价*数量之和)，单位:分
     */
    @Column(name = "calculate_money")
    @ApiModelProperty("计算的订单总金额")
    private Integer calculateMoney;

    /**
     * 交易时间  格式:YYYY-MM-dd HH:mm:ss
     */
    @Column(name = "trading_time")
    @ApiModelProperty("交易时间")
    private Date tradingTime;

    public PovertyOrderEntity() {
        super();
    }

    public PovertyOrderEntity(String token, String info, String channel, String openid, String orderId, Long oid, Long orgId, String orgName, Long userId, String userName, Long partyOrgId, String partyOrgName, Long unitOrgId, String unitOrgName, Integer totalPrice, Integer logistics, Integer donation, Integer donationCount, Integer calculateDonation, Integer calculateMoney, Date tradingTime) {
        this.token = token;
        this.info = info;
        this.channel = channel;
        this.openid = openid;
        this.orderId = orderId;
        this.oid = oid;
        this.orgId = orgId;
        this.orgName = orgName;
        this.userId = userId;
        this.userName = userName;
        this.partyOrgId = partyOrgId;
        this.partyOrgName = partyOrgName;
        this.unitOrgId = unitOrgId;
        this.unitOrgName = unitOrgName;
        this.totalPrice = totalPrice;
        this.logistics = logistics;
        this.donation = donation;
        this.donationCount = donationCount;
        this.calculateDonation = calculateDonation;
        this.calculateMoney = calculateMoney;
        this.tradingTime = tradingTime;
    }
}
