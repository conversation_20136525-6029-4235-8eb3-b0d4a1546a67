package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * UserThirdEntity
 *
 * <AUTHOR>
 * @date 2018-05-23
 */
@Data
@ApiModel
@Table(name = "t_user_third")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserThirdEntity {

    @ApiModelProperty("ID")
    @Id
    @Column(name = "third_id")
    @JsonProperty("third_id")
    private Long thirdId;

    @ApiModelProperty("用户ID")
    @Column(name = "user_id")
    @JsonProperty("user_id")
    private Long userId;

    @ApiModelProperty("单位或组织ID")
    @Column(name = "oid")
    @JsonProperty("oid")
    private Long oId;

    @ApiModelProperty("第三方登录类型")
    @Column(name = "th_type")
    @JsonProperty("th_type")
    private Integer thType;

    @ApiModelProperty("用户token/openId")
    @Column(name = "th_token")
    @JsonProperty("th_token")
    private String thToken;

    @ApiModelProperty("头像")
    @Column(name = "head")
    @JsonProperty("head")
    private String head;

    @ApiModelProperty("昵称")
    @Column(name = "nick_name")
    @JsonProperty("nick_name")
    private String nickName;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "create_time")
    @JsonProperty("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @Column(name = "update_time")
    @JsonProperty("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
