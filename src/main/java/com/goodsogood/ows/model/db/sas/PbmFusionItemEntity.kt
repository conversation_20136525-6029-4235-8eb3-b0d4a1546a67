package com.goodsogood.ows.model.db.sas

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModelProperty
import javax.persistence.Column
import javax.persistence.GeneratedValue
import javax.persistence.Id
import javax.persistence.Table

/**
 * 党业融合 - 组织融合度
 * <AUTHOR>
 * @Description PbmEntities
 */
@Table(name = "t_pbm_fusion_item")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class PbmFusionItemEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "fusion_item_id")
    var fusionItemId: Long? = null,

    @Column(name = "region_id")
    var regionId: Long? = null,

    @Column(name = "category")
    @ApiModelProperty("类型 1-目标融合，2-组织融合，3-工作融合，4-数据融合")
    var category: Int? = null,

    @Column(name = "name")
    @ApiModelProperty("指标名称")
    var name: String? = null,

    @Column(name = "description")
    @ApiModelProperty("说明")
    var description: String? = null,

    @Column(name = "base_score")
    @ApiModelProperty("基础值")
    var baseScore: Double = 0.0,

    @Column(name = "cycle")
    @ApiModelProperty("考核周期 1-周，2-月度，3-季度，4-年度，5-累计, 6-半年")
    var cycle: Int? = null,

    @Column(name = "strategy_name")
    @ApiModelProperty("工厂类-策略名")
    var strategyName: String? = null,

    @Column(name = "rule")
    @ApiModelProperty("指标计算规则")
    var rule: String? = null,

    @Column(name = "remark")
    @ApiModelProperty("备注")
    var remark: String? = null,

    @Transient
    var score : Double? =0.00,

    @Transient
    var result : String? = null

) {
    override fun toString(): String {
        return "PbmFusionItemEntity(fusionItemId=$fusionItemId, regionId=$regionId, category=$category, name=$name, description=$description, baseScore=$baseScore, cycle=$cycle, strategyName=$strategyName, rule=$rule, remark=$remark)"
    }
}

/**
 * 党业融合 - 组织融合度
 * <AUTHOR>
 * @Description PbmEntities
 */
@Table(name = "t_pbm_fusion_item")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class PbmFusionItemVO @JvmOverloads constructor(

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "fusion_item_id")
    var fusionItemId: Long? = null,

    @Column(name = "category")
    @ApiModelProperty("类型 1-目标融合，2-组织融合，3-工作融合，4-数据融合")
    var category: String? = null,

    @Column(name = "name")
    @ApiModelProperty("指标名称")
    var name: String? = null,

    @Column(name = "description")
    @ApiModelProperty("说明")
    var description: String? = null,

    @Column(name = "base_score")
    @ApiModelProperty("基础值")
    var baseScore: Double = 0.0,

    @Column(name = "cycle")
    @ApiModelProperty("考核周期 1-周，2-月度，3-季度，4-年度，5-累计, 6-半年")
    var cycle: String? = null,

    @Column(name = "rule")
    @ApiModelProperty("指标计算规则")
    var rule: String? = null,

    @Column(name = "remark")
    @ApiModelProperty("备注")
    var remark: String? = null

) {
    override fun toString(): String {
        return "PbmFusionItemVO(fusionItemId=$fusionItemId, category=$category, name=$name, description=$description, baseScore=$baseScore, cycle=$cycle, rule=$rule, remark=$remark)"
    }
}
