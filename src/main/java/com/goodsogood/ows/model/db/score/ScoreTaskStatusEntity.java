package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 积分任务状态表
 * t_score_task_status 实体类
 * <AUTHOR>
*/
@Data
@ApiModel
@Table(name = "t_score_task_status")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScoreTaskStatusEntity {

	@Id
	@ApiModelProperty(name = "id")
	@JsonProperty(value = "task_status_id")
	@Column(name = "task_status_id")
	private Long taskStatusId;

	@ApiModelProperty("积分任务配置编号")
	@JsonProperty(value = "task_info_id")
	@Column(name = "task_info_id")
	private Long taskInfoId;

	/**
	 * 完成任务对象编号  如果是个人积分任务就是用户编号，组织积分任务就是对应的组织编号
	 */
	@ApiModelProperty("完成任务对象编号")
	@JsonProperty(value = "object_id")
	@Column(name = "object_id")
	private Long objectId;


	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private LocalDateTime createTime;

}

