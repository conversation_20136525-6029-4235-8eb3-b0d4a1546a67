package com.goodsogood.ows.model.db.supervise;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_supervise_record 实体类
 *
 * 一键督办记录表 
 *
 * <AUTHOR>
 * @create 2021-12-24 16:16
*/
@Data
@ApiModel
@Table(name = "t_supervise_record")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SuperviseRecordEntity {
	
	@Id
	@JsonProperty(value = "supervise_record_id")
	@Column(name = "supervise_record_id")
	private Long superviseRecordId;
	
	
	@ApiModelProperty("用户id")
	@JsonProperty(value = "user_id")
	@Column(name = "user_id")
	private Long userId;


    @ApiModelProperty("用户id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;
	
	
	@ApiModelProperty("记录类型 1.一键督办")
	@Column(name = "type")
	private Integer type;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("更新时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;

    @ApiModelProperty("发送记录")
    @JsonProperty(value = "remark")
    @Column(name = "remark")
    private String remark;
	
}

