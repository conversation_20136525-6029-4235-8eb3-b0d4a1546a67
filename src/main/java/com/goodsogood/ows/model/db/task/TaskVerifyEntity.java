package com.goodsogood.ows.model.db.task;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "t_task_verify")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TaskVerifyEntity {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "task_verify_id")
    @ApiModelProperty("主键")
    private Long taskVerifyId;

    /**
     * 任务id
     */
    @Column(name = "task_id")
    @ApiModelProperty("任务id")
    private Long taskId;

    /**
     * 审核组织id
     */
    @Column(name = "org_id")
    @ApiModelProperty("审核组织id")
    private Long orgId;

    /**
     * 审核用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty("审核用户id")
    private Long userId;

    /**
     * 审核用户名
     */
    @Column(name = "username")
    @ApiModelProperty("审核用户名")
    private String username;

    /**
     * 派发时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("派发时间")
    private Date createTime;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty("创建人")
    private Long createUser;
}
