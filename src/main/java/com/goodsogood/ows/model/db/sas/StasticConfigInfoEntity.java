package com.goodsogood.ows.model.db.sas;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 党务看板统计设置信息
 *
 * <AUTHOR>
 * @date 2019/4/19 11:12
 */
@Data
@Table(name = "t_statistical_config_info")
public class StasticConfigInfoEntity {

    @Id
    @ApiModelProperty(value = "设置信息记录ID")
    @Column(name = "config_info_id")
    @JsonProperty("config_info_id")
    @GeneratedValue(generator = "JDBC")
    private Long configInfoId;

    @ApiModelProperty(value = "区县Id")
    @Column(name = "region_id")
    @JsonProperty("region_id")
    @GeneratedValue(generator = "JDBC")
    private Long regionId;

    @ApiModelProperty(value = "统计类型1.党支部组织生活统计 2.领导干部双重组织生活统计 3.党费交纳完成情况统计")
    @Column(name = "statistical_type")
    @JsonProperty("statistical_type")
    private Integer statisticalType;

    @ApiModelProperty(value = "显示组织类型(存入的组织类型类型id,多个以逗号分隔）")
    @Column(name = "show_organization_types")
    @JsonProperty("show_organization_types")
    private String showOrganizationTypes;

    @ApiModelProperty(value = "全部组织类型(存入的组织类型类型id,多个以逗号分隔）")
    @Column(name = "all_organization_types")
    @JsonProperty("all_organization_types")
    private String allOrganizationTypes;

    @ApiModelProperty(value = "显示活动类型(存入的活动类型类型id,多个以逗号分隔）")
    @Column(name = "show_activity_types")
    @JsonProperty("show_activity_types")
    private String showActivityTypes;

    @ApiModelProperty(value = "全部活动类型(存入的活动类型类型id,多个以逗号分隔）")
    @Column(name = "all_activity_types")
    @JsonProperty("all_activity_types")
    private String allActivityTypes;

    @ApiModelProperty(value = "最后修改人")
    @Column(name = "last_change_user")
    private Long lastChangeUser;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @Column(name = "update_time")
    private Date updateTime;




}
