package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
* @Author: tc
* @Description 积分订单商品数据表
* @Date 20:16 2018/9/20
*/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_score_order_commodity")
public class ScoreOrderCommodityEntity extends ScoreOrderCommoditySupperEntity{
     @Id
     @Column(name = "order_commodity_id")
     @ApiModelProperty(name = "主键编号")
     private Long orderCommodityId;

     @Column(name = "order_id")
     @ApiModelProperty(name = "积分订单表id")
     private Long orderId;
}
