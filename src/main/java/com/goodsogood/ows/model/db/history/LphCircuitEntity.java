package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_circuit 实体类
 *
 * 线路总表 
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_circuit")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphCircuitEntity  extends  BaseEntity{
	
//	@Id
	@JsonProperty(value = "circuit_id")
	@Column(name = "circuit_id")
	private Long circuitId;
	
	
	@ApiModelProperty("区县id")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Integer regionId;
	
	
	@ApiModelProperty("线路 名称")
	@Column(name = "name")
	private String name;
	
	
	@ApiModelProperty("0：支线(默认) 1：主线")
	@Column(name = "type")
	private Byte type;
	
	
	@JsonProperty(value = "img_url")
	@Column(name = "img_url")
	private String imgUrl;
	
	
	@Column(name = "content")
	private String content;
	
	
	@ApiModelProperty("闯关值名称")
	@JsonProperty(value = "value_name")
	@Column(name = "value_name")
	private String valueName;
	
	
	@ApiModelProperty("闯关值图片url")
	@JsonProperty(value = "value_url")
	@Column(name = "value_url")
	private String valueUrl;
	
	
	@ApiModelProperty("线路关卡数")
	@JsonProperty(value = "line_num")
	@Column(name = "line_num")
	private Integer lineNum;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
}

