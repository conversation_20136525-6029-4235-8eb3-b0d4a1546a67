package com.goodsogood.ows.model.db.eval;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * t_eval_count_year 实体类
 * <p>
 * 扣分统计 年
 *
 * <AUTHOR>
 * @create 2019-03-11 10:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Table(name = "t_eval_count_year")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EvalCountYearEntity extends EvalCountBaseInfo {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "count_year_id")
    @Column(name = "count_year_id")
    private Long countYearId;

}

