package com.goodsogood.ows.model.db.supervise;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_supervise_extend 实体类
 *
 * <AUTHOR>
 * @create 2021-10-13 17:24
*/
@ApiModel
@Table(name = "t_supervise_extend")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SuperviseExtendEntity {
	
	@Id
	@JsonProperty(value = "supervise_person_id")
	@Column(name = "supervise_person_id")
	private Long supervisePersonId;
	
	
	@ApiModelProperty("组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;


    @ApiModelProperty("组织id")
    @JsonProperty(value = "sub_org_id")
    @Column(name = "sub_org_id")
    private Long subOrgId;

    @ApiModelProperty("组织书记,多个逗号分隔")
    @JsonProperty(value = "org_secretary_id")
    @Column(name = "org_secretary_id")
    private String orgSecretaryId;

    @ApiModelProperty("组织书记")
    @JsonProperty(value = "org_secretary")
    @Column(name = "org_secretary")
    private String orgSecretary;


    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;

    @ApiModelProperty("详细内容")
    @JsonProperty(value = "detail_content")
    @Column(name = "detail_content")
    private String detailContent;

    @ApiModelProperty("组织层级关系")
    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    private String orgLevel;
	
	@ApiModelProperty("区县id")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Long regionId;
	
	
	@ApiModelProperty("用户id")
	@JsonProperty(value = "user_id")
	@Column(name = "user_id")
	private Long userId;
	
	
	@ApiModelProperty("用户姓名")
	@JsonProperty(value = "user_name")
	@Column(name = "user_name")
	private String userName;
	
	
	@ApiModelProperty("手机号（脱敏)")
	@JsonProperty(value = "user_phone")
	@Column(name = "user_phone")
	private String userPhone;
	
	
	@ApiModelProperty("属于那个选项的详情")
	@JsonProperty(value = "option_key")
	@Column(name = "option_key")
	private String optionKey;
	
	
	@ApiModelProperty("监督预警项名称")
	@JsonProperty(value = "option_name")
	@Column(name = "option_name")
	private String optionName;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("修改时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;

	public Long getSupervisePersonId() {
		return supervisePersonId;
	}

	public void setSupervisePersonId(Long supervisePersonId) {
		this.supervisePersonId = supervisePersonId;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Long getSubOrgId() {
		return subOrgId;
	}

	public void setSubOrgId(Long subOrgId) {
		this.subOrgId = subOrgId;
	}

	public String getOrgSecretaryId() {
		return orgSecretaryId;
	}

	public void setOrgSecretaryId(String orgSecretaryId) {
		this.orgSecretaryId = orgSecretaryId;
	}

	public String getOrgSecretary() {
		return orgSecretary;
	}

	public void setOrgSecretary(String orgSecretary) {
		this.orgSecretary = orgSecretary;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getDetailContent() {
		return detailContent;
	}

	public void setDetailContent(String detailContent) {
		this.detailContent = detailContent;
	}

	public String getOrgLevel() {
		return orgLevel;
	}

	public void setOrgLevel(String orgLevel) {
		this.orgLevel = orgLevel;
	}

	public Long getRegionId() {
		return regionId;
	}

	public void setRegionId(Long regionId) {
		this.regionId = regionId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public String getOptionKey() {
		return optionKey;
	}

	public void setOptionKey(String optionKey) {
		this.optionKey = optionKey;
	}

	public String getOptionName() {
		return optionName;
	}

	public void setOptionName(String optionName) {
		this.optionName = optionName;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
}

