package com.goodsogood.ows.model.db.tbcFusion;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * @Author: mengting
 * @Date: 2022/5/25 16:08
 */
@Data
@ApiModel
@Table(name = "t_party_person_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PartyPersonInfoEntity {
    @ApiModelProperty("营销的uuid")
    @Id
    private String personUuid;


    @ApiModelProperty("营销的姓名")
    private String personName;


    @ApiModelProperty("营销的电话")
    private String masterMobile;


    @ApiModelProperty("营销的机构")
    private String orgUuid;


    @ApiModelProperty("对应党建系统的userid")
    private Long userId;


    @ApiModelProperty("每日更新")
    private LocalDateTime ds;


    private LocalDateTime createTime;

    private String phoneSecret;


}
