package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_user_basic_info 实体类
 *
 * 用户基本信息 
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_user_basic_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphUserBasicInfoEntity  extends  BaseEntity{
	
//	@Id
	@ApiModelProperty("主键")
	@JsonProperty(value = "basic_info_id")
	@Column(name = "basic_info_id")
	private Long basicInfoId;
	
	
	@ApiModelProperty("小程序open_id")
	@JsonProperty(value = "open_id")
	@Column(name = "open_id")
	private String openId;
	
	
	@ApiModelProperty("姓名")
	@Column(name = "name")
	private String name;
	
	
	@ApiModelProperty("电话")
	@Column(name = "phone")
	private String phone;
	
	
	@ApiModelProperty("收货地址")
	@JsonProperty(value = "shipping_address")
	@Column(name = "shipping_address")
	private String shippingAddress;
	
	
	@ApiModelProperty("组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
}

