package com.goodsogood.ows.model.db.ppmd;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *   组织层级信息表实体类
 *  tc 2019.01.22
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_ppmd_org_level_info")
public class OrgLevelInfoEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "org_level_info_id")
    @Column(name = "org_level_info_id")
    @ApiModelProperty(name = "主键编号")
    private Long orgLevelInfoId;

    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    @ApiModelProperty(name = "组织编号")
    private Long orgId;

    /**
     * 组织的上级集合，逗号间隔(不包含自己、所属党委(工委直接下级)、工委及以上的组织编号)
     */
    @JsonProperty(value = "parent_list")
    @Column(name = "parent_list")
    @ApiModelProperty(name = "上级集合")
    private String parentList;

    /**
     * 组织的下级集合，逗号间隔(不包含自己)
     */
    @JsonProperty(value = "child_list")
    @Column(name = "child_list")
    @ApiModelProperty(name = "下级集合")
    private String childList;

    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    @ApiModelProperty(name = "创建时间")
    private Date createTime;

}
