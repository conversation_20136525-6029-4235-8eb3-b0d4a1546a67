package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
public class VideoAndPictureVO {
    /**
     * 是否显示 1是0否
     */
    @JsonProperty(value = "is_show")
    private Integer isShow;
    /**
     * 视频或图片列表
     */
    private List<VideoAndPictureBase> base;

    public Integer getIsShow() {
        return isShow;
    }

    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }

    public List<VideoAndPictureBase> getBase() {
        return base;
    }

    public void setBase(List<VideoAndPictureBase> base) {
        this.base = base;
    }
}
