package com.goodsogood.ows.model.db.sas;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_statistical_org_life_view 实体类
 *
 * 组织生活一浏览表 
 *
 * <AUTHOR>
 * @create 2019-08-01 10:13
*/
@Data
@ApiModel
@Table(name = "t_statistical_org_life_view")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatisticalOrgLifeViewEntity {

	public static final long TYPE_DUAL_ORG_LIFE = -1L;

	public static final String TYPE_DUAL_ORG_LIFE_NAME = "双重组织生活";

	@Id
	@GeneratedValue(generator = "JDBC")
	@ApiModelProperty("id")
	@JsonProperty(value = "org_life_view_id")
	@Column(name = "org_life_view_id")
	private Long orgLifeViewId;


	@ApiModelProperty("组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;
	
	
	@ApiModelProperty("组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;


	@ApiModelProperty("组织排序权重")
	@JsonProperty(value = "org_seq")
	@Column(name = "org_seq")
	private Integer orgSeq;


	@ApiModelProperty("活动类型id。当该组织当月没有其它类型的统计数据时为 -1 双重组织生活")
	@JsonProperty(value = "type_id")
	@Column(name = "type_id")
	private Long typeId;
	
	
	@ApiModelProperty("类型名称")
	@JsonProperty(value = "type_name")
	@Column(name = "type_name")
	private String typeName;
	
	
	@ApiModelProperty("举办次数")
	@JsonProperty(value = "participate_num")
	@Column(name = "participate_num")
	private Integer participateNum;
	
	
	@ApiModelProperty("统计的年")
	@JsonProperty(value = "statistical_year")
	@Column(name = "statistical_year")
	private Integer statisticalYear;
	
	
	@ApiModelProperty("统计的月（1-12）")
	@JsonProperty(value = "statistical_month")
	@Column(name = "statistical_month")
	private Integer statisticalMonth;


	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("更新时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
}

