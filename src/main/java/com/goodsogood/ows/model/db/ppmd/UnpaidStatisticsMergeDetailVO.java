package com.goodsogood.ows.model.db.ppmd;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.goodsogood.ows.helper.Json.DoubleKeepTwoSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @describe 党费未交详情VO
 * @date 2018-12-21
 */
@Data
@ApiModel(value = "党费未交合并VO")
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class UnpaidStatisticsMergeDetailVO<T> {

    @JsonProperty(value = "list")
    @ApiModelProperty("返回数组")
    private List<T> list;

    /**
     * 上一次更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty(value = "update_time")
    private Date updateTime;

    @JsonProperty(value = "type")
    @ApiModelProperty("查询返回类型 1:工委 2:党委 3:支部")
    private Integer type;

    /**
     * 欠交详情
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Accessors(chain = true)
    public static class UnpaidUser {

        /**
         * 当月标准
         */
        @JsonProperty(value = "standard")
        @JsonSerialize(using = DoubleKeepTwoSerializer.class)
        private Double standard;

        /**
         * 组织id
         */
        @JsonProperty(value = "org_id")
        private Long orgId;

        /**
         * 组织名称
         */
        @JsonProperty(value = "org_name")
        private String orgName;

        /**
         * 交纳至月份
         */
        @JsonProperty(value = "pay_for")
        private String payFor;

        /**
         * 用户名称
         */
        @JsonProperty(value = "user_name")
        private String userName;

        /**
         * 用户id
         */
        @JsonProperty(value = "user_id")
        private Long userId;

        /**
         * 欠交月份月数
         */
        @JsonProperty(value = "un_paid_months")
        private Integer unPaidMonths;

        @JsonProperty(value = "cert_number_secret")
        private String certNumberSecret;

        /**
         * 总欠交金额（元）
         */
        @JsonProperty(value = "un_paid_total")
        @JsonSerialize(using = DoubleKeepTwoSerializer.class)
        private Double unPaidTotal;
    }
}
