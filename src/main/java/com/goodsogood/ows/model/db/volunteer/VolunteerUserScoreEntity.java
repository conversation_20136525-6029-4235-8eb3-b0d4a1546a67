package com.goodsogood.ows.model.db.volunteer;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/13
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_volunteer_user_score")
public class VolunteerUserScoreEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "score_id")
    @Column(name = "score_id")
    @ApiModelProperty(name = "主键")
    private Long scoreId;

    @JsonProperty(value = "recruit_id")
    @Column(name = "recruit_id")
    @ApiModelProperty(name = "招募id")
    private Long recruitId;

    @JsonProperty(value = "volunteer_user_id")
    @Column(name = "volunteer_user_id")
    @ApiModelProperty(name = "志愿者id")
    private Long volunteerUserId;

    @JsonProperty(value = "time_id")
    @Column(name = "time_id")
    @ApiModelProperty(name = "计时id")
    private Long timeId;

    @ApiModelProperty(name = "积分")
    private Long score;

    @JsonProperty(value = "get_way")
    @Column(name = "get_way")
    @ApiModelProperty(name = "获取方式 0-参与积分，1-时长积分")
    private Integer getWay;

    @ApiModelProperty(name = "状态 0-未结算，1-已结算")
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    @ApiModelProperty(name = "创建时间")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @JsonProperty(value = "settlement_time")
    @Column(name = "settlement_time")
    @ApiModelProperty(name = "结算时间")
    private Date settlementTime;

    @JsonProperty(value = "project_id")
    @Column(name = "project_id")
    @ApiModelProperty(name = "志愿项目id")
    private Long projectId;

    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    @ApiModelProperty(name = "党建系统用户id")
    private Long userId;

    @JsonProperty(value = "volunteer_team_id")
    @Column(name = "volunteer_team_id")
    @ApiModelProperty(name = "项目所属志愿团队id")
    private Long volunteerTeamId;

    @ApiModelProperty("区县id(冗余。默认3)")
    @JsonProperty(value = "region_id")
    @Column(name = "region_id")
    private Long regionId;
}
