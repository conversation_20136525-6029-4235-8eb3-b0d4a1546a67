package com.goodsogood.ows.model.db.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * dss操作历史表
 * <AUTHOR>
 */
@Data
@ApiModel
@Table(name = "t_dss_history")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DssHistoryEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("主键")
    @JsonProperty("dss_history_id")
    @Column(name = "dss_history_id")
    private Long dssHistoryId;

    @ApiModelProperty(value = "操作人用户ID")
    @Column(name = "user_id")
    @JsonProperty("user_id")
    private Long userId;

    @ApiModelProperty(value = "操作时间")
    @Column(name = "time")
    @JsonProperty("time")
    private Date time;

    @ApiModelProperty(value = "操作人IP地址")
    @Column(name = "ip")
    @JsonProperty("ip")
    private String ip;

    @ApiModelProperty(value = "区县Id")
    @Column(name = "region_id")
    @JsonProperty("region_id")
    private Long regionId;

    @ApiModelProperty(value = "操作类型 1-生成数据，2-下载文件")
    @Column(name = "type")
    @JsonProperty("type")
    private Integer type;

    @ApiModelProperty(value = "操作类型 1-生成数据，2-下载文件")
    @Column(name = "url")
    @JsonProperty("url")
    private String url;
}
