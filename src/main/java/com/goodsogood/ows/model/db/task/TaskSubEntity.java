package com.goodsogood.ows.model.db.task;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 任务子表
 * @date 2021/09/13
 */
@Data
@ApiModel
@Table(name = "t_task_sub")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskSubEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 任务id-主键
     */
    @Id
    @ApiModelProperty("任务id-主键")
    private Long taskId;

    /**
     * 任务描述
     */
    @ApiModelProperty("任务描述")
    @Column(name = "description")
    private String description;

    /**
     * 任务积分
     */
    @ApiModelProperty("任务积分")
    @Column(name = "score")
    private Integer score;

    /**
     * 是否考核任务 1-是 0-否
     */
    @ApiModelProperty("是否考核任务 1-是 0-否")
    @Column(name = "is_examine")
    private Integer isExamine;

    /**
     * 考核类型 1-填报任务
     */
    @ApiModelProperty("考核类型 1-填报任务")
    @Column(name = "examine_type")
    private Integer examineType;

    /**
     * 接收范围 1组织 2人员
     */
    @ApiModelProperty("接收范围 1组织 2人员")
    @Column(name = "access_range")
    private Integer accessRange;

    /**
     * 审核者-考核组织id
     */
    @ApiModelProperty("审核者-考核组织id")
    @Column(name = "examine_id")
    private Long examineId;

    /**
     * 是否审核
     */
    @ApiModelProperty("是否审核。0：否，1：是")
    @Column(name = "is_verify")
    private Integer isVerify;

    /**
     * 任务周期 0-不重复 1-每天 2-每周 3-每月 4-每季度 5-每半年 6-每年
     */
    @ApiModelProperty("任务周期 0-不重复 1-每天 2-每周 3-每月 4-每季度 5-每半年 6-每年")
    @Column(name = "cycle")
    private Integer cycle;

    /**
     * 月份
     */
    @ApiModelProperty("月份")
    @Column(name = "month")
    private String month;

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    @Column(name = "day")
    private String day;

    /**
     * cron表达式
     */
    @ApiModelProperty("cron表达式")
    @Column(name = "cron")
    private String cron;

    /**
     * 短信通知 1-是 0-否
     */
    @ApiModelProperty("短信通知 1-是 0-否")
    @Column(name = "is_short_note_type")
    private Integer isShortNoteType;

    /**
     * 短信提醒 1-是 0-否
     */
    @ApiModelProperty("短信提醒 1-是 0-否")
    @Column(name = "is_SMS_reminder_type")
    private Integer isSMSReminderType;

    /**
     * 钉钉通知 1-是 0-否
     */
    @ApiModelProperty("钉钉通知 1-是 0-否")
    @Column(name = "ding_notify")
    private Integer dingNotify;

    /**
     * 钉钉提醒 1-提醒 0-不提醒
     */
    @ApiModelProperty("钉钉提醒 1-提醒 0-不提醒")
    @Column(name = "ding_remind")
    private Integer dingRemind;

    /**
     * 是否开启重复提醒 1: 开启 0: 不开启
     */
    @ApiModelProperty("是否开启重复提醒 1: 开启 0: 不开启")
    @Column(name = "repeat_remind_type")
    private Integer repeatRemindType;

    /**
     * 重复提醒周期单位 1:天 2:周 3:月
     */
    @ApiModelProperty("重复提醒周期单位 1:天 2:周 3:月")
    @Column(name = "remind_type")
    private Integer remindType;

    /**
     * 0:无提醒 1:开始时 2:5分钟前 3:15分钟前 4:30分钟前 5:一小时前 6:一天前 7:自定义
     */
    @ApiModelProperty("0:无提醒 1:开始时 2:5分钟前 3:15分钟前 4:30分钟前 5:一小时前 6:一天前 7:自定义")
    @Column(name = "ding_remind_type")
    private Integer dingRemindType;

    /**
     * 自定义提醒时间 ding_remind ==1 时
     */
    @ApiModelProperty("自定义提醒时间 ding_remind ==1 时")
    @Column(name = "custom_remind_time")
    private Date customRemindTime;

    /**
     * 设置重复提醒后的 cron
     */
    @ApiModelProperty("设置重复提醒后的 cron")
    @Column(name = "repetition_remind_cron")
    private String repetitionRemindCron;

    /**
     * 是否需要回调 1-是 0-否
     */
    @ApiModelProperty("是否需要回调 1-是 0-否")
    @Column(name = "need_callback")
    private Integer needCallback;

    /**
     * 是否定时催办 1:是  0:否
     */
    @ApiModelProperty("是否定时催办 1:是  0:否")
    @Column(name = "speed_up_type")
    private Integer speedUpType;

    /**
     * 定时催办时间
     */
    @ApiModelProperty("定时催办时间")
    @Column(name = "speed_up_time")
    private Date speedUpTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 派发时间
     */
    @ApiModelProperty("派发时间")
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人")
    @Column(name = "last_change_user")
    private Long lastChangeUser;

}
