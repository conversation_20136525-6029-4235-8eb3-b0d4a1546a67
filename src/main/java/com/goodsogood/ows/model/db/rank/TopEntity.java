package com.goodsogood.ows.model.db.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Create by FuXiao on 2020/11/2
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_rank_top")
public class TopEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "top_id")
    @Column(name = "top_id")
    @ApiModelProperty(name = "类型id")
    private Long topId;

    @JsonProperty(value = "top_name")
    @Column(name = "top_name")
    @ApiModelProperty(name = "类型名称")
    private String topName;

    @JsonProperty(value = "type")
    @Column(name = "type")
    @ApiModelProperty(name = "1人员 2组织")
    private Integer type;

    @JsonProperty(value = "percentage")
    @Column(name = "percentage")
    @ApiModelProperty(name = "计算系数 例如30%用0.3表示")
    private Double percentage;
}
