package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@ApiModel
@Table(name = "t_menu_route")
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MenuRouteEntity {

    @Id
    @ApiModelProperty("menu_route_id")
    private Long menuRouteId;

    @ApiModelProperty("menu_id")
    private String menuId;

    @ApiModelProperty("name")
    private String name;

    @ApiModelProperty("menu_route")
    private String menuRoute;
}
