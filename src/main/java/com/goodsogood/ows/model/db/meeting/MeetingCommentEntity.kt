package com.goodsogood.ows.model.db.meeting

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.time.LocalDateTime
import java.util.Date
import javax.persistence.*

/**
 * 民主评议主表
 */
@ApiModel
@Table(name = "t_meeting_comment")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingCommentEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("民主评议主键ID")
    @JsonProperty(value = "comment_id")
    @Column(name = "comment_id")
    var commentId: Long? = null

    @ApiModelProperty("年度")
    var year: Int? = null

    @ApiModelProperty("组织ID")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    var orgId: Long? = null

    @ApiModelProperty("组织父级ID")
    @JsonProperty(value = "parent_id")
    @Column(name = "parent_id")
    var parentId: Long? = null

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    var orgName: String? = null

    @ApiModelProperty("组织层级关系")
    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    var orgLevel: String? = null

    @ApiModelProperty("优秀党员数量")
    @JsonProperty(value = "excellent_num")
    @Column(name = "excellent_num")
    var excellentNum: Int = 0

    @ApiModelProperty("区县ID")
    @JsonProperty(value = "region_id")
    @Column(name = "region_id")
    var regionId: Long? = null

    @ApiModelProperty("状态: 0-未开启，1-已开启，2-待审查，3-审查未通过，4-待审定，5-审定未通过，6-审定通过")
    @Column(name = "status")
    var status: Int = 0

    @Transient
    @ApiModelProperty("操作: 0-详情，1-审查，2-审定, 3-详情+修订")
    var operate: Int = 0

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    var createTime: LocalDateTime? = null

    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    var updateTime: LocalDateTime? = null

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    var createUser: Long? = null

    @ApiModelProperty("最后修改人")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    var lastChangeUser: Long? = null
}

/**
 * 民主评议审批表
 */
@ApiModel
@Table(name = "t_meeting_comment_approve")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingCommentApproveEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("民主评议审批主键ID")
    @JsonProperty(value = "comment_approve_id")
    @Column(name = "comment_approve_id")
    var commentApproveId: Long? = null

    @ApiModelProperty("民主评议主键ID")
    @JsonProperty(value = "comment_id")
    @Column(name = "comment_id")
    var commentId: Long? = null

    @ApiModelProperty("审批状态 1-审查通过，2-审查未通过，3-审定通过，4-审定未通过")
    @JsonProperty(value = "approve_status")
    @Column(name = "approve_status")
    var approveStatus: Int? = null

    @ApiModelProperty("审批人")
    @JsonProperty(value = "approve_user")
    @Column(name = "approve_user")
    var approveUser: Long? = null

    @ApiModelProperty("审批建议")
    @JsonProperty(value = "content")
    @Column(name = "content")
    var content: String? = null

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    var createTime: LocalDateTime? = null

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    var createUser: Long? = null
}

/**
 * 民主评议党员表
 */
@ApiModel
@Table(name = "t_meeting_comment_member")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingCommentMemberEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("民主评议党员主键ID")
    @JsonProperty(value = "comment_member_id")
    @Column(name = "comment_member_id")
    var commentMemberId: Long? = null

    @ApiModelProperty("民主评议主键ID")
    @JsonProperty(value = "comment_id")
    @Column(name = "comment_id")
    var commentId: Long? = null

    @ApiModelProperty("年度")
    @Column(name = "year")
    var year: Int? = null

    @ApiModelProperty("用户ID")
    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    var userId: Long? = null

    @ApiModelProperty("用户姓名")
    @JsonProperty(value = "user_name")
    @Column(name = "user_name")
    var userName: String? = null

    @ApiModelProperty("脱敏手机号")
    @JsonProperty(value = "phone")
    @Column(name = "phone")
    var phone: String? = null

    @ApiModelProperty("手机号密文")
    @JsonProperty(value = "phone_secret")
    @Column(name = "phone_secret")
    var phoneSecret: String? = null

    @ApiModelProperty("政治面貌")
    @JsonProperty(value = "political_type")
    @Column(name = "political_type")
    var politicalType: Int? = null

    @ApiModelProperty("组织ID")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    var orgId: Long? = null

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    var orgName: String? = null

    @ApiModelProperty("自评等级 1-优秀，2-合格，3-基本合格，4-不合格")
    @JsonProperty(value = "self_rating")
    @Column(name = "self_rating")
    var selfRating: Int? = null

    @ApiModelProperty("自评内容")
    @JsonProperty(value = "self_content")
    @Column(name = "self_content")
    var selfContent: String? = null

    @ApiModelProperty("草稿状态 0-草稿，1-正常")
    @JsonProperty(value = "is_draft")
    @Column(name = "is_draft")
    var isDraft: Int? = null

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    var createTime: LocalDateTime? = null

    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    var updateTime: LocalDateTime? = null

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    var createUser: Long? = null

    @ApiModelProperty("最后修改人")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    var lastChangeUser: Long? = null
}


/**
 * 互评表
 */
@ApiModel
@Table(name = "t_meeting_comment_member_appraisal")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingCommentMemberAppraisalEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("民主评议互评主键ID")
    @JsonProperty(value = "comment_member_appraisal_id")
    @Column(name = "comment_member_appraisal_id")
    var commentMemberAppraisalId: Long? = null

    @ApiModelProperty("民主评议党员主键ID")
    @JsonProperty(value = "comment_member_id")
    @Column(name = "comment_member_id")
    var commentMemberId: Long? = null

    @ApiModelProperty("用户ID")
    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    var userId: Long? = null

    @ApiModelProperty("用户姓名")
    @JsonProperty(value = "user_name")
    @Column(name = "user_name")
    var userName: String? = null

    @ApiModelProperty("脱敏手机号")
    @JsonProperty(value = "phone")
    @Column(name = "phone")
    var phone: String? = null

    @ApiModelProperty("手机号密文")
    @JsonProperty(value = "phone_secret")
    @Column(name = "phone_secret")
    var phoneSecret: String? = null

    @ApiModelProperty("互评等级 1-优秀，2-合格，3-基本合格，4-不合格")
    @JsonProperty(value = "appraisal_rating")
    @Column(name = "appraisal_rating")
    var appraisalRating: Int? = null

    @ApiModelProperty("互评内容")
    @JsonProperty(value = "appraisal_content")
    @Column(name = "appraisal_content")
    var appraisalContent: String? = null

    @ApiModelProperty("草稿状态 0-草稿，1-正常")
    @JsonProperty(value = "is_draft")
    @Column(name = "is_draft")
    var isDraft: Int? = null

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    var createTime: LocalDateTime? = null

    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    var updateTime: LocalDateTime? = null

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    var createUser: Long? = null

    @ApiModelProperty("最后修改人")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    var lastChangeUser: Long? = null
}

/**
 * 综合评定表
 */
@ApiModel
@Table(name = "t_meeting_comment_member_complex")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingCommentMemberComplexEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("民主评议综合评定主键ID")
    @JsonProperty(value = "comment_member_complex_id")
    @Column(name = "comment_member_complex_id")
    var commentMemberComplexId: Long? = null

    @ApiModelProperty("民主评议党员主键ID")
    @JsonProperty(value = "comment_member_id")
    @Column(name = "comment_member_id")
    var commentMemberId: Long? = null

    @ApiModelProperty("综合评定等级 1-优秀，2-合格，3-基本合格，4-不合格")
    @JsonProperty(value = "complex_rating")
    @Column(name = "complex_rating")
    var complexRating: Int? = null

    @ApiModelProperty("综合评定内容")
    @JsonProperty(value = "complex_suggestion")
    @Column(name = "complex_suggestion")
    var complexSuggestion: String? = null

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    var createTime: LocalDateTime? = null

    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    var updateTime: LocalDateTime? = null

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    var createUser: Long? = null

    @ApiModelProperty("最后修改人")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    var lastChangeUser: Long? = null
}

/**
 * 民主评议统计表
 */
@ApiModel
@Table(name = "t_meeting_comment_statistics")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingCommentStatisticsEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "comment_statistics_id")
    @Column(name = "comment_statistics_id")
    var commentStatisticsId: Long? = null

    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("comment_id")
    @JsonProperty(value = "comment_id")
    @Column(name = "comment_id")
    var commentId: Long? = null

    @ApiModelProperty("党员所属组织ID")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    var orgId: Long? = null

    @ApiModelProperty("党员所属组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    var orgName: String? = null

    @ApiModelProperty("党员所属组织层级关系")
    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    var orgLevel: String? = null

    @ApiModelProperty("评议年度")
    @JsonProperty(value = "year")
    @Column(name = "year")
    var year: Int? = null

    @ApiModelProperty("党员数量")
    @JsonProperty(value = "party_number")
    @Column(name = "party_number")
    var partyNumber: Int? = null

    @ApiModelProperty("参加评议的党员数")
    @JsonProperty(value = "join_number")
    @Column(name = "join_number")
    var joinNumber: Int? = null

    @ApiModelProperty("评议等级(1-优秀、2-合格、3-基本合格、4-不合格)")
    @JsonProperty(value = "rating")
    @Column(name = "rating")
    var rating: Int? = null

    @ApiModelProperty("评议等级对应党员数")
    @JsonProperty(value = "rating_number")
    @Column(name = "rating_number")
    var ratingNumber: Int? = null

    @Column(name = "region_id")
    @JsonProperty("region_id")
    @ApiModelProperty("区县编号")
    var regionId: Long? = null

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    var createUser: Long? = null

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    var createTime: LocalDateTime? = null

    @ApiModelProperty("最后修改人")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    var lastChangeUser: Long? = null

    @ApiModelProperty("修改时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    var updateTime: LocalDateTime? = null
}

/**
 * 综合评定历史表
 */
@ApiModel
@Table(name = "t_meeting_comment_member_complex_history")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingCommentMemberComplexHistoryEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("民主评议综合评定历史主键ID")
    @JsonProperty(value = "history_id")
    @Column(name = "history_id")
    var historyId: Long? = null

    @ApiModelProperty("民主评议综合评定主键ID")
    @JsonProperty(value = "complex_id")
    @Column(name = "complex_id")
    var complexId: Long? = null

    @ApiModelProperty("综合评定等级 1-优秀，2-合格，3-基本合格，4-不合格")
    var rating: Int? = null

    @ApiModelProperty("综合评定内容")
    var suggestion: String? = null

    @ApiModelProperty("记录人")
    var recorder: Long? = null

    @ApiModelProperty("记录时间")
    @JsonProperty(value = "record_time")
    @Column(name = "record_time")
    var recordTime: LocalDateTime? = null
}
/**
 * 状态枚举类
 */
enum class CommentStatusEnum(val key: Int, val value: String) {

    UNOPENED(0, "未开启"),
    ACTIVATED(1, "已开启"),
    REVIEWING(2, "待上级组织审查"),
    REVIEW_FAILED(3, "审查未通过"),
    PENGDING(4, "待上级组织审定"),
    PEND_FAILED(5, "审定未通过"),
    APPROVED(6, "审定通过");

    companion object {
        fun getCommentStatusEnum(key: Int?): CommentStatusEnum? {
            CommentStatusEnum.values().forEach {
                if (it.key == key) {
                    return it
                }
            }
            return null
        }
    }
}

enum class CommentRatingEnum(val key: Int, val value: String) {

    EXCELLENT(1, "优秀"),
    QUALIFIED(2, "合格"),
    BASIC_QUALIFIED(3, "基本合格"),
    UNQUALIFIED(4, "不合格");

    companion object {
        fun getCommentRatingEnum(key: Int?): CommentRatingEnum? {
            CommentRatingEnum.values().forEach {
                if (it.key == key) {
                    return it
                }
            }
            return null
        }
    }
}