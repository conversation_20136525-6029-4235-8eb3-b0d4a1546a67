package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.GeneratedValue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @description 组织积分管理
 * @date 2021-12-13
 */
@Entity
@Data
@Table(name = "t_sas_score_manager_flow")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SasScoreManagerFlowEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "flow_id")
    @GeneratedValue(generator = "JDBC")
    private Long flowId;

    /**
     * region_id
     */
    @Column(name = "region_id")
    private Long regionId;

    /**
     * 根据类型区分 可能是组织id，用户id，党组id....
     */
    @Column(name = "data_id")
    private Long dataId;

    /**
     * 1:组织id 2:人员id 3:党组id
     */
    @Column(name = "data_type")
    private Integer dataType;

    /**
     * 业务数据id
     */
    @Column(name = "source_data_id")
    private Long sourceDataId;

    /**
     * 业务数据id str
     */
    @Column(name = "source_data_id_str")
    private String sourceDataIdStr;

    /**
     * 业务类型
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 类型说明
     */
    @Column(name = "type_str")
    private String typeStr;

    /**
     * 0:不需要发送 1:新增待发送 2:发送成功 3:发送失败
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 1:有效 0:无效
     */
    @Column(name = "is_del")
    private Integer isDel;

    /**
     * 该数据应该所处的时间
     */
    @Column(name = "handle_time")
    private String handleTime;

    /**
     * 调用积分中心提交的token
     */
    @Column(name = "token")
    private String token;

    /**
     * 积分
     */
    @Column(name = "score")
    private Long score;

    /**
     * 积分类型
     */
    @Column(name = "score_type")
    private Integer scoreType;

    /**
     * 0:加分 1:减分
     */
    @Column(name = "send_type")
    private Integer sendType;

    /**
     * 发送时间
     */
    @Column(name = "send_time")
    private LocalDateTime sendTime;

    /**
     * create_user
     */
    @Column(name = "create_user")
    private Long createUser;

    /**
     * create_time
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * update_user
     */
    @Column(name = "update_user")
    private Long updateUser;

    /**
     * change_time
     */
    @Column(name = "change_time")
    private LocalDateTime changeTime;

    /**
     * 冗余字段 业务审查
     */
    @Column(name = "var1")
    private String var1;

    /**
     * 冗余字段 业务审查
     */
    @Column(name = "var2")
    private String var2;

    /**
     * 冗余字段 业务审查
     */
    @Column(name = "var3")
    private String var3;

    /**
     * 冗余字段 业务审查
     */
    @Column(name = "var4")
    private String var4;

    /**
     * 冗余字段 业务审查
     */
    @Column(name = "var5")
    private String var5;

    public SasScoreManagerFlowEntity() {
    }

}