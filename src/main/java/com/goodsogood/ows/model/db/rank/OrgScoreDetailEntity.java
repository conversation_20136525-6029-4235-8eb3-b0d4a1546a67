package com.goodsogood.ows.model.db.rank;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * Create by FuXiao on 2020/10/23
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_rank_org_score_detail")
public class OrgScoreDetailEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "org_score_detail_id")
    @Column(name = "org_score_detail_id")
    @ApiModelProperty(name = "主键")
    private Long orgScoreDetailId;

    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    @ApiModelProperty(name = "组织id")
    private Long orgId;

    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    @ApiModelProperty(name = "组织名称")
    private String orgName;

    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    @ApiModelProperty(name = "组织架构")
    private String orgLevel;

    @JsonProperty(value = "score")
    @Column(name = "score")
    @ApiModelProperty(name = "得分")
    private Double score;

    @JsonProperty(value = "score_rule_id")
    @Column(name = "score_rule_id")
    @ApiModelProperty(name = "对应的规则id")
    private Long scoreRuleId;

    @JsonProperty(value = "year")
    @Column(name = "year")
    @ApiModelProperty(name = "年度")
    private Long year;

    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    @ApiModelProperty(name = "录入时间")
    private Date createTime;

    @JsonProperty(value = "create_user_id")
    @Column(name = "create_user_id")
    @ApiModelProperty(name = "录入人id")
    private Long createUserId;

    @JsonProperty(value = "create_user_name")
    @Column(name = "create_user_name")
    @ApiModelProperty(name = "录入人名称")
    private String createUserName;

    @JsonProperty(value = "top_id")
    @Column(name = "top_id")
    @ApiModelProperty(name = "类型id")
    private Long topId;
}
