package com.goodsogood.ows.model.db.volunteer;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/13
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_volunteer_user_time")
public class VolunteerUserTimeEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "time_id")
    @Column(name = "time_id")
    @ApiModelProperty(name = "主键id")
    private Long timeId;

    @JsonProperty(value = "parent_id")
    @Column(name = "parent_id")
    @ApiModelProperty(name = "父类id")
    private Long parentId;

    @JsonProperty(value = "recruit_id")
    @Column(name = "recruit_id")
    @ApiModelProperty(name = "招募id")
    private Long recruitId;

    @JsonProperty(value = "volunteer_user_id")
    @Column(name = "volunteer_user_id")
    @ApiModelProperty(name = "志愿者id")
    private Long volunteerUserId;

    @JsonProperty(value = "admin_show")
    @Column(name = "admin_show")
    @ApiModelProperty(name = "管理员是否可见 0-可见，1-不可见")
    private Integer adminShow;

    @ApiModelProperty(name = "小时")
    private Integer hour;

    @ApiModelProperty(name = "分钟")
    private Integer minute;

    @JsonProperty(value = "entry_mode")
    @Column(name = "entry_mode")
    @ApiModelProperty(name = "录入方式 0-签到录入（团队录入），1-管理员录入")
    private Integer entryMode;

    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    @ApiModelProperty(name = "创建人")
    private Long createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    @ApiModelProperty(name = "创建时间")
    private Date createTime;

    @Transient
    @JsonIgnore
    @ApiModelProperty(name = "党建系统用户id")
    private Long userId;

    @Transient
    @JsonIgnore
    @ApiModelProperty(name = "岗位id")
    private Long projectTaskId;

    @JsonProperty(value = "volunteer_team_id")
    @Column(name = "volunteer_team_id")
    @ApiModelProperty(name = "项目所属志愿团队id")
    private Long volunteerTeamId;

    @JsonProperty(value = "project_id")
    @Column(name = "project_id")
    @ApiModelProperty(name = "志愿项目id")
    private Long projectId;

    @ApiModelProperty("区县id(冗余。默认3)")
    @JsonProperty(value = "region_id")
    @Column(name = "region_id")
    private Long regionId;
}
