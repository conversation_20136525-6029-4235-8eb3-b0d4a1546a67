package com.goodsogood.ows.model.db.ecp

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import lombok.Data
import java.io.Serializable
import java.util.*
import javax.persistence.*

/**
 * @description t_ecp_org
 * <AUTHOR>
 * @date 2022-04-22
 */
@Entity
@Data
@Table(name = "t_ecp_org")
@ApiModel("t_ecp_org")
class EcpOrgEntity : Serializable {

    /**
     * 云区队伍表主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("云区组织表主键")
    @Column(name = "ecp_org_key_id")
    var ecpOrgKeyId: Long? = null

    /**
     * 云区组织id
     */
    @ApiModelProperty("云区组织id")
    @Column(name = "ecp_org_id")
    var ecpOrgId: Long? = null


    /**
     * level
     */
    @ApiModelProperty("云区组织levle")
    @Column(name = "org_level")
    var orgLevel: String? = null

    /**
     * 组织logo图片对象 或者 url
     */
    @ApiModelProperty("组织logo图片对象 或者 url")
    @Column(name = "ecp_logo")
    var ecpLogo: String? = null

    /**
     * 组织名称
     */
    @ApiModelProperty("组织名称")
    @Column(name = "ecp_org_name")
    var ecpOrgName: String? = null

    /**
     * 组织简介
     */
    @ApiModelProperty("组织简介")
    @Column(name = "ecp_content")
    var ecpContent: String? = null

    /**
     * 开放范围
     */
    @ApiModelProperty("开放范围 0:所有职工 1:组织 2:人员 3:党组")
    @Column(name = "ecp_org_range")
    var ecpOrgRange: Int? = null

    /**
     * 开放范围选择的单位json: [{unit_id:1，unit_name: xxx支部 }]
     */
    @ApiModelProperty("开放范围选择的单位json: [{unit_id:1，unit_name: xxx支部 }]")
    @Column(name = "ecp_unit_json")
    var ecpUnitJson: String? = null

    /**
     * 开放选择的标志
     */
    @ApiModelProperty("开放选择的标志 1:选择党组织 2:选择行政单位 3:选择人员 4:选择群组 5:选择党组")
    @Column(name = "ecp_open_flag")
    var ecpOpenFlag: Int? = null

    /**
     * 人员限制标志 0: 无限制 1:指定人数
     */
    @ApiModelProperty("人员限制标志 0: 无限制 1:指定人数")
    @Column(name = "ecp_num_flag")
    var ecpNumFlag: Int? = null

    /**
     * 指定人数
     */
    @ApiModelProperty("指定人数")
    @Column(name = "ecp_user_num")
    var ecpUserNum: Int? = null

    /**
     * 队伍成员数
     */
    @ApiModelProperty("队伍成员数")
    @Column(name = "ecp_org_user_num")
    var ecpOrgUserNum: Int? = 0

    /**
     * 任务数
     */
    @ApiModelProperty("任务数")
    @Column(name = "ecp_task_num")
    var ecpTaskNum: Int? = 0

    /**
     * 队伍下的帖子动态数
     */
    @ApiModelProperty("队伍下的帖子动态数")
    @Column(name = "ecp_message_num")
    var ecpMessageNum: Int? = 0

    /**
     * 0:未删除 1:已删除
     */
    @ApiModelProperty("0:未删除 1:已删除")
    @Column(name = "is_del")
    var isDel: Int? = 0

    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    @Column(name = "create_user_id")
    var createUserId: Long? = null

    /**
     * 创建用户所在组织
     */
    @ApiModelProperty("创建用户所在组织")
    @Column(name = "create_org_id")
    var createOrgId: Long? = null

    /**
     * 最后操作用户
     */
    @ApiModelProperty("最后操作用户")
    @Column(name = "last_user_id")
    var lastUserId: Long? = null

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @Column(name = "create_time")
    var createTime: Date? = null

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @Column(name = "update_time")
    var updateTime: Date? = null

    companion object {
        const val serialVersionUID = 1L
    }
}


/**
 * @description 云区任务关联表
 * <AUTHOR>
 * @date 2022-04-22
 */
@Entity
@Data
@Table(name = "t_ecp_org_task")
@ApiModel("云区任务关联表")
class EcpOrgTaskEntity : Serializable {

    /**
     * 云区任务关联表主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("云区任务关联表主键")
    @Column(name = "ecp_org_task_id")
    var ecpOrgTaskId: Long? = null

    /**
     * 任务id
     */
    @ApiModelProperty("任务id")
    @Column(name = "ecp_task_id")
    var ecpTaskId: Long? = null

    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    @Column(name = "ecp_org_id")
    var ecpOrgId: Long? = null

    /**
     * 标签id
     */
    @ApiModelProperty("标签id")
    @Column(name = "ecp_tag_id")
    var ecpTagId: Long? = null


    @ApiModelProperty("任务标题")
    @Column(name = "ecp_task_name")
    var ecpTaskName: String? = null


    @ApiModelProperty("任务级别 1-A级任务 2-B级任务 3-C级任务 4-D级任务 5-其他")
    @Column(name = "ecp_level")
    var ecpLevel: Int? = null

    @ApiModelProperty("任务状态:任务状态 1-草稿 2-进行中 3-已结束 4-已提交 5-已退回 6-已撤回 7-未通过 8-通过 9-已转派")
    @Column(name = "ecp_task_status")
    var ecpTaskStatus: Int? = null


    /**
     * 是否是主任务 0:不是 1:是
     */
    @ApiModelProperty("是否是主任务 0:不是 1:是")
    @Column(name = "is_host")
    var isHost: Int? = 0

    /**
     * 这个任务的父级任务id
     */
    @ApiModelProperty("这个任务的父级任务id")
    @Column(name = "this_host_id")
    var thisHostId: Int? = 0

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @Column(name = "create_time")
    var createTime: Date? = null

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @Column(name = "update_time")
    var updateTime: Date? = null

    companion object {
        const val serialVersionUID = 1L
    }
}


/**
 * @description 人员执行任务流水表
 * <AUTHOR>
 * @date 2022-04-22
 */
@Entity
@Data
@Table(name = "t_ecp_task_log")
@ApiModel("人员执行任务流水表")
class EcpTaskLogEntity : Serializable {

    /**
     * 云区任务流水主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("云区任务流水主键")
    @Column(name = "ecp_task_log_id")
    var ecpTaskLogId: Long? = null

    /**
     * 队伍用户关联表id
     */
    @ApiModelProperty("队伍用户关联表id")
    @Column(name = "ecp_user_id")
    var ecpUserId: Long? = null

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    @Column(name = "user_id")
    var userId: Long? = null

    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    @Column(name = "ecp_org_id")
    var ecpOrgId: Long? = null

    /**
     * 任务id
     */
    @ApiModelProperty("任务id")
    @Column(name = "ecp_task_id")
    var ecpTaskId: Long? = null

    /**
     * 任务完成方范围标志(1:人员 2:组织)
     */
    @ApiModelProperty("任务完成方范围标志(1:人员 2:组织)")
    @Column(name = "task_range_flag")
    var taskRangeFlag: Int? = null

    @ApiModelProperty("是否完成  0:完成 1:未完成")
    @JsonProperty("is_finish")
    var isFinish: Int? = null


    /**
     * 队伍成员数(rn如果是人员则为空rn如果是组织则显示他的成员数rn)
     */
    @ApiModelProperty("队伍成员数(rn如果是人员则为空rn如果是组织则显示他的成员数rn)")
    @Column(name = "ecp_org_user_num")
    var ecpOrgUserNum: Int? = null

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @Column(name = "create_time")
    var createTime: Date? = null

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @Column(name = "update_time")
    var updateTime: Date? = null

    companion object {
        const val serialVersionUID = 1L
    }
}


/**
 * @description 成员与组织关联流水表
 * <AUTHOR>
 * @date 2022-04-22
 */
@Entity
@Data
@Table(name = "t_ecp_user")
@ApiModel("成员与组织关联流水表")
class EcpUserEntity : Serializable {

    /**
     * 成员与组织关联流水主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("成员与组织关联流水主键")
    @Column(name = "ecp_user_id")
    var ecpUserId: Long? = null

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    @Column(name = "user_id")
    var userId: Long? = null

    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    @Column(name = "user_name")
    var userName: String? = null

    /**
     * 用户头像地址
     */
    @ApiModelProperty("用户头像地址")
    @Column(name = "user_header_img")
    var userHeaderImg: String? = null

    /**
     * 用户所在组织id
     */
    @ApiModelProperty("用户所在组织id")
    @Column(name = "user_org_id")
    var userOrgId: Long? = null

    /**
     * 用户所在组织名称
     */
    @ApiModelProperty("用户所在组织名称")
    @Column(name = "user_org_name")
    var userOrgName: String? = null

    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    @Column(name = "ecp_org_id")
    var ecpOrgId: Long? = null

    /**
     * 组织名称
     */
    @ApiModelProperty("组织名称")
    @Column(name = "org_name")
    var orgName: String? = null

    /**
     * 被点赞数 (被点赞了就加1 被取消点赞了就-1)
     */
    @ApiModelProperty("被点赞数 (被点赞了就加1 被取消点赞了就-1)")
    @Column(name = "by_like_num")
    var byLikeNum: Int? = null

    /**
     * 自己的点赞数
     */
    @ApiModelProperty("自己的点赞数")
    @Column(name = "is_like_num")
    var isLikeNum: Int? = null

    /**
     * 动态数
     */
    @ApiModelProperty("动态数")
    @Column(name = "message_num")
    var messageNum: Int? = null

    /**
     * 评论数
     */
    @ApiModelProperty("评论数")
    @Column(name = "comment_num")
    var commentNum: Int? = null

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @Column(name = "create_time")
    var createTime: Date? = null

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @Column(name = "update_time")
    var updateTime: Date? = null

    companion object {
        const val serialVersionUID = 1L
    }
}