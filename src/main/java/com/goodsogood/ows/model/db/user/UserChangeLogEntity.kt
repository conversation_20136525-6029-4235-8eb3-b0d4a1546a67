package com.goodsogood.ows.model.db.user

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import lombok.Data
import java.time.LocalDateTime
import java.util.*
import javax.persistence.Column
import javax.persistence.Id
import javax.persistence.Table

/**
 *
 * <AUTHOR>
 * @Date 2023-03-22 15:09:43
 * @Description UserChangeLogEntity
 *
 */
@Data
@ApiModel
@Table(name = "t_user_change_log")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class UserChangeLogEntity(

    @Id
    @ApiModelProperty(value = "主键")
    @Column(name = "user_change_log_id")
    var userChangeLogId: Long? = null,

    @Column(name = "user_id")
    var userId: Long? = null,

    @Column(name = "user_name")
    var userName: String? = null,

    @Column(name = "phone")
    var phone: String? = null,

    @Column(name = "org_id")
    var orgId: Long? = null,

    @Column(name = "unit_id")
    var unitId: Long? = null,

    @Column(name = "tags_after")
    var tagsAfter: String? = null,

    @Column(name = "tags_before")
    var tagsBefore: String? = null,

    @Column(name = "sequence_after")
    var sequenceAfter: Int? = null,

    @Column(name = "sequence_before")
    var sequenceBefore: Int? = null,

    @Column(name = "create_time")
    var createTime: LocalDateTime? = null
) {
    override fun toString(): String {
        return "UserChangeLogEntity(userChangeLogId=$userChangeLogId, userId=$userId, orgId=$orgId, unitId=$unitId, tagsAfter=$tagsAfter, tagsBefore=$tagsBefore, sequenceAfter=$sequenceAfter, sequenceBefore=$sequenceBefore, createTime=$createTime)"
    }
}