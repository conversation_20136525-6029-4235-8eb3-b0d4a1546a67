package com.goodsogood.ows.model.db.ppmd;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 交费流水实体
 * User: R
 * Date: 2018/10/19
 * Time: 16:23
 * Created with IntelliJ IDEA.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Table(name = "t_ppmd_pay_log")
public class PayLogEntity {
    @Id
    @Column(name = "pay_log_id")
    @JsonProperty("pay_log_id")
    @ApiModelProperty("交费流水ID")
    private Long payLogId;

    @Column(name = "pay_info_id")
    @JsonProperty("pay_info_id")
    @ApiModelProperty("支付订单id")
    private Long payInfoId;

    @Column(name = "org_id")
    @JsonProperty("org_id")
    @ApiModelProperty("流水ID")
    private Long orgId;

    @Column(name = "org_name")
    @JsonProperty("org_name")
    @ApiModelProperty("组织名称")
    private String orgName;

    @Column(name = "office_id")
    @JsonProperty("office_id")
    @ApiModelProperty("机关单位编号")
    private Long officeId;


    @Column(name = "office_name")
    @JsonProperty("office_name")
    @ApiModelProperty("机关组织名称")
    private String officeName;

    @Column(name = "user_id")
    @JsonProperty("user_id")
    @ApiModelProperty("用户ID")
    private Long userId;

    @Column(name = "user_name")
    @JsonProperty("user_name")
    @ApiModelProperty("用户名称")
    private String userName;

    @Column(name = "pay_method")
    @JsonProperty("pay_method")
    @ApiModelProperty("交费方式,，1：银联支付,2：微信支付")
    private Integer payMethod;

    @Column(name = "pay_cardinal_number")
    @JsonProperty("pay_cardinal_number")
    @ApiModelProperty("交费基数")
    private Integer payCardinalNumber;

    @Column(name = "pay_ratio")
    @JsonProperty("pay_ratio")
    @ApiModelProperty("交费比例")
    private Double payRatio;

    @Column(name = "ratio_type")
    @JsonProperty("ratio_type")
    @ApiModelProperty("交费比例类型:1.正常党员交费比例  2.退休党员交费比例")
    private Integer ratioType;

    @Column(name = "pay_log_type")
    @JsonProperty("pay_log_type")
    @ApiModelProperty("记录类型:1 正常交纳记录、2 免交记录、3 补交记录、4 正常欠交记录 5 少交欠交记录  6固定缴纳记录")
    private Integer payLogType;

    @Column(name = "pay_already")
    @JsonProperty("pay_already")
    @ApiModelProperty("已交金额 单位:分  如果记录类型为欠交(4和5)，则记录欠交的金额(负数)")
    private Integer payAlready;

    @Column(name = "pay_type")
    @JsonProperty("pay_type")
    @ApiModelProperty("交费类型，1：自己缴费，2：他人代缴,3:代人缴费")
    private Integer payType;

    @Column(name = "pay_for")
    @JsonProperty("pay_for")
    @ApiModelProperty("应支付年月")
    private String payFor;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "pay_date")
    @JsonProperty("pay_date")
    @ApiModelProperty("实际支付时间")
    private Date payDate;

    @Column(name = "pay_user_id")
    @JsonProperty("pay_user_id")
    @ApiModelProperty("支付人ID")
    private Long payUserId;

    @Column(name = "pay_user_name")
    @JsonProperty("pay_user_name")
    @ApiModelProperty("支付人姓名")
    private String payUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    @JsonProperty("create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @JsonProperty(value = "reason_id")
    @Column(name = "reason_id")
    @ApiModelProperty(name = "关联的原因描述id")
    private Integer reasonId;

    @Column(name = "reason")
    @ApiModelProperty(name = "其他选项时，原因文本")
    private String reason;

    @Column(name = "org_level")
    @ApiModelProperty(name = "组织树父级路径")
    private String orgLevel;
}
