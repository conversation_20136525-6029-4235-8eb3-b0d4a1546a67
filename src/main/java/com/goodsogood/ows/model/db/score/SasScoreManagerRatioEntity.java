package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.GeneratedValue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @description 日比率数据
 * @date 2021-12-13
 */
@Entity
@Data
@Table(name = "t_sas_score_manager_ratio")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SasScoreManagerRatioEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "ratio_id")
    private Long ratioId;

    /**
     * region_id
     */
    @Column(name = "region_id")
    private Long regionId;

    /**
     * org_id
     */
    @Column(name = "org_id")
    private Long orgId;

    /**
     * 1:组织平均登录率 2:组织浏览新闻完成率
     */
    @Column(name = "type")
    private Integer type;

    /**
     * date
     */
    @Column(name = "date")
    private LocalDate date;

    /**
     * 比例
     */
    @Column(name = "ratio")
    private Double ratio;

    /**
     * create_user
     */
    @Column(name = "create_user")
    private Long createUser;

    /**
     * create_time
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * update_user
     */
    @Column(name = "update_user")
    private Long updateUser;

    /**
     * 变更时间
     */
    @Column(name = "change_time")
    private LocalDateTime changeTime;

    /**
     * 总数人数
     */
    @Column(name = "total")
    private Integer total;

    /**
     * 实际人数
     */
    @Column(name = "find")
    private Integer find;


    public SasScoreManagerRatioEntity() {
    }

}