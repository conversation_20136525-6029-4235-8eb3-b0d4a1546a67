package com.goodsogood.ows.model.db.user;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-11-04 16:01
 * @since 3.0.1
 **/
@Table(name = "t_user_snapshot")
@Data
public class UserSnapshotEntity {

     @Id
     @GeneratedValue(generator = "JDBC")
     @Column(name = "user_snapshot_id")
     private Long userSnapshotId;
     @Column(name = "date_month")
     private String dateMonth;
     @Column(name = "user_id")
     private Long userId;
     @Column(name = "user_name")
     private String userName;
     @Column(name = "birthday")
     private String birthday;
     @Column(name = "age")
     private Integer age;
     @Column(name = "birthplace")
     private String birthplace;
     @Column(name = "phone")
     private String phone;
     @Column(name = "phone_secret")
     private String phoneSecret;
     @Column(name = "cert_number")
     private String certNumber;
     @Column(name = "cert_number_secret")
     private String certNumberSecret;
     @ApiModelProperty(name = "预备党员时间")
     @Column(name = "pro_party_time")
     private String proPartyTime;
     @ApiModelProperty(name = "入党时间")
     @Column(name = "joining_time")
     private String joiningTime;
     @ApiModelProperty(name = "进入支部时间")
     @Column(name = "join_party_time")
     private String joinPartyTime;
     @ApiModelProperty(name = "发展对象日期")
     @Column(name = "develop_time")
     private String developTime;
     @Column(name = "ethnic")
     private Integer ethnic;
     @Column(name = "education")
     private Integer education;
     @Column(name = "is_retire")
     private Integer isRetire;
     @Column(name = "political_type")
     private Integer politicalType;
     @Column(name = "gender")
     private Integer gender;
     @ApiModelProperty("申请入党日期")
     @JsonProperty(value = "apply_time")
     @Column(name = "apply_time")
     private String applyTime;
     @ApiModelProperty("积极分子日期")
     @JsonProperty(value = "activist_time")
     @Column(name = "activist_time")
     private String activistTime;
     @Column(name = "org_id")
     private Long orgId;
     @Column(name = "org_type_child")
     private Integer orgTypeChild;
     @Column(name = "org_level")
     private String orgLevel;
     @Column(name = "org_old_id")
     private Long orgOldId;
     @Column(name = "org_old_type_child")
     private Integer orgOldTypeChild;
     @Column(name = "org_old_level")
     private String orgOldLevel;
     @Column(name = "status")
     private Integer status;
     @Column(name = "status_old")
     private Integer statusOld;
     @Column(name = "region_id")
     private Long regionId;
     @Column(name = "position_code")
     private Integer positionCode;
     @Column(name = "position")
     private String position;
     @Column(name = "create_time")
     private Date createTime;

}
