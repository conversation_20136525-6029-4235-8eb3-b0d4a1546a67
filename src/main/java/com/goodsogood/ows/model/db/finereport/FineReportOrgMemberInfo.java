package com.goodsogood.ows.model.db.finereport;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * @program: downloadfile
 * @description: 党组人员信息
 * @author: Mr.<PERSON>
 * @create: 2020-07-30 15:43
 **/
@Data
@Table(name = "t_finereport_org_member_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FineReportOrgMemberInfo {
    @JsonProperty(value = "member_info_id")
    @Column(name = "member_info_id")
    private Integer memberInfoId=0;


    //("组织id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId=0L;

    //("组织id")
    @JsonProperty(value = "region_id")
    @Column(name = "region_id")
    private Long regionId=0L;

    //统计标识1.表示每天更新 2.每个月更新用于决策报表
    @JsonProperty(value = "sta_flag")
    @Column(name = "sta_flag")
    private int staFlag=0;

    //("组织排序")
    @JsonProperty(value = "seq")
    @Column(name = "seq")
    private Integer seq=0;

    //("组织层级")
    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    private String orgLevel="";

    //("党委名称")
    @JsonProperty(value = "party_name")
    @Column(name = "party_name")
    private String partyName="";


    //("党委简称")
    @JsonProperty(value = "party_short_name")
    @Column(name = "party_short_name")
    private String partyShortName="";


    //("党员总数")
    @JsonProperty(value = "party_member_total")
    @Column(name = "party_member_total")
    private Integer partyMemberTotal=0;

    //("正式党员")
    @JsonProperty(value = "formal_party_member")
    @Column(name = "formal_party_member")
    private Integer formalPartyMember=0;

    //("预备党员")
    @JsonProperty(value = "pre_party_member")
    @Column(name = "pre_party_member")
    private Integer prePartyMember=0;

    //("其它党员")
    @JsonProperty(value = "other_party_member")
    @Column(name = "other_party_member")
    private Integer otherPartyMember=0;


    //("女党员")
    @JsonProperty(value = "female_member")
    @Column(name = "female_member")
    private Integer femaleMember=0;

    //("男党员")
    @JsonProperty(value = "male_member")
    @Column(name = "male_member")
    private Integer maleMember=0;

    //("其它性别党员")
    @JsonProperty(value = "other_sex_member")
    @Column(name = "other_sex_member")
    private Integer otherSexMember=0;


    //("汉族")
    @JsonProperty(value = "nationality_party_member")
    @Column(name = "nationality_party_member")
    private Integer nationalityPartyMember=0;


    //("少数民族党员")
    @JsonProperty(value = "minority_party_member")
    @Column(name = "minority_party_member")
    private Integer minorityPartyMember=0;

    //("未知民族")
    @JsonProperty(value = "other_nationality_party_member")
    @Column(name = "other_nationality_party_member")
    private Integer otherNationalityPartyMember=0;


    //("30以下")
    @Column(name = "age1")
    private Integer age1=0;


    //("31-35")
    @Column(name = "age2")
    private Integer age2=0;


    //("36-40")
    @Column(name = "age3")
    private Integer age3=0;


    //("41-45")
    @Column(name = "age4")
    private Integer age4=0;


    //("46-50")
    @Column(name = "age5")
    private Integer age5=0;


    //("51-55")
    @Column(name = "age6")
    private Integer age6=0;


    //("56-60")
    @Column(name = "age7")
    private Integer age7=0;


    //("61以上")
    @Column(name = "age8")
    private Integer age8=0;


    //("研究生")
    @Column(name = "education1")
    private Integer education1=0;


    //("大学本科")
    @Column(name = "education2")
    private Integer education2=0;


    //("大学专科")
    @Column(name = "education3")
    private Integer education3=0;


    //("中专")
    @Column(name = "education4")
    private Integer education4=0;


    //("高中、中技")
    @Column(name = "education5")
    private Integer education5=0;


    //("初中及以下")
    @Column(name = "education6")
    private Integer education6=0;

    //("其它学历")
    @Column(name = "education7")
    private Integer education7=0;

    //("新中国成立前")
    @Column(name = "time1")
    private Integer time1=0;


    //("新中国成立后至党的十一届三中全会前")
    @Column(name = "time2")
    private Integer time2=0;


    //("党的十一届三中全会后至党的十八大前")
    @Column(name = "time3")
    private Integer time3=0;


    //("党的十八大以来")
    @Column(name = "time4")
    private Integer time4=0;

    //其它
    @Column(name = "time5")
    private Integer time5=0;


    //("党政机关工作人员")
    @Column(name = "profession1")
    private Integer profession1=0;


    //("企事业单位管理人员")
    @Column(name = "profession2")
    private Integer profession2=0;


    //("企事业单位专业技术人员")
    @Column(name = "profession3")
    private Integer profession3=0;


    //("工勤技能人员")
    @Column(name = "profession4")
    private Integer profession4=0;


    //("学生")
    @Column(name = "profession5")
    private Integer profession5=0;


    //("农牧渔民")
    @Column(name = "profession6")
    private Integer profession6=0;

    //("离退休人员")
    @Column(name = "profession7")
    private Integer profession7=0;

    //("解放军武警 ")
    @Column(name = "profession8")
    private Integer profession8=0;

    //("其它")
    @Column(name = "profession9")
    private Integer profession9=0;

    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private Date createTime;


    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    private Date updateTime;

    //("统计年份")
    @JsonProperty(value = "sta_year")
    @Column(name = "sta_year")
    private String staYear;

}
