package com.goodsogood.ows.model.db.history;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_lph_user_prize_award 实体类
 *
 * 实际获奖流水(产品配置) 
 *
 * <AUTHOR>
 * @create 2021-05-07 16:08
*/
@Data
@ApiModel
@Table(name = "t_lph_user_prize_award")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LphUserPrizeAwardEntity   extends  BaseEntity{
	
//	@Id
	@JsonProperty(value = "award_id")
	@Column(name = "award_id")
	private Long awardId;
	
	
	@ApiModelProperty("奖品id")
	@JsonProperty(value = "prize_id")
	@Column(name = "prize_id")
	private Long prizeId;
	
	
	@ApiModelProperty("区县id")
	@JsonProperty(value = "region_id")
	@Column(name = "region_id")
	private Integer regionId;
	
	
	@JsonProperty(value = "open_id")
	@Column(name = "open_id")
	private String openId;
	
	
	@ApiModelProperty("奖品类型 奖品类型(实物:0,积分:1)")
	@JsonProperty(value = "prize_type")
	@Column(name = "prize_type")
	private Byte prizeType;
	
	
	@ApiModelProperty("中奖下标")
	@JsonProperty(value = "award_index")
	@Column(name = "award_index")
	private Integer awardIndex;
	
	
	@ApiModelProperty("奖品流水id")
	@Column(name = "token")
	private String token;
	
	
	@ApiModelProperty("当前用户抽奖次数")
	@Column(name = "number")
	private Integer number;
	
	
	@ApiModelProperty("0:未确认 1:确认未分配 2:已分配(回调成功) 3:推送成功 4:推送失败 5:已中奖")
	@Column(name = "type")
	private Byte type;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
	
	@ApiModelProperty("奖励积分")
	@JsonProperty(value = "score_num")
	@Column(name = "score_num")
	private Long scoreNum;
	
}

