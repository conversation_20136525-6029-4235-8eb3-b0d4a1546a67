package com.goodsogood.ows.model.db.sas

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModelProperty
import javax.persistence.Column
import javax.persistence.GeneratedValue
import javax.persistence.Id
import javax.persistence.Table

/**
 *
 * <AUTHOR>
 * @Date 2022-06-21 18:55:28
 * @Description PbmEntities
 *
 */
@Table(name = "t_pbm_work_item")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class WorkItemEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "work_item_id")
    var workItemId: Long? = null,

    @Column(name = "rule_id")
    var ruleId: Long? = null,

    @Column(name = "region_id")
    var regionId: Long? = null,

    @Column(name = "name")
    @ApiModelProperty("指标名称")
    var name: String? = null,

    @Column(name = "type")
    @ApiModelProperty("类型 1-党务，2-业务")
    var type: Int? = null,

    @Column(name = "against")
    @ApiModelProperty("针对对象 1-人员，2-组织")
    var against: Int? = null,

    @Column(name = "cycle")
    @ApiModelProperty("考核周期 1-周，2-月度，3-季度，4-年度，5-累计")
    var cycle: Int? = null,

    @Column(name = "criterion")
    @ApiModelProperty("考核标准")
    var criterion: String? = null,

    @Column(name = "strategy_name")
    @ApiModelProperty("工厂类-策略名")
    var strategyName: String? = null,

    @Column(name = "data_type")
    @ApiModelProperty("数据类型 1-int,2-double,3-time,4-boolean")
    var dataType: Int? = null,

    @Column(name = "description")
    @ApiModelProperty("工作结果描述")
    var description: String? = null,

    @Column(name = "compare_type")
    @ApiModelProperty("比较类型 0-不需要排序，1-百分比，2-超过数量，3-判断选项")
    var compareType: Int? = null,

    @Column(name = "compare_description")
    @ApiModelProperty("结果比较描述")
    var compareDescription: String? = null,

    @Column(name = "remark")
    @ApiModelProperty("备注")
    var remark: String? = null

) {
    override fun toString(): String {
        return "WorkItemEntity(workItemId=$workItemId, ruleId=$ruleId, regionId=$regionId, name=$name, type=$type, cycle=$cycle, criterion=$criterion, strategyName=$strategyName, dataType=$dataType, description=$description, compareType=$compareType, compareDescription=$compareDescription, remark=$remark)"
    }
}
