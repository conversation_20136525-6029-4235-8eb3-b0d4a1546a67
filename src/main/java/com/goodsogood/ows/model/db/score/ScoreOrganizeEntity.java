package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * t_score_organize 实体类
 *
 * 组织账户信息 
 *
 * <AUTHOR>
 * @create 2019-03-14 09:25
*/
@Data
@ApiModel
@Table(name = "t_score_organize")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScoreOrganizeEntity {
	
	@Id
	@JsonProperty(value = "organize_id")
	@Column(name = "organize_id")
	private Long organizeId;
	
	
	@JsonProperty(value = "app_id")
	@Column(name = "app_id")
	private Long appId;
	
	
	@ApiModelProperty("组织Id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;
	
	
	@ApiModelProperty("组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;
	
	
	@ApiModelProperty("人民币账户流水，全局唯一一个")
	@JsonProperty(value = "account_balance")
	@Column(name = "account_balance")
	private BigDecimal accountBalance;
	
	
	@ApiModelProperty("账号类型 1.专户 2.组织账户 3.rmb账户")
	@JsonProperty(value = "account_type")
	@Column(name = "account_type")
	private Integer accountType;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;

	@ApiModelProperty("组织支付状态 0：启用 1. 禁用")
	@JsonProperty(value = "org_pay_status")
	@Column(name = "org_pay_status")
	private Integer orgPayStatus;
	
}

