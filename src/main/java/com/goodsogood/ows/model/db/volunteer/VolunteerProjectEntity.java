package com.goodsogood.ows.model.db.volunteer;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/13
 * Description: 志愿项目主表
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_volunteer_project")
public class VolunteerProjectEntity {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "project_id")
    @Column(name = "project_id")
    @ApiModelProperty(name = "主键id")
    private Long projectId;

    /**
     * 组织id
     */
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    @ApiModelProperty(name = "组织id")
    private Long orgId;

    /**
     * 志愿团体id
     */
    @JsonProperty(value = "volunteer_team_id")
    @Column(name = "volunteer_team_id")
    @ApiModelProperty(name = "志愿团体id")
    private Long volunteerTeamId;

    /**
     * 志愿团体名称
     */
    @JsonProperty(value = "volunteer_team_name")
    @Column(name = "volunteer_team_name")
    @ApiModelProperty(name = "志愿团体名称")
    private String volunteerTeamName;

    /**
     * 项目名称
     */
    @JsonProperty(value = "name")
    @Column(name = "name")
    @ApiModelProperty(name = "项目名称")
    private String name;

    /**
     * 服务对象
     */
    @JsonProperty(value = "server_project")
    @Column(name = "server_project")
    @ApiModelProperty(name = "服务对象")
    private String serverProject;

    /**
     * 服务省code
     */
    @JsonProperty(value = "server_province_code")
    @Column(name = "server_province_code")
    @ApiModelProperty(name = "服务省code")
    private Integer serverProvinceCode;

    /**
     * 服务省名称
     */
    @JsonProperty(value = "server_province_name")
    @Column(name = "server_province_name")
    @ApiModelProperty(name = "服务省名称")
    private String serverProvinceName;

    /**
     * 服务市code
     */
    @JsonProperty(value = "server_city_code")
    @Column(name = "server_city_code")
    @ApiModelProperty(name = "服务市code")
    private Integer serverCityCode;

    /**
     * 服务市名称
     */
    @JsonProperty(value = "server_city_name")
    @Column(name = "server_city_name")
    @ApiModelProperty(name = "服务市名称")
    private String serverCityName;

    /**
     * 服务区code
     */
    @JsonProperty(value = "server_district_code")
    @Column(name = "server_district_code")
    @ApiModelProperty(name = "服务区code")
    private Integer serverDistrictCode;

    /**
     * 服务区名称
     */
    @JsonProperty(value = "server_district_name")
    @Column(name = "server_district_name")
    @ApiModelProperty(name = "服务区名称")
    private String serverDistrictName;

    /**
     * 服务地点
     */
    @JsonProperty(value = "server_address")
    @Column(name = "server_address")
    @ApiModelProperty(name = "服务地点")
    private String serverAddress;

    /**
     * 联系人
     */
    @JsonProperty(value = "contact_name")
    @Column(name = "contact_name")
    @ApiModelProperty(name = "联系人")
    private String contactName;

    /**
     * 联系手机
     */
    @JsonProperty(value = "contact_phone")
    @Column(name = "contact_phone")
    @ApiModelProperty(name = "联系手机")
    private String contactPhone;

    /**
     * 项目开始时间
     */
    @JsonProperty(value = "project_start_time")
    @Column(name = "project_start_time")
    @ApiModelProperty(name = "项目开始时间")
    private Date projectStartTime;

    /**
     * 项目结束时间
     */
    @JsonProperty(value = "project_end_time")
    @Column(name = "project_end_time")
    @ApiModelProperty(name = "项目结束时间")
    private Date projectEndTime;

    /**
     * 招募时间
     */
    @JsonProperty(value = "recruit_start_time")
    @Column(name = "recruit_start_time")
    @ApiModelProperty(name = "招募时间")
    private Date recruitStartTime;

    /**
     * 招募结束时间 必须要小于等于项目结束时间
     */
    @JsonProperty(value = "recruit_end_time")
    @Column(name = "recruit_end_time")
    @ApiModelProperty(name = "招募时间")
    private Date recruitEndTime;

    /**
     * 项目服务时间 (用户手动输入)
     */
    @JsonProperty(value = "server_time")
    @Column(name = "server_time")
    @ApiModelProperty(name = "项目服务时间 (用户手动输入)")
    private String serverTime;

    /**
     * 其他志愿者保障
     */
    @JsonProperty(value = "other_protect")
    @Column(name = "other_protect")
    @ApiModelProperty(name = "其他志愿者保障")
    private String otherProtect;

    /**
     * 项目介绍
     */
    @JsonProperty(value = "project_summary")
    @Column(name = "project_summary")
    @ApiModelProperty(name = "项目介绍")
    private String projectSummary;

    /**
     * 1:正常 2:作废
     */
    @JsonProperty(value = "status")
    @Column(name = "status")
    @ApiModelProperty(name = "1:正常 2:作废")
    private Integer status;

    /**
     * 1:进行中 2:已结项 3:已结算(已结项) 4:未发布(草稿)
     */
    @JsonProperty(value = "state")
    @Column(name = "state")
    @ApiModelProperty(name = "1:进行中 2:已结项 3:已结算(已结项) 4:未发布(草稿)")
    private Integer state;

    /**
     * 创建时间
     */
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    @ApiModelProperty(name = "创建时间")
    private Date createTime;

    /**
     * 创建用户
     */
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    @ApiModelProperty(name = "创建用户")
    private Long createUser;

    /**
     * 创建志愿者id
     */
    @JsonProperty(value = "create_volunteer_user")
    @Column(name = "create_volunteer_user")
    @ApiModelProperty(name = "创建志愿者id")
    private Long createVolunteerUser;

    /**
     * 最后一个修改的用户
     */
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    @ApiModelProperty(name = "最后一个修改的用户")
    private Long lastChangeUser;

    /**
     * 最后修改时间
     */
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    @ApiModelProperty(name = "最后修改时间")
    private Date updateTime;

//    /**
//     * 是否成立第二支部 0-否，1-是
//     */
//    @JsonProperty(value = "is_create_branch")
//    @Column(name = "is_create_branch")
//    @ApiModelProperty(name = "是否成立第二支部 0-否，1-是")
//    private Integer isCreateBranch;
//
//    /**
//     * 第二支部名称
//     */
//    @JsonProperty(value = "branch_name")
//    @Column(name = "branch_name")
//    @ApiModelProperty(name = "第二支部名称")
//    private String branchName;

    /**
     * 区县id
     */
    @JsonProperty(value = "region_id")
    @Column(name = "region_id")
    @ApiModelProperty(name = "区县Id")
    private Long regionId;
}
