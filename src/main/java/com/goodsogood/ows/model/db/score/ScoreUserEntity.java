package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


/**
 * 用户积分实体
 * <AUTHOR>
 * @create 2018-06-09 15:07
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
@Table(name = "t_score_user")
public class ScoreUserEntity {

     @Id
     @Column(name = "score_user_id")
     @ApiModelProperty(name = "积分用户id")
     @JsonProperty(value = "score_user_id")
     private Long scoreUserId;


     @Column(name = "user_name")
     @ApiModelProperty(name = "用户姓名")
     @JsonProperty(value = "name")
     private String userName;

     @Column(name = "user_id")
     @ApiModelProperty(name = "用户id")
     @JsonProperty(value = "user_id")
     private Long userId;

     @Column(name = "token")
     @ApiModelProperty(name = "提交数据唯一标识")
     @JsonProperty(value = "token")
     private String token;

     @Column(name = "cert_number")
     @ApiModelProperty(name = "身份证")
     @JsonProperty(value = "id_card")
     private String certNumber;

     //@Column(name = "encrpy_cert_number")
     @Transient
     @ApiModelProperty(name = "加密身份证")
     private String encrpyCertNumber;

     @Column(name = "dese_cert_number")
     @ApiModelProperty(name = "脱敏身份证")
     private String deseCertNumber;

     @Column(name = "phone")
     @ApiModelProperty(name = "电话")
     private String phone;

     //@Column(name = "encrpy_phone")
     @Transient
     @ApiModelProperty(name = "加密电话号码")
     private String encrpyPhone;

     @Column(name = "dese_phone")
     @ApiModelProperty(name = "脱敏电话")
     private String desePhone;

     @Column(name = "create_time")
     @ApiModelProperty(name = "创建时间")
     private Date createTime;

     @Column(name = "update_time")
     @ApiModelProperty(name = "更新时间")
     private Date updateTime;

}
