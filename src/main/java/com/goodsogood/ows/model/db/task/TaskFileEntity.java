package com.goodsogood.ows.model.db.task;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "t_task_file")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TaskFileEntity {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "task_file_id")
    @ApiModelProperty("主键")
    private Long taskFileId;

    /**
     * 任务id
     */
    @Column(name = "task_id")
    @ApiModelProperty("任务id")
    private Long taskId;

    /**
     * 任务流水id
     */
    @Column(name = "task_log_id")
    @ApiModelProperty("任务流水id")
    private Long taskLogId;

    /**
     * 文件id
     */
    @Column(name = "file_id")
    @ApiModelProperty("文件id")
    private Long fileId;

    /**
     * 文件名称
     */
    @Column(name = "file_name")
    @ApiModelProperty("文件名称")
    private String fileName;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /**
     * 派发时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("派发时间")
    private Date createTime;

    /**
     * 最后修改人
     */
    @Column(name = "last_change_user")
    @ApiModelProperty("最后修改人")
    private Long lastChangeUser;
}
