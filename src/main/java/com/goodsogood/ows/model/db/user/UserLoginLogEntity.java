package com.goodsogood.ows.model.db.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 用户登录日志entity
 *
 * <AUTHOR>
 * @date 2018-04-03
 */
@Data
@ApiModel
@Table(name = "t_user_login_log")
public class UserLoginLogEntity {

    @Id
    @ApiModelProperty("登录用户ID")
    @Column(name = "login_id")
    private Long loginId;

    @ApiModelProperty("登录状态")
    @Column(name = "login_status")
    private Integer loginStatus;

    @ApiModelProperty("登录IP")
    @Column(name = "login_ip")
    private String loginIp;

    @ApiModelProperty("登录时间")
    @Column(name = "login_date")
    private Date loginDate;

    @ApiModelProperty("登录来源")
    private String source;

    @ApiModelProperty("token")
    private String token;

    @ApiModelProperty("登录端 1-PC, 2-WeChat")
    private Integer belong;

    @ApiModelProperty("登录组织ID")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("登录区县ID")
    @Column(name = "region_id")
    private Long regionId;

    @ApiModelProperty("微信端的openId")
    @Column(name = "open_id")
    private String openId;


}