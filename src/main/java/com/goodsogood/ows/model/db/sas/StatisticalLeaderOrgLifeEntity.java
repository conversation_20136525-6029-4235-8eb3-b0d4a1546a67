package com.goodsogood.ows.model.db.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.annotations.Param;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_statistical_leader_org_life 实体类
 *
 * 领导干部双重组织生活 
 *
 * <AUTHOR>
 * @create 2019-04-22 16:01
*/
@Data
@ApiModel
@Table(name = "t_statistical_leader_org_life")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatisticalLeaderOrgLifeEntity {
	
	@Id
	@JsonProperty(value = "leader_org_life_id")
	@Column(name = "leader_org_life_id")
	private Long leaderOrgLifeId;
	
	
	@ApiModelProperty("领导人ID")
	@JsonProperty(value = "leader_user_id")
	@Column(name = "leader_user_id")
	private Long leaderUserId;
	
	
	@ApiModelProperty("领导人名字")
	@JsonProperty(value = "leader_name")
	@Column(name = "leader_name")
	private String leaderName;
	
	
	@ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "region_id")
    @Column(name = "region_id")
    private Long regionId;
	
	
	@ApiModelProperty("组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;
	
	
	@ApiModelProperty("组织类型id")
	@JsonProperty(value = "org_type_id")
	@Column(name = "org_type_id")
	private Integer orgTypeId;
	
	
	@ApiModelProperty("你级组织Id")
	@JsonProperty(value = "parent_org_id")
	@Column(name = "parent_org_id")
	private Long parentOrgId;
	
	
	@ApiModelProperty("组织父级路径")
	@JsonProperty(value = "org_level")
	@Column(name = "org_level")
	private String orgLevel;
	
	
	@ApiModelProperty("是否离退休党组织 1-是 2-否")
	@JsonProperty(value = "is_retire")
	@Column(name = "is_retire")
	private Integer isRetire;
	
	
	@ApiModelProperty("活动类型id")
	@JsonProperty(value = "activity_type_id")
	@Column(name = "activity_type_id")
	private Integer activityTypeId;
	
	
	@ApiModelProperty("活动类型名称")
	@JsonProperty(value = "activity_type_name")
	@Column(name = "activity_type_name")
	private String activityTypeName;
	
	
	@ApiModelProperty("参与次数")
	@JsonProperty(value = "participate_num")
	@Column(name = "participate_num")
	private Integer participateNum;
	
	
	@JsonProperty(value = "statistical_year")
	@Column(name = "statistical_year")
	private Integer statisticalYear;
	
	
	@ApiModelProperty("统计的月（1-12）")
	@JsonProperty(value = "statistical_month")
	@Column(name = "statistical_month")
	private Integer statisticalMonth;

	@ApiModelProperty("统计时间(yyyy-MM)")
	@JsonProperty(value = "statistical_date")
	@Column(name = "statistical_date")
	private String statisticalDate;
	
	
	@ApiModelProperty("逻辑状态:(1-有效, 0-无效)")
	@Column(name = "status")
	private Integer status;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
}

