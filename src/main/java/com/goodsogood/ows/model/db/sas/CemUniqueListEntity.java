package com.goodsogood.ows.model.db.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.data.annotation.Id;

import javax.persistence.Column;
import javax.persistence.Table;

@Data
@ApiModel
@Table(name = "t_cem_unique_list")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CemUniqueListEntity {
    @Id
    @Column(name = "list_id")
    private Long listId;

    @Column(name = "rule_id")
    private Integer ruleId;

    @Column(name = "unique_code")
    private String uniqueCode;

}
