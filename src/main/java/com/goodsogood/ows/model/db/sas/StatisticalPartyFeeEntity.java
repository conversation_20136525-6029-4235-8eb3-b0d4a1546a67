package com.goodsogood.ows.model.db.sas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_statistical_party_fee 实体类
 *
 * 党费交纳完成情况统计 
 *
 * <AUTHOR>
 * @create 2019-04-22 16:04
*/
@Data
@ApiModel
@Table(name = "t_statistical_party_fee")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatisticalPartyFeeEntity{

	@Id
	@JsonProperty(value = "party_fee_id")
	@Column(name = "party_fee_id")
	private Long partyFeeId;


	@ApiModelProperty("组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;

    @ApiModelProperty("区县id")
    @JsonProperty(value = "region_id")
    @Column(name = "region_id")
    private Long regionId;
	
	
	@ApiModelProperty("组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;
	
	
	@ApiModelProperty("组织类型")
	@JsonProperty(value = "org_type_id")
	@Column(name = "org_type_id")
	private Integer orgTypeId;
	
	
	@ApiModelProperty("上级组织Id")
	@JsonProperty(value = "parent_org_id")
	@Column(name = "parent_org_id")
	private Long parentOrgId;
	
	
	@ApiModelProperty("组织父级路径")
	@JsonProperty(value = "org_level")
	@Column(name = "org_level")
	private String orgLevel;
	
	
	@ApiModelProperty("是否离退休党组织 1-是 2-否")
	@JsonProperty(value = "is_retire")
	@Column(name = "is_retire")
	private Integer isRetire;


	@ApiModelProperty("组织建立时间")
	@JsonProperty(value = "org_create_time")
	@Column(name = "org_create_time")
	private Date orgCreateTime;
	
	
	@ApiModelProperty("应交人数（默认为0）")
	@JsonProperty(value = "payable_person_num")
	@Column(name = "payable_person_num")
	private Integer payablePersonNum;
	
	
	@ApiModelProperty("未交纳人数")
	@JsonProperty(value = "unpaid_person_num")
	@Column(name = "unpaid_person_num")
	private Integer unpaidPersonNum;

	@ApiModelProperty("未交纳人数")
	@JsonProperty(value = "eval_unpaid_person_num")
	@Column(name = "eval_unpaid_person_num")
	private Integer evalUnpaidPersonNum;
	
	
	@JsonProperty(value = "statistical_year")
	@Column(name = "statistical_year")
	private Integer statisticalYear;
	
	
	@ApiModelProperty("统计的月（1-12）")
	@JsonProperty(value = "statistical_month")
	@Column(name = "statistical_month")
	private Integer statisticalMonth;

	@ApiModelProperty("统计时间(yyyy-MM)")
	@JsonProperty(value = "statistical_date")
	@Column(name = "statistical_date")
	private String statisticalDate;
	
	
	@ApiModelProperty("逻辑状态:(1-有效, 0-无效)")
	@Column(name = "status")
	private Integer status;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
}

