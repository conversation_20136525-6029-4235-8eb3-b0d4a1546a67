package com.goodsogood.ows.model.db.task;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "t_task")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TaskEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "task_id")
    @ApiModelProperty("任务id")
    private Long taskId;

    /**
     * 任务标题
     */
    @Column(name = "task_title")
    @ApiModelProperty("任务标题")
    private String taskTitle;

    /**
     * 任务级别 13-党建任务、14-创新任务、15-业务任务
     */
    @Column(name = "task_class")
    @ApiModelProperty("任务类型 13-党建任务、14-创新任务、15-业务任务")
    private Integer taskClass;

    /**
     * 任务级别 1-a级任务 2-b级任务 3-c级任务 4-d级任务 5-其他
     */
    @Column(name = "task_level")
    @ApiModelProperty("任务级别 1-a级任务 2-b级任务 3-c级任务 4-d级任务 5-其他")
    private Integer taskLevel;

    /**
     * 开始时间 yyyy-mm-dd hh:mm:ss
     */
    @Column(name = "begin_time")
    @ApiModelProperty("开始时间 yyyy-mm-dd hh:mm:ss")
    private Date beginTime;

    /**
     * 结束时间 yyyy-mm-dd hh:mm:ss
     */
    @Column(name = "end_time")
    @ApiModelProperty("结束时间 yyyy-mm-dd hh:mm:ss")
    private Date endTime;

    /**
     * 是否是草稿 1-是 0-否
     */
    @Column(name = "is_draft")
    @ApiModelProperty("是否是草稿 1-是 0-否")
    private Integer isDraft;

    /**
     * 父任务id
     */
    @Column(name = "parent_task_id")
    @ApiModelProperty("父任务id")
    private Long parentTaskId;

    /**
     * 子任务数量
     */
    @Column(name = "sub_total")
    @ApiModelProperty("子任务数量")
    private Integer subTotal;

    /**
     * 子任务已完成数量
     */
    @Column(name = "sub_done_total")
    @ApiModelProperty("子任务已完成数量")
    private Integer subDoneTotal;

    /**
     * 第三方来源标记，默认为null
     */
    @Column(name = "source_mark")
    @ApiModelProperty("第三方来源标记，默认为null")
    private String sourceMark;

    /**
     * 模板任务id
     */
    @Column(name = "virtual_task_id")
    @ApiModelProperty("模板任务id")
    private Long virtualTaskId;

    /**
     * 任务状态 1-草稿 2-进行中 3-已结束 4-已提交 5-已退回 6-已撤回 7-未通过 8-通过 9-已转派
     */
    @Column(name = "status")
    @ApiModelProperty("任务状态 1-草稿 2-进行中 3-已结束 4-已提交 5-已退回 6-已撤回 7-未通过 8-通过 9-已转派 10-已取消")
    private Integer status;

    @Column(name = "is_del")
    @ApiModelProperty("是否删除（0：否，1：是）")
    private Integer isDel;

    /**
     * 第三方来源标记uri
     */
    @Column(name = "source_uri")
    @ApiModelProperty("第三方来源标记uri")
    private String sourceUri;

    /**
     * 创建组织id
     */
    @Column(name = "create_org")
    @ApiModelProperty("创建组织id")
    private Long createOrg;

    /**
     * 区县id
     */
    @Column(name = "region_id")
    @ApiModelProperty("区县id")
    private Long regionId;

    /**
     * 派发时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("派发时间")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /**
     * 最后修改人
     */
    @Column(name = "last_change_user")
    @ApiModelProperty("最后修改人")
    private Long lastChangeUser;
}
