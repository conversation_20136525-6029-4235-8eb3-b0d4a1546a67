package com.goodsogood.ows.model.db.score;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * t_score_detail_other 实体类
 *
 * 用户积分流水，专门用来存非学习系统的流水记录 
 *
 * <AUTHOR>
 * @create 2019-03-14 09:22
*/
@Data
@ApiModel
@Table(name = "t_score_detail_other")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScoreDetailOtherEntity {
	
	@Id
	@ApiModelProperty(name = "id")
	@JsonProperty(value = "score_detail_id")
	@Column(name = "score_detail_id")
	private Long scoreDetailId;
	
	
	@ApiModelProperty("用户积分id")
	@JsonProperty(value = "score_user_id")
	@Column(name = "score_user_id")
	private Long scoreUserId;
	
	
	@ApiModelProperty("第三方系统id")
	@JsonProperty(value = "app_id")
	@Column(name = "app_id")
	private Long appId;
	
	
	@ApiModelProperty("消费积分（正负分值）")
	@Column(name = "score")
	private Long score;

	@ApiModelProperty("操作rmb数量")
	@Column(name = "rmb")
	private BigDecimal rmb;

	@ApiModelProperty("融合商城汇率")
	@JsonProperty(value = "rate")
	@Column(name = "rate")
	private Double rate;

	@ApiModelProperty("组织Id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;
	
	
	@ApiModelProperty("单次消费token")
	@Column(name = "token")
	private String token;
	
	
	@ApiModelProperty("操作类型：0:添加；1:扣分； 2:调整")
	@JsonProperty(value = "oper_type")
	@Column(name = "oper_type")
	private Integer operType;
	
	
	@ApiModelProperty("1：党建积分   2：群工积分   3：积分兑换   99:其他")
	@JsonProperty(value = "score_type")
	@Column(name = "score_type")
	private Integer scoreType;
	
	
	@JsonProperty(value = "score_type_name")
	@Column(name = "score_type_name")
	private String scoreTypeName;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
	
	@ApiModelProperty("备注（冗余字段），可以描述产生的原因")
	@Column(name = "remark")
	private String remark;
	
	
	@ApiModelProperty("交易流水号，将uuid字段重命名位本字段")
	@JsonProperty(value = "trade_no")
	@Column(name = "trade_no")
	private String tradeNo;
	
	
	@ApiModelProperty("第三方系统交易流水号")
	@JsonProperty(value = "out_trade_no")
	@Column(name = "out_trade_no")
	private String outTradeNo;
	
	
	@ApiModelProperty("积分生成/消费时间")
	@JsonProperty(value = "consume_time")
	@Column(name = "consume_time")
	private Date consumeTime;

	public ScoreDetailOtherEntity(){}

	private ScoreDetailOtherEntity(Builder builder) {
		setScoreDetailId(builder.scoreDetailId);
		setScoreUserId(builder.scoreUserId);
		setAppId(builder.appId);
		setScore(builder.score);
		setRate(builder.rate);
		setRmb(builder.rmb);
		setOrgId(builder.orgId);
		setToken(builder.token);
		setOperType(builder.operType);
		setScoreType(builder.scoreType);
		setScoreTypeName(builder.scoreTypeName);
		setCreateTime(builder.createTime);
		setUpdateTime(builder.updateTime);
		setRemark(builder.remark);
		setTradeNo(builder.tradeNo);
		setOutTradeNo(builder.outTradeNo);
		setConsumeTime(builder.consumeTime);
	}

	public static Builder newBuilder() {
		return new Builder();
	}

	public static final class Builder {
		private Long scoreDetailId;
		private Long scoreUserId;
		private Long appId;
		private Long score;
		private Double rate;
		private BigDecimal rmb;
		private Long orgId;
		private String token;
		private Integer operType;
		private Integer scoreType;
		private String scoreTypeName;
		private Date createTime;
		private Date updateTime;
		private String remark;
		private String tradeNo;
		private String outTradeNo;
		private Date consumeTime;

		private Builder() {
		}

		public Builder scoreDetailId(Long val) {
			scoreDetailId = val;
			return this;
		}

		public Builder scoreUserId(Long val) {
			scoreUserId = val;
			return this;
		}

		public Builder appId(Long val) {
			appId = val;
			return this;
		}

		public Builder score(Long val) {
			score = val;
			return this;
		}

		public Builder rate(Double val) {
			rate = val;
			return this;
		}

		public Builder rmb(BigDecimal val) {
			rmb = val;
			return this;
		}

		public Builder orgId(Long val) {
			orgId = val;
			return this;
		}

		public Builder token(String val) {
			token = val;
			return this;
		}

		public Builder operType(Integer val) {
			operType = val;
			return this;
		}

		public Builder scoreType(Integer val) {
			scoreType = val;
			return this;
		}

		public Builder scoreTypeName(String val) {
			scoreTypeName = val;
			return this;
		}

		public Builder createTime(Date val) {
			createTime = val;
			return this;
		}

		public Builder updateTime(Date val) {
			updateTime = val;
			return this;
		}

		public Builder remark(String val) {
			remark = val;
			return this;
		}

		public Builder tradeNo(String val) {
			tradeNo = val;
			return this;
		}

		public Builder outTradeNo(String val) {
			outTradeNo = val;
			return this;
		}

		public Builder consumeTime(Date val) {
			consumeTime = val;
			return this;
		}

		public ScoreDetailOtherEntity build() {
			return new ScoreDetailOtherEntity(this);
		}
	}
}

