package com.goodsogood.ows;

import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.filter.TranslateRequestFilter;
import com.goodsogood.ows.helper.SpringContextUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.cache.RedisCacheManagerBuilderCustomizer;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;

@SpringBootApplication
@EnableAsync
@EnableDiscoveryClient
@EnableScheduling
@EnableAspectJAutoProxy
@EnableCaching // 开启spring cache，一旦配置了@cache注解，Redis需要配置CacheManager，一定要写一个定时任务来通过注解@CacheEvict来清理
public class OwsSasApplication {


    private final HttpComponentsClientHttpRequestFactory clientHttpRequestFactory;

    public OwsSasApplication(HttpComponentsClientHttpRequestFactory clientHttpRequestFactory) {
        this.clientHttpRequestFactory = clientHttpRequestFactory;
    }

    public static void main(String[] args) {
        //关闭spring自动装载日志
        // System.setProperty("org.springframework.boot.logging.LoggingSystem", LoggingSystem.NONE);
        ApplicationContext app = SpringApplication.run(OwsSasApplication.class, args);
        SpringContextUtil.setApplicationContext(app);
    }

    @Value("${region-label}")
    private String label;


    @Value("${tog-services.user-center}")
    private String userCenter;

    @LoadBalanced
    @Bean //必须new 一个RestTemplate并放入spring容器当中,否则启动时报错
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        clientHttpRequestFactory.setConnectTimeout(5000);
        clientHttpRequestFactory.setConnectionRequestTimeout(5000);
        restTemplate.setRequestFactory(clientHttpRequestFactory);
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        return restTemplate;
    }

    /**
     * 外网调用
     */
    @Bean("outerRestTemplate")
    public RestTemplate outerRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        SimpleClientHttpRequestFactory simpleClientHttpRequestFactory = new SimpleClientHttpRequestFactory();
        simpleClientHttpRequestFactory.setConnectTimeout(5000);
        simpleClientHttpRequestFactory.setReadTimeout(5000);
        restTemplate.setRequestFactory(simpleClientHttpRequestFactory);
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        return restTemplate;
    }

    @Configuration
    public class ApplicationConfiguration {
        @Bean
        public SimpleApplicationConfigHelper applicationConfigHelper(StringRedisTemplate redisTemplate,
                                                                     RestTemplate restTemplate) {
            // 此处三个参数分别为，StringRedisTemplate
            // restTemplate，注意如果是内部应用直接使用spring cloud的restTemplate，如果是消息中心、微信中心这种独立的项目，请使用原生的restTemplate
            // 第三个参数为用户中心的服务名称，如果是内部应用根据环境自己配置即可，如果是微信中心、消息中心请填写网关地址
            return new SimpleApplicationConfigHelper(redisTemplate, restTemplate, userCenter, label);
        }
    }

    /**
     * 线程池
     *
     * @param i  core pool
     * @param i2 max pool
     * @param i3 queue
     * @param s  name
     * @return new ThreadPoolTaskExecutor
     */
    private Executor getExecutor(int i, int i2, int i3, String s) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(i);
        executor.setMaxPoolSize(i2);
        executor.setQueueCapacity(i3);
        executor.setThreadNamePrefix(s);
        executor.initialize();
        return executor;
    }

    @Bean
    public Executor sasOrgLifeExecutor() {
        return getExecutor(5, 10, 1000, "SAS_ORG_LIFE_");
    }

    /**
     * 验证组织生活变动情况线程池处理
     *
     * @return
     */
    @Bean("valMeetingExecutor")
    public Executor valMeetingExecutor() {
        return getExecutor(10, 20, 1000, "VAl_MEETING_");
    }

    @Bean("reportExecutor")
    public Executor reportExecutor() {
        return getExecutor(2, 5, 15000, "ORG_REPORT_");
    }

    @Bean("sasOrgFeeExecutor")
    public Executor sasOrgFeeExecutor() {
        return getExecutor(6, 12, 5000, "SAS_ORG_PAY_FEE_");
    }

    @Bean("personReportExecutor")
    public Executor personReportExecutor() {
        return getExecutor(4, 8, 5000, "PERSON_REPORT_");
    }

    @Bean("workReportExecutor")
    public Executor workReportExecutor() {
        return getExecutor(4, 8, 10000, "WORK_REPORT_");
    }

    @Bean("workReportUserExecutor")
    public Executor workReportUserExecutor() {
        return getExecutor(4, 8, 10000, "WORK_REPORT_USER_");
    }

    @Bean("dssIndexExecutor")
    public Executor dssIndexExecutor() {
        return getExecutor(1, 1, 1, "DSS_INDEX_");
    }

    @Bean("dssCommitteeExecutor")
    public Executor dssCommitteeExecutor() {
        return getExecutor(10, 10, 50, "DSS_COMMITTEE_");
    }

    @Bean("dssBranchExecutor")
    public Executor dssBranchExecutor() {
        return getExecutor(10, 20, 100, "DSS_BRANCH_");
    }

    @Bean("dssUserExecutor")
    public Executor dssUserExecutor() {
        return getExecutor(20, 20, 100, "DSS_USER_");
    }

    @Bean("dssCommitteeListExecutor")
    public Executor dssCommitteeListExecutor() {
        return getExecutor(10, 20, 20, "DSS_COMMITTEE_LIST_");
    }

    @Bean("dssBranchListExecutor")
    public Executor dssBranchListExecutor() {
        return getExecutor(10, 20, 50, "DSS_BRANCH_LIST_");
    }

    @Bean("dssUserListExecutor")
    public Executor dssUserListExecutor() {
        return getExecutor(20, 20, 100, "DSS_USER_LIST_");
    }

    @Bean("dssAddBufferExecutor")
    public Executor dssAddBufferExecutor() {
        return getExecutor(1, 2, 10_000, "DSS_ADD_BUFFER_");
    }

    @Bean("dssInsertExecutor")
    public Executor dssInsertExecutor() {
        return getExecutor(2, 4, 500_000, "DSS_INSERT_");
    }

    @Bean("dssDeleteFileExecutor")
    public Executor dssDeleteFileExecutor() {
        return getExecutor(1, 1, 1, "DSS_DELETE_FILE_");
    }

    @Bean("dssBuildFileExecutor")
    public Executor dssBuildFileExecutor() {
        return getExecutor(1, 1, 1, "DSS_BUILD_FILE_");
    }

    @Bean("dssBuildFileUserExecutor")
    public Executor dssBuildFileUserExecutor() {
        return getExecutor(6, 12, 20_000, "DSS_BUILD_FILE_USER_");
    }

    @Bean("dssBuildIndexExecutor")
    public Executor dssBuildIndexExecutor() {
        return getExecutor(1, 1, 1, "DSS_INDEX_BUILD_");
    }

    @Bean("userAutoScoreExecutor")
    public Executor userAutoScoreExecutor() {
        return getExecutor(40, 40, 200, "USER_AUTO_SCORE_");
    }

    @Bean("orgAutoScoreExecutor")
    public Executor orgAutoScoreExecutor() {
        return getExecutor(40, 40, 200, "ORG_AUTO_SCORE_");
    }

    @Bean("userCompensateExecutor")
    public Executor userCompensateExecutor() {
        return getExecutor(40, 40, 100000, "USER_COMPENSATE_");
    }

    @Bean("orgCompensateExecutor")
    public Executor orgCompensateExecutor() {
        return getExecutor(40, 40, 10000, "ORG_COMPENSATE_");
    }

    @Bean("dingDingExecutor")
    public Executor dingDingExecutor() {
        return getExecutor(1, 1, 1, "DD_PUSH_");
    }

    @Bean("dssCompensateExecutor")
    public Executor dssCompensateExecutor() {
        return getExecutor(10, 20, 100_000, "DSS_COMPENSATE_");
    }

    @Bean("superviseExecutor")
    public Executor superviseExecutor() {
        return getExecutor(40, 40, 10000, "SUPERVISE_COMPENSATE_");
    }

    @Bean("scoreManagerExecutor")
    public Executor scoreManagerExecutor() {
        return getExecutor(10, 20, 10000, "SCORE_MANAGER_EXECUTOR_");
    }

    @Bean("tbcFusionExecutor")
    public Executor tbcFusionExecutor() {
        return getExecutor(10, 20, 10000, "TBC_FUSION_EXECUTOR_");
    }

    @Bean("superviseManageExecutor")
    public Executor superviseManageExecutor() {
        return getExecutor(40, 40, 10000, "SUPERVISE_MANAGE_EXECUTOR_");
    }


    @Bean("experienceExecutor")
    public Executor experienceExecutor() {
        return getExecutor(10, 20, 10000, "EXPERIENCE_COMPENSATE_");
    }

    @Bean("meetingEvalExecutor")
    public Executor meetingEvalExecutor() {
        return getExecutor(20, 40, 10000, "MEETING_EVAL_EXECUTOR_");
    }

    @Bean("pbmAddBufferExecutor")
    public Executor pbmAddBufferExecutor() {
        return getExecutor(1, 2, 10_000, "PBM_ADD_BUFFER_");
    }

    @Bean("excelImportExecutor")
    public Executor excelImportExecutor() {
        return getExecutor(1, 2, 5, "excel-import");
    }

    @Bean("excelExportExecutor")
    public Executor excelExportExecutor() {
        return getExecutor(1, 2, 5, "excel-export");
    }

    /**
     * 获得 拷贝请求的 过滤器（拦截器使用）
     *
     * @return TranslateRequestFilter
     */
    @Bean
    public FilterRegistrationBean<TranslateRequestFilter> translateRequestFilterRegistration() {
        FilterRegistrationBean<TranslateRequestFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new TranslateRequestFilter());
        registration.addUrlPatterns("/*");
        registration.setOrder(1);
        return registration;
    }
}