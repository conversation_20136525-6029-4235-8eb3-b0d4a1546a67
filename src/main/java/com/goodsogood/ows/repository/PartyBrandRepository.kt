package com.goodsogood.ows.repository

import com.goodsogood.ows.model.mongodb.PartyBrandBase
import com.goodsogood.ows.model.mongodb.meeting.TopPriorityEntity
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

/**
 * <AUTHOR>
 * @date 2024/2/23
 * @description class PartyBrandRepository
 */
@Repository
interface PartyBrandRepository : MongoRepository<PartyBrandBase, String> {
    /**
     * 通过状态和组织id获取党建品牌数量
     * @param status 状态
     * @param orgId 组织id
     * @return Long
     */
    fun countByStatusAndOrgIdIn(status: Int, orgId: MutableCollection<Long>): Long

    /**
     * 通过状态获取当前品牌数量
     * @param status 状态
     * @return Long
     */
    fun countByStatus(status: Int): Long
}