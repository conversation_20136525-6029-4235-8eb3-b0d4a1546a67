package com.goodsogood.ows.utils;

import com.goodsogood.ows.common.DingDingMessage;
import com.goodsogood.ows.configuration.LoggingAspect;
import com.goodsogood.ows.helper.SpringContextUtil;
import com.goodsogood.ows.service.PushDingDingService;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.ThreadContext;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.util.function.Supplier;

/**
 * 日志工具类
 *
 * <AUTHOR>
 * @date 2020/12/3
 */
@Log4j2
public class LoggingUtils {

    private static final String KEY = "method_tracker_id";

    /**
     * 包装方法，每次执行时构造新的tracker_id
     *
     * @param remark     描述
     * @param method     执行方法
     * @param forceReset 是否强制重置method_tracker_id
     * @param <T>        不支持任何异步方法！！！（没必要支持，此方法专用于打分计算，内部均为单线程处理）
     * @return
     */
    public static <T> T wrap(String remark, Supplier<T> method, boolean forceReset, Object... param) {
        String methodTrackerId = UUID.randomUUID().toString().replace("-", "");
        if (forceReset) {
            ThreadContext.put(KEY, methodTrackerId);
        } else {
            // 构造当前方法执行时的tracker_id
            if (!ThreadContext.containsKey(KEY)) {
                ThreadContext.put(KEY, methodTrackerId);
            }
        }
        try {
            T t = method.get();
            ThreadContext.remove(KEY);
            return t;
        } catch (Exception e) {
            StringBuilder paramMsg = new StringBuilder();
            if (param != null || param.length > 0) {
                paramMsg.append(JsonUtils.toJson(param));
            }
            if (e instanceof LoggingAspect.InvokeException) {
                log.error(remark + "执行报错,异常如下,参数 = " + paramMsg, ((LoggingAspect.InvokeException) e).getThrowable());
            } else {
                log.error(remark + "执行报错,异常如下,参数 = " + paramMsg, e);
            }
            try {
                PushDingDingService pushDingDingService = (PushDingDingService) SpringContextUtil.getBean(PushDingDingService.class);
                String content;
                if (e instanceof LoggingAspect.InvokeException) {
                    content = remark + "执行报错,异常[" + ((LoggingAspect.InvokeException) e).getThrowable().toString() + "]";
                } else {
                    content = remark + "执行报错," + e.getClass().getName();
                }
                DingDingMessage dingDingMessage = DingDingMessage.builder()
                        .trackId(methodTrackerId)
                        .time(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                        .content(content)
                        .build();
                pushDingDingService.push(dingDingMessage);
            } catch (Exception ex) {
                log.error("钉钉推送出错", ex);
            }
            WrapLoggingException wrapLoggingException = new WrapLoggingException(methodTrackerId, e);
            ThreadContext.remove(KEY);
            throw wrapLoggingException;
        }
    }

    @Data
    public static class WrapLoggingException extends RuntimeException {

        /**
         * 异常方法tracker_id
         */
        private final String methodTrackerId;
        /**
         * 异常
         */
        private final Exception exception;

        public WrapLoggingException(String methodTrackerId, Exception exception) {
            this.methodTrackerId = methodTrackerId;
            this.exception = exception;
        }
    }

}
