package com.goodsogood.ows.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import lombok.extern.log4j.Log4j2;

import javax.annotation.Nonnull;
import java.io.IOException;
import java.lang.reflect.Type;

/**
 * Description: JSONUtil 工具类
 * Object -> JsonString 或 JsonString -> Object
 *
 * <AUTHOR>
 * @date 2018/12/25 10:10
 */
@Log4j2
public class JSONUtil {
    private static final ObjectMapper OBJECTMAPPER =
            new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

    static {
        OBJECTMAPPER.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        OBJECTMAPPER.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        OBJECTMAPPER.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
//        OBJECTMAPPER.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
    }

    /**
     * Object转JSON String
     *
     * @param t 可序列化对象
     * @return String t=null返回null字符串；异常返回null对象
     */
    public static <T> String toJSONString(T t) throws JsonProcessingException {
        return OBJECTMAPPER.writeValueAsString(t);
    }

    /**
     * Object转JSON String
     *
     * @param t 可序列化对象
     * @return String t=null返回null字符串；忽略异常,返回空字符串
     */
    public static <T> String toJSONIgnoredExc(T t) {
        try {
            return toJSONString(t);
        } catch (JsonProcessingException e) {
            log.error("JSON转换失败:" + e.getMessage(), e);
            return "";
        }
    }

    /**
     * JSONString转Object
     *
     * @param json JSONString
     * @param type 类类型
     * @param <T>  泛型Class
     * @return String
     */
    public static <T> T toObject(@Nonnull String json, Type type) throws IOException {
        return toObject(
                json,
                new TypeReference<T>() {
                    @Override
                    public Type getType() {
                        return type;
                    }
                });
    }

    /**
     * JSONString转Object
     *
     * @param json JSONString
     * @param type TypeReference
     * @param <T>  泛型 Class
     * @return T
     */
    public static <T> T toObject(@Nonnull String json, TypeReference<T> type) throws IOException {
        ObjectReader r = OBJECTMAPPER.readerFor(type).with(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS);
        return r.readValue(json);
    }

    /**
     * JSONString转Object
     *
     * @param json  JSONString
     * @param clazz Class
     * @param <T>泛型 Class
     * @return T
     */
    public static <T> T toObject(@Nonnull String json, Class<T> clazz) throws IOException {
        ObjectReader r = OBJECTMAPPER.readerFor(clazz).with(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS);
        return r.readValue(json);
    }
}
