package com.goodsogood.ows.utils;

import lombok.extern.log4j.Log4j2;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Log4j2
public class FileUtil {

    public static void download(HttpServletResponse response, File file) throws IOException {
        // 发送给客户端的数据
        OutputStream outputStream = response.getOutputStream();
        byte[] buff = new byte[1024];
        setHeader(response, file.getName());
        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file))) {
            // 读取filename
            int i = bis.read(buff);
            while (i != -1) {
                outputStream.write(buff, 0, buff.length);
                outputStream.flush();
                i = bis.read(buff);
            }
        }
    }

    /**
     * 获取路径中的以指定后缀结尾的文件
     *
     * @param path
     * @param endsName
     * @return 文件数组
     */
    public static File[] getEndsWithNameFile(String path, String endsName) {
        File src = new File(path);
        File[] listFiles = new File[]{};
        if (src.isDirectory()) {
            // 存在并且为目录
            listFiles = src.listFiles((dir, name) -> {
                // dir 当前目录对象(sort)
                // name 文件全名称
                //  **规避类似temp.java 的文件目录**
                return new File(dir, name).isFile() && name.endsWith(endsName);
            });
        }
        return listFiles;
    }

    public static void deleteFile(File file) {
        //判断文件不为null或文件目录存在
        if (file == null || !file.exists()) {
            System.out.println("文件删除失败,请检查文件路径是否正确");
            return;
        }
        //取得这个目录下的所有子文件对象
        File[] files = file.listFiles();
        //遍历该目录下的文件对象
        if (null != files && files.length > 0) {
            for (File f : files) {
                //打印文件名
                String name = file.getName();
                System.out.println(name);
                //判断子目录是否存在子目录,如果是文件则删除
                if (f.isDirectory()) {
                    deleteFile(f);
                } else {
                    f.delete();
                }
            }
        }
        //删除空文件夹  for循环已经把上一层节点的目录清空。
        file.delete();
    }

    /**
     * 设置头信息
     *
     * @param response
     * @param fileName
     */
    private static void setHeader(HttpServletResponse response, String fileName) {
        try {
            // 设置响应头的文件名称信息
            String filename = URLEncoder.encode(fileName.replaceAll(" ", ""), "utf-8");
            // 设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename="
                    + new String(filename.getBytes(), StandardCharsets.UTF_8));
            // 设置响应头导出文件格式
            response.setCharacterEncoding("UTF-8");
        } catch (Exception e) {
            log.error("导出设置头信息出错", e);
        }
    }

}
