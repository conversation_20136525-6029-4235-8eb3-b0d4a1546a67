package com.goodsogood.ows.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.function.Function;

/**
 * @Author: mengting
 * @Date: 2022/5/16 15:58
 */


public class NewExcelUtil {
    /**
     * 根绝list生成workbook
     * @param dataList 已经处理成List<List<String>>类型的数据，第一个list相当于有多少行的数据，里面的list代表每行数据
     * @return workbook
     */
    public static SXSSFWorkbook createWorkBook(List<List<String>> dataList){
        // 创建excel工作簿
        SXSSFWorkbook book = new SXSSFWorkbook(100);//在内存中只保留100行记录,超过100就将之前的存储到磁盘里
        Sheet sheet1 = book.createSheet();
        CellStyle cs1 = getStyle(book,1);
        CellStyle cs2 = getStyle(book,2);
        List<String> firstLine = dataList.get(0);
        for(int i = 0 ; i<dataList.size(); i++){
            Row row = sheet1.createRow(i);
            if(i == 0){//第一行是标题行
                for(int j = 0 ;j<firstLine.size();j++){
                    Cell cell = row.createCell(j);
                    cell.setCellStyle(cs1);
                    cell.setCellValue(firstLine.get(j));
                }
            }else{
                List<String> rowData = dataList.get(i);
                for(int j = 0 ;j<firstLine.size();j++){
                    Cell cell =  row.createCell(j);
                    cell.setCellStyle(cs2);
                    cell.setCellValue(rowData.get(j));
                }
            }
        }
        return book;
    }

    /**
     * 设置格式
     * @param book  workbook
     * @param type  1：标题行  2：正常数据行
     * @return  格式
     */
    static  CellStyle getStyle(SXSSFWorkbook book,int type){
        if(1==type){//标题行
            CellStyle cs = book.createCellStyle();
            Font f = book.createFont();
            f.setFontHeightInPoints((short) 10);
            f.setColor(IndexedColors.BLACK.getIndex());
            f.setBold(true);
            f.setColor(IndexedColors.BLACK.getIndex());
            cs.setFont(f);
            cs.setBorderLeft(BorderStyle.THIN);
            cs.setBorderRight(BorderStyle.THIN);
            cs.setBorderTop(BorderStyle.THIN);
            cs.setBorderBottom(BorderStyle.THIN);
            cs.setAlignment(HorizontalAlignment.CENTER);
            return cs;

        }else{
            CellStyle cs2 = book.createCellStyle();
            Font f2 = book.createFont();
            f2.setFontHeightInPoints((short) 10);
            cs2.setFont(f2);
            cs2.setBorderLeft(BorderStyle.THIN);
            cs2.setBorderRight(BorderStyle.THIN);
            cs2.setBorderTop(BorderStyle.THIN);
            cs2.setBorderBottom(BorderStyle.THIN);
            cs2.setAlignment(HorizontalAlignment.CENTER);
            return cs2;
        }
    }

    /**
     * 导出excel
     * @param response  响应流
     * @param name   文件名
     * @param list list数据
     * @param function  将list数据转为List<List<String>>>的方法
     */
    public  static <T> void export(HttpServletResponse response, String name, List<T> list,
                               Function<List<T>,List<List<String>>> function) throws IOException {
            List<List<String>> dataList = function.apply(list);
            SXSSFWorkbook workbook = NewExcelUtil.createWorkBook(dataList);
            //设置输出头
            response.setHeader("content-disposition", "attachment;filename=" +
                    java.net.URLEncoder.encode(name + ".xls", "UTF-8"));
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setContentType("application/octet-stream;charset=UTF-8");
            workbook.write(response.getOutputStream());
    }
}
