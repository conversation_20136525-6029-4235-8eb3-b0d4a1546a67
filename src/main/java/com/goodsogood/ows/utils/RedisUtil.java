package com.goodsogood.ows.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Description: Redis工具类
 *
 * <AUTHOR>
 * @date 2018/8/10 11:37
 */
@Component
@Log4j2
public class RedisUtil {

    private static StringRedisTemplate stringRedisTemplate;
    private static String APPLICATION_NAME;

    @Autowired
    public void setStaticFields(
            StringRedisTemplate stringRedisTemplate,
            @Value("${spring.application.name}") String applicationName) {
        RedisUtil.stringRedisTemplate = stringRedisTemplate;
        RedisUtil.APPLICATION_NAME = applicationName;
    }

    /**
     * 生成key
     *
     * @param key           关键词
     * @param prefix        key前缀 默认当前模块名称
     * @param defaultPrefix 是否有默认模块名为前缀
     * @return key: 前缀+key
     */
    public static String getKey(String key, boolean defaultPrefix, String... prefix) {
        if (defaultPrefix) {
            return RedisUtil.APPLICATION_NAME + ":" + key;
        } else {
            if (prefix.length > 0 && StringUtils.isNotBlank(prefix[0])) {
                return prefix[0] + ":" + key;
            }
        }
        return key;
    }

    /**
     * 添加缓存
     */
    public static <T> void put(String key, T t, long timeout, TimeUnit unit) throws JsonProcessingException {
        if (StringUtils.isBlank(key)) {
            log.debug("添加redis缓存失败！: key->{}", key);
            return;
        }
        String content = JSONUtil.toJSONString(t);
        log.debug("添加redis缓存: key->{};val->{}", key, content);
        RedisUtil.stringRedisTemplate.opsForValue().set(key, content, timeout, unit);
    }

    /**
     * 添加缓存
     */
    public static <T> void put(String key, T t) throws JsonProcessingException {
        if (StringUtils.isBlank(key)) {
            log.debug("添加redis缓存失败: key->{}", key);
            return;
        }
        String content = JSONUtil.toJSONString(t);
        log.debug("添加redis缓存: key->{};val->{}", key, content);
        RedisUtil.stringRedisTemplate.opsForValue().set(key, content);
    }

    /**
     * 刷新缓存有效时间
     *
     * @param key     key
     * @param timeout 时间
     * @param unit    单位
     * @return Boolean
     */
    public Boolean expire(String key, long timeout, TimeUnit unit) {
        return stringRedisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获取缓存
     */
    public static <T> T get(String key, Type type) throws IOException {
        String content = get(key);
        if (StringUtils.isBlank(content)) {
            return null;
        }
        return JSONUtil.toObject(content, type);
    }

    /**
     * 获取缓存
     */
    public static <T> T get(String key, @Nonnull TypeReference<T> type) throws IOException {
        String content = get(key);
        if (StringUtils.isBlank(content)) {
            return null;
        }
        return JSONUtil.toObject(content, type);
    }

    /**
     * 获取缓存
     */
    public static <T> T get(String key, @Nonnull Class<T> clazz) throws IOException {
        String content = get(key);
        if (StringUtils.isBlank(content)) {
            return null;
        }
        return JSONUtil.toObject(content, clazz);
    }

    private static String get(String key) {
        String content = RedisUtil.stringRedisTemplate.opsForValue().get(key);
        log.debug("获取redis缓存: key->{};val->{}", key, content);
        return content;
    }
    /**
     * 判断key是否存在
     */
    public static boolean hasKey(String key) {
        boolean hasKey = !StringUtils.isBlank(key) && RedisUtil.stringRedisTemplate.hasKey(key);
        log.debug("判断缓存是否存在: key->{};hasKey->{}", key, hasKey);
        return hasKey;
    }

    /**
     * 模糊删除
     */
    public static void delete(String pattern) {
        RedisUtil.stringRedisTemplate.delete(keys(pattern));
    }

    /**
     * 获取keys
     */
    private static Set<String> keys(String pattern) {
        return RedisUtil.stringRedisTemplate.keys(pattern);
    }


}
