package com.goodsogood.ows.utils;

import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-06-12 15:51
 **/
public class ListUtils {

    private static final String REGEX_COMMA = ",";

    /**
     * 逗号分割的int字符串转换为list<int>
     *
     * @param intString int字符串
     * @return List<Integer>
     */
    public static List<Integer> intStringToList(String intString) {
        List<Integer> integerList = new ArrayList<>(16);
        if (StringUtils.isNotBlank(intString)) {
            for (String i : intString.split(REGEX_COMMA)) {
                if (StringUtils.isNotBlank(i)) {
                    integerList.add(Integer.valueOf(i.trim()));
                }
            }
        }
        return integerList;
    }

    /**
     * 字符串拼接
     */
    public static String join(CharSequence conjunction, Iterable iterable) {
        if (iterable == null) return null;
        StringBuilder stringBuilder = new StringBuilder();
        iterable.forEach(x -> stringBuilder.append(conjunction).append(x.toString()));
        return stringBuilder.substring(1, stringBuilder.length());
    }

    /**
     * 把list 按大小数据分成几个List
     *
     * @param list
     * @param unitSize
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> splitList(List<T> list, int unitSize) {
        if (list == null || list.isEmpty() || unitSize < 1) {
            return null;
        }
        int wholeSize = list.size();
        int groupNum = wholeSize / unitSize;
        List<List<T>> result = new ArrayList<>();
        for (int i = 0; i < groupNum; i++) {
            List<T> subList = list.subList(i * unitSize, (i + 1) * unitSize);
            result.add(subList);
        }
        if (unitSize * groupNum < wholeSize) {
            List<T> restList = list.subList(unitSize * groupNum, wholeSize);
            result.add(restList);
        }
        return result;
    }


}
