package com.goodsogood.ows.utils;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe 自定义sql 工具类
 * @date 2018-11-23
 */
@Component
public class SqlProviderUtils {


    /**
     * 交费精度 1 元、2 角、3 分
     */
    private static final Integer staticPayUnit = 3;

    /**
     * 交费规则 1 向上取整、2 向下取整、3 四舍五入
     */
    private static final Integer staticPayRule = 2;

    /**
     * 预执行sql in map里面会放入这些参数
     *
     * @param map 参数
     * @param ids in查询ids参数
     * @return #{alias_id_0_0},#{alias_id_0_1},#{alias_id_0_2}
     */
    public static String sqlListPrepare(Map<String, Object> map, List<?> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return "";
        }

        String alias = generateIdAlias(map);

        StringBuilder idsSql = new StringBuilder();
        for (int i = 0; i < ids.size(); i++) {
            String item = alias + "_" + i;
            map.put(item, ids.get(i));
            idsSql.append("#{").append(item).append("},");
        }

        return idsSql.substring(0, idsSql.length() - 1);

    }

    public static String sqlListPrepare(Map<String, Object> map, String... ids) {
        return sqlListPrepare(map, Arrays.asList(ids));
    }

    private static String generateIdAlias(Map<String, Object> map) {
        return generateIdAlias(map, 0);
    }

    private static String generateIdAlias(Map<String, Object> map, Integer index) {
        String key = "alias_id_" + index;

        if (map.containsKey(key)) {
            return generateIdAlias(map, index + 1);
        } else {
            map.put(key, new Object());
            return key;
        }
    }

    /**
     * 根据对应的字段和配置文件对金额的保留位数、进位规则配置，拼接sql查询字符串
     * payUnit 交费精度 1 元、2 角、3 分
     * payRule 交费规则 1 向上取整、2 向下取整、3 四舍五入
     * tc 2019.01.07
     */
    public static String payShouldSqlHelper(String fieldOne, String fieldTwo) {
        //保留位数
        int defaultScale = staticPayUnit == 1 ? 0 : staticPayUnit == 2 ? 1 : staticPayUnit == 3 ? 2 : 1;
        //使用函数
        String method = staticPayRule == 1 ? "ceiling" : staticPayRule == 2 ? "floor" : staticPayRule == 3 ? "round" : "ceiling";
        //非四舍五入的规则时，介入变量
        String i = String.valueOf(Math.pow(10, defaultScale));
        //eg:ceiling(tab1.cardinalNumber/100*tab4.proportion/100*100)/100
        //eg:round(tab1.cardinalNumber/100*tab4.proportion/100,2)
        String re = "";
        if ("round".equals(method)) {
            re = method + "(" + fieldOne + "*" + fieldTwo + "," + defaultScale + ")";
        } else {
            re = method + "(" + fieldOne + "*" + fieldTwo + "*" + i + ")/" + i;
        }
        return re;
    }

}
