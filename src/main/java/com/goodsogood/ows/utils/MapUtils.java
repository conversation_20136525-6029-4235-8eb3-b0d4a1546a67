package com.goodsogood.ows.utils;

import java.util.Map;

/**
 * 时间转换
 *
 * <AUTHOR>
 * @date 2019/4/23 18:12
 */
public class MapUtils {
    public static String getString(Map<String, Object> map, String key, String defaultVal) {
        if (map.containsKey(key) && map.get(key) != null) {
            return map.get(key).toString();
        }
        return defaultVal;
    }

    public static Double getDouble(Map<String, Double> map, String key, Double defaultVal) {
        if (map.containsKey(key) && map.get(key) != null) {
            return map.get(key);
        }
        return defaultVal;
    }

    public static Integer getInt(Map<String, Object> map, String key, Integer defaultVal) {
        if (map.containsKey(key) && map.get(key) != null) {
            return Integer.valueOf(map.get(key).toString());
        }
        return defaultVal;
    }

    public static Long getLong(Map<String, Object> map, String key, Long defaultVal) {
        if (map.containsKey(key) && map.get(key) != null) {
            return Long.valueOf(map.get(key).toString());
        }
        return defaultVal;
    }

    public static boolean isNotEmpty(Map map) {
        return !isEmpty(map);
    }

    public static boolean isEmpty(Map map) {
        return map == null || map.isEmpty();
    }
}
