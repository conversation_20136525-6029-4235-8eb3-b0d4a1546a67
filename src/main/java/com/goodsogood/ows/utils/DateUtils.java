package com.goodsogood.ows.utils;

import com.goodsogood.ows.model.vo.sas.DateResult;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.util.Assert;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 时间转换
 *
 * <AUTHOR>
 * @date 2019/4/23 18:12
 */
@Log4j2
public class DateUtils {
    /**
     * 12 月份
     */
    public static final int DECEMBER = 12;

    /**
     * 根据年月获取日期
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/4/23 19:04
     */

    public static DateResult getDate(Integer year, Integer startMonth, Integer endMonth) {
        DateResult result = new DateResult();
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, startMonth - 1);
        Date startDate = calendar.getTime();
        calendar.set(Calendar.MONTH, endMonth - 1);
        Date endDate = calendar.getTime();
        result.setStartTime(startDate);
        result.setEndTime(endDate);
        return result;
    }

    /**
     * dateFormat yyyy-mm
     *
     * @param year  年份
     * @param month 月份
     * @return yyyy-mm
     */
    public static String dateFormat(int year, int month) {
        if (month > 9) {
            return year + "-" + month;
        }
        return year + "-0" + month;
    }

    /**
     * 格式化日期
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/4/23 19:14
     */

    public static String dateFormat(Date date, String format) {
        if (date == null) {
            return "";
        }
        if (StringUtils.isBlank(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        // 设置要获取到什么样的时间
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        // 获取String类型的时间
        return sdf.format(date);
    }

    /**
     * 得到统计开始的时间-到当月所有月份，不包括当月
     *
     * @return
     */
    public static List<String> getMonthsBetweenTimes(String staStartMonth) {
        List<String> list = new ArrayList<>();
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
            String nowDate = format.format(new Date());//当前月份
            Date d1 = new SimpleDateFormat("yyyy-MM").parse(staStartMonth);
            Date d2 = new SimpleDateFormat("yyyy-MM").parse(nowDate);
            //定义日期实例
            Calendar dd = Calendar.getInstance();
            dd.setTime(d1);//设置日期起始时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            while (dd.getTime().before(d2)) {//判断是否到结束日期
                String str = sdf.format(dd.getTime());
                list.add(str);//输出日期结果
                dd.add(Calendar.MONTH, 1);//进行当前日期月份加1
            }
            return list;
        } catch (Exception e) {
            System.out.println("异常" + e.getMessage());
            return null;
        }
    }

    /**
     * 获取指定日期月份
     *
     * @return Calendar.MONTH 月份从0开始，返回时+1 ; date=null return null
     */
    public static Integer getMonth(Date date) {
        if (date == null) {
            return null;
        }
        Calendar cn = Calendar.getInstance();
        cn.setTime(date);
        return cn.get(Calendar.MONTH) + 1;
    }

    /**
     * 获取指定日期年份
     */
    public static Integer getYear(Date date) {
        if (date == null) {
            return null;
        }
        Calendar cn = Calendar.getInstance();
        cn.setTime(date);
        return cn.get(Calendar.YEAR);
    }


    /**
     * 获取指定年第一天
     *
     * @return Date
     */
    public static Date firstDayOfYear(Integer year) {
        if (year == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.MONTH, 0);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMinimum(Calendar.DAY_OF_MONTH));
        set0Millisecond(c);
        return c.getTime();
    }

    /**
     * 获取指定年最后一天
     *
     * @return Date
     */
    public static Date endDayOfYear(Integer year) {
        if (year == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.MONTH, 12);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMinimum(Calendar.DAY_OF_MONTH));
        set0Millisecond(c);
        return c.getTime();
    }

    /**
     * 获取指定年/月的第一天
     *
     * @return Date
     */
    public static Date firstDayOfMonthByDate(Date date) {
        if (date == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        // 设置为当前月第一天
        c.set(Calendar.DAY_OF_MONTH, c.getActualMinimum(Calendar.DAY_OF_MONTH));
        set0Millisecond(c);
        return c.getTime();
    }

    /**
     * 获取指定年/月的第一天
     *
     * @param date yyyy-MM
     * @return yyyy-MM-dd
     */
    public static String firstDayOfMonthByDate(String date) {
        if (date == null) {
            return null;
        }
        Date date1 = stringToDate(date, "yyyy-MM");
        date1 = firstDayOfMonthByDate(date1);
        String format = dateFormat(date1, "yyyy-MM-dd");
        return format;
    }

    /**
     * 获取指定年/月的最后一天
     *
     * @return Date
     */
    public static Date lastDayOfMonthByDate(Date date) {
        if (date == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        // 设置为当前月最后一天
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        setLastMillisecond(c);
        return c.getTime();
    }

    /**
     * 获取指定年/月的最后一天
     *
     * @param date yyyy-MM
     * @return yyyy-MM-dd
     */
    public static String lastDayOfMonthByDate(String date) {
        if (date == null) {
            return null;
        }
        Date date1 = stringToDate(date, "yyyy-MM");
        date1 = lastDayOfMonthByDate(date1);
        String format = dateFormat(date1, "yyyy-MM-dd");
        return format;
    }

    /**
     * 设置时分秒 00:00:00
     */
    private static Calendar set0Millisecond(Calendar cn) {
        cn.set(Calendar.HOUR_OF_DAY, 0);
        cn.set(Calendar.MINUTE, 0);
        cn.set(Calendar.SECOND, 0);
        cn.set(Calendar.MILLISECOND, 0);
        return cn;
    }


    /**
     * 设置为当前月最后一天
     *
     * @param c Calendar
     */
    private static void setLastMillisecond(Calendar c) {
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
    }

    /**
     * 获得指定前 后 i 月的数据
     *
     * @param i 当前时间前为负数
     * @return yyyy-MM
     */
    public static String getBeforeTimeOfMonth(int i) {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH, i);
        return new SimpleDateFormat("yyyy-MM").format(c.getTime());
    }

    /**
     * 获取指定月份的最后一天最后一秒
     *
     * @param statsDate
     * @return
     */
    public static String getEndOfMonth(String statsDate) {
        if (org.springframework.util.StringUtils.isEmpty(statsDate)) {
            return null;
        }
        Integer year = Integer.valueOf(statsDate.split("-")[0]);
        Integer month = Integer.valueOf(statsDate.split("-")[1]);
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime()) + " 23:59:59";
    }

    /**
     * String 转换为 Date
     *
     * @param dateString
     * @param dateFormat
     * @return java.util.Date
     * <AUTHOR>
     * @date 2019/12/6
     */
    public static Date stringToDate(String dateString, String dateFormat) {
        if (StringUtils.isBlank(dateFormat)) {
            dateFormat = "yyyy-MM-dd HH:mm:ss";
        }
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        Date date = null;
        try {
            date = sdf.parse(dateString);
        } catch (ParseException e) {
            log.error("时间转换格式错误", e);
        }
        return date;
    }

    /**
     * 指定月是否小于等于查询月
     *
     * @param sourceTime 指定月
     * @param targetTime 查询月
     * @return
     */
    public static boolean isInclude(String sourceTime, String targetTime) {
        Assert.notNull(sourceTime, "this sourceTime is required; it must not be null");
        Assert.notNull(targetTime, "this targetTime is required; it must not be null");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
        try {
            return simpleDateFormat.parse(sourceTime).getTime() <= simpleDateFormat.parse(targetTime).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取间隔月数集合
     *
     * @param stime
     * @param etime
     * @param excludeAfertMonth 是否排除当前月以后的月份  0 否 1 是 默认 0
     * @return
     */
    public static List<String> getBetweenMonthsList(String stime, String etime, Integer excludeAfertMonth) {
        if (excludeAfertMonth == null) {
            excludeAfertMonth = 0;
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM");
        Date sdate = null;
        Date eDate = null;
        try {
            sdate = df.parse(stime);
            eDate = df.parse(etime);
        } catch (ParseException e) {
            log.error("获取查询条件里开始时间和结束时间包含月份集合转换两个时间格式时出错，" +
                    "stime = {}, etime = {} format = {}", stime, etime, "yyyy-MM");
        }
        Calendar c = Calendar.getInstance();
        List<String> list = new ArrayList<String>();
        while (sdate.getTime() <= eDate.getTime()) {
            if (excludeAfertMonth == 1) {
                //判断是否是本月以后的月份
                if (!DateUtils.afertThisMonth(df.format(sdate))) {
                    list.add(df.format(sdate));
                }
            } else {
                list.add(df.format(sdate));
            }
            c.setTime(sdate);
            c.add(Calendar.MONTH, 1); // 月份加1天
            sdate = c.getTime();
        }
        return list;
    }

    /**
     * 判断是否在当前月之后
     *
     * @param statsDate
     * @return
     */
    public static boolean afertThisMonth(String statsDate) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-m");
        Date date = new Date();

        return df.format(date).compareTo(statsDate) < 0;
    }

    /**
     * 转换日期格式
     *
     * @param statsDate
     * @return
     */
    public static String toFormat(String statsDate, String inputFormat, String outFormat) {
        if (org.springframework.util.StringUtils.isEmpty(inputFormat)) {
            inputFormat = "yyyy-MM";
        }
        if (org.springframework.util.StringUtils.isEmpty(outFormat)) {
            outFormat = "yyyy年M月";
        }
        SimpleDateFormat input = new SimpleDateFormat(inputFormat);
        SimpleDateFormat out = new SimpleDateFormat(outFormat);
        try {
            Date date = input.parse(statsDate);
            return out.format(date);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换日期格式
     *
     * @param date
     * @return
     */
    public static String toFormatDate(Date date, String formatStr) {
        SimpleDateFormat sdf = new SimpleDateFormat(formatStr);
        try {
            return sdf.format(date);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 判断是否为当前月
     *
     * @param statsDate
     * @return
     */
    public static boolean isCurrentMonth(String statsDate) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM");
        Date date = new Date();
        return df.format(date).equals(statsDate);
    }

    /**
     * 判断是否为当前月
     *
     * @param statsDate
     * @return
     */
    public static boolean isThisMonth(String statsDate) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM");
        Date date = new Date();
        return df.format(date).equals(statsDate);
    }

    /**
     * 判断查询月是否大于当前时间
     *
     * @param queryDateStr 查询的时间 yyyy-MM
     */
    public static boolean isAfterDateMonth(String queryDateStr) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
        return simpleDateFormat.format(new Date()).compareTo(queryDateStr) <= 0;
    }

    /**
     * 判断是否为当前月
     *
     * @param year
     * @param month
     * @return
     */
    public static boolean isCurrentMonth(int year, int month) {
        DateTime now = DateTime.now();
        return now.getYear() == year && now.getMonthOfYear() == month;
    }

    /**
     * LocalDateTime -> Date
     *
     * @param localDateTime
     * @return
     */
    public static Date asDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant());
    }

    /**
     * Date -> LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime asLocalDateTime(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
    }

    /**
     * 根据年份确定月份值,如果是当年月份为当前月份，历史年份使用12月
     */
    public static Integer getEndMonth(Integer year) {
        Integer yearNow = LocalDate.now().getYear();
        Integer month = 12;
        if (yearNow.equals(year)) {
            month = LocalDate.now().getMonthValue();
        }
        return month;
    }

    /**
     * 根据年获取指定年最有效的月份
     *
     * @param year
     * @return
     */
    public static Integer getValidMonth(Integer year) {
        LocalDate now = LocalDate.now();
        if (year < now.getYear()) {
            return 12;
        }
        return now.getMonthValue();
    }


    //连续前面几个月不包括当月
    public static List<String> getBeforeMonth(Integer monthCount) {
        List<String> listDate = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        for (int i = 1; i <= monthCount; i++) {
            Date date = new Date();
            Calendar calendar = Calendar.getInstance(); //得到日历
            calendar.setTime(date);//把当前时间赋给日历
            calendar.add(Calendar.MONTH, -(i));
            date = calendar.getTime();
            listDate.add(dateFormat.format(date));
        }
        return listDate;
    }

    //连续前面几个月不包括当月
    public static List<String> getBeforeMonth(Integer monthCount, String formart) {
        List<String> listDate = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat(formart);
        for (int i = 1; i <= monthCount; i++) {
            Date date = new Date();
            Calendar calendar = Calendar.getInstance(); //得到日历
            calendar.setTime(date);//把当前时间赋给日历
            calendar.add(Calendar.MONTH, -(i));
            date = calendar.getTime();
            listDate.add(dateFormat.format(date));
        }
        return listDate;
    }


    //连续前面几个月括当月
    public static List<String> getContinuousMonth(Integer monthCount, String formart) {
        List<String> listDate = new LinkedList<>();
        SimpleDateFormat dateFormat = null;
        if (null == formart) {
            dateFormat = new SimpleDateFormat("yyyy-MM");
        } else {
            dateFormat = new SimpleDateFormat(formart);
        }
        for (int i = monthCount; i >= 0; i--) {
            Date date = new Date();
            Calendar calendar = Calendar.getInstance(); //得到日历
            calendar.setTime(date);//把当前时间赋给日历
            calendar.add(Calendar.MONTH, -(i));
            date = calendar.getTime();
            listDate.add(dateFormat.format(date));
        }
        return listDate;
    }

    /**
     * 算的是当前季度与上一个季度的数据 现在不使用 保存
     *
     * @param currentQuarterly
     * @return
     */
    public static List<String> getMonthsByQuarterlyBak(int currentQuarterly) {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance(); //得到日历
        calendar.setTime(date);
        //如果是第一季度
        int month = calendar.get(Calendar.MONTH) + 1;
        if (currentQuarterly == 1 || currentQuarterly == 2) {
            return getBeforeMonth(month + 6);
        } else {
            int startMonth = (currentQuarterly - 2) * 3;
            return getBeforeMonth(month - startMonth);
        }
    }


    /**
     * @param currentQuarterly 连续前两个季度
     * @return
     */
    public static List<String> getMonthsByQuarterly(int currentQuarterly) {
        List<String> list = new LinkedList<>();
        int currentYear = getCurrentYear();
        if (currentQuarterly == 1) {
            //得到上年数据
            int lastYear = currentYear - 1;
            list.add("" + lastYear + "-" + "07");
            list.add("" + lastYear + "-" + "08");
            list.add("" + lastYear + "-" + "09");
            list.add("" + lastYear + "-" + "10");
            list.add("" + lastYear + "-" + "11");
            list.add("" + lastYear + "-" + "12");
            list.add("" + currentYear + "-" + "01");
            list.add("" + currentYear + "-" + "02");
            list.add("" + currentYear + "-" + "03");
            return list;
        } else if (currentQuarterly == 2) {
            //得到上年数据
            int lastYear = currentYear - 1;
            //去年的第四季度的年月
            list.add("" + lastYear + "-" + "10");
            list.add("" + lastYear + "-" + "11");
            list.add("" + lastYear + "-" + "12");
            //今年的第一季度
            list.add("" + currentYear + "-" + "01");
            list.add("" + currentYear + "-" + "02");
            list.add("" + currentYear + "-" + "03");
            list.add("" + currentYear + "-" + "04");
            list.add("" + currentYear + "-" + "05");
            list.add("" + currentYear + "-" + "06");
            return list;
        } else if (currentQuarterly == 3) {
            list.add("" + currentYear + "-" + "01");
            list.add("" + currentYear + "-" + "02");
            list.add("" + currentYear + "-" + "03");
            list.add("" + currentYear + "-" + "04");
            list.add("" + currentYear + "-" + "05");
            list.add("" + currentYear + "-" + "06");
            list.add("" + currentYear + "-" + "07");
            list.add("" + currentYear + "-" + "08");
            list.add("" + currentYear + "-" + "09");
            return list;
        } else if (currentQuarterly == 4) {
            list.add("" + currentYear + "-" + "04");
            list.add("" + currentYear + "-" + "05");
            list.add("" + currentYear + "-" + "06");
            list.add("" + currentYear + "-" + "07");
            list.add("" + currentYear + "-" + "08");
            list.add("" + currentYear + "-" + "09");
            list.add("" + currentYear + "-" + "10");
            list.add("" + currentYear + "-" + "11");
            list.add("" + currentYear + "-" + "12");
            return list;
        }
        return null;
    }


    /**
     * @param currentQuarterly 连续前两个季度
     * @return
     */
    public static List<String> getMonthsByQuarterlyName(int currentQuarterly) {
        List<String> list = new LinkedList<>();
        int currentYear = getCurrentYear();
        if (currentQuarterly == 1) {
            //得到上年数据
            int lastYear = currentYear - 1;
            list.add("" + lastYear + "年" + "第3");
            list.add("" + lastYear + "年" + "第4");
            return list;
        } else if (currentQuarterly == 2) {
            //得到上年数据
            int lastYear = currentYear - 1;
            list.add("" + lastYear + "年" + "第4");
            list.add("" + currentYear + "年" + "第1");
            return list;
        } else if (currentQuarterly == 3) {
            list.add("" + currentYear + "年" + "第1");
            list.add("" + currentYear + "年" + "第2");
            return list;
        } else if (currentQuarterly == 4) {
            list.add("" + currentYear + "年" + "第2");
            list.add("" + currentYear + "年" + "第3");
            return list;
        }
        return null;
    }

    /**
     * @param currentQuarterly 得到连续两个季度 第一个季度最后一个月
     * @return
     */
    public static String getLastQuarterlyEndMonth(int currentQuarterly) {
        int currentYear = getCurrentYear();
        int lastYear = currentYear - 1;
        if (currentQuarterly == 1) {
            return ("" + lastYear + "-" + "09");
        } else if (currentQuarterly == 2) {
            return ("" + lastYear + "-" + "12");
        } else if (currentQuarterly == 3) {
            return ("" + currentYear + "-" + "03");
        } else if (currentQuarterly == 4) {
            return ("" + currentYear + "-" + "06");
        }
        return null;
    }

    /**
     * month
     *
     * @return
     */
    public static String getNextMonth(String month) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
            Date date = dateFormat.parse(month);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.MONTH, 1);
            date = calendar.getTime();
            return dateFormat.format(date);
        } catch (Exception ex) {
            log.error("getNextMonth解析出错，{}", ex);
            return null;
        }
    }


    /**
     * month
     *
     * @return
     */
    public static String getLastMonth(String month) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
            Date date = dateFormat.parse(month);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.MONTH, -1);
            date = calendar.getTime();
            return dateFormat.format(date);
        } catch (Exception ex) {
            log.error("getNextMonth解析出错，{}", ex);
            return null;
        }
    }


    /**
     * 得到当前所在季度
     *
     * @return
     */
    public static Integer getCurrentQuarterly() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        return calendar.get(Calendar.MONTH) / 3 + 1;
    }

    /**
     * 得到当前所在季节
     *
     * @return
     */
    public static Integer getCurrentYear() {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance(); //得到日历
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);

    }


    /**
     * 得到本年季度开始时间
     *
     * @return
     */
    public static String getCurrentQuarterlyStartTime(int currentQuarterly) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int currentYear = getCurrentYear();
        int month = currentQuarterly == 1 ? 1 : (currentQuarterly - 1) * 3;
        return "" + currentYear + "-" + (month < 10 ? "0" + month : month);
    }


    /**
     * 得到本年季度结束时间
     *
     * @return
     */
    public static String getCurrentQuarterlyEndTime(int currentQuarterly) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int currentYear = getCurrentYear();
        int month = (currentQuarterly) * 3;
        return "" + currentYear + "-" + (month < 10 ? "0" + month : month);
    }

    /**
     * 根据年 月 获取对应的月份 天数
     */
    public static int getDaysByYearMonth(int year, int month) {
        Calendar a = Calendar.getInstance();
        a.set(Calendar.YEAR, year);
        a.set(Calendar.MONTH, month - 1);
        a.set(Calendar.DATE, 1);
        a.roll(Calendar.DATE, -1);
        int maxDate = a.get(Calendar.DATE);
        return maxDate;
    }


    public static void main(String[] args) throws IOException {
        int daysOfMonth = getDaysOfMonth(new Date(), -1);
        System.out.println(Calendar.getInstance().get(Calendar.DAY_OF_MONTH));
        System.out.println(daysOfMonth);
    }


    //当得到当月天数 month=0(当月） month= -1(上个月）
    public static int getDaysOfMonth(Date date, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, month);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }


    /**
     * 得到当前时间
     */
    public static String getCurrentFormatTime() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        return simpleDateFormat.format(new Date());
    }

    /**
     * 得到当前月
     *
     * @return
     */
    public static String getCurrentMonth() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMM");
        return simpleDateFormat.format(new Date());
    }


    public static String getCurrentMonth(String dateFormat) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
        return simpleDateFormat.format(new Date());
    }

    /**
     * 获取当前时间对应月数偏移量的时间
     *
     * @param month
     * @param format 指定的格式
     * @param dt     指定的日期
     * @return
     */
    public static String getDateByMonths(Integer month, String format, String dt) throws Exception {
        if (org.springframework.util.StringUtils.isEmpty(format)) {
            format = "yyyy-MM";
        }
        SimpleDateFormat df = new SimpleDateFormat(format);
        Date date;
        if (org.springframework.util.StringUtils.isEmpty(dt)) {
            date = new Date();
        } else {
            date = df.parse(dt);
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        //日期天偏移量
        c.add(Calendar.MONTH, month);
        date = c.getTime();
        return df.format(date);
    }

    /**
     * 获取当前时间对应天数偏移量的时间
     *
     * @param day
     * @return
     */
    public static String getDateByDays(Integer day, String format) {
        if (org.springframework.util.StringUtils.isEmpty(format)) {
            format = "yyyy-MM-dd";
        }
        SimpleDateFormat df = new SimpleDateFormat(format);
        Date date = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        //日期天偏移量
        c.add(Calendar.DATE, day);
        date = c.getTime();
        return df.format(date);
    }

    /**
     * 根据月份范围查询周数
     *
     * @param startTime yyyy-MM-dd
     * @param endTime   yyyy-MM-dd
     * @return List
     */
    public static List<Integer> getWeeksBetweenDates(String startTime, String endTime) {
        List<Integer> result = new ArrayList<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date start;
        Date end;
        try {
            start = format.parse(startTime);
            end = format.parse(endTime);
            Calendar calendar = Calendar.getInstance();
            calendar.setFirstDayOfWeek(Calendar.MONDAY);
            calendar.setTime(start);
            result.add(calendar.get(Calendar.WEEK_OF_YEAR));
            while (calendar.getTime().before(end)) {
                calendar.add(Calendar.WEEK_OF_YEAR, 1);
                Integer weekStr = calendar.get(Calendar.WEEK_OF_YEAR);
                result.add(weekStr);
            }
        } catch (ParseException e) {
            log.error("系统异常{}", e.getMessage(), e);
        }
        return result;
    }

    /**
     * 指定月份的季度
     *
     * @return Quarter
     */
    public static Integer getQuarterly() {
        String quarterly= ""+  Calendar.getInstance().get(Calendar.YEAR)+getQuarter(new Date()).quarter;
        return Integer.valueOf(quarterly);
    }

    /**
     * 指定日期的季度
     *
     * @param date 指定日期为null，返回null
     * @return 未匹配季度 返回null
     */
    public static Quarter getQuarter(Date date) {
        if (date == null) {
            return null;
        }
        int currentMonth = getMonth(date);
        return getQuarterByMonth(currentMonth);
    }

    /**
     * 指定月份的季度
     *
     * @param currentMonth 月份
     * @return Quarter
     */
    public static Quarter getQuarterByMonth(int currentMonth) {
        Quarter currentQuarter = null;
        for (Quarter quarter : Quarter.values()) {
            if (quarter.startMonth <= currentMonth && quarter.endMonth >= currentMonth) {
                currentQuarter = quarter;
                break;
            }
        }
        return currentQuarter;
    }

    public enum Quarter {
        // 第一季度"
        ONE(1, 1, 3, "第一季度"),
        // 第二季度"
        TWO(2, 4, 6, "第二季度"),
        // 第三季度
        THREE(3, 7, 9, "第三季度"),
        // 第四季度
        FOUR(4, 10, 12, "第四季度");
        /**
         * 季度
         */
        private int quarter;
        /**
         * 季度开始月份
         */
        private int startMonth;
        /**
         * 季度截止月份
         */
        private int endMonth;
        /**
         * 描述
         */
        private String description;

        public int getQuarter() {
            return quarter;
        }

        public int getStartMonth() {
            return startMonth;
        }

        public int getEndMonth() {
            return endMonth;
        }

        public String getDescription() {
            return description;
        }

        /**
         * @param quarter     季度
         * @param startMonth  开始月份
         * @param endMonth    结束月份
         * @param description 中文描述
         */
        Quarter(int quarter, int startMonth, int endMonth, String description) {
            this.quarter = quarter;
            this.startMonth = startMonth;
            this.endMonth = endMonth;
            this.description = description;
        }

        /**
         * 季度
         *
         * @param quarter 季度
         * @return Quarter
         */
        public static Quarter quarter(int quarter) {
            Quarter currentQuarter = null;
            for (Quarter q : Quarter.values()) {
                if (q.getQuarter() == quarter) {
                    currentQuarter = q;
                    break;
                }
            }
            return currentQuarter;
        }

    }

}
