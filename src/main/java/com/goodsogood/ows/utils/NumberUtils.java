package com.goodsogood.ows.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;

/**
 * <AUTHOR>
 * @date 2019/12/3
 * Description:
 */
public class NumberUtils {

    /**
     * 默认小数位数
     */
    private static final int DEFAULT_SCALE = 2;

    /**
     * 交费精度 1 元、2 角、3 分
     */
    private static Integer staticPayUnit;

    /**
     * 交费规则 1 向上取整、2 向下取整、3 四舍五入
     */
    private static Integer staticPayRule;

    /**
     * 相除
     *
     * @param before    被除数
     * @param after     除数
     * @param scale     保留小数点位数
     * @param roundType 取小数点规则 //向零方向舍入 RoundingMode.DOWN //四舍五入 RoundingMode.HALF_UP
     */
    public static BigDecimal divide(Number before, Number after, int scale, RoundingMode roundType) {
        if (null == before || null == after) {
            return new BigDecimal("0");
        }
//        Assert.notNull(before, "before must not be null");
//        Assert.notNull(after, "after must not be null");
        return new BigDecimal(before.toString()).divide(new BigDecimal(after.toString()), scale, roundType);
    }

    /**
     * double的乘法运算
     */
    public static Long multiplyLong(double d1, double d2) {
        BigDecimal b1 = BigDecimal.valueOf(d1);
        BigDecimal b2 = BigDecimal.valueOf(d2);
        return b1.multiply(b2).longValue();
    }

    /**
     * double的乘法运算
     */
    public static Integer multiplyInteger(double d1, double d2) {
        BigDecimal b1 = BigDecimal.valueOf(d1);
        BigDecimal b2 = BigDecimal.valueOf(d2);
        return b1.multiply(b2).intValue();
    }

    /**
     * double的乘法运算
     */
    public static Double multiplyDouble(double d1, double d2) {
        BigDecimal b1 = BigDecimal.valueOf(d1);
        BigDecimal b2 = BigDecimal.valueOf(d2);
        return b1.multiply(b2).doubleValue();
    }

    /**
     * double的乘法运算
     */
    public static double multiply(long d1, double d2) {
        BigDecimal b1 = BigDecimal.valueOf(d1);
        BigDecimal b2 = BigDecimal.valueOf(d2);
        return b1.multiply(b2).doubleValue();
    }

    /**
     * Double的除法运算
     * payUnit 交费精度 1 元、2 角、3 分
     * payRule 交费规则 1 向上取整、2 向下取整、3 四舍五入
     *
     * @param d1
     * @param d2
     */
    public static double divideCeiling(Double d1, Double d2) {
        BigDecimal b1 = BigDecimal.valueOf(d1);
        BigDecimal b2 = BigDecimal.valueOf(d2);
        //保留位数
        int defaultScale = staticPayUnit == 1 ? 0 : staticPayUnit == 2 ? 1 : staticPayUnit == 3 ? 2 : 1;
        //进位模式
        int roundModel = staticPayRule == 1 ? BigDecimal.ROUND_CEILING : staticPayRule == 2 ? BigDecimal.ROUND_FLOOR : staticPayRule == 3 ? BigDecimal.ROUND_HALF_UP : BigDecimal.ROUND_FLOOR;
        return b1.divide(b2, defaultScale, roundModel).doubleValue();
    }

    /**
     * double的除法运算,默认使用四舍五入
     * 默认保留两位小数
     */
    public static double divide(double d1, double d2) {
        BigDecimal b1 = BigDecimal.valueOf(d1);
        BigDecimal b2 = BigDecimal.valueOf(d2);
        return b1.divide(b2, DEFAULT_SCALE, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * Integer的除法运算,默认使用向上取整
     * 默认保留两位小数
     */
    public static double divideCeiling(Integer d1, Integer d2) {
        BigDecimal b1 = BigDecimal.valueOf(d1);
        BigDecimal b2 = BigDecimal.valueOf(d2);
        return b1.divide(b2, DEFAULT_SCALE, RoundingMode.CEILING).doubleValue();
    }

    /**
     * Integer的除法运算,默认使用向上取整
     *
     * @param scale 保留位数
     */
    public static double divideCeiling(Integer d1, Integer d2, Integer scale) {
        BigDecimal b1 = BigDecimal.valueOf(d1);
        BigDecimal b2 = BigDecimal.valueOf(d2);
        return b1.divide(b2, scale, RoundingMode.CEILING).doubleValue();
    }

    /**
     * double的除法运算,默认使用四舍五入
     * 默认保留两位小数
     *
     * @return
     */
    public static Double divide(double d1, double d2, int defaultScale) {
        try {
            BigDecimal b1 = BigDecimal.valueOf(d1);
            BigDecimal b2 = BigDecimal.valueOf(d2);
            return b1.divide(b2, defaultScale, RoundingMode.HALF_UP).doubleValue();
        } catch (Exception exception) {
            System.out.println(exception);
            System.out.println(d1);
            return 0.0;
        }
    }

    /**
     * Long的除法运算
     */
    public static Long divideLong(Long d1, Long d2) {
        BigDecimal b1 = new BigDecimal(d1);
        BigDecimal b2 = new BigDecimal(d2);
        return b1.divide(b2, 0, RoundingMode.HALF_UP).longValue();
    }

    /**
     * 提供精确的加法运算
     *
     * @param v1 被加数
     * @param v2 加数
     * @return 两个参数的和
     */
    public static Double add(double v1, double v2) {
        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);
        return b1.add(b2).doubleValue();
    }

    /**
     * 提供精确的加法运算
     *
     * @param v1 被加数
     * @param v2 加数
     * @return 两个参数的和
     */
    public static Long addLong(Long v1, Long v2) {
        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);
        return b1.add(b2).longValue();
    }

    /**
     * 提供精确的加法运算
     *
     * @param v1 被加数
     * @param v2 加数
     * @return 两个参数的和
     */
    public static Integer addInteger(Integer v1, Integer v2) {
        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);
        return b1.add(b2).intValue();
    }

    /**
     * 提供精确的减法运算
     *
     * @param v1 被减数
     * @param v2 减数
     * @return 两个参数的差
     */
    public static Double subtract(double v1, double v2) {
        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);
        return b1.subtract(b2).doubleValue();
    }


    /**
     * 格式化时间格式
     * 比如:
     *
     * @param value
     * @return
     */
    public static String formatNumber(double value) {
        NumberFormat nf = NumberFormat.getNumberInstance();
        /*
         * setMinimumFractionDigits设置成2
         * 如果不这么做，那么当value的值是100.00的时候返回100
         * 而不是100.00
         */
        nf.setMaximumFractionDigits(2);
        nf.setRoundingMode(RoundingMode.HALF_UP);
        /*
         * 如果想输出的格式用逗号隔开，可以设置成true
         */
        nf.setGroupingUsed(false);
        return nf.format(value);
    }
}


