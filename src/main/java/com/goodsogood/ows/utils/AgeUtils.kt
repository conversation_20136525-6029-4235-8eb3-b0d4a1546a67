package com.goodsogood.ows.utils

import java.util.*


/**
 *
 * <AUTHOR>
 * @createTime 2022年06月20日 09:58:00
 */
class AgeUtils {

    companion object {
        fun getAge(birth: Date?): Int {
            val cal = Calendar.getInstance()
            val thisYear = cal[Calendar.YEAR]
            val thisMonth = cal[Calendar.MONTH]
            val dayOfMonth = cal[Calendar.DAY_OF_MONTH]
            cal.time = birth
            val birthYear = cal[Calendar.YEAR]
            val birthMonth = cal[Calendar.MONTH]
            val birthdayOfMonth = cal[Calendar.DAY_OF_MONTH]
            var age = thisYear - birthYear

            // 未足月
            if (thisMonth <= birthMonth) {
                // 当月
                if (thisMonth == birthMonth) {
                    // 未足日
                    if (dayOfMonth < birthdayOfMonth) {
                        age--
                    }
                } else {
                    age--
                }
            }
            return age
        }
    }

}