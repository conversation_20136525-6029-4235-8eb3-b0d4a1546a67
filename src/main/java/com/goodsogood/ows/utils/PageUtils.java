package com.goodsogood.ows.utils;


import com.goodsogood.ows.common.bean.PageBean;

/**
 * <AUTHOR>
 * @create 2018-10-24 10:44
 **/
public class PageUtils {

    /**
     * 初始化分页信息
     *
     * @param pageNo   Integer 页码 默认1
     * @param pageSize Integer 每页行数 默认10
     * @return PageBean
     */
    public static PageBean page(Integer pageNo, Integer pageSize) {
        if (pageNo == null) {
            pageNo = 1;
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 10;
        }

        PageBean page = new PageBean();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);

        return page;
    }

}
