package com.goodsogood.ows.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;

/**
 * sql拼接工具
 *
 * <AUTHOR>
 * @date 2020/11/23
 */
public class SqlJointUtil {

    /**
     * 拼接多条sql
     *
     * @param sqlTemplate    sql模板
     * @param templateParams 模板参数集合
     * @param unionAll       true = unionAll，false = union
     * @return
     */
    public static String joint(String sqlTemplate,
                               List<Map<String, String>> templateParams,
                               boolean unionAll) {
        if (StringUtils.isBlank(sqlTemplate)) {
            return null;
        }
        if (templateParams == null || templateParams.isEmpty()) {
            return null;
        }
        StringBuffer result = new StringBuffer();
        for (Map<String, String> templateParam : templateParams) {
            if (unionAll) {
                result.append(" UNION ALL ").append(joint(sqlTemplate, templateParam));
            } else {
                result.append(" UNION ").append(joint(sqlTemplate, templateParam));
            }
        }
        if (unionAll) {
            return result.toString().replaceFirst("UNION ALL", "");

        } else {
            return result.toString().replaceFirst("UNION", "");
        }
    }

    /**
     * 拼接单条sql
     *
     * @param sqlTemplate   sql模板
     * @param templateParam 模板参数
     * @return
     */
    public static String joint(String sqlTemplate, Map<String, String> templateParam) {
        if (StringUtils.isBlank(sqlTemplate)) {
            return null;
        }
        if (templateParam == null || templateParam.isEmpty()) {
            return null;
        }
        String tempSql = sqlTemplate;
        for (Map.Entry<String, String> stringStringEntry : templateParam.entrySet()) {
            tempSql = tempSql.replaceAll("\\$\\{" + stringStringEntry.getKey() + "\\}", stringStringEntry.getValue());
        }
        return tempSql;
    }

    /**
     * 拼接sql
     *
     * @param sqlList  sql集合
     * @param unionAll true = unionAll，false = union
     * @return
     */
    public static String joint(List<String> sqlList, boolean unionAll) {
        if (CollectionUtils.isEmpty(sqlList)) {
            return null;
        }
        StringBuffer result = new StringBuffer();
        sqlList.forEach(t -> {
            if (unionAll) {
                result.append(" UNION ALL ").append(t);
            } else {
                result.append(" UNION ").append(t);
            }
        });
        if (unionAll) {
            return result.toString().replaceFirst("UNION ALL", "");

        } else {
            return result.toString().replaceFirst("UNION", "");
        }
    }


    /**
     * Collection转string
     *
     * @param list            参数集合
     * @param convertFunction 转换函数
     * @param <T>
     * @return
     */
    public static <T> String collectionToStr(Collection<T> list, Function<T, String> convertFunction) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        StringBuffer result = new StringBuffer();
        for (T o : list) {
            result.append(",").append(convertFunction.apply(o));
        }
        return result.delete(0, 1).toString();
    }

    /**
     * List<Long>转string
     *
     * @param list
     * @return
     */
    public static String longListToStr(List<Long> list) {
        return collectionToStr(list, (k -> k.toString()));
    }

    /**
     * list<Integer>转string
     *
     * @param list
     * @return
     */
    public static String integerListToStr(List<Integer> list) {
        return collectionToStr(list, (k -> k.toString()));
    }

//    public static void main(String[] args) {
//        String SQL_TEMPLATE = "SELECT \n" +
//                "HOUR( d.consume_time ) time,\n" +
//                "count( 0 ) total \n" +
//                "FROM t_score_detail d \n" +
//                "WHERE \n" +
//                "d.consume_time >= '${startTime} 00:00:00' \n" +
//                "AND d.consume_time <= '${endTime} 23:59:59' \n" +
//                "AND d.score_user_id IN (${scoreUserIds})\n" +
//                "GROUP BY\n" +
//                "HOUR ( d.consume_time )";
//        List<Map<String, String>> list = new ArrayList<>();
//        Map<String, String> map = new HashMap<>();
//        map.put("startTime", "2020-01-12");
//        map.put("endTime", "2020-01-12");
//        map.put("scoreUserIds", "1,2,3");
//        Map<String, String> map1 = new HashMap<>();
//        map1.put("startTime", "2020-01-12");
//        map1.put("endTime", "2020-01-12");
//        map1.put("scoreUserIds", "1,2,3");
//        list.add(map);
//        list.add(map1);
//        String scoreUserIds = collectionToStr(list, k -> k.get("scoreUserIds"));
//        System.out.println(scoreUserIds);
//        System.out.println(joint(SQL_TEMPLATE, list, false));
//    }

}
