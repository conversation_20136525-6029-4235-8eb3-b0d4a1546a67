package com.goodsogood.ows.utils;

import lombok.extern.log4j.Log4j2;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * <p>文件导出工具</p>
 * <p>主要是把生成好的文件，用二进制流返回给前端，并让浏览器下载</p>
 * <AUTHOR>
 * @create 2018-04-04 16:05
 **/
@Log4j2
public class ExclExportUtils {

     /**
      * 导出03格式的exc
      * @param request
      * @param response
      * @param wb
      * @param fileName
      * @return
      */
     public static boolean export03(HttpServletRequest request,
                                    HttpServletResponse response,
                                    HSSFWorkbook wb, String fileName){
          boolean flag = false;
          try {
               // 导出excel文件
               //设置头信息
               setHeader(response, fileName);
               // 使用响应的输出流导出excel文件
               wb.write(response.getOutputStream());
               flag = true;
          } catch (UnsupportedEncodingException e) {
               log.error("导出文件出错：不支持编码格式", e);
          } catch (IOException e) {
               log.error("导出文件出错：IO异常", e);
          } catch (Exception e){
               log.error("导出文件出错", e);
          }
          return flag;
     }
     /**
      * 导出03格式的exc
      * @param request
      * @param response
      * @param wb
      * @param fileName
      * @return
      */
     public static boolean export07(HttpServletRequest request,
                                    HttpServletResponse response,
                                    XSSFWorkbook wb, String fileName){
          boolean flag = false;
          try {
               // 导出excel文件
               wb = new XSSFWorkbook();
               //设置头信息
               setHeader(response, fileName);
               // 使用响应的输出流导出excel文件
               wb.write(response.getOutputStream());
               flag = true;
          } catch (UnsupportedEncodingException e) {
               log.error("导出文件出错：不支持编码格式", e);
          } catch (IOException e) {
               log.error("导出文件出错：IO异常", e);
          } catch (Exception e){
               log.error("导出文件出错", e);
          }
          return flag;
     }

     /**
      * 设置头信息
      * @param response
      * @param fileName
      */
     public static void setHeader(HttpServletResponse response, String fileName){
          try {
               // 设置响应头的文件名称信息
               String filename = URLEncoder.encode(fileName.replaceAll(" ", "") + ".xls", "utf-8");
               // 设置response的Header
              response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
               response.addHeader("Content-Disposition", "attachment;filename="
                       + new String(filename.getBytes(), StandardCharsets.UTF_8));
               // 设置响应头导出文件格式
               response.setContentType("application/ms-excel;charset=UTF-8");
               response.setCharacterEncoding("UTF-8");
          } catch (Exception e){
               log.error("导出设置头信息出错", e);
          }
     }


}
