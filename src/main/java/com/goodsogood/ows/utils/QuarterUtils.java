package com.goodsogood.ows.utils;

/**
 * <p>Description: 季度工具类</p>
 *
 * <AUTHOR>
 * @version 2020/10/28 18:04
 */
public class QuarterUtils {
    /**
     * 指定月份的季度
     *
     * @param currentMonth 月份
     * @return Quarter
     */
    public static Quarter getQuarterByMonth(int currentMonth) {
        Quarter currentQuarter = null;
        for (Quarter quarter : Quarter.values()) {
            if (quarter.startMonth <= currentMonth && quarter.endMonth >= currentMonth) {
                currentQuarter = quarter;
                break;
            }
        }
        return currentQuarter;
    }
    
    /**
     * 季度 1,2,3,4
     */
    public enum Quarter {
        // 第一季度"
        ONE(1, 1, 3, "第一季度"),
        // 第二季度"
        TWO(2, 4, 6, "第二季度"),
        // 第三季度
        THREE(3, 7, 9, "第三季度"),
        // 第四季度
        FOUR(4, 10, 12, "第四季度");
        /**
         * 季度
         */
        private final int quarter;
        /**
         * 季度开始月份
         */
        private final int startMonth;
        /**
         * 季度截止月份
         */
        private final int endMonth;
        /**
         * 描述
         */
        private final String description;

        public int getQuarter() {
            return quarter;
        }

        public int getStartMonth() {
            return startMonth;
        }

        public int getEndMonth() {
            return endMonth;
        }

        public String getDescription() {
            return description;
        }

        /**
         * @param quarter     季度
         * @param startMonth  开始月份
         * @param endMonth    结束月份
         * @param description 中文描述
         */
        Quarter(int quarter, int startMonth, int endMonth, String description) {
            this.quarter = quarter;
            this.startMonth = startMonth;
            this.endMonth = endMonth;
            this.description = description;
        }

        /**
         * 季度
         *
         * @param quarter 季度
         * @return Quarter
         */
        public static Quarter quarter(int quarter) {
            Quarter currentQuarter = null;
            for (Quarter q : Quarter.values()) {
                if (q.getQuarter() == quarter) {
                    currentQuarter = q;
                    break;
                }
            }
            return currentQuarter;
        }
    }
}
