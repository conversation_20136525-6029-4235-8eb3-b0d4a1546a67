package com.goodsogood.ows.utils

import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.service.experience.UserTaskService
import io.micrometer.core.instrument.util.StringUtils
import org.slf4j.LoggerFactory
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*
import java.util.function.Consumer
import java.util.regex.Pattern

object MongoAggProjectUtils {


    private var log = LoggerFactory.getLogger(UserTaskService::class.java)

    /**
     *
     * @param collectionName mongoDB集合名称
     * @param c 实体类对象
     * @param underline 1:子集点转下划线名字
     * 2:子集点属性名
     * @return
     */
    fun getProject(collectionName: String, c: Class<*>?, underline: Int?): Array<String>? {
        if (c == null) {
            log.error("AggProjectUtil -> getProject:请传递类对象[{}]",
                null as Any?)
            return null
        }
        val fields = c.declaredFields
        val fieldsList: MutableList<String> = ArrayList()
        val list: MutableList<String> = ArrayList()
        for (field in fields) {
            fieldsList.add(field.name)
        }
        when (underline) {
            1 -> {
                fieldsList.forEach(Consumer { x: String ->
                    // 每个属性转换下滑线
                    val name = matcher(x)
                    if (StringUtils.isBlank(collectionName)) {
                        throw ApiException("子集名不能为空")
                    }
                    // 组合字符串
                    list.add("$collectionName.$name")
                })
                return list.toTypedArray()
            }
            2 -> {
                fieldsList.forEach(Consumer { x: String ->
                    // 组合字符串
                    if (StringUtils.isBlank(collectionName)) {
                        throw ApiException("子集名不能为空")
                    }
                    list.add("$collectionName.$x")
                })
                return list.toTypedArray()
            }
        }
        return null
    }

    /**
     * @param o 对象
     * @param underline 1:对象属性名转下划线
     * 2:对象属性名
     * @return
     */
    fun getProject(o: Class<*>?, underline: Int?): MutableList<String>? {
        val fieldsList: MutableList<String> = ArrayList()
        val list = check(o, fieldsList)
        when (underline) {
            1 -> {
                // 获取属性名
                fieldsList.forEach(Consumer { x: String ->
                    // 转换下滑线
                    val name = matcher(x)
                    // 组合字符串
                    list?.add(name)
                })
                return list
            }
            2 -> return fieldsList
        }
        return null
    }

    /**
     * 属性转下划线
     */
    fun matcher(x: String): String {
        val matcher = Pattern.compile("[A-Z]").matcher(x)
        val sb = StringBuffer()
        while (matcher.find()) {
            matcher.appendReplacement(sb, "_" + matcher.group(0).lowercase(Locale.getDefault()))
        }
        matcher.appendTail(sb)
        // 下划线转换过后的字符
        return sb.toString()
    }

    /**
     * 解析并获取类中的属性
     */
    fun check(o: Class<*>?, fieldsList: MutableList<String>): MutableList<String>? {
        if (o == null) {
            log.error("AggProjectUtil -> getProject:请传递类对象[{}]",
                null as Any?)
            return null
        }
        val fields = o.declaredFields
        val list: MutableList<String> = ArrayList()
        for (field in fields) {
            fieldsList.add(field.name)
        }
        return list
    }

    /**
     * 将date转成ISODate 以便mongo识别
     * @param dateStr
     * @return
     */
    fun dateToISODate(dateStr: Date): Date {
        var parse = Date()
        try {
            // 解析字符串时间
            val format = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
            parse = format.parse(format.format(dateStr))
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return parse
    }
}