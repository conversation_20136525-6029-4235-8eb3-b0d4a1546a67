package com.goodsogood.ows.service;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.OutsideMapConfig;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.meeting.MeetingCommentStatisticsMapper;
import com.goodsogood.ows.mapper.meeting.StatisticsMapper;
import com.goodsogood.ows.mapper.ppmd.PpmdStatisticsMapper;
import com.goodsogood.ows.mapper.supervise.SuperviseHistoryMapper;
import com.goodsogood.ows.mapper.user.LeaderMapper;
import com.goodsogood.ows.mapper.user.OrgPeriodMemberMapper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseHistoryEntity;
import com.goodsogood.ows.model.db.user.OrgPeriodMemberEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.mongodb.PartyBrandBase;
import com.goodsogood.ows.model.mongodb.PartyPositions;
import com.goodsogood.ows.model.vo.dataCockpit.*;
import com.goodsogood.ows.model.vo.overview.OverviewActivityFinishVo;
import com.goodsogood.ows.model.vo.supervise.OptionForm;
import com.goodsogood.ows.model.vo.supervise.SuperviseListForm;
import com.goodsogood.ows.model.vo.supervise.SuperviseTipsForm;
import com.goodsogood.ows.service.overview.OverviewService;
import com.goodsogood.ows.service.supervise.SuperviseService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 *  数据驾驶舱服务类
 *
 *
 */
@Service
@Log4j2
public class DataCockpitService {

    private final OrganizationMapper organizationMapper;

    private final OrgTypeConfig orgTypeConfig;

    private final OrgService orgService;

    private final LeaderMapper leaderMapper;

    private final UserMapper userMapper;

    private final OrgPeriodMemberMapper orgPeriodMemberMapper;

    private final StatisticsMapper statisticsMapper;

    private final MeetingCommentStatisticsMapper meetingCommentStatisticsMapper;

    private final OverviewService overviewService;

    private final MyMongoTemplate mongoTemplate;

    private final SuperviseService superviseService;

    private final SuperviseHistoryMapper superviseHistoryMapper;

    private final OutsideMapConfig outsideMapConfig;

    private final PpmdStatisticsMapper ppmdStatisticsMapper;

    public DataCockpitService(OrganizationMapper organizationMapper, OrgTypeConfig orgTypeConfig, OrgService orgService, LeaderMapper leaderMapper, UserMapper userMapper, OrgPeriodMemberMapper orgPeriodMemberMapper, StatisticsMapper statisticsMapper, MeetingCommentStatisticsMapper meetingCommentStatisticsMapper, OverviewService overviewService, MyMongoTemplate mongoTemplate, SuperviseService superviseService, SuperviseHistoryMapper superviseHistoryMapper, OutsideMapConfig outsideMapConfig, PpmdStatisticsMapper ppmdStatisticsMapper){
        this.organizationMapper = organizationMapper;
        this.orgTypeConfig = orgTypeConfig;
        this.orgService = orgService;
        this.leaderMapper = leaderMapper;
        this.userMapper = userMapper;
        this.orgPeriodMemberMapper = orgPeriodMemberMapper;
        this.statisticsMapper = statisticsMapper;
        this.meetingCommentStatisticsMapper = meetingCommentStatisticsMapper;
        this.overviewService = overviewService;
        this.mongoTemplate = mongoTemplate;
        this.superviseService = superviseService;
        this.superviseHistoryMapper = superviseHistoryMapper;
        this.outsideMapConfig = outsideMapConfig;
        this.ppmdStatisticsMapper = ppmdStatisticsMapper;
    }

    public Integer orgTyp(HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //获取当前登录组织信息
        return isOrgType(sysHeader.getOid());
    }

    private Integer isOrgType(Long oid){
        OrganizationEntity organizationEntity = organizationMapper.selectByPrimaryKey(oid);
        if(orgTypeConfig.getCommunistChild().contains(organizationEntity.getOrgTypeChild()) || oid == 3L){
            return 1;
        }else if(orgTypeConfig.getBranchChild().contains(organizationEntity.getOrgTypeChild())){
            return 2;
        }else{
            return 3;
        }
    }

    public Object orgOverview(HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //获取当前登录组织信息
        OrganizationEntity organizationEntity = organizationMapper.selectByPrimaryKey(sysHeader.getOid());
        //判断当前组织信息是党委还是党支部
        if(orgTypeConfig.getCommunistChild().contains(organizationEntity.getOrgTypeChild()) || sysHeader.getOid() == 3L){
            //党委
            PartyOrgOverviewVO partyOrgOverviewVO = new PartyOrgOverviewVO();
            //获取当前党委下所有党组织信息
            List<OrganizationEntity> resultList;
            try {
                resultList = this.orgService.findAllChildOrg(sysHeader.getOid(), 1, sysHeader.getRegionId(),false);
            }catch (Exception e){
                log.info("获取组织下所有党组织异常");
                return null;
            }

            Integer partyOrganizationNum = 0;
            //组织下支部数据
            List<Long> partyBranchs = new ArrayList<>();
            //循环当前组织下所有组织，计算各个组织数量
            for(OrganizationEntity orgNameResultForm : resultList){
                Integer orgTypeChild = orgNameResultForm.getOrgTypeChild();
                if(orgTypeConfig.getCommunistChild().contains(orgTypeChild)){
                    //党委+1
                    partyOrgOverviewVO.setPartyCommitteeNum(partyOrgOverviewVO.getPartyCommitteeNum()+1);
                    partyOrganizationNum ++;
                }else if(orgTypeConfig.getGeneralBranchChild().contains(orgTypeChild)){
                    //党总支+1
                    partyOrgOverviewVO.setPartyGeneralBranchNum(partyOrgOverviewVO.getPartyGeneralBranchNum()+1);
                    partyOrganizationNum ++;
                }else if(orgTypeConfig.getBranchChild().contains(orgTypeChild)){
                    //党支部+1
                    partyOrgOverviewVO.setPartyBranchNum(partyOrgOverviewVO.getPartyBranchNum()+1);
                    partyBranchs.add(orgNameResultForm.getOrganizationId());
                    partyOrganizationNum ++;
                }else if(orgTypeConfig.getPartyGroup().contains(orgTypeChild)){
                    //党组+1
                    partyOrgOverviewVO.setPartyGroupNum(partyOrgOverviewVO.getPartyGroupNum()+1);
                    partyOrganizationNum ++;
                }else if(orgTypeConfig.getCommunistGroup().contains(orgTypeChild)){
                    //党小组+1
                    partyOrgOverviewVO.setCommunistGroupNum(partyOrgOverviewVO.getCommunistGroupNum()+1);
                    partyOrganizationNum ++;
                }
            }
            //党组织数量
            partyOrgOverviewVO.setPartyOrganizationNum(partyOrganizationNum);
            //获取当前组织对应行政单位下级数量
            Example orgEx = new Example(OrganizationEntity.class);
            Example.Criteria criteria = orgEx.createCriteria();
            criteria.andLike("orgLevel", "%-" + organizationEntity.getOwnerId() + "-%");
            criteria.andEqualTo("status", Constants.STATUS_YES);
            criteria.orEqualTo("organizationId", organizationEntity.getOwnerId());
            Integer administrativeUnitNum = organizationMapper.selectCountByExample(orgEx);
            partyOrgOverviewVO.setAdministrativeUnitNum(administrativeUnitNum);

            //领导班子成员数量
            Integer teamMembersNum = leaderMapper.getAllOrgChildLeaderCount(organizationEntity.getOwnerId());
            partyOrgOverviewVO.setTeamMembersNum(teamMembersNum);

            //获取党支部书记数量
            Integer partyBranchSecretaryNum = orgPeriodMemberMapper.getOrgPeriodMemberBoos1(partyBranchs);
            partyOrgOverviewVO.setPartyBranchSecretaryNum(partyBranchSecretaryNum);

            //获取当前党组及其下级党员数量
            Integer partyMemberNum = userMapper.getOrgIdAllMembers(sysHeader.getOid());
            partyOrgOverviewVO.setPartyMemberNum(partyMemberNum);

            return partyOrgOverviewVO;
        }else if(orgTypeConfig.getBranchChild().contains(organizationEntity.getOrgTypeChild())){
            //党支部
            BranchOrgOverviewVO branchOrgOverviewVO = new BranchOrgOverviewVO();

            //获取当前党组及其下级党员数量
            Integer partyMemberNum = userMapper.getOrgIdAllMembers(sysHeader.getOid());
            branchOrgOverviewVO.setPartyMemberNum(partyMemberNum);

            //获取当前党支部书记、获取当前届次开始结束时间
            UserOrgPeriodVO userOrgPeriodVO = orgPeriodMemberMapper.getMyOrgPeriodMemberBoos1(sysHeader.getOid());
            if(userOrgPeriodVO != null){
                branchOrgOverviewVO.setPartyBranchSecretary(userOrgPeriodVO.getUserName());
                branchOrgOverviewVO.setPeriodStartTime(userOrgPeriodVO.getStartTime());
                branchOrgOverviewVO.setPeriodEndTime(userOrgPeriodVO.getEndTime());
            }

            //获取其他成员的名称
            List<OrgPeriodMemberEntity> orgPeriodMemberEntities = orgPeriodMemberMapper.getMyOrgPeriodOtherMember(sysHeader.getOid());
            if(orgPeriodMemberEntities != null){
                String otherUserNames = orgPeriodMemberEntities.stream().map(OrgPeriodMemberEntity::getUserName).collect(Collectors.joining(","));
                branchOrgOverviewVO.setOtherMember(otherUserNames);
            }

            return branchOrgOverviewVO;
        }
        return null;
    }

    public Object organizationalLife(HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //获取当前登录组织信息
        OrganizationEntity organizationEntity = organizationMapper.selectByPrimaryKey(sysHeader.getOid());
        //本年度起止时间
        LocalDate currentDate = LocalDate.now();
        Integer year = currentDate.getYear();
        //过去当前季度
        int currentQuarterly = DateUtils.getCurrentQuarterly();
        String startTime = DateUtils.getCurrentQuarterlyStartTime(currentQuarterly);
        String endTime = DateUtils.getCurrentQuarterlyEndTime(currentQuarterly);
        //判断当前组织信息是党委还是党支部
        if(orgTypeConfig.getCommunistChild().contains(organizationEntity.getOrgTypeChild()) || sysHeader.getOid() == 3L){
            //党委
            PartyOrganizationalLifeVO organizationalLifeVO = new PartyOrganizationalLifeVO();
            List<DataCockpitOverviewActivityVo> seriess = statisticsMapper.getMeetingNumByOrgId(startTime,endTime,sysHeader.getOid());
            for (DataCockpitOverviewActivityVo series : seriess){
                Integer value = series.getValue() != null?series.getValue():0;
                if(series.getType().equals(1)){
                    //党支部党员大会
                    organizationalLifeVO.setPartyCongressNum(value);
                    organizationalLifeVO.setPartyCongressProportion(calculateProportion(series));
                }else if(series.getType().equals(2)){
                    //党支部委员会会议(支委会)
                    organizationalLifeVO.setBranchCommitteeNum(value);
                    organizationalLifeVO.setBranchCommitteeProportion(calculateProportion(series));
                }else if(series.getType().equals(3)){
                    //党小组会
                    organizationalLifeVO.setPartyGroupNum(value);
                    organizationalLifeVO.setPartyGroupProportion(calculateProportion(series));
                }else if(series.getType().equals(4)){
                    //党课
                    organizationalLifeVO.setPartyLectureNum(value);
                    organizationalLifeVO.setPartyLectureProportion(calculateProportion(series));
                }else if(series.getType().equals(5)){
                    //主题党日
                    organizationalLifeVO.setThemePartyDayNum(value);
                    organizationalLifeVO.setThemePartyDayProportion(calculateProportion(series));
                }else if(series.getType().equals(25)){
                    //中心组学习
                    organizationalLifeVO.setCentralGroupLearningNum(value);
                    organizationalLifeVO.setCentralGroupLearningProportion(calculateProportion(series));
                }
            }
            List<DataCockpitOverviewActivityVo> totals = statisticsMapper.getMeetingYearTotalNumByOrgId(year.toString(),sysHeader.getOid());
            //计算总活动次数
            Integer totalNum = 0;
            for(DataCockpitOverviewActivityVo total: totals){
                totalNum = totalNum + total.getTotal();
            }
            organizationalLifeVO.setTotalNum(totalNum);
            return organizationalLifeVO;
        }else if(orgTypeConfig.getBranchChild().contains(organizationEntity.getOrgTypeChild())){
            //党支部
            BranchOrganizationalLifeVO branchOrganizationalLifeVO = new BranchOrganizationalLifeVO();
            Integer totalNum = 0;
            //本季度党会大会
            OverviewActivityFinishVo overviewActivityFinishVo = statisticsMapper.
                    staOverviewMeetingBranch1(startTime, endTime, sysHeader.getOid());
            branchOrganizationalLifeVO.setPartyCongressNum(transformationNum(overviewActivityFinishVo.getFinished1()));
            branchOrganizationalLifeVO.setPartyCongressProgress(packInfoNew1(overviewActivityFinishVo.getFinished1()));
            totalNum += branchOrganizationalLifeVO.getPartyCongressNum();
            //党支部委员会会议(支委会)
            OverviewActivityFinishVo overviewActivityFinishVo3 = statisticsMapper.
                    staOverviewMeetingBranch3(startTime, endTime, sysHeader.getOid());
            branchOrganizationalLifeVO.setBranchCommitteeNum(transformationNum(overviewActivityFinishVo3.getFinished3()));
            branchOrganizationalLifeVO.setBranchCommitteeProgress(packInfoNew1(overviewActivityFinishVo3.getFinished3()));
            totalNum += branchOrganizationalLifeVO.getBranchCommitteeNum();
            //党小组会
            OverviewActivityFinishVo overviewActivityFinishVo4 = statisticsMapper.
                    staDataCockpitFinished4(startTime, endTime, sysHeader.getOid());
            overviewActivityFinishVo4 = overviewActivityFinishVo4 == null?new OverviewActivityFinishVo():overviewActivityFinishVo4;
            branchOrganizationalLifeVO.setPartyGroupNum(transformationNum(overviewActivityFinishVo4.getFinished4()));
            branchOrganizationalLifeVO.setPartyGroupProgress(packInfoNew1(overviewActivityFinishVo4.getFinished4()));
            totalNum += branchOrganizationalLifeVO.getPartyGroupNum();
            //党课
            OverviewActivityFinishVo overviewActivityFinishVo2 = statisticsMapper.
                    staOverviewMeetingBranch2(startTime, endTime, sysHeader.getOid());
            branchOrganizationalLifeVO.setPartyLectureNum(transformationNum(overviewActivityFinishVo2.getFinished2()));
            branchOrganizationalLifeVO.setPartyLectureProgress(packInfoNew1(overviewActivityFinishVo2.getFinished2()));
            totalNum += branchOrganizationalLifeVO.getPartyLectureNum();
            //主题党日
            OverviewActivityFinishVo overviewActivityFinishVo5 = statisticsMapper.
                    staOverviewMeetingBranch5(startTime, endTime, sysHeader.getOid());
            branchOrganizationalLifeVO.setThemePartyDayNum(transformationNum(overviewActivityFinishVo5.getFinished5()));
            branchOrganizationalLifeVO.setThemePartyDayProgress(packInfoNew1(overviewActivityFinishVo5.getFinished5()));
            totalNum += branchOrganizationalLifeVO.getThemePartyDayNum();
            branchOrganizationalLifeVO.setTotalNum(totalNum);
            return branchOrganizationalLifeVO;
        }
        return null;
    }

    public Object evaluation(HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //获取今年年份的民主评议数据
        Integer year = LocalDate.now().getYear();
        List<StaticsForm> staticsForms = meetingCommentStatisticsMapper.queryStatics(year,sysHeader.getOid());
        PartyEvaluationVO partyEvaluationVO = new PartyEvaluationVO();
        for(StaticsForm staticsForm : staticsForms){
            //党员数量
            partyEvaluationVO.setPartyMemberNum(partyEvaluationVO.getPartyMemberNum() + staticsForm.getPartyNumber());
            //已评数量
            partyEvaluationVO.setCompletedNum(partyEvaluationVO.getCompletedNum() + staticsForm.getJoinNumber());
            //优秀数量
            partyEvaluationVO.setLevel1Num(partyEvaluationVO.getLevel1Num() + staticsForm.getLevel1());
            //合格数量
            partyEvaluationVO.setLevel2Num(partyEvaluationVO.getLevel2Num() + staticsForm.getLevel2());
            //基本合格数量
            partyEvaluationVO.setLevel3Num(partyEvaluationVO.getLevel3Num() + staticsForm.getLevel3());
            //不合格数量
            partyEvaluationVO.setLevel4Num(partyEvaluationVO.getLevel4Num() + staticsForm.getLevel4());
        }
        //未评数量 = 总党员数 - 已评数量
        partyEvaluationVO.setUnfinishedNum(partyEvaluationVO.getPartyMemberNum() - partyEvaluationVO.getCompletedNum());
        //评选进度（完成进度）
        partyEvaluationVO.setCompletedProgress(evaluationGetProgress(partyEvaluationVO.getCompletedNum(),partyEvaluationVO.getPartyMemberNum()));
        //优秀占比
        partyEvaluationVO.setLevel1Proportion(evaluationGetProgress(partyEvaluationVO.getLevel1Num(),partyEvaluationVO.getPartyMemberNum()));
        //合格占比
        partyEvaluationVO.setLevel2Proportion(evaluationGetProgress(partyEvaluationVO.getLevel2Num(),partyEvaluationVO.getPartyMemberNum()));
        //基本合格占比
        partyEvaluationVO.setLevel3Proportion(evaluationGetProgress(partyEvaluationVO.getLevel3Num(),partyEvaluationVO.getPartyMemberNum()));
        //不合格占比
        partyEvaluationVO.setLevel4Proportion(evaluationGetProgress(partyEvaluationVO.getLevel4Num(),partyEvaluationVO.getPartyMemberNum()));
        return partyEvaluationVO;
    }

    public Object partyBuildingBrand(HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //获取当前党委下所有党组织信息
        List<OrganizationEntity> resultList;
        PartyBuildingBrandVO partyBuildingBrandVO = new PartyBuildingBrandVO();
        try {
            resultList = this.orgService.findAllChildOrg(sysHeader.getOid(), 1, sysHeader.getRegionId(),false);
        }catch (Exception e){
            log.info("获取组织下所有党组织异常");
            return partyBuildingBrandVO;
        }
        if(resultList == null || resultList.size() == 0){
            return partyBuildingBrandVO;
        }
        List<Long> orgList = resultList.stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toList());
        //获取党建阵地
        Criteria criteria = Criteria.where("status").is(2);
        criteria.and("orgId").in(orgList);
        Query query = new Query(criteria);
        List<PartyPositions> partyPositionsList = mongoTemplate.find(query, PartyPositions.class);
        //放入两个党建阵地
        Integer indexBuildingBrand = 1;
        List<PartyBuildingBrandVO.partyBuildingBattlefieldItem> partyBuildingBattlefieldItems = new ArrayList<>();
        for(PartyPositions partyPositions : partyPositionsList){
            PartyBuildingBrandVO.partyBuildingBattlefieldItem partyBuildingBattlefieldItem = new PartyBuildingBrandVO().new partyBuildingBattlefieldItem();
            partyBuildingBattlefieldItem.setPartyBuildingBattlefieldName(partyPositions.getPartyPositionName());
            partyBuildingBattlefieldItem.setPartyBuildingBattlefieldImgUrl(partyPositions.getPictures().get(0).getPath());
            partyBuildingBattlefieldItems.add(partyBuildingBattlefieldItem);
            if(indexBuildingBrand >= 2){
                break;
            }
            indexBuildingBrand ++;
        }
        partyBuildingBrandVO.setPartyBuildingBattlefields(partyBuildingBattlefieldItems);
        //赋值党建阵地个数
        partyBuildingBrandVO.setPartyBuildingBattlefieldNum(partyPositionsList.size());
        //获取党建品牌
        Criteria criteriaBase = Criteria.where("status").is(2);
        criteriaBase.and("orgId").in(orgList);
        Query queryBase = new Query(criteriaBase);
        List<PartyBrandBase> partyBrandBases = mongoTemplate.find(queryBase, PartyBrandBase.class);
        //放入两个党建品牌
        Integer indexBrandBase = 1;
        List<PartyBuildingBrandVO.PartyBuildingBrandItem> partyBuildingBrandItems = new ArrayList<>();
        for(PartyBrandBase partyBrandBase : partyBrandBases){
            PartyBuildingBrandVO.PartyBuildingBrandItem partyBuildingBrandItem = new PartyBuildingBrandVO().new PartyBuildingBrandItem();
            partyBuildingBrandItem.setPartyBuildingBrandName(partyBrandBase.getBrandName());
            partyBuildingBrandItem.setPartyBuildingBrandImgUrl(partyBrandBase.getLogo().getPath());
            partyBuildingBrandItems.add(partyBuildingBrandItem);
            if(indexBrandBase >= 2){
                break;
            }
            indexBrandBase ++;
        }
        partyBuildingBrandVO.setPartyBuildingBrands(partyBuildingBrandItems);
        //赋值党建品牌个数
        partyBuildingBrandVO.setPartyBuildingBrandNum(partyBrandBases.size());
        return partyBuildingBrandVO;
    }

    public Object mapData(HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //获取当前党委下所有党组织信息
        List<OrganizationEntity> resultList;
        try {
            resultList = this.orgService.findAllNextChildOrg(sysHeader.getOid(), sysHeader.getRegionId(),false,true,true);
        }catch (Exception e){
            log.info("获取组织下所有党组织异常");
            return null;
        }
        List<Long> outsideMapOrgIds = outsideMapConfig.getOrgId();
        MapDataVO mapDataVO = new MapDataVO();
        //判断是否最后一级，党支部就是最后一级
        mapDataVO.setIsLast(isOrgType(sysHeader.getOid()));
        if(mapDataVO.getIsLast() == 2){
            //是最后一级.只返回他自己
            resultList = new ArrayList<>();
            OrganizationEntity organizationEntity = organizationMapper.selectByPrimaryKey(sysHeader.getOid());
            resultList.add(organizationEntity);
        }
        List<MapDataVO.ResultOrg> resultOrgs = new ArrayList<>();
        for(OrganizationEntity organizationEntity : resultList){
            MapDataVO.ResultOrg resultOrg = new MapDataVO().new ResultOrg();
            resultOrg.setOrganizationId(organizationEntity.getOrganizationId());
            resultOrg.setName(organizationEntity.getName());
            resultOrg.setLongitude(organizationEntity.getLongitude());
            resultOrg.setLatitude(organizationEntity.getLatitude());
            resultOrg.setOrgTypeChild(organizationEntity.getOrgTypeChild());
            resultOrg.setOrgTypeChildStr(getOrgTypeStr(organizationEntity.getOrgTypeChild()));
            resultOrg.setIsOutsideMap(outsideMapOrgIds.contains(organizationEntity.getOrganizationId())?1:0);
            resultOrg.setOrgType(isOrgType(organizationEntity.getOrganizationId()));
            resultOrgs.add(resultOrg);
        }
        mapDataVO.setResultOrgs(resultOrgs);
        return mapDataVO;
    }

    public Object partyMember(HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        PartyMemberVO partyMemberVO = new PartyMemberVO();
        //获取当前党组织下所有人员集合
        List<UserEntity> allUsers = userMapper.getAllUserByOrgId(sysHeader.getOid());
        //所有党员集合
        List<UserEntity> partyMembers = new ArrayList<>();
        //学历
        List<Integer> eLevel1 = new ArrayList<>(Arrays.asList(103901,103902));               //研究生及以上
        List<Integer> eLevel2 = new ArrayList<>(Arrays.asList(103903));                      //本科
        List<Integer> eLevel3 = new ArrayList<>(Arrays.asList(103908));                      //高中
        List<Integer> eLevel4 = new ArrayList<>(Arrays.asList(103909,103910,103911));        //初中及以下
        for(UserEntity userEntity : allUsers){
            //计算当前组织男党员人数
            if(userEntity != null && userEntity.getPoliticalType() != null && userEntity.getPoliticalType().equals(1)){
                //党员
                partyMemberVO.setPartyMemberNum(partyMemberVO.getPartyMemberNum() + 1);
                partyMembers.add(userEntity);
                //性别
                if(userEntity.getGender() != null && userEntity.getGender().equals(1)){
                    //男党员
                    partyMemberVO.setMalePartyGroupNum(partyMemberVO.getMalePartyGroupNum() + 1);
                }else if(userEntity.getGender() != null && userEntity.getGender().equals(2)){
                    //女党员
                    partyMemberVO.setFemalePartyLectureNum(partyMemberVO.getFemalePartyLectureNum() + 1);
                }
                if(userEntity.getEducation() != null && eLevel1.contains(userEntity.getEducation())){
                    partyMemberVO.getEducation().setLevel1(partyMemberVO.getEducation().getLevel1() + 1);
                }else if(userEntity.getEducation() != null && eLevel2.contains(userEntity.getEducation())){
                    partyMemberVO.getEducation().setLevel2(partyMemberVO.getEducation().getLevel2() + 1);
                }else if(userEntity.getEducation() != null && eLevel3.contains(userEntity.getEducation())){
                    partyMemberVO.getEducation().setLevel3(partyMemberVO.getEducation().getLevel3() + 1);
                }else if(userEntity.getEducation() != null && eLevel4.contains(userEntity.getEducation())){
                    partyMemberVO.getEducation().setLevel4(partyMemberVO.getEducation().getLevel4() + 1);
                }
                //党龄
                if(userEntity.getJoiningTime() != null){
                    try{
                        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        Date joinTime = dateFormat.parse(userEntity.getJoiningTime());
                        if(dateFormat.parse("2012-11-01").compareTo(joinTime) <= 0){
                            //2012年11月及以后
                            partyMemberVO.getPartyStanding().setLevel1(partyMemberVO.getPartyStanding().getLevel1() +1);
                        }else if(dateFormat.parse("2002-11-01").compareTo(joinTime) <= 0 &&
                                dateFormat.parse("2012-10-31").compareTo(joinTime) >= 0){
                            //2002年11月-2012年10月
                            partyMemberVO.getPartyStanding().setLevel2(partyMemberVO.getPartyStanding().getLevel2() +1);
                        }else if(dateFormat.parse("1979-01-01").compareTo(joinTime) <= 0 &&
                                dateFormat.parse("2002-10-31").compareTo(joinTime) >= 0){
                            //1979年1月-2002年10月
                            partyMemberVO.getPartyStanding().setLevel3(partyMemberVO.getPartyStanding().getLevel3() +1);
                        }else if(dateFormat.parse("1976-11-01").compareTo(joinTime) <= 0 &&
                                dateFormat.parse("1978-12-31").compareTo(joinTime) >= 0){
                            //1976年11月-1978年12月
                            partyMemberVO.getPartyStanding().setLevel4(partyMemberVO.getPartyStanding().getLevel4() +1);
                        }else if(dateFormat.parse("1976-07-06").compareTo(joinTime) > 0){
                            //1976年11月-1978年12月
                            partyMemberVO.getPartyStanding().setLevel5(partyMemberVO.getPartyStanding().getLevel5() +1);
                        }
                    }catch (Exception e){
                        log.error("dataCockpit入党时间转换失败,"+ userEntity.getJoiningTime());
                    }
                }
                //年龄
                if(userEntity.getAge() != null && userEntity.getAge() > 0){
                    //如果有年龄数据根据年龄判断
                    if(userEntity.getAge() <= 35){
                        //35岁以下
                        partyMemberVO.getAge().setLevel1(partyMemberVO.getAge().getLevel1() +1);
                    }else if(userEntity.getAge() >= 36 && userEntity.getAge() <= 50){
                        //36至50岁
                        partyMemberVO.getAge().setLevel2(partyMemberVO.getAge().getLevel2() +1);
                    }else if(userEntity.getAge() >= 51 && userEntity.getAge() <= 60){
                        //51至60岁
                        partyMemberVO.getAge().setLevel3(partyMemberVO.getAge().getLevel3() +1);
                    }else if(userEntity.getAge() > 60){
                        //61岁以上
                        partyMemberVO.getAge().setLevel4(partyMemberVO.getAge().getLevel4() +1);
                    }
                }
            }
        }
        //查询组织总人数，计算占比（可能包含非党员的人员）
        Double partyMemberProportion = evaluationGetProgress(partyMemberVO.getPartyMemberNum(),allUsers.size());
        partyMemberVO.setPartyMemberProportion(partyMemberProportion);
        return partyMemberVO;

    }

    public Object supervise(HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        SuperviseVO superviseVO = new SuperviseVO();
        //获取当前组织统计数据
        SuperviseTipsForm tips = superviseService.tips(sysHeader.getOid());
        superviseVO.setNowSuperviseNum(tips.getSelf());
        //获取昨日统计数据
        LocalDate localDate = LocalDate.now();
        LocalDate previousDate = localDate.minusDays(1);
        Date before1DayDate = Date.from(previousDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String dateDay = dateFormat.format(before1DayDate);
        Example example = new Example(SuperviseHistoryEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andLessThanOrEqualTo("date",dateDay+" 23:59:59")
                .andGreaterThanOrEqualTo("date",dateDay+" 00:00:00")
                .andEqualTo("orgId",sysHeader.getOid());
        List<SuperviseHistoryEntity> superviseHistoryEntity = superviseHistoryMapper.selectByExample(example);
        //正常情况下只会存在一条
        if(superviseHistoryEntity != null && superviseHistoryEntity.size() > 0){
            superviseVO.setBeforeDaySuperviseNum(superviseHistoryEntity.get(0).getSelf());
        }
        //获取预警项
        SuperviseListForm superviseListForm = superviseService.list(86L, sysHeader.getOid());
        List<SuperviseVO.SuperviseItem> superviseItems = new ArrayList<>();
        if(superviseListForm != null && superviseListForm.getOptionList() != null && superviseListForm.getOptionList().size() > 0){
            for(OptionForm optionForm : superviseListForm.getOptionList()){
                SuperviseVO.SuperviseItem superviseItem = new SuperviseVO().new SuperviseItem();
                superviseItem.setName(optionForm.getOptionName());
                superviseItem.setDate(optionForm.getCreateTime());
                superviseItems.add(superviseItem);
            }
            superviseVO.setSuperviseItems(superviseItems);
        }
        return superviseVO;
    }

    public Object partyFeePayment(HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        PartyFeePaymentVo partyFeePaymentVo = new PartyFeePaymentVo();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        String nowMonth = dateFormat.format(new Date());
        //已交人数和金额
        List<PpmdStsVo> submittedPpmdStsVos = ppmdStatisticsMapper.getSubmitted(sysHeader.getOid(),nowMonth);
        for(PpmdStsVo submittedPpmdStsVo : submittedPpmdStsVos){
            if(submittedPpmdStsVo.getPartyMemberAmount() != null && submittedPpmdStsVo.getPartyMemberAmount() != 0){
                partyFeePaymentVo.setSubmittedAmount(new BigDecimal(partyFeePaymentVo.getSubmittedAmount()).add(new BigDecimal(submittedPpmdStsVo.getPartyMemberAmount())).doubleValue());
            }
            if(submittedPpmdStsVo.getPartyMemberNum() != null && submittedPpmdStsVo.getPartyMemberNum() != 0){
                partyFeePaymentVo.setSubmittedMemberNum(partyFeePaymentVo.getSubmittedMemberNum() + submittedPpmdStsVo.getPartyMemberNum());
            }
        }
        //应交人数和金额
        List<PpmdStsVo> notSubmittedPpmdStsVos = ppmdStatisticsMapper.getNotSubmitted(sysHeader.getOid(),nowMonth);
        for(PpmdStsVo notSubmittedPpmdStsVo : notSubmittedPpmdStsVos){
            if(notSubmittedPpmdStsVo.getPartyMemberNum() != null && notSubmittedPpmdStsVo.getPartyMemberNum() != 0){
                partyFeePaymentVo.setNotSubmittedMemberNum(notSubmittedPpmdStsVo.getPartyMemberNum() - partyFeePaymentVo.getSubmittedMemberNum());
            }
        }

        //获取去年年月
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -1);
        Date oneYearAgo = calendar.getTime();
        String lastYearMonth = dateFormat.format(oneYearAgo);

        List<PartyFeePaymentVo.MonthData> thisYearMonthData = new ArrayList<>();
        List<PartyFeePaymentVo.MonthData> lastYearMonthData = new ArrayList<>();
        //统计去年同月1 5 10 15 20 25 最后一天，当月已交人数
        List<String> allDays = new ArrayList<>(Arrays.asList("01","05","10","15","20","25"));
        //最后一天赋值
        allDays.add(calendar.getActualMaximum(Calendar.DAY_OF_MONTH)+"");
        for(String thisDay : allDays){
            //今年
            Integer thisYearCount = ppmdStatisticsMapper.getSubmittedMemberNum(sysHeader.getOid(),nowMonth,nowMonth+"-"+thisDay);
            PartyFeePaymentVo.MonthData thisMonthData = new PartyFeePaymentVo().new MonthData();
            thisMonthData.setDay(Integer.valueOf(thisDay));
            thisMonthData.setSubmittedMemberNum(thisYearCount == null?0:thisYearCount);
            thisYearMonthData.add(thisMonthData);
            //去年
            Integer lastYearCount = ppmdStatisticsMapper.getSubmittedMemberNum(sysHeader.getOid(),lastYearMonth,lastYearMonth+"-"+thisDay);
            PartyFeePaymentVo.MonthData lastMonthData = new PartyFeePaymentVo().new MonthData();
            lastMonthData.setDay(Integer.valueOf(thisDay));
            lastMonthData.setSubmittedMemberNum(lastYearCount == null?0:lastYearCount);
            lastYearMonthData.add(lastMonthData);
        }
        partyFeePaymentVo.setLastYearMonthData(lastYearMonthData);
        partyFeePaymentVo.setThisYearMonthData(thisYearMonthData);
        return partyFeePaymentVo;
    }

    private String getOrgTypeStr(Integer orgTypeChild){
        //党委
        if(orgTypeConfig.getCommittee().contains(orgTypeChild)){
            return "党委";
        }else if(orgTypeConfig.getGeneralBranchChild().contains(orgTypeChild)){
            return "总支";
        }else if(orgTypeConfig.getBranchChild().contains(orgTypeChild)){
            return "支部";
        }else if(orgTypeConfig.getPartyGroup().contains(orgTypeChild)){
            return "党组";
        }else if(orgTypeConfig.getCommunistGroup().contains(orgTypeChild)){
            return "小组";
        }else{
            return "-";
        }

    }

    private Double evaluationGetProgress(Integer a,Integer total){
        if(total.equals(0)){
            return 0.0;
        }
        return new BigDecimal(a).multiply(new BigDecimal(100)).divide(new BigDecimal(total),2, RoundingMode.HALF_UP).doubleValue();
    }

    private Double calculateProportion(DataCockpitOverviewActivityVo dataCockpitOverviewActivityVo){
        if(dataCockpitOverviewActivityVo.getTotal() == null ||
                dataCockpitOverviewActivityVo.getTotal() == 0 ||
                dataCockpitOverviewActivityVo.getValue() == null ||
                dataCockpitOverviewActivityVo.getValue() == 0){
            return 0.00;
        }
        double proportion = BigDecimal.valueOf((double) dataCockpitOverviewActivityVo.getValue() / dataCockpitOverviewActivityVo.getTotal() * 100)
                .setScale(4, RoundingMode.HALF_UP).doubleValue();
        return proportion;
    }

    /**
     * 包装信息
     *
     * @return
     */
    private Double packInfoNew1(String value) {
        try {
            int result = Integer.parseInt(value);
            return result > 0 ? 100.00 : 0.00;
        } catch (Exception ex) {
            return 0.00;
        }
    }

    private Integer transformationNum(String value){
        if(StringUtils.isEmpty(value) || value.equals("-")){
            return 0;
        }else {
            return Integer.valueOf(value);
        }
    }
}
