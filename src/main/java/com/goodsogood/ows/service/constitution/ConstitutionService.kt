package com.goodsogood.ows.service.constitution

import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.configuration.CountExcludeConfig
import com.goodsogood.ows.configuration.TagConfig
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.mapper.constitution.ConstitutionMapper
import com.goodsogood.ows.model.db.user.OrganizationEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.model.vo.constitution.ConstitutionCountVO
import com.goodsogood.ows.service.investigation.InvestigationService
import com.goodsogood.ows.service.sas.OpenService
import com.goodsogood.ows.service.user.OrgService
import lombok.extern.slf4j.Slf4j
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Service
import java.util.stream.Collectors

@Service
@Slf4j
class ConstitutionService(
    @Autowired val errors: Errors,
    @Autowired val orgService: OrgService,
    @Autowired val openService: OpenService,
    @Autowired val constitutionMapper: ConstitutionMapper,
    @Autowired final val countExcludeConfig: CountExcludeConfig,
    @Autowired final val tagConfig: TagConfig
) {

    private val log = LoggerFactory.getLogger(InvestigationService::class.java)

    private val tagId = tagConfig.constitution

    fun count(headers: HttpHeaders, name: String?, year: Int?): List<ConstitutionCountVO>? {
        val excludedUnitsStr = countExcludeConfig.excludeOrgIds
        val excludedUnits = excludedUnitsStr?.split(",")?.mapNotNull { it.toLongOrNull() }?.toList()
        val orgId = HeaderHelper.buildMyHeader(headers).uoid ?: HeaderHelper.buildMyHeader(headers).oid
        val yearInit: Int = if (year == null) {
            val now = java.time.Year.now()
            now.value
        } else {
            year
        }
        if (orgId == 3L) {
            //顶级组织

            //所有党小组的数据 -- 调用远程服务
            val allGroupIdList: List<Long> = orgService.getAllGroupsIds()
            //所有党小组的统计数据
            val groupCountList = constitutionMapper.countElseOrgByList(
                allGroupIdList,
                null,
                yearInit,
                tagId,
                true
            )
            log.debug("所有党小组的统计数据:{}", groupCountList)
            //所有党小组统计数据的父节点id -- 调用远程服务
            val groupList: List<OrganizationBase> = openService.findOrgByIds(
                groupCountList.map { it.orgId }.toList(),
                headers
            )
            val groupParentList: List<OrganizationBase> =
                openService.findOrgByIds(groupList.map { it.parentId }.toList(), headers)
            log.debug("所有党小组统计数据的父节点id:{}", groupParentList)
            //所有非党小组的统计数据
            val exceptCountList = constitutionMapper.countElseOrgByList(
                allGroupIdList,
                null,
                yearInit,
                tagId,
                false
            )
            log.debug("所有非党小组的统计数据:{}", exceptCountList)
            //获取所有非党小组的orgList --调用远程服务
            val exceptOrgList: List<OrganizationBase> =
                openService.findOrgByIds(exceptCountList.map { it.orgId }.toList(), headers)
            log.debug("所有非党小组的orgList:{}", exceptOrgList)
            val resultMap = mutableMapOf<Long, ConstitutionCountVO>()
            var ownerId: Long
            var ownerName: String
            for (i in 0 until groupParentList.size) {
                ownerId = groupParentList[i].ownerId
                ownerName = groupParentList[i].ownerName
                if (excludedUnits != null) {
                    if (ownerId in excludedUnits) continue
                }
                if (!resultMap.containsKey(ownerId)) {
                    resultMap[ownerId] = ConstitutionCountVO(
                        unitId = ownerId,
                        unitName = ownerName,
                        times = groupCountList[i].times
                    )
                } else {
                    resultMap[ownerId]?.times = groupCountList[i].times?.let {
                        resultMap[ownerId]?.times?.plus(
                            it
                        )
                    }
                }
            }
            for (i in 0 until exceptOrgList.size) {
                ownerId = exceptOrgList[i].ownerId
                ownerName = exceptOrgList[i].ownerName
                if (excludedUnits != null) {
                    if (ownerId in excludedUnits) continue
                }
                if (!resultMap.containsKey(ownerId)) {
                    resultMap[ownerId] = ConstitutionCountVO(
                        unitId = ownerId,
                        unitName = ownerName,
                        times = exceptCountList[i].times
                    )
                } else {
                    resultMap[ownerId]?.times = exceptCountList[i].times?.let {
                        resultMap[ownerId]?.times?.plus(
                            it
                        )
                    }
                }
            }
            //获取所有单位
            val corpList = openService.getCorps(headers)

            val resultList = resultMap.values.toList().toMutableList()
            corpList.forEach { corp ->
                if (resultList.none { it.unitId == corp.orgId }) {
                    val newReadClassCountVO =
                        ConstitutionCountVO(unitId = corp.orgId, unitName = corp.name, times = 0)
                    resultList += newReadClassCountVO
                }
            }

//            log.debug("name是:{}", name)
            //模糊查询
            val list: MutableList<ConstitutionCountVO> = (if (name != null) {
                resultList.filter { it.unitName!!.contains(name) }
            } else {
                resultList
            }).toMutableList()
            //seq初始化   duration保留一位小数
            list.forEach { readClassCountVO ->
                // 找到与 corpList 中 unitId 相匹配的元素
                val matchedCorp = corpList.find { it.orgId == readClassCountVO.unitId }
                // 如果找到了匹配的元素，则将其 seq 赋值给 readClassCountVO 的 seq
                matchedCorp?.let {
                    readClassCountVO.seq = it.seq
                }
            }
            return list.sortedBy { it.seq }
        } else {
            //非顶级组织

            val orgList = orgService.findAllChildOrg(orgId, 1, null, false)
            val orgIds = orgList.stream()
                .map(OrganizationEntity::getOrganizationId)
                .collect(Collectors.toList())
            val list = constitutionMapper.countElseOrgByList(orgIds, name, yearInit, tagId, true).toMutableList()
            orgList.forEach { org ->
                if (list.none { it.orgId == org.organizationId }) {
                    val newReadClassCountVO =
                        ConstitutionCountVO(orgId = org.organizationId, orgName = org.name, times = 0)
                    list += newReadClassCountVO
                }
            }
            val resultList: MutableList<ConstitutionCountVO> = if (name != null) {
                list.filter { it.orgName?.contains(name, ignoreCase = true) == true }.toMutableList()
            } else {
                list.toMutableList()
            }
            //赋值组织名称
            list.forEach {
                orgList.forEach { orgItem ->
                    if (it.orgId == orgItem.organizationId) {
                        it.orgName = orgItem.name
                    }
                }
            }
            return resultList.sortedBy { it.orgId }
        }
    }
}