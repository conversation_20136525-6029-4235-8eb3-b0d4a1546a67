package com.goodsogood.ows.service.operation;

import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.model.mongodb.operation.ClickMenu;
import com.goodsogood.ows.model.mongodb.operation.DateVisitRate;
import com.goodsogood.ows.model.mongodb.operation.OrgVisitRate;
import com.goodsogood.ows.model.mongodb.operation.VisitDays;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.operation.OverviewVo;
import com.goodsogood.ows.model.vo.operation.VisitDateResult;
import com.goodsogood.ows.model.vo.operation.VisitNumVo;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.NumberUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : operationService
 * <AUTHOR> tc
 * @Date: 2022/4/13 14:49
 * @Description : 系统运营功能服务类
 */
@Service
@Log4j2
public class OperationService {

    private final DataSourceService dataSourceService;
    private final MyMongoTemplate mongoTemplate;
    private final SimpleApplicationConfigHelper configHelper;
    private final OrganizationMapper organizationMapper;
    private final OrgTypeConfig orgTypeConfig;
    @Value("${user-option.testBranch}")
    private String testBranch;
    @Value("${user-option.virtualOrg}")
    private String virtualOrg;

    @Autowired
    public OperationService(DataSourceService dataSourceService, MyMongoTemplate mongoTemplate, SimpleApplicationConfigHelper configHelper, OrganizationMapper organizationMapper, OrgTypeConfig orgTypeConfig) {
        this.dataSourceService = dataSourceService;
        this.mongoTemplate = mongoTemplate;
        this.configHelper = configHelper;
        this.organizationMapper = organizationMapper;
        this.orgTypeConfig = orgTypeConfig;
    }

    /**
     * 查询数据概览
     *
     * @param header
     * @return
     */
    public OverviewVo overview(HeaderHelper.SysHeader header) {
        OverviewVo re = new OverviewVo();
        try {
            //获取登录人员信息
            Long regionId = header.getRegionId();
            Long userId = header.getUserId();
            Long orgId = header.getUoid() != null ? header.getUoid() : header.getOid();
            re = dataSourceService.findOverview(regionId, userId, orgId);
        } catch (Exception e) {
            log.error("查询数据概览报错！", e);
        }
        return re;
    }

    /**
     * 按日期查询统计访问率
     *
     * @param header
     * @return
     */
    public List<VisitDateResult> visitRateDate(HeaderHelper.SysHeader header, String startTime, String endTime) {
        List<VisitDateResult> re = new ArrayList<>();
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("regionId").is(header.getRegionId())
                    .andOperator(Criteria.where("statsDate").gte(startTime),
                            Criteria.where("statsDate").lte(endTime)));
            List<DateVisitRate> tmp = mongoTemplate.find(query, DateVisitRate.class);
            for (DateVisitRate ovr : tmp) {
                VisitDateResult vdr = new VisitDateResult();
                vdr.setStatsDate(ovr.getStatsDate());
                vdr.setVisitNum(ovr.getVisitNum());
                vdr.setVisitRate(Double.valueOf(ovr.getVisitRate()));
                re.add(vdr);
            }
            //根据要求排序
            re = re.stream().sorted(Comparator.comparing(VisitDateResult::getStatsDate)).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("按日期查询统计访问率报错！", e);
        }
        return re;
    }


    /**
     * 按组织统计访问率
     *
     * @param header
     * @return
     */
    public List<VisitDateResult> visitRateOrg(HeaderHelper.SysHeader header, String statsDate, Long orgId) {
        List<VisitDateResult> re = new ArrayList<>();
        try {
            Long regionId = header.getRegionId();
            List<Long> orgIdList = this.findOrgIdListHelper(header, regionId, orgId);
            log.debug("按组织统计访问率 返回的组织列表:参数:header={} orgId={}   结果:orgIdList={}  ", header, orgId, orgIdList);
            if (orgIdList.size() > 0) {
                //记录查询结果
                Query query = new Query();
                query.addCriteria(Criteria.where("regionId").is(regionId)
                        .and("orgId").in(orgIdList)
                        .andOperator(Criteria.where("statsDate").gte(statsDate),
                                Criteria.where("statsDate").lte(statsDate)));
                List<OrgVisitRate> tmp = mongoTemplate.find(query, OrgVisitRate.class);
                if (!CollectionUtils.isEmpty(tmp)) {
                    for (OrgVisitRate t : tmp) {
                        VisitDateResult v = new VisitDateResult();
                        BeanUtils.copyProperties(t, v);
                        String orgName = StringUtils.isNotBlank(t.getShortName()) ? t.getShortName() : t.getOrgName();
                        v.setOrgName(orgName);
                        v.setVisitRate(Double.valueOf(t.getVisitRate()));
                        re.add(v);
                    }
                    //根据要求排序
                    re = re.stream().sorted(Comparator.comparing(VisitDateResult::getVisitRate).reversed().thenComparing(VisitDateResult::getVisitTime).thenComparing(VisitDateResult::getOrgId)).collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            log.error("按组织统计访问率报错！", e);
        }
        return re;
    }

    /**
     * 按组织统计人均访问天数
     *
     * @param header
     * @return
     */
    public List<VisitDateResult> visitDaysOrg(HeaderHelper.SysHeader header, Long orgId) {
        List<VisitDateResult> re = new ArrayList<>();
        try {
            Long regionId = header.getRegionId();
            List<Long> orgIdList = this.findOrgIdListHelper(header, regionId, orgId);
            log.debug("按组织统计人均访问天数 返回的组织列表:参数:header={} orgId={}   结果:orgIdList={}  ", header, orgId, orgIdList);
            if (orgIdList.size() > 0) {
                //记录查询结果
                String statsDate = DateUtils.getDateByDays(-1, "yyyy-MM-dd");
                Query query = new Query();
                query.addCriteria(Criteria.where("regionId").is(regionId)
                        .and("orgId").in(orgIdList)
                        .andOperator(Criteria.where("statsDate").gte(statsDate),
                                Criteria.where("statsDate").lte(statsDate)));
                List<OrgVisitRate> tmp = mongoTemplate.find(query, OrgVisitRate.class);
                if (!CollectionUtils.isEmpty(tmp)) {
                    for (OrgVisitRate t : tmp) {
                        VisitDateResult v = new VisitDateResult();
                        BeanUtils.copyProperties(t, v);
                        if (StringUtils.isNotBlank(t.getVisitDays())) {
                            v.setVisitDays(Double.valueOf(t.getVisitDays()));
                        }
                        String orgName = StringUtils.isNotBlank(t.getShortName()) ? t.getShortName() : t.getOrgName();
                        v.setOrgName(orgName);
                        re.add(v);
                    }
                    //根据要求排序
                    re = re.stream().sorted(Comparator.comparing(VisitDateResult::getVisitDays).reversed().thenComparing(VisitDateResult::getVisitTime)).collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            log.error("按组织统计人均访问天数报错！", e);
        }
        return re;
    }

    /**
     * 按用户统计累计访问天数
     *
     * @param header
     * @param type   1.全市 2.所在单位 3.所在支部
     * @return
     */
    public List<VisitDateResult> visitDaysUser(HeaderHelper.SysHeader header, Integer type) {
        List<VisitDateResult> re = new ArrayList<>();
        try {
            Long regionId = header.getRegionId();
            //记录查询结果
            String statsDate = DateUtils.getDateByDays(-1, "yyyy-MM-dd");
            Query query = new Query();
            Criteria cr = Criteria.where("regionId").is(regionId)
                    .andOperator(Criteria.where("statsDate").gte(statsDate),
                            Criteria.where("statsDate").lte(statsDate));
            //判断范围
            if (type == 2) {
                //所在单位范围
                Long ownerId = organizationMapper.findUserUnitId(regionId, 102803, header.getUserId());
                List<Long> userIdList = organizationMapper.findUnitUser(regionId, ownerId);
                cr.and("userId").in(userIdList);
            } else if (type == 3) {
                //所在支部范围
                OrganizationBase ob = organizationMapper.findUserOrgInfo(regionId, 102803, header.getUserId());
                cr.and("orgId").is(ob.getOrganizationId());
            }
            query.addCriteria(cr);
            List<VisitDays> tmp = mongoTemplate.find(query, VisitDays.class);
            if (!CollectionUtils.isEmpty(tmp)) {
                for (VisitDays t : tmp) {
                    VisitDateResult v = new VisitDateResult();
                    BeanUtils.copyProperties(t, v);
                    if (Objects.nonNull(t.visitDays)) {
                        v.setVisitDays(Double.valueOf(t.getVisitDays()));
                        v.setVisitTime(t.getVisitTime());
                    }
                    String orgName = StringUtils.isNotBlank(t.getShortName()) ? t.getShortName() : t.getOrgName();
                    v.setOrgName(orgName);
                    re.add(v);
                }
                //根据要求排序
                re = re.stream().sorted(Comparator.comparing(VisitDateResult::getVisitDays).reversed().thenComparing(VisitDateResult::getVisitTime)).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("按用户统计累计访问天数报错！", e);
        }
        return re;
    }

    /**
     * 按时间段同级全市访问量
     *
     * @param header
     * @return
     */
    public List<VisitDateResult> visitNum(HeaderHelper.SysHeader header, String startTime, String endTime) {
        List<VisitDateResult> re = new ArrayList<>();
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("regionId").is(header.getRegionId())
                    .andOperator(Criteria.where("statsDate").gte(startTime),
                            Criteria.where("statsDate").lte(endTime)));
            List<VisitNumVo> tmp = mongoTemplate.find(query, VisitNumVo.class);
            if (!CollectionUtils.isEmpty(tmp)) {
                //根据时间段分组汇总
                Set<String> rangeSet = new HashSet<>();
                Map<String, VisitDateResult> tmpMap = new HashMap<>();
                tmp.forEach(t -> {
                    if (rangeSet.add(t.getRange())) {
                        VisitDateResult vdr = new VisitDateResult();
                        vdr.setRange(t.getRange());
                        vdr.setVisitNum(t.getVisitNum());
                        tmpMap.put(t.getRange(), vdr);
                    } else {
                        VisitDateResult vdr = tmpMap.get(t.getRange());
                        //累计结果
                        vdr.setRange(t.getRange());
                        vdr.setVisitNum(NumberUtils.addInteger(vdr.getVisitNum(), t.getVisitNum()));
                        tmpMap.put(t.getRange(), vdr);
                    }
                });
                //填入结果
                re.addAll(tmpMap.values());
                //根据要求排序
                re = re.stream().sorted(Comparator.comparing(VisitDateResult::getRange)).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("按时间段同级全市访问量报错！", e);
        }
        return re;
    }

    /**
     * 统计访问量最高的功能
     *
     * @param header
     * @return
     */
    public VisitDateResult clickMenuTop(HeaderHelper.SysHeader header) {
        VisitDateResult re = new VisitDateResult();
        try {
            //当月
            String month = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            String monthStartTime = month + "-01";
            String monthEndTime = DateUtils.getEndOfMonth(month);
            //查询当月访问人次数据
            Query queryVisitMonth = new Query();
            queryVisitMonth.addCriteria(Criteria.where("regionId").is(header.getRegionId())
                    .andOperator(Criteria.where("statsDate").gte(monthStartTime),
                            Criteria.where("statsDate").lte(monthEndTime)));
            List<VisitNumVo> tmpVisitMonth = mongoTemplate.find(queryVisitMonth, VisitNumVo.class);
            if (tmpVisitMonth.size() > 0) {
                VisitDateResult maxVisit = this.findMaxVisitDate(tmpVisitMonth);
                Query queryClickMonth = new Query();
                queryClickMonth.addCriteria(Criteria.where("regionId").is(header.getRegionId())
                        .andOperator(Criteria.where("statsDate").gte(maxVisit.getStatsDate()),
                                Criteria.where("statsDate").lte(maxVisit.getStatsDate())));
                queryClickMonth.with(Sort.by(new Sort.Order(Sort.Direction.DESC, "clickNum")));
                ClickMenu tmpClickMonth = mongoTemplate.findOne(queryClickMonth, ClickMenu.class);
                //处理当月数据
                re.setTopVisitMonth(maxVisit.getStatsDate());
                re.setVisitNumMonth(maxVisit.getVisitNum());
                re.setMenuNameMonth(tmpClickMonth != null ? tmpClickMonth.getMenuName() : "");
            }

            //当年
            Integer year = Integer.valueOf(month.split("-")[0]);
            String yearStartTime = year + "-01-01";
            String yearEndTime = year + "-12-31";
            //查询当年访问人次数据
            Query queryVisitYear = new Query();
            queryVisitYear.addCriteria(Criteria.where("regionId").is(header.getRegionId())
                    .andOperator(Criteria.where("statsDate").gte(yearStartTime),
                            Criteria.where("statsDate").lte(yearEndTime)));
            List<VisitNumVo> tmpVisitYear = mongoTemplate.find(queryVisitYear, VisitNumVo.class);
            if (tmpVisitYear.size() > 0) {
                VisitDateResult maxVisit = this.findMaxVisitDate(tmpVisitYear);

                Query queryClickYear = new Query();
                queryClickYear.addCriteria(Criteria.where("regionId").is(header.getRegionId())
                        .andOperator(Criteria.where("statsDate").gte(maxVisit.getStatsDate()),
                                Criteria.where("statsDate").lte(maxVisit.getStatsDate())));
                queryClickYear.with(Sort.by(new Sort.Order(Sort.Direction.DESC, "clickNum")));
                ClickMenu tmpClickYear = mongoTemplate.findOne(queryClickYear, ClickMenu.class);
                //处理当年数据
                re.setTopVisitYear(maxVisit.getStatsDate());
                re.setVisitNumYear(maxVisit.getVisitNum());
                re.setMenuNameYear(tmpClickYear != null ? tmpClickYear.getMenuName() : "");
            }
        } catch (Exception e) {
            log.error("统计访问量最高的功能报错！", e);
        }
        return re;
    }

    /**
     * 功能访问情况
     *
     * @param header
     * @return
     */
    public List<VisitDateResult> clickMenu(HeaderHelper.SysHeader header, String startTime, String endTime) {
        List<VisitDateResult> re = new ArrayList<>();
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("regionId").is(header.getRegionId())
                    .andOperator(Criteria.where("statsDate").gte(startTime),
                            Criteria.where("statsDate").lte(endTime)));
            List<ClickMenu> tmp = mongoTemplate.find(query, ClickMenu.class);
            if (!CollectionUtils.isEmpty(tmp)) {
                //根据功能名称分组汇总
                Set<String> nameSet = new HashSet<>();
                Map<String, VisitDateResult> tmpMap = new HashMap<>();
                tmp.forEach(t -> {
                    if (nameSet.add(t.getMenuName())) {
                        VisitDateResult vdr = new VisitDateResult();
                        vdr.setMenuName(t.getMenuName());
                        vdr.setClickNum(t.getClickNum());
                        vdr.setVisitTime(t.getLastClickTime());
                        tmpMap.put(t.getMenuName(), vdr);
                    } else {
                        VisitDateResult vdr = tmpMap.get(t.getMenuName());
                        //累计结果
                        vdr.setMenuName(t.getMenuName());
                        vdr.setClickNum(NumberUtils.addLong(vdr.getClickNum(), t.getClickNum()));
                        //最后点击时间
                        if (vdr.getVisitTime().compareTo(t.getLastClickTime()) < 0) {
                            vdr.setVisitTime(t.getLastClickTime());
                        }
                        tmpMap.put(t.getMenuName(), vdr);
                    }
                });
                re.addAll(tmpMap.values());
                //根据要求排序
                re = re.stream().sorted(Comparator.comparing(VisitDateResult::getClickNum).reversed().thenComparing(VisitDateResult::getVisitTime)).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("功能访问情况报错！", e);
        }
        return re;
    }

    /**
     * 根据用户登录组织类型，判断应该返回的
     *
     * @param header
     * @param regionId
     * @param orgId
     * @return
     */
    private List<Long> findOrgIdListHelper(HeaderHelper.SysHeader header, Long regionId, Long orgId) {
        List<Long> orgIdList = new ArrayList<>();
        if (orgId == null) {
            //默认展示排行榜
            //登录的组织编号
            Long oid = header.getUoid() != null ? header.getUoid() : header.getOid();
            if (oid.equals(configHelper.getOrgByRegionId(regionId).getOrgId())) {
                //如果是顶级组织登录，查看顶级单位下一级党组织统计结果
                orgIdList = organizationMapper.findUnitTopOrgIds(regionId, testBranch, virtualOrg);
            } else {
                //如果不是顶级组织登录，判断是否为党小组
                OrganizationBase orgInfo = organizationMapper.findOrgTypeChildByOrgId(regionId, oid, 102803, testBranch);
                boolean isOrgGroup = orgTypeConfig.checkIsCommunistGroup(orgInfo.getOrgTypeChild());
                if (!isOrgGroup) {
                    //如果不是党组组织,查看同父组织的同级组织统计结果，排除党小组
                    orgIdList = organizationMapper.findBrotherOrgId(regionId, oid, 102803, testBranch, 1);
                } else {
                    //党小组组织，查看所属支部同父组织的同级组织统计结果
                    orgIdList = organizationMapper.findBrotherOrgId(regionId, orgInfo.getParentId(), 102803, testBranch, null);
                }
            }
        } else {
            //指定钻取组织的直属下级
            List<OrganizationBase> orgList = organizationMapper.findChildByOrgIdExclude(regionId, orgId, 102803, orgTypeConfig.getCommunistGroup(), testBranch);
            orgIdList = orgList.stream().map(OrganizationBase::getOrgId).collect(Collectors.toList());
        }
        return orgIdList;
    }

    private VisitDateResult findMaxVisitDate(List<VisitNumVo> visitDate) {
        VisitDateResult re = new VisitDateResult();
        //根据日期分组汇总
        Set<String> statsDateSet = new HashSet<>();
        Map<String, VisitDateResult> tmpMap = new HashMap<>();
        visitDate.forEach(t -> {
            if (statsDateSet.add(t.getStatsDate())) {
                VisitDateResult vdr = new VisitDateResult();
                vdr.setStatsDate(t.getStatsDate());
                vdr.setVisitNum(t.getVisitNum());
                tmpMap.put(t.getStatsDate(), vdr);
            } else {
                VisitDateResult vdr = tmpMap.get(t.getStatsDate());
                //累计结果
                vdr.setStatsDate(t.getStatsDate());
                vdr.setVisitNum(NumberUtils.addInteger(vdr.getVisitNum(), t.getVisitNum()));
                tmpMap.put(t.getStatsDate(), vdr);
            }
        });
        //找出最高访问日期
        Optional<VisitDateResult> maxVisitNum = tmpMap.values().stream().max(Comparator.comparingInt(VisitDateResult::getVisitNum));
        re = maxVisitNum.get();
        return re;
    }

}
