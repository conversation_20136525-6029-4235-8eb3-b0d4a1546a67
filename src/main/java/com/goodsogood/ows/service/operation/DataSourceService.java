package com.goodsogood.ows.service.operation;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.TimeIntervalConfig;
import com.goodsogood.ows.mapper.user.LoginStatisticsMapper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.mapper.user.UserLoginLogMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.mapper.utilsProxy.BlockchainAmountMapper;
import com.goodsogood.ows.model.db.user.LoginStatisticsEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.mongodb.operation.OperationSystem;
import com.goodsogood.ows.model.vo.VisitLoginForm;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.operation.*;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.NumberUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;
import redis.clients.jedis.commands.JedisCommands;
import redis.clients.jedis.commands.MultiKeyCommands;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @ClassName : DataSourceService
 * <AUTHOR> tc
 * @Date: 2022/4/13 15:05
 * @Description : 数据来源服务类
 */
@Service
@Log4j2
public class DataSourceService {


    @Value("${user-option.testBranch}")
    private String testBranch;

    private final UserMapper userMapper;
    private final UserService userService;
    private final OrgService orgService;
    private final OrgTypeConfig orgTypeConfig;
    private final OrganizationMapper organizationMapper;
    private final UserLoginLogMapper userLoginLogMapper;
    private final StringRedisTemplate redisTemplate;
    private final SimpleApplicationConfigHelper simpleApplicationConfigHelper;
    private final TimeIntervalConfig timeIntervalConfig;
    private final MyMongoTemplate mongoTemplate;
    private final BlockchainAmountMapper blockchainAmountMapper;
    private final LoginStatisticsMapper loginStatisticsMapper;

    @Autowired
    public DataSourceService(UserMapper userMapper, UserService userService, OrgService orgService,
                             OrgTypeConfig orgTypeConfig, OrganizationMapper organizationMapper,
                             UserLoginLogMapper userLoginLogMapper, StringRedisTemplate redisTemplate,
                             SimpleApplicationConfigHelper simpleApplicationConfigHelper,
                             TimeIntervalConfig timeIntervalConfig, MyMongoTemplate mongoTemplate,
                             BlockchainAmountMapper blockchainAmountMapper, LoginStatisticsMapper loginStatisticsMapper) {
        this.userMapper = userMapper;
        this.userService = userService;
        this.orgService = orgService;
        this.orgTypeConfig = orgTypeConfig;
        this.organizationMapper = organizationMapper;
        this.userLoginLogMapper = userLoginLogMapper;
        this.redisTemplate = redisTemplate;
        this.simpleApplicationConfigHelper = simpleApplicationConfigHelper;
        this.timeIntervalConfig = timeIntervalConfig;
        this.mongoTemplate = mongoTemplate;
        this.blockchainAmountMapper = blockchainAmountMapper;
        this.loginStatisticsMapper = loginStatisticsMapper;
    }

    /**
     * 根据用户编号和登录的单位编号查询系统运行信息概览
     *
     * @param userId userId
     * @param orgId  orgId
     */
    public OverviewVo findOverview(Long regionId, Long userId, Long orgId) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.debug("【系统运行报告】，耗时 -> [{}]", stopWatch.toString());
        OverviewVo vo = new OverviewVo();
        //用户编号，用户名称，累计访问天数，全市排行，目前选择登录的单位排行，目前登录单位今日访问人数，目前登录单位今日访问率，全市今日访问人数，全市今日访问率，目前登录单位在线人数，全市在线人数
        try {
            // 查询当前登录人信息
            final UserInfoBase baseInfo = userService.userBaseInfo(userId, orgId, null, regionId);
            if (baseInfo != null) {
                vo.setUserId(userId);
                vo.setUserName(baseInfo.getUserName());
            }
            log.debug("【系统运行报告】，开始设置排名，耗时 -> [{}]", stopWatch.toString());
            //设置区块链存链数量
            Integer ba = blockchainAmountMapper.findBlockchainAmount(regionId);
            log.debug("【系统运行报告】，设置区块链存链数量，耗时 -> [{}]", stopWatch.toString());
            vo.setBlockchainNum(ba != null ? ba : 0);
            //更新时间
            vo.setStatsDate(DateUtils.dateFormat(new Date(), "yyyy-MM-dd HH:mm"));
            // 设置排名
            setRanking(vo, regionId, userId, orgId, stopWatch);
            log.debug("【系统运行报告】，设置排名结束，耗时 -> [{}]", stopWatch.toString());
        } catch (Exception e) {
            log.error("获取用户[{}]信息失败", userId, e);
        }
        return vo;
    }

    /**
     * 根据组织编号查询访问率，人均访问次数分布
     *
     * @param statsDate 统计日期  YYYY-MM-DD
     * @param orgIds    组织ID数组
     * @return VisitRateVo
     */
    public List<VisitRateVo> findVisitRate(Long regionId, String statsDate, List<Long> orgIds) {
        // 日期,上一级组织编号，组织编号，组织名称，组织简称，组织人数(包含所有下级)，组织访问人数(包含所有下级)，最后登录时间，人均访问天数
        log.debug("根据组织查询访问率，日期[{}],组织{}", statsDate, orgIds.toString());
        List<VisitRateVo> result = new ArrayList<>();
        List<Long> testOrgList = Arrays.stream(this.testBranch.split(",")).map(Long::valueOf).collect(Collectors.toList());
        // 根据测试组织查询测试组织用户
        List<Long> testUserList = this.userMapper.findTestOrgUsers(testOrgList);
        orgIds.forEach(orgId -> {
            VisitRateVo resultVo = new VisitRateVo();
            resultVo.setStatsDate(statsDate);
            resultVo.setRegionId(regionId);
            // 组织以及组织所有下级组织-排除测试组织
            List<OrganizationEntity> orgList = this.findAllChildOrg(regionId, orgId, testOrgList);
            // 组织基本信息
            for (OrganizationEntity org : orgList) {
                if (org.getOrganizationId().equals(orgId)) {
                    resultVo.setOrgId(orgId);
                    resultVo.setParentOrgId(org.getParentId());
                    resultVo.setOrgName(org.getName());
                    resultVo.setShortName(org.getShortName());
                    break;
                }
            }
            // 组织所有下级组织ID
            List<Long> orgs = orgList.stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toList());
            // 排除所有不统计的测试组织
            String[] testIds = this.testBranch.split(",");
            for (String testId : testIds) {
                orgs.remove(Long.valueOf(testId));
            }
            // 根据组织ID查询当前组织关系的用户ID
            List<UserEntity> users = this.userMapper.getUserByOrgIds(orgs, 1, null, null,
                    this.orgTypeConfig.getCommunistGroup(), Constants.IS_EMPLOYEE);
            List<Long> userIdList = users.stream().map(UserEntity::getUserId).collect(Collectors.toList());
            // 组织访问人数
            List<VisitLoginForm> allVisitList = this.userLoginLogMapper.findLoginCountByDate(statsDate, 0, userIdList, testUserList);
            if (!CollectionUtils.isEmpty(allVisitList)) {
                int visitNum = allVisitList.size();
                // 组织访问总天数
                resultVo.setVisitNum(visitNum);
                // 登录时间
                resultVo.setVisitTime(allVisitList.get(0).getVisitTime());
                // 组织人员总数
                List<UserEntity> countList = this.userMapper.findUserList(orgId, null, null,
                        this.orgTypeConfig.getCommunistGroup(), Constants.IS_EMPLOYEE);
                // 组织用户累计登录天数
                List<VisitLoginForm> visitLoginFormList = this.userLoginLogMapper.findLoginCountByOrgAndUser(userIdList, statsDate);
                AtomicInteger visitSumDays = new AtomicInteger();
                if (!CollectionUtils.isEmpty(visitLoginFormList)) {
                    visitLoginFormList.forEach(n -> visitSumDays.addAndGet(n.getVisitDays()));
                }
                if (!CollectionUtils.isEmpty(countList)) {
                    int peopleNum = countList.size();
                    resultVo.setPeopleNum(peopleNum);
                    if (peopleNum > 0) {
                        resultVo.setVisitDays(NumberUtils.divide(visitSumDays.get(), peopleNum));
                    } else {
                        resultVo.setVisitDays(0.0);
                    }
                }
            }
            result.add(resultVo);
        });
        log.debug("根据组织查询访问率,日期[{}],result{}", statsDate, result.toString());
        return result;
    }

    /**
     * 查询人员累计访问天数
     *
     * @param statsDate 统计日期  YYYY-MM-DD
     * @return list
     */
    public List<VisitDaysVo> findVisitDays(Long regionId, String statsDate) {
        // 日期,用户编号，用户名称，组织编号，组织名称，累计访问天数，当日首次访问时间
        List<VisitDaysVo> result = new ArrayList<>();
        // 根据区县ID查询顶级党组织ID
        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        // 测试组织orgList
        List<Long> testBranchList = Arrays.stream(this.testBranch.split(",")).map(Long::valueOf).collect(Collectors.toList());
        // 根据测试组织查询测试组织用户
        List<Long> testUserList = this.userMapper.findTestOrgUsers(testBranchList);
        // 根据顶级组织查询所有党员
        List<UserEntity> allList = this.userMapper.findNewUserList(orgData.getOrgId(), testBranchList, this.orgTypeConfig.getCommunistGroup());
        if (!CollectionUtils.isEmpty(allList)) {
            // 查询全市用户累计访问天数
            List<VisitLoginForm> countDays = this.userLoginLogMapper.findLoginCountByUser(testUserList, statsDate, testBranchList);
            // 查询全市所有用户当日首次访问时间
            List<VisitLoginForm> allVisitList = this.userLoginLogMapper.findLoginCountByDate(statsDate, 1, null, testUserList);
            if (!CollectionUtils.isEmpty(allVisitList)) {
                // 首次访问时间
                Map<Long, LocalDateTime> map = allVisitList.stream().collect(Collectors.toMap(VisitLoginForm::getLoginId, VisitLoginForm::getVisitTime, (a, b) -> b));
                // 累计访问天数
                Map<Long, Integer> daysMap = countDays.stream().collect(Collectors.toMap(VisitLoginForm::getLoginId, VisitLoginForm::getVisitDays, (a, b) -> b));
                for (UserEntity userEntity : allList) {
                    VisitDaysVo resultVo = new VisitDaysVo();
                    resultVo.setRegionId(regionId);
                    resultVo.setStatsDate(statsDate);
                    resultVo.setUserId(userEntity.getUserId());
                    resultVo.setUserName(userEntity.getName());
                    resultVo.setOrgId(userEntity.getOrgId());
                    resultVo.setOrgName(userEntity.getOrgName());
                    resultVo.setShortName(userEntity.getShortName());
                    LocalDateTime vt;
                    if (map.get(userEntity.getUserId()) != null) {
                        vt = map.get(userEntity.getUserId());
                    } else {
                        // 最后登录时间不应该受查询时间限制
                        // 目前如果最后访问/登录时间为空，获取用户最后登录日期那天的最早登录时间
                        //tc 2022-04-20
                        String lastTime = userLoginLogMapper.findUserLastLoginDate(regionId, userEntity.getUserId(), statsDate);
                        if (StringUtils.isNotBlank(lastTime)) {
                            vt = DateUtils.asLocalDateTime(DateUtils.stringToDate(lastTime, ""));
                        } else {
                            vt = DateUtils.asLocalDateTime(DateUtils.stringToDate("1900-01-01 00:00:00", ""));
                        }
                    }
                    resultVo.setVisitTime(vt);
                    Integer visitDays = daysMap.get(userEntity.getUserId());
                    resultVo.setVisitDays(Objects.isNull(visitDays) ? 0 : visitDays);
                    result.add(resultVo);
                }
            }
        }
        return result;
    }

    /**
     * 功能热词图
     *
     * @param statsDate 统计日期  YYYY-MM-DD
     * @param channel   渠道
     * @param type      类型
     * @param menuIds   功能菜单编号
     * @return list
     */
    public List<ClickMenuVo> findClickMenu(Long regionId, String statsDate, String channel, String type, List<String> menuIds) {
        //日期，功能名称，点击次数,当日最后一次点击时间
        List<ClickMenuVo> result = new ArrayList<>();
//        Query query = new Query();
        String startTime = statsDate + " 00:00:00";
        String endTime = statsDate + " 23:59:59";
//        query.addCriteria(Criteria.where("regionId").is(regionId).and("channal").is(channel).and("type").is(type)
//                .andOperator(Criteria.where("transferTime").gte(DateUtils.asLocalDateTime(DateUtils.stringToDate(startTime, ""))), Criteria.where("transferTime").lte(DateUtils.asLocalDateTime(DateUtils.stringToDate(endTime, ""))))
//                .and("object").in(menuIds));
//        List<OperationSystem> re = mongoTemplate.find(query, OperationSystem.class);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("regionId").is(regionId).and("channal").is(channel).and("type").is(type)
                        .andOperator(Criteria.where("transferTime").gte(DateUtils.asLocalDateTime(DateUtils.stringToDate(startTime, ""))), Criteria.where("transferTime").lte(DateUtils.asLocalDateTime(DateUtils.stringToDate(endTime, ""))))
                        .and("object").in(menuIds))).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        List<OperationSystem> re = mongoTemplate.aggregate(aggregation, OperationSystem.class).getMappedResults();
        //处理返回结果,根据菜单编号分组聚合
        Map<String, ClickMenuVo> temp = new HashMap<>();
        Set<String> menuNameSet = new HashSet<>();
        re.stream().forEach(r -> {
            if (menuNameSet.add(r.getObject())) {
                //新建
                ClickMenuVo cv = new ClickMenuVo();
                cv.setRegionId(regionId);
                cv.setStatsDate(statsDate);
                cv.setMenuId(r.getObject());
                cv.setClickNum(r.getAmount().longValue());
                cv.setLastClickTime(DateUtils.asLocalDateTime(r.getTransferTime()));
                //记录
                temp.put(r.getObject(), cv);
            } else {
                //累计
                ClickMenuVo cv = temp.get(r.getObject());
                cv.setClickNum(NumberUtils.addLong(cv.getClickNum(), r.getAmount().longValue()));
                //如果点击时间更晚跟新最后点击时间
                if (cv.getLastClickTime().isBefore(DateUtils.asLocalDateTime(r.getTransferTime()))) {
                    cv.setLastClickTime(DateUtils.asLocalDateTime(r.getTransferTime()));
                }
                //记录
                temp.put(r.getObject(), cv);
            }
        });
        //组合返回
        result.addAll(temp.values());
        log.debug("功能热词图断点->[{}],[{}],[{}]", aggregation, re, result);
        return result;
    }

    /**
     * 集中访问时间图
     *
     * @param statsDate    统计日期  YYYY-MM-DD
     * @param timeInterval 时间间隔
     * @return list
     */
    public List<VisitNumVo> findVisitNumByInterval(Long regionId, String statsDate, Integer timeInterval) {
        // 日期，时段，累计访问人次
        List<VisitNumVo> result = new ArrayList<>();
        // 测试组织orgList
        List<Long> testBranchList = Arrays.stream(this.testBranch.split(",")).map(Long::valueOf).collect(Collectors.toList());
        // 根据测试组织查询测试用户
        List<Long> testUserList = this.userMapper.findTestOrgUsers(testBranchList);
        // 时间段
        this.timeIntervalConfig.getIntervals().forEach(interval -> {
            if (interval.getInterval().equals(timeInterval)) {
                interval.getScopes().forEach(scope -> {
                    VisitNumVo resultVo = new VisitNumVo();
                    resultVo.setStatsDate(statsDate);
                    resultVo.setRegionId(regionId);
                    // 时间段
                    String range = scope.getFirst().concat("-").concat(scope.getSecond());
                    resultVo.setRange(range);
                    // 查询条件
                    String first = statsDate.concat(" ").concat(scope.getFirst()).concat(":00");
                    String second = statsDate.concat(" ").concat(scope.getSecond()).concat(":00");
                    // 查询累计登录次数
                    Integer visitNum = this.userLoginLogMapper.findTotalByTimes(first, second, regionId, testUserList);
                    resultVo.setVisitNum(visitNum);
                    result.add(resultVo);
                });
            }
        });
        return result;
    }

    /**
     * 查询组织所有下级
     *
     * @param regionId 区县ID
     * @param orgId    组织ID
     * @param orgs     测试组织ID
     * @return list
     */
    private List<OrganizationEntity> findAllChildOrg(Long regionId, Long orgId, List<Long> orgs) {
        // 组织以及组织所有下级组织
        return this.organizationMapper.findAllChildOrg(regionId, orgId, orgs);
    }

    /**
     * 查询单位下所有组织
     *
     * @param regionId regionId
     * @param corpId   corpId
     * @return list
     */
    private List<OrganizationEntity> findAllOrgByUnitOrg(Long regionId, Long corpId) {
        Example orgExample = new Example(OrganizationEntity.class);
        orgExample.selectProperties("organizationId", "parentId", "name", "shortName");
        orgExample.createCriteria().andEqualTo("status", Constants.STATUS_YES)
                .andEqualTo("regionId", regionId)
                .andEqualTo("ownerId", corpId);
        // 组织以及组织所有下级组织
        return this.organizationMapper.selectByExample(orgExample);
    }

    /**
     * 获取组织列表下的人员列表
     *
     * @param orgIds 组织ID列表
     * @param subOrg 是否包含下级组织 1-是，0-否
     * <AUTHOR>
     * 2022/4/15 5:13 PM
     */
    private List<UserEntity> getUserList(List<Long> orgIds, Integer subOrg, List<Long> testOrgList) {
        return this.userMapper.getUserByOrgIds(orgIds, subOrg, 0, testOrgList, null, null);
    }

    /**
     * @title 计算登录排名
     * <AUTHOR>
     * @updateTime 2022/4/15 5:57 PM
     */
    private void setRanking(OverviewVo vo, Long regionId, Long userId, Long orgId, StopWatch stopWatch) {
        log.debug("【系统运行报告】计算登录排名，耗时 -> [{}]", stopWatch.toString());
        final String dateFormat = DateUtils.dateFormat(new Date(), "yyyy-MM-dd");
        // 判断当前登录组织是否是顶级组织
        final Region.OrgData orgData = simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        // 测试组织orgList
        List<Long> testBranchList = Arrays.stream(this.testBranch.split(",")).map(Long::valueOf).collect(Collectors.toList());
        log.debug("【系统运行报告】开始查询全市党员人员列表，耗时 -> [{}]", stopWatch.toString());
        // 获取全市党员人员列表
        final List<UserEntity> allList = getUserList(Collections.singletonList(orgData.getOrgId()), 1, testBranchList);
        log.debug("【系统运行报告】获取全市党员人员列表，耗时 -> [{}]", stopWatch.toString());
        final List<Long> allIdList = allList.stream().map(UserEntity::getUserId).collect(Collectors.toList());
        log.debug("【系统运行报告】获取全市党员人员IDS，耗时 -> [{}]", stopWatch.toString());
        List<Long> orgIds;
        // 全市总排名
        List<LoginStatisticsEntity> overallRanking = this.getLoginStatistics(allIdList);
        log.debug("【系统运行报告】全市总排名查询，耗时 -> [{}]", stopWatch.toString());
        // 本单位排名
        List<LoginStatisticsEntity> unitRanking = Lists.newArrayList();
        // 当天全市登录次数
        int overallCnt = userLoginLogMapper.findNowLoginCountByUser(allIdList, dateFormat);
        log.debug("【系统运行报告】当天全市登录次数[{}]，耗时 -> [{}]", overallCnt, stopWatch.toString());
        // 当天单位登录次数
        int unitCnt = 0;
        // 单位人员总数
        int unitMemberCnt = 0;
        // 获取全市党员数量
        int overallMemberCnt = allList.size();
        log.debug("【系统运行报告】获取全市党员数量[{}]，耗时 -> [{}]", overallMemberCnt, stopWatch.toString());
        // 查询当前登录组织信息
        OrganizationEntity org = orgService.getById(orgId);
        // 判断当前组织的组织类型为党小组，则取上级组织
        if (orgTypeConfig.getCommunistGroup().contains(org.getOrgTypeChild())) {
            org = orgService.getById(org.getParentId());
        }
        // 根据上级组织的ownerId(单位ID)查询单位关联的所有组织
        if (org.getOwnerId() != null) {
            List<OrganizationEntity> orgList = findAllOrgByUnitOrg(regionId, org.getOwnerId());
            log.debug("【系统运行报告】查询单位关联的所有组织[{}]，耗时 -> [{}]", orgList.stream().map(OrganizationEntity::getOrganizationId), stopWatch.toString());
            orgIds = orgList.stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toList());
            orgList.clear();
            // 获取组织下的所有人员
            final List<UserEntity> userList = getUserList(orgIds, 0, testBranchList);
            unitMemberCnt = userList.size();
            log.debug("【系统运行报告】获取组织下的所有人员[{}]，耗时 -> [{}]", unitMemberCnt, stopWatch.toString());
            final List<Long> userIds = userList.stream().map(UserEntity::getUserId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userIds)) {
                // 获取累计登录排名
                unitRanking = this.getLoginStatistics(userIds);
                log.debug("【系统运行报告】获取累计登录排名[{}]，耗时 -> [{}]", unitRanking, stopWatch.toString());
                // 获取当天登录次数
                unitCnt = userLoginLogMapper.findNowLoginCountByUser(userIds, dateFormat);
                log.debug("【系统运行报告】获取当天登录次数[{}]，耗时 -> [{}]", unitCnt, stopWatch.toString());
            }
        }
        // 全市排名
        for (int i = 0; i < overallRanking.size(); i++) {
            final LoginStatisticsEntity form = overallRanking.get(i);
            if (form.getUserId().equals(userId)) {
                vo.setVisitNum(form.getLoginDay());
                vo.setRankAll(i + 1);
            }
        }
        log.debug("【系统运行报告】全市排名，耗时 -> [{}]", stopWatch.toString());
        // 本单位排名
        for (int i = 0; i < unitRanking.size(); i++) {
            final LoginStatisticsEntity form = unitRanking.get(i);
            if (form.getUserId().equals(userId)) {
                vo.setRankUnit(i + 1);
            }
        }
        log.debug("【系统运行报告】本单位排名，耗时 -> [{}]", stopWatch.toString());
        // 本单位今日访问人数
        vo.setUnitVisit(unitCnt);
        // 本单位今天访问率
        log.debug("本单位今日访问人数[{}]/总人数[{}]", unitCnt, unitMemberCnt);
        vo.setUnitVisitRate(unitMemberCnt == 0 ? 0 : (BigDecimal.valueOf((float) unitCnt / unitMemberCnt).setScale(2, RoundingMode.HALF_UP).doubleValue() * 100));
        // 全市今日访问人数
        vo.setAllVisit(overallCnt);
        // 全市今日访问率
        log.debug("全市今日访问率[{}]/总人数[{}]", overallCnt, overallMemberCnt);
        vo.setAllVisitRate(overallMemberCnt == 0 ? 0 : (BigDecimal.valueOf((float) overallCnt / overallMemberCnt).setScale(2, RoundingMode.HALF_UP).doubleValue() * 100));
        log.debug("计算排名，耗时 -> [{}]", stopWatch.toString());
        // 获取在线人数
        getOnlineNum(vo, orgData.getOrgId(), org.getOwnerId());
        log.debug("计算在线人数，耗时 -> [{}]", stopWatch.toString());
        stopWatch.stop();
    }

    private void getOnlineNum(OverviewVo vo, Long oid, Long unitId) {
        // 本单位当前在线人数
        List<String> orgLevelList = organizationMapper.getOrgIdsByUnit(unitId);
        orgLevelList = orgLevelList.stream().sorted(Comparator.comparingInt(String::length)).collect(Collectors.toList());
        filterLevelList(orgLevelList);
        int unitCnt = 0;
        for (String orgLevel : orgLevelList) {
            unitCnt += scan(Constants.LOGIN_TOKEN + orgLevel).size();
        }
        // 全市在线人数
        final OrganizationEntity entity = organizationMapper.selectByPrimaryKey(oid);
        vo.setAllOnline(scan(Constants.LOGIN_TOKEN + entity.getOrgLevel()).size());
        vo.setUnitOnline(unitCnt);
    }

    public Set<String> scan(String key) {
        return redisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keys = Sets.newHashSet();

            JedisCommands commands = (JedisCommands) connection.getNativeConnection();
            MultiKeyCommands multiKeyCommands = (MultiKeyCommands) commands;

            ScanParams scanParams = new ScanParams();
            scanParams.match("*" + key + "*");
            scanParams.count(1000); // 这个不是返回结果的数量，应该是每次scan的数量
            ScanResult<String> scan = multiKeyCommands.scan("0", scanParams);
            while (null != scan.getCursor()) {
                keys.addAll(scan.getResult()); // 这一次scan match到的结果
                if (!"0".equals(scan.getCursor())) { // 不断拿着新的cursor scan，最终会拿到所有匹配的值
                    scan = multiKeyCommands.scan(scan.getCursor(), scanParams);
                } else {
                    break;
                }
            }
            return keys;
        });
    }

    private void filterLevelList(List<String> orgLevelList) {
        if (CollectionUtils.isEmpty(orgLevelList)) {
            return;
        }
        for (int i = 0; i < orgLevelList.size(); i++) {
            for (int j = i + 1; j < orgLevelList.size(); j++) {
                final String jName = orgLevelList.get(j);
                final String iName = orgLevelList.get(i);
                if (jName.contains(iName)) {
                    orgLevelList.remove(j);
                    j = j - 1;
                }
            }
        }
    }

    private List<LoginStatisticsEntity> getLoginStatistics(List<Long> userIds) {
        Example ex = new Example(LoginStatisticsEntity.class);
        ex.orderBy("loginDay").desc().orderBy("updateTime");
        Example.Criteria criteria = ex.createCriteria();
        if (CollectionUtils.isNotEmpty(userIds)) {
            criteria.andIn("userId", userIds);
        }
        return loginStatisticsMapper.selectByExample(ex);
    }
}
