package com.goodsogood.ows.service.operation;

import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.MongoCollectionNameConfig;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.OperationSystemConfig;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.mongodb.operation.ClickMenu;
import com.goodsogood.ows.model.mongodb.operation.DateVisitRate;
import com.goodsogood.ows.model.mongodb.operation.OrgVisitRate;
import com.goodsogood.ows.model.mongodb.operation.VisitDays;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.operation.ClickMenuVo;
import com.goodsogood.ows.model.vo.operation.VisitDaysVo;
import com.goodsogood.ows.model.vo.operation.VisitNumVo;
import com.goodsogood.ows.model.vo.operation.VisitRateVo;
import com.goodsogood.ows.utils.NumberUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : DataDisposeService
 * <AUTHOR> tc
 * @Date: 2022/4/13 15:05
 * @Description : 数据处理服务类
 */
@Service
@Log4j2
public class DataDisposeService {

    private final DataSourceService dataSourceService;
    private final SimpleApplicationConfigHelper configHelper;
    private final OrganizationMapper organizationMapper;
    private final MyMongoTemplate mongoTemplate;
    private final OrgTypeConfig orgTypeConfig;
    private final OperationSystemConfig operationSystemConfig;
    private final MongoCollectionNameConfig mongoCollectionNameConfig;
    private final UserMapper userMapper;
    @Value("${user-option.testBranch}")
    private String testBranch;


    @Autowired
    public DataDisposeService(DataSourceService dataSourceService, SimpleApplicationConfigHelper configHelper, OrganizationMapper organizationMapper, MyMongoTemplate mongoTemplate, OrgTypeConfig orgTypeConfig, OperationSystemConfig operationSystemConfig, MongoCollectionNameConfig mongoCollectionNameConfig,
                              UserMapper userMapper) {
        this.dataSourceService = dataSourceService;
        this.configHelper = configHelper;
        this.organizationMapper = organizationMapper;
        this.mongoTemplate = mongoTemplate;
        this.orgTypeConfig = orgTypeConfig;
        this.operationSystemConfig = operationSystemConfig;
        this.mongoCollectionNameConfig = mongoCollectionNameConfig;
        this.userMapper = userMapper;
    }

    /**
     * 根据组织编查询访问率，人均访问次数分布
     *
     * @param statsDate 统计日期  YYYY-MM-DD
     * @return string
     */
    public String findVisitRate(Long regionId, String statsDate) {
        try {
            List<Long> testBranchList = Arrays.stream(this.testBranch.split(",")).map(Long::valueOf).collect(Collectors.toList());
            //先清除指定月份数据
            this.delStatisticsData(regionId, statsDate, DateVisitRate.class);
            //先清除指定月份数据
            this.delStatisticsData(regionId, statsDate, OrgVisitRate.class);
            //根据regionId获取顶级组织编号
            Long topOrgId = this.configHelper.getOrgByRegionId(regionId).getOrgId();
            //查询顶级组织的直接下级,排除党小组组织
            List<OrganizationBase> orgList = organizationMapper.findChildByOrgIdExclude(regionId, topOrgId, 102803, orgTypeConfig.getCommunistGroup(), testBranch);
            List<Long> orgIdList = orgList.stream().map(OrganizationBase::getOrgId).collect(Collectors.toList());
            if (orgIdList.size() > 0) {
                //顶级组织的所有直接下级数据
                List<VisitRateVo> visitRateData = dataSourceService.findVisitRate(regionId, statsDate, orgIdList);
                //先计算一下全市访问情况
                //访问人数
                Integer visitNum = visitRateData.stream().reduce(0, (subtotal, visit) -> subtotal + visit.getVisitNum(), Integer::sum);
                //总人数
//                Integer peopleNum = visitRateData.stream().reduce(0, (subtotal, visit) -> subtotal + visit.getPeopleNum(), Integer::sum);
                Integer peopleNum = this.userMapper.findUserTotal(topOrgId, testBranchList);
                //访问率
                double visitRate = 0.0;
                if (peopleNum != null && peopleNum > 0) {
                    visitRate = NumberUtils.divide(visitNum, peopleNum, operationSystemConfig.getScale(), RoundingMode.HALF_UP).doubleValue();
                    visitRate = NumberUtils.multiplyDouble(visitRate, 100.0);
                }

                //按日期统计访问率分布  存入mongodb
                DateVisitRate dvr = new DateVisitRate(regionId, statsDate, visitNum, peopleNum, Double.toString(visitRate));
                mongoTemplate.save(dvr);
                //递归查询下级
                this.visitRateHelper(regionId, statsDate, orgIdList, visitRateData);
                //按组织统计访问率 存入mogodb
                List<OrgVisitRate> orgVisitRateList = new ArrayList<>();
                visitRateData.forEach(vrd -> {
                    //访问率
                    double vRate = 0.0;
                    if (vrd.getPeopleNum() != null && vrd.getPeopleNum() > 0) {
                        vRate = NumberUtils.divide(vrd.getVisitNum(), vrd.getPeopleNum(), operationSystemConfig.getScale(), RoundingMode.HALF_UP).doubleValue();
                        vRate = NumberUtils.multiplyDouble(vRate, 100.0);
                    }
                    String vTime = "9999-99-99 99:99:99";
                    if (vrd.getVisitTime() != null) {
                        vTime = vrd.getVisitTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    }
                    OrgVisitRate o = new OrgVisitRate(regionId, statsDate, vrd.getParentOrgId(), vrd.getOrgId(), vrd.getOrgName(), vrd.getShortName(), vrd.getVisitNum(), vrd.getPeopleNum(), Double.toString(vRate), vrd.getVisitDays().toString(), vTime);
                    orgVisitRateList.add(o);
                });
                mongoTemplate.insertAll(orgVisitRateList);
            }
        } catch (Exception e) {
            log.error("根据组织编查询访问率，人均访问次数分布 处理报错！regionId={} statsDate={}", regionId, statsDate, e);
        }
        return "success";
    }

    /**
     * 查询人员累计访问天数
     *
     * @param statsDate 统计日期  YYYY-MM-DD
     * @return
     */
    public String findVisitDays(Long regionId, String statsDate) {
        try {
            //先清除指定月份数据
            this.delStatisticsData(regionId, statsDate, VisitDays.class);
            //从新统计
            List<VisitDaysVo> visitDaysData = dataSourceService.findVisitDays(regionId, statsDate);
            //存入mogodb
            if (visitDaysData != null && visitDaysData.size() > 0) {
                List<VisitDays> visitDaysList = visitDaysData.stream().map(vdd -> {
                    VisitDays vd = new VisitDays();
                    BeanUtils.copyProperties(vdd, vd);
                    if (Objects.nonNull(vdd.getVisitTime())) {
                        vd.setVisitTime(vdd.getVisitTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                    vd.setRegionId(regionId);
                    return vd;
                }).collect(Collectors.toList());
                mongoTemplate.insertAll(visitDaysList);
            }
        } catch (Exception e) {
            log.error("查询人员累计访问天数 报错！ regionId={} statsDate={}", regionId, statsDate, e);
        }
        return "success";
    }

    /**
     * 功能访问次数统计
     *
     * @param statsDate 统计日期  YYYY-MM-DD
     * @return
     */
    public String findClickMenu(Long regionId, String statsDate) {
        try {
            //先清除指定月份数据
            this.delStatisticsData(regionId, statsDate, ClickMenu.class);
            //日期，功能名称，点击次数,当日最后一次点击时间
            //根据配置获取要统计的功能菜单点击量
            OperationSystemConfig.ClickMenuConf cm = operationSystemConfig.getClickMenu().get(regionId);
            if (cm != null) {
                List<String> menuIds = Arrays.asList(cm.getMenuIds().split(","));
                List<ClickMenuVo> clickMenuData = dataSourceService.findClickMenu(regionId, statsDate, cm.getChannel(), cm.getType(), menuIds);
                log.debug("功能访问次数统计断点一=>{}", clickMenuData);
                if (clickMenuData != null && clickMenuData.size() > 0) {
                    //获取指定要合并统计的菜单编号配置
                    List<OperationSystemConfig.MenuStatisticsMerge> msmList = operationSystemConfig.getClickMenuStatistics().get(regionId);
                    //根据配置合并统计
                    Map<String, ClickMenu> mergeMap = new HashMap<>();
                    msmList.forEach(msm -> {
                        for (ClickMenuVo cmd : clickMenuData) {
                            if (Arrays.stream(msm.getMergeList().split(",")).anyMatch(o -> o.equals(cmd.getMenuId()))) {
                                if (mergeMap.containsKey(msm.getMergeName())) {
                                    ClickMenu cmTemp = mergeMap.get(msm.getMergeName());
                                    //累加点击量
                                    cmTemp.setClickNum(NumberUtils.addLong(cmTemp.getClickNum(), cmd.getClickNum()));
                                    //设置最后点击时间
                                    if (cmTemp.getLastClickTime().compareTo(cmd.getLastClickTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))) < 0) {
                                        cmTemp.setLastClickTime(cmd.getLastClickTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                                    }
                                } else {
                                    ClickMenu cmTemp = new ClickMenu();
                                    cmTemp.setRegionId(regionId);
                                    //设置统计日期
                                    cmTemp.setStatsDate(statsDate);
                                    //设置点击量
                                    cmTemp.setClickNum(cmd.getClickNum());
                                    //设置指定的合并名称
                                    cmTemp.setMenuName(msm.getMergeName());
                                    cmTemp.setLastClickTime(cmd.getLastClickTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                                    mergeMap.put(msm.getMergeName(), cmTemp);
                                }
                            }
                        }
                    });
                    log.debug("功能访问次数统计断点二=>{}", mergeMap);
                    //存入mogodb
                    mongoTemplate.insertAll(mergeMap.values());
                }
            } else {
                log.error("功能访问次数统计 未配置！ regionId={}  statsDate={}", regionId, statsDate);
            }
        } catch (Exception e) {
            log.error("功能访问次数统计 报错！ regionId ={}  statsDate={}", regionId, statsDate, e);
        }
        return "success";
    }

    /**
     * 分时段统计访问人数
     *
     * @param statsDate 统计日期  YYYY-MM-DD
     * @return
     */
    public String findVisitNumByInterval(Long regionId, String statsDate) {
        try {
            //先清除指定月份数据
            this.delStatisticsData(regionId, statsDate, VisitNumVo.class);
            //从新统计结果
            List<VisitNumVo> visitNumByIntervalData = dataSourceService.findVisitNumByInterval(regionId, statsDate, operationSystemConfig.getTimeInterval());
            if (visitNumByIntervalData != null && visitNumByIntervalData.size() > 0) {
                mongoTemplate.insertAll(visitNumByIntervalData);
            }
        } catch (Exception e) {
            log.error("分时段统计访问人数 报错！regionId={}  statsDate={}", regionId, statsDate, e);
        }
        return "success";
    }

    /**
     * 递归逐级查询组织访问率信息
     *
     * @return
     */
    private List<VisitRateVo> visitRateHelper(Long regionId, String statsDate, List<Long> orgIds, List<VisitRateVo> re) {
        for (Long orgId : orgIds) {
            List<OrganizationBase> orgList = organizationMapper.findChildByOrgIdExclude(regionId, orgId, 102803, orgTypeConfig.getCommunistGroup(), testBranch);
            List<Long> orgIdList = orgList.stream().map(OrganizationBase::getOrgId).collect(Collectors.toList());
            if (orgIdList.size() > 0) {
                List<VisitRateVo> tmp = dataSourceService.findVisitRate(regionId, statsDate, orgIdList);
                if (tmp != null && tmp.size() > 0) {
                    re.addAll(tmp);
                }
                //递归查询下一级
                visitRateHelper(regionId, statsDate, orgIdList, re);
            }
        }
        return re;
    }

    /**
     * 删除指定统计日期和指定集合，统计的数据
     */
    private void delStatisticsData(Long regionId, String statsDate, Class collectionObject) {
        Query query = new Query(Criteria.where("regionId").is(regionId).and("statsDate").is(statsDate));
        String name = collectionObject.getName();
        String collectionName = mongoCollectionNameConfig.getCollectionNames().get(name);
        boolean exists = mongoTemplate.exists(query, collectionObject);
        if (exists) {
            mongoTemplate.remove(query, collectionName);
        }
    }
}
