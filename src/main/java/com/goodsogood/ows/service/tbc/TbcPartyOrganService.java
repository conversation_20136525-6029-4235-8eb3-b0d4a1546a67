package com.goodsogood.ows.service.tbc;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.helper.PageBeanHelper;
import com.goodsogood.ows.mapper.user.OrgGroupMapper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.mapper.user.TbcUserReportMapper;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.vo.sas.OrgGroupEntity;
import com.goodsogood.ows.model.vo.tbc.*;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ruoyu
 * @date : 2020/12/28
 * 党组织情况
 */
@Service
@Log4j2
public class TbcPartyOrganService {

    private final TbcUserReportMapper tbcUserReportMapper;
    private final OrganizationMapper organizationMapper;
    private final OrgTypeConfig orgTypeConfig;
    private final StringRedisTemplate redisTemplate;
    private final OrgGroupMapper orgGroupMapper;
    private final TbcPageHelper tbcPageHelper;

    @Autowired
    public TbcPartyOrganService(TbcUserReportMapper tbcUserReportMapper,
                                OrganizationMapper organizationMapper,
                                OrgTypeConfig orgTypeConfig,
                                StringRedisTemplate redisTemplate,
                                OrgGroupMapper orgGroupMapper,
                                TbcPageHelper tbcPageHelper) {
        this.tbcUserReportMapper = tbcUserReportMapper;
        this.organizationMapper = organizationMapper;
        this.orgTypeConfig = orgTypeConfig;
        this.redisTemplate = redisTemplate;
        this.orgGroupMapper = orgGroupMapper;
        this.tbcPageHelper = tbcPageHelper;
    }

    /**
     * 党组织情况报表
     */
    public PartyOrganizationForm partyOrg(Long orgId) {
        String redisKey = Constants.TBC_PARTY_ORG_ + orgId;
        try {
            if (redisTemplate.hasKey(redisKey)) {
                return JsonUtils.fromJson(redisTemplate.boundValueOps(redisKey).get(), PartyOrganizationForm.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("党组织情况 反序列化错误:[{}]", e.getMessage());
        }

        PartyOrganizationForm form = new PartyOrganizationForm();
        //组织架构
        form.setStructure(getStructure(orgId));
        //换届信息
        form.setChangePeriod(getChangePeriod(orgId));
        //组织数量
        form.setPartyNums(getPartyNums(orgId));
        //支委会设置
        form.setChangePeriodTag(getChangePeriodTag(orgId));
        //党支部设置
        form.setPartyBranch(getPartyBranch(orgId));

        //缓存到redis中
        try {
            redisTemplate.boundValueOps(redisKey).set(JsonUtils.toJson(form), Constants.TBC_CACHE_TIME, TimeUnit.MINUTES);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("党组织情况 序列化错误:[{}]", e.getMessage());
        }

        return form;
    }

    /**
     * 组织数量详情 (党总支、党支部、党小组)
     *
     * @param orgId 组织id
     * @param type  1.党总支 2.党支部 3.党小组
     */
    public Page<TbcOrganNumDetailForm> partyOrgDetail(Long orgId, Integer type, Integer page, Integer size) {
        String cacheKey = Constants.TBC_PARTY_ORG_ + orgId + "_partyOrgDetail_"+type;
        PageBeanHelper.PageBean pageBean = PageBeanHelper.PageBean.builder()
                .pageNo(page)
                .pageSize(size)
                .build();

        switch (type) {
            case 1:
                //党总支
                String generalBranchChild = StringUtils.join(orgTypeConfig.getGeneralBranchChild(), ",");
                return tbcPageHelper.getPageInfo(() -> tbcUserReportMapper.getOrgChildInfoByType(orgId, generalBranchChild),
                        pageBean, cacheKey, TbcOrganNumDetailForm.class);
//                return PageHelper.startPage(page, size)
//                        .doSelectPage(
//                                () -> tbcUserReportMapper.getOrgChildInfoByType(orgId, generalBranchChild));
            case 2:
                //党支部
                String branchChild = StringUtils.join(orgTypeConfig.getBranchChild(), ",");
                return tbcPageHelper.getPageInfo(() -> tbcUserReportMapper.getOrgChildInfoByType(orgId, branchChild),
                        pageBean, cacheKey, TbcOrganNumDetailForm.class);
//                return PageHelper.startPage(page, size)
//                        .doSelectPage(
//                                () -> tbcUserReportMapper.getOrgChildInfoByType(orgId, branchChild));
            case 3:
                //党小组
                return tbcPageHelper.getPageInfo(() -> tbcUserReportMapper.getOrgChildInfoByTypeGroup(orgId),
                        pageBean, cacheKey, TbcOrganNumDetailForm.class);
//                return PageHelper.startPage(page, size)
//                        .doSelectPage(
//                                () -> tbcUserReportMapper.getOrgChildInfoByTypeGroup(orgId));
            default:
                throw new IllegalArgumentException();
        }
    }

    /**
     * 党支部设置(详情)
     *
     * @param orgId 查询的组织id
     */
    public Page<TbcOrganBranchDetailForm> partyBranchDetail(Long orgId, Integer page, Integer size) {
        String cacheKey = Constants.TBC_PARTY_ORG_ + orgId + "_partyBranchDetail";
        PageBeanHelper.PageBean pageBean = PageBeanHelper.PageBean.builder()
                .pageNo(page)
                .pageSize(size)
                .build();
        return tbcPageHelper.getPageInfo(() -> tbcUserReportMapper.partyBranchDetail(orgId),
                pageBean, cacheKey, TbcOrganBranchDetailForm.class);
//        Page<TbcOrganBranchDetailForm> pages =
//                PageHelper.startPage(page, size)
//                        .doSelectPage(
//                                () -> tbcUserReportMapper.partyBranchDetail(orgId));
//        return pages;
    }


    /**
     * 党支部设置情况
     */
    private List<PartyOrganizationForm.TbcPartyOrganizationTbcBranchForm> getPartyBranch(Long orgId) {
        String join = StringUtils.join(orgTypeConfig.getBranchChild(), ",");
        List<PartyOrganizationForm.TbcPartyOrganizationTbcBranchForm> branchForm = tbcUserReportMapper.getPartyBranch(orgId, join);
        return branchForm;
    }

    /**
     * 支委会设置情况
     */
    private PartyOrganizationForm.TbcPartyOrganizationChangePeriodTagForm getChangePeriodTag(Long orgId) {
        String join = StringUtils.join(orgTypeConfig.getBranchChild(), ",");
        //应设置支委数量
        Integer shouldPeriodTagNum = tbcUserReportMapper.getshouldPeriodTagNum(orgId, join);
        //已设置支委数量
        Integer periodTagNum = tbcUserReportMapper.getPeriodTagNum(orgId, join);
        //未设置支委数量
        int notTagNun = 0;
        List<PartyOrganizationForm.TbcTbcOrganInnerPeriodForm> notTagNum = tbcUserReportMapper.getNotTagNum(orgId, join);
        if (!CollectionUtils.isEmpty(notTagNum)) {
            notTagNun = notTagNum.size();
        }

        PartyOrganizationForm.TbcPartyOrganizationChangePeriodTagForm build = PartyOrganizationForm.TbcPartyOrganizationChangePeriodTagForm.builder()
                .shouldTagNum(shouldPeriodTagNum)
                .setTagNum(periodTagNum)
                .notTagNum(notTagNun)
                .notTagData(notTagNum)
                .build();

        return build;
    }


    /**
     * 组织数量
     *
     * @param orgId
     * @return
     */
    private PartyOrganizationForm.TbcPartyOrganizationPartyNumsForm getPartyNums(Long orgId) {
        //党总支
        String generalBranchChild = StringUtils.join(orgTypeConfig.getGeneralBranchChild(), ",");
        //党支部
        String branchChild = StringUtils.join(orgTypeConfig.getBranchChild(), ",");
        PartyOrganizationForm.TbcPartyOrganizationPartyNumsForm numsForm = tbcUserReportMapper.getPartyNums(orgId, generalBranchChild, branchChild);
        return numsForm;
    }

    /**
     * 换届信息
     *
     * @param orgId
     * @return
     */
    private PartyOrganizationForm.TbcPartyOrganizationPeriodForm getChangePeriod(Long orgId) {
        PartyOrganizationForm.TbcPartyOrganizationPeriodForm periodForm =
                PartyOrganizationForm.TbcPartyOrganizationPeriodForm
                        .builder()
                        .alreadyExpireNum(0)
                        .nowExpireNum(0)
                        .sixExpireNum(0)
                        .alreadyExpireData(new ArrayList<>())
                        .nowExpireData(new ArrayList<>())
                        .sixExpireData(new ArrayList<>())
                        .validData(new ArrayList<>())
                        .build();
        String nowDay = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        String typeChild = StringUtils.join(orgTypeConfig.getBranchChild(), ",");
        List<PartyOrganizationForm.TbcTbcOrganInnerPeriodForm> expireOrganPeriod = tbcUserReportMapper.getExpireOrganPeriod(orgId, nowDay, typeChild);
        List<Long> excludeOrgId = new ArrayList<>();
        if (!CollectionUtils.isEmpty(expireOrganPeriod)) {
            expireOrganPeriod.forEach(x -> {
                //1:本月到期  2:6个月内到期  3:已到期未换届
                switch (x.getType()) {
                    case 1:
                        periodForm.setNowExpireNum(periodForm.getNowExpireNum() + 1);
                        periodForm.getNowExpireData().add(x);
                        excludeOrgId.add(x.getOrgId());
                        break;
                    case 2:
                        periodForm.setSixExpireNum(periodForm.getSixExpireNum() + 1);
                        periodForm.getSixExpireData().add(x);
                        excludeOrgId.add(x.getOrgId());
                        break;
                    case 3:
                        periodForm.setAlreadyExpireNum(periodForm.getAlreadyExpireNum() + 1);
                        periodForm.getAlreadyExpireData().add(x);
                        break;
                    case 4:
                        periodForm.getValidData().add(x);
                        break;
                    default:
                        throw new IllegalArgumentException();
                }
            });
        }
        //已设置
        periodForm.setValidData(periodForm.getValidData()
                .stream()
                .filter(x -> !excludeOrgId.contains(x.getOrgId())).collect(Collectors.toList()));

        return periodForm;
    }

    /**
     * 组织架构
     *
     * @param orgId 组织id
     */
    private List<PartyOrganizationForm.TbcTbcOrganStructureForm> getStructure(Long orgId) {
        List<PartyOrganizationForm.TbcTbcOrganStructureForm> list = new ArrayList<>(1);
        //确认查询组织是否有效
        Example orgExample = new Example(OrganizationEntity.class);
        orgExample.createCriteria()
                .andEqualTo("organizationId", orgId)
                .andEqualTo("status", Constants.STATUS_YES);
        OrganizationEntity organizationEntity = organizationMapper.selectOneByExample(orgExample);
        if (null == organizationEntity) {
            return list;
        }

        //获取指定组织及其下级党小组信息 并封装
        Map<Long, List<String>> orgGroupMap = new HashMap<>();
        List<OrgGroupEntity> groupList = orgGroupMapper.selectGroupByOrg(orgId);
        if (!CollectionUtils.isEmpty(groupList)) {
            groupList.forEach(x -> {
                if (orgGroupMap.containsKey(x.getOrgId())) {
                    orgGroupMap.get(x.getOrgId()).add(x.getOrgGroupName());
                } else {
                    List<String> tempList = new ArrayList<>();
                    tempList.add(x.getOrgGroupName());
                    orgGroupMap.put(x.getOrgId(), tempList);
                }
            });
        }

        PartyOrganizationForm.TbcTbcOrganStructureForm build = new PartyOrganizationForm.TbcTbcOrganStructureForm();
        build.setOrgId(organizationEntity.getOrganizationId());
        build.setOrgName(organizationEntity.getName());
        build.setHasChild(Constants.HAS_CHILD_NO);
        build.setHasGroup(orgGroupMap.containsKey(organizationEntity.getOrganizationId()) ? 1 : 2);
        build.setGroup(orgGroupMap.get(organizationEntity.getOrganizationId()));

        list.add(build);
        //查询下级
        List<OrganizationEntity> organizationListBySeq = tbcUserReportMapper.getOrganizationListBySeq(orgId);
        if (CollectionUtils.isEmpty(organizationListBySeq)) {
            return list;
        }
        list.get(0).setHasChild(Constants.HAS_CHILD_YES);
        list.get(0).setStructure(new ArrayList<>());
        //递归排序
        recursionOrg(organizationListBySeq, list.get(0).getStructure(), orgId, orgGroupMap);
        return list;
    }


    /**
     * 递归循环根据组织层级进行排序
     *
     * @param list      原数据
     * @param newList   排序后数据
     * @param orgId     递归节点 上级组织id
     * @param orgGroups 党小组
     */
    private Boolean recursionOrg(List<OrganizationEntity> list,
                                 List<PartyOrganizationForm.TbcTbcOrganStructureForm> newList,
                                 Long orgId,
                                 Map<Long, List<String>> orgGroups) {
        boolean hasChild = false;
        for (int i = 0; i < list.size(); i++) {
            OrganizationEntity orgInfo = list.get(i);
            Integer orgTypeChild = orgInfo.getOrgTypeChild();
            boolean b = /* 满足直接上级为我传入的上级组织id */(orgInfo.getParentId().equals(orgId)
                    && !orgTypeChild.equals(10280304)
//                    /* 以下组织类型满足可能包含下级的情况 */ ((orgTypeChild.equals(10280301)) || (orgTypeChild.equals(10280303)) || orgTypeChild.equals(10280308) || orgTypeChild.equals(10280313))
            );

            if (b) {
                hasChild = true;
                PartyOrganizationForm.TbcTbcOrganStructureForm structureForm = addCreateStructureInfo(newList, orgInfo, orgGroups);
                Boolean aBoolean = recursionOrg(list, structureForm.getStructure(), orgInfo.getOrganizationId(), orgGroups);
                //如果有下级组织
                if (aBoolean) {
                    structureForm.setHasChild(Constants.HAS_CHILD_YES);
                }
            } else if (orgInfo.getParentId().equals(orgId)) {
                hasChild = true;
                addCreateStructureInfo(newList, orgInfo, orgGroups);
            }
        }
        return hasChild;
    }

    private PartyOrganizationForm.TbcTbcOrganStructureForm addCreateStructureInfo(List<PartyOrganizationForm.TbcTbcOrganStructureForm> newList,
                                                                                  OrganizationEntity orgId,
                                                                                  Map<Long, List<String>> orgGroups) {
        PartyOrganizationForm.TbcTbcOrganStructureForm build =
                new PartyOrganizationForm.TbcTbcOrganStructureForm();
        build.setOrgId(orgId.getOrganizationId());
        build.setOrgName(orgId.getName());
        build.setStructure(new ArrayList<>());
        build.setHasChild(Constants.HAS_CHILD_NO);
        build.setHasGroup(orgGroups.containsKey(orgId.getOrganizationId()) ? 1 : 2);
        build.setGroup(orgGroups.get(orgId.getOrganizationId()));
        newList.add(build);
        return build;
    }


}
