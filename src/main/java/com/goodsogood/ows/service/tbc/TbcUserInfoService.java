package com.goodsogood.ows.service.tbc;

import com.goodsogood.ows.mapper.sas.TbcUserInfoMapper;
import com.goodsogood.ows.model.vo.tbc.TbcPbmVo;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-12-27 17:19
 **/
@Service
@Log4j2
public class TbcUserInfoService {

    private final TbcUserInfoMapper tbcUserInfoMapper;

    @Autowired
    public TbcUserInfoService(TbcUserInfoMapper tbcUserInfoMapper) {
        this.tbcUserInfoMapper = tbcUserInfoMapper;
    }


    /**
     * 党业融合统计用户先峰指数信息
     * @return
     */
    TbcPbmVo getTbcUserPbmInfo(){
        return tbcUserInfoMapper.getTbcUserPbmInfo();
    }


    /**
     * 党业融合统计用户3指标信息
     * @return
     */
    Map<String,TbcPbmVo> getTbcUserThreeIndex(){
        Map<String, TbcPbmVo> map = new HashMap<>();
        TbcPbmVo partyIndex = tbcUserInfoMapper.getTbcUserPartyIndexPbmInfo();
        TbcPbmVo businessIndex = tbcUserInfoMapper.getTbcUserBusinessIndexPbmInfo();
        TbcPbmVo innovationIndex = tbcUserInfoMapper.getTbcUserInnovationIndexPbmInfo();
        map.put("partyIndex", partyIndex);
        map.put("businessIndex", businessIndex);
        map.put("innovationIndex", innovationIndex);
        return map;
    }


}