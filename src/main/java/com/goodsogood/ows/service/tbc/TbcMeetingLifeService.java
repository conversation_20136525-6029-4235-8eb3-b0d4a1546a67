package com.goodsogood.ows.service.tbc;

import com.github.pagehelper.Page;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.configuration.TbcMonitorsTagConfig;
import com.goodsogood.ows.helper.PageBeanHelper;
import com.goodsogood.ows.mapper.meeting.TbcMeetingMapper;
import com.goodsogood.ows.mapper.user.OptionMapper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.mapper.user.TbcUserReportMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.user.OptionEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.vo.tbc.*;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.SqlJointUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ruoyu
 * @date : 2020/12/28
 * 组织生活情况
 */
@Service
@Log4j2
public class TbcMeetingLifeService {


    private final TbcMeetingMapper tbcMeetingMapper;
    private final TbcUserReportMapper tbcUserReportMapper;
    private final StringRedisTemplate redisTemplate;
    private final TbcPageHelper tbcPageHelper;
    private final UserMapper userMapper;
    private final OptionMapper optionMapper;
    private final OrganizationMapper organizationMapper;
    private final TbcMonitorsTagConfig tbcMonitorsTagConfig;

    @Autowired
    public TbcMeetingLifeService(TbcMeetingMapper tbcMeetingMapper,
                                 TbcUserReportMapper tbcUserReportMapper,
                                 StringRedisTemplate redisTemplate,
                                 TbcPageHelper tbcPageHelper,
                                 UserMapper userMapper, OptionMapper optionMapper, OrganizationMapper organizationMapper, TbcMonitorsTagConfig tbcMonitorsTagConfig) {
        this.tbcMeetingMapper = tbcMeetingMapper;
        this.tbcUserReportMapper = tbcUserReportMapper;
        this.redisTemplate = redisTemplate;
        this.tbcPageHelper = tbcPageHelper;
        this.userMapper = userMapper;
        this.optionMapper = optionMapper;
        this.organizationMapper = organizationMapper;
        this.tbcMonitorsTagConfig = tbcMonitorsTagConfig;
    }

    /**
     * 烟草组织生活大屏 主方法
     *
     * @param orgId
     * @return
     */
    public OrganizationLifeForm meetingLife(Long orgId, Long regionId) {
        String redisKey = Constants.TBC_MEETING_LIFE_ + orgId;
        try {
            if (redisTemplate.hasKey(redisKey)) {
                return JsonUtils.fromJson(redisTemplate.boundValueOps(redisKey).get(), OrganizationLifeForm.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("烟草组织生活大屏 主方法 反序列化失败:[{}]", e.getMessage());
        }


        OrganizationLifeForm organizationLifeForm = new OrganizationLifeForm();
        //支部建设动态 由于暂时在nginx静态目录中获取 所以这里为空
        organizationLifeForm.setPartyInfo(null);
        //民主评议党员情况
        organizationLifeForm.setUserComment(getUserComment(orgId));
        //述职评议组织情况
        organizationLifeForm.setOrgComment(getOrgComment(orgId));
        //组织生活开展情况
        organizationLifeForm.setMeetingActivity(getMeetingActivity(orgId, regionId));
        //三会一课
        organizationLifeForm.setCenterMeetingActivity(getCenterMeetingActivity(orgId, regionId));
        //主题党日开展情况
        organizationLifeForm.setCenterPartyActivity(getCenterPartyActivity(orgId, regionId));
        //领导干部双重组织生活
        organizationLifeForm.setLeaderMeetingActivity(getTbcOrganizationLifeLeaderMeetingActivity(regionId, orgId));

        try {
            //缓存到redis中
            redisTemplate.boundValueOps(redisKey).set(JsonUtils.toJson(organizationLifeForm), Constants.TBC_CACHE_TIME, TimeUnit.MINUTES);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("烟草组织生活大屏 主方法 序列化失败:[{}]", e.getMessage());
        }

        return organizationLifeForm;
    }

    /**
     * 民主评议党员情况详情
     * 默认只查询优秀的
     *
     * @param orgId 组织Id
     */
    public Page<OrganLifeUserCommentForm> meetingLifeUserComment(Long orgId, Integer page, Integer size) {
        //获取上一年度
        int lastYear = LocalDate.now().minusYears(1).getYear();

        String cacheKey = Constants.TBC_MEETING_LIFE_ + orgId + "_meetingLifeUserComment";
        PageBeanHelper.PageBean pageBean = PageBeanHelper.PageBean.builder()
                .pageNo(page)
                .pageSize(size)
                .build();
        return tbcPageHelper.getPageInfo(() -> tbcMeetingMapper.meetingLifeUserComment(orgId, lastYear),
                pageBean, cacheKey, OrganLifeUserCommentForm.class);

//        return PageHelper.startPage(page, size)
//                .doSelectPage(
//                        () -> tbcMeetingMapper.meetingLifeUserComment(orgId, lastYear));
    }

    /**
     * 民主评议组织情况详情
     * 默认只查询优秀的
     *
     * @param orgId 组织id
     */
    public Page<OrganLifeUserCommentForm> meetingLifeOrgComment(Long orgId, Integer page, Integer size) {
        //获取上一年度
        Integer lastYear = LocalDate.now().minusYears(1).getYear();
        String cacheKey = Constants.TBC_MEETING_LIFE_ + orgId + "_meetingLifeOrgComment";
        PageBeanHelper.PageBean pageBean = PageBeanHelper.PageBean.builder()
                .pageNo(page)
                .pageSize(size)
                .build();
        return tbcPageHelper.getPageInfo(() -> tbcMeetingMapper.meetingLifeOrgComment(orgId, lastYear),
                pageBean, cacheKey, OrganLifeUserCommentForm.class);
//        return PageHelper.startPage(page, size)
//                .doSelectPage(
//                        () -> tbcMeetingMapper.meetingLifeOrgComment(orgId, lastYear));
    }

    /**
     * 主题党日开展情况
     */
    private List<OrganizationLifeForm.OrganizationLifeCenterPartyActivityForm> getCenterPartyActivity(Long orgId, Long regionId) {
        List<OrganizationLifeForm.OrganizationLifeCenterPartyActivityForm> activityFormList = tbcMeetingMapper.getCenterPartyActivity(3L, orgId, regionId);
        return activityFormList;
    }

    /**
     * 三会一课
     */
    private List<OrganizationLifeForm.OrganizationLifeCenterMeetingActivityForm> getCenterMeetingActivity(Long orgId, Long regionId) {
        List<OrganizationLifeForm.OrganizationLifeCenterMeetingActivityForm> formList = tbcMeetingMapper.getCenterMeetingActivityIsDone(3L, orgId, regionId);
        if (CollectionUtils.isEmpty(formList)) {
            return null;
        }

        String orgIds = StringUtils.join(formList.stream().map(x -> x.getOrgId()).collect(Collectors.toList()), ",");
        List<OrganizationLifeForm.MeetingDoneInfo> meetingDoneInfos = tbcMeetingMapper.getCenterMeetingActivityIsDoneDetail(3L, orgIds, regionId);
        if (CollectionUtils.isEmpty(meetingDoneInfos)) {
            return formList;
        }

        formList.forEach(x -> {
            x.setInfo(new ArrayList<>());
            for (OrganizationLifeForm.MeetingDoneInfo y : meetingDoneInfos) {
                if (x.getOrgId().equals(y.getOrganizationId())) {
                    x.getInfo().add(y);
                } else if (x.getInfo().size() > 0) {
                    break;
                }
            }
        });

        return formList;
    }

    /**
     * 组织生活开展情况
     */
    private OrganizationLifeForm.OrganizationLifeMeetingActivityForm getMeetingActivity(Long orgId, Long regionId) {
        OrganizationLifeForm.OrganizationLifeMeetingActivityForm activityForm = new OrganizationLifeForm.OrganizationLifeMeetingActivityForm();
        //本年度共开展支部活动次数
        activityForm.setTotal(tbcMeetingMapper.getMeetingActivityTotal(3L, orgId, regionId));
        //会议开展类型数据
        activityForm.setList(tbcMeetingMapper.getMeetingActivityDetail(3L, orgId, regionId));
        if (!CollectionUtils.isEmpty(activityForm.getList())) {
            for (OrganizationLifeForm.OrganizationLifeInnerMeetingActivityForm meetingActivityForm : activityForm.getList()) {
                //应前端要求 修改类型名称
                if (meetingActivityForm.getTypeName().equals("党支部党员大会")) {
                    meetingActivityForm.setTypeName("党员大会");
                } else if (meetingActivityForm.getTypeName().equals("党支部委员会会议")) {
                    meetingActivityForm.setTypeName("支委会");
                }

                if (meetingActivityForm.getUndoneNum() != 0) {
                    switch (meetingActivityForm.getTypeId()) {
                        case 60:
                        case 63:
                            meetingActivityForm.setUndoneInfo(tbcMeetingMapper.getQuarterMeetingUnDoneInfo(3L, meetingActivityForm.getTypeId(), orgId, regionId));
                            break;
                        case 61:
                        case 62:
                        case 64:
                            meetingActivityForm.setUndoneInfo(tbcMeetingMapper.getMonthMeetingUnDoneInfo(3L, meetingActivityForm.getTypeId(), orgId, regionId));
                            break;
                        default:
                            throw new IllegalArgumentException();
                    }
                }
            }
        }
        return activityForm;
    }

    /**
     * 述职评议组织情况
     */
    private List<OrganizationLifeForm.OrganizationLifeCommentForm> getOrgComment(Long orgId) {
        int lastYear = LocalDate.now().minusYears(1).getYear();
        List<OrganizationLifeForm.OrganizationLifeCommentForm> commentForm = tbcMeetingMapper.getOrgComment(orgId, lastYear);
        return commentForm;
    }

    /**
     * 民主评议党员情况数据
     */
    private List<OrganizationLifeForm.OrganizationLifeCommentForm> getUserComment(Long orgId) {
        int lastYear = LocalDate.now().minusYears(1).getYear();
        List<OrganizationLifeForm.OrganizationLifeCommentForm> commentForm = tbcMeetingMapper.getOrgUserComment(orgId, lastYear);
        return commentForm;
    }

    /**
     * 领导成员双重组织生活
     *
     * @param regionId 区县id
     * @param orgId    组织id
     * @return
     */
    private List<OrganizationLifeForm.OrganizationLifeLeaderMeetingActivityForm> getTbcOrganizationLifeLeaderMeetingActivity(
            Long regionId, Long orgId) {
        // 获取领导成员
        List<OrganizationLifeForm.OrganizationLifeLeaderMeetingActivityForm> leader = tbcUserReportMapper.getLeader(orgId);
        if (CollectionUtils.isEmpty(leader)) {
            return Collections.EMPTY_LIST;
        }

        Map<Long, OrganizationLifeForm.OrganizationLifeLeaderMeetingActivityForm> leaderMap = new HashMap<>(leader.size());
        leader.forEach(t -> {
            leaderMap.put(t.getUserId(), t);
        });

        String userIds = SqlJointUtil.collectionToStr(leader, t -> t.getUserId().toString());

        // 查询会议的签到状态（如果是联系领导，直接认为已参加该会议）
        List<OrganizationLifeForm.OrganizationLifeLeaderMeetingActivityForm> joinNumList =
                tbcMeetingMapper.meetingJoinNum(3L, regionId, userIds);
        Map<Long, OrganizationLifeForm.OrganizationLifeLeaderMeetingActivityForm> joinNumMap = null;

        if (!CollectionUtils.isEmpty(joinNumList)) {
            joinNumMap = new HashMap<>(joinNumList.size());
            for (OrganizationLifeForm.OrganizationLifeLeaderMeetingActivityForm t : joinNumList) {
                joinNumMap.put(t.getUserId(), t);
            }
        }

        Map<Long, OrganizationLifeForm.OrganizationLifeLeaderMeetingActivityForm> finalJoinNumMap = joinNumMap;
        leaderMap.forEach((k, v) -> {
            if (!CollectionUtils.isEmpty(finalJoinNumMap)) {
                if (finalJoinNumMap.containsKey(k)) {
                    v.setNum(finalJoinNumMap.get(k).getNum());
                } else {
                    v.setNum(0);
                }
            } else {
                v.setNum(0);
            }
        });
        return leader;
    }

    public List<TbcCommendPenalizeVo> getCommendPenalizeList() {
        List<TbcCommendPenalizeVo> list = new ArrayList<>();
        Map<Integer, List<Integer>> penalizeLevel = this.tbcMonitorsTagConfig.getCommendPenalizeLevel();
        List<Integer> level = new ArrayList<>();
        for (List<Integer> type : penalizeLevel.values()) {
            level.addAll(type);
        }
        List<TbcCommendPenalizeDbForm> dbFormList = tbcMeetingMapper.meetingCommendPenalize(level);
        Map<Integer, List<TbcCommendPenalizeDbForm>> map = dbFormList.stream()
                .collect(Collectors.groupingBy(TbcCommendPenalizeDbForm::getType));
        Map<Integer, List<OptionEntity>> optionMap = optionMapper.likeCode("1048").stream()
                .collect(Collectors.groupingBy(OptionEntity::getOpKey));
        List<TbcCommendPenalizeOneVo> voList = new ArrayList<>();
        if (map.containsKey(1)) {
            List<TbcCommendPenalizeDbForm> userFormList = map.get(1);
            List<Long> userIds = userFormList.stream().map(TbcCommendPenalizeDbForm::getId).collect(Collectors.toList());
            List<UserCorpInfo> userCorpInfos = userMapper.getUserCorp(userIds);
            Map<Long, List<UserCorpInfo>> userMap = userCorpInfos.stream().collect(Collectors.groupingBy(UserCorpInfo::getUserId));
            for (TbcCommendPenalizeDbForm form : userFormList) {
                TbcCommendPenalizeOneVo vo = new TbcCommendPenalizeOneVo();
                Integer type = getType(penalizeLevel, form.getLevel());
                vo.setType(type);
                if (userMap.containsKey(form.getId()) && optionMap.containsKey(form.getName())) {
                    UserCorpInfo user = userMap.get(form.getId()).get(0);
                    OptionEntity option = optionMap.get(form.getName()).get(0);
                    vo.setItem(user.getCorpName() + user.getUsername() + "，获得“" + option.getOpValue() +"”");
                    voList.add(vo);
                }
            }
        }
        if(map.containsKey(2)) {
            List<TbcCommendPenalizeDbForm> orgFormList = map.get(2);
            List<Long> orgIds = orgFormList.stream().map(TbcCommendPenalizeDbForm::getId).collect(Collectors.toList());
            Example example = new Example(OrganizationEntity.class);
            example.createCriteria().andIn("organizationId", orgIds);
            List<OrganizationEntity> orgList = this.organizationMapper.selectByExample(example);
            Map<Long, List<OrganizationEntity>> orgMap = orgList.stream().collect(Collectors.groupingBy(OrganizationEntity::getOrganizationId));
            for (TbcCommendPenalizeDbForm form : orgFormList) {
                TbcCommendPenalizeOneVo vo = new TbcCommendPenalizeOneVo();
                Integer type = getType(penalizeLevel, form.getLevel());
                vo.setType(type);
                if (orgMap.containsKey(form.getId()) && optionMap.containsKey(form.getName())) {
                    OrganizationEntity org = orgMap.get(form.getId()).get(0);
                    OptionEntity option = optionMap.get(form.getName()).get(0);
                    vo.setItem(org.getName() + "，获得“" + option.getOpValue() +"”");
                    voList.add(vo);
                }
            }
        }

        Map<Integer, List<TbcCommendPenalizeOneVo>> voMap = voList.stream().collect(Collectors.groupingBy(TbcCommendPenalizeOneVo::getType));
        for (Map.Entry<Integer, List<TbcCommendPenalizeOneVo>> entry : voMap.entrySet()) {
            TbcCommendPenalizeVo resultVo = new TbcCommendPenalizeVo();
            resultVo.setType(entry.getKey());
            List<TbcCommendPenalizeOneVo> entryValue = entry.getValue();
            resultVo.setNum(entryValue.size());
            List<String> strList = entryValue.stream().map(TbcCommendPenalizeOneVo::getItem).collect(Collectors.toList());
            resultVo.setList(strList);
            list.add(resultVo);
        }

        return list;
    }

    private Integer getType(Map<Integer, List<Integer>> penalizeLevel, Integer level) {
        Optional<Map.Entry<Integer, List<Integer>>> entryOptional =
                penalizeLevel.entrySet().stream().filter(o -> o.getValue().contains(level)).findFirst();
        return entryOptional.map(Map.Entry::getKey).orElse(null);
    }

}
