package com.goodsogood.ows.service.tbc;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.ExcludeOrgConfig;
import com.goodsogood.ows.configuration.LearnScoreConfiguration;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.learn.TheoryArmsMapper;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.mapper.meeting.experience.MeetingEvalMapper;
import com.goodsogood.ows.mapper.task.TaskLogMapper;
import com.goodsogood.ows.mapper.task.TaskMapper;
import com.goodsogood.ows.mapper.user.MenuRouteMapper;
import com.goodsogood.ows.mapper.user.TbcPartyMapper;
import com.goodsogood.ows.mapper.user.UserLoginLogMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.user.MenuRouteEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.mongodb.dss.MaxDayVisit;
import com.goodsogood.ows.model.mongodb.dss.MaxMonthVisit;
import com.goodsogood.ows.model.mongodb.dss.OrgUserActive;
import com.goodsogood.ows.model.mongodb.dss.PieObject;
import com.goodsogood.ows.model.mongodb.operation.ClickMenu;
import com.goodsogood.ows.model.vo.GroupValue;
import com.goodsogood.ows.model.vo.operation.VisitDateResult;
import com.goodsogood.ows.model.vo.operation.VisitNumVo;
import com.goodsogood.ows.model.vo.tbc.*;
import com.goodsogood.ows.model.vo.user.*;
import com.goodsogood.ows.service.cilckSpy.ClickSpyService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.NumberUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Log4j2
public class TbcPartyUsersService {

    @Value("${tbc-tag.fullTimeId}")
    private String fullTimeId;

    @Value("${tbc-tag.partyTimeId}")
    private String partyTimeId;

    @Value("${overview.exclude-org-ids}")
    private String excludeOrgIds;
    /**
     * 30岁以下
     */
    private static final String AGE_ONE = "u.age<30";
    /**
     * 30-45 岁
     */
    private static final String AGE_TWO = "u.age>=30 and u.age<45";
    /**
     * 45-55 岁
     */
    private static final String AGE_THREE = "u.age>=45 and u.age<55";
    /**
     * 55岁以上
     */
    private static final String AGE_FOUR = "u.age>=55";
    /**
     * 研究生
     */
    private static final String BACKDROP_GRADUATE = "u.education in(1013,1014,1015)";
    /**
     * 大学 本科
     */
    private static final String BACKDROP_UNDERGRADUATE = "u.education in(1010,1011,1012,1042)";
    /**
     * 大专
     */
    private static final String BACKDROP_JUNIOR_COLLEGE = "u.education in(1008,1009,1039)";
    /**
     * 中转
     */
    private static final String BACKDROP_SECONDARY = "u.education in(1005)";
    /**
     * 初中
     */
    private static final String BACKDROP_SENIOR_HIGH_SCHOOL = "u.education IN ( 1001, 1002, 1003, 1041, 1004, 1006, 1007, 1026, 1044, 1045 )";

    static final SimpleDateFormat MAX_DAY_FORMAT = new SimpleDateFormat("MM月dd日");
    private final TbcPartyMapper tbcPartyMapper;
    private final StringRedisTemplate redisTemplate;
    private final UserMapper userMapper;
    private final TaskMapper taskMapper;
    private final TaskLogMapper taskLogMapper;
    private final TheoryArmsMapper theoryArmsMapper;
    private final LearnScoreConfiguration learnScoreConfiguration;
    private final MeetingMapper meetingMapper;

    private final SimpleApplicationConfigHelper applicationConfigHelper;
    private final UserLoginLogMapper userLoginLogMapper;
    private final ExcludeOrgConfig excludeOrgConfig;
    private final OrgTypeConfig orgTypeConfig;
    private final ObjectMapper objectMapper;
    private final ClickSpyService clickSpyService;
    private final MenuRouteMapper menuRouteMapper;
    private final MyMongoTemplate mongoTemplate;
    private final MeetingEvalMapper meetingEvalMapper;

    @Autowired
    public TbcPartyUsersService(TbcPartyMapper tbcPartyMapper,
                                StringRedisTemplate redisTemplate,
                                UserMapper userMapper,
                                TaskMapper taskMapper,
                                TaskLogMapper taskLogMapper,
                                TheoryArmsMapper theoryArmsMapper,
                                LearnScoreConfiguration learnScoreConfiguration,
                                MeetingMapper meetingMapper, SimpleApplicationConfigHelper applicationConfigHelper, UserLoginLogMapper userLoginLogMapper, ExcludeOrgConfig excludeOrgConfig, OrgTypeConfig orgTypeConfig, ObjectMapper objectMapper, ClickSpyService clickSpyService, MenuRouteMapper menuRouteMapper, MyMongoTemplate mongoTemplate, MeetingEvalMapper meetingEvalMapper) {
        this.tbcPartyMapper = tbcPartyMapper;
        this.redisTemplate = redisTemplate;
        this.userMapper = userMapper;
        this.taskMapper = taskMapper;
        this.taskLogMapper = taskLogMapper;
        this.theoryArmsMapper = theoryArmsMapper;
        this.learnScoreConfiguration = learnScoreConfiguration;
        this.meetingMapper = meetingMapper;
        this.applicationConfigHelper = applicationConfigHelper;
        this.userLoginLogMapper = userLoginLogMapper;
        this.excludeOrgConfig = excludeOrgConfig;
        this.orgTypeConfig = orgTypeConfig;
        this.objectMapper = objectMapper;
        this.clickSpyService = clickSpyService;
        this.menuRouteMapper = menuRouteMapper;
        this.mongoTemplate = mongoTemplate;
        this.meetingEvalMapper = meetingEvalMapper;
    }


    /**
     * 党员详情信息
     *
     * @return
     */
    public PartyCaucusForm partyUser(Long orgId) {
        String redisKey = Constants.TBC_PARTY_USER_ + orgId;
        try {
            if (Boolean.TRUE.equals(redisTemplate.hasKey(redisKey))) {
                return JsonUtils.fromJson(redisTemplate.boundValueOps(redisKey).get(), PartyCaucusForm.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("党员详情信息 redis数据反序列化失败[{}]", e.getMessage());
        }

        PartyCaucusForm from = new PartyCaucusForm();
        //获取当前年份
        SimpleDateFormat d = new SimpleDateFormat("yyyy");
        Date now = new Date();
        String staYear = d.format(now);
        //党员人数
        from.setCaucus(getPartyNumber(orgId, staYear));
        //民族分布
        from.setNation(getPartyNation(orgId));
        //党建视频
        from.setVideo(getPartyView(orgId));
        //领导成员
        from.setLeader(getPartyLead(orgId));
        //性别比例
        from.setGender(getPartyGender(orgId, staYear));
        //年龄分布
        from.setAge(getPartyAge(orgId));
        //学历情况
        from.setBackdrop(getPartyBack(orgId));

        try {
            //缓存到redis中
            redisTemplate.boundValueOps(redisKey).set(JsonUtils.toJson(from), Constants.TBC_CACHE_TIME, TimeUnit.MINUTES);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("党员详情信息 序列化存储到redis失败[{}]", e.getMessage());
        }

        return from;
    }

    /**
     * 党员人数详情
     */
    private PartyCaucusForm.TbcPrepareNumberForm getPartyNumber(Long orgId, String staYear) {
//        List<PartyCaucusForm.TbcPrepareNumberForm> list = new ArrayList<>();
        PartyCaucusForm.TbcPrepareNumberForm form = tbcPartyMapper.getCaucus(orgId, staYear);
        if (null == form) {
            return null;
        }
        form.setStructure(tbcPartyMapper.getPrepareMember(orgId));
        return form;
    }

    /**
     * 民族分布详情TbcEthnicDistributionOfPartyMembersFrom
     */
    private List<PartyCaucusForm.TbcEthnicDistributionOfPartyMembers> getPartyNation(Long orgId) {
        return tbcPartyMapper.getMemberNation(orgId);
    }

    /**
     * 图片视频详情
     * 当前文件传输通过前端写死 这里后台不传输内容
     */
    private PartyCaucusForm.TbcVideoAndImgViewForm getPartyView(Long ignoredOrgId) {
        return null;
    }

    /**
     * 领导成员详情
     */
    private List<PartyCaucusForm.TbcDetailsOfLeadingMembersForm> getPartyLead(Long orgId) {
        return tbcPartyMapper.getPartyLead(orgId);
    }

    /**
     * 性别比例详情
     */
    private PartyCaucusForm.TbcGenderNumberForm getPartyGender(Long orgId, String staYear) {
        return tbcPartyMapper.getGenderNumber(orgId, staYear);
    }

    /**
     * 年龄分布详情
     */
    private PartyCaucusForm.TbcAgeGradesForm getPartyAge(Long orgId) {
        PartyCaucusForm.TbcAgeGradesForm tbcAgeGradesForm = new PartyCaucusForm.TbcAgeGradesForm();
        tbcAgeGradesForm.setAgeNumber1(tbcPartyMapper.getAgeNumber(orgId, AGE_ONE));
        tbcAgeGradesForm.setAgeNumber2(tbcPartyMapper.getAgeNumber(orgId, AGE_TWO));
        tbcAgeGradesForm.setAgeNumber3(tbcPartyMapper.getAgeNumber(orgId, AGE_THREE));
        tbcAgeGradesForm.setAgeNumber4(tbcPartyMapper.getAgeNumber(orgId, AGE_FOUR));
        return tbcAgeGradesForm;
    }

    /**
     * 学历情况详情
     */
    private PartyCaucusForm.TbcDetailPagesForm getPartyBack(Long orgId) {
        PartyCaucusForm.TbcDetailPagesForm pagesForm = new PartyCaucusForm.TbcDetailPagesForm();
        pagesForm.setPeopleNumber1(tbcPartyMapper.getBackdropPages(orgId, BACKDROP_GRADUATE));
        pagesForm.setPeopleNumber2(tbcPartyMapper.getBackdropPages(orgId, BACKDROP_UNDERGRADUATE));
        pagesForm.setPeopleNumber3(tbcPartyMapper.getBackdropPages(orgId, BACKDROP_JUNIOR_COLLEGE));
        pagesForm.setPeopleNumber4(tbcPartyMapper.getBackdropPages(orgId, BACKDROP_SECONDARY));
        pagesForm.setPeopleNumber5(tbcPartyMapper.getBackdropPages(orgId, BACKDROP_SENIOR_HIGH_SCHOOL));
        return pagesForm;
    }

    /**
     * 党业发展趋势图
     *
     * @param sysHeader sysHeader
     * @return list
     */
    public List<DevelopForm> develop(HeaderHelper.SysHeader sysHeader) {
        // 获取当前年往前5年，包含当年
        List<Integer> yearList = new ArrayList<>();
        int curYear = DateUtils.getCurrentYear();
        for (int i = 0; i < 5; i++) {
            yearList.add(curYear);
            curYear--;
        }
        return this.userMapper.develop(sysHeader.getOid(), sysHeader.getRegionId(), yearList);
    }

    /**
     * 党务干部
     *
     * @param sysHeader sysHeader
     * @return PartyLeaderForm
     */
    public PartyLeaderForm partyLeader(HeaderHelper.SysHeader sysHeader) {
        PartyLeaderForm result = new PartyLeaderForm();
        // 获取书记，副书记，委员数量
        Integer partyTotal = this.userMapper.findPartyLeaderTotal(sysHeader.getRegionId(), 1);
        result.setPartyTotal(partyTotal);
        Integer deputyTotal = this.userMapper.findPartyLeaderTotal(sysHeader.getRegionId(), 2);
        result.setDeputyTotal(deputyTotal);
        Integer memberTotal = this.userMapper.findPartyLeaderTotal(sysHeader.getRegionId(), 3);
        result.setMemberTotal(memberTotal);
        // 获取专职和兼职百分比
        List<String> tagList = new ArrayList<>();
        // 专职人员数量
        tagList.add(this.fullTimeId);
        Integer fullTimeTotal = this.userMapper.fullPartyTimeTotal(sysHeader.getRegionId(), tagList);
        result.setFullTimeTotal(fullTimeTotal);
        // 兼职人员数量
        tagList.clear();
        tagList.add(this.partyTimeId);
        Integer partyTimeTotal = this.userMapper.fullPartyTimeTotal(sysHeader.getRegionId(), tagList);
        result.setPartyTimeTotal(partyTimeTotal);
        // 专职以及兼职总人数
        tagList.clear();
        tagList.add(this.fullTimeId);
        tagList.add(this.partyTimeId);
        Integer sumTotal = this.userMapper.fullPartyTimeTotal(sysHeader.getRegionId(), tagList);
        result.setSumTotal(sumTotal);
        return result;
    }

    /**
     * 学习教育
     *
     * @param sysHeader sysHeader
     * @return StudyForm
     */
    public StudyForm study(HeaderHelper.SysHeader sysHeader) {
        StudyForm result = new StudyForm();
        // 学习强国
        StudyForm.Study studyScore = this.userMapper.studyScore(DateUtils.getYear(new Date()));
        result.setStudyScore(studyScore);
        List<StudyForm.StudyMap> param = new ArrayList<>();
        // 趋势图，获取当前月近12个月数据，不包含当前月
        for (int i = 1; i < 13; i++) {
            StudyForm.StudyMap form = new StudyForm.StudyMap();
            try {
                String yearMonth = DateUtils.getDateByMonths(-i, "yyyyMM", DateUtils.getCurrentMonth());
                // 年份
                int year = Integer.parseInt(yearMonth.substring(0, 4));
                // 月份
                int month = Integer.parseInt(yearMonth.substring(4, 6));
                form.setYear(year);
                form.setMonth(month);
                param.add(form);
            } catch (Exception e) {
                log.error("获取当前日期异常{}", e.getMessage(), e);
            }
        }
        List<StudyForm.StudyMap> trendList = this.userMapper.trendList(param);
        result.setTrendList(trendList);
        // 网络学院
        StudyForm.Study studyTime = this.userMapper.studyTime(DateUtils.getYear(new Date()));
        result.setStudyTime(studyTime);
        // 数智党建
        StudyForm.StudyDuration studyDuration = new StudyForm.StudyDuration();
        // 查询党员
        List<Long> partyTotal = this.userMapper.staPartyMemberTotal(sysHeader.getOid(), excludeOrgIds);
        if (!CollectionUtils.isEmpty(partyTotal)) {
            studyDuration.setPartyTotal(partyTotal.size());
            // 参与学习人次
            Integer trainingTime = this.meetingMapper.learnTime(partyTotal);
            studyDuration.setTrainingTime(trainingTime);
        }
        // 集中学习时长
        Integer learningTime = this.meetingMapper.trainingTime();
        studyDuration.setLearnTime(learningTime);
        // 每日一课
        Long dayExamId = this.learnScoreConfiguration.getTraining().getId();
        // 查询昨日
        String yesterday = String.valueOf(DateUtil.yesterday()).substring(0, 10);
        Integer dayTotal = this.theoryArmsMapper.findDayExam(yesterday, dayExamId);
        studyDuration.setDayTotal(dayTotal);
        // 每月一测
        Long monthExamId = this.learnScoreConfiguration.getExaminMonth().getId();
        // 查询上一月
        String lastMonth = String.valueOf(DateUtil.lastMonth()).substring(0, 7);
        Integer monthTotal = this.theoryArmsMapper.findMonthExam(lastMonth, monthExamId);
        studyDuration.setMonthTotal(monthTotal);
        // 每季一考
        Long quartExamId = this.learnScoreConfiguration.getExaminQuart().getId();
        // 当前季度
        int quarter = DateUtil.quarter(new Date());
        // 当前年
        int year = DateUtil.year(new Date());
        // 如果当前是第一季度，则查询上一年度第四季度
        if (quarter == 1) {
            year--;
            quarter += 3;
        } else {
            quarter--;
        }
        Integer quarterTotal = this.theoryArmsMapper.findQuartExam(quarter, quartExamId, year);
        studyDuration.setQuarterTotal(quarterTotal);
        result.setStudyDuration(studyDuration);
        return result;
    }

    public SysTaskForm task(HeaderHelper.SysHeader sysHeader) {
        SysTaskForm result = new SysTaskForm();
        // 任务数量 13-党建任务 14-创新任务 15-业务任务 26,27-云区任务
        List<DevelopForm> totalList = this.taskMapper.taskTotal();
        if (!CollectionUtils.isEmpty(totalList)) {
            Optional<DevelopForm> pt = totalList.stream().filter(f -> Integer.parseInt(f.getItem()) == 13).findFirst();
            int partyTotal = 0;
            if (pt.isPresent()) {
                partyTotal = Objects.nonNull(pt.get().getTotal()) ? pt.get().getTotal() : 0;
                result.setPartyTotal(partyTotal);
            }
            Optional<DevelopForm> it = totalList.stream().filter(f -> Integer.parseInt(f.getItem()) == 14).findFirst();
            int innovationTotal = 0;
            if (it.isPresent()) {
                innovationTotal = Objects.nonNull(it.get().getTotal()) ? it.get().getTotal() : 0;
                result.setInnovationTotal(innovationTotal);
            }
            Optional<DevelopForm> bt = totalList.stream().filter(f -> Integer.parseInt(f.getItem()) == 15).findFirst();
            int businessTotal = 0;
            if (bt.isPresent()) {
                businessTotal = Objects.nonNull(bt.get().getTotal()) ? bt.get().getTotal() : 0;
                result.setBusinessTotal(businessTotal);
            }
            Optional<DevelopForm> one = totalList.stream().filter(f -> Integer.parseInt(f.getItem()) == 26).findFirst();
            int ecp26 = 0;
            if (one.isPresent()) {
                ecp26 = Objects.nonNull(one.get().getTotal()) ? one.get().getTotal() : 0;
            }
            Optional<DevelopForm> two = totalList.stream().filter(f -> Integer.parseInt(f.getItem()) == 27).findFirst();
            int ecp27 = 0;
            if (two.isPresent()) {
                ecp27 = Objects.nonNull(two.get().getTotal()) ? two.get().getTotal() : 0;
            }
            result.setEcpTotal(ecp26 + ecp27);
            result.setTotal(partyTotal + innovationTotal + businessTotal + ecp26 + ecp27);
        }
        // 完成度 1-完成 2-进行中
        List<DevelopForm> doList = this.taskMapper.taskDone();
        if (!CollectionUtils.isEmpty(doList)) {
            Optional<DevelopForm> doneTotal = doList.stream().filter(d -> Integer.parseInt(d.getItem()) == 1).findFirst();
            doneTotal.ifPresent(developForm -> result.setDoneTotal(developForm.getTotal()));
            Optional<DevelopForm> doingTotal = doList.stream().filter(d -> Integer.parseInt(d.getItem()) == 2).findFirst();
            doingTotal.ifPresent(developForm -> result.setDoingTotal(developForm.getTotal()));
        }
        // 查询党员
        List<Long> partyMember = this.userMapper.staPartyMemberTotal(sysHeader.getOid(), excludeOrgIds);
        List<Long> excludeOrgList = Arrays.stream(this.excludeOrgIds.split(","))
                .map(Long::valueOf).collect(Collectors.toList());
        // 非党员
        List<Long> otherMember = this.userMapper.staNoPartyMemberTotal(null, excludeOrgList);
        // 获取参与率
        Integer partyAttendTotal = this.taskLogMapper.attendTotal(partyMember);
        result.setPartyAttendTotal(partyAttendTotal);
        Integer otherAttendTotal = this.taskLogMapper.attendTotal(otherMember);
        result.setOtherAttendTotal(otherAttendTotal);
        return result;
    }

    public ExamineForm examine(HeaderHelper.SysHeader sysHeader, Integer year) {
        ExamineForm form = new ExamineForm();
        form = this.tbcPartyMapper.getExamine(sysHeader.getRegionId(), year);
        return form;
    }

    public RankForm rank(HeaderHelper.SysHeader sysHeader, Integer year) {
        RankForm form = new RankForm();
        form = this.tbcPartyMapper.getRank(sysHeader.getRegionId(), year);
        return form;
    }

    public OrgUserActive active(Long orgId, Long regionId, Integer year) {
        Region.OrgData orgData = this.applicationConfigHelper.getOrgByRegionId(regionId);
        int month = LocalDateTime.now().getMonth().getValue();
        OrgUserActive active = new OrgUserActive();
        // 查询年度累计访问量
        Integer userLoginTotal;
        // 年度最高日访问量
        UserLoginRankForm maxDayRankForm;
        // 年度最高月访问量
        UserLoginRankForm maxMonthRankForm;
        // 年度月均访问量
        int monthVisitNum;
        if (orgId.equals(orgData.getOrgId())) {
            userLoginTotal = this.userLoginLogMapper.getUserLoginTotal(regionId, year);
            maxDayRankForm = this.userLoginLogMapper.getMaxDayLogin(regionId, year);
            maxMonthRankForm = this.userLoginLogMapper.getMaxMonthLogin(regionId, year);
            if (DateUtils.getYear(new Date()).equals(year)) {
                monthVisitNum = Math.round((float) userLoginTotal / month);
            } else {
                monthVisitNum = Math.round((float) userLoginTotal / 12);
            }
        } else {
            if (DateUtils.getYear(new Date()).equals(year)) {
                userLoginTotal = this.userLoginLogMapper.getUserLoginTotalByOrg(orgId, year);
                maxDayRankForm = this.userLoginLogMapper.getMaxDayLoginByOrg(orgId, year);
                maxMonthRankForm = this.userLoginLogMapper.getMaxMonthLoginByOrg(orgId, year);
                monthVisitNum = Math.round((float) userLoginTotal / month);
            } else {
                String dateMonth = year + "-12";
                userLoginTotal = this.userLoginLogMapper.getHistoryUserLoginTotalByOrg(orgId, year, dateMonth);
                maxDayRankForm = this.userLoginLogMapper.getHistoryMaxDayLoginByOrg(orgId, year, dateMonth);
                maxMonthRankForm = this.userLoginLogMapper.getHistoryMaxMonthLoginByOrg(orgId, year, dateMonth);
                monthVisitNum = Math.round((float) userLoginTotal / 12);
            }
        }
        // 年度最高日访问量
        MaxDayVisit maxDayVisit = new MaxDayVisit();
        if (null != maxDayRankForm) {
            String maxDay = MAX_DAY_FORMAT.format(maxDayRankForm.getDate());
            maxDayVisit.setDay(maxDay);
            maxDayVisit.setNum(maxDayRankForm.getNum());
        }
        // 年度最高月访问量
        MaxMonthVisit maxMonthVisit = new MaxMonthVisit();
        if (null != maxMonthRankForm) {
            maxMonthVisit.setMonth(maxMonthRankForm.getMonth());
            maxMonthVisit.setNum(maxMonthRankForm.getNum());
        }
        // 最常访问模块
        List<PieObject> visitModule = this.getOrgVisitModule(orgId, year);
        // 封装数据
        active.setYearVisitNum(userLoginTotal);
        active.setAverageMonthVisitNum(monthVisitNum);
        active.setMaxDayVisit(maxDayVisit);
        active.setMaxMonthVisit(maxMonthVisit);
        active.setVisitModule(visitModule);
        return active;
    }

    public List<PieObject> getOrgVisitModule(Long orgId, Integer year) {
        List<UserEntity> userList = this.userMapper.findUserList(orgId, 1, this.excludeOrgConfig.getOrgIds(), this.orgTypeConfig.getNoStatisticsChild(), null);
        Map<String, PieObject> map = new HashMap<>();
        userList.forEach(user -> {
            List<PieObject> visitList = this.getUserVisitModule(user.getUserId(), year);
            for (PieObject pie : visitList) {
                String key = pie.getName();
                if (map.containsKey(key)) {
                    Integer oldNum = map.get(key).getValue();
                    if (null != oldNum) {
                        pie.setValue(oldNum + pie.getValue());
                    }
                }
                map.put(key, pie);
            }
        });
        return map.values().stream().sorted(Comparator.comparingInt(PieObject::getValue).reversed()).collect(Collectors.toList());
    }

    public List<PieObject> getUserVisitModule(Long userId, Integer year) {
        List<PieObject> pieList = new ArrayList<>();
        String redisKey = Constants.DSS_USER_INFO_KEY + year + "_" + userId;
        try {
            if (this.redisTemplate.hasKey(redisKey)) {
                String json = this.redisTemplate.opsForValue().get(redisKey);
                pieList = this.objectMapper.readValue(json, new TypeReference<List<PieObject>>() {
                });
            } else {
                List<GroupValue> valueList = this.clickSpyService.getUserClickSpy(userId, year);
                List<MenuRouteEntity> entityList = this.getMenuRouteList();
                List<PieObject> finalPieList = pieList;
                entityList.forEach(menuRoute -> {
                    PieObject object = new PieObject();
                    object.setName(menuRoute.getName());
                    int total = 0;
                    for (GroupValue value : valueList) {
                        if (value.get_id().contains(menuRoute.getMenuRoute())) {
                            total += value.getTotal().intValue();
                        }
                    }
                    object.setValue(total);
                    finalPieList.add(object);
                });
                pieList = pieList.stream().sorted(Comparator.comparingInt(PieObject::getValue).reversed()).collect(Collectors.toList());
                String json = this.objectMapper.writeValueAsString(pieList);
                this.redisTemplate.opsForValue().set(redisKey, json, 10, TimeUnit.HOURS);
            }
        } catch (JsonProcessingException e) {
            log.error("JSON转换异常", e);
        }
        return pieList;
    }

    /**
     * 获取前端路由表
     *
     * @return
     */
    public List<MenuRouteEntity> getMenuRouteList() {
        List<MenuRouteEntity> menuRouteList = new ArrayList<>();
        try {
            if (this.redisTemplate.hasKey(Constants.MENU_ROUTE_KEY)) {
                String json = this.redisTemplate.opsForValue().get(Constants.MENU_ROUTE_KEY);
                menuRouteList = this.objectMapper.readValue(json, new TypeReference<List<MenuRouteEntity>>() {
                });
            } else {
                menuRouteList = this.menuRouteMapper.selectAll();
                String json = this.objectMapper.writeValueAsString(menuRouteList);
                this.redisTemplate.opsForValue().set(Constants.MENU_ROUTE_KEY, json, 10, TimeUnit.HOURS);
            }
        } catch (JsonProcessingException e) {
            log.error("JSON转换异常", e);
        }
        return menuRouteList;
    }

    public VisitDateResult clickMenuTop(HeaderHelper.SysHeader header) {
        VisitDateResult re = new VisitDateResult();
        try {
            //当日
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            String today = formatter.format(LocalDate.now());
            //查询当年访问人次数据
            Query queryVisitYear = new Query();
            queryVisitYear.addCriteria(Criteria.where("regionId").is(header.getRegionId())
                    .andOperator(Criteria.where("statsDate").gte(today),
                            Criteria.where("statsDate").lte(today)));
            List<VisitNumVo> tmpVisit = mongoTemplate.find(queryVisitYear, VisitNumVo.class);
            if (tmpVisit.size() > 0) {
                VisitDateResult maxVisit = this.findMaxVisitDate(tmpVisit);

                Query queryClickYear = new Query();
                queryClickYear.addCriteria(Criteria.where("regionId").is(header.getRegionId())
                        .andOperator(Criteria.where("statsDate").gte(maxVisit.getStatsDate()),
                                Criteria.where("statsDate").lte(maxVisit.getStatsDate())));
                queryClickYear.with(Sort.by(new Sort.Order(Sort.Direction.DESC, "clickNum")));
                ClickMenu tmpClickYear = mongoTemplate.findOne(queryClickYear, ClickMenu.class);
                //处理当日数据
                re.setMenuName(tmpClickYear != null ? tmpClickYear.getMenuName() : "");
            }
        } catch (Exception e) {
            log.error("统计访问量最高的功能报错！", e);
        }
        return re;
        }


    private VisitDateResult findMaxVisitDate (List < VisitNumVo > visitDate) {
        VisitDateResult re = new VisitDateResult();
        //根据日期分组汇总
        Set<String> statsDateSet = new HashSet<>();
        Map<String, VisitDateResult> tmpMap = new HashMap<>();
        visitDate.forEach(t -> {
            if (statsDateSet.add(t.getStatsDate())) {
                VisitDateResult vdr = new VisitDateResult();
                vdr.setStatsDate(t.getStatsDate());
                vdr.setVisitNum(t.getVisitNum());
                tmpMap.put(t.getStatsDate(), vdr);
            } else {
                VisitDateResult vdr = tmpMap.get(t.getStatsDate());
                //累计结果
                vdr.setStatsDate(t.getStatsDate());
                vdr.setVisitNum(NumberUtils.addInteger(vdr.getVisitNum(), t.getVisitNum()));
                tmpMap.put(t.getStatsDate(), vdr);
            }
        });
        //找出最高访问日期
        Optional<VisitDateResult> maxVisitNum = tmpMap.values().stream().max(Comparator.comparingInt(VisitDateResult::getVisitNum));
        re = maxVisitNum.get();
        return re;
    }

    public VisitForm visit(HeaderHelper.SysHeader sysHeader) {
        //总人数
        Integer num = this.meetingEvalMapper.selectRankUserCount(sysHeader.getRegionId());
        //当日
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String today = formatter.format(LocalDate.now());
        Double visitToDay = this.tbcPartyMapper.getVisitToDay(today);
        //每个月
        List<VisitMonthForm> visitMonth = this.tbcPartyMapper.getVisitMonth();
        VisitForm form = new VisitForm();
        form.setVisitDay(visitToDay/num);
        form.setVisitMonth(visitMonth);
        return form;
    }


    public Object supervise(HeaderHelper.SysHeader sysHeader) {

        return null;
    }
}
