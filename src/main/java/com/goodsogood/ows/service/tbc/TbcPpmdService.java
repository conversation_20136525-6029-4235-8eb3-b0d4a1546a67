package com.goodsogood.ows.service.tbc;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.helper.PageBeanHelper;
import com.goodsogood.ows.mapper.ppmd.TbcPpmdMapper;
import com.goodsogood.ows.model.vo.tbc.OfficeDuesForm;
import com.goodsogood.ows.model.vo.tbc.PartyCaucusUserForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ruoyu
 * @date : 2020/12/28
 * 党费情况
 */
@Service
@Log4j2
public class TbcPpmdService {

    private final TbcPpmdMapper tbcPpmdMapper;
    private final StringRedisTemplate redisTemplate;
    private final TbcPageHelper tbcPageHelper;

    @Autowired
    public TbcPpmdService(TbcPpmdMapper tbcPpmdMapper,
                          StringRedisTemplate redisTemplate,
                          TbcPageHelper tbcPageHelper) {
        this.tbcPpmdMapper = tbcPpmdMapper;
        this.redisTemplate = redisTemplate;
        this.tbcPageHelper = tbcPageHelper;
    }

    /**
     * 获取党费情况
     *
     * @param oid
     * @param rid
     * @return
     */
    public OfficeDuesForm getDuesForm(Long oid, Long rid) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        OfficeDuesForm duesForm;
        String month = new SimpleDateFormat("yyyy-MM").format(new Date());
        String duesFormCacheKey = Constants.TBC_DUES_FORM_ + oid;
        //判断缓存
        if (this.redisTemplate.hasKey(duesFormCacheKey)) {
            duesForm = Utils.fromJson(this.redisTemplate.opsForValue().get(duesFormCacheKey), OfficeDuesForm.class);
        } else {
            duesForm = new OfficeDuesForm();
            try {
                //党费交纳情况
                duesForm.setStatus(this.getStatus(oid, rid, month));
                //党费交纳趋势
                duesForm.setTrend(this.getTrend(oid, month));
                //支部交纳情况
                duesForm.setBranch(this.getBranchForm(oid, month));
                //党员交费排名前40
                duesForm.setRankList(this.getRankList(oid, month));
                this.redisTemplate.opsForValue().set(duesFormCacheKey, Utils.toJson(duesForm), Constants.TBC_CACHE_TIME, TimeUnit.MINUTES);
            } catch (Exception e) {
                e.printStackTrace();
                log.error(e.getMessage());
            }
        }
        stopWatch.stop();
        log.debug("耗时 ->[{}]", stopWatch.getTotalTimeMillis());
        return duesForm;
    }

    /**
     * 党费交纳情况
     *
     * @param oid
     * @param rid
     * @return
     */
    public OfficeDuesForm.StatusForm getStatus(Long oid, Long rid, String month) {
        log.debug("开始查询党费交纳情况，单位 -> [{}]，月份 -> [{}]", oid, month);
        String year = month.substring(0, 4);
        OfficeDuesForm.StatusForm status;
        String statusCacheKey = "status_" + oid + "_" + rid + "_" + month;
        //判断缓存
        if (this.redisTemplate.hasKey(statusCacheKey)) {
            status = Utils.fromJson(this.redisTemplate.opsForValue().get(statusCacheKey), OfficeDuesForm.StatusForm.class);
        } else {
            //先查找今日，初始化status并赋3个值
            status = tbcPpmdMapper.getDayStatus(oid, month);
            //年度交纳金额
            status.setPaidYear(tbcPpmdMapper.getPayYear(oid, year));
            //本月交纳金额和人数
            Map<String, Double> paidAndNumber = tbcPpmdMapper.getMonthPayAndNumber(oid, month);
            Object paidMonth = paidAndNumber.get("paidMonth");
            Object numberMonth = paidAndNumber.get("numberMonth");
            status.setPaidMonth(Double.parseDouble(String.valueOf(paidMonth)));
            status.setNumberMonth(Integer.parseInt(String.valueOf(numberMonth)));
            //本月未交纳金额
            status.setUnpaidMonth(tbcPpmdMapper.getMonthUnpaid(oid, month));
            //本月交纳进度（交纳人数百分比）
            Double percent = tbcPpmdMapper.getPercent(oid, month, rid);
            StringBuilder p = new StringBuilder(percent.toString());
            if (!p.substring(p.lastIndexOf(".") + 1).equals("0")) {
                status.setPercent(p.append("%").toString());
            } else {
                status.setPercent(p.substring(0, p.lastIndexOf(".")).concat("%"));
            }
            this.redisTemplate.opsForValue().set(statusCacheKey, Utils.toJson(status), 5, TimeUnit.MINUTES);
        }
        log.debug("查询结束，查询结果 -> [{}]", status);
        return status;
    }

    /**
     * 今日交费交互数据
     *
     * @param oid
     * @return
     */
    public Page getDetails(Long oid, boolean flag, Integer page, Integer size) {
        log.debug("今日交费交互数据，单位 -> [{}]，类型 -> [{}]", oid, flag == true ? "本人交费" : "他人代交");
        String month = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        List<OfficeDuesForm.Details> list;
        //判断交互类型（true：本人交费，false：他人代交）

        String cacheKey = Constants.TBC_DUES_FORM_ + oid + "_getDetails";
        PageBeanHelper.PageBean pageBean = PageBeanHelper.PageBean.builder()
                .pageNo(page)
                .pageSize(size)
                .build();
        if (flag) {
            return tbcPageHelper.getPageInfo(() -> tbcPpmdMapper.getDetails(oid, month, 1),
                    pageBean, cacheKey + "_1", OfficeDuesForm.Details.class);
//            return PageHelper.startPage(page,size).doSelectPage(()->tbcPpmdMapper.getDetails(oid, month, 1));
        } else {
            return tbcPageHelper.getPageInfo(() -> tbcPpmdMapper.getDetails(oid, month, 2),
                    pageBean, cacheKey + "_2", OfficeDuesForm.Details.class);
//            return PageHelper.startPage(page,size).doSelectPage(()->tbcPpmdMapper.getDetails(oid, month, 2));
        }
    }

    /**
     * 党费交纳趋势
     *
     * @param oid
     * @param month
     * @return
     */
    public Map<String, List<OfficeDuesForm.Node>> getTrend(Long oid, String month) {
        log.debug("开始查询党费交纳趋势，单位 -> [{}]，月份 -> [{}]", oid, month);
        Map<String, List<OfficeDuesForm.Node>> trend = new HashMap<>();
        //本月趋势
        String nowCacheKey = "month_trend_" + month + "_" + oid;
        List<OfficeDuesForm.Node> now;
        //判断缓存
        if (this.redisTemplate.hasKey(nowCacheKey)) {
            now = Utils.fromJson(this.redisTemplate.opsForValue().get(nowCacheKey), List.class);
        } else {
            now = tbcPpmdMapper.getTrend(oid, month);
            if (!CollectionUtils.isEmpty(now)) {
                now.sort((n1, n2) -> n1.getDay() - n2.getDay());
                OfficeDuesForm.Node lastNode = now.get(now.size() - 1);
                now = now.stream().filter(node -> node.getDay() != 0 && (node.getDay() == 1 || node.getDay() % 5 == 0)).collect(Collectors.toList());
                if (!now.contains(lastNode)) {
                    now.add(lastNode);
                }
                this.redisTemplate.opsForValue().set(nowCacheKey, Utils.toJson(now), 5, TimeUnit.MINUTES);
            }
        }
        trend.put("now", now);
        //上月趋势
        String[] s = month.split("-");
        if (s[1].equals("01")) {
            s[0] = String.valueOf(Integer.valueOf(s[0]) - 1);
            s[1] = "12";
        } else if (s[1].equals("10")) {
            s[1] = "09";
        } else {
            s[1] = s[1].substring(0, 1).concat(String.valueOf(Integer.valueOf(s[1].substring(1)) - 1));
        }
        month = String.join("-", s);
        List<OfficeDuesForm.Node> last;
        String lastCacheKey = "month_trend_" + month + "_" + oid;
        //判断缓存
        if (!this.redisTemplate.hasKey(lastCacheKey)) {
            last = tbcPpmdMapper.getTrend(oid, month);
            if (!CollectionUtils.isEmpty(last)) {
                last.sort((n1, n2) -> n1.getDay() - n2.getDay());
                OfficeDuesForm.Node lastNode = last.get(last.size() - 1);
                last = last.stream().filter(node -> node.getDay() != 0 && (node.getDay() == 1 || node.getDay() % 5 == 0)).collect(Collectors.toList());
                if (!last.contains(lastNode)) {
                    last.add(lastNode);
                }
                this.redisTemplate.opsForValue().set(lastCacheKey, Utils.toJson(last), 1, TimeUnit.HOURS);
            }
        } else {
            last = Utils.fromJson(this.redisTemplate.opsForValue().get(lastCacheKey), List.class);
        }
        trend.put("last", last);
        log.debug("查询结束，查询结果 -> [{}]", trend);
        return trend;
    }

    /**
     * 支部交纳情况
     *
     * @param oid
     * @param month
     * @return
     */
    public OfficeDuesForm.BranchForm getBranchForm(Long oid, String month) {
        log.debug("开始查询支部交纳情况，单位 -> [{}]，月份 -> [{}]", oid, month);
        String branchCacheKey = "branch_" + oid + "_" + month;
        OfficeDuesForm.BranchForm branch;
        //判断缓存
        if (this.redisTemplate.hasKey(branchCacheKey)) {
            branch = Utils.fromJson(this.redisTemplate.opsForValue().get(branchCacheKey), OfficeDuesForm.BranchForm.class);
        } else {
            branch = new OfficeDuesForm.BranchForm();
            //支部列表
            List<OfficeDuesForm.Paid> unPaidList = tbcPpmdMapper.getBranchList(oid, month);
            if (!CollectionUtils.isEmpty(unPaidList)) {
                unPaidList.sort((p1, p2) -> p1.getBranchId().intValue() - p2.getBranchId().intValue());
            }
            branch.setList(unPaidList);
            //交齐和未交齐的支部数量
            branch.setNumberPaid(Integer.valueOf(String.valueOf(branch.getList().stream().filter(paid -> paid.getIsFinish() == 1).count())));
            branch.setNumberUnpaid(branch.getList().size() - branch.getNumberPaid());
            this.redisTemplate.opsForValue().set(branchCacheKey, Utils.toJson(branch), 5, TimeUnit.MINUTES);
        }
        log.debug("查询结束，查询结果 -> [{}]", branch);
        return branch;
    }

    /**
     * 支部下的未缴费党员列表
     *
     * @param bid
     * @return
     */
    public Page getUnpaidMember(Long oid, Long bid, Integer page, Integer size) {
        String month = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String cacheKey = Constants.TBC_DUES_FORM_ + oid + "_getUnpaidMember_"+bid;
        PageBeanHelper.PageBean pageBean = PageBeanHelper.PageBean.builder()
                .pageNo(page)
                .pageSize(size)
                .build();
        return tbcPageHelper.getPageInfo(() -> tbcPpmdMapper.getUnpaidMember(oid, bid, month),
                pageBean, cacheKey, OfficeDuesForm.Details.class);
//        return PageHelper.startPage(page, size).doSelectPage(() -> tbcPpmdMapper.getUnpaidMember(oid, bid, month));
    }

    /**
     * 党员交费排名前40
     *
     * @param oid
     * @param month
     * @return
     */
    public List<OfficeDuesForm.Details> getRankList(Long oid, String month) {
        log.debug("开始查询党员交费排名前40，单位 -> [{}]，月份 -> [{}]", oid, month);
        String rankCacheKey = "rank_list_" + month + "_" + oid;
        List<OfficeDuesForm.Details> detailsList;
        //判断缓存
        if (this.redisTemplate.hasKey(rankCacheKey)) {
            detailsList = Utils.fromJson(this.redisTemplate.opsForValue().get(rankCacheKey), List.class);
        } else {
            detailsList = tbcPpmdMapper.getRankList(oid, month);
            if (!CollectionUtils.isEmpty(detailsList) && detailsList.size() >= 40) {
                //40名额已满，取前40，缓存1小时
                detailsList = detailsList.subList(0, 40);
                this.redisTemplate.opsForValue().set(rankCacheKey, Utils.toJson(detailsList), 1, TimeUnit.HOURS);
            } else {
                //40名额未满，缓存5分钟
                this.redisTemplate.opsForValue().set(rankCacheKey, Utils.toJson(detailsList), 5, TimeUnit.MINUTES);
            }
        }
        log.debug("查询结束，查询结果 -> [{}]", detailsList);
        return detailsList;
    }

}
