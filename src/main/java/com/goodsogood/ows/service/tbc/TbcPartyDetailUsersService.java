package com.goodsogood.ows.service.tbc;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.helper.PageBeanHelper;
import com.goodsogood.ows.mapper.user.TbcPartyMapper;
import com.goodsogood.ows.mapper.user.TbcPartyUserMapper;
import com.goodsogood.ows.model.vo.tbc.PartyCaucusUserForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Service
@Log4j2
public class TbcPartyDetailUsersService {

    /*30岁以下*/
    private static final String AGE_ONE = "u.age<30";

    /*30-45 岁*/
    private static final String AGE_TWO = "u.age>=30 and u.age<45";

    /*45-55 岁*/
    private static final String AGE_THREE = "u.age>=45 and u.age<55";

    /* 55岁以上*/
    private static final String AGE_FOUR = "u.age>=55";

    /*研究生*/
    private static final String BACKDROP_GRADUATE = "u.education in(1013,1014,1015)";

    /*大学 本科*/
    private static final String BACKDROP_UNDERGRADUATE = "u.education in(1010,1011,1012,1042)";

    /*大专*/
    private static final String BACKDROP_JUNIOR_COLLEGE = "u.education in(1008,1009,1039)";

    /*中专*/
    private static final String BACKDROP_SECONDARY = "u.education in(1005)";

    /*初中*/
    private static final String BACKDROP_SENIOR_HIGH_SCHOOL = "u.education IN ( 1001, 1002, 1003, 1041, 1004, 1006, 1007, 1026, 1044, 1045 )";

    /*学历默认值*/
    private static final String BACKDROP_DEFAULT = "u.education IN (1001,1002,1003,1041,1004,1006,1007,1026,1044,1045,1013,1014,1015,1010,1011,1012,1042,1008,1009,1039,1005)";

    /*年龄默认值*/
    private static final String AGE = "u.age";

    /*民族默认值*/
    private static final String NATION = "u.ethnic";

    private final TbcPartyMapper tbcPartyMapper;
    private final TbcPartyUserMapper tbcPartyUserMapper;
    private final StringRedisTemplate redisTemplate;
    private final TbcPageHelper tbcPageHelper;

    @Autowired
    public TbcPartyDetailUsersService(TbcPartyMapper tbcPartyMapper,
                                      TbcPartyUserMapper tbcPartyUserMapper,
                                      StringRedisTemplate redisTemplate,
                                      TbcPageHelper tbcPageHelper) {
        this.tbcPartyMapper = tbcPartyMapper;
        this.tbcPartyUserMapper = tbcPartyUserMapper;
        this.redisTemplate = redisTemplate;
        this.tbcPageHelper = tbcPageHelper;
    }


    /**
     * 党员人员详细信息
     *
     * @param orgId
     * @param nationId
     * @return
     */
    public PartyCaucusUserForm partyNationUser(Long orgId, Long nationId, Integer page, Integer size) {
        //redis缓存
       /* String redisKey = Constants.TBC_PARTY_Details_ + orgId;
        try {
            if (redisTemplate.hasKey(redisKey)) {
                return JsonUtils.fromJson(redisTemplate.boundValueOps(redisKey).get(), PartyCaucusUserForm.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("党员人员详情信息 redis数据反序列化失败[{}]", e.getMessage());
        }*/
        PartyCaucusUserForm from = new PartyCaucusUserForm();
        from.setNationNum(getNationNum(orgId, nationId, page, size));
        return from;
    }

    /**
     * 年龄阶段信息
     *
     * @param orgId
     * @param ageType
     * @return
     */
    public PartyCaucusUserForm partyAgeUser(Long orgId, Integer ageType, Integer page, Integer size) {
        PartyCaucusUserForm from = new PartyCaucusUserForm();
        from.setAgeNum(getAgeNums(orgId, ageType, page, size));
        return from;
    }

    /**
     * 学历背景信息
     *
     * @param orgId
     * @param backType
     * @return
     */
    public PartyCaucusUserForm partyBackUser(Long orgId, Integer backType, Integer page, Integer size) {
        PartyCaucusUserForm from = new PartyCaucusUserForm();
        from.setBackdrop(getPartyBack(orgId, backType, page, size));
        return from;
    }

    /**
     * 民族人员详情
     *
     * @param orgId
     * @return
     */
    private Page<PartyCaucusUserForm.TbcPNationalDetailsFrom> getNationNum(Long orgId, Long nationId, Integer page, Integer size) {
        //List<PartyCaucusUserForm.TbcEthnicDistributionOfPartyMembers> from = tbcPartyUserMapper.getNationDetails(orgId);
            /*if (nationId == 1053L) {
                //汉族id
                Long ethnic = 1053L;
                for (int i = 0; i < from.size(); i++) {
                    Long ethnicId = Long.valueOf(from.get(i).getNationId());
                    if (ethnicId.equals(ethnic)) {
                        continue;
                    }
                PageHelper.startPage(page, size)
                .doSelectPage(
            () -> tbcPartyUserMapper.getNationalDetails(orgId, BACKDROP_DEFAULT, AGE, ethnicId));
                }
            }*/
        String cacheKey = Constants.TBC_PARTY_USER_ + orgId + "_getNationNum_"+nationId;
        PageBeanHelper.PageBean pageBean = PageBeanHelper.PageBean.builder()
                .pageNo(page)
                .pageSize(size)
                .build();
        return tbcPageHelper.getPageInfo(() -> tbcPartyUserMapper.getNationalDetails(orgId, nationId),
                pageBean, cacheKey, PartyCaucusUserForm.TbcPNationalDetailsFrom.class);

//        return PageHelper.startPage(page, size)
//                .doSelectPage(
//                        () ->tbcPartyUserMapper.getNationalDetails(orgId, nationId));
    }

    /**
     * 各阶段年龄详情
     *
     * @param orgId
     * @return
     */
    private Page<PartyCaucusUserForm.TbcAgeForDetailsFrom> getAgeNums(Long orgId, Integer ageType, Integer page, Integer size) {
        String cacheKey = Constants.TBC_PARTY_USER_ + orgId + "_getAgeNums_" + ageType;
        PageBeanHelper.PageBean pageBean = PageBeanHelper.PageBean.builder()
                .pageNo(page)
                .pageSize(size)
                .build();

        switch (ageType) {
            case 1:
                return tbcPageHelper.getPageInfo(() -> tbcPartyUserMapper.getAgeDetail(orgId, AGE_ONE),
                        pageBean, cacheKey, PartyCaucusUserForm.TbcAgeForDetailsFrom.class);
//                return PageHelper.startPage(page, size)
//                        .doSelectPage(
//                                () -> tbcPartyUserMapper.getAgeDetail(orgId, AGE_ONE));
            case 2:

                return tbcPageHelper.getPageInfo(() -> tbcPartyUserMapper.getAgeDetail(orgId, AGE_TWO),
                        pageBean, cacheKey, PartyCaucusUserForm.TbcAgeForDetailsFrom.class);

//                return PageHelper.startPage(page, size)
//                        .doSelectPage(
//                                () -> tbcPartyUserMapper.getAgeDetail(orgId, AGE_TWO));

            case 3:
                return tbcPageHelper.getPageInfo(() -> tbcPartyUserMapper.getAgeDetail(orgId, AGE_THREE),
                        pageBean, cacheKey, PartyCaucusUserForm.TbcAgeForDetailsFrom.class);

//                return PageHelper.startPage(page, size)
//                        .doSelectPage(
//                                () -> tbcPartyUserMapper.getAgeDetail(orgId, AGE_THREE));
            case 4:
                return tbcPageHelper.getPageInfo(() -> tbcPartyUserMapper.getAgeDetail(orgId, AGE_FOUR),
                        pageBean, cacheKey, PartyCaucusUserForm.TbcAgeForDetailsFrom.class);
//                return PageHelper.startPage(page, size)
//                        .doSelectPage(
//                                () -> tbcPartyUserMapper.getAgeDetail(orgId, AGE_FOUR));
                    /*case 5:
                        PageHelper.startPage(page, size)
                                .doSelectPage(
                                        () -> tbcPartyUserMapper.getAgeDetail(orgId, BACKDROP_DEFAULT, AGE_FOUR, NATION));
                        tbcPartyUserMapper.getAgeDetail(orgId, BACKDROP_DEFAULT, AGE_ONE, NATION);
                        tbcPartyUserMapper.getAgeDetail(orgId, BACKDROP_DEFAULT, AGE_TWO, NATION);
                        tbcPartyUserMapper.getAgeDetail(orgId, BACKDROP_DEFAULT, AGE_THREE, NATION);
                        tbcPartyUserMapper.getAgeDetail(orgId, BACKDROP_DEFAULT, AGE_FOUR, NATION);
                        break;*/
        }
        return null;
    }

    // 学历情况详情
    private Page<PartyCaucusUserForm.TbcDegreeInDetailsFrom> getPartyBack(Long orgId, Integer backType, Integer page, Integer size) {
        String cacheKey = Constants.TBC_PARTY_USER_ + orgId + "_getPartyBack_" + backType;
        PageBeanHelper.PageBean pageBean = PageBeanHelper.PageBean.builder()
                .pageNo(page)
                .pageSize(size)
                .build();
        switch (backType) {
            case 1:
                return tbcPageHelper.getPageInfo(() -> tbcPartyUserMapper.getDegreeDetails(orgId, BACKDROP_GRADUATE, AGE, NATION),
                        pageBean, cacheKey, PartyCaucusUserForm.TbcDegreeInDetailsFrom.class);
//                return PageHelper.startPage(page, size)
//                        .doSelectPage(
//                                () -> tbcPartyUserMapper.getDegreeDetails(orgId, BACKDROP_GRADUATE, AGE, NATION));
            case 2:
                return tbcPageHelper.getPageInfo(() -> tbcPartyUserMapper.getDegreeDetails(orgId, BACKDROP_UNDERGRADUATE, AGE, NATION),
                        pageBean, cacheKey, PartyCaucusUserForm.TbcDegreeInDetailsFrom.class);
//                return PageHelper.startPage(page, size)
//                        .doSelectPage(
//                                () -> tbcPartyUserMapper.getDegreeDetails(orgId, BACKDROP_UNDERGRADUATE, AGE, NATION));
            case 3:
                return tbcPageHelper.getPageInfo(() -> tbcPartyUserMapper.getDegreeDetails(orgId, BACKDROP_JUNIOR_COLLEGE, AGE, NATION),
                        pageBean, cacheKey, PartyCaucusUserForm.TbcDegreeInDetailsFrom.class);
//                return PageHelper.startPage(page, size)
//                        .doSelectPage(
//                                () -> tbcPartyUserMapper.getDegreeDetails(orgId, BACKDROP_JUNIOR_COLLEGE, AGE, NATION));
            case 4:
                return tbcPageHelper.getPageInfo(() -> tbcPartyUserMapper.getDegreeDetails(orgId, BACKDROP_SECONDARY, AGE, NATION),
                        pageBean, cacheKey, PartyCaucusUserForm.TbcDegreeInDetailsFrom.class);
//                return PageHelper.startPage(page, size)
//                        .doSelectPage(
//                                () -> tbcPartyUserMapper.getDegreeDetails(orgId, BACKDROP_SECONDARY, AGE, NATION));
            case 5:
                return tbcPageHelper.getPageInfo(() -> tbcPartyUserMapper.getDegreeDetails(orgId, BACKDROP_SENIOR_HIGH_SCHOOL, AGE, NATION),
                        pageBean, cacheKey, PartyCaucusUserForm.TbcDegreeInDetailsFrom.class);
//                return PageHelper.startPage(page, size)
//                        .doSelectPage(
//                                () -> tbcPartyUserMapper.getDegreeDetails(orgId, BACKDROP_SENIOR_HIGH_SCHOOL, AGE, NATION));
        }
        return null;
    }

}
