package com.goodsogood.ows.service.tbc;

import com.goodsogood.ows.mapper.sas.TbcOrgInfoMapper;
import com.goodsogood.ows.model.vo.tbc.TbcPbmVo;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-12-27 17:19
 **/
@Service
@Log4j2
public class TbcOrgInfoService {

    private final TbcOrgInfoMapper tbcOrgInfoMapper;

    @Autowired
    public TbcOrgInfoService(TbcOrgInfoMapper tbcOrgInfoMapper) {
        this.tbcOrgInfoMapper = tbcOrgInfoMapper;
    }



    /**
     * 党业融合统计用户堡垒指数信息
     * @return
     */
    TbcPbmVo getTbcOrgPbmInfo(){
        return tbcOrgInfoMapper.getTbcOrgPbmInfo();
    }


    /**
     * 党业融合统计组织3指标信息
     * @return
     */
    Map<String,TbcPbmVo> getTbcOrgThreeIndex(){
        Map<String, TbcPbmVo> map = new HashMap<>();
        TbcPbmVo partyIndex = tbcOrgInfoMapper.getTbcOrgPartyIndexPbmInfo();
        TbcPbmVo businessIndex = tbcOrgInfoMapper.getTbcOrgBusinessIndexPbmInfo();
        TbcPbmVo innovationIndex = tbcOrgInfoMapper.getTbcOrgInnovationIndexPbmInfo();
        map.put("partyIndex", partyIndex);
        map.put("businessIndex", businessIndex);
        map.put("innovationIndex", innovationIndex);
        return map;
    }

}