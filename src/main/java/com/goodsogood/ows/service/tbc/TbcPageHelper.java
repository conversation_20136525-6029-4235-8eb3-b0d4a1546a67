package com.goodsogood.ows.service.tbc;

import com.github.pagehelper.Page;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.helper.PageBeanHelper;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/1/12
 */
@Component
@Log4j2
public class TbcPageHelper {

    private final StringRedisTemplate redisTemplate;

    @Autowired
    public TbcPageHelper(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * @param mapperSelect mapper 各业务数据获取的方法
     * @param pageBean     页数封装类
     * @param cacheName    缓存key
     * @param clazz        序列化类class
     */
    public <T> Page<T> getPageInfo(Supplier<List<T>> mapperSelect,
                                   PageBeanHelper.PageBean pageBean,
                                   String cacheName, Class<T> clazz) {
        //如果有缓存则直接缓存获取
        if (redisTemplate.hasKey(cacheName)) {
            List<T> ts = (List<T>) JsonUtils.fromJson(redisTemplate.boundValueOps(cacheName).get(), List.class, clazz);
            return PageBeanHelper.pageData(ts, pageBean);
        }

        List<T> ts = mapperSelect.get();
        if (CollectionUtils.isEmpty(ts)) {
            return new Page<T>();
        }
        //缓存到redis
        redisTemplate.boundValueOps(cacheName).set(JsonUtils.toJson(ts), Constants.TBC_CACHE_TIME, TimeUnit.MINUTES);
        return PageBeanHelper.pageData(ts, pageBean);
    }


}
