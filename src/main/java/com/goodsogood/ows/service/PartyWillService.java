package com.goodsogood.ows.service;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.CountExcludeConfig;
import com.goodsogood.ows.configuration.TagConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.CenterGroupMapper;
import com.goodsogood.ows.mapper.PartyWillMapper;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.vo.CenterGroupVo;
import com.goodsogood.ows.model.vo.PartyWillVo;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.sas.OpenService;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Log4j2
public class PartyWillService {
    private final Errors errors;
    private final OpenService openService;
    private final PartyWillMapper partyWillMapper;
    private final OrgService orgService;
    private final TagConfig tagConfig;
    private final CountExcludeConfig countExcludeConfig;

    public PartyWillService(OpenService openService, PartyWillMapper partyWillMapper, OrgService orgService, TagConfig tagConfig, Errors errors, CountExcludeConfig countExcludeConfig) {
        this.openService = openService;
        this.partyWillMapper = partyWillMapper;
        this.orgService = orgService;
        this.tagConfig = tagConfig;
        this.errors = errors;
        this.countExcludeConfig = countExcludeConfig;
    }
    public List<PartyWillVo> statistics(HttpHeaders headers, String name, Integer year) {
        Long topics = tagConfig.getTopics(); //第一议题tagid
        Long educations = tagConfig.getEducations();//主题教育tagid
        Long fusions = tagConfig.getFusions();//党业融合tagid
        String excludeOrgIds = countExcludeConfig.getExcludeOrgIds();//排除统计的单位id
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        Long oid = sysHeader.getUoid() == null ? sysHeader.getOid() : sysHeader.getUoid();
        if (oid == 3) {
            List<PartyWillVo> partyWillList = partyWillMapper.statistics(topics, fusions, educations,year);
            List<Long> orgIds = partyWillList.stream()
                    .map(PartyWillVo::getOrgId)
                    .collect(Collectors.toList());
            log.debug("所有的orgId:{}", orgIds);
            String[] excludeIdsArray = excludeOrgIds.split(",");
            List<Long> excludeIds = new ArrayList<>();
            for (String id : excludeIdsArray) {
                excludeIds.add(Long.parseLong(id.trim()));
            }
            List<OrganizationEntity> orgAndOwnerId = orgService.getOwnerId(orgIds);
            log.debug("所有的orgId和ownerId:{}", orgAndOwnerId);
            List<OrganizationEntity> orgAndOwnerIdExclude = new ArrayList<>();
            for (OrganizationEntity entity : orgAndOwnerId) {
                if (!excludeIds.contains(entity.getOwnerId())) {
                    orgAndOwnerIdExclude.add(entity);
                }
            }
            log.debug("排除统计的orgId和ownerId:{}", orgAndOwnerIdExclude);
            for (PartyWillVo partyWillVo : partyWillList) {
                Long orgId = partyWillVo.getOrgId();
                for (OrganizationEntity organizationEntity : orgAndOwnerIdExclude) {
                    if (organizationEntity.getOrganizationId().equals(orgId)) {
                        partyWillVo.setUnitId(organizationEntity.getOwnerId());
                    }
                }
            }
            // 过滤掉unitId为空的数据
            List<PartyWillVo> resultList = partyWillList.stream()
                    .filter(partyWillVo -> partyWillVo.getUnitId() != null)
                    .collect(Collectors.toList());
            //获取所有单位
            List<OpenService.OrgBaseInfo> corpList = openService.getCorps(headers);
            Map<Long, String> unitIdToNameMap = new HashMap<>();
            corpList.forEach(org -> unitIdToNameMap.put(org.getOrgId(), org.getName()));
            log.debug("所有的unitId和unitName:{}", unitIdToNameMap);
            for (PartyWillVo partyWillVo : resultList) {
                Long unitId = partyWillVo.getUnitId();
                String unitName = unitIdToNameMap.get(unitId);
                if (unitName != null) {
                    partyWillVo.setUnitName(unitName);
                }
            }
            // 将unitIdToNameMap中所有没被使用的单位id和名称添加到partyWillList中
            for (Long unitId : unitIdToNameMap.keySet()) {
                boolean flag = false;
                for (PartyWillVo partyWillVo : resultList) {
                    if (partyWillVo.getUnitId().equals(unitId)) {
                        flag = true;
                        break;
                    }
                }
                if (!flag) {
                    PartyWillVo partyWillVo = new PartyWillVo();
                    partyWillVo.setUnitId(unitId);
                    partyWillVo.setUnitName(unitIdToNameMap.get(unitId));
                    partyWillVo.setLearn(0);
                    partyWillVo.setTopics(0);
                    partyWillVo.setFusions(0);
                    partyWillVo.setEducations(0);
                    resultList.add(partyWillVo);
                }
            }
            //根据name模糊查询
            List<PartyWillVo> filteredList;
            if (name != null) {
                filteredList = resultList.stream()
                        .filter(vo -> vo.getUnitName() != null && vo.getUnitName().toLowerCase().contains(name.toLowerCase()))
                        .collect(Collectors.toList());
            } else {
                filteredList = resultList;
            }
            for (PartyWillVo partyWillVo : filteredList) {
                Optional<OpenService.OrgBaseInfo> optionalOrg = corpList.stream()
                        .filter(it -> it.getOrgId().equals(partyWillVo.getUnitId()))
                        .findFirst();
                optionalOrg.ifPresent(corp -> {
                    partyWillVo.setSeq(corp.getSeq());
                });
                if (partyWillVo.getSeq() == null){
                    partyWillVo.setSeq(0);
                }
            }
            Collections.sort(filteredList, Comparator.comparing(PartyWillVo::getSeq));

            return filteredList;
        }
        else {
            LinkedList<OrganizationEntity> orgList = orgService.findAllChildOrg(oid, 1, null, false);
            Map<Long, String> orgNameMap = orgList.stream()
                    .collect(Collectors.toMap(OrganizationEntity::getOrganizationId, OrganizationEntity::getName));
            List<Long> orgIds = new ArrayList<>(orgNameMap.keySet());
            log.debug("所有的非顶级组织orgId:{}", orgIds);
            if (orgIds != null && orgIds.size() > 0){
                List<PartyWillVo> partyWillVoList = partyWillMapper.statisticsOrgIds(orgIds, topics, fusions, educations, year);

                if (name!= null) {
                    List<PartyWillVo> sortedList = partyWillVoList.stream()
                            .filter(item -> item.getOrgName() != null && item.getOrgName().toLowerCase().contains(name.toLowerCase()))
                            .peek(item -> {
                                item.setOrgId(item.getOrgId());
                                item.setOrgName(orgNameMap.get(item.getOrgId()));
                                if (item.getLearn() == null) {
                                    item.setLearn(0);
                                }
                                if (item.getTopics() == null) {
                                    item.setTopics(0);
                                }
                                if (item.getFusions() == null) {
                                    item.setFusions(0);
                                }
                                if (item.getEducations() == null) {
                                    item.setEducations(0);
                                }
                            })
                            .sorted(Comparator.comparingLong(PartyWillVo::getOrgId))
                            .collect(Collectors.toList());

                    List<Long> missingOrgIds = orgIds.stream()
                            .filter(orgId -> sortedList.stream().noneMatch(item -> item.getOrgId() == orgId))
                            .collect(Collectors.toList());

                    Stream<PartyWillVo> missingData = missingOrgIds.stream()
                            .map(orgId -> {
                                PartyWillVo partyWillVo = new PartyWillVo();
                                partyWillVo.setOrgId(orgId);
                                partyWillVo.setOrgName(orgNameMap.get(orgId));
                                partyWillVo.setLearn(0);
                                partyWillVo.setTopics(0);
                                partyWillVo.setFusions(0);
                                partyWillVo.setEducations(0);
                                return partyWillVo;
                            });

                    sortedList.addAll(missingData.collect(Collectors.toList()));
                    sortedList.sort(Comparator.comparingLong(PartyWillVo::getOrgId));

                    return sortedList;
                }else {
                    List<PartyWillVo> sortedList = partyWillVoList.stream()
                            .peek(item -> {
                                item.setOrgId(item.getOrgId());
                                item.setOrgName(orgNameMap.get(item.getOrgId()));
                                if (item.getLearn() == null) {
                                    item.setLearn(0);
                                }
                                if (item.getTopics() == null) {
                                    item.setTopics(0);
                                }
                                if (item.getFusions() == null) {
                                    item.setFusions(0);
                                }
                                if (item.getEducations() == null) {
                                    item.setEducations(0);
                                }
                            })
                            .sorted(Comparator.comparingLong(PartyWillVo::getOrgId))
                            .collect(Collectors.toList());

                    List<Long> missingOrgIds = orgIds.stream()
                            .filter(orgId -> sortedList.stream().noneMatch(item -> item.getOrgId() == orgId))
                            .collect(Collectors.toList());

                    Stream<PartyWillVo> missingData = missingOrgIds.stream()
                            .map(orgId -> {
                                PartyWillVo partyWillVo = new PartyWillVo();
                                partyWillVo.setOrgId(orgId);
                                partyWillVo.setOrgName(orgNameMap.get(orgId));
                                partyWillVo.setLearn(0);
                                partyWillVo.setTopics(0);
                                partyWillVo.setFusions(0);
                                partyWillVo.setEducations(0);
                                return partyWillVo;
                            });

                    sortedList.addAll(missingData.collect(Collectors.toList()));
                    sortedList.sort(Comparator.comparingLong(PartyWillVo::getOrgId));

                    return sortedList;
                }
            }else {
                throw new ApiException(
                        "组织id获取失败",new Result<>(errors, 6001, HttpStatus.OK.value()));

            }
        }
    }
}
