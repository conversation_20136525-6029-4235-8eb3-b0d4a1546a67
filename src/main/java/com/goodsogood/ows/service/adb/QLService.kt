package com.goodsogood.ows.service.adb

import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.configuration.AdbQLConfig
import com.goodsogood.ows.configuration.AdbQLConfig.Companion.columnName
import com.goodsogood.ows.configuration.AdbQLConfig.Companion.format
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.model.antd.AntDesignBase
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.model.vo.ql.Column
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.PreparedStatementCreator
import org.springframework.stereotype.Service
import java.sql.PreparedStatement
import java.util.*

/**
 * <AUTHOR>
 * @date 2023/9/26
 * @description class QLService，因为sql配置在yml中，这里要使用jdbcTemplate，配置见 AdbDataSourceConfig.java
 */
@Service
class QLService(val adbJdbcTemplate: JdbcTemplate, val adbQLConfig: AdbQLConfig, val errors: Errors) {
    private var log = LoggerFactory.getLogger(this.javaClass)

    /**
     *  查询轻流表数据
     *  @param sqlName sql名称
     *  @param params 查询参数
     *  @param pageable 分页参数
     *  @return Page<Map<String, Any>>
     *      Map<String, Any> 为查询结果的一行数据，key为列名，value为列值
     *      Page<Map<String, Any>> 为查询结果的一页数据
     *      Page 为分页对象，包含查询结果的一页数据、分页参数、总条数
     */
    fun findAll(sqlName: String, params: List<Map<String, Any>>, pageable: Pageable): Page<SortedMap<String, Any?>> {
        if (adbQLConfig.sqls[sqlName] == null || adbQLConfig.sqls[sqlName]!!.sql == null) {
            // sql找不到
            throw ApiException(
                "sqlName: $sqlName not found",
                Result<Any>(errors, 9404, HttpStatus.FORBIDDEN.value(), sqlName)
            )
        }
        // 生成条件
        val conditions = buildDynamicConditions(params)
        // 构建 SQL 查询以支持分页
        val sql = adbQLConfig.sqls[sqlName]!!.sql!!.replace("\${where}", conditions)
        val countSql = if ((adbQLConfig.sqls[sqlName]?.countSql
                ?: "").isNotEmpty()
        ) {
            adbQLConfig.sqls[sqlName]?.countSql!!.replace("\${where}", conditions)
        } else {
            "SELECT COUNT(*) FROM ($sql) AS countQuery"
        }
        val totalCount = this.findByDynamicConditions(countSql, params)[0].values.first() as Long
        // adbJdbcTemplate.queryForObject(countSql, Long::class.java) ?: 0L

        val paginationSql = "$sql LIMIT ${pageable.pageSize} OFFSET ${pageable.offset}"
        log.debug("=========> paginationSql: {}", paginationSql)
        val resultList = this.findByDynamicConditions(paginationSql, params).map {
            it.map { (k, v) ->
//                log.debug("=========> k: {}:{}, v: {}:{}", k.javaClass, k, v?.javaClass, v)
                // 获取映射的列名，并对日期进行格式化
                k to (adbQLConfig.sqls[sqlName]!!.params!!.find { p -> p[k] != null }
                    ?.get(k)?.format(v) ?: v)
            }.toMap().toSortedMap()
        }
        return PageImpl(resultList, pageable, totalCount)
    }

    /**
     * 创建动态条件，其中params的key为列名，根据其value来判断
     * 1. 如果是字符串则使用like，
     * 2. 如果是数字则使用=，
     * 3. 如果是Pair代表是日期则使用between
     * 4. 如果是Collection，代表需要用in
     * 这里不直接拼接 value值，使用 ? 来替换
     * @param params 查询参数
     * @return 动态条件
     */
    private fun buildDynamicConditions(params: List<Map<String, Any>>): String {
        val conditions = params.joinToString(" AND ") { param ->
            val condition = param.map { (k, v) ->
                when (v) {
                    is String -> {
                        "$k LIKE ?"
                    }

                    is Number -> {
                        "$k = ?"
                    }

                    is Pair<*, *> -> {
                        "$k >= ? AND $k <= ?"
                    }

                    is Collection<*> -> {
                        if (v.isEmpty()) {
                             ""
                        } else {
                            if(v.first() is String) {
                                // 如果是字符串，需要拼凑成 $k like '%v1%' or $k like '%v2%' or $k like '%v3%'
                                v.joinToString(" OR ") { "$k LIKE ?" }
                            } else {
                                "$k IN (${v.joinToString(",") { "?" }})"
                            }
                        }
                    }

                    else -> {
                        "$k = ?"
                    }
                }
            }.joinToString(" OR ")
            "( $condition )"
        }

        log.debug("=========> conditions: {}", conditions)
        return conditions.trim().ifEmpty { "1=1" }
    }

    /**
     * 通过 PreparedStatement 动态查询结果
     * @param sql sql语句
     * @param params 查询参数
     * @return 查询结果
     */
    private fun findByDynamicConditions(sql: String, params: List<Map<String, Any>>): List<Map<String, Any?>> {
        val preparedStatementCreator = PreparedStatementCreator {
            val preparedStatement: PreparedStatement = it.prepareStatement(sql)
            var parameterIndex = 1
            params.forEach { param ->
                for ((key, value) in param) {
                    when (value) {
                        is Collection<*> -> {
                            // For IN clause
                            for (item in value) {
                                if(item is String){
                                    preparedStatement.setString(parameterIndex++, "%$item%")
                                }else {
                                    preparedStatement.setObject(parameterIndex++, item)
                                }
                            }
                        }

                        is Pair<*, *> -> {
                            // For BETWEEN clause
                            // 判断value.first和 value.second格式，如果是类似2023-01-01这种 yyyy-MM-dd的格式，需要添加时分秒
                            val first = value.first as String
                            val second = value.second as String
                            val firstDate = if (first.length == 10) {
                                "$first 00:00:00"
                            } else {
                                first
                            }
                            val secondDate = if (second.length == 10) {
                                "$second 23:59:59"
                            } else {
                                second
                            }
                            preparedStatement.setString(parameterIndex++, firstDate)
                            preparedStatement.setString(parameterIndex++, secondDate)
                        }

                        is String -> {
                            // For LIKE clause
                            preparedStatement.setString(parameterIndex++, "%$value%")
                        }

                        is Number -> {
                            // For other conditions
                            preparedStatement.setLong(parameterIndex++, value.toLong())
                        }

                        else -> {
                            // For other conditions
                            preparedStatement.setObject(parameterIndex++, value)
                        }
                    }
                }
            }
            preparedStatement
        }
        return adbJdbcTemplate.query(preparedStatementCreator) { resultSet, _ ->
            resultSet.toMap()
        } ?: listOf()
    }

    private fun java.sql.ResultSet.toMap(): Map<String, Any?> {
        val metaData = metaData
        val columnCount = metaData.columnCount
        val result = mutableMapOf<String, Any?>()
        for (i in 1..columnCount) {
            result[metaData.getColumnLabel(i)] = getObject(i)
        }
        return result
    }

    fun getColumn(sqlName: String): List<Column> {
        if (adbQLConfig.sqls[sqlName] == null || adbQLConfig.sqls[sqlName]!!.sql == null) {
            // sql找不到
            throw ApiException(
                "sqlName: $sqlName not found",
                Result<Any>(errors, 9404, HttpStatus.FORBIDDEN.value(), sqlName)
            )
        }
        return adbQLConfig.sqls[sqlName]!!.params!!.map {
            Column(
                key = it.keys.first(),
                name = it.values.first().name,
                width = it.values.first().width,
                index = it.values.first().index,
            )
        }.sortedBy { it.index }
    }

    fun getQueryColumn(sqlName: String): List<AntDesignBase> {
        if (adbQLConfig.sqls[sqlName] == null || adbQLConfig.sqls[sqlName]!!.sql == null) {
            // sql找不到
            throw ApiException(
                "sqlName: $sqlName not found",
                Result<Any>(errors, 9404, HttpStatus.FORBIDDEN.value(), sqlName)
            )
        }
        return adbQLConfig.sqls[sqlName]!!.queryParams!!.map {
            AdbQLConfig.getRealQueryParams(it)
        }.sortedBy { it.index }
    }
}