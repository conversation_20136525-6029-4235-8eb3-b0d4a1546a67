package com.goodsogood.ows.service.readClass

import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.configuration.CountExcludeConfig
import com.goodsogood.ows.configuration.TagConfig
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.mapper.count.ReadClassCountMapper
import com.goodsogood.ows.model.db.user.OrganizationEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.model.vo.count.ReadClassCountVO
import com.goodsogood.ows.service.investigation.InvestigationService
import com.goodsogood.ows.service.sas.OpenService
import com.goodsogood.ows.service.user.OrgService
import lombok.extern.slf4j.Slf4j
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Service
import java.util.*
import java.util.stream.Collectors

@Service
@Slf4j
class ReadClassService(
    @Autowired val errors: Errors,
    @Autowired val readClassCountMapper: ReadClassCountMapper,
    @Autowired val orgService: OrgService,
    @Autowired val openService: OpenService,
    @Autowired val countExcludeConfig: CountExcludeConfig,
    @Autowired val tagConfig: TagConfig
) {

    private val log = LoggerFactory.getLogger(InvestigationService::class.java)

    private val tagId = tagConfig.readClass

    fun count(headers: HttpHeaders, name: String?, year: Int?): List<ReadClassCountVO>? {
        val excludedUnitsStr = countExcludeConfig.excludeOrgIds
//        log.debug("---excludedUnitsStr:{}",excludedUnitsStr)
        val excludedUnits = excludedUnitsStr?.split(",")?.mapNotNull { it.toLongOrNull() }?.toList()
        val orgId = HeaderHelper.buildMyHeader(headers).uoid ?: HeaderHelper.buildMyHeader(headers).oid
        val yearInit: Int = if (year == null) {
            val now = java.time.Year.now()
            now.value
        } else {
            year
        }
        if (orgId == 3L) {
            //顶级组织

            //所有党小组的数据 -- 调用远程服务
            val allGroupIdList: List<Long> = orgService.getAllGroupsIds()
            //所有党小组的统计数据
            val groupCountList = readClassCountMapper.countElseOrgByList(
                allGroupIdList,
                null,
                yearInit,
                tagId,
                true
            )
            val groupList: List<OrganizationBase> = openService.findOrgByIds(
                groupCountList.map { it.orgId }.toList(),
                headers
            )
            //所有党小组统计数据的父节点id -- 调用远程服务
            val groupParentList = openService.findOrgByIds( groupList.map { it.parentId }.toList(),headers      )
            //所有非党小组的统计数据
            val exceptCountList = readClassCountMapper.countElseOrgByList(
                allGroupIdList,
                null,
                yearInit,
                tagId,
                false
            )
            //获取所有非党小组的orgList --调用远程服务
            val exceptOrgList: List<OrganizationBase> =
                openService.findOrgByIds(exceptCountList.map { it.orgId }.toList(), headers)
            val resultMap = mutableMapOf<Long, ReadClassCountVO>()
            var ownerId: Long
            var ownerName: String
            for (i in 0 until groupParentList.size) {
                ownerId = groupParentList[i].ownerId
                ownerName = groupParentList[i].ownerName
                if (excludedUnits != null) {
                    if (ownerId in excludedUnits) continue
                }
                if (!resultMap.containsKey(ownerId)) {
                    resultMap[ownerId] = ReadClassCountVO(
                        unitId = ownerId,
                        unitName = ownerName,
                        times = groupCountList[i].times,
                        duration = groupCountList[i].duration
                    )
                } else {
                    resultMap[ownerId]?.times = groupCountList[i].times?.let {
                        resultMap[ownerId]?.times?.plus(
                            it
                        )
                    }
                    resultMap[ownerId]?.duration = groupCountList[i].duration?.let {
                        resultMap[ownerId]?.duration?.plus(
                            it
                        )
                    }
                }
            }
            for(i in 0 until exceptOrgList.size){
                ownerId = exceptOrgList[i].ownerId
                ownerName = exceptOrgList[i].ownerName
                if (excludedUnits != null) {if (ownerId in excludedUnits) continue}
                if(!resultMap.containsKey(ownerId)){
                    resultMap[ownerId] = ReadClassCountVO(
                        unitId = ownerId,
                        unitName = ownerName,
                        times = exceptCountList[i].times,
                        duration = exceptCountList[i].duration
                    )
                }else{
                    resultMap[ownerId] ?.times = exceptCountList[i].times?.let {
                        resultMap[ownerId]?.times?.plus(
                            it
                        )
                    }
                    resultMap[ownerId]?.duration = exceptCountList[i].duration?.let {
                        resultMap[ownerId]?.duration?.plus(
                            it
                        )
                    }
                }
            }
            //获取所有单位
            val corpList = openService.getCorps(headers)
            val resultList = resultMap.values.toList().toMutableList()
            corpList.forEach { corp ->
                if (resultList.none { it.unitId == corp.orgId }) {
                    val newReadClassCountVO =
                        ReadClassCountVO(unitId = corp.orgId, unitName = corp.name, times = 0, duration = 0.0)
                    resultList += newReadClassCountVO
                }
            }

            log.debug("name是:{}", name)
            //模糊查询
            val list: MutableList<ReadClassCountVO> = (if (name != null) {
                resultList.filter { it.unitName!!.contains(name) }
            } else {
                resultList
            }).toMutableList()
            //seq初始化   duration保留一位小数
            list.forEach { readClassCountVO ->
                // 找到与 corpList 中 unitId 相匹配的元素
                val matchedCorp = corpList.find { it.orgId == readClassCountVO.unitId }
                // 如果找到了匹配的元素，则将其 seq 赋值给 readClassCountVO 的 seq
                matchedCorp?.let {
                    readClassCountVO.seq = it.seq
                }
                readClassCountVO.duration = "%.1f".format(readClassCountVO.duration).toDouble()
            }

            return list.sortedBy { it.seq }
        } else {
            //非顶级组织

            log.debug("我的读书班调试信息--->name:{}", name)
            val orgList = orgService.findAllChildOrg(orgId, 1, null, false)
            val orgIds = orgList.stream()
                .map(OrganizationEntity::getOrganizationId)
                .collect(Collectors.toList())
            val list = readClassCountMapper.countElseOrgByList(orgIds, name, yearInit, tagId,true).toMutableList()
            orgList.forEach { org ->
                if (list.none { it.orgId == org.organizationId }) {
                    val newReadClassCountVO =
                        ReadClassCountVO(orgId = org.organizationId, orgName = org.name, times = 0, duration = 0.0)
                    list += newReadClassCountVO
                }
            }
            val resultList: MutableList<ReadClassCountVO> = if (name != null) {
                log.debug("过滤name的list:{}", list)
                list.filter { it.orgName?.contains(name, ignoreCase = true) == true }.toMutableList()
            } else {
                list.toMutableList()
            }
            for(i in 0 until list.size){
                list[i].duration = "%.1f".format(list[i].duration).toDouble()
                list[i].orgName = orgList[i].name
            }
            //赋值组织名称
            list.forEach {
                it.duration = "%.1f".format(it.duration).toDouble()
                orgList.forEach { orgItem ->
                    if (it.orgId == orgItem.organizationId) {
                        it.orgName = orgItem.name
                    }
                }
            }
            return resultList.sortedBy { it.orgId }
        }
    }

}