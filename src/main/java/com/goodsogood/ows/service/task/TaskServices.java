package com.goodsogood.ows.service.task;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.task.TaskAccessMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.vo.EcpRecordVo;
import com.goodsogood.ows.model.vo.EcpTaskListForm;
import com.goodsogood.ows.model.vo.EcpTaskStaFrom;
import com.goodsogood.ows.service.user.UserMongoService;
import com.goodsogood.ows.utils.ListUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
@Service
public class TaskServices {

    private final TaskAccessMapper taskAccessMapper;
    private final UserMapper userMapper;

    private final UserMongoService userMongoService;

    public TaskServices(TaskAccessMapper taskAccessMapper,
                        UserMapper userMapper,
                        UserMongoService userMongoService) {
        this.taskAccessMapper = taskAccessMapper;
        this.userMapper = userMapper;
        this.userMongoService = userMongoService;
    }

    /**
     * 我的云上任务汇总统计
     * 累计完成 统计单个用户的完成情况
     * 本单位党员人均与系统党员人均 都是统计党员为基础信息
     * @param sysHeader
     * @return
     */
    public EcpTaskStaFrom taskSta(HeaderHelper.SysHeader sysHeader,Long unitId) {
        EcpTaskStaFrom ecpTaskStaFrom = new EcpTaskStaFrom();
        List<Long> singleUser = Collections.singletonList(sysHeader.getUserId());
        //查询累计完成情况
        Integer cumulativeCompletion = taskAccessMapper.ecpUserTaskSta(singleUser);
        ecpTaskStaFrom.setCumulativeCompletion(cumulativeCompletion);
        //查询用户所在单位的党员信息
        List<Long> unitUserIds = userMongoService.getUserIdList(sysHeader.getRegionId(), unitId, 1);
        if(!CollectionUtils.isEmpty(unitUserIds)){
            Integer unitAverage = taskAccessMapper.ecpUserTaskSta(unitUserIds);
            ecpTaskStaFrom.setUnitAverage( unitAverage / unitUserIds.size());
            //ecpTaskStaFrom.setUnitAverage(NumberUtils.divide(unitAverage,unitUserIds.size(),4) );
        }
        //查询系统党员
        List<Long> allUserIds = userMongoService.getUserIdList(sysHeader.getRegionId(),null, 1);
        if(!CollectionUtils.isEmpty(allUserIds)) {
            List<List<Long>> lists = ListUtils.splitList(allUserIds, 600);
            int systemAverage=0;
            for(List<Long> list:lists) {
                int batchResult = taskAccessMapper.ecpUserTaskSta(list);
                systemAverage += batchResult;
            }
            ecpTaskStaFrom.setSystemAverage( systemAverage / allUserIds.size());
            //ecpTaskStaFrom.setSystemAverage(NumberUtils.divide(systemAverage,allUserIds.size(),4));
        }
        return ecpTaskStaFrom;
    }

    /**
     * 我的云上任务列表
     * @param sysHeader
     * @return
     */
    public List<EcpTaskListForm> taskRecordList(HeaderHelper.SysHeader sysHeader,Integer type) {
        List<Integer> collectStatus = Arrays.stream(new int[]{3,7,8}).boxed().collect(Collectors.toList());
        //默认是已完成  1.已完成 2.待完成
        if(type!=1) {
             collectStatus = Arrays.stream(new int[]{1,2, 5, 9}).boxed().collect(Collectors.toList());
        }
        return taskAccessMapper.ecpTaskRecordList(sysHeader.getUserId(), collectStatus);
    }


    /**
     * 融合云区动态按全市系统统计
     * @param sysHeader
     * @return
     */
    public List<EcpRecordVo> unitRecord(HeaderHelper.SysHeader sysHeader,Long unitId) {
        Long userOwnerId = userMapper.getUserOwnerId(sysHeader.getRegionId(), sysHeader.getUserId());
        List<EcpRecordVo> ecpRecordVos = taskAccessMapper.unitRecord(userOwnerId);
        return ecpRecordVos;
       // return handleRecord(ecpRecordVos);
    }

    /**
     * 融合云区动态按全市系统统计
     * @param sysHeader
     * @return
     */
    public List<EcpRecordVo> allRecord(HeaderHelper.SysHeader sysHeader) {
        List<EcpRecordVo> ecpRecordVos = taskAccessMapper.allRecord();
        return ecpRecordVos;
       //return handleRecord(ecpRecordVos);
    }


    /**
     * 处理记录
     * @param list
     */
    private List<EcpRecordVo> handleRecord(List<EcpRecordVo> list){
        List<EcpRecordVo> all = new LinkedList<>();
        if(!CollectionUtils.isEmpty(list)) {
            for(int i=0;i<list.size();i++){
                EcpRecordVo ecpRecordVo = list.get(i);
                if (ecpRecordVo.getType() == 1) {
                    if(null==ecpRecordVo.getEcpOrgId()){
                        continue;
                    }
                    String ecpOrgName = taskAccessMapper.getEcpOrgName(ecpRecordVo.getEcpOrgId());
                    if(StringUtils.isEmpty(ecpOrgName)){
                        continue;
                    }
                    ecpRecordVo.setContent(ecpOrgName);
                } else {
                    if(null==ecpRecordVo.getTaskId()){
                        continue;
                    }
                    String taskTitle = taskAccessMapper.getTaskTitle(ecpRecordVo.getTaskId());
                    if(StringUtils.isEmpty(taskTitle)){
                        continue;
                    }
                    ecpRecordVo.setContent(taskTitle);
                }
                all.add(ecpRecordVo);
                if(all.size()>=20){
                    break;
                }
            }
        }
        return all;
    }
}
