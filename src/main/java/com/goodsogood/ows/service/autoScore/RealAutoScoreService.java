package com.goodsogood.ows.service.autoScore;

import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.ExcludeOrgConfig;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.ScoreConfig;
import com.goodsogood.ows.mapper.rank.OrgScoreMapper;
import com.goodsogood.ows.mapper.rank.UserScoreMapper;
import com.goodsogood.ows.mapper.sas.AutoScoreMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.model.mongodb.dss.*;
import com.goodsogood.ows.model.vo.rank.TotalScoreVo;
import com.goodsogood.ows.model.vo.sas.PartyOrgVo;
import com.goodsogood.ows.service.impl.DssIndexBuilder;
import com.goodsogood.ows.service.impl.DssPartyBranchBuilder;
import com.goodsogood.ows.service.impl.DssPartyCommitteeBuilder;
import com.goodsogood.ows.service.impl.DssUserBuilder;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDateTime;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Create by FuXiao on 2020/11/10
 */
@Service
@Log4j2
public class RealAutoScoreService implements DssIndexBuilder, DssPartyBranchBuilder, DssPartyCommitteeBuilder, DssUserBuilder {

    private static final String USER_SCORE_MEDIAN = "USER_SCORE_MEDIAN_";
    private static final String ORG_SCORE_MEDIAN = "ORG_SCORE_MEDIAN_";

    private static final String USER_TOTAL_SCORE = "USER_TOTAL_SCORE_";
    private static final String ORG_TOTAL_SCORE = "ORG_TOTAL_SCORE_";

    private final OrgService orgService;
    private final UserService userService;
    private final UserMapper userMapper;
    private final UserScoreMapper userScoreMapper;
    private final OrgScoreMapper orgScoreMapper;
    private final ComputeService computeService;
    private final AutoScoreMapper autoScoreMapper;
    private final ScoreConfig scoreConfig;
    private final StringRedisTemplate redisTemplate;
    private final SimpleApplicationConfigHelper applicationConfigHelper;
    private final OrgTypeConfig orgTypeConfig;
    private final ExcludeOrgConfig excludeOrgConfig;
    private final RankComputeService rankComputeService;

    public RealAutoScoreService(OrgService orgService, UserService userService, UserMapper userMapper, UserScoreMapper userScoreMapper, OrgScoreMapper orgScoreMapper, ComputeService computeService, AutoScoreMapper autoScoreMapper, ScoreConfig scoreConfig, StringRedisTemplate redisTemplate, SimpleApplicationConfigHelper applicationConfigHelper, OrgTypeConfig orgTypeConfig, ExcludeOrgConfig excludeOrgConfig, RankComputeService rankComputeService) {
        this.orgService = orgService;
        this.userService = userService;
        this.userMapper = userMapper;
        this.userScoreMapper = userScoreMapper;
        this.orgScoreMapper = orgScoreMapper;
        this.computeService = computeService;
        this.autoScoreMapper = autoScoreMapper;
        this.scoreConfig = scoreConfig;
        this.redisTemplate = redisTemplate;
        this.applicationConfigHelper = applicationConfigHelper;
        this.orgTypeConfig = orgTypeConfig;
        this.excludeOrgConfig = excludeOrgConfig;
        this.rankComputeService = rankComputeService;
    }

    @Override
    public IndexInfo buildIndex(IndexInfo info) {
        //查询所有党委id
        Set<Long> officeIdList;
        if (info.getYear() < new DateTime().getYear()) {
            officeIdList = orgService.getOfficeCommittee(info.getRootId(), info.getYear(), 12);
        } else {
            officeIdList = orgService.getOfficeCommittee(info.getRegionId(), info.getYear(), LocalDateTime.now().getMonthOfYear());
        }
        List<IndexGrandMap> res = new ArrayList<>();

        List<TotalScoreVo> totalScoreVos = new ArrayList<>();
        if (0 != officeIdList.size()) {
            totalScoreVos = orgScoreMapper.findOrgTotalScore(officeIdList, info.getYear());
        }

        IndexGrandMap grandMap5 = new IndexGrandMap();
        grandMap5.setName("5星级党委");

        IndexGrandMap grandMap4 = new IndexGrandMap();
        grandMap4.setName("4星级党委");

        IndexGrandMap grandMap3 = new IndexGrandMap();
        grandMap3.setName("3星级党委");

        IndexGrandMap grandMap2 = new IndexGrandMap();
        grandMap2.setName("2星级党委");

        IndexGrandMap grandMap1 = new IndexGrandMap();
        grandMap1.setName("1星级党委");

        List<OfficeOrg> dssOrgChildLists5 = new ArrayList<>();
        List<OfficeOrg> dssOrgChildLists4 = new ArrayList<>();
        List<OfficeOrg> dssOrgChildLists3 = new ArrayList<>();
        List<OfficeOrg> dssOrgChildLists2 = new ArrayList<>();
        List<OfficeOrg> dssOrgChildLists1 = new ArrayList<>();

        int size = totalScoreVos.size();
        int avg = (0 == size) ? 0 : totalScoreVos.stream().mapToInt(TotalScoreVo::getScore).sum()/size;

        for (int i = 0; i < size; i++) {
            if (i <= size*0.25-1) { dssOrgChildLists5.add(OfficeOrg.builder().orgId(totalScoreVos.get(i).getId()).build()); }
            else if (totalScoreVos.get(i).getScore() > avg) { dssOrgChildLists4.add(OfficeOrg.builder().orgId(totalScoreVos.get(i).getId()).build()); }
            else if (totalScoreVos.get(i).getScore() > avg*0.7) { dssOrgChildLists3.add(OfficeOrg.builder().orgId(totalScoreVos.get(i).getId()).build()); }
            else if (totalScoreVos.get(i).getScore() > avg*0.5) { dssOrgChildLists2.add(OfficeOrg.builder().orgId(totalScoreVos.get(i).getId()).build()); }
            else { dssOrgChildLists1.add(OfficeOrg.builder().orgId(totalScoreVos.get(i).getId()).build()); }
        }

        grandMap1.setOrgList(dssOrgChildLists1);
        grandMap1.setValue(dssOrgChildLists1.size());
        grandMap1.setPercent(String.valueOf(dssOrgChildLists1.size()*100/totalScoreVos.size()));
        grandMap2.setOrgList(dssOrgChildLists2);
        grandMap2.setValue(dssOrgChildLists2.size());
        grandMap2.setPercent(String.valueOf(dssOrgChildLists2.size()*100/totalScoreVos.size()));
        grandMap3.setOrgList(dssOrgChildLists3);
        grandMap3.setValue(dssOrgChildLists3.size());
        grandMap3.setPercent(String.valueOf(dssOrgChildLists3.size()*100/totalScoreVos.size()));
        grandMap4.setOrgList(dssOrgChildLists4);
        grandMap4.setValue(dssOrgChildLists4.size());
        grandMap4.setPercent(String.valueOf(dssOrgChildLists4.size()*100/totalScoreVos.size()));
        grandMap5.setOrgList(dssOrgChildLists5);
        grandMap5.setValue(dssOrgChildLists5.size());
        grandMap5.setPercent(String.valueOf(dssOrgChildLists5.size()*100/totalScoreVos.size()));

        res.add(grandMap5);
        res.add(grandMap4);
        res.add(grandMap3);
        res.add(grandMap2);
        res.add(grandMap1);

        info.setGrandMap(res);

        return info;
    }

    @Override
    public PartyBranchInfo buildPartyBranch(PartyBranchInfo info) {
        //综合评分
        TotalScoreVo totalScoreVo = orgScoreMapper.findSingleOrgTotal(info.getOrganizationId(), info.getYear());
        info.setSumScore((null == totalScoreVo || null == totalScoreVo.getScore()) ? 0.0 : computeService.conversion(1L, totalScoreVo.getScore().doubleValue(), info.getYear()));
        //雷达图
        List<RadarChartMap> radarChartMaps = new ArrayList<>();
        for (int i = 1; i <= 5; i++) {
            RadarChartMap radarChartMap = new RadarChartMap();
            switch (i) {
                case 1:
                    radarChartMap.setName("基础信息完整度");
                    Integer tmp = autoScoreMapper.findOrgRuleScore("基础信息完整度", info.getYear(), info.getOrganizationId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(1L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(1L, 192d, info.getYear()));
                    break;
                case 2:
                    radarChartMap.setName("基础工作完成度");
                    tmp = autoScoreMapper.findOrgRuleScore("基础工作", info.getYear(), info.getOrganizationId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(1L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(1L, 477d, info.getYear()));
                    break;
                case 3:
                    radarChartMap.setName("组织建设规范度");
                    tmp = autoScoreMapper.findOrgRuleScore("组织建设", info.getYear(), info.getOrganizationId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(1L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(1L, 180d, info.getYear()));
                    break;
                case 4:
                    radarChartMap.setName("党务工作活跃度");
                    tmp = autoScoreMapper.findOrgRuleScore("活跃程度", info.getYear(), info.getOrganizationId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(1L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(1L, 240d, info.getYear()));
                    break;
                default:
                    radarChartMap.setName("党建工作认可度");
                    tmp = autoScoreMapper.findOrgRuleScore("优秀组织", info.getYear(), info.getOrganizationId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(1L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(1L, 5d, info.getYear()));
                    break;
            }
            radarChartMaps.add(radarChartMap);
        }
        info.setRadarChartMap(radarChartMaps);

        //饼图
        List<Long> userIdList = new ArrayList<>();
        List<UserEntity> userInfoBaseList = userMapper.findUserList(info.getOrganizationId(), 1, this.excludeOrgConfig.getOrgIds(), this.orgTypeConfig.getNoStatisticsChild(), null);
        for (UserEntity entity : userInfoBaseList) {
            userIdList.add(entity.getUserId());
        }

        List<UserGrandMap> res = new ArrayList<>();
        List<TotalScoreVo> totalScoreVos = new ArrayList<>();
        if (0 != userIdList.size()) {
            totalScoreVos = userScoreMapper.findUserTotalScore(userIdList, info.getYear());
        }

        UserGrandMap grandMap1 = new UserGrandMap();
        grandMap1.setName("95分以上的党员");

        UserGrandMap grandMap2 = new UserGrandMap();
        grandMap2.setName("90-95分的党员");

        UserGrandMap grandMap3 = new UserGrandMap();
        grandMap3.setName("90分以下的党员");

        UserGrandMap grandMap4 = new UserGrandMap();
        grandMap4.setName("未评分的党员");

        List<DssUserChild> dssUserLists1 = new ArrayList<>();
        List<DssUserChild> dssUserLists2 = new ArrayList<>();
        List<DssUserChild> dssUserLists3 = new ArrayList<>();
        List<DssUserChild> dssUserLists4 = new ArrayList<>();

        for (TotalScoreVo tScoreVo : totalScoreVos) {
            Double score = computeService.conversion(1L, tScoreVo.getScore().doubleValue(), info.getYear());

            if (score >= 95.0) {
                DssUserChild list = new DssUserChild();
                list.setUserId(tScoreVo.getId());
                dssUserLists1.add(list);
            }

            else if (score >= 90.0 && score < 95.0) {
                DssUserChild list = new DssUserChild();
                list.setUserId(tScoreVo.getId());
                dssUserLists2.add(list);
            }

            else if (score < 90.0) {
                DssUserChild list = new DssUserChild();
                list.setUserId(tScoreVo.getId());
                dssUserLists3.add(list);
            }

            else {
                DssUserChild list = new DssUserChild();
                list.setUserId(tScoreVo.getId());
                dssUserLists4.add(list);
            }
        }

        grandMap1.setUserList(dssUserLists1);
        grandMap1.setValue(dssUserLists1.size());
        grandMap2.setUserList(dssUserLists2);
        grandMap2.setValue(dssUserLists2.size());
        grandMap3.setUserList(dssUserLists3);
        grandMap3.setValue(dssUserLists3.size());
        grandMap4.setUserList(dssUserLists4);
        grandMap4.setValue(dssUserLists4.size());

        res.add(grandMap1);
        res.add(grandMap2);
        res.add(grandMap3);
        res.add(grandMap4);

        info.setGrandMap(res);

        List<MapObject> annualScoreMap = new ArrayList<>();
        //满分
        MapObject mapObject1 = new MapObject();
        mapObject1.setType("满分");
        List<Double> scoreList1 = new ArrayList<>();
        int month = 12;
        if (info.getYear().equals(LocalDateTime.now().getYear())) {
            month = LocalDateTime.now().getMonthOfYear() - 1;
        }
        for (int i = 0; i < month; i++) {
            scoreList1.add(Double.parseDouble(scoreConfig.getTotalScore().getOrg().split(",")[i]));
        }
        mapObject1.setScoreList(scoreList1);
        annualScoreMap.add(mapObject1);

        //本组织
        MapObject mapObject2 = new MapObject();
        mapObject2.setType("本组织");
        List<Double> scoreList2 = new ArrayList<>();

        //中位数
        MapObject mapObject3 = new MapObject();
        mapObject3.setType("中位数");
        List<Double> scoreList3 = new ArrayList<>();
        for (int i = 1; i <= month; i++) {
            String time = info.getYear() + "-";
            if (i < 10) { time += "0"; }
            time += i;
            Double score = orgScoreMapper.findOrgMonthScore(info.getOrganizationId(), time);
            scoreList2.add(null == score ? 0d : score);
            scoreList3.add(this.getMedian(time, 1));
        }
        mapObject2.setScoreList(scoreList2);
        annualScoreMap.add(mapObject2);

        mapObject3.setScoreList(scoreList3);
        annualScoreMap.add(mapObject3);

        info.setAnnualScoreMap(annualScoreMap);

        //星级
        Long parentId;
        if (info.getYear().equals(LocalDateTime.now().getYear())) {
            PartyOrgVo partyOrgVo = this.orgService.findPartyId(info.getOrganizationId());
            parentId = partyOrgVo.getPartyId();
        } else {
            OrgSnapshotEntity orgInfo = this.orgService.getHistoryOrgParty(info.getOrganizationId(), info.getYear(), null, info.getRegionId());
            parentId = orgInfo.getOrgId();
        }
        List<TotalScoreVo> totalScoreOrgVos = this.getTotalScoreVo(parentId, info.getYear(), 2);
        int size = totalScoreOrgVos.size();
        log.debug(info.getOrganizationId() + "的所属党委下共有" + size + "个支部");
        int avg = (0 == size) ? 0 : totalScoreOrgVos.stream().mapToInt(TotalScoreVo::getScore).sum()/size;

        for (int i = 0; i <= size*0.25-1; i++) {
            if (0 == size) { break; }
            if (totalScoreOrgVos.get(i).getId().equals(info.getOrganizationId())) {
                info.setStar(5);
            }
        }
        if (null == info.getStar()) {
            int actualScore = (null == totalScoreVo || null == totalScoreVo.getScore()) ? 0 : totalScoreVo.getScore();
            if (actualScore > avg) {
                info.setStar(4);
            } else if (actualScore > avg * 0.7) {
                info.setStar(3);
            } else if (actualScore > avg * 0.5) {
                info.setStar(2);
            } else {
                info.setStar(1);
            }
        }
        log.debug(info.getOrganizationId() + "的星级评定为" + info.getStar());

        //支部在本党委
        ScoreRanking scoreRanking = new ScoreRanking();
        ScoreRankingBase scoreRankingBase = new ScoreRankingBase();

        List<Integer> rankList = new ArrayList<>();
        List<Double> percentList = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            OrgSnapshotEntity historyOrgParty = orgService.getHistoryOrgParty(info.getOrganizationId(), info.getYear(), i, info.getRegionId());
            String time = info.getYear().toString() + "-";
            if (i < 10) { time += "0"; }
            time += i;
            int rank = 0;
            double percent = 0;
            try {
                while (redisTemplate.hasKey("BRANCH_RANKING_" + historyOrgParty.getOrgId()) && "1".equals(redisTemplate.opsForValue().get("BRANCH_RANKING_" + historyOrgParty.getOrgId()))) {}
                rank = Integer.parseInt(Objects.requireNonNull(redisTemplate.opsForHash().get("BRANCH_RANKING_" + historyOrgParty.getOrgId() + "_" + time, info.getOrganizationId().toString())).toString());
            } catch (NullPointerException e) {
                if (!redisTemplate.opsForHash().hasKey("BRANCH_RANKING_" + historyOrgParty.getOrgId() + "_" + time, info.getOrganizationId().toString())) {
                    rankComputeService.orgRanking(historyOrgParty.getOrgId(), info.getYear(), 1, time);
                }
                Object value = redisTemplate.opsForHash().get("BRANCH_RANKING_" + historyOrgParty.getOrgId() + "_" + time, info.getOrganizationId().toString());
                rank = (null == value) ? 0 : Integer.parseInt(value.toString());
            }
            int pSize = orgService.getHistoryPartyBranch(historyOrgParty.getOrgId(), time, true, null).size();
            percent = pSize == 0 ? 0 : (double) rank / pSize * 100;
            if (0d != percent) {
                BigDecimal bd = BigDecimal.valueOf(percent);
                bd = bd.setScale(2, RoundingMode.DOWN);
                percent = Double.parseDouble(bd.toString());
            }
            rankList.add(rank);
            percentList.add(percent);
        }
        scoreRankingBase.setRanking(rankList);
        scoreRankingBase.setPercent(percentList);
        scoreRanking.setCurrent(scoreRankingBase);

        //支部在本市
        ScoreRankingBase scoreRankingBaseAll = new ScoreRankingBase();

        List<Integer> allRankList = new ArrayList<>();
        List<Double> allPercentList = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            String time = info.getYear().toString() + "-";
            if (i < 10) { time += "0"; }
            time += i;
            int rank = 0;
            double percent = 0d;
            try {
                while (redisTemplate.hasKey("BRANCH_RANKING_" + 3) && "1".equals(redisTemplate.opsForValue().get("BRANCH_RANKING_" + 3))) {}
                rank = Integer.parseInt(Objects.requireNonNull(redisTemplate.opsForHash().get("BRANCH_RANKING_" + 3 + "_" + time, info.getOrganizationId().toString())).toString());
            } catch (NullPointerException e) {
                if (!redisTemplate.opsForHash().hasKey("BRANCH_RANKING_" + 3 + "_" + time, info.getOrganizationId().toString())) {
                    rankComputeService.orgRanking(3L, info.getYear(), 1, time);
                }
                Object value = redisTemplate.opsForHash().get("BRANCH_RANKING_" + 3 + "_" + time, info.getOrganizationId().toString());
                rank = (null == value) ? 0 : Integer.parseInt(value.toString());
            }
            int pSize = orgService.getHistoryPartyBranch(3L, time, true, null).size();
            percent = pSize == 0 ? 0 : (double) rank / pSize * 100;
            if (0d != percent) {
                BigDecimal bd = BigDecimal.valueOf(percent);
                bd = bd.setScale(2, RoundingMode.DOWN);
                percent = Double.parseDouble(bd.toString());
            }
            allRankList.add(rank);
            allPercentList.add(percent);
        }
        scoreRankingBaseAll.setRanking(allRankList);
        scoreRankingBaseAll.setPercent(allPercentList);
        scoreRanking.setAll(scoreRankingBaseAll);
        info.setScoreRanking(scoreRanking);
        return info;
    }

    @Override
    public PartyCommitteeInfo buildPartyCommittee(PartyCommitteeInfo info) {
        Region.OrgData orgData = new Region.OrgData(); //= applicationConfigHelper.getOrgByRegionId(info.getRegionId());
        orgData.setOrgId(3L);
        //综合评分
        TotalScoreVo totalScoreVo = orgScoreMapper.findSingleOrgTotal(info.getOrganizationId(), info.getYear());
        info.setSumScore((null == totalScoreVo || null == totalScoreVo.getScore()) ? 0.0 : computeService.conversion(1L, totalScoreVo.getScore().doubleValue(), info.getYear()));
        //雷达图
        List<RadarChartMap> radarChartMaps = new ArrayList<>();
        for (int i = 1; i <= 5; i++) {
            RadarChartMap radarChartMap = new RadarChartMap();
            switch (i) {
                case 1:
                    radarChartMap.setName("基础信息完整度");
                    Integer tmp = autoScoreMapper.findOrgRuleScore("基础信息完整度", info.getYear(), info.getOrganizationId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(1L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(1L, 192d, info.getYear()));
                    break;
                case 2:
                    radarChartMap.setName("基础工作完成度");
                    tmp = autoScoreMapper.findOrgRuleScore("基础工作", info.getYear(), info.getOrganizationId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(1L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(1L, 597d, info.getYear()));
                    break;
                case 3:
                    radarChartMap.setName("组织建设规范度");
                    tmp = autoScoreMapper.findOrgRuleScore("组织建设", info.getYear(), info.getOrganizationId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(1L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(1L, 300d, info.getYear()));
                    break;
                case 4:
                    radarChartMap.setName("党务工作活跃度");
                    tmp = autoScoreMapper.findOrgRuleScore("活跃程度", info.getYear(), info.getOrganizationId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(1L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(1L, 240d, info.getYear()));
                    break;
                default:
                    radarChartMap.setName("党建工作认可度");
                    tmp = autoScoreMapper.findOrgRuleScore("优秀组织", info.getYear(), info.getOrganizationId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(1L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(1L, 5d, info.getYear()));
                    break;
            }
            radarChartMaps.add(radarChartMap);
        }
        info.setRadarChartMap(radarChartMaps);
        //评分情况饼图
        List<Long> orgIdList = new ArrayList<>();
        orgIdList.add(info.getOrganizationId());
        if (info.getYear() < new DateTime().getYear()) {
            orgIdList = orgService.getHistoryPartyBranch(info.getOrganizationId(), info.getYear(), true, null).stream().map(OrgSnapshotEntity::getOrgId).collect(Collectors.toList());
        } else {
            orgIdList = orgService.getPartyBranch(info.getOrganizationId(), null, true).stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toList());
        }
        List<GrandMap> res = new ArrayList<>();

        List<TotalScoreVo> totalScoreVos = new ArrayList<>();
        if (0 != orgIdList.size()) {
            totalScoreVos = orgScoreMapper.findOrgTotalScore(new HashSet<>(orgIdList), info.getYear());
        }

        GrandMap grandMap1 = new GrandMap();
        grandMap1.setName("95分以上的组织");

        GrandMap grandMap2 = new GrandMap();
        grandMap2.setName("90-95分的组织");

        GrandMap grandMap3 = new GrandMap();
        grandMap3.setName("90分以下的组织");

        GrandMap grandMap4 = new GrandMap();
        grandMap4.setName("未评分的组织");

        List<DssOrgChild> dssOrgChildLists1 = new ArrayList<>();
        List<DssOrgChild> dssOrgChildLists2 = new ArrayList<>();
        List<DssOrgChild> dssOrgChildLists3 = new ArrayList<>();
        List<DssOrgChild> dssOrgChildLists4 = new ArrayList<>();

        for (TotalScoreVo tScoreVo : totalScoreVos) {

            Double score = computeService.conversion(1L, tScoreVo.getScore().doubleValue(), info.getYear());

            if (score >= 95.0) {
                dssOrgChildLists1.add(DssOrgChild.builder().orgId(tScoreVo.getId()).build());
            }

            else if (score >= 90.0 && score < 95.0) {
                dssOrgChildLists2.add(DssOrgChild.builder().orgId(tScoreVo.getId()).build());
            }

            else if (score < 90.0) {
                dssOrgChildLists3.add(DssOrgChild.builder().orgId(tScoreVo.getId()).build());
            }

            else {
                dssOrgChildLists4.add(DssOrgChild.builder().orgId(tScoreVo.getId()).build());
            }
        }

        grandMap1.setOrgList(dssOrgChildLists1);
        grandMap1.setValue(dssOrgChildLists1.size());
        grandMap2.setOrgList(dssOrgChildLists2);
        grandMap2.setValue(dssOrgChildLists2.size());
        grandMap3.setOrgList(dssOrgChildLists3);
        grandMap3.setValue(dssOrgChildLists3.size());
        grandMap4.setOrgList(dssOrgChildLists4);
        grandMap4.setValue(dssOrgChildLists4.size());

        res.add(grandMap1);
        res.add(grandMap2);
        res.add(grandMap3);
        res.add(grandMap4);

        info.setGrandMap(res);

        List<MapObject> annualScoreMap = new ArrayList<>();
        //满分
        MapObject mapObject1 = new MapObject();
        mapObject1.setType("满分");
        List<Double> scoreList1 = new ArrayList<>();
        int month = 12;
        if (info.getYear().equals(LocalDateTime.now().getYear())) {
            month = LocalDateTime.now().getMonthOfYear() - 1;
        }
        for (int i = 0; i < month; i++) {
            scoreList1.add(Double.parseDouble(scoreConfig.getTotalScore().getOrg().split(",")[i]));
        }
        mapObject1.setScoreList(scoreList1);
        annualScoreMap.add(mapObject1);

        //本组织
        MapObject mapObject2 = new MapObject();
        mapObject2.setType("本组织");
        List<Double> scoreList2 = new ArrayList<>();

        //中位数
        MapObject mapObject3 = new MapObject();
        mapObject3.setType("中位数");
        List<Double> scoreList3 = new ArrayList<>();

        for (int i = 1; i <= month; i++) {
            String time = info.getYear() + "-";
            if (i < 10) { time += "0"; }
            time += i;
            Double score = orgScoreMapper.findOrgMonthScore(info.getOrganizationId(), time);
            scoreList2.add(null == score ? 0d : score);
            scoreList3.add(this.getMedian(time, 1));
        }
        mapObject2.setScoreList(scoreList2);
        annualScoreMap.add(mapObject2);

        mapObject3.setScoreList(scoreList3);
        annualScoreMap.add(mapObject3);

        info.setAnnualScoreMap(annualScoreMap);

        //星级
        Long parentId;
        if (info.getYear().equals(LocalDateTime.now().getYear())) {
            OrganizationEntity org = this.orgService.getById(info.getOrganizationId());
            parentId = org.getParentId();
        } else {
            OrgSnapshotEntity orgInfo = this.orgService.getHistoryOrgInfo(info.getOrganizationId(), info.getYear(), null);
            parentId = orgInfo.getOrgPid();
        }
        if(orgData.getOrgId().equals(parentId)) {
            List<TotalScoreVo> totalScoreOrgVos = this.getTotalScoreVo(orgData.getOrgId(), info.getYear(), 1);
            int size = totalScoreOrgVos.size();
            int avg = (0 == size) ? 0 : totalScoreOrgVos.stream().mapToInt(TotalScoreVo::getScore).sum()/size;

            for (int i = 0; i <= size*0.25-1; i++) {
                if (totalScoreOrgVos.get(i).getId().equals(info.getOrganizationId())) {
                    info.setStar(5);
                }
            }
            if (null == info.getStar()) {
                int actualScore = (null == totalScoreVo || null == totalScoreVo.getScore()) ? 0 : totalScoreVo.getScore();
                if (actualScore > avg) {
                    info.setStar(4);
                } else if (actualScore > avg * 0.7) {
                    info.setStar(3);
                } else if (actualScore > avg * 0.5) {
                    info.setStar(2);
                } else {
                    info.setStar(1);
                }
            }
        } else {
            info.setStar(0);
        }

        //党委在全市
        ScoreRankingBase scoreRankingBaseAll = new ScoreRankingBase();

        List<Integer> allRankList = new ArrayList<>();
        List<Double> allPercentList = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            String time = info.getYear().toString() + "-";
            if (i < 10) { time += "0"; }
            time += i;
            int rank = 0;
            double percent = 0;
            try {
                while (redisTemplate.hasKey("PARTY_RANKING_" + 3) && "1".equals(redisTemplate.opsForValue().get("PARTY_RANKING_" + 3))) {}
                rank = Integer.parseInt(Objects.requireNonNull(redisTemplate.opsForHash().get("PARTY_RANKING_" + 3 + "_" + time, info.getOrganizationId().toString())).toString());
            } catch (NullPointerException e) {
                if (!redisTemplate.opsForHash().hasKey("PARTY_RANKING_" + 3 + "_" + time, info.getOrganizationId().toString())) {
                    rankComputeService.orgRanking(3L, info.getYear(), 2, time);
                }
                Object value = redisTemplate.opsForHash().get("PARTY_RANKING_" + 3 + "_" + time, info.getOrganizationId().toString());
                rank = (null == value) ? 0 : Integer.parseInt(value.toString());
            }
            int allSize = orgService.getHistoryOfficeCommittee(3L, time).size();
            percent = allSize == 0 ? 0 : (double) rank / allSize * 100 ;
            if (0d != percent) {
                BigDecimal bd = BigDecimal.valueOf(percent);
                bd = bd.setScale(2, RoundingMode.DOWN);
                percent = Double.parseDouble(bd.toString());
            }
            allRankList.add(rank);
            allPercentList.add(percent);
        }
        scoreRankingBaseAll.setRanking(allRankList);
        scoreRankingBaseAll.setPercent(allPercentList);
        info.setScoreRanking(scoreRankingBaseAll);
        return info;
    }

    @Override
    public UserInfo buildUser(UserInfo info) {
        //综合评分
        TotalScoreVo totalScoreVo = userScoreMapper.findSingleUserTotal(info.getUserId(), info.getYear());
        info.setCompositeScore((null == totalScoreVo || null == totalScoreVo.getScore()) ? 0.0 : computeService.conversion(2L, totalScoreVo.getScore().doubleValue(), info.getYear()));
        //雷达图
        List<RadarChartMap> radarChartMaps = new ArrayList<>();
        for (int i = 1; i <= 5; i++) {
            RadarChartMap radarChartMap = new RadarChartMap();
            switch (i) {
                case 1:
                    radarChartMap.setName("基础信息完整度");
                    Integer tmp = autoScoreMapper.findUserRuleScore("基础工作", info.getYear(), info.getUserId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(2L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(2L, 664d, info.getYear()));
                    break;
                case 2:
                    radarChartMap.setName("党务工作活跃度");
                    tmp = autoScoreMapper.activeLevel(info.getYear(), info.getUserId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(2L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(2L, 240d, info.getYear()));
                    break;
                case 3:
                    radarChartMap.setName("个人综合认可度");
                    tmp = autoScoreMapper.findUserRuleScore("优秀党员", info.getYear(), info.getUserId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(2L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(2L, 5d, info.getYear()));
                    break;
                case 4:
                    radarChartMap.setName("服务群众参与度");
                    tmp = autoScoreMapper.volunteerLearn("志愿者", info.getYear(), info.getUserId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(2L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(2L, 60d, info.getYear()));
                    break;
                default:
                    radarChartMap.setName("理论学习勤奋度");
                    tmp = autoScoreMapper.volunteerLearn("学习", info.getYear(), info.getUserId());
                    radarChartMap.setValue(null == tmp ? 0.0 : computeService.conversion(2L, tmp.doubleValue(), info.getYear()));
                    radarChartMap.setFullMarks(computeService.conversion(2L, 60d, info.getYear()));
                    break;
            }
            radarChartMaps.add(radarChartMap);
        }
        info.setRadarChartMap(radarChartMaps);

        List<MapObject> annualScoreMap = new ArrayList<>();
        //满分
        MapObject mapObject1 = new MapObject();
        mapObject1.setType("满分");
        List<Double> scoreList1 = new ArrayList<>();
        int month = 12;
        if (info.getYear().equals(LocalDateTime.now().getYear())) {
            month = LocalDateTime.now().getMonthOfYear() - 1;
        }
        for (int i = 0; i < month; i++) {
            scoreList1.add(Double.parseDouble(scoreConfig.getTotalScore().getUser().split(",")[i]));
        }
        mapObject1.setScoreList(scoreList1);
        annualScoreMap.add(mapObject1);

        //本人得分
        MapObject mapObject2 = new MapObject();
        mapObject2.setType("本人得分");
        List<Double> scoreList2 = new ArrayList<>();

        //中位数
        MapObject mapObject3 = new MapObject();
        mapObject3.setType("中位数");
        List<Double> scoreList3 = new ArrayList<>();

        for (int i = 1; i <= month; i++) {
            String time = info.getYear() + "-";
            if (i < 10) { time += "0"; }
            time += i;
            Double score = userScoreMapper.findUserMonthScore(info.getUserId(), time);
            scoreList2.add(null == score ? 0d : score);
            scoreList3.add(this.getMedian(time, 2));
        }
        mapObject2.setScoreList(scoreList2);
        annualScoreMap.add(mapObject2);

        mapObject3.setScoreList(scoreList3);
        annualScoreMap.add(mapObject3);

        info.setAnnualScoreMap(annualScoreMap);

        //星级
        List<TotalScoreVo> totalScoreUserVos = this.getTotalScoreVo(info.getRegionId(), info.getYear(), 3);
        int size = totalScoreUserVos.size();
        int avg = (0 == size) ? 0 : totalScoreUserVos.stream().mapToInt(TotalScoreVo::getScore).sum()/size;

        for (int i = 0; i <= size/3-1; i++) {
            if (0 == size) { break; }
            if (totalScoreUserVos.get(i).getId().equals(info.getUserId())) {
                info.setStar(5);
            }
        }
        if (null == info.getStar()) {
            int actualScore = (null == totalScoreVo || null == totalScoreVo.getScore()) ? 0 : totalScoreVo.getScore();
            if (actualScore > avg) {
                info.setStar(4);
            } else if (actualScore > avg * 0.7) {
                info.setStar(3);
            } else if (actualScore > avg * 0.5) {
                info.setStar(2);
            } else {
                info.setStar(1);
            }
        }

        ScoreRanking scoreRanking = new ScoreRanking();
        //人员在本支部
        ScoreRankingBase scoreRankingBase = new ScoreRankingBase();

        List<Integer> rankList = new ArrayList<>();
        List<Double> percentList = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            UserSnapshotEntity historyUserInfo = userService.getHistoryUserInfo(info.getUserId(), info.getYear(), i);
            String time = info.getYear().toString() + "-";
            if (i < 10) { time += "0"; }
            time += i;
            int rank = 0;
            double percent = 0;
            try {
                while (redisTemplate.hasKey("USER_BRANCH_RANKING_" + historyUserInfo.getOrgId()) && "1".equals(redisTemplate.opsForValue().get("USER_BRANCH_RANKING_" + historyUserInfo.getOrgId()))) {}
                rank = Integer.parseInt(Objects.requireNonNull(redisTemplate.opsForHash().get("USER_BRANCH_RANKING_" + historyUserInfo.getOrgId() + "_" + time, info.getUserId().toString())).toString());
            } catch (NullPointerException e) {
                if (!redisTemplate.opsForHash().hasKey("USER_BRANCH_RANKING_" + historyUserInfo.getOrgId() + "_" + time, info.getUserId().toString())) {
                    rankComputeService.userRanking(historyUserInfo.getOrgId(), info.getYear(), 1, time);
                }
                Object value = redisTemplate.opsForHash().get("USER_BRANCH_RANKING_" + historyUserInfo.getOrgId() + "_" + time, info.getUserId().toString());
                rank = (null == value) ? 0 : Integer.parseInt(value.toString());
            }
            int uSize = userService.getHistoryUserListByOrg(historyUserInfo.getOrgId(), time).size();
            percent = uSize == 0 ? 0 : (double) rank / uSize * 100;
            if (0d != percent) {
                BigDecimal bd = BigDecimal.valueOf(percent);
                bd = bd.setScale(2, RoundingMode.DOWN);
                percent = Double.parseDouble(bd.toString());
            }
            rankList.add(rank);
            percentList.add(percent);
        }
        scoreRankingBase.setRanking(rankList);
        scoreRankingBase.setPercent(percentList);
        scoreRanking.setCurrent(scoreRankingBase);

        //人员在本党委
        ScoreRankingBase scoreRankingBaseAll = new ScoreRankingBase();

        List<Integer> allRankList = new ArrayList<>();
        List<Double> allPercentList = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            String time = info.getYear().toString() + "-";
            if (i < 10) { time += "0"; }
            time += i;
            int rank = 0;
            double percent = 0;
            //查询所属党委id
            UserSnapshotEntity historyUserInfo = userService.getHistoryUserInfo(info.getUserId(), info.getYear(), i);
            if (null == historyUserInfo || null == historyUserInfo.getOrgId()) {
                continue;
            }
            OrgSnapshotEntity historyOrgParty = orgService.getHistoryOrgParty(historyUserInfo.getOrgId(), info.getYear(), i, info.getRegionId());
            if (null == historyOrgParty || null == historyOrgParty.getOrgId()) {
                continue;
            }
            try {
                while (redisTemplate.hasKey("USER_PARTY_RANKING_" + historyOrgParty.getOrgId()) && "1".equals(redisTemplate.opsForValue().get("USER_PARTY_RANKING_" + historyOrgParty.getOrgId()))) {}
                rank = Integer.parseInt(Objects.requireNonNull(redisTemplate.opsForHash().get("USER_PARTY_RANKING_" + historyOrgParty.getOrgId() + "_" + time, info.getUserId().toString())).toString());
            } catch (NullPointerException e) {
                if (!redisTemplate.opsForHash().hasKey("USER_PARTY_RANKING_" + historyOrgParty.getOrgId() + "_" + time, info.getUserId().toString())) {
                    rankComputeService.userRanking(historyOrgParty.getOrgId(), info.getYear(), 2, time);
                }
                Object value = redisTemplate.opsForHash().get("USER_PARTY_RANKING_" + historyOrgParty.getOrgId() + "_" + time, info.getUserId().toString());
                rank = (null == value) ? 0 : Integer.parseInt(value.toString());
            }
            int allSize = userService.getHistoryUserListByOrg(historyOrgParty.getOrgId(), time).size();
            percent = allSize == 0 ? 0 : (double) rank / allSize * 100;
            if (0d != percent) {
                BigDecimal bd = BigDecimal.valueOf(percent);
                bd = bd.setScale(2, RoundingMode.DOWN);
                percent = Double.parseDouble(bd.toString());
            }
            allRankList.add(rank);
            allPercentList.add(percent);
        }
        scoreRankingBaseAll.setRanking(allRankList);
        scoreRankingBaseAll.setPercent(allPercentList);
        scoreRanking.setAll(scoreRankingBaseAll);
        info.setScoreRanking(scoreRanking);
        return info;
    }

    /**
     * 获取中位数
     * @param time
     * @param type  1-组织， 2-人员
     * @return
     */
    private Double getMedian(String time, int type) {
        double median;
        if (type == 1) {
            if(this.redisTemplate.opsForHash().hasKey(ORG_SCORE_MEDIAN,time)) {
                median =  Double.parseDouble(Objects.requireNonNull(this.redisTemplate.opsForHash().get(ORG_SCORE_MEDIAN, time)).toString());
            } else {
                List<Double> doubleList = orgScoreMapper.forMedian(time);
                median = CollectionUtils.isEmpty(doubleList) ? 0d : doubleList.get(doubleList.size()/2);
                this.redisTemplate.opsForHash().put(ORG_SCORE_MEDIAN, time, Double.toString(median));
                this.redisTemplate.expire(ORG_SCORE_MEDIAN, 30, TimeUnit.MINUTES);
            }
        } else {
            if(this.redisTemplate.opsForHash().hasKey(USER_SCORE_MEDIAN,time)) {
                median =  Double.parseDouble(Objects.requireNonNull(this.redisTemplate.opsForHash().get(USER_SCORE_MEDIAN, time)).toString());
            } else {
                List<Double> doubleList = userScoreMapper.forMedian(time);
                median = CollectionUtils.isEmpty(doubleList) ? 0d : doubleList.get(doubleList.size()/2);
                this.redisTemplate.opsForHash().put(USER_SCORE_MEDIAN, time, Double.toString(median));
                this.redisTemplate.expire(USER_SCORE_MEDIAN, 30, TimeUnit.MINUTES);
            }
        }
        return median;
    }

    private List<TotalScoreVo> getTotalScoreVo(Long id, int year, int type) {
        List<TotalScoreVo> totalScoreVos = new ArrayList<>();
        if (type == 1) {
            String redisKey = ORG_TOTAL_SCORE + year + "_" + id;
            if(this.redisTemplate.hasKey(redisKey)) {
                String json = this.redisTemplate.opsForValue().get(redisKey);
                totalScoreVos = (List<TotalScoreVo>) JsonUtils.fromJson(json, ArrayList.class, TotalScoreVo.class);
            } else {
                Set<Long> officeIdList;
                if (year < new DateTime().getYear()) {
                    officeIdList = orgService.getOfficeCommittee(id, year, 12);
                } else {
                    officeIdList = orgService.getOfficeCommittee(id, year, LocalDateTime.now().getMonthOfYear());
                }
                if (0 != officeIdList.size()) {
                    totalScoreVos = orgScoreMapper.findOrgTotalScore(officeIdList, year);
                }
                this.redisTemplate.opsForValue().set(redisKey, JsonUtils.toJson(totalScoreVos), 30, TimeUnit.MINUTES);
            }
        } else if(type == 2){
            String redisKey = ORG_TOTAL_SCORE + year + "_" + id;
            if(this.redisTemplate.hasKey(redisKey)) {
                String json = this.redisTemplate.opsForValue().get(redisKey);
                totalScoreVos = (List<TotalScoreVo>) JsonUtils.fromJson(json, ArrayList.class, TotalScoreVo.class);
            } else {
                List<Long> orgIdList = new ArrayList<>();
                if (year < new DateTime().getYear()) {
                    orgIdList = orgService.getHistoryPartyBranch(id, year, true, null).stream().map(OrgSnapshotEntity::getOrgId).collect(Collectors.toList());
                } else {
                    orgIdList = orgService.getPartyBranch(id, null, true).stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toList());
                }
                if (0 != orgIdList.size()) {
                    totalScoreVos = orgScoreMapper.findOrgTotalScore(new HashSet<>(orgIdList), year);
                }
                this.redisTemplate.opsForValue().set(redisKey, JsonUtils.toJson(totalScoreVos), 30, TimeUnit.MINUTES);
            }
        } else {
            String redisKey = USER_TOTAL_SCORE + year + "_" + id;
            if(this.redisTemplate.hasKey(redisKey)) {
                String json = this.redisTemplate.opsForValue().get(redisKey);
                totalScoreVos = (List<TotalScoreVo>) JsonUtils.fromJson(json, ArrayList.class, TotalScoreVo.class);
            } else {
                Region.OrgData orgData = applicationConfigHelper.getOrgByRegionId(id);
                List<Long> userIdList;
                if (year < new DateTime().getYear()) {
                    List<UserSnapshotEntity> historyUserList = userService.getHistoryUserListByOrg(orgData.getOrgId(), year);
                    userIdList = historyUserList.stream().map(UserSnapshotEntity::getUserId).collect(Collectors.toList());
                } else {
                    List<UserEntity> userInfoBaseList = userMapper.findUserList(orgData.getOrgId(), 1, this.excludeOrgConfig.getOrgIds(), this.orgTypeConfig.getNoStatisticsChild(), null);
                    userIdList = userInfoBaseList.stream().map(UserEntity::getUserId).collect(Collectors.toList());
                }
                totalScoreVos = userScoreMapper.findUserTotalScore(userIdList, year);
                this.redisTemplate.opsForValue().set(redisKey, JsonUtils.toJson(totalScoreVos), 30, TimeUnit.MINUTES);
            }
        }
        return totalScoreVos;
    }
}