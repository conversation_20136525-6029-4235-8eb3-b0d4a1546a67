package com.goodsogood.ows.service.autoScore;

import com.goodsogood.ows.configuration.ScoreConfig;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.stream.IntStream;

/**
 * Create by FuXiao on 2020/11/18
 */
@Service
@Log4j2
public class ComputeService {
    private final ScoreConfig scoreConfig;

    public ComputeService(ScoreConfig scoreConfig) {
        this.scoreConfig = scoreConfig;
    }

    /**
     * 计算组织或人员自动打分时的满分
     *
     * @param topId 1组织 2人员
     * @return
     */
    public Double computeTotalScore(Long topId, int year) {
        String splitTmp = "";
        if (topId == 1L) {
            splitTmp = scoreConfig.getTotalScore().getOrg();
        } else {
            splitTmp = scoreConfig.getTotalScore().getUser();
        }
        double res;
        String[] tmp = splitTmp.split(",");
        int month = 12;
        if (year == new DateTime().getYear()) {
            month = new DateTime().getMonthOfYear() - 1;
        }
        res = IntStream.range(0, month).mapToDouble(i -> Double.parseDouble(tmp[i])).sum();
        return res;
    }

    /**
     * 将实际分数转换为百分制 保留两位小数
     *
     * @param topId 1组织 2人员
     * @param score
     * @return
     */
    public Double conversion(Long topId, Double score, int year) {
        double res = score / computeTotalScore(topId, year) * 100;
        BigDecimal b = BigDecimal.valueOf(res);
        return b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }
}
