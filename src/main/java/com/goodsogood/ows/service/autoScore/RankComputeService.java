package com.goodsogood.ows.service.autoScore;

import com.goodsogood.ows.mapper.rank.OrgScoreMapper;
import com.goodsogood.ows.mapper.rank.UserScoreMapper;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import com.goodsogood.ows.model.vo.rank.TotalScoreVo;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Create by FuXiao on 2021/1/12
 */
@Log4j2
@Service
public class RankComputeService {
    private final OrgService orgService;
    private final UserService userService;
    private final StringRedisTemplate redisTemplate;
    private final OrgScoreMapper orgScoreMapper;
    private final UserScoreMapper userScoreMapper;

    public RankComputeService(OrgService orgService, UserService userService, StringRedisTemplate redisTemplate, OrgScoreMapper orgScoreMapper, UserScoreMapper userScoreMapper) {
        this.orgService = orgService;
        this.userService = userService;
        this.redisTemplate = redisTemplate;
        this.orgScoreMapper = orgScoreMapper;
        this.userScoreMapper = userScoreMapper;
    }

    /**
     * 查询组织12个月的排名
     * @param parentId
     * @param year
     * @param type 1支部 2党委
     */
    public void orgRanking(Long parentId, Integer year, Integer type, String time) {
        List<OrganizationEntity> list = new ArrayList<>();
        List<OrgSnapshotEntity> hList = new ArrayList<>();
        String key = "";
        if (1 == type) {
            key = "BRANCH_RANKING_";
            //用SETNX实现分布式锁
            redisTemplate.opsForValue().increment(key + parentId, 1);
            redisTemplate.expire(key + parentId, 5, TimeUnit.MINUTES);
            if (year < new DateTime().getYear()) {
                hList = orgService.getHistoryPartyBranch(parentId, time, true, null);
            } else {
                list = orgService.getPartyBranch(parentId, null, true);
            }
        } else {
            key = "PARTY_RANKING_";
            //用SETNX实现分布式锁
            redisTemplate.opsForValue().increment(key + parentId, 1);
            redisTemplate.expire(key + parentId, 5, TimeUnit.MINUTES);
            if (year < new DateTime().getYear()) {
                hList = orgService.getHistoryOfficeCommittee(3L, time);
            } else {
                list = orgService.getOfficeCommittee(3L);
            }
        }
        Set<Long> orgIdList = new HashSet<>();
        if (year < new DateTime().getYear()) {
            orgIdList = hList.stream().map(OrgSnapshotEntity::getOrgId).collect(Collectors.toSet());
        } else {
            orgIdList = list.stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toSet());
        }
        if (0 == orgIdList.size()) return;
        List<TotalScoreVo> orgRanking = orgScoreMapper.findOrgRanking(orgIdList, time);
        int rank = 1, count = 1;
        int score = 0;
        for (TotalScoreVo vo : orgRanking) {
            if (score != vo.getScore()) {
                score = vo.getScore();
                rank = count;
            }
            if (!redisTemplate.opsForHash().hasKey(key + parentId + "_" + time, vo.getId().toString())) {
                redisTemplate.opsForHash().put(key + parentId + "_" + time, vo.getId().toString(), String.valueOf(rank));
                redisTemplate.expire(key + parentId + "_" + time, 1, TimeUnit.DAYS);
            }
            count++;
        }
        //用SETNX实现分布式锁
        redisTemplate.opsForValue().decrement(key + parentId, 1);
        redisTemplate.expire(key + parentId, 5, TimeUnit.MINUTES);
    }

    /**
     * 查询人员12个月的排名
     * @param orgId
     * @param year
     * @param type 1支部 2党委
     */
    public void userRanking(Long orgId, Integer year, Integer type, String time) {
        String key = "";
        if (1 == type) { key = "USER_BRANCH_RANKING_"; }
        else { key = "USER_PARTY_RANKING_"; }
        //用SETNX实现分布式锁
        redisTemplate.opsForValue().increment(key + orgId, 1);
        redisTemplate.expire(key + orgId, 5, TimeUnit.MINUTES);
        List<UserEntity> list = new ArrayList<>();
        List<UserSnapshotEntity> hList = new ArrayList<>();
        if (year < new DateTime().getYear()) {
            hList = userService.getHistoryUserListByOrg(orgId, time);
        } else {
            list = userService.getUserByOrg(orgId);
        }
        Set<Long> userIdList = new HashSet<>();
        if (year < new DateTime().getYear()) {
            userIdList = hList.stream().map(UserSnapshotEntity::getUserId).collect(Collectors.toSet());
        } else {
            userIdList = list.stream().map(UserEntity::getUserId).collect(Collectors.toSet());
        }
        if (0 == userIdList.size()) return;
        List<TotalScoreVo> userRanking = userScoreMapper.findUserRanking(userIdList, time);
        int rank = 1, count = 1;
        int score = 0;
        for (TotalScoreVo vo : userRanking) {
            if (score != vo.getScore()) {
                score = vo.getScore();
                rank = count;
            }
            if (!redisTemplate.opsForHash().hasKey(key + orgId + "_" + time, vo.getId().toString())) {
                redisTemplate.opsForHash().put(key + orgId + "_" + time, vo.getId().toString(), String.valueOf(rank));
                redisTemplate.expire(key + orgId + "_" + time, 1, TimeUnit.DAYS);
            }
            count++;
        }
        redisTemplate.opsForValue().decrement(key + orgId, 1);
        redisTemplate.expire(key + orgId, 5, TimeUnit.MINUTES);
    }
}
