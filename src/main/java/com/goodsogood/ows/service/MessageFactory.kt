package com.goodsogood.ows.service

import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.configuration.Global
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.model.vo.Result
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentMap

@Component
class MessageFactory(@Autowired val errors: Errors,
                     @Autowired val messageMap: Map<String, IMessageService> = ConcurrentHashMap()) {

    fun getMessage(component: String): IMessageService {
        return messageMap[component] ?: throw ApiException("工厂类找不到${component}", Result<Any>(errors, Global.Errors.NOT_FOUND, HttpStatus.OK.value(), component))
    }
}