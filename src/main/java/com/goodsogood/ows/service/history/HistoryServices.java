package com.goodsogood.ows.service.history;

import com.goodsogood.ows.configuration.PullDataConfig;
import com.goodsogood.ows.mapper.history.*;
import com.goodsogood.ows.mapper.pull.*;
import com.goodsogood.ows.model.db.history.*;
import com.goodsogood.ows.utils.ListUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
@Log4j2
public class HistoryServices {
    private  final LphBalanceLogMapper lphBalanceLogMapper;
    private final LphBalanceLogPullMapper lphBalanceLogPullMapper;
    private final LphCircuitDetailMapper lphCircuitDetailMapper;
    private final LphCircuitDetailPullMapper lphCircuitDetailPullMapper;
    private final LphCircuitPullMapper lphCircuitPullMapper;
    private final LphCircuitMapper lphCircuitMapper;
    private final LphLineStatusMapper lphLineStatusMapper;
    private final LphLineStatusPullMapper lphLineStatusPullMapper ;
    private final LphMedalMapper lphMedalMapper;
    private final LphMedalPullMapper lphMedalPullMapper;
    private final LphPrizeMapper lphPrizeMapper;
    private final LphPrizePullMapper lphPrizePullMapper;
    private final LphUserMapper lphUserMapper;
    private final LphUserPullMapper lphUserPullMapper;
    private final LphUserBasicInfoMapper lphUserBasicInfoMapper;
    private final LphUserBasicInfoPullMapper lphUserBasicInfoPullMapper;
    private final LphUserInformationMapper lphUserInformationMapper;
    private final LphUserInformationPullMapper lphUserInformationPullMapper;
    private final LphUserLevelMapper lphUserLevelMapper;
    private final LphUserLevelPullMapper lphUserLevelPullMapper;
    private final LphUserLineStatusMapper lphUserLineStatusMapper;
    private final LphUserLineStatusPullMapper lphUserLineStatusPullMapper;
    private final LphUserLoginLogMapper lphUserLoginLogMapper;
    private final LphUserLoginLogPullMapper lphUserLoginLogPullMapper;
    private final LphUserPrizeMapper lphUserPrizeMapper;
    private final LphUserPrizePullMapper lphUserPrizePullMapper;
    private final LphUserPrizeAwardMapper lphUserPrizeAwardMapper;
    private final LphUserPrizeAwardPullMapper lphUserPrizeAwardPullMapper;
    private final LphUserRelationMapper lphUserRelationMapper;
    private final LphUserRelationPullMapper lphUserRelationPullMapper;
    private final LphUserSourceMapper lphUserSourceMapper;
    private final LphUserSourcePullMapper lphUserSourcePullMapper;
    private final LphUserStepMapper lphUserStepMapper;
    private final LphUserStepPullMapper lphUserStepPullMapper;
    private final LphUserTaskMapper lphUserTaskMapper;
    private final LphUserTaskPullMapper lphUserTaskPullMapper;
    private final LphReportMapper lphReportMapper;
    private final LphReportPullMapper lphReportPullMapper;
    private final PullDataConfig pullDataConfig;

    public HistoryServices(LphBalanceLogMapper lphBalanceLogMapper,
                           LphBalanceLogPullMapper lphBalanceLogPullMapper,
                           LphCircuitDetailMapper lphCircuitDetailMapper,
                           LphCircuitDetailPullMapper lphCircuitDetailPullMapper,
                           LphCircuitPullMapper lphCircuitPullMapper,
                           LphCircuitMapper lphCircuitMapper,
                           LphLineStatusMapper lphLineStatusMapper,
                           LphLineStatusPullMapper lphLineStatusPullMapper,
                           LphMedalMapper lphMedalMapper,
                           LphMedalPullMapper lphMedalPullMapper,
                           LphPrizeMapper lphPrizeMapper,
                           LphPrizePullMapper lphPrizePullMapper,
                           LphUserMapper lphUserMapper,
                           LphUserPullMapper lphUserPullMapper,
                           LphUserBasicInfoMapper lphUserBasicInfoMapper,
                           LphUserBasicInfoPullMapper lphUserBasicInfoPullMapper,
                           LphUserInformationMapper lphUserInformationMapper,
                           LphUserInformationPullMapper lphUserInformationPullMapper,
                           LphUserLevelMapper lphUserLevelMapper,
                           LphUserLevelPullMapper lphUserLevelPullMapper,
                           LphUserLineStatusMapper lphUserLineStatusMapper,
                           LphUserLineStatusPullMapper lphUserLineStatusPullMapper,
                           LphUserLoginLogMapper lphUserLoginLogMapper,
                           LphUserLoginLogPullMapper lphUserLoginLogPullMapper,
                           LphUserPrizeMapper lphUserPrizeMapper,
                           LphUserPrizePullMapper lphUserPrizePullMapper,
                           LphUserPrizeAwardMapper lphUserPrizeAwardMapper,
                           LphUserPrizeAwardPullMapper lphUserPrizeAwardPullMapper,
                           LphUserRelationMapper lphUserRelationMapper,
                           LphUserRelationPullMapper lphUserRelationPullMapper,
                           LphUserSourceMapper lphUserSourceMapper,
                           LphUserSourcePullMapper lphUserSourcePullMapper,
                           LphUserStepMapper lphUserStepMapper,
                           LphUserStepPullMapper lphUserStepPullMapper,
                           LphUserTaskMapper lphUserTaskMapper,
                           LphUserTaskPullMapper lphUserTaskPullMapper,
                           LphReportMapper lphReportMapper,
                           LphReportPullMapper lphReportPullMapper,
                           PullDataConfig pullDataConfig) {
        this.lphBalanceLogMapper = lphBalanceLogMapper;
        this.lphBalanceLogPullMapper = lphBalanceLogPullMapper;
        this.lphCircuitDetailMapper = lphCircuitDetailMapper;
        this.lphCircuitDetailPullMapper = lphCircuitDetailPullMapper;
        this.lphCircuitPullMapper = lphCircuitPullMapper;
        this.lphCircuitMapper = lphCircuitMapper;
        this.lphLineStatusMapper = lphLineStatusMapper;
        this.lphLineStatusPullMapper = lphLineStatusPullMapper;
        this.lphMedalMapper = lphMedalMapper;
        this.lphMedalPullMapper = lphMedalPullMapper;
        this.lphPrizeMapper = lphPrizeMapper;
        this.lphPrizePullMapper = lphPrizePullMapper;
        this.lphUserMapper = lphUserMapper;
        this.lphUserPullMapper = lphUserPullMapper;
        this.lphUserBasicInfoMapper = lphUserBasicInfoMapper;
        this.lphUserBasicInfoPullMapper = lphUserBasicInfoPullMapper;
        this.lphUserInformationMapper = lphUserInformationMapper;
        this.lphUserInformationPullMapper = lphUserInformationPullMapper;
        this.lphUserLevelMapper = lphUserLevelMapper;
        this.lphUserLevelPullMapper = lphUserLevelPullMapper;
        this.lphUserLineStatusMapper = lphUserLineStatusMapper;
        this.lphUserLineStatusPullMapper = lphUserLineStatusPullMapper;
        this.lphUserLoginLogMapper = lphUserLoginLogMapper;
        this.lphUserLoginLogPullMapper = lphUserLoginLogPullMapper;
        this.lphUserPrizeMapper = lphUserPrizeMapper;
        this.lphUserPrizePullMapper = lphUserPrizePullMapper;
        this.lphUserPrizeAwardMapper = lphUserPrizeAwardMapper;
        this.lphUserPrizeAwardPullMapper = lphUserPrizeAwardPullMapper;
        this.lphUserRelationMapper = lphUserRelationMapper;
        this.lphUserRelationPullMapper = lphUserRelationPullMapper;
        this.lphUserSourceMapper = lphUserSourceMapper;
        this.lphUserSourcePullMapper = lphUserSourcePullMapper;
        this.lphUserStepMapper = lphUserStepMapper;
        this.lphUserStepPullMapper = lphUserStepPullMapper;
        this.lphUserTaskMapper = lphUserTaskMapper;
        this.lphUserTaskPullMapper = lphUserTaskPullMapper;
        this.lphReportMapper = lphReportMapper;
        this.lphReportPullMapper = lphReportPullMapper;
        this.pullDataConfig = pullDataConfig;
    }

    public void handlerTable(String tableName) {
        if(tableName.equals("t_lph_balance_log")){
            handlerLphBalanceLogTable();
        }else  if(tableName.equals("t_lph_circuit")){
            handlerLphCircuitTable();
        }else  if(tableName.equals("t_lph_circuit_detail")){
            handlerLphCircuitDetailTable();
        }else  if(tableName.equals("t_lph_line_status")){
            handlerLphLineStatusTable();
        }else  if(tableName.equals("t_lph_medal")){
            handlerLphMedalTable();
        }else  if(tableName.equals("t_lph_prize")){
            handlerLphPrizeTable();
        }else  if(tableName.equals("t_lph_user")){
            handlerLphUserTable();
        }else  if(tableName.equals("t_lph_user_basic_info")){
            handlerLphUserBasicInfoTable();
        }else  if(tableName.equals("t_lph_user_information")){
            handlerLphUserInformationTable();
        }else  if(tableName.equals("t_lph_user_level")){
            handlerLphUserLevelTable();
        }else  if(tableName.equals("t_lph_user_line_status")){
            handlerLphUserLineStatusTable();
        }else  if(tableName.equals("t_lph_user_login_log")){
            handlerLphUserLoginLogTable();
        }else  if(tableName.equals("t_lph_user_prize")) {
            handlerLphUserPrizeTable();
        }else  if(tableName.equals("t_lph_user_prize_award")) {
            handlerLphUserPrizeAwardTable();
        }else  if(tableName.equals("t_lph_user_relation")) {
            handlerLphUserRelationTable();
        }else  if(tableName.equals("t_lph_user_source")) {
            handlerLphUserSourceTable();
        }else  if(tableName.equals("t_lph_user_step")) {
            handlerLphUserStepTable();
        }else  if(tableName.equals("t_lph_user_task")) {
            handlerLphUserTaskTable();
        }else  if(tableName.equals("t_lph_report")) {
            handlerLphReportTable();
        }
    }

    /**
     * 处理lphBalanceLog
     * @return
     */
    private void handlerLphBalanceLogTable(){
        try {
            log.info("同步lphBalanceLog开始");
            Example example = new Example(LphBalanceLogEntity.class);
            String startTime = getLastDay(new Date(), 1) +" "+ pullDataConfig.getHistoryDate();
            String endTime = getLastDay(new Date(), 0) + " "+pullDataConfig.getHistoryDate();
            example.createCriteria().andBetween("logTime", startTime, endTime);
            List<LphBalanceLogEntity> list = lphBalanceLogMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            //清空之前数据
            lphBalanceLogPullMapper.deleteByExample(example);
            List<List<LphBalanceLogEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphBalanceLogPullMapper::insertList);
            log.info("同步lphBalanceLog结束");
        }catch (Exception ex){
            log.error("同步lphBalanceLog发生异常-ex={}",ex);
        }
    }

    /**
     * 处理LphCircuitEntity
     * @return
     */
    private void handlerLphCircuitTable(){
        try {
            log.info("同步LphCircuitEntity开始");
            Example example = new Example(LphCircuitEntity.class);
            List<LphCircuitEntity> list = lphCircuitMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            lphCircuitPullMapper.deleteByExample(example);
            List<List<LphCircuitEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphCircuitPullMapper::insertList);
            log.info("同步LphCircuitEntity结束");
        }catch (Exception ex){
            log.error("同步LphCircuitEntity发生异常-ex={}",ex);
        }
    }

    /**
     * 处理LphCircuitDetailEntity
     * @return
     */
    private void handlerLphCircuitDetailTable(){
        try {
            log.info("同步LphCircuitDetailEntity开始");
            Example example = new Example(LphCircuitDetailEntity.class);
            handlerStaTime( example);
            List<LphCircuitDetailEntity> list = lphCircuitDetailMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            lphCircuitDetailPullMapper.deleteByExample(example);
            List<List<LphCircuitDetailEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphCircuitDetailPullMapper::insertList);
            log.info("同步LphCircuitDetailEntity结束");
        }catch (Exception ex){
            log.error("同步LphCircuitDetailEntity发生异常-ex={}",ex);
        }
    }

    /**
     * 处理LphLineStatusEntity
     * @return
     */
    private void handlerLphLineStatusTable(){
        try {
                log.info("同步LphLineStatusEntity开始");
                Example example = new Example(LphLineStatusEntity.class);
                handlerStaTime( example);
                List<LphLineStatusEntity> list = lphLineStatusMapper.selectByExample(example);
                if(CollectionUtils.isEmpty(list)){
                    return;
                }
                //清空之前数据
                //lphLineStatusPullMapper.deleteByExample(example);
                lphLineStatusPullMapper.truncateTable("t_lph_user_line_status");
                List<List<LphLineStatusEntity>> lists = ListUtils.splitList(list, 500);
                //再写入数据
                lists.forEach(lphLineStatusPullMapper::insertList);
                log.info("同步LphLineStatusEntity结束");
        }catch (Exception ex){
            log.error("同步LphLineStatusEntity发生异常-ex={}",ex);
        }
    }

    /**
     * 处理LphMedalEntity
     * @return
     */
    private void handlerLphMedalTable(){
        try {
            log.info("同步LphMedalEntity开始");
            Example example = new Example(LphMedalEntity.class);
            handlerStaTime(example);
            List<LphMedalEntity> list = lphMedalMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            //清空之前数据
            lphMedalPullMapper.deleteByExample(example);
            List<List<LphMedalEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphMedalPullMapper::insertList);
            log.info("同步LphMedalEntity结束");
        } catch (Exception ex){
            log.error("同步LphMedalEntity发生异常-ex={}",ex);
        }
    }

    /**
     * 处理LphPrizeEntity
     * @return
     */
    private void handlerLphPrizeTable(){
        try {
            log.info("同步LphPrizeEntity开始");
            Example example = new Example(LphPrizeEntity.class);
            handlerStaTime( example);
            List<LphPrizeEntity> list = lphPrizeMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            lphPrizePullMapper.deleteByExample(example);
            List<List<LphPrizeEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphPrizePullMapper::insertList);
            log.info("同步LphPrizeEntity结束");
        } catch (Exception ex){
            log.error("同步LphPrizeEntity发生异常-ex={}",ex);
        }
    }

    /**
     * 处理LphUserEntity
     * @return
     */
    private void handlerLphUserTable(){
        try {
            log.info("同步LphUserEntity开始");
            Example example = new Example(LphUserEntity.class);
            //handlerStaTime( example);
            List<LphUserEntity> list = lphUserMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            //lphUserPullMapper.deleteByExample(example);
            lphLineStatusPullMapper.truncateTable("t_lph_user");
            List<List<LphUserEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphUserPullMapper::insertList);
            log.info("同步LphUserEntity结束");
        } catch (Exception ex){
            log.error("同步LphUserEntity发生异常-ex={}",ex);
        }
    }

    /**
     * 处理LphUserBasicInfoEntity
     * @return
     */
    private void handlerLphUserBasicInfoTable(){
        try {
            log.info("同步LphUserBasicInfoEntity开始");
            Example example = new Example(LphUserBasicInfoEntity.class);
            handlerStaTime( example);
            List<LphUserBasicInfoEntity> list = lphUserBasicInfoMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            lphUserBasicInfoPullMapper.deleteByExample(example);
            List<List<LphUserBasicInfoEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphUserBasicInfoPullMapper::insertList);
            log.info("同步LphCircuitDetail结束");
        } catch (Exception ex){
            log.error("同步LphCircuitDetail发生异常-ex={}",ex);
        }
    }

    /**
     * 处理LphUserInformationEntity
     * @return
     */
    private void handlerLphUserInformationTable(){
        try {
            log.info("同步LphUserInformationEntity开始");
            Example example = new Example(LphUserInformationEntity.class);
            //全量同步数据
            //handlerStaTime( example);
            List<LphUserInformationEntity> list = lphUserInformationMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            lphUserInformationPullMapper.deleteByExample(example);
            List<List<LphUserInformationEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphUserInformationPullMapper::insertList);
            log.info("同步LphUserInformationEntity结束");
        } catch (Exception ex){
            log.error("同步LphUserInformationEntity发生异常-ex={}",ex);
        }
    }


    /**
     * 处理LphUserLevelEntity
     * @return
     */
    private void handlerLphUserLevelTable(){
        try {
            log.info("同步LphUserLevelEntity开始");
            Example example = new Example(LphUserLevelEntity.class);
            handlerStaTime( example);
            List<LphUserLevelEntity> list = lphUserLevelMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            lphUserLevelPullMapper.deleteByExample(example);
            List<List<LphUserLevelEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphUserLevelPullMapper::insertList);
            log.info("同步LphUserLevelEntity结束");
        } catch (Exception ex){
            log.error("同步LphUserLevelEntity发生异常-ex={}",ex);
        }
    }

    /**
     * 处理LphUserLineStatusEntity
     * @return
     */
    private void handlerLphUserLineStatusTable(){
        try {
            log.info("同步LphUserLineStatusEntity开始");
            Example example = new Example(LphUserLineStatusEntity.class);
            //handlerStaTime( example);
            List<LphUserLineStatusEntity> list = lphUserLineStatusMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            lphUserLineStatusPullMapper.deleteByExample(example);
            List<List<LphUserLineStatusEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphUserLineStatusPullMapper::insertList);
            log.info("同步LphUserLineStatusEntity结束");
        } catch (Exception ex){
            log.error("同步LphUserLineStatusEntity发生异常-ex={}",ex);
        }
    }

    /**
     * 处理LphUserLoginLogEntity
     * @return
     */
    private void handlerLphUserLoginLogTable(){
        try {
            log.info("同步LphUserLoginLogEntity开始");
            Example example = new Example(LphUserLoginLogEntity.class);
            handlerStaTime( example);
            List<LphUserLoginLogEntity> list = lphUserLoginLogMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            lphUserLoginLogPullMapper.deleteByExample(example);
            List<List<LphUserLoginLogEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphUserLoginLogPullMapper::insertList);
            log.info("同步LphUserLoginLogEntity结束");
        } catch (Exception ex){
            log.error("同步LphUserLoginLogEntity发生异常-ex={}",ex);
        }
    }


    /**
     * 处理LphUserPrizeEntity
     * @return
     */
    private void handlerLphUserPrizeTable(){
        try {
            log.info("同步LphUserPrizeEntity开始");
            Example example = new Example(LphUserPrizeEntity.class);
            handlerStaTime( example);
            List<LphUserPrizeEntity> list = lphUserPrizeMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            lphUserPrizePullMapper.deleteByExample(example);
            List<List<LphUserPrizeEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphUserPrizePullMapper::insertList);
            log.info("同步LphUserPrizeEntity结束");
        } catch (Exception ex){
            log.error("同步LphUserPrizeEntity发生异常-ex={}",ex);
        }
    }


    /**
     * 处理LphUserPrizeAwardEntity
     * @return
     */
    private void handlerLphUserPrizeAwardTable(){
        try {
            log.info("同步LphUserPrizeAwardEntity开始");
            Example example = new Example(LphUserPrizeAwardEntity.class);
            handlerStaTime( example);
            List<LphUserPrizeAwardEntity> list = lphUserPrizeAwardMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            lphUserPrizeAwardPullMapper.deleteByExample(example);
            List<List<LphUserPrizeAwardEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphUserPrizeAwardPullMapper::insertList);
            log.info("同步LphUserPrizeAwardEntity结束");
        } catch (Exception ex){
            log.error("同步LphUserPrizeAwardEntity发生异常-ex={}",ex);
        }
    }



    /**
     * 处理LphUserRelationEntity
     * @return
     */
    private void handlerLphUserRelationTable(){
        try {
            log.info("同步LphUserRelationEntity开始");
            Example example = new Example(LphUserRelationEntity.class);
            handlerStaTime( example);
            List<LphUserRelationEntity> list = lphUserRelationMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            lphUserRelationPullMapper.deleteByExample(example);
            List<List<LphUserRelationEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphUserRelationPullMapper::insertList);
            log.info("同步LphUserRelationEntity结束");
        } catch (Exception ex){
            log.error("同步LphUserRelationEntity发生异常-ex={}",ex);
        }
    }

    /**
     * 处理LphUserSourceEntity
     * @return
     */
    private void handlerLphUserSourceTable(){
//        log.info("同步LphUserSourceEntity开始");
//        Example example = new Example(LphUserSourceEntity.class);
//        handlerStaTime( example);
//        List<LphUserSourceEntity> list = lphUserSourceMapper.selectByExample(example);
//        if(CollectionUtils.isEmpty(list)){
//            return;
//        }
//        List<List<LphUserSourceEntity>> lists = ListUtils.splitList(list, 500);
//        //再写入数据
//        lists.forEach(lphUserSourcePullMapper::insertList);
//        log.info("同步LphUserSourceEntity结束");
    }

    /**
     * 处理LphUserStepEntity
     * @return
     */
    private void handlerLphUserStepTable(){
        try {
            log.info("同步LphUserStepEntity开始");
            Example example = new Example(LphUserStepEntity.class);
            handlerStaTime( example);
            List<LphUserStepEntity> list = lphUserStepMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            lphUserStepPullMapper.deleteByExample(example);
            List<List<LphUserStepEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphUserStepPullMapper::insertList);
            log.info("同步LphUserStepEntity结束");
        } catch (Exception ex){
            log.error("同步LphUserStepEntity发生异常-ex={}",ex);
        }
    }

    /**
     * 处理LphUserTaskEntity
     * @return
     */
    private void handlerLphUserTaskTable(){
        try {
            log.info("同步LphUserTaskEntity开始");
            Example example = new Example(LphUserTaskEntity.class);
            handlerStaTime( example);
            List<LphUserTaskEntity> list = lphUserTaskMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            lphUserTaskPullMapper.deleteByExample(example);
            List<List<LphUserTaskEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphUserTaskPullMapper::insertList);
            log.info("同步LphUserTaskEntity结束");
        } catch (Exception ex){
            log.error("同步LphUserTaskEntity发生异常-ex={}",ex);
        }
    }

    /**
     * 处理LphReportEntity
     * @return
     */
    private void handlerLphReportTable(){
        try {
            log.info("同步LphReportEntity开始");
            Example example = new Example(LphReportEntity.class);
            //handlerStaTime( example);
            List<LphReportEntity> list = lphReportMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(list)){
                return;
            }
            //清空之前数据
            lphReportPullMapper.deleteByExample(example);
            List<List<LphReportEntity>> lists = ListUtils.splitList(list, 500);
            //再写入数据
            lists.forEach(lphReportPullMapper::insertList);
            log.info("同步LphReportEntity结束");
        } catch (Exception ex){
            log.error("同步LphReportEntity发生异常-ex={}",ex);
        }
    }

    /**
     * 处理统计时间 昨天0点到今天0点的数据
     * @param example
     */
    private void handlerStaTime(Example example){
        String startTime=getLastDay(new Date(),1)+" "+pullDataConfig.getHistoryDate();
        String endTime=getLastDay(new Date(),0)+" "+pullDataConfig.getHistoryDate();
        example.createCriteria().andBetween("createTime",startTime,endTime);
    }


    /**
     * 得到前几天时间
     * @param date
     * @return
     */
    private static String getLastDay(Date date, Integer preDay) {
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd"); //设置时间格式
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, -(preDay));  //设置为前一天
        return sdf.format(calendar.getTime());
    }

}
