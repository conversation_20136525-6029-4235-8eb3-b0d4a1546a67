package com.goodsogood.ows.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.ExcelExportInfoConfig;
import com.goodsogood.ows.model.vo.DynamicColVo;
import com.goodsogood.ows.model.vo.FileCommonForm;
import lombok.Cleanup;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.OutputStream;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Log4j2
public class ExcelServices {
    private final FileService fileService;
    private final ExcelExportInfoConfig excelExportInfoConfig;
    private final Errors errors;
    private final RestTemplate restTemplate;

    public ExcelServices(FileService fileService,
                         ExcelExportInfoConfig excelExportInfoConfig,
                         Errors errors, RestTemplate restTemplate) {
        this.fileService = fileService;
        this.excelExportInfoConfig = excelExportInfoConfig;
        this.errors = errors;
        this.restTemplate = restTemplate;
    }

    /**
     * 固定导出表格信息
     * @param token 前端唯一标识
     * @param title 标题名称
     * @param sheetName sheetName
     * @param list 列表信息
     * @param clazz 类型的值
     */
    @Async("excelExportExecutor")
    public void exportFixExcel(HttpHeaders headers, String token,String title, String sheetName, Collection<?> list, Class<?> clazz){
        try {
            setTokenStatus(token,1,"token="+token+"的exportFixExcel开始上传");
            ExportParams exportParams = new ExportParams();
            if(StringUtils.isNotEmpty(sheetName)) {
                exportParams.setSheetName(sheetName);
            }
            if(StringUtils.isNotEmpty(title)) {
                exportParams.setTitle(title);
            }
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, clazz, list);
            String fileName = encodingFilename(sheetName);
            @Cleanup
            OutputStream out = Files.newOutputStream(Paths.get(fileName));
            workbook.write(out);
            //调用上传接口
            fileService.sendFileCenter(excelExportInfoConfig.getFileServices(),fileName,token,errors,headers,restTemplate);
        }catch (Exception ex){
            log.error("exportFixExcel发生异常",ex);
            setTokenStatus(token,-1,"exportFixExcel发生异常");
        }
    }


    /**
     * 获得要下载列的信息
     */
    public List<DynamicColVo> getDynamicColInfo(Class<?> clazz ,Integer type){
        Field[] declaredFields = clazz.getDeclaredFields();
        List<DynamicColVo> list = new ArrayList<>();
        for (Field  field : declaredFields) {
            if(field.isAnnotationPresent(Excel.class)){
                Excel excel = field.getAnnotation(Excel.class);
                DynamicColVo dynamicColVo = new DynamicColVo();
                dynamicColVo.setCode(field.getName());
                dynamicColVo.setName(excel.name());
                dynamicColVo.setWidth(excel.width());
                dynamicColVo.setOrderNum(Integer.valueOf(excel.orderNum()));
                if(type!=1) {
                    dynamicColVo.setDatabaseFormat(excel.databaseFormat());
                    dynamicColVo.setGroupName(excel.groupName());
                    dynamicColVo.setFormat(excel.format());
                    dynamicColVo.setReplace(excel.replace());
                    dynamicColVo.setDict(excel.dict());
                    dynamicColVo.setHyperlink(excel.isHyperlink());
                    dynamicColVo.setFixedIndex(excel.fixedIndex());
                    dynamicColVo.setImageType(excel.imageType());
                    dynamicColVo.setNeedMerge(excel.needMerge());
                    dynamicColVo.setMergeVertical(excel.mergeVertical());
                    dynamicColVo.setMergeRely(excel.mergeRely());
                    dynamicColVo.setSuffix(excel.suffix());
                    dynamicColVo.setNumFormat(excel.numFormat());
                    dynamicColVo.setEnumExportField(excel.enumExportField());
                    dynamicColVo.setIsColumnHidden(excel.isColumnHidden());
                }
                list.add(dynamicColVo);
            }
        }
        return list;
    }

    /**
     * 编码文件名
     */
    public String encodingFilename(String filename) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileFormat = sdf.format(new Date());
        filename =excelExportInfoConfig.getDirPath() + filename + "_" + fileFormat + ".xlsx";
        return filename;
    }

    private void setTokenStatus(String token,Integer status,String errMsg){
        FileCommonForm fileCommonForm = new FileCommonForm();
        fileCommonForm.setStatus(status);
        if(StringUtils.isNotEmpty(errMsg)) {
            fileCommonForm.setErrorMsg(errMsg);
        }
        fileService.setFileCommonByToken(token, fileCommonForm);
    }

    /**
     * 构造动态excel 信息
     * @param headers
     * @param token
     * @param title
     * @param sheetName
     * @param list
     * @param clazz
     */
    @Async("excelExportExecutor")
    public void exportVariableExcel(HttpHeaders headers, String token,String title,
                                    String sheetName, Collection<?> list,
                                    Class<?> clazz,List<DynamicColVo> dynamicColInfo){
        List<ExcelExportEntity> beanList = new ArrayList<>();
        List<Map<String, Object>> listMap = new ArrayList<>();
        //得到所有注解的配置信息
        List<DynamicColVo>  noteInfo= getDynamicColInfo(clazz, 2);
        //构建表头
        dynamicColInfo.forEach(item->{
            ExcelExportEntity excelExportEntity = new ExcelExportEntity(item.getName(),item.getCode());
            Optional<DynamicColVo> first = noteInfo.stream().filter(it -> it.getCode().equals(item.getCode())).findFirst();
            if(first.isPresent()){
                DynamicColVo dynamicColVo = first.get();
                //复制注解信息
                BeanUtils.copyProperties(dynamicColVo,excelExportEntity,"name");
            }
            excelExportEntity.setOrderNum(item.getOrderNum());
            beanList.add(excelExportEntity);
        });
        //构建数据
        list.forEach(item->{
            Map<String, Object> map = new HashMap<>();
            Field[] declaredFields = item.getClass().getDeclaredFields();
            dynamicColInfo.forEach(it->{
                for (Field  field : declaredFields) {
                    if(field.getName().equals(it.getCode())){
                        String key = it.getCode();
                        try {
                            field.setAccessible(true);
                            Object value = field.get(item);
                            map.put(key, value);
                            break;
                        } catch (Exception e) {
                            log.error("buildExportVariableExcel发生异常",e);
                        }
                    }
                }
            });
            listMap.add(map);
        });
        try{
            setTokenStatus(token,1,"token="+token+"的exportVariableExcel开始上传");
            ExportParams exportParams = new ExportParams();
            if(StringUtils.isNotEmpty(sheetName)) {
                exportParams.setSheetName(sheetName);
            }
            if(StringUtils.isNotEmpty(title)) {
                exportParams.setTitle(title);
            }
            Workbook workbook =  ExcelExportUtil.exportExcel(exportParams, beanList , listMap);
            String fileName = encodingFilename(sheetName);
            @Cleanup
            OutputStream out = Files.newOutputStream(Paths.get(fileName));
            workbook.write(out);

            //调用上传接口
            fileService.sendFileCenter(excelExportInfoConfig.getFileServices(),fileName,token,errors,headers,restTemplate);
        }catch (Exception ex){
            log.error("exportVariableExcel发生异常",ex);
            setTokenStatus(token,-1,"exportVariableExcel发生异常");
        }
    }

    @Async("excelExportExecutor")
    public void exportFailList(HttpHeaders headers, List<?> failList, String token,Class<?> clazz) {
            exportFixExcel(headers,token,"","错误人员列表",failList,clazz);
    }
}
