package com.goodsogood.ows.service.activity;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.ScoreMasterIndexProperty;
import com.goodsogood.ows.mapper.activity.DonateUserMapper;
import com.goodsogood.ows.model.mongodb.ScoreInfo;
import com.goodsogood.ows.model.vo.activity.Donate;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/20
 * Description:  donate user upload mongodb 一元捐
 */
@Service
@Log4j2
public class ActivityDetailDonateService {

    private final MyMongoTemplate mongoTemplate;
    private final DonateUserMapper donateUserMapper;
    private final ScoreMasterIndexProperty scoreMasterIndexProperty;

    @Autowired
    public ActivityDetailDonateService(MyMongoTemplate mongoTemplate, DonateUserMapper donateUserMapper, ScoreMasterIndexProperty scoreMasterIndexProperty) {
        this.mongoTemplate = mongoTemplate;
        this.donateUserMapper = donateUserMapper;
        this.scoreMasterIndexProperty = scoreMasterIndexProperty;
    }

    /**
     * 积分换书情况
     *
     * @param date 需要处理的月
     */
    public void toDonateUserReport(Date date) {
        String format = DateUtils.dateFormat(date, "yyyy-MM");
        buyDonateDataUploadMongodbNow(format, format.concat(Constants.START_MONTH_TIME), format.concat(Constants.END_MONTH_TIME));
    }

    public void buyDonateDataUploadMongodbNow(String queryTime, String startDate, String endDate){
        Integer skip = 0;
        while (true) {

            List<Donate> list = donateUserMapper.findDonateUsers(startDate, endDate, scoreMasterIndexProperty.getLimitSize(), skip);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }

            updateOrInsertNow(queryTime,list);

            skip += scoreMasterIndexProperty.getLimitSize();
        }
    }

    private void updateOrInsertNow( String queryTime, List<Donate> list) {
        list.forEach(x -> {
            Query query = new Query(Criteria.where("userId").is(x.getTempUserId()).and("queryTime").is(queryTime));
            Update update = new Update().addToSet("donates", x);
            x.setTempUserId(null);
            mongoTemplate.upsert(query, update, ScoreInfo.class);
        });
    }

//    /**
//     * 积分换书 学习情况
//     *
//     * @param queryTime 查询数据的月份
//     * @param startDate 开始时间
//     * @param endDate   结束时间
//     */
//    public void buyDonateDataUploadMongodb(String queryTime, String startDate, String endDate) {
//
//        Integer skip = 0;
//        while (true) {
//            List<Donate> list = donateUserMapper.findDonateUsers(startDate, endDate, limitSize, skip);
//
//            if (CollectionUtils.isEmpty(list)) {
//                break;
//            }
//
//            List<Donate> tempList = new ArrayList<>();
//            Long tempUserId = null;
//            for (int i = 0; i < list.size(); i++) {
//                Donate donate = list.get(i);
//
//                /* 第一次循环判断mongodb中是否存在该donate数据用于追加上次循环最后的数据 */
//                if (i == 0) {
////                    Query query = new Query(Criteria.where("user_id").is(donate.getTempUserId()).and("query_time").is(queryTime).and("donates").exists(true));
////                    boolean exists = mongoTemplate.exists(query, ScoreInfo.class);
////                    if (exists) {
////                        List<ScoreInfo> scoreInfos = mongoTemplate.find(new Query(Criteria.where("user_id").is(donate.getTempUserId()).and("query_time").is(queryTime)), ScoreInfo.class);
////                        if (scoreInfos.size() > 1) {
////                            throw new ApiException(String.format("ActivityDetailDonateService.buyDonateDataUploadMongodb 查询到多条数据 user_id:[%s] query_time:[%s]", donate.getTempUserId(), queryTime));
////                        }
////                        tempList = scoreInfos.get(0).getDonates();
////                        tempUserId = donate.getTempUserId();
////                    } else {
////                        tempUserId = donate.getTempUserId();
////                    }
//                    tempUserId = donate.getTempUserId();
//                }
//
//                /* 判断当前donate对象user_id是否与上条user_id 相等 如果相等则追加 如果不相等则把tempList数据插入到mongo中 */
//                if (tempUserId.equals(donate.getTempUserId())) {
//                    donate.setTempUserId(null);
//                    tempList.add(donate);
//                } else {
//                    updateOrInsert(tempUserId, queryTime, tempList);
//                    tempUserId = donate.getTempUserId();
//                    tempList = new ArrayList<>();
//                    donate.setTempUserId(null);
//                    tempList.add(donate);
//                }
//
//                /* 本轮循环结束 需要手动保存数据到mongodb */
//                if (i == list.size() - 1) {
//                    updateOrInsert(tempUserId, queryTime, tempList);
//                }
//            }
//
//            skip += limitSize;
//        }
//    }
//
//    private void updateOrInsert(Long userId, String queryTime, List<Donate> list) {
//        Query query = new Query(Criteria.where("user_id").is(userId).and("query_time").is(queryTime));
//        Update update = new Update().addToSet("donates").each(list);
//        mongoTemplate.upsert(query, update, ScoreInfo.class);
//    }


}
