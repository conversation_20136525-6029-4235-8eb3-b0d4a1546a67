package com.goodsogood.ows.service.activity;

import com.goodsogood.ows.mapper.activity.RevisitMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2021-09-15 16:41
 **/
@Service
@Log4j2
public class RevisitService {

    private final RevisitMapper revisitMapper;
    private final RevisitResultService revisitResultService;


    @Autowired
    public RevisitService(RevisitMapper revisitMapper,
                          RevisitResultService revisitResultService) {
        this.revisitMapper = revisitMapper;
        this.revisitResultService = revisitResultService;
    }


}