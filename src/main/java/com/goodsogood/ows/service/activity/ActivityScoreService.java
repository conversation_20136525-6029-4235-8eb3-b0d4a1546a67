package com.goodsogood.ows.service.activity;

import com.goodsogood.ows.configuration.ScoreConfig;
import com.goodsogood.ows.mapper.activity.ActivityUserScoreMapper;
import com.goodsogood.ows.model.vo.ScoreResult;
import com.goodsogood.ows.model.vo.rank.Month;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.service.user.UserSnapshotService;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.goodsogood.ows.utils.DateUtils.dateFormat;

/**
 * Description: 纪实打分
 *
 * <AUTHOR>
 * @version 2020/11/6 15:03
 */
@Service
@Log4j2
public class ActivityScoreService {
  private final ActivityUserScoreMapper activityUserScoreMapper;
  private final UserSnapshotService userSnapshotService;
  private final ScoreConfig scoreConfig;

  public ActivityScoreService(
      ActivityUserScoreMapper activityUserScoreMapper,
      UserSnapshotService userSnapshotService,
      ScoreConfig scoreConfig) {
    this.activityUserScoreMapper = activityUserScoreMapper;
    this.userSnapshotService = userSnapshotService;
    this.scoreConfig = scoreConfig;
  }

  /**
   * 用户得分
   *
   * <p>/** 互动 是否参加投票、问卷、有奖竞答、线下互动等 5 判断当前党员在当月是否参加互动，有则得该项分
   *
   * @param regionId 区县id
   * @param userIdList 用户信息
   * @param year 年份
   * @return double 分值
   */
  public ScoreResultVo userActivityLevelScore(Long regionId, Integer year, List<Long> userIdList) {
    List<ActivityUserScoreMapper.UserActivityScoreForm> formList = new ArrayList<>();
    for (Month month : Month.values()) {
      int m = month.value();
      // 查询非离退休党员的id
      Set<Long> userIdSet = userSnapshotService.noRetired(regionId,userIdList,year,m);
      if (CollectionUtils.isNotEmpty(userIdSet)) {
        ActivityUserScoreMapper.UserActivityScoreForm form =
            ActivityUserScoreMapper.UserActivityScoreForm.builder()
                .userIdSet(userIdSet)
                .regionId(regionId)
                .types(scoreConfig.getUserScore().getActivity().getTypes())
                .score(scoreConfig.getUserScore().getActivity().getScore())
                .currentMonth(dateFormat(year, m))
                .deductScore(0.0)
                .build();
        formList.add(form);
      }
    }
    List<ScoreResult> scoreList = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(formList)) {
      scoreList = activityUserScoreMapper.userActivityScore(formList);
    }
    ScoreResultVo scoreResultVo = ScoreResult.getScoreResultVo(year, userIdList, scoreList);
    log.info("用户参加互动得分:{}", JsonUtils.toJson(scoreResultVo));
    return scoreResultVo;
  }
}
