package com.goodsogood.ows.service.activity.dss;

import com.goodsogood.ows.annotions.Logging;
import com.goodsogood.ows.configuration.ActivityTypeConfig;
import com.goodsogood.ows.mapper.activity.ActivityMapper;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.model.mongodb.dss.ActivityInfo;
import com.goodsogood.ows.model.mongodb.dss.ActivityJoinList;
import com.goodsogood.ows.service.impl.DssUserBuilder;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: 决策辅助用户
 *
 * <AUTHOR>
 * @version 2020/11/9 11:12
 */
@Service
@Log4j2
public class DssUserFromActivity implements DssUserBuilder {
  private final ActivityMapper activityMapper;
  private final ActivityTypeConfig activityTypeConfig;



  public DssUserFromActivity(ActivityMapper activityMapper, ActivityTypeConfig activityTypeConfig) {
    this.activityMapper = activityMapper;
    this.activityTypeConfig = activityTypeConfig;
  }

  @Override
  @Logging
  public UserInfo buildUser(UserInfo info) {
    ActivityInfo activityInfo = new ActivityInfo();
    info.setActivityInfo(activityInfo);
    // 查询用户参加列表列表
    List<ActivityJoinList> lists = new ArrayList<>();
    if(StringUtils.isNotBlank(activityTypeConfig.getDssTypes())){
      lists = activityMapper.activityJoinListByUserId(
              info.getRegionId(), info.getUserId(), info.getYear(), activityTypeConfig.getDssTypes());
    }
    activityInfo.setActivityJoinList(lists);
    activityInfo.setJoinNum(lists.size());
    return info;
  }
}
