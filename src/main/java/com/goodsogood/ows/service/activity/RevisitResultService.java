package com.goodsogood.ows.service.activity;


import com.goodsogood.ows.mapper.activity.RevisitResultMapper;
import com.goodsogood.ows.model.db.activity.RevisitResultEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021-09-15 16:41
 **/
@Service
@Log4j2
public class RevisitResultService {

    private final RevisitResultMapper revisitResultMapper;



    @Autowired
    public RevisitResultService(RevisitResultMapper revisitResultMapper) {
        this.revisitResultMapper = revisitResultMapper;
    }


    /**
     * 得到参与用户信息统计
     * @param startMonth
     * @param endMonth
     * @param types
     * @return
     */
    public Set<Long> getAddUserInfo(String startMonth, String endMonth, Set<Long> allUserInfo, List<Integer> types) {
        Example example=new Example(RevisitResultEntity.class);
        example.createCriteria().andIn("userId", allUserInfo)
                .andIn("type", types)
                .andBetween("createTime", startMonth, endMonth);
        example.selectProperties("userId");
        return revisitResultMapper.selectByExample(example).stream().
                map(RevisitResultEntity::getUserId).collect(Collectors.toSet());
    }


    /**
     * 得到参与用户信息统计
     * @param startMonth
     * @param endMonth
     * @param types
     * @return
     */
    public Set<Long> getAddUserInfoByAddScore(String startMonth, String endMonth, Long orgId, List<Integer> types) {
        Example example=new Example(RevisitResultEntity.class);
        example.createCriteria().andEqualTo("orgId", orgId)
                .andIn("type", types)
                .andBetween("createTime", startMonth, endMonth)
                .andIsNotNull("remark");
        example.selectProperties("userId");
        return revisitResultMapper.selectByExample(example).stream().
                map(RevisitResultEntity::getUserId).collect(Collectors.toSet());
    }



}