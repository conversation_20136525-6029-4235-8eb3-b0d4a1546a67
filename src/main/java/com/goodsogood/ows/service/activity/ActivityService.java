package com.goodsogood.ows.service.activity;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.mapper.activity.ActivityMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.ActivityInfo;
import com.goodsogood.ows.model.vo.activity.ActivityElectronicReport;
import com.goodsogood.ows.model.vo.activity.ActivityOrgElectronicReport;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: ows-activity
 * @description: 活动类型Service
 * @author: Mr.LiGuoYong
 * @create: 2019-11-19 09:31
 **/
@Service
@Log4j2
public class ActivityService {

    private final ActivityMapper activityMapper;
    private final MyMongoTemplate mongoTemplate;
    private final OrgService orgService;
    private final UserMapper userMapper;
    List<Pair<Query, Update>> updates = new ArrayList<>();

    public ActivityService(ActivityMapper activityMapper, MyMongoTemplate mongoTemplate, OrgService orgService, UserMapper userMapper) {
        this.activityMapper = activityMapper;
        this.mongoTemplate = mongoTemplate;
        this.orgService = orgService;
        this.userMapper = userMapper;
    }


    public void buildActivityStaInfo(Boolean isInit) {
        String startTime, endTime;
        if (isInit) {
            startTime = "2018-01-01 00:00:00";
            endTime = DateUtils.getBeforeTimeOfMonth(-1).concat(Constants.END_MONTH_TIME);
        } else {
            String beforeTimeOfMonth = DateUtils.getBeforeTimeOfMonth(-1);
            startTime = beforeTimeOfMonth.concat(Constants.START_MONTH_TIME);
            endTime = beforeTimeOfMonth.concat(Constants.END_MONTH_TIME);
        }
        buildActivityStaInfo(startTime, endTime);
    }

    public void buildActivityStaInfo(String runDate) {
        buildActivityStaInfo(runDate.concat(Constants.START_MONTH_TIME),runDate.concat(Constants.END_MONTH_TIME));
    }

    /**
     * 开始统计信息
     *
     * @return
     */
    public void buildActivityStaInfo(String startTime, String endTime) {
//        //默认查询30天
//        Integer queryDays = 30;
//        if (isInit) {
//            //初始化拉取活动两年的信息
//            queryDays = 720;
//        }
        Assert.notNull(startTime, "startTime it must not be null");
        Assert.notNull(endTime, "endTime it must not be null");

        List<ActivityInfo> participantActivity = activityMapper.getParticipantActivity(startTime, endTime);
        if (CollectionUtils.isEmpty(participantActivity)) {
            return;
        }
        log.debug("ActivityService.buildActivityStaInfo 数据[{}]", participantActivity.size());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("更新活动信息开始-{}", stopWatch.toString());
        participantActivity.stream().forEach(item -> {
            try {
                //查询到没有数据不处理
                ActivityInfo activityInfo = activityMapper.getActivityInfo(item.getActivityId());
                if (activityInfo == null || CollectionUtils.isEmpty(activityInfo.getParticipantUsers())) {
                    return;
                }
                Query query = new Query().addCriteria(Criteria.where("activityId").is(item.getActivityId()));
                boolean exists = mongoTemplate.exists(query, ActivityInfo.class);
                log.debug("ActivityService.buildActivityStaInfo 活动[{}] 是否存在[{}]", item.getActivityId(), exists);
                if (exists) {
                    mongoTemplate.remove(query, ActivityInfo.class);
                    log.debug("ActivityService.buildActivityStaInfo 删除数据[{}]", item.getActivityId());
                }
    //                        ActivityInfo activityReportVo = mongoTemplate.findOne(query, ActivityInfo.class);
    //                        if (null != activityReportVo) {
    //                            //当参与人数没有变化不更新(同时中奖信息也不会发生变化) 这里去重是因为mongoDB toSet重复的不添加
    //                            if (activityReportVo.getParticipantNum() == activityInfo.getParticipantUsers().
    //                                    stream().distinct().toArray().length) {
    //                                return;
    //                            }
    //                        }
                //入库MongoDB
                insertUserInfoMongo(activityInfo);
            } catch (Exception ex) {
                ex.printStackTrace();
                log.error("更新活动信息发生异常activityId-{}，exMsg-{}", item.getActivityId(), ex.getMessage());
            }
        }
        );
        log.info("更新活动信息结束-{}", stopWatch.toString());
        stopWatch.stop();
    }

    /**
     * 入库MongoDB
     * 参与用户
     */
    private void insertUserInfoMongo(ActivityInfo activityReportVo) {
        OrganizationEntity organizationEntity = orgService.getById(activityReportVo.getOrgId());
        BeanUtils.copyProperties(organizationEntity, activityReportVo);
        activityReportVo.setOrgName(organizationEntity.getName());
        activityReportVo.setOrgType(organizationEntity.getOrgType().longValue());
        activityReportVo.setOrgTypeChild(organizationEntity.getOrgTypeChild().longValue());
        activityReportVo.setOrgCreateTime(organizationEntity.getCreateTime());
        //得到活动中奖用户
        List<ActivityInfo.WinUsers> winUsers = activityMapper.getGetWinUserByActivityId(activityReportVo.getActivityId());
        activityReportVo.setWinUsersNum(CollectionUtils.isEmpty(winUsers) ? 0 : winUsers.size());
        activityReportVo.setWinUsers(winUsers);
        /* 参与用户 */
        addParticipantUserOrg(activityReportVo.getParticipantUsers());
        activityReportVo.setParticipantNum(activityReportVo.getParticipantUsers().stream().distinct().toArray().length);
        activityReportVo.setParticipantUsers(activityReportVo.getParticipantUsers());
        log.debug("ActivityService.insertUserInfoMongo 插入数据[{}]", activityReportVo.getActivityId());
        mongoTemplate.insert(activityReportVo);

//        Update update = new Update();
//        Query query = new Query();
//        OrganizationEntity organizationEntity = orgService.getById(activityReportVo.getOrgId());
//        update.set("orgName", organizationEntity.getName());
//        update.set("orgId", activityReportVo.getOrgId());
//        update.set("parentId", organizationEntity.getParentId());
//        update.set("orgType", organizationEntity.getOrgType());
//        update.set("orgTypeChild", organizationEntity.getOrgTypeChild());
//        update.set("orgLevel", organizationEntity.getOrgLevel());
//        update.set("orgCreateTime", organizationEntity.getCreateTime());
//        query.addCriteria(Criteria.where("activityId").is(activityReportVo.getActivityId()));
//        update.set("activityName", activityReportVo.getActivityName());
//        update.set("activityType", activityReportVo.getActivityType());
//        update.set("activityTypeName", activityReportVo.getActivityTypeName());
//        update.set("activityCreatTime", activityReportVo.getActivityCreatTime());
//        //得到活动中奖用户
//        List<ActivityInfo.WinUsers> winUsers = activityMapper.getGetWinUserByActivityId(activityReportVo.getActivityId());
//        update.addToSet("winUsers").each(winUsers);
//        update.set("winUsersNum", CollectionUtils.isEmpty(winUsers) ? 0 : winUsers.size());
//        //参与活动用户预发布有重复提交的情况
//        /* 新增参与用户的组织关系id 用于在处理组织活动时用于查询 */
//        update.set("participantNum", activityReportVo.getParticipantUsers().stream().distinct().toArray().length);
//        update.addToSet("participantUsers").each(activityReportVo.getParticipantUsers());
//        Pair<Query, Update> pair = Pair.of(query, update);
//        updates.add(pair);
//        BulkWriteResult bulkWriteResult = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, ActivityInfo.class)
//                .upsert(updates)
//                .execute();
//        int matchedCount = bulkWriteResult.getMatchedCount();
//        log.info("activityId-{},matchedCount-{}", activityReportVo.getActivityId(), matchedCount);
    }

    /**
     * 根据用户Id以及统计时间 查询用户统计信息
     * staYearMonth yyyyMM 格式:201808
     *
     * @return
     */
    public List<ActivityElectronicReport> getActivityStaInfoByUserId(List<ActivityInfo> activityReportVos, Long userId, Integer staYearMonth) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
//        log.debug("根据用户id-" + userId + "查询用户统计信息-startTime = {}", stopWatch.toString());
//        Query query = new Query();
//        query.addCriteria(Criteria.where("participantUsers.participationDate").is(staYearMonth))
//                .addCriteria(Criteria.where("participantUsers.userId").is(userId));
//        query.fields().include("participantUsers").include("activityName").include("activityTypeName").include("activityType");
//        List<ActivityInfo> activityReportVos = mongoTemplate.find(query, ActivityInfo.class);

        if (CollectionUtils.isEmpty(activityReportVos)) {
            return new ArrayList<>();
        }

        List<ActivityElectronicReport> activityStaResults = new ArrayList<>();
        activityReportVos.stream().filter(this::filterActivityType).forEach(item -> {
            List<ActivityInfo.ParticipantUsers> collect = item.getParticipantUsers().stream().
                    filter(t -> t.getUserId().equals(userId) && staYearMonth.equals(t.getParticipationDate())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                return;
            }
            collect.forEach(e -> {
                ActivityElectronicReport activityStaResult = new ActivityElectronicReport();
                BeanUtils.copyProperties(e, activityStaResult);
                activityStaResult.setActivityName(item.getActivityName());
                activityStaResult.setActivityTypeName(item.getActivityTypeName());
                activityStaResults.add(activityStaResult);
            });
        });
        log.debug("根据用户id-" + userId + "查询用户统计信息-endTime = {}", stopWatch.toString());
        return activityStaResults;
    }

    public List<ActivityInfo> getActivityInfos(Long regionId,Integer staYearMonth) {
        Query query = new Query();
        query.addCriteria(Criteria.where("participantUsers.participationDate").is(staYearMonth))
                .addCriteria(Criteria.where("regionId").is(regionId));
        query.fields().include("participantUsers").include("activityName").include("activityTypeName").include("activityType");
        return mongoTemplate.find(query, ActivityInfo.class);
    }

    public List<ActivityInfo> getAllActivityInfos(Long regionId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("regionId").is(regionId));
        query.fields().include("participantUsers").include("activityName").include("activityTypeName").include("activityType");
        return mongoTemplate.find(query, ActivityInfo.class);
    }

    public List<ActivityOrgElectronicReport> getActivityStaInfoByOrgId(Long orgId, Integer staYearMonth) {

        //封装查询条件
        String orgLevel = "-" + orgId.toString() + "-";
        Criteria criteria = new Criteria();
        Query query = new Query();
        criteria.orOperator(Criteria.where("participantUsers.orgId").is(orgId),
                Criteria.where("participantUsers.orgLevel").regex(orgLevel));
        query.addCriteria(Criteria.where("participantUsers.participationDate").is(staYearMonth))
                .addCriteria(criteria);
        query.fields().exclude("winUsers");
        List<ActivityInfo> activityReportVos = mongoTemplate.find(query, ActivityInfo.class);
        List<ActivityOrgElectronicReport> orgReports = new ArrayList<>();

        HashMap<String, Integer> typeCount = new HashMap<>(4);
        typeCount.put("投票", 0);
        typeCount.put("问卷调查", 0);
        typeCount.put("有奖竞答", 0);
        typeCount.put("线下活动", 0);

        if (CollectionUtils.isEmpty(activityReportVos)) {
            typeCount.forEach((k, v) -> orgReports.add(new ActivityOrgElectronicReport(k, v)));
            return orgReports;
        }

        activityReportVos.stream().filter(this::filterActivityType).forEach(x -> {
            if (!CollectionUtils.isEmpty(x.getParticipantUsers())) {
                String count = x.getParticipantUsers().stream().filter(y -> {
                    return staYearMonth.equals(y.getParticipationDate())
                            && (orgId.equals(y.getOrgId()) ||
                            (StringUtils.isNotBlank(y.getOrgLevel()) && y.getOrgLevel().contains(orgLevel)));
                }).count() + "";
                if (typeCount.containsKey(x.getActivityTypeName())) {
                    typeCount.put(x.getActivityTypeName(), typeCount.get(x.getActivityTypeName()) + new Integer(count));
                } else {
                    typeCount.put(x.getActivityTypeName(), new Integer(count));
                }
            }
        });

        typeCount.forEach((k, v) -> orgReports.add(new ActivityOrgElectronicReport(k, v)));

        return orgReports;

    }


    /**
     * 统计组织及其下级组织活动信息
     * staYearMonth yyyyMM 格式:201808
     *
     * @return
     */
//    public List<ActivityOrgElectronicReport> getActivityStaInfoByOrgId(Long orgId, Integer staYearMonth) {
//        StopWatch stopWatch = new StopWatch();
//        stopWatch.start();
//        log.info("根据组织id-" + orgId + "查询组织统计信息-startTime = {}", stopWatch.toString());
//        Query query = new Query();
//        Criteria criteria = new Criteria();
//        String orgLevel="-" + orgId.toString() + "-";
//        criteria.orOperator(Criteria.where("participantUsers.orgId").is(orgId), Criteria.where("participantUsers.orgLevel").regex(orgLevel));
//        query.addCriteria(Criteria.where("participantUsers.participationDate").is(staYearMonth))
//                .addCriteria(criteria);
//        query.fields().exclude("winUsers");
//        List<ActivityInfo> activityReportVos = mongoTemplate.find(query, ActivityInfo.class);
//        List<ActivityOrgElectronicReport> orgReports = new ArrayList<>();
//        if (CollectionUtils.isEmpty(activityReportVos)) {
//            return orgReports;
//        }
//        activityReportVos.stream().filter(this::filterActivityType).forEach(item -> {
//            ActivityOrgElectronicReport orgReport = new ActivityOrgElectronicReport();
//            orgReport.setTypeName(item.getActivityTypeName());
//            //这里活动数据 有可能一个活动参与数据有跨月数据
//            orgReport.setParticipantCount(item.getParticipantUsers().stream().
//                    filter(e -> e.getParticipationDate().intValue() == staYearMonth.intValue() && (orgId.equals(e.getOrgId())|| (StringUtils.isNotBlank(e.getOrgLevel()) && e.getOrgLevel().contains(orgLevel)))).toArray().length);
//            orgReports.add(orgReport);
//        });
//        //对List进行汇统计
//        List<ActivityOrgElectronicReport> orgReportsNew = new ArrayList<>();
//        orgReports.stream().collect(Collectors.groupingBy(o -> new ActivityOrgElectronicReport(o.getTypeName()),
//                Collectors.summingInt(ActivityOrgElectronicReport::getParticipantCount))).forEach((k, v) -> {
//            ActivityOrgElectronicReport activityOrgElectronicReport = new ActivityOrgElectronicReport();
//            activityOrgElectronicReport.setParticipantCount(v);
//            activityOrgElectronicReport.setTypeName(k.getTypeName());
//            orgReportsNew.add(activityOrgElectronicReport);
//        });
//        log.info("根据组织id-" + orgId + "查询组织统计信息-endTime = {}", stopWatch.toString());
//        return orgReportsNew;
//    }

    /**
     * 过滤不是以下活动类型的数据 并把调查评价合并到问卷调查
     * "\t\tWHEN type = 1 THEN\n" +
     * "\t\t'投票' \n" +
     * "\t\tWHEN type = 2 THEN\n" +
     * "\t\t'问卷调查' \n" +
     * "\t\tWHEN type = 3 THEN\n" +
     * "\t\t'有奖竞答' \n" +
     * "\t\tWHEN type = 4 THEN\n" +
     * "\t\t'线下活动' \n" +
     * "\t\tWHEN type = 6 THEN\n" +
     * "\t\t'调查评价' \n" +
     *
     * @param activityInfo
     * @return
     */
    private boolean filterActivityType(ActivityInfo activityInfo) {
        if (activityInfo == null || activityInfo.getActivityType() == null
                || activityInfo.getActivityTypeName() == null) {
            return false;
        }
        Long activityType = activityInfo.getActivityType();
        if (activityType.equals(1L)
                || activityType.equals(3L)
                || activityType.equals(4L)
                || activityType.equals(2L)) {
            return true;
        } else if (activityType.equals(6L)) {
            activityInfo.setActivityTypeName("问卷调查");
            return true;
        } else {
            return false;
        }
    }

    /**
     * 补充人员所关联的组织信息
     *
     * @param participantUsers
     */
    private void addParticipantUserOrg(List<ActivityInfo.ParticipantUsers> participantUsers) {
        if (CollectionUtils.isEmpty(participantUsers)) {
            return;
        }

        List<ActivityInfo.ParticipantUsers> activityMongoUsers = userMapper.getActivityMongoUsers(participantUsers.stream().map(ActivityInfo.ParticipantUsers::getUserId).collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(activityMongoUsers)) {
            return;
        }

        Map<Long, ActivityInfo.ParticipantUsers> collect = new HashMap<>();

        for (ActivityInfo.ParticipantUsers activityMongoUser : activityMongoUsers) {
            collect.put(activityMongoUser.getUserId(), activityMongoUser);
        }
        participantUsers.forEach(x -> {
            ActivityInfo.ParticipantUsers tempParticipantUser = collect.get(x.getUserId());
            if (tempParticipantUser != null) {
                x.setOrgId(tempParticipantUser.getOrgId());
                x.setOrgLevel(tempParticipantUser.getOrgLevel());
            }
        });
    }
}
