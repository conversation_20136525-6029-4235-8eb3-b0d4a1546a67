package com.goodsogood.ows.service.pbm.impl

import com.goodsogood.ows.mapper.user.UserLoginLogMapper
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import com.goodsogood.ows.service.pbm.PbmUserWorkDataService
import org.springframework.stereotype.Service

/**
 * 登录数智党建平台
 * <AUTHOR>
 * @createTime 2022年06月21日 23:18:00
 */
@Service("USER_LOGIN")
class PbmUserLoginServiceImpl(val userLoginLogMapper: UserLoginLogMapper) : PbmUserWorkDataService {

    override fun getUserWorkData(userId: Long, startTime: String?, endTime: String?, week: Int?, regionId: Long, workItem: WorkItemEntity): Any? {
        return userLoginLogMapper.getNumByUserAndMonth(userId, startTime, endTime, regionId)
    }

}