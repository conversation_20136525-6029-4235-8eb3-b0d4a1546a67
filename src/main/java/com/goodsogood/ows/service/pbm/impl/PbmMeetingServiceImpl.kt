package com.goodsogood.ows.service.pbm.impl

import com.goodsogood.ows.mapper.meeting.MeetingMapper
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import com.goodsogood.ows.service.pbm.IPbmOrgWorkDataService
import com.goodsogood.ows.service.pbm.PbmUserWorkDataService
import org.springframework.stereotype.Service

/**
 * 党员大会按时出勤率
 * <AUTHOR>
 * @createTime 2022-06-24
 */
@Service("MEETING_PARTY_MEMBER")
class PbmPartyMemberServiceImpl(val meetingMapper: MeetingMapper) : PbmUserWorkDataService {
    override fun getUserWorkData(userId: Long, startTime: String?, endTime: String?, week: Int?, regionId: Long, workItem: WorkItemEntity): Any? {
        return meetingMapper.meetingJoinRate(userId, "党支部党员大会",startTime, endTime)
    }
}

/**
 * 党小组会按时出勤率
 * <AUTHOR>
 * @createTime 2022-06-24
 */
@Service("MEETING_PARTY_GROUP")
class PbmPartyGroupServiceImpl(val meetingMapper: MeetingMapper) : PbmUserWorkDataService {
    override fun getUserWorkData(userId: Long, startTime: String?, endTime: String?, week: Int?, regionId: Long, workItem: WorkItemEntity): Any? {
        return meetingMapper.meetingJoinRate(userId, "党小组会",startTime, endTime)
    }
}

/**
 * 党课按时出勤率
 * <AUTHOR>
 * @createTime 2022-06-24
 */
@Service("MEETING_PARTY_CLASS")
class PbmPartyClassServiceImpl(val meetingMapper: MeetingMapper) : PbmUserWorkDataService {
    override fun getUserWorkData(userId: Long, startTime: String?, endTime: String?, week: Int?, regionId: Long, workItem: WorkItemEntity): Any? {
        return meetingMapper.meetingJoinRate(userId, "党课",startTime, endTime)
    }
}

/**
 * 主题党日按时出勤率
 * <AUTHOR>
 * @createTime 2022-06-24
 */
@Service("MEETING_PARTY_DAY")
class PbmPartyDayServiceImpl(val meetingMapper: MeetingMapper) : PbmUserWorkDataService {
    override fun getUserWorkData(userId: Long, startTime: String?, endTime: String?, week: Int?, regionId: Long, workItem: WorkItemEntity): Any? {
        return meetingMapper.meetingJoinRate(userId, "主题党日",startTime, endTime)
    }
}

/**
 *  讲党课次数
 * <AUTHOR>
 * @createTime 2022-06-24
 */
@Service("MEETING_PARTY_CLASS_COUNT")
class PbmPartyDayCountServiceImpl(val meetingMapper: MeetingMapper) : PbmUserWorkDataService {
    override fun getUserWorkData(userId: Long, startTime: String?, endTime: String?, week: Int?, regionId: Long, workItem: WorkItemEntity): Any? {
        return meetingMapper.partyClassCount(userId, startTime, endTime)
    }
}

/**
 * 党员大会组织完成情况
 * 返回 未完成数量
 * <AUTHOR>
 * @createTime 2022-06-24
 */
@Service("MEETING_PARTY_MEMBER_ORG")
class IPbmPartyMemberOrgServiceImpl(val meetingMapper: MeetingMapper) : IPbmOrgWorkDataService {
    override fun getOrgWorkData(unitId: Long, startTime: String?, endTime: String?, week: Int?, regionId: Long, workItem: WorkItemEntity): Any? {
        val cn = meetingMapper.meetingUnfinishedCountByUnitId(1, unitId,startTime, endTime)
        return cn<=0
    }
}

/**
 * 党支部委员会会议组织完成情况
 * 返回 未完成数量
 * <AUTHOR>
 * @createTime 2022-06-24
 */
@Service("MEETING_PARTY_COMMITTEE_ORG")
class IPbmPartyCommitteeOrgServiceImpl(val meetingMapper: MeetingMapper) : IPbmOrgWorkDataService {
    override fun getOrgWorkData(unitId: Long, startTime: String?, endTime: String?, week: Int?, regionId: Long, workItem: WorkItemEntity): Any? {
        return meetingMapper.meetingUnfinishedCountByUnitId(2, unitId,startTime, endTime)
    }
}

/**
 * 党小组会组织完成情况
 * 返回 未完成数量
 * <AUTHOR>
 * @createTime 2022-06-24
 */
@Service("MEETING_PARTY_GROUP_ORG")
class IPbmPartyGroupOrgServiceImpl(val meetingMapper: MeetingMapper) : IPbmOrgWorkDataService {
    override fun getOrgWorkData(unitId: Long, startTime: String?, endTime: String?, week: Int?, regionId: Long, workItem: WorkItemEntity): Any? {
        return meetingMapper.meetingUnfinishedCountPartyGroup(unitId,startTime, endTime)
    }
}

/**
 * 党课组织完成情况
 * 返回 未完成数量
 * <AUTHOR>
 * @createTime 2022-06-24
 */
@Service("MEETING_PARTY_CLASS_ORG")
class IPbmPartyClassOrgServiceImpl(val meetingMapper: MeetingMapper) : IPbmOrgWorkDataService {
    override fun getOrgWorkData(unitId: Long, startTime: String?, endTime: String?, week: Int?, regionId: Long, workItem: WorkItemEntity): Any? {
        val cn = meetingMapper.meetingUnfinishedCountByUnitId(4, unitId,startTime, endTime)
        return cn<=0
    }
}

/**
 * 主题党日组织完成情况
 * 返回 未完成数量
 * <AUTHOR>
 * @createTime 2022-06-24
 */
@Service("MEETING_PARTY_DAY_ORG")
class IPbmPartyDayOrgServiceImpl(val meetingMapper: MeetingMapper) : IPbmOrgWorkDataService {
    override fun getOrgWorkData(unitId: Long, startTime: String?, endTime: String?, week: Int?, regionId: Long, workItem: WorkItemEntity): Any? {
        val cn = meetingMapper.meetingUnfinishedCountByUnitId(5, unitId,startTime, endTime)
        return cn<=0
    }
}
