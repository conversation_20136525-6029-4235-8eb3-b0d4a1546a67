package com.goodsogood.ows.service.pbm.impl

import com.goodsogood.ows.mapper.user.UserMapper
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import com.goodsogood.ows.service.pbm.PbmUserWorkDataService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * 个人成长轨迹实现
 */
@Service("USER_HIGHLIGHT")
class UserHighlightServiceImpl(val userMapper: UserMapper) : PbmUserWorkDataService {

    val log: Logger = LoggerFactory.getLogger(UserHighlightServiceImpl::class.java)
    /**
     * 个人成长轨迹完成情况
     */
    override fun getUserWorkData(userId: Long, startTime: String?, endTime: String?,
                                 week: Int?, regionId: Long, workItem: WorkItemEntity
    ): Any? {

      val startNewTime= "$startTime 00:00:00";
      val startEndTime= "$endTime 23:59:59";
      log.info("UserHighlightServiceImpl.getUserWorkData,userId={},startNewTime={},startEndTime={}",userId,
              startNewTime, startEndTime)
      return  userMapper.userHighlight(userId, startNewTime, startEndTime)
    }
}