package com.goodsogood.ows.service.pbm.impl

import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import com.goodsogood.ows.model.mongodb.clickSpy.NewsListEntity
import com.goodsogood.ows.model.vo.news.NewsListFrom
import com.goodsogood.ows.service.experience.UserTaskService
import com.goodsogood.ows.service.pbm.PbmUserWorkDataService
import com.goodsogood.ows.service.user.OrgMongoService
import com.goodsogood.ows.utils.MongoAggProjectUtils
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.AggregationOptions
import org.springframework.data.mongodb.core.aggregation.DateOperators
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.stereotype.Service
import java.text.SimpleDateFormat
import java.util.*


/**
 * 新闻
 * <AUTHOR>
 * @Date 2022-06-23 10:29:00
 * @Description PbmNewsStudyServiceImpl
 *
 */
@Service("NEWS_STUDY")
class PbmNewsStudyServiceImpl(
    @Autowired val mongoTemplate: MyMongoTemplate,
    @Autowired val userTaskService: UserTaskService,
) : PbmUserWorkDataService {
    val log: Logger = LogManager.getLogger(OrgMongoService::class.java)
    override fun getUserWorkData(
        userId: Long,
        startTime: String?,
        endTime: String?,
        week: Int?,
        regionId: Long,
        workItem: WorkItemEntity,
    ): Any? {
        val sdf = SimpleDateFormat("yyyy-MM-dd")
        val calendar = Calendar.getInstance() // 获取当前日期
        calendar.time = sdf.parse(startTime)
        calendar[Calendar.HOUR_OF_DAY] = 0
        calendar[Calendar.MINUTE] = 0
        calendar[Calendar.SECOND] = 0
        calendar[Calendar.MILLISECOND] = 0
        val begin = Date(calendar.timeInMillis)
        calendar.time = sdf.parse(endTime)
        calendar[Calendar.HOUR_OF_DAY] = 23
        calendar[Calendar.MINUTE] = 59
        calendar[Calendar.SECOND] = 59
        calendar[Calendar.MILLISECOND] = 0
        val end = Date(calendar.timeInMillis)
        log.debug("查询新闻中心用户看新闻的数据断点二【${begin},${end}】")
        val criteria = Criteria.where("type").`is`("A")
            .and("regionId").`is`(regionId)
            .and("prefix").`in`("1", "4", "18")
            .and("userId").`is`(userId.toString())
            .and("transferTime").gte(MongoAggProjectUtils.dateToISODate(begin))
            .lte(MongoAggProjectUtils.dateToISODate(end))
        val aggregationOptions = AggregationOptions.builder().allowDiskUse(true).build()
        val aggregation = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.project("transferTime")
                .and(DateOperators.DateToString.dateOf("transferTime").toString("%Y-%m-%d"))
                .`as`("date"),
            Aggregation.group("date").count().`as`("count"),
            Aggregation.project("date", "count").and("date").previousOperation()
        ).withOptions(aggregationOptions)
        log.debug("查询新闻中心用户看新闻的数据断点三【${aggregation}】")
        val newsList = mongoTemplate.aggregate(
            aggregation,
            mongoTemplate.getCollectionName(NewsListEntity::class.java),
            NewsListFrom::class.java).mappedResults
        var count = 0
        if (newsList.isNotEmpty()) {
            newsList.forEach { count += it.count!! }
        }
        return count
    }


}