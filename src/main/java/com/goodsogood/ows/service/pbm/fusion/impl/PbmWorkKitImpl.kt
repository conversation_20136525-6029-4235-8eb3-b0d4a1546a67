package com.goodsogood.ows.service.pbm.fusion.impl

import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.PbmWorkDataConfig
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData
import com.goodsogood.ows.model.mongodb.pbm.PbmUnitKitInfo
import com.goodsogood.ows.service.pbm.fusion.FusionResultUtils
import com.goodsogood.ows.service.pbm.fusion.IFusionDataService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.stereotype.Service
import java.text.SimpleDateFormat
import java.util.Calendar

/**
 *
 * <AUTHOR>
 * @createTime 2023年03月16日 10:02:00
 */
@Service("WORK_KIT")
class PbmWorkKitImpl @Autowired constructor(
    private val mongoTemplate: MyMongoTemplate,
    private val pbmWorkDataConfig: PbmWorkDataConfig
) : IFusionDataService {

    override fun collect(
        unitId: Long,
        unitName: String,
        itemId: Long,
        year: Int,
        month: Int,
        regionId: Long
    ): MutableList<out FusionBaseData> {
        return mutableListOf()
    }

    override fun calculate(
        itemList: MutableList<FusionItemData>,
        year: Int,
        month: Int,
        regionId: Long,
        itemEntity: PbmFusionItemEntity
    ): MutableList<FusionItemData> {
        val unitIds = itemList.map { it.unitId }
        val cal = Calendar.getInstance()
        cal.set(Calendar.YEAR, year)
        cal.set(Calendar.MONTH, month - 2)
        val format = SimpleDateFormat("yyyy-MM")
        val criteria = Criteria.where("unitId").`in`(unitIds)
            .and("regionId").`is`(regionId)
            .and("date").`is`(format.format(cal.time))
        val query = Query(criteria)
        val kitInfos = this.mongoTemplate.find(query, PbmUnitKitInfo::class.java)
        val kitInfoMap = kitInfos.groupBy { it.unitId }.mapValues { (_, v) ->
            v[0]
        }
        itemList.forEach {
            val kitInfo = kitInfoMap[it.unitId]
            it.itemScore = kitInfo?.goodnessOfKit ?: 0.0
        }
        FusionResultUtils.rank(itemList, itemEntity.baseScore, pbmWorkDataConfig)
        itemList.forEach {
            if (it.rank != null && it.rank != 0) {
                it.result = "排名：${it.rank}"
            } else {
                it.result = "排名：--"
            }
        }
        return itemList
    }
}