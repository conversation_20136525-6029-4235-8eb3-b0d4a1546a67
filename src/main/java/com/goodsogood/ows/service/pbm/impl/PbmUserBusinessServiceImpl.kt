package com.goodsogood.ows.service.pbm.impl

import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.PbmWorkDataConfig
import com.goodsogood.ows.mapper.dataworks.PartyBusinessRuleMapper
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import com.goodsogood.ows.model.mongodb.pbm.BusinessForm
import com.goodsogood.ows.model.mongodb.pbm.UserWorkDetailInfo
import com.goodsogood.ows.service.pbm.PbmUserWorkDataService
import com.goodsogood.ows.service.user.UserMongoService
import org.springframework.stereotype.Service
import java.io.File
import java.time.LocalDateTime
import java.util.*

/**
 *
 * <AUTHOR>
 * @Date 2022-06-22 9:38:18
 * @Description PbmPartyBusinessServiceImpl
 *
 */
@Service("USER_BUSINESS")
class PbmUserBusinessServiceImpl(
    val mongoTemplate: MyMongoTemplate,
    val partyBusinessRuleMapper: PartyBusinessRuleMapper,
    val pbmWorkDataConfig: PbmWorkDataConfig,
    val userMongoService: UserMongoService
) : PbmUserWorkDataService {

    /**
     * 业务工作
     */
    override fun getUserWorkData(
        userId: Long,
        startTime: String?,
        endTime: String?,
        week: Int?,
        regionId: Long,
        workItem: WorkItemEntity
    ): Any? {
        val calDate = startTime?.replace("-", "")?.substring(0, 6)?.toInt()
        return workItem.ruleId?.let { calDate?.let { it1 -> this.findUserBusiness(userId, it, it1) } }?.calResult
    }

    override fun dealData(
        userId: Long,
        rankValue: Long?,
        value: Any?,
        year: Int,
        month: Int,
        week: Int?,
        entity: WorkItemEntity
    ): UserWorkDetailInfo {
        val desc = if (value != null) {
            entity.description?.format(value)
        } else {
            entity.description?.format(0)
        }
        val calDate: Int =
            if (month > 10) year.toString().plus(month).toInt() else year.toString().plus("0").plus(month).toInt()
        val businessForm = this.findUserBusiness(userId, entity.ruleId!!, calDate)
        // 客户拜访时长 ruleId = 5
        val resultCompare: String? = if (this.pbmWorkDataConfig.ruleIds!!.contains(entity.ruleId)) {
            businessForm?.explainStr
        } else {
            // 超过了%s的员工,根据排名查询百分比
            var per = this.partyBusinessRuleMapper.findUserPer(userId, entity.ruleId, calDate)
            if (Objects.isNull(per)) per = "0%"
            // 获取用户所属序列
            val sequence = this.userMongoService.getUserSequence(userId)
            val content = if (sequence != null && sequence >= 0) this.pbmWorkDataConfig.contentMap[sequence] else ""
            entity.compareDescription?.format(content.plus(per))
        }
        return UserWorkDetailInfo(
            userId = userId,
            regionId = entity.regionId,
            workItemId = entity.workItemId,
            workItemName = entity.name,
            cycle = entity.cycle,
            criterion = entity.criterion,
            year = year,
            month = month,
            week = week,
            rankValue = rankValue,
            workResult = desc,
            resultCompare = resultCompare,
            remark = entity.remark,
            type = entity.type,
            createTime = LocalDateTime.now()
        )
    }

    override fun getResultCompare(
        file: File,
        total: Long,
        workItem: WorkItemEntity,
        entity: UserWorkDetailInfo
    ): UserWorkDetailInfo? {
        return entity
    }

    private fun findUserBusiness(userId: Long, ruleId: Long, calDate: Int): BusinessForm? {
        return this.partyBusinessRuleMapper.findUserBusiness(userId, ruleId, calDate)
    }
}

