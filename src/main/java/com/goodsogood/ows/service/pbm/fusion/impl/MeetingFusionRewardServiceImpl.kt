package com.goodsogood.ows.service.pbm.fusion.impl

import com.goodsogood.ows.configuration.ExcludeOrgConfig
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.PbmWorkDataConfig
import com.goodsogood.ows.helper.NumEncryptUtils
import com.goodsogood.ows.mapper.meeting.MeetingOrgCommendPenalizeMapper
import com.goodsogood.ows.mapper.meeting.UserCommendPenalizeMapper
import com.goodsogood.ows.mapper.user.OptionMapper
import com.goodsogood.ows.mapper.user.OrganizationMapper
import com.goodsogood.ows.mapper.user.UserMapper
import com.goodsogood.ows.model.db.meeting.UserCommendPenalizeEntity
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity
import com.goodsogood.ows.model.db.user.OptionEntity
import com.goodsogood.ows.model.mongodb.fusion.ExcellentDetail
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData
import com.goodsogood.ows.service.pbm.CalculatingTimeUtils
import com.goodsogood.ows.service.pbm.fusion.IFusionDataService
import com.goodsogood.ows.utils.ArithUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import tk.mybatis.mapper.entity.Example
import java.util.stream.Collectors

/**
 * 创先争优情况
 * <AUTHOR>
 * @createTime 2023年03月13日 20:14:00
 */
@Service("FUSION_REWARD")
class MeetingFusionRewardServiceImpl @Autowired constructor(
    private val userMapper: UserMapper,
    private val optionMapper: OptionMapper,
    private val mongoTemplate: MyMongoTemplate,
    private val excludeOrgConfig: ExcludeOrgConfig,
    private val pbmWorkDataConfig: PbmWorkDataConfig,
    private val organizationMapper: OrganizationMapper,
    private val userCommendPenalizeMapper: UserCommendPenalizeMapper,
    private val orgCommendPenalizeMapper: MeetingOrgCommendPenalizeMapper
) : IFusionDataService {

    override fun collect(
        unitId: Long,
        unitName: String,
        itemId: Long,
        year: Int,
        month: Int,
        regionId: Long
    ): MutableList<out FusionBaseData> {
        // 组织列表
        val orgIds = this.getOrgListByUnitId(
            unitId, regionId, excludeOrgConfig.orgIds,
            organizationMapper
        )
        // 人员列表
        val userList = this.getUserListByUnitId(
            unitId, regionId, excludeOrgConfig.orgIds,
            organizationMapper, userMapper
        )
        val userMap = userList.groupBy { it.userId }
        // 人员奖励信息
        val rewardByUser = if (userList.isNotEmpty()) {
            val userIds = userList.map { it.userId }
            this.userCommendPenalizeMapper.getCommendPenalizeByUser(userIds, year, month)
        } else {
            mutableListOf()
        }
        // 组织奖励信息
        val rewardByOrg = if (orgIds.isNotEmpty()) {
            this.orgCommendPenalizeMapper.getCommendPenalizeByOrg(orgIds, year, month)
        } else {
            mutableListOf()
        }
        // 字典表
        val optionMap: Map<Int, List<OptionEntity>> = optionMapper.likeCode("1048")
            .groupBy { it.opKey }
        val mutableList = mutableListOf<ExcellentDetail>()
        // 循环人员奖励
        for (reward in rewardByUser) {
            val score = pbmWorkDataConfig.rewardMap?.get(reward.level)?: continue
            val userInfo = userMap[reward.userId]?.get(0) ?: continue
            val levelOp = optionMap[reward.level]?.get(0)
            val nameOp = optionMap[reward.name]?.get(0)
            val detail = ExcellentDetail()
            detail.also {
                it.unitId = unitId
                it.unitName = unitName
                it.itemId = itemId
                it.year = year
                it.month = month
                it.sourceId = reward.userId
                it.sourceName = reward.userName
                it.phone = NumEncryptUtils.decrypt(userInfo.phone, userInfo.phoneSecret)
                it.rewardId = reward.meetingUserCommendPenalizeId
                it.rewardLevel = levelOp?.opValue
                it.rewardName = nameOp?.opValue
                it.score = score
            }
            mutableList.add(detail)
        }
        // 循环组织奖励
        for (reward in rewardByOrg) {
            val score = pbmWorkDataConfig.rewardMap?.get(reward.level)?: continue
            val levelOp = optionMap[reward.level]?.get(0)
            val nameOp = optionMap[reward.name]?.get(0)
            val detail = ExcellentDetail()
            detail.also {
                it.unitId = unitId
                it.unitName = unitName
                it.itemId = itemId
                it.year = year
                it.month = month
                it.sourceId = reward.orgId
                it.sourceName = reward.orgName
                it.rewardId = reward.meetingOrgCommendPenalizeId
                it.rewardLevel = levelOp?.opValue
                it.rewardName = nameOp?.opValue
                it.score = score
            }
            mutableList.add(detail)
        }
        return mutableList
    }

    override fun calculate(
        itemList: MutableList<FusionItemData>,
        year: Int,
        month: Int,
        regionId: Long,
        itemEntity: PbmFusionItemEntity
    ): MutableList<FusionItemData> {
        val unitList = itemList.mapNotNull { it.unitId }
        val timeRange = CalculatingTimeUtils.getMeetingTimeRange(year, month, itemEntity.cycle!!)
        // 查询基础数据
        val collectData = this.getCollectData(
            clazz = ExcellentDetail::class.java,
            mongoTemplate = mongoTemplate,
            unitIds = unitList,
            itemId = itemEntity.fusionItemId!!,
            year = timeRange.startYear!!,
            month = timeRange.startMonth,
            pbmWorkDataConfig = pbmWorkDataConfig
        )
        var maxScore = 0.0
        val scoreMap = collectData.groupBy { it.unitId }
            .mapValues { (k, v) ->
                val itemScore = v.sumOf { it.score }
                if (itemScore > maxScore) {
                    maxScore = itemScore
                }
                FusionItemData(
                    unitId = k,
                    itemScore = itemScore
                )
            }
        itemList.forEach {
            var itemScore: Double? = 0.0
            if (scoreMap.containsKey(it.unitId)) {
                val data = scoreMap[it.unitId]
                if (data != null) {
                    itemScore = data.itemScore
                    it.itemScore = itemScore
                }
            }
            it.score = if (maxScore != 0.0) {
                ArithUtils.round(ArithUtils.div(itemScore?: 0.0, maxScore) * 10, 2)
            } else {
                0.0
            }
            it.result = "最高分：${maxScore} \n本单位赋分：$itemScore"
            it.maxScore = maxScore
        }
        return itemList
    }
}