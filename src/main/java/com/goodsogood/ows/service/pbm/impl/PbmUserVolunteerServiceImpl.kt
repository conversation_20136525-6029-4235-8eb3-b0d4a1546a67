package com.goodsogood.ows.service.pbm.impl

import com.goodsogood.ows.mapper.activity.RevisitMapper
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import com.goodsogood.ows.service.pbm.PbmUserWorkDataService
import org.springframework.stereotype.Service

/**
 * 用户入党志愿书
 */
@Service("USER_VOLUNTEER")
class PbmUserVolunteerServiceImpl (val revisitMapper: RevisitMapper) : PbmUserWorkDataService {

    override fun getUserWorkData(userId: Long, startTime: String?, endTime: String?, week: Int?, regionId: Long, workItem: WorkItemEntity): Any? {
        return revisitMapper.staRevisitDays(2, userId, startTime, endTime, regionId)
    }
}