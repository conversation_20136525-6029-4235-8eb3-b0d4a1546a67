package com.goodsogood.ows.service.pbm

import com.github.phantomthief.collection.BufferTrigger
import com.github.phantomthief.collection.impl.MultiIntervalTriggerStrategy
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.model.mongodb.pbm.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit
import javax.annotation.PostConstruct
import javax.annotation.PreDestroy


@Service
class PbmBufferTriggerService(val mongoTemplate: MyMongoTemplate) {

    val log: Logger = LoggerFactory.getLogger(PbmBufferTriggerService::class.java)

    lateinit var bufferTrigger: BufferTrigger<Any>

    @PostConstruct
    fun initBufferTrigger() {

        bufferTrigger = BufferTrigger.simple<Any, Set<Any>>()
                .triggerStrategy(MultiIntervalTriggerStrategy()
                        .on(10, TimeUnit.SECONDS, 1)
                        .on(5, TimeUnit.SECONDS, 50)
                        .on(1, TimeUnit.SECONDS, 200))
                .consumer(this::insert)
                .enableBackPressure()
                .maxBufferCount(100_000)
                .build()
    }

    fun insert(list: Collection<Any>) {
        list.forEach {
            when(it) {
                is UserWorkDetailInfo -> insertOrUpdate(it)
                is OrgWorkDetailInfo -> insertOrUpdate(it)
                is PbmUserKitInfo -> insertOrUpdate(it)
                is PbmUnitKitInfo -> insertOrUpdate(it)
                is PbmOrgUserKitInfo -> insertOrUpdate(it)
                else -> {
                    log.debug("PbmBufferTriggerService新增失败 ${it}，类型不匹配")
                }
            }
        }
    }

    @Async("pbmAddBufferExecutor")
    fun addBuffer(any: Any) {
        this.bufferTrigger.enqueue(any)
    }

    @PreDestroy
    fun beforeDestroy() {
        this.bufferTrigger.manuallyDoTrigger()
    }

    /**
     * @title 新增或更新人员工作详情
     * <AUTHOR>
     */
    private fun insertOrUpdate(entity: UserWorkDetailInfo) {
        // 查询条件
        val criteria = Criteria.where("userId").`is`(entity.userId)
            .and("workItemId").`is`(entity.workItemId)
            .and("year").`is`(entity.year)
            .and("month").`is`(entity.month)
            .and("type").`is`(entity.type)
            .and("week").`is`(entity.week)
        val query = Query(criteria)
        // 更新条件
        val update = Update()
        update.set("userId", entity.userId)
            .set("regionId", entity.regionId)
            .set("year", entity.year)
            .set("month", entity.month)
            .set("week", entity.week)
            .set("workItemId", entity.workItemId)
            .set("workItemName", entity.workItemName)
            .set("cycle", entity.cycle)
            .set("criterion", entity.criterion)
            .set("criterion", entity.criterion)
            .set("rankValue", entity.rankValue)
            .set("resultCompare", entity.resultCompare)
            .set("workResult", entity.workResult)
            .set("remark", entity.remark)
            .set("type", entity.type)
            .set("createTime", entity.createTime)
            .set("updateTime", entity.updateTime)
        mongoTemplate.upsert(query, update, UserWorkDetailInfo::class.java)
    }

    /**
     * @title 新增或更新组织工作详情
     * <AUTHOR>
     */
    private fun insertOrUpdate(entity: OrgWorkDetailInfo) {
        // 查询条件
        val criteria = Criteria.where("orgId").`is`(entity.orgId)
            .and("workItemId").`is`(entity.workItemId)
            .and("year").`is`(entity.year)
            .and("month").`is`(entity.month)
            .and("type").`is`(entity.type)
            .and("week").`is`(entity.week)
        val query = Query(criteria)
        // 更新条件
        val update = Update()
        update.set("orgId", entity.orgId)
            .set("regionId", entity.regionId)
            .set("year", entity.year)
            .set("month", entity.month)
            .set("week", entity.week)
            .set("workItemId", entity.workItemId)
            .set("workItemName", entity.workItemName)
            .set("cycle", entity.cycle)
            .set("criterion", entity.criterion)
            .set("criterion", entity.criterion)
            .set("rankValue", entity.rankValue)
            .set("resultCompare", entity.resultCompare)
            .set("workResult", entity.workResult)
            .set("remark", entity.remark)
            .set("type", entity.type)
            .set("createTime", entity.createTime)
            .set("updateTime", entity.updateTime)
        mongoTemplate.upsert(query, update, OrgWorkDetailInfo::class.java)
    }

    /**
     * @title 新增或更新单位拟合度实体
     * <AUTHOR>
     */
    private fun insertOrUpdate(entity: PbmUnitKitInfo) {
        // 查询条件
        val criteria = Criteria.where("unitId").`is`(entity.unitId)
            .and("date").`is`(entity.date)
        val query = Query(criteria)
        // 更新条件
        val update = Update()
        update.set("unitId", entity.unitId)
            .set("unitName", entity.unitName)
            .set("regionId", entity.regionId)
            .set("date", entity.date)
            .set("business", entity.business)
            .set("businessTotal", entity.businessTotal)
            .set("partyBuild", entity.partyBuild)
            .set("partyBuildTotal", entity.partyBuildTotal)
            .set("businessTotalLog", entity.businessTotalLog)
            .set("partyBuildTotalLog", entity.partyBuildTotalLog)
            .set("goodnessOfKit", entity.goodnessOfKit)
            .set("avg", entity.avg)
            .set("rank", entity.rank)
            .set("high", entity.high)
            .set("self", entity.self)
            .set("createTime", entity.createTime)
            .set("updateTime", entity.updateTime)
        mongoTemplate.upsert(query, update, PbmUnitKitInfo::class.java)
    }

    /**
     * @title 新增或更新用户拟合度实体
     * <AUTHOR>
     */
    private fun insertOrUpdate(entity: PbmUserKitInfo) {
        // 查询条件
        val criteria = Criteria.where("userId").`is`(entity.userId)
            .and("date").`is`(entity.date)
        val query = Query(criteria)
        // 更新条件
        val update = Update()
        update.set("userId", entity.userId)
            .set("userName", entity.userName)
            .set("phone", entity.phone)
            .set("politicalType", entity.politicalType)
            .set("sequence", entity.sequence)
            .set("orgId", entity.orgId)
            .set("orgName", entity.orgName)
            .set("unitId", entity.unitId)
            .set("unitName", entity.unitName)
            .set("title", entity.title)
            .set("selfRank", entity.selfRank)
            .set("sequenceRank", entity.sequenceRank)
            .set("regionId", entity.regionId)
            .set("date", entity.date)
            .set("business", entity.business)
            .set("businessTotal", entity.businessTotal)
            .set("partyBuild", entity.partyBuild)
            .set("partyBuildTotal", entity.partyBuildTotal)
            .set("businessTotalLog", entity.businessTotalLog)
            .set("partyBuildTotalLog", entity.partyBuildTotalLog)
            .set("goodnessOfKit", entity.goodnessOfKit)
            .set("createTime", entity.createTime)
            .set("updateTime", entity.updateTime)
        mongoTemplate.upsert(query, update, PbmUserKitInfo::class.java)
    }

    /**
     * @title 新增或更新单位拟合度实体
     * <AUTHOR>
     */
    private fun insertOrUpdate(entity: PbmOrgUserKitInfo) {
        // 查询条件
        val criteria = Criteria.where("unitId").`is`(entity.unitId)
            .and("date").`is`(entity.date)
            .and("type").`is`(entity.type)
        val query = Query(criteria)
        // 更新条件
        val update = Update()
        update.set("unitId", entity.unitId)
            .set("unitName", entity.unitName)
            .set("regionId", entity.regionId)
            .set("date", entity.date)
            .set("type", entity.type)
            .set("selfBusinessAvg", entity.selfBusinessAvg)
            .set("selfPartyAvg", entity.selfPartyAvg)
            .set("selfKitAvg", entity.selfKitAvg)
            .set("allBusinessAvg", entity.allBusinessAvg)
            .set("allKitAvg", entity.allKitAvg)
            .set("businessExtreme", entity.businessExtreme)
            .set("partyBuildExtreme", entity.partyBuildExtreme)
            .set("allPartyMedian", entity.allPartyMedian)
            .set("allNonPartyMedian", entity.allNonPartyMedian)
            .set("maxUserKitInfo", entity.maxUserKitInfo)
            .set("maxUserPartyInfo", entity.maxUserPartyInfo)
            .set("createTime", entity.createTime)
            .set("updateTime", entity.updateTime)
        mongoTemplate.upsert(query, update, PbmOrgUserKitInfo::class.java)
    }
}