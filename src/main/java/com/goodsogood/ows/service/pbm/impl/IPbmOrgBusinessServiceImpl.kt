package com.goodsogood.ows.service.pbm.impl

import com.goodsogood.ows.common.Constants
import com.goodsogood.ows.configuration.PbmWorkDataConfig
import com.goodsogood.ows.mapper.dataworks.PartyBusinessCalResultMapper
import com.goodsogood.ows.mapper.dataworks.PartyBusinessRuleMapper
import com.goodsogood.ows.model.db.tbcFusion.PartyBusinessCalResultEntity
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import com.goodsogood.ows.model.mongodb.pbm.BusinessForm
import com.goodsogood.ows.model.mongodb.pbm.OrgWorkDetailInfo
import com.goodsogood.ows.service.pbm.IPbmOrgWorkDataService
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import tk.mybatis.mapper.entity.Example
import java.io.File
import java.time.LocalDateTime

/**
 *
 * <AUTHOR>
 * @Date 2022-06-22 17:58:04
 * @Description PbmOrgBusinessServiceImpl
 *
 */
@Service("ORG_BUSINESS")
class IPbmOrgBusinessServiceImpl(
    val partyBusinessRuleMapper: PartyBusinessRuleMapper,
    val pbmWorkDataConfig: PbmWorkDataConfig,
    val partyBusinessCalResultMapper: PartyBusinessCalResultMapper
) : IPbmOrgWorkDataService {
    val log: Logger = LoggerFactory.getLogger(IPbmOrgWorkDataService::class.java)
    override fun getOrgWorkData(
        orgId: Long,
        startTime: String?,
        endTime: String?,
        week: Int?,
        regionId: Long,
        workItem: WorkItemEntity
    ): Any? {
        // 如果考核周期是 1-周，通过时间范围转换为周
        var calDate: Int? = null
        var yearDate: Int? = null
        if (workItem.cycle == Constants.CYCLE_WEEK) {
            // 根据周数查询指标对应结果
            yearDate = startTime?.substring(0, 4)?.toInt()
        } else {
            // 考核周期为 2-月
            calDate = DateTime.parse(startTime, DateTimeFormat.forPattern("yyyy-MM-dd")).toString("yyyyMM").toInt()
            log.debug("店铺管理系统推广到位率-calDate-$calDate,参数$orgId,$startTime,$endTime,$week,$regionId,$workItem")
        }
        return workItem.ruleId?.let {
            this.findOrgBusiness(
                ruleId = it,
                orgId = orgId,
                calDate = calDate,
                yearDate = yearDate,
                week = week
            )?.calResult
        }
    }

    override fun dealData(
        orgId: Long,
        rankValue: Long?,
        value: Any?,
        year: Int,
        month: Int,
        week: Int?,
        entity: WorkItemEntity
    ): OrgWorkDetailInfo {
        val desc = if (value != null) {
            entity.description?.format("$value%")
        } else {
            entity.description?.format("0%")
        }
        var resultCompare: String? = entity.compareDescription?.format("")
        var calDate: Int? = null
        var yearDate: Int? = null
        if (entity.cycle == Constants.CYCLE_WEEK) {
            // 根据周数查询指标对应结果
            yearDate = year
        } else {
            // 考核周期为 2-月
            calDate = if (month <= 10) year.toString().plus("0").plus(month.toString()).toInt() else year.toString()
                .plus(month.toString()).toInt()
        }
        val orgBusinessForm = entity.ruleId?.let { this.findOrgBusiness(it, orgId, calDate, yearDate, week) }
        if (orgBusinessForm != null) {
            // ruleId=4 客户毛利率
            resultCompare = if (this.pbmWorkDataConfig.ruleIds!!.contains(entity.ruleId)) {
                orgBusinessForm.explainStr
            } else {
                // 超过了%s家单位-根据排名算
                val example = Example(PartyBusinessCalResultEntity::class.java)
                example.createCriteria().andGreaterThan("rankNum", orgBusinessForm.rankNum)
                    .andEqualTo("regionId", entity.regionId)
                    .andEqualTo("calDate", calDate)
                    .andEqualTo("ruleId", entity.ruleId)
                val counts = this.partyBusinessCalResultMapper.selectCountByExample(example)
                entity.compareDescription?.format(counts)
            }
        }
        return OrgWorkDetailInfo(
            orgId = orgId,
            workItemId = entity.workItemId,
            workItemName = entity.name,
            cycle = entity.cycle,
            criterion = entity.criterion,
            type = entity.type,
            year = year,
            month = month,
            regionId = entity.regionId,
            week = week,
            workResult = desc,
            resultCompare = resultCompare,
            remark = entity.remark,
            createTime = LocalDateTime.now()
        )
    }

    override fun getResultCompare(
        file: File,
        total: Long,
        workItem: WorkItemEntity,
        entity: OrgWorkDetailInfo
    ): OrgWorkDetailInfo? {
        return entity
    }

    private fun findOrgBusiness(
        ruleId: Long,
        orgId: Long,
        calDate: Int? = null,
        yearDate: Int? = null,
        week: Int? = null
    ): BusinessForm? {
        log.debug("店铺管理系统推广到位率1-参数$ruleId,$orgId,$calDate,$yearDate,$week")
        return this.partyBusinessRuleMapper.findOrgBusiness(ruleId, orgId, calDate, yearDate, week)
    }

}