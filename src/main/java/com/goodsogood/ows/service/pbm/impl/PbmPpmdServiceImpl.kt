package com.goodsogood.ows.service.pbm.impl

import com.goodsogood.ows.common.ExternalSort
import com.goodsogood.ows.mapper.ppmd.PayLogMapper
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import com.goodsogood.ows.model.mongodb.pbm.UserWorkDetailInfo
import com.goodsogood.ows.service.pbm.IPbmOrgWorkDataService
import com.goodsogood.ows.service.pbm.PbmUserWorkDataService
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.io.File
import java.math.RoundingMode
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.time.LocalDateTime

/**
 * 党费交纳时间
 * <AUTHOR>
 * @createTime 2022-06-24
 */
@Service("PPMD_PAY_DATE")
class PbmPayDateServiceImpl(val payLogMapper: PayLogMapper) : PbmUserWorkDataService {
    override fun getUserWorkData(
        userId: Long,
        startTime: String?,
        endTime: String?,
        week: Int?,
        regionId: Long,
        workItem: WorkItemEntity
    ): Any? {
        return payLogMapper.findPayDate(regionId, userId, startTime?.substring(0, 7))
    }

    override fun dealData(
        userId: Long,
        rankValue: Long?,
        value: Any?,
        year: Int,
        month: Int,
        week: Int?,
        entity: WorkItemEntity
    ): UserWorkDetailInfo {
        var obj = entity.description
        obj = if (value != null) {
            val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
            obj?.format(format.format(value))
        } else {
            obj?.format("未交纳")
        }
        return UserWorkDetailInfo(
            userId = userId,
            regionId = entity.regionId,
            workItemId = entity.workItemId,
            workItemName = entity.name,
            cycle = entity.cycle,
            criterion = entity.criterion,
            year = year,
            month = month,
            week = week,
            rankValue = rankValue,
            workResult = obj,
            remark = entity.remark,
            type = entity.type,
            createTime = LocalDateTime.now()
        )
    }

    override fun getResultCompare(
        file: File,
        total: Long,
        workItem: WorkItemEntity,
        entity: UserWorkDetailInfo
    ): UserWorkDetailInfo? {
        val score = entity.rankValue
        // 取重复排名的第一个下标
        val rank = if (score != null) {
            ExternalSort.getDataLastIndex(file, score)
        } else {
            0
        }
        // 取去重排名的的下标
        val scoreRank = if (score != null) {
            ExternalSort.getDeduplicateRankIndex(file, score)
        } else {
            0
        }
        // 取去重后的下标
        val size = ExternalSort.getDataSize(file)
        // 百分比格式化
        val df = DecimalFormat("##.#")
        df.roundingMode = RoundingMode.CEILING
        var compare = ""
        when (workItem.compareType) {
            1 -> {
                compare = when (rank) {
                    -1L, 0L -> "100%"
                    else -> {
                        val re = (total - rank).toDouble().div(total.toDouble()) * 100
                        "${df.format(re)}%"
                    }
                }
            }
        }
        entity.resultCompare = workItem.compareDescription?.format(compare)
        entity.updateTime = LocalDateTime.now()
        return entity
    }
}

/**
 * 单位组织党费交纳率
 * <AUTHOR>
 * @createTime 2022-06-24
 */
@Service("PPMD_PAY_RATE")
class IPbmPayRateServiceImpl(val payLogMapper: PayLogMapper) : IPbmOrgWorkDataService {
    @Value("\${user-option.testBranch}")
    private val testBranch: String? = null
    override fun getOrgWorkData(
        unitId: Long,
        startTime: String?,
        endTime: String?,
        week: Int?,
        regionId: Long,
        workItem: WorkItemEntity
    ): Any? {
        // 排除所有不统计的测试组织
//        val testIds: String? =
        return payLogMapper.findOrgPayRate(regionId, unitId, startTime?.substring(0, 7), this.testBranch)
    }
}
