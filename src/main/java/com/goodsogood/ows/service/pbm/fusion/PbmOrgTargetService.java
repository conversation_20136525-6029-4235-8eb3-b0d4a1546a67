package com.goodsogood.ows.service.pbm.fusion;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.mapper.sas.PbmOrgTargetMapper;
import com.goodsogood.ows.model.db.sas.PbmOrgTargetEntity;
import com.goodsogood.ows.model.vo.pbm.PbmOrgTargetBuildVo;
import com.goodsogood.ows.model.vo.pbm.PbmOrgTargetPersonVo;
import com.goodsogood.ows.model.vo.pbm.PbmOrgTargetUnitVo;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

@Service
@Log4j2
public class PbmOrgTargetService {

    private final Errors errors;

    private final PbmOrgTargetMapper pbmOrgTargetMapper;

    public PbmOrgTargetService(Errors errors, PbmOrgTargetMapper pbmOrgTargetMapper){
        this.errors = errors;
        this.pbmOrgTargetMapper = pbmOrgTargetMapper;
    }

    public Page<PbmOrgTargetEntity> pbmOrgTargetList(Integer pageNo, Integer pageSize, String time, String orgName) {
        return PageHelper.startPage(pageNo, pageSize)
                .doSelectPage(() -> pbmOrgTargetMapper.pbmOrgTargetList(orgName, time));
    }

    public List<PbmOrgTargetUnitVo> getUnitList(String time, String orgName){
        return pbmOrgTargetMapper.getUnitList(orgName, time);
    }

    public void updatePbmOrgTarget(PbmOrgTargetEntity pbm, Long userId, Integer type) {
        Date now = new Date();
        Example example = new Example(PbmOrgTargetEntity.class);
        example.createCriteria().andEqualTo("orgId",pbm.getOrgId())
                .andEqualTo("time",pbm.getTime());
        PbmOrgTargetEntity pbmOrgTargetEntity = pbmOrgTargetMapper.selectOneByExample(example);
        if( pbmOrgTargetEntity != null){
            if (type == 3) {
                if (pbm.getExamineRemark() == null) {
                    pbmOrgTargetMapper.updateExamineRemark(pbm.getOrgId(),pbm.getTime());
                }
                if (pbm.getExamineScore() == null) {
                    pbmOrgTargetMapper.updateExamineScore(pbm.getOrgId(),pbm.getTime());
                }
            }else if (type == 2) {
                if (pbm.getSatisfiedRemark()== null){
                    pbmOrgTargetMapper.updateSatisfiedRemark(pbm.getOrgId(),pbm.getTime());
                }
                if (pbm.getSatisfiedScore() == null){
                    pbmOrgTargetMapper.updateSatisfiedScore(pbm.getOrgId(),pbm.getTime());
                }
            }else if (type == 1) {
                if (pbm.getTargetRemark()== null){
                    pbmOrgTargetMapper.updateTargetRemark(pbm.getOrgId(),pbm.getTime());
                }
                if (pbm.getTargetScore() == null){
                    pbmOrgTargetMapper.updateTargetScore(pbm.getOrgId(), pbm.getTime());
                }
            }
                pbm.setUpdateTime(now);
                pbm.setLastChangeUser(userId);
                pbmOrgTargetMapper.updateByPrimaryKeySelective(pbm);
        }else {
                pbm.setCreateTime(now);
                pbm.setLastChangeUser(userId);
                pbmOrgTargetMapper.insertSelective(pbm);
        }
        }

    public List<PbmOrgTargetPersonVo> getPersonList(String time, String orgName) {
        return pbmOrgTargetMapper.getPersonList(orgName, time);
    }

    public List<PbmOrgTargetBuildVo> getBuildList(String time, String orgName) {
        return pbmOrgTargetMapper.getBuildList(orgName, time);
    }
}
