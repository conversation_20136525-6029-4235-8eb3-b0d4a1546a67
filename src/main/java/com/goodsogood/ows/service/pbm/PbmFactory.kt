package com.goodsogood.ows.service.pbm

import com.goodsogood.ows.service.pbm.fusion.IFusionDataService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.concurrent.ConcurrentHashMap

/**
 * 用户工作详情工厂类
 * <AUTHOR>
 * @createTime 2022年06月21日 19:50:00
 */
@Component
class UserWorkDataFactory(
    @Autowired private val dataServiceMap: MutableMap<String, PbmUserWorkDataService> = ConcurrentHashMap()) {

    val log: Logger = LoggerFactory.getLogger(UserWorkDataFactory::class.java)

    fun getUserWorkData(name: String?): PbmUserWorkDataService? {
        val dataService = dataServiceMap[name]
        if (dataService == null) {
            log.error("未定义服务: $name")
            return null
        }
        return dataService
    }
}

/**
 * 组织工作详情工厂类
 */
@Component
class OrgWorkDataFactory(
    @Autowired private val dataServiceMap: MutableMap<String, IPbmOrgWorkDataService> = ConcurrentHashMap()) {

    val log: Logger = LoggerFactory.getLogger(OrgWorkDataFactory::class.java)

    fun getOrgWorkData(name: String?): IPbmOrgWorkDataService? {
        val dataService = dataServiceMap[name]
        if (dataService == null) {
            log.error("未定义服务: $name")
            return null
        }
        return dataService
    }
}

/**
 * 党业融合工厂类
 */
@Component
class FusionFactory(
    @Autowired
    private val dataServiceMap: MutableMap<String, IFusionDataService> = ConcurrentHashMap()) {

    val log: Logger = LoggerFactory.getLogger(FusionFactory::class.java)

    fun getFusionService(name: String?): IFusionDataService? {
        val dataService = dataServiceMap[name]
        if (dataService == null) {
            log.error("未定义服务: $name")
            return null
        }
        return dataService
    }
}