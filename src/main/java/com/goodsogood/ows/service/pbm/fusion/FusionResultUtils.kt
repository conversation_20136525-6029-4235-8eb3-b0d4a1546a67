package com.goodsogood.ows.service.pbm.fusion

import com.goodsogood.ows.configuration.PbmWorkDataConfig
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData
import com.goodsogood.ows.utils.ArithUtils
import java.util.*
import java.util.stream.Collectors

/**
 * <AUTHOR>
 * @createTime 2023年03月13日 11:39:00
 */
class FusionResultUtils {

    companion object {
        /**
         * 设置排名及排名对应的分值
         *
         * @param itemList  要排名的党业融合数据
         * @param baseScore 满分
         */
        fun rank(itemList: List<FusionItemData>, baseScore: Double, pbmWorkDataConfig: PbmWorkDataConfig) {
            //先降序排序
            val list = itemList.sortedByDescending { it.itemScore }
            var rank = 0
            var midScore: Double? = null
            var tempScore: Double? = null

            list.forEachIndexed a@ { i, fusionUnitData ->
                val itemScore = fusionUnitData.itemScore ?: return@a
                if (tempScore == null || itemScore.compareTo(tempScore!!) != 0) {
                    rank = i + 1
                    fusionUnitData.rank = rank
                    tempScore = list[i].itemScore
                    midScore = ArithUtils.round(baseScore * getRankDouble(rank, pbmWorkDataConfig), 2)
                    fusionUnitData.score = midScore!!
                } else {
                    fusionUnitData.rank = rank
                    fusionUnitData.score = midScore!!
                }
            }
        }

        /**
         * 获取排名对应的分值占比
         *
         * @param rank 排名
         * @return 分值占比 1~0
         */
        private fun getRankDouble(rank: Int, pbmWorkDataConfig: PbmWorkDataConfig): Double {
            val rankRange: Map<Double, String>? = pbmWorkDataConfig.rankRangeMap
            for ((key, value) in rankRange!!) {
                val rankRanges = Arrays.stream(value.split(",".toRegex()).dropLastWhile { it.isEmpty() }
                    .toTypedArray()).map { s: String -> s.toInt() }.collect(Collectors.toList())
                if (rank >= rankRanges[0] && rank <= rankRanges[1]) {
                    return key
                }
            }
            return 0.00
        }
    }
}