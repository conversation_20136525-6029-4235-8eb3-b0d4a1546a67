package com.goodsogood.ows.service.pbm

import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.ceil

/**
 *
 * <AUTHOR>
 * @createTime 2022年06月26日 16:21:00
 */
class CalculatingTimeUtils {

    companion object {

        /**
         * @title 计算时间
         * @description
         * <AUTHOR>
         * @param   year      年
         * @param   month     月
         * @param   week      周
         * @param   cycle     周期   1-周，2-月度，3-季度，4-年度，5-累计，6-半年
         * @updateTime 2022/6/22 21:15
         * @return
         * @throws
         */
        fun calculatingTime(year: Int, month: Int, week: Int? = null, cycle: Int? = null): MutableList<TimeRange> {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd")
            val mutableList = mutableListOf<TimeRange>()
            val cal = Calendar.getInstance(Locale.CHINA)
            cal.firstDayOfWeek = Calendar.MONDAY
            cal.minimalDaysInFirstWeek = 4
            cal.set(Calendar.YEAR, year)
            when (cycle) {
                // 周
                1 -> {
                    if (week != null) {
                        mutableList.add(calculateWeek(cal, week, dateFormat))
                    } else {
                        // week为空时, 需要计算月份包含的周数
                        cal.set(Calendar.MONTH, month - 1)
                        cal.set(Calendar.DAY_OF_MONTH, 1)
                        val startWeek = cal.get(Calendar.WEEK_OF_YEAR)
                        cal.add(Calendar.MONTH, 1)
                        cal.set(Calendar.DAY_OF_MONTH, 0)
                        val endWeek = cal.get(Calendar.WEEK_OF_YEAR)
                        (startWeek..endWeek).forEach { index ->
                            mutableList.add(calculateWeek(cal, index, dateFormat))
                        }
                    }
                }
                // 月度
                2 -> {
                    val timeRange = TimeRange()
                    cal.set(Calendar.MONTH, month - 1)
                    cal.set(Calendar.DAY_OF_MONTH, 1)
                    timeRange.startTime = dateFormat.format(cal.time)
                    timeRange.startTimestamp = cal.timeInMillis
                    timeRange.startYear = cal.get(Calendar.YEAR)
                    timeRange.startMonth = cal.get(Calendar.MONTH)
                    cal.add(Calendar.MONTH, 1)
                    cal.set(Calendar.DAY_OF_MONTH, 0)
                    timeRange.endTime = dateFormat.format(cal.time)
                    timeRange.endTimestamp = cal.timeInMillis
                    timeRange.endYear = cal.get(Calendar.YEAR)
                    timeRange.endMonth = cal.get(Calendar.MONTH)
                    mutableList.add(timeRange)
                }
                // 季度
                3 -> {
                    val timeRange = TimeRange()
                    //根据月份获取所在季度
                    val quarter = ceil(month / 3.0).toInt()
                    //所在季度的第一个月
                    //所在季度的第一个月
                    val startMonth = quarter * 3 - 2
                    cal.set(Calendar.MONTH, startMonth - 1)
                    cal.set(Calendar.DAY_OF_MONTH, 1)
                    timeRange.startTime = dateFormat.format(cal.time)
                    timeRange.startTimestamp = cal.timeInMillis
                    timeRange.startYear = cal.get(Calendar.YEAR)
                    timeRange.startMonth = cal.get(Calendar.MONTH)
                    cal.set(Calendar.MONTH, month)
                    cal.set(Calendar.DAY_OF_MONTH, 0)
                    timeRange.endTime = dateFormat.format(cal.time)
                    timeRange.endTimestamp = cal.timeInMillis
                    timeRange.endYear = cal.get(Calendar.YEAR)
                    timeRange.endMonth = cal.get(Calendar.MONTH)
                    mutableList.add(timeRange)
                }
                // 年度
                4 -> {
                    val timeRange = TimeRange()
                    cal.set(Calendar.DAY_OF_YEAR, 1)
                    timeRange.startTime = dateFormat.format(cal.time)
                    timeRange.startTimestamp = cal.timeInMillis
                    timeRange.startYear = cal.get(Calendar.YEAR)
                    timeRange.startMonth = cal.get(Calendar.MONTH)
                    cal.set(Calendar.MONTH, month)
                    cal.set(Calendar.DAY_OF_MONTH, 0)
                    timeRange.endTime = dateFormat.format(cal.time)
                    timeRange.endTimestamp = cal.timeInMillis
                    timeRange.endYear = cal.get(Calendar.YEAR)
                    timeRange.endMonth = cal.get(Calendar.MONTH)
                    mutableList.add(timeRange)
                }
                // 累计
                5 -> {
                    val timeRange = TimeRange()
                    cal.set(Calendar.MONTH, month)
                    cal.set(Calendar.DAY_OF_MONTH, 0)
                    timeRange.endTime = dateFormat.format(cal.time)
                    timeRange.endTimestamp = cal.timeInMillis
                    timeRange.endYear = cal.get(Calendar.YEAR)
                    timeRange.endMonth = cal.get(Calendar.MONTH)
                    mutableList.add(timeRange)
                }
                // 半年
                6 -> {
                    val timeRange = TimeRange()
                    if (month <= 6) {
                        cal.set(Calendar.DAY_OF_YEAR, 1)
                        timeRange.startTime = dateFormat.format(cal.time)
                        timeRange.startTimestamp = cal.timeInMillis
                        timeRange.startYear = cal.get(Calendar.YEAR)
                        timeRange.startMonth = cal.get(Calendar.MONTH)
                        cal.set(Calendar.MONTH, month)
                        cal.set(Calendar.DAY_OF_MONTH, 0)
                        timeRange.endTime = dateFormat.format(cal.time)
                        timeRange.endTimestamp = cal.timeInMillis
                        timeRange.endYear = cal.get(Calendar.YEAR)
                        timeRange.endMonth = cal.get(Calendar.MONTH)
                        mutableList.add(timeRange)
                    } else {
                        cal.set(Calendar.MONTH, 6)
                        cal.set(Calendar.DAY_OF_MONTH, 1)
                        timeRange.startTime = dateFormat.format(cal.time)
                        timeRange.startTimestamp = cal.timeInMillis
                        timeRange.startYear = cal.get(Calendar.YEAR)
                        timeRange.startMonth = cal.get(Calendar.MONTH)
                        cal.set(Calendar.MONTH, month)
                        cal.set(Calendar.DAY_OF_MONTH, 0)
                        timeRange.endTime = dateFormat.format(cal.time)
                        timeRange.endTimestamp = cal.timeInMillis
                        timeRange.endYear = cal.get(Calendar.YEAR)
                        timeRange.endMonth = cal.get(Calendar.MONTH)
                        mutableList.add(timeRange)
                    }
                }
            }
            return mutableList
        }

        //1-周，2-月度，3-季度，4-年度，5-累计, 6-半年
        fun getMeetingTimeRange(year: Int, month: Int, cycle: Int): TimeRange {
            val timeRange = TimeRange()
            when (cycle) {
                2 -> {
                    if (month == 1) {
                        timeRange.startMonth = 12
                        timeRange.endMonth = 12
                        timeRange.startYear = year - 1
                    } else {
                        timeRange.startMonth = month - 1
                        timeRange.endMonth = month - 1
                        timeRange.startYear = year
                    }
                    return timeRange
                }

                3 -> {
                    if (month <= 3) {
                        timeRange.startMonth = 10
                        timeRange.endMonth = 12
                        timeRange.startYear = year - 1
                    } else {
                        timeRange.startYear = year
                        val quart = if (month % 3 == 0) month / 3 else month / 3 + 1
                        val endMonth = (quart - 1) * 3
                        timeRange.startMonth = endMonth - 2
                        timeRange.endMonth = endMonth
                    }
                    return timeRange
                }

                4 -> {
                    timeRange.startYear = year - 1
                    timeRange.startMonth = 1
                    timeRange.endMonth = 12
                    return timeRange
                }

                6 -> {
                    if (month > 6) {
                        timeRange.startYear = year
                        timeRange.startMonth = 1
                        timeRange.endMonth = 6
                    } else {
                        timeRange.startYear = year - 1
                        timeRange.startMonth = 7
                        timeRange.endMonth = 12
                    }
                    return timeRange
                }
            }
            return timeRange
        }

        fun calculateBeforeMonth(year: Int, month: Int, size: Int = 6): MutableList<String> {
            val dateFormat = SimpleDateFormat("yyyy-MM")
            val list = mutableListOf<String>()
            val cal = Calendar.getInstance(Locale.CHINA)
            cal.set(Calendar.YEAR, year)
            cal.set(Calendar.MONTH, month - 1)
            for (i in 1..size) {
                cal.add(Calendar.MONTH, -1)
                list.add(dateFormat.format(cal.time))
            }
            return list
        }

        /**
         * @title 计算周
         * <AUTHOR>
         */
        private fun calculateWeek(cal: Calendar, week: Int, dateFormat: SimpleDateFormat): TimeRange {
            val timeRange = TimeRange()
            cal.set(Calendar.WEEK_OF_YEAR, week)
            cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
            timeRange.startTime = dateFormat.format(cal.time)
            timeRange.startTimestamp = cal.timeInMillis
            timeRange.startYear = cal.get(Calendar.YEAR)
            timeRange.startMonth = cal.get(Calendar.MONTH)
            cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY)
            timeRange.endTime = dateFormat.format(cal.time)
            timeRange.endTimestamp = cal.timeInMillis
            timeRange.endYear = cal.get(Calendar.YEAR)
            timeRange.endMonth = cal.get(Calendar.MONTH)
            timeRange.week = week
            return timeRange
        }
    }
}

data class TimeRange(
    var startTime: String? = null,

    var startTimestamp: Long? = null,

    var startYear: Int? = null,

    var startMonth: Int? = null,

    var endTime: String? = null,

    var endTimestamp: Long? = null,

    var endYear: Int? = null,

    var endMonth: Int? = null,
    var month : Int? = null,

    /** 如果周围最小周期，这里代表年度第几周 */
    var week: Int? = null
)