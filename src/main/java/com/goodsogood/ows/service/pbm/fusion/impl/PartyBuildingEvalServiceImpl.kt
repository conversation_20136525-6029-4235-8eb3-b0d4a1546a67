package com.goodsogood.ows.service.pbm.fusion.impl

import cn.hutool.core.collection.CollUtil
import cn.hutool.core.util.ObjectUtil
import cn.hutool.core.util.StrUtil
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.PbmWorkDataConfig
import com.goodsogood.ows.mapper.sas.PbmTargetMapper
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData
import com.goodsogood.ows.model.mongodb.fusion.PartyBuildTarget
import com.goodsogood.ows.service.pbm.CalculatingTimeUtils
import com.goodsogood.ows.service.pbm.fusion.FusionResultUtils
import com.goodsogood.ows.service.pbm.fusion.IFusionDataService
import org.springframework.stereotype.Service

/**
 * 党建考核情况
 */
@Service("PARY_BULID_EVAL")
class PartyBuildingEvalServiceImpl(
    val pbmWorkDataConfig: PbmWorkDataConfig,
    var pbmTargetMapper: PbmTargetMapper,
    val myMongoTemplate: MyMongoTemplate
) : IFusionDataService {
    /**
     * 收集党业融合基础数据
     * @param unitId    单位ID
     * @param unitName  单位名称
     * @param itemId    指标ID
     * @param year      年份
     * @param month     月份
     * @param regionId  区县ID
     * <AUTHOR>
     * @return 注意返回的所有值将直接展示到页面，所以部分数据需要翻译过后保存。例如：奖励名称、奖励级别等
     * 【注需要将入参回填到返回参数里】
     */
    override fun collect(
        unitId: Long,
        unitName: String,
        itemId: Long,
        year: Int,
        month: Int,
        regionId: Long
    ): MutableList<out FusionBaseData> {
        val updateTime = System.currentTimeMillis()
        //查询党建考核积分
        val result = pbmTargetMapper.getPartyBuildingEvalScore( year, unitId )
        result.forEach {
            it.itemId = itemId
            it.unitId = unitId
            it.unitName = unitName
            it.year = year
            it.month = month
            it.updateTime = updateTime
        }
        return result
    }

    /**
     * 计算党业融合指标得分
     * @param itemList  单位指标列表 包含单位ID、单位名称、指标ID、年份、月份
     * @param year      年份 - 避免从itemList获取
     * @param month      月份 - 避免从itemList获取
     * @param regionId   区县ID
     * @param itemEntity 指标对象
     * <AUTHOR>
     * @return 返回实体类中
     * itemScore - 为该指标赋分（可能为平均值、考核得分、综合等，可为空）
     * score - 指标得分（根据规则计算得分，用于计算融合度，不能为空）
     * rank - 根据情况，需要需要排名，请填入排名
     */
    override fun calculate(
        itemList: MutableList<FusionItemData>,
        year: Int,
        month: Int,
        regionId: Long,
        itemEntity: PbmFusionItemEntity
    ): MutableList<FusionItemData> {
        val timeRange = CalculatingTimeUtils.getMeetingTimeRange(year = year, month = month, cycle = itemEntity.cycle!!)
        val collectData = this.getCollectData(
            clazz = PartyBuildTarget::class.java,
            mongoTemplate = myMongoTemplate,
            unitIds = itemList.map { unitData -> unitData.unitId!! },
            itemId = itemEntity.fusionItemId!!,
            year = timeRange.startYear!!,
            month = null,
            startMonth = timeRange.startMonth,
            endMonth = timeRange.endMonth,
            pbmWorkDataConfig = pbmWorkDataConfig
        )
        //赋值
        itemList.forEach { item ->
            if(CollUtil.isNotEmpty(collectData)) {
                val find = collectData.find { item.unitId?.equals(it.unitId)!! }
                if (null != find) {
                    item.itemScore = find.itemScore
                } else {
                    item.itemScore = null
                }
            }else{
                item.itemScore = null
            }
        }
        itemList.forEach {
            // 计算排名和指标得分
            FusionResultUtils.rank(itemList, itemEntity.baseScore, pbmWorkDataConfig)
            if(ObjectUtil.isEmpty(it.itemScore)){
                it.result ="-";
            }else {
                // 结果
                it.result = "考核结果：${it.itemScore}\n排名：${it.rank}"
            }
        }
        return itemList
    }

}