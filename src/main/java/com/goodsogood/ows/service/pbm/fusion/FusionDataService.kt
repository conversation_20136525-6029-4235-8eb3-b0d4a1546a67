package com.goodsogood.ows.service.pbm.fusion

import com.goodsogood.ows.common.Constants
import com.goodsogood.ows.config.SimpleApplicationConfigHelper
import com.goodsogood.ows.configuration.ExcludeOrgConfig
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.OrgTypeConfig
import com.goodsogood.ows.configuration.PbmWorkDataConfig
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.mapper.sas.FusionItemMapper
import com.goodsogood.ows.mapper.user.OrganizationMapper
import com.goodsogood.ows.mapper.user.UserOrgAndCorpMapper
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity
import com.goodsogood.ows.model.db.sas.PbmFusionItemVO
import com.goodsogood.ows.model.db.user.OrganizationEntity
import com.goodsogood.ows.model.mongodb.fusion.FusionData
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData
import com.goodsogood.ows.model.vo.fusion.FusionIndexVO
import com.goodsogood.ows.model.vo.fusion.FusionMapDetail
import com.goodsogood.ows.service.pbm.FusionFactory
import com.goodsogood.ows.service.user.OrgService
import com.goodsogood.ows.utils.ArithUtils
import com.goodsogood.ows.utils.DateUtils
import org.apache.commons.lang3.time.StopWatch
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cache.annotation.Cacheable
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.stereotype.Service
import tk.mybatis.mapper.entity.Example
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.*
import kotlin.comparisons.*

/**
 * 党业融合服务类
 * <AUTHOR>
 * @createTime 2023年03月13日 21:06:00
 */
@Service
class FusionDataService @Autowired constructor(
    private val orgTypeConfig: OrgTypeConfig,
    private val fusionFactory: FusionFactory,
    private val mongoTemplate: MyMongoTemplate,
    private val fusionItemMapper: FusionItemMapper,
    private val excludeOrgConfig: ExcludeOrgConfig,
    private val pbmWorkDataConfig: PbmWorkDataConfig,
    private val organizationMapper: OrganizationMapper,
    val userOrgAndCorpMapper: UserOrgAndCorpMapper,
    val orgService: OrgService,
    val simpleApplicationConfigHelper: SimpleApplicationConfigHelper
) {

    private val log: Logger = LoggerFactory.getLogger(this::class.java)

    /**
     * 收集基础数据
     * @param unitId
     * @param itemId
     * @param year
     * @param month
     * @param regionId
     */
    fun collect(unitId: Long? = null, itemId: Long? = null, year: Int, month: Int? = null, regionId: Long) {
        // 查询党业融合指标
        val example = Example(PbmFusionItemEntity::class.java)
        val baseCriteria = example.createCriteria().andIsNotNull("strategyName")
        if (itemId != null) {
            baseCriteria.andEqualTo("fusionItemId", itemId)
        }
        val fusionItemList = this.fusionItemMapper.selectByExample(example)
        // 查询单位列表
        val units = organizationMapper.findUnits(regionId, unitId, excludeOrgConfig.orgIds)
        // 更新时间
        val timestamp = System.currentTimeMillis() / 1000
        // 月份列表
        val monthList = if (month == null) {
            val months = listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12)
            if (year == LocalDate.now().year) {
                var mo = LocalDate.now().month.value
                if (mo > 1) {
                    mo -= 1
                }
                months.subList(0, mo)
            } else {
                months
            }
        } else {
            listOf(month)
        }
        val stopWatch = StopWatch()
        stopWatch.start()
        log.debug("收集基础数据开始 -> {}", stopWatch)
        fusionItemList.forEach { item ->
            log.debug("收集[{}]指标基础数据开始 -> {}", item.name, stopWatch)
            // 根据方法获取service
            val fusionService =
                fusionFactory.getFusionService(item.strategyName) ?: throw ApiException("未查询到服务方法")
            for (unit in units) {
                try {
                    monthList.forEach { m ->
                        val criteria = Criteria.where("unitId").`is`(unit.unitId)
                            .and("itemId").`is`(item.fusionItemId)
                            .and("year").`is`(year)
                            .and("month").`is`(m)
                        val query = Query(criteria)
                        mongoTemplate.remove(query, pbmWorkDataConfig.fusionMongo)
                        val baseDataList =
                            fusionService.collect(
                                unit.unitId,
                                unit.unitName,
                                item.fusionItemId!!,
                                year,
                                m,
                                regionId
                            )
                        if (baseDataList.isNotEmpty()) {
                            baseDataList.forEach {
                                it.updateTime = timestamp
                                mongoTemplate.insert(it, pbmWorkDataConfig.fusionMongo)
                            }
                        }
                    }
                } catch (e: Exception) {
                    log.error("【收集党业融合指标报错】-> [${item.name}]指标报错服务[${item.strategyName}]", e)
                    continue
                }
            }
            log.debug("收集[{}]指标基础数据结束 -> {}", item.name, stopWatch)
        }
        log.debug("收集基础数据结束 -> {}", stopWatch)
        stopWatch.stop()
    }

    /**
     * 计算党业融合指标
     * @param unitId
     * @param itemId
     * @param year
     * @param month
     * @param regionId
     */
    fun calculate(unitId: Long? = null, itemId: Long? = null, year: Int, month: Int? = null, regionId: Long) {
        // 查询党业融合指标
        val example = Example(PbmFusionItemEntity::class.java)
        val baseCriteria = example.createCriteria().andIsNotNull("strategyName")
        if (itemId != null) {
            baseCriteria.andEqualTo("fusionItemId", itemId)
        }
        val fusionItemList = this.fusionItemMapper.selectByExample(example)
        // 查询单位列表
        val units = organizationMapper.findUnits(regionId, unitId, excludeOrgConfig.orgIds)
        // 更新时间
        val timestamp = System.currentTimeMillis() / 1000
        // 月份列表
        val monthList = if (month == null) {
            val months = listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12)
            if (year == LocalDate.now().year) {
                var mo = LocalDate.now().month.value
                if (mo > 1) {
                    mo -= 1
                }
                months.subList(0, mo)
            } else {
                months
            }
        } else {
            listOf(month)
        }
        for (item in fusionItemList) {
            try {
                monthList.forEach { m ->
                    // 根据方法获取service
                    val fusionService =
                        fusionFactory.getFusionService(item.strategyName) ?: throw ApiException("未查询到服务方法")
                    val itemList = units.map { unit ->
                        FusionItemData(
                            unitId = unit.unitId,
                            unitName = unit.unitName,
                            year = year,
                            month = m,
                            itemId = item.fusionItemId,
                            updateTime = timestamp
                        )
                    }.toMutableList()
                    val fusionUnitData = fusionService.calculate(itemList, year, m, regionId, item)
                    log.debug("[{}]指标在计算后结果：{}", itemId, fusionUnitData)
                    if (fusionUnitData.isNotEmpty()) {
                        fusionUnitData.forEach {
                            insertOrUpdateBase(it)
                        }
                    }
                }
            } catch (e: Exception) {
                log.error("【计算党业融合指标报错】-> [${item.name}]指标报错服务[${item.strategyName}]", e)
                continue
            }
        }
    }

    private fun insertOrUpdateBase(data: FusionItemData) {
        val criteria = Criteria.where("unitId").`is`(data.unitId)
            .and("itemId").`is`(data.itemId)
            .and("year").`is`(data.year)
            .and("month").`is`(data.month)
        val query = Query(criteria)
        if (mongoTemplate.count(query, FusionItemData::class.java) > 0) {
            mongoTemplate.remove(query, FusionItemData::class.java)
        }
        mongoTemplate.insert(data)

    }

    /**
     * 计算党业融合度
     * @param itemId
     * @param year
     * @param month      入库的数据就是当月的数据
     * @param regionId
     */
    fun summary(itemId: Long? = null, year: Int, month: Int, regionId: Long) {
        // 查询党业融合指标
        val example = Example(PbmFusionItemEntity::class.java)
        if (itemId != null) {
            example.createCriteria().andEqualTo("fusionItemId", itemId)
        }
        val fusionItemList = this.fusionItemMapper.selectByExample(example)
        // 计算指标基础知之和
        val baseTotal = fusionItemList.map { BigDecimal(it.baseScore) }.reduce { acc, value -> acc + value }
        val zero = BigDecimal(0.0)
        val hundred = BigDecimal(100)
        // 查询单位列表
        val units = organizationMapper.findUnits(regionId, null, excludeOrgConfig.orgIds)
        // 查询上个月
        val fusionList = if (month - 1 == 0) {
            this.findFusionData(listOf(year - 1), month = 12)
        } else {
            this.findFusionData(listOf(year), month = month - 1)
        }
        val lastMap = fusionList.groupBy { it.unitId }.mapValues { (_, v) -> v[0] }
        // 因为逻辑问题，导致指标计算表中的月份比当前月份多一个月，需要处理
        var m = month
        var y = year
        if (m >= 12) {
            m = 1
            y += 1
        } else {
            m += 1
        }
        // 查询党业融合计算指标
        val dataList = mutableListOf<FusionData>()
        units.forEach { unit ->
            val fusionData = findFusionItemData(unit.unitId, y, m)
            val unitScoreTotal = fusionData.map { BigDecimal(it.score) }.reduce { acc, value -> acc + value }
                .setScale(2, RoundingMode.HALF_UP)
            val fusionValue = if (baseTotal == zero) {
                0.0
            } else {
                unitScoreTotal.divide(baseTotal, 4, RoundingMode.HALF_UP).multiply(hundred)
                    .setScale(2, RoundingMode.HALF_UP).toDouble()
            }
            log.debug(
                "unitId: {}, year: {}, month: {}, unitScoreTotal: {}, baseTotal: {}, fusion: {}",
                unit.unitId,
                year,
                month,
                unitScoreTotal,
                baseTotal,
                fusionValue
            )
            val data = FusionData(
                unitId = unit.unitId,
                unitName = unit.unitName,
                year = year,
                month = month,
                fusion = fusionValue,
                updateTime = System.currentTimeMillis() / 1000
            )
            dataList.add(data)
        }
        // 排序
        jumpRankingSort(dataList)
        dataList.forEach { fusionData ->
            lastMap[fusionData.unitId]?.rank?.let {
                fusionData.change = it - fusionData.rank
            }
            this.insertOrUpdateFusion(fusionData)
        }
    }

    private fun insertOrUpdateFusion(data: FusionData) {
        val criteria = Criteria.where("unitId").`is`(data.unitId)
            .and("year").`is`(data.year)
            .and("month").`is`(data.month)
        val query = Query(criteria)
        if (mongoTemplate.count(query, FusionData::class.java) > 0) {
            mongoTemplate.remove(query, FusionData::class.java)
        }
        mongoTemplate.insert(data)
    }

    /**
     * 获取融合度
     * @param unitId
     */
    fun findFusion(unitId: Long? = null, yearValue: Int? = null, monthValue: Int? = null): FusionIndexVO {
        val fusionVO = FusionIndexVO()
        // 获取当前时间
        var year = yearValue
        var month = monthValue
        if (year == null || month == null) {
            val calendar = Calendar.getInstance()
            year = calendar[Calendar.YEAR]
            month = calendar[Calendar.MONTH]
        }
        // 融合度数据
        val dataList = findFusionData(years = mutableListOf(year, year - 1), unitId = unitId)
        if (dataList.isEmpty()) {
            return fusionVO
        }
        val fusionData = dataList.sortedWith(compareBy<FusionData>({ it.year }, { it.month }).reversed())
        if (fusionData.isNotEmpty()) {
            fusionVO.unitId = fusionData[0].unitId
            fusionVO.unitName = fusionData[0].unitName
            fusionVO.fusionValue = fusionData[0].fusion
            fusionVO.rank = fusionData[0].rank
            if (fusionData.size > 1) {
                fusionVO.change = fusionData[0].change
            }
            fusionVO.line = fusionData.groupBy { it.year }.mapValues { o ->
                // 根据月份排序
                o.value.sortedBy { it.month }
                    // 转换为String
                    .map { it.fusion.toString() }
                    // 转为可拓展List
                    .toMutableList()
                    // apply
                    .apply {
                        // 应用
                        repeat(12 - o.value.size) { add("-") }
                    }
            }
        }

        val monthData = findFusionData(years = mutableListOf(year), month = month)
        if (monthData.isEmpty()) {
            return fusionVO
        }
        val fusionList = monthData.map { it.fusion }
        if (fusionList.isNotEmpty()) {
            fusionVO.avg = ArithUtils.round(fusionList.average(), 2)
            fusionVO.max = ArithUtils.round(fusionList.max(), 2)
            fusionVO.min = ArithUtils.round(fusionList.min(), 2)
        }
        // 更新时间
        val maxUpdateTime = dataList.mapNotNull { it.updateTime }.maxBy { it }
        fusionVO.updateTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
            .format(maxUpdateTime.times(1000))
        return fusionVO
    }

    fun getFusionMapDetail(
        yearValue: Int? = null,
        monthValue: Int? = null,
        regionId: Long
    ): List<FusionMapDetail> {
        // 获取当前时间
        var year = yearValue
        var month = monthValue
        if (year == null || month == null) {
            val calendar = Calendar.getInstance()
            year = calendar[Calendar.YEAR]
            month = calendar[Calendar.MONTH]
        }
        val fusionData = this.findFusionData(years = listOf(year), month = month)
        val resultList = mutableListOf<FusionMapDetail>()
        if (fusionData.isEmpty()) {
            return resultList
        }
        val idList = fusionData.map { it.unitId }
        val example = Example(OrganizationEntity::class.java)
        example.createCriteria()
            .andIn("organizationId", idList)
        val groupMap =
            this.organizationMapper.selectByExample(example).groupBy { it.organizationId }.mapValues { (_, v) -> v[0] }
        val topOrg = this.organizationMapper.findTopOrgId(orgTypeConfig.branchChild, excludeOrgConfig.orgIds)
        val map = topOrg.groupBy { it.unitId }.mapValues { (_, v) -> v[0] }
        fusionData.forEach {
            val detail = FusionMapDetail()
            detail.unitId = it.unitId
            detail.unitName = it.unitName
            detail.fusion = it.fusion
            detail.rank = it.rank
            detail.change = it.change
            if (map.containsKey(it.unitId)) {
                val org = map[it.unitId]
                if (org != null) {
                    detail.adcode = org.adcode
                    detail.unitShortName = groupMap[it.unitId]?.shortName
                    detail.longitude = org.longitude
                    detail.latitude = org.latitude
                    detail.branch = org.branch
                    detail.member = org.member
                }
            }
            resultList.add(detail)
        }
        return resultList.sortedByDescending { it.fusion }
    }

    /**
     * 查询计算好的指标
     * @param unitId
     * @param year
     * @param month
     */
    private fun findFusionItemData(unitId: Long, year: Int, month: Int): MutableList<FusionItemData> {
        val criteria = Criteria.where("unitId").`is`(unitId)
            .and("year").`is`(year)
            .and("month").`is`(month)
        val query = Query(criteria)
        return this.mongoTemplate.find(query, FusionItemData::class.java)
    }

    /**
     * 查询计算融合度
     * @param unitId
     * @param years
     */
    private fun findFusionData(years: List<Int>, unitId: Long? = null, month: Int? = null): List<FusionData> {
        val criteria = Criteria.where("year").`in`(years)
        if (unitId != null) {
            criteria.and("unitId").`is`(unitId)
        }
        if (month != null) {
            criteria.and("month").`is`(month)
        }
        val query = Query(criteria)
        return this.mongoTemplate.find(query, FusionData::class.java).sortedByDescending { it.month }
    }

    /**
     * 收集基础数据-全量，包含开始月份和结束月份
     * @param startMonth yyyy-MM
     * @param endMonth yyyy-MM
     */
    fun collectAll(unitId: Long?, itemId: Long?, startMonth: String, endMonth: String, regionId: Long) {
        val monthList = DateUtils.getBetweenMonthsList(startMonth, endMonth, 0)
        for (month in monthList) {
            val format: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
            val date = LocalDate.parse("$month-01", format).plusMonths(1)
            collect(unitId, itemId, date.year, date.monthValue, regionId)
            log.debug("党业融合基础数据:${month}已完成")
        }
    }

    /**
     * 计算数据-全量 ，包含开始月份和结束月份
     *  @param startMonth yyyy-MM
     *  @param endMonth yyyy-MM
     */
    fun calculateAll(unitId: Long? = null, itemId: Long? = null, startMonth: String, endMonth: String, regionId: Long) {
        val monthList = DateUtils.getBetweenMonthsList(startMonth, endMonth, 0)
        for (month in monthList) {
            val format: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
            val date = LocalDate.parse(month + "-01", format)
            collect(unitId, itemId, date.year, date.monthValue, regionId)
            log.debug("党业融合数据计算:${month}已完成")
        }
    }

    fun getUserCorpInfo(userId: Long, header: HeaderHelper.SysHeader): List<String> {
        val corpData = this.simpleApplicationConfigHelper.getCorpByRegionId(header.regionId)
        val userOrgInfoList = this.userOrgAndCorpMapper.selectByUserAndOrg(
            userId,
            header.regionId
        )
        val resultList = mutableListOf<String>()
        userOrgInfoList.forEach {
            if (it.orgId > 2) {
                val orgList = mutableListOf<OrganizationEntity>()
                this.getParentOrg(it.orgId, corpData.orgId, orgList)
                val reversed = orgList.reversed()
                val list = reversed.map { it.name }.toList()
                resultList.add(list.joinToString("-"))
            }
        }
        return resultList
    }


    fun getParentOrg(orgId: Long?, topOrgId: Long = 1, orgList: MutableList<OrganizationEntity>) {
        val entity = this.orgService.getById(orgId)
        orgList.add(entity)
        if (entity.parentId!! > topOrgId) {
            this.getParentOrg(entity.parentId, topOrgId, orgList)
        }
    }

    fun jumpRankingSort(datas: List<FusionData>) {
        // 将融合度按从高到低排序
        val sortedScores = datas.sortedByDescending { it.fusion }

        var rank = 1 // 初始化排名为第1名
        var prevScore = sortedScores[0].fusion // 记录前一个分数

        for (i in sortedScores.indices) {
            val data = sortedScores[i]

            if (i != 0) {
                if (data.fusion < prevScore) {
                    // 如果当前分数低于前一个分数，排名跳到下下一名
                    rank = i + 1
                }
            }
            data.rank = rank
            prevScore = data.fusion
        }
    }

    fun item(): List<PbmFusionItemVO> {
        val fusionItemList = this.fusionItemMapper.selectAll()
        return fusionItemList.map {
            PbmFusionItemVO(
                fusionItemId = it.fusionItemId,
                category = when (it.category) {
                    1 -> "目标融合"
                    2 -> "组织融合"
                    3 -> "工作融合"
                    4 -> "数据融合"
                    else -> ""
                },
                name = it.name,
                description = it.description,
                baseScore = it.baseScore,
                cycle = when (it.cycle) {
                    1 -> "周"
                    2 -> "月度"
                    3 -> "季度"
                    4 -> "累计"
                    5 -> "半年"
                    else -> ""
                },
                rule = it.rule,
                remark = it.remark
            )
        }
    }
}