package com.goodsogood.ows.service.pbm

import com.goodsogood.ows.common.Constants
import com.goodsogood.ows.common.UserSeqEnum
import com.goodsogood.ows.component.Init
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.model.mongodb.pbm.*
import com.goodsogood.ows.model.mongodb.user.User
import com.goodsogood.ows.model.vo.experience.OrgUserReportVO
import com.goodsogood.ows.model.vo.user.PbmUserInfo
import com.goodsogood.ows.service.tbcFusion.TbcScoreService
import com.goodsogood.ows.service.user.OrgService
import com.goodsogood.ows.service.user.UserMongoService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service
import java.math.RoundingMode
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.util.*
import kotlin.math.hypot
import kotlin.math.ln

/**
 *
 * <AUTHOR>
 * @createTime 2022年06月26日 10:50:00
 */
@Service
class PbmWorkKitService(
    val tbcScoreService: TbcScoreService,
    val pbmBufferTriggerService: PbmBufferTriggerService,
    val userMongoService: UserMongoService,
    val orgService: OrgService,
    val redisTemplate: StringRedisTemplate,
    val mongoTemplate: MyMongoTemplate,
) {

    companion object {
        const val REDIS_KEY_WORK_KIT_ORG_USER = "REDIS_KEY_WORK_KIT_ORG_USER"
        const val REDIS_KEY_WORK_KIT_ORG = "REDIS_KEY_WORK_KIT_ORG"
        const val REDIS_KEY_WORK_KIT_USER = "REDIS_KEY_WORK_KIT_USER"
    }

    private val log: Logger = LoggerFactory.getLogger(PbmWorkKitService::class.java)

    /**
     * @title 计算用户工作数据      免责声明: 由于项目赶工，代码难以理解。非本人技术问题。
     * <AUTHOR>
     * @param regionId      区域ID
     * @param unitId        单位ID
     * @param year          时间  yyyy
     * @param month         月份  MM
     * @param isCalculateUser  是否计算人员
     * @param type          类型  0 -> 全体，1 -> 卷烟营销，2 -> 烟叶生成，3 -> 专卖管理，4 -> 综合管理
     * @updateTime 2022/6/22 20:53
     * @return
     * @throws
     */
    fun calculatePbmKit(
        regionId: Long,
        unitId: Long? = null,
        year: Int,
        month: Int,
        isCalculateUser: Boolean = true,
        type: Int = 0
    ) {
        // 计算时间
        val timeRanges = CalculatingTimeUtils.calculatingTime(year = year, month = month, cycle = 2)
        // 时间格式
        val dateFormat = SimpleDateFormat("yyyy-MM")
        val dateDayFormat = SimpleDateFormat("yyyy-MM-dd")
        val calendar = Calendar.getInstance()
        calendar[Calendar.YEAR] = year
        calendar[Calendar.MONTH] = month - 1
        val dateStr = dateFormat.format(calendar.time)
        // 当前结束时间
        calendar.add(Calendar.MONTH, 1)
        calendar[Calendar.DAY_OF_MONTH] = 0
        val endYearTime = dateDayFormat.format(calendar.time)
        // 年份开始时间
        calendar[Calendar.MONTH] = Calendar.JANUARY
        calendar[Calendar.DAY_OF_MONTH] = 1
        val startYearTime = dateDayFormat.format(calendar.time)
        timeRanges.forEach { time ->
            // 查询单位
            val units = this.userMongoService.getUnitList(regionId = regionId, unitId)
            val unitIds = units.map { it.unitId }
            // 查询单月单位党建、业务积分
            val unitPartyAndBusiness =
                this.tbcScoreService.findUnitPartyAndBusiness(regionId, unitIds, time.startTime, time.endTime)
            //log.debug("单月人员$unitIds 党建、业务积分 -> $unitPartyAndBusiness")
            // 查询累计单位党建、业务积分
            val unitPartyAndBusinessTotal =
                this.tbcScoreService.findUnitPartyAndBusiness(regionId, unitIds, startYearTime, endYearTime)
            //log.debug("累计人员$unitIds 党建、业务积分 -> $unitPartyAndBusinessTotal")
            // 单位党建积分对数集合(求平均值、最高值、计算排名)
            val partyScoreLogList = mutableListOf<Double>()
            // 单位业务积分对数集合(求平均值、最高值、计算排名)
            val businessScoreLogList = mutableListOf<Double>()
            // 单位拟合度集合(求平均值、最高值、计算排名)
            val kitScoreList = mutableListOf<Double>()
            // 全体党员党建积分集合
            val userPartyScoreList = mutableListOf<Long>()
            // 全体党员业务积分集合
            val userBusinessScoreList = mutableListOf<Long>()
            // 全体党员拟合度集合
            val userKitScoreList = mutableListOf<Double>()
            // 全体党员的业务积分总和
            var allBeBusinessSum = 0L
            // 全体非党员的业务积分总和
            var allNonBusinessSum = 0L
            // 全体党员的拟合度总和
            var allBeKitSum = 0.00
            // 党员党建积分最大值
            var maxPartyScore = 0L
            // 党员党建积分最小值
            var minPartyScore = 0L
            // 党员业务积分最大值
            var maxBusinessScore = 0L
            // 党员业务积分最小值
            var minBusinessScore = 0L
            // 党员人数
            var allBeNum = 0
            // 非党员人数
            var allNonNum = 0
            // 业务线拟合度排名集合
            val mutableMap = initSeqRankMap()
            // 清库
            redisTemplate.delete(REDIS_KEY_WORK_KIT_ORG_USER)
            redisTemplate.delete(REDIS_KEY_WORK_KIT_ORG)
            redisTemplate.delete(REDIS_KEY_WORK_KIT_USER)
            units.forEach { unit ->
                // 单位实体类
                val unitKitInfo = PbmUnitKitInfo(
                    regionId = regionId,
                    date = dateStr,
                    unitId = unit.unitId,
                    unitName = unit.unitName
                )
                // 获取单位党建、业务积分
                val unitScoreVo = unitPartyAndBusiness[unit.unitId]
                val unitScoreTotalVo = unitPartyAndBusinessTotal[unit.unitId]
                if (unitScoreVo != null && unitScoreTotalVo != null) {
                    unitKitInfo.partyBuild = unitScoreVo.partyScore
                    unitKitInfo.partyBuildTotal = unitScoreTotalVo.partyScore
                    unitKitInfo.partyBuildTotalLog = dealNumbers(mathLog(value = unitScoreTotalVo.partyScore))
                    unitKitInfo.business = unitScoreVo.businessScore
                    unitKitInfo.businessTotal = unitScoreTotalVo.businessScore
                    unitKitInfo.businessTotalLog = dealNumbers(mathLog(value = unitScoreTotalVo.businessScore))
                    unitKitInfo.goodnessOfKit =
                        dealNumbers(
                            getHypotenuseFromLength(
                                unitKitInfo.businessTotalLog,
                                unitKitInfo.partyBuildTotalLog
                            )
                        )
                    partyScoreLogList.add(unitKitInfo.partyBuildTotalLog)
                    businessScoreLogList.add(unitKitInfo.businessTotalLog)
                    //log.debug("unit: ${unit.unitId} 拟合度为${unitKitInfo.goodnessOfKit}")
                    kitScoreList.add(unitKitInfo.goodnessOfKit)
                    //log.debug("拟合度集合现有${kitScoreList}")
                }
                unitKitInfo.createTime = LocalDateTime.now()
                // 存库
                redisTemplate.opsForHash<String, String>().put(
                    REDIS_KEY_WORK_KIT_ORG,
                    unit.unitId.toString(),
                    Init.objectMapper.writeValueAsString(unitKitInfo)
                )
                // 计算人员
                if (isCalculateUser) {
                    // 组织人员数据
                    val orgUserKitInfo = PbmOrgUserKitInfo(
                        unitId = unit.unitId, unitName = unit.unitName, regionId = regionId,
                        date = dateStr, type = type
                    )
                    // 本单位党员的业务积分总和
                    var beBusinessSum = 0L
                    // 本单位党员的党建积分总和
                    var bePartySum = 0L
                    // 本单位非党员的业务积分总和
                    var nonBusinessSum = 0L
                    // 本单位党员的拟合度总和
                    var beKitSum = 0.00
                    // 本单位党员人数
                    var beNum = 0
                    // 本单位非党员人数
                    var nonNum = 0
                    // 最高业务积分集合
                    val maxUserBusinessList = mutableListOf<PbmUserKitInfo>()
                    // 最高党建积分集合
                    val maxUserPartyList = mutableListOf<PbmUserKitInfo>()
                    // 本单位拟合度排序集合
                    val selfRankList = mutableListOf<RankEntity>()
                    // 查询人员
                    val userList =
                        userMongoService.getUserList(
                            regionId = regionId,
                            unitId = unit.unitId,
                            sequence = type,
                            isParty = 2
                        )
                    val userIds = userList.map { it.userId }
                    // 查询人员的党建积分、业务积分
                    val partyAndBusiness =
                        tbcScoreService.findUserPartyAndBusiness(regionId, userIds, time.startTime, time.endTime)
                    //log.debug("单月人员$unitIds 党建、业务积分 -> $partyAndBusiness")
                    val partyAndBusinessTotal =
                        tbcScoreService.findUserPartyAndBusiness(regionId, userIds, startYearTime, endYearTime)
                    //log.debug("累计人员$unitIds 党建、业务积分 -> $partyAndBusinessTotal")
                    userList.forEach { user ->
                        // 基本信息封装
                        val kitInfo = getPbmUserKitInfo(user, regionId, dateStr, unit)
                        // 获取党建、业务积分
                        val scoreVo = partyAndBusiness[user.userId]
                        val scoreTotalVo = partyAndBusinessTotal[user.userId]
                        if (scoreVo != null && scoreTotalVo != null) {
                            val partyScore = scoreVo.partyScore
                            val businessScore = scoreVo.businessScore
                            kitInfo.business = businessScore
                            kitInfo.businessTotal = scoreTotalVo.businessScore
                            kitInfo.businessTotalLog = dealNumbers(mathLog(value = scoreTotalVo.businessScore))
                            kitInfo.partyBuild = partyScore
                            kitInfo.partyBuildTotal = scoreTotalVo.partyScore
                            kitInfo.partyBuildTotalLog = dealNumbers(mathLog(value = scoreTotalVo.partyScore))
                            kitInfo.goodnessOfKit =
                                dealNumbers(
                                    getHypotenuseFromLength(
                                        kitInfo.businessTotalLog,
                                        kitInfo.partyBuildTotalLog
                                    )
                                )
                            if (mutableListOf(1, 5, 17, 18).contains(user.politicalType)) {
                                // 党员
                                beBusinessSum += businessScore
                                bePartySum += partyScore
                                allBeBusinessSum += businessScore
                                beKitSum += kitInfo.goodnessOfKit
                                allBeKitSum += kitInfo.goodnessOfKit
                                // 党员党建积分最大值
                                if (partyScore > maxPartyScore) {
                                    maxPartyScore = partyScore
                                }
                                // 党员党建积分最小值
                                if (minPartyScore == 0L || partyScore < minPartyScore) {
                                    minPartyScore = partyScore
                                }
                                // 党员业务积分最大值
                                if (businessScore > maxBusinessScore) {
                                    maxBusinessScore = businessScore
                                }
                                // 党员业务积分最小值
                                if (minBusinessScore == 0L || businessScore < minBusinessScore) {
                                    minBusinessScore = businessScore
                                }
                                // 全体党员党建积分集合
                                userPartyScoreList.add(partyScore)
                                // 全体党员业务积分集合
                                userBusinessScoreList.add(businessScore)
                                // 全体党员拟合度积分集合
                                userKitScoreList.add(kitInfo.goodnessOfKit)
                                // 本单位拟合度排序集合
                                val rankEntity = RankEntity(id = kitInfo.userId, score = kitInfo.goodnessOfKit)
                                selfRankList.add(rankEntity)
                                mutableMap[kitInfo.sequence]?.add(rankEntity)
                                // 党员数量
                                beNum += 1
                                allBeNum += 1
                                // 最高党建积分
                                if (maxUserPartyList.isEmpty()) {
                                    maxUserPartyList.add(kitInfo)
                                } else {
                                    if (partyScore > maxUserPartyList[0].partyBuild) {
                                        maxUserPartyList.clear()
                                        maxUserPartyList.add(kitInfo)
                                    } else if (partyScore == maxUserPartyList[0].partyBuild) {
                                        maxUserPartyList.add(kitInfo)
                                    }
                                }
                            } else {
                                // 非党员
                                nonBusinessSum += businessScore
                                allNonBusinessSum += businessScore
                                // 非党员数量
                                nonNum += 1
                                allNonNum += 1
                            }
                            // 最高业务积分
                            if (maxUserBusinessList.isEmpty()) {
                                maxUserBusinessList.add(kitInfo)
                            } else {
                                if (businessScore > maxUserBusinessList[0].business) {
                                    maxUserBusinessList.clear()
                                    maxUserBusinessList.add(kitInfo)
                                } else if (businessScore == maxUserBusinessList[0].business) {
                                    maxUserBusinessList.add(kitInfo)
                                }
                            }
                        }
                        if (type == 0) {
                            // 人员先存放到redis
                            redisTemplate.opsForHash<String, String>().put(
                                REDIS_KEY_WORK_KIT_USER,
                                kitInfo.userId.toString(),
                                Init.objectMapper.writeValueAsString(kitInfo)
                            )
                        }
                    }
                    // 计算本单位拟合度排名
                    if (type == 0) {
                        val rankScore = rankScore(selfRankList)
                        userList.forEach { user ->
                            // 从redis里获取单位单位Kit信息
                            val userStr = redisTemplate.opsForHash<String, String>()
                                .get(REDIS_KEY_WORK_KIT_USER, user.userId.toString())
                            val kitInfo = Init.objectMapper.readValue(userStr, PbmUserKitInfo::class.java)
                            kitInfo.selfRank = PbmKitChild(k = rankScore[user.userId]?.toDouble())
                            // 人员先存放到redis
                            redisTemplate.opsForHash<String, String>().put(
                                REDIS_KEY_WORK_KIT_USER,
                                kitInfo.userId.toString(),
                                Init.objectMapper.writeValueAsString(kitInfo)
                            )
                        }
                    }
                    // 本单位当前业务线的业务积分平均值
                    log.debug("[kitData]$unit 单位当前业务线党员的业务积分平均值$beBusinessSum 党员数量$beNum")
                    log.debug("[kitData]$unit 单位当前业务线非党员的业务积分平均值$nonBusinessSum 非党员数量$nonNum")
                    orgUserKitInfo.selfBusinessAvg = ScoreInfo(
                        be = dealNumbers(beBusinessSum.div(beNum.toDouble())),
                        non = dealNumbers(nonBusinessSum.div(nonNum.toDouble())),
                        beNum = beNum,
                        nonNum = nonNum
                    )
                    // 本单位当前业务线党员的党建积分平均值
                    log.debug("[kitData]$unit 单位当前业务线党员的党建积分平均值$bePartySum 党员数量$beNum")
                    orgUserKitInfo.selfPartyAvg = ScoreInfo(
                        be = dealNumbers(bePartySum.div(beNum.toDouble())),
                        beNum = beNum
                    )
                    // 本单位当前业务线党员的拟合度平均值
                    orgUserKitInfo.selfKitAvg = ScoreInfo(
                        be = dealNumbers(beKitSum.div(beNum)),
                        beNum = beNum
                    )
                    orgUserKitInfo.maxUserKitInfo = maxUserBusinessList
                    orgUserKitInfo.maxUserPartyInfo = maxUserPartyList
                    orgUserKitInfo.createTime = LocalDateTime.now()
                    // 暂存redis
                    redisTemplate.opsForHash<String, String>().put(
                        REDIS_KEY_WORK_KIT_ORG_USER,
                        unit.unitId.toString(),
                        Init.objectMapper.writeValueAsString(orgUserKitInfo)
                    )
                }
            }
            val resultMap = mutableMapOf<Int, MutableMap<Long?, Int>>()
            // 业务线拟合度排行
            mutableMap.entries.forEach {
                resultMap[it.key] = rankScore(it.value)
            }
            redisTemplate.opsForHash<String, String>().keys(REDIS_KEY_WORK_KIT_USER).forEach {
                // 从redis里获取单位单位Kit信息
                val userStr = redisTemplate.opsForHash<String, String>()
                    .get(REDIS_KEY_WORK_KIT_USER, it)
                val kitInfo = Init.objectMapper.readValue(userStr, PbmUserKitInfo::class.java)
                kitInfo.sequenceRank = PbmKitChild(k = resultMap[kitInfo.sequence]?.get(kitInfo.userId)?.toDouble())
                // 新增人员数据到Mongo
                kitInfo.createTime = LocalDateTime.now()
                pbmBufferTriggerService.addBuffer(kitInfo)
            }
            // 业务线党员拟合度平均值
            log.debug("业务线党员拟合度平均值 -> 拟合度总和: $allBeKitSum, 总党员数: $allBeNum")
            val allBeKitAvg = ScoreInfo(be = dealNumbers(allBeKitSum.div(allBeNum)), beNum = allBeNum)
            // 党建、业务、拟合度进行降序排序
            partyScoreLogList.sortDescending()
            log.debug("党建积分集合: $partyScoreLogList")
            businessScoreLogList.sortDescending()
            kitScoreList.sortDescending()
            //log.debug("拟合度集合2: $kitScoreList")
            // 组织平均值
            log.debug("单位党建业务积分对数总和: ${partyScoreLogList.sum()}")
            log.debug("单位业务业务积分对数总和: ${businessScoreLogList.sum()}")
            log.debug("单位拟合度总和: ${kitScoreList.sum()}")
            val avg = PbmKitChild(
                p = dealNumbers(partyScoreLogList.sum().div(units.size.toDouble())),
                b = dealNumbers(businessScoreLogList.sum().div(units.size.toDouble())),
                k = dealNumbers(kitScoreList.sum().div(units.size.toDouble()))
            )
            // 组织的最高值
            val high = PbmKitChild(
                p = partyScoreLogList[0],
                b = businessScoreLogList[0],
                k = kitScoreList[0]
            )
            val kitInfo = PbmKitChild()
            if (isCalculateUser) {
                // 当前业务线用户党建积分中位数
                if (userPartyScoreList.isNotEmpty()) {
                    val userPartyScoreList2 = userPartyScoreList.filter { it != 0L }.toMutableList()
                    if (userPartyScoreList.size != userPartyScoreList2.size) {
                        userPartyScoreList2.add(0)
                    }
                    userPartyScoreList2.sortDescending()
                    log.debug("当前业务线党建积分集合: $userPartyScoreList2")
                    kitInfo.p = if (userPartyScoreList2.size == 1) {
                        userPartyScoreList2[0].toDouble()
                    } else if (userPartyScoreList2.size % 2 == 1) {
                        dealNumbers(userPartyScoreList2[(userPartyScoreList2.size - 1).div(2)].toDouble())
                    } else {
                        dealNumbers(
                            (userPartyScoreList2[(userPartyScoreList2.size.div(2)) - 1] + userPartyScoreList2[userPartyScoreList2.size.div(
                                2
                            )]).toDouble().div(2)
                        )
                    }
                }
                // 当前业务线用户业务积分中位数
                if (userBusinessScoreList.isNotEmpty()) {
                    val userBusinessScoreList2 = userBusinessScoreList.filter { it != 0L }.toMutableList()
                    if (userBusinessScoreList.size != userBusinessScoreList2.size) {
                        userBusinessScoreList2.add(0)
                    }
                    userBusinessScoreList2.sortDescending()
                    log.debug("当前业务线业务积分集合: $userBusinessScoreList2")
                    kitInfo.b = if (userBusinessScoreList2.size == 1) {
                        userBusinessScoreList2[0].toDouble()
                    } else if (userBusinessScoreList2.size % 2 == 1) {
                        dealNumbers(userBusinessScoreList2[(userBusinessScoreList2.size - 1) / 2].toDouble())
                    } else {
                        dealNumbers((userBusinessScoreList2[(userBusinessScoreList2.size / 2) - 1] + userBusinessScoreList2[userBusinessScoreList2.size / 2]).toDouble() / 2)
                    }
                }
                // 当前业务线用户拟合度中位数
                if (userKitScoreList.isNotEmpty()) {
                    val userKitScoreList2 = userKitScoreList.filter { it != 0.0 }.toMutableList()
                    if (userKitScoreList.size != userKitScoreList2.size) {
                        userKitScoreList2.add(0.0)
                    }
                    userKitScoreList2.sortDescending()
                    //log.debug("当前业务线拟合度集合: $userKitScoreList")
                    kitInfo.k = if (userKitScoreList2.size == 1) {
                        userKitScoreList2[0]
                    } else if (userKitScoreList2.size % 2 == 1) {
                        dealNumbers(userKitScoreList2[(userKitScoreList2.size - 1) / 2])
                    } else {
                        dealNumbers((userKitScoreList2[userKitScoreList2.size / 2 - 1] + userKitScoreList2[userKitScoreList2.size / 2]) / 2)
                    }
                }
            }

            units.forEach { unit ->
                // 从redis里获取单位单位Kit信息
                val infoStr = redisTemplate.opsForHash<String, String>()
                    .get(REDIS_KEY_WORK_KIT_ORG, unit.unitId.toString())
                val unitKitInfo = Init.objectMapper.readValue(infoStr, PbmUnitKitInfo::class.java)
                if (unitKitInfo != null) {
                    // 计算组织的排名
                    unitKitInfo.rank = PbmKitChild(
                        p = (partyScoreLogList.indexOfFirst { it == unitKitInfo.partyBuildTotalLog } + 1).toDouble(),
                        b = (businessScoreLogList.indexOfFirst { it == unitKitInfo.businessTotalLog } + 1).toDouble(),
                        k = (kitScoreList.indexOfFirst { it == unitKitInfo.goodnessOfKit } + 1).toDouble(),
                    )
                    // 计算组织的平均值
                    unitKitInfo.avg = avg
                    //计算组织的最高值
                    unitKitInfo.high = high
                    unitKitInfo.updateTime = LocalDateTime.now()
                    if (type == 0) {
                        pbmBufferTriggerService.addBuffer(unitKitInfo)
                    }
                    // 从redis里获取单位用户信息
                    val unitStr = redisTemplate.opsForHash<String, String>()
                        .get(REDIS_KEY_WORK_KIT_ORG_USER, unit.unitId.toString())
                    val orgUserKitInfo = Init.objectMapper.readValue(unitStr, PbmOrgUserKitInfo::class.java)
                    if (orgUserKitInfo != null) {
                        // 全体党员业务积分平均数
                        orgUserKitInfo.allBusinessAvg = ScoreInfo(
                            be = dealNumbers(allBeBusinessSum.div(allBeNum.toDouble())),
                            non = dealNumbers(allNonBusinessSum.div(allNonNum.toDouble())),
                            beNum = allBeNum,
                            nonNum = allNonNum
                        )
                        // 全体党员业务积分极值
                        orgUserKitInfo.businessExtreme = Extremum(max = maxBusinessScore, min = minBusinessScore)
                        // 全体党员党建积分极值
                        orgUserKitInfo.partyBuildExtreme = Extremum(max = maxPartyScore, min = minPartyScore)
                        // 中位数
                        orgUserKitInfo.allPartyMedian = kitInfo
                        // 业务线党员拟合度平均值
                        orgUserKitInfo.allKitAvg = allBeKitAvg
                        orgUserKitInfo.updateTime = LocalDateTime.now()
                        pbmBufferTriggerService.addBuffer(orgUserKitInfo)
                    }
                }
            }
            // 清库
            redisTemplate.delete(REDIS_KEY_WORK_KIT_ORG_USER)
            redisTemplate.delete(REDIS_KEY_WORK_KIT_ORG)
            redisTemplate.delete(REDIS_KEY_WORK_KIT_USER)
        }
    }

    /**
     * @title 处理基本信息
     * <AUTHOR>
     * @param user      userMongo
     * @param dateStr   时间字符串
     * @updateTime 2022/6/26 17:23
     * @return
     * @throws
     */
    private fun getPbmUserKitInfo(
        user: PbmUserInfo,
        regionId: Long,
        dateStr: String,
        unit: OrgUserReportVO
    ): PbmUserKitInfo {
        val kitInfo = PbmUserKitInfo(
            userId = user.userId,
            userName = user.name,
            phone = user.phoneSecret,
            regionId = regionId,
            politicalType = user.politicalType,
            sequence = user.sequence,
            date = dateStr,
            title = user.title
        )
        // 所属组织
        if (user.isEmployee == 1) {
            kitInfo.orgId = user.orgId
            kitInfo.orgName = user.orgName
            // 所属单位
            val userUnit = userMongoService.getUnitIdByOrgId(user.orgId)
            if (userUnit != null) {
                kitInfo.unitId = userUnit.organizationId
                kitInfo.unitName = userUnit.name
            }
        } else {
            kitInfo.unitId = unit.unitId
            kitInfo.unitName = unit.unitName
        }
        return kitInfo
    }

    /**
     * @title 获取组织工作详情
     * <AUTHOR>
     */
    fun geUnitKitInfo(unitId: Long, date: String): PbmUnitKitInfo? {
        val criteria = Criteria.where("unitId").`is`(unitId)
            .and("date").`is`(date)
        val query = Query(criteria)
        return mongoTemplate.findOne(query, PbmUnitKitInfo::class.java)
    }

    /**
     * @title 获取组织工作详情
     * <AUTHOR>
     */
    fun geOrgUserKitInfo(unitId: Long, date: String): PbmOrgUserKitInfo? {
        val criteria = Criteria.where("unitId").`is`(unitId)
            .and("date").`is`(date)
        val query = Query(criteria)
        return mongoTemplate.findOne(query, PbmOrgUserKitInfo::class.java)
    }

    /**
     * @title 求对数
     * <AUTHOR>
     * @param   value   求对数的值
     * @param   base    对数的基数
     * @updateTime 2022/6/26 17:23
     * @return
     * @throws
     */
    fun mathLog(value: Long, base: Long = 100): Double {
        return if (value <= 0L) {
            0.0
        } else {
            ln(value.toDouble()) / ln(base.toDouble())
        }
    }

    /**
     * @title  直角三角获取斜边
     * <AUTHOR>
     * @param a 直角边长
     * @param b 直角边长
     * @return 斜边边长
     */
    fun getHypotenuseFromLength(a: Double?, b: Double?): Double {
        return if (a != null && b != null) {
            hypot(a, b)
        } else {
            0.0
        }
    }

    /**
     * 保留小数点后一位
     */
    fun dealNumbers(data: Double?): Double {
        val d = if (data == null || data.isNaN()) {
            0.0
        } else {
            data
        }
        val df = DecimalFormat("#.#")
        df.roundingMode = RoundingMode.HALF_UP
        return df.format(d).toDouble()
    }

    /**
     * 初始化Map
     */
    fun initSeqRankMap(): MutableMap<Int, MutableList<RankEntity>> {
        val map = mutableMapOf<Int, MutableList<RankEntity>>()
        map[UserSeqEnum.UserSeqEnum_1.seqNumber] = mutableListOf()
        map[UserSeqEnum.UserSeqEnum_2.seqNumber] = mutableListOf()
        map[UserSeqEnum.UserSeqEnum_3.seqNumber] = mutableListOf()
        map[UserSeqEnum.UserSeqEnum_4.seqNumber] = mutableListOf()
        return map
    }

    /**
     * 排序
     * @return Map<Id, 排名次数>
     */
    fun rankScore(rankList: MutableList<RankEntity>): MutableMap<Long?, Int> {
        val mapOf = mutableMapOf<Long?, Int>()
        val entryList = rankList.groupBy { it.score }.entries
            .sortedWith { o1, o2 -> -(o1.key!!.compareTo(o2.key!!)) }
        var index = 1
        entryList.forEach { entry ->
            entry.value.forEach { e ->
                mapOf[e.id] = index
            }
            index += entry.value.size
        }
        return mapOf
    }
}