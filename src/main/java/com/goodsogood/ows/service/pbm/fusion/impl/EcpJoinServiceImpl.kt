package com.goodsogood.ows.service.pbm.fusion.impl

import cn.hutool.core.util.NumberUtil
import com.goodsogood.ows.config.SimpleApplicationConfigHelper
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.PbmWorkDataConfig
import com.goodsogood.ows.mapper.ecp.EcpTaskMapper
import com.goodsogood.ows.mapper.user.OrganizationMapper
import com.goodsogood.ows.mapper.user.UserMapper
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity
import com.goodsogood.ows.model.mongodb.fusion.EcpJoinDetail
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData
import com.goodsogood.ows.service.pbm.CalculatingTimeUtils
import com.goodsogood.ows.service.pbm.fusion.IFusionDataService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.util.*

/**
 * 云区参与情况
 *
 * <AUTHOR>
 * @Date 2023-03-14 10:06:56
 * @Description EcpJoinServiceImpl
 *
 */
@Service("ECP_JOIN")
class EcpJoinServiceImpl(
    val ecpTaskMapper: EcpTaskMapper,
    val myMongoTemplate: MyMongoTemplate,
    val userMapper: UserMapper,
    val organizationMapper: OrganizationMapper,
    val pbmWorkDataConfig: PbmWorkDataConfig,
    val simpleApplicationConfigHelper: SimpleApplicationConfigHelper
) : IFusionDataService {

    val log: Logger = LoggerFactory.getLogger(EcpJoinServiceImpl::class.java)

    @Value("\${user-option.virtualOrg}")
    private val virtualOrg: String? = null

    override fun collect(
        unitId: Long,
        unitName: String,
        itemId: Long,
        year: Int,
        month: Int,
        regionId: Long
    ): MutableList<out FusionBaseData> {
        val updateTime = System.currentTimeMillis()
        var result = mutableListOf<EcpJoinDetail>()
        // yyyy-MM
        val monthStr = if (month >= 10) month else "0".plus(month)
        val yearMonth = year.toString().plus("-").plus(monthStr)
        // 需要排除的支部ID
        val excludeOrgs = this.virtualOrg?.split(",")?.map { it.toLong() }
        val queryList = mutableListOf<Long>()
        // 根据单位ID查询单位党员
        val unitUserList = this.getUserListByUnitId(unitId, regionId, excludeOrgs!!, organizationMapper, userMapper)
        val userList = unitUserList.map { it.userId }
        queryList.addAll(userList)
        // 根据单位查询党支部ID
        val includeOrgList = this.getOrgListByUnitId(unitId, regionId, excludeOrgs, organizationMapper)
        if (includeOrgList.isNotEmpty()) {
            // 根据党支部ID查询非党员
            val normalUserList = this.userMapper.staNoPartyMemberTotal(includeOrgList, excludeOrgs)
            queryList.addAll(normalUserList)
            if (queryList.isNotEmpty()) {
                // 查询所有用户参与次数
                result = ecpTaskMapper.findDoneTotal(yearMonth = yearMonth, userList = queryList)
            }
        }
        result.forEach {
            it.itemId = itemId
            it.unitId = unitId
            it.unitName = unitName
            it.year = year
            it.month = month
            it.updateTime = updateTime
        }
        return result
    }

    override fun calculate(
        itemList: MutableList<FusionItemData>,
        year: Int,
        month: Int,
        regionId: Long,
        itemEntity: PbmFusionItemEntity
    ): MutableList<FusionItemData> {
        val timeRange =
            CalculatingTimeUtils.getMeetingTimeRange(year = year, month = month, cycle = itemEntity.cycle!!)
        val ecpJoinDetail = this.getCollectData(
            clazz = EcpJoinDetail::class.java,
            mongoTemplate = myMongoTemplate,
            unitIds = itemList.map { unitData -> unitData.unitId!! },
            itemEntity.fusionItemId!!,
            year = timeRange.startYear!!,
            startMonth = timeRange.startMonth,
            endMonth = timeRange.endMonth,
            pbmWorkDataConfig = pbmWorkDataConfig
        )
        // 需要排除的支部ID
        val excludeOrgs = this.virtualOrg?.split(",")?.map { it.toLong() }
        itemList.forEach { item ->
            // 查询党员
            val partyUserList = this.getUserListByUnitId(
                unitId = item.unitId!!,
                regionId = regionId,
                excludeOrg = excludeOrgs!!,
                organizationMapper = organizationMapper,
                userMapper = userMapper
            )
            // 根据单位查询党支部ID
            val includeOrgList = this.getOrgListByUnitId(item.unitId!!, regionId, excludeOrgs, organizationMapper)
            // 根据党支部ID查询非党员
            val normalUserList = this.userMapper.staNoPartyMemberTotal(includeOrgList, excludeOrgs)
            val partyUserIdList = partyUserList.map { it.userId }
            // 总参与次数
            val total: Int
            // 党员参与次数
            var partyTotal = 0
            // 非党员参与次数
            var normalTotal = 0
            val ecpJoinList = ecpJoinDetail.groupBy { it.unitId }
            ecpJoinList[item.unitId]?.forEach { detail ->
                if (partyUserIdList.contains(detail.userId)) {
                    partyTotal += detail.joinNum
                }
                if (normalUserList.contains(detail.userId)) {
                    normalTotal += detail.joinNum
                }
            }
            total = partyTotal + normalTotal
            when {
                total > 0 -> {
                    // 计算分值
                    val baseScore =
                        NumberUtil.round(NumberUtil.mul(NumberUtil.div(partyTotal, total), itemEntity.baseScore), 2)
                    item.score = baseScore.toDouble()
                }
            }
            // 结果
            item.result = String.format("总参与：%d人次\n党员参与：%d人次", total, partyTotal)

        }
        return itemList
    }
}