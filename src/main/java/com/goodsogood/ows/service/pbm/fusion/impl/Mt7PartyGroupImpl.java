package com.goodsogood.ows.service.pbm.fusion.impl;

import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.PbmWorkDataConfig;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.mapper.user.OrgLeaderMapper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.mapper.user.PartyGroupMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData;
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData;
import com.goodsogood.ows.model.mongodb.fusion.LeaderDutyDetail;
import com.goodsogood.ows.model.mongodb.fusion.MeetingDetail;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.service.pbm.CalculatingTimeUtils;
import com.goodsogood.ows.service.pbm.TimeRange;
import com.goodsogood.ows.service.pbm.fusion.IFusionDataService;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 党组责任落实 半年  4分 各单位党组会次数半年1次以上，达标则得分，否则不得分
 */
@Service("PARTYGROUP")
@Log4j2
public class Mt7PartyGroupImpl  implements IFusionDataService {
    private final OrganizationMapper organizationMapper;
    private final MeetingMapper meetingMapper;
    private final PartyGroupMapper partyGroupMapper;
    private final OrgLeaderMapper orgLeaderMapper;
    private final PbmWorkDataConfig pbmWorkDataConfig;
    private final MyMongoTemplate myMongoTemplate;
    @Value("${user-option.virtualOrg}")
    private String excludeOrg;

    @Autowired
    public Mt7PartyGroupImpl(OrganizationMapper organizationMapper, MeetingMapper meetingMapper, PartyGroupMapper partyGroupMapper, OrgLeaderMapper orgLeaderMapper, PbmWorkDataConfig pbmWorkDataConfig, MyMongoTemplate myMongoTemplate) {
        this.organizationMapper = organizationMapper;
        this.meetingMapper = meetingMapper;
        this.partyGroupMapper = partyGroupMapper;
        this.orgLeaderMapper = orgLeaderMapper;
        this.pbmWorkDataConfig = pbmWorkDataConfig;
        this.myMongoTemplate = myMongoTemplate;
    }


    @NotNull
    @Override
    public List<? extends FusionBaseData> collect(long unitId, @NotNull String unitName, long itemId, int year, int month, long regionId) {
        String status = pbmWorkDataConfig.getMeetingMap().getStatus();
        List<? extends FusionBaseData> list = new ArrayList<>();
        String monthStr = month>9? ""+month: "0"+month;
        String dateMonth = year+"-"+monthStr;
        //查询单位下的组织id
        List<Long> excludeOrgs = Arrays.asList(excludeOrg.split(",")).stream().map(i->Long.valueOf(i)).collect(Collectors.toList());
        List<Long> orgIds = organizationMapper.findAllOrgByUnit(unitId, excludeOrgs);
        if(CollectionUtils.isEmpty(orgIds)){
            return Collections.emptyList();
        }
        List<MeetingDetail> partyGroup = meetingMapper.partyGroup(pbmWorkDataConfig.getMeetingMap().getTypes(itemId),status,dateMonth,orgIds);
        partyGroup.parallelStream().forEach(i->{
            setUnitInfo(unitId, unitName, itemId, year, month, i);
        });
        return partyGroup;
    }


    private static void setUnitInfo(long unitId, String unitName, long itemId, int year, int month, MeetingDetail i) {
        i.setUnitId(unitId);
        i.setUnitName(unitName);
        i.setYear(year);
        i.setMonth(month);
        i.setItemId(itemId);
    }


    @NotNull
    @Override
    public List<FusionItemData> calculate(@NotNull List<FusionItemData> itemList, int year, int month, long regionId, @NotNull PbmFusionItemEntity itemEntity) {
        List<Long> unitIds = itemList.stream().map(FusionItemData::getUnitId).collect(Collectors.toList());
        TimeRange timeRange = CalculatingTimeUtils.Companion.getMeetingTimeRange(year, month, itemEntity.getCycle());
        List<MeetingDetail> list = this.getCollectData(MeetingDetail.class,myMongoTemplate,unitIds,itemEntity.getFusionItemId(),timeRange.getStartYear(),null,timeRange.getStartMonth(), timeRange.getEndMonth(), pbmWorkDataConfig);
        Map<Long,List<MeetingDetail>> map = list.stream().collect(Collectors.groupingBy(MeetingDetail::getUnitId));
        itemList.forEach(i->{
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(map.get(i.getUnitId())) && map.get(i.getUnitId()).size()>=1){
              i.setResult("完成");
              i.setScore(itemEntity.getBaseScore());
            }else{
                i.setResult("未完成");
                i.setScore(0.00);
            }
        });
        return itemList;
    }




    @NotNull
    @Override
    public List<UserInfoBase> getUserListByUnitId(long unitId, long regionId, @NotNull List<Long> excludeOrg, @NotNull OrganizationMapper organizationMapper, @NotNull UserMapper userMapper) {
        return IFusionDataService.DefaultImpls.getUserListByUnitId(this, unitId, regionId, excludeOrg, organizationMapper, userMapper);
    }

    @NotNull
    @Override
    public OrganizationEntity getOrgById(long orgId, @NotNull OrganizationMapper organizationMapper) {
        return IFusionDataService.DefaultImpls.getOrgById(this, orgId, organizationMapper);
    }

    @Nullable
    @Override
    public List<Long> getOrgListByUnitId(long unitId, long regionId, @NotNull List<Long> excludeOrg, @NotNull OrganizationMapper organizationMapper) {
        return IFusionDataService.DefaultImpls.getOrgListByUnitId(this, unitId, regionId, excludeOrg, organizationMapper);
    }

    @NotNull
    @Override
    public <T extends FusionBaseData> List<T> getCollectData(@NotNull Class<T> clazz, @NotNull MyMongoTemplate mongoTemplate, @NotNull List<Long> unitIds, long itemId, int year, @Nullable Integer month, @Nullable Integer startMonth, @Nullable Integer endMonth, @NotNull PbmWorkDataConfig pbmWorkDataConfig) {
        return IFusionDataService.DefaultImpls.getCollectData(this, clazz, mongoTemplate, unitIds, itemId, year, month, startMonth, endMonth, pbmWorkDataConfig);
    }
}
