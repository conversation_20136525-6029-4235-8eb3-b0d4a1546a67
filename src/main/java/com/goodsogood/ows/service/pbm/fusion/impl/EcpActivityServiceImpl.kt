package com.goodsogood.ows.service.pbm.fusion.impl

import cn.hutool.core.util.NumberUtil
import com.goodsogood.ows.configuration.EcpTagConfig
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.PbmWorkDataConfig
import com.goodsogood.ows.mapper.ecp.EcpOrgMapper
import com.goodsogood.ows.mapper.user.OrganizationMapper
import com.goodsogood.ows.mapper.user.UserMapper
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity
import com.goodsogood.ows.model.mongodb.fusion.EcpActivityDetail
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData
import com.goodsogood.ows.service.pbm.CalculatingTimeUtils
import com.goodsogood.ows.service.pbm.fusion.FusionResultUtils
import com.goodsogood.ows.service.pbm.fusion.IFusionDataService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

/**
 * 工作融合-云区活动情况
 *
 * <AUTHOR>
 * @Date 2023-03-13 20:36:01
 * @Description EcpActivityServiceImpl
 *
 */
@Service("ECP_ACTIVITY")
class EcpActivityServiceImpl(
    val organizationMapper: OrganizationMapper,
    val ecpOrgMapper: EcpOrgMapper,
    val ecpTagConfig: EcpTagConfig,
    val myMongoTemplate: MyMongoTemplate,
    val pbmWorkDataConfig: PbmWorkDataConfig,
    val userMapper: UserMapper
) : IFusionDataService {

    val log: Logger = LoggerFactory.getLogger(EcpActivityServiceImpl::class.java)

    @Value("\${user-option.virtualOrg}")
    private val virtualOrg: String? = null

    /**
     * 生成基础数据
     */
    override fun collect(
        unitId: Long,
        unitName: String,
        itemId: Long,
        year: Int,
        month: Int,
        regionId: Long
    ): MutableList<out FusionBaseData> {
        val updateTime = System.currentTimeMillis()
        var result = mutableListOf<EcpActivityDetail>()
        // 需要排除的支部ID
        val excludeOrgs = this.virtualOrg?.split(",")?.map { it.toLong() }
        // 根据支部ID查询单位党员
        val unitUserList = this.getUserListByUnitId(unitId, regionId, excludeOrgs!!, organizationMapper, userMapper)
        // 获取所有用户ID列表
        val userList = unitUserList.map { it.userId }
        // 查询组织 处理日期格式yyyy-MM
        val monthStr = if (month >= 10) month else "0".plus(month)
        val yearMonth = year.toString().plus("-").plus(monthStr)
        val ecpTagList = mutableListOf<Long>()
        ecpTagConfig.monopolyId?.let(ecpTagList::add)
        ecpTagConfig.marketingId?.let(ecpTagList::add)
        ecpTagConfig.tobaccoId?.let(ecpTagList::add)
        ecpTagConfig.synthesisId?.let(ecpTagList::add)
        if (userList.isNotEmpty()) {
            result = this.ecpOrgMapper.ecpActivityDetail(
                yearMonth = yearMonth,
                userList = userList,
                ecpTagList = ecpTagList
            )
        }
        result.forEach {
            it.itemId = itemId
            it.unitId = unitId
            it.unitName = unitName
            it.year = year
            it.month = month
            it.updateTime = updateTime
        }
        log.debug("云区活动情况：[${result}]")
        return result
    }

    /**
     * 计算
     */
    override fun calculate(
        itemList: MutableList<FusionItemData>,
        year: Int,
        month: Int,
        regionId: Long,
        itemEntity: PbmFusionItemEntity
    ): MutableList<FusionItemData> {

        val timeRange =
            CalculatingTimeUtils.getMeetingTimeRange(year = year, month = month, cycle = itemEntity.cycle!!)
        val ecpActivityDetail = this.getCollectData(
            clazz = EcpActivityDetail::class.java,
            mongoTemplate = myMongoTemplate,
            unitIds = itemList.map { unitData -> unitData.unitId!! },
            itemId = itemEntity.fusionItemId!!,
            year = timeRange.startYear!!,
            month = null,
            startMonth = timeRange.startMonth,
            endMonth = timeRange.endMonth,
            pbmWorkDataConfig = pbmWorkDataConfig
        )
        // 计算任务平均数，根据平均数计算排名
        // 分母
        val ecpUnitList = ecpActivityDetail.groupBy { it.unitId }
        itemList.forEach {
            // 分子
            var ecpOrgTotal = 0
            ecpUnitList[it.unitId]?.forEach { detail -> ecpOrgTotal += detail.taskNum }
            if (ecpOrgTotal > 0) {
                val averTotal = NumberUtil.round(NumberUtil.div(ecpOrgTotal, ecpUnitList[it.unitId]?.size), 2)
                it.itemScore = averTotal.toDouble()
            }
        }
        // 计算排名和指标得分
        FusionResultUtils.rank(itemList, itemEntity.baseScore, pbmWorkDataConfig)
        itemList.forEach {
            // 设置结果
            if (it.rank != null && it.rank != 0) {
                it.result = "排名：${it.rank}"
            } else {
                it.result = "排名：--"
            }
        }
        return itemList
    }
}