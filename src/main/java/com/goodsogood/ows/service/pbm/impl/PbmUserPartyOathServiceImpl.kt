package com.goodsogood.ows.service.pbm.impl

import com.goodsogood.ows.mapper.activity.RevisitMapper
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import com.goodsogood.ows.service.pbm.PbmUserWorkDataService
import org.springframework.stereotype.Service

/**
 * 用户入党誓词
 */
@Service("USER_PARTY_OATH")
class PbmUserPartyOathServiceImpl (val revisitMapper:  RevisitMapper) : PbmUserWorkDataService {

    override fun getUserWorkData(userId: Long, startTime: String?, endTime: String?, week: Int?,regionId: Long, workItem: WorkItemEntity): Any? {
        return revisitMapper.staRevisitDays(1, userId, startTime, endTime, regionId)
    }

}