package com.goodsogood.ows.service.pbm.fusion;


import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.mapper.sas.PbmTargetMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.sas.PbmTargetEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.pbm.PbmImportResultVO;
import com.goodsogood.ows.model.vo.pbm.PbmTargetExportVO;
import com.goodsogood.ows.model.vo.pbm.PbmTargetVO;
import com.goodsogood.ows.service.ExcelServices;
import com.goodsogood.ows.service.ThirdService;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 指标管理
 */
@Service
@Log4j2
public class PbmTargetService {

    private final StringRedisTemplate stringRedisTemplate;
    private final com.goodsogood.ows.mapper.sas.PbmTargetMapper pbmTargetMapper;

    private final ThirdService thirdService;

    private final Errors errors;

    private final ExcelServices excelServices;

    private final UserMapper userMapper;

    private final SimpleApplicationConfigHelper simpleApplicationConfigHelper;

    private final OrgService orgService;

    private final FusionDataService fusionDataService;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public  PbmTargetService(StringRedisTemplate stringRedisTemplate, PbmTargetMapper pbmTargetMapper, ThirdService thirdService, Errors errors, ExcelServices excelServices, UserMapper userMapper, SimpleApplicationConfigHelper simpleApplicationConfigHelper, OrgService orgService, FusionDataService fusionDataService) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.pbmTargetMapper = pbmTargetMapper;
        this.thirdService = thirdService;
        this.errors = errors;
        this.excelServices = excelServices;
        this.userMapper = userMapper;
        this.simpleApplicationConfigHelper = simpleApplicationConfigHelper;
        this.orgService = orgService;
        this.fusionDataService = fusionDataService;
    }


    public Page<PbmTargetEntity> pbmTargetList(Integer pageNo, Integer pageSize,
                                               Long orgId, String time,String name,Integer isPartyMember){
        return PageHelper.startPage(pageNo, pageSize)
                .doSelectPage(() -> pbmTargetMapper.pbmTargetList(orgId, time, name,isPartyMember));
    }

    public List<PbmTargetExportVO> getList(Long orgId, String time,String name,Integer isPartyMember){
        return pbmTargetMapper.getList(orgId, time, name,isPartyMember);
    }

    @Async("excelImportExecutor")
    public void pbmTargetImport(Long regionId, String token,
                                @RequestHeader HttpHeaders headers, Long orgId,
                                MultipartFile upFile, HeaderHelper.SysHeader header) throws Exception {
        PbmImportResultVO<PbmTargetVO> pbmImportResultVO = new PbmImportResultVO<>();
        pbmImportResultVO.setTips(-1);
        setImportResultByToken(token, pbmImportResultVO);
        InputStream inputStream = upFile.getInputStream();
        //导入设置格式
        ImportParams importParams = new ImportParams();
        List<PbmTargetVO> list;
        try {
            list = ExcelImportUtil.importExcel(inputStream, PbmTargetVO.class, importParams);
            //easypoi 当只有一条记录的时候它解析过后还是有两记录
            List<PbmTargetVO> filterEasyPoiList = list.stream().
                    filter(it -> StringUtils.isNotEmpty(it.getUserName())).collect(Collectors.toList());
            //插入列表
            List<PbmTargetEntity> insertList = new ArrayList<>();
            //更新列表
            List<PbmTargetEntity> updateList = new ArrayList<>();
            // 错误列表
            List<PbmTargetVO> failList = new ArrayList<>();
            //过滤后机同的数据
            List<PbmTargetVO> filterAfterList = new ArrayList<>();
            //过滤重复的数据
            Map<String, List<PbmTargetVO>> collect = filterEasyPoiList.stream().collect(
                    Collectors.groupingBy(item -> item.getTime() + "-" + item.getUserName() + "-" + item.getPhone()));
            collect.forEach((key,val)->{
                if(val.size()>1){
                    val.forEach(item->{
                        item.setTips("用户信息填写重复");
                    });
                    failList.addAll(val);
                }else {
                    filterAfterList.addAll(val);
                }
            });
            List<UserInfoBase> userPlaintext = thirdService.findUserPlaintext();
            if (CollectionUtil.isEmpty(userPlaintext)) {
                throw new ApiException("根据组织id无法查询用户明文信息", new Result<>(errors, 1744, HttpStatus.OK.value(),
                        "根据组织id无法查询用户明文信息"));
            }
            AtomicReference<Boolean> isJumpForEach = new AtomicReference<>(false);
            filterAfterList.forEach(item -> {
                if (StrUtil.isEmpty(item.getUserName()) || StrUtil.isEmpty(item.getPhone())
                        || StrUtil.isEmpty(item.getTime()) || item.getScore() == null) {
                    item.setTips("用户信息填写出错");
                    failList.add(item);
                    return;
                }
                if (StringUtils.isBlank(item.getUserName())){
                    isJumpForEach.set(true);
                }
                if (StringUtils.isBlank(item.getPhone())){
                    isJumpForEach.set(true);
                }
                if (StringUtils.isBlank(item.getTime())) {
                    isJumpForEach.set(true);
                }
                if (item.getTime() != null){
                    if (isValidDate(item.getTime())){
                        isJumpForEach.set(false);
                    }else {
                        isJumpForEach.set(true);
                    }
                }
                if (isJumpForEach.get()) {
                    item.setTips("数据格式不规范");
                    failList.add(item);
                    isJumpForEach.set(false);
                    return;
                }
                boolean isExistUser = userPlaintext.stream().anyMatch(it ->
                        item.getUserName().equals(it.getName()) && item.getPhone().equals(it.getPhone()));
                if (!isExistUser) {
                    item.setTips("用户信息不存在");
                    failList.add(item);
                    isJumpForEach.set(false);
                    return;
                }
                Long userId = userMapper.getUserIdByPhone(item.getUserName(), NumEncryptUtils.encrypt(item.getPhone(),2));
                List<Long> branchIdList = userMapper.getBranchIdList(userId);
                if (branchIdList.size() > 1) {
                    item.setTips("用户有多个支部");
                    failList.add(item);
                    isJumpForEach.set(false);
                    return;
                }
                //开始验证这个用户
                Example example = new Example(PbmTargetEntity.class);
                List<Long> unitIdList = userMapper.getUnitId(userId);
                String unitId = "";
                String unit = "";
                if (unitIdList != null && unitIdList.size() > 0) {
                    List<String> unitString = unitIdList.stream().map(x -> x + "").collect(Collectors.toList());
                    unitId = String.join(",", unitString);
                }
                List<String> unitList = fusionDataService.getUserCorpInfo(userId,header);
                if (unitList != null && unitList.size() > 0) {
                    unit = String.join(",", unitList);
                }
                List<Long> departmentIdList = userMapper.getDepartmentId(userId);
                List<String> departmentIdString = departmentIdList.stream().map(x -> x + "").collect(Collectors.toList());
                String departmentId = String.join(",", departmentIdString);
                List<String> departmentList = userMapper.getDepartment(userId);
                String department = String.join(",", departmentList);
                Integer isPartyMember = userMapper.getIsPartyMember(userId);
                Long branchId = userMapper.getBranchId(userId);
                String branch = userMapper.getBranch(userId);
                Long userOrgId = userMapper.getOrgId(userId);
                example.createCriteria().andEqualTo("userId", userId)
                        .andEqualTo("time", item.getTime().replace("-",""))
                        .andEqualTo("orgId",orgId);
                PbmTargetEntity pbmTargetEntities = pbmTargetMapper.selectOneByExample(example);
                if (null != pbmTargetEntities) {
                    BeanUtils.copyProperties(item, pbmTargetEntities, "pbmTargetId");
                    pbmTargetEntities.setOrgId(orgId);
                    pbmTargetEntities.setTime(item.getTime().replace("-",""));
                    pbmTargetEntities.setUserId(userId);
                    if(!"null".equals(unit)){
                        pbmTargetEntities.setUnit(unit);
                    }if(!"null".equals(unitId)){
                        pbmTargetEntities.setUnitId(unitId);
                    }
                    pbmTargetEntities.setDepartmentId(departmentId);
                    pbmTargetEntities.setBranchId(branchId);
                    pbmTargetEntities.setDepartment(department);
                    pbmTargetEntities.setIsPartyMember(isPartyMember);
                    pbmTargetEntities.setBranch(branch);
                    pbmTargetEntities.setUpdateTime(new Date());
                    pbmTargetEntities.setLastChangeUser(header.getUserId());
                    updateList.add(pbmTargetEntities);
                } else {
                    PbmTargetEntity pbmTargetEntitiy = new PbmTargetEntity();
                    BeanUtils.copyProperties(item, pbmTargetEntitiy);
                    pbmTargetEntitiy.setTime(item.getTime().replace("-",""));
                    pbmTargetEntitiy.setUserId(userId);
                    if(!"null".equals(unit)){
                        pbmTargetEntitiy.setUnit(unit);
                    }
                    pbmTargetEntitiy.setUnit(unit);
                    pbmTargetEntitiy.setOrgId(orgId);
                    if(!"null".equals(unitId)){
                        pbmTargetEntitiy.setUnitId(unitId);
                    }
                    pbmTargetEntitiy.setUnitId(unitId);
                    pbmTargetEntitiy.setDepartmentId(departmentId);
                    pbmTargetEntitiy.setBranchId(branchId);
                    pbmTargetEntitiy.setDepartment(department);
                    pbmTargetEntitiy.setIsPartyMember(isPartyMember);
                    pbmTargetEntitiy.setBranch(branch);
                    pbmTargetEntitiy.setCreateTime(new Date());
                    pbmTargetEntitiy.setLastChangeUser(header.getUserId());
                    insertList.add(pbmTargetEntitiy);
                }
            });
            //更新数据库数据
            if (CollectionUtil.isNotEmpty(updateList)) {
                updateList.forEach(pbmTargetMapper::updateByPrimaryKey);
                //studyScoreMapper.updateList(updateList);
            }
            if (CollectionUtil.isNotEmpty(insertList)) {
                pbmTargetMapper.insertList(insertList);
            }
            //统计导入数据并且输出给前端
            if (CollectionUtil.isNotEmpty(failList)) {
                pbmImportResultVO.setListFailedList(failList);
                //导出错误列表
                String tokenError = UUID.randomUUID().toString();
                pbmImportResultVO.setToken(tokenError);
                excelServices.exportFailList(headers, failList, tokenError,PbmTargetVO.class);
            }
            pbmImportResultVO.setSucCount(updateList.size() + insertList.size());
            pbmImportResultVO.setFailCount(failList.size());
        } catch (Exception e) {
            log.error("PbmTargetService.pbmTargetExport发生异常", e);
            pbmImportResultVO.setErrorMsg(JSONUtil.toJsonStr(e));
            pbmImportResultVO.setTips(2);
        }
        pbmImportResultVO.setTips(1);
        setImportResultByToken(token, pbmImportResultVO);
    }

    /**
     * 更新导出结果
     */
    public void setImportResultByToken(String token, Object object) {
        try {
            stringRedisTemplate.opsForValue().set(token, objectMapper.writeValueAsString(object), 30, TimeUnit.MINUTES);
        } catch (JsonProcessingException e) {
            log.error("setFileCommonByToken，写入标记缓存发生异常", e);
        }
    }


    public ResponseEntity<Result<?>> queryImportResult(String token) throws JsonProcessingException {
        String result = stringRedisTemplate.opsForValue().get(token);
        return new ResponseEntity<>(new Result<>(objectMapper.readValue(result, PbmImportResultVO.class), errors), HttpStatus.OK);
    }


    public static boolean isValidDate(String dateStr){
        //判断结果 默认为true
        boolean judgeresult=false;
        if(dateStr.length() == 7){
            judgeresult = true;
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        try{
            format.setLenient(false);
            format.parse(dateStr);
        }catch(Exception e){
            judgeresult = false;
        }
        String yearStr=dateStr.split("-")[0];
        if(yearStr.startsWith("0")||yearStr.length()!=4){
            judgeresult=false;
        }
        return judgeresult;
    }
}
