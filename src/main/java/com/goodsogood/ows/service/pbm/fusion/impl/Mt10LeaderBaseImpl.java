package com.goodsogood.ows.service.pbm.fusion.impl;

import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.PbmWorkDataConfig;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.mapper.user.OrgLeaderMapper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.mapper.user.PartyGroupMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.fusion.DualLifeDetail;
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData;
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData;
import com.goodsogood.ows.model.mongodb.fusion.LeadersContactDetail;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.service.pbm.CalculatingTimeUtils;
import com.goodsogood.ows.service.pbm.TimeRange;
import com.goodsogood.ows.service.pbm.fusion.IFusionDataService;
import com.goodsogood.ows.utils.ArithUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 领导联系基层 年度 2分
 */
@Service("LEADERBASE")
@Log4j2
public class Mt10LeaderBaseImpl  implements IFusionDataService {
    private final OrganizationMapper organizationMapper;
    private final MeetingMapper meetingMapper;
    private final PartyGroupMapper partyGroupMapper;
    private final OrgLeaderMapper orgLeaderMapper;
    private final PbmWorkDataConfig pbmWorkDataConfig;
    private final MyMongoTemplate myMongoTemplate;

    @Value("${user-option.virtualOrg}")
    private String excludeOrg;

    @Autowired
    public Mt10LeaderBaseImpl(OrganizationMapper organizationMapper, MeetingMapper meetingMapper, PartyGroupMapper partyGroupMapper, OrgLeaderMapper orgLeaderMapper, PbmWorkDataConfig pbmWorkDataConfig, MyMongoTemplate myMongoTemplate) {
        this.organizationMapper = organizationMapper;
        this.meetingMapper = meetingMapper;
        this.partyGroupMapper = partyGroupMapper;
        this.orgLeaderMapper = orgLeaderMapper;
        this.pbmWorkDataConfig = pbmWorkDataConfig;
        this.myMongoTemplate = myMongoTemplate;
    }


    @NotNull
    @Override
    public List<? extends FusionBaseData> collect(long unitId, @NotNull String unitName, long itemId, int year, int month, long regionId) {
        String status = pbmWorkDataConfig.getMeetingMap().getStatus();
        List<? extends FusionBaseData> list = new ArrayList<>();
        String monthStr = month>9? ""+month: "0"+month;
        String dateMonth = year+"-"+monthStr;
        //查询用户中心领导以及领导联系支部
        List<LeadersContactDetail> partyMemberOrgList = partyGroupMapper.queryPartyMemberOrg(unitId);//党组班子成员及其联系支部
        if(CollectionUtils.isEmpty(partyMemberOrgList)){
            return Collections.emptyList();
        }
        List<LeadersContactDetail> lectureList = meetingMapper.lectureList(unitId, pbmWorkDataConfig.getMeetingMap().getTypes(itemId),status,dateMonth,partyMemberOrgList);
        if(CollectionUtils.isEmpty(lectureList)){//没有讲党课的人
            partyMemberOrgList.forEach(i-> setUnitInfo(unitId, unitName, itemId, year, month, i));
            return partyMemberOrgList;
        }
        Map<Long,List<LeadersContactDetail>> lectureLifeMap = lectureList.stream().collect(Collectors.groupingBy(LeadersContactDetail::getUserId));
        partyMemberOrgList.forEach(i->{
            if(CollectionUtils.isEmpty(lectureLifeMap.get(i.getUserId()))){
                i.setJoinNum(0);
                setUnitInfo(unitId, unitName, itemId, year, month, i);
            }else{
                List<LeadersContactDetail> lectures =  lectureLifeMap.get(i.getUserId());
                long joinNum = lectures.stream().filter(j->j.getOrgId().equals(i.getOrgId())).count();//注意空指针
                i.setJoinNum((int) joinNum);
                setUnitInfo(unitId, unitName, itemId, year, month, i);
            }
        });
        return partyMemberOrgList;
    }


    private static void setUnitInfo(long unitId, String unitName, long itemId, int year, int month, LeadersContactDetail i) {
        i.setUnitId(unitId);
        i.setUnitName(unitName);
        i.setYear(year);
        i.setMonth(month);
        i.setItemId(itemId);
    }



    @NotNull
    @Override
    public List<FusionItemData> calculate(@NotNull List<FusionItemData> itemList, int year, int month, long regionId, @NotNull PbmFusionItemEntity itemEntity) {
        List<Long> unitIds = itemList.stream().map(FusionItemData::getUnitId).collect(Collectors.toList());
        TimeRange timeRange = CalculatingTimeUtils.Companion.getMeetingTimeRange(year, month, itemEntity.getCycle());
        List<LeadersContactDetail> list = this.getCollectData(LeadersContactDetail.class, myMongoTemplate,unitIds,itemEntity.getFusionItemId(),timeRange.getStartYear(),null,timeRange.getStartMonth(), timeRange.getEndMonth(), pbmWorkDataConfig);
        Map<Long,List<LeadersContactDetail>> map = list.stream().collect(Collectors.groupingBy(LeadersContactDetail::getUnitId));
        itemList.forEach(i->{
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(map.get(i.getUnitId()))){
                long leaderNum = map.get(i.getUnitId()).stream().map(LeadersContactDetail::getUserId).distinct().count();
                Map<String, Integer> userNumMap = map.get(i.getUnitId()).stream().collect(Collectors.groupingBy(s-> ""+s.getUserId()+"_"+s.getOrgId(),Collectors.summingInt(LeadersContactDetail::getJoinNum)));
                Set<String> notFinishUser = new HashSet<>();
                for(Map.Entry<String, Integer> entry: userNumMap.entrySet()){
                    if(entry.getValue()==0){
                        notFinishUser.add(entry.getKey().split("_")[0]);
                    }
                }
                long notFinishNum = notFinishUser.isEmpty()? 0 :  notFinishUser.stream().distinct().count();
                long finishNum = leaderNum - notFinishNum;
                i.setResult("完成比例:"+finishNum+"/"+leaderNum);
                if(leaderNum==0){
                    i.setScore(0.00);
                }else{
                    i.setScore(ArithUtils.round(finishNum/(leaderNum*1.0)*itemEntity.getBaseScore(),2));
                }
            }else{
                i.setResult("完成比例:0/0");
                i.setScore(0.00);
            }
        });
        return itemList;
    }

    @NotNull
    @Override
    public List<UserInfoBase> getUserListByUnitId(long unitId, long regionId, @NotNull List<Long> excludeOrg, @NotNull OrganizationMapper organizationMapper, @NotNull UserMapper userMapper) {
        return IFusionDataService.DefaultImpls.getUserListByUnitId(this, unitId, regionId, excludeOrg, organizationMapper, userMapper);
    }

    @NotNull
    @Override
    public OrganizationEntity getOrgById(long orgId, @NotNull OrganizationMapper organizationMapper) {
        return IFusionDataService.DefaultImpls.getOrgById(this, orgId, organizationMapper);
    }




    @Nullable
    @Override
    public List<Long> getOrgListByUnitId(long unitId, long regionId, @NotNull List<Long> excludeOrg, @NotNull OrganizationMapper organizationMapper) {
        return IFusionDataService.DefaultImpls.getOrgListByUnitId(this, unitId, regionId, excludeOrg, organizationMapper);
    }

    @NotNull
    @Override
    public <T extends FusionBaseData> List<T> getCollectData(@NotNull Class<T> clazz, @NotNull MyMongoTemplate mongoTemplate, @NotNull List<Long> unitIds, long itemId, int year, @Nullable Integer month, @Nullable Integer startMonth, @Nullable Integer endMonth, @NotNull PbmWorkDataConfig pbmWorkDataConfig) {
        return IFusionDataService.DefaultImpls.getCollectData(this, clazz, mongoTemplate, unitIds, itemId, year, month, startMonth, endMonth, pbmWorkDataConfig);
    }
}
