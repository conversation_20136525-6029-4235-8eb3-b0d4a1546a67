package com.goodsogood.ows.service.pbm

import com.goodsogood.ows.common.ExternalSort
import com.goodsogood.ows.component.Init
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.mapper.sas.WorkItemMapper
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import com.goodsogood.ows.model.mongodb.pbm.OrgWorkDetailInfo
import com.goodsogood.ows.model.mongodb.pbm.UserWorkDetailInfo
import com.goodsogood.ows.service.user.UserMongoService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service
import tk.mybatis.mapper.entity.Example
import java.io.File
import java.math.RoundingMode
import java.text.DecimalFormat
import java.util.*


/**
 * 党业融合 - 党务/业务工作详情主服务类
 * <AUTHOR>
 * @createTime 2022年06月22日 19:59:00
 */
@Service
class PbmWorkDataService(
    val userWorkDataFactory: UserWorkDataFactory,
    val orgWorkDataFactory: OrgWorkDataFactory,
    val userMongoService: UserMongoService,
    val mongoTemplate: MyMongoTemplate,
    val workItemMapper: WorkItemMapper,
    val redisTemplate: StringRedisTemplate,
    val pbmBufferTriggerService: PbmBufferTriggerService
) {

    companion object {
        const val REDIS_DETAIL_USER_KEY = "REDIS_DETAIL_USER_KEY"
        const val REDIS_DETAIL_ORG_KEY = "REDIS_DETAIL_USER_KEY"
    }

    val log: Logger = LoggerFactory.getLogger(PbmWorkDataService::class.java)

    /**
     * @title 计算用户工作数据
     * <AUTHOR>
     * @param regionId      区域ID
     * @param unitId        单位ID
     * @param year          时间  yyyy
     * @param month         月份  MM
     * @param week          周   一年第几周
     * @param workItemId    指标ID
     * @param type          类型 1-党务，2-业务
     * @updateTime 2022/6/22 20:53
     * @return
     * @throws
     */
    fun calculatePbmUserWork(
        regionId: Long,
        unitId: Long? = null,
        year: Int,
        month: Int,
        week: Int? = null,
        workItemId: Long? = null,
        type: Int
    ) {
        val userIds =
            this.userMongoService.getUserIdList(regionId = regionId, unitId = unitId, isParty = type)
        // 查询指标
        val workItemList = this.getWorkItem(regionId, workItemId, 1, type)
        // 循环指标
        workItemList.forEach { item ->
            val uuid = UUID.randomUUID().toString()
            val redisKey = REDIS_DETAIL_USER_KEY + uuid
            log.debug("开始计算指标[${item.name}] uuid: $uuid")
            if (item.strategyName.isNullOrBlank()) {
                log.debug("指标[${item.name}]策略为空")
                return@forEach
            }
            // 本次使用外排的文件
            val mainFile = File(ExternalSort.path + ExternalSort.MAIN_FILE + uuid)
            log.debug("调用${item}排序文件生成${mainFile}")
            // 清空redis
            redisTemplate.delete(redisKey)
            // 根据指标获取服务类
            val service = userWorkDataFactory.getUserWorkData(item.strategyName)
            // 计算时间
            val timeRanges = CalculatingTimeUtils.calculatingTime(year, month, week, item.cycle)
            if (service != null) {
                // 循环查询时间
                timeRanges.forEach { time ->
                    // 循环人员ID
                    userIds.forEach { userId ->
                        if (userId != null) {
                            // 获取当前指标的值
                            val data =
                                service.getUserWorkData(userId, time.startTime, time.endTime, time.week, regionId, item)
                            // 值加入外排逻辑
                            val v = getLong(item.dataType, data)
                            // 写入排序文件
                            if (item.compareType != 0) {
                                //log.debug("[addData]写入文件${mainFile}数据: $v")
                                ExternalSort.addData(mainFile, v)
                            }
                            // 处理数据
                            val detail = service.dealData(userId, v, data, year, month, time.week, item)
                            // 判断指标是否需要比较
                            if (item.compareType != 0) {
                                // 存redis
                                redisTemplate.opsForHash<String, String>()
                                    .put(redisKey, userId.toString(), Init.objectMapper.writeValueAsString(detail))
                            } else {
                                // 存库
                                pbmBufferTriggerService.addBuffer(detail)
                            }
                        }
                    }
                    // 判断指标是否需要比较
                    if (item.compareType != 0) {
                        log.debug("调用${item}排序开始")
                        // 计算排名
                        val mergedFile = ExternalSort.mSort(mainFile, userIds.size.div(10))
                        log.debug("调用${item}排序完成${mergedFile}")
                        // 排名计算完成, 写入结果
                        userIds.forEach { userId ->
                            // 从redis里获取
                            val detail = redisTemplate.opsForHash<String, String>().get(redisKey, userId.toString())
                            if (detail != null) {
                                val detailInfo = Init.objectMapper.readValue(detail, UserWorkDetailInfo::class.java)
                                service.getResultCompare(mergedFile, userIds.size.toLong(), item, detailInfo)
                                pbmBufferTriggerService.addBuffer(detailInfo)
                            }
                        }
                        // 删除排序好的文件
                        ExternalSort.deleteFile(mergedFile)
                    }
                }
                // 删除原文件
                ExternalSort.deleteFile(mainFile)
            }
            // 清空redis
            redisTemplate.delete(redisKey)
            log.debug("指标[${item.name}]计算完成")
        }
    }

    /**
     * @title 计算单位工作数据
     * <AUTHOR>
     * @param regionId      区域ID
     * @param unitId        单位ID
     * @param year          时间  yyyy
     * @param month         月份  MM
     * @param week          周   一年第几周
     * @param workItemId    指标ID
     * @param type          类型 1-党务，2-业务
     * @updateTime 2022/6/22 20:53
     * @return
     * @throws
     */
    fun calculatePbmOrgWork(
        regionId: Long,
        unitId: Long? = null,
        year: Int,
        month: Int,
        week: Int? = null,
        workItemId: Long? = null,
        type: Int? = null
    ) {
        // 判断type是党务还是业务
        val unitIds = this.userMongoService.getUnitIdList(regionId = regionId, unitId = unitId)
        // 查询指标
        val workItemList = this.getWorkItem(regionId, workItemId, 2, type)
        // 循环指标
        workItemList.forEach { item ->
            val uuid = UUID.randomUUID().toString()
            val redisKey = REDIS_DETAIL_ORG_KEY + uuid
            log.debug("开始计算指标[${item.name}] uuid: $uuid")
            if (item.strategyName.isNullOrBlank()) {
                log.debug("指标[${item.name}]策略为空")
                return@forEach
            }
            // 本次使用外排的文件
            val mainFile = File(ExternalSort.path + ExternalSort.MAIN_FILE + UUID.randomUUID().toString())
            log.debug("调用${item}排序文件生成${mainFile}")
            // 清空redis
            redisTemplate.delete(redisKey)
            // 根据指标获取服务类
            val service = orgWorkDataFactory.getOrgWorkData(item.strategyName)
            // 计算时间
            val timeRanges = CalculatingTimeUtils.calculatingTime(year, month, week, item.cycle)
            if (service != null) {
                // 循环查询时间
                timeRanges.forEach { time ->
                    // 循环人员ID
                    unitIds.forEach { unitId ->
                        // 获取当前指标的值
                        val data =
                            service.getOrgWorkData(unitId, time.startTime, time.endTime, time.week, regionId, item)
                        log.debug("组织工作详情单元 $unitId 获取 ${item.workItemId} 的值 $data")
                        // 值加入外排逻辑
                        val v = getLong(item.dataType, data)
                        // 写入排序文件
                        if (item.compareType != 0) {
                            //log.debug("[addData]写入文件${mainFile}数据: $v")
                            ExternalSort.addData(mainFile, v)
                        }
                        // 处理数据
                        val detail = service.dealData(unitId, v, data, year, month, time.week, item)
                        log.debug("党组织工作详情数据处理结果：$detail")
                        // 判断指标是否需要比较
                        if (item.compareType != 0) {
                            // 存redis
                            redisTemplate.opsForHash<String, String>()
                                .put(redisKey, unitId.toString(), Init.objectMapper.writeValueAsString(detail))
                        } else {
                            // 存库
                            pbmBufferTriggerService.addBuffer(detail)
                        }
                    }
                    // 判断指标是否需要比较
                    if (item.compareType != 0) {
                        //log.debug("调用${item}排序开始")
                        // 计算排名 返回的排序好的文件
                        val mergedFile = ExternalSort.mSort(mainFile, unitIds.size.div(10))
                        //log.debug("调用${item}排序完成${mergedFile}")
                        // 排名计算完成, 写入结果
                        unitIds.forEach { unitId ->
                            // 从mongo里获取
                            val detail = redisTemplate.opsForHash<String, String>().get(redisKey, unitId.toString())
                            if (detail != null) {
                                val detailInfo = Init.objectMapper.readValue(detail, OrgWorkDetailInfo::class.java)
                                service.getResultCompare(mergedFile, unitIds.size.toLong(), item, detailInfo)
                                pbmBufferTriggerService.addBuffer(detailInfo)
                            }
                        }
                        // 删除排序好的文件
                        ExternalSort.deleteFile(mergedFile)
                    }
                }
                // 删除原文件
                ExternalSort.deleteFile(mainFile)
            }
            // 清空redis
            redisTemplate.delete(redisKey)
            log.debug("指标[${item.name}]计算完成")
        }
    }

    /**
     * @title 查询指标列表
     * <AUTHOR>
     * @param regionId
     * @param workItemId
     * @param against
     * @param type
     * @updateTime 2022/6/24 14:20
     * @return
     * @throws
     */
    private fun getWorkItem(
        regionId: Long,
        workItemId: Long? = null,
        against: Int? = null,
        type: Int? = null
    ): MutableList<WorkItemEntity> {
        val example = Example(WorkItemEntity::class.java)
        val criteria = example.createCriteria()
        criteria.andEqualTo("regionId", regionId)
        criteria.andEqualTo("type", type)
        criteria.andEqualTo("against", against)
        if (workItemId != null) {
            criteria.andEqualTo("workItemId", workItemId)
        }
        return workItemMapper.selectByExample(example)
    }

    /**
     * 转换Long
     */
    private fun getLong(dataType: Int?, data: Any?): Long {
        var v: Long = -1
        when (dataType) {
            1 -> {
                v = if (data != null && data is Int) {
                    data.toLong()
                } else {
                    0
                }
            }
            2 -> {
                v = if (data != null && data is Double) {
                    val df = DecimalFormat("#.#")
                    df.roundingMode = RoundingMode.CEILING
                    (df.format(data).toDouble() * 10).toLong()
                } else {
                    0
                }
            }
            3 -> {
                v = if (data != null && data is Date) {
                    data.time
                } else {
                    // 暂时为党费交纳提供，如果没有交纳时间，定为9999-12-31 24:00:00
                    253402272000000L
                }
            }
        }
        return v
    }
}