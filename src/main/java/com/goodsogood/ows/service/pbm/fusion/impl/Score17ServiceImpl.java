package com.goodsogood.ows.service.pbm.fusion.impl;

import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.PbmWorkDataConfig;
import com.goodsogood.ows.mapper.score.ScoreMidDetailMapper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData;
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData;
import com.goodsogood.ows.model.mongodb.fusion.SystemUsageDetail;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.service.pbm.CalculatingTimeUtils;
import com.goodsogood.ows.service.pbm.TimeRange;
import com.goodsogood.ows.service.pbm.fusion.FusionResultUtils;
import com.goodsogood.ows.service.pbm.fusion.IFusionDataService;
import com.goodsogood.ows.utils.ArithUtils;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service("SCORESTATICAL")
@Log4j2
public class Score17ServiceImpl implements IFusionDataService {
    private final ScoreMidDetailMapper scoreMidDetailMapper;
    private final MyMongoTemplate myMongoTemplate;
    private final PbmWorkDataConfig pbmWorkDataConfig;

    @Autowired
    public Score17ServiceImpl(ScoreMidDetailMapper scoreMidDetailMapper, MyMongoTemplate myMongoTemplate, PbmWorkDataConfig pbmWorkDataConfig) {
        this.scoreMidDetailMapper = scoreMidDetailMapper;
        this.myMongoTemplate = myMongoTemplate;
        this.pbmWorkDataConfig = pbmWorkDataConfig;
    }

    @NotNull
    @Override
    public List<? extends FusionBaseData> collect(long unitId, @NotNull String unitName, long itemId, int year, int month, long regionId) {
        String monthStr = month > 9 ? "" + month : "0" + month;
        String dateMonth = year + "-" + monthStr;
        List<SystemUsageDetail> scoreList = scoreMidDetailMapper.queryUsualMonthScore(unitId, dateMonth);
        scoreList.parallelStream().forEach(i -> {
            i.setItemId(itemId);
            i.setUnitId(unitId);
            i.setUnitName(unitName);
            i.setYear(year);
            i.setMonth(month);
        });
        return scoreList;
    }


    @NotNull
    @Override
    public List<FusionItemData> calculate(@NotNull List<FusionItemData> itemList, int year, int month, long regionId, @NotNull PbmFusionItemEntity itemEntity) {
        List<Long> unitIds = itemList.stream().map(FusionItemData::getUnitId).collect(Collectors.toList());
        TimeRange timeRange = CalculatingTimeUtils.Companion.getMeetingTimeRange(year, month, itemEntity.getCycle());
        List<SystemUsageDetail> list = this.getCollectData(SystemUsageDetail.class, myMongoTemplate, unitIds, itemEntity.getFusionItemId(), timeRange.getStartYear(), null, timeRange.getStartMonth(), timeRange.getEndMonth(), pbmWorkDataConfig);
        Map<Long, List<SystemUsageDetail>> map = list.stream().collect(Collectors.groupingBy(SystemUsageDetail::getUnitId));
        itemList.forEach(i -> {
            if (CollectionUtils.isEmpty(map) || CollectionUtils.isEmpty(map.get(i.getUnitId()))) {
                i.setItemScore(0.00);
            } else {
                OptionalDouble scoreOption = map.get(i.getUnitId()).stream().mapToDouble(SystemUsageDetail::getScore).average();
                double score = scoreOption.isPresent() ? ArithUtils.round(scoreOption.getAsDouble(), 2) : 0.00;
                i.setItemScore(score);
            }
        });
        //进行排名
        FusionResultUtils.Companion.rank(itemList, itemEntity.getBaseScore(), pbmWorkDataConfig);
        //设置result
        itemList.forEach(i -> {
            if (i.getRank() != null && i.getRank() != 0) {
                i.setResult("排名:" + i.getRank());
            } else {
                i.setResult("排名: --");
            }
        });
        return itemList;
    }

    @NotNull
    @Override
    public List<UserInfoBase> getUserListByUnitId(long unitId, long regionId, @NotNull List<Long> excludeOrg, @NotNull OrganizationMapper organizationMapper, @NotNull UserMapper userMapper) {
        return IFusionDataService.DefaultImpls.getUserListByUnitId(this, unitId, regionId, excludeOrg, organizationMapper, userMapper);
    }

    @NotNull
    @Override
    public OrganizationEntity getOrgById(long orgId, @NotNull OrganizationMapper organizationMapper) {
        return IFusionDataService.DefaultImpls.getOrgById(this, orgId, organizationMapper);
    }


    @Nullable
    @Override
    public List<Long> getOrgListByUnitId(long unitId, long regionId, @NotNull List<Long> excludeOrg, @NotNull OrganizationMapper organizationMapper) {
        return IFusionDataService.DefaultImpls.getOrgListByUnitId(this, unitId, regionId, excludeOrg, organizationMapper);
    }

    @NotNull
    @Override
    public <T extends FusionBaseData> List<T> getCollectData(@NotNull Class<T> clazz, @NotNull MyMongoTemplate mongoTemplate, @NotNull List<Long> unitIds, long itemId, int year, @Nullable Integer month, @Nullable Integer startMonth, @Nullable Integer endMonth, @NotNull PbmWorkDataConfig pbmWorkDataConfig) {
        return IFusionDataService.DefaultImpls.getCollectData(this, clazz, mongoTemplate, unitIds, itemId, year, month, startMonth, endMonth, pbmWorkDataConfig);
    }
}
