package com.goodsogood.ows.service.pbm.impl

import com.goodsogood.ows.mapper.user.OrgPeriodMapper
import com.goodsogood.ows.mapper.user.OrganizationMapper
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import com.goodsogood.ows.model.mongodb.pbm.OrgWorkDetailInfo
import com.goodsogood.ows.service.pbm.IPbmOrgWorkDataService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * 健全组织机构，配强组织力量
 *
 * <AUTHOR>
 * @Date 2022-06-27 21:21:46
 * @Description PbmUserOrgPeriodServiceImpl
 *
 */
@Service("ORG_PERIOD_DETAIL")
class IPbmUserOrgPeriodServiceImpl(
    val orgPeriodMapper: OrgPeriodMapper,
    val organizationMapper: OrganizationMapper
) : IPbmOrgWorkDataService {

    private val log: Logger = LoggerFactory.getLogger(IPbmOrgWorkDataService::class.java)

    @Value("\${user-option.virtualOrg}")
    private val virtualOrg: String? = null


    override fun getOrgWorkData(
        orgId: Long,
        startTime: String?,
        endTime: String?,
        week: Int?,
        regionId: Long,
        workItem: WorkItemEntity
    ): Any? {
        val queryDate = endTime?.substring(0, 8).plus("01")
        val orgIds = mutableListOf<Long>()
        // 根据单位ID查询支部ids
        val excludeOrg = this.virtualOrg?.split(",")
        excludeOrg?.forEach {
            orgIds.add(it.toLong())
        }
        val orgList = this.organizationMapper.findAllOrgByUnit(orgId, orgIds)
        log.debug("健全组织机构，配强组织力量-orgList=[${orgList}],orgId=${orgId},startTime=${startTime},endTime=${endTime}")
        for (it in orgList) {
            val cnt = this.orgPeriodMapper.periodExistLeaderCount(regionId, queryDate, it)
            if (cnt == 0) return false
        }
        return true
    }

    override fun dealData(
        orgId: Long,
        rankValue: Long?,
        value: Any?,
        year: Int,
        month: Int,
        week: Int?,
        entity: WorkItemEntity
    ): OrgWorkDetailInfo {
        log.debug("健全组织机构，配强组织力量-value=[${value}],orgId=[${orgId}]")
        var v = "未设置"
        if (value is Boolean) {
            v = if (value) "已设置" else "未设置"
        }
        return OrgWorkDetailInfo(
            orgId = orgId,
            regionId = entity.regionId,
            workItemId = entity.workItemId,
            workItemName = entity.name,
            cycle = entity.cycle,
            criterion = entity.criterion,
            year = year,
            month = month,
            week = week,
            rankValue = rankValue,
            workResult = entity.description?.format(v),
            remark = entity.remark,
            type = entity.type,
            createTime = LocalDateTime.now()
        )
    }
}