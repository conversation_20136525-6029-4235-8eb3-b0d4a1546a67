package com.goodsogood.ows.service.pbm

import com.goodsogood.ows.common.ExternalSort
import com.goodsogood.ows.model.mongodb.pbm.UserWorkDetailInfo
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import java.io.File
import java.math.RoundingMode
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.time.LocalDateTime

/**
 * 用户工作详情服务接口类
 * <AUTHOR>
 * @createTime 2022年06月21日 17:26:00
 */
interface PbmUserWorkDataService {

    /**
     * @title 抽取用户工作详情报告
     * @description     抽取用户工作详情报告 - 接口类
     * <AUTHOR>
     * @param   userId      用户ID
     * @param   startTime   开始时间   格式：yyyy-MM-dd  要包含开始时间
     * @param   endTime     结束时间   格式：yyyy-MM-dd  要包含结束时间
     * @param   week        年度第几周
     * @updateTime 2022/6/21 23:28
     * @return 返回的数据类型 包含int,double,time,boolean
     * 注:
     *  当没有完成时，返回null
     *  当类型为百分比时 需要乘100，返回为小数点暂不保留，后续有逻辑处理。
     *  当返回类型为时间时 返回类型转换为Date
     */
    fun getUserWorkData(userId: Long, startTime: String?, endTime: String?, week: Int? = null, regionId: Long, workItem: WorkItemEntity): Any?

    /**
     * @title 计算 结果比较的值
     * @description 需要拉通所有用户后才能进行计算
     * <AUTHOR> 
     * @param 
     * @updateTime 2022/6/22 11:08 
     * @return 
     * @throws 
     */
    fun getResultCompare(file: File, total: Long, workItem: WorkItemEntity, entity: UserWorkDetailInfo): UserWorkDetailInfo? {
        val score = entity.rankValue
        // 取重复排名的第一个下标
        val rank = if(score != null) { ExternalSort.getDataIndex(file, score) } else { 0 }
        // 取去重排名的的下标
        val scoreRank = if(score != null) { ExternalSort.getDeduplicateRankIndex(file, score) } else { 0 }
        // 取去重后的长度
        val size = ExternalSort.getDataSize(file)
        // 百分比格式化
        val df = DecimalFormat("##.#")
        df.roundingMode = RoundingMode.CEILING
        var compare = ""
        when(workItem.compareType) {
            1 -> {
                compare = if (scoreRank == size) {
                    "100%"
                } else {
                    when (rank) {
                        -1L, 0L -> "0%"
                        else -> {
                            val re = rank.toDouble().div(total.toDouble()) * 100
                            "${df.format(re)}%"
                        }
                    }
                }
            }
            2 -> {
                compare = when(scoreRank) {
                    -1L,0L ->  "0"
                    else -> {
                        rank.toString()
                    }
                }
            }
        }
        entity.resultCompare = workItem.compareDescription?.format(compare)
        entity.updateTime = LocalDateTime.now()
        return entity
    }

    /**
     * @title 处理用户工作详情报告
     * @description    抽取用户工作详情报告 - 接口类 - 提供默认方法，如果需要特殊处理的，可以重新实现
     * <AUTHOR>
     * @param   userId      用户ID
     * @param   entity      指标实体类
     * @updateTime 2022/6/22 09:32
     * @return  返回用户工作详情类
     * @throws
     */
    fun dealData(userId: Long, rankValue: Long? = null, value: Any? = null, year: Int, month: Int, week: Int? = null, entity: WorkItemEntity) : UserWorkDetailInfo {
        var obj = entity.description
        // 判断value 的数据类型
        when(entity.dataType) {
            // int
            1 -> {
                obj = obj?.format(value)
            }
            // double
            2 -> {
                if(value is Double) {
                    val df = DecimalFormat("#.#")
                    df.roundingMode = RoundingMode.CEILING
                    obj = obj?.format("${df.format(value).toDouble().toInt()}%")
                }
            }
            // time
            3 -> {
                obj = if (value != null) {
                    val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                    obj?.format(format.format(value))
                } else {
                    obj?.format("未完成")
                }
            }
            // boolean
            4 -> {
                if (value is Boolean) {
                    obj = if (value) {
                        obj?.format("已完成")
                    } else {
                        obj?.format("未完成")
                    }
                }
            }
        }
        return UserWorkDetailInfo(
            userId = userId,
            regionId =entity.regionId,
            workItemId = entity.workItemId,
            workItemName = entity.name,
            cycle = entity.cycle,
            criterion = entity.criterion,
            year = year,
            month = month,
            week = week,
            rankValue = rankValue,
            workResult = obj,
            remark = entity.remark,
            type = entity.type,
            createTime = LocalDateTime.now()
        )
    }
}