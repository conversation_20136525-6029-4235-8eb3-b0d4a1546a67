package com.goodsogood.ows.service.pbm.impl

import com.goodsogood.ows.mapper.user.PartyGroupMapper
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import com.goodsogood.ows.model.mongodb.pbm.OrgWorkDetailInfo
import com.goodsogood.ows.service.pbm.IPbmOrgWorkDataService
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * 成立党的建设工作领导小组
 *
 * <AUTHOR>
 * @Date 2022-06-28 11:29:03
 * @Description PbmLeaderGroupServiceImpl
 *
 */
@Service("PARTY_LEADER_GROUP")
class IPbmLeaderGroupServiceImpl(
    private val partyGroupMapper: PartyGroupMapper
) : IPbmOrgWorkDataService {

    override fun getOrgWorkData(
        orgId: Long,
        startTime: String?,
        endTime: String?,
        week: Int?,
        regionId: Long,
        workItem: WorkItemEntity
    ): Any? {
        // 查询本单位是否设置了党建工作领导小组
        val existLeader = this.partyGroupMapper.existLeaderGroup(orgId)
        return existLeader is Long
    }

    override fun dealData(
        orgId: Long,
        rankValue: Long?,
        value: Any?,
        year: Int,
        month: Int,
        week: Int?,
        entity: WorkItemEntity
    ): OrgWorkDetailInfo {
        var v = "未设置"
        if (value is Boolean) {
            v = if (value) "已设置" else "未设置"
        }
        return OrgWorkDetailInfo(
            orgId = orgId,
            regionId = entity.regionId,
            workItemId = entity.workItemId,
            workItemName = entity.name,
            cycle = entity.cycle,
            criterion = entity.criterion,
            year = year,
            month = month,
            week = week,
            rankValue = rankValue,
            workResult = entity.description?.format(v),
            remark = entity.remark,
            type = entity.type,
            createTime = LocalDateTime.now()
        )
    }
}

/**
 * 建立党支部工作联系点制度
 *
 * <AUTHOR>
 * @Date 2022-06-28 11:29:03
 * @Description PbmBranchContactServiceImpl
 *
 */
@Service("PARTY_GROUP_CONTACT")
class IPbmBranchContactServiceImpl(
    private val partyGroupMapper: PartyGroupMapper
) : IPbmOrgWorkDataService {

    override fun getOrgWorkData(
        orgId: Long,
        startTime: String?,
        endTime: String?,
        week: Int?,
        regionId: Long,
        workItem: WorkItemEntity
    ): Any {
        // 查询本单位的党组成员是否均设置了联系支部
        return this.partyGroupMapper.existWorkContact(orgId, 2)
    }

    override fun dealData(
        orgId: Long,
        rankValue: Long?,
        value: Any?,
        year: Int,
        month: Int,
        week: Int?,
        entity: WorkItemEntity
    ): OrgWorkDetailInfo {
        var v = "未设置"
        if (value is Boolean) {
            v = if (value) "已设置" else "未设置"
        }
        return OrgWorkDetailInfo(
            orgId = orgId,
            regionId = entity.regionId,
            workItemId = entity.workItemId,
            workItemName = entity.name,
            cycle = entity.cycle,
            criterion = entity.criterion,
            year = year,
            month = month,
            week = week,
            rankValue = rankValue,
            workResult = entity.description?.format(v),
            remark = entity.remark,
            type = entity.type,
            createTime = LocalDateTime.now()
        )
    }
}

/**
 * 建立干部基层联系点制度
 *
 * <AUTHOR>
 * @Date 2022-06-28 11:29:03
 * @Description PbmLeaderContactServiceImpl
 *
 */
@Service("PARTY_LEADER_GROUP_CONTACT")
class IPbmLeaderContactServiceImpl(
    private val partyGroupMapper: PartyGroupMapper
) : IPbmOrgWorkDataService {

    override fun getOrgWorkData(
        orgId: Long,
        startTime: String?,
        endTime: String?,
        week: Int?,
        regionId: Long,
        workItem: WorkItemEntity
    ): Any {
        // 查询本单位的党组成员是否均设置了基层联系点
        return this.partyGroupMapper.existWorkContact(orgId, 3)
    }

    override fun dealData(
        orgId: Long,
        rankValue: Long?,
        value: Any?,
        year: Int,
        month: Int,
        week: Int?,
        entity: WorkItemEntity
    ): OrgWorkDetailInfo {
        var v = "未设置"
        if (value is Boolean) {
            v = if (value) "已设置" else "未设置"
        }
        return OrgWorkDetailInfo(
            orgId = orgId,
            regionId = entity.regionId,
            workItemId = entity.workItemId,
            workItemName = entity.name,
            cycle = entity.cycle,
            criterion = entity.criterion,
            year = year,
            month = month,
            week = week,
            rankValue = rankValue,
            workResult = entity.description?.format(v),
            remark = entity.remark,
            type = entity.type,
            createTime = LocalDateTime.now()
        )
    }
}