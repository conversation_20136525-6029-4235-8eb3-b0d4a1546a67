package com.goodsogood.ows.service.pbm.fusion.impl;

import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.PbmWorkDataConfig;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.mapper.user.OrgLeaderMapper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.mapper.user.PartyGroupMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.fusion.DualLifeDetail;
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData;
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.service.pbm.CalculatingTimeUtils;
import com.goodsogood.ows.service.pbm.TimeRange;
import com.goodsogood.ows.service.pbm.fusion.IFusionDataService;
import com.goodsogood.ows.utils.ArithUtils;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 双重组织生活  季度  4  单位领导干部每季度在所在支部参加三会一课、主题党日不少于2次，按单位领导干部完成比例进行指标分值计算
 */
@Service("DOUBLELIFE")
@Log4j2
public class Mt9DoubleLifeImpl implements IFusionDataService {
    private final OrganizationMapper organizationMapper;
    private final MeetingMapper meetingMapper;
    private final PartyGroupMapper partyGroupMapper;
    private final OrgLeaderMapper orgLeaderMapper;
    private final PbmWorkDataConfig pbmWorkDataConfig;
    private final MyMongoTemplate myMongoTemplate;
    private final UserMapper userMapper;
    @Value("${user-option.virtualOrg}")
    private String excludeOrg;

    @Autowired
    public Mt9DoubleLifeImpl(OrganizationMapper organizationMapper, MeetingMapper meetingMapper, PartyGroupMapper partyGroupMapper, OrgLeaderMapper orgLeaderMapper, PbmWorkDataConfig pbmWorkDataConfig, MyMongoTemplate myMongoTemplate, UserMapper userMapper) {
        this.organizationMapper = organizationMapper;
        this.meetingMapper = meetingMapper;
        this.partyGroupMapper = partyGroupMapper;
        this.orgLeaderMapper = orgLeaderMapper;
        this.pbmWorkDataConfig = pbmWorkDataConfig;
        this.myMongoTemplate = myMongoTemplate;
        this.userMapper = userMapper;
    }


    @NotNull
    @Override
    public List<? extends FusionBaseData> collect(long unitId, @NotNull String unitName, long itemId, int year, int month, long regionId) {
        String status = pbmWorkDataConfig.getMeetingMap().getStatus();
        List<? extends FusionBaseData> list = new ArrayList<>();
        String monthStr = month > 9 ? "" + month : "0" + month;
        String dateMonth = year + "-" + monthStr;
        List<DualLifeDetail> partyMemberList = partyGroupMapper.queryPartyMember(unitId);//党组班子成员
        List<DualLifeDetail> leaderMemberList = orgLeaderMapper.queryLeaderMember(unitId);//行政领导班子成员
        partyMemberList.addAll(leaderMemberList);
        if (CollectionUtils.isEmpty(partyMemberList)) {
            return Collections.emptyList();
        }
        List<DualLifeDetail> userList = partyMemberList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(s -> s.getUserId()))), ArrayList::new));
        //查询这些人的所属支部或党小组
        List<DualLifeDetail> userOrgList = userMapper.queryOrgAndGroup(userList);
        Map<Long,List<DualLifeDetail>> userOrgMap = userOrgList.stream().collect(Collectors.groupingBy(DualLifeDetail::getUserId));
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(userList)) {
            List<DualLifeDetail> leaderLife = meetingMapper.leaderLife(unitId, pbmWorkDataConfig.getMeetingMap().getTypes(itemId), status, dateMonth, userList);
            if (CollectionUtils.isEmpty(leaderLife)) {
                userList.parallelStream().forEach(j -> {
                    j.setJoinNum(0);
                    j.setUnitId(unitId);
                    j.setUnitName(unitName);
                    j.setYear(year);
                    j.setMonth(month);
                    j.setItemId(itemId);

                });
                return userList;
            }
            //组织生活数据
            Map<Long, List<DualLifeDetail>> leaderLifeMap = leaderLife.stream().collect(Collectors.groupingBy(DualLifeDetail::getUserId));
            userList.forEach(i -> {
                if (CollectionUtils.isEmpty(leaderLifeMap.get(i.getUserId()))) {
                    i.setJoinNum(0);
                    setUnitInfo(unitId, unitName, itemId, year, month, i);
                } else {
                    List<DualLifeDetail> tempList = leaderLifeMap.get(i.getUserId());
                    if(!CollectionUtils.isEmpty(tempList)){
                        if(!CollectionUtils.isEmpty(userOrgMap.get(i.getUserId()))){
                            //取交集
                            tempList.retainAll(userOrgMap.get(i.getUserId()));
                            i.setJoinNum(CollectionUtils.isEmpty(tempList)? 0 : tempList.size());
                        }else{
                            //如没有交集
                            i.setJoinNum(0);
                        }

                    }
//                    Map<Long,List<DualLifeDetail>> map = userOrgMap.get(i.getUserId()).stream().collect(Collectors.groupingBy(DualLifeDetail::getOrgId));
//                    long joinNum = leaderLife.stream().filter(j->j.getUserId().compareTo(i.getUserId())!=0 && map.containsKey(j.getOrgId())).count();
//                    i.setJoinNum((int) joinNum);
                    setUnitInfo(unitId, unitName, itemId, year, month, i);
                }
            });
            return userList;
        }
        return Collections.emptyList();
    }

    private static void setUnitInfo(long unitId, String unitName, long itemId, int year, int month, DualLifeDetail i) {
        i.setUnitId(unitId);
        i.setUnitName(unitName);
        i.setYear(year);
        i.setMonth(month);
        i.setItemId(itemId);
    }


    @NotNull
    @Override
    public List<FusionItemData> calculate(@NotNull List<FusionItemData> itemList, int year, int month, long regionId, @NotNull PbmFusionItemEntity itemEntity) {
        List<Long> unitIds = itemList.stream().map(FusionItemData::getUnitId).collect(Collectors.toList());
        TimeRange timeRange = CalculatingTimeUtils.Companion.getMeetingTimeRange(year, month, itemEntity.getCycle());
        List<DualLifeDetail> list = this.getCollectData(DualLifeDetail.class, myMongoTemplate, unitIds, itemEntity.getFusionItemId(), timeRange.getStartYear(), null, timeRange.getStartMonth(), timeRange.getEndMonth(), pbmWorkDataConfig);
        Map<Long, List<DualLifeDetail>> map = list.stream().collect(Collectors.groupingBy(DualLifeDetail::getUnitId));
        itemList.forEach(i -> {
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(map.get(i.getUnitId()))) {
                log.debug("双重党业融合year:"+ timeRange.getStartYear()+"_"+timeRange.getStartMonth());
                log.debug("双重党业融合list:"+ JsonUtils.toJson(map.get(i.getUnitId())));
                long leaderNum = map.get(i.getUnitId()).stream().map(DualLifeDetail::getUserId).distinct().count();
                Map<Long, Integer> userNumMap = map.get(i.getUnitId()).stream().collect(Collectors.groupingBy(DualLifeDetail::getUserId, Collectors.summingInt(DualLifeDetail::getJoinNum)));
                int finishNum = 0;
                for (Map.Entry<Long, Integer> entry : userNumMap.entrySet()) {
                    if (entry.getValue() >= 2) {
                        finishNum++;
                    }
                }
                i.setResult("完成比例:" + finishNum + "/" + leaderNum);
                if (leaderNum == 0) {
                    i.setScore(0.00);
                } else {
                    i.setScore(ArithUtils.round(finishNum / (leaderNum*1.0) * itemEntity.getBaseScore(),2));
                }
            } else {
                i.setResult("完成比例:0/0");
                i.setScore(0.00);
            }
        });
        return itemList;
    }

    @NotNull
    @Override
    public List<UserInfoBase> getUserListByUnitId(long unitId, long regionId, @NotNull List<Long> excludeOrg, @NotNull OrganizationMapper organizationMapper, @NotNull UserMapper userMapper) {
        return IFusionDataService.DefaultImpls.getUserListByUnitId(this, unitId, regionId, excludeOrg, organizationMapper, userMapper);
    }

    @NotNull
    @Override
    public OrganizationEntity getOrgById(long orgId, @NotNull OrganizationMapper organizationMapper) {
        return IFusionDataService.DefaultImpls.getOrgById(this, orgId, organizationMapper);
    }

    @Nullable
    @Override
    public List<Long> getOrgListByUnitId(long unitId, long regionId, @NotNull List<Long> excludeOrg, @NotNull OrganizationMapper organizationMapper) {
        return IFusionDataService.DefaultImpls.getOrgListByUnitId(this, unitId, regionId, excludeOrg, organizationMapper);
    }

    @NotNull
    @Override
    public <T extends FusionBaseData> List<T> getCollectData(@NotNull Class<T> clazz, @NotNull MyMongoTemplate mongoTemplate, @NotNull List<Long> unitIds, long itemId, int year, @Nullable Integer month, @Nullable Integer startMonth, @Nullable Integer endMonth, @NotNull PbmWorkDataConfig pbmWorkDataConfig) {
        return IFusionDataService.DefaultImpls.getCollectData(this, clazz, mongoTemplate, unitIds, itemId, year, month, startMonth, endMonth, pbmWorkDataConfig);
    }
}
