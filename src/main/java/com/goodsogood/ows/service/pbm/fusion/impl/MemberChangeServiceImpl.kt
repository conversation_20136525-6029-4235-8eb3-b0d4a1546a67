package com.goodsogood.ows.service.pbm.fusion.impl

import com.goodsogood.ows.common.SequenceEnum
import com.goodsogood.ows.configuration.EcpTagConfig
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.PbmWorkDataConfig
import com.goodsogood.ows.mapper.user.OrganizationMapper
import com.goodsogood.ows.mapper.user.UserChangeLogMapper
import com.goodsogood.ows.mapper.user.UserMapper
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData
import com.goodsogood.ows.model.mongodb.fusion.MemberChangeDetail
import com.goodsogood.ows.service.pbm.CalculatingTimeUtils
import com.goodsogood.ows.service.pbm.fusion.IFusionDataService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

/**
 * 党务干部交流
 * <AUTHOR>
 * @Date 2023-03-23 9:48:35
 * @Description PartyLeaderCommunicationServiceImpl
 *
 */
@Service("MEMBER_CHANGE")
class MemberChangeServiceImpl(
    val organizationMapper: OrganizationMapper,
    val userMapper: UserMapper,
    val userChangeLogMapper: UserChangeLogMapper,
    val myMongoTemplate: MyMongoTemplate,
    val pbmWorkDataConfig: PbmWorkDataConfig,
    val ecpTagConfig: EcpTagConfig
) : IFusionDataService {

    val log: Logger = LoggerFactory.getLogger(MemberChangeServiceImpl::class.java)

    @Value("\${tbc-tag.fullTimeId}")
    private val fullTimeId: String? = null

    @Value("\${tbc-tag.partyTimeId}")
    private val partyTimeId: String? = null

    @Value("\${user-option.virtualOrg}")
    private val virtualOrg: String? = null

    override fun collect(
        unitId: Long,
        unitName: String,
        itemId: Long,
        year: Int,
        month: Int,
        regionId: Long
    ): MutableList<out FusionBaseData> {
        val updateTime = System.currentTimeMillis()
        val result = mutableListOf<MemberChangeDetail>()
        // 需要排除的支部ID
        val orgIds = mutableListOf<Long>()
        val excludeOrg = this.virtualOrg?.split(",")
        excludeOrg?.forEach {
            orgIds.add(it.toLong())
        }
        // 根据支部ID查询单位党员
        val unitUserList = this.getUserListByUnitId(unitId, regionId, orgIds, organizationMapper, userMapper)
        // 获取所有用户ID列表
        val userList = unitUserList.map { it.userId }
        // 根据用户列表查询基础数据
        userList.forEach {
            // 党务到业务
            val partyToBusiness = this.userChangeLogMapper.partyToBusiness(
                it,
                SequenceEnum.MANAGE.type,
                year
            )
            if (partyToBusiness != null) {
                // 是否去掉 专职党务工作者 标签
                val removeTagDone = this.userChangeLogMapper.removeTag(fullTimeId!!, it, year)
                if (removeTagDone != null && removeTagDone > 0) {
                    partyToBusiness.removeTagDone = 1
                }
                result.add(partyToBusiness)
            }
            // 业务到党务
            val businessToParty = this.userChangeLogMapper.businessToParty(
                it,
                year,
                SequenceEnum.MARKETING.type,
                SequenceEnum.PRODUCTION.type,
                SequenceEnum.MONOPOLY.type,
                SequenceEnum.MANAGE.type
            )
            if (businessToParty != null) {
                // 是否添加 专职党务工作者 标签
                val addTagDone = this.userChangeLogMapper.addTag(fullTimeId!!, it, year)
                if (addTagDone != null && addTagDone > 0) {
                    businessToParty.addTagDone = 1
                }
                result.add(businessToParty)
            }
        }
        result.forEach {
            it.itemId = itemId
            it.unitId = unitId
            it.unitName = unitName
            it.year = year
            it.month = month
            it.updateTime = updateTime
        }
        return result
    }

    override fun calculate(
        itemList: MutableList<FusionItemData>,
        year: Int,
        month: Int,
        regionId: Long,
        itemEntity: PbmFusionItemEntity
    ): MutableList<FusionItemData> {
        val timeRange =
            CalculatingTimeUtils.getMeetingTimeRange(year = year, month = month, cycle = itemEntity.cycle!!)
        val memberChangeDetail = this.getCollectData(
            clazz = MemberChangeDetail::class.java,
            mongoTemplate = myMongoTemplate,
            unitIds = itemList.map { unitData -> unitData.unitId!! },
            itemId = itemEntity.fusionItemId!!,
            year = timeRange.startYear!!,
            month = null,
            startMonth = timeRange.startMonth,
            endMonth = timeRange.endMonth,
            pbmWorkDataConfig = pbmWorkDataConfig
        )
        val memberChangeList = memberChangeDetail.groupBy { it.unitId }
        // 计算分值
        itemList.forEach {
            when {
                year > this.ecpTagConfig.year!! -> {
                    var tag = 0
                    memberChangeList[it.unitId]?.forEach { detail ->
                        if (detail.removeTagDone == 1) {
                            tag++
                        }
                        if (detail.addTagDone == 1) {
                            tag++
                        }
                    }
                    when {
                        tag >= 2 -> {
                            it.score = itemEntity.baseScore
                            it.result = "已完成"
                        }

                        else -> {
                            it.score = 0.0
                            it.result = "未完成"
                        }
                    }
                }

                else -> {
                    it.score = itemEntity.baseScore
                    it.result = "已完成"
                }
            }
        }
        return itemList
    }
}