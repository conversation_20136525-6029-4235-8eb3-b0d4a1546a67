package com.goodsogood.ows.service.pbm.fusion.impl

import com.goodsogood.ows.configuration.EcpTagConfig
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.PbmWorkDataConfig
import com.goodsogood.ows.mapper.ecp.EcpOrgMapper
import com.goodsogood.ows.mapper.user.OrganizationMapper
import com.goodsogood.ows.mapper.user.UserMapper
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity
import com.goodsogood.ows.model.mongodb.fusion.EcpOrgDetail
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData
import com.goodsogood.ows.service.pbm.CalculatingTimeUtils
import com.goodsogood.ows.service.pbm.fusion.IFusionDataService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.util.CollectionUtils
import java.util.*

/**
 * 云区组织建设
 *
 * <AUTHOR>
 * @Date 2023-03-14 10:17:49
 * @Description EcpOrgServiceImpl
 *
 */
@Service("ECP_ORG")
class EcpOrgServiceImpl(
    val userMapper: UserMapper,
    val organizationMapper: OrganizationMapper,
    val ecpOrgMapper: EcpOrgMapper,
    val ecpTagConfig: EcpTagConfig,
    val myMongoTemplate: MyMongoTemplate,
    val pbmWorkDataConfig: PbmWorkDataConfig
) : IFusionDataService {

    val log: Logger = LoggerFactory.getLogger(EcpOrgServiceImpl::class.java)

    @Value("\${user-option.virtualOrg}")
    private val virtualOrg: String? = null

    /**
     * 数据收集
     */
    override fun collect(
        unitId: Long,
        unitName: String,
        itemId: Long,
        year: Int,
        month: Int,
        regionId: Long
    ): MutableList<out FusionBaseData> {
        val updateTime = System.currentTimeMillis()
        val result = mutableListOf<EcpOrgDetail>()
        // 根据单位ID查询支部ids
        val excludeOrgs = this.virtualOrg?.split(",")?.map { it.toLong() }
        // 根据单位ID查询单位类别
        val option = this.organizationMapper.getCategoryById(unitId)
        val resultEcp = EcpOrgDetail()
        if (Objects.nonNull(option) && Objects.nonNull(option.opKey)) {
            resultEcp.unitCategoryCode = option.opKey.toInt()
            resultEcp.unitCategoryName = option.opValue
        }
        // 根据单位ID查询单位党员
        val unitUserList = this.getUserListByUnitId(unitId, regionId, excludeOrgs!!, organizationMapper, userMapper)
        val ecpTagList = mutableListOf<Long>()
        ecpTagConfig.monopolyId?.let(ecpTagList::add)
        ecpTagConfig.marketingId?.let(ecpTagList::add)
        ecpTagConfig.tobaccoId?.let(ecpTagList::add)
        ecpTagConfig.synthesisId?.let(ecpTagList::add)
        when {
            !CollectionUtils.isEmpty(unitUserList) -> {
                val userList: List<Long> = unitUserList.map { it.userId }
                val formList = this.ecpOrgMapper.ecpOrgDetail(year, ecpTagList, userList)
                formList.forEach {
                    if (it.tagId == this.ecpTagConfig.monopolyId) {
                        resultEcp.monopolyName = it.separateUnitName
                        resultEcp.monopolyNum = it.total ?: 0
                    }
                    if (it.tagId == this.ecpTagConfig.marketingId) {
                        resultEcp.marketingName = it.separateUnitName
                        resultEcp.marketingNum = it.total ?: 0
                    }
                    if (it.tagId == this.ecpTagConfig.tobaccoId) {
                        resultEcp.tobaccoName = it.separateUnitName
                        resultEcp.tobaccoNum = it.total ?: 0
                    }
                    if (it.tagId == this.ecpTagConfig.synthesisId) {
                        resultEcp.synthesisName = it.separateUnitName
                        resultEcp.synthesisNum = it.total ?: 0
                    }
                }
                result.add(resultEcp)
            }
        }
        result.forEach {
            it.unitId = unitId
            it.unitName = unitName
            it.itemId = itemId
            it.year = year
            it.month = month
            it.updateTime = updateTime
        }
        return result
    }

    /**
     * 计算分值
     */
    override fun calculate(
        itemList: MutableList<FusionItemData>,
        year: Int,
        month: Int,
        regionId: Long,
        itemEntity: PbmFusionItemEntity
    ): MutableList<FusionItemData> {
        val timeRange =
            CalculatingTimeUtils.getMeetingTimeRange(year = year, month = month, cycle = itemEntity.cycle!!)
        val ecpOrgDetail = this.getCollectData(
            EcpOrgDetail::class.java,
            mongoTemplate = myMongoTemplate,
            unitIds = itemList.map { unitData -> unitData.unitId!! },
            itemId = itemEntity.fusionItemId!!,
            year = timeRange.startYear!!,
            month = null,
            startMonth = timeRange.startMonth,
            endMonth = timeRange.endMonth,
            pbmWorkDataConfig = pbmWorkDataConfig
        )
        // 计算分值
        val ecpCodeList = mutableListOf<Int>()
        ecpTagConfig.institutionCode?.let(ecpCodeList::add)
        ecpTagConfig.companyCode?.let(ecpCodeList::add)
        ecpTagConfig.marketingCode?.let(ecpCodeList::add)
        val ecpOrgList = ecpOrgDetail.groupBy { it.unitId }
        itemList.forEach {
            ecpOrgList[it.unitId]?.forEach { ecpOrg ->
                if (ecpOrg.unitCategoryCode in ecpCodeList) {
                    // 机关/专业分公司/纯销区的单位，必须创建带有专卖、营销、综合标签的云区组织各1个以上；
                    when {
                        ecpOrg.monopolyNum <= 0 || ecpOrg.marketingNum <= 0 || ecpOrg.synthesisNum <= 0 -> {
                            it.score = 0.0
                            it.result = "未完成"
                        }

                        else -> {
                            it.score = itemEntity.baseScore
                            it.result = "已完成"
                        }
                    }
                } else if (ecpOrg.unitCategoryCode == ecpTagConfig.tobaccoCode) {
                    // 两烟产区的单位，必须创建带有专卖、营销、综合、烟叶标签的云上组织各1个以上
                    when {
                        ecpOrg.monopolyNum <= 0 || ecpOrg.marketingNum <= 0 || ecpOrg.synthesisNum <= 0 || ecpOrg.tobaccoNum <= 0 -> {
                            it.score = 0.0
                            it.result = "未完成"
                        }

                        else -> {
                            it.score = itemEntity.baseScore
                            it.result = "已完成"
                        }
                    }
                }
            }
        }
        return itemList
    }
}