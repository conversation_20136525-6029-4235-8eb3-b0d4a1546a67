package com.goodsogood.ows.service.pbm.fusion

import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.PbmWorkDataConfig
import com.goodsogood.ows.mapper.user.OrganizationMapper
import com.goodsogood.ows.mapper.user.UserMapper
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity
import com.goodsogood.ows.model.db.user.OrganizationEntity
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData
import com.goodsogood.ows.model.vo.activity.UserInfoBase
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.util.CollectionUtils

/**
 * 收集基础数据接口层
 * <AUTHOR>
 * @createTime 2023年03月10日 15:35:00
 */
interface IFusionDataService {

    /**
     * 收集党业融合基础数据
     * @param unitId    单位ID
     * @param unitName  单位名称
     * @param itemId    指标ID
     * @param year      年份
     * @param month     月份
     * @param regionId  区县ID
     * <AUTHOR>
     * @return 注意返回的所有值将直接展示到页面，所以部分数据需要翻译过后保存。例如：奖励名称、奖励级别等
     * 【注: 需要将入参回填到返回参数里】
     */
    fun collect(
        unitId: Long,
        unitName: String,
        itemId: Long,
        year: Int,
        month: Int,
        regionId: Long
    ): MutableList<out FusionBaseData>

    /**
     * 计算党业融合指标得分
     * @param itemList  单位指标列表 包含单位ID、单位名称、指标ID、年份、月份
     * @param year      年份 - 避免从itemList获取
     * @param month      月份 - 避免从itemList获取
     * @param regionId   区县ID
     * @param itemEntity 指标对象
     * <AUTHOR>
     * @return 返回实体类中
     * itemScore - 为该指标赋分（可能为平均值、考核得分、综合等，可为空）
     * score - 指标得分（根据规则计算得分，用于计算融合度，不能为空）
     * rank - 根据情况，需要需要排名，请填入排名
     */
    fun calculate(
        itemList: MutableList<FusionItemData>,
        year: Int,
        month: Int,
        regionId: Long,
        itemEntity: PbmFusionItemEntity
    ): MutableList<FusionItemData>

    /**
     * 获取基础数据 - 仅限调用collect收集数据调用
     * @param mongoTemplate mongoTemplate
     * @param unitIds       单位ID
     * @param itemId        指标ID
     * @param year          年份
     * @param month         月份 可为空
     * @param startMonth    时间范围-开始月份-包含 可为空
     * @param startMonth    时间范围-结束月份-包含 可为空
     * <AUTHOR>
     * @return 返回结果用子类接受
     */
    fun <T : FusionBaseData> getCollectData(
        clazz: Class<T>,
        mongoTemplate: MyMongoTemplate,
        unitIds: List<Long>,
        itemId: Long,
        year: Int,
        month: Int? = null,
        startMonth: Int? = null,
        endMonth: Int? = null,
        pbmWorkDataConfig: PbmWorkDataConfig
    ): MutableList<T> {
        val criteria = Criteria.where("unitId").`in`(unitIds).and("itemId").`is`(itemId).and("year").`is`(year)
        if (month != null) {
            criteria.and("month").`is`(month)
        }
        if (startMonth != null && endMonth != null) {
            criteria.and("month").gte(startMonth).lte(endMonth)
        }
        val query = Query(criteria)
        return mongoTemplate.find(query, clazz, pbmWorkDataConfig.fusionMongo)
    }

    /**
     * 根据单位ID查询组织列表
     * @param unitId 单位ID
     * @param regionId 区县ID
     * @param excludeOrg 要排除的组织ID
     * @param organizationMapper organizationMapper
     */
    fun getOrgListByUnitId(
        unitId: Long,
        regionId: Long,
        excludeOrg: List<Long>,
        organizationMapper: OrganizationMapper
    ): MutableList<Long> {
        return organizationMapper.findAllOrgByUnit(unitId, excludeOrg)
    }

    /**
     * 根据单位ID查询党员信息
     * @param unitId 单位ID
     * @param regionId 区县ID
     * @param excludeOrg 要排除的组织ID
     * @param organizationMapper organizationMapper
     * @param userMapper userMapper
     */
    fun getUserListByUnitId(
        unitId: Long,
        regionId: Long,
        excludeOrg: List<Long>,
        organizationMapper: OrganizationMapper,
        userMapper: UserMapper
    ): MutableList<UserInfoBase> {
        val orgList = this.getOrgListByUnitId(unitId, regionId, excludeOrg, organizationMapper)
        if (!CollectionUtils.isEmpty(orgList)) {
            // 根据支部ID查询单位党员
            return userMapper.unitUserList(orgList, excludeOrg, regionId)
        }
        return mutableListOf()
    }

    /**
     * 根据组织ID查询组织详情
     * @param orgId orgId
     * @param organizationMapper organizationMapper
     */
    fun getOrgById(orgId: Long, organizationMapper: OrganizationMapper): OrganizationEntity {
        return organizationMapper.selectByPrimaryKey(organizationMapper)
    }
}