package com.goodsogood.ows.service.pbm.impl

import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.mapper.user.OrganizationMapper
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import com.goodsogood.ows.model.mongodb.PartyBrandBase
import com.goodsogood.ows.model.mongodb.PartyPositions
import com.goodsogood.ows.model.mongodb.pbm.OrgWorkDetailInfo
import com.goodsogood.ows.service.pbm.IPbmOrgWorkDataService
import com.goodsogood.ows.service.pbm.PbmWorkDataService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * 单位组织品牌是否设置
 * 单位的任意下级组织设置了品牌就算该单位已设置
 * <AUTHOR>
 * @createTime 2022-06-24
 */
@Service("USER_BRAND")
class IPbmUserBrandServiceImpl(val mongoTemplate: MyMongoTemplate, val organizationMapper: OrganizationMapper) : IPbmOrgWorkDataService {
    val log: Logger = LoggerFactory.getLogger(PbmWorkDataService::class.java)
    override fun getOrgWorkData(unitId: Long, startTime: String?, endTime: String?, week: Int?, regionId: Long, workItem: WorkItemEntity): Any? {
        //查询单位下级组织
        val subAllOrgInfo: List<Long> = organizationMapper.getSubAllOrgId(unitId)
        //品牌查询
        val query = Query()
        query.addCriteria(Criteria.where("orgId").`in`(subAllOrgInfo))
        val reBrand: Long = mongoTemplate.count(query, PartyBrandBase::class.java)
        return reBrand>0L
    }

    override fun dealData(
        orgId: Long,
        rankValue: Long?,
        value: Any?,
        year: Int,
        month: Int,
        week: Int?,
        entity: WorkItemEntity
    ): OrgWorkDetailInfo {
        var v = "未上传"
        if (value is Boolean) {
            v = if (value) "已上传" else "未上传"
        }
        return OrgWorkDetailInfo(
            orgId = orgId,
            regionId = entity.regionId,
            workItemId = entity.workItemId,
            workItemName = entity.name,
            cycle = entity.cycle,
            criterion = entity.criterion,
            year = year,
            month = month,
            week = week,
            rankValue = rankValue,
            workResult = entity.description?.format(v),
            remark = entity.remark,
            type = entity.type,
            createTime = LocalDateTime.now()
        )
    }
}

/**
 * 单位组织阵地是否设置
 * <AUTHOR>
 * @createTime 2022-06-24
 */
@Service("USER_POSITION")
class IPbmUserPositionServiceImpl(val mongoTemplate: MyMongoTemplate, val organizationMapper: OrganizationMapper) : IPbmOrgWorkDataService {
    val log: Logger = LoggerFactory.getLogger(PbmWorkDataService::class.java)
    override fun getOrgWorkData(unitId: Long, startTime: String?, endTime: String?, week: Int?, regionId: Long, workItem: WorkItemEntity): Any? {
        //查询单位下级组织
        val subAllOrgInfo: List<Long> = organizationMapper.getSubAllOrgId(unitId)
        //阵地查询
        val query = Query()
        query.addCriteria(
            Criteria.where("regionId").`is`(regionId).and("orgId").`in`(subAllOrgInfo)
        )
        val reFront: Long = mongoTemplate.count(query, PartyPositions::class.java)
        return reFront>0L
    }

    override fun dealData(
        orgId: Long,
        rankValue: Long?,
        value: Any?,
        year: Int,
        month: Int,
        week: Int?,
        entity: WorkItemEntity
    ): OrgWorkDetailInfo {
        var v = "未上传"
        if (value is Boolean) {
            v = if (value) "已上传" else "未上传"
        }
        return OrgWorkDetailInfo(
            orgId = orgId,
            regionId = entity.regionId,
            workItemId = entity.workItemId,
            workItemName = entity.name,
            cycle = entity.cycle,
            criterion = entity.criterion,
            year = year,
            month = month,
            week = week,
            rankValue = rankValue,
            workResult = entity.description?.format(v),
            remark = entity.remark,
            type = entity.type,
            createTime = LocalDateTime.now()
        )
    }
}
