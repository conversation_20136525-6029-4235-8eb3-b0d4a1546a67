package com.goodsogood.ows.service.pbm.fusion.impl

import cn.hutool.core.bean.BeanUtil
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.PbmWorkDataConfig
import com.goodsogood.ows.mapper.sas.PbmTargetMapper
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData
import com.goodsogood.ows.service.pbm.fusion.FusionResultUtils
import com.goodsogood.ows.service.pbm.fusion.IFusionDataService
import com.goodsogood.ows.utils.ArithUtils
import org.springframework.stereotype.Service
import java.util.*

/**
 * 党群绩效对比
 */
@Service("PARTY_GROUP_COMPARE")
class PartyGroupCompareServiceImpl(
    val pbmWorkDataConfig: PbmWorkDataConfig,
    var pbmTargetMapper: PbmTargetMapper,
    val myMongoTemplate: MyMongoTemplate
) : IFusionDataService {
    /**
     * 收集党业融合基础数据
     * @param unitId    单位ID
     * @param unitName  单位名称
     * @param itemId    指标ID
     * @param year      年份
     * @param month     月份
     * @param regionId  区县ID
     * <AUTHOR>
     * @return 注意返回的所有值将直接展示到页面，所以部分数据需要翻译过后保存。例如：奖励名称、奖励级别等
     * 【注需要将入参回填到返回参数里】
     */
    override fun collect(
        unitId: Long,
        unitName: String,
        itemId: Long,
        year: Int,
        month: Int,
        regionId: Long
    ): MutableList<out FusionBaseData> {
        val updateTime = System.currentTimeMillis()
        val monthStr = if (month > 9) "" + month else "0$month"
        val queryTime = "$year$monthStr"
        //查询党建考核积分
        val result = pbmTargetMapper.getPartyGroupCompareInfo( queryTime, unitId )
        result.forEach {
            it.itemId = itemId
            it.unitId = unitId
            it.unitName = unitName
            it.year = year
            it.month = month
            it.updateTime = updateTime
        }
        return result
    }

    /**
     * 计算党业融合指标得分
     * @param itemList  单位指标列表 包含单位ID、单位名称、指标ID、年份、月份
     * @param year      年份 - 避免从itemList获取
     * @param month      月份 - 避免从itemList获取
     * @param regionId   区县ID
     * @param itemEntity 指标对象
     * <AUTHOR>
     * @return 返回实体类中
     * itemScore - 为该指标赋分（可能为平均值、考核得分、综合等，可为空）
     * score - 指标得分（根据规则计算得分，用于计算融合度，不能为空）
     * rank - 根据情况，需要需要排名，请填入排名
     */
    override fun calculate(
        itemList: MutableList<FusionItemData>,
        year: Int,
        month: Int,
        regionId: Long,
        itemEntity: PbmFusionItemEntity
    ): MutableList<FusionItemData> {
        val queryMonth = month - 1
        val monthStr = if (queryMonth > 9) "" + queryMonth else "0$queryMonth"
        val queryTime = "$year$monthStr"
        itemList.forEach { item ->
            val partyGroupCompareCalc = pbmTargetMapper.getPartyGroupCompareCalc(queryTime, item.unitId)
            if(!Objects.isNull(partyGroupCompareCalc)){
                if(Objects.isNull(partyGroupCompareCalc.notPartyMemberAvgScore)){
                    item.itemScore=0.0
                }else{
                    if(partyGroupCompareCalc.notPartyMemberAvgScore<=0){
                        item.itemScore=0.0
                    }else{
                        item.partyAvgScore = ArithUtils.round(partyGroupCompareCalc.partyMemberAvgScore,2)
                        item.noPartyAvgScore =ArithUtils.round( partyGroupCompareCalc.notPartyMemberAvgScore,2)
                        item.itemScore = ArithUtils.round((item.partyAvgScore!! / item.noPartyAvgScore!!), 2)
                    }
                }
            }else{
                item.itemScore=0.0
                item.partyAvgScore=0.0
                item.noPartyAvgScore =0.0
            }
        }
        //对过滤以后数据进行排名
        val filterList = itemList.filter { it.itemScore!! > 0 }.toList()
        filterList.forEach {
            // 计算排名和指标得分
            FusionResultUtils.rank(filterList, itemEntity.baseScore, pbmWorkDataConfig)
            // 结果
            it.result = String.format("排名：%s", it.rank)
        }
        //返回最后的拼装的数据
        itemList.forEach{item->
            val find = filterList.find { item.unitId?.equals(it.unitId)!! }
            if(!Objects.isNull(find)){
                BeanUtil.copyProperties(item,find,true)
            }
        }
        return itemList
    }

}