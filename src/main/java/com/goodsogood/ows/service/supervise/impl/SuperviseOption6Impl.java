package com.goodsogood.ows.service.supervise.impl;

import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.mapper.user.OrgGroupMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.service.sas.StatisticalOrgLifeService;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import com.goodsogood.ows.service.supervise.SuperviseService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 连续2个月未开展党小组会
 */
@Service("option6_impl")
@Log4j2
public class SuperviseOption6Impl implements SuperviseOptionCalculate {

    private final OrgTypeConfig orgTypeConfig;
    private final SuperviseConfig superviseConfig;
    private final StatisticalOrgLifeService statisticalOrgLifeService;
    private final MeetingMapper meetingMapper;
    private final OrgGroupMapper orgGroupMapper;
    private final SuperviseService superviseService;

    public SuperviseOption6Impl(OrgTypeConfig orgTypeConfig, SuperviseConfig superviseConfig,
                                StatisticalOrgLifeService statisticalOrgLifeService,
                                MeetingMapper meetingMapper, OrgGroupMapper orgGroupMapper,
                                SuperviseService superviseService) {
        this.orgTypeConfig = orgTypeConfig;
        this.superviseConfig = superviseConfig;
        this.statisticalOrgLifeService = statisticalOrgLifeService;
        this.meetingMapper = meetingMapper;
        this.orgGroupMapper = orgGroupMapper;
        this.superviseService = superviseService;
    }


    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
        //连接两个月
        try {
            List<String> months = DateUtils.getContinuousMonth(2,null);
            String startTime = months.get(0)+"-01 ";
            String endTime =DateUtils.getNextMonth( months.get(months.size()-1) )+"-01" ;
            Integer activityId = superviseConfig.getBranch().get("option6").getActivityId();
            List<Long> orgGroupEntities = orgGroupMapper.getPartyGroupInfo(organizationForm.getOrganizationId());
            if(CollectionUtils.isEmpty(orgGroupEntities)){
                superviseEntity.setOption6(0);
                return 0;
            }
            String orgIds = orgGroupEntities.stream().map(String::valueOf).collect(Collectors.joining(","));
            //它下面党小组有一个数量超过3项 就预警
            int meetTaskCount = meetingMapper.getMeetTaskCountBySpecial(orgIds, activityId, startTime, endTime).size();
            //如果没有任务就不用预警
            if(meetTaskCount<1){
                superviseEntity.setOption6(0);
                return 0;
            }
            int count =  meetingMapper.getMeetTaskCountByOrgIds(orgIds, activityId, startTime, endTime,1);
            if(count<1){
                Object[] mms = DateUtils.getBeforeMonth(2, "M").stream().
                        map(Integer::parseInt).sorted(Integer::compareTo).toArray();
                String option6 = String.format(superviseConfig.getDetail().get("option6").getContent(), mms);
                superviseService.addOrgExtendInfo( Collections.singletonList(organizationForm.getOrganizationId()),
                        "option6", organizationForm,option6);
            }
            superviseEntity.setOption6((count>0?0:1));
            return superviseEntity.getOption6();
        }catch (Exception exception){
            log.error("calOptionNumber6异常",exception);
            log.error("calOptionNumber6-organizationForm={}",organizationForm);
            return 0;
        }

    }

}
