package com.goodsogood.ows.service.supervise.impl;

import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.user.Org;
import com.goodsogood.ows.service.RegionService;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import com.goodsogood.ows.service.supervise.SuperviseService;
import com.goodsogood.ows.service.user.OrgMongoService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.MapUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 组织信息不完整 实现类
 */
@Service("option1_impl")
@Log4j2
public class SuperviseOption1Impl implements SuperviseOptionCalculate {

    private final OrgTypeConfig orgTypeConfig;
    private final OrganizationMapper organizationMapper;
    private final MyMongoTemplate mongoTemplate;
    private final SimpleApplicationConfigHelper applicationConfigHelper;
    private final StringRedisTemplate redisTemplate;
    private final OrgService orgService;
    private final SuperviseService superviseService;
    private final RegionService regionService;
    private final OrgMongoService orgMongoService;

    private static final String optionId = "option1";


    public SuperviseOption1Impl(OrgTypeConfig orgTypeConfig,
                                OrganizationMapper organizationMapper,
                                MyMongoTemplate mongoTemplate,
                                SimpleApplicationConfigHelper applicationConfigHelper,
                                StringRedisTemplate redisTemplate, OrgService orgService,
                                SuperviseService superviseService, RegionService regionService,
                                OrgMongoService orgMongoService) {
        this.orgTypeConfig = orgTypeConfig;
        this.organizationMapper = organizationMapper;
        this.mongoTemplate = mongoTemplate;
        this.applicationConfigHelper = applicationConfigHelper;
        this.redisTemplate = redisTemplate;
        this.orgService = orgService;
        this.superviseService = superviseService;
        this.regionService = regionService;
        this.orgMongoService = orgMongoService;
    }


    /**
     * 动态字段相关判断逻辑
     */
    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
        //党委党总支层级
        if (regionService.getRegionTopOrgID(organizationForm.getRegionId()).equals(organizationForm.getOrganizationId())||
            orgTypeConfig.checkIsCommunist(organizationForm.getOrgTypeChild())) {
            log.debug("SuperviseOption1Impl->党委:[{}] 执行逻辑:[{}]",
                    organizationForm.getOrganizationId(), Utils.toJson(organizationForm));
            List<OrganizationEntity> organizationEntities = orgService.selectAllChildOrgBYOrgAndType(organizationForm.getRegionId(),
                    organizationForm.getOrganizationId(),
                    orgTypeConfig.getBasicAndTopChild());
            if (!CollectionUtils.isEmpty(organizationEntities)) {
                log.debug("SuperviseOption1Impl->党委:[{}] 下级组织[{}]",
                        organizationForm.getOrganizationId(), organizationEntities.size());
                List<Map> checkField = getCheckField(organizationForm.getRegionId());
                log.debug("SuperviseOption1Impl->党委:[{}] 获取区县自定义检查字段[{}]",
                        organizationForm.getOrganizationId(), Utils.toJson(checkField));
                int errorNum = 0;
                Set<Long> errorIds = new HashSet<>();
                for (OrganizationEntity entity : organizationEntities) {
                    Org orgInfo = findOrgInMongoById(entity.getOrganizationId(), organizationForm.getRegionId());
                    log.debug("SuperviseOption1Impl->党委下级:[{}] 数据:[{}]", entity.getOrganizationId(), JsonUtils.toJson(orgInfo));
                    if (null == entity.getIsRetire() || null == orgInfo
                            || StringUtils.isBlank(orgInfo.getOrgAddress())) {
//                        log.debug("SuperviseOption1Impl->党委下级:[{}] IsRetire:[{}] OrgAddress:[{}]",
//                                entity.getOrganizationId(), entity.getIsRetire(), orgInfo.getOrgAddress());
                        errorIds.add(entity.getOrganizationId());
                        errorNum += 1;
                        continue;
                    }
                    for (Map x : checkField) {
                        String fieldName = MapUtils.getString(x, "fieldName", null);
                        String alias = MapUtils.getString(x, "alias", null);
                        final String value = MapUtils.getString(orgInfo.getFields(), fieldName, null);
                        if (MapUtils.isEmpty(orgInfo.getFields())
                                || StringUtils.isBlank(fieldName)
                                || StringUtils.isBlank(alias)
                                || StringUtils.isBlank(value)) {
                            log.debug("SuperviseOption1Impl->党委下级:[{}] fieldName:[{}] value:[{}]",
                                    entity.getOrganizationId(), fieldName, value);
                            errorIds.add(entity.getOrganizationId());
                            errorNum += 1;
                            break;
                        }
                    }
                }

                if (!CollectionUtils.isEmpty(errorIds)) {
                    log.debug("SuperviseOption1Impl->党委:[{}] 不完整组织数量[{}] errorNum:[{}]",
                            organizationForm.getOrganizationId(), errorIds.size(), errorNum);
                    for (Long id : errorIds) {
                        String s = orgMongoService.calOptionStr(id, organizationForm.getRegionId());
                        superviseService.addOrgExtendInfo(
                                Collections.singletonList(id),
                                optionId,
                                organizationForm,s);
                    }
                } else {
                    log.debug("SuperviseOption1Impl->党委:[{}] 下级组织组织全部完整", organizationForm.getOrganizationId());
                }
                superviseEntity.setOption1(errorNum);
                return errorNum;
            } else {
                log.debug("SuperviseOption1Impl->党委:[{}] 未查询到组织,结束!", organizationForm.getOrganizationId());
            }
        } else if //党组织/党小组类型
        (orgTypeConfig.checkIsBranch(organizationForm.getOrgTypeChild())) {
            log.debug("SuperviseOption1Impl->党支部:[{}] 执行逻辑:[{}]",
                    organizationForm.getOrganizationId(), Utils.toJson(organizationForm));
            Org orgInfo = findOrgInMongoById(organizationForm.getOrganizationId(), organizationForm.getRegionId());
            log.debug("SuperviseOption1Impl->党支部:[{}] mongoInfo:[{}]",
                    organizationForm.getOrganizationId(), Utils.toJson(orgInfo));
            if (null == organizationForm.getIsRetire() || null == orgInfo
                    || StringUtils.isBlank(orgInfo.getOrgAddress())) {
                String s = orgMongoService.calOptionStr(organizationForm.getOrganizationId(), organizationForm.getRegionId());
                superviseService.addOrgExtendInfo(
                        Collections.singletonList(organizationForm.getOrganizationId()),
                        optionId,
                        organizationForm,s);
                log.debug("SuperviseOption1Impl->党支部:[{}] 组织信息不完整！", organizationForm.getOrganizationId());
                superviseEntity.setOption1(1);
                return 1;
            }
            List<Map> checkField = getCheckField(organizationForm.getRegionId());
            log.debug("SuperviseOption1Impl->党支部:[{}] 获取区县自定义检查字段[{}]",
                    organizationForm.getOrganizationId(), Utils.toJson(checkField));
            for (Map x : checkField) {
                String fieldName = MapUtils.getString(x, "fieldName", null);
                String alias = MapUtils.getString(x, "alias", null);
                if (MapUtils.isEmpty(orgInfo.getFields())
                        || StringUtils.isBlank(fieldName)
                        || StringUtils.isBlank(alias)
                        || StringUtils.isBlank(MapUtils.getString(orgInfo.getFields(), fieldName, null))) {
                    String s = orgMongoService.calOptionStr(organizationForm.getOrganizationId(), organizationForm.getRegionId());
                    superviseService.addOrgExtendInfo(
                            Collections.singletonList(organizationForm.getOrganizationId()),
                            optionId,
                            organizationForm,s);
                    log.debug("SuperviseOption1Impl->党支部:[{}] 组织信息不完整", organizationForm.getOrganizationId());
                    superviseEntity.setOption1(1);
                    return 1;
                }
            }
            log.debug("SuperviseOption1Impl->党支部:[{}] 组织信息完整", organizationForm.getOrganizationId());
        }
        superviseEntity.setOption1(0);
        return 0;
    }

    private static final String SUPERVISE_CHECK_FIELDS = "SUPERVISE_CHECK_FIELDS_%s";
    private static final Long CACHE_TIME = 60L;
    private static final String CHECK_FIELDS = "'联系人','联系电话','党组织属地'";

    /**
     * 获取区县检查对应的自定义字段
     */
    private List<Map> getCheckField(Long regionId) {
        String cacheKey = String.format(SUPERVISE_CHECK_FIELDS, regionId);
        String value = redisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotBlank(value)) {
            return (List<Map>) Utils.fromJson(value, List.class, Map.class);
        }
        Region.OrgData orgData = applicationConfigHelper.getOrgByRegionId(regionId);
        Long orgId = orgData.getOrgId();
        List<Map> checkField = organizationMapper.getCheckField(regionId, orgId, CHECK_FIELDS);
        if (CollectionUtils.isEmpty(checkField)) {
            return new ArrayList<>();
        }
        redisTemplate.opsForValue().set(cacheKey, Utils.toJson(checkField), CACHE_TIME, TimeUnit.MINUTES);
        return checkField;
    }

    private Org findOrgInMongoById(Long orgId, Long regionId) {
        Query query = Query.query(Criteria
                .where("regionId").is(regionId)
                .and("organizationId").is(orgId));

        return mongoTemplate.findOne(query, Org.class);
    }

}
