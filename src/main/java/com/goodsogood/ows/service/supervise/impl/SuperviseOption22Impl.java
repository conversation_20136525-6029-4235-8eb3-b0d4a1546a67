package com.goodsogood.ows.service.supervise.impl;

import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.mapper.supervise.SuperviseMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.service.RegionService;
import com.goodsogood.ows.service.supervise.SuperviseExtendService;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import com.goodsogood.ows.service.supervise.SuperviseService;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 组织生活异常的党支部
 */
@Service("option22_impl")
@Log4j2
public class SuperviseOption22Impl implements SuperviseOptionCalculate {

    private final OrgTypeConfig orgTypeConfig;
    private final OrgService orgService;
    private final SuperviseService superviseService;
    private final SuperviseExtendService superviseExtendService;
    private final RegionService regionService;
    private final SuperviseMapper superviseMapper;
    private final SuperviseConfig superviseConfig;

    public SuperviseOption22Impl(OrgTypeConfig orgTypeConfig,
                                 OrgService orgService,
                                 SuperviseService superviseService,
                                 SuperviseExtendService superviseExtendService,
                                 RegionService regionService,
                                 SuperviseMapper superviseMapper,
                                 SuperviseConfig superviseConfig) {
        this.orgTypeConfig = orgTypeConfig;
        this.orgService = orgService;
        this.superviseService = superviseService;
        this.superviseExtendService = superviseExtendService;
        this.regionService = regionService;
        this.superviseMapper = superviseMapper;
        this.superviseConfig = superviseConfig;
    }


    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
        //如果是党委或者党总支
        if (regionService.getRegionTopOrgID(organizationForm.getRegionId()).equals(organizationForm.getOrganizationId())
                ||orgTypeConfig.checkIsCommunist(organizationForm.getOrgTypeChild())) {
            //得到这个党委下面所有支部id
            List<Long> listOrgIds = orgService.selectAllChildOrgBYOrgAndType(organizationForm.getRegionId(),
                    organizationForm.getOrganizationId(),orgTypeConfig.getBranchChild()).stream()
                    .map(OrganizationEntity::getOrganizationId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(listOrgIds)){
                superviseEntity.setOption22(0);
                return 0;
            }
            //统计组织生活异常记录
            //Integer count = superviseService.getRevisitMeetingCount(listOrgIds, "option22",organizationForm);
            //superviseEntity.setOption22(count);
            Set<Long> superviseEntities = superviseMapper.getRevisitMeetingCount(listOrgIds);
            superviseEntities.forEach(item->{
                String summaryInfo =String.join(",",superviseMapper.getOption22SummaryInfo(item));
                superviseExtendService.addExtendOrgInfoByOrgIds(Collections.singletonList(item),
                        superviseConfig.getCommittee().get("option22").getOptionKey(),
                        superviseConfig.getCommittee().get("option22").getOptionName(),
                        organizationForm.getRegionId(), organizationForm.getOrganizationId(),summaryInfo);
            });
            superviseEntity.setOption22(superviseEntities.size());
            return superviseEntity.getOption22();
        }
        return 0;
    }





}
