package com.goodsogood.ows.service.supervise.impl;

import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.service.sas.StatisticalUserOrgLifeService;
import com.goodsogood.ows.service.supervise.SuperviseExtendService;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 连续2个月未参加主题党日的党员
 */
@Service("option13_impl")
@Log4j2
public class SuperviseOption13Impl implements SuperviseOptionCalculate {

    private final SuperviseConfig superviseConfig;
    private final StatisticalUserOrgLifeService statisticalUserOrgLifeService;
    private final UserService userService;
    private final SuperviseExtendService superviseExtendService;
    private final MeetingMapper meetingMapper;

    public SuperviseOption13Impl(OrgTypeConfig orgTypeConfig,
                                 SuperviseConfig superviseConfig,
                                 StatisticalUserOrgLifeService statisticalUserOrgLifeService,
                                 UserService userService,
                                 SuperviseExtendService superviseExtendService,
                                 MeetingMapper meetingMapper) {
        this.superviseConfig = superviseConfig;
        this.statisticalUserOrgLifeService = statisticalUserOrgLifeService;
        this.userService = userService;
        this.superviseExtendService = superviseExtendService;
        this.meetingMapper = meetingMapper;
    }


    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
        //连续两个月
        List<String> continuousTwoMonth = DateUtils.getContinuousMonth(2,null);
        String startTime = continuousTwoMonth.get(0)+"-01 ";
        String endTime =DateUtils.getNextMonth( continuousTwoMonth.get(continuousTwoMonth.size()-1) )+"-01" ;
        Integer activityId = superviseConfig.getBranch().get("option13").getActivityId();
        int  meetTaskCount = meetingMapper.getMeetTaskCount(organizationForm.getOrganizationId(), activityId, startTime, endTime);
        //如果没有任务就不用预警
        if(meetTaskCount<3){
            superviseEntity.setOption13(0);
            return 0;
        }
        Set<Long> allUserInfo = userService.getSuperviseUserIds(continuousTwoMonth,organizationForm);
        if(CollectionUtils.isEmpty(allUserInfo)){
            superviseEntity.setOption13(0);
            return 0;
        }
        //查询当前组织连续两个月的用户id
        Set<Integer> year = new HashSet<>();
        Set<Integer> month = new HashSet<>();
        continuousTwoMonth.forEach(item->{
            String[] array = item.split("-");
            year.add(Integer.valueOf(array[0]));
            month.add(Integer.valueOf(array[1]));
        });
        //得到连续两个月的用户信息 上个月查询快照表
        Set<Long> activityUserInfo = statisticalUserOrgLifeService.getActivityUserInfo(activityId, year, month, allUserInfo);
        //取差集
        allUserInfo.removeAll(activityUserInfo);
        //设置选项的值
        superviseEntity.setOption13(allUserInfo.size());
        //写入t_supervise_extend扩展表信息
        Object[] mms = DateUtils.getBeforeMonth(2, "M").stream().
                map(Integer::parseInt).sorted(Integer::compareTo).toArray();
        superviseExtendService.addExtendUserInfo(allUserInfo,
                superviseConfig.getBranch().get("option13").getOptionKey(),
                superviseConfig.getBranch().get("option13").getOptionName(),
                organizationForm.getRegionId(),organizationForm.getOrganizationId(),mms);
        return allUserInfo.size();
    }





}
