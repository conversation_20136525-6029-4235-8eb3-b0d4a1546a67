package com.goodsogood.ows.service.supervise.impl;

import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.service.sas.StatisticalOrgLifeService;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import com.goodsogood.ows.service.supervise.SuperviseService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 连续2个季度未开展党课
 */
@Service("option9_impl")
@Log4j2
public class SuperviseOption9Impl implements SuperviseOptionCalculate {

    private final OrgTypeConfig orgTypeConfig;
    private final SuperviseConfig superviseConfig;
    private final StatisticalOrgLifeService statisticalOrgLifeService;
    private final MeetingMapper meetingMapper;
    private final SuperviseService superviseService;

    public SuperviseOption9Impl(OrgTypeConfig orgTypeConfig,
                                SuperviseConfig superviseConfig,
                                StatisticalOrgLifeService statisticalOrgLifeService,
                                MeetingMapper meetingMapper,
                                SuperviseService superviseService) {
        this.orgTypeConfig = orgTypeConfig;
        this.superviseConfig = superviseConfig;
        this.statisticalOrgLifeService = statisticalOrgLifeService;
        this.meetingMapper = meetingMapper;
        this.superviseService = superviseService;
    }


    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
        //连续两个季度
        int currentQuarterly = DateUtils.getCurrentQuarterly();
        List<String> months = DateUtils.getMonthsByQuarterly(currentQuarterly);
        Integer activityId = superviseConfig.getBranch().get("option9").getActivityId();
        String startTime = months.get(0)+"-01";
        String endTime= DateUtils.getNextMonth(months.get(months.size()-1))+"-01" ;
        int meetTaskCount = meetingMapper.getMeetTaskCount(organizationForm.getOrganizationId(), activityId, startTime, endTime);
        //如果没有任务就不用预警
        if(meetTaskCount<3){
            superviseEntity.setOption9(0);
            return 0;
        }
        Integer count = meetingMapper.getMeetTaskCountByOrgIds(""+organizationForm.getOrganizationId(), activityId,
                startTime,endTime,1);
        if(count<1){
            Object[] mms = Objects.requireNonNull(DateUtils.getMonthsByQuarterlyName(currentQuarterly)).toArray();
            String option9 = String.format(superviseConfig.getDetail().get("option9").getContent(), mms);
            superviseService.addOrgExtendInfo( Collections.singletonList(organizationForm.getOrganizationId()),
                    "option9", organizationForm,option9);
        }
        superviseEntity.setOption9((count>0?0:1));
        return superviseEntity.getOption9();
    }


}
