package com.goodsogood.ows.service.supervise.impl;

import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.service.activity.RevisitResultService;
import com.goodsogood.ows.service.sas.StatisticalUserOrgLifeService;
import com.goodsogood.ows.service.supervise.SuperviseExtendService;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 连续2个季度未完成重温入党志愿书的党员
 */
@Service("option19_impl")
@Log4j2
public class SuperviseOption19Impl implements SuperviseOptionCalculate {

    private final SuperviseConfig superviseConfig;
    private final RevisitResultService revisitResultService;
    private final StatisticalUserOrgLifeService statisticalUserOrgLifeService;
    private final UserService userService;
    private final SuperviseExtendService superviseExtendService;

    public SuperviseOption19Impl(SuperviseConfig superviseConfig,
                                 RevisitResultService revisitResultService,
                                 StatisticalUserOrgLifeService statisticalUserOrgLifeService,
                                 UserService userService,
                                 SuperviseExtendService superviseExtendService) {
        this.superviseConfig = superviseConfig;
        this.revisitResultService = revisitResultService;
        this.statisticalUserOrgLifeService = statisticalUserOrgLifeService;
        this.userService = userService;

        this.superviseExtendService = superviseExtendService;
    }


    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
        //连续两个季度
        Integer currentQuarterly = DateUtils.getCurrentQuarterly();
        List<String> continuousTwoQuarterly = DateUtils.getMonthsByQuarterly(currentQuarterly);
        //得到这几个月的所有党员
        Set<Long> allUserInfo = userService.getSuperviseUserIds(continuousTwoQuarterly,organizationForm);
        String startMonth = continuousTwoQuarterly.get(0)+"-01 00:00:00";
        String endMonth = DateUtils.getNextMonth(continuousTwoQuarterly.get(continuousTwoQuarterly.size()-1))+"-01 00:00:00" ;
        List<Integer> listType = new ArrayList<>();
        listType.add(2);
        listType.add(4);
        if(CollectionUtils.isEmpty(allUserInfo)) {
            superviseEntity.setOption19(0);
            return 0;
        }
        //得到参加用户信息
        Set<Long> activityUserInfo = revisitResultService.getAddUserInfo(startMonth, endMonth, allUserInfo, listType);
        //取差集
        allUserInfo.removeAll(activityUserInfo);
        //写入t_supervise_extend扩展表信息
        Object[] mms = Objects.requireNonNull(DateUtils.getMonthsByQuarterlyName(currentQuarterly)).toArray();
        superviseExtendService.addExtendUserInfo(allUserInfo,
                superviseConfig.getBranch().get("option19").getOptionKey(),
                superviseConfig.getBranch().get("option19").getOptionName(),
                organizationForm.getRegionId(),organizationForm.getOrganizationId(),mms);
        superviseEntity.setOption19(allUserInfo.size());
        return allUserInfo.size();
    }





}
