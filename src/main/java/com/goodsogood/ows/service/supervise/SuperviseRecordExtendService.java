package com.goodsogood.ows.service.supervise;

import com.goodsogood.ows.mapper.supervise.SuperviseRecordExtendMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseRecordExtendEntity;
import com.goodsogood.ows.model.vo.supervise.SendMsgVo;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-01-24 14:16
 **/
@Service
@Log4j2
public class SuperviseRecordExtendService {

    private final SuperviseRecordExtendMapper superviseRecordExtendMapper;

    @Autowired
    public SuperviseRecordExtendService(SuperviseRecordExtendMapper superviseRecordExtendMapper) {
        this.superviseRecordExtendMapper = superviseRecordExtendMapper;
    }

    /**
     * 得到当天已经发送数据
     * @param list
     * @return
     */
    public List<SuperviseRecordExtendEntity> getTodayMsgRecord( ){
        //按照时间批量记录
        String currentFormatTime = DateUtils.getCurrentFormatTime();
        Example example=new Example(SuperviseRecordExtendEntity.class);
        example.createCriteria().andEqualTo("recordNum", currentFormatTime);
        return superviseRecordExtendMapper.selectByExample(example);
    }
}