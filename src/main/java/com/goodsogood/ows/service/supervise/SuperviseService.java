package com.goodsogood.ows.service.supervise;

import com.github.pagehelper.PageHelper;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.supervise.SuperviseExtendMapper;
import com.goodsogood.ows.mapper.supervise.SuperviseHistoryMapper;
import com.goodsogood.ows.mapper.supervise.SuperviseMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.supervise.SuperviseExtendEntity;
import com.goodsogood.ows.model.db.supervise.SuperviseHistoryEntity;
import com.goodsogood.ows.model.db.supervise.SuperviseRecordExtendEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.supervise.*;
import com.goodsogood.ows.service.RegionService;
import com.goodsogood.ows.service.user.OrgPeriodService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.SpringContextUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.lang.reflect.Field;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021-10-13 17:24
 **/
@Service
@Log4j2
public class SuperviseService {

    private final SuperviseMapper superviseMapper;
    private final SuperviseExtendMapper superviseExtendMapper;
    private final SuperviseConfig superviseConfig;
    private final SuperviseExtendService superviseExtendService;
    private final OrgService orgService;
    private final OrgTypeConfig orgTypeConfig;
    private final OrgPeriodService orgPeriodService;
    private final UserMapper userMapper;
    private final SuperviseAsyncService superviseAsyncService;
    private final SuperviseRecordService superviseRecordService;
    private final SuperviseRecordExtendService superviseRecordExtendService;
    private final RegionService regionService;

    private final SuperviseHistoryMapper superviseHistoryMapper;


    @Autowired
    public SuperviseService(SuperviseMapper superviseMapper,
                            SuperviseExtendMapper superviseExtendMapper,
                            SuperviseConfig superviseConfig,
                            SuperviseExtendService superviseExtendService,
                            OrgService orgService,
                            OrgTypeConfig orgTypeConfig,
                            OrgPeriodService orgPeriodService, UserMapper userMapper,
                            SuperviseAsyncService superviseAsyncService,
                            SuperviseRecordService superviseRecordService,
                            SuperviseRecordExtendService superviseRecordExtendService,
                            RegionService regionService, SuperviseHistoryMapper superviseHistoryMapper) {
        this.superviseMapper = superviseMapper;
        this.superviseExtendMapper = superviseExtendMapper;
        this.superviseConfig = superviseConfig;
        this.superviseExtendService = superviseExtendService;
        this.orgService = orgService;
        this.orgTypeConfig = orgTypeConfig;
        this.orgPeriodService = orgPeriodService;
        this.userMapper = userMapper;
        this.superviseAsyncService = superviseAsyncService;
        this.superviseRecordService = superviseRecordService;
        this.superviseRecordExtendService = superviseRecordExtendService;
        this.regionService = regionService;
        this.superviseHistoryMapper = superviseHistoryMapper;
    }

    /**
     * 得到入党誓词以及入党志愿书 低于50% 支部
     *
     * @param listOrgIds
     * @param optionKey
     * @return
     */
    public List<SuperviseEntity> getRevisitCount(List<Long> listOrgIds, String optionKey) {
        Example example = new Example(SuperviseEntity.class);
        example.selectProperties("orgType", "orgTypeChild", "orgId", "orgName", "orgSecretary", "orgLevel");
        example.createCriteria().andIn("orgId", listOrgIds)
                .andGreaterThan(optionKey, 0);
        return superviseMapper.selectByExample(example);
    }

    /**
     * 根据组织id 得到组织信息
     *
     * @param listOrgIds
     * @return
     */
    public List<SuperviseEntity> getSuperviseOrgInfo(Collection<Long> listOrgIds) {
        Example example = new Example(SuperviseEntity.class);
        example.selectProperties("orgType", "orgTypeChild", "orgId", "orgName", "orgSecretary", "orgLevel");
        example.createCriteria().andIn("orgId", listOrgIds);
        return superviseMapper.selectByExample(example);
    }

    /**
     * 统计组织生活异常记录
     *
     * @param listOrgIds
     * @return
     */
    public Integer getRevisitMeetingCount(Collection<Long> listOrgIds, String optionKey, OrganizationEntity organizationForm) {
        Set<Long> superviseEntities = superviseMapper.getRevisitMeetingCount(listOrgIds);
        if (!CollectionUtils.isEmpty(superviseEntities)) {
            superviseExtendService.addExtendOrgInfo(getSuperviseOrgInfo(superviseEntities),
                    superviseConfig.getCommittee().get(optionKey).getOptionKey(),
                    superviseConfig.getCommittee().get(optionKey).getOptionName(),
                    organizationForm.getRegionId(), organizationForm.getOrganizationId());
        }
        return superviseEntities.size();
    }


    /**
     * 异步处理数据
     *
     * @param organizationForm
     */
    @Async("superviseExecutor")
    public void pullSupervise(OrganizationEntity organizationForm, Map<String, SuperviseConfig.Option> branch,
                              CountDownLatch latch, LogAspectHelper.SSLog ssLog) {
//        LogAspectHelper.logAspectHelperBuilder().reSetContext(ssLog);
        SuperviseEntity superviseEntity = new SuperviseEntity();
        superviseEntity.setOrgId(organizationForm.getOrganizationId());
        superviseEntity.setOrgName(organizationForm.getName());
        superviseEntity.setOrgLevel(organizationForm.getOrgLevel());
        superviseEntity.setRegionId(organizationForm.getRegionId());
        superviseEntity.setOrgType(organizationForm.getOrgType());
        superviseEntity.setOrgTypeChild(organizationForm.getOrgTypeChild());
        superviseEntity.setParentId(organizationForm.getParentId());
        Map<String, String> periodLeaderByOrg = orgPeriodService.findPeriodLeaderByOrg(organizationForm.getOrganizationId());
        if (null != periodLeaderByOrg) {
            superviseEntity.setOrgSecretary(periodLeaderByOrg.get("org_secretary"));
            superviseEntity.setOrgSecretaryId(periodLeaderByOrg.get("org_secretary_id"));
        }
        try {
            //配置支部实现类
            branch.values().forEach(item -> {
                if (!item.getIsCal()) {
                    return;
                }
                SuperviseOptionCalculate superviseOptionCalculate = (SuperviseOptionCalculate)
                        SpringContextUtil.getBean(item.getImplClassName());
                superviseOptionCalculate.calOptionNumber(organizationForm, superviseEntity);
                //latch.countDown();
            });
//            SuperviseOptionCalculate superviseOptionCalculate =(SuperviseOptionCalculate)
//            SpringContextUtil.getBean("option23_impl");
//            superviseOptionCalculate.calOptionNumber(organizationForm, superviseEntity);
            superviseEntity.setCreateTime(new Date());
            //通过反射 得到是不是要预警显示
            List<String> listOptionKey = branch.values().stream().filter(SuperviseConfig.Option::getIsShow)
                    .map(SuperviseConfig.Option::getOptionKey)
                    .collect(Collectors.toList());
            //设置是不是预警
            log.info("开始计算org=" + organizationForm.getOrganizationId() + "统计数据");
            setIsSupervise(listOptionKey, superviseEntity);
            superviseMapper.insert(superviseEntity);
        } catch (Exception ex) {
            log.error("反射异常,异常", ex);
        } finally {
            latch.countDown();
        }
    }


    /**
     * 根据反射看属性有没有大于o
     */
    private void setIsSupervise(List<String> listOptionKey, SuperviseEntity superviseEntity) {
        //根据查询列名展示出来信息
        for (String item : listOptionKey) {
            Object filedValue = getFieldValueByFieldName(item, superviseEntity);
            if (null != filedValue) {
                int result = Integer.parseInt(String.valueOf(filedValue));
                if (result > 0) {
                    superviseEntity.setIsSupervise(1);
                    break;
                }
            }
        }
    }

    /**
     * 添加组织异常记录
     *
     * @param listOrgIds
     * @return
     */
    public Integer addOrgExtendInfo(Collection<Long> listOrgIds, String optionKey, OrganizationEntity organizationForm) {
        if (CollectionUtils.isEmpty(listOrgIds)) {
            return 0;
        }
        superviseExtendService.addExtendOrgInfoByOrgIds(listOrgIds,
                superviseConfig.getCommittee().get(optionKey).getOptionKey(),
                superviseConfig.getCommittee().get(optionKey).getOptionName(),
                organizationForm.getRegionId(), organizationForm.getOrganizationId(), null);
        return listOrgIds.size();
    }


    /**
     * 添加组织异常记录 包括详情记录
     *
     * @param listOrgIds
     * @return
     */
    public Integer addOrgExtendInfo(Collection<Long> listOrgIds, String optionKey,
                                    OrganizationEntity organizationForm, String detailContent) {
        if (CollectionUtils.isEmpty(listOrgIds)) {
            return 0;
        }
        if (regionService.getRegionTopOrgID(organizationForm.getRegionId()).equals(organizationForm.getOrganizationId()) ||
                orgTypeConfig.checkIsCommunist(organizationForm.getOrgTypeChild())) {
            superviseExtendService.addExtendOrgInfoByOrgIds(listOrgIds,
                    superviseConfig.getCommittee().get(optionKey).getOptionKey(),
                    superviseConfig.getCommittee().get(optionKey).getOptionName(),
                    organizationForm.getRegionId(), organizationForm.getOrganizationId(), detailContent);
        } else {
            superviseExtendService.addExtendOrgInfoByOrgIds(listOrgIds,
                    superviseConfig.getBranch().get(optionKey).getOptionKey(),
                    superviseConfig.getBranch().get(optionKey).getOptionName(),
                    organizationForm.getRegionId(), organizationForm.getOrganizationId(), detailContent);
        }
        return listOrgIds.size();
    }

    /**
     * 监督预警列表首页
     *
     * @param regionId
     * @param orgId
     * @return
     */
    public SuperviseListForm list(Long regionId, Long orgId) {
        SuperviseListForm superviseListForm = new SuperviseListForm();
        OrganizationEntity organizationEntity = orgService.getById(orgId);
        SuperviseListForm.TabInfo currentTab = new SuperviseListForm.TabInfo();
        SuperviseListForm.TabInfo nextTab = new SuperviseListForm.TabInfo();
        currentTab.setTabName("按监督预警项统计");
        List<OptionForm> optionList = null;
        Region.RegionData regionData = regionService.regionData(regionId);
        log.info("监督预警列表首页得到org=" + orgId + ",orgTypeChild=" + organizationEntity.getOrgTypeChild());
        //如果是党委或者党总支
        //如果顶级组织id与传来orgId一样 就认为顶级党委放进去
        Long topOrgId = regionData.getOrgData().getOrgId();
        if (Objects.equals(topOrgId, orgId) || orgTypeConfig.checkIsCommunist(organizationEntity.getOrgTypeChild())) {
            Map<String, SuperviseConfig.Option> committee = superviseConfig.getCommittee();
            //设置key
            currentTab.setTabKey("committee");
            //设置next_tab
            nextTab.setTabName("按组织统计");
            nextTab.setTabKey("org_sta");
            //得到查询列名
            List<String> listOptionKey = committee.values().stream().filter(SuperviseConfig.Option::getIsShow)
                    .map(SuperviseConfig.Option::getOptionKey)
                    .collect(Collectors.toList());
            optionList = getOptionList(listOptionKey, committee, orgId);
            superviseListForm.setOptionList(optionList);
        }
        //党支部或者党小组
        if (orgTypeConfig.checkIsBranch(organizationEntity.getOrgTypeChild())) {
            //设置key
            currentTab.setTabKey("branch");
            //设置next_tab
            nextTab.setTabName("按党员统计");
            nextTab.setTabKey("user_sta");
            Map<String, SuperviseConfig.Option> branch = superviseConfig.getBranch();
            //得到查询列名
            List<String> listOptionKey = branch.values().stream().filter(SuperviseConfig.Option::getIsShow)
                    .map(SuperviseConfig.Option::getOptionKey)
                    .collect(Collectors.toList());
            optionList = getOptionList(listOptionKey, branch, orgId);
            superviseListForm.setOptionList(optionList);
        }
        superviseListForm.setCurrentTab(currentTab);
        superviseListForm.setNextTab(nextTab);
        return superviseListForm;
    }

    /**
     * 根据查询的属性获取列表
     *
     * @param listOptionKey
     * @return
     */
    private List<OptionForm> getOptionList(List<String> listOptionKey,
                                           Map<String, SuperviseConfig.Option> config,
                                           Long orgId) {
        Example example = new Example(SuperviseEntity.class);
        List<String> res = new ArrayList<>();
        res.addAll(listOptionKey);
        res.add("createTime");
        example.selectProperties(res.toArray(new String[0]));
        example.createCriteria().andEqualTo("orgId", orgId);
        SuperviseEntity superviseEntity = superviseMapper.selectOneByExample(example);
        List<OptionForm> list = new ArrayList<>();
        //根据查询列名展示出来信息
        listOptionKey.forEach(item -> {
            OptionForm optionForm = new OptionForm();
            Object filedValue = getFieldValueByFieldName(item, superviseEntity);
            if (null == filedValue) {
                return;
            }
            int satCount = Integer.parseInt(String.valueOf(filedValue));
            if (satCount == 0) {
                return;
            }
            optionForm.setOptionKey(item);
            optionForm.setQueryType(config.get(item).getQueryType());
            optionForm.setOptionName(config.get(item).getOptionName());
            optionForm.setCount(satCount);
            optionForm.setIsLink(config.get(item).getIsLink());
            optionForm.setCreateTime(superviseEntity.getCreateTime());
            list.add(optionForm);
        });
        return list;
    }


    /**
     * 根据属性名获取属性值
     *
     * @param fieldName
     * @param object
     * @return
     */
    public Object getFieldValueByFieldName(String fieldName, Object object) {
        try {
            Field field = object.getClass().getDeclaredField(fieldName);
            //设置对象的访问权限，保证对private的属性的访问
            field.setAccessible(true);
            return field.get(object);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 是不是顶级组织id
     *
     * @param orgId
     * @return
     */
    private boolean isTopOrgId(Long orgId) {
        //如果是党委或者党总支
        //如果顶级组织id与传来orgId一样 就认为顶级党委放进去
        Region.RegionData regionData = regionService.regionData(86L);
        Long topOrgId = regionData.getOrgData().getOrgId();
        return Objects.equals(topOrgId, orgId);
    }

    /**
     * 监督预警列表-用户统计
     *
     * @param orgId
     * @param optionKey
     * @return
     */
    public List<SuperviseUserForm> superviseBranchDetail(Long orgId, String optionKey, String[] col,
                                                         PageNumber pageNumber, HeaderHelper.SysHeader header) {
        Example example = new Example(SuperviseExtendEntity.class);
        example.selectProperties(col);
        example.createCriteria().andEqualTo("orgId", orgId).andEqualTo("optionKey", optionKey);
        return PageHelper.startPage(pageNumber.getPage(), pageNumber.getRows())
                .doSelectPage(() -> superviseExtendMapper.selectByExample(example));
    }

    /**
     * 监督预警列表-用户统计
     *
     * @param orgId
     * @param optionKey
     * @return
     */
    public List<SuperviseUserForm> superviseCommitteeDetail(Long orgId, String optionKey, String[] col,
                                                            PageNumber pageNumber, HeaderHelper.SysHeader header) {
        Example example = new Example(SuperviseExtendEntity.class);
        example.selectProperties(col);
        example.createCriteria().andEqualTo("optionKey", optionKey)
                .andEqualTo("orgId", orgId);
        return PageHelper.startPage(pageNumber.getPage(), pageNumber.getRows())
                .doSelectPage(() -> superviseExtendMapper.selectByExample(example));
    }


    /**
     * 监督党委-按组织统计
     *
     * @param orgId
     * @param pageNumber
     * @param sysHeader
     * @return
     */
    public List<SuperviseOrgForm> listByOrgSta(Long orgId, String orgName, PageNumber pageNumber, HeaderHelper.SysHeader sysHeader) {
//        List<String> listOptionKeyCommittee  = superviseConfig.getCommittee().values().stream()
//                .filter(SuperviseConfig.Option::getIsShow)
//                .filter(SuperviseConfig.Option::getIsSelf)
//                .map(SuperviseConfig.Option::getOptionCol)
//                .collect(Collectors.toList());
        List<String> listOptionKeyCommittee = new ArrayList<>();
        listOptionKeyCommittee.add("option_1");
        String partSqlCommittee = "sum(" + String.join("+", listOptionKeyCommittee) + ") as count";
        List<Integer> committeeType = orgTypeConfig.getCommittee();

        List<String> listOptionKeyBranch = superviseConfig.getBranch().values().stream()
                .filter(SuperviseConfig.Option::getIsShow)
                .filter(SuperviseConfig.Option::getIsSelf)
                .map(SuperviseConfig.Option::getOptionCol)
                .collect(Collectors.toList());
        List<Integer> branchType = orgTypeConfig.getBranch();
        String partSqlBranch = "sum(" + String.join("+", listOptionKeyBranch) + ") as count";
        return PageHelper.startPage(pageNumber.getPage(), pageNumber.getRows())
                .doSelectPage(() -> superviseMapper.listByOrgSta(orgId, orgName, partSqlCommittee, listOptionKeyCommittee,
                        partSqlBranch, listOptionKeyBranch, committeeType, branchType
                ));
    }

    /**
     * 获取列表信息-按组织统计-详情
     *
     * @param orgId
     * @return
     */
    public List<OptionForm> listByOrgStaDetail(Long orgId, Long subOrgId) {
        OrganizationEntity organizationEntity = orgService.getById(subOrgId);
        Map<String, SuperviseConfig.Option> config = null;
        //得到查询列名
        List<String> listOptionKey = new ArrayList<>();
        if (orgTypeConfig.checkIsCommunist(organizationEntity.getOrgTypeChild())) {
            config = superviseConfig.getCommittee();
            listOptionKey.add("option1");
        } else if (orgTypeConfig.checkIsBranch(organizationEntity.getOrgTypeChild())) {
            config = superviseConfig.getBranch();
            listOptionKey = config.values().stream()
                    .filter(SuperviseConfig.Option::getIsShow).filter(SuperviseConfig.Option::getIsSelf)
                    .map(SuperviseConfig.Option::getOptionKey)
                    .collect(Collectors.toList());
        } else {
            return null;
        }
        Example example = new Example(SuperviseEntity.class);
        example.selectProperties(listOptionKey.toArray(new String[listOptionKey.size()]));
        example.createCriteria().andEqualTo("orgId", subOrgId);
        SuperviseEntity superviseEntity = superviseMapper.selectOneByExample(example);
        List<OptionForm> list = new ArrayList<>();
        //根据查询列名展示出来信息
        Map<String, SuperviseConfig.Option> finalConfig = config;
        listOptionKey.forEach(item -> {
            Object filedValue = getFieldValueByFieldName(item, superviseEntity);
            if (null == filedValue) {
                return;
            }
            OptionForm optionForm = new OptionForm();
            optionForm.setOptionKey(item);
            optionForm.setOptionName(finalConfig.get(item).getOptionName());
            int result = Integer.parseInt(String.valueOf(filedValue));
            if (result <= 0) {
                return;
            }
            optionForm.setCount(result);
            Example exampleExtend = new Example(SuperviseExtendEntity.class);
            if ("option1".equals(item)) {
                exampleExtend.createCriteria().andEqualTo("orgId", subOrgId)
                        .andEqualTo("subOrgId", subOrgId)
                        .andEqualTo("optionKey", item);
            } else {
                exampleExtend.createCriteria().andEqualTo("orgId", subOrgId)
                        .andEqualTo("optionKey", item);
            }
            SuperviseExtendEntity superviseExtendEntity = superviseExtendMapper.selectOneByExample(exampleExtend);
            optionForm.setDetailContent(superviseExtendEntity.getDetailContent());
            list.add(optionForm);
        });
        return list;
    }

    /**
     * 监督党委-按党员统计
     *
     * @param orgId
     * @param pageNumber
     * @param sysHeader
     * @return
     */
    public List<SuperviseUserForm> listByUserSta(Long orgId, String userName, String mobile,
                                                 PageNumber pageNumber, HeaderHelper.SysHeader sysHeader) {
        return PageHelper.startPage(pageNumber.getPage(), pageNumber.getRows())
                .doSelectPage(() -> superviseExtendMapper.listByUserSta(orgId, userName, mobile));
    }

    /**
     * 监督党委-按党员统计-详情
     *
     * @param userId
     * @param pageNumber
     * @param sysHeader
     * @return
     */
    public List<OptionForm> listByUserStaDetail(Long userId, Long orgId, PageNumber pageNumber, HeaderHelper.SysHeader sysHeader) {
        return PageHelper.startPage(pageNumber.getPage(), pageNumber.getRows())
                .doSelectPage(() -> superviseExtendMapper.listByUserStaDetail(userId, orgId));
    }


    /**
     * 监督预警提示信息
     *
     * @param orgId
     * @return
     */
    public SuperviseTipsForm tips(Long orgId) {
        OrganizationEntity organizationEntity = orgService.getById(orgId);
        SuperviseTipsForm superviseTipsForm = new SuperviseTipsForm();
        superviseTipsForm.setSelf(getSelfOrgAbnormal(orgId, organizationEntity, superviseTipsForm));
        superviseTipsForm.setSub(getSubOrgAbnormal(orgId, organizationEntity));
        return superviseTipsForm;
    }

    /**
     * 得到本组织异常
     *
     * @param orgId
     * @return
     */
    private Integer getSelfOrgAbnormal(Long orgId, OrganizationEntity organizationEntity,
                                       SuperviseTipsForm superviseTipsForm) {
        //如果是党委或者党总支
        String partSql = "";
        List<String> listOptionKey = new ArrayList<>();
        if (isTopOrgId(orgId) || orgTypeConfig.checkIsCommunist(organizationEntity.getOrgTypeChild())) {
//            listOptionKey = superviseConfig.getCommittee().values().stream().filter(SuperviseConfig.Option::getIsShow)
//                    .map(SuperviseConfig.Option::getOptionCol)
//                    .collect(Collectors.toList());
            listOptionKey.add("option_1");
            partSql = "sum(" + String.join("+", listOptionKey) + ") as count";
            superviseTipsForm.setListTips(superviseConfig.getCommitteeTips());
        }
        //党支部或者党小组
        if (orgTypeConfig.checkIsBranch(organizationEntity.getOrgTypeChild())) {
            listOptionKey = superviseConfig.getBranch().values().stream().filter(SuperviseConfig.Option::getIsShow)
                    .map(SuperviseConfig.Option::getOptionCol)
                    .collect(Collectors.toList());
            partSql = "sum(" + String.join("+", listOptionKey) + ") as count";
            superviseTipsForm.setListTips(superviseConfig.getBranchTips());
        }
        return superviseMapper.superviseOrgSelfOrgAbnormalByListKey(orgId, listOptionKey, partSql);
    }

    /**
     * 得到下级组织异常
     *
     * @param orgId
     * @return
     */
    private Integer getSubOrgAbnormal(Long orgId, OrganizationEntity organizationEntity) {
        //如果是党委或者党总支
        if (isTopOrgId(orgId) || orgTypeConfig.checkIsCommunist(organizationEntity.getOrgTypeChild())) {

            List<String> listOptionKeyCommittee = new ArrayList<>();
            listOptionKeyCommittee.add("option_1");
            String partSqlCommittee = "sum(" + String.join("+", listOptionKeyCommittee) + ") as count";
            List<Integer> committeeType = orgTypeConfig.getCommittee();


            List<String> listOptionKeyBranch = superviseConfig.getBranch().values().stream()
                    .filter(SuperviseConfig.Option::getIsShow)
                    .filter(SuperviseConfig.Option::getIsSelf)
                    .map(SuperviseConfig.Option::getOptionCol)
                    .collect(Collectors.toList());
            List<Integer> branchType = orgTypeConfig.getBranch();


            String partSqlBranch = "sum(" + String.join("+", listOptionKeyBranch) + ") as count";
            return superviseMapper.listByOrgStaByIsSupervise(orgId, partSqlCommittee, listOptionKeyCommittee,
                    partSqlBranch, listOptionKeyBranch, committeeType, branchType).size();
        }

        //党支部或者党小组
        if (orgTypeConfig.checkIsBranch(organizationEntity.getOrgTypeChild())) {
            List<String> listOptionKey = superviseConfig.getBranch().values().stream()
                    .filter(SuperviseConfig.Option::getIsShow)
                    .map(SuperviseConfig.Option::getOptionKey)
                    .collect(Collectors.toList());
            Example example = new Example(SuperviseExtendEntity.class);
            example.selectProperties("userId");
            example.createCriteria().andIn("optionKey", listOptionKey)
                    .andEqualTo("orgId", orgId)
                    .andIsNotNull("userId");
            return new HashSet<>(superviseExtendMapper.selectByExample(example)).size();
        }
        return null;
    }


    /**
     * 删除之前拉的数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void delSuperviseData() {
        Example superviseEntity = new Example(SuperviseEntity.class);
        superviseMapper.deleteByExample(superviseEntity);

        Example superviseExtend = new Example(SuperviseExtendEntity.class);
        superviseExtendMapper.deleteByExample(superviseExtend);
    }

    /**
     * 发送消息接口
     *
     * @param sysHeader
     */
    @Async("superviseManageExecutor")
    public void sendMsgSummary(HeaderHelper.SysHeader sysHeader, List<Long> orgIds, Long recordId,
                               List<OneClickForm.OptionInfo> optionKeys, List<Long> userIds, List<Long> chooseOrgIds, Integer type) {
        Map<String, SuperviseConfig.SendMsg> sendMsgMap = superviseConfig.getSendMsg();
        List<SuperviseConfig.SendMsg> sendMsg = new ArrayList<>();
        sendMsgMap.forEach((k, v) -> {
            sendMsg.add(v);
        });
        //拼装消息发送内容 并且过滤今天已经送的信息不再发送
        List<SendMsgVo> listSendMsg = buildSendMsg(sysHeader, orgIds, optionKeys, userIds, chooseOrgIds, type);
        if (CollectionUtils.isEmpty(listSendMsg)) {
            log.info("没有得到拼装信息。。。");
            return;
        }
        //设置用户信息
        listSendMsg.forEach(item -> {
            Set<SendMsgVo.UserInfo> userInfos = userMapper.getUserNameByUserIds(new ArrayList<>(item.getSendUserIds()));
            item.setUserInfos(userInfos);
        });
        //过滤发送纪录
        List<SuperviseRecordExtendEntity> todayMsgRecord = superviseRecordExtendService.getTodayMsgRecord();
        List<SendMsgVo> filterList = listSendMsg.stream().filter(item -> filerMsg(todayMsgRecord,
                (Long) item.getSendUserIds().toArray()[0], item.getOrgId(), item.getOptionKey())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            return;
        }
        //对数据进行重新操作
        List<SendMsgVo> variableMsg = filterList.stream().filter(SendMsgVo::isVariableMsg).collect(Collectors.toList());
        //根据组织进行分类
        Map<Long, List<SendMsgVo>> collect = variableMsg.stream().collect(Collectors.groupingBy(SendMsgVo::getTemplateId));
        Long singleOrgId = orgIds.get(0);
        //最后拼装发送的数据
        List<SendMsgVo> listSendLastMsg = new ArrayList<>();
        collect.forEach((k, v) -> {
            SuperviseConfig.SendMsg sendMsgVo = sendMsg.stream().filter(item -> item.getTemplateId().equals(k)).findFirst().get();
            //针对用户发送
            if (sendMsgVo.getSendObject() == 2) {
                Set<Long> sendUserIds = v.stream().map(SendMsgVo::getSendUserIds).
                        collect(HashSet::new, HashSet::addAll, HashSet::addAll);
                Set<SendMsgVo.UserInfo> userInfos = userMapper.getUserNameByUserIds(new ArrayList<>(sendUserIds));
                //写入组织id
                userInfos.forEach(item -> {
                    SuperviseExtendEntity superviseExtendEntity = superviseExtendMapper
                            .getExtendInfo(v.get(0).getOptionKey(), singleOrgId, item.getUserId());
                    if (null == superviseExtendEntity) {
                        return;
                    }
                    item.setOrgId(superviseExtendEntity.getSubOrgId() == null ? superviseExtendEntity.getOrgId()
                            : superviseExtendEntity.getSubOrgId());
                });
                SendMsgVo sendMsgNew = new SendMsgVo();
                sendMsgNew.setTemplateId(k);
                sendMsgNew.setSendObject(sendMsgVo.getSendObject());
                sendMsgNew.setVariableMsg(true);
                sendMsgNew.setUserInfos(userInfos);
                sendMsgNew.setSendUserIds(sendUserIds);
                sendMsgNew.setOptionKey(v.get(0).getOptionKey());
                listSendLastMsg.add(sendMsgNew);
            } else {
                listSendLastMsg.addAll(v);
            }
        });
        //普通消息
        List<SendMsgVo> sameMsg = filterList.stream().filter(item -> !item.isVariableMsg()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sameMsg)) {
            Map<String, List<SendMsgVo>> collect1 = sameMsg.stream().collect(Collectors.groupingBy(SendMsgVo::getContent));
            collect1.forEach((k, v) -> {
                SendMsgVo sendMsgVo = new SendMsgVo();
                sendMsgVo.setTemplateId(157L);
                sendMsgVo.setContent(k);
                sendMsgVo.setSendObject(2);
                sendMsgVo.setVariableMsg(false);
                sendMsgVo.setOptionKey(v.get(0).getOptionKey());
                Set<Long> sendUserIds = v.stream().map(SendMsgVo::getSendUserIds).
                        collect(HashSet::new, HashSet::addAll, HashSet::addAll);
                //过滤用户
                Set<SendMsgVo.UserInfo> userInfos = userMapper.getUserNameByUserIds(new ArrayList<>(sendUserIds));
                sendMsgVo.setSendUserIds(sendUserIds);
                sendMsgVo.setUserInfos(userInfos);
                listSendLastMsg.add(sendMsgVo);
            });
        }
        //添加一键督办选项记录
        superviseAsyncService.addSuperviseSendRecord(sysHeader, listSendLastMsg);
        //查询已经发送过的记录
        superviseRecordService.updateRecord(listSendLastMsg, recordId);
        //发送消息
        superviseAsyncService.sendSuperviseMsg(sysHeader, listSendLastMsg);
    }

    /**
     * 过滤消息
     *
     * @return
     */
    private boolean filerMsg(List<SuperviseRecordExtendEntity> todayMsgRecord, Long userId, Long orgId, String optionKey) {
        //普通消息每人发一条
        if (optionKey.equals("option1") || optionKey.equals("option2")) {
            return todayMsgRecord.stream().noneMatch(item -> (item.getUserId().equals(userId)
                    && item.getOptionKey().equals(optionKey)));
        } else {
            return todayMsgRecord.stream().noneMatch(item -> (item.getUserId().equals(userId)
                    && item.getOrgId().equals(orgId) && item.getOptionKey().equals(optionKey)));
        }
    }

    /**
     * 发送消息接口
     *
     * @param orgIds
     */
    public List<SendMsgVo> buildSendMsg(HeaderHelper.SysHeader sysHeader,
                                        List<Long> orgIds, List<OneClickForm.OptionInfo> optionKeys, List<Long> userIds,
                                        List<Long> chooseOrgIds, Integer type) {
        Map<String, SuperviseConfig.SendMsg> sendMsgMap = superviseConfig.getSendMsg();
        List<SuperviseConfig.SendMsg> sendMsgConfig = new ArrayList<>();
        sendMsgMap.forEach((k, v) -> {
            sendMsgConfig.add(v);
        });
        //查询所有
        Long singleOrgId = orgIds.get(0);
        Example example = new Example(SuperviseExtendEntity.class);
        example.createCriteria().andEqualTo("orgId", singleOrgId);
        //今天已经发送的消息
        List<SuperviseRecordExtendEntity> todayMsgRecord = superviseRecordExtendService.getTodayMsgRecord();
        //选项发送
        if (type == 1) {
            return dealOptionKeys(chooseOrgIds, optionKeys, sendMsgConfig, singleOrgId, todayMsgRecord);
            //2、按组织
        } else if (type == 2) {
            return dealOrg(sysHeader.getOid(), chooseOrgIds, optionKeys, sendMsgConfig, false);
            //3.按用户
        } else if (type == 3) {
            example.and().andIn("userId", userIds);
            //查询所有信息
            List<SuperviseExtendEntity> superviseEntities = superviseExtendMapper.selectByExample(example);
            return dealUser(superviseEntities, sendMsgConfig, todayMsgRecord, singleOrgId);
            //4.选项详情面
        } else if (type == 4) {
            return dealOptionKeyDetail(chooseOrgIds, optionKeys, sendMsgConfig, singleOrgId, todayMsgRecord);
            //5、组织详情
        } else if (type == 5) {
            //组织详情就是单个组织
            return dealOrg(sysHeader.getOid(), chooseOrgIds, optionKeys, sendMsgConfig, true);
            //6.用户详情
        } else if (type == 6) {
            List<String> listOptionKeys = optionKeys.stream().map(OneClickForm.OptionInfo::getOptionKey).collect(Collectors.toList());
            example.and().andIn("userId", userIds).andIn("optionKey", listOptionKeys);
            //查询所有信息
            List<SuperviseExtendEntity> superviseEntities = superviseExtendMapper.selectByExample(example);
            return dealUser(superviseEntities, sendMsgConfig, todayMsgRecord, singleOrgId);
        }
        return null;
    }

    public void saveSuperviseHistory(){
        //获取所有组织
        List<OrganizationEntity> allOrg = orgService.findAllNextChildOrg(superviseConfig.getTopOrgId(),86L,false,true,false);
        LocalDate localDate = LocalDate.now();
        LocalDate previousDate = localDate.minusDays(1);
        Date before1DayDate = Date.from(previousDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        List<SuperviseHistoryEntity> superviseHistoryEntities = new ArrayList<>();
        for(OrganizationEntity organizationEntity : allOrg){
            SuperviseHistoryEntity superviseHistoryEntity = new SuperviseHistoryEntity();
            superviseHistoryEntity.setDate(before1DayDate);
            superviseHistoryEntity.setSelf(getSelfOrgAbnormal(organizationEntity.getOrganizationId(), organizationEntity, new SuperviseTipsForm()));
            superviseHistoryEntity.setSub(getSubOrgAbnormal(organizationEntity.getOrganizationId(), organizationEntity));
            superviseHistoryEntity.setOrgId(organizationEntity.getOrganizationId());
            superviseHistoryEntities.add(superviseHistoryEntity);
        }
        superviseHistoryMapper.insertList(superviseHistoryEntities);
    }

    /**
     * 处理选项
     *
     * @return
     */
    private List<SendMsgVo> dealOptionKeys(List<Long> chooseOrgIds, List<OneClickForm.OptionInfo> optionKeys,
                                           List<SuperviseConfig.SendMsg> sendMsgConfig, Long singleOrgId,
                                           List<SuperviseRecordExtendEntity> todayMsgRecord) {
        Example examSuperviseEntity = new Example(SuperviseEntity.class);
        examSuperviseEntity.createCriteria().andEqualTo("orgId", singleOrgId);
        SuperviseEntity superviseEntity = superviseMapper.selectOneByExample(examSuperviseEntity);
        List<SendMsgVo> list = new ArrayList<>();
        optionKeys.forEach(item -> {
            Optional<SuperviseConfig.SendMsg> sendMsgOptional = sendMsgConfig.stream().
                    filter(it1 -> it1.getOptionKey().equals(item.getOptionKey())).findFirst();
            if (!sendMsgOptional.isPresent()) {
                return;
            }
            SuperviseConfig.SendMsg sendMsg = sendMsgOptional.get();
            //如果是发送组织
            if (item.getCount() == 1 && "org".equals(item.getQueryType())) {
                SendMsgVo sendMsgVo = new SendMsgVo();
                sendMsgVo.setSendObject(sendMsg.getSendObject());
                sendMsgVo.setTemplateId(sendMsg.getTemplateId());
                sendMsgVo.setContent(sendMsg.getMsgContent());
                sendMsgVo.setVariableMsg(sendMsg.getIsVariableMsg());
                sendMsgVo.setOrgName(superviseEntity.getOrgName());
                sendMsgVo.setOptionKey(item.getOptionKey());
                sendMsgVo.setOrgId(singleOrgId);
                sendMsgVo.setSendUserIds(getAdministratorInfo(singleOrgId));
                list.add(sendMsgVo);
            } else {
                Example example = new Example(SuperviseExtendEntity.class);
                example.createCriteria().andEqualTo("orgId", singleOrgId)
                        .andEqualTo("optionKey", item.getOptionKey());
                List<SuperviseExtendEntity> superviseEntities = superviseExtendMapper.selectByExample(example);
                List<SendMsgVo> sendMsgVos = dealUser(superviseEntities, sendMsgConfig, todayMsgRecord, singleOrgId);
                list.addAll(sendMsgVos);
            }
        });
        return list;
    }

    /**
     * 处理选项详情
     * 选项详情是能点开 说明数量必须大于1
     *
     * @return
     */
    private List<SendMsgVo> dealOptionKeyDetail(List<Long> chooseOrgIds, List<OneClickForm.OptionInfo> optionKeys,
                                                List<SuperviseConfig.SendMsg> sendMsgConfig, Long singleOrgId,
                                                List<SuperviseRecordExtendEntity> todayMsgRecord) {
        Example example = new Example(SuperviseExtendEntity.class);
        example.createCriteria().andEqualTo("optionKey", optionKeys.get(0).getOptionKey())
                .andEqualTo("orgId", singleOrgId)
                .andIn("subOrgId", chooseOrgIds);
        //查询所有信息
        List<SuperviseExtendEntity> superviseEntities = superviseExtendMapper.selectByExample(example);
        return dealUser(superviseEntities, sendMsgConfig, todayMsgRecord, singleOrgId);
    }

    /**
     * 处理组织
     *
     * @return
     */
    private List<SendMsgVo> dealOrg(Long orgId, List<Long> chooseOrgIds, List<OneClickForm.OptionInfo> optionKeys,
                                    List<SuperviseConfig.SendMsg> sendMsgConfig, boolean isDetail) {
        List<SendMsgVo> list = new ArrayList<>();
        //获取列表信息-按组织统计-详情
        chooseOrgIds.forEach(item -> {
            Set<Long> administratorInfo = getAdministratorInfo(item);
            if (CollectionUtils.isEmpty(administratorInfo)) {
                return;
            }
            List<OptionForm> superviseList = listByOrgStaDetail(orgId, item);
            List<OptionForm> superviseNewList = null;
            //单个组织可能要过滤消息
            if (isDetail) {
                List<String> collect = optionKeys.stream().map(OneClickForm.OptionInfo::getOptionKey).collect(Collectors.toList());
                superviseNewList = superviseList.stream().filter(it -> collect.contains(it.getOptionKey()))
                        .collect(Collectors.toList());
            } else {
                superviseNewList = superviseList;
            }
            superviseNewList.forEach(it -> {
                Optional<SuperviseConfig.SendMsg> sendMsgOptional = sendMsgConfig.stream().
                        filter(it1 -> it1.getOptionKey().equals(it.getOptionKey())).findFirst();
                if (!sendMsgOptional.isPresent()) {
                    return;
                }
                SuperviseConfig.SendMsg sendMsg = sendMsgOptional.get();
                SendMsgVo sendMsgVo = new SendMsgVo();
                sendMsgVo.setSendObject(sendMsg.getSendObject());
                sendMsgVo.setTemplateId(sendMsg.getTemplateId());
                sendMsgVo.setContent(sendMsg.getMsgContent());
                sendMsgVo.setVariableMsg(sendMsg.getIsVariableMsg());
                sendMsgVo.setOptionKey(sendMsg.getOptionKey());
                sendMsgVo.setOrgId(item);
                sendMsgVo.setOrgName(orgService.getById(item).getName());
                sendMsgVo.setSendUserIds(administratorInfo);
                list.add(sendMsgVo);
            });
        });
        return list;
    }

    /**
     * 得到组织管理员信息
     *
     * @param orgId
     * @return
     */
    private Set<Long> getAdministratorInfo(Long orgId) {
        Example examSuperviseEntity = new Example(SuperviseEntity.class);
        examSuperviseEntity.createCriteria().andEqualTo("orgId", orgId);
        SuperviseEntity superviseEntity = superviseMapper.selectOneByExample(examSuperviseEntity);
        //得到书记 id
        String orgSecretary = superviseEntity.getOrgSecretaryId();
        Set<Long> result = new HashSet<>();
        if (!StringUtils.isEmpty(orgSecretary)) {
            result.addAll(Arrays.stream(orgSecretary.split(",")).map(Long::valueOf).collect(Collectors.toSet()));
        }
        //查询组织管理员
        Set<Long> administrator = userMapper.getAdministratorByOrgId(superviseEntity.getOrgId());
        result.addAll(administrator);
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result;
    }


    /**
     * 处理用户
     *
     * @param superviseEntities
     * @param sendMsgConfig
     * @param todayMsgRecord
     * @param singleOrgId
     * @return
     */
    private List<SendMsgVo> dealUser(List<SuperviseExtendEntity> superviseEntities,
                                     List<SuperviseConfig.SendMsg> sendMsgConfig,
                                     List<SuperviseRecordExtendEntity> todayMsgRecord,
                                     Long singleOrgId) {
        //根据组织进行分类
        List<SendMsgVo> list = new ArrayList<>();
        superviseEntities.forEach(item -> {
            Optional<SuperviseConfig.SendMsg> sendMsgOptional = sendMsgConfig.stream().
                    filter(it -> it.getOptionKey().equals(item.getOptionKey())).findFirst();
            if (!sendMsgOptional.isPresent()) {
                return;
            }
            SuperviseConfig.SendMsg sendMsg = sendMsgOptional.get();
            SendMsgVo sendMsgVo = new SendMsgVo();
            sendMsgVo.setSendObject(sendMsg.getSendObject());
            sendMsgVo.setTemplateId(sendMsg.getTemplateId());
            sendMsgVo.setContent(sendMsg.getMsgContent());
            sendMsgVo.setVariableMsg(sendMsg.getIsVariableMsg());
            sendMsgVo.setOrgName(item.getOrgName());
            sendMsgVo.setOrgId(item.getOrgId());
            sendMsgVo.setOptionKey(item.getOptionKey());
            if (sendMsg.getSendObject() == 1) {
                //得到书记 id
                Set<Long> result = new HashSet<>();
                String orgSecretary = item.getOrgSecretaryId();
                if (!StringUtils.isEmpty(orgSecretary)) {
                    result.addAll(Arrays.stream(orgSecretary.split(",")).map(Long::valueOf).collect(Collectors.toSet()));
                }
                //查询组织管理员
                //Set<Long> administrator = userMapper.getAdministratorByOrgId(item.getOrgId());
                //result.addAll(administrator);
                Set<Long> administrator1 = userMapper.getAdministratorByOrgId(item.getSubOrgId());
                result.addAll(administrator1);
                if (CollectionUtils.isEmpty(result)) {
                    return;
                }
                //设置用户信息
                sendMsgVo.setSendUserIds(result);
            } else {
                sendMsgVo.setSendUserIds(Collections.singletonList(item.getUserId()));
            }
            //发送那个组织id
            sendMsgVo.setOrgId(item.getSubOrgId() == null ? item.getOrgId() : item.getSubOrgId());
            list.add(sendMsgVo);
        });
        return new ArrayList<>(new HashSet<>(list));
    }

    /**
     * 监督预警查看具体的细项数据
     * 如果登录的组织是党委或者党总支 就查看他下面的组织
     * 如果登录的党支部或者党小组 就查看下面的人员信息
     *
     * @param sysHeader
     * @param optionKey
     * @param detailOrgId
     * @param userId
     * @return
     */
    public ResponseEntity<Result<?>> clickDetail(HeaderHelper.SysHeader sysHeader, String optionKey,
                                                 Long detailOrgId, Long userId) {
        Long orgId = sysHeader.getOid();
        OrganizationEntity organizationEntity = orgService.getById(orgId);
        if (isTopOrgId(orgId) || orgTypeConfig.checkIsCommunist(organizationEntity.getOrgTypeChild())) {
            return null;
        }
        //党支部或者党小组
        if (orgTypeConfig.checkIsBranch(organizationEntity.getOrgTypeChild())) {
            return null;
        }
        return null;
    }
}