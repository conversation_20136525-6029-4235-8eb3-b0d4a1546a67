package com.goodsogood.ows.service.supervise.impl;

import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.service.sas.StatisticalOrgLifeService;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import com.goodsogood.ows.service.supervise.SuperviseService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 连续2个月未开展支部委员会
 */
@Service("option5_impl")
@Log4j2
public class SuperviseOption5Impl implements SuperviseOptionCalculate {

    private final OrgTypeConfig orgTypeConfig;
    private final StatisticalOrgLifeService statisticalOrgLifeService;
    private final SuperviseConfig superviseConfig;
    private final MeetingMapper meetingMapper;
    private final SuperviseService superviseService;

    public SuperviseOption5Impl(OrgTypeConfig orgTypeConfig,
                                StatisticalOrgLifeService statisticalOrgLifeService,
                                SuperviseConfig superviseConfig,
                                MeetingMapper meetingMapper,
                                SuperviseService superviseService) {
        this.orgTypeConfig = orgTypeConfig;
        this.statisticalOrgLifeService = statisticalOrgLifeService;
        this.superviseConfig = superviseConfig;
        this.meetingMapper = meetingMapper;
        this.superviseService = superviseService;
    }


    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
        //连接3个月 包括当月
        try {
            List<String> months = DateUtils.getContinuousMonth(2, null);
            String startTime = months.get(0) + "-01 ";
            String endTime = DateUtils.getNextMonth(months.get(months.size() - 1)) + "-01";
            Integer activityId = superviseConfig.getBranch().get("option5").getActivityId();
            //如果本月有任务就要预警
            int meetTaskCount = meetingMapper.getMeetTaskCount(organizationForm.getOrganizationId(), activityId, startTime, endTime);
            //如果没有任务就不用预警
            if (meetTaskCount < 3) {
                superviseEntity.setOption5(0);
                return 0;
            }
            //没有数据 就预警(包括本月都没有记录就预警前面两个月纪录）
            Integer count = meetingMapper.getMeetTaskCountByOrgIds("" + organizationForm.getOrganizationId(),
                    activityId, startTime, endTime, 1);
            if (count < 1) {
                Object[] mms = DateUtils.getBeforeMonth(2, "M").stream().
                        map(Integer::parseInt).sorted(Integer::compareTo).toArray();
                String option5 = String.format(superviseConfig.getDetail().get("option5").getContent(), mms);
                superviseService.addOrgExtendInfo(Collections.singletonList(organizationForm.getOrganizationId()),
                        "option5", organizationForm, option5);
            }
            superviseEntity.setOption5((count > 0 ? 0 : 1));
            return superviseEntity.getOption5();
        }catch (Exception exception){
            log.error("calOptionNumber5异常",exception);
            log.error("calOptionNumber5-organizationForm={}",organizationForm);
            return 0;
        }
    }

}
