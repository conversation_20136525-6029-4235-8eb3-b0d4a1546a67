package com.goodsogood.ows.service.supervise.impl;

import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.mapper.user.OrgPeriodMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.vo.supervise.SupervisePeriodForm;
import com.goodsogood.ows.service.RegionService;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import com.goodsogood.ows.service.supervise.SuperviseService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.ListUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 党支部正式党员7名（含）以上，未设立支委会
 */
@Service("option3_impl")
@Log4j2
public class SuperviseOption3Impl implements SuperviseOptionCalculate {

    private final OrgTypeConfig orgTypeConfig;
    private final OrgPeriodMapper orgPeriodMapper;
    private final OrgService orgService;
    private final SuperviseService superviseService;
    private final RegionService regionService;
    private final SuperviseConfig superviseConfig;


    public SuperviseOption3Impl(OrgTypeConfig orgTypeConfig,
                                OrgPeriodMapper orgPeriodMapper,
                                OrgService orgService,
                                SuperviseService superviseService,
                                RegionService regionService,
                                SuperviseConfig superviseConfig) {
        this.orgTypeConfig = orgTypeConfig;
        this.orgPeriodMapper = orgPeriodMapper;
        this.orgService = orgService;
        this.superviseService = superviseService;
        this.regionService = regionService;
        this.superviseConfig = superviseConfig;
    }

    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
        Set<Long> all = null;
        log.debug("SuperviseOption3Impl-> 组织:[{}] 执行[{}]", organizationForm.getOrganizationId(),
                Utils.toJson(organizationForm));
        if (regionService.getRegionTopOrgID(organizationForm.getRegionId()).equals(organizationForm.getOrganizationId())
                ||orgTypeConfig.checkIsCommunist(organizationForm.getOrgTypeChild())) {
            List<OrganizationEntity> organizationEntities = orgService.
                    selectAllChildOrgBYOrgAndType(organizationForm.getRegionId(),
                            organizationForm.getOrganizationId(),
                            orgTypeConfig.getBranchChild());
            if (CollectionUtils.isEmpty(organizationEntities)) {
                log.debug("SuperviseOption3Impl-> 组织:[{}]->党委 未查询到下级组织", organizationForm.getOrganizationId());
                superviseEntity.setOption3(0);
                return 0;
            } else {
                log.debug("SuperviseOption3Impl-> 组织:[{}]->党委 查询到下级组织:[{}]",
                        organizationForm.getOrganizationId(), organizationEntities.size());
            }
            String join = ListUtils.join(",", organizationEntities.stream()
                    .map(OrganizationEntity::getOrganizationId).collect(Collectors.toList()));
            all = orgPeriodMapper.findNotExistPeriodTagByOrgs(organizationForm.getRegionId(), join);
            log.debug("SuperviseOption3Impl-> 组织:[{}]->党委 查询到未满足条件的组织数量:[{}]",
                    organizationForm.getOrganizationId(), CollectionUtils.isEmpty(all) ? 0 : all.size());
        } else if (orgTypeConfig.checkIsBranch(organizationForm.getOrgTypeChild())) {
            all = orgPeriodMapper.findNotExistPeriodTagByOrgs(organizationForm.getRegionId()
                    , organizationForm.getOrganizationId().toString());
            log.debug("SuperviseOption3Impl-> 组织:[{}]->组织 查询到未满足条件的组织数量:[{}]",
                    organizationForm.getOrganizationId(), CollectionUtils.isEmpty(all) ? 0 : all.size());
        }
        //显示数量与终于查询
        if (!CollectionUtils.isEmpty(all)) {
            all.forEach(item->{
                SupervisePeriodForm supervisePeriodForm = orgPeriodMapper.getIsCreatePeriod(item);
                String option3 = "";
                if(null!=supervisePeriodForm){
                    option3 = String.format(superviseConfig.getDetail().get("option3").getContent(),
                            supervisePeriodForm.getTotal());
                }
                superviseService.addOrgExtendInfo(Collections.singletonList(item),
                        "option3",organizationForm,option3);
            });
            superviseEntity.setOption3(all.size());
            return all.size();
        }
        log.debug("SuperviseOption3Impl-> 组织:[{}]->组织 最终查询到未满足的数量为:[{}]",
                organizationForm.getOrganizationId(), CollectionUtils.isEmpty(all) ? 0 : all.size());
        superviseEntity.setOption3(0);
        return 0;
    }
}
