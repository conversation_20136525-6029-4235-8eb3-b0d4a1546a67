package com.goodsogood.ows.service.supervise.impl;

import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.mapper.user.OrgGroupMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.service.RegionService;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import com.goodsogood.ows.service.supervise.SuperviseService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.ListUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 党委/党总支
 * 党支部党员人数20人以上，未划分党小组
 */
@Service("option10_impl")
@Log4j2
public class SuperviseOption10Impl implements SuperviseOptionCalculate {

    private final OrgTypeConfig orgTypeConfig;
    private final OrgGroupMapper orgGroupMapper;
    private final OrgService orgService;
    private final SuperviseService superviseService;
    private final RegionService regionService;
    private final SuperviseConfig superviseConfig;

    public SuperviseOption10Impl(OrgTypeConfig orgTypeConfig,
                                 OrgGroupMapper orgGroupMapper,
                                 OrgService orgService,
                                 SuperviseService superviseService,
                                 RegionService regionService,
                                 SuperviseConfig superviseConfig) {
        this.orgTypeConfig = orgTypeConfig;
        this.orgGroupMapper = orgGroupMapper;
        this.orgService = orgService;
        this.superviseService = superviseService;
        this.regionService = regionService;
        this.superviseConfig = superviseConfig;
    }

    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
        List<Long> listOrgIds = null;
        //只判断是否为党支部类型 党小组组织类型直接返回0
        if(orgTypeConfig.getBranchChild().contains(organizationForm.getOrgTypeChild())) {
            if (!orgTypeConfig.getBranchChild().contains(organizationForm.getOrgTypeChild())) {
                superviseEntity.setOption10(0);
                return 0;
            }
             listOrgIds = orgGroupMapper.findNotExisGroupNumByOrg(organizationForm.getRegionId(),
                    organizationForm.getOrganizationId().toString());
        }
        //如果是党委与党总支
        if(regionService.getRegionTopOrgID(organizationForm.getRegionId()).equals(organizationForm.getOrganizationId())
                ||orgTypeConfig.checkIsCommunist(organizationForm.getOrgTypeChild())) {
            if (!orgTypeConfig.checkIsCommunist(organizationForm.getOrgTypeChild())&&
                    !regionService.getRegionTopOrgID(organizationForm.getRegionId()).equals(organizationForm.getOrganizationId())) {
                superviseEntity.setOption10(0);
                return 0;
            }
            List<OrganizationEntity> list = orgService.selectAllChildOrgBYOrgAndType(organizationForm.getRegionId(),
                    organizationForm.getOrganizationId(),
                    orgTypeConfig.getBranchChild());
            if (CollectionUtils.isEmpty(list)) {
                superviseEntity.setOption10(0);
                return 0;
            }
            String join = ListUtils.join(",", list.stream().
                    map(OrganizationEntity::getOrganizationId).collect(Collectors.toList()));
             listOrgIds = orgGroupMapper.findNotExisGroupNumByOrg(organizationForm.getRegionId(), join);
        }
        if(!CollectionUtils.isEmpty(listOrgIds)){
            //添加异常记录入库数量一样 保证入库数据与显示数量一样
            String option10 = superviseConfig.getDetail().get("option10").getContent();
            //写入细项
            listOrgIds.forEach(item->{
                Integer existGroupNumber = orgGroupMapper.findExistGroupNumber(organizationForm.getRegionId(), item);
                String format = String.format(option10, existGroupNumber);
                superviseService.addOrgExtendInfo(Collections.singletonList(item), "option10", organizationForm,format);
            });

            superviseEntity.setOption10(listOrgIds.size());
            return listOrgIds.size();
        }
        superviseEntity.setOption10(0);
        return 0;
    }

}
