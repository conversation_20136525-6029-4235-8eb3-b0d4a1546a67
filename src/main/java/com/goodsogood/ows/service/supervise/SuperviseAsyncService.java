package com.goodsogood.ows.service.supervise;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.mapper.supervise.SuperviseRecordExtendMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseRecordExtendEntity;
import com.goodsogood.ows.model.vo.PushRequest;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.supervise.PushDingDingTemplateForm;
import com.goodsogood.ows.model.vo.supervise.PushWechatTemplateForm;
import com.goodsogood.ows.model.vo.supervise.SendMsgForm;
import com.goodsogood.ows.model.vo.supervise.SendMsgVo;
import com.goodsogood.ows.service.rank.RestTemplateHelper;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;


/**
 * 监督预警消息发送
 *
 * <AUTHOR>
 * @date 2020-12-24
 */
@Log4j2
@Service
public class SuperviseAsyncService {

    private final TogServicesConfig togServicesConfig;
    private final RestTemplate restTemplate;
    private final SuperviseRecordExtendMapper superviseRecordExtendMapper;
    private final UserMapper userMapper;

    @Autowired
    public SuperviseAsyncService(TogServicesConfig togServicesConfig,
                                 RestTemplate restTemplate,
                                 SuperviseRecordExtendMapper superviseRecordExtendMapper,
                                 UserMapper userMapper) {
        this.togServicesConfig = togServicesConfig;
        this.restTemplate = restTemplate;
        this.superviseRecordExtendMapper = superviseRecordExtendMapper;
        this.userMapper = userMapper;
    }

    /**
     * 监督预警消息推送
     */
    @Async("superviseManageExecutor")
    public void sendSuperviseMsg(HeaderHelper.SysHeader header, List<SendMsgVo> list) {
        Date date = new Date();
        if (CollectionUtils.isEmpty(list)) return;
        for (SendMsgVo x : list) {
            PushWechatTemplateForm form;
            // 如果是发给组织 并且是 有变量的消息
            if (x.getSendObject().equals(Constants.SEND_MSG_ORG)) {
                if (x.isVariableMsg()) {
                    // 有变量的消息
                    sendSuperviseMsg(header, x.getTemplateId(), x.getSendUserIds(), x.getUserInfos(), x.getOrgName(), 1);
                } else {
                    // 没有变量的消息
                    form = getPushWechat(date, x.getContent(),x.getTemplateId(),x.getOrgId(), x.getSendUserIds());
                    sendCommonPushRequest(header, form);
                }
            } else if (x.getSendObject().equals(Constants.SEND_MSG_USER)) {
                if (x.isVariableMsg()) {
                    // 有变量的消息 查询人员的名称 根据对应名称发送消息
                    sendSuperviseMsg(header, x.getTemplateId(), x.getSendUserIds(), x.getUserInfos(), "", 2);
                } else {
                    // 没有变量的消息 发送普通消息
                    form = getPushWechat(date, x.getContent(),x.getTemplateId(),x.getOrgId(), x.getSendUserIds());
                    sendCommonPushRequest(header, form);
                }
            }
        }
    }



    /**
     * 添加扩展记录
     */
    @Async("superviseExecutor")
    public void addSuperviseSendRecord(HeaderHelper.SysHeader header, List<SendMsgVo> list) {
        List<SuperviseRecordExtendEntity> listRecord = new ArrayList<>();
        list.forEach(item->{
            //如果是普通消息 或者 为变量消息并且发送给管理
           if(!item.isVariableMsg() ||(item.isVariableMsg()&& item.getSendObject()==1)) {
               Collection<SendMsgVo.UserInfo> userInfos = item.getUserInfos();
               userInfos.forEach(it->{
                   SuperviseRecordExtendEntity superviseRecordExtendEntity = new SuperviseRecordExtendEntity();
                   superviseRecordExtendEntity.setSendObject(item.getSendObject());
                   superviseRecordExtendEntity.setCreateTime(new Date());
                   superviseRecordExtendEntity.setTemplateId(item.getTemplateId().intValue());
                   superviseRecordExtendEntity.setOptionKey(item.getOptionKey());
                   superviseRecordExtendEntity.setUserId(it.getUserId());
                   superviseRecordExtendEntity.setRecordNum(Integer.valueOf(DateUtils.getCurrentFormatTime()));
                   superviseRecordExtendEntity.setOrgId(item.getOrgId());
                   listRecord.add(superviseRecordExtendEntity);
               });
           }else {
               //发送用户记录信
               SuperviseRecordExtendEntity superviseRecordExtendEntity = new SuperviseRecordExtendEntity();
               superviseRecordExtendEntity.setSendObject(item.getSendObject());
               superviseRecordExtendEntity.setCreateTime(new Date());
               superviseRecordExtendEntity.setTemplateId(item.getTemplateId().intValue());
               superviseRecordExtendEntity.setOptionKey(item.getOptionKey());
               superviseRecordExtendEntity.setUserId((Long) item.getSendUserIds().toArray()[0]);
               superviseRecordExtendEntity.setOrgId(item.getOrgId());
               superviseRecordExtendEntity.setRecordNum(Integer.valueOf(DateUtils.getCurrentFormatTime()));
               listRecord.add(superviseRecordExtendEntity);
           }
            if (listRecord.size() >= 300) {
                superviseRecordExtendMapper.insertList(listRecord);
                listRecord.clear();
            }
        });
        if(!CollectionUtils.isEmpty(listRecord)){
            superviseRecordExtendMapper.insertList(listRecord);
            listRecord.clear();
        }
    }

    /**
     * PushWechatTemplateForm -> builder
     *
     * @param date    当前时间
     * @param content 发送内容
     * @param info    发送用户
     * @return PushWechatTemplateForm
     */
    public PushWechatTemplateForm getPushWechat(Date date, String content, Long templateId, Long orgId, Collection<Long> info) {
        PushWechatTemplateForm pushWechatTemplateForm;
        log.debug("监督预警普通消息发送开始=>【{}】，【{}】，【{}】，【{}】", content,templateId,orgId,info);
        Set<Long> userIds = new HashSet<>(info);
        pushWechatTemplateForm = PushWechatTemplateForm.builder()
                .channelType((byte) 4)
                .templateId(templateId)
                .channelType(Constants.DING_DING_TYPE)
                .source(Constants.SOURCE)
                .pushType(0)
//                .pushTime(date)
                .full(0)
                .isCurrentOrg(0)
                .orgId(orgId)
                .userIds(userIds)
                .data(content).build();
        log.debug("监督预警普通消息发送内容=>{}", pushWechatTemplateForm);
        return pushWechatTemplateForm;
    }

    /**
     * 监督预警消息推送
     *
     * @param templateId 模板id
     * @param userInfos  操作用户信息组
     * @param name       对象名称
     * @param flag       0:无参数消息
     *                   1:组织名称
     *                   2:用户名称
     */
    public void sendSuperviseMsg(HeaderHelper.SysHeader header, Long templateId, Collection<Long> sendUserInfo, Collection<SendMsgVo.UserInfo> userInfos, String name, int flag) {
        if (flag == 1) {
            if (CollectionUtils.isEmpty(sendUserInfo)) return;
            sendUserInfo.forEach(x -> {
                sendMsg(header.getRegionId(), templateId, x, name, flag);
            });
        } else if (flag == 2) {
            if (CollectionUtils.isEmpty(userInfos)) return;
            for (SendMsgVo.UserInfo info : userInfos) {
                sendMsg(header.getRegionId(), templateId, info.getUserId(), info.getUserName(), flag);
            }
        }
    }

    public void sendMsg(Long regionId, Long templateId, Long userId, String name, int flag) {
        PushRequest pushRequest = new PushRequest();
        List<SendMsgForm> dataList = new ArrayList<>(1);
        SendMsgForm form = new SendMsgForm();
        // 设置模板所需 参数
        if (flag == 1) {
            form.setOrgName(name);
        } else if (flag == 2) {
            form.setUserName(name);
        }
        form.setUserId(userId);
        dataList.add(form);
        pushRequest.setTemplateId(templateId);
        pushRequest.setChannelType((byte) 4);
        pushRequest.setSource(Constants.SOURCE);
        pushRequest.setData(dataList);
        sendPushRequest(pushRequest, regionId);
    }

    /**
     * 消息发送
     *
     * @param pushRequest 推送中心封装类
     * @param regionId    区县id
     */
    public void sendPushRequest(PushRequest pushRequest, Long regionId) {
        String url = String.format("http://%s/global/push/diff", togServicesConfig.getPushCenter());
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("_region_id", regionId.toString());
        try {
            log.debug("预警推送消息开始->pushRequest:【{}】，url->【{}】", pushRequest, url);
            RemoteApiHelper.post(this.restTemplate, url, pushRequest, httpHeaders, new TypeReference<Result<Long>>() {
            });
            log.debug("预警推送消息成功->pushRequest:【{}】，url->【{}】", pushRequest, url);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("预警消息发送失败->pushRequest:【{}】，message->【{}】", pushRequest, e.getMessage(), e);
        }
    }

    /**
     * 普通钉钉消息发送
     *
     * @param pushWechatTemplateForm 推送中心封装类
     * @param sysHeader              区县id
     */
    public void sendCommonPushRequest(HeaderHelper.SysHeader sysHeader, PushWechatTemplateForm pushWechatTemplateForm) {
        String url = String.format("http://%s/global/push/same", togServicesConfig.getPushCenter());
        try {
            PushDingDingTemplateForm pushDingDingTemplateForm = PushDingDingTemplateForm.buildByWechatForm(pushWechatTemplateForm);
            log.debug("预警普通推送消息开始->pushWechatTemplateForm:【{}】，url->【{}】", pushWechatTemplateForm, url);
            RestTemplateHelper.post(sysHeader, url, pushDingDingTemplateForm, new TypeReference<Result<Boolean>>() {
            });
            log.debug("预警普通推送消息成功->pushWechatTemplateForm:【{}】，url->【{}】", pushWechatTemplateForm, url);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("预警普通消息发送失败->pushWechatTemplateForm:【{}】，message->【{}】", pushWechatTemplateForm, e.getMessage(), e);
        }
    }
}
