package com.goodsogood.ows.service.supervise;

import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.mapper.supervise.SuperviseExtendMapper;
import com.goodsogood.ows.mapper.supervise.SuperviseMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.supervise.SuperviseExtendEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.service.user.OrgPeriodService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.SpringContextUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @create 2021-10-13 17:24
 **/
@Service
@Log4j2
public class SuperviseExtendService {

    private final SuperviseExtendMapper superviseExtendMapper;
    private final UserService userService;
    private final SuperviseConfig superviseConfig;
    private final SuperviseMapper superviseMapper;
    private final OrgPeriodService orgPeriodService;
    private final OrgService orgService;

    @Autowired
    public SuperviseExtendService(SuperviseExtendMapper superviseExtendMapper,
                                  UserService userService,
                                  SuperviseConfig superviseConfig,
                                  SuperviseMapper superviseMapper,
                                  OrgPeriodService orgPeriodService,
                                  OrgService orgService) {
        this.superviseExtendMapper = superviseExtendMapper;
        this.userService = userService;
        this.superviseConfig = superviseConfig;
        this.superviseMapper = superviseMapper;
        this.orgPeriodService = orgPeriodService;
        this.orgService = orgService;
    }

    /**
     * 添加扩展信息 用户
     * @param allUserInfo
     */
    @Transactional(rollbackFor = Exception.class)
    public void addExtendUserInfo(Set<Long> allUserInfo, String optionKey, String optionName,
                                  Long regionId,
                                  Long orgId) {

        List<UserEntity> userInfoByUserIds = userService.getUserInfoByUserIds(allUserInfo);
        List<SuperviseExtendEntity> list = new ArrayList<>();
        userInfoByUserIds.forEach(item->{
            SuperviseExtendEntity superviseExtendEntity = new SuperviseExtendEntity();
            superviseExtendEntity.setRegionId(regionId);
            superviseExtendEntity.setOptionKey(optionKey);
            superviseExtendEntity.setOptionName(optionName);
            superviseExtendEntity.setOrgId(orgId);
            superviseExtendEntity.setCreateTime(new Date());
            superviseExtendEntity.setUserId(item.getUserId());
            superviseExtendEntity.setUserName(item.getName());
            superviseExtendEntity.setUserPhone(item.getPhoneSecret());
            list.add(superviseExtendEntity);
        });
        if(!CollectionUtils.isEmpty(list)) {
            superviseExtendMapper.insertList(list);
        }
    }



    /**
     * 添加扩展信息 用户
     * @param allUserInfo
     */
    @Transactional(rollbackFor = Exception.class)
    public void addExtendUserInfo(Set<Long> allUserInfo, String optionKey, String optionName,
                                  Long regionId,
                                  Long orgId, Object[] mms) {
        List<UserEntity> userInfoByUserIds = userService.getUserInfoByUserIds(allUserInfo);
        List<SuperviseExtendEntity> list = new ArrayList<>();
        userInfoByUserIds.forEach(item->{
            SuperviseExtendEntity superviseExtendEntity = new SuperviseExtendEntity();
            superviseExtendEntity.setRegionId(regionId);
            superviseExtendEntity.setOptionKey(optionKey);
            superviseExtendEntity.setOptionName(optionName);
            superviseExtendEntity.setOrgId(orgId);
            superviseExtendEntity.setCreateTime(new Date());
            superviseExtendEntity.setUserId(item.getUserId());
            superviseExtendEntity.setUserName(item.getName());
            superviseExtendEntity.setUserPhone(item.getPhoneSecret());
            //格式化出详情数据
            String optionFormat = String.format(superviseConfig.getDetail().get(optionKey).getContent(), mms);
            String formatLastResult = item.getName() + "同志，" + optionFormat;
            superviseExtendEntity.setDetailContent(formatLastResult);
            list.add(superviseExtendEntity);
        });
        if(!CollectionUtils.isEmpty(list)) {
            superviseExtendMapper.insertList(list);
        }
    }



    /**
     * 添加扩展信息 组织
     * @param superviseEntities
     */
    @Transactional(rollbackFor = Exception.class)
    public void addExtendOrgInfo(List<SuperviseEntity> superviseEntities,String optionKey,String optionName,
                              Long regionId,
                              Long orgId) {
        List<SuperviseExtendEntity> list = new ArrayList<>();
        superviseEntities.forEach(item->{
            SuperviseExtendEntity superviseExtendEntity = new SuperviseExtendEntity();
            superviseExtendEntity.setRegionId(regionId);
            superviseExtendEntity.setOptionKey(optionKey);
            superviseExtendEntity.setOptionName(optionName);
            superviseExtendEntity.setOrgId(orgId);
            superviseExtendEntity.setCreateTime(new Date());
            //设置下级组织id
            superviseExtendEntity.setSubOrgId(item.getOrgId());
            superviseExtendEntity.setOrgLevel(item.getOrgLevel());
            superviseExtendEntity.setOrgName(item.getOrgName());
            superviseExtendEntity.setOrgSecretary(item.getOrgSecretary());
            list.add(superviseExtendEntity);
        });
        if(!CollectionUtils.isEmpty(list)) {
            superviseExtendMapper.insertList(list);
        }
    }

    /**
     * 添加扩展信息 组织
     * @param listOrgIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void addExtendOrgInfoByOrgIds(Collection<Long> listOrgIds,String optionKey,String optionName,
                                 Long regionId,
                                 Long orgId,String detailContent) {
        List<SuperviseExtendEntity> list = new ArrayList<>();
        listOrgIds.forEach(item->{
            SuperviseExtendEntity superviseExtendEntity = new SuperviseExtendEntity();
            superviseExtendEntity.setRegionId(regionId);
            superviseExtendEntity.setOptionKey(optionKey);
            superviseExtendEntity.setOptionName(optionName);
            superviseExtendEntity.setOrgId(orgId);
            superviseExtendEntity.setCreateTime(new Date());

            OrganizationEntity organizationEntity = orgService.getById(item);
            //设置下级组织id
            superviseExtendEntity.setSubOrgId(organizationEntity.getOrganizationId());
            superviseExtendEntity.setOrgLevel(organizationEntity.getOrgLevel());
            superviseExtendEntity.setOrgName(organizationEntity.getName());
            //得到组织书记
            Map<String, String> periodLeaderByOrg = orgPeriodService.findPeriodLeaderByOrg(organizationEntity.getOrganizationId());
            if(null!=periodLeaderByOrg) {
                superviseExtendEntity.setOrgSecretary(periodLeaderByOrg.get("org_secretary"));
                superviseExtendEntity.setOrgSecretaryId(periodLeaderByOrg.get("org_secretary_id"));
           }
            superviseExtendEntity.setDetailContent(detailContent);
            list.add(superviseExtendEntity);
        });
        if(!CollectionUtils.isEmpty(list)) {
            superviseExtendMapper.insertList(list);
        }
    }




    public SuperviseEntity pullSuperviseBranchInfoTest(OrganizationEntity organizationForm,
                                               Map<String, SuperviseConfig.Option> branch,
                                               String implClassName) {
        SuperviseEntity superviseEntity = new SuperviseEntity();
        superviseEntity.setOrgId(organizationForm.getOrganizationId());
        superviseEntity.setOrgName(organizationForm.getName());
        superviseEntity.setOrgLevel(organizationForm.getOrgLevel());
        superviseEntity.setRegionId(organizationForm.getRegionId());
        superviseEntity.setOrgType(organizationForm.getOrgType());
        superviseEntity.setOrgTypeChild(organizationForm.getOrgTypeChild());
        Map<String, String> periodLeaderByOrg = orgPeriodService.findPeriodLeaderByOrg(organizationForm.getOrganizationId());
        if(null!=periodLeaderByOrg) {
            superviseEntity.setOrgSecretary(periodLeaderByOrg.get("org_secretary"));
            superviseEntity.setOrgSecretaryId(periodLeaderByOrg.get("org_secretary_id"));
        }
        //配置支部实现类
        try {
            SuperviseOptionCalculate superviseOptionCalculate =(SuperviseOptionCalculate)
                    SpringContextUtil.getBean(implClassName);
            superviseOptionCalculate.calOptionNumber(organizationForm, superviseEntity);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("反射异常,方法名-->{}",implClassName);
        }
        superviseEntity.setCreateTime(new Date());
        return superviseEntity;
    }

}