package com.goodsogood.ows.service.supervise.impl;

import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.mapper.user.OrgPeriodMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.vo.supervise.SupervisePeriodForm;
import com.goodsogood.ows.service.RegionService;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import com.goodsogood.ows.service.supervise.SuperviseService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.ListUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组织逾期未换届
 */
@Service("option2_impl")
@Log4j2
public class SuperviseOption2Impl implements SuperviseOptionCalculate {

    private final OrgTypeConfig orgTypeConfig;
    private final OrgPeriodMapper orgPeriodMapper;
    private final OrgService orgService;
    private final SuperviseService superviseService;
    private final RegionService regionService;
    private final SuperviseConfig superviseConfig;

    public SuperviseOption2Impl(OrgTypeConfig orgTypeConfig,
                                OrgPeriodMapper orgPeriodMapper,
                                OrgService orgService,
                                SuperviseService superviseService,
                                RegionService regionService,
                                SuperviseConfig superviseConfig) {
        this.orgTypeConfig = orgTypeConfig;
        this.orgPeriodMapper = orgPeriodMapper;
        this.orgService = orgService;
        this.superviseService = superviseService;
        this.regionService = regionService;
        this.superviseConfig = superviseConfig;
    }

    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
        List<Long> listOrgIds = null;
        //是否为党委/党总支
        if (regionService.getRegionTopOrgID(organizationForm.getRegionId()).equals(organizationForm.getOrganizationId())
                ||orgTypeConfig.checkIsCommunist(organizationForm.getOrgTypeChild())) {
            List<OrganizationEntity> organizationEntities = orgService.selectAllChildOrgBYOrgAndType(organizationForm.getRegionId(),
                    organizationForm.getOrganizationId(),
                    orgTypeConfig.getBranchChild());
            if (CollectionUtils.isEmpty(organizationEntities)) {
                superviseEntity.setOption2(0);
                return 0;
            }
            List<OrganizationEntity> expireOrg = orgPeriodMapper.findExpireOrg(organizationForm.getRegionId(),
                    superviseEntity.getOrgId(),1);
            listOrgIds=expireOrg.stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toList());
         //* 是否为党支部/党小组 */
        } else if(orgTypeConfig.checkIsBranch(organizationForm.getOrgTypeChild())) {
            List<OrganizationEntity> expireOrg = orgPeriodMapper.findExpireOrg(organizationForm.getRegionId(),
                    superviseEntity.getOrgId(),2);
            listOrgIds=expireOrg.stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toList());
        }
        if(!CollectionUtils.isEmpty(listOrgIds)){
            //添加异常记录入库数量一样 保证入库数据与显示数量一样
            listOrgIds.forEach(item->{
                SupervisePeriodForm supervisePeriodForm = orgPeriodMapper.getOverdueInfoByOrgId(item);
                String option2 = "";
                if(null!=supervisePeriodForm){
                    option2 = String.format(superviseConfig.getDetail().get("option2").getContent(),
                            supervisePeriodForm.getStartTime(),supervisePeriodForm.getEndTime());
                }
                superviseService.addOrgExtendInfo(Collections.singletonList(item),
                        "option2",organizationForm,option2);
            });
            superviseEntity.setOption2(listOrgIds.size());
            return listOrgIds.size();
        }
        superviseEntity.setOption2(0);
        return 0;
    }
}
