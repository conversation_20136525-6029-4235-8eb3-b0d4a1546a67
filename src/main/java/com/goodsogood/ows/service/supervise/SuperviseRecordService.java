package com.goodsogood.ows.service.supervise;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.supervise.SuperviseRecordMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseRecordEntity;
import com.goodsogood.ows.model.vo.supervise.SendMsgVo;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-12-24 16:16
 **/
@Service
@Log4j2
public class SuperviseRecordService {

    private final SuperviseRecordMapper superviseRecordMapper;
    private static final ObjectMapper OBJECTMAPPER = new ObjectMapper();

    @Autowired
    public SuperviseRecordService(SuperviseRecordMapper superviseRecordMapper) {
        this.superviseRecordMapper = superviseRecordMapper;
    }

    /**
     * 插入记录
     * @return
     */
    public Long insertRecord(HeaderHelper.SysHeader sysHeader, Long orgId){
        SuperviseRecordEntity superviseRecordEntity = new SuperviseRecordEntity();
        superviseRecordEntity.setType(1);
        superviseRecordEntity.setCreateTime(new Date());
        superviseRecordEntity.setUserId(sysHeader.getUserId());
        superviseRecordEntity.setOrgId(orgId);
        superviseRecordMapper.insertUseGeneratedKeys(superviseRecordEntity);
        return superviseRecordEntity.getSuperviseRecordId();

    }

    /**
     * 验证当天是否发送
     * @return
     */
    public Integer valTodayIsSend( HeaderHelper.SysHeader sysHeader,Long orgId){
        return    superviseRecordMapper.valTodayIsSend(orgId);
    }


    /**
     * 插入记录
     * @return
     */
    public void updateRecord(List<SendMsgVo> listSendMsg,Long recordId)  {
        SuperviseRecordEntity superviseRecordEntity = superviseRecordMapper.selectByPrimaryKey(recordId);
        try {
            String jsonResult = OBJECTMAPPER.writeValueAsString(listSendMsg);
            superviseRecordEntity.setRemark(jsonResult);
            superviseRecordEntity.setUpdateTime(new Date());
            superviseRecordMapper.updateByPrimaryKeySelective(superviseRecordEntity);
        }catch (Exception exception){
            log.error("updateRecord-解析出错:",exception);
        }
    }


}