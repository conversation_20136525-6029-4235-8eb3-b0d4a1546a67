package com.goodsogood.ows.service.supervise.impl;

import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.mapper.supervise.SuperviseExtendMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.supervise.SuperviseExtendEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.service.RegionService;
import com.goodsogood.ows.service.activity.RevisitResultService;
import com.goodsogood.ows.service.supervise.SuperviseExtendService;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import com.goodsogood.ows.service.supervise.SuperviseService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 连续2个季度重温入党志愿书完成率低于50%的党支部
 */
@Service("option24_impl")
@Log4j2
public class SuperviseOption24Impl implements SuperviseOptionCalculate {

    private final OrgTypeConfig orgTypeConfig;
    private final RevisitResultService revisitResultService;
    private final UserService userService;
    private final OrgService orgService;
    private final SuperviseService superviseService;
    private final SuperviseExtendService superviseExtendService;
    private final SuperviseConfig superviseConfig;
    private final RegionService regionService;
    private final SuperviseExtendMapper superviseExtendMapper;



    public SuperviseOption24Impl(OrgTypeConfig orgTypeConfig,
                                 RevisitResultService revisitResultService,
                                 UserService userService,
                                 OrgService orgService,
                                 SuperviseService superviseService,
                                 SuperviseExtendService superviseExtendService,
                                 SuperviseConfig superviseConfig,
                                 RegionService regionService,
                                 SuperviseExtendMapper superviseExtendMapper) {
        this.orgTypeConfig = orgTypeConfig;
        this.revisitResultService = revisitResultService;
        this.userService = userService;
        this.orgService = orgService;
        this.superviseService = superviseService;
        this.superviseExtendService = superviseExtendService;
        this.superviseConfig = superviseConfig;
        this.regionService = regionService;
        this.superviseExtendMapper = superviseExtendMapper;
    }


    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
        //是否为党支部
        if (orgTypeConfig.checkIsSingleBranch(organizationForm.getOrgTypeChild())) {
            //连续两个季度
            Integer currentQuarterly = DateUtils.getCurrentQuarterly();
            List<String> continuousTwoQuarterly = DateUtils.getMonthsByQuarterly(currentQuarterly);
            Set<Long> allUserInfo = userService.getSuperviseUserIds(continuousTwoQuarterly,organizationForm);
            String startMonth = continuousTwoQuarterly.get(0)+"-01 00:00:00" ;
            String endMonth = DateUtils.getNextMonth(continuousTwoQuarterly.get(continuousTwoQuarterly.size()-1))+"-01 00:00:00"  ;
            List<Integer> listType = new ArrayList<>();
            listType.add(2);
            listType.add(4);
            //得到参加用户信息
            Set<Long> activityUserInfo = revisitResultService.getAddUserInfoByAddScore(startMonth, endMonth,
                    organizationForm.getOrganizationId(), listType);
            //得到参与率
            double participationRate =(double) activityUserInfo.size() / allUserInfo.size();
            if(participationRate<0.5){
                superviseEntity.setOption24(1);
                //开始第一个季度
                List<String> monthsByQuarterly = Objects.requireNonNull(DateUtils.getMonthsByQuarterly(currentQuarterly))
                        .subList(0, 3);
                String startMonthOne = monthsByQuarterly.get(0)+"-01 00:00:00" ;
                String endMonthOne = DateUtils.getNextMonth(monthsByQuarterly.get(monthsByQuarterly.size()-1))+"-01 00:00:00";
                //得到参加用户信息第一季度
                Set<Long> activityUserInfoOne = revisitResultService.getAddUserInfoByAddScore(startMonthOne, endMonthOne,
                        organizationForm.getOrganizationId(), listType);
                //得到参与率
                double participationRateOne = BigDecimal.valueOf((double) activityUserInfoOne.size() / allUserInfo.size())
                        .setScale(2, RoundingMode.HALF_UP).doubleValue();
                //开始第二个季度
                List<String> monthsByQuarterlyTow = Objects.requireNonNull(DateUtils.getMonthsByQuarterly(currentQuarterly))
                        .subList(3, 6);
                String startMonthTwo = monthsByQuarterlyTow.get(0)+"-01 00:00:00" ;
                String endMonthTwo = DateUtils.getNextMonth(monthsByQuarterlyTow.get(monthsByQuarterlyTow.size()-1))+"-01 00:00:00";
                //得到参加用户信息第二季度
                Set<Long> activityUserInfoTwo = revisitResultService.getAddUserInfoByAddScore(startMonthTwo, endMonthTwo,
                        organizationForm.getOrganizationId(), listType);
                double participationRateTwo =BigDecimal.valueOf((double) activityUserInfoTwo.size() / allUserInfo.size())
                        .setScale(2, RoundingMode.HALF_UP).doubleValue();
                List<String> monthsByQuarterlyName = DateUtils.getMonthsByQuarterlyName(currentQuarterly);
                String option23 = String.format(superviseConfig.getDetail().get("option24").getContent(),
                        monthsByQuarterlyName.get(0),participationRateOne * 100+"%", monthsByQuarterlyName.get(1),
                        participationRateTwo * 100+"%");
                superviseService.addOrgExtendInfo( Collections.singletonList(organizationForm.getOrganizationId()),
                        "option24", organizationForm,option23);
            }else {
                superviseEntity.setOption24(0);
            }
            return superviseEntity.getOption24();
        }
        //如果是党委或者党总支
        if (regionService.getRegionTopOrgID(organizationForm.getRegionId()).equals(organizationForm.getOrganizationId())
                ||orgTypeConfig.checkIsCommunist(organizationForm.getOrgTypeChild())) {
            //得到这个党委下面所有支部id
            List<Long> listOrgIds = orgService.selectAllChildOrgBYOrgAndType(organizationForm.getRegionId(),
                    organizationForm.getOrganizationId(),orgTypeConfig.getBranchChild()).stream()
                    .map(OrganizationEntity::getOrganizationId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(listOrgIds)){
                superviseEntity.setOption24(0);
                return 0;
            }
            //得到入党志愿书以及入党总数低于百分50
            List<SuperviseEntity> list = superviseService.getRevisitCount(listOrgIds, "option24");
            list.forEach(item->{
                Example example = new Example(SuperviseExtendEntity.class);
                example.createCriteria().andEqualTo("orgId",item.getOrgId())
                        .andEqualTo("optionKey","option24");
                SuperviseExtendEntity superviseExtendEntity = superviseExtendMapper.selectOneByExample(example);
                superviseExtendService.addExtendOrgInfoByOrgIds(
                        Collections.singletonList(item.getOrgId()),
                        superviseConfig.getCommittee().get("option24").getOptionKey(),
                        superviseConfig.getCommittee().get("option24").getOptionName(),
                        organizationForm.getRegionId(),organizationForm.getOrganizationId(),
                        superviseExtendEntity.getDetailContent());
            });
            superviseEntity.setOption24(list.size());
            return superviseEntity.getOption24();
        }
        return  0;
    }





}
