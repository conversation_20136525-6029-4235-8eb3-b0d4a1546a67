package com.goodsogood.ows.service.supervise.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.model.db.ppmd.UnpaidStatisticsMergeDetailVO;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import com.goodsogood.ows.service.supervise.SuperviseService;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 党委/党总支
 * 连续6个月未交纳党费的党员
 */
@Service("option17_impl")
@Log4j2
public class SuperviseOption17Impl implements SuperviseOptionCalculate {

    private static final String CACHE_KEY = "ppmd_supervision_unpaid_statistics_six";
    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy_MM");
    private static final String optionId = "option17";

    private final OrgTypeConfig orgTypeConfig;
    private final ObjectMapper objectMapper;
    private final StringRedisTemplate redisTemplate;
    private final SuperviseService superviseService;

    public SuperviseOption17Impl(OrgTypeConfig orgTypeConfig,
                                 ObjectMapper objectMapper,
                                 StringRedisTemplate redisTemplate,
                                 SuperviseService superviseService) {
        this.orgTypeConfig = orgTypeConfig;
        this.objectMapper = objectMapper;
        this.redisTemplate = redisTemplate;
        this.superviseService = superviseService;
    }


    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
        int n = 0;
        if (!orgTypeConfig.checkIsCommunist(organizationForm.getOrgTypeChild())) {
            superviseEntity.setOption17(n);
            return n;
        }
        Date date = new Date();
        String hashKey = organizationForm.getOrganizationId() + "_" + SIMPLE_DATE_FORMAT.format(date);
        Object object = redisTemplate.opsForHash().get(CACHE_KEY, hashKey);
        try {
            if (null != object) {
                UnpaidStatisticsMergeDetailVO<UnpaidStatisticsMergeDetailVO.UnpaidUser>
                        unpaidUserUnpaidStatisticsMergeDetailVO = objectMapper.readValue((String) object,
                        new TypeReference<UnpaidStatisticsMergeDetailVO<UnpaidStatisticsMergeDetailVO.UnpaidUser>>() {
                });
                List<UnpaidStatisticsMergeDetailVO.UnpaidUser> list = unpaidUserUnpaidStatisticsMergeDetailVO.getList();
                if (!CollectionUtils.isEmpty(list)) {
//                    Set<Long> collect = list.stream()
//                            .filter(x -> null != x.getOrgId())
//                            .map(UnpaidStatisticsMergeDetailVO.UnpaidUser::getOrgId)
//                            .collect(Collectors.toSet());
//                    n = superviseService.getRevisitMeetingCount(
//                            collect,
//                            optionId,
//                            organizationForm);
                    Set<Long> collect = list.stream().map(UnpaidStatisticsMergeDetailVO.UnpaidUser::getUserId)
                            .collect(Collectors.toSet());
                    n = collect.size();
                }
                superviseEntity.setOption17(n);
                return n;
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            log.error("SuperviseOption16Impl->错误[{}]", e.getMessage(), e);
        }

        superviseEntity.setOption17(n);
        return n;
    }
}
