package com.goodsogood.ows.service.supervise.impl;

import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.vo.sas.OrganizationForm;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

/**
 * 未配齐组织书记
 */
@Service("option4_impl")
@Log4j2
public class SuperviseOption4Impl implements SuperviseOptionCalculate {

    private final OrgTypeConfig orgTypeConfig;

    public SuperviseOption4Impl(OrgTypeConfig orgTypeConfig) {
        this.orgTypeConfig = orgTypeConfig;
    }


    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
        return null;
    }





}
