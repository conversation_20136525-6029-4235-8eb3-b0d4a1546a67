package com.goodsogood.ows.service.supervise.impl;

import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.mapper.user.OrgGroupMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.ListUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 党支部党员人数20人以上，未划分党小组的
 */
@Service("option21_impl")
@Log4j2
public class SuperviseOption21Impl implements SuperviseOptionCalculate {

    private final OrgTypeConfig orgTypeConfig;
    private final OrgGroupMapper orgGroupMapper;
    private final OrgService orgService;

    public SuperviseOption21Impl(OrgTypeConfig orgTypeConfig, OrgGroupMapper orgGroupMapper, OrgService orgService) {
        this.orgTypeConfig = orgTypeConfig;
        this.orgGroupMapper = orgGroupMapper;
        this.orgService = orgService;
    }


    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
//        if (!orgTypeConfig.checkIsCommunist(organizationForm.getOrgTypeChild())) {
//            superviseEntity.setOption21(0);
//            return 0;
//        }
//        List<OrganizationEntity> list = orgService.selectAllChildOrgBYOrgAndType(organizationForm.getRegionId(),
//                organizationForm.getOrganizationId(),
//                orgTypeConfig.getBranchChild());
//        if (CollectionUtils.isEmpty(list)) {
//            superviseEntity.setOption21(0);
//            return 0;
//        }
//        String join = ListUtils.join(",", list.stream().
//                map(OrganizationEntity::getOrganizationId).collect(Collectors.toList()));
//        Long number = orgGroupMapper.findNotExisGroupNumByOrg(organizationForm.getRegionId(), join);
//        superviseEntity.setOption21(number.intValue());
//        return number.intValue();
          return  0;
    }

}
