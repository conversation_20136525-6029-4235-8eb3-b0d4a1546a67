package com.goodsogood.ows.service.supervise.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.model.db.ppmd.UnpaidStatisticsMergeDetailVO;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.service.supervise.SuperviseExtendService;
import com.goodsogood.ows.service.supervise.SuperviseOptionCalculate;
import com.goodsogood.ows.service.supervise.SuperviseService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 党支部/党小组
 * 有党员连续5个月未交纳党费的党支部
 */
@Service("option25_impl")
@Log4j2
public class SuperviseOption25Impl implements SuperviseOptionCalculate {

    private static final String CACHE_KEY = "ppmd_supervision_unpaid_statistics_five";
    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy_MM");
    private static final String optionId = "option25";

    private final OrgTypeConfig orgTypeConfig;
    private final ObjectMapper objectMapper;
    private final StringRedisTemplate redisTemplate;
    private final SuperviseService superviseService;
    private final SuperviseExtendService superviseExtendService;
    private final SuperviseConfig superviseConfig;

    public SuperviseOption25Impl(OrgTypeConfig orgTypeConfig,
                                 ObjectMapper objectMapper,
                                 StringRedisTemplate redisTemplate,
                                 SuperviseService superviseService,
                                 SuperviseExtendService superviseExtendService,
                                 SuperviseConfig superviseConfig) {
        this.orgTypeConfig = orgTypeConfig;
        this.objectMapper = objectMapper;
        this.redisTemplate = redisTemplate;
        this.superviseService = superviseService;
        this.superviseExtendService = superviseExtendService;
        this.superviseConfig = superviseConfig;
    }

    @Override
    public Integer calOptionNumber(OrganizationEntity organizationForm, SuperviseEntity superviseEntity) {
        int n = 0;
        if (!orgTypeConfig.checkIsBranch(organizationForm.getOrgTypeChild())) {
            superviseEntity.setOption25(n);
            return n;
        }
        Date date = new Date();
        String hashKey = organizationForm.getOrganizationId() + "_" + SIMPLE_DATE_FORMAT.format(date);
        Object object = redisTemplate.opsForHash().get(CACHE_KEY, hashKey);
        try {
            if (null != object) {
                UnpaidStatisticsMergeDetailVO<UnpaidStatisticsMergeDetailVO.UnpaidUser>
                        unpaidUserUnpaidStatisticsMergeDetailVO =
                        objectMapper.readValue((String) object, new
                                TypeReference<UnpaidStatisticsMergeDetailVO<UnpaidStatisticsMergeDetailVO.UnpaidUser>>() {
                                });
                List<UnpaidStatisticsMergeDetailVO.UnpaidUser> list = unpaidUserUnpaidStatisticsMergeDetailVO.getList();
                if (!CollectionUtils.isEmpty(list)) {
                    Set<Long> collect = list.stream().map(UnpaidStatisticsMergeDetailVO.UnpaidUser::getUserId)
                            .collect(Collectors.toSet());
                    //写入t_supervise_extend扩展表信息
                    Object[] mms = DateUtils.getBeforeMonth(5, "M").stream().
                            map(Integer::parseInt).sorted(Integer::compareTo).toArray();
                    superviseExtendService.addExtendUserInfo(
                            collect,
                            superviseConfig.getCommittee().get(optionId).getOptionKey(),
                            superviseConfig.getCommittee().get(optionId).getOptionName(),
                            organizationForm.getRegionId(), organizationForm.getOrganizationId(),mms);
                    n = collect.size();
                }
                superviseEntity.setOption25(n);
                return n;
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            log.error("SuperviseOption25Impl->错误[{}]", e.getMessage(), e);
        }

        superviseEntity.setOption25(n);
        return n;
    }
}
