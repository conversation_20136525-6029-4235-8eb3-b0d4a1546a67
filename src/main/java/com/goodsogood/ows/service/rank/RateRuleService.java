package com.goodsogood.ows.service.rank;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.goodsogood.ows.mapper.rank.RateDetailsMapper;
import com.goodsogood.ows.mapper.rank.RateRuleMapper;
import com.goodsogood.ows.model.db.rank.RateDetailsEntity;
import com.goodsogood.ows.model.db.rank.RateRuleEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.rank.OrgWhereResultForm;
import com.goodsogood.ows.model.vo.rank.StarOrgVo;
import com.goodsogood.ows.model.vo.rank.UserOrgResultChildForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Create by FuXiao on 2020/10/19
 */
@Service
@Log4j2
public class RateRuleService {
    private final RateRuleMapper rateRuleMapper;
    private final RateDetailsMapper rateDetailsMapper;
    private final StringRedisTemplate redisTemplate;
    private final ClientUserCenterService clientUserCenterService;
    private final ObjectMapper objectMapper;

    public RateRuleService(RateRuleMapper rateRuleMapper, RateDetailsMapper rateDetailsMapper, StringRedisTemplate redisTemplate, ClientUserCenterService clientUserCenterService, ObjectMapper objectMapper) {
        this.rateRuleMapper = rateRuleMapper;
        this.rateDetailsMapper = rateDetailsMapper;
        this.redisTemplate = redisTemplate;
        this.clientUserCenterService = clientUserCenterService;
        this.objectMapper = objectMapper;
    }

    public Integer insert(RateRuleEntity entity) {
        rateRuleMapper.insertSelective(entity);
        return rateRuleMapper.maxId();
    }

    public void delete(Long id) {
        rateRuleMapper.deleteByPrimaryKey(id);
    }

    public void update(RateRuleEntity entity) {
        rateRuleMapper.updateByPrimaryKeySelective(entity);
    }

    public RateRuleEntity find(Long id) {
        return rateRuleMapper.selectByPrimaryKey(id);
    }

    public List<RateRuleEntity> findAll(Integer type) {
        return rateRuleMapper.selectByExample(type);
    }

    /**
     * 根据评级规则id对所有人员或组织进行评级
     */
    public void rating(Long rateRuleId) {
        Example example = new Example(RateDetailsEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("rateRuleId", rateRuleId);
        //查询该评级规则的每项细则
        List<RateDetailsEntity> ruleList = rateDetailsMapper.selectByExample(example);
        RateRuleEntity rateRuleEntity = rateRuleMapper.selectByPrimaryKey(rateRuleId);
        Map<Object, Object> map = new HashMap<>();
        if (1 == rateRuleEntity.getType()) {
            //对人员评级
            map = redisTemplate.opsForHash().entries("USER_SCORE_" + rateRuleEntity.getYear());
        } else {
            //对组织评级
            map = redisTemplate.opsForHash().entries("ORG_SCORE_" + rateRuleEntity.getYear());
        }

        Long sort = 1L;
        boolean flag = true;
        while (flag) {
            flag = false;
            List<RateDetailsEntity> param = new ArrayList<>();
            for (RateDetailsEntity entity : ruleList) {
                if (entity.getSort().equals(sort)) {
                    param.add(entity);
                    flag = true;
                }
            }
            if (flag) {
                matchRule(param, map);
                sort++;
            }
        }
    }

    public void matchRule(List<RateDetailsEntity> ruleList, Map<Object, Object> map) {
        List<Double> scoreList = map.values().stream().map(o -> Double.valueOf(o.toString())).sorted().collect(Collectors.toList());
        Set<Object> idList = map.keySet();
        List<Long> res = idList.stream().map(id -> Long.valueOf(id.toString())).collect(Collectors.toList());
        for (RateDetailsEntity entity : ruleList) {
            List<Long> tmp = new ArrayList<>();
            Double compareScore = 0D;
            switch (entity.getCompareContent()) {
                case 1:
                    //中位数
                    compareScore = scoreList.get(scoreList.size()/2);
                    if (null != entity.getCoefficient()) compareScore *= entity.getCoefficient();
                    break;
                case 2:
                    //排名
                    compareScore = scoreList.get((int) (scoreList.size()*entity.getCoefficient()));
                    break;
                case 3:
                    //第一名
                    compareScore = scoreList.get(scoreList.get(scoreList.size()-1).intValue());
                    if (null != entity.getCoefficient()) compareScore *= entity.getCoefficient();
                    break;
                default:
                    break;
            }
            switch (entity.getCompareType()) {
                case 1:
                    //大于
                    for (Object id : idList) {
                        if (Double.valueOf(map.get(id).toString()) > compareScore) {
                            tmp.add(Long.valueOf(id.toString()));
                        }
                    }
                    break;
                case 2:
                    //小于
                    for (Object id : idList) {
                        if (Double.valueOf(map.get(id).toString()) < compareScore) {
                            tmp.add(Long.valueOf(id.toString()));
                        }
                    }
                    break;
                case 3:
                    //大于等于
                    for (Object id : idList) {
                        if (Double.valueOf(map.get(id).toString()) >= compareScore) {
                            tmp.add(Long.valueOf(id.toString()));
                        }
                    }
                    break;
                case 4:
                    //小于等于
                    for (Object id : idList) {
                        if (Double.valueOf(map.get(id).toString()) <= compareScore) {
                            tmp.add(Long.valueOf(id.toString()));
                        }
                    }
                    break;
                case 5:
                    //等于
                    for (Object id : idList) {
                        if (Double.valueOf(map.get(id).toString()).equals(compareScore)) {
                            tmp.add(Long.valueOf(id.toString()));
                        }
                    }
                    break;
                default:
                    break;
            }
            res.retainAll(tmp);
        }
        //将评级结果缓存
        redisTemplate.opsForHash().put("RATING_" + ruleList.get(0).getRateRuleId(),
                ruleList.get(0).getRateName(), res);
    }

    public List<StarOrgVo> findStarOrg(Long orgId, Long rateRuleId) throws IOException {
        //解析redis获取该评级规则下的userIdList
        Set<Object> keys = redisTemplate.opsForHash().keys("RATING_" + rateRuleId);
        List<StarOrgVo> res = new ArrayList<>();
        //访问用户中心，查询组织以及下级列表
        Result<List<OrgWhereResultForm>> forms = clientUserCenterService.findOrgByWhere(orgId, 1, 99999);
        for (OrgWhereResultForm form : forms.getData()) {
            StarOrgVo vo = new StarOrgVo();
            vo.setOrgId(form.getOrgId());
            vo.setOrgName(form.getOrgName());
            Result<Page<UserOrgResultChildForm>> userList = clientUserCenterService.getUserList(form.getOrgId(), null, 1, 99999);
            Map<String, Integer> map = new HashMap<>();
            for (Object key : keys) {
                int count = 0;
                for (UserOrgResultChildForm resultChildForm : userList.getData()) {
                    List<Long> userIds = objectMapper.readValue(String.valueOf(redisTemplate.opsForHash().get("RATING_" + rateRuleId, key.toString())), new TypeReference<List<Long>>() {
                    });
                    if (userIds.contains(resultChildForm.getUserId())) count++;
                }
                map.put(key.toString(), count);
            }
            vo.setMap(map);
            res.add(vo);
        }
        return res;
    }
}
