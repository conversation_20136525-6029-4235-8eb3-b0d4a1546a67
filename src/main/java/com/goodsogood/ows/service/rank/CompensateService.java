package com.goodsogood.ows.service.rank;

import com.goodsogood.ows.mapper.rank.*;
import com.goodsogood.ows.model.db.rank.OrgScoreEntity;
import com.goodsogood.ows.model.db.rank.ScoreRuleEntity;
import com.goodsogood.ows.model.db.rank.UserScoreEntity;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import com.goodsogood.ows.model.vo.rank.Month;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.LoggingUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.joda.time.DateTime;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;
import java.util.UUID;

/**
 * Create by FuXiao on 2020/12/9
 * 自动打分补偿机制
 */
@Service
@Log4j2
public class CompensateService {
    private final ScoreRuleMapper scoreRuleMapper;
    private final SnapshotMapper snapshotMapper;
    private final UserScoreMapper userScoreMapper;
    private final OrgScoreMapper orgScoreMapper;
    private final UserService userService;
    private final OrgService orgService;
    private final RemoteModService remoteModService;
    private final ActivityAutomaticGradeService activityAutomaticGradeService;

    public CompensateService(ScoreRuleMapper scoreRuleMapper, SnapshotMapper snapshotMapper, UserScoreMapper userScoreMapper, OrgScoreMapper orgScoreMapper, UserService userService, OrgService orgService, RemoteModService remoteModService, ActivityAutomaticGradeService activityAutomaticGradeService) {
        this.scoreRuleMapper = scoreRuleMapper;
        this.snapshotMapper = snapshotMapper;
        this.userScoreMapper = userScoreMapper;
        this.orgScoreMapper = orgScoreMapper;
        this.userService = userService;
        this.orgService = orgService;
        this.remoteModService = remoteModService;
        this.activityAutomaticGradeService = activityAutomaticGradeService;
    }

    @Async("userCompensateExecutor")
    public void userCompensate(Long userId, Long scoreRuleId, Integer year) {
        List<Long> userIdList = new ArrayList<>();
        userIdList.add(userId);

        String token = UUID.randomUUID().toString();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.debug("进入人员打分补偿线程 date->{} : {} ", token, stopWatch.toString());
        List<UserScoreEntity> detailList = new ArrayList<>();

        String ruleDescription = scoreRuleMapper.findRuleDescription(scoreRuleId);

        try {
            ScoreResultVo scoreResultVo = new ScoreResultVo();
            stopWatch.split();
            switch (ruleDescription) {
                case "组织生活":
                    scoreResultVo = remoteModService.userBasicWorkScoreFromMeeting(3L, userIdList, year);
                    log.debug("人员自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "党费":
                    scoreResultVo = remoteModService.ppmdUserScore(3L, userIdList, year);
                    log.debug("人员自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "民主评议":
                    scoreResultVo = remoteModService.lastYearUserCommentResult(userIdList, year);
                    log.debug("人员自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "消费扶贫":
                    scoreResultVo = remoteModService.creditUserPovertyScore(3L, userIdList, year);
                    log.debug("人员自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "积分换书":
                    scoreResultVo = remoteModService.creditUserBookScore(3L, userIdList, year);
                    log.debug("人员自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "志愿者":
                    scoreResultVo = remoteModService.userVolunteerProjectScore(3L, userIdList, year);
                    log.debug("人员自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "学习":
                    scoreResultVo = remoteModService.userOnlineStudyScore(3L, userIdList, year);
                    log.debug("人员自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "一元捐":
                    scoreResultVo = remoteModService.userDonateScore(3L, userIdList, year);
                    log.debug("人员自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "互动":
                    scoreResultVo = remoteModService.userActivityLevelScore(3L, userIdList, year);
                    log.debug("人员自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "奖惩登记":
                    scoreResultVo = remoteModService.excellentPartyMemberScore(3L, userIdList, year);
                    log.debug("人员自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "双重组织生活":
                    scoreResultVo = remoteModService.userLeadScore(3L, userIdList, year);
                    log.debug("人员自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "讲党课":
                    scoreResultVo = remoteModService.leaderLectures(userIdList, year);
                    log.debug("人员自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                default:
                    break;
            }
            EnumMap<Month, Double> enumMap = scoreResultVo.getScoreResultMap().get(userId);
            int month = 12;
            if (year == new DateTime().getYear()) {
                month = new DateTime().getMonthOfYear() - 1;
            }
            for (int i = 1; i <= month; i++) {
                UserScoreEntity userScoreEntity = new UserScoreEntity();
                userScoreEntity.setPercentage(1D);
                userScoreEntity.setTopId(2L);
                userScoreEntity.setScoreRuleId(scoreRuleId);
                userScoreEntity.setScoreRuleName(ruleDescription);
                userScoreEntity.setUserId(userId);
                Double score = enumMap.get(Month.getEnumKey(i));
                userScoreEntity.setScore(score);

                String time = year + "-";
                if (i < 10) {
                    time += "0";
                }
                time += i;
                userScoreEntity.setTime(time);
                stopWatch.split();
                UserSnapshotEntity userSnapshot = snapshotMapper.findUserSnapshot(userId, time);
                int count = 0;
                while (count < 3 && null == userSnapshot) {
                    Thread.sleep(333);
                    userSnapshot = snapshotMapper.findUserSnapshot(userId, time);
                    count++;
                }
                log.debug("人员自动打分查询快照表 userId:{} date->{} : {} ", userId, token, stopWatch.getSplitTime());
                if (null == userSnapshot || null == userSnapshot.getUserName() || null == userSnapshot.getOrgId()) {
                    log.debug("未在快照表中查找到userId-->{}", userId);
                    continue;
                }
                userScoreEntity.setUserName(userSnapshot.getUserName());
                userScoreEntity.setOrgId(userSnapshot.getOrgId());

                detailList.add(userScoreEntity);
            }
            log.debug("人员自动打分补偿一次耗时 date->{} : {} ", token, stopWatch.getSplitTime());
        } catch (Exception e) {
            log.error("人员自动打分补偿发生错误-->" + token + ",子模块-->" + ruleDescription + ",userIdList-->" +
                    ArrayUtils.toString(userIdList, "")
                    + ",exception-->{}", e);
        }
        log.debug("人员自动打分补偿插入前耗时 date->{} : {} ", token, stopWatch.getSplitTime());
        userScoreMapper.insertList(detailList);
        log.debug("人员自动打分流水补偿添加完成 date->{} : {}, userIdList-->{}", token, stopWatch.getTime(), userIdList);
        stopWatch.stop();
        Thread.yield();
    }

    @Async("orgCompensateExecutor")
    public void orgCompensate(Long orgId, Long scoreRuleId, Integer year) {
        List<Long> orgIdList = new ArrayList<>();
        orgIdList.add(orgId);

        String token = UUID.randomUUID().toString();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.debug("进入组织自动打分补偿线程 date->{} : {} ", token, stopWatch.toString());

        List<OrgScoreEntity> detailList = new ArrayList<>();

        String ruleDescription = scoreRuleMapper.findRuleDescription(scoreRuleId);

        try {
            ScoreResultVo scoreResultVo = new ScoreResultVo();
            stopWatch.split();
            switch (ruleDescription) {
                case "组织信息完整性":
                    scoreResultVo = orgService.getOrgScoreList(orgIdList, year);
                    log.debug("组织自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "党员信息完整度":
                    scoreResultVo = userService.getUserScoreList(orgIdList, year);
                    log.debug("组织自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "党支部组织生活开展情况":
                    scoreResultVo = remoteModService.orgBasicWorkScoreFromMeeting(3L, orgIdList, year);
                    log.debug("组织自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "党费收缴情况":
                    scoreResultVo = remoteModService.ppmdOrgScore(3L, orgIdList, year);
                    log.debug("组织自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "民主评议结果":
                    scoreResultVo = remoteModService.lastYearOrgCommentResult(orgIdList, year);
                    log.debug("组织自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "述职评议结果":
                    scoreResultVo = remoteModService.orgDebriefReviewScoreFromMeeting(3L, orgIdList, year);
                    log.debug("组织自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "领导班子成员支部联系点情况":
                    scoreResultVo = remoteModService.orgCheckLeaderBaseInfo(orgIdList, year);
                    log.debug("组织自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "届次换届":
                    scoreResultVo = remoteModService.orgCheckPeriodInfo(orgIdList, year);
                    log.debug("组织自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "消费扶贫":
                    scoreResultVo = remoteModService.creditOrgPovertyScore(3L, orgIdList, year);
                    log.debug("组织自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "在线学习":
                    scoreResultVo = remoteModService.orgOnlineStudyScore(3L, orgIdList, year);
                    log.debug("组织自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "积分换书":
                    scoreResultVo = remoteModService.creditOrgBookScore(3L, orgIdList, year);
                    log.debug("组织自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "志愿活动":
                    scoreResultVo = remoteModService.orgVolunteerProjectScore(3L, orgIdList, year);
                    log.debug("组织自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "捐款":
                    scoreResultVo = activityAutomaticGradeService.orgDonateScore(3L, orgIdList, year);
                    log.debug("组织自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                case "奖惩登记":
                    scoreResultVo = remoteModService.orgCommendPenalizeScoreFromMeeting(3L, orgIdList, year);
                    log.debug("组织自动打分补偿{} date->{} : {} ", ruleDescription, token, stopWatch.getSplitTime());
                    break;
                default:
                    break;
            }

            EnumMap<Month, Double> enumMap = scoreResultVo.getScoreResultMap().get(orgId);
            int month = 12;
            if (year == new DateTime().getYear()) {
                month = new DateTime().getMonthOfYear() - 1;
            }
            for (int i = 1; i <= month; i++) {
                OrgScoreEntity orgScoreEntity = new OrgScoreEntity();
                orgScoreEntity.setPercentage(1D);
                orgScoreEntity.setTopId(1L);
                orgScoreEntity.setScoreRuleId(scoreRuleId);
                orgScoreEntity.setScoreRuleName(ruleDescription);
                orgScoreEntity.setOrgId(orgId);
                Double score = enumMap.get(Month.getEnumKey(i));
                orgScoreEntity.setScore(score);

                String time = year + "-";
                if (i < 10) {
                    time += "0";
                }
                time += i;
                orgScoreEntity.setTime(time);
                stopWatch.split();
                //自旋3次
                OrgSnapshotEntity orgSnapshot = snapshotMapper.findOrgSnapshot(orgId, time);
                int count = 0;
                while (count < 3 && null == orgSnapshot) {
                    Thread.sleep(333);
                    orgSnapshot = snapshotMapper.findOrgSnapshot(orgId, time);
                    count++;
                }
                log.debug("组织自动打分查询快照表 orgId:{} date->{} : {} ", orgId, token, stopWatch.getSplitTime());
                if (null == orgSnapshot || null == orgSnapshot.getOrgName()) {
                    log.debug("未在快照表中查找到orgId-->{}", orgId);
                    continue;
                }
                orgScoreEntity.setOrgName(orgSnapshot.getOrgName());

                detailList.add(orgScoreEntity);
            }
            log.debug("组织自动打分补偿一次耗时 date->{} : {} ", token, stopWatch.getSplitTime());
        } catch (Exception e) {
            if (e instanceof LoggingUtils.WrapLoggingException) {
                log.error("组织自动打分补偿发生错误-->" + token + ",子模块-->" + ruleDescription +
                        ",method_tracker_id-->" + ((LoggingUtils.WrapLoggingException) e).getMethodTrackerId() + ",orgIdList-->" +
                        ArrayUtils.toString(orgIdList, "")
                        + ",exception-->{}", ((LoggingUtils.WrapLoggingException) e).getException());
            } else {
                log.error("组织自动打分补偿发生错误-->" + token + ",子模块-->" + ruleDescription + ",orgIdList-->" +
                        ArrayUtils.toString(orgIdList, "")
                        + ",exception-->{}", e);
            }
        }
        log.debug("组织自动打分补偿插入前耗时 date->{} : {} ", token, stopWatch.getSplitTime());
        orgScoreMapper.insertList(detailList);
        log.debug("组织自动打分流水补偿添加完成,date->{},orgIdList-->{}", stopWatch.getTime(), orgIdList);
        stopWatch.stop();
        Thread.yield();
    }
}
