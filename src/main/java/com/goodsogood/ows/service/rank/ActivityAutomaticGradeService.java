package com.goodsogood.ows.service.rank;

import com.goodsogood.ows.configuration.ScoreConfig;
import com.goodsogood.ows.mapper.activity.DonateUserMapper;
import com.goodsogood.ows.model.vo.rank.OrgJoinTotalVO;
import com.goodsogood.ows.model.vo.rank.Month;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.model.vo.rank.UserJoinTotalVO;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.SqlJointUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 活动中心自动打分服务类
 *
 * <AUTHOR>
 * @date 2020/11/7
 */
@Service
@Log4j2
public class ActivityAutomaticGradeService {

    @Value("${user-option.retireCode}")
    private String retireCode;

    @Value("${user-option.politicalCode}")
    private String politicalCode;

    private final AutomaticGradeServiceComponent automaticGradeServiceComponent;
    private final ScoreConfig scoreConfig;
    private final DonateUserMapper donateUserMapper;

    @Autowired
    public ActivityAutomaticGradeService(AutomaticGradeServiceComponent automaticGradeServiceComponent,
                                         ScoreConfig scoreConfig,
                                         DonateUserMapper donateUserMapper) {
        this.automaticGradeServiceComponent = automaticGradeServiceComponent;
        this.scoreConfig = scoreConfig;
        this.donateUserMapper = donateUserMapper;
    }

    /**
     * 组织积分捐赠得分
     *
     * @param regionId 区域id
     * @param orgIds   组织id
     * @param year     年
     * @return
     */
    public ScoreResultVo orgDonateScore(Long regionId, List<Long> orgIds, Integer year) {
        return automaticGradeServiceComponent.getOrgScore(
                regionId,
                orgIds,
                year,
                (param) -> {
                    Map<String, String> params = automaticGradeServiceComponent.buildParamMapUseSqlTemplateReplace(param);
                    return SqlJointUtil.joint(
                            DonateUserMapper.JOIN_DONATE_USER_TOTAL_SQL_TEMPLATE,
                            params
                    );
                },
                (sqlList) -> {
                    String joint = SqlJointUtil.joint(sqlList, true);
                    List<OrgJoinTotalVO> orgJoinTotalVOS = donateUserMapper.joinDonateUserTotal(joint);
                    EnumMap<Month, Double> sqlResult = new EnumMap(Month.class);
                    orgJoinTotalVOS.forEach(k -> {
                        Double score = automaticGradeServiceComponent.getScoreByRule(
                                scoreConfig.getOrgScore().getDonate(),
                                k.getDateMonth(),
                                k.getOrgId(),
                                k.getJoinTotal()
                        );
                        if (score != null) {
                            sqlResult.put(Month.getEnumKey(Integer.parseInt(k.getDateMonth().split("-")[1])), score);
                        }
                    });
                    return sqlResult;
                });
    }

    /**
     * 用户参加积分捐赠得分
     *
     * @param regionId 区县id
     * @param userIds  用户id集合
     * @param year     年
     * @return
     */
    public ScoreResultVo userDonateScore(Long regionId,
                                         List<Long> userIds,
                                         Integer year) {
        return automaticGradeServiceComponent.getUserScore(
                regionId,
                userIds,
                year,
                (param) -> {
                    String startTime = DateUtils.firstDayOfMonthByDate(param.getDateMonth());
                    String endTime = DateUtils.lastDayOfMonthByDate(param.getDateMonth());
                    List<UserJoinTotalVO> userJoinTotalVOS = donateUserMapper.joinDonateTotal(param.getUserIds(), startTime, endTime);
                    if (userJoinTotalVOS != null && !userJoinTotalVOS.isEmpty()) {
                        Map<Long, Double> result = new HashMap<>();
                        userJoinTotalVOS.forEach(t -> {
                            if (t.getUserId() != null && t.getJoinTotal() != null) {
                                Double score = automaticGradeServiceComponent.getScoreByRule(scoreConfig.getUserScore().getDonate(), t.getJoinTotal());
                                if (score != null) {
                                    result.put(t.getUserId(), score);
                                }
                            }
                        });
                        return result;
                    }
                    return null;
                });
    }
}
