package com.goodsogood.ows.service.rank;

import com.goodsogood.ows.configuration.ScoreConfig;
import com.goodsogood.ows.mapper.volunteer.VolunteerProjectMapper;
import com.goodsogood.ows.model.vo.rank.Month;
import com.goodsogood.ows.model.vo.rank.OrgJoinTotalVO;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.model.vo.rank.UserJoinTotalVO;
import com.goodsogood.ows.service.user.OrgSnapshotService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.SqlJointUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 积分中心自动打分服务类
 *
 * <AUTHOR>
 * @date 2020/11/7
 */
@Service
public class VolunteerAutomaticGradeServiceComponent {

    @Value("${user-option.retireCode}")
    private String retireCode;

    @Value("${user-option.politicalCode}")
    private String politicalCode;

    private final AutomaticGradeServiceComponent automaticGradeServiceComponent;
    private final ScoreConfig scoreConfig;
    private final VolunteerProjectMapper volunteerProjectMapper;
    private final OrgSnapshotService orgSnapshotService;

    @Autowired
    public VolunteerAutomaticGradeServiceComponent(ScoreConfig scoreConfig,
                                                   VolunteerProjectMapper volunteerProjectMapper,
                                                   AutomaticGradeServiceComponent automaticGradeServiceComponent,
                                                   OrgSnapshotService orgSnapshotService) {
        this.scoreConfig = scoreConfig;
        this.volunteerProjectMapper = volunteerProjectMapper;
        this.automaticGradeServiceComponent = automaticGradeServiceComponent;
        this.orgSnapshotService = orgSnapshotService;
    }

    /**
     * 组织志愿活动得分
     *
     * @param regionId 区县id
     * @param orgIds   组织id集合
     * @param year     年
     * @return
     */
    public ScoreResultVo orgVolunteerProjectScore(Long regionId, List<Long> orgIds, Integer year) {
        return automaticGradeServiceComponent.getOrgScore(
                regionId,
                orgIds,
                year,
                (param) -> {
                    Map<String, String> params = automaticGradeServiceComponent.buildParamMapUseSqlTemplateReplace(param);
                    return SqlJointUtil.joint(
                            VolunteerProjectMapper.JOIN_VOLUNTEER_PROJECT_USER_TOTAL_SQL_TEMPLATE,
                            params
                    );
                },
                (sqlList) -> {
                    String joint = SqlJointUtil.joint(sqlList, true);
                    List<OrgJoinTotalVO> orgJoinTotalVOS = volunteerProjectMapper.joinVolunteerProjectUserTotal(joint);
                    EnumMap<Month, Double> sqlResult = new EnumMap(Month.class);
                    orgJoinTotalVOS.forEach(k -> {
                        Double score = automaticGradeServiceComponent.getScoreByRule(
                                scoreConfig.getOrgScore().getVolunteer(),
                                k.getDateMonth(),
                                k.getOrgId(),
                                k.getJoinTotal()
                        );
                        if (score != null) {
                            sqlResult.put(Month.getEnumKey(Integer.parseInt(k.getDateMonth().split("-")[1])), score);
                        }
                    });
                    return sqlResult;
                });
    }

    /**
     * 用户参加志愿活动得分
     *
     * @param regionId 区县id
     * @param userIds  用户id集合
     * @param year     年
     * @return
     */
    public ScoreResultVo userVolunteerProjectScore(Long regionId,
                                                   List<Long> userIds,
                                                   Integer year) {

        return automaticGradeServiceComponent.getUserScore(
                regionId,
                userIds,
                year,
                (param) -> {
                    String startTime = DateUtils.firstDayOfMonthByDate(param.getDateMonth());
                    String endTime = DateUtils.lastDayOfMonthByDate(param.getDateMonth());
                    List<UserJoinTotalVO> userJoinTotalVOS = volunteerProjectMapper.joinVolunteerProjectTotal(param.getUserIds(), startTime, endTime, param.getRegionId());
                    if (userJoinTotalVOS != null && !userJoinTotalVOS.isEmpty()) {
                        Map<Long, Double> result = new HashMap<>();
                        userJoinTotalVOS.forEach(t -> {
                            if (t.getUserId() != null && t.getJoinTotal() != null) {
                                Double score = automaticGradeServiceComponent.getScoreByRule(scoreConfig.getUserScore().getVolunteer(), t.getJoinTotal());
                                if (score != null) {
                                    result.put(t.getUserId(), score);
                                }
                            }
                        });
                        return result;
                    }
                    return null;
                });
    }


}
