package com.goodsogood.ows.service.rank;

import com.goodsogood.ows.mapper.rank.RateDetailsMapper;
import com.goodsogood.ows.model.db.rank.RateDetailsEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * Create by FuXiao on 2020/10/19
 */
@Service
@Log4j2
public class RateDetailsService {
    private final RateDetailsMapper rateDetailsMapper;

    public RateDetailsService(RateDetailsMapper rateDetailsMapper) {
        this.rateDetailsMapper = rateDetailsMapper;
    }

    public void insert(RateDetailsEntity entity) {
        rateDetailsMapper.insertSelective(entity);
    }

    public void delete(Long id) {
        Example example = new Example(RateDetailsEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("rateRuleId", id);
        rateDetailsMapper.deleteByExample(example);
    }

    public void update(RateDetailsEntity entity) {
        rateDetailsMapper.updateByPrimaryKeySelective(entity);
    }

    public RateDetailsEntity findDetails(Long id) {
        return rateDetailsMapper.selectByPrimaryKey(id);
    }

    public List<RateDetailsEntity> findAll(Long rateRuleId) {
        Example example = new Example(RateDetailsEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("rateRuleId", rateRuleId);
        return rateDetailsMapper.selectByExample(example);
    }
}
