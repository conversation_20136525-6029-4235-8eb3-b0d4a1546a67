package com.goodsogood.ows.service.rank;

import com.goodsogood.ows.mapper.rank.TopMapper;
import com.goodsogood.ows.model.db.rank.TopEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * Create by FuXiao on 2020/11/2
 */
@Service
@Log4j2
public class TopService {
    private final TopMapper topMapper;

    public TopService(TopMapper topMapper) {
        this.topMapper = topMapper;
    }

    /**
     * 新增顶部类型
     */
    public void insert(TopEntity entity) {
        topMapper.insert(entity);
    }

    /**
     * 删除顶部类型
     */
    public void delete(Long topId) {
        topMapper.deleteByPrimaryKey(topId);
    }

    /**
     * 修改顶部类型
     */
    public void update(TopEntity entity) {
        topMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 根据分类查询顶部类型
     */
    public List<TopEntity> find(Integer type) {
        Example example = new Example(TopEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("type", type);
        return topMapper.selectByExample(example);
    }
}
