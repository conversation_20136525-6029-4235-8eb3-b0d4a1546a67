package com.goodsogood.ows.service.rank;

import com.goodsogood.ows.mapper.rank.ScoreRuleMapper;
import com.goodsogood.ows.mapper.rank.UserScoreDetailMapper;
import com.goodsogood.ows.mapper.rank.UserScoreMapper;
import com.goodsogood.ows.model.db.rank.UserScoreDetailEntity;
import com.goodsogood.ows.model.db.rank.UserScoreEntity;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

/**
 * Create by FuXiao on 2020/10/19
 */
@Service
@Log4j2
public class UserScoreService {
    private final StringRedisTemplate redisTemplate;
    private final UserScoreMapper userScoreMapper;
    private final UserScoreDetailMapper userScoreDetailMapper;
    private final ScoreRuleMapper scoreRuleMapper;
    private final ClientUserCenterService clientUserCenterService;

    public UserScoreService(StringRedisTemplate redisTemplate, UserScoreMapper userScoreMapper, UserScoreDetailMapper userScoreDetailMapper, ScoreRuleMapper scoreRuleMapper, ClientUserCenterService clientUserCenterService) {
        this.redisTemplate = redisTemplate;
        this.userScoreMapper = userScoreMapper;
        this.userScoreDetailMapper = userScoreDetailMapper;
        this.scoreRuleMapper = scoreRuleMapper;
        this.clientUserCenterService = clientUserCenterService;
    }

    /**
     * 计算人员分数存储到redis
     */
    public void calculateScore(Long userId) {
        List<Long> yearList = userScoreMapper.findYear();
        for (Long year : yearList) {
            Double score = 0D;
            List<UserScoreEntity> list = this.findByUserIdYear(userId, year);
            for (UserScoreEntity entity : list) {
                if (entity.getScore().equals(-9999.0D)) {
                    score = 0D;
                    break;
                }
                if (null != entity.getScore() && 0L != entity.getScore()) {
                    Double percent = scoreRuleMapper.findPerCent(entity.getScoreRuleId());
                    score += entity.getScore() * percent;
                }
            }
            redisTemplate.opsForHash().put("USER_SCORE_" + year, userId.toString(), String.format("%.1f", score));
        }
    }

    /**
     * 根据userId查询
     */
    public List<UserScoreEntity> findByUserIdYear(Long userId, Long year) {
        Example example = new Example(UserScoreEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("year", year);
        return userScoreMapper.selectByExample(example);
    }

    /**
     * 给人员打分
     */
    public void score(List<UserScoreDetailEntity> entities) {
        OrganizationBase organizationBase = clientUserCenterService.findOrgById(entities.get(0).getOrgId());
        String orgLevel = organizationBase.getOrgLevel();
        //先清空该用户得分
        Example example = new Example(UserScoreEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", entities.get(0).getUserId());
        userScoreMapper.deleteByExample(example);
        for (UserScoreDetailEntity entity : entities) {
            //先存流水
            entity.setCreateTime(new Date());
            entity.setOrgLevel(orgLevel);
            userScoreDetailMapper.insert(entity);
            //再新增
            UserScoreEntity userScore = new UserScoreEntity();
            userScore.setUserId(entity.getUserId());
            userScore.setUserName(entity.getUserName());
            userScore.setOrgId(entity.getOrgId());
            userScore.setTime(entity.getYear().toString());
            userScore.setScore(entity.getScore());
            userScore.setTopId(entity.getTopId());
            userScore.setScoreRuleId(entity.getScoreRuleId());
            userScoreMapper.insert(userScore);
        }
    }

    /**
     * 查询人员每项得分
     */
    public Map<Long, List<UserScoreEntity>> findUserScore(Long userId, Long year) {
        Example example = new Example(UserScoreEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("year", year);
        List<UserScoreEntity> entities = userScoreMapper.selectByExample(example);
        Map<Long, List<UserScoreEntity>> map = new HashMap<>();
        for (UserScoreEntity entity : entities) {
            List<UserScoreEntity> tmp = new ArrayList<>();
            if (map.containsKey(entity.getTopId())) { tmp = map.get(entity.getTopId()); }
            tmp.add(entity);
            map.put(entity.getTopId(), tmp);
        }
        return map;
    }
}
