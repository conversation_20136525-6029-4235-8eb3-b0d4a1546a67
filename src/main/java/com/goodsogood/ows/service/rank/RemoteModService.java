package com.goodsogood.ows.service.rank;

import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.ScoreConfig;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.mapper.user.LeaderMapper;
import com.goodsogood.ows.mapper.user.OrgPeriodMapper;
import com.goodsogood.ows.model.vo.rank.Month;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.model.vo.user.RankPeriodIsEquleFrom;
import com.goodsogood.ows.service.activity.ActivityScoreService;
import com.goodsogood.ows.service.meeting.MeetingScoreService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.ListUtils;
import com.goodsogood.ows.utils.LoggingUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * Create by FuXiao on 2020/11/5
 */
@Service
@Log4j2
public class RemoteModService {

    private final PpmdAutomaticGradeService ppmdAutomaticGradeService;
    private final CreditAutomaticGradeService creditAutomaticGradeService;
    private final VolunteerAutomaticGradeServiceComponent volunteerAutomaticGradeService;
    private final ActivityAutomaticGradeService activityAutomaticGradeService;
    private final MeetingScoreService meetingScoreService;
    private final ActivityScoreService activityScoreService;
    private final ScoreConfig scoreConfig;
    private final MeetingMapper meetingMapper;
    private final OrgTypeConfig orgTypeConfig;
    private final LeaderMapper leaderMapper;
    private final OrgPeriodMapper orgPeriodMapper;
    private final RemoteRuleWryService remoteRuleWryService;

    @Autowired
    public RemoteModService(PpmdAutomaticGradeService ppmdAutomaticGradeService,
                            CreditAutomaticGradeService creditAutomaticGradeService,
                            VolunteerAutomaticGradeServiceComponent volunteerAutomaticGradeService,
                            ActivityAutomaticGradeService activityAutomaticGradeService,
                            MeetingScoreService meetingScoreService, ActivityScoreService activityScoreService, ScoreConfig scoreConfig,
                            MeetingMapper meetingMapper,
                            OrgTypeConfig orgTypeConfig,
                            LeaderMapper leaderMapper,
                            OrgPeriodMapper orgPeriodMapper,
                            RemoteRuleWryService remoteRuleWryService) {
        this.ppmdAutomaticGradeService = ppmdAutomaticGradeService;
        this.creditAutomaticGradeService = creditAutomaticGradeService;
        this.volunteerAutomaticGradeService = volunteerAutomaticGradeService;
        this.activityAutomaticGradeService = activityAutomaticGradeService;
        this.meetingScoreService = meetingScoreService;
        this.activityScoreService = activityScoreService;
        this.scoreConfig = scoreConfig;
        this.meetingMapper = meetingMapper;
        this.orgTypeConfig = orgTypeConfig;
        this.leaderMapper = leaderMapper;
        this.orgPeriodMapper = orgPeriodMapper;
        this.remoteRuleWryService = remoteRuleWryService;
    }

    /**
     * 党费平台组织自动评分
     *
     * @param regionId
     * @param orgIdList
     * @param year      tc 2020-11-06
     * @return
     */
    public ScoreResultVo ppmdOrgScore(Long regionId, List<Long> orgIdList, Integer year) {
        return ppmdAutomaticGradeService.ppmdOrgScore(regionId, orgIdList, year);
    }

    /**
     * 党费平台党员自动评分
     *
     * @param regionId
     * @param userIdList
     * @param year       tc 2020-11-06
     * @return
     */
    public ScoreResultVo ppmdUserScore(Long regionId, List<Long> userIdList, Integer year) {
        return ppmdAutomaticGradeService.ppmdUserScore(regionId, userIdList, year);
    }

    /**
     * 积分平台组织自动评分，消费扶贫
     *
     * @param regionId
     * @param orgIdList
     * @param year      tc 2020-11-06
     * @return
     */
    public ScoreResultVo creditOrgPovertyScore(Long regionId, List<Long> orgIdList, Integer year) {
        return creditAutomaticGradeService.creditOrgPovertyScore(regionId, orgIdList, year);
    }

    /**
     * 积分平台组织自动评分，积分换书
     *
     * @param regionId
     * @param orgIdList
     * @param year      tc 2020-11-06
     * @return
     */
    public ScoreResultVo creditOrgBookScore(Long regionId, List<Long> orgIdList, Integer year) {
        return creditAutomaticGradeService.creditOrgBookScore(regionId, orgIdList, year);
    }

    /**
     * 积分平台党员自动评分，消费扶贫
     *
     * @param regionId
     * @param userIdList
     * @param year       tc 2020-11-06
     * @return
     */
    public ScoreResultVo creditUserPovertyScore(Long regionId, List<Long> userIdList, Integer year) {
        return creditAutomaticGradeService.creditUserPovertyScore(regionId, userIdList, year);
    }

    /**
     * 积分平台党员自动评分，积分换书
     *
     * @param regionId
     * @param userIdList
     * @param year       tc 2020-11-06
     * @return
     */
    public ScoreResultVo creditUserBookScore(Long regionId, List<Long> userIdList, Integer year) {
        return creditAutomaticGradeService.creditUserBookScore(regionId, userIdList, year);
    }

    /**
     * 组织志愿活动得分
     *
     * @param regionId 区域id
     * @param orgId    组织id
     * @param year     年
     * @return
     */
    public ScoreResultVo orgVolunteerProjectScore(Long regionId, List<Long> orgId, Integer year) {
        return LoggingUtils.wrap(
                "组织志愿活动打分",
                () -> volunteerAutomaticGradeService.orgVolunteerProjectScore(regionId, orgId, year),
                true,
                orgId,
                year
        );
    }

    /**
     * 用户参加志愿活动得分
     *
     * @param regionId 区县id
     * @param userIds  用户id集合
     * @param year     年
     * @return
     */
    public ScoreResultVo userVolunteerProjectScore(Long regionId,
                                                   List<Long> userIds,
                                                   Integer year) {
        return LoggingUtils.wrap(
                "用户志愿活动打分",
                () -> volunteerAutomaticGradeService.userVolunteerProjectScore(regionId, userIds, year),
                true,
                userIds,
                year
        );
    }

    /**
     * 组织在线学习得分
     *
     * @param regionId 区域id
     * @param orgIds   组织id
     * @param year     年
     * @return
     */
    public ScoreResultVo orgOnlineStudyScore(Long regionId, List<Long> orgIds, Integer year) {
        return LoggingUtils.wrap(
                "组织在线学习打分",
                () -> creditAutomaticGradeService.orgOnlineStudyScore(regionId, orgIds, year),
                true,
                orgIds,
                year
        );
    }

    /**
     * 用户在线学习得分
     *
     * @param regionId 区县id
     * @param userIds  用户id集合
     * @param year     年
     * @return
     */
    public ScoreResultVo userOnlineStudyScore(Long regionId,
                                              List<Long> userIds,
                                              Integer year) {
        return LoggingUtils.wrap(
                "用户在线学习打分",
                () -> creditAutomaticGradeService.userOnlineStudyScore(regionId, userIds, year),
                true,
                userIds,
                year
        );
    }

    /**
     * 组织积分捐赠得分
     *
     * @param regionId 区域id
     * @param orgIds   组织id
     * @param year     年
     * @return
     */
    public ScoreResultVo orgDonateScore(Long regionId, List<Long> orgIds, Integer year) {
        return LoggingUtils.wrap(
                "组织积分捐赠打分",
                () -> activityAutomaticGradeService.orgDonateScore(regionId, orgIds, year),
                true,
                orgIds,
                year
        );
    }

    /**
     * 用户参加积分捐赠得分
     *
     * @param regionId 区县id
     * @param userIds  用户id集合
     * @param year     年
     * @return
     */
    public ScoreResultVo userDonateScore(Long regionId,
                                         List<Long> userIds,
                                         Integer year) {

        return LoggingUtils.wrap(
                "用户积分捐赠打分",
                () -> activityAutomaticGradeService.userDonateScore(regionId, userIds, year),
                true,
                userIds,
                year
        );
    }

    /**
     * 组织
     * 基础工作-领导班子成员支部联系点情况
     *
     * @param orgId
     * @param queryDate
     * @return
     */
    public Double orgCheckLeaderBaseInfo(Long orgId, String queryDate) {
        return checkLeaderContactEqual(orgId, queryDate) + isCreateLeaderContact(orgId, queryDate);
    }


    public ScoreResultVo orgCheckLeaderBaseInfo(List<Long> orgIds, Integer year) {
        //支部联系点设置是否合理（联系点与所在支部是否一致）
        ScoreResultVo scoreResultVo1 = remoteRuleWryService.queryOnlyByYearMonth(x -> true,
                RemoteRuleWryService.RemoteRuleWryQuery.builder()
                        .ids(orgIds)
                        .queryYearDate(year.toString())
                        .build(),
                query -> leaderMapper.checkLeaderContactEqual2(query.getIdStr(), query.getQueryYearMonthDate()),
                addFunction -> addFunction.getABoolean() != null && addFunction.getABoolean() ?
                        scoreConfig.getOrgScore().getCheckLeaderContactEqual() : 0.0);
        //是否设置支部联系点（有/无？）
        ScoreResultVo scoreResultVo2 = remoteRuleWryService.queryOnlyByYearMonth(x -> true,
                RemoteRuleWryService.RemoteRuleWryQuery.builder()
                        .ids(orgIds)
                        .queryYearDate(year.toString())
                        .build(),
                query -> leaderMapper.isCreateLeaderContact2(query.getIdStr(), query.getQueryYearMonthDate()),
                addFunction -> addFunction.getABoolean() != null && addFunction.getABoolean() ?
                        scoreConfig.getOrgScore().getIsCreateLeaderContact() : 0.0);

        return RemoteRuleWryService.mergeScoreResultVo(orgIds, scoreResultVo1, scoreResultVo2);
    }


    /**
     * 组织
     * 组织建设-届次换届
     *
     * @param orgId
     * @param queryDate
     * @return
     */
    public Double orgCheckPeriodInfo(Long orgId, String queryDate) {
        double result = 0.0;
        result += isCreatePeriod(orgId, queryDate);
        result += checkTopOrgRepeatExistPosition(orgId, queryDate);
        result += checkTopOrgLackPosition(orgId, queryDate);
        result += checkPeriodPerson1(orgId, queryDate);
        result += checkPeriodPerson2(orgId, queryDate);
        return result;
    }

    public ScoreResultVo orgCheckPeriodInfo(List<Long> orgIds, Integer year) {
        RemoteRuleWryService.RemoteRuleWryQuery build = RemoteRuleWryService.RemoteRuleWryQuery.builder()
                .ids(orgIds)
                .queryYearDate(year.toString())
                .build();
        //组织 组织建设 届次换届 所有基层党组织 是否按期换届
        ScoreResultVo scoreResultVo1 = remoteRuleWryService.queryOnlyByYearMonth(
                x -> true,
                build,
                query -> orgPeriodMapper.isCreatePeriod2(query.getIdStr(), query.getQueryYearMonthDate(),
                        ListUtils.join(",", orgTypeConfig.getBasicChild())),
                result -> result.getABoolean() != null && Boolean.TRUE.equals(result.getABoolean()) ?
                        scoreConfig.getOrgScore().getIsCreatePeriod() : 0.0
        );
        //组织 组织建设 届次换届 机关党委 机关党委书记、专职副书记任职年限是否超过两届 测试 4685 2020-10
        ScoreResultVo scoreResultVo2 = remoteRuleWryService.queryOnlyByYearMonthId(
                filter -> true,
                build,
                this::getCheckTopOrgRepeatExistPositionQueryResult,
                result -> result.getABoolean() != null && Boolean.TRUE.equals(result.getABoolean()) ?
                        scoreConfig.getOrgScore().getCheckTopOrgRepeatExistPosition() : 0.0

        );
        //组织 组织建设 届次换届 机关党委 机关党委书记、专职副书记是否出现空缺
        ScoreResultVo scoreResultVo3 = remoteRuleWryService.queryOnlyByYearMonth(
                filter -> true,
                build,
                query -> orgPeriodMapper.checkTopOrgLackPosition2(query.getIdStr(), query.getQueryYearMonthDate()),
                result -> result.getABoolean() != null && Boolean.TRUE.equals(result.getABoolean()) ?
                        scoreConfig.getOrgScore().getCheckTopOrgLackPosition() : 0.0
        );
        //组织 组织建设 届次换届 党支部 支部人数符合规定（未超过50人或小于3人）
        ScoreResultVo scoreResultVo4 = remoteRuleWryService.queryOnlyByYearMonth(
                filter -> true,
                build,
                query -> orgPeriodMapper.newCheckPeriodPerson1(query.getIdStr(), query.getQueryYearMonthDate(), ListUtils.join(",", orgTypeConfig.getBranchChild())),
                result -> result.getABoolean() != null && Boolean.TRUE.equals(result.getABoolean()) ?
                        scoreConfig.getOrgScore().getCheckPeriodPerson1() : 0.0
        );
        //组织 组织建设 届次换届 党支部 支委会设置符合规定（超过7人且成立支委会）
        ScoreResultVo scoreResultVo5 = remoteRuleWryService.queryOnlyByYearMonth(
                filter -> true,
                build,
                query -> orgPeriodMapper.newCheckPeriodPerson2(query.getIdStr(), query.getQueryYearMonthDate(), ListUtils.join(",", orgTypeConfig.getBranchChild())),
                result -> result.getABoolean() != null && Boolean.TRUE.equals(result.getABoolean()) ?
                        scoreConfig.getOrgScore().getCheckPeriodPerson2() : 0.0
        );
        return RemoteRuleWryService.mergeScoreResultVo(orgIds, scoreResultVo1, scoreResultVo2, scoreResultVo3, scoreResultVo4, scoreResultVo5);
    }

    @Nullable
    private List<RemoteRuleWryService.QueryResult> getCheckTopOrgRepeatExistPositionQueryResult(RemoteRuleWryService.RemoteRuleWryQuery query) {
        List<RankPeriodIsEquleFrom> list = orgPeriodMapper.getPeriodForTime(query.getId(), query.getQueryYearMonthDate());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Date date = DateUtils.stringToDate(query.getQueryYearMonthDate(), "yyyy-MM");
        Long unixSecond = date.getTime() / 1000;
        Long newPeriodId = null;
        Long beforePeriodId = null;
        //找到符合查询时间最新的届次和前一次届次
        for (RankPeriodIsEquleFrom rankPeriodIsEquleFrom : list) {
            if (newPeriodId != null) {
                beforePeriodId = rankPeriodIsEquleFrom.getPeriodId();
                break;
            }
            if (rankPeriodIsEquleFrom.getUnixStartTime() <= unixSecond
                    && rankPeriodIsEquleFrom.getUnixEndTime() >= unixSecond) {
                newPeriodId = rankPeriodIsEquleFrom.getPeriodId();
            }
        }
        boolean isAdd = false;
        if (newPeriodId != null && beforePeriodId != null) {
            Long count = orgPeriodMapper.checkPeriodMemberIsRepeat(newPeriodId, beforePeriodId);
            if (count == null || count.equals(0L)) {
                isAdd = true;
            }
        } else if (newPeriodId != null) {
            isAdd = true;
        }
        if (isAdd) {
            RemoteRuleWryService.QueryResult queryResult = new RemoteRuleWryService.QueryResult();
            queryResult.setId(query.getId());
            queryResult.setABoolean(true);
            ArrayList<RemoteRuleWryService.QueryResult> queryResultArrayList = new ArrayList<>();
            queryResultArrayList.add(queryResult);
            return queryResultArrayList;
        }
        return null;
    }

    /**
     * 组织 基础工作 民主评议结果 所有基层党组织 是否已完成上一年度民主评议结果录入
     * 判断当前组织是否录入了上一年度全部非离退休党员的民主评议结果，是则得该项分；每年12月判断一次，其他月份不判断也不得该项
     *
     * @param orgId     查询组织id
     * @param queryDate 当前查询月 yyyy-MM
     */
    public Double lastYearOrgCommentResult(Long orgId, String queryDate) {
        String[] split = queryDate.split("-");
        if (new Integer(split[1]) % 12 != 0 || DateUtils.isAfterDateMonth(queryDate)) {
            return 0d;
        }
        String join = ListUtils.join(",", orgTypeConfig.getBasicChild());
        Boolean aBoolean = meetingMapper.lastYearOrgCommentResult(orgId, new Integer(split[0]) - 1, split[1], join);
        if (aBoolean != null && aBoolean) {
            return scoreConfig.getOrgScore().getLastYearOrgCommentResult();
        } else {
            return 0d;
        }
    }

    public ScoreResultVo lastYearOrgCommentResult(List<Long> orgIds, Integer year) {
        return remoteRuleWryService.queryOnlyByYearMonthId(
                x -> "12".equals(x.getQueryMonthDate()) && !DateUtils.isAfterDateMonth(x.getQueryYearMonthDate()),
                RemoteRuleWryService.RemoteRuleWryQuery.builder()
                        .ids(orgIds)
                        .queryYearDate(year - 1 + "")
                        .build(),
                query -> meetingMapper.lastYearOrgCommentResult2(query.getId(), query.getQueryYearDate(), query.getQueryMonthDate(),
                        ListUtils.join(",", orgTypeConfig.getBasicChild())),
                addFunction -> addFunction.getABoolean() != null && addFunction.getABoolean() ?
                        scoreConfig.getOrgScore().getLastYearOrgCommentResult() : 0.0);
    }


//    public ScoreResultVo lastYearOrgCommentResult(List<Long> orgIdList, Integer year) {
//        ScoreResultVo scoreResultVo = new ScoreResultVo();
//        String idJoin = ListUtils.join(",", orgIdList);
//        Map<Long, EnumMap<Month, Double>> map = initIdMap(orgIdList);
//        scoreResultVo.setScoreResultMap(map);
//        for (int month = 1; month <= 12; month++) {
//
//        }
//    }

    /**
     * 初始化用户或者组织map
     */
    private Map<Long, EnumMap<Month, Double>> initIdMap(List<Long> ids) {
        Map<Long, EnumMap<Month, Double>> map = new HashMap<>();
        for (Long id : ids) {
            map.put(id, new EnumMap<>(Month.class));
        }
        return map;
    }


    /**
     * 党员 领导干部示范带头 讲党课 机关单位领导干部 讲党课次数是否满足规定，每年大于等于1次
     * 判断当前领导干部的讲党课次数是否满足规定，每年最后一月判断，其他月份不判断也不得该项分
     *
     * @param userId    用户id
     * @param queryDate 查询月 yyyy-MM
     */
    public Double leaderLectures(Long userId, String queryDate) {
        String[] split = queryDate.split("-");
        if (new Integer(split[1]) % 12 != 0 || DateUtils.isAfterDateMonth(queryDate)) {
            return 0d;
        }
        Boolean b = meetingMapper.leaderLectures(userId, split[0]);
        if (b != null && b) {
            return scoreConfig.getUserScore().getLeaderLecturesScore();
        } else {
            return 0d;
        }
    }

    public ScoreResultVo leaderLectures(List<Long> userIds, Integer year) {
        return remoteRuleWryService.queryOnlyByYearMonth(
                filter -> "12".equals(filter.getQueryMonthDate()) && !DateUtils.isAfterDateMonth(filter.getQueryYearMonthDate()),
                RemoteRuleWryService.RemoteRuleWryQuery.builder()
                        .ids(userIds)
                        .queryYearDate(year.toString())
                        .build(),
                query -> meetingMapper.leaderLectures2(query.getIdStr(), query.getQueryYearDate()),
                result -> result.getABoolean() != null && Boolean.TRUE.equals(result.getABoolean()) ?
                        scoreConfig.getUserScore().getLeaderLecturesScore() : 0.0
        );
    }


    /**
     * 党员 基础工作 民主评议 非离退休党员 是否有有评议结果（按年统计）
     * 判断当前党员上一年度的述职评议结果等次，优得4分，良得3分，中得2分，差得1分；每年12月判断一次，其他月份不判断也不得该项分
     *
     * @param userId    用户id
     * @param queryDate 查询月 yyyy-MM
     */
    public Double lastYearUserCommentResult(Long userId, String queryDate) {
        String[] split = queryDate.split("-");
        if (new Integer(split[1]) % 12 != 0 || DateUtils.isAfterDateMonth(queryDate)) {
            return 0d;
        }
        List<Integer> ratings = meetingMapper.getYearUserCommentResult(userId, new Integer(split[0]) - 1, split[1]);
        if (CollectionUtils.isEmpty(ratings)) {
            return 0d;
        } else {
            switch (ratings.get(0)) {
                case 1:
                    return scoreConfig.getUserScore().getLastYearUserCommentResult1();
                case 2:
                    return scoreConfig.getUserScore().getLastYearUserCommentResult2();
                case 3:
                    return scoreConfig.getUserScore().getLastYearUserCommentResult3();
                case 4:
                    return scoreConfig.getUserScore().getLastYearUserCommentResult4();
                default:
                    return 0d;
            }
        }
    }

    public ScoreResultVo lastYearUserCommentResult(List<Long> userIds, Integer year) {
        return remoteRuleWryService.queryOnlyByYearMonth(
                filter -> "12".equals(filter.getQueryMonthDate()) && !DateUtils.isAfterDateMonth(filter.getQueryYearMonthDate()),
                RemoteRuleWryService.RemoteRuleWryQuery.builder()
                        .ids(userIds)
                        .queryYearDate(year - 1 + "")
                        .build(),
                query -> meetingMapper.getYearUserCommentResult2(query.getIdStr(), query.getQueryYearDate(), query.getQueryMonthDate()),
                result -> {
                    switch (result.getType()) {
                        case 1:
                            return scoreConfig.getUserScore().getLastYearUserCommentResult1();
                        case 2:
                            return scoreConfig.getUserScore().getLastYearUserCommentResult2();
                        case 3:
                            return scoreConfig.getUserScore().getLastYearUserCommentResult3();
                        case 4:
                            return scoreConfig.getUserScore().getLastYearUserCommentResult4();
                        default:
                            return 0.0;
                    }
                }
        );
    }


    /**
     * 组织得分
     *
     * @param regionId  区县id
     * @param orgIdList 组织id
     * @param year      年份
     * @return 分数 基础工作 开展组织生活得分
     * <AUTHOR>
     */
    public ScoreResultVo orgBasicWorkScoreFromMeeting(Long regionId, List<Long> orgIdList, Integer year) {
        return LoggingUtils.wrap(
                "组织开展组织生活打分",
                () -> meetingScoreService.orgBasicWorkScore(regionId, year, orgIdList),
                true,
                orgIdList,
                year
        );
    }

    /**
     * 组织得分
     *
     * @param regionId  区县id
     * @param orgIdList 组织id
     * @param year      年份
     * @return 分数 基础工作 述职评议分数
     * <AUTHOR> orgDebriefReviewScore
     */
    public ScoreResultVo orgDebriefReviewScoreFromMeeting(
            Long regionId, List<Long> orgIdList, Integer year) {
        return LoggingUtils.wrap(
                "组织述职评议打分",
                () -> meetingScoreService.orgDebriefReviewScore(regionId, year, orgIdList),
                true,
                orgIdList,
                year
        );
    }

    /**
     * 组织得分
     *
     * @param regionId  区县id
     * @param orgIdList 组织id
     * @param year      年份
     * @return 分数 优秀组织 奖惩得分
     * <AUTHOR>
     */
    public ScoreResultVo orgCommendPenalizeScoreFromMeeting(
            Long regionId, List<Long> orgIdList, Integer year) {
        return LoggingUtils.wrap(
                "组织奖惩打分",
                () -> meetingScoreService.orgCommendPenalizeScore(regionId, year, orgIdList),
                true,
                orgIdList,
                year
        );
    }

    /**
     * 用户得分
     *
     * @param regionId   区县id
     * @param userIdList 用户id
     * @param year       年份
     * @return 分数 基础工作 参加组织生活得分
     * <AUTHOR>
     */
    public ScoreResultVo userBasicWorkScoreFromMeeting(Long regionId, List<Long> userIdList, Integer year) {
        return LoggingUtils.wrap(
                "用户参加组织生活生活打分",
                () -> meetingScoreService.userBasicWorkScore(regionId, year, userIdList),
                true,
                userIdList,
                year
        );
    }

    /**
     * 用户得分
     *
     * @param regionId   区县id
     * @param userIdList 用户id
     * @param year       年份
     * @return 分数 活跃程度 互动得分
     */
    public ScoreResultVo userActivityLevelScore(Long regionId, List<Long> userIdList, Integer year) {
        return LoggingUtils.wrap(
                "用户参加互动打分",
                () -> activityScoreService.userActivityLevelScore(regionId, year, userIdList),
                true,
                userIdList,
                year
        );
    }

    /**
     * 用户得分
     *
     * @param regionId   区县id
     * @param userIdList 用户id
     * @param year       年份
     * @return 分数 优秀党员 奖惩得分
     */
    public ScoreResultVo excellentPartyMemberScore(Long regionId, List<Long> userIdList, Integer year) {
        return LoggingUtils.wrap(
                "用户奖惩打分",
                () -> meetingScoreService.excellentPartyMemberScore(regionId, year, userIdList),
                true,
                userIdList,
                year
        );
    }

    /**
     * 用户得分
     *
     * @param regionId   区县id
     * @param userIdList 用户id
     * @param year       年份
     * @return 分数 领导干部示范带头 双重组织生活得分
     */
    public ScoreResultVo userLeadScore(Long regionId, List<Long> userIdList, Integer year) {
        return LoggingUtils.wrap(
                "用户双重组织生活打分",
                () -> meetingScoreService.userLeadScore(regionId, year, userIdList),
                true,
                userIdList,
                year
        );
    }

    /**
     * 组织 基础工作 领导班子成员支部联系点情况 机关党委 支部联系点设置是否合理（联系点与所在支部是否一致）
     * 判断当前组织的全部领导联系点是否和其所在支部一致，不一致则得该项分
     *
     * @param orgId     组织id
     * @param queryDate 查询时间 yyyy-MM
     */
    private Double checkLeaderContactEqual(Long orgId, String queryDate) {
        Boolean aBoolean = leaderMapper.checkLeaderContactEqual(orgId, queryDate);
        if (aBoolean != null && aBoolean) {
            return scoreConfig.getOrgScore().getCheckLeaderContactEqual();
        } else {
            return 0d;
        }
    }

    /**
     * 组织 基础工作 领导班子成员支部联系点情况 机关党委 是否设置支部联系点（有/无？）
     * 判断当前组织的全部领导是否已设置联系点，是则得该项分
     *
     * @param orgId     组织id
     * @param queryDate 查询时间 yyyy-MM
     */
    private Double isCreateLeaderContact(Long orgId, String queryDate) {
        Boolean createLeaderContact = leaderMapper.isCreateLeaderContact(orgId, queryDate);
        if (createLeaderContact != null && createLeaderContact) {
            return scoreConfig.getOrgScore().getIsCreateLeaderContact();
        } else {
            return 0d;
        }
    }

    /**
     * 组织 组织建设 届次换届 机关党委 机关党委书记、专职副书记任职年限是否超过两届
     * 判断当前组织的党委书记、专职副书记任意一项是否与上一届相同，不相同则得该项分
     * 测试 4685 2020-10
     *
     * @param orgId     组织id
     * @param queryDate 查询时间 yyyy-MM
     */
    private Double checkTopOrgRepeatExistPosition(Long orgId, String queryDate) {
        // 获取该组织存在的所有支委会届次信息与 '党委书记','党委专职副书记' 人员
        List<RankPeriodIsEquleFrom> list = orgPeriodMapper.getPeriodForTime(orgId, queryDate);
        if (CollectionUtils.isEmpty(list)) {
            return 0d;
        }

        Date date = DateUtils.stringToDate(queryDate, "yyyy-MM");
        Long unixSecond = date.getTime() / 1000;
        Long newPeriodId = null;
        Long beforePeriodId = null;
        //找到符合查询时间最新的届次和前一次届次
        for (RankPeriodIsEquleFrom rankPeriodIsEquleFrom : list) {
            if (newPeriodId != null) {
                beforePeriodId = rankPeriodIsEquleFrom.getPeriodId();
                break;
            }
            if (rankPeriodIsEquleFrom.getUnixStartTime() <= unixSecond
                    && rankPeriodIsEquleFrom.getUnixEndTime() >= unixSecond) {
                newPeriodId = rankPeriodIsEquleFrom.getPeriodId();
            }
        }

        // 存在指定查询时间届次及上一届次时 判断届次里面'党委书记','党委专职副书记' 是否存在重复人员
        if (newPeriodId != null && beforePeriodId != null) {
            Long count = orgPeriodMapper.checkPeriodMemberIsRepeat(newPeriodId, beforePeriodId);
            if (count == null || count.equals(0L)) {
                return scoreConfig.getOrgScore().getCheckTopOrgRepeatExistPosition();
            }
            //只存在指定时间一条届次 没有再查询到上一届次返回有效
        } else if (newPeriodId != null) {
            return scoreConfig.getOrgScore().getCheckTopOrgRepeatExistPosition();
        }

        //都不满足
        return 0d;
    }

    /**
     * 组织 组织建设 届次换届 机关党委 机关党委书记、专职副书记是否出现空缺
     * 判断当前组织的届次中是否缺少党委书记、专职副书记，不缺少则得该项分
     *
     * @param orgId     组织id
     * @param queryDate 查询时间 yyyy-MM
     */
    private Double checkTopOrgLackPosition(Long orgId, String queryDate) {
        Boolean aBoolean = orgPeriodMapper.checkTopOrgLackPosition(orgId, queryDate);
        if (aBoolean != null && aBoolean) {
            return scoreConfig.getOrgScore().getCheckTopOrgLackPosition();
        } else {
            return 0d;
        }
    }

    /**
     * 组织 组织建设 届次换届 党支部 支部人数符合规定（未超过50人或小于3人）
     * 判断当前组织是否满足人数规定，满足则得该项分
     *
     * @param orgId     组织id
     * @param queryDate 查询时间 yyyy-MM
     */
    private Double checkPeriodPerson1(Long orgId, String queryDate) {
        Boolean aBoolean = orgPeriodMapper.checkPeriodPerson1(orgId, queryDate, ListUtils.join(",", orgTypeConfig.getBranchChild()));
        if (aBoolean != null && aBoolean) {
            return scoreConfig.getOrgScore().getCheckPeriodPerson1();
        } else {
            return 0d;
        }
    }

    /**
     * 组织 组织建设 届次换届 党支部 支委会设置符合规定（超过7人且成立支委会）
     * 判断当前组织的支委会设置情况是否满足规定（人数超过7人且设置了支委会；人数少于7人且未设置支委会），是则得该项分
     *
     * @param orgId     组织id
     * @param queryDate 查询时间 yyyy-MM
     */
    private Double checkPeriodPerson2(Long orgId, String queryDate) {
        Boolean b = orgPeriodMapper.checkPeriodPerson2(orgId, queryDate, ListUtils.join(",", orgTypeConfig.getBranchChild()));
        if (b != null && b) {
            return scoreConfig.getOrgScore().getCheckPeriodPerson1();
        } else {
            return 0d;
        }
    }

    /**
     * 组织 组织建设 届次换届 所有基层党组织 是否按期换届
     * 判断当前组织的届次是否已过期，未过期则得该项分
     *
     * @param orgId     组织id
     * @param queryDate 查询时间 yyyy-MM
     */
    private Double isCreatePeriod(Long orgId, String queryDate) {
        Boolean createPeriod = orgPeriodMapper.isCreatePeriod(orgId, queryDate, ListUtils.join(",", orgTypeConfig.getBasicChild()));
        if (createPeriod != null && createPeriod) {
            return scoreConfig.getOrgScore().getIsCreatePeriod();
        } else {
            return 0d;
        }
    }
}
