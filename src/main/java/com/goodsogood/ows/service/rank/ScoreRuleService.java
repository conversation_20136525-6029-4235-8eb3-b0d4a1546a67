package com.goodsogood.ows.service.rank;

import com.goodsogood.ows.mapper.rank.ScoreRuleMapper;
import com.goodsogood.ows.model.db.rank.ScoreRuleEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by FuXiao on 2020/10/19
 */
@Service
@Log4j2
public class ScoreRuleService {
    private final ScoreRuleMapper scoreRuleMapper;

    public ScoreRuleService(ScoreRuleMapper scoreRuleMapper) {
        this.scoreRuleMapper = scoreRuleMapper;
    }

    public void insert(ScoreRuleEntity entity) {
        scoreRuleMapper.insertSelective(entity);
    }

    public void delete(Long id) {
        scoreRuleMapper.deleteByPrimaryKey(id);
    }

    public void update(ScoreRuleEntity entity) {
        scoreRuleMapper.updateByPrimaryKeySelective(entity);
    }

    public ScoreRuleEntity find(Long id) {
        return scoreRuleMapper.selectByPrimaryKey(id);
    }

    public List<ScoreRuleEntity> findByColumn(Long columnId) {
        Example example = new Example(ScoreRuleEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("columnId", columnId);
        List<ScoreRuleEntity> res = new ArrayList<>();
        res.addAll(scoreRuleMapper.selectByExample(example));
        return res;
    }

    public void updatePercent(Long topId, Double percentage) {
        scoreRuleMapper.updatePercent(topId, percentage);
    }
}
