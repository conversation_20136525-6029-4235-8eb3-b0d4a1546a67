package com.goodsogood.ows.service.rank;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.overview.PartyResultForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * 获取用户信息 Date: 2018/6/19 Time: 10:34
 *
 * <AUTHOR>
 */
@Service
@Log4j2
public class RestTemplateUserService {

    @Value("${tog-services.user-center}")
    private String userCenter;

    private final RestTemplate restTemplate;
    private final Errors errors;

    public RestTemplateUserService(RestTemplate restTemplate, Errors errors) {
        this.restTemplate = restTemplate;
        this.errors = errors;
    }

    /**
     * 根据组织查询它的一级党委
     */
    public PartyResultForm findPartyInfo(HeaderHelper.SysHeader sysHeader) {
        String url = String.format("http://%s/org/find-party-id?org_id=%s", userCenter,sysHeader.getOid());
        return RestTemplateHelper.get(
                sysHeader, url, new TypeReference<Result<PartyResultForm>>() {});
    }


}
