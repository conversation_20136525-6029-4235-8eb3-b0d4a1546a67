package com.goodsogood.ows.service.rank;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.rank.OrgWhereResultForm;
import com.goodsogood.ows.model.vo.rank.UserOrgResultChildForm;
import com.goodsogood.ows.model.vo.sas.OrgUserQueryForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;

/**
 * Create by FuXiao on 2020/10/22
 */
@Service
@Log4j2
public class ClientUserCenterService {
    private final RestTemplate restTemplate;
    private final TogServicesConfig togServicesConfig;

    public ClientUserCenterService(RestTemplate restTemplate, TogServicesConfig togServicesConfig) {
        this.restTemplate = restTemplate;
        this.togServicesConfig = togServicesConfig;
    }

    /**
     * 访问用户中心，通过组织id获得用户信息
     */
    public Result<Page<UserOrgResultChildForm>> getUserList(Long orgId, String userName, Integer page, Integer pageSize) {
        HttpHeaders headers = new HttpHeaders();
        headers.put("_tk", Collections.singletonList("-1"));
        headers.add("_region_id", "3");
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        OrgUserQueryForm body = new OrgUserQueryForm();
        body.setOrgId(orgId);
        if (null != userName && !"".equals(userName)) body.setName(userName);
        body.setPage(page);
        body.setPagesize(pageSize);
        Result<Page<UserOrgResultChildForm>> userOrgResultChildForms = null;
        int count = 0;
        try {
            do {
                userOrgResultChildForms = RemoteApiHelper.postAndResult(this.restTemplate,
                        String.format("http://%s/org/user/getUserList",
                        togServicesConfig.getUserCenter()),
                        body, headers, new TypeReference<Result<Page<UserOrgResultChildForm>>>() {
                });
                count++;
            } while (null == userOrgResultChildForms && count < 5);
        } catch (Exception e) {
            log.error("根据条件查询成员列表出错！ orgId ={}", orgId, e);
        }
        log.debug("根据条件查询成员列表返回结果:查询 orgId ={}  结果 userOrgResultChildForms ={}", orgId, userOrgResultChildForms);
        return userOrgResultChildForms;
    }

    /**
     * 访问用户中心，查询组织以及下级列表
     */
    public Result<List<OrgWhereResultForm>> findOrgByWhere(Long orgId, Integer page, Integer pageSize) {
        HttpHeaders headers = new HttpHeaders();
        headers.put("_tk", Collections.singletonList("-1"));
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        Result<List<OrgWhereResultForm>> orgWhereResultForms = null;
        int count = 0;
        try {
            do {
                orgWhereResultForms = RemoteApiHelper.getAndResult(this.restTemplate, String.format("http://%s/org/find-org-by-where?org_id=%s&page=%s&page_size=%s",
                        togServicesConfig.getUserCenter(),
                        orgId, page, pageSize), headers, new TypeReference<Result<List<OrgWhereResultForm>>>() {
                });
                count++;
            } while (null == orgWhereResultForms && count < 5);
        } catch (Exception e) {
            log.error("查询组织以及下级列表出错！ orgId ={}", orgId, e);
        }
        log.debug("查询组织以及下级列表返回结果:查询 orgId ={}  结果 orgWhereResultForms ={}", orgId, orgWhereResultForms);
        return orgWhereResultForms;
    }

    /**
     * 访问用户中心，查询组织架构
     */
    public OrganizationBase findOrgById(Long orgId) {
        HttpHeaders headers = new HttpHeaders();
        headers.put("_tk", Collections.singletonList("-1"));
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        OrganizationBase organizationBase = null;
        int count = 0;
        try {
            do {
                organizationBase = RemoteApiHelper.get(this.restTemplate, String.format("http://%s/find-org-by-id?org_id=%s",
                        togServicesConfig.getUserCenter(),
                        orgId), headers, new TypeReference<Result<OrganizationBase>>() {
                });
                count++;
            } while (null == organizationBase && count < 5);
        } catch (Exception e) {
            log.error("查询组织架构出错！ orgId ={}", orgId, e);
        }
        log.debug("查询组织架构返回结果:查询 orgId ={}  结果 organizationBase ={}", orgId, organizationBase);
        return organizationBase;
    }
}
