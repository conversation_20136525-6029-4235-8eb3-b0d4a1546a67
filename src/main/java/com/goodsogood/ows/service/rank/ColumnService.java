package com.goodsogood.ows.service.rank;

import com.goodsogood.ows.mapper.rank.ColumnMapper;
import com.goodsogood.ows.model.db.rank.ColumnEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * Create by FuXiao on 2020/10/19
 */
@Service
@Log4j2
public class ColumnService {
    private final ColumnMapper columnMapper;

    public ColumnService(ColumnMapper columnMapper) {
        this.columnMapper = columnMapper;
    }

    public void insert(ColumnEntity entity) {
        columnMapper.insertSelective(entity);
    }

    public void delete(Long id) {
        columnMapper.deleteColumn(id);
    }

    public void update(ColumnEntity entity) {
        columnMapper.updateByPrimaryKeySelective(entity);
    }

    public ColumnEntity find(Long id) {
        Example example = new Example(ColumnEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("columnId", id);
        criteria.andEqualTo("status", 1);
        return columnMapper.selectByExample(example).get(0);
    }

    public List<ColumnEntity> findAll(Long topId) {
        Example example = new Example(ColumnEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("topId", topId);
        return columnMapper.selectByExample(example);
    }
}
