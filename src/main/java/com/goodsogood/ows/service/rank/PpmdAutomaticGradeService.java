package com.goodsogood.ows.service.rank;

import com.goodsogood.ows.common.AutomaticGradeUtils;
import com.goodsogood.ows.common.PpmdAutonmaSqlTemplate;
import com.goodsogood.ows.configuration.ScoreConfig;
import com.goodsogood.ows.mapper.rank.PpmdMapper;
import com.goodsogood.ows.mapper.rank.SnapshotMapper;
import com.goodsogood.ows.model.vo.rank.*;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.SqlJointUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.goodsogood.ows.utils.SqlJointUtil.joint;

/**
 * 党费平台自动打分服务类
 *
 * <AUTHOR> tc
 * @date 2020/11/6
 */
@Service
@Log4j2
public class PpmdAutomaticGradeService {

    private final PpmdMapper ppmdMapper;
    private final ScoreConfig scoreConfig;
    private final StringRedisTemplate redisTemplate;
    private final SnapshotMapper snapshotMapper;
    private final AutomaticGradePpmdComponent automaticGradePpmdComponent;

    @Autowired
    public PpmdAutomaticGradeService(PpmdMapper ppmdMapper, ScoreConfig scoreConfig, StringRedisTemplate redisTemplate, SnapshotMapper snapshotMapper, AutomaticGradePpmdComponent automaticGradePpmdComponent) {
        this.ppmdMapper = ppmdMapper;
        this.scoreConfig = scoreConfig;
        this.redisTemplate = redisTemplate;
        this.snapshotMapper = snapshotMapper;
        this.automaticGradePpmdComponent = automaticGradePpmdComponent;
    }

    /**
     * 党费平台组织自动评分
     * @param regionId
     * @param orgIdList
     * @param year
     * @return
     */
    public ScoreResultVo ppmdOrgScore(Long regionId,List<Long> orgIdList,Integer year){
        List<ScoreResultVo> score = new ArrayList<>(5);
        //组织是否交齐得分
        score.add(this.findPpmdOrgIsFinish(regionId,orgIdList,year));
        //组织是否存在未设置党费标准得分
        score.add(this.findPpmdOrgIsUnset(regionId,orgIdList,year));
        //组织交齐日期得分
        score.add(this.findPpmdOrgFinishDay(regionId,orgIdList,year));
        //组织积极缴纳排行榜
        score.add(this.isInLeaderboard(orgIdList,year,2));
        //党委积极缴纳排行榜
        score.add(this.isInLeaderboard(orgIdList,year,3));
        //计算大类得分
        ScoreResultVo vo = AutomaticGradeUtils.sumScoreResultVo(score);
        return vo;
    }

    /**
     * 党费平台党员自动评分
     * @param regionId
     * @param userIdList
     * @param year
     * @return
     */
    public ScoreResultVo ppmdUserScore(Long regionId,List<Long> userIdList,Integer year){
        List<ScoreResultVo> score = new ArrayList<>(3);
        //党员是否交纳得分
        score.add(this.findPpmdUserIsFinish(regionId,userIdList,year));
        //党员交纳日期得分
        score.add(this.findPpmdUserFinishDay(regionId,userIdList,year));
        //党员积极缴纳排行榜
        score.add(this.isInLeaderboard(userIdList, year, 1));
        //计算大类得分
        ScoreResultVo vo = AutomaticGradeUtils.sumScoreResultVo(score);
        return vo;
    }

    /**
     * 判定组织是否交齐得分
     * @param regionId
     * @param orgIdList
     * @param year
     * @return
     */
    private ScoreResultVo findPpmdOrgIsFinish(Long regionId, List<Long> orgIdList, Integer year){
        return automaticGradePpmdComponent.getOrgScore(
                regionId,
                orgIdList,
                year,
                (param) -> {
                    Map<String, String> params = automaticGradePpmdComponent.buildParamMapUseSqlTemplateReplace(param);
                    return SqlJointUtil.joint(
                            PpmdAutonmaSqlTemplate.unfinishNum,
                            params
                    );
                },
                (sqlList) -> {
                    String joint = joint(sqlList, true);
                    List<PpmdOrgVO> ppmdOrgVOList = ppmdMapper.executePpmdOrgSql(joint);
                    EnumMap<Month, Double> sqlResult = new EnumMap(Month.class);
                    ppmdOrgVOList.forEach(k -> {
                        //根据打分规则，生成分数
                        Double score = null;
                        if(k.getRt() == null  || k.getRt()==0){
                            score = scoreConfig.getOrgScore().getPpmdIsFinish();
                        }
                        if (score != null) {
                            sqlResult.put(Month.getEnumKey(Integer.parseInt(k.getDateMonth().split("-")[1])), score);
                        }
                    });
                    return sqlResult;
                });
    }

    /**
     * 判定组织是否存在未设置得分
     * @param regionId
     * @param orgIdList
     * @param year
     * @return
     */
    private ScoreResultVo findPpmdOrgIsUnset(Long regionId,List<Long> orgIdList,Integer year){
        return automaticGradePpmdComponent.getOrgScore(
                regionId,
                orgIdList,
                year,
                (param) -> {
                    Map<String, String> params = automaticGradePpmdComponent.buildParamMapUseSqlTemplateReplace(param);
                    return SqlJointUtil.joint(
                            PpmdAutonmaSqlTemplate.unsetNum,
                            params
                    );
                },
                (sqlList) -> {
                    String joint = joint(sqlList, true);
                    List<PpmdOrgVO> ppmdOrgVOList = ppmdMapper.executePpmdOrgSql(joint);
                    EnumMap<Month, Double> sqlResult = new EnumMap(Month.class);
                    ppmdOrgVOList.forEach(k -> {
                        //根据打分规则，生成分数
                        Double score = null;
                        if(k.getRt() == null  || k.getRt()==0){
                            score = scoreConfig.getOrgScore().getPpmdIsUnset();
                        }
                        if (score != null) {
                            sqlResult.put(Month.getEnumKey(Integer.parseInt(k.getDateMonth().split("-")[1])), score);
                        }
                    });
                    return sqlResult;
                });
    }

    /**
     * 判定组织交齐日期得分
     * @param regionId
     * @param orgIdList
     * @param year
     * @return
     */
    private ScoreResultVo findPpmdOrgFinishDay(Long regionId,List<Long> orgIdList,Integer year){
        return automaticGradePpmdComponent.getOrgScore(
                regionId,
                orgIdList,
                year,
                (param) -> {
                    Map<String, String> params = automaticGradePpmdComponent.buildParamMapUseSqlTemplateReplace(param);
                    return SqlJointUtil.joint(
                            PpmdAutonmaSqlTemplate.finishDay,
                            params
                    );
                },
                (sqlList) -> {
                    String joint = joint(sqlList, true);
                    List<PpmdOrgVO> ppmdOrgVoList = ppmdMapper.executePpmdOrgSql(joint);
                    EnumMap<Month, Double> sqlResult = new EnumMap(Month.class);
                    if (ppmdOrgVoList != null && !ppmdOrgVoList.isEmpty()) {
                    ppmdOrgVoList.forEach(k -> {
                        //根据打分规则，生成分数
                        Double score = null;
                        if(k.getRt() != null && k.getRt() != 0){
                            score = ScoreConfig.getScoreByRule(scoreConfig.getOrgScore().getPpmdFinishDay(), k.getRt());
                        }
                        if (score != null) {
                            sqlResult.put(Month.getEnumKey(Integer.parseInt(k.getDateMonth().split("-")[1])), score);
                        }
                    });
                    return sqlResult;
                    }
                    return null;
                });
    }

    /**
     * 判定党员是否交纳得分
     * @param regionId
     * @param userIdList
     * @param year
     * @return
     */
    private ScoreResultVo findPpmdUserIsFinish(Long regionId,List<Long> userIdList,Integer year){
        return automaticGradePpmdComponent.getUserScore(
                regionId,
                userIdList,
                year,
                (param) -> {
                    List<PpmdUserVO> ppmdUserVoList = ppmdMapper.getUserPayDay(param.getRegionId(), param.getUserIds(), param.getDateMonth());
                    if (ppmdUserVoList != null && !ppmdUserVoList.isEmpty()) {
                        Map<Long, Double> result = new HashMap<>();
                        ppmdUserVoList.forEach(t -> {
                            //根据打分规则，生成分数
                            if (t.getUserId() != null && t.getPayDay() != null) {
                                Double score = scoreConfig.getUserScore().getPpmdIsFinish();
                                if (score != null) {
                                    result.put(t.getUserId(), score);
                                }
                            }
                        });
                        return result;
                    }
                    return null;
                });
    }

    /**
     * 判定党员交纳日期得分
     * @param regionId
     * @param userIdList
     * @param year
     * @return
     */
    private ScoreResultVo findPpmdUserFinishDay(Long regionId,List<Long> userIdList,Integer year){
        return automaticGradePpmdComponent.getUserScore(
                regionId,
                userIdList,
                year,
                (param) -> {
                    List<PpmdUserVO> ppmdUserVoList = ppmdMapper.getUserPayDay(param.getRegionId(), param.getUserIds(), param.getDateMonth());
                    if (ppmdUserVoList != null && !ppmdUserVoList.isEmpty()) {
                        Map<Long, Double> result = new HashMap<>();
                        ppmdUserVoList.forEach(t -> {
                            //根据打分规则，生成分数
                            if (t.getUserId() != null && t.getPayDay() != null) {
                                Double score = ScoreConfig.getScoreByRule(scoreConfig.getUserScore().getPpmdFinishDay(), t.getPayDay());
                                if (score != null) {
                                    result.put(t.getUserId(), score);
                                }
                            }
                        });
                        return result;
                    }
                    return null;
                });
    }

    /**
     * 是否进入积极缴纳排行榜
     * @param idList
     * @param year
     * @param type 1党员 2支部 3党委
     * @return
     */
    public ScoreResultVo isInLeaderboard(List<Long> idList, Integer year, Integer type) {
        ScoreResultVo scoreResultVo = new ScoreResultVo();
        Map<Long, EnumMap<Month, Double>> map = new HashMap<>();
        for (Long id : idList) {
            EnumMap<Month, Double> enumMap = new EnumMap<>(Month.class);
            for (int j = 1; j <= 12; j++) {
                String queryDate = year + "-";
                if (j < 10) {
                    queryDate += "0";
                }
                queryDate += j;
                String key = "";

                if (1 == type) key = "ACTIVE_PAYMENT_USER_3_" + queryDate;
                else if (2 == type) key = "ACTIVE_PAYMENT_ORG_3_" + queryDate;
                else key = "ACTIVE_PAYMENT_PARTY_3_" + queryDate;

                Long size = redisTemplate.opsForList().size(key);
                double score = 0d;
                boolean flag = false;
                for (int i = 0; i < size; i++) {
                    String p = redisTemplate.opsForList().index(key, i);
                    if (1 == type) {
                        ActiveUserVo activeUserVo = JsonUtils.fromJson(p, ActiveUserVo.class);
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        if (null == activeUserVo || null == activeUserVo.getPayDate()) break;
                        String payDate = dateFormat.format(activeUserVo.getPayDate());
                        Long userId = snapshotMapper.findUserId(payDate, activeUserVo.getUserName());
                        if (id.equals(userId)) { flag = true; break; }
                    }
                    else {
                        ActiveOrgVo activeOrgVo = JsonUtils.fromJson(p, ActiveOrgVo.class);
                        if (id.equals(activeOrgVo.getOrgId())) { flag = true; break; }
                    }
                }
                if (flag) score = 5d;
                enumMap.put(Month.getEnumKey(j), score);
            }
            map.put(id, enumMap);
        }
        scoreResultVo.setScoreResultMap(map);
        return scoreResultVo;
    }
}
