package com.goodsogood.ows.service.rank;

import com.goodsogood.ows.common.AutomaticGradeUtils;
import com.goodsogood.ows.model.vo.rank.Month;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.service.user.OrgSnapshotService;
import com.goodsogood.ows.service.user.UserSnapshotService;
import com.goodsogood.ows.utils.SqlJointUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2020/11/25
 */
@Component
public class AutomaticGradePpmdComponent {

    protected final UserSnapshotService userSnapshotService;
    protected final OrgSnapshotService orgSnapshotService;

    @Autowired
    protected AutomaticGradePpmdComponent(UserSnapshotService userSnapshotService,
                                          OrgSnapshotService orgSnapshotService) {
        this.userSnapshotService = userSnapshotService;
        this.orgSnapshotService = orgSnapshotService;
    }


    @Data
    @AllArgsConstructor
    public static class Param {

        /**
         * 快照月份，YYYY-mm
         */
        private String dateMonth;

        /**
         * 区县id
         */
        private Long regionId;

        /**
         * 组织id
         */
        private Long orgId;

        /**
         * 当月组织下所有基层党组织编号，按逗号分隔
         */
        private String orgIds;

        /**
         * 当月组织下的非离退休党员用户id，按逗号分隔
         */
        private String userIds;
    }

    /**
     * 生成指定年的每个月的快照日期
     * key = YYYY-mm
     * value = 月份
     *
     * @param year 年
     * @return
     */
    private Map<String, Integer> genMonthDate(int year) {
        Map<String, Integer> times = new HashMap<>(12);
        int nowYear = LocalDate.now().getYear();
        int month;
        if (year < nowYear) {
            month = 12;
        } else {
            month = LocalDate.now().getMonthValue();
        }
        for (int i = 1; i <= month; i++) {
            if (i < 10) {
                times.put(year + "-0" + i, i);
            } else {
                times.put(year + "-" + i, i);
            }
        }
        return times;
    }

    /**
     * 批量获取组织得分
     *
     * @param regionId   区县id
     * @param orgIdList     组织id集合
     * @param year       年
     * @param sqlHandler sql处理器
     * @param sqlExecute sql执行器
     * @return
     */
    public ScoreResultVo getOrgScore(Long regionId,
                                     List<Long> orgIdList,
                                     Integer year,
                                     Function<Param, String> sqlHandler,
                                     Function<List<String>, EnumMap<Month, Double>> sqlExecute) {
        Map<String, Integer> times = genMonthDate(year);
        ScoreResultVo scoreResultVo = new ScoreResultVo();
        Map<Long, EnumMap<Month, Double>> result = new HashMap<>();
        // 初始化返回实体
        orgIdList.forEach(id -> {
            EnumMap enumMap = new EnumMap(Month.class);
            for (int i = 1; i <= 12; i++) {
                enumMap.put(Month.getEnumKey(i), 0.0);
            }
            result.put(id, enumMap);
        });
        scoreResultVo.setScoreResultMap(result);

        // key = 组织id, value = {key = dateMonth(YYYY-mm), value = 是否为基层党组织}
        Map<Long, Map<String, Boolean>> orgValidResult = new HashMap<>();

        times.forEach((monthDate, month) -> {
            // 批量判断组织是否为基层党组织
            Map<Long, Boolean> baseOrgMap = orgSnapshotService.isBaseOrg(regionId, orgIdList, monthDate);
            baseOrgMap.forEach((orgId, isValid) -> {
                if (!orgValidResult.containsKey(orgId)) {
                    orgValidResult.put(orgId, new HashMap<>());
                }
                Map<String, Boolean> temp = orgValidResult.get(orgId);
                temp.put(monthDate, isValid);
            });
        });

        // 基层党组织需要执行真实的sql，非基层党组织返回0即可
        orgValidResult.forEach((orgId, res) -> {
            List<String> sqlList = new ArrayList<>(12);
            res.forEach((dateMonth, valid) -> {
                if (!result.containsKey(orgId)) {
                    result.put(orgId, new EnumMap(Month.class));
                }
                EnumMap temp = result.get(orgId);
                if (!valid) {
                    temp.put(Month.getEnumKey(Integer.parseInt(dateMonth.split("-")[1])), 0.0);
                } else {
                    //获取当前组织，当月所有有效基层组织id
                    String orgIds = orgSnapshotService.getBaseOrgIncludeChildOrgIds(regionId, dateMonth,orgId);
                    if(orgIds!=null){
                        String sql = sqlHandler.apply(new Param(dateMonth, regionId, orgId, orgIds,null));
                        sqlList.add(sql);
                    }
                }
            });
            if (!sqlList.isEmpty()) {
                EnumMap<Month, Double> sqlResult = sqlExecute.apply(sqlList);
                if (sqlResult != null && !sqlResult.isEmpty()) {
                    EnumMap<Month, Double> monthDoubleEnumMap = result.get(orgId);
                    sqlResult.forEach((k, v) -> {
                        monthDoubleEnumMap.put(k, v);
                    });
                }
            }
        });
        return scoreResultVo;
    }

    /**
     * 根据Param构建参数map，用于sql模板参数替换
     *
     * @param param
     * @return
     */
    public Map<String, String> buildParamMapUseSqlTemplateReplace(Param param) {
        Map<String, String> params = new HashMap<>();
        params.put("dateMonth", param.getDateMonth());
        params.put("orgId", String.valueOf(param.getOrgId()));
        params.put("regionId", String.valueOf(param.getRegionId()));
        params.put("userIds", param.getUserIds());
        params.put("orgIds", param.getOrgIds());
        return params;
    }


    /**
     * 批量获取用户得分
     *
     * @param regionId   区县id
     * @param userIdList    用户id集合
     * @param year       年
     * @param sqlExecute sql执行器
     * @return
     */
    public ScoreResultVo getUserScore(Long regionId,
                                      List<Long> userIdList,
                                      Integer year,
                                      Function<AutomaticGradePpmdComponent.Param, Map<Long, Double>> sqlExecute) {
        ScoreResultVo scoreResultVo =AutomaticGradeUtils.initScoreResultVo(userIdList);
        Map<Long, EnumMap<Month, Double>> result = scoreResultVo.getScoreResultMap();
        Map<String, Integer> times = genMonthDate(year);
        List<Long> validUserIds = new ArrayList<>(userIdList.size());
        times.forEach((dateMonth, month) -> {
            Month monthKey = Month.getEnumKey(month);
            // 批量判断用户合法性
            Map<Long, Boolean> validMap = userSnapshotService.isRetired(regionId, userIdList, dateMonth);
            validMap.forEach((userId, isRetired) -> {
                if (isRetired) {
                    result.get(userId).put(monthKey, 0.0d);
                } else {
                    validUserIds.add(userId);
                }
            });
            if (validUserIds != null && !validUserIds.isEmpty()) {
                String validUserIdsStr = SqlJointUtil.longListToStr(validUserIds);
                Map<Long, Double> sqlResult = sqlExecute.apply(new AutomaticGradePpmdComponent.Param(dateMonth, regionId, null,null, validUserIdsStr));
                if (sqlResult != null) {
                    sqlResult.forEach((k, v) -> {
                        if (k != null && v != null) {
                            result.get(k).put(monthKey, v);
                        }
                    });
                }
            }
            validUserIds.clear();
        });
        return scoreResultVo;
    }
}
