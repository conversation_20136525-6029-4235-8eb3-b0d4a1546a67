package com.goodsogood.ows.service.rank;

import com.goodsogood.ows.configuration.ScoreConfig;
import com.goodsogood.ows.model.vo.rank.Month;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.service.user.OrgSnapshotService;
import com.goodsogood.ows.service.user.UserSnapshotService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.SqlJointUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2020/11/7
 */
@Component
public class AutomaticGradeServiceComponent {

    protected final UserSnapshotService userSnapshotService;
    protected final OrgSnapshotService orgSnapshotService;

    @Autowired
    protected AutomaticGradeServiceComponent(UserSnapshotService userSnapshotService,
                                             OrgSnapshotService orgSnapshotService) {
        this.userSnapshotService = userSnapshotService;
        this.orgSnapshotService = orgSnapshotService;
    }


    @Data
    @AllArgsConstructor
    public static class Param {

        /**
         * 快照月份，YYYY-mm
         */
        private String dateMonth;

        /**
         * 区县id
         */
        private Long regionId;

        /**
         * 组织id
         */
        private Long orgId;

        /**
         * 当月组织下的非离退休党员用户id，按逗号分隔
         */
        private String userIds;

    }

    /**
     * 生成指定年的每个月的快照日期
     * key = YYYY-mm
     * value = 月份
     *
     * @param year 年
     * @return
     */
    private Map<String, Integer> genMonthDate(int year) {
        Map<String, Integer> times = new HashMap<>(12);
        int nowYear = LocalDate.now().getYear();
        int month;
        if (year < nowYear) {
            month = 12;
        } else {
            month = LocalDate.now().getMonthValue();
        }
        for (int i = 1; i <= month; i++) {
            if (i < 10) {
                times.put(year + "-0" + i, i);
            } else {
                times.put(year + "-" + i, i);
            }
        }
        return times;
    }

    /**
     * 初始化一个返回结果
     *
     * @param ids
     * @return
     */
    private ScoreResultVo initScoreResultVo(List<Long> ids) {
        ScoreResultVo scoreResultVo = new ScoreResultVo();
        Map<Long, EnumMap<Month, Double>> result = new HashMap<>();
        // 初始化返回实体
        ids.forEach(id -> {
            EnumMap enumMap = new EnumMap(Month.class);
            for (int i = 1; i <= 12; i++) {
                enumMap.put(Month.getEnumKey(i), 0.0);
            }
            result.put(id, enumMap);
        });
        scoreResultVo.setScoreResultMap(result);
        return scoreResultVo;
    }

    /**
     * 批量获取组织得分
     *
     * @param regionId   区县id
     * @param orgIds     组织id集合
     * @param year       年
     * @param sqlHandler sql处理器
     * @param sqlExecute sql执行器
     * @return
     */
    public ScoreResultVo getOrgScore(Long regionId,
                                     List<Long> orgIds,
                                     Integer year,
                                     Function<Param, String> sqlHandler,
                                     Function<List<String>, EnumMap<Month, Double>> sqlExecute) {
        Map<String, Integer> times = genMonthDate(year);
        ScoreResultVo scoreResultVo = initScoreResultVo(orgIds);
        Map<Long, EnumMap<Month, Double>> result = scoreResultVo.getScoreResultMap();

        // key = 组织id, value = {key = dateMonth(YYYY-mm), value = 是否为基层党组织}
        Map<Long, Map<String, Boolean>> orgValidResult = new HashMap<>();

        times.forEach((monthDate, month) -> {
            // 批量判断组织是否为基层党组织
            Map<Long, Boolean> baseOrgMap = orgSnapshotService.isBaseOrg(regionId, orgIds, monthDate);
            baseOrgMap.forEach((orgId, isValid) -> {
                if (!orgValidResult.containsKey(orgId)) {
                    orgValidResult.put(orgId, new HashMap<>());
                }
                Map<String, Boolean> temp = orgValidResult.get(orgId);
                temp.put(monthDate, isValid);
            });
        });

        // 基层党组织需要执行真实的sql，非基层党组织返回0即可
        orgValidResult.forEach((orgId, res) -> {
            List<String> sqlList = new ArrayList<>(12);
            res.forEach((dateMonth, valid) -> {
                if (!result.containsKey(orgId)) {
                    result.put(orgId, new EnumMap(Month.class));
                }
                EnumMap temp = result.get(orgId);
                if (!valid) {
                    temp.put(Month.getEnumKey(Integer.parseInt(dateMonth.split("-")[1])), 0.0);
                } else {
                    // 获取当前组织，当月的有效用户id
                    String userIds = userSnapshotService.getNotRetiredUserIdsByOrg(regionId, orgId, dateMonth);
                    if (userIds != null) {
                        String sql = sqlHandler.apply(new Param(dateMonth, regionId, orgId, userIds));
                        sqlList.add(sql);
                    }
                }
            });
            if (!sqlList.isEmpty()) {
                EnumMap<Month, Double> sqlResult = sqlExecute.apply(sqlList);
                if (sqlResult != null && !sqlResult.isEmpty()) {
                    EnumMap<Month, Double> monthDoubleEnumMap = result.get(orgId);
                    sqlResult.forEach((k, v) -> {
                        monthDoubleEnumMap.put(k, v);
                    });
                }
            }
        });
        return scoreResultVo;
    }

    /**
     * 根据Param构建参数map，用于sql模板参数替换
     *
     * @param param
     * @return
     */
    public Map<String, String> buildParamMapUseSqlTemplateReplace(Param param) {
        Map<String, String> params = new HashMap<>();
        String startTime = DateUtils.firstDayOfMonthByDate(param.getDateMonth());
        String endTime = DateUtils.lastDayOfMonthByDate(param.getDateMonth());
        params.put("dateMonth", param.getDateMonth());
        params.put("userId", param.getUserIds());
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        params.put("orgId", String.valueOf(param.getOrgId()));
        params.put("regionId", String.valueOf(param.getRegionId()));
        return params;
    }

    /**
     * 根据Param构建参数map，用于sql模板参数替换
     * 第二种 为了消费扶贫和积分换书参数
     *
     * @param param
     * @return
     */
    public Map<String, String> buildParamMapUseSqlTemplateReplaceTwo(Param param) {
        Map<String, String> params = new HashMap<>();
        params.put("regionId", String.valueOf(param.getRegionId()));
        params.put("dateMonth", param.getDateMonth());
        params.put("orgId", String.valueOf(param.getOrgId()));
        params.put("userIds", param.getUserIds());
        return params;
    }

    /**
     * 根据规则获取得分
     *
     * @param rules     规则
     * @param joinTotal 参与人数
     * @param total     总人数
     * @return
     */
    public Double getScoreByRule(List<ScoreConfig.Rule> rules, Long joinTotal, Long total) {
        joinTotal = joinTotal == null ? 0 : joinTotal;
        total = total == null ? 0 : total;
        Integer percentage = 0;
        if (joinTotal > 0 && total > 0) {
            percentage = ScoreConfig.getPercentage(joinTotal, total);
        }
        return ScoreConfig.getScoreByRule(rules, percentage);
    }

    /**
     * 根据规则获取得分
     *
     * @param rules     规则
     * @param dateMonth 快照年月
     * @param orgId     组织id
     * @param joinTotal 参与人数
     * @return
     */
    public Double getScoreByRule(List<ScoreConfig.Rule> rules, String dateMonth, Long orgId, Long joinTotal) {
        if (StringUtils.isNotBlank(dateMonth) && orgId != null) {
            Long total = orgSnapshotService.getOrgUserTotal(orgId, dateMonth);
            Double score = getScoreByRule(
                    rules,
                    joinTotal,
                    total
            );
            return score;
        }
        return null;
    }

    /**
     * 根据规则获取得分
     *
     * @param rule      规则
     * @param joinTotal 参与次数
     * @return
     */
    public Double getScoreByRule(ScoreConfig.Rule rule, Long joinTotal) {
        if (rule == null || joinTotal == null) {
            return null;
        }
        return joinTotal > rule.getMin() ? rule.getScore() : 0.0d;
    }

    /**
     * 批量获取用户得分
     *
     * @param regionId   区县id
     * @param userIds    用户id集合
     * @param year       年
     * @param sqlExecute sql执行器
     * @return
     */
    public ScoreResultVo getUserScore(Long regionId,
                                      List<Long> userIds,
                                      Integer year,
                                      Function<Param, Map<Long, Double>> sqlExecute) {
        ScoreResultVo scoreResultVo = initScoreResultVo(userIds);
        Map<Long, EnumMap<Month, Double>> result = scoreResultVo.getScoreResultMap();
        Map<String, Integer> times = genMonthDate(year);
        List<Long> validUserIds = new ArrayList<>(userIds.size());
        times.forEach((dateMonth, month) -> {
            Month monthKey = Month.getEnumKey(month);
            // 批量判断用户合法性
            Map<Long, Boolean> validMap = userSnapshotService.isRetired(regionId, userIds, dateMonth);
            validMap.forEach((userId, isRetired) -> {
                if (isRetired) {
                    result.get(userId).put(monthKey, 0.0d);
                } else {
                    validUserIds.add(userId);
                }
            });
            if (validUserIds != null && !validUserIds.isEmpty()) {
                String validUserIdsStr = SqlJointUtil.longListToStr(validUserIds);
                Map<Long, Double> sqlResult = sqlExecute.apply(new Param(dateMonth, regionId, null, validUserIdsStr));
                if (sqlResult != null) {
                    sqlResult.forEach((k, v) -> {
                        if (k != null && v != null) {
                            result.get(k).put(monthKey, v);
                        }
                    });
                }
            }
            validUserIds.clear();
        });
        return scoreResultVo;
    }

}
