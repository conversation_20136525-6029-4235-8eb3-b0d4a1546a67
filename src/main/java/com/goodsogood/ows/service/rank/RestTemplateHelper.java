package com.goodsogood.ows.service.rank;

import com.fasterxml.jackson.core.type.TypeReference;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.ArrayUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Nullable;
import java.io.IOException;

/**
 * <p>Description: restTemplate 请求</p>
 *
 * <AUTHOR>
 * @version 2019/7/25 17:02
 */
@Component
@Log4j2
public class RestTemplateHelper {
    private static RestTemplate restTemplate;
    private static Errors errors;

    @Autowired
    public void setStaticFields(RestTemplate restTemplate, Errors errors) {
        RestTemplateHelper.restTemplate = restTemplate;
        RestTemplateHelper.errors = errors;
    }

    /**
     * 初始化带regionId的LogHeader
     */
    public static HttpHeaders getRegionIdLogHeader(Long regionId) {
        if (regionId == null) {
            throw new NullPointerException("区县ID为空");
        }
        HttpHeaders httpHeaders = LogAspectHelper.putLogHeader();
        httpHeaders.set(HeaderHelper.OPERATOR_REGION, String.valueOf(regionId));
        return httpHeaders;
    }

    /**
     * 初始化带regionId的LogHeader
     */
    public static HeaderHelper.SysHeader getRegionIdLogSysHeader(Long regionId) {
        return HeaderHelper.buildMyHeader(getRegionIdLogHeader(regionId));
    }


    /**
     * post 请求
     *
     * @param url           请求的url
     * @param typeReference 序列化类型
     * @param body          请求参数
     * @param <T>           返回类型
     * @param <B>           请求参数类型
     * @return 可能为null
     * @throws ApiException 自定义API异常
     */
    public static <T, B> T post(HeaderHelper.SysHeader sysHeader, String url, @Nullable B body, TypeReference<Result<T>> typeReference) throws ApiException {
        return post(HeaderHelper.setMyHttpHeader(LogAspectHelper.putLogHeader(), sysHeader), url, body, typeReference);
    }

    /**
     * post 请求
     *
     * @param url           请求的url
     * @param typeReference 序列化类型
     * @param body          请求参数
     * @param <T>           返回类型
     * @param <B>           请求参数类型
     * @return 可能为null
     * @throws ApiException 自定义API异常
     */
    private static <T, B> T post(HttpHeaders headers, String url, @Nullable B body, TypeReference<Result<T>> typeReference, int... successCodes) throws ApiException {
        if (successCodes == null || successCodes.length == 0) {
            successCodes = new int[]{0};
        }
        preconditionsHeader(headers);
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        try {
            T res = RemoteApiHelper.post(restTemplate, url, body, headers, typeReference);
            log.debug("url->{}\n参数->{}\n 响应结果->{}", url, JsonUtils.toJson(body), JsonUtils.toJson(res));
            return res;
        } catch (ApiException e) {
            Result<?> result = e.getResult();
            log.warn("error->{}", result);
            if (result != null && ArrayUtils.contains(successCodes, result.getCode())) {
                return null;
            } else {
                return restTemplateError("restTemplate post 请求-> " + url + " 出错！\n" + "参数->" + JsonUtils.toJson(body), e);
            }
        } catch (Exception e) {
            return restTemplateError("restTemplate post 请求-> " + url + " 出错！\n" + "参数->" + JsonUtils.toJson(body), e);
        }
    }

    /**
     * get 请求
     *
     * @param url           请求的url
     * @param typeReference 序列化类型
     * @param <T>           返回类型
     * @return 可能为null
     * @throws ApiException 自定义API异常
     */
    public static <T> T get(HeaderHelper.SysHeader sysHeader, String url, TypeReference<Result<T>> typeReference) throws ApiException {
        return get(HeaderHelper.setMyHttpHeader(LogAspectHelper.putLogHeader(), sysHeader), url, typeReference);
    }

    /**
     * get 请求
     *
     * @param url           请求的url
     * @param typeReference 序列化类型
     * @param <T>           返回类型
     * @return 可能为null
     * @throws ApiException 自定义API异常
     */
    public static <T> T get(HttpHeaders headers, String url, TypeReference<Result<T>> typeReference) throws ApiException {
        preconditionsHeader(headers);
        log.debug("restTemplate get请求： url->{}", url);
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        try {
            T res = RemoteApiHelper.get(
                    restTemplate,
                    url,
                    headers, typeReference
            );
            log.debug("restTemplate get请求：res->{}", JsonUtils.toJson(res));
            return res;
        } catch (ApiException e) {
            Result<?> result = e.getResult();
            log.debug("restTemplate get请求： url->{}, result->{}", url, JsonUtils.toJson(result));
            if (result != null && result.getCode() == 0) {
                return null;
            } else {
                return restTemplateError("restTemplate get请求-> " + url + " 出错！", e);
            }
        } catch (IOException e) {
            return restTemplateError("restTemplate get请求-> " + url + " 出错！", e);
        }
    }

    private static <T> T restTemplateError(String message, Exception e) {
        throw new ApiException(
                message,
                e,
                new Result<>(errors, 9907, HttpStatus.SERVICE_UNAVAILABLE.value()));
    }

    private static void preconditionsHeader(HttpHeaders headers) {
        checkNotNull(headers, HeaderHelper.OPERATOR_REGION);

        String tidKey = "_tid";
        if (headers.get(tidKey) == null) {
            headers.set("_tid", ThreadContext.get("tracker_id"));
        }
        String psidKey = "_psid";
        if (headers.get(psidKey) == null) {
            headers.set("_psid", ThreadContext.get("span_id"));
        }
    }

    /**
     * 非空验证
     */
    private static void checkNotNull(HttpHeaders headers, String... keys) {
        if (headers == null) {
            headersNullError("请求头信息参数有错, headers为空");
        }
        if (keys != null) {
            for (String k : keys) {
                if (headers.get(k) == null) {
                    headersNullError("请求头信息参数有错, " + k + " 为空");
                }
            }
        }
    }

    private static void headersNullError(String message) {
        throw new ApiException(
                message,
                new Result<>(
                        errors,
                        Global.Errors.ERROR_HEADER,
                        HttpStatus.OK.value()));
    }
}
