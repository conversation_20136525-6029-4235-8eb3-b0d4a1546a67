package com.goodsogood.ows.service.rank;

import com.goodsogood.ows.model.vo.rank.Month;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.utils.ListUtils;
import lombok.Builder;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * <AUTHOR> ruoyu
 * @date : 2020/11/23
 */
@Service
@Log4j2
public class RemoteRuleWryService {


    /**
     * 仅按年、月 遍历查询
     *
     * @param predicateQueryDate 判断查询的时间是否符合打分规则上的时间 如季度年度
     * @param query              中转参数用于适配executeQuery&addFunction的执行
     * @param executeQuery       mapper执行查询方法
     * @param addFunction        得分逻辑
     * @return 结果集
     */
    public ScoreResultVo queryOnlyByYearMonth(Predicate<RemoteRuleWryQuery> predicateQueryDate, RemoteRuleWryQuery query,
                                              Function<RemoteRuleWryQuery, List<QueryResult>> executeQuery,
                                              Function<QueryResult, Double> addFunction) {
        //初始化部分数据
        ScoreResultVo scoreResultVo = new ScoreResultVo();
        Map<Long, EnumMap<Month, Double>> map = new HashMap<>(query.getIds().size());
        scoreResultVo.setScoreResultMap(map);
        query.setIdStr(ListUtils.join(",", query.getIds()));
        //循环12个月
        for (Month month : Month.values()) {
            initMonthData(query, map, month);
            //该时间是否符合打分项打分规则
            if (predicateQueryDate.test(query)) {
                //调用查询
                List<QueryResult> apply = executeQuery.apply(query);
                //返回的结果集
                if (!CollectionUtils.isEmpty(apply)) {
                    //遍历每个组织 如果符合则进行加分
                    apply.forEach(r -> setMap(map, r.getId(), month, addFunction.apply(r)));
                }
            }
        }
        return scoreResultVo;
    }

    /**
     * 仅按年、月 遍历每个id单独sql查询
     *
     * @param predicateQueryDate 判断查询的时间是否符合打分规则上的时间 如季度年度
     * @param query              中转参数用于适配executeQuery&addFunction的执行
     * @param executeQuery       mapper执行查询方法
     * @param addFunction        得分逻辑
     * @return 结果集
     */
    public ScoreResultVo queryOnlyByYearMonthId(Predicate<RemoteRuleWryQuery> predicateQueryDate, RemoteRuleWryQuery query,
                                                Function<RemoteRuleWryQuery, List<QueryResult>> executeQuery,
                                                Function<QueryResult, Double> addFunction) {
        //初始化部分数据
        ScoreResultVo scoreResultVo = new ScoreResultVo();
        Map<Long, EnumMap<Month, Double>> map = new HashMap<>(query.getIds().size());
        scoreResultVo.setScoreResultMap(map);
        //循环12个月
        for (Month month : Month.values()) {
            //初始化当前月的数据
            initMonthData(query, map, month);
            //该时间是否符合打分项打分规则
            if (predicateQueryDate.test(query)) {
                for (Long id : query.getIds()) {
                    //调用查询
                    query.setId(id);
                    List<QueryResult> apply = executeQuery.apply(query);
                    query.setId(null);
                    //返回的结果集
                    if (!CollectionUtils.isEmpty(apply)) {
                        //遍历每个组织 如果符合则进行加分
                        setMap(map, id, month, addFunction.apply(apply.get(0)));
                    }
                }
           }
        }
        return scoreResultVo;
    }

    private void initMonthData(RemoteRuleWryQuery query, Map<Long, EnumMap<Month, Double>> map, Month month) {
        String monthStr = month.value() < 10 ? "0" + month.value() : "" + month.value();
        String queryMonth = query.getQueryYearDate() + "-" + monthStr;
        query.setQueryMonthDate(monthStr);
        query.setQueryYearMonthDate(queryMonth);
        //每循环一month月数据 都先全部初始化一次0分
        query.ids.forEach(id -> setMap(map, id, month, 0.0));
    }


    /**
     * 设置分数
     *
     * @param map   Map<Long, EnumMap<Month, Double>>
     * @param id    用户或者组织id
     * @param month 设置月份
     * @param score 分数
     */
    private void setMap(Map<Long, EnumMap<Month, Double>> map, Long id, Month month, Double score) {
        if (map.containsKey(id)) {
            EnumMap<Month, Double> enumMap = map.get(id);
            enumMap.put(month, score);
        } else {
            EnumMap<Month, Double> enumMap = new EnumMap<>(Month.class);
            enumMap.put(month, score);
            map.put(id, enumMap);
        }
    }

    /**
     * 合并一个大项中每个小项分别得出的分数 O(n)
     *
     * @param ids            用户或者组织id
     * @param scoreResultVos 每项得出的分数  默认保证与ids长度一致
     */
    public static ScoreResultVo mergeScoreResultVo(List<Long> ids, ScoreResultVo... scoreResultVos) {
        if (scoreResultVos.length == 1) {
            return scoreResultVos[0];
        }
        ScoreResultVo scoreResultVo = scoreResultVos[0];
        for (Long id : ids) {
            EnumMap<Month, Double> enumMap = scoreResultVo.getScoreResultMap().get(id);
            for (int i = 1; i < scoreResultVos.length; i++) {
                EnumMap<Month, Double> tempEnumMap = scoreResultVos[i].getScoreResultMap().get(id);
                for (Month value : Month.values()) {
                    enumMap.put(value, enumMap.get(value) + tempEnumMap.get(value));
                }
            }
        }
        return scoreResultVo;
    }


    /**
     * 中转对象
     */
    @Data
    @Builder
    public static class RemoteRuleWryQuery {
        /**
         * 查询年度 yyyy 初次调用时需要传入
         */
        private String queryYearDate;

        /**
         * 查询月度 MM
         */
        private String queryMonthDate;

        /**
         * 查询年月 yyyy-MM
         */
        private String queryYearMonthDate;

        /**
         * 用户或者组织id  初次调用时需要传入
         */
        private List<Long> ids;

        /**
         * id 的字符串化  1,2,3,4,5
         */
        private String idStr;

        /**
         * 不能批量in查询只能每个id一次一次查询时存在
         */
        private Long id;
    }

    /**
     * mapper 通用返回的结果集
     */
    @Data
    public static class QueryResult {
        /**
         * 用户或组织id 必须返回
         */
        private Long id;

        /**
         * 是否满足条件
         */
        private Boolean aBoolean;

        /**
         * 如果返回各类型
         */
        private Integer type;

        /**
         * 如果返回的条数count()数
         */
        private Long count;
    }
}
