package com.goodsogood.ows.service.rank;


import com.goodsogood.ows.common.CreditAutonmaSqlTemplate;
import com.goodsogood.ows.configuration.ScoreConfig;
import com.goodsogood.ows.mapper.rank.CreditMapper;
import com.goodsogood.ows.mapper.score.ScoreUserMapper;
import com.goodsogood.ows.model.vo.rank.Month;
import com.goodsogood.ows.model.vo.rank.OrgJoinTotalVO;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.model.vo.rank.UserJoinTotalVO;
import com.goodsogood.ows.service.score.ScoreUserService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.SqlJointUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 积分中心自动打分服务类
 *
 * <AUTHOR> tc
 * @date 2020/11/6
 */
@Service
@Log4j2
public class CreditAutomaticGradeService {

    private final AutomaticGradeServiceComponent automaticGradeServiceComponent;
    private final ScoreUserMapper scoreUserMapper;
    private final ScoreConfig scoreConfig;
    private final CreditMapper creditMapper;
    private final ScoreUserService scoreUserService;

    @Autowired
    public CreditAutomaticGradeService(AutomaticGradeServiceComponent automaticGradeServiceComponent,
                                       ScoreConfig scoreConfig,
                                       ScoreUserMapper scoreUserMapper,
                                       CreditMapper creditMapper,
                                       ScoreUserService scoreUserService) {
        this.automaticGradeServiceComponent = automaticGradeServiceComponent;
        this.scoreConfig = scoreConfig;
        this.scoreUserMapper = scoreUserMapper;
        this.creditMapper = creditMapper;
        this.scoreUserService = scoreUserService;
    }

    /**
     * 积分平台组织自动评分,消费扶贫
     *
     * @param regionId
     * @param orgIdList 组织集合
     * @param year      查询年份
     * @return
     */
    public ScoreResultVo creditOrgPovertyScore(Long regionId, List<Long> orgIdList, Integer year) {
        //组织产生消费扶贫订单的党员占比得分
        return this.findCreditOrgPovertyOrder(regionId, orgIdList, year);
    }

    /**
     * 积分平台组织自动评分，积分换书
     *
     * @param regionId
     * @param orgIdList
     * @param year
     * @return
     */
    public ScoreResultVo creditOrgBookScore(Long regionId, List<Long> orgIdList, Integer year) {
        //组织产生积分换书订单的党员占比得分
        return this.findCreditOrgBookOrder(regionId, orgIdList, year);
    }

    /**
     * 积分平台党员自动评分,扶贫消费
     *
     * @param regionId
     * @param userIdList
     * @param year
     * @return
     */
    public ScoreResultVo creditUserPovertyScore(Long regionId, List<Long> userIdList, Integer year) {
        //党员是否购买扶贫产品得分
        return this.findCreditUserPovertyOrder(regionId, userIdList, year);
    }

    /**
     * 积分平台党员自动评分，积分换书
     *
     * @param regionId
     * @param userIdList
     * @param year
     * @return
     */
    public ScoreResultVo creditUserBookScore(Long regionId, List<Long> userIdList, Integer year) {
        return this.findCreditUserBookOrder(regionId, userIdList, year);
    }


    /**
     * 判定组织消费扶贫得分
     *
     * @param regionId
     * @param orgIdList
     * @param year
     * @return
     */
    private ScoreResultVo findCreditOrgPovertyOrder(Long regionId, List<Long> orgIdList, Integer year) {
        return automaticGradeServiceComponent.getOrgScore(
                regionId,
                orgIdList,
                year,
                (param) -> {
                    Map<String, String> params = automaticGradeServiceComponent.buildParamMapUseSqlTemplateReplaceTwo(param);
                    return SqlJointUtil.joint(
                            CreditAutonmaSqlTemplate.orgPovertyOrderRate,
                            params
                    );
                },
                (sqlList) -> {
                    String joint = SqlJointUtil.joint(sqlList, true);
                    List<OrgJoinTotalVO> joinTotalVOS = creditMapper.joinUserTotal(joint);
                    if (joinTotalVOS != null && !joinTotalVOS.isEmpty()) {
                        EnumMap<Month, Double> sqlResult = new EnumMap(Month.class);
                        joinTotalVOS.forEach(k -> {
                            //根据打分规则，生成分数
                            Double score = automaticGradeServiceComponent.getScoreByRule(
                                    scoreConfig.getOrgScore().getCreditPovertyOrder(),
                                    k.getDateMonth(),
                                    k.getOrgId(),
                                    k.getJoinTotal()
                            );
                            if (score != null) {
                                sqlResult.put(Month.getEnumKey(Integer.parseInt(k.getDateMonth().split("-")[1])), score);
                            }
                        });
                        return sqlResult;
                    }
                    return null;
                });
    }

    /**
     * 判定组织积分换书得分
     *
     * @param regionId
     * @param orgIdList
     * @param year
     * @return
     */
    private ScoreResultVo findCreditOrgBookOrder(Long regionId, List<Long> orgIdList, Integer year) {
        return automaticGradeServiceComponent.getOrgScore(
                regionId,
                orgIdList,
                year,
                (param) -> {
                    Map<String, String> params = automaticGradeServiceComponent.buildParamMapUseSqlTemplateReplaceTwo(param);
                    return SqlJointUtil.joint(
                            CreditAutonmaSqlTemplate.orgOrderRate,
                            params
                    );
                },
                (sqlList) -> {
                    String joint = SqlJointUtil.joint(sqlList, true);
                    List<OrgJoinTotalVO> joinTotalVOS = creditMapper.joinUserTotal(joint);
                    if (joinTotalVOS != null && !joinTotalVOS.isEmpty()) {
                        EnumMap<Month, Double> sqlResult = new EnumMap(Month.class);
                        joinTotalVOS.forEach(k -> {
                            //根据打分规则，生成分数
                            Double score = automaticGradeServiceComponent.getScoreByRule(
                                    scoreConfig.getOrgScore().getCreditBookOrder(),
                                    k.getDateMonth(),
                                    k.getOrgId(),
                                    k.getJoinTotal()
                            );
                            if (score != null) {
                                sqlResult.put(Month.getEnumKey(Integer.parseInt(k.getDateMonth().split("-")[1])), score);
                            }
                        });
                        return sqlResult;
                    }
                    return null;
                });
    }

    /**
     * 判定用户消费扶贫得分
     *
     * @param regionId
     * @param userIdList
     * @param year
     * @return
     */
    private ScoreResultVo findCreditUserPovertyOrder(Long regionId, List<Long> userIdList, Integer year) {
        return automaticGradeServiceComponent.getUserScore(
                regionId,
                userIdList,
                year,
                (param) -> {
                    List<UserJoinTotalVO> userJoinTotalVOS = creditMapper.getPovertyOrderCount(regionId, param.getUserIds(), param.getDateMonth());
                    if (userJoinTotalVOS != null && !userJoinTotalVOS.isEmpty()) {
                        Map<Long, Double> result = new HashMap<>();
                        userJoinTotalVOS.forEach(t -> {
                            if (t.getUserId() != null && t.getJoinTotal() != null) {
                                Double score = null;
                                //根据打分规则，生成分数
                                if (t.getJoinTotal() != null && t.getJoinTotal() > 0) {
                                    score = scoreConfig.getUserScore().getPovertyOrder();
                                }
                                if (score != null) {
                                    result.put(t.getUserId(), score);
                                }
                            }
                        });
                        return result;
                    }
                    return null;
                });
    }

    /**
     * 判定用户积分换书得分
     *
     * @param regionId
     * @param userIdList
     * @param year
     * @return
     */
    private ScoreResultVo findCreditUserBookOrder(Long regionId, List<Long> userIdList, Integer year) {
        return automaticGradeServiceComponent.getUserScore(
                regionId,
                userIdList,
                year,
                (param) -> {
                    List<UserJoinTotalVO> userJoinTotalVOS = creditMapper.getOrderCount(regionId, param.getUserIds(), param.getDateMonth());
                    if (userJoinTotalVOS != null && !userJoinTotalVOS.isEmpty()) {
                        Map<Long, Double> result = new HashMap<>();
                        userJoinTotalVOS.forEach(t -> {
                            if (t.getUserId() != null && t.getJoinTotal() != null) {
                                Double score = null;
                                //根据打分规则，生成分数
                                if (t.getJoinTotal() != null && t.getJoinTotal() > 0) {
                                    score = scoreConfig.getUserScore().getBookOrder();
                                }
                                if (score != null) {
                                    result.put(t.getUserId(), score);
                                }
                            }
                        });
                        return result;
                    }
                    return null;
                });
    }

    /**
     * 组织在线学习得分
     *
     * @param regionId 区县id
     * @param orgIds   组织id集合
     * @param year     年
     * @return
     */
    public ScoreResultVo orgOnlineStudyScore(Long regionId, List<Long> orgIds, Integer year) {
        return automaticGradeServiceComponent.getOrgScore(
                regionId,
                orgIds,
                year,
                (param) -> {
                    Map<String, String> params = automaticGradeServiceComponent.buildParamMapUseSqlTemplateReplace(param);
                    List<Long> scoreUserIds = scoreUserService.userToScoreUserIds(param.getUserIds());
                    if (CollectionUtils.isEmpty(scoreUserIds)) {
                        params.put("scoreUserId", "-1");
                    } else {
                        params.put("scoreUserId", SqlJointUtil.longListToStr(scoreUserIds));
                    }
                    return SqlJointUtil.joint(
                            ScoreUserMapper.JOIN_ONLINE_STUDY_USER_TOTAL_SQL_TEMPLATE,
                            params
                    );
                },
                (sqlList) -> {
                    String joint = SqlJointUtil.joint(sqlList, true);
                    List<OrgJoinTotalVO> orgJoinTotalVOS = scoreUserMapper.joinOnlineStudyUserTotal(joint);
                    EnumMap<Month, Double> sqlResult = new EnumMap(Month.class);
                    if (orgJoinTotalVOS != null && !orgJoinTotalVOS.isEmpty()) {
                        orgJoinTotalVOS.forEach(k -> {
                            Double score = automaticGradeServiceComponent.getScoreByRule(
                                    scoreConfig.getOrgScore().getStudy(),
                                    k.getDateMonth(),
                                    k.getOrgId(),
                                    k.getJoinTotal()
                            );
                            if (score != null) {
                                sqlResult.put(Month.getEnumKey(Integer.parseInt(k.getDateMonth().split("-")[1])), score);
                            }
                        });
                        return sqlResult;
                    }
                    return null;
                });
    }

    /**
     * 用户在线学习得分
     *
     * @param regionId 区县id
     * @param userIds  用户id集合
     * @param year     年
     * @return
     */
    public ScoreResultVo userOnlineStudyScore(Long regionId,
                                              List<Long> userIds,
                                              Integer year) {
        return automaticGradeServiceComponent.getUserScore(
                regionId,
                userIds,
                year,
                (param) -> {
                    String startTime = DateUtils.firstDayOfMonthByDate(param.getDateMonth());
                    String endTime = DateUtils.lastDayOfMonthByDate(param.getDateMonth());
                    List<UserJoinTotalVO> userJoinTotalVOS = scoreUserMapper.joinOnlineStudyTotal(param.getUserIds(), startTime, endTime);
                    if (userJoinTotalVOS != null && !userJoinTotalVOS.isEmpty()) {
                        Map<Long, Double> result = new HashMap<>();
                        userJoinTotalVOS.forEach(t -> {
                            if (t.getUserId() != null && t.getJoinTotal() != null) {
                                Double score = automaticGradeServiceComponent.getScoreByRule(scoreConfig.getUserScore().getStudy(), t.getJoinTotal());
                                if (score != null) {
                                    result.put(t.getUserId(), score);
                                }
                            }
                        });
                        return result;
                    }
                    return null;
                });
    }

}
