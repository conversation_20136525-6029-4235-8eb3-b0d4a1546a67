package com.goodsogood.ows.service.rank;

import com.goodsogood.ows.mapper.rank.OrgScoreDetailMapper;
import com.goodsogood.ows.mapper.rank.OrgScoreMapper;
import com.goodsogood.ows.mapper.rank.ScoreRuleMapper;
import com.goodsogood.ows.model.db.rank.OrgScoreDetailEntity;
import com.goodsogood.ows.model.db.rank.OrgScoreEntity;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

/**
 * Create by FuXiao on 2020/10/23
 */
@Service
@Log4j2
public class OrgScoreService {
    private final OrgScoreMapper orgScoreMapper;
    private final OrgScoreDetailMapper orgScoreDetailMapper;
    private final ScoreRuleMapper scoreRuleMapper;
    private final ClientUserCenterService clientUserCenterService;
    private final StringRedisTemplate redisTemplate;

    public OrgScoreService(OrgScoreMapper orgScoreMapper, OrgScoreDetailMapper orgScoreDetailMapper, ScoreRuleMapper scoreRuleMapper, ClientUserCenterService clientUserCenterService, StringRedisTemplate redisTemplate) {
        this.orgScoreMapper = orgScoreMapper;
        this.orgScoreDetailMapper = orgScoreDetailMapper;
        this.scoreRuleMapper = scoreRuleMapper;
        this.clientUserCenterService = clientUserCenterService;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 计算人员分数存储到redis
     */
    public void calculateScore(Long orgId) {
        List<Long> yearList = orgScoreMapper.findYear();
        for (Long year : yearList) {
            Double score = 0D;
            List<OrgScoreEntity> list = this.findByOrgIdYear(orgId, year);
            for (OrgScoreEntity entity : list) {
                if (entity.getScore().equals(-9999.0D)) {
                    score = 0D;
                    break;
                }
                if (null != entity.getScore() && 0L != entity.getScore()) {
                    Double percent = scoreRuleMapper.findPerCent(entity.getScoreRuleId());
                    score += entity.getScore() * percent;
                }
            }
            redisTemplate.opsForHash().put("ORG_SCORE_" + year, orgId.toString(), String.format("%.1f", score));
        }
    }

    /**
     * 根据orgId查询
     */
    public List<OrgScoreEntity> findByOrgIdYear(Long orgId, Long year) {
        Example example = new Example(OrgScoreEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", orgId);
        criteria.andEqualTo("year", year);
        return orgScoreMapper.selectByExample(example);
    }

    /**
     * 给组织打分
     */
    public void score(List<OrgScoreDetailEntity> entities) {
        OrganizationBase organizationBase = clientUserCenterService.findOrgById(entities.get(0).getOrgId());
        String orgLevel = organizationBase.getOrgLevel();
        //先清空该组织得分
        Example example = new Example(OrgScoreEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", entities.get(0).getOrgId());
        orgScoreMapper.deleteByExample(example);
        for (OrgScoreDetailEntity entity : entities) {
            //先存流水
            entity.setCreateTime(new Date());
            entity.setOrgLevel(orgLevel);
            orgScoreDetailMapper.insert(entity);
            //再新增
            OrgScoreEntity orgScore = new OrgScoreEntity();
            orgScore.setOrgId(entity.getOrgId());
            orgScore.setOrgName(entity.getOrgName());
            orgScore.setTime(entity.getYear().toString());
            orgScore.setScore(entity.getScore());
            orgScore.setTopId(entity.getTopId());
            orgScore.setScoreRuleId(entity.getScoreRuleId());
            orgScoreMapper.insert(orgScore);
        }
    }

    /**
     * 查询人员每项得分
     */
    public Map<Long, List<OrgScoreEntity>> findOrgScore(Long orgId, Long year) {
        Example example = new Example(OrgScoreEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", orgId);
        criteria.andEqualTo("year", year);
        List<OrgScoreEntity> entities = orgScoreMapper.selectByExample(example);
        Map<Long, List<OrgScoreEntity>> map = new HashMap<>();
        for (OrgScoreEntity entity : entities) {
            List<OrgScoreEntity> tmp = new ArrayList<>();
            if (map.containsKey(entity.getTopId())) { tmp = map.get(entity.getTopId()); }
            tmp.add(entity);
            map.put(entity.getTopId(), tmp);
        }
        return map;
    }
}
