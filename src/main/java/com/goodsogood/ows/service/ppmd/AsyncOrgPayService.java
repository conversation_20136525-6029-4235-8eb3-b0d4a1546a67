package com.goodsogood.ows.service.ppmd;

import com.goodsogood.ows.mapper.ppmd.OrgPayMapper;
import com.goodsogood.ows.mapper.ppmd.PartyMemberMapper;
import com.goodsogood.ows.mapper.ppmd.PayLogMapper;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.vo.ppmd.OrgNumVo;
import com.goodsogood.ows.model.vo.ppmd.OrgPayVo;
import com.goodsogood.ows.model.vo.sas.ActiveOrgVo;
import com.goodsogood.ows.model.vo.sas.SasOrgPayForm;
import com.goodsogood.ows.model.vo.sas.SasOrgPaySituationForm;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 党费统计异步服务层
 * @date 2019/11/27
 */
@Service
@Log4j2
public class AsyncOrgPayService {

    private static final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM");
    private static final Calendar calendar = Calendar.getInstance();

    private final PayLogMapper payLogMapper;
    private final PartyMemberMapper partyMemberMapper;
    private final OrgPayMapper orgPayMapper;
    private final OrgService orgService;

    @Autowired
    public AsyncOrgPayService(PayLogMapper payLogMapper, PartyMemberMapper partyMemberMapper,
                              OrgPayMapper orgPayMapper, OrgService orgService) {
        this.payLogMapper = payLogMapper;
        this.partyMemberMapper = partyMemberMapper;
        this.orgPayMapper = orgPayMapper;
        this.orgService = orgService;
    }

    /**
     * 计算组织的党费缴纳情况
     * <AUTHOR>
     * @date 2019/11/27
     * @param orgNum
     * @param time
     * @param queryTime
     * @param users
     * @param orgVos
     * @return void
     */
//    @Async("reportExecutor")
    void calculationOrgActive(OrgNumVo orgNum, String time, String queryTime, Set<Long> users, List<ActiveOrgVo> orgVos, CountDownLatch latch){
        //查询组织中的所有userId
        try {
            List<Long> userIdList = this.partyMemberMapper.findUserIdByOrg(time, orgNum.getOrgId());
            OrganizationEntity org = this.orgService.getById(orgNum.getOrgId());
            if (!CollectionUtils.isEmpty(userIdList)) {
                users.addAll(userIdList.stream().collect(Collectors.toSet()));
                OrgPayVo orgPay = this.payLogMapper.findFinalTimeByUserList(queryTime, userIdList);
                if (null == orgPay) {
                    return;
                }
                if (orgPay.getNum().equals(orgNum.getNum())) {
                    ActiveOrgVo activeOrgVo = new ActiveOrgVo();
                    activeOrgVo.setFinishTime(orgPay.getPayDate());
                    activeOrgVo.setOrgId(orgNum.getOrgId());
                    activeOrgVo.setOrgName(org.getShortName());
                    orgVos.add(activeOrgVo);
                }
            }
        } catch (Exception e) {
            log.error("计算["+orgNum.getOrgId()+"]是否交齐出问题", e);
        } finally {
            latch.countDown();
        }
    }

    @Transactional
    public List<SasOrgPaySituationForm> getOrgPaySituation(SasOrgPayForm form, StopWatch stopWatch){
        // 解决voList线程不安全
        List<SasOrgPaySituationForm> voList = Collections.synchronizedList(new ArrayList<>());
        /*ThreadLocal<List<SasOrgPaySituationVO>> threadLocal = new ThreadLocal<List<SasOrgPaySituationVO>>() {
            @Override
            protected List<SasOrgPaySituationVO> initialValue() {
                return voList;
            }
        };*/
        // 获取组织列表
        List<Long> orgIds = form.getOrgIds();
        // 获取查询时间
        String startTime = form.getDate();
        // 是否只查询一个月
        boolean isOneMonth = form.isOneMonth();
        log.info("当前党务看板查询时间 -> [{}], 统计组织列表 -> [{}]", startTime, orgIds);
        // 遍历组织信息，查询党费缴纳情况
        log.info("开始查询组织党费缴纳信息, 当前耗时 -> [{}]", stopWatch.toString());
        try {
            if (!CollectionUtils.isEmpty(orgIds)) {
                String maxTime;
                // 查询开始月份到当前月份的所有月份
                if (isOneMonth){
                    maxTime = calculateEndTime(startTime);
                } else {
                    maxTime = getMaxTime();
                }
                //定义计数器
                CountDownLatch latch = new CountDownLatch(orgIds.size() * compareTo(startTime, maxTime));
                while (dateCompare(startTime, maxTime) < 0) {
                    log.info("查询在[{}]时间党费缴纳信息, 当前耗时 -> [{}]", startTime, stopWatch.toString());
                    for (Long orgId : orgIds) {
                        this.getOrgPayFee(voList, orgId, startTime, latch, stopWatch);
                    }
                    startTime = calculateEndTime(startTime);
                }
                log.info("等待所有线程运行完毕, 当前耗时 -> [{}]", stopWatch.toString());
                // 等待所有线程与完毕
                try {
                    latch.await();
                } catch (InterruptedException e) {
                    log.error("程序出现异常: ",e);
                    Thread.currentThread().interrupt();
                }
                log.info("所有线程已经运行完毕, 当前耗时 -> [{}]", stopWatch.toString());
            }
        } catch (ParseException e) {
            log.error("获取结束时间失败, 错误信息: ", e);
        }
        log.info("结束查询组织党费缴纳信息, 当前耗时 -> [{}]", stopWatch.toString());
        return voList;
    }

    /**
     * 获取后一个月份
     * @param time
     * @return
     * @throws ParseException
     */
    private static String calculateEndTime(String time) throws ParseException {
        calendar.setTime(formatter.parse(time));
        calendar.add(Calendar.MONTH, 1);
        return formatter.format(calendar.getTime());
    }

    /**
     * 获取当前时间的后一个月
     * @return
     */
    private static String getMaxTime() {
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, 1);
        return formatter.format(calendar.getTime());
    }

    /**
     * 比较两个月份的大小
     * @param dateFirst
     * @param dateLast
     * @return
     * @throws ParseException
     */
    public static int dateCompare(String dateFirst, String dateLast) throws ParseException {
        Date first = formatter.parse(dateFirst);
        Date end = formatter.parse(dateLast);
        if (first.after(end)) {
            return 1;
        } else if (first.before(end)) {
            return -1;
        }
        return 0;
    }

    /**
     * 计算两个时间相差几个月
     * @param beforeTime
     * @param afterTime
     * @return
     * @throws ParseException
     */
    public static int compareTo(String beforeTime, String afterTime) throws ParseException {
        Calendar before = Calendar.getInstance();
        Calendar after = Calendar.getInstance();
        before.setTime(formatter.parse(beforeTime));
        after.setTime(formatter.parse(afterTime));
        int result = after.get(Calendar.MONTH) - before.get(Calendar.MONTH);
        int month = (after.get(Calendar.YEAR) - before.get(Calendar.YEAR)) * 12;
        return Math.abs(month + result);
    }

    /**
     * 查询党费缴纳情况
     * @param orgId
     * @param startTime
     */
    @Async("sasOrgFeeExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void getOrgPayFee(List<SasOrgPaySituationForm> voList, Long orgId, String startTime, CountDownLatch latch, StopWatch stopWatch){
        try {
            SasOrgPaySituationForm vo = new SasOrgPaySituationForm();
            // 获取当前组织应缴纳人数
            List<Long> userList = this.orgPayMapper.getOrgPayAbleUser(orgId, startTime);
            int payAbleNum = userList.size();
            log.info("查询组织[{}]应缴纳人数 -> [{}]", orgId, payAbleNum);
            int unPayNum = 0;
            int evalNuPayNum = 0;
            if (payAbleNum != 0) {
                // 获取当前组织党费已缴纳人数
                int orgPayNum = this.orgPayMapper.getOrgPayNum(userList, startTime);
                log.info("查询组织[{}]已缴纳人数 -> [{}]", orgId, orgPayNum);
                // 组织未缴纳党费人数
                unPayNum = payAbleNum - orgPayNum;
                // 获取考核未缴纳党费人数
                evalNuPayNum = this.orgPayMapper.getEvalUnNum(orgId, startTime);
                log.info("查询组织[{}]考核未缴纳人数 -> [{}]", orgId, evalNuPayNum);
            }
            vo.setOrgId(orgId);
            vo.setDate(startTime);
            vo.setPayAbleNum(payAbleNum);
            vo.setUnPayNum(unPayNum);
            vo.setEvalUnPayNum(evalNuPayNum);
            voList.add(vo);
        } catch (Exception e) {
            log.error("线程出现异常: ",e);
        } finally {
            latch.countDown();
            log.info("本次线程运行时间 -> [{}]",stopWatch.toString());
        }
    }
}
