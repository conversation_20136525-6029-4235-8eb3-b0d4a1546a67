package com.goodsogood.ows.service.ppmd;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.model.db.ppmd.OrgLevelInfoEntity;
import com.goodsogood.ows.model.db.ppmd.StatisticsInfoEntity;
import com.goodsogood.ows.model.vo.ppmd.StatsPartyMemberVo;
import com.goodsogood.ows.model.vo.ppmd.StatsPayLogVo;
import com.goodsogood.ows.model.vo.ppmd.StatsPayRatioVo;
import com.goodsogood.ows.model.vo.ppmd.StatsRedisVo;
import com.goodsogood.ows.model.vo.user.OrgUserCountForm;
import com.goodsogood.ows.model.vo.user.OrgUserCountQueryForm;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.NumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 党费交纳情况统计缓存工具
 *
 * <AUTHOR>
 */
@Slf4j
public class StatisticsRedisUtil {
    public static final String OFFICE = "office";
    public static final String ORG = "org";
    public static final String ALL = "all";
    public static final String PARTYNUMYES = "yes";
    public static final String PARTYNUMNO= "no";

    /**
     * 统计党费交纳情况到缓存
     *
     * @param redisTemplate Redis客户端
     * @param statsDate     统计日期
     * @param mode          统计模式   office只统计党委 org 只统计党支部  all 统计所有
     * @param refreshIds    指定刷新的组织编号
     * @return 统计结果
     */
    public static Map<String, List<StatsRedisVo>> putStatisticsRedis(final StringRedisTemplate redisTemplate,
                                                                     final PpmdStatisticsService statisticsService,
                                                                     String statsDate, String mode,
                                                                     OrgLevelInfoService orgLevelInfoService,
                                                                     List<Long> refreshIds, OrgService orgService,
                                                                     String politicalCode) {
       return putStatisticsRedis(redisTemplate,statisticsService,statsDate,mode,orgLevelInfoService,refreshIds,PARTYNUMYES,orgService, politicalCode);
    }

    /**
     * 统计党费交纳情况到缓存
     *
     * @param redisTemplate Redis客户端
     * @param statsDate     统计日期
     * @param mode          统计模式   office只统计党委 org 只统计党支部  all 统计所有
     * @param refreshIds    指定刷新的组织编号
     * @param partyNumFlag  是否刷新党员人数标记   0 否  1是 默认1
     * @return 统计结果
     */
    public static Map<String, List<StatsRedisVo>> putStatisticsRedis(final StringRedisTemplate redisTemplate,
                                                                     final PpmdStatisticsService statisticsService,
                                                                     String statsDate, String mode,
                                                                     OrgLevelInfoService orgLevelInfoService,
                                                                     List<Long> refreshIds,String partyNumFlag,
                                                                     OrgService orgService, String politicalCode) {
        return putStatisticsRedis(redisTemplate,statisticsService,statsDate,mode,orgLevelInfoService,refreshIds,partyNumFlag,0,orgService, politicalCode);
    }

    /**
     * 统计党费交纳情况到缓存
     *
     * @param redisTemplate Redis客户端
     * @param statsDate     统计日期
     * @param mode          统计模式   office只统计党委 org 只统计党支部  all 统计所有
     * @param refreshIds    指定刷新的组织编号
     * @param partyNumFlag  是否刷新党员人数标记   0 否  1是 默认1
     * @return 统计结果
     */
    public static Map<String, List<StatsRedisVo>> putStatisticsRedis(final StringRedisTemplate redisTemplate,
                                                                     final PpmdStatisticsService statisticsService,
                                                                     String statsDate, String mode,
                                                                     OrgLevelInfoService orgLevelInfoService,
                                                                     List<Long> refreshIds,String partyNumFlag,Integer partyNumVariate,
                                                                     OrgService orgService, String politicalCode) {
        Map<String, List<StatsRedisVo>> re = new HashMap<>();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.debug("统计党费交纳情况到缓存 开始！statsDate = {} mode = {}", statsDate, mode);
        try {
            /**step1 查询数据库获取统计信息*/
            //获取生效的交纳标准
            List<StatsPartyMemberVo> spmvList = new ArrayList<>();
            /**
             * 如果指定了更新的组织编号，优化更新缓存。
             * tc  2019.01.24  v1.0.3.1
             */
            if(refreshIds!=null&&refreshIds.size()>0&&!ALL.equals(mode)){
                spmvList = statisticsService.findStatsPartyMember(statsDate,mode,refreshIds);
            }else {
                spmvList = statisticsService.findStatsPartyMember(statsDate);
            }

            log.debug("获取生效的交纳标准完成。size = {},time = {}", spmvList != null ? spmvList.size() : 0, stopWatch);
            //获取生效的交纳比例
            List<StatsPayRatioVo> sprvList = statisticsService.findStatsPayRatio(statsDate);
            log.debug("获取生效的交纳比例完成。size = {},time = {}", sprvList != null ? sprvList.size() : 0, stopWatch);
            Map<Integer,List<StatsPayRatioVo>> sprvMap = findStatsPayRatioList(sprvList);
            log.debug("生效的交纳比例分组完成。size = {},time = {}", sprvMap != null ? sprvMap.size() : 0, stopWatch);
            //获取已经交纳记录
            List<StatsPayLogVo> splvList = statisticsService.findStatsPayLog(statsDate);
            log.debug("获取已经交纳记录完成。size = {},time = {}", splvList != null ? splvList.size() : 0, stopWatch);

            /**step2 信息整理，分为党委和党支部两个维度统计*/
            //以党委为维度分组
            Map<String, List<StatsPartyMemberVo>> officeMemberMap = new HashMap<>();
            //以党支部为维度分组
            Map<String, List<StatsPartyMemberVo>> orgMemberMap = new HashMap<>();
            //已交情况，用用户编号进行整理
            Map<Long, StatsPayLogVo> logMap = new HashMap<>();

            //临时存储党委编号
            Set<String> tmpOffice = new HashSet<>();
            Set<String> tmpOrg = new HashSet<>();

            /**交纳标准分组*/
            for (StatsPartyMemberVo spmv : spmvList) {
                if (!ORG.equals(mode)) {
                    //党委维度分组
                    String officeId = spmv.getOfficeId() != null ? spmv.getOfficeId().toString() : null;
                    setMemberMap(officeId, tmpOffice, spmv, officeMemberMap,OFFICE,null,null);
                }
                if (!OFFICE.equals(mode)) {
                    //党支部维度分组
                    String orgId = spmv.getOrgId() != null ? spmv.getOrgId().toString() : null;
                    setMemberMap(orgId, tmpOrg, spmv, orgMemberMap,ORG,statsDate,orgLevelInfoService);
                }
            }
            log.debug("交纳标准分组完毕。党委维度 Size = {}, 党支部维度 Size = {},time = {}", officeMemberMap != null ? officeMemberMap.size() : 0, orgMemberMap != null ? orgMemberMap.size() : 0, stopWatch);

            /**清除临时存储的编号*/
            tmpOffice.clear();
            tmpOrg.clear();

            /**已交纳分组*/
            for (StatsPayLogVo splv : splvList) {
                logMap.put(splv.getUserId(),splv);
            }
            log.debug("已经交纳整理完毕。time = {}", stopWatch);

            /**step2 信息处理，两个维度数据进行统计计算*/
            List<StatsRedisVo> officeSrList = null;
            List<StatsRedisVo> orgSrList = null;
            if (!ORG.equals(mode)) {
                officeSrList = setStatsRedis(officeMemberMap, logMap, sprvMap, statsDate, OFFICE, statisticsService, partyNumFlag, partyNumVariate, orgService, politicalCode);
                re.put(OFFICE, officeSrList);
            }
            if (!OFFICE.equals(mode)) {
                orgSrList = setStatsRedis(orgMemberMap, logMap, sprvMap, statsDate, ORG, statisticsService, partyNumFlag, partyNumVariate, orgService, politicalCode);
                re.put(ORG, orgSrList);
            }

            log.debug("信息处理，两个维度数据进行统计计算完毕。time = {}", stopWatch);

            /**step3 存入数据库和缓存*/
            if (!ORG.equals(mode)) {
                String officeRedisKey = Constants.STATISTICS_REDIS_OFFICE_KEY+statsDate;
                if (officeSrList != null) {
                    //存放到数据
                    boolean r;
                    if(refreshIds!=null&&refreshIds.size()>0&&!ALL.equals(mode)){
                        /**如果指定了刷新的组织编号，就要先删除掉对应的记录，再重新添加*/
                        r = statisticsService.saveStatisticsInfoByRefreshIds(setStatisticsInfo(officeSrList,officeRedisKey,OFFICE),officeRedisKey,refreshIds);
                    }else{
                        r = statisticsService.saveStatisticsInfo(setStatisticsInfo(officeSrList,officeRedisKey,OFFICE),officeRedisKey);
                    }
                    log.debug("处理统计党委存入数据库结束。是否成功 = {} startDate = {} size = {} time = {}",r,statsDate, officeSrList != null ? officeSrList.size() : 0, stopWatch);
                    //如果存放数据库成功就加入缓存
                    if(r){
                        //存入缓存
                        if(refreshIds!=null&&refreshIds.size()>0&&!ALL.equals(mode)){
                            /**如果指定了刷新的组织编号，就要比较后处理缓存 开始*/
//                            putRedisByRefreshIds(officeRedisKey,officeSrList,redisTemplate,refreshIds,officeMemberMap);
                            /**如果指定了刷新的组织编号，就要比较后处理缓存 结束*/
                            /**如果指定了刷新的组织编号，就从统计表里刷新缓存 开始*/
                            List<StatsRedisVo> srvl = statisticsService.findStatisticsInfo(officeRedisKey);
                            redisTemplate.opsForValue().set(officeRedisKey, JsonUtils.toJson(srvl),Constants.STATISTICS_REDIS_KEY_TIMEOUT_DAY,TimeUnit.DAYS);
                            /**如果指定了刷新的组织编号，就从统计表里刷新缓存 结束*/
                        }else{
                            redisTemplate.opsForValue().set(officeRedisKey, JsonUtils.toJson(officeSrList),Constants.STATISTICS_REDIS_KEY_TIMEOUT_DAY,TimeUnit.DAYS);
                        }
                        log.debug("处理统计党委存入缓存结束。startDate = {} size = {} time = {}", statsDate, officeSrList != null ? officeSrList.size() : 0, stopWatch);
                    }
                }
            }
            if (!OFFICE.equals(mode)) {
                String orgRedisKey = Constants.STATISTICS_REDIS_ORG_KEY+statsDate;
                if (orgSrList != null) {
                    //存放到数据
                    /**如果指定了刷新的组织编号，就要先删除掉对应的记录，再重新添加*/
                    boolean r;
                    if(refreshIds!=null&&refreshIds.size()>0&&!ALL.equals(mode)){
                        /**如果指定了刷新的组织编号，就要先删除掉对应的记录，再重新添加*/
                        r = statisticsService.saveStatisticsInfoByRefreshIds(setStatisticsInfo(orgSrList,orgRedisKey,ORG),orgRedisKey,refreshIds);
                    }else{
                        r = statisticsService.saveStatisticsInfo(setStatisticsInfo(orgSrList,orgRedisKey,ORG),orgRedisKey);
                    }
                    log.debug("处理统计党支部存入数据库结束。是否成功 = {} startDate = {} size = {} time = {}",r, statsDate, orgSrList != null ? orgSrList.size() : 0, stopWatch);
                    if(r){
                        //存入缓存
                        if(refreshIds!=null&&refreshIds.size()>0&&!ALL.equals(mode)){
                            /**如果指定了刷新的组织编号，就要比较后处理缓存  开始*/
//                            putRedisByRefreshIds(orgRedisKey,orgSrList,redisTemplate,refreshIds,orgMemberMap);
                            /**如果指定了刷新的组织编号，就要比较后处理缓存  结束*/
                            /**如果指定了刷新的组织编号，就从统计表里刷新缓存 开始*/
                            List<StatsRedisVo> srvl = statisticsService.findStatisticsInfo(orgRedisKey);
                            redisTemplate.opsForValue().set(orgRedisKey, JsonUtils.toJson(srvl),Constants.STATISTICS_REDIS_KEY_TIMEOUT_DAY,TimeUnit.DAYS);
                            /**如果指定了刷新的组织编号，就从统计表里刷新缓存 结束*/
                        }else{
                            redisTemplate.opsForValue().set(orgRedisKey, JsonUtils.toJson(orgSrList),Constants.STATISTICS_REDIS_KEY_TIMEOUT_DAY,TimeUnit.DAYS);
                        }
                        log.debug("处理统计党支部存入缓存结束。startDate = {} size = {} time = {}", statsDate, orgSrList != null ? orgSrList.size() : 0, stopWatch);
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理统计党费交纳情况到缓存出错！statsDate = {} mode = {}", statsDate, mode, e);
            throw e;
        }
        return re;
    }

    /**
     * 获取党费统计的缓存
     *
     * @param redisTemplate Redis客户端
     * @return
     */
    public static List<StatsRedisVo> findStatisticsRedis(final StringRedisTemplate redisTemplate,
                                                         final PpmdStatisticsService statisticsService,
                                                         String statsDate, String mode,
                                                         OrgLevelInfoService orgLevelInfoService,
                                                         OrgService orgService, String politicalCode) {
        List<StatsRedisVo> re = null;
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            log.debug("获取党费交纳统计结果开始。statsDate = {} mode = {} time = {}", statsDate,mode, stopWatch);

            String redisKey="";
            if(OFFICE.equals(mode)){
                //获取党委交纳党费统计结果
                redisKey = Constants.STATISTICS_REDIS_OFFICE_KEY+statsDate;
            }
            if(ORG.equals(mode)){
                //获取党支部交纳党费统计结果
                redisKey = Constants.STATISTICS_REDIS_ORG_KEY+statsDate;
            }
            //通过缓存获取统计结果
            Object r =  redisTemplate.opsForValue().get(redisKey);
            if(r!=null){
                re =(List<StatsRedisVo>) JsonUtils.fromJson(String.valueOf(r), ArrayList.class, StatsRedisVo.class);
            }else{
                //尝试从统计表里获取
                re = statisticsService.findStatisticsInfo(redisKey);
            }
            if(re!=null&&re.size()>0){

            }else{
                //立即从业务数据表里查询
                re = putStatisticsRedis(redisTemplate,statisticsService,statsDate,mode,orgLevelInfoService,null, orgService, politicalCode).get(mode);
            }
            log.debug("获取交纳党费统计结果完成。size = {} time = {}", re.size(), stopWatch);
        } catch (Exception e) {
            log.error("获取交纳党费统计结果出错! statsDate = {} mode = {}", statsDate,mode, e);
        }
        return re;
    }

    /**
     *  根据交纳比例类型，给比例分组
     * @param sprvList  交纳比例区间值集合
     * @return
     */
    public static Map<Integer,List<StatsPayRatioVo>> findStatsPayRatioList(List<StatsPayRatioVo> sprvList){
        Map<Integer,List<StatsPayRatioVo>> reMap = new HashMap<>(2);
        Set<Integer> keySet = new HashSet<>(2);
        for (StatsPayRatioVo sprv : sprvList) {
            Integer key = sprv.getRatioType();
            if(keySet.add(key)){
                List<StatsPayRatioVo> tmp = new ArrayList<>(2);
                tmp.add(sprv);
                reMap.put(key,tmp);
            }else{
                List<StatsPayRatioVo> tmp = reMap.get(key);
                tmp.add(sprv);
                reMap.put(key,tmp);
            }
        }
        return reMap;
    }

    /**
     *  根据交纳基数和交纳比例区间值  获取交纳的比例
     * @param cn  交纳基数
     * @param sprvList  交纳比例区间值集合
     * @return
     */
    public static Double findProportion(Integer cn,List<StatsPayRatioVo> sprvList){
        Double proportion = null;
        for (StatsPayRatioVo sprv : sprvList) {
            if(sprv.getUpperLimit()==null){
                sprv.setUpperLimit(Integer.MAX_VALUE);
            }
            if (cn >= sprv.getLowerLimit() && cn <= sprv.getUpperLimit()) {
                proportion = sprv.getProportion();
                break;
            }
        }
        return proportion;
    }

    /**
     * 信息处理，根据统计维度对数据进行统计计算
     * @param memberMap 人员交纳标准数据组
     * @param logMap  人员已交纳数据组
     * @param sprvMap 人员党费交纳比例
     * @param statsDate 统计日期
     * @param mode  统计维度  offic 党委  org 党支部
     * @param partyNumFlag  是否刷新党员人数标记   NO 否  YES是 默认YES
     * @return
     */
    private static List<StatsRedisVo> setStatsRedis(Map<String, List<StatsPartyMemberVo>> memberMap,
                                                    Map<Long, StatsPayLogVo> logMap, Map<Integer,List<StatsPayRatioVo>> sprvMap,
                                                    String statsDate,String mode,PpmdStatisticsService statisticsService,
                                                    String partyNumFlag,Integer partyNumVariate, OrgService orgService,
                                                    String politicalCode) {
        /**
         * 如果需要更新组织党员人数，收集这次需要查询用户中心的组织编号，用于批量调用
         * 2019.07.01 tc
         */
        //是否需要去用户中心获取党员人数   true 需要   false 不需要
        boolean b = (partyNumFlag==null||PARTYNUMYES.equals(partyNumFlag))&&(partyNumVariate==null||partyNumVariate==0);
        //收集查询组织编号
        Set<Long> queryOrgId = new HashSet(10);
        //查询结果
        Map<Long, OrgUserCountForm> orgInfoMap = new HashMap<>(10);
        if(b){
            memberMap.keySet().stream().forEach(ofid -> {
                List<StatsPartyMemberVo> ofspm = memberMap.get(ofid);
                queryOrgId.add(Long.valueOf(ofid));
                for (StatsPartyMemberVo of : ofspm) {
                    queryOrgId.add(of.getOrgId());
                }
            });
            //批量查询用户中心
            Long s= System.currentTimeMillis();
            List<OrgUserCountForm> re = findPartyMember(orgService, new ArrayList<>(queryOrgId), politicalCode);
            Long e= System.currentTimeMillis();
            log.debug("批量查询党员人数远程调用完成，耗时:{}ms",(e-s));
            //处理结果
            re.stream().forEach(form->{
                orgInfoMap.put(form.getOrgId(),form);
            });
        }
        //计算党委维度信息
        List<StatsRedisVo> frList = new ArrayList<>();
        memberMap.keySet().stream().forEach(ofid -> {
            StatsRedisVo srv = new StatsRedisVo();
            //统计党员人数记录不同支部编号的变量
            Set<Long> orgIdSet = new HashSet<>();
            //统计党员人数变量
            Integer numTotal = 0;
            //统计应交人数变量
            Integer numShould = 0;
            //统计应交金额变量
            Long payShould = 0L;
            //统计已交人数变量
            Integer numAlready = 0;
            //统计已交金额变量
            Long payAlready = 0L;
            List<StatsPartyMemberVo> ofspm = memberMap.get(ofid);
            for (StatsPartyMemberVo of : ofspm) {
                /**根据标记判断是否需要去用户中心获取党员人数  修改 tc 2019.04.04*/
                if(b){
                    /**党员人数总和(包含离退休党员)，调用用户中心接口获取  修改 tc 2018.12.04*/
                    //如果不同支部，累加支部党员人数
                    if(orgIdSet.add(of.getOrgId())){
                        OrgUserCountForm tmp = orgInfoMap.get(of.getOrgId());
                        numTotal+= tmp!=null?tmp.getUserCount():0;
                    }
                }

                StatsPayLogVo sp = logMap.get(of.getUserId());
                if(sp!=null&&(sp.getPayLogType()==1||sp.getPayLogType()==3)){
                    numAlready++;
                    payAlready += sp.getPayAlready();
                }

                /**
                 * 未设置基数党员统计为应交人数
                 * tc 2019.04.19  v1.0.4
                 *
                 * && of.getType() != 4
                 */
                if (of.getType() != 3) {
                    numShould++;
                    /**因为加入了交费进位规则配置,所以已经交费了的党员用已交金额去计算总共应交的金额*/
                    if(sp!=null){
                        payShould += sp.getPayAlready();
                    }else{
                        if (of.getType() == 2 || of.getType() == 5) {
                            payShould += NumberUtils.multiplyLong(NumberUtils.divideCeiling(of.getRevisedPartyFee()*1.0,100.0),100.0);
                        } else if(of.getType() == 1) {
                            //计算按比例应交金额
                            Integer cn = of.getCardinalNumber();
                            Double proportion = findProportion(cn,sprvMap.get(of.getRatioType()));
                            payShould += NumberUtils.multiplyLong(NumberUtils.divideCeiling(NumberUtils.multiply(cn, proportion), 10000.0),100.0);
                        } else if(of.getType() == 4){
                            //未设置基数党员
                        }
                    }
                }
            }
            //组合
            srv.setId(Long.valueOf(ofid));
            /**根据标记判断是否需要去缓存或者数据库里获取党员人数  修改 tc 2019.04.04*/
            OrgUserCountForm orgInfo = null;
            if(!b){
                //如果不去用户中心获取党员人数，就需要从缓存里或者记录表里获取原本的党员人数
                orgInfo = statisticsService.findPartNumByRedisOrDB(srv.getId(),statsDate,mode);
                numTotal = orgInfo!=null?orgInfo.getUserCount():0;
                if(PARTYNUMYES.equals(partyNumFlag)&&partyNumVariate!=null&&partyNumVariate!=0){
                    numTotal+=partyNumVariate;
                }
            }
            if(OFFICE.equals(mode)){
                srv.setName(ofspm.get(0).getOfficeName());
            }else if(ORG.equals(mode)){
                /**
                 * 根据orgId查询组织信息，因为存在总支部无人，但子支部里有人的情况，
                 * t_ppmd_party_member表里没有总支部的数据。
                 * tc 2019.01.22
                 * 根据是否查询用户中心获取组织信息，来选择获取组织名称的方式
                 *  tc 2019-07-02
                 **/
                OrgUserCountForm ob = null;
                if(b){
                    //用户中心批量获取的组织信息
                    ob = orgInfoMap.get(Long.valueOf(ofid));
                }else{
                    //通过缓存或数据库获取的组织信息
                    ob = orgInfo;
                }
                Long officeId = ob!=null?ob.getPartyId():ofspm.get(0).getOfficeId();
                String orgName = ob!=null?ob.getOrgName():ofspm.get(0).getOrgName();
                srv.setParentOrgId(officeId);
                srv.setName(orgName);
            }

            srv.setNumTotal(numTotal);
            srv.setNumShould(numShould);
            srv.setPayShould(payShould);
            srv.setNumAlready(numAlready);
            srv.setPayAlready(payAlready);
            srv.setNumOwing(numShould - numAlready);
            srv.setPayOwing(payShould - payAlready);
            srv.setStatsDate(statsDate);
            frList.add(srv);
        });
        return frList;
    }

    /**
     *  根据人员的组织编号对人员进行分组
     * @param id  被分组的组织编号(党委或者党支部编号)
     * @param tmp 被分组的组织编号临时set集合，用于同组织的数据放同一个map里
     * @param spmv 交纳人员信息对象
     * @param memberMap  分组结果容器
     * @param mode  统计模式 党委维度统计或党支部维度统计
     */
    private static void setMemberMap(String id, Set<String> tmp, StatsPartyMemberVo spmv, Map<String, List<StatsPartyMemberVo>> memberMap,String mode,String statsDate,OrgLevelInfoService orgLevelInfoService) {
        if (!StringUtils.isEmpty(id)) {
            setMemberMapHelper(id,tmp,spmv,memberMap);
            /**
             * 如果是党支部统计模式，查找是否存在上级组织，如果存在就把此人员加入到上级组织的集合里去
             *  tc 2019.01.21
             */
            if(ORG.equals(mode)){
                //查询组织层级信息
                OrgLevelInfoEntity orgLevelInfo = orgLevelInfoService.fingOrgLevelInfoByRedis(id,statsDate);
                //处理数据
                if(orgLevelInfo!=null){
                    //存入上级的组织的人员集合里
                    String parentList = orgLevelInfo.getParentList();
                    if(StringUtils.isNotEmpty(parentList)){
                        Arrays.stream(parentList.split(",")).forEach(poid->{
                            setMemberMapHelper(poid,tmp,spmv,memberMap);
                        });
                    }
                }
            }
        }
    }

    /**
     * 设置统计信息表实体类信息
     * @param srList
     * @param redisKey
     * @param mode
     * @return
     */
    private static List<StatisticsInfoEntity> setStatisticsInfo(List<StatsRedisVo> srList, String redisKey, String mode){
        List<StatisticsInfoEntity> re = new ArrayList<>();
        srList.stream().forEach(sr ->{
            StatisticsInfoEntity sie = new StatisticsInfoEntity();
            BeanUtils.copyProperties(sr,sie);
            sie.setOrgId(sr.getId());
            sie.setOrgName(sr.getName());
            sie.setRedisKey(redisKey);
            sie.setStatisticsType(OFFICE.equals(mode)?1:2);
            sie.setCreateTime(new Date());
            re.add(sie);
        });
        return re;
    }


    /**
     * 根据人员的组织编号对已交纳人员进行分组
     * @param id 被分组的组织编号(党委或者党支部编号)
     * @param tmp 被分组的组织编号临时set集合，用于同组织的数据放同一个map里
     * @param splv 已交纳人员对象
     * @param logMap  分组结果容器
     */
    /*private static void setLogMap(String id, Set<String> tmp, StatsPayLogVo splv, Map<String, List<StatsPayLogVo>> logMap) {
        if (!StringUtils.isEmpty(id)) {
            if (tmp.add(id)) {
                List<StatsPayLogVo> list = new ArrayList<>();
                list.add(splv);
                logMap.put(id, list);
            } else {
                List<StatsPayLogVo> list = logMap.get(id);
                list.add(splv);
                logMap.put(id, list);
            }
        }
    }*/

    /**
     *  根据人员的组织编号对人员进行分组的重复代码封装方法
     * @param id
     * @param tmp
     * @param spmv
     * @param memberMap
     */
    private static void setMemberMapHelper(String id, Set<String> tmp, StatsPartyMemberVo spmv, Map<String, List<StatsPartyMemberVo>> memberMap){
        if (tmp.add(id)) {
            List<StatsPartyMemberVo> list = new ArrayList<>();
            list.add(spmv);
            memberMap.put(id, list);
        } else {
            List<StatsPartyMemberVo> list = memberMap.get(id);
            list.add(spmv);
            memberMap.put(id, list);
        }
    }

    /**如果指定了刷新的组织编号，就要先取出原来集合遍历，到要获取到对应要更新的组织，复制对应属性在存入缓存
     *  因为加入了删除历史基数的情况，删除掉组织里最后一人后需要完全删除这个组织的缓存信息，
     *  实际处理时总支部下的子支部的情况需要比较刷新编号来删除没人的组织
     *  tc  2019.04.25 v1.0.4
     *
     *  tc 2019.04.28 发现并发的时候有可能脏读，要处理就得加锁，暂时废弃
     * */
    @Deprecated
    private static void putRedisByRefreshIds(String redisKey, List<StatsRedisVo> srList, StringRedisTemplate redisTemplate,
                                             List<Long> refreshIds, Map<String, List<StatsPartyMemberVo>> memberMap){
        //获取原来的缓存值
        Object r =  redisTemplate.opsForValue().get(redisKey);
        if(r!=null){
            List<StatsRedisVo> dataList =(List<StatsRedisVo>) JsonUtils.fromJson(String.valueOf(r), ArrayList.class, StatsRedisVo.class);
            //确认替换(新增)和删除的组织编号
            Set<Long> replace = new HashSet<>(5);
            Set<Long> del = new HashSet<>(5);
            for(Long id:refreshIds){
                List<StatsPartyMemberVo> sv = memberMap.get(id.toString());
                if(sv!=null){
                    replace.add(id);
                }else{
                    del.add(id);
                }
            }
            log.debug("指定了刷新统计缓存的组织编号，需要替换缓存的组织编号 replace={}  需要删除缓存的组织编号 del={} ", replace, del);

            //记录找到个数
            int find = 0;
            List<StatsRedisVo> delList = new ArrayList<>();
            for(int k=0; k<dataList.size();k++){
                StatsRedisVo dl = dataList.get(k);
                //处理的互斥标记,如果找到替换了就不查找删除了
                int f = -1;
                for(int i=0; i<srList.size();i++){
                    StatsRedisVo sr = srList.get(i);
                    if(sr.getId().equals(dl.getId())){
                        dataList.set(k,sr);
                        replace.remove(dl.getId());
                        find++;
                        f = 1;
                        break;
                    }
                }
                if(f!=1){
                    for(Long id:del){
                        if(id.equals(dl.getId())){
                            delList.add(dl);
                            find++;
                            break;
                        }
                    }
                }
                //遍历完所有需要刷新的组织就结束循环
                if(find == refreshIds.size()){
                    break;
                }
            }
            //删除需要删除的组织
            dataList.removeAll(delList);
            //添加未找到替换对象的组织
            if(replace.size()!=0){
                for(Long id:replace){
                    for(StatsRedisVo sr : srList){
                        if(sr.getId().equals(id)){
                            dataList.add(sr);
                            break;
                        }
                    }
                }
            }
            redisTemplate.opsForValue().set(redisKey, JsonUtils.toJson(dataList),Constants.STATISTICS_REDIS_KEY_TIMEOUT_DAY,TimeUnit.DAYS);
        }
    }

    private static List<OrgUserCountForm> findPartyMember(OrgService orgService, List<Long> orgIdList, String politicalCode){
        OrgUserCountQueryForm form = new OrgUserCountQueryForm();
        form.setIdList(orgIdList);
        form.setIsEmployee(Constants.STATUS_YES);
        form.setPoliticalType(politicalCode);
        return orgService.findUserCountByList(form);
    }
}