package com.goodsogood.ows.service.ppmd;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.mapper.ppmd.PpmdStatisticsMapper;
import com.goodsogood.ows.model.db.ppmd.OrgLevelInfoEntity;
import com.goodsogood.ows.model.db.ppmd.StatisticsInfoEntity;
import com.goodsogood.ows.model.vo.ppmd.*;
import com.goodsogood.ows.model.vo.sas.*;
import com.goodsogood.ows.model.vo.user.OrgUserCountForm;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.NumberUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 党费统计服务层
 * @date 2020/1/14
 */
@Service
@Log4j2
public class PpmdStatisticsService {

    @Value("${user-option.politicalCode}")
    @NotBlank
    private String politicalCode;

    private static final String MONTHWAY = "month";
    private static final String TOTALWAY = "total";

    private final PpmdStatisticsMapper ppmdStatisticsMapper;
    private final StringRedisTemplate redisTemplate;
    private final OrgService orgService;
    private final OrgLevelInfoService orgLevelInfoService;

    @Autowired
    public PpmdStatisticsService(PpmdStatisticsMapper ppmdStatisticsMapper, StringRedisTemplate redisTemplate,
                                 OrgService orgService, OrgLevelInfoService orgLevelInfoService) {
        this.ppmdStatisticsMapper = ppmdStatisticsMapper;
        this.redisTemplate = redisTemplate;
        this.orgService = orgService;
        this.orgLevelInfoService = orgLevelInfoService;
    }

    /**
     * 党委党费交纳统计总计
     *
     * @param form
     * @return
     */
    public List<StatsResultVo> unitpayTotal(StatsParamForm form) {
        return findStatsResult(form,StatisticsRedisUtil.OFFICE,TOTALWAY);
    }

    /**
     * 党委党费交纳统计单月
     *
     * @param form
     * @return
     */
    public List<StatsResultVo> unitpayMonth(StatsParamForm form) {
        return findStatsResult(form,StatisticsRedisUtil.OFFICE,MONTHWAY);
    }

    /**
     * 党支部费交纳统计总计
     *
     * @param form
     * @return
     */
    public List<StatsResultVo> branchpayTotal(StatsParamForm form) {
        return findStatsResult(form,StatisticsRedisUtil.ORG,TOTALWAY);
    }

    /**
     * 党支部费交纳统计单月
     *
     * @param form
     * @return
     */
    public List<StatsResultVo> branchpayMonth(StatsParamForm form) {
        return findStatsResult(form,StatisticsRedisUtil.ORG,MONTHWAY);
    }


    /**
     *
     * @param form
     * @param mode  维度   office 机关单位   org 党支部
     * @param way  方式  month 单月   total 汇总
     * @return
     */
    private List<StatsResultVo> findStatsResult(StatsParamForm form, String mode, String way){
        //分解插叙时间月份
        List<String> months = DateUtils.getBetweenMonthsList(form.getStartDate(),form.getEndDate(),1);
        //查询缓存
        List<StatsResultVo> re = new ArrayList<>();
        if(months.size()>0){
            List<String> monthCount = new ArrayList<>();
            /**获取查询时间段数据，并根据条件筛选结果*/
            List<StatsRedisVo> reTmp = new ArrayList<>();
            months.stream().forEach(m->{
                monthCount.add(m);
                //获取单月数据
                List<StatsRedisVo> tmp = StatisticsRedisUtil.findStatisticsRedis(redisTemplate,this,m,mode,orgLevelInfoService, orgService, politicalCode);
                //根据条件筛选
                tmp.stream().forEach(t->{
                    //需要满足条件计数
                    int condition = 0;
                    //已满足条件计数
                    int meet = 0;
                    if(form.getOrgId()!=null){
                        condition ++;
                        if(form.getOrgId().equals(t.getId())){
                            meet++;
                        }
                    }
                    if(!StringUtils.isEmpty(form.getOrgName())){
                        condition ++;
                        if(t.getName().indexOf(form.getOrgName())!=-1){
                            meet++;
                        }
                    }
                    if(form.getExamine()!=null){
                        condition ++;
                        if(form.getExamine().equals(t.getExamine())){
                            meet++;
                        }
                    }
                    if(form.getParentOrgId()!=null){
                        condition ++;
                        if(form.getParentOrgId().equals(t.getParentOrgId())){
                            meet++;
                        }
                    }
                    //所有条件都满足时
                    if(meet>=condition){
                        reTmp.add(t);
                    }
                });
            });

            /**结果根据编号和日期排序*/
            Collections.sort(reTmp, new Comparator<StatsRedisVo>(){
                @Override
                public int compare(StatsRedisVo o1, StatsRedisVo o2) {
                    //按照StatsRedisVo的id字段进行升序排列，如果id相同则按statsDate字段升序
                    if(o1.getId() > o2.getId()){
                        return 1;
                    }
                    if(o1.getId().equals(o2.getId())){
                        return o1.getStatsDate().compareTo(o2.getStatsDate());
                    }
                    return -1;
                }
            });

            /**组合结果*/
            /**记录统计 临时变量 开始*/
            Long orgId = reTmp.size()>0?reTmp.get(0).getId():null;
            String orgName = null;
            Integer examine = null;
            Long payShould = 0L;
            Long payAlready = 0L;
            Long payOwing = 0L;
            //汇总统计时，统计时间内每月在组织党员人数总和
            Integer numTotal=0;
            //汇总统计时，统计时间内每月应交人数总和
            Integer numShould = 0;
            //汇总统计时，统计时间内每月已交人数总和
            Integer numAlready = 0;
            //汇总统计时，统计时间内每月欠交人数总和
            Integer numOwing = 0;
            /**统计组织类型判断 1 党委  2 支部  修改 tc 2018.12.05*/
            Integer orgType = null;
            /**记录统计 临时变量 结束*/
            for(StatsRedisVo ret:reTmp){
                //如果方式为 total 汇总,进行汇总计算
                if(TOTALWAY.equals(way)){
                    //相同id的计算汇总
                    if(ret.getId().equals(orgId)){
                        orgType = ret.getOrgType();
                        payShould+=ret.getPayShould();
                        payAlready+=ret.getPayAlready();
                        payOwing+=ret.getPayOwing();
                        /**统计人数总和  修改 tc 2018.11.21*/
                        numTotal+=ret.getNumTotal();
                        numShould+=ret.getNumShould();
                        numAlready+=ret.getNumAlready();
                        numOwing+=ret.getNumOwing();
                    }else {
                        StatsResultVo srv = new StatsResultVo();
                        srv.setOrgId(orgId);
                        srv.setOrgName(orgName);
                        srv.setExamine(examine);
                        srv.setStatsDate(DateUtils.toFormat(months.get(0),null,null)+"-"+DateUtils.toFormat(months.get(months.size()-1),null,null));
                        srv.setPayShould(NumberUtils.divide(payShould,100));
                        srv.setPayAlready(NumberUtils.divide(payAlready,100));
                        srv.setPayOwing(NumberUtils.divide(payOwing,100));
                        srv.setMonthCount(monthCount.size());
                        /**统计组织类型判断 1 党委  2 支部  修改 tc 2018.12.05*/
                        srv.setOrgType(orgType);
                        /**统计人数总和  修改 tc 2018.11.21*/
                        if(form.getPersonTime()==1){
                            srv.setNumTotal(numTotal);
                            srv.setNumShould(numShould);
                            srv.setNumAlready(numAlready);
                            srv.setNumOwing(numOwing);
                        }
                        re.add(srv);

                        //初始化为当前
                        payShould = ret.getPayShould();
                        payAlready = ret.getPayAlready();
                        payOwing = ret.getPayOwing();
                        /**统计人数总和   修改 tc 2018.11.21*/
                        numTotal = ret.getNumTotal();
                        numShould = ret.getNumShould();
                        numAlready = ret.getNumAlready();
                        numOwing = ret.getNumOwing();
                        /**统计组织类型判断 1 党委  2 支部  修改 tc 2018.12.05*/
                        orgType = ret.getOrgType();
                    }
                    orgId = ret.getId();
                    orgName = ret.getName();
                    examine = ret.getExamine();
                }else if(MONTHWAY.equals(way)){
                    StatsResultVo srv = new StatsResultVo();
                    BeanUtils.copyProperties(ret,srv);
                    srv.setOrgId(ret.getId());
                    srv.setOrgName(ret.getName());
                    //转化金额单位，分 --> 元
                    srv.setPayShould(NumberUtils.divide(ret.getPayShould(),100));
                    srv.setPayAlready(NumberUtils.divide(ret.getPayAlready(),100));
                    srv.setPayOwing(NumberUtils.divide(ret.getPayOwing(),100));
                    re.add(srv);
                }
            }
            if(reTmp.size()>0){
                //设置最后一个
                if(TOTALWAY.equals(way)){
                    StatsResultVo srv = new StatsResultVo();
                    srv.setOrgId(orgId);
                    srv.setOrgName(orgName);
                    srv.setExamine(examine);
                    srv.setStatsDate(DateUtils.toFormat(months.get(0),null,null)+"-"+DateUtils.toFormat(months.get(months.size()-1),null,null));
                    srv.setPayShould(NumberUtils.divide(payShould,100));
                    srv.setPayAlready(NumberUtils.divide(payAlready,100));
                    srv.setPayOwing(NumberUtils.divide(payOwing,100));
                    srv.setMonthCount(monthCount.size());
                    /**统计人数总和  修改 tc 2018.11.21*/
                    if(form.getPersonTime()==1){
                        srv.setNumTotal(numTotal);
                        srv.setNumShould(numShould);
                        srv.setNumAlready(numAlready);
                        srv.setNumOwing(numOwing);
                    }
                    /**统计组织类型判断 1 党委  2 支部  修改 tc 2018.12.05*/
                    srv.setOrgType(orgType);
                    re.add(srv);
                }
            }
        }
        return re;
    }


    /**
     * 根据缓存Key获取历史统计数据
     * @param redisKey
     * @return
     */
    public List<StatsRedisVo> findStatisticsInfo(String redisKey){
        return ppmdStatisticsMapper.findStatisticsInfo(redisKey,null,null);
    }

    /**
     * 根据指定的2级维度组织编号和3级维度组织编号，获取这些党员生效的交纳标准
     * 当指定了获取
     * tc 2019.01.24  v1.0.3.1
     * @param statsDate
     * @param mode 统计模式   office只统计党委 org 只统计党支部，一次只支持单个模式刷新
     * @param refreshIds  刷新的组织编号
     * @return
     */
    public List<StatsPartyMemberVo> findStatsPartyMember(String statsDate, String mode, List<Long> refreshIds){
        return ppmdStatisticsMapper.findStatsPartyMemberByIdList(statsDate,mode,refreshIds);
    }

    /**
     * 获取生效的交纳标准
     * @param statsDate
     * @return
     */
    public List<StatsPartyMemberVo> findStatsPartyMember(String statsDate){
        return ppmdStatisticsMapper.findStatsPartyMember(statsDate);
    }

    /**
     * 获取生效的交纳比例
     * @param statsDate
     * @return
     */
    public List<StatsPayRatioVo> findStatsPayRatio(String statsDate){
        return ppmdStatisticsMapper.findStatsPayRatio(statsDate);
    }

    /**
     * 获取已经交纳记录
     * @param statsDate
     * @return
     */
    public List<StatsPayLogVo> findStatsPayLog(String statsDate){
        return ppmdStatisticsMapper.findStatsPayLog(statsDate);
    }

    /**
     *  根据刷新编号去保存统计信息
     * @param statisticsInfoList
     * @param redisKey
     * @return
     * tc  2019.01.24
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveStatisticsInfoByRefreshIds(List<StatisticsInfoEntity> statisticsInfoList, String redisKey, List<Long> refreshIds){
        boolean re = true;
        try{
            //先根据redisKey删除之前统计的历史记录
            ppmdStatisticsMapper.delStatisticsInfoByRefreshIds(redisKey,refreshIds);
            //添加重新统计的记录
            if(statisticsInfoList!=null&&statisticsInfoList.size()>0){
                ppmdStatisticsMapper.insertList(statisticsInfoList);
            }
        }catch(Exception e){
            re = false;
            log.error("保存统计信息，出错  redisKey ={}",redisKey, e);
            // 配合Transactional手动让spring回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return re;
    }

    /**
     *  保存统计信息
     * @param statisticsInfoList
     * @param redisKey
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveStatisticsInfo(List<StatisticsInfoEntity> statisticsInfoList,String redisKey){
        boolean re = true;
        try{
            //先根据redisKey删除之前统计的历史记录
            ppmdStatisticsMapper.delStatisticsInfoByRedisKey(redisKey);
            //添加重新统计的记录
            if(statisticsInfoList!=null&&statisticsInfoList.size()>0){
                ppmdStatisticsMapper.insertList(statisticsInfoList);
            }
        }catch(Exception e){
            re = false;
            log.error("保存统计信息，出错  redisKey ={}",redisKey, e);
            // 配合Transactional手动让spring回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return re;
    }

    /**
     *  从缓存里或者记录表里获取原本的党员人数
     * @param orgId        组织编号  ORG 模式就是支部编号，OFFICE 模式就是党委编号
     * @param statsDate   统计日期
     * @param mode        模式 ORG支部   OFFICE 党委
     */
    public OrgUserCountForm findPartNumByRedisOrDB(Long orgId, String statsDate, String mode){
        OrgUserCountForm re = null;
        /**先查询缓存*/
        //组合redisKey
        String redisKey;
        if(StatisticsRedisUtil.OFFICE.equals(mode)){
            redisKey = Constants.STATISTICS_REDIS_OFFICE_KEY+statsDate;
        }else if(StatisticsRedisUtil.ORG.equals(mode)){
            redisKey = Constants.STATISTICS_REDIS_ORG_KEY+statsDate;
        }else {
            return re;
        }
        //获取原来的缓存值
        Object r =  redisTemplate.opsForValue().get(redisKey);
        if(r!=null){
            List<StatsRedisVo> dataList =(List<StatsRedisVo>) JsonUtils.fromJson(String.valueOf(r), ArrayList.class, StatsRedisVo.class);
            for(int i=0; i<dataList.size();i++){
                StatsRedisVo dl = dataList.get(i);
                if(orgId.equals(dl.getId())){
                    if(StatisticsRedisUtil.OFFICE.equals(mode)){
                        re = OrgUserCountForm.builder().partyId(dl.getId()).partyName(dl.getName()).userCount(dl.getNumTotal()).build();
                    }else if(StatisticsRedisUtil.ORG.equals(mode)){
                        re = OrgUserCountForm.builder().partyId(dl.getParentOrgId()).orgId(dl.getId()).orgName(dl.getName()).userCount(dl.getNumTotal()).build();
                    }
                    break;
                }
            }
        }
        //如果缓存里没有获取到，去数据库查询
        if(re!=null){
            List<StatsRedisVo> reList = ppmdStatisticsMapper.findStatisticsInfo(redisKey,orgId,statsDate);
            if(reList!=null && reList.size()>0){
                StatsRedisVo dl = reList.get(0);
                if(StatisticsRedisUtil.OFFICE.equals(mode)){
                    re = OrgUserCountForm.builder().partyId(dl.getId()).partyName(dl.getName()).userCount(dl.getNumTotal()).build();
                }else if(StatisticsRedisUtil.ORG.equals(mode)){
                    re = OrgUserCountForm.builder().partyId(dl.getParentOrgId()).orgId(dl.getId()).orgName(dl.getName()).userCount(dl.getNumTotal()).build();
                }
            }
        }
        return re;
    }

    /**
     * 根据组织编号获取组织所有下级组织（包含本组织）
     * @param orgId
     * @param queryDate
     * @return
     */
    private List<Long> findChildList(Long orgId,String queryDate){
        List<Long> orgIdList = new ArrayList(1);
        orgIdList.add(orgId);
        OrgLevelInfoEntity oli = this.orgLevelInfoService.fingOrgLevelInfoByRedis(orgId.toString(),queryDate);
        if(oli!=null){
            String child = oli.getChildList();
            if(!StringUtils.isEmpty(child)){
                Arrays.stream(child.split(",")).forEach(id->{
                    orgIdList.add(Long.valueOf(id));
                });
            }
        }
        return orgIdList;
    }

    /**
     * 党员交费查询
     *
     * @param payInfoForm
     * @return
     */
    public List<UserPayInfoVo> findPayInfo(QueryUserInfoForm payInfoForm) {
        List<UserPayInfoVo> infoList = new ArrayList<>(10);
        //收集党员编号
        List<Long> userList = Arrays.stream(payInfoForm.getUserIdList().split(",")).map(id->Long.valueOf(id)).collect(Collectors.toList());

        if(userList!=null&&userList.size()>0){
            PayDetailsForm form = new PayDetailsForm();
            form.setQueryDate(payInfoForm.getDate());
            //查询党员已经党费
            List<PayDetailsLogAppVo> pdlList = ppmdStatisticsMapper.findBranchPayDetailsAppPayLog(form,userList);
            pdlList.stream().forEach(pd->{
                UserPayInfoVo infoVo = new UserPayInfoVo();
                infoVo.setUserId(pd.getUserId());
                infoVo.setMoney(pd.getPayAlready());
                Integer type = 1;
                if(pd.getPayLogType()!=1&&pd.getPayLogType()!=2&&pd.getPayLogType()!=3){
                    type = 2;
                }
                infoVo.setType(type);
                infoList.add(infoVo);
            });
        }
        return infoList;
    }
}
