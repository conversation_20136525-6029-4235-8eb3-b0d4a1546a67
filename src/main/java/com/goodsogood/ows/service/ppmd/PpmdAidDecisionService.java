package com.goodsogood.ows.service.ppmd;

import com.goodsogood.ows.mapper.ppmd.PpmdAidDecisionMapper;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.model.mongodb.dss.*;
import com.goodsogood.ows.model.vo.ppmd.PpmdAidDecisionVo;
import com.goodsogood.ows.service.impl.DssIndexBuilder;
import com.goodsogood.ows.service.impl.DssPartyBranchBuilder;
import com.goodsogood.ows.service.impl.DssPartyCommitteeBuilder;
import com.goodsogood.ows.service.impl.DssUserBuilder;
import com.goodsogood.ows.utils.NumberUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 辅助决策党费报表
 *
 * <AUTHOR> tc
 * @date 2020/11/9
 */
@Service
@Log4j2
public class PpmdAidDecisionService implements DssIndexBuilder, DssPartyCommitteeBuilder,
        DssPartyBranchBuilder, DssUserBuilder {

    private final PpmdAidDecisionMapper ppmdAidDecisionMapper;

    @Autowired
    public PpmdAidDecisionService(PpmdAidDecisionMapper ppmdAidDecisionMapper) {
        this.ppmdAidDecisionMapper = ppmdAidDecisionMapper;
    }

    @Override
    public IndexInfo buildIndex(IndexInfo info) {
        /**
         * 初始化
         */
        if(info.getPayInfo()==null){
            info.setPayInfo(new PayInfo());
            info.getPayInfo().setPayCompletionMap(new PayCompletionMap());
            info.getPayInfo().setPayTrendMap(new PayTrendMap());
        }
        this.findOrgInfo(info.getRegionId(),info.getYear(),info.getRootId(),info,IndexInfo.class);
        return info;
    }

    @Override
    public PartyBranchInfo buildPartyBranch(PartyBranchInfo info) {
        /**
         * 初始化
         */
        if(info.getPayInfo()==null){
            info.setPayInfo(new PayInfo());
            info.getPayInfo().setPayCompletionMap(new PayCompletionMap());
            info.getPayInfo().setPayTrendMap(new PayTrendMap());
        }
        this.findOrgInfo(info.getRegionId(),info.getYear(),info.getOrganizationId(),info,PartyBranchInfo.class);
        return info;
    }


    @Override
    public PartyCommitteeInfo buildPartyCommittee(PartyCommitteeInfo info) {
        /**
         * 初始化
         */
        if(info.getPayInfo()==null){
            info.setPayInfo(new PayInfo());
            info.getPayInfo().setPayCompletionMap(new PayCompletionMap());
            info.getPayInfo().setPayTrendMap(new PayTrendMap());
        }
        this.findOrgInfo(info.getRegionId(),info.getYear(),info.getOrganizationId(),info,PartyCommitteeInfo.class);
        return info;
    }


    @Override
    public UserInfo buildUser(UserInfo info) {
        /**
         * 初始化
         */
        if(info.getPayInfo()==null){
            info.setPayInfo(new UserPayInfo());
        }
        this.findUserInfo(info);
        return info;
    }


    /**
     * 根据年份确定月份值,如果是当年月份为当前月份，历史年份使用12月
     */
    private String getMonth(Integer year){
        Integer yearNow = LocalDate.now().getYear();
        String month = "12";
        if(yearNow.equals(year)){
            Integer m = LocalDate.now().getMonthValue();
            month=m<10?"0"+m:""+m;
        }
        return month;
    }

    /**
     * 首页、党委、党支部通用设置数据类
     */
    private void findOrgInfo(Long regionId,Integer year, Long orgId, DssBaseInfo info, Class className){
        try{
            /**
             * 获取要设置的参数
             */
            PayInfo payInfo = (PayInfo) ReflectionUtils.findMethod(className,"getPayInfo").invoke(info);
            /**
             * 年度党费交纳统计
             */
            PpmdAidDecisionVo vo1 = new PpmdAidDecisionVo();
            if(className.isAssignableFrom(IndexInfo.class)){
                //首页报表的党费统计
                vo1 = ppmdAidDecisionMapper.allOrgPayAlready(regionId,year.toString());
            }else {
                //党委、党支部的党费统计
                vo1 = ppmdAidDecisionMapper.orgPayAlready(regionId,year.toString(),orgId);
            }
            payInfo.setCurYear(vo1.getCurYear());

            /**
             * 党费交纳完成进度和每月一号交纳完成进度汇总多月
             */
            List<Integer> complete = new ArrayList<>(12);
            List<Integer> incomplete = new ArrayList<>(12);
            List<Double> person = new ArrayList<>(12);
            List<Double> money = new ArrayList<>(12);
            for(int i=1;i<=Integer.valueOf(getMonth(year));i++){
                //循环年月
                String m = i<10?"0"+i:String.valueOf(i);
                PpmdAidDecisionVo vo2 = new PpmdAidDecisionVo();
                String queryMonth = year+"-"+m;
                if(className.isAssignableFrom(PartyBranchInfo.class)){
                    //党支部报表的党费完成进度
                    vo2 = ppmdAidDecisionMapper.orgMemberPayFinish(regionId,queryMonth,orgId);
                }else {
                    if(className.isAssignableFrom(IndexInfo.class)){
                        //首页
                        vo2 = ppmdAidDecisionMapper.orgPayFinish(regionId,queryMonth,orgId);
                    }else {
                        //党委
                        vo2 = ppmdAidDecisionMapper.orgPartyPayFinish(regionId,queryMonth,orgId);
                    }
                }
                complete.add(vo2.getFinish());
                incomplete.add(vo2.getUnfinish());

                PpmdAidDecisionVo vo3 = new PpmdAidDecisionVo();
                if(className.isAssignableFrom(IndexInfo.class)){
                    //首页报表
                    vo3 = ppmdAidDecisionMapper.allOrgPayMonthFirst(regionId,queryMonth);
                }else {
                    vo3 = ppmdAidDecisionMapper.orgPayMonthFirst(regionId,queryMonth,orgId);
                }
                if(vo3.getNumShould()!=0&&vo3.getPayShould()!=0){
                    person.add(NumberUtils.divide(NumberUtils.multiplyDouble(vo3.getNumAlready(),100.0),vo3.getNumShould()));
                    money.add(NumberUtils.divide(NumberUtils.multiplyDouble(vo3.getPayAlready(),100.0),vo3.getPayShould()));
                }else {
                    if(i>Integer.valueOf(getMonth(year))){
                        //如果是未来月份
                        person.add(0.0);
                        money.add(0.0);
                    }else{
                        person.add(1.0);
                        money.add(1.0);
                    }
                }
            }
            payInfo.getPayCompletionMap().setComplete(complete);
            payInfo.getPayCompletionMap().setIncomplete(incomplete);
            payInfo.getPayTrendMap().setPerson(person);
            payInfo.getPayTrendMap().setMoney(money);
        }catch(Exception e){
            log.error("<党费模块> 首页，党委，党支部通用的设置数据方法,报错！ year={} orgId={} className ={}",year,orgId,className,e);
        }
    }

    /**
     * 党员页面设置数据
     * @param info
     */
    private void findUserInfo(UserInfo info){
        try {
            UserPayInfo userPayInfo = info.getPayInfo();
            String queryMonth = info.getYear()+"-"+getMonth(info.getYear());
            /**
             * 党员党费标准
             */
            UserPayInfo vo1 = ppmdAidDecisionMapper.memberCardinalNumber(info.getRegionId(),queryMonth,info.getUserId());
            userPayInfo.setStandard(vo1!=null?vo1.getStandard():null);
            /**
             * 党员交费排名
             */
            List<PayInitiative> trend = new ArrayList<>(12);
            for(int i=1;i<=Integer.valueOf(getMonth(info.getYear()));i++){
                //循环年月
                String m = i<10?"0"+i:String.valueOf(i);
                String query = info.getYear()+"-"+m;
                PayInitiative payInitiative = new PayInitiative();
                PpmdAidDecisionVo vo2 =  ppmdAidDecisionMapper.memberRank(info.getRegionId(),query,info.getUserId());
                payInitiative.setDay(vo2.getPayDay()!=null?Integer.valueOf(vo2.getPayDay()):null);
                payInitiative.setMonth(i);
                payInitiative.setLimit(vo2.getRank()!=0?vo2.getRank():null);
                if(vo2.getNumShould()!=0&&vo2.getRank()!=0){
                    payInitiative.setRanking(NumberUtils.divide(NumberUtils.multiplyDouble(vo2.getRank(),100.0),vo2.getNumShould()));
                }else{
                    payInitiative.setRanking(null);
                }
                trend.add(payInitiative);
            }
            userPayInfo.setTrend(trend);
        }catch(Exception e){
            log.error("<党费模块> 用户页面设置数据，报错！ info={}",info,e);
        }
    }

}
