package com.goodsogood.ows.service.ppmd;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.mapper.ppmd.OrgPayMapper;
import com.goodsogood.ows.mapper.ppmd.PartyMemberMapper;
import com.goodsogood.ows.mapper.ppmd.PayLogMapper;
import com.goodsogood.ows.model.db.ppmd.PayLogEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.PpmdPayInfo;
import com.goodsogood.ows.model.vo.ppmd.ActiveOrgInfo;
import com.goodsogood.ows.model.vo.ppmd.ActiveUserInfo;
import com.goodsogood.ows.model.vo.ppmd.PayRankVo;
import com.goodsogood.ows.model.vo.sas.*;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.maxBy;

/**
 * <AUTHOR>
 * @description 党费统计服务层
 * @date 2019/11/19
 */
@Service
@Log4j2
public class OrgPayService {

    private final OrgPayMapper orgPayMapper;
    private final PayLogMapper payLogMapper;
    private final AsyncOrgPayService asyncOrgPayService;
    private final OrgService orgService;
    private final StringRedisTemplate redisTemplate;
    private final PartyMemberMapper partyMemberMapper;
    private final SimpleApplicationConfigHelper configHelper;

    @Autowired
    public OrgPayService(OrgPayMapper orgPayMapper, PayLogMapper payLogMapper, AsyncOrgPayService asyncOrgPayService,
                         OrgService orgService, StringRedisTemplate redisTemplate, PartyMemberMapper partyMemberMapper,
                         SimpleApplicationConfigHelper configHelper) {
        this.orgPayMapper = orgPayMapper;
        this.payLogMapper = payLogMapper;
        this.asyncOrgPayService = asyncOrgPayService;
        this.orgService = orgService;
        this.redisTemplate = redisTemplate;
        this.partyMemberMapper = partyMemberMapper;
        this.configHelper = configHelper;
    }

    /**
     * 查询党组织用户信息列表
     * <AUTHOR>
     * @date 2019/11/20
     * @param orgId
     * @param queryTime
     * @param payInfo
     * @return void
     */
    public void getPpmdUserList(Long orgId, String queryTime, PpmdPayInfo payInfo){
        // 查询应交人员的党费缴纳情况
        List<PayLogEntity> payLogList = this.orgPayMapper.getOrgPayByOrgId(orgId, queryTime);
        List<PpmdPayInfo.User> userList = this.getPpmdUserList(payLogList, payInfo.getOrgLevel(), payInfo.getOrgLevel());
        payInfo.setUserList(userList);
    }

    /**
     * 对党费流水进行分类
     * <AUTHOR>
     * @date 2019/12/6
     * @param payLogList
     * @param queryTime
     * @return java.util.List<com.goodsogood.ows.model.mongodb.PpmdPayInfo>
     */
    @Transactional(rollbackFor = Exception.class)
    public List<PpmdPayInfo> getPpmdOrgList(List<PayLogEntity> payLogList, String queryTime) {
        List<PpmdPayInfo> payInfoList = new ArrayList<>();
        // 根据officeId进行分组
        Map<Long, List<PayLogEntity>> partyCollect =
                payLogList.stream().collect(Collectors.groupingBy(PayLogEntity::getOfficeId));

        for (Map.Entry<Long, List<PayLogEntity>> partyPayLogMap: partyCollect.entrySet()) {
            List<PayLogEntity> partyPayLogList = partyPayLogMap.getValue();
            // 根据orgId进行分组
            Map<Long, List<PayLogEntity>> OrgCollect =
                    partyPayLogList.stream().collect(Collectors.groupingBy(PayLogEntity::getOrgId));
            Long officeId = partyPayLogMap.getKey();
            // 党委的党费实体类
            PpmdPayInfo partyPayInfo = new PpmdPayInfo();
            partyPayInfo.setOrgId(officeId);
            // 获取当前组织的regionID
            OrganizationEntity party = this.orgService.getById(officeId);
            Long regionId = party.getRegionId();
            partyPayInfo.setRegionId(regionId);
            Region.OrgData orgData = this.configHelper.getOrgByRegionId(regionId);
            if (null != party) {
                partyPayInfo.setOrgName(party.getName());
                partyPayInfo.setOrgType(party.getOrgType());
                partyPayInfo.setOrgTypeChild(party.getOrgTypeChild());
                partyPayInfo.setOrgCreateTime(party.getOrgCreateTime());
                partyPayInfo.setStatsDate(queryTime);
                partyPayInfo.setParentId(party.getParentId());
                partyPayInfo.setOrgLevel(party.getOrgLevel());
            }
            for (Map.Entry<Long, List<PayLogEntity>> orgPayLogMap: OrgCollect.entrySet()) {
                Long orgId = orgPayLogMap.getKey();
                List<PayLogEntity> orgPayLogValue = orgPayLogMap.getValue();
                // 判断组织是否在当前月份转过组织
                Set<String> orgLevelSet = new HashSet<>();
                try {
                    orgLevelSet = orgPayLogValue.stream().filter(payLog -> (payLog.getPayLogType() == 1 || payLog.getPayLogType() == 3))
                            .map(PayLogEntity::getOrgLevel).collect(Collectors.toSet());
                } catch (Exception e) {
                    log.error("当前组织没有查询到有效的层级关系");
                    orgLevelSet.add("");
                }
                // 如果存在多个层级关系, 计算有效的层级关系
                String validLevel = "";
                try {
                    validLevel = orgPayLogValue.stream().filter(payLog ->
                            (StringUtils.isNotBlank(payLog.getOrgLevel()) && (payLog.getPayLogType() == 1 || payLog.getPayLogType() == 3)))
                            .collect(maxBy(Comparator.comparing(PayLogEntity::getCreateTime))).get().getOrgLevel();
                } catch (Exception e) {
                    log.error("当前组织没有查询到有效的层级关系");
                    validLevel = "";
                }
                if (!CollectionUtils.isEmpty(orgLevelSet)) {
                    for (String orgLevel : orgLevelSet) {
                        PpmdPayInfo payInfo = new PpmdPayInfo();
                        payInfo.setOrgId(orgId);
                        payInfo.setStatsDate(queryTime);
                        if (StringUtils.isNotBlank(orgLevel)) {
                            payInfo.setOrgLevel(orgLevel);
                            String[] str = orgLevel.split("-");
                            if (str.length != 0) {
                                String parentId = str[str.length - 1];
                                payInfo.setParentId(Long.valueOf(parentId));
                                for (String orgIdStr : str) {
                                    if (StringUtils.isNotBlank(orgIdStr)) {
                                        Long orgIdL = Long.valueOf(orgIdStr);
                                        if (orgIdL >= orgData.getOrgId()) {
                                            String parentOrgaLevel = orgLevel.substring(0, orgLevel.indexOf(orgIdStr));
                                            PpmdPayInfo parentPayInfo = new PpmdPayInfo();
                                            OrganizationEntity entity = this.orgService.getById(orgIdL);
                                            if(entity != null) {
                                                parentPayInfo.setOrgId(orgIdL);
                                                parentPayInfo.setOrgName(entity.getName());
                                                parentPayInfo.setOrgCreateTime(entity.getOrgCreateTime());
                                                parentPayInfo.setOrgType(entity.getOrgType());
                                                parentPayInfo.setOrgTypeChild(entity.getOrgTypeChild());
                                                parentPayInfo.setStatsDate(queryTime);
                                                parentPayInfo.setOrgLevel(parentOrgaLevel);
                                                parentPayInfo.setRegionId(entity.getRegionId());
                                                String[] split = parentOrgaLevel.split("-");
                                                parentPayInfo.setParentId(Long.valueOf(split[split.length - 1]));
                                                this.getPpmdUserList(orgIdL, queryTime, parentPayInfo);
                                                payInfoList.add(parentPayInfo);
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            if (!officeId.equals(orgId)) {
                                payInfo.setOrgLevel(Constants.INIT_ORG_LEVEL + orgData.getOrgId() + "-" + officeId + "-");
                                payInfo.setParentId(officeId);
                            } else {
                                payInfo.setOrgLevel(Constants.INIT_ORG_LEVEL+ orgData.getOrgId() + "-");
                                payInfo.setParentId(orgData.getOrgId());
                            }
                        }
                        OrganizationEntity org = this.orgService.getById(orgId);
                        if (null != org) {
                            payInfo.setRegionId(org.getRegionId());
                            payInfo.setOrgName(org.getName());
                            payInfo.setOrgType(org.getOrgType());
                            payInfo.setOrgTypeChild(org.getOrgTypeChild());
                            payInfo.setOrgCreateTime(org.getOrgCreateTime());
                        }
                        List<PpmdPayInfo.User> ppmdUserList = this.getPpmdUserList(orgPayLogValue, orgLevel, validLevel);
                        payInfo.setUserList(ppmdUserList);
                        payInfoList.add(payInfo);
                    }
                }
            }
            payInfoList.add(partyPayInfo);
        }
        return payInfoList;
    }

    private  List<PpmdPayInfo.User> getPpmdUserList(List<PayLogEntity> payLogList, String orgLevel, String validLevel) {
        List<PpmdPayInfo.User> userList = new ArrayList<>();
        List<Long> userIdList = new ArrayList<>();
        orgLevel = StringUtils.isBlank(orgLevel) ? "" : orgLevel;
        for (PayLogEntity payLogEntity : payLogList) {
            String level = StringUtils.isBlank(payLogEntity.getOrgLevel()) ? "" : payLogEntity.getOrgLevel();
            if (userIdList.contains(payLogEntity.getUserId())) { continue; }

            // 判断当前数据缴纳人的缴纳状态
            // 如果为缴纳状态 -> 1-5月份中旬, 组织层级关系未有效记录
            //                 5月份下旬以后则 记录了层级关系, 则需要判断当前数据的层级关系与组织层级关系是否一致
            // 如果为欠交状态 -> 欠交数据未记录层级关系， 则需要需要挂靠到有效的层级组织关系
            if (payLogEntity.getPayLogType().equals(1) || payLogEntity.getPayLogType().equals(3) ) {
                if (!level.equals(orgLevel)) { continue; }
            } else {
                if (!orgLevel.equals(validLevel)){ continue; }
            }

            Long ppmdAmount = 0L;
            Long amount = 0L;
            List<String> payFor = new ArrayList<>();
            PpmdPayInfo.User user = new PpmdPayInfo().new User();
            user.setUserId(payLogEntity.getUserId());
            user.setUserName(payLogEntity.getUserName());
            for (PayLogEntity payLog : payLogList) {
                String level2 = StringUtils.isBlank(payLog.getOrgLevel()) ? "" : payLog.getOrgLevel();
                if (!level.equals(level2)) {continue;}
                if (payLogEntity.getUserId().equals(payLog.getUserId())) {
                    // 判断缴纳日志状态为，正常交纳与补交
                    if (payLog.getPayLogType().equals(1) || payLog.getPayLogType().equals(3)) {
                        user.setStatus(1);
                        // 金额叠加
                        amount += payLog.getPayAlready();
                        ppmdAmount += payLog.getPayAlready();
                        // 保存缴纳的是哪月的党费
                        payFor.add(payLog.getPayFor());
                    }
                    // 判断缴纳日志状态为欠交
                    if (payLog.getPayLogType().equals(4) || payLog.getPayLogType().equals(5)) {
                        user.setStatus(0);
                        // 金额叠加
                        ppmdAmount += (long) Math.abs(payLog.getPayAlready());
                        // 保存是哪月的党费
                        payFor.add(payLog.getPayFor());
                    }
                    // 免交
                    if (payLog.getPayLogType().equals(2)) {
                        user.setStatus(2);
                    }
                    // 写入缴纳党费时间
                    if (user.getPayDate() == null) {
                        user.setPayDate(payLog.getPayDate());
                    } else {
                        if (payLog.getPayDate() != null) {
                            if (payLog.getPayDate().before(user.getPayDate())) {
                                user.setPayDate(payLog.getPayDate());
                            }
                        }
                    }
                }
            }
            user.setPayAmount(amount / 100d);
            user.setPpmdAmount(ppmdAmount / 100d);
            user.setPpmdDate(payFor);
            userList.add(user);
            userIdList.add(payLogEntity.getUserId());
        }
        return userList;
    }

    /**
     * 查询党委党费缴纳排行榜
     * <AUTHOR>
     * @date 2019/11/20
     * @param orgId
     * @param orgLevel
     * @param queryTime
     * @param payInfo
     * @return void
     */
    public void getActivePayOrg(Long orgId, String orgLevel, String queryTime, PpmdPayInfo payInfo){
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        // 查询组织上级党委
        Long partyId = null;
        try {
            String[] str = orgLevel.split("-");
            if (str.length > 3) {
                partyId = Long.valueOf(str[4]);
            }
        } catch (Exception e) {
        }

        if (null != orgId && (null == partyId || partyId == 3)) {
            PartyOrgVo partyOrgVo = this.orgService.findPartyId(orgId);
            partyId = partyOrgVo.getPartyId();
        }
        if (null != partyId) {
            String orgKey = Constants.REDIS_KEY_REPORT_PPMD_ACTIVE_ORG_PAY + partyId + "_" + queryTime;
            String userKey = Constants.REDIS_KEY_REPORT_PPMD_ACTIVE_USER_PAY + partyId + "_" + queryTime;
            log.debug("查询当前组织的上级党委 -> [{}], 耗时 -> [{}]", partyId, stopWatch.toString());
            if (this.redisTemplate.hasKey(orgKey)) {
                List<ActiveOrgInfo> orgInfoList = (List<ActiveOrgInfo>)Utils.fromJson(this.redisTemplate.opsForList().range(orgKey, 0L, -1L).toString(), ArrayList.class, ActiveOrgInfo.class);
                if (!CollectionUtils.isEmpty(orgInfoList)) {
                    ActiveOrgInfo orgInfo = orgInfoList.stream().reduce((s1, s2) -> s1.getLastDate().compareTo(s2.getLastDate()) > 0 ? s2 : s1).get();
                    if (null != orgInfo) {
                        PpmdPayInfo.FirstOrg firstOrg = new PpmdPayInfo().new FirstOrg();
                        firstOrg.setOrgId(orgInfo.getOrgId());
                        firstOrg.setOrgName(orgInfo.getOrgName());
                        firstOrg.setPayTime(orgInfo.getLastDate());
                        payInfo.setFirstOrg(firstOrg);
                    }
                }
            }
            if (this.redisTemplate.hasKey(userKey)) {
                List<ActiveUserInfo> userInfoList = (List<ActiveUserInfo>)Utils.fromJson(this.redisTemplate.opsForList().range(userKey, 0L, -1L).toString(), ArrayList.class, ActiveUserInfo.class);
                if (!CollectionUtils.isEmpty(userInfoList)) {
                    ActiveUserInfo userInfo = userInfoList.stream().reduce((s1, s2) -> s1.getPayDate().compareTo(s2.getPayDate()) > 0 ? s2 : s1).get();
                    if (null != userInfo) {
                        PpmdPayInfo.FirstUser firstUser = new PpmdPayInfo().new FirstUser();
                        firstUser.setUserName(userInfo.getUserName());
                        firstUser.setPayTime(userInfo.getPayDate());
                        payInfo.setFirstUser(firstUser);
                    }
                }
            }
        }
        stopWatch.stop();
    }

    /**
     * 党务看板拉取党费数据
     * <AUTHOR>
     * @date 2019/12/19
     * @param form
     * @return java.util.List<com.goodsogood.ows.model.vo.sas.SasOrgPaySituationForm>
     */
    public List<SasOrgPaySituationForm> getOrgPaySituation(SasOrgPayForm form) {
        List<SasOrgPaySituationForm> list = new ArrayList<>();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        // 判断时间格式
        String date = form.getDate();
        // 指定日期格式为四位年/两位月份，注意yyyy-MM区分大小写；
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        try {
            // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期
            format.setLenient(false);
            Date parseDate = format.parse(date);
            form.setDate(format.format(parseDate));
            log.info("当前消耗时间 -> [{}]", stopWatch.toString());
            // 调用服务层查询组织党费统计
            list = this.asyncOrgPayService.getOrgPaySituation(form, stopWatch);
        } catch (Exception e) {
            log.error("查询党费信息报错", e);
        }
        return list;
    }



}
