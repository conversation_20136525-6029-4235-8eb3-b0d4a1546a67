package com.goodsogood.ows.service.ppmd;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.mapper.ppmd.OrgPayMapper;
import com.goodsogood.ows.model.db.ppmd.PayLogEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.PpmdPayInfo;
import com.goodsogood.ows.model.mongodb.report.PpmdReportInfo;
import com.goodsogood.ows.model.vo.report.PersonElectronicReport;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 电子党务报告-党费服务层
 * @date 2019/11/19
 */
@Service
@Log4j2
public class PpmdReportService {

    private final OrgService orgService;
    private final OrgPayService orgPayService;
    private final MyMongoTemplate mongoTemplate;
    private final OrgPayMapper orgPayMapper;
    private final ObjectMapper objectMapper;
    private final StringRedisTemplate redisTemplate;

    @Autowired
    public PpmdReportService(OrgService orgService, OrgPayService orgPayService,
                             MyMongoTemplate mongoTemplate,
                             OrgPayMapper orgPayMapper, ObjectMapper objectMapper,
                             StringRedisTemplate redisTemplate) {
        this.orgService = orgService;
        this.orgPayService = orgPayService;
        this.mongoTemplate = mongoTemplate;
        this.orgPayMapper = orgPayMapper;
        this.objectMapper = objectMapper;
        this.redisTemplate = redisTemplate;
    }

    public List<PpmdPayInfo> generatePpmdReport(String queryTime) {
        List<PayLogEntity> payLogList = this.orgPayMapper.getOrgPayByDate(queryTime);
        List<PpmdPayInfo> payInfoList = this.orgPayService.getPpmdOrgList(payLogList, queryTime);
        Date date = new Date();
        payInfoList.forEach(payInfo -> {
            log.debug("查询要组织[{}]的党费信息 -> [{}]", payInfo.getOrgId(), payInfo);
            // 查询组织所属党委下排名信息
            this.orgPayService.getActivePayOrg(payInfo.getOrgId(), payInfo.getOrgLevel(), queryTime, payInfo);
            payInfo.setCreateTime(date);
            this.savePpmdReport(payInfo);
        });
        return payInfoList;
    }

    /**
     * 查询当个组织的党费信息
     *
     * @param org
     * @param queryTime
     * @param date
     * @return com.goodsogood.ows.model.mongodb.PpmdPayInfo
     * <AUTHOR>
     * @date 2019/11/20
     */
    @Async("reportExecutor")
    public Future<PpmdPayInfo> getPpmdReportByOrgId(OrganizationEntity org, String queryTime, Date date, CountDownLatch latch) {
        PpmdPayInfo payInfo = new PpmdPayInfo();
        try {
            // 存入组织基础信息
            payInfo.setOrgId(org.getOrganizationId());
            payInfo.setOrgName(org.getName());
            payInfo.setParentId(org.getParentId());
            payInfo.setOrgType(org.getOrgType());
            payInfo.setOrgTypeChild(org.getOrgTypeChild());
            payInfo.setOrgLevel(org.getOrgLevel());
            payInfo.setOrgCreateTime(org.getOrgCreateTime());
            payInfo.setStatsDate(queryTime);
            payInfo.setCreateTime(date);
            payInfo.setRegionId(org.getRegionId());
            // 查询组织下人员缴纳党费信息列表
            this.orgPayService.getPpmdUserList(org.getOrganizationId(), queryTime, payInfo);
            // 查询组织所属党委下排名信息
            this.orgPayService.getActivePayOrg(org.getOrganizationId(), org.getOrgLevel(), queryTime, payInfo);
            log.debug("查询要组织[{}]的党费信息 -> [{}]", org.getOrganizationId(), payInfo);
            this.savePpmdReport(payInfo);
        } catch (Exception e) {
            log.error(e);
        } finally {
            latch.countDown();
        }
        return new AsyncResult<PpmdPayInfo>(payInfo);
    }

    /**
     * 保存党费报表数据
     *
     * @param reportInfo
     * <AUTHOR>
     * @date 2019/11/20
     */
    public void savePpmdReport(PpmdPayInfo reportInfo) {
        // 需要判断之前是否有数据存在
        List<PpmdPayInfo> ppmdPayInfo = this.getPpmdPayInfo(reportInfo.getOrgId(), reportInfo.getStatsDate());

        if (ppmdPayInfo.size() == 0) {
            this.mongoTemplate.save(reportInfo);
        } else {
            this.updatePpmdPayInfo(reportInfo);
        }
    }

    /**
     * 从mongoDB查询组织ID在当前月份数据
     *
     * @param orgId
     * @param queryTime
     * @return java.util.List<com.goodsogood.ows.model.mongodb.PpmdPayInfo>
     * <AUTHOR>
     * @date 2019/11/20
     */
    private List<PpmdPayInfo> getPpmdPayInfo(Long orgId, String queryTime){
        Criteria criteria = new Criteria();
        criteria.andOperator(
                Criteria.where("orgId").is(orgId),
                Criteria.where("statsDate").is(queryTime)
        );
        Query query = new Query(criteria);
        List<PpmdPayInfo> ppmdPayInfos =
                this.mongoTemplate.find(query, PpmdPayInfo.class);
        log.debug("查询结果 -> [{}]", ppmdPayInfos);
        return ppmdPayInfos;
    }

    /**
     * 更新操作
     *
     * @param payInfo
     * <AUTHOR>
     * @date 2019/11/20
     */
    private void updatePpmdPayInfo(PpmdPayInfo payInfo) {
        // 查询字段
        Criteria criteria = new Criteria();
        criteria.andOperator(
                Criteria.where("orgId").is(payInfo.getOrgId()),
                Criteria.where("statsDate").is(payInfo.getStatsDate()),
                Criteria.where("orgLevel").is(payInfo.getOrgLevel())
        );
        Query query = new Query(criteria);
        PpmdPayInfo ppmdPayInfo = this.mongoTemplate.findOne(query, PpmdPayInfo.class);
        // 更新字段
        Update update = new Update();
        update.set("orgName", payInfo.getOrgName());
        update.set("parentId", payInfo.getParentId());
        update.set("orgType", payInfo.getOrgType());
        update.set("orgTypeChild", payInfo.getOrgTypeChild());
        update.set("regionId", payInfo.getRegionId());
        update.set("orgLevel", payInfo.getOrgLevel());
        if (!CollectionUtils.isEmpty(payInfo.getUserList())) {
            List<PpmdPayInfo.User> userList = payInfo.getUserList();
            if (null != ppmdPayInfo) {
                if (!CollectionUtils.isEmpty(ppmdPayInfo.getUserList())) {
                    userList.addAll(ppmdPayInfo.getUserList());
                    Set<PpmdPayInfo.User> userSet = new HashSet<>(userList);
                    userList = userSet.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparingLong(PpmdPayInfo.User::getUserId))), ArrayList::new));
                }
            }
            update.set("userList", userList);
        }
        if (null != payInfo.getFirstUser()) {
            update.set("firstUser", payInfo.getFirstUser());
        }
        if (null != payInfo.getFirstOrg()) {
            update.set("firstOrg", payInfo.getFirstOrg());
        }
        update.set("updateTime", new Date());
        // 更新
        this.mongoTemplate.upsert(query, update, PpmdPayInfo.class);
    }

    public PersonElectronicReport.PpmdPayInfo getUserPpmdReport(Long regionId,Long userId, String queryTime) {
        return getUserPpmdReport(userId, queryTime, getPpmdPayUserInfo(regionId,userId, queryTime));
    }


    public List<PpmdPayInfo> getPpmdPayUserInfo(Long regionId,Long userId,String queryTime){
        Criteria criteria = new Criteria();
        criteria.and("userList.userId").is(userId).and("regionId").is(regionId);
        if(!StringUtils.isEmpty(queryTime)){
            criteria.and("statsDate").is(queryTime);
        }

        Query query = new Query(criteria);
        return this.mongoTemplate.find(query, PpmdPayInfo.class);
    }

    /**
     * 获取个人的党费报告
     *
     * @param userId
     * @param queryTime
     * <AUTHOR>
     * @date 2019/11/22
     */
    public PersonElectronicReport.PpmdPayInfo getUserPpmdReport(Long userId, String queryTime, List<PpmdPayInfo> payInfoList) {
        PersonElectronicReport.PpmdPayInfo ppmdPayInfo = new PersonElectronicReport.PpmdPayInfo();

        if (!CollectionUtils.isEmpty(payInfoList)) {
            payInfoList.stream().filter(x -> queryTime.equals(x.getStatsDate())).forEach(payInfo -> {
                List<PpmdPayInfo.User> userList = payInfo.getUserList();
                if (!CollectionUtils.isEmpty(userList)) {
                    userList.forEach(user -> {
                        if (user.getUserId().equals(userId)) {
                            ppmdPayInfo.setIsPay(user.getStatus() == 1 ? 1 : 2);
                            if (user.getStatus() == 1) {
                                ppmdPayInfo.setPayNumber(user.getPayAmount());
                                ppmdPayInfo.setPayTime(user.getPayDate());
                                ppmdPayInfo.setPpmdTime(user.getPpmdDate());
                            }
                        }
                    });
                }
                PersonElectronicReport.PpmdPayInfo.PayFastUser payFastUser = new PersonElectronicReport.PpmdPayInfo.PayFastUser();
                if (payInfo.getFirstUser() != null) {
                    payFastUser.setUserName(payInfo.getFirstUser().getUserName());
                    payFastUser.setPayTime(payInfo.getFirstUser().getPayTime());
                }
                ppmdPayInfo.setPayFastUsers(payFastUser);
            });
        }
        return ppmdPayInfo;
    }

    /**
     * 获取组织党费党务报告
     *
     * @param orgId
     * @param queryTime yyyy-MM  2019-10
     * @return com.goodsogood.ows.model.mongodb.OrgReportInfo.PpmdInfo
     * <AUTHOR>
     * @date 2019/11/26
     */
    public PpmdReportInfo getOrgPpmdReport(Long orgId, String queryTime) {
        PpmdReportInfo ppmdReport = new PpmdReportInfo();
        // 查询字段
        Criteria criteria = new Criteria();
        // 匹配
        Pattern pattern = Pattern.compile("^.*-" + orgId + "-.*$", Pattern.CASE_INSENSITIVE);
        criteria.orOperator(
                Criteria.where("orgId").is(orgId),
                Criteria.where("orgLevel").regex(pattern)
        );
        criteria.andOperator(
                Criteria.where("statsDate").is(queryTime)
        );
        Query query = new Query(criteria);
        List<PpmdPayInfo> ppmdPayInfos = this.mongoTemplate.find(query, PpmdPayInfo.class);
        // 党费
        Double amount = 0.0;
        // 党员数量
        Set<Long> userIdSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(ppmdPayInfos)) {
            // 遍历查询出来的党费情况
            for (PpmdPayInfo payInfo : ppmdPayInfos) {
                List<PpmdPayInfo.User> userList = payInfo.getUserList();
                // 遍历人员列表
                if (!CollectionUtils.isEmpty(userList)) {
                    for (PpmdPayInfo.User user : userList) {
                        if (user.getStatus() == 1) {
                            userIdSet.add(user.getUserId());
                            amount += user.getPayAmount();
                        }
                    }
                }
            }
        }
        ppmdReport.setPayNum(userIdSet.size());
        ppmdReport.setPayAmount((double) Math.round(amount * 100) / 100);

        List<PpmdPayInfo> payInfo = this.getPpmdPayInfo(orgId, queryTime);
        if (payInfo.size() != 0) {
            PpmdPayInfo info = payInfo.get(0);
            PpmdPayInfo.FirstUser firstUser = info.getFirstUser();
            if (null != firstUser) {
                ppmdReport.setFirstUser(firstUser.getUserName());
            }
            PpmdPayInfo.FirstOrg firstOrg = info.getFirstOrg();
            if (null != firstOrg) {
                ppmdReport.setFirstOrg(firstOrg.getOrgName());
            }
        } else {
            PpmdPayInfo info = new PpmdPayInfo();
            OrganizationEntity org = this.orgService.getById(orgId);
            this.orgPayService.getActivePayOrg(orgId, org.getOrgLevel(), queryTime, info);
            PpmdPayInfo.FirstUser firstUser = info.getFirstUser();
            if (null != firstUser) {
                ppmdReport.setFirstUser(firstUser.getUserName());
            }
            PpmdPayInfo.FirstOrg firstOrg = info.getFirstOrg();
            if (null != firstOrg) {
                ppmdReport.setFirstOrg(firstOrg.getOrgName());
            }
        }
        return ppmdReport;
    }

    /**
     * 查询党委党费欠交情况，这个是工委管理员查看
     * @return
     */

    public  Integer getOwnOrgCount(Long regionId){
        String queryDate = DateUtils.toFormatDate(new Date(), "yyyy-MM");
        String redisKey = "ppmd_supervision_unpaid_statistics_" + regionId + "_" + queryDate;
        String result=redisTemplate.opsForValue().get(redisKey);
        Map<String, Object> map = null;
        try {
            map = objectMapper.readValue(result, new TypeReference<Map<String, Object>>(){});
        } catch (IOException e) {
            e.printStackTrace();
        }
        List<Object> mapUnpaidNumber= (List<Object>) map.get("unpaid_number_list");
        return mapUnpaidNumber.size();
       // return orgPayMapper.getOwnOrgCount(queryDate,regionId);
    }

}
