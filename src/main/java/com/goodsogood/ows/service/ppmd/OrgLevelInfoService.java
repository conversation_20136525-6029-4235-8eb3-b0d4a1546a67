package com.goodsogood.ows.service.ppmd;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.mapper.ppmd.OrgLevelInfoMapper;
import com.goodsogood.ows.model.db.ppmd.OrgLevelInfoEntity;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 组织层级关系服务层
 * @date 2020/1/14
 */
@Service
@Log4j2
public class OrgLevelInfoService {

    private final OrgLevelInfoMapper orgLevelInfoMapper;
    private final StringRedisTemplate redisTemplate;

    @Autowired
    public OrgLevelInfoService(OrgLevelInfoMapper orgLevelInfoMapper, StringRedisTemplate redisTemplate) {
        this.orgLevelInfoMapper = orgLevelInfoMapper;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 根据组织编号和缓存月份查询组织层级信息
     * @param id
     * @param statsDate yyyy-MM
     * @return
     */
    public OrgLevelInfoEntity fingOrgLevelInfoByRedis(String id, String statsDate){
        OrgLevelInfoEntity orgLevelInfo = null;
        String redisKey = Constants.ORG_LEVEL_INFO_KEY+statsDate;
        Object r = redisTemplate.opsForHash().get(redisKey,id);
        if(r!=null){
            orgLevelInfo = JsonUtils.fromJson(String.valueOf(r), OrgLevelInfoEntity.class);
        }else {
            //如果缓存里获取失败，从数据库查询
            orgLevelInfo = this.findOrgLevelInfo(Long.valueOf(id),statsDate);
            if(orgLevelInfo!=null){
                //补充缓存
                if(redisTemplate.hasKey(redisKey)){
                    redisTemplate.opsForHash().put(redisKey,id,JsonUtils.toJson(orgLevelInfo));
                }else{
                    redisTemplate.opsForHash().put(redisKey,id,JsonUtils.toJson(orgLevelInfo));
                    //设置过期时间
                    redisTemplate.expire(redisKey,Constants.ORG_LEVEL_INFO_KEY_TIMEOUT_DAY, TimeUnit.DAYS);
                }
            }
        }
        return orgLevelInfo;
    }

    /**
     * 根据查询月份和组织编号查询组织的层级关系
     * @param orgId
     * @param queryMonth
     * @return
     */
    public OrgLevelInfoEntity findOrgLevelInfo(Long orgId, String queryMonth){
        //获取查询月的最后一天最后一秒
        String queryTime = DateUtils.getEndOfMonth(queryMonth);
        return orgLevelInfoMapper.findOrgLevelInfo(orgId,queryTime);
    }

}
