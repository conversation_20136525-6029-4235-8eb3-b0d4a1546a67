package com.goodsogood.ows.service.cilckSpy;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.model.mongodb.clickSpy.DataCollectionEntity;
import com.goodsogood.ows.model.vo.GroupValue;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.bson.Document;
import org.bson.json.JsonWriterSettings;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@Log4j2
public class ClickSpyService {

    private static final ObjectMapper OBJECTMAPPER = new ObjectMapper();

    private final MyMongoTemplate mongoTemplate;

    @Autowired
    public ClickSpyService(MyMongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    /**
     * 查询组织点击事件
     * @param userId
     * @param year
     */
    public List<GroupValue> getUserClickSpy(Long userId, Integer year){
        Date startDate = DateUtils.firstDayOfYear(year);
        Date endDate = DateUtils.endDayOfYear(year);
        DateTime startTime = new DateTime(startDate);
        DateTime endTime = new DateTime(endDate);
        Criteria criteria = Criteria.where("transferTime").gte(startTime).lt(endTime)
                                    .and("userId").is(userId.toString());
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.project("object","transferTime","amount","userId"),
                Aggregation.match(criteria),
                Aggregation.group("object") .sum("amount").as("total")
        );

        AggregationResults<DataCollectionEntity> pageView = this.mongoTemplate.aggregate(aggregation, DataCollectionEntity.class);
        Document results = pageView.getRawResults();
        Object obj = results.get("results");
        List<GroupValue> list = null;
        try {
            String json = OBJECTMAPPER.writeValueAsString(obj);
            log.debug("json -> [{}]", json);
            list = OBJECTMAPPER.readValue(json, new TypeReference<List<GroupValue>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("JSON转换异常",e);
        }
        return list;
    }
}
