package com.goodsogood.ows.service.volunteer;

import com.goodsogood.ows.annotions.Logging;
import com.goodsogood.ows.mapper.volunteer.VolunteerTeamSummaryMapper;
import com.goodsogood.ows.mapper.volunteer.VolunteerUserSummaryMapper;
import com.goodsogood.ows.model.db.volunteer.AppraiseInfo;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.model.mongodb.dss.UserVolunteer;
import com.goodsogood.ows.model.mongodb.dss.VolunteerBulletMap;
import com.goodsogood.ows.model.mongodb.dss.VolunteerInfo;
import com.goodsogood.ows.model.mongodb.dss.VolunteerTrendMap;
import com.goodsogood.ows.model.vo.volunteer.TrendVO;
import com.goodsogood.ows.service.impl.DssIndexBuilder;
import com.goodsogood.ows.service.impl.DssPartyBranchBuilder;
import com.goodsogood.ows.service.impl.DssPartyCommitteeBuilder;
import com.goodsogood.ows.service.impl.DssUserBuilder;
import com.goodsogood.ows.service.user.VolunteerUserService;
import com.goodsogood.ows.utils.SqlJointUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 志愿相关辅助决策报表
 */
@Service
@Log4j2
public class VolunteerReportService implements DssIndexBuilder, DssPartyBranchBuilder, DssPartyCommitteeBuilder, DssUserBuilder {

    private final VolunteerUserService volunteerUserService;
    private final VolunteerTeamSummaryMapper volunteerTeamSummaryMapper;
    private final VolunteerUserSummaryMapper volunteerUserSummaryMapper;

    @Autowired
    public VolunteerReportService(VolunteerUserService volunteerUserService,
                                  VolunteerTeamSummaryMapper volunteerTeamSummaryMapper,
                                  VolunteerUserSummaryMapper volunteerUserSummaryMapper) {
        this.volunteerUserService = volunteerUserService;
        this.volunteerTeamSummaryMapper = volunteerTeamSummaryMapper;
        this.volunteerUserSummaryMapper = volunteerUserSummaryMapper;
    }

    /**
     * @param info 决策辅助首页实体类
     *             info.rootId 顶级党组织ID
     *             info.year   生成年份
     */
    @Override
    @Logging
    public IndexInfo buildIndex(IndexInfo info) {
        assert info != null && info.getYear() != null && info.getRootId() != null && info.getRegionId() != null;
        //志愿大对象
        VolunteerInfo volunteerInfo = info.getVolunteerInfo();
        if (volunteerInfo == null) {
            volunteerInfo = new VolunteerInfo();
            info.setVolunteerInfo(volunteerInfo);
        }
        //志愿者进度条
        List<VolunteerBulletMap> volunteerBulletMap = volunteerInfo.getVolunteerBulletMap();
        if (CollectionUtils.isEmpty(volunteerBulletMap)) {
            volunteerBulletMap = new ArrayList<>(4);
            volunteerInfo.setVolunteerBulletMap(volunteerBulletMap);
        } else {
            volunteerBulletMap.clear();
        }
        //志愿者开展趋势图
        VolunteerTrendMap volunteerTrendMap = volunteerInfo.getVolunteerTrendMap();
        if (volunteerTrendMap == null) {
            volunteerTrendMap = new VolunteerTrendMap();
            volunteerInfo.setVolunteerTrendMap(volunteerTrendMap);
        }

        //基本信息
        volunteerInfo.setVolunteerTeamTotal(volunteerTeamSummaryMapper.getTeamNumber(info.getRegionId(), info.getYear(), null));
        volunteerInfo.setVolunteerUserTotal(volunteerTeamSummaryMapper.getVolunteerNumber(info.getRegionId(), info.getYear()));
        volunteerInfo.setVolunteerServiceTotal(volunteerTeamSummaryMapper.getServiceNumber(info.getRegionId(), info.getYear()));
        volunteerInfo.setRecourseTotal(volunteerTeamSummaryMapper.getHelpNumber(info.getRegionId(), info.getYear()));


        String startTime = getYearStartTime(info.getYear());
        String endTime = getYearEndTime(info.getYear());
        // 开展送服务
        Integer reservationNumber = volunteerTeamSummaryMapper.getReservationNumber(info.getRegionId(), startTime, endTime);
        volunteerInfo.setVolunteerSupportServiceTotal(reservationNumber == null ? 0 : reservationNumber);
        // 志愿者参与
        Integer joinNumber = volunteerTeamSummaryMapper.getJoinNumber(info.getRegionId(), startTime, endTime);
        volunteerInfo.setVolunteerJoinTotal(joinNumber == null ? 0 : joinNumber);


        //开展了志愿项⽬的团体 进度条
        volunteerBulletMap.add(createVolunteerBulletMap("开展了志愿项⽬的团体"
                , volunteerInfo.getVolunteerTeamTotal(), volunteerTeamSummaryMapper.getServiceTeamNumber(info.getRegionId(), info.getYear())));

        // 开展了送服务的团体 进度条
        Integer reservationTeamNumber = volunteerTeamSummaryMapper.getReservationTeamNumber(info.getRegionId(), startTime, endTime);
        volunteerBulletMap.add(createVolunteerBulletMap("开展了送服务的团体"
                , volunteerInfo.getVolunteerTeamTotal(), reservationTeamNumber == null ? 0 : reservationTeamNumber));

        //求助接单的志愿团体 进度条
        volunteerBulletMap.add(createVolunteerBulletMap("求助接单的志愿团体"
                , volunteerInfo.getVolunteerTeamTotal(), volunteerTeamSummaryMapper.getHelpTeamNumber(info.getRegionId(), info.getYear())));

        // 参与了志愿项⽬的志愿者 进度条
        Integer joinUserTeam = volunteerTeamSummaryMapper.getJoinUserTeam(info.getRegionId(), startTime, endTime);
        volunteerBulletMap.add(createVolunteerBulletMap("参与了志愿项⽬的志愿者"
                , volunteerInfo.getVolunteerUserTotal(), joinUserTeam == null ? 0 : joinUserTeam));


        //志愿服务开展趋势
        volunteerTrendMap.setProject(getTrendMap(volunteerTeamSummaryMapper.getProjectNum(info.getRegionId(), info.getYear())));
        //求助接单开展趋势
        volunteerTrendMap.setHelp(getTrendMap(volunteerTeamSummaryMapper.getHelpNum(info.getRegionId(), info.getYear())));
        // 送服务开展趋势
        volunteerTrendMap.setService(getTrendMap(volunteerTeamSummaryMapper.getReservationNum(info.getRegionId(), startTime, endTime)));
        return info;
    }

    /**
     * @param info 决策辅助党委详情页实体类
     *             info.organizationId 组织ID
     *             info.year   生成年份
     * @return
     */
    @Override
    @Logging
    public PartyCommitteeInfo buildPartyCommittee(PartyCommitteeInfo info) {
        assert info != null && info.getRegionId() != null && info.getOrganizationId() != null && info.getYear() != null;
        //志愿大对象
        VolunteerInfo volunteerInfo = info.getVolunteerInfo();
        if (volunteerInfo == null) {
            volunteerInfo = new VolunteerInfo();
            info.setVolunteerInfo(volunteerInfo);
        }
        //志愿者进度条
        List<VolunteerBulletMap> volunteerBulletMap = volunteerInfo.getVolunteerBulletMap();
        if (CollectionUtils.isEmpty(volunteerBulletMap)) {
            volunteerBulletMap = new ArrayList<>(4);
            volunteerInfo.setVolunteerBulletMap(volunteerBulletMap);
        }
        //志愿者开展趋势图
        VolunteerTrendMap volunteerTrendMap = volunteerInfo.getVolunteerTrendMap();
        if (volunteerTrendMap == null) {
            volunteerTrendMap = new VolunteerTrendMap();
            volunteerInfo.setVolunteerTrendMap(volunteerTrendMap);
        }

        //基本信息
        volunteerInfo.setVolunteerTeamTotal(volunteerTeamSummaryMapper.getTeamNumber(info.getRegionId(), info.getYear(), info.getOrganizationId()));
        volunteerInfo.setVolunteerUserTotal(volunteerTeamSummaryMapper.getVolunteerNumberByOrg(info.getYear(), info.getOrganizationId()));
        volunteerInfo.setVolunteerServiceTotal(volunteerTeamSummaryMapper.getServiceNumberByOrg(info.getRegionId(), info.getYear(), info.getOrganizationId()));
        volunteerInfo.setRecourseTotal(volunteerTeamSummaryMapper.getHelpNumberByOrg(info.getRegionId(), info.getYear(), info.getOrganizationId()));

        String startTime = getYearStartTime(info.getYear());
        String endTime = getYearEndTime(info.getYear());

        // 组织id转志愿团队id
        Long volunteerTeamId = volunteerTeamSummaryMapper.getVolunteerTeamId(info.getOrganizationId());
        if (volunteerTeamId == null) {
            volunteerTeamId = -1L;
        }

        // 开展送服务
        Integer reservationNumber = volunteerTeamSummaryMapper.getReservationNumberByOrg(volunteerTeamId, startTime, endTime);
        volunteerInfo.setVolunteerSupportServiceTotal(reservationNumber == null ? 0 : reservationNumber);
        // 志愿者参与
        Integer joinNumber = volunteerTeamSummaryMapper.getJoinNumberByOrg(volunteerTeamId, startTime, endTime);
        volunteerInfo.setVolunteerJoinTotal(joinNumber == null ? 0 : joinNumber);


        //开展了志愿项⽬的团体 进度条
        volunteerBulletMap.add(createVolunteerBulletMap("开展了志愿项⽬的团体"
                , volunteerInfo.getVolunteerTeamTotal(), volunteerTeamSummaryMapper.getServiceTeamNumberByOrg(info.getRegionId(), info.getYear(), info.getOrganizationId())));

        // 开展了送服务的团体 进度条
        Integer reservationTeamNumber = volunteerTeamSummaryMapper.getReservationTeamNumberByOrg(volunteerTeamId, startTime, endTime);
        volunteerBulletMap.add(createVolunteerBulletMap("开展了送服务的团体"
                , volunteerInfo.getVolunteerTeamTotal(), reservationTeamNumber == null ? 0 : reservationTeamNumber));

        //求助接单的志愿团体 进度条
        volunteerBulletMap.add(createVolunteerBulletMap("求助接单的志愿团体"
                , volunteerInfo.getVolunteerTeamTotal(), volunteerTeamSummaryMapper.getHelpTeamNumberByOrg(info.getRegionId(), info.getYear(), info.getOrganizationId())));

        // 参与了志愿项⽬的志愿者 进度条
        Integer joinUserTeam = volunteerTeamSummaryMapper.getJoinUserTeamByOrg(volunteerTeamId, startTime, endTime);
        volunteerBulletMap.add(createVolunteerBulletMap("参与了志愿项⽬的志愿者"
                , volunteerInfo.getVolunteerUserTotal(), joinUserTeam == null ? 0 : joinUserTeam));

        //志愿服务开展趋势
        volunteerTrendMap.setProject(getTrendMap(volunteerTeamSummaryMapper.getProjectNumByOrg(info.getRegionId(), info.getYear(), info.getOrganizationId())));
        //求助接单开展趋势
        volunteerTrendMap.setHelp(getTrendMap(volunteerTeamSummaryMapper.getHelpNumByOrg(info.getRegionId(), info.getYear(), info.getOrganizationId())));
        // 送服务开展趋势
        volunteerTrendMap.setService(getTrendMap(volunteerTeamSummaryMapper.getReservationNumByOrg(volunteerTeamId, startTime, endTime)));
        return info;
    }

    /**
     * @param info 决策辅助党支部详情页实体类
     *             info.organizationId 组织ID
     *             info.year   生成年份
     * @return
     */
    @Override
    @Logging
    public PartyBranchInfo buildPartyBranch(PartyBranchInfo info) {
        return info;
    }

    /**
     * @param info 决策辅助用户详情页实体类
     *             info.organizationId 组织ID
     *             info.year   生成年份
     * @return
     */
    @Override
    @Logging
    public UserInfo buildUser(UserInfo info) {
        return buildUserList(Arrays.asList(info)).get(0);
    }

    @Override
    @Logging
    public List<UserInfo> buildUserList(List<UserInfo> infoList) {
        if (infoList == null || infoList.isEmpty()) {
            return infoList;
        }
        List<Long> userIdList = new ArrayList<>(infoList.size());
        Map<Long, UserInfo> userInfoMap = infoList.stream().collect(Collectors.toMap(UserInfo::getUserId, (u) -> u));
        // 初始化数据
        infoList.forEach(t -> {
            userIdList.add(t.getUserId());
            if (t.getVolunteer() == null) {
                UserVolunteer userVolunteer = new UserVolunteer();
                userVolunteer.setJoinNum(0);
                userVolunteer.setServiceTime(0.0d);
                userVolunteer.setVolunteerScore(0);
                userVolunteer.setPunctualGrade(0.0d);
                userVolunteer.setAttitudeGrade(0.0d);
                userVolunteer.setProfessionalGrade(0.0d);
                t.setVolunteer(userVolunteer);
            }
        });

        String startTime = getYearStartTime(infoList.get(0).getYear());
        String endTime = getYearEndTime(infoList.get(0).getYear());
        String userIds = SqlJointUtil.collectionToStr(infoList, u -> u.getUserId().toString());
        // 评分
        List<AppraiseInfo> appraiseInfoList = volunteerUserSummaryMapper.getAppraiseByUserId(userIds, startTime, endTime);
        if (!CollectionUtils.isEmpty(appraiseInfoList)) {
            appraiseInfoList.forEach(t -> {
                if (t != null) {
                    Long userId = t.getUserId();
                    UserInfo userInfo = userInfoMap.get(userId);
                    userInfo.getVolunteer().setAttitudeGrade(t.getAttitude() == null ? 0 : t.getAttitude());
                    userInfo.getVolunteer().setProfessionalGrade(t.getProfessional() == null ? 0 : t.getProfessional());
                    userInfo.getVolunteer().setPunctualGrade(t.getPunctuality() == null ? 0 : t.getPunctuality());
                }
            });
        }
        // 志愿积分
        List<AppraiseInfo> scoreList = volunteerUserSummaryMapper.getScoreByUserId(userIds, startTime, endTime);
        if (!CollectionUtils.isEmpty(scoreList)) {
            scoreList.forEach(t -> {
                if (t != null) {
                    Long userId = t.getUserId();
                    UserInfo userInfo = userInfoMap.get(userId);
                    userInfo.getVolunteer().setVolunteerScore(t.getScore() == null ? 0 : t.getScore());
                }
            });
        }
        // 服务时长
        List<AppraiseInfo> timeList = volunteerUserSummaryMapper.getTimeByUser(userIds, startTime, endTime);
        if (!CollectionUtils.isEmpty(timeList)) {
            timeList.forEach(t -> {
                if (t != null) {
                    Long userId = t.getUserId();
                    UserInfo userInfo = userInfoMap.get(userId);
                    BigDecimal serviceTime = t.getTime() == null ? BigDecimal.ZERO : BigDecimal.valueOf(t.getTime());
                    if (serviceTime.doubleValue() > 0) {
                        BigDecimal divide = serviceTime.divide(BigDecimal.valueOf(60), 1, RoundingMode.DOWN);
                        userInfo.getVolunteer().setServiceTime(divide.doubleValue());
                    } else {
                        userInfo.getVolunteer().setServiceTime(serviceTime.doubleValue());
                    }
                }
            });
        }
        // 参与次数
        Map<Long, Long> volunteerUserMap = volunteerUserService.getVolunteerUserId(userIdList);
        if (CollectionUtils.isEmpty(volunteerUserMap)) {
            volunteerUserMap = new HashMap<>();
            volunteerUserMap.put(-1L, -1L);
        }
        String volunteerUserIds = SqlJointUtil.collectionToStr(volunteerUserMap.entrySet(), v -> v.getKey().toString());
        List<AppraiseInfo> serviceNumberList = volunteerUserSummaryMapper.getTotalByUser(volunteerUserIds, startTime, endTime);
        if (!CollectionUtils.isEmpty(serviceNumberList)) {
            Map<Long, Long> finalVolunteerUserMap = volunteerUserMap;
            serviceNumberList.forEach(t -> {
                if (t != null) {
                    Long userId = finalVolunteerUserMap.get(t.getVolunteerUserId());
                    UserInfo userInfo = userInfoMap.get(userId);
                    userInfo.getVolunteer().setJoinNum(t.getServiceNumber() == null ? 0 : t.getServiceNumber());
                }
            });
        }
        return infoList;
    }

    private VolunteerBulletMap createVolunteerBulletMap(String title, Integer setMeasures, Integer ranges) {
        VolunteerBulletMap volunteerBulletMap = new VolunteerBulletMap();
        volunteerBulletMap.setTitle(title);
        volunteerBulletMap.setMeasures(setMeasures);
        volunteerBulletMap.setRanges(ranges);
        return volunteerBulletMap;
    }

    private List<Integer> getTrendMap(List<TrendVO> projectNum) {
        HashMap<Integer, Integer> tempMonthNum = new HashMap<>(12);
        if (!CollectionUtils.isEmpty(projectNum)) {
            projectNum.forEach(x -> tempMonthNum.put(x.getMonth() - 1, x.getNum()));
        }
        List<Integer> result = new ArrayList<>(12);
        for (int i = 0; i < 12; i++) {
            result.add(tempMonthNum.getOrDefault(i, 0));
        }
        return result;
    }

    // ----------------------------------------- 辅助决策-zch start -----------------------------------------

    /**
     * 获取某年开始的第一天
     *
     * @param year 年
     * @return yyyy-MM
     */
    public static String getYearStartTime(Integer year) {
        return new StringBuilder().append(year).append("-01-01").toString();
    }

    /**
     * 获取某年结束的最后一天
     *
     * @param year 年
     * @return yyyy-MM
     */
    public static String getYearEndTime(Integer year) {
        return new StringBuilder().append(year).append("-12-31").toString();
    }
    // ----------------------------------------- 辅助决策-zch end -----------------------------------------
}
