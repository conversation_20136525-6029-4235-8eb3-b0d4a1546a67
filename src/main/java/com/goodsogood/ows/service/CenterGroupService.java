package com.goodsogood.ows.service;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.CountExcludeConfig;
import com.goodsogood.ows.configuration.TagConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.CenterGroupMapper;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.vo.CenterGroupVo;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.sas.OpenService;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Log4j2
public class CenterGroupService {
    private final Errors errors;
    private final OpenService openService;
    private final CenterGroupMapper centerGroupMapper;
    private final OrgService orgService;
    private final TagConfig tagConfig;
    private final CountExcludeConfig countExcludeConfig;

    public CenterGroupService(OpenService openService, CenterGroupMapper centerGroupMapper, OrgService orgService, TagConfig tagConfig, Errors errors, CountExcludeConfig countExcludeConfig) {
        this.openService = openService;
        this.centerGroupMapper = centerGroupMapper;
        this.orgService = orgService;
        this.tagConfig = tagConfig;
        this.errors = errors;
        this.countExcludeConfig = countExcludeConfig;
    }
    public List<CenterGroupVo> statistics(HttpHeaders headers, String name, Integer year) {
        Long topics = tagConfig.getTopics(); //第一议题tagid
        Long educations = tagConfig.getEducations();//主题教育tagid
        Long fusions = tagConfig.getFusions();//党业融合tagid
        String excludeOrgIds = countExcludeConfig.getExcludeOrgIds();//排除统计的单位id
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        Long oid = sysHeader.getUoid() == null ? sysHeader.getOid() : sysHeader.getUoid();
        if (oid == 3) {
            List<CenterGroupVo> centerGroupVoList = centerGroupMapper.statistics(topics, fusions, educations,year);
            List<Long> orgIds = centerGroupVoList.stream()
                    .map(CenterGroupVo::getOrgId)
                    .collect(Collectors.toList());
            log.debug("所有的orgIds:{}", orgIds);
            String[] excludeIdsArray = excludeOrgIds.split(",");
            List<Long> excludeIds = new ArrayList<>();
            for (String id : excludeIdsArray) {
                excludeIds.add(Long.parseLong(id.trim()));
            }
            List<OrganizationEntity> orgAndOwnerId = orgService.getOwnerId(orgIds);
            log.debug("所有的orgId和ownerId:{}", orgAndOwnerId);
            List<OrganizationEntity> orgAndOwnerIdExclude = new ArrayList<>();
            for (OrganizationEntity entity : orgAndOwnerId) {
                if (!excludeIds.contains(entity.getOwnerId())) {
                    orgAndOwnerIdExclude.add(entity);
                }
            }
            log.debug("排除统计的orgId和ownerId:{}", orgAndOwnerIdExclude);
            for (CenterGroupVo centerGroupVo : centerGroupVoList) {
                Long orgId = centerGroupVo.getOrgId();
                for (OrganizationEntity organizationEntity : orgAndOwnerIdExclude) {
                    if (organizationEntity.getOrganizationId().equals(orgId)) {
                        centerGroupVo.setUnitId(organizationEntity.getOwnerId());
                    }
                }
            }
            // 过滤掉unitId为空的数据
            List<CenterGroupVo> resultList = centerGroupVoList.stream()
                    .filter(centerGroupVo -> centerGroupVo.getUnitId() != null)
                    .collect(Collectors.toList());
            //获取所有单位
            List<OpenService.OrgBaseInfo> corpList = openService.getCorps(headers);
            Map<Long, String> unitIdToNameMap = new HashMap<>();
            corpList.forEach(org -> unitIdToNameMap.put(org.getOrgId(), org.getName()));
            log.debug("所有的unitId和unitName:{}", unitIdToNameMap);
            for (CenterGroupVo centerGroupVo : resultList) {
                Long unitId = centerGroupVo.getUnitId();
                String unitName = unitIdToNameMap.get(unitId);
                if (unitName != null) {
                    centerGroupVo.setUnitName(unitName);
                }
            }
            // 将unitIdToNameMap中所有没被使用的单位id和名称添加到centerGroupVoList中
            for (Long unitId : unitIdToNameMap.keySet()) {
                boolean flag = false;
                for (CenterGroupVo centerGroupVo : resultList) {
                    if (centerGroupVo.getUnitId() != null && centerGroupVo.getUnitId().equals(unitId)) {
                        flag = true;
                        break;
                    }
                }
                if (!flag) {
                    CenterGroupVo centerGroupVo = new CenterGroupVo();
                    centerGroupVo.setUnitId(unitId);
                    centerGroupVo.setUnitName(unitIdToNameMap.get(unitId));
                    centerGroupVo.setLearn(0);
                    centerGroupVo.setTopics(0);
                    centerGroupVo.setFusions(0);
                    centerGroupVo.setEducations(0);
                    resultList.add(centerGroupVo);
                }
            }
            //根据name模糊查询
            List<CenterGroupVo> filteredList;
            if (name != null) {
                filteredList = resultList.stream()
                        .filter(vo -> vo.getUnitName() != null && vo.getUnitName().toLowerCase().contains(name.toLowerCase()))
                        .collect(Collectors.toList());
            } else {
                filteredList = resultList;
            }
            for (CenterGroupVo centerGroupVo : filteredList) {
//                corpList.stream().filter(it -> it.getOrgId().longValue() == centerGroupVo.getUnitId().longValue())
//                        .findFirst()
//                        .ifPresent(corp -> {
//                            centerGroupVo.setSeq(corp.getSeq());
//                        });
                Optional<OpenService.OrgBaseInfo> optionalOrg = corpList.stream()
                        .filter(it -> it.getOrgId().equals(centerGroupVo.getUnitId()))
                        .findFirst();
                optionalOrg.ifPresent(corp -> {
                    centerGroupVo.setSeq(corp.getSeq());
                });
                if (centerGroupVo.getSeq() == null){
                    centerGroupVo.setSeq(0);
                }
            }
            Collections.sort(filteredList, Comparator.comparing(CenterGroupVo::getSeq));

            return filteredList;
        }
        else {
            LinkedList<OrganizationEntity> orgList = orgService.findAllChildOrg(oid, 1, null, false);
//            List<Long> orgIds = orgList.stream()
//                    .map(OrganizationEntity::getOrganizationId)
//                    .collect(Collectors.toList());
            Map<Long, String> orgNameMap = orgList.stream()
                    .collect(Collectors.toMap(OrganizationEntity::getOrganizationId, OrganizationEntity::getName));
            List<Long> orgIds = new ArrayList<>(orgNameMap.keySet());
            log.debug("所有的非顶级组织orgId:{}", orgIds);
            if (orgIds != null && orgIds.size() > 0){
                List<CenterGroupVo> centerGroupVoList = centerGroupMapper.statisticsOrgIds(orgIds, topics, fusions, educations, year);
                if (name!= null) {
//                    List<CenterGroupVo> filteredList = centerGroupVoList.stream()
//                            .filter(item -> item.getOrgName() != null && item.getOrgName().toLowerCase().contains(name.toLowerCase()))
//                            .sorted(Comparator.comparingLong(CenterGroupVo::getOrgId))
//                            .collect(Collectors.toList());
//                    return filteredList;
                    List<CenterGroupVo> sortedList = centerGroupVoList.stream()
                            .filter(item -> item.getOrgName() != null && item.getOrgName().toLowerCase().contains(name.toLowerCase()))
                            .peek(item -> {
                                item.setOrgId(item.getOrgId());
                                item.setOrgName(orgNameMap.get(item.getOrgId()));
                                if (item.getLearn() == null) {
                                    item.setLearn(0);
                                }
                                if (item.getTopics() == null) {
                                    item.setTopics(0);
                                }
                                if (item.getFusions() == null) {
                                    item.setFusions(0);
                                }
                                if (item.getEducations() == null) {
                                    item.setEducations(0);
                                }
                            })
                            .sorted(Comparator.comparingLong(CenterGroupVo::getOrgId))
                            .collect(Collectors.toList());

                    List<Long> missingOrgIds = orgIds.stream()
                            .filter(orgId -> sortedList.stream().noneMatch(item -> item.getOrgId() == orgId))
                            .collect(Collectors.toList());

                    Stream<CenterGroupVo> missingData = missingOrgIds.stream()
                            .map(orgId -> {
                                CenterGroupVo centerGroupVo = new CenterGroupVo();
                                centerGroupVo.setOrgId(orgId);
                                centerGroupVo.setOrgName(orgNameMap.get(orgId));
                                centerGroupVo.setLearn(0);
                                centerGroupVo.setTopics(0);
                                centerGroupVo.setFusions(0);
                                centerGroupVo.setEducations(0);
                                return centerGroupVo;
                            });

                    sortedList.addAll(missingData.collect(Collectors.toList()));
                    sortedList.sort(Comparator.comparingLong(CenterGroupVo::getOrgId));

                    return sortedList;
                }else {
//                    List<CenterGroupVo> sortedList = centerGroupVoList.stream()
//                            .sorted(Comparator.comparingLong(CenterGroupVo::getOrgId))
//                            .collect(Collectors.toList());
//                    return sortedList;
                    List<CenterGroupVo> sortedList = centerGroupVoList.stream()
                            .peek(item -> {
                                item.setOrgId(item.getOrgId());
                                item.setOrgName(orgNameMap.get(item.getOrgId()));
                                if (item.getLearn() == null) {
                                    item.setLearn(0);
                                }
                                if (item.getTopics() == null) {
                                    item.setTopics(0);
                                }
                                if (item.getFusions() == null) {
                                    item.setFusions(0);
                                }
                                if (item.getEducations() == null) {
                                    item.setEducations(0);
                                }
                            })
                            .sorted(Comparator.comparingLong(CenterGroupVo::getOrgId))
                            .collect(Collectors.toList());

                    List<Long> missingOrgIds = orgIds.stream()
                            .filter(orgId -> sortedList.stream().noneMatch(item -> item.getOrgId() == orgId))
                            .collect(Collectors.toList());

                    Stream<CenterGroupVo> missingData = missingOrgIds.stream()
                            .map(orgId -> {
                                CenterGroupVo centerGroupVo = new CenterGroupVo();
                                centerGroupVo.setOrgId(orgId);
                                centerGroupVo.setOrgName(orgNameMap.get(orgId));
                                centerGroupVo.setLearn(0);
                                centerGroupVo.setTopics(0);
                                centerGroupVo.setFusions(0);
                                centerGroupVo.setEducations(0);
                                return centerGroupVo;
                            });

                    sortedList.addAll(missingData.collect(Collectors.toList()));
                    sortedList.sort(Comparator.comparingLong(CenterGroupVo::getOrgId));

                    return sortedList;
                }
            }else {
                throw new ApiException(
                        "orgIds为空",new Result<>(errors, 6001, HttpStatus.OK.value()));
            }
        }
    }
}
