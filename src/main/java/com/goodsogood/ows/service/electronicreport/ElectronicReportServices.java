package com.goodsogood.ows.service.electronicreport;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.RegionListConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.*;
import com.goodsogood.ows.model.mongodb.report.UserScoreDonateInfo;
import com.goodsogood.ows.model.mongodb.report.UserScoreStudyInfo;
import com.goodsogood.ows.model.mongodb.report.UserScoreStudyReport;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.activity.ActivityElectronicReport;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.report.PersonElectronicReport;
import com.goodsogood.ows.service.activity.ActivityService;
import com.goodsogood.ows.service.meeting.MeetingReportService;
import com.goodsogood.ows.service.mongodb.UserScoreReportService;
import com.goodsogood.ows.service.ppmd.PpmdReportService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import com.mongodb.BasicDBObject;
import com.mongodb.bulk.BulkWriteResult;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletionService;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @program: ows-sas
 * @description: 电子报告services
 * @author: Mr.LiGuoYong
 * @create: 2019-11-22 11:08
 **/
@Service
@Log4j2
public class ElectronicReportServices {

    private final ActivityService activityService;
    private final UserScoreReportService userScoreReportService;
    private final UserService userService;
    private final PpmdReportService ppmdReportService;
    private final MeetingReportService meetingReportService;
    private final MyMongoTemplate mongoTemplate;
    private final UserMapper userMapper;
    private final OrgService orgService;
    private final OrgTypeConfig orgTypeConfig;
    private final String startStaDate = "2019-01";
    private final Errors errors;
    private final RegionListConfig regionListConfig;
    private final SimpleApplicationConfigHelper configHelper;


    public ElectronicReportServices(ActivityService activityService,
                                    UserScoreReportService userScoreReportService,
                                    UserService userService,
                                    PpmdReportService ppmdReportService,
                                    MeetingReportService meetingReportService,
                                    MyMongoTemplate mongoTemplate,
                                    UserMapper userMapper,
                                    OrgService orgService,
                                    OrgTypeConfig orgTypeConfig,
                                    Errors errors, RegionListConfig regionListConfig,
                                    SimpleApplicationConfigHelper configHelper) {
        this.activityService = activityService;
        this.userScoreReportService = userScoreReportService;
        this.userService = userService;
        this.ppmdReportService = ppmdReportService;
        this.meetingReportService = meetingReportService;
        this.mongoTemplate = mongoTemplate;
        this.userMapper = userMapper;
        this.orgService = orgService;
        this.orgTypeConfig = orgTypeConfig;
        this.errors = errors;
        this.regionListConfig = regionListConfig;
        this.configHelper = configHelper;
    }

    /**
     * 得到用户电子党务报告
     * staYearMonth 统计年月 比如2019-11
     *
     * @return
     */
    public PersonElectronicReport getPersonElectronicReport(Long regionId, Long userId, String staYearMonth) throws NoSuchElementException {
        Optional<PersonElectronicReport> personElectronicReport = findPersonElectronicReport(regionId, userId, staYearMonth);
        if (!personElectronicReport.isPresent()) {
            UserInfoBase userInfoByUserId = userMapper.getUserInfoByUserId(userId, regionId);
            if (userInfoByUserId != null) {
                createReport(userInfoByUserId, staYearMonth, regionId);
                return findPersonElectronicReport(regionId, userId, staYearMonth).get();
            } else {
                log.error("个人电子党务报统计 该用户无效 userId [{}],staYearMonth [{}],regionId[{}]", userId, staYearMonth, regionId);
                throw new ApiException("没有查询信息", new Result(errors, 9404, HttpStatus.OK.value(), "电子党务信息"));
            }
        }
        return personElectronicReport.get();
    }

    private Optional<PersonElectronicReport> findPersonElectronicReport(Long regionId, Long userId, String staYearMonth) {
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(userId)).addCriteria(Criteria.where("regionId").is(regionId));
        query.fields().include("personElectronicReport").include("userId");
        UserStaInfo userStaInfos = mongoTemplate.findOne(query, UserStaInfo.class);
        if (userStaInfos == null) {
            return Optional.empty();
        }
        return userStaInfos.getPersonElectronicReport().
                stream().sorted(Comparator.comparing(PersonElectronicReport::getCreateTime).reversed()).
                filter(e -> e.getStatisticsTime().equals(staYearMonth)).findFirst();
    }

    /**
     * 生成一个人某月的报告 时间范围2019-01开始
     *
     * @param userId       startStaDate 统计时间 yyyy-MM
     * @param startStaDate
     */
    public void createPersonMonthEleReport(Long regionId, Long userId, String[] startStaDate) {
        UserInfoBase userInfoBase = userMapper.getUserInfoByUserId(userId, regionId);
        if (userInfoBase == null) {
            throw new ApiException("没有查询到用户信息！");
        }
        Arrays.stream(startStaDate).forEach(item -> {
            try {
                createReport(userInfoBase, item, regionId);
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new ApiException("生成异常，ex->" + ex.getMessage());
            }
        });
    }


    /**
     * 删除一个人某月的报告 时间范围2019-01开始
     *
     * @param userId       startStaDate 统计时间 yyyy-MM
     * @param startStaDate
     */
    public void deletePersonMonthEleReport(Long userId, String[] startStaDate) {
        Arrays.stream(startStaDate).forEach(item -> {
            Update update = new Update();
            update.pull("personElectronicReport", new BasicDBObject("statisticsTime", item));
            Query query = new Query();
            query.addCriteria(Criteria.where("userId").is(userId));
            mongoTemplate.updateMulti(query, update, UserStaInfo.class);
        });

    }

    /**
     * 生成电子党务报告
     * staYearMonth 统计年月 比如2019-11
     *
     * @return
     */
    public void createPersonElectronicReport(Boolean isInit) {
        List<Long> regionList = this.regionListConfig.getReport();
        regionList.forEach(regionId -> {
            //要查询系统里面有好多用户
            List<UserInfoBase> allUserInfo = userMapper.getAllUserInfo(regionId);
            //allUserInfo = allUserInfo.stream().filter(item -> item.getUserId() == 63519L).collect(Collectors.toList());
            List<ActivityInfo> allActivityInfos = activityService.getAllActivityInfos(regionId);
            List<String> staTime = getStaTime(startStaDate, isInit);
            ExecutorService executorService = Executors.newFixedThreadPool(4);
            allUserInfo.forEach(item -> {
                executorService.execute(() -> {
                    List<ScoreInfo> scoreUserInfo = userScoreReportService.getScoreUserInfo(item.getUserId());
                    List<MeetingInfo> meetingList = meetingReportService.getMeetingList(regionId, item.getUserId(), null);
                    List<PpmdPayInfo> ppmdPayInfo = ppmdReportService.getPpmdPayUserInfo(regionId, item.getUserId(), null);
                    staTime.forEach(innerItem -> {
                        try {
                            log.debug("--------------thread name: [{}] run of createPersonElectronicReport  -----------------", Thread.currentThread().getName());
                            createReport(item, innerItem, allActivityInfos, scoreUserInfo, meetingList, ppmdPayInfo);
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error("生成电子党务1报告信息出错，userId->{},startStaDate->{},ex-{}",
                                    item.getUserId(), startStaDate, e.getMessage());
                        }
                    });
                });
            });

            executorService.shutdown();

            while (!executorService.isTerminated()) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }


//        try {
//            List<UserInfoBase> allUserInfo = userMapper.getAllUserInfo();
//            List<String> staTime = getStaTime(startStaDate, isInit);
//            staTime.forEach(innerItem -> {
//                List<ActivityInfo> activityInfos = activityService.getActivityInfos(Integer.parseInt(innerItem.replace("-", "")));
//                allUserInfo.forEach(userInfo -> {
//                    try {
//                        createReport(userInfo, innerItem, activityInfos);
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                        log.error("生成电子党务1报告信息出错,userId->{},startStaDate->{},ex-{}",
//                                userInfo.getUserId(), startStaDate, e.getMessage());
//                    }
//                });
//            });
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("生成电子党务2报告信息出错, startStaDate->{},ex-{}",
//                    startStaDate, e.getMessage());
//        }


//        //要查询系统里面有好多用户
//        List<UserInfoBase> allUserInfo = userMapper.getAllUserInfo();
//        allUserInfo.forEach(item -> {
//            try {
//                List<String> staTime = getStaTime(startStaDate, isInit);
//                staTime.forEach(innerItem -> {
//                    try {
//                        createReport(item, innerItem);
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                        log.error("生成电子党务1报告信息出错，userId->{},startStaDate->{},ex-{}",
//                                item.getUserId(), startStaDate, e.getMessage());
//                    }
//                });
//            } catch (Exception e) {
//                e.printStackTrace();
//                log.error("生成电子党务2报告信息出错，userId->{},startStaDate->{},ex-{}",
//                        item.getUserId(), startStaDate, e.getMessage());
//            }
//        });
        });
    }

    /**
     * 生成指定月的个人数据
     *
     * @param initTime
     */
    public void createPersonElectronicReportByMonth(Long regionId, String initTime) {
        //要查询系统里面有好多用户
        List<UserInfoBase> allUserInfo = userMapper.getAllUserInfo(regionId);
        List<ActivityInfo> activityInfos = activityService.getActivityInfos(regionId, Integer.parseInt(initTime.replace("-", "")));
        allUserInfo.forEach(item -> {
            try {
                List<ScoreInfo> scoreUserInfo = userScoreReportService.getScoreUserInfo(item.getUserId());
                List<MeetingInfo> meetingList = meetingReportService.getMeetingList(regionId, item.getUserId(), initTime);
                List<PpmdPayInfo> ppmdPayInfo = ppmdReportService.getPpmdPayUserInfo(regionId, item.getUserId(), initTime);
                createReport(item, initTime, activityInfos, scoreUserInfo, meetingList, ppmdPayInfo);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("生成电子党务1报告信息出错，userId->{},startStaDate->{},ex-{}",
                        item.getUserId(), startStaDate, e.getMessage());
            }
        });
    }


    /**
     * startDate 为yyyy-MM 数据结构
     *
     * @param startStaDate
     * @param isInit       初始化的就是2019-01 到2019-10 之间的数据 是否初始化
     * @return
     */
    private static List<String> getStaTime(String startStaDate, Boolean isInit) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        //当前月份
        String nowDate = format.format(new Date());
        /*定义起始日期 定义结束日期  可以去当前月也可以手动写日期。*/
        Date startDate, endDate;

        try {
            startDate = format.parse(startStaDate);
            endDate = format.parse(nowDate);
            //定义日期实例
            Calendar dd = Calendar.getInstance();
            //设置日期起始时间
            dd.setTime(startDate);
            List<String> listMonthStr = new ArrayList<>();
            //判断是否到结束日期
            while (dd.getTime().before(endDate)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
                String monthStr = sdf.format(dd.getTime());
                //进行当前日期月份加1
                dd.add(Calendar.MONTH, 1);
                listMonthStr.add(monthStr);
            }
            if (isInit) {
                return listMonthStr;
            } else {
                List<String> listLastMonth = new ArrayList<>();
                listLastMonth.add(listMonthStr.get(listMonthStr.size() - 1));
                return listLastMonth;
            }
        } catch (Exception ignored) {
        }

        return new ArrayList<>();
    }

    /**
     * 固定线程池
     */
    private static final ExecutorService executor = Executors.newFixedThreadPool(2);

    public void createReport(UserInfoBase userInfoBase, String staYearMonth, Long regionId) {
        try {
            createReport(userInfoBase, staYearMonth
                    , activityService.getActivityInfos(regionId, Integer.parseInt(staYearMonth.replace("-", "")))
                    , userScoreReportService.getScoreUserInfo(userInfoBase.getUserId())
                    , meetingReportService.getMeetingList(regionId, userInfoBase.getUserId(), staYearMonth)
                    , ppmdReportService.getPpmdPayUserInfo(regionId, userInfoBase.getUserId(), staYearMonth)
            );
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 生成电子党务报告
     * staYearMonth 统计年月 比如2019-11
     *
     * @return
     */
//    @Async("personReportExecutor")
    public void createReport(UserInfoBase userInfoBase, String staYearMonth, List<ActivityInfo> activityInfos, List<ScoreInfo> scoreInfos, List<MeetingInfo> meetingInfos, List<PpmdPayInfo> ppmdPayInfos) throws Exception {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.debug("--------------thread name: [{}] run of createReport  -----------------", Thread.currentThread().getName());
        log.info("开始用户Id-{},月份数据-{},开始时间-{} ", userInfoBase.getUserId(), staYearMonth, stopWatch.toString());
        PersonElectronicReport personElectronicReport = new PersonElectronicReport();
        personElectronicReport.setUserId(userInfoBase.getUserId());
        personElectronicReport.setUserName(userInfoBase.getUserName());
        OrganizationEntity org = this.orgService.getById(userInfoBase.getOrgId());
        // 获取当前
        Region.OrgData orgData = this.configHelper.getOrgByRegionId(org.getRegionId());
        if (org.getParentId().equals(orgData.getOrgId()) &&
                this.orgTypeConfig.getCommunistChild().contains(org.getOrgTypeChild())) {
            personElectronicReport.setOrgName(org.getShortName());
        } else {
            personElectronicReport.setOrgName(org.getName());
        }
        //添加区域Id
        personElectronicReport.setRegionId(org.getRegionId());
        personElectronicReport.setStatisticsTime(staYearMonth);
        // 构建完成服务
        CompletionService completionService = new ExecutorCompletionService(executor);
        // 提到获取活动信息
        completionService.submit(() -> {
            StopWatch stopWatch1 = new StopWatch();
            stopWatch1.start();
            List<ActivityElectronicReport> activityStaInfo = activityService.
                    getActivityStaInfoByUserId(activityInfos, userInfoBase.getUserId(),
                            Integer.parseInt(staYearMonth.replace("-", "")));
            PersonElectronicReport.ActivityJoinInfo activityJoinInfo = new PersonElectronicReport.ActivityJoinInfo();
            List<PersonElectronicReport.ActivityJoinInfo.ActivityJoinList> activityJoinLists = new ArrayList<>();
            if (CollectionUtils.isEmpty(activityStaInfo)) {
                activityJoinInfo.setJoinNum(0);
            } else {
                try {
                    activityJoinInfo.setJoinNum(activityStaInfo.size());
                    activityStaInfo.forEach(item -> {
                        PersonElectronicReport.ActivityJoinInfo.ActivityJoinList activityJoinList
                                = new PersonElectronicReport.ActivityJoinInfo.ActivityJoinList();
                        activityJoinList.setJoinDate(item.getParticipationTime());
                        activityJoinList.setTypeName(item.getActivityTypeName());
                        activityJoinList.setActivityName(item.getActivityName());
                        activityJoinLists.add(activityJoinList);
                    });
                } catch (Exception ex) {
                    return -1;
                }
            }
            activityJoinInfo.setActivityJoinList(activityJoinLists);
            personElectronicReport.setActivityJoinInfo(activityJoinInfo);
            log.debug("--------------thread name: [{}] user name [{}] time [{}] run of activity OK!!-----------------",
                    Thread.currentThread().getName(), userInfoBase.getUserId(), stopWatch1.toString());
            return 1;
        });
        // 获取学习以及扶贫情况
        completionService.submit(() -> {
            StopWatch stopWatch1 = new StopWatch();
            stopWatch1.start();
            UserScoreStudyReport userStudyByMonth = userScoreReportService.
                    getUserStudyByMonth(userInfoBase.getRegionId(), userInfoBase.getUserId(), staYearMonth, scoreInfos);
            PersonElectronicReport.StudyInfo studyInfo = new PersonElectronicReport.StudyInfo();
            PersonElectronicReport.DonateInfo donateInfo = new PersonElectronicReport.DonateInfo();
            if (userStudyByMonth == null) {
                studyInfo.setTotal(UserScoreStudyInfo.getInitEntity());
                studyInfo.setDetail(UserScoreStudyInfo.getInitEntity());
                donateInfo.setTotal(UserScoreDonateInfo.getInitEntity());
                donateInfo.setDetail(UserScoreDonateInfo.getInitEntity());
            } else {
                studyInfo.setTotal(userStudyByMonth.getStudyTotal() == null ? UserScoreStudyInfo.getInitEntity()
                        : userStudyByMonth.getStudyTotal());
                studyInfo.setDetail(userStudyByMonth.getStudyDetail() == null ? UserScoreStudyInfo.getInitEntity()
                        : userStudyByMonth.getStudyDetail());
                donateInfo.setTotal(userStudyByMonth.getDonateTotal() == null ? UserScoreDonateInfo.getInitEntity()
                        : userStudyByMonth.getDonateTotal());
                donateInfo.setDetail(userStudyByMonth.getDonateDetail() == null ? UserScoreDonateInfo.getInitEntity()
                        : userStudyByMonth.getDonateDetail());
            }
            personElectronicReport.setStudyInfo(studyInfo);
            personElectronicReport.setDonateInfo(donateInfo);
            log.debug("--------------thread name: [{}] user name [{}] time [{}] run of score OK!!-----------------",
                    Thread.currentThread().getName(), userInfoBase.getUserId(), stopWatch1.toString());
            return 2;
        });

        //统计党费情况
        completionService.submit(() -> {
            StopWatch stopWatch1 = new StopWatch();
            stopWatch1.start();
            PersonElectronicReport.PpmdPayInfo userPpmdReport = ppmdReportService.getUserPpmdReport(userInfoBase.getUserId(), staYearMonth, ppmdPayInfos);
            personElectronicReport.setPpmdPayInfo(userPpmdReport);
            log.debug("--------------thread name: [{}] user name [{}] time [{}] run of ppmd OK!!-----------------",
                    Thread.currentThread().getName(), userInfoBase.getUserId(), stopWatch1.toString());
            return 3;
        });

        //统计组织生活情况
        completionService.submit(() -> {
            StopWatch stopWatch1 = new StopWatch();
            stopWatch1.start();
            PersonElectronicReport.MeetingJoinInfo meetingJoinInfo = meetingReportService.getUserMeetingReport(userInfoBase.getUserId(), staYearMonth, meetingInfos);
            personElectronicReport.setMeetingJoinInfo(meetingJoinInfo);
            log.debug("--------------thread name: [{}] user name [{}] time [{}] run of meeting OK!!-----------------",
                    Thread.currentThread().getName(), userInfoBase.getUserId(), stopWatch1.toString());
            return 4;
        });

        Object r1 = completionService.take().get();
        Object r2 = completionService.take().get();
        Object r3 = completionService.take().get();
        Object r4 = completionService.take().get();
        log.info("开始用户Id-{},月份数据-{},执行结果r1-({}),r2-({}),r3-({}),r4-({})",
                userInfoBase.getUserId(), staYearMonth, r1, r2, r3, r4);
        log.info("开始用户Id-{},月份数据-{},结束时间-{} ", userInfoBase.getUserId(), staYearMonth, stopWatch.toString());
        stopWatch.stop();
        //关闭线程池
        // executor.isShutdown();
        insertUserStaInfoMongo(personElectronicReport, userInfoBase);
    }

    /**
     * 入库MongoDB
     * 个人电子党务报告这里每个月可以生成多份没有做限制
     * 在查询的时候 根据生成时间倒序查询最近一条记录
     */
    private void insertUserStaInfoMongo(PersonElectronicReport personElectronicReport, UserInfoBase userInfoBase) {
//        log.debug("--------------thread name: [{}] user name [{}] run of MongoUpdateOrInsert-----------------", Thread.currentThread().getName(), userInfoBase.getUserId());
        List<Pair<Query, Update>> updates = new ArrayList<>();
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(userInfoBase.getUserId())).
                addCriteria(Criteria.where("regionId").is(userInfoBase.getRegionId()));
        Update update = new Update();
        update.set("userId", userInfoBase.getUserId());
        update.set("userName", userInfoBase.getUserName());
        update.set("orgName", userInfoBase.getOrgName());
        update.set("regionId", userInfoBase.getRegionId());
        //设定创建时间
        personElectronicReport.setCreateTime(new Date());
        List<PersonElectronicReport> list = new ArrayList<>();
        list.add(personElectronicReport);
        update.addToSet("personElectronicReport").each(list);
        Pair<Query, Update> pair = Pair.of(query, update);
        updates.add(pair);
        BulkWriteResult bulkWriteResult = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, UserStaInfo.class)
                .upsert(updates)
                .execute();
        int matchedCount = bulkWriteResult.getMatchedCount();
        log.debug("--------------thread name: [{}] user name [{}] run of MongoUpdateOrInsert OK!!-----------------", Thread.currentThread().getName(), userInfoBase.getUserId());
        log.info("userId-{},matchedCount-{}", userInfoBase.getUserId(), matchedCount);
    }


}

