package com.goodsogood.ows.service.electronicreport;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.config.ApplicationConfigHelper;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.RegionListConfig;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.OrgReportInfo;
import com.goodsogood.ows.model.mongodb.report.*;
import com.goodsogood.ows.model.vo.report.OrgReportForm;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @description 异步电子党务报告服务层
 * @date 2019/11/27
 */
@Service
@Log4j2
public class OrgReportService {

    private final AsyncReportService reportService;
    private final OrgService orgService;
    private final OrgTypeConfig orgTypeConfig;
    private final SimpleApplicationConfigHelper applicationConfigHelper;
    private final RegionListConfig regionListConfig;

    @Value("${region-label}")
    private String label;

    @Autowired
    public OrgReportService(AsyncReportService reportService, OrgService orgService, OrgTypeConfig orgTypeConfig,
                            SimpleApplicationConfigHelper applicationConfigHelper, RegionListConfig regionListConfig) {
        this.reportService = reportService;
        this.orgService = orgService;
        this.orgTypeConfig = orgTypeConfig;
        this.applicationConfigHelper = applicationConfigHelper;
        this.regionListConfig = regionListConfig;
    }

    /**
     * 循环组织，查询电子报告信息，存库
     * <AUTHOR>
     * @date 2019/11/26
     * @param queryTime
     * @return void
     */
    public void runOrgReport(String queryTime) {
        //得到需要运行的区县列表
        List<Long> regionList = this.regionListConfig.getReport();
        regionList.forEach(regionId->{
        //LinkedList<OrganizationEntity> orgList = this.orgService.findAllChildOrg(Constants.ROOT_ORG_ID, Constants.INCLUDE,item.getRegionId());
        //得到它的顶级id
        Long topOrgId = this.applicationConfigHelper.getOrgByRegionId(regionId).getOrgId();
        log.debug("[{}]区县的顶级组织 -> [{}]", regionId, topOrgId);
        LinkedList<OrganizationEntity> orgList = this.orgService.findAllChildOrg(topOrgId, Constants.INCLUDE, regionId, false);
            if (!CollectionUtils.isEmpty(orgList)) {
                Date date = new Date();
                CountDownLatch latch = new CountDownLatch(orgList.size());
                orgList.forEach(org -> {
                    String[] str = queryTime.split("-");
                    if (str.length != 2){
                        log.error("时间格式出现问题");
                        return;
                    }
                    Integer year = Integer.valueOf(str[0]);
                    Integer month = Integer.valueOf(str[1]);
                    OrgReportInfo orgReport = this.reportService.getOrgReportInfoByMongo(org.getOrganizationId(), year);
                    if (orgReport == null ){
                        orgReport =  new OrgReportInfo();
                        // 封装基本信息
                        orgReport.setOrgId(org.getOrganizationId());
                        orgReport.setStatsYear(year);
                    }
                    if (org.getParentId().equals(topOrgId) &&
                            this.orgTypeConfig.getCommunistChild().contains(org.getOrgTypeChild())) {
                        orgReport.setOrgName(StringUtils.isBlank(org.getShortName()) ? org.getName() : org.getShortName());
                    } else {
                        orgReport.setOrgName(org.getName());
                    }
                    orgReport.setRegionId(regionId);
                    this.reportService.setOrgReportInfo(org.getOrganizationId(), orgReport, queryTime, month, date, latch);
                });
                try {
                    latch.await();
                    orgList.clear();
                } catch (InterruptedException e) {
                    log.error("计数器异常", e);
                    Thread.currentThread().interrupt();
                }
            }
        });
    }

    /**
     * 单个组织的电子报告
     * <AUTHOR>
     * @date 2019/11/26
     * @param orgId
     * @param startDate
     * @param endDate
     * @return com.goodsogood.ows.model.mongodb.OrgReportInfo
     */
    public OrgReportForm getOrgReportInfo(Long orgId, String startDate, String endDate){
        OrgReportForm reportForm = new OrgReportForm();
        // 获取时间间隔的月份列表
        List<Calendar> dateList = this.getMonthList(startDate, endDate);
        List<OrgReportInfo> reportList = this.getReportList(dateList, orgId);

        // 封装数据
        reportForm.setOrgId(orgId);
        reportForm.setOrgName(reportList.get(0).getOrgName());
        reportForm.setStartDate(startDate);
        reportForm.setEndDate(endDate);

        PpmdReportInfo ppmdReportInfo = new PpmdReportInfo();
        MeetingReportInfo meetingReportInfo = new MeetingReportInfo();
        ActivityReportInfo activityReportInfo = new ActivityReportInfo();
        StudyReportInfo studyReportInfo = new StudyReportInfo();
        DonateReportInfo donateReportInfo = new DonateReportInfo();

        for (OrgReportInfo reportInfo : reportList) {
            List<OrgReportInfo.ReportInfo> reportInfoList = reportInfo.getReportInfoList();
            for (OrgReportInfo.ReportInfo report : reportInfoList) {
                boolean match = dateList.stream().anyMatch(date -> report.getStatsMonth().equals(date.get(Calendar.MONTH) + 1));
                if (match) {
                    // 党费报告
                    PpmdReportInfo ppmdReport = report.getPpmdReport();
                    if (dateList.size() == 1) {
                        ppmdReportInfo.setFirstOrg(ppmdReport.getFirstOrg());
                        ppmdReportInfo.setFirstUser(ppmdReport.getFirstUser());
                    }
                    BigDecimal b1 = BigDecimal.valueOf(ppmdReportInfo.getPayAmount());
                    BigDecimal b2 = BigDecimal.valueOf(ppmdReport.getPayAmount());
                    ppmdReportInfo.setPayAmount(b1.add(b2).doubleValue());
                    ppmdReportInfo.setPayNum(ppmdReportInfo.getPayNum() + ppmdReport.getPayNum());
                    // 纪实报告 -> 党组织信息
                    MeetingReportInfo meetingReport = report.getMeetingReport();
                    MeetingReportInfo.OrgNum orgNum = meetingReport.getOrgNum();
                    if (null != orgNum) {
                        boolean b = null != meetingReportInfo.getOrgNum();
                        orgNum.setBranchNum(orgNum.getBranchNum() + (b ? meetingReportInfo.getOrgNum().getBranchNum() : 0));
                        orgNum.setCommunistNum(orgNum.getCommunistNum() + (b ? meetingReportInfo.getOrgNum().getCommunistNum() : 0));
                        orgNum.setPartyNum(orgNum.getPartyNum() + (b ? meetingReportInfo.getOrgNum().getPartyNum() : 0));
                        orgNum.setOtherNum(orgNum.getOtherNum() + (b ? meetingReportInfo.getOrgNum().getOtherNum() : 0));
                    }
                    meetingReportInfo.setOrgNum(orgNum);
                    // 纪实报告 -> 组织生活信息
                    MeetingReportInfo.MeetingNum meetingNum = meetingReport.getMeetingNum();
                    if (null != meetingNum) {
                        meetingNum.setMeetingCount(meetingNum.getMeetingCount() + (null != meetingReportInfo.getMeetingNum() ? meetingReportInfo.getMeetingNum().getMeetingCount() : 0));
                        List<MeetingReportInfo.MeetingNum.Type> typeList = meetingReport.getMeetingNum().getTypeList();
                        List<MeetingReportInfo.MeetingNum.Type> typeList1 = (null != meetingReportInfo.getMeetingNum() ? meetingReportInfo.getMeetingNum().getTypeList() : new ArrayList<>());
                        List<MeetingReportInfo.MeetingNum.Type> newList = new ArrayList<>();
                        for (MeetingReportInfo.MeetingNum.Type type : typeList) {
                            boolean bl = true;
                            for (MeetingReportInfo.MeetingNum.Type type1 : typeList1) {
                                if (type.getId().equals(type1.getId())) {
                                    bl = false;
                                    type1.setNum(type.getNum() + type1.getNum());
                                }
                            }
                            if (bl) {
                                newList.add(type);
                            }
                        }
                        newList.addAll(typeList1);
                        meetingNum.setTypeList(newList);
                        meetingReportInfo.setMeetingNum(meetingNum);
                    }
                    // 活动报告
                    ActivityReportInfo activityReport = report.getActivityReport();
                    activityReportInfo.setPersonNum(activityReportInfo.getPersonNum() + activityReport.getPersonNum());
                    List<ActivityReportInfo.Type> activityReportTypeList = activityReport.getTypeList();
                    if (!CollectionUtils.isEmpty(activityReportTypeList)) {
                        for (ActivityReportInfo.Type type : activityReportTypeList) {
                            type.setNum(type.getNum() + (null != activityReportInfo.getTypeList() ? activityReportInfo.getTypeList().stream().filter(t -> t!=null && t.getType().equals(type.getType())).findFirst().get().getNum() : 0));
                        }
                    }
                    activityReportInfo.setTypeList(activityReportTypeList);
                    // 学习报告
                    StudyReportInfo studyReport = report.getStudyReport();
                    if (dateList.size() == 1) {
                        studyReportInfo.setFirstName(studyReport.getFirstName());
                    }
                    studyReportInfo.setBookNum(studyReportInfo.getBookNum() + studyReport.getBookNum());
                    studyReportInfo.setPersonNum(studyReportInfo.getPersonNum() + studyReport.getPersonNum());
                    studyReportInfo.setScore(studyReportInfo.getScore() + studyReport.getScore());
                    // 扶贫情况
                    DonateReportInfo donateReport = report.getDonateReport();
                    donateReportInfo.setCharityAmount(donateReportInfo.getCharityAmount() + donateReport.getCharityAmount());
                    donateReportInfo.setCharityNum(donateReportInfo.getCharityNum() + donateReport.getCharityNum());
                    donateReportInfo.setDonateNum(donateReportInfo.getDonateNum() + donateReport.getDonateNum());
                    donateReportInfo.setDonateScore(donateReportInfo.getDonateScore() + donateReport.getDonateScore());
                    donateReportInfo.setPovertyPersonNum(donateReportInfo.getPovertyPersonNum() + donateReport.getPovertyPersonNum());
                    donateReportInfo.setPovertyAmount(donateReportInfo.getPovertyAmount() + donateReport.getPovertyAmount());
                    donateReportInfo.setPovertyNum(donateReportInfo.getPovertyNum() + donateReport.getPovertyNum());

                }
            }
            reportForm.setPpmdReport(ppmdReportInfo);
            reportForm.setMeetingReport(meetingReportInfo);
            reportForm.setActivityReport(activityReportInfo);
            reportForm.setStudyReport(studyReportInfo);
            reportForm.setDonateReport(donateReportInfo);
        }

        return reportForm;
    }

    /**
     * 根据两个时间获取月份列表
     * @param startDate
     * @param endDate
     * @return
     */
    private List<Calendar> getMonthList(String startDate, String endDate) {
        List<Calendar> result = new ArrayList<>();
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
            Date start = format.parse(startDate);
            Date end = format.parse(endDate);
            // 定义时间
            Calendar date = Calendar.getInstance();
            date.setTime(start);
            // 判断是否到结束日期
            while (date.getTime().before(end)) {
                Calendar tempDate = Calendar.getInstance();
                tempDate.setTime(date.getTime());
                result.add(tempDate);
                date.add(Calendar.MONTH, 1);
            }
            result.add(date);
        } catch (Exception e) {
            log.error("时间转换出现问题", e);
        }
        return result;
    }

    /**
     * 获取组织报告列表
     * @param dateList
     * @param orgId
     * @param orgId
     * @return
     */
    private List<OrgReportInfo> getReportList(List<Calendar> dateList, Long orgId) {
        List<OrgReportInfo> reportList = new ArrayList<>(dateList.size());
        Set<Integer> yearList = new HashSet<>();
        OrganizationEntity org = this.orgService.getById(orgId);
        Region.OrgData orgData = this.applicationConfigHelper.getOrgByRegionId(org.getRegionId());
        // 循环在mongoDB里查询出数据
        for (Calendar date : dateList) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
            Integer year = date.get(Calendar.YEAR);
            Integer month = date.get(Calendar.MONTH) + 1;
            OrgReportInfo reportInfo = this.reportService.getOrgReportInfoByMongo(orgId, year);
            if (null == reportInfo) {
                reportInfo = new OrgReportInfo();
                reportInfo.setOrgId(orgId);
                if (org.getParentId().equals(orgData.getOrgId()) &&
                        this.orgTypeConfig.getCommunistChild().contains(org.getOrgTypeChild())) {
                    reportInfo.setOrgName(org.getShortName());
                } else {
                    reportInfo.setOrgName(org.getName());
                }
                reportInfo.setStatsYear(year);
            }
            // 判断是否已经查询当年数据
            boolean anyMatch = yearList.stream().anyMatch(y -> year.equals(y));
            // 判断数据中是否存在查询月数据
            List<OrgReportInfo.ReportInfo> reportInfoList = reportInfo.getReportInfoList() == null ? new ArrayList<>() : reportInfo.getReportInfoList();
            boolean match = reportInfoList.stream().anyMatch(info -> month.equals(info.getStatsMonth()));
            if (!match) {
                try {
                    CountDownLatch latch = new CountDownLatch(1);
                    reportInfo = this.reportService.setOrgReportInfo(orgId, reportInfo, format.format(date.getTime()), month, new Date(), latch).get();
                    latch.await();
                } catch (InterruptedException | ExecutionException e) {
                    log.error("计数器异常", e);
                    Thread.currentThread().interrupt();
                }
            }
            if (anyMatch) {
                if (match) {
                    continue;
                } else {
                    reportList.clear();
                    yearList.clear();
                }
            }
            reportList.add(reportInfo);
            yearList.add(year);
        }
        return reportList;
    }
}
