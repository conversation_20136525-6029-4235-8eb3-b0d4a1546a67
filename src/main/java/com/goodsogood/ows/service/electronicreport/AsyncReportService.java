package com.goodsogood.ows.service.electronicreport;

import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.model.mongodb.OrgReportInfo;
import com.goodsogood.ows.model.mongodb.report.*;
import com.goodsogood.ows.model.vo.activity.ActivityOrgElectronicReport;
import com.goodsogood.ows.model.vo.score.ScoreOrgElectronicReport;
import com.goodsogood.ows.service.activity.ActivityService;
import com.goodsogood.ows.service.meeting.MeetingReportService;
import com.goodsogood.ows.service.ppmd.PpmdReportService;
import com.goodsogood.ows.service.score.ScoreService;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @description 党务报告服务层
 * @date 2019/11/25
 */
@Service
@Log4j2
public class AsyncReportService {

    private final OrgService orgService;
    private final PpmdReportService ppmdReportService;
    private final MeetingReportService meetingReportService;
    private final ActivityService activityService;
    private final ScoreService scoreService;
    private final MyMongoTemplate mongoTemplate;

    @Autowired
    public AsyncReportService(OrgService orgService, PpmdReportService ppmdReportService,
                              MeetingReportService meetingReportService, ActivityService activityService,
                              ScoreService scoreService, MyMongoTemplate mongoTemplate) {
        this.orgService = orgService;
        this.ppmdReportService = ppmdReportService;
        this.meetingReportService = meetingReportService;
        this.activityService = activityService;
        this.scoreService = scoreService;
        this.mongoTemplate = mongoTemplate;
    }

    /**
     * 获取组织的电子报告信息并存库
     *
     * @param orgId
     * @param queryTime
     * @param date
     * @return com.goodsogood.ows.model.mongodb.OrgReportInfo
     * <AUTHOR>
     * @date 2019/11/26
     */
    @Async("reportExecutor")
    @Transactional
    Future<OrgReportInfo> setOrgReportInfo(Long orgId, OrgReportInfo orgReport, String queryTime, Integer month, Date date, CountDownLatch latch) {
        List<OrgReportInfo.ReportInfo> reportInfoList = orgReport.getReportInfoList() == null ? new ArrayList<>() : orgReport.getReportInfoList();
        try {
            // 判断是否存在当月数据
            boolean match = reportInfoList.stream().anyMatch(info -> month.equals(info.getStatsMonth()));
            // 设置月份
            OrgReportInfo.ReportInfo reportInfo = new OrgReportInfo().new ReportInfo();
            reportInfo.setStatsMonth(month);
            // 党费信息
            PpmdReportInfo orgPpmdReport = this.ppmdReportService.getOrgPpmdReport(orgId, queryTime);
            reportInfo.setPpmdReport(orgPpmdReport);
            //  组织生活情况
            MeetingReportInfo orgMeetingInfo = this.meetingReportService.getOrgMeetingInfo(orgId, queryTime);
            reportInfo.setMeetingReport(orgMeetingInfo);
            // 党群活动情况
            ActivityReportInfo orgActivityReport = this.getOrgActivityReport(orgId, queryTime);
            reportInfo.setActivityReport(orgActivityReport);
            // 学习和扶贫情况
            this.getOrgScoreReport(orgId, queryTime, reportInfo);
            // 判断原本是否存在当月数据
            if (match) {
                // 替代原数据
                for (int i = 0; i < reportInfoList.size(); i++) {
                    OrgReportInfo.ReportInfo info = reportInfoList.get(i);
                    if (month.equals(info.getStatsMonth())) {
                        reportInfoList.set(i, reportInfo);
                        break;
                    }
                }
            } else {
                reportInfoList.add(reportInfo);
            }
            // 时间
            orgReport.setCreateTime(date);
            orgReport.setUpdateTime(date);
            // 排序
            Collections.sort(reportInfoList);
            orgReport.setReportInfoList(reportInfoList);
            // 新增或更新
            this.saveOrgReport(orgReport);

        } catch (Exception e) {
            log.error("计算组织[" + orgId + "]的党务报告出现问题", e);
        } finally {
            latch.countDown();
        }
        return new AsyncResult<OrgReportInfo>(orgReport);
    }


    /**
     * 查询组织党群活动数据
     *
     * @param orgId
     * @param queryTime
     * @return com.goodsogood.ows.model.mongodb.OrgReportInfo.ActivityReport
     * <AUTHOR>
     * @date 2019/11/26
     */
    private ActivityReportInfo getOrgActivityReport(Long orgId, String queryTime) {
        ActivityReportInfo activityReport = new ActivityReportInfo();
        Integer date = Integer.valueOf(queryTime.replace("-", ""));
        List<ActivityOrgElectronicReport> activityStaInfo = this.activityService.getActivityStaInfoByOrgId(orgId, date);
        if (!CollectionUtils.isEmpty(activityStaInfo)) {
            List<ActivityReportInfo.Type> typeList = new ArrayList<>(activityStaInfo.size());
            activityStaInfo.forEach(report -> {
                ActivityReportInfo.Type type = new ActivityReportInfo().new Type();
                type.setType(report.getTypeName());
                type.setNum(report.getParticipantCount());
                typeList.add(type);
            });
            activityReport.setTypeList(typeList);
        }
        return activityReport;
    }

    private void getOrgScoreReport(Long orgId, String queryTime, OrgReportInfo.ReportInfo reportInfo) {
        try {
            ScoreOrgElectronicReport orgStudyPovertyInfo = this.scoreService.getOrgStudyPovertyInfo(orgId, queryTime);
            // 组织学习情况
            UserScoreStudyInfo studyInfo = orgStudyPovertyInfo.getUserScoreStudyInfo();
            if (null != studyInfo) {
                StudyReportInfo studyReport = new StudyReportInfo();
                studyReport.setPersonNum(studyInfo.getStudyNum());
                studyReport.setScore(studyInfo.getReceiveScoreNum());
                studyReport.setBookNum(studyInfo.getExchangeBookNum());
                studyReport.setFirstName(orgStudyPovertyInfo.getUserName());
                reportInfo.setStudyReport(studyReport);
            }
            // 扶贫情况
            UserScoreDonateInfo donateInfo = orgStudyPovertyInfo.getUserScoreDonateInfo();
            if (null != donateInfo) {
                DonateReportInfo donateReport = new DonateReportInfo();
                donateReport.setDonateNum(donateInfo.getDonateNum());
                donateReport.setDonateScore(donateInfo.getDonateScore());
                donateReport.setPovertyPersonNum(donateInfo.getPovertyPersonNum());
                donateReport.setPovertyNum(donateInfo.getPovertyNum());
                donateReport.setPovertyAmount(donateInfo.getPovertyMoney());
                donateReport.setCharityNum(donateInfo.getCharityNum());
                donateReport.setCharityAmount(donateInfo.getCharityMoney());
                reportInfo.setDonateReport(donateReport);
            }
        } catch (Exception e) {
            log.error("获取组织积分情况异常", e);
        }
    }

    /**
     * 如果MongoDB存在则更新，不存在就新增
     *
     * @param reportInfo
     * @return void
     * <AUTHOR>
     * @date 2019/11/26
     */
    private void saveOrgReport(OrgReportInfo reportInfo) {
        OrgReportInfo orgReportInfo = this.getOrgReportInfoByMongo(reportInfo.getOrgId(),
                reportInfo.getStatsYear());
        if (null == orgReportInfo) {
            this.mongoTemplate.save(reportInfo);
        } else {
            this.updateOrgReport(reportInfo);
        }
    }

    /**
     * 从MongoDB查询数据
     *
     * @param orgId
     * @param year
     * @return java.util.List<com.goodsogood.ows.model.mongodb.OrgReportInfo>
     * <AUTHOR>
     * @date 2019/11/26
     */
    public OrgReportInfo getOrgReportInfoByMongo(Long orgId, Integer year,Long regionId) {
        Criteria criteria = new Criteria();
        criteria.andOperator(
                Criteria.where("orgId").is(orgId),
                Criteria.where("statsYear").is(year),
                Criteria.where("regionId").is(regionId)
        );
        Query query = new Query(criteria);
        OrgReportInfo orgReportInfo = this.mongoTemplate.findOne(query, OrgReportInfo.class);
        log.debug("查询结果 -> [{}]", orgReportInfo);
        return orgReportInfo;
    }

    /**
     * 从MongoDB查询数据
     *
     * @param orgId
     * @param year
     * @return java.util.List<com.goodsogood.ows.model.mongodb.OrgReportInfo>
     * <AUTHOR>
     * @date 2019/11/26
     */
    public OrgReportInfo getOrgReportInfoByMongo(Long orgId, Integer year ) {
        Criteria criteria = new Criteria();
        criteria.andOperator(
                Criteria.where("orgId").is(orgId),
                Criteria.where("statsYear").is(year)
        );
        Query query = new Query(criteria);
        OrgReportInfo orgReportInfo = this.mongoTemplate.findOne(query, OrgReportInfo.class);
        log.debug("查询结果 -> [{}]", orgReportInfo);
        return orgReportInfo;
    }

    /**
     * 更新组织电子报告Doc
     *
     * @param reportInfo
     * @return void
     * <AUTHOR>
     * @date 2019/11/26
     */
    private void updateOrgReport(OrgReportInfo reportInfo) {
        // 查询字段
        Criteria criteria = new Criteria();
        criteria.andOperator(
                Criteria.where("orgId").is(reportInfo.getOrgId()),
                Criteria.where("statsYear").is(reportInfo.getStatsYear())
        );
        Query query = new Query(criteria);
        // 更新字段
        Update update = new Update();
        update.set("orgName", reportInfo.getOrgName());
        update.set("reportInfoList", reportInfo.getReportInfoList());
        update.set("updateTime", reportInfo.getUpdateTime());
        update.set("regionId", reportInfo.getRegionId());
        this.mongoTemplate.upsert(query, update, OrgReportInfo.class);
    }
}
