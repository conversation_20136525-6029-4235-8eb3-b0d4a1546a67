package com.goodsogood.ows.service.impl;

import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import org.springframework.scheduling.annotation.Async;

import java.util.ArrayList;
import java.util.List;

/**
 * 决策辅助党委详情页构造接口
 * <AUTHOR>
 */
public interface DssPartyCommitteeBuilder {

    /**
     * 生成决策辅助党委详情页
     * @param info 决策辅助党委详情页实体类
     *             info.organizationId 组织ID
     *             info.year   生成年份
     * @return 相应的模块存入相应的数据后返回
     */
    PartyCommitteeInfo buildPartyCommittee(PartyCommitteeInfo info);

    /**
     * 生成决策辅助党委详情页  - 批量处理
     * @param infoList 决策辅助党委详情页实体类列表
     *             info.organizationId 组织ID
     *             info.year   生成年份
     * @return 相应的模块存入相应的数据后返回
     */
    default List<PartyCommitteeInfo> buildPartyCommitteeList(List<PartyCommitteeInfo> infoList) {
        List<PartyCommitteeInfo> infos = new ArrayList<>(infoList.size());
        infoList.forEach(info -> {
            info = buildPartyCommittee(info);
            infos.add(info);
        });
        return infos;
    }
}
