package com.goodsogood.ows.service.impl;

import com.goodsogood.ows.model.mongodb.UserInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 决策辅助用户详情页构造接口
 * <AUTHOR>
 */
public interface DssUserBuilder {

    /**
     * 生成决策辅助用户详情页
     * @param info 决策辅助用户详情页实体类
     *             info.organizationId 组织ID
     *             info.year   生成年份
     *             info.regionId 区县
     * @return 相应的模块存入相应的数据后返回
     */
    UserInfo buildUser(UserInfo info);

    /**
     * 生成决策辅助用户详情页 - 批量处理
     * @param infoList 决策辅助用户详情页实体类列表
     *             info.organizationId 组织ID
     *             info.year   生成年份
     *             info.regionId 区县
     * @return 相应的模块存入相应的数据后返回
     */
    default List<UserInfo> buildUserList(List<UserInfo> infoList) {
        List<UserInfo> infos = new ArrayList<>(infoList.size());
        infoList.forEach(info -> {
            info = buildUser(info);
            infos.add(info);
        });
        return infos;
    }

}
