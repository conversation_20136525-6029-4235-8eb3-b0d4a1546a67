package com.goodsogood.ows.service.impl;


import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import java.util.ArrayList;
import java.util.List;

/**
 * 决策辅助党支部详情页构造接口
 * <AUTHOR>
 */
public interface DssPartyBranchBuilder {

    /**
     * 生成决策辅助党支部详情页
     * @param info 决策辅助党支部详情页实体类
     *             info.organizationId 组织ID
     *             info.year   生成年份
     * @return 相应的模块存入相应的数据后返回
     */
    PartyBranchInfo buildPartyBranch(PartyBranchInfo info);

    /**
     * 生成决策辅助党支部详情页 - 批量处理
     * @param infoList 决策辅助党支部详情页实体类列表
     *             info.organizationId 组织ID
     *             info.year   生成年份
     * @return 相应的模块存入相应的数据后返回
     */
    default List<PartyBranchInfo> buildPartyBranchList(List<PartyBranchInfo> infoList){
        List<PartyBranchInfo> infos = new ArrayList<>(infoList.size());
        infoList.forEach(info -> {
            info = buildPartyBranch(info);
            infos.add(info);
        });
        return infos;
    }

}
