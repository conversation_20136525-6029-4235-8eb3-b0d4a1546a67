package com.goodsogood.ows.service.learn;

import com.goodsogood.ows.configuration.DssIndexScoreConstant;
import com.goodsogood.ows.configuration.LearnScoreConfiguration;
import com.goodsogood.ows.mapper.learn.TheoryArmsMapper;
import com.goodsogood.ows.mapper.score.ScoreTaskStatusMapper;
import com.goodsogood.ows.model.db.doris.IndexUserScoreEntity;
import com.goodsogood.ows.model.db.score.ScoreTaskStatusEntity;
import com.goodsogood.ows.model.vo.learn.LearnScoreVO;
import com.goodsogood.ows.model.vo.score.ScoreConsumeForm;
import com.goodsogood.ows.service.dss.DssDorisScoreService;
import com.goodsogood.ows.service.score.ScoreAddService;
import com.goodsogood.ows.utils.RedisLockUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: mengting
 * @Date: 2022/7/18 17:42
 */
@Service
@Log4j2
public class LearnService {
    private final TheoryArmsMapper theoryArmsMapper;
    private final LearnScoreConfiguration learnScoreConfiguration;
    private final StringRedisTemplate stringRedisTemplate;
    private final DssDorisScoreService dssDorisScoreService;
    @Value("${scheduler.exam-score.run}")
    private Boolean examRun;
    private final String EXAMLOCK = "exam-score-lock";
    private final String TRANNINGKEY = "training_score";
    private final String EXAMMONTHKEY = "exam_month_score";
    private final String EXAMQUARTZKEY = "exam_quartz_score";

    private final String DAYFINISHKEY = "day_study_finish";
    private final String MONTHFINISHKEY = "month_study_finish";
    private final String QUARTFINISHKEY = "quart_study_finish";


    private final Integer TRANNINGEXPIRE = 1;
    private final Integer EXAMMONTHEXPIRE = 32;
    private final Integer EXAMQUARTZEXPIRE = 93;
    private final List<Long> testUser = Arrays.asList(14285L, 9L, 8L);

    private final ScoreTaskStatusMapper scoreTaskStatusMapper;


    private final RedisTemplate redisTemplate;


    private final ScoreAddService scoreAddService;

    @Autowired
    public LearnService(TheoryArmsMapper theoryArmsMapper, LearnScoreConfiguration learnScoreConfiguration, StringRedisTemplate stringRedisTemplate, DssDorisScoreService dssDorisScoreService, ScoreTaskStatusMapper scoreTaskStatusMapper, RedisTemplate redisTemplate, ScoreAddService scoreAddService) {
        this.theoryArmsMapper = theoryArmsMapper;
        this.learnScoreConfiguration = learnScoreConfiguration;
        this.stringRedisTemplate = stringRedisTemplate;
        this.dssDorisScoreService = dssDorisScoreService;
        this.scoreTaskStatusMapper = scoreTaskStatusMapper;
        this.redisTemplate = redisTemplate;
        this.scoreAddService = scoreAddService;
    }

    @Scheduled(cron = "${scheduler.exam-score.cron}")
    public void runScheduler() {
        Runnable task = this::run;
        schedulerHandler(examRun, EXAMLOCK, task);
    }


    public void schedulerHandler(Boolean run, String lock, Runnable task) {
        if (!run) {
            log.debug("学习加积分：每日一练+每月一测+每季一考：未开启定时任务");
            return;
        }
        String uuid = null;
        try {
            uuid = UUID.randomUUID().toString();
            Thread.sleep((new Random().nextInt(10) + 1) * 1000);
            boolean b = RedisLockUtil.tryGetDistributedLock(stringRedisTemplate, lock, uuid, 60 * 1000);
            if (!b) {
                log.debug("学习加积分：每日一练等->run 未获取到锁,结束执行!");
                return;
            }
            task.run();
        } catch (Exception e) {
            e.printStackTrace();
            log.debug("学习加积分：每日一练等->run 出现异常:[{}]", e.getMessage(), e);
        } finally {
            RedisLockUtil.releaseDistributedLock(stringRedisTemplate, lock, uuid);
        }
    }


    public void run() {
        try {
            training();
        } catch (Exception e) {
            log.error("每日一练积分发送失败" + e);
        }
        try {
            examMonth();
        } catch (Exception e) {
            log.error("每月一测积分发送失败" + e);
        }
        try {
            examQuart();
        } catch (Exception e) {
            log.error("每季一考积分发送失败" + e);
        }
        try {
            statusFinish(LocalDate.now());
        } catch (Exception e) {
            log.error("学习教育已完成状态更新失败:" + e);
        }
    }


    public void statusFinish(LocalDate date) {
        List<ScoreTaskStatusEntity> list = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String todayStr = date.format(formatter);
        //把已经进行每日一练的数据放入积分任务完成表，每日一练即便不加分，也是显示“已完成”.==>任务流水表不再记录，因为记录会有很多重复数据，该任务是15分钟一跑
        Set<String> trainingFinishUser = theoryArmsMapper.queryTrainingUser(todayStr, learnScoreConfiguration.getTraining().getId());
        //查看缓存中是否已有
        Set<String> hadDayScoreUser = redisTemplate.opsForSet().members(DAYFINISHKEY + todayStr);
        if (!CollectionUtils.isEmpty(hadDayScoreUser)) {
            trainingFinishUser.removeAll(hadDayScoreUser);
        }
        insertStatus(trainingFinishUser, 13L, list);
        for (String userId : trainingFinishUser) {
            redisTemplate.opsForSet().add(DAYFINISHKEY + todayStr, userId);
            redisTemplate.expire(DAYFINISHKEY + todayStr, TRANNINGEXPIRE, TimeUnit.DAYS);
        }
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String thisMonth = date.format(monthFormatter);
        //每月一测
        Set<String> monthFinishUser = theoryArmsMapper.queryExamineMonthUser(thisMonth, learnScoreConfiguration.getExaminMonth().getId());
        if (!CollectionUtils.isEmpty(monthFinishUser)) {
            Set<String> monthHadScoreUser = redisTemplate.opsForSet().members(MONTHFINISHKEY + thisMonth);
            if (!CollectionUtils.isEmpty(monthHadScoreUser)) {
                monthFinishUser.removeAll(monthHadScoreUser);
            }
            insertStatus(monthFinishUser, 14L, list);
            for (String userId : monthFinishUser) {
                redisTemplate.opsForSet().add(MONTHFINISHKEY + thisMonth, userId);
                redisTemplate.expire(MONTHFINISHKEY + thisMonth, EXAMMONTHEXPIRE, TimeUnit.DAYS);
            }
        }
        int month = date.getMonthValue();
        int quarter = month % 3 == 0 ? month / 3 : month / 3 + 1;
        int year = date.getYear();
        Long examId = learnScoreConfiguration.getExaminQuart().getId();
        //每季一考
        Set<String> quartFinishUser = theoryArmsMapper.queryExamineQuartUser(quarter, year, examId);
        if (!CollectionUtils.isEmpty(quartFinishUser)) {
            Set<String> quartHadScoreUser = redisTemplate.opsForSet().members(QUARTFINISHKEY + quarter);
            if (!CollectionUtils.isEmpty(quartHadScoreUser)) {
                quartFinishUser.removeAll(quartHadScoreUser);
            }
            insertStatus(quartFinishUser, 15L, list);
            for (String userId : quartFinishUser) {
                redisTemplate.opsForSet().add(QUARTFINISHKEY + quarter, userId);
                redisTemplate.expire(QUARTFINISHKEY + quarter, EXAMQUARTZEXPIRE, TimeUnit.DAYS);
            }
        }
        //调用积分中心进行数据新增
        if (!CollectionUtils.isEmpty(list)) {
            scoreTaskStatusMapper.insertData(list);
        }
    }

    public void insertStatus(Set<String> finishUser, Long taskInfoId, List<ScoreTaskStatusEntity> list) {
        for (String userId : finishUser) {
            ScoreTaskStatusEntity statusEntity = new ScoreTaskStatusEntity();
            statusEntity.setTaskInfoId(taskInfoId);
            statusEntity.setObjectId(Long.valueOf(userId));
            list.add(statusEntity);
        }
    }

    //每日一练 全对加3分，每天只能加一次
    public void training() {
        log.debug("每日一练开始");
        LocalDateTime time = LocalDateTime.now();
        //如果当前是2点以前，再把上日的数据跑一次，因为两小时一次会因为22-24点之间的数据会处理不到
        if (time.getHour() < 2) {
            LocalDate lastDay = LocalDate.now().minusDays(1);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String lastDayStr = lastDay.format(formatter);
            trainingByDate(lastDayStr);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String todayStr = LocalDate.now().format(formatter);
        trainingByDate(todayStr);
    }

    public void trainingByDate(String todayStr) {
        Set<Long> user = theoryArmsMapper.queryTraining(todayStr, learnScoreConfiguration.getTraining().getId());
        //查询redis中哪些人今日已经加过积分
        Set<Long> hadScoreUser = redisTemplate.opsForSet().members(TRANNINGKEY + todayStr);
        //如果缓存没有，不用查数据库了，积分中心会自动过滤重复数据
        //排除已经加过积分的
        user.removeAll(hadScoreUser);
        //只选取测试人员
//        Set<Long> user = user1.stream().filter(i->testUser.contains(i)).collect(Collectors.toSet());
        //给这些人加积分
        List<IndexUserScoreEntity> dataList = new ArrayList();
        for (Long userId : user) {
            try {
                String token = TRANNINGKEY + todayStr + "_" + userId;
                Double score = learnScoreConfiguration.getTraining().getScore();
                Integer dataMonth = Integer.parseInt(todayStr.substring(0, 4) + todayStr.substring(5, 7));
                sendScore(token, userId, score, learnScoreConfiguration.getTraining().getExplain(), learnScoreConfiguration.getTraining().getRemark());
                dataList.add(dssDorisScoreService.createUserScore(DssIndexScoreConstant.LEARNBYDATE, userId, score.longValue(), dataMonth));
                log.debug("mt添加到缓存开始1");
                redisTemplate.opsForSet().add(TRANNINGKEY + todayStr, userId);
                redisTemplate.expire(TRANNINGKEY + todayStr, TRANNINGEXPIRE, TimeUnit.DAYS);
                log.debug("mt添加到缓存结束1");
            } catch (Exception e) {
                log.error(userId + "每日一练加学习积分出错" + e);
            }
        }
        dssDorisScoreService.batchInsertUserScore(dataList);
    }

    private void sendScore(String token, Long userId, Double score, String explain, String remark) {
        ScoreConsumeForm scoreConsumeForm = new ScoreConsumeForm();
        scoreConsumeForm.setUserId(userId);
        scoreConsumeForm.setScoreType(30);//学习教育积分
        scoreConsumeForm.setOrgId(3L);
        scoreConsumeForm.setOperType(0);
        scoreConsumeForm.setScore(score.longValue());
        log.debug("业务指标分值：" + scoreConsumeForm.getScore());
        scoreConsumeForm.setToken(token);
        scoreConsumeForm.setExplainTxt(explain);
        scoreConsumeForm.setRemark(remark);
        log.debug("调用李国勇方法增加积分" + scoreConsumeForm);
        scoreAddService.addScoreByUserId(86L, scoreConsumeForm);
    }

    //每月一测 每月第一次答题加分 答对一题加0.5
    //查询昨日有哪些人属于第一次答题
    public void examMonth() {
        log.debug("每月一测开始");
        if (LocalDate.now().getDayOfMonth() == 1 && LocalDateTime.now().getHour() < 2) {//如果是1号的凌晨2点，需要跑上月数据，防止遗漏
            LocalDate lastDay = LocalDate.now().minusDays(1);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            String lastMonth = lastDay.format(formatter);
            examMonthByMonth(lastMonth);
        }
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String thisMonth = today.format(formatter);
        examMonthByMonth(thisMonth);
    }

    public void examMonthByMonth(String thisMonth) {
        List<LearnScoreVO> vo = theoryArmsMapper.queryExamineMonth(
                thisMonth, learnScoreConfiguration.getExaminMonth().getId());
        Set<Long> hadScoreUser = redisTemplate.opsForSet().members(EXAMMONTHKEY + thisMonth);
        //缓存里没有不用再查积分中心，积分中心会自动过滤已经加过分的数据
        Map<Long, List<LearnScoreVO>> map = vo.stream().collect(Collectors.groupingBy(LearnScoreVO::getUserId,
                LinkedHashMap::new, Collectors.toList()));
        if (CollectionUtils.isEmpty(map)) {
            return;
        }
        List<IndexUserScoreEntity> dataList = new ArrayList();
        for (Map.Entry<Long, List<LearnScoreVO>> entry : map.entrySet()) {
            try {
                Long userId = entry.getKey();
                if (hadScoreUser.contains(userId)) {
                    continue;
                }
                //只对测试人员加积分
//                if(testUser.contains(userId)){
                Integer num = entry.getValue().get(0).getNum() == null ? 0 : entry.getValue().get(0).getNum();//第一次答对题数
                Double score = num * learnScoreConfiguration.getExaminMonth().getScore();
                String token = EXAMMONTHKEY + thisMonth + "_" + userId;
                String explain = learnScoreConfiguration.getExaminMonth().getExplain();
                String remark = learnScoreConfiguration.getExaminMonth().getRemark();
                sendScore(token, userId, score, explain, remark);
                Integer dataMonth = Integer.parseInt(thisMonth.substring(0, 4) + thisMonth.substring(5, 7));
                dataList.add(dssDorisScoreService.createUserScore(DssIndexScoreConstant.LEARNBYMONTH, userId, score.longValue(), dataMonth));
                log.debug("mt添加到缓存开始2");
                redisTemplate.opsForSet().add(EXAMMONTHKEY + thisMonth, userId);
                redisTemplate.expire(EXAMMONTHKEY + thisMonth, EXAMMONTHEXPIRE, TimeUnit.DAYS);
                log.debug("mt添加到缓存结束2");
//                }
            } catch (Exception e) {
                log.error(entry.getKey() + "每月一测加学习积分出错_" + entry.getValue() + e);
            }
        }
        dssDorisScoreService.batchInsertUserScore(dataList);
    }

    //每季一考 仅每季度第一次答题加积分，答对一次加0.5
    public void examQuart() {
        log.debug("每季一考开始");
        //如果是每个季度的第一月份的1号的2点，需要再跑上个季度的数据，防止数据遗漏 22-24点间的
        List<Integer> quartFirstMonth = Arrays.asList(1, 4, 7, 10);
        if (quartFirstMonth.contains(LocalDate.now().getMonthValue()) && LocalDateTime.now().getHour() < 2) {
            LocalDate lastDay = LocalDate.now().minusDays(1);
            examQuartByQuart(lastDay);
        }
        //查询当前季度
        examQuartByQuart(LocalDate.now());

    }


    public void examQuartByQuart(LocalDate date) {
        int month = date.getMonthValue();
        int quarter = month % 3 == 0 ? month / 3 : month / 3 + 1;
        Integer year = date.getYear();
        String redisKey = EXAMQUARTZKEY + quarter + "_" + year;
        Long examId = learnScoreConfiguration.getExaminQuart().getId();
        List<LearnScoreVO> vo = theoryArmsMapper.queryExamineQuart(quarter, year, examId);
        Map<Long, List<LearnScoreVO>> map = vo.stream().collect(Collectors.groupingBy(LearnScoreVO::getUserId,
                LinkedHashMap::new, Collectors.toList()));
        //查询redis中哪些人今日已经加过积分
        Set<Long> hadScoreUser = redisTemplate.opsForSet().members(redisKey);
        //缓存里没有不用再查数据库，积分中心会自动过滤已加分的数据
        if (CollectionUtils.isEmpty(map)) {
            return;
        }
        List<IndexUserScoreEntity> dataList = new ArrayList();
        for (Map.Entry<Long, List<LearnScoreVO>> entry : map.entrySet()) {
            try {
                Long userId = entry.getKey();
                if (hadScoreUser.contains(userId)) {
                    continue;
                }
                //只对测试人员加积分
//                if(testUser.contains(userId)){
                Integer num = entry.getValue().get(0).getNum();//第一次答对题数
                Double score = num * learnScoreConfiguration.getExaminQuart().getScore();
                String token = EXAMQUARTZKEY + "_" + year + "_" + quarter + "_" + userId;
                String explain = learnScoreConfiguration.getExaminQuart().getExplain();
                String remark = learnScoreConfiguration.getExaminQuart().getRemark();
                sendScore(token, userId, score, explain, remark);
                String monthStr = month > 9 ? "" + month : "0" + month;
                Integer dataMonth = Integer.parseInt(year + monthStr);
                dataList.add(dssDorisScoreService.createUserScore(DssIndexScoreConstant.LEARNBYQUARTZ, userId, score.longValue(), dataMonth));
                log.debug("mt添加到缓存开始3");
                redisTemplate.opsForSet().add(redisKey, userId);
                redisTemplate.expire(redisKey, EXAMQUARTZEXPIRE, TimeUnit.DAYS);
                log.debug("mt添加到缓存结束3");
//                }
            } catch (Exception e) {
                log.error(entry.getKey() + "每季一考加学习积分出错" + e);
            }
        }
        dssDorisScoreService.batchInsertUserScore(dataList);
    }
}
