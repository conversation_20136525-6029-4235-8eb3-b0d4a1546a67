package com.goodsogood.ows.service.pull;

import com.goodsogood.ows.configuration.PullDataConfig;
import com.goodsogood.ows.service.history.HistoryServices;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

/**
 * 拉取数据services
 */
@Service
@Log4j2
public class PullDataService {

    private final HistoryServices historyServices;
    private final PullDataConfig pullDataConfig;


    public PullDataService(HistoryServices historyServices,
                           PullDataConfig pullDataConfig) {
        this.historyServices = historyServices;
        this.pullDataConfig = pullDataConfig;
    }
    /**
     * 处理小程序数据拉取
     * @param tableName
     */
    public void handlerHistoryPullData(String tableName) throws Exception {
        if(!"all".equals(tableName)){
            historyServices.handlerTable(tableName);
        }else {
            pullDataConfig.getHistory().forEach(historyServices::handlerTable);
        }
    }
}
