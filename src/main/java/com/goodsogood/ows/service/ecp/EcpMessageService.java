package com.goodsogood.ows.service.ecp;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.MessageCommon;
import com.goodsogood.ows.common.MessageEnum;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.model.mongodb.ecp.HomeMessageForm;
import com.goodsogood.ows.model.mongodb.ecp.MessageImg;
import com.goodsogood.ows.model.mongodb.ecp.MessageVideo;
import com.goodsogood.ows.model.vo.MessageVO;
import com.goodsogood.ows.service.IMessageService;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 云区今日最新消息
 *
 * <AUTHOR>
 * @date 2021/10/12
 */
@Log4j2
@Component(MessageCommon.ECP)
public class EcpMessageService implements IMessageService {

    private final MyMongoTemplate myMongoTemplate;

    @Autowired
    public EcpMessageService(MyMongoTemplate myMongoTemplate) {
        this.myMongoTemplate = myMongoTemplate;
    }

    @NotNull
    @Override
    public List<MessageVO> generateMessage(long regionId, long rootId, @Nullable Integer page, @Nullable Integer pageSize) {
        if (null == page && null == pageSize) {
            page = 1;
            pageSize = 5;
        }

        List<MessageVO> messageVOS = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date start = calendar.getTime();//今日时间

        Criteria criteria = Criteria
                .where("checkStatus").is(Constants.CHECK_THREE)//是终审
                .and("regionId").is(regionId)
                .and("topStatus").ne(1) //不等与1
                .and("changeTime").gte(start);//大于等于当前时间

        Aggregation aggregation = Aggregation.newAggregation(//聚合
                Aggregation.match(criteria),
                Aggregation.project("checkStatus", "messagesId", "content",
                        "messageImg", "messageVideo", "changeTime"),
                Aggregation.sort(Sort.by(Sort.Order.desc("changeTime"))),
                Aggregation.skip(page),
                Aggregation.limit(pageSize));

        List<HomeMessageForm> formList = myMongoTemplate
                .aggregate(aggregation, HomeMessageForm.class).getMappedResults();

        if (!CollectionUtils.isEmpty(formList)) {//封装
            for (HomeMessageForm k : formList) {
                MessageVO vo = new MessageVO();
                if (!CollectionUtils.isEmpty(k.getMessageImg())) {
                    vo.setFile(k.getMessageImg()
                            .stream()
                            .map(MessageImg::getMessageImgUrl)
                            .collect(Collectors.toList()));
                }
                if (!CollectionUtils.isEmpty(k.getMessageVideo())) {
                    vo.setFile(k.getMessageVideo()
                            .stream()
                            .map(MessageVideo::getMessageVideoUrl)
                            .collect(Collectors.toList()));
                }
                vo.setText(k.getContent());
                vo.setType(MessageEnum.ECP.getType());
                vo.setTypeName(MessageEnum.ECP.getTypeName());
                messageVOS.add(vo);
            }
        }
        return messageVOS;
    }

}
