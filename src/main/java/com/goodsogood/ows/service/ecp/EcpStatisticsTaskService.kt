package com.goodsogood.ows.service.ecp

import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.mapper.ecp.EcpTaskMapper
import com.goodsogood.ows.mapper.meeting.experience.MeetingEvalMapper
import com.goodsogood.ows.model.vo.ecp.EcpStatisticsCityVO
import com.goodsogood.ows.model.vo.ecp.EcpStatisticsListVO
import com.goodsogood.ows.model.vo.ecp.EcpStatisticsUserListVO
import com.goodsogood.ows.model.vo.ecp.EcpStatisticsUserVO
import com.goodsogood.ows.service.user.OrgMongoService
import com.goodsogood.ows.service.user.UserMongoService
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.util.ObjectUtils
import java.text.DecimalFormat


/**
 * 云上任务参与情况统计
 *
 * <AUTHOR>
 * @createTime 2022-06-26
 */
@Service
class EcpStatisticsTaskService(
    @Autowired var meetingEvalMapper: MeetingEvalMapper,
    @Autowired var userMongoService: UserMongoService,
    @Autowired var ecpTaskMapper: EcpTaskMapper,
) {
    val log: Logger = LogManager.getLogger(OrgMongoService::class.java)

    /**
     * 全市系统: 云上任务参与情况
     */
    fun findCityTaskRate(header: HeaderHelper.SysHeader): EcpStatisticsListVO {
        var ecpStatisticsListVO = ecpTaskMapper.findEcpStatisticsListVO(header.regionId)
        if (ObjectUtils.isEmpty(ecpStatisticsListVO)) {
            ecpStatisticsListVO = EcpStatisticsListVO()
            ecpStatisticsListVO.partyNum = 0
            ecpStatisticsListVO.partyFinishNum = 0
            ecpStatisticsListVO.notPartyNum = 0
            ecpStatisticsListVO.notPartyFinish = 0
        }
        return ecpStatisticsListVO
    }

    /**
     * 本单位: 云上任务参与情况
     */
    fun findUnitTaskRate(header: HeaderHelper.SysHeader, unitId: Long): EcpStatisticsUserVO {
        //本单位全体员工数
        val count1 = userMongoService.getUserIdList(header.regionId, unitId, 2).size
        //本单位党员数
        val count2 = userMongoService.getUserIdList(header.regionId, unitId, 1).size
        var ecpStatisticsUserVO = ecpTaskMapper.findEcpStatisticsUserVO(header.regionId, unitId, count1, count2)
        if (ObjectUtils.isEmpty(ecpStatisticsUserVO)) {
            ecpStatisticsUserVO = EcpStatisticsUserVO()
            ecpStatisticsUserVO.staffRate = "0.0%"
            ecpStatisticsUserVO.partyRate = "0.0%"
            ecpStatisticsUserVO.peopleNum = 0
            ecpStatisticsUserVO.partyPeopleNum = 0
            ecpStatisticsUserVO.notPartyPeopleNum = 0
        }
        return ecpStatisticsUserVO
    }

    /**
     * 全市系统: 云上任务参与情况列表
     */
    fun findCityTaskList(
        header: HeaderHelper.SysHeader,
        page: Int,
        pageSize: Int,
        type: Int?,
        sort: Int?,
    ): Page<MutableList<EcpStatisticsCityVO>> {
//        val count = tbcAllUserCount(header.regionId)
        val ecpList = ecpTaskMapper.findEcpStatisticsCityList(header.regionId, type, sort)
        // 获取单位的员工总数map1
        val map1 = findUnitUserNum(header.regionId, ecpList.map { it.ownerId!! }.toMutableList(), 2)
        // 获取单位的党员总数map2
        val map2 = findUnitUserNum(header.regionId, ecpList.map { it.ownerId!! }.toMutableList(), 1)
        val df = DecimalFormat("#.#%")
        return PageHelper.startPage<Any>(page, pageSize).doSelectPage {
            val list = ecpTaskMapper.findEcpStatisticsCityList(header.regionId, type, sort)
            if (list.isNotEmpty()) {
                list.forEach {
                    it.staffRate = df.format(it.staff?.toDouble()?.div(map1[it.ownerId]!!.toDouble()) ?: 0.0)
                    it.partyRate = df.format(it.party?.toDouble()?.div(map2[it.ownerId]!!.toDouble()) ?: 0.0)
                    it.staff = null
                    it.party = null
                }
            }
        }
    }

    /**
     * 本单位: 云上任务参与情况人员列表
     */
    fun findUnitTaskUserList(
        header: HeaderHelper.SysHeader,
        unitId: Long,
        type: Int?,
        page: Int,
        pageSize: Int,
    ): Page<MutableList<EcpStatisticsUserListVO>> {
        return PageHelper.startPage<Any>(page, pageSize).doSelectPage {
            ecpTaskMapper.findEcpStatisticsUserList(header.regionId, unitId, type)
        }
    }


    /**
     * 根据单位ids 批量查询人员信息
     * @param   regionId    区域ID
     * @param   tree        tree    组织树 1-单位树， 2-党组织树
     * @param   unitId      单位ID   如果需要查询全系统人员 该字段传null
     * @param   isParty     1 - 党员，   2 - 全体
     * key : unitId
     * value: userNum
     */
    fun findUnitUserNum(regionId: Long, unitId: MutableList<Long>, isParty: Int): MutableMap<Long, Int> {
        val map = mutableMapOf<Long, Int>()
        if (unitId.isNotEmpty()) {
            unitId.forEach {
                val usersNum = userMongoService.getUserIdList(regionId, it, isParty).size
                map[it] = usersNum
            }
        }
        return map
    }

    /**
     * 获取全市党员
     * type:1 全市党员
     * type:2 本单位党员数
     */
    fun tbcAllUserCount(regionId: Long): Int {
        return meetingEvalMapper.selectRankUserCount(regionId)
    }

    /**
     * 云上任务总-大屏用
     */
    fun tbcAllEcpTaskCount(): Long {
        return  ecpTaskMapper.totalEcpTask()?:0L
    }
}