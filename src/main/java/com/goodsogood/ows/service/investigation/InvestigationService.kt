package com.goodsogood.ows.service.investigation

import com.github.pagehelper.ISelect
import com.github.pagehelper.PageHelper
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.mapper.count.SurveyStatisticsMapper
import com.goodsogood.ows.model.vo.count.SurveyStatisticVO
import lombok.extern.slf4j.Slf4j
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
@Slf4j
class InvestigationService(
    @Autowired val errors: Errors,
    @Autowired val surveyStatisticsMapper: SurveyStatisticsMapper
) {

    private val log = LoggerFactory.getLogger(InvestigationService::class.java)

    fun surveyStatistics(year: Int?, leader: String?, department: String?, interview: Int?): Any {
//        return PageHelper.startPage<List<SurveyStatisticVO>>(page, pageSize)
//            .doSelectPage<MutableList<SurveyStatisticVO>> {
//                surveyStatisticsMapper.getSurveyStatistics(leader, department, mode)
//            }
        val list = surveyStatisticsMapper.getSurveyStatistics(leader, department, interview, year)
        list.let {
            for (vo in it) {
                val interviewSet= vo.interviewConcat?.split(",")?.toSet()
                vo.interview =  interviewSet?.joinToString(separator = ",")
            }
        }
        return list

    }
}