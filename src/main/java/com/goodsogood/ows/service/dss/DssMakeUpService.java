package com.goodsogood.ows.service.dss;

import com.aidangqun.log4j2cm.aop.HttpLogAspect;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.RateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 辅助决策补录数据服务
 * <AUTHOR>
 */
@Service
@Slf4j
public class DssMakeUpService {

	private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

	@Value("${rabbit-value.dss-user.queue}")
	private String userQueue;

	@Value("${rabbit-value.dss-branch.queue}")
	private String branchQueue;

	@Value("${rabbit-value.dss-committee.queue}")
	private String committeeQueue;

	private final SimpleApplicationConfigHelper applicationConfigHelper;
	private final OrgService orgService;
	private final UserService userService;
	private final DssAsyncService asyncService;
	private final DssBaseService dssBaseService;
	private final RabbitTemplate rabbitTemplate;

	public DssMakeUpService(SimpleApplicationConfigHelper applicationConfigHelper,
							OrgService orgService,
							UserService userService,
							DssAsyncService asyncService,
							DssBaseService dssBaseService,
							RabbitTemplate rabbitTemplate) {
		this.applicationConfigHelper = applicationConfigHelper;
		this.orgService = orgService;
		this.userService = userService;
		this.asyncService = asyncService;
		this.dssBaseService = dssBaseService;
		this.rabbitTemplate = rabbitTemplate;
	}

	/**
	 * @param dataType
	 *              2 - 党委
	 *              3 - 党支部
	 *              4 - 用户
	 */
	public void makeUpDssData(Integer year, Long regionId, Integer dataType) {
		switch (dataType) {
			case 2 :
				this.makeUpCommittee(year, regionId);
				break;
			case 3 :
				this.makeUpBranch(year, regionId);
				break;
			case 4 :
				this.makeUpUser(year, regionId);
				break;
			default:
				log.error("参入传入错误.... -> dataType : [{}]", dataType);
				break;
		}
	}

	private void makeUpCommittee(Integer year, Long regionId) {
		LogAspectHelper.SSLog ssLog = HttpLogAspect.getSSLog();
		LocalDateTime dateTime = LocalDateTime.now();
		String today = DATE_FORMAT.format(dateTime);
		List<PartyCommitteeInfo> committeeInfoList;
		List<PartyCommitteeInfo> deleteCommitteeInfoList;
		int pageSize = 1;
		if (year.equals(dateTime.getYear())) {
			// 查询下级党委
			List<OrganizationEntity> mysqlOrgList = this.orgService.getCommunistAndGeneral(regionId, null);
			List<Long> orgId1 = mysqlOrgList.stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toList());
			Map<String, Object> resultMap = new HashMap<>(2);
			resultMap.put("year", year);
			resultMap.put("regionId", regionId);
			List<PartyCommitteeInfo> mongoOrgList = this.dssBaseService.findList(PartyCommitteeInfo.class, resultMap);
			List<Long> orgId2 = mongoOrgList.stream().map(PartyCommitteeInfo::getOrganizationId).collect(Collectors.toList());
			List<Long> makeUpOrgIds = orgId1.stream().filter(orgId -> !orgId2.contains(orgId)).collect(Collectors.toList());
			List<OrganizationEntity> makeUpOrg = mysqlOrgList.stream().filter(org -> makeUpOrgIds.contains(org.getOrganizationId())).collect(Collectors.toList());
			log.debug("党委[{}]年需要补录的数据 -> [{}]", year, makeUpOrg.stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toList()));
			committeeInfoList = makeUpOrg.stream()
					.map(org -> this.asyncService.getCommitteeInfo(year, regionId, org.getOrganizationId(),
							org.getName(), org.getShortName(), org.getOrgLevel(), org.getOrgTypeChild(), today, dateTime, ssLog))
					.collect(Collectors.toList());
			// 查询反向对比需要删除的数据
			deleteCommitteeInfoList = mongoOrgList.stream().filter(org -> !orgId1.contains(org.getOrganizationId())).collect(Collectors.toList());
			mysqlOrgList.clear();
			mongoOrgList.clear();
		} else {
			// 查询下级党委
			List<OrgSnapshotEntity> mysqlOrgList = this.orgService.getHistoryCommunistAndGeneral(regionId, year, null);
			List<Long> orgId1 = mysqlOrgList.stream().map(OrgSnapshotEntity::getOrgId).collect(Collectors.toList());
			Map<String, Object> resultMap = new HashMap<>(2);
			resultMap.put("year", year);
			resultMap.put("regionId", regionId);
			List<PartyCommitteeInfo> mongoOrgList = this.dssBaseService.findList(PartyCommitteeInfo.class, resultMap);
			List<Long> orgId2 = mongoOrgList.stream().map(PartyCommitteeInfo::getOrganizationId).collect(Collectors.toList());
			List<Long> makeUpOrgIds = orgId1.stream().filter(orgId -> !orgId2.contains(orgId)).collect(Collectors.toList());
			List<OrgSnapshotEntity> makeUpOrg = mysqlOrgList.stream().filter(org -> makeUpOrgIds.contains(org.getOrgId())).collect(Collectors.toList());
			log.debug("党委[{}]年需要补录的数据 -> [{}]", year, makeUpOrg.stream().map(OrgSnapshotEntity::getOrgId).collect(Collectors.toList()));
			committeeInfoList = makeUpOrg.stream()
					.map(org -> this.asyncService.getCommitteeInfo(year, regionId, org.getOrgId(),
							org.getOrgName(), org.getOrgShortName(), org.getOrgLevel(), org.getOrgTypeChild(), today, dateTime, ssLog))
					.collect(Collectors.toList());
			// 查询反向对比需要删除的数据
			deleteCommitteeInfoList = mongoOrgList.stream().filter(org -> !orgId1.contains(org.getOrganizationId())).collect(Collectors.toList());
			mysqlOrgList.clear();
			mongoOrgList.clear();
		}
		RateUtils.build(committeeInfoList.size(), ssLog.getTrackerId());
		for(int i = 1; i <= ((committeeInfoList.size() + pageSize -1) / pageSize); i++) {
			List<PartyCommitteeInfo> committeeInfos = committeeInfoList.stream().skip((i - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
			this.rabbitTemplate.convertAndSend(committeeQueue, committeeInfos);
		}
		log.debug("党委[{}]年需要删除的数据 -> [{}]", year, deleteCommitteeInfoList.stream().map(PartyCommitteeInfo::getOrganizationId).collect(Collectors.toList()));
		Map<String, Object> queryMap = new HashMap<>();
		deleteCommitteeInfoList.forEach(info -> {
			queryMap.clear();
			queryMap.put("organizationId", info.getOrganizationId());
			queryMap.put("year", info.getYear());
			this.dssBaseService.delete(PartyCommitteeInfo.class, queryMap);
		});
	}

	private void makeUpBranch(Integer year, Long regionId) {
		LogAspectHelper.SSLog ssLog = HttpLogAspect.getSSLog();
		String trackerId = ssLog.getTrackerId();
		LocalDateTime dateTime = LocalDateTime.now();
		String today = DATE_FORMAT.format(dateTime);
		int pageSize = 1;
		// 查询下级党支部
		Region.OrgData orgData = applicationConfigHelper.getOrgByRegionId(regionId);
		List<PartyBranchInfo> branchInfoList;
		List<PartyBranchInfo> deleteBranchInfoList;
		if (year.equals(dateTime.getYear())) {
			List<OrganizationEntity> mysqlOrgList = this.orgService.getPartyBranch(orgData.getOrgId(), null, true);
			List<Long> orgId1 = mysqlOrgList.stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toList());
			Map<String, Object> resultMap = new HashMap<>(2);
			resultMap.put("year", year);
			resultMap.put("regionId", regionId);
			List<PartyBranchInfo> mongoOrgList = this.dssBaseService.findList(PartyBranchInfo.class, resultMap);
			List<Long> orgId2 = mongoOrgList.stream().map(PartyBranchInfo::getOrganizationId).collect(Collectors.toList());
			List<Long> makeUpOrgIds = orgId1.stream().filter(orgId -> !orgId2.contains(orgId)).collect(Collectors.toList());
			List<OrganizationEntity> makeUpOrg = mysqlOrgList.stream().filter(org -> makeUpOrgIds.contains(org.getOrganizationId())).collect(Collectors.toList());
			log.debug("党支部[{}]年需要补录的数据 -> [{}]", year, makeUpOrg.stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toList()));
			branchInfoList = makeUpOrg.stream()
					.map(organization ->
							this.asyncService.getBranchInfo(year, regionId, organization.getOrganizationId(),
									organization.getName(), organization.getShortName(), organization.getOrgLevel(), organization.getOrgTypeChild(),
									today, dateTime, ssLog))
					.collect(Collectors.toList());
			// 查询反向对比需要删除的数据
			deleteBranchInfoList = mongoOrgList.stream().filter(org -> !orgId1.contains(org.getOrganizationId())).collect(Collectors.toList());
			mysqlOrgList.clear();
			mongoOrgList.clear();
		} else {
			List<OrgSnapshotEntity> mysqlOrgList = this.orgService.getHistoryPartyBranch(orgData.getOrgId(), year, true, null);
			List<Long> orgId1 = mysqlOrgList.stream().map(OrgSnapshotEntity::getOrgId).collect(Collectors.toList());
			Map<String, Object> resultMap = new HashMap<>(2);
			resultMap.put("year", year);
			resultMap.put("regionId", regionId);
			List<PartyBranchInfo> mongoOrgList = this.dssBaseService.findList(PartyBranchInfo.class, resultMap);
			List<Long> orgId2 = mongoOrgList.stream().map(PartyBranchInfo::getOrganizationId).collect(Collectors.toList());
			List<Long> makeUpOrgIds = orgId1.stream().filter(orgId -> !orgId2.contains(orgId)).collect(Collectors.toList());
			List<OrgSnapshotEntity> makeUpOrg = mysqlOrgList.stream().filter(org -> makeUpOrgIds.contains(org.getOrgId())).collect(Collectors.toList());
			log.debug("党支部[{}]年需要补录的数据 -> [{}]", year, makeUpOrg.stream().map(OrgSnapshotEntity::getOrgId).collect(Collectors.toList()));
			branchInfoList = makeUpOrg.stream()
					.map(snapshotEntity ->
							this.asyncService.getBranchInfo(year, regionId, snapshotEntity.getOrgId(),
									snapshotEntity.getOrgName(), snapshotEntity.getOrgShortName(), snapshotEntity.getOrgLevel(), snapshotEntity.getOrgTypeChild(),
									today, dateTime, ssLog))
					.collect(Collectors.toList());
			// 查询反向对比需要删除的数据
			deleteBranchInfoList = mongoOrgList.stream().filter(org -> !orgId1.contains(org.getOrganizationId())).collect(Collectors.toList());
			mysqlOrgList.clear();
			mongoOrgList.clear();
		}
		RateUtils.build(branchInfoList.size(), trackerId);
		for(int i = 1; i <= ((branchInfoList.size() + pageSize -1) / pageSize); i++) {
			List<PartyBranchInfo> branchInfos = branchInfoList.stream().skip((i - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
			this.rabbitTemplate.convertAndSend(branchQueue, branchInfos);
		}
		log.debug("党支部[{}]年需要删除的数据 -> [{}]", year, deleteBranchInfoList.stream().map(PartyBranchInfo::getOrganizationId).collect(Collectors.toList()));
		Map<String, Object> queryMap = new HashMap<>();
		deleteBranchInfoList.forEach(info -> {
			queryMap.clear();
			queryMap.put("organizationId", info.getOrganizationId());
			queryMap.put("year", info.getYear());
			this.dssBaseService.delete(PartyBranchInfo.class, queryMap);
		});
	}

	private void makeUpUser(Integer year, Long regionId) {
		LogAspectHelper.SSLog ssLog = HttpLogAspect.getSSLog();
		String trackerId = ssLog.getTrackerId();
		LocalDateTime dateTime = LocalDateTime.now();
		String today = DATE_FORMAT.format(dateTime);
		int pageSize = 100;
		// 查询下级党委
		Region.OrgData orgData = applicationConfigHelper.getOrgByRegionId(regionId);
		List<UserInfo> userInfoList;
		List<UserInfo> deleteUserInfoList;
		if (year.equals(dateTime.getYear())) {
			List<UserEntity> mysqlUserList = this.userService.getUserByOrg(orgData.getOrgId());
			List<Long> userList1 = mysqlUserList.stream().map(UserEntity::getUserId).collect(Collectors.toList());
			Map<String, Object> resultMap = new HashMap<>(2);
			resultMap.put("year", year);
			resultMap.put("regionId", regionId);
			List<UserInfo> mongoUserList = this.dssBaseService.findList(UserInfo.class, resultMap);
			List<Long> userList2 = mongoUserList.stream().map(UserInfo::getUserId).collect(Collectors.toList());
			List<Long> makeUpList = userList1.stream().filter(userId -> !userList2.contains(userId)).collect(Collectors.toList());
			List<UserEntity> makeUpUser = mysqlUserList.stream().filter(user -> makeUpList.contains(user.getUserId())).collect(Collectors.toList());
			log.debug("党员[{}]年需要补录的数据 -> [{}]", year, makeUpUser.stream().map(UserEntity::getUserId).collect(Collectors.toList()));
			userInfoList = makeUpUser.stream()
					.map(user -> this.asyncService.getUserInfo(user.getUserId(), user.getName(),
							user.getPoliticalType(), user.getGender(), user.getAge(),
							user.getPosition(), user.getJoiningTime(), user.getEducation(), user.getBirthPlace(),
							user.getPhoneSecret(), year, dateTime, regionId, today, ssLog))
					.collect(Collectors.toList());
			// 查询反向对比需要删除的数据
			deleteUserInfoList = mongoUserList.stream().filter(user -> !userList1.contains(user.getUserId())).collect(Collectors.toList());
			mysqlUserList.clear();
			mongoUserList.clear();
		} else {
			List<UserSnapshotEntity> mysqlUserList = this.userService.getHistoryUserList(regionId, year);
			List<Long> userList1 = mysqlUserList.stream().map(UserSnapshotEntity::getUserId).collect(Collectors.toList());
			Map<String, Object> resultMap = new HashMap<>(2);
			resultMap.put("year", year);
			resultMap.put("regionId", regionId);
			List<UserInfo> mongoUserList = this.dssBaseService.findList(UserInfo.class, resultMap);
			List<Long> userList2 = mongoUserList.stream().map(UserInfo::getUserId).collect(Collectors.toList());
			List<Long> makeUpList = userList1.stream().filter(userId -> !userList2.contains(userId)).collect(Collectors.toList());
			List<UserSnapshotEntity> makeUpUser = mysqlUserList.stream().filter(user -> makeUpList.contains(user.getUserId())).collect(Collectors.toList());
			log.debug("党员[{}]年需要补录的数据 -> [{}]", year, makeUpUser.stream().map(UserSnapshotEntity::getUserId).collect(Collectors.toList()));
			userInfoList = makeUpUser.stream()
					.map(user -> this.asyncService.getUserInfo(user.getUserId(), user.getUserName(),
							user.getPoliticalType(), user.getGender(), user.getAge(),
							user.getPosition(), user.getProPartyTime(), user.getEducation(), user.getBirthplace(),
							user.getPhoneSecret(), year, dateTime, regionId, today, ssLog))
					.collect(Collectors.toList());
			// 查询反向对比需要删除的数据
			deleteUserInfoList = mongoUserList.stream().filter(user -> !userList1.contains(user.getUserId())).collect(Collectors.toList());
			mysqlUserList.clear();
			mongoUserList.clear();
		}
		RateUtils.build(Math.toIntExact(userInfoList.size()), trackerId);
		for(int i = 1; i <= ((userInfoList.size() + pageSize -1) / pageSize); i++) {
			List<UserInfo> userInfos = userInfoList.stream().skip((i - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
			this.rabbitTemplate.convertAndSend(userQueue, userInfos);
		}
		log.debug("党员[{}]年需要删除的数据 -> [{}]", year, deleteUserInfoList.stream().map(UserInfo::getUserId).collect(Collectors.toList()));
		Map<String, Object> queryMap = new HashMap<>();
		deleteUserInfoList.forEach(info -> {
			queryMap.clear();
			queryMap.put("userId", info.getUserId());
			queryMap.put("year", info.getYear());
			this.dssBaseService.delete(UserInfo.class, queryMap);
		});
	}
}
