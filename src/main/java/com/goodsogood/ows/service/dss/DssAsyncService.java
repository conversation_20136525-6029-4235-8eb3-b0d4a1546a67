package com.goodsogood.ows.service.dss;

import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.mapper.meeting.UserCommendPenalizeMapper;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.model.mongodb.dss.*;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.sas.PartyOrgVo;
import com.goodsogood.ows.service.impl.DssPartyBranchBuilder;
import com.goodsogood.ows.service.impl.DssPartyCommitteeBuilder;
import com.goodsogood.ows.service.impl.DssUserBuilder;
import com.goodsogood.ows.service.user.OptionService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.FileUtil;
import com.goodsogood.ows.utils.RateUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 异步服务
 *
 * <AUTHOR>
 */
@Service
@Log4j2
public class DssAsyncService {

    // 默认为24小时
    @Value("${dss.countdown}")
    private Long countdown = 86400000L;

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATE_FORMAT_2 = DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss");
    private static final DateTimeFormatter DATE_FORMAT_D = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

    private final List<DssPartyCommitteeBuilder> commitBuilderList;
    private final List<DssPartyBranchBuilder> branchBuilderList;
    private final List<DssUserBuilder> userBuilderList;
    private final UserService userService;
    private final OptionService optionService;
    private final UserCommendPenalizeMapper userCommendPenalizeMapper;
    private final StringRedisTemplate redisTemplate;
    private final DssBaseService dssBaseService;
    private final OrgService orgService;
    private final DssBufferTriggerService bufferTriggerService;

    @Autowired
    public DssAsyncService(List<DssPartyCommitteeBuilder> commitBuilderList,
                           List<DssPartyBranchBuilder> branchBuilderList,
                           List<DssUserBuilder> userBuilderList,
                           UserService userService,
                           OptionService optionService,
                           UserCommendPenalizeMapper userCommendPenalizeMapper,
                           StringRedisTemplate redisTemplate,
                           DssBaseService dssBaseService,
                           OrgService orgService,
                           DssBufferTriggerService bufferTriggerService) {
        this.commitBuilderList = commitBuilderList;
        this.branchBuilderList = branchBuilderList;
        this.userBuilderList = userBuilderList;
        this.userService = userService;
        this.optionService = optionService;
        this.userCommendPenalizeMapper = userCommendPenalizeMapper;
        this.redisTemplate = redisTemplate;
        this.dssBaseService = dssBaseService;
        this.orgService = orgService;
        this.bufferTriggerService = bufferTriggerService;
    }

    /**
     * 生层党委数据
     *
     * @param year     年份
     * @param regionId 区域ID
     * @param orgId    组织ID
     * @param orgLevel 组织架构
     * @param today    今天日期
     * @param dateTime 日期
     * @param uuid     uuid
     */
    @Async("dssCommitteeExecutor")
    public void saveCommitteeInfo(Integer year, Long regionId, Long orgId, String name,
                                  String shortName, String orgLevel, Integer orgTypeChild, String today,
                                  LocalDateTime dateTime, String uuid, LogAspectHelper.SSLog ssLog) {
        // 在进入异步方法时，重新设置下上下文
        LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
        helper.reSetContext(ssLog);
        PartyCommitteeInfo committeeInfo = this.getCommitteeInfo(year, regionId, orgId, name, shortName, orgLevel,
                orgTypeChild, today, dateTime, ssLog);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        for (DssPartyCommitteeBuilder committeeBuilder : this.commitBuilderList) {
            try {
                committeeInfo = committeeBuilder.buildPartyCommittee(committeeInfo);
                log.debug("[DSS单个党委][{}], 模块[{}]调用完成, 耗时 -> [{}]", orgId, committeeBuilder, stopWatch.toString());
            } catch (Exception e) {
                log.debug("[DSS单个党委报错]单个组织[{}] 年份 [{}] -> 调用[{}]报错", orgId, year, committeeBuilder);
                log.error("党委生成数据报错 -> ", e);
                //this.addErrorList(committeeInfo.getOrganizationId(), 1);
            }
        }
        // 封装组织列表数据
        List<DssOrgChild> dssOrgChild = this.dssBaseService.addOrgListInfo(committeeInfo.getOrgList(), year, true);
        committeeInfo.setOrgList(dssOrgChild);
        List<GrandMap> grandMapList = this.dssBaseService.addOrgList(committeeInfo.getGrandMap(), year);
        committeeInfo.setGrandMap(grandMapList);
        log.debug("[DSS单个党委][{}]重新封装组织列表数据完成, 耗时 -> [{}]", orgId, stopWatch.toString());
        this.bufferTriggerService.addBuffer(committeeInfo);
        log.debug("[DSS单个党委][{}]数据保存调用完成, 耗时 -> [{}]", orgId, stopWatch.toString());
        stopWatch.stop();
        //进度自增
        if (StringUtils.isNotBlank(uuid)) {
            RateUtils.auto(uuid);
        }
    }

    /**
     * 生层党委数据
     *
     * @param committeeInfo
     */
    public void saveCommitteeInfo(PartyCommitteeInfo committeeInfo, String trackerId) {
        Long orgId = committeeInfo.getOrganizationId();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Integer year = committeeInfo.getYear();
        for (DssPartyCommitteeBuilder committeeBuilder : this.commitBuilderList) {
            try {
                committeeInfo = committeeBuilder.buildPartyCommittee(committeeInfo);
                log.debug("[DSS单个党委][{}], 模块[{}]调用完成, 耗时 -> [{}]", orgId, committeeBuilder, stopWatch.toString());
            } catch (Exception e) {
                log.debug("[DSS单个党委报错]单个组织[{}] 年份 [{}] -> 调用[{}]报错", orgId, year, committeeBuilder);
                log.error("党委生成数据报错 -> ", e);
                if (StringUtils.isNotBlank(trackerId)) {
                    RateUtils.auto(trackerId);
                }
                throw e;
                //this.addErrorList(committeeInfo.getOrganizationId(), 1);
            }
        }
        // 封装组织列表数据
        List<DssOrgChild> dssOrgChild = this.dssBaseService.addOrgListInfo(committeeInfo.getOrgList(), year, true);
        committeeInfo.setOrgList(dssOrgChild);
        List<GrandMap> grandMapList = this.dssBaseService.addOrgList(committeeInfo.getGrandMap(), year);
        committeeInfo.setGrandMap(grandMapList);
        log.debug("[DSS单个党委][{}], 重新封装组织列表数据完成, 耗时 -> [{}]", orgId, stopWatch.toString());
        this.bufferTriggerService.addBuffer(committeeInfo);
        log.debug("[DSS单个党委][{}], 数据保存调用完成, 耗时 -> [{}]", orgId, stopWatch.toString());
        stopWatch.stop();
        this.dssBaseService.rateAndDing(trackerId, String.format("辅助决策党委[%s]数据生成完毕", year), null);
    }


    /**
     * 党支部保存数据
     *
     * @param year     年份
     * @param regionId 区域ID
     * @param orgId    组织ID
     * @param orgLevel 组织架构
     * @param today    今天日期
     * @param dateTime 日期
     */
    @Async("dssBranchExecutor")
    public void saveBranchInfo(Integer year, Long regionId, Long orgId, String name,
                               String shortName, String orgLevel, Integer orgTypeChild, String today,
                               LocalDateTime dateTime, String uuid, LogAspectHelper.SSLog ssLog) {
        PartyBranchInfo branchInfo = this.getBranchInfo(year, regionId, orgId, name, shortName, orgLevel,
                orgTypeChild, today, dateTime, ssLog);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        for (DssPartyBranchBuilder branchBuilder : this.branchBuilderList) {
            try {
                branchInfo = branchBuilder.buildPartyBranch(branchInfo);
                log.debug("[DSS单个党支部][{}], 模块[{}]调用完成, 耗时 -> [{}]", orgId, branchBuilder, stopWatch.toString());
            } catch (Exception e) {
                log.debug("[DSS单个党支部报错]单个组织[{}] 年份 [{}] -> 调用[{}]报错", orgId, year, branchBuilder);
                log.error("党支部生成数据报错 -> ", e);
                //this.addErrorList(branchInfo.getOrganizationId(), 2);
            }
        }
        // 封装人员列表数据
        List<UserGrandMap> userGrandMaps = this.dssBaseService.addUserList(branchInfo.getGrandMap(), year, regionId);
        branchInfo.setGrandMap(userGrandMaps);
        List<DssUserChild> dssUserChildren = this.dssBaseService.addUserListInfo(branchInfo.getUserList(), year, regionId);
        branchInfo.setUserList(dssUserChildren);
        log.debug("[DSS单个党支部][{}]重新封装组织列表数据完成, 耗时 -> [{}]", orgId, stopWatch.toString());
        this.bufferTriggerService.addBuffer(branchInfo);
        log.debug("[DSS单个党支部][{}]数据保存调用完成, 耗时 -> [{}]", orgId, stopWatch.toString());
        stopWatch.stop();
        //进度自增
        if (StringUtils.isNotBlank(uuid)) {
            RateUtils.auto(uuid);
        }
    }

    /**
     * 党支部保存数据
     *
     * @param branchInfo
     */
    public void saveBranchInfo(PartyBranchInfo branchInfo, String trackerId) {
        Long orgId = branchInfo.getOrganizationId();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Integer year = branchInfo.getYear();
        for (DssPartyBranchBuilder branchBuilder : this.branchBuilderList) {
            try {
                branchInfo = branchBuilder.buildPartyBranch(branchInfo);
                log.debug("[DSS单个党支部][{}], 模块[{}]调用完成, 耗时 -> [{}]", orgId, branchBuilder, stopWatch.toString());
            } catch (Exception e) {
                log.debug("[DSS单个党支部报错]单个组织[{}] 年份 [{}] -> 调用[{}]报错", orgId, year, branchBuilder);
                log.error("党支部生成数据报错 -> ", e);
                if (StringUtils.isNotBlank(trackerId)) {
                    RateUtils.auto(trackerId);
                }
                throw e;
                //this.addErrorList(orgId, 2);
            }
        }
        // 封装人员列表数据
        List<UserGrandMap> userGrandMaps = this.dssBaseService.addUserList(branchInfo.getGrandMap(), year, branchInfo.getRegionId());
        branchInfo.setGrandMap(userGrandMaps);
        List<DssUserChild> dssUserChildren = this.dssBaseService.addUserListInfo(branchInfo.getUserList(), year, branchInfo.getRegionId());
        branchInfo.setUserList(dssUserChildren);
        log.debug("[DSS单个党支部][{}], 重新封装组织列表数据完成, 耗时 -> [{}]", orgId, stopWatch.toString());
        //保存数据
        this.bufferTriggerService.addBuffer(branchInfo);
        log.debug("[DSS单个党支部][{}], 数据保存调用完成, 耗时 -> [{}]", orgId, stopWatch.toString());
        stopWatch.stop();
        this.dssBaseService.rateAndDing(trackerId, String.format("辅助决策党支部[%s]数据生成完毕", year), null);
    }

    @Async("dssUserExecutor")
    public void saveUserInfo(Long userId, String name, Integer politicalType, Integer gender, Integer age,
                             String position, String joiningTime, Integer education, String birPlace,
                             String phoneSecret, Integer year, LocalDateTime dateTime, Long regionId, String today, String uuid,
                             LogAspectHelper.SSLog ssLog) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        UserInfo userInfo = this.getUserInfo(userId, name, politicalType, gender, age, position, joiningTime, education,
                birPlace, phoneSecret, year, dateTime, regionId, today, ssLog);
        log.debug("[DSS单个用户][{}], 封装基础数据完成, 耗时 -> [{}]", userId, stopWatch.toString());
        for (DssUserBuilder userBuilder : this.userBuilderList) {
            try {
                userInfo = userBuilder.buildUser(userInfo);
                log.debug("[DSS单个用户][{}], 模块[{}]调用完成, 耗时 -> [{}]", userId, userBuilder, stopWatch.toString());
            } catch (Exception e) {
                log.debug("[DSS单个用户报错]单个用户[{}] 年份 [{}] -> 调用[{}]报错", userId, year, userBuilder);
                log.error("党员生成数据报错 -> ", e);
                //this.addErrorList(userInfo.getUserId(), 3);
            }
        }
        // 保存数据
        this.bufferTriggerService.addBuffer(userInfo);
        log.debug("[DSS单个用户][{}], 数据保存调用完成, 耗时 -> [{}]", userId, stopWatch.toString());
        stopWatch.stop();
        //进度自增
        if (StringUtils.isNotBlank(uuid)) {
            RateUtils.auto(uuid);
        }
    }

    public void saveUserInfo(UserInfo userInfo, String trackerId) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Integer year = userInfo.getYear();
        for (DssUserBuilder userBuilder : this.userBuilderList) {
            try {
                userInfo = userBuilder.buildUser(userInfo);
                log.debug("[DSS单个用户][{}], 模块[{}]调用完成, 耗时 -> [{}]", userInfo.getUserId(), userBuilder, stopWatch.toString());
                log.debug("[DSS单个用户][{}], 模块[{}]调用完成, 耗时 -> [{}]", userInfo.getUserId(), userBuilder, stopWatch.toString());
            } catch (Exception e) {
                log.debug("[DSS单个用户报错]单个用户[{}] 年份 [{}] -> 调用[{}]报错", userInfo.getUserId(), year, userBuilder);
                log.error("党员生成数据报错 -> ", e);
                if (StringUtils.isNotBlank(trackerId)) {
                    RateUtils.auto(trackerId);
                }
                throw e;
                //this.addErrorList(userInfo.getUserId(), 3);
            }
        }
        // 保存数据
        this.bufferTriggerService.addBuffer(userInfo);
        log.debug("[DSS单个用户][{}], 数据保存调用完成, 耗时 -> [{}]", userInfo.getUserId(), stopWatch.toString());
        this.dssBaseService.rateAndDing(trackerId, String.format("辅助决策数据党员[%s]年的数据完成", year), null);
    }

    /**
     * 生成辅助决策党委基础类
     *
     * @param year
     * @param regionId
     * @param orgId
     * @param name
     * @param orgLevel
     * @param orgTypeChild
     * @param today
     * @param dateTime
     * @return
     */
    public PartyCommitteeInfo getCommitteeInfo(Integer year, Long regionId, Long orgId, String name,
                                               String shortName, String orgLevel, Integer orgTypeChild,
                                               String today, LocalDateTime dateTime, LogAspectHelper.SSLog ssLog) {
        PartyCommitteeInfo committeeInfo = new PartyCommitteeInfo();
        committeeInfo.setYear(year);
        committeeInfo.setRegionId(regionId);
        committeeInfo.setOrganizationId(orgId);
        committeeInfo.setOrgName(name);
        committeeInfo.setOrgShortName(shortName);
        committeeInfo.setOrgLevel(orgLevel);
        committeeInfo.setUpdateTime(today);
        committeeInfo.setCreateTime(DateUtils.asDate(dateTime));
        committeeInfo.setOrgTypeChild(orgTypeChild);
        committeeInfo.setSsLog(ssLog);
        return committeeInfo;
    }

    /**
     * 生成辅助决策党支部基础类
     *
     * @param year
     * @param regionId
     * @param orgId
     * @param name
     * @param shortName
     * @param orgLevel
     * @param today
     * @param dateTime
     * @return
     */
    public PartyBranchInfo getBranchInfo(Integer year, Long regionId, Long orgId, String name,
                                         String shortName, String orgLevel, Integer orgTypeChild,
                                         String today, LocalDateTime dateTime, LogAspectHelper.SSLog ssLog) {
        PartyBranchInfo branchInfo = new PartyBranchInfo();
        branchInfo.setYear(year);
        branchInfo.setRegionId(regionId);
        branchInfo.setOrganizationId(orgId);
        branchInfo.setOrgName(name);
        branchInfo.setOrgShortName(shortName);
        branchInfo.setOrgLevel(orgLevel);
        branchInfo.setUpdateTime(today);
        branchInfo.setCreateTime(DateUtils.asDate(dateTime));
        branchInfo.setOrgTypeChild(orgTypeChild);
        if (year.equals(LocalDateTime.now().getYear())) {
            PartyOrgVo partyOrg = this.orgService.findPartyId(orgId);
            DssOrg company = DssOrg.builder().orgId(partyOrg.getPartyId()).name(partyOrg.getPartyName()).shortName(partyOrg.getPartyShortName()).build();
            branchInfo.setCompany(company);
        } else {
            OrgSnapshotEntity orgParty = this.orgService.getHistoryOrgParty(orgId, year, null, regionId);
            DssOrg company = DssOrg.builder().orgId(orgParty.getOrgId()).name(orgParty.getOrgName()).shortName(orgParty.getOrgShortName()).build();
            branchInfo.setCompany(company);
        }
        branchInfo.setSsLog(ssLog);
        return branchInfo;
    }

    /**
     * 生成辅助决策基本类
     *
     * @param userId
     * @param name
     * @param politicalType
     * @param gender
     * @param age
     * @param position
     * @param joiningTime
     * @param education
     * @param birPlace
     * @param year
     * @param dateTime
     * @param regionId
     * @param today
     * @return
     */
    public UserInfo getUserInfo(Long userId, String name, Integer politicalType, Integer gender, Integer age,
                                String position, String joiningTime, Integer education, String birPlace, String phoneSecret,
                                Integer year, LocalDateTime dateTime, Long regionId, String today,
                                LogAspectHelper.SSLog ssLog) {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(userId);
        userInfo.setName(name);
        userInfo.setPhoneSecret(phoneSecret);
        userInfo.setCreateTime(DateUtils.asDate(dateTime));
        userInfo.setYear(year);
        userInfo.setRegionId(regionId);
        userInfo.setUpdateTime(today);
        userInfo.setPoliticalType(this.optionService.getValueByKey(Constants.POLITICAL_CODE, politicalType));
        userInfo.setGender(this.optionService.getValueByKey(Constants.GENDER_CODE, gender));
        userInfo.setEducation(this.optionService.getValueByKey(Constants.EDUCATION_CODE, education));
        userInfo.setAgeSection(this.dssBaseService.getAgeSection(age));
        userInfo.setNativePlace(birPlace);
        userInfo.setPosition(position);
        if (year.equals(LocalDateTime.now().getYear())) {
            UserInfoBase uInfo = this.userService.getUserInfoByUserId(userId, regionId);
            userInfo.setBranch(null == uInfo ? DssOrg.builder().build() : DssOrg.builder().orgId(uInfo.getOrgId()).name(uInfo.getOrgName()).build());
            userInfo.setCompany(null == uInfo ? DssOrg.builder().build() : DssOrg.builder().orgId(uInfo.getPartyOrgId()).name(uInfo.getPartyOrgName()).build());
        } else {
            UserSnapshotEntity userSnapshotEntity = this.userService.getHistoryUserInfo(userId, year, null);
            if (null != userSnapshotEntity.getOrgId()) {
                OrgSnapshotEntity historyOrgInfo = this.orgService.getHistoryOrgInfo(userSnapshotEntity.getOrgId(), year, null);
                OrgSnapshotEntity historyOrgParty = this.orgService.getHistoryOrgParty(userSnapshotEntity.getOrgId(), year, null, regionId);
                userInfo.setBranch(null == historyOrgInfo ? DssOrg.builder().build() : DssOrg.builder().orgId(historyOrgInfo.getOrgId()).name(historyOrgInfo.getOrgName()).shortName(historyOrgInfo.getOrgShortName()).build());
                userInfo.setCompany(null == historyOrgParty ? DssOrg.builder().build() : DssOrg.builder().orgId(historyOrgParty.getOrgId()).name(historyOrgParty.getOrgName()).shortName(historyOrgParty.getOrgShortName()).build());
            }
        }
        userInfo.setTag(this.userService.getTagName(userId));
        if (StringUtils.isNotBlank(joiningTime)) {
            try {
                joiningTime = DATE_FORMAT_D.format(DATE_FORMAT_2.parse(joiningTime));
            } catch (Exception e) {
                try {
                    joiningTime = DATE_FORMAT_D.format(DATE_FORMAT.parse(joiningTime));
                } catch (Exception e1) {
                    log.error("入党时间转换失败[{}] -> ", joiningTime, e);
                }
            }
        }
        userInfo.setJoinPartyTime(joiningTime);
        userInfo.setBonusPenaltyInfo(
                this.userCommendPenalizeMapper.getUserCommendPenalize(userId, year));
        userInfo.setSsLog(ssLog);
        log.debug("初始化用户[{}]", userInfo.getUserId());
        return userInfo;
    }

    @Async("dssDeleteFileExecutor")
    public void deleteFile(File file) {
        try {
            Thread.sleep(countdown);
        } catch (InterruptedException e) {
            log.error("删除文件线程报错 -> ", e);
            Thread.currentThread().interrupt();
        }
        FileUtil.deleteFile(file);
        log.debug("辅助决策文件[{}]在[{}]时被删除", file.getName(), DateUtils.dateFormat(new Date(), null));
    }

    public Integer getAgeByBirthday(String birthday, Integer year) {
        int age = 0;
        if (org.apache.commons.lang.StringUtils.isNotBlank(birthday)) {
            String birthYear = DateUtils.toFormat(birthday, "yyyy-MM-dd", "yyyy");
            if (org.apache.commons.lang.StringUtils.isNotBlank(birthYear)) {
                age = year - Integer.parseInt(birthYear);
            }
        }
        return age;
    }

    public void addErrorList(Long id, Integer type) {
        String redisKey;
        switch (type) {
            case 1:
                redisKey = "DSS_ERROR_COMMITTEE_LIST";
                break;
            case 2:
                redisKey = "DSS_ERROR_BRANCH_LIST";
                break;
            default:
                redisKey = "DSS_ERROR_USER_LIST";
                break;
        }
        this.redisTemplate.opsForList().leftPush(redisKey, id.toString());
    }
}
