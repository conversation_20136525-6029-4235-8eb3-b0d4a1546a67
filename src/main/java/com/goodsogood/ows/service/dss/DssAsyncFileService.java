package com.goodsogood.ows.service.dss;

import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.helper.entity.JsonFile;
import com.goodsogood.ows.helper.entity.User;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.utils.RateUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;

@Service
@Log4j2
public class DssAsyncFileService {

	private static final Integer PAGE_SIZE = 1000;

	private final MyMongoTemplate mongoTemplate;

	@Autowired
	public DssAsyncFileService(MyMongoTemplate mongoTemplate) {
		this.mongoTemplate = mongoTemplate;
	}

	@Async("dssBuildFileUserExecutor")
	public void buildDownloadUser(Long regionId, Set<User> users, Set<JsonFile> jsonFileList, String childUuid, String uuid,
								  AtomicReference<Integer> after, Integer[] years, Integer page, CountDownLatch latch) {
		List<UserInfo> userInfoList = new ArrayList<>();
		if (null != years && years.length > 0) {
			for (Integer year : years) {
				userInfoList.addAll(this.getUserInfoList(regionId, year, page));
			}
		} else {
			userInfoList = this.getUserInfoList(regionId, null, page);
		}
		userInfoList.forEach(user -> {
			JsonFile jsonFile = new JsonFile(4, String.valueOf(user.getYear()), Utils.toJson(user), user.getUserId());
			jsonFileList.add(jsonFile);
			// 封装人员files
			String branch = StringUtils.isBlank(user.getBranch().getName()) ? "" : user.getBranch().getName();
			String office = StringUtils.isBlank(user.getCompany().getName())? "" : user.getCompany().getName();
			User u = new User(user.getUserId(), String.valueOf(user.getYear()), user.getName(), user.getPhoneSecret(),
					branch, office, null);
			users.add(u);
			RateUtils.auto(childUuid);
			RateUtils.incr(childUuid, after, 95, uuid);
			latch.countDown();
		});
	}

	/**
	 * 从mongoDB获取首页详情
	 * @param regionId
	 * @param year
	 * @return
	 */
	public List<IndexInfo> getIndexInfo(Long regionId, Integer year) {
		Criteria criteria = Criteria.where("regionId").is(regionId);
		if (null != year) {
			criteria.and("year").is(year);
		}
		Query query = new Query(criteria);
		return this.mongoTemplate.find(query, IndexInfo.class);
	}

	/**
	 * 从mongoDB获取党委详情
	 * @param regionId
	 * @param year
	 * @return
	 */
	public List<PartyCommitteeInfo> getCommitteeInfoList(Long regionId, Integer year) {
		Criteria criteria = Criteria.where("regionId").is(regionId);
		if (null != year) {
			criteria.and("year").is(year);
		}
		Query query = new Query(criteria);
		return this.mongoTemplate.find(query, PartyCommitteeInfo.class);
	}

	/**
	 * 从mongoDB获取党支部详情
	 * @param regionId
	 * @param year
	 * @return
	 */
	public List<PartyBranchInfo> getBranchInfoList(Long regionId, Integer year) {
		Criteria criteria = Criteria.where("regionId").is(regionId);
		if (null != year) {
			criteria.and("year").is(year);
		}
		Query query = new Query(criteria);
		return this.mongoTemplate.find(query, PartyBranchInfo.class);
	}

	/**
	 * 从mongoDB获取用户详情列表
	 * @param regionId
	 * @param year
	 * @return
	 */
	public List<UserInfo> getUserInfoList(Long regionId, Integer year, long page) {
		Criteria criteria = Criteria.where("regionId").is(regionId);
		if (null != year) {
			criteria.and("year").is(year);
		}
		AggregationOptions aggregationOptions = AggregationOptions.builder().allowDiskUse(true).build();
		Aggregation aggregation = Aggregation.newAggregation(
				Aggregation.match(criteria),
				this.buildGroup(UserInfo.class, "userId"),
				Aggregation.sort(Sort.by(Sort.Direction.DESC, "updateTime")),
				Aggregation.skip((page - 1) * PAGE_SIZE),
				Aggregation.limit(PAGE_SIZE),
				Aggregation.sort(Sort.by(Sort.Direction.ASC, "userId"))
		).withOptions(aggregationOptions);
		AggregationResults<UserInfo> aggregate = this.mongoTemplate.aggregate(aggregation, UserInfo.class);
		return aggregate.getMappedResults();
	}

	/**
	 * 从mongoDB获取用户总数
	 * @param regionId
	 * @param year
	 * @return
	 */
	public Integer getUserInfoCount(Long regionId, Integer year) {
		Criteria criteria = Criteria.where("regionId").is(regionId);
		if (null != year) {
			criteria.and("year").is(year);
		}
		Query query = new Query(criteria);
		return Long.valueOf(this.mongoTemplate.count(query, UserInfo.class)).intValue();
	}

	private <T> GroupOperation buildGroup(Class<T> entityClass, String groupBy) {
		Field[] fields = entityClass.getDeclaredFields();
		GroupOperation groupOperation = Aggregation.group(groupBy);
		for (Field field: fields) {
			groupOperation = groupOperation.first(field.getName()).as(field.getName());
		}
		return groupOperation;
	}
}
