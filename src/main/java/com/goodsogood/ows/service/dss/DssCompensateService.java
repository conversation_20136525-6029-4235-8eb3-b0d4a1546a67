package com.goodsogood.ows.service.dss;

import com.aidangqun.log4j2cm.aop.HttpLogAspect;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 辅助决策单模块补偿服务
 * <AUTHOR>
 */
@Service
@Log4j2
public class DssCompensateService {

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final DssBaseService dssBaseService;
    private final DssAsyncCompensateService asyncCompensateService;


    @Autowired
    public DssCompensateService(DssBaseService dssBaseService,
                                DssAsyncCompensateService asyncCompensateService) {
        this.dssBaseService = dssBaseService;
        this.asyncCompensateService = asyncCompensateService;
    }

    /**
     * 补偿
     * @param moduleType
     *              1 - 考核
     *              2 - 组织生活
     *              3 - 领导班子
     *              4 - 报表统计
     *              5 - 党费
     *              6 - 自动打分
     *              7 - 积分
     *              8 - 用户活跃度
     *              9 - 志愿者
     *              10 - 其他基础数据
     * @param dataType
     *              1 - 首页
     *              2 - 党委
     *              3 - 党支部
     *              4 - 用户
     * @param regionId
     *              区县ID
     * @param year
     *              年份
     * @param id
     *              主键
     */
    public void setDssOneModule(Integer moduleType, Integer dataType, Long regionId, int year, Long id) {
        LogAspectHelper.SSLog ssLog = HttpLogAspect.getSSLog();
        LocalDateTime dateTime = LocalDateTime.now();
        String today = DATE_FORMAT.format(dateTime);
        Map<String ,Object> queryMap = new ConcurrentHashMap<>();
        queryMap.put("regionId", regionId);
        queryMap.put("year", year);
        switch (dataType) {
            // 首页
            case 1 :
                List<IndexInfo> list = this.dssBaseService.findList(IndexInfo.class, queryMap);
                list.forEach(info -> this.asyncCompensateService.updateIndex(moduleType, today, info, ssLog));
                break;
            // 党委
            case 2 :
                if (null != id) {
                    queryMap.put("organizationId", id);
                }
                List<PartyCommitteeInfo> committeeInfoList = this.dssBaseService.findList(PartyCommitteeInfo.class, queryMap);
                committeeInfoList.forEach(info -> this.asyncCompensateService.updateCommittee(moduleType, today, info, ssLog));
                break;
            // 党支部
            case 3 :
                if (null != id) {
                    queryMap.put("organizationId", id);
                }
                List<PartyBranchInfo> branchInfoList = this.dssBaseService.findList(PartyBranchInfo.class, queryMap);
                branchInfoList.forEach(info -> this.asyncCompensateService.updateBranch(moduleType, today, info, ssLog));
                break;
            case 4 :
                if (null != id) {
                    queryMap.put("userId", id);
                }
                List<UserInfo> userInfoList = this.dssBaseService.findList(UserInfo.class, queryMap);
                userInfoList.forEach(info -> this.asyncCompensateService.updateUser(moduleType, today, info, ssLog));
                break;
            default :
                log.error("参入传入错误.... -> dataType : [{}]", dataType);
                break;
        }
    }
}
