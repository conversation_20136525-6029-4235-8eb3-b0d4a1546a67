package com.goodsogood.ows.service.dss;

import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.mapper.meeting.UserCommendPenalizeMapper;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.model.mongodb.dss.*;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.sas.PartyOrgVo;
import com.goodsogood.ows.service.autoScore.RealAutoScoreService;
import com.goodsogood.ows.service.eval.dss.DssIndexFromEval;
import com.goodsogood.ows.service.eval.dss.DssPartyBranchFromEval;
import com.goodsogood.ows.service.eval.dss.DssPartyCommitteeFromEval;
import com.goodsogood.ows.service.eval.dss.DssUserFromEval;
import com.goodsogood.ows.service.finereport.OrgInfoReportService;
import com.goodsogood.ows.service.meeting.dss.DssIndexFromMeeting;
import com.goodsogood.ows.service.meeting.dss.DssPartyBranchFromMeeting;
import com.goodsogood.ows.service.meeting.dss.DssPartyCommitteeFromMeeting;
import com.goodsogood.ows.service.meeting.dss.DssUserFromMeeting;
import com.goodsogood.ows.service.ppmd.PpmdAidDecisionService;
import com.goodsogood.ows.service.score.ScoreAidDecisionService;
import com.goodsogood.ows.service.user.*;
import com.goodsogood.ows.service.volunteer.VolunteerReportService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

@Service
@Log4j2
public class DssAsyncCompensateService {

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATE_FORMAT_2 = DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss");
    private static final DateTimeFormatter DATE_FORMAT_D = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

    /** 考核 - 1 */
    private final DssIndexFromEval indexFromEval;
    private final DssPartyCommitteeFromEval committeeFromEval;
    private final DssPartyBranchFromEval branchFromEval;
    private final DssUserFromEval userFromEval;
    /** 组织生活 - 2 */
    private final DssIndexFromMeeting indexFromMeeting;
    private final DssPartyCommitteeFromMeeting committeeFromMeeting;
    private final DssPartyBranchFromMeeting branchFromMeeting;
    private final DssUserFromMeeting userFromMeeting;
    /** 领导班子 - 3 */
    private final DssPeriodAndLeaderService periodAndLeaderService;
    /** 报表统计 - 4 */
    private final OrgInfoReportService infoReportService;
    /** 党费 - 5 */
    private final PpmdAidDecisionService ppmdAidDecisionService;
    /** 自动打分 - 6 */
    private final RealAutoScoreService autoScoreService;
    /** 积分 - 7 */
    private final ScoreAidDecisionService scoreAidDecisionService;
    /** 用户活跃度 - 8 */
    private final UserLoginService userLoginService;
    /** 志愿者 - 9 */
    private final VolunteerReportService volunteerReportService;
    /** 其他基础数据 - 10 */
    private final DssUserService dssUserService;
    private final DssBaseService dssBaseService;

    private final OrgService orgService;
    private final UserService userService;
    private final OptionService optionService;
    private final DssBufferTriggerService bufferTriggerService;
    private final UserCommendPenalizeMapper userCommendPenalizeMapper;


    @Autowired
    public DssAsyncCompensateService(DssIndexFromEval indexFromEval,
                                     DssIndexFromMeeting indexFromMeeting,
                                     DssPartyCommitteeFromEval committeeFromEval,
                                     DssPartyCommitteeFromMeeting committeeFromMeeting,
                                     DssPartyBranchFromEval branchFromEval,
                                     DssPartyBranchFromMeeting branchFromMeeting,
                                     DssUserFromEval userFromEval,
                                     DssUserFromMeeting userFromMeeting,
                                     DssPeriodAndLeaderService periodAndLeaderService,
                                     OrgInfoReportService infoReportService,
                                     PpmdAidDecisionService ppmdAidDecisionService,
                                     RealAutoScoreService autoScoreService,
                                     ScoreAidDecisionService scoreAidDecisionService,
                                     UserLoginService userLoginService,
                                     VolunteerReportService volunteerReportService,
                                     DssUserService dssUserService,
                                     DssBaseService dssBaseService,
                                     OrgService orgService,
                                     UserService userService,
                                     OptionService optionService,
                                     DssBufferTriggerService bufferTriggerService,
                                     UserCommendPenalizeMapper userCommendPenalizeMapper) {
        this.indexFromEval = indexFromEval;
        this.indexFromMeeting = indexFromMeeting;
        this.committeeFromEval = committeeFromEval;
        this.committeeFromMeeting = committeeFromMeeting;
        this.branchFromEval = branchFromEval;
        this.branchFromMeeting = branchFromMeeting;
        this.userFromEval = userFromEval;
        this.userFromMeeting = userFromMeeting;
        this.periodAndLeaderService = periodAndLeaderService;
        this.infoReportService = infoReportService;
        this.ppmdAidDecisionService = ppmdAidDecisionService;
        this.autoScoreService = autoScoreService;
        this.scoreAidDecisionService = scoreAidDecisionService;
        this.userLoginService = userLoginService;
        this.volunteerReportService = volunteerReportService;
        this.dssUserService = dssUserService;
        this.dssBaseService = dssBaseService;
        this.orgService = orgService;
        this.userService = userService;
        this.optionService = optionService;
        this.bufferTriggerService = bufferTriggerService;
        this.userCommendPenalizeMapper = userCommendPenalizeMapper;
    }

    /**
     * 更新党委
     * @param moduleType
     * @param info
     */
    public void updateIndex(Integer moduleType, String today, IndexInfo info, LogAspectHelper.SSLog ssLog) {
        switch (moduleType) {
            case 1 :
                info = this.indexFromEval.buildIndex(info);
                break;
            case 2 :
                info = this.indexFromMeeting.buildIndex(info);
                break;
            case 3 :
                info = this.periodAndLeaderService.buildIndex(info);
                break;
            case 4 :
                info = this.infoReportService.buildIndex(info);
                break;
            case 5 :
                info = this.ppmdAidDecisionService.buildIndex(info);
                break;
            case 6 :
                info = this.autoScoreService.buildIndex(info);
                break;
            case 7 :
                info = this.scoreAidDecisionService.buildIndex(info);
                break;
            case 8 :
                info = this.userLoginService.buildIndex(info);
                break;
            case 9 :
                info = this.volunteerReportService.buildIndex(info);
                break;
            case 10 :
                List<IndexGrandMap> grandMap = this.dssBaseService.addOrgListIndex(info.getGrandMap(), info.getYear());
                info.setGrandMap(grandMap);
                break;
            default:
                log.error("参数传入错误.... -> moduleType : [{}]", moduleType);
                break;
        }
        info.setSsLog(ssLog);
        info.setUpdateTime(today);
        this.bufferTriggerService.addBuffer(info);
    }

    @Async("dssCompensateExecutor")
    public void updateCommittee(Integer moduleType, String today, PartyCommitteeInfo info, LogAspectHelper.SSLog ssLog) {
        //重新设置上下文
        LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
        helper.reSetContext(ssLog);
        switch (moduleType) {
            case 1 :
                info = this.committeeFromEval.buildPartyCommittee(info);
                break;
            case 2 :
                info = this.committeeFromMeeting.buildPartyCommittee(info);
                break;
            case 3 :
                info = this.periodAndLeaderService.buildPartyCommittee(info);
                break;
            case 4 :
                info = this.infoReportService.buildPartyCommittee(info);
                break;
            case 5 :
                info = this.ppmdAidDecisionService.buildPartyCommittee(info);
                break;
            case 6 :
                info = this.autoScoreService.buildPartyCommittee(info);
                break;
            case 7 :
                info = this.scoreAidDecisionService.buildPartyCommittee(info);
                break;
            case 8 :
                info = this.userLoginService.buildPartyCommittee(info);
                break;
            case 9 :
                info = this.volunteerReportService.buildPartyCommittee(info);
                break;
            case 10 :
                if (info.getYear().equals(LocalDateTime.now().getYear())) {
                    OrganizationEntity org = this.orgService.getById(info.getOrganizationId());
                    info.setOrgName(org.getName());
                    info.setOrgShortName(org.getShortName());
                    info.setOrgLevel(org.getOrgLevel());
                    info.setOrgTypeChild(org.getOrgTypeChild());
                } else {
                    OrgSnapshotEntity historyOrgInfo = this.orgService.getHistoryOrgInfo(info.getOrganizationId(), info.getYear(), null);
                    info.setOrgName(historyOrgInfo.getOrgName());
                    info.setOrgShortName(historyOrgInfo.getOrgShortName());
                    info.setOrgLevel(historyOrgInfo.getOrgLevel());
                    info.setOrgTypeChild(historyOrgInfo.getOrgTypeChild());
                }
                info = this.dssUserService.buildPartyCommittee(info);
                List<DssOrgChild> dssOrgChild = this.dssBaseService.addOrgListInfo(info.getOrgList(), info.getYear(), true);
                info.setSecPartyBranchTotal(dssOrgChild.size());
                info.setOrgList(dssOrgChild);
                List<GrandMap> grandMapList = this.dssBaseService.addOrgList(info.getGrandMap(), info.getYear());
                info.setGrandMap(grandMapList);
                break;
            default:
                log.error("参数传入错误.... -> moduleType : [{}]", moduleType);
                break;
        }
        info.setSsLog(ssLog);
        info.setUpdateTime(today);
        this.bufferTriggerService.addBuffer(info);
    }

    @Async("dssCompensateExecutor")
    public void updateBranch(Integer moduleType, String today, PartyBranchInfo info, LogAspectHelper.SSLog ssLog) {
        //重新设置上下文
        LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
        helper.reSetContext(ssLog);
        switch (moduleType) {
            case 1 :
                info = this.branchFromEval.buildPartyBranch(info);
                break;
            case 2 :
                info = this.branchFromMeeting.buildPartyBranch(info);
                break;
            case 3 :
                info = this.periodAndLeaderService.buildPartyBranch(info);
                break;
            case 4 :
                info = this.infoReportService.buildPartyBranch(info);
                break;
            case 5 :
                info = this.ppmdAidDecisionService.buildPartyBranch(info);
                break;
            case 6 :
                info = this.autoScoreService.buildPartyBranch(info);
                break;
            case 7 :
                info = this.scoreAidDecisionService.buildPartyBranch(info);
                break;
            case 8 :
                info = this.userLoginService.buildPartyBranch(info);
                break;
            case 9 :
                info = this.volunteerReportService.buildPartyBranch(info);
                break;
            case 10 :
                if (info.getYear().equals(LocalDateTime.now().getYear())) {
                    OrganizationEntity org = this.orgService.getById(info.getOrganizationId());
                    info.setOrgName(org.getName());
                    info.setOrgShortName(org.getShortName());
                    info.setOrgLevel(org.getOrgLevel());
                    info.setOrgTypeChild(org.getOrgTypeChild());
                    PartyOrgVo partyOrg = this.orgService.findPartyId(info.getOrganizationId());
                    DssOrg company = DssOrg.builder().orgId(partyOrg.getPartyId()).name(partyOrg.getPartyName()).shortName(partyOrg.getPartyShortName()).build();
                    info.setCompany(company);
                } else {
                    OrgSnapshotEntity historyOrgInfo = this.orgService.getHistoryOrgInfo(info.getOrganizationId(), info.getYear(), null);
                    info.setOrgName(historyOrgInfo.getOrgName());
                    info.setOrgShortName(historyOrgInfo.getOrgShortName());
                    info.setOrgLevel(historyOrgInfo.getOrgLevel());
                    info.setOrgTypeChild(historyOrgInfo.getOrgTypeChild());
                    OrgSnapshotEntity orgParty = this.orgService.getHistoryOrgParty(info.getOrganizationId(), info.getYear(), null,info.getRegionId());
                    DssOrg company = DssOrg.builder().orgId(orgParty.getOrgId()).name(orgParty.getOrgName()).shortName(orgParty.getOrgShortName()).build();
                    info.setCompany(company);
                }
                info = this.dssUserService.buildPartyBranch(info);
                List<UserGrandMap> userGrandMaps = this.dssBaseService.addUserList(info.getGrandMap(), info.getYear(), info.getRegionId());
                info.setGrandMap(userGrandMaps);
                List<DssUserChild> dssUserChildren = this.dssBaseService.addUserListInfo(info.getUserList(), info.getYear(), info.getRegionId());
                info.setPartyMemberTotal(dssUserChildren.size());
                info.setUserList(dssUserChildren);
                break;
            default:
                log.error("参数传入错误.... -> moduleType : [{}]", moduleType);
                break;
        }
        info.setSsLog(ssLog);
        info.setUpdateTime(today);
        this.bufferTriggerService.addBuffer(info);
    }

    @Async("dssCompensateExecutor")
    public void updateUser(Integer moduleType, String today, UserInfo info, LogAspectHelper.SSLog ssLog) {
        //重新设置上下文
        LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
        helper.reSetContext(ssLog);
        switch (moduleType) {
            case 1 :
                info = this.userFromEval.buildUser(info);
                break;
            case 2 :
                info = this.userFromMeeting.buildUser(info);
                break;
            case 3 :
                info = this.periodAndLeaderService.buildUser(info);
                break;
            case 4 :
                break;
            case 5 :
                info = this.ppmdAidDecisionService.buildUser(info);
                break;
            case 6 :
                info = this.autoScoreService.buildUser(info);
                break;
            case 7 :
                info = this.scoreAidDecisionService.buildUser(info);
                break;
            case 8 :
                info = this.userLoginService.buildUser(info);
                break;
            case 9 :
                info = this.volunteerReportService.buildUser(info);
                break;
            case 10 :
                if (info.getYear().equals(LocalDateTime.now().getYear())) {
                    UserEntity user = this.userService.getUserInfo(info.getUserId());
                    info.setName(user.getName());
                    info.setPhoneSecret(user.getPhoneSecret());
                    info.setPoliticalType(this.optionService.getValueByKey(Constants.POLITICAL_CODE, user.getPoliticalType()));
                    info.setGender(this.optionService.getValueByKey(Constants.GENDER_CODE, user.getGender()));
                    info.setEducation(this.optionService.getValueByKey(Constants.EDUCATION_CODE, user.getEducation()));
                    info.setAgeSection(this.dssBaseService.getAgeSection(user.getAge()));
                    info.setNativePlace(user.getBirthPlace());
                    info.setPosition(user.getPosition());
                    UserInfoBase uInfo = this.userService.getUserInfoByUserId(info.getUserId(), info.getRegionId());
                    info.setBranch(DssOrg.builder().orgId(uInfo.getOrgId()).name(uInfo.getOrgName()).build());
                    info.setCompany(DssOrg.builder().orgId(uInfo.getPartyOrgId()).name(uInfo.getPartyOrgName()).build());
                    String joiningTime = user.getJoiningTime();
                    try {
                        joiningTime = DATE_FORMAT_D.format(DATE_FORMAT_2.parse(joiningTime));
                    } catch (Exception e) {
                        try {
                            joiningTime = DATE_FORMAT_D.format(DATE_FORMAT.parse(joiningTime));
                        } catch (Exception e1) {
                            log.error("入党时间转换失败[{}] -> ", joiningTime, e);
                        }
                    }
                    info.setJoinPartyTime(joiningTime);
                } else {
                    UserSnapshotEntity user = this.userService.getHistoryUserInfo(info.getUserId(), info.getYear(), null);
                    info.setName(user.getUserName());
                    info.setPhoneSecret(user.getPhoneSecret());
                    info.setPoliticalType(this.optionService.getValueByKey(Constants.POLITICAL_CODE, user.getPoliticalType()));
                    info.setGender(this.optionService.getValueByKey(Constants.GENDER_CODE, user.getGender()));
                    info.setEducation(this.optionService.getValueByKey(Constants.EDUCATION_CODE, user.getEducation()));
                    info.setAgeSection(this.dssBaseService.getAgeSection(user.getAge()));
                    info.setNativePlace(user.getBirthplace());
                    info.setPosition(user.getPosition());
                    OrgSnapshotEntity historyOrgInfo = this.orgService.getHistoryOrgInfo(user.getOrgId(), info.getYear(), null);
                    OrgSnapshotEntity historyOrgParty = this.orgService.getHistoryOrgParty(user.getOrgId(), info.getYear(), null, info.getRegionId());
                    info.setBranch(DssOrg.builder().orgId(historyOrgInfo.getOrgId()).name(historyOrgInfo.getOrgName()).shortName(historyOrgInfo.getOrgShortName()).build());
                    info.setCompany(DssOrg.builder().orgId(historyOrgParty.getOrgId()).name(historyOrgParty.getOrgName()).shortName(historyOrgParty.getOrgShortName()).build());
                    String joiningTime = user.getProPartyTime();
                    try {
                        joiningTime = DATE_FORMAT_D.format(DATE_FORMAT_2.parse(joiningTime));
                    } catch (Exception e) {
                        try {
                            joiningTime = DATE_FORMAT_D.format(DATE_FORMAT.parse(joiningTime));
                        } catch (Exception e1) {
                            log.error("入党时间转换失败[{}] -> ", joiningTime, e);
                        }
                    }
                    info.setJoinPartyTime(joiningTime);
                }
                info.setTag(this.userService.getTagName(info.getUserId()));
                info.setBonusPenaltyInfo(this.userCommendPenalizeMapper.getUserCommendPenalize(info.getUserId(), info.getYear()));
                break;
            default:
                log.error("参数传入错误.... -> moduleType : [{}]", moduleType);
                break;
        }
        info.setSsLog(ssLog);
        info.setUpdateTime(today);
        this.bufferTriggerService.addBuffer(info);
    }

    public enum ClassEnumType {
        /** 考核 */
        EVAL("考核", new String[]{"DssIndexFromEval", "DssPartyCommitteeFromEval", "DssPartyBranchFromEval", "DssUserFromEval"}, 1),
        /** 组织生活 */
        MEETING("组织生活", new String[]{"DssIndexFromMeeting", "DssPartyCommitteeFromMeeting", "DssPartyBranchFromMeeting", "DssUserFromMeeting"}, 2),
        /** 领导班子 */
        LEADER("领导班子", new String[]{"DssPeriodAndLeaderService"}, 3),
        /** 报表统计 */
        REPORT("报表统计", new String[]{"OrgInfoReportService"}, 4),
        /** 党费 */
        PPMD("党费", new String[]{"PpmdAidDecisionService"}, 5),
        /** 评分系统 */
        AUTO_SCORE("评分系统", new String[]{"RealAutoScoreService"}, 6),
        /** 积分 */
        SCORE("积分", new String[]{"ScoreAidDecisionService"}, 7),
        /** 用户活跃度 */
        LOGIN("用户活跃度", new String[]{"UserLoginService"}, 8),
        /** 志愿者 */
        VOLUNTEER("志愿者", new String[]{"VolunteerReportService"}, 9),
        /** 基础信息 */
        BASE_INFO("基础信息", new String[]{""}, 10);

        private final String name;
        private String[] className;
        private Integer moduleId;

        ClassEnumType(String name, String[] className, int moduleId) {
            this.name = name;
            this.className = className;
            this.moduleId = moduleId;
        }

        public String[] getClassName() {
            return className;
        }

        public void setClassName(String[] className) {
            this.className = className;
        }

        public Integer getModuleId() {
            return moduleId;
        }

        public void setModuleId(Integer moduleId) {
            this.moduleId = moduleId;
        }

    }
}


