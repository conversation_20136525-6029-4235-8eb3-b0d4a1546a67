package com.goodsogood.ows.service.dss;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.PADFileHelper;
import com.goodsogood.ows.helper.entity.JsonFile;
import com.goodsogood.ows.helper.entity.Org;
import com.goodsogood.ows.helper.entity.User;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.user.UserDssVo;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.FileUtil;
import com.goodsogood.ows.utils.RateUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 辅助决策文件服务
 * <AUTHOR>
 */
@Service
@Log4j2
public class DssFileService {

    @Value("${dss.debug}")
    private boolean debug;

    @Value("${dss.version}")
    private String version;

    @Value("${dss.path}")
    private String path;

    @Value("${dss.clean}")
    private boolean clean;

    private static final Integer PAGE_INDEX = 1;
    private static final Integer PAGE_SIZE = 1000;

    private final Errors errors;
    private final OrgTypeConfig orgTypeConfig;
    private final UserService userService;
    private final DssAsyncFileService asyncFileService;
    private final DssAsyncService dssAsyncService;

    @Autowired
    public DssFileService(Errors errors,
                          OrgTypeConfig orgTypeConfig,
                          UserService userService,
                          DssAsyncFileService asyncFileService, DssAsyncService dssAsyncService) {
        this.errors = errors;
        this.orgTypeConfig = orgTypeConfig;
        this.userService = userService;
        this.asyncFileService = asyncFileService;
        this.dssAsyncService = dssAsyncService;
    }

    @Async("dssBuildFileExecutor")
    public void buildFile(Long regionId, Integer[] years, Long userId, String uuid) {
        try{
            RateUtils.build(100, uuid);
            PADFileHelper fileHelper = new PADFileHelper(path, UUID.randomUUID().toString(), version);
            // 构建文件
            fileHelper.clear().setDebug(debug);
            // 生成辅助决策数据
            fileHelper = this.getJsonFile(fileHelper, regionId, years, uuid);
            // 构建
            UserInfoBase user = this.userService.getUserInfoByUserId(userId, regionId);
            fileHelper.build(this.formatStr(user.getPhone(), 2), this.formatStr(user.getUserName(), 1), clean);
            RateUtils.update(uuid, 5L);
            log.debug("文件生成完毕！");
        } catch (Exception e) {
            log.error("构建文件失败 ->" ,e);
            RateUtils.delete(uuid);
        }
    }

    public PADFileHelper getJsonFile(PADFileHelper fileHelper, Long regionId, Integer[] years, String uuid) {
        Set<JsonFile> jsonFileList = Collections.synchronizedSet(new HashSet<>());
        List<PartyCommitteeInfo> committeeInfoList = new ArrayList<>();
        List<PartyBranchInfo> branchInfoList = new ArrayList<>();
        List<IndexInfo> indexInfos = new ArrayList<>();
        Integer userInfoCount = 0;
        if (null != years && years.length > 0) {
            for (Integer year : years) {
                // 获取首页的数据
                indexInfos.addAll(this.asyncFileService.getIndexInfo(regionId, year));
                // 生成党委、党总支的JsonFile
                committeeInfoList.addAll(this.asyncFileService.getCommitteeInfoList(regionId, year));
                // 生成党支部的JsonFile
                branchInfoList.addAll(this.asyncFileService.getBranchInfoList(regionId, year));
                // 生成人员的JsonFile
                userInfoCount += this.asyncFileService.getUserInfoCount(regionId, year);
            }
        } else {
            indexInfos = this.asyncFileService.getIndexInfo(regionId, null);
            committeeInfoList = this.asyncFileService.getCommitteeInfoList(regionId, null);
            branchInfoList = this.asyncFileService.getBranchInfoList(regionId, null);
            userInfoCount += this.asyncFileService.getUserInfoCount(regionId, null);
        }
        //设计计数器
        AtomicReference<Integer> after = new AtomicReference<>(0);
        String childUuid = RateUtils.build(indexInfos.size() + committeeInfoList.size() + branchInfoList.size() + userInfoCount);
        // 处理数据
        indexInfos.forEach(info -> {
            fileHelper.addOrgFile(String.valueOf(info.getYear()), Utils.toJson(info));
            RateUtils.auto(childUuid);
            RateUtils.incr(childUuid, after, 95, uuid);
        });
        List<Org> orgs = new ArrayList<>();
        committeeInfoList.forEach(committee -> {
            JsonFile jsonFile = new JsonFile(2, String.valueOf(committee.getYear()), Utils.toJson(committee), committee.getOrganizationId());
            jsonFileList.add(jsonFile);
            // 封装党委files
            Org o = new Org(committee.getOrganizationId(), String.valueOf(committee.getYear()), committee.getOrgName(),
                    committee.getOrgShortName(), 2, this.getOrgTypeStr(committee.getOrgTypeChild()));
            orgs.add(o);
            RateUtils.auto(childUuid);
            RateUtils.incr(childUuid, after, 95, uuid);
        });
        branchInfoList.forEach(branch -> {
            JsonFile jsonFile = new JsonFile(3, String.valueOf(branch.getYear()), Utils.toJson(branch),
                    branch.getOrganizationId());
            jsonFileList.add(jsonFile);
            // 封装党支部files
            Org o = new Org(branch.getOrganizationId(), String.valueOf(branch.getYear()), branch.getOrgName(),
                    branch.getOrgShortName(), 3, this.getOrgTypeStr(branch.getOrgTypeChild()));
            orgs.add(o);
            RateUtils.auto(childUuid);
            RateUtils.incr(childUuid, after, 95, uuid);
        });
        int totalPage = (userInfoCount + PAGE_SIZE - 1) / PAGE_SIZE;
        CountDownLatch latch = new CountDownLatch(userInfoCount);
        Set<User> users = Collections.synchronizedSet(new HashSet<>());
        for (int page = PAGE_INDEX; page <= totalPage; page++) {
            this.asyncFileService.buildDownloadUser(regionId, users, jsonFileList, childUuid, uuid, after, years, page, latch);
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("构建下载文件计数器报错 -> ", e);
            Thread.currentThread().interrupt();
        }
        fileHelper.addOrg(orgs.toArray(new Org[0]));
        fileHelper.addUser(users.toArray(new User[0]));
        // 导入json数据
        fileHelper.addFiles(jsonFileList.toArray(new JsonFile[0]));
        return fileHelper;
    }

    public void downloadFile(HttpServletResponse response, String dssFileUuid) throws IOException {
        File[] endsWithNameFile = FileUtil.getEndsWithNameFile(path, Constants.DSS_FILE_ENDS_NAME);
        if (endsWithNameFile.length != 0) {
            File file = endsWithNameFile[0];
            FileUtil.download(response, file);
            this.dssAsyncService.deleteFile(file);
        } else {
            String rate = RateUtils.getRate(dssFileUuid, 2);
            throw new ApiException("文件还未生成完毕，暂不能下载 -> " + rate, new Result<>(errors, 5000, HttpStatus.OK.value(), rate + "%"));
        }
    }

    /**
     * 格式化姓名和手机号
     * @param str
     * @param type  1-姓名, 2-手机号
     * @return
     */
    public String formatStr(String str, Integer type) {
        if (type.equals(1)) {
            // 拆分姓名
            if (StringUtils.isNotBlank(str)) {
                str = String.format("%s_%s", str.charAt(0), str.substring(str.length() - 1));
            }
        } else if (type.equals(2)) {
            // 拆分手机号
            if (StringUtils.isNotBlank(str)) {
                str = String.format("%s____%s", str.substring(0, 3), str.substring(str.length() - 4));
            }
        }
        return str;
    }



    private String getOrgTypeStr(Integer orgTypeChild) {
        String orgType;
        if (this.orgTypeConfig.getCommunistChild().contains(orgTypeChild)) {
            orgType = "党委";
        } else if(this.orgTypeConfig.getGeneralBranchChild().contains(orgTypeChild)) {
            orgType = "党总支";
        } else {
            orgType = "党支部";
        }
        return orgType;
    }
}
