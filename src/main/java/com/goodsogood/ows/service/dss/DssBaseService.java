package com.goodsogood.ows.service.dss;

import com.goodsogood.ows.common.AgeSectionEnum;
import com.goodsogood.ows.common.DingDingMessage;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.model.mongodb.dss.*;
import com.goodsogood.ows.service.PushDingDingService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.RateUtils;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.beanutils.BeanUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Log4j2
public class DssBaseService {

    private static final DateTimeFormatter DATE_FORMAT_2 = DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss");

    private final OrgService orgService;
    private final UserService userService;
    private final PushDingDingService dingDingService;
    private final MyMongoTemplate mongoTemplate;

    @Autowired
    public DssBaseService(OrgService orgService,
                          UserService userService,
                          PushDingDingService dingDingService,
                          MyMongoTemplate mongoTemplate) {
        this.orgService = orgService;
        this.userService = userService;
        this.dingDingService = dingDingService;
        this.mongoTemplate = mongoTemplate;
    }

    /**
     * 首页评分党委列表数据封装
     * @param grandMapList
     * @param year
     * @return
     */
    public List<IndexGrandMap> addOrgListIndex(List<IndexGrandMap> grandMapList, Integer year) {
        if(!CollectionUtils.isEmpty(grandMapList)) {
            List<OfficeOrg> allGrand = new ArrayList<>();
            grandMapList.stream().map(IndexGrandMap::getOrgList).forEach(allGrand::addAll);
            List<OfficeOrg> orgList = this.addIndexOrgListInfo(new ArrayList<>(allGrand), year);
            grandMapList.forEach(grand -> {
                List<OfficeOrg> childList = new ArrayList<>();
                for (OfficeOrg org : grand.getOrgList()) {
                    childList.add(orgList.stream().filter(item -> org.getOrgId().equals(item.getOrgId())).findFirst().orElse(null));
                }
                childList = childList.stream().filter(Objects::nonNull).sorted(Comparator.comparing(OfficeOrg::getGrand, Comparator.nullsFirst(Double::compareTo)).reversed()).collect(Collectors.toList());
                grand.setOrgList(childList);
            });
        }
        return grandMapList;
    }

    private List<OfficeOrg> addIndexOrgListInfo(List<OfficeOrg> dssOrgChild, Integer year) {
        List<OfficeOrg> orgResult = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dssOrgChild)) {
            for (OfficeOrg org : dssOrgChild) {
                OfficeOrg orgInfo = this.orgService.buildIndexOfficeList(org.getOrgId(), year);
                orgResult.add(orgInfo);
            }
            orgResult = orgResult.stream().filter(Objects::nonNull).sorted(Comparator.comparing(DssOrgChild::getGrand, Comparator.nullsFirst(Double::compareTo)).reversed()).collect(Collectors.toList());
        }
        return orgResult;
    }

    /**
     * 组织数据重新封装
     * @param grandMapList
     * @param year
     * @return
     */
    public List<GrandMap> addOrgList(List<GrandMap> grandMapList, Integer year) {
        if(!CollectionUtils.isEmpty(grandMapList)) {
            List<DssOrgChild> allGrand = new ArrayList<>();
            grandMapList.stream().map(GrandMap::getOrgList).forEach(allGrand::addAll);
            List<DssOrgChild> orgList = this.addOrgListInfo(new ArrayList<>(allGrand), year, true);
            grandMapList.forEach(grand -> {
                List<DssOrgChild> childList = new ArrayList<>();
                for (DssOrgChild org : grand.getOrgList()) {
                    childList.add(orgList.stream().filter(item -> org.getOrgId().equals(item.getOrgId())).findFirst().orElse(null));
                }
                childList = childList.stream().filter(Objects::nonNull).sorted(Comparator.comparing(DssOrgChild::getGrand, Comparator.nullsFirst(Double::compareTo)).reversed()).collect(Collectors.toList());
                grand.setOrgList(childList);
            });
        }
        return grandMapList;
    }

    public List<DssOrgChild> addOrgListInfo(List<DssOrgChild> dssOrgChild, Integer year, boolean flag) {
        List<DssOrgChild> orgResult = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dssOrgChild)) {
            for (DssOrgChild org : dssOrgChild) {
                DssOrgChild orgInfo = this.orgService.buildOrgList(org.getOrgId(), year, flag);
                orgResult.add(orgInfo);
            }
            orgResult = orgResult.stream().filter(Objects::nonNull).sorted(Comparator.comparing(DssOrgChild::getGrand, Comparator.nullsFirst(Double::compareTo)).reversed()).collect(Collectors.toList());
        }
        return orgResult;
    }

    /**
     * 保存或更新MongoDB数据
     * @param entityClass
     * @param objectCollection
     * @param idList
     * @param <T>
     */
    public <T> void insertOrUpdateData(Class<T> entityClass, List<T> objectCollection, List<ObjectId> idList) {
        if (!CollectionUtils.isEmpty(idList)) {
            Criteria criteria = Criteria.where("_id").in(idList);
            Query query = new Query(criteria);
            this.mongoTemplate.remove(query, entityClass);
            log.debug("需要删除数据 -> [{}]", idList);
        }
        this.mongoTemplate.insertAll(objectCollection);
    }

    public <T> void insertOrUpdateData(Class<T> entityClass, List<T> objectCollection, Map<String, Object> queryMap) {
        Criteria criteria = this.getCriteria(queryMap);
        Query query = new Query(criteria);
        this.mongoTemplate.remove(query, entityClass);
        this.mongoTemplate.insertAll(objectCollection);
    }

    /**
     * 获取年龄区间
     * @param age
     * @return
     */
    public Integer[] getAgeSection(Integer age) {
        return Arrays.stream(AgeSectionEnum.values()).map(AgeSectionEnum::getSection).filter(section -> age >= section[0] && age <= section[1]).findFirst().orElse(null);
    }

    public List<UserGrandMap> addUserList(List<UserGrandMap> userMapList, Integer year, Long regionId) {
        if(!CollectionUtils.isEmpty(userMapList)) {
            List<DssUserChild> childList = new ArrayList<>();
            userMapList.stream().map(UserGrandMap::getUserList).forEach(childList::addAll);
            List<DssUserChild> dssUserChild = this.addUserListInfo(childList, year, regionId);
            userMapList.forEach(map -> {
                List<DssUserChild> userList = new ArrayList<>();
                for (DssUserChild user : map.getUserList()) {
                    userList.add(dssUserChild.stream().filter(item -> user.getUserId().equals(item.getUserId())).findFirst().orElse(null));
                }
                userList = userList.stream().filter(Objects::nonNull).sorted(Comparator.comparing(DssUserChild::getGrand, Comparator.nullsFirst(Double::compareTo)).reversed()).collect(Collectors.toList());
                map.setUserList(userList);
            });
        }
        return userMapList;
    }

    public List<DssUserChild> addUserListInfo(List<DssUserChild> dssUserChild, Integer year, Long regionId) {
        List<DssUserChild> userResult = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dssUserChild)) {
            for (DssUserChild user : dssUserChild) {
                DssUserChild userInfo = this.userService.buildUserList(user.getUserId(), regionId, year);
                userResult.add(userInfo);
            }
            userResult = userResult.stream().filter(Objects::nonNull).sorted(Comparator.comparing(DssUserChild::getGrand, Comparator.nullsFirst(Double::compareTo)).reversed()).collect(Collectors.toList());
        }
        return userResult;
    }

    public Criteria getCriteria(Map<String, Object> queryMap) {
        log.debug("打印queryMap -> [{}]", queryMap);
        Criteria criteria = new Criteria();
        int i = 0;
        for (String key: queryMap.keySet()) {
            Object o = queryMap.get(key);
            if (i == 0) {
                if (o instanceof Collection<?>) {
                    criteria = Criteria.where(key).in((Collection<?>)o);
                } else {
                    criteria = Criteria.where(key).is(o);
                }
                i = 1;
            } else {
                if (o instanceof Collection<?>) {
                    criteria.and(key).in((Collection<?>)o);
                } else {
                    criteria.and(key).is(o);
                }
            }
        }
        return criteria;
    }

    public <T> Update getUpdate(Class<T> entityClass, T object)  {
        Update update = new Update();
        Field[] fields = entityClass.getDeclaredFields();
        Map<String, String> objectMap = null;
        try {
            objectMap = BeanUtils.describe(object);
        } catch (Exception e) {
            log.error("对象转换Map报错 -> ", e);
            e.printStackTrace();
        }
        for (Field field: fields) {
            assert objectMap != null;
            update.set(field.getName(), objectMap.get(field.getName()));
        }
        return update;
    }

    public <T> List<T> findList(Class<T> entityClass, Map<String ,Object> queryMap) {
        Criteria criteria = this.getCriteria(queryMap);
        Query query = new Query(criteria);
        return this.mongoTemplate.find(query, entityClass);
    }

    public <T> T findOne(Class<T> entityClass, Map<String ,Object> queryMap) {
        Criteria criteria = this.getCriteria(queryMap);
        Query query = new Query(criteria);
        return this.mongoTemplate.findOne(query, entityClass);
    }

    public <T> Long delete(Class<T> entityClass, Map<String ,Object> queryMap) {
        Criteria criteria = this.getCriteria(queryMap);
        Query query = new Query(criteria);
        DeleteResult deleteResult = this.mongoTemplate.remove(query, entityClass);
        return deleteResult.getDeletedCount();
    }

    public void rateAndDing(String trackerId, String message, Long delta) {
        if (null == delta) {
            RateUtils.auto(trackerId);
        } else {
            RateUtils.update(trackerId, delta);
        }
        if (RateUtils.complete(trackerId)) {
            DingDingMessage dingMessage = DingDingMessage.builder()
                    .trackId(trackerId)
                    .time(LocalDateTime.now().format(DATE_FORMAT_2))
                    .content(message)
                    .build();
            this.dingDingService.push(dingMessage);
        }
    }

}
