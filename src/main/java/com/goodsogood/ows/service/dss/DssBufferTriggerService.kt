package com.goodsogood.ows.service.dss

import com.github.phantomthief.collection.BufferTrigger
import com.github.phantomthief.collection.impl.MultiIntervalTriggerStrategy
import com.goodsogood.ows.model.mongodb.IndexInfo
import com.goodsogood.ows.model.mongodb.PartyBranchInfo
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo
import com.goodsogood.ows.model.mongodb.UserInfo
import com.goodsogood.ows.model.mongodb.dss.DssBaseInfo
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit
import java.util.stream.Collectors
import javax.annotation.PostConstruct
import javax.annotation.PreDestroy


@Service
class DssBufferTriggerService(@Autowired val dssBaseService: DssBaseService,
                              @Autowired val asyncBufferTriggerService: DssAsyncBufferTriggerService) {

    val log: Logger = LoggerFactory.getLogger(DssBufferTriggerService::class.java)

    lateinit var bufferTrigger: BufferTrigger<DssBaseInfo>

    @PostConstruct
    fun initBufferTrigger() {

        bufferTrigger = BufferTrigger.simple<DssBaseInfo, Set<DssBaseInfo>>()
                .triggerStrategy(MultiIntervalTriggerStrategy()
                        .on(10, TimeUnit.SECONDS, 1)
                        .on(5, TimeUnit.SECONDS, 50)
                        .on(1, TimeUnit.SECONDS, 200))
                .consumer(this::insert)
                .enableBackPressure()
                .maxBufferCount(100_000)
                .build()
    }

    fun insert(list: Collection<DssBaseInfo>) {
        log.debug("导入数据数量 -> [${list.size}]")
        var indexInfoList = mutableListOf<IndexInfo>()
        var committeeInfoList = mutableListOf<PartyCommitteeInfo>()
        var branchInfoList = mutableListOf<PartyBranchInfo>()
        var userInfoList = mutableListOf<UserInfo>()
        list.forEach{
            when (it) {
                is IndexInfo -> {
                    indexInfoList.add(it)
                }
                is PartyCommitteeInfo -> {
                    committeeInfoList.add(it)
                }
                is PartyBranchInfo -> {
                    branchInfoList.add(it)
                }
                is UserInfo -> {
                    userInfoList.add(it)
                }
                else -> log.error("传入的参数有问题! -> [$it]")
            }
        }
        val indexCollect = indexInfoList.stream().collect(Collectors.groupingBy(IndexInfo::getYear))
        indexCollect.forEach { (t, u) ->  this.asyncBufferTriggerService.insertAndUpdateData(u, t,IndexInfo::class.java)}
        val committeeCollect = committeeInfoList.stream().collect(Collectors.groupingBy(PartyCommitteeInfo::getYear))
        committeeCollect.forEach { (t, u) ->  this.asyncBufferTriggerService.insertAndUpdateData(u, t, PartyCommitteeInfo::class.java)}
        val branchCollect = branchInfoList.stream().collect(Collectors.groupingBy(PartyBranchInfo::getYear))
        branchCollect.forEach { (t, u) ->  this.asyncBufferTriggerService.insertAndUpdateData(u, t, PartyBranchInfo::class.java)}
        val userCollect = userInfoList.stream().collect(Collectors.groupingBy(UserInfo::getYear))
        userCollect.forEach { (t, u) ->  this.asyncBufferTriggerService.insertAndUpdateData(u, t, UserInfo::class.java)}
        log.debug("导入数据数量 -> [${list.size}] 完成！")
    }

    @Async("dssAddBufferExecutor")
    fun addBuffer(any: DssBaseInfo) {
        this.bufferTrigger.enqueue(any)
    }

    @PreDestroy
    fun beforeDestroy() {
        this.bufferTrigger.manuallyDoTrigger()
    }
}