package com.goodsogood.ows.service.dss;

import com.aidangqun.log4j2cm.aop.HttpLogAspect;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.dss.IndexGrandMap;
import com.goodsogood.ows.service.impl.DssIndexBuilder;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.RateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 辅助决策 - 单条数据处理
 * <AUTHOR>
 */
@Service
@Log4j2
public class DssService {

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final List<DssIndexBuilder> indexBuilderList;
    private final SimpleApplicationConfigHelper applicationConfigHelper;
    private final OrgService orgService;
    private final UserService userService;
    private final DssAsyncService dssAsyncService;
    private final OrgTypeConfig orgTypeConfig;
    private final DssBaseService dssBaseService;
    private final DssBufferTriggerService bufferTriggerService;


    @Autowired
    public DssService(List<DssIndexBuilder> indexBuilderList,
                      SimpleApplicationConfigHelper applicationConfigHelper,
                      OrgService orgService,
                      UserService userService,
                      DssAsyncService dssAsyncService,
                      OrgTypeConfig orgTypeConfig,
                      DssBaseService dssBaseService,
                      DssBufferTriggerService bufferTriggerService) {
        this.indexBuilderList = indexBuilderList;
        this.applicationConfigHelper = applicationConfigHelper;
        this.orgService = orgService;
        this.userService = userService;
        this.dssAsyncService = dssAsyncService;
        this.orgTypeConfig = orgTypeConfig;
        this.dssBaseService = dssBaseService;
        this.bufferTriggerService = bufferTriggerService;
    }

    @Async("dssBuildExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void build(Long regionId, Integer year, String uuid) {
        RateUtils.build(100, uuid);
        // 首页
        try {
            this.buildIndex(regionId, year);
            RateUtils.update(uuid, 10L);
        } catch (Exception e) {
            log.error("辅助决策-首页数据生成报错 -> ", e);
        }
        // 党委
        try {
            String committeeUuid = this.buildCommittee(regionId, year);
            RateUtils.incr(committeeUuid, 5, uuid);
        } catch (Exception e) {
            log.error("辅助决策-党委数据生成报错 -> ", e);
        }
        // 党支部
        try {
            String branchUuid = this.buildBranch(regionId, year);
            RateUtils.incr(branchUuid, 5, uuid);
        } catch (Exception e) {
            log.error("辅助决策-党支部数据生成报错 -> ", e);
        }
        // 用户
        try {
            String userUuid = this.buildUserInfo(regionId, year);
            RateUtils.incr(userUuid, 2, uuid);
        } catch (Exception e) {
            log.error("辅助决策-党员数据生成报错 -> ", e);
        }
    }

    /**
     * 主页创建
     * @param regionId
     * @param year
     */
    @Async("dssIndexExecutor")
    public void buildIndex(Long regionId, Integer year) {
        LocalDateTime dateTime = LocalDateTime.now();
        Region.OrgData orgData = applicationConfigHelper.getOrgByRegionId(regionId);
        IndexInfo indexInfo = new IndexInfo();
        indexInfo.setRootId(orgData.getOrgId());
        indexInfo.setRegionId(regionId);
        indexInfo.setYear(year);
        indexInfo.setUpdateTime(DATE_FORMAT.format(dateTime));
        indexInfo.setCreateTime(DateUtils.asDate(dateTime));
        for (DssIndexBuilder indexBuilder : this.indexBuilderList) {
            try {
                indexInfo = indexBuilder.buildIndex(indexInfo);
            } catch (Exception e) {
                log.debug("调用[{}]报错", indexBuilder);
                log.error("首页生成数据报错 -> " ,e);
            }
        }
        // 封装组织列表数据
        List<IndexGrandMap> grandMap = this.dssBaseService.addOrgListIndex(indexInfo.getGrandMap(), year);
        indexInfo.setGrandMap(grandMap);
        this.bufferTriggerService.addBuffer(indexInfo);
    }

    /**
     * 党委、党总支数据创建
     * @param regionId
     * @param year
     */
    @Transactional(rollbackFor = Exception.class)
    public String buildCommittee(Long regionId, Integer year) {
        LogAspectHelper.SSLog ssLog = HttpLogAspect.getSSLog();
        String trackerId = ssLog.getTrackerId();
        LocalDateTime dateTime = LocalDateTime.now();
        String today = DATE_FORMAT.format(dateTime);
        if (year.equals(dateTime.getYear())) {
            // 查询下级党委
            List<OrganizationEntity> officeCommittee = this.orgService.getCommunistAndGeneral(regionId, null);
            RateUtils.build(officeCommittee.size(), trackerId);
            officeCommittee.forEach(org -> this.dssAsyncService.saveCommitteeInfo(year, regionId,
                    org.getOrganizationId(), org.getName(), org.getShortName(), org.getOrgLevel(),
                    org.getOrgTypeChild(),today, dateTime, trackerId, ssLog));
        } else {
            // 查询下级党委
            List<OrgSnapshotEntity> orgSnapshotList = this.orgService.getHistoryCommunistAndGeneral(regionId, year, null);
            RateUtils.build(orgSnapshotList.size(), trackerId);
            orgSnapshotList.forEach(org -> this.dssAsyncService.saveCommitteeInfo(year, regionId,
                    org.getOrgId(), org.getOrgName(), org.getOrgShortName(), org.getOrgLevel(), org.getOrgTypeChild(),
                    today, dateTime, trackerId, ssLog));
        }
        return trackerId;
    }

    /**
     * 党委下级支部数据创建
     * @param regionId
     * @param year
     */
    @Transactional(rollbackFor = Exception.class)
    public String buildBranch(Long regionId, Integer year) {
        LogAspectHelper.SSLog ssLog = HttpLogAspect.getSSLog();
        String trackerId = ssLog.getTrackerId();
        LocalDateTime dateTime = LocalDateTime.now();
        String today = DATE_FORMAT.format(dateTime);
        // 查询下级党委
        Region.OrgData orgData = applicationConfigHelper.getOrgByRegionId(regionId);
        if (year.equals(dateTime.getYear())) {
            List<OrganizationEntity> partyBranch = this.orgService.getPartyBranch(orgData.getOrgId(), null, true);
            RateUtils.build(partyBranch.size(), trackerId);
            partyBranch.forEach(organization -> this.dssAsyncService.saveBranchInfo(year, regionId, organization.getOrganizationId(),
                    organization.getName(), organization.getShortName(), organization.getOrgLevel(), organization.getOrgTypeChild(), today, dateTime, trackerId, ssLog));
        } else {
            List<OrgSnapshotEntity> historyPartyBranch = this.orgService.getHistoryPartyBranch(orgData.getOrgId(), year, true, null);
            RateUtils.build(historyPartyBranch.size(), trackerId);
            historyPartyBranch.forEach(snapshotEntity -> this.dssAsyncService.saveBranchInfo(year, regionId, snapshotEntity.getOrgId(),
                    snapshotEntity.getOrgName(), snapshotEntity.getOrgShortName(), snapshotEntity.getOrgLevel(), snapshotEntity.getOrgTypeChild(), today, dateTime, trackerId, ssLog));
        }
        return trackerId;
    }

    /**
     * 构建用户信息
     * @param regionId
     * @param year
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String buildUserInfo(Long regionId, Integer year) {
        LogAspectHelper.SSLog ssLog = HttpLogAspect.getSSLog();
        String trackerId = ssLog.getTrackerId();
        LocalDateTime dateTime = LocalDateTime.now();
        String today = DATE_FORMAT.format(dateTime);
        // 查询下级党委
        Region.OrgData orgData = applicationConfigHelper.getOrgByRegionId(regionId);
        if (year.equals(dateTime.getYear())) {
            List<UserEntity> userList = this.userService.getUserByOrg(orgData.getOrgId());
            RateUtils.build(userList.size(), trackerId);
            userList.forEach(user -> this.dssAsyncService.saveUserInfo(user.getUserId(), user.getName(),
                    user.getPoliticalType(), user.getGender(), user.getAge(),
                    user.getPosition(), user.getJoiningTime(), user.getEducation(), user.getBirthPlace(),
                    user.getPhoneSecret(), year, dateTime, regionId, today, trackerId, ssLog));
        } else {
            List<UserSnapshotEntity> userList = this.userService.getHistoryUserList(regionId, year);
            RateUtils.build(userList.size(), trackerId);
            userList.forEach(user -> this.dssAsyncService.saveUserInfo(user.getUserId(), user.getUserName(), user.getPoliticalType(),
                    user.getGender(), this.dssAsyncService.getAgeByBirthday(user.getBirthday(), year),
                    user.getPosition(), user.getProPartyTime(), user.getEducation(),
                    user.getBirthplace(), user.getPhoneSecret(), year, dateTime, regionId, today, trackerId, ssLog));
        }
        return trackerId;
    }

    public void buildDssDataByOrg(Long regionId, long[] orgIds, int[] years) {
        LogAspectHelper.SSLog ssLog = HttpLogAspect.getSSLog();
        LocalDateTime now = LocalDateTime.now();
        String today = DATE_FORMAT.format(now);
        for (int year : years) {
            if (year == now.getYear()) {
                LinkedList<OrganizationEntity> allChildOrg = new LinkedList<>();
                List<UserEntity> userList = new ArrayList<>();
                for (long orgId : orgIds) {
                    LinkedList<OrganizationEntity> entities = this.orgService.findAllChildOrg(orgId, Constants.STATUS_YES, regionId, false);
                    List<UserEntity> users = this.userService.getUserByOrg(orgId);
                    allChildOrg.addAll(entities);
                    userList.addAll(users);
                }
                allChildOrg.forEach(org -> {
                    if (this.orgTypeConfig.getCommunistAndGeneral().contains(org.getOrgTypeChild())) {
                        this.dssAsyncService.saveCommitteeInfo(year, regionId, org.getOrganizationId(), org.getName(),
                                org.getShortName(), org.getOrgLevel(), org.getOrgTypeChild(), today, now, null, ssLog);
                    } else if (this.orgTypeConfig.getBranchChild().contains(org.getOrgTypeChild())) {
                        this.dssAsyncService.saveBranchInfo(year, regionId, org.getOrganizationId(), org.getName(),
                                org.getShortName(), org.getOrgLevel(), org.getOrgTypeChild(),today, now, null, ssLog);
                    }
                });
                userList.forEach(user -> {
                    this.dssAsyncService.saveUserInfo(user.getUserId(), user.getName(), user.getPoliticalType(), user.getGender(),
                            user.getAge(), user.getPosition(), user.getJoiningTime(), user.getEducation(),
                            user.getBirthPlace(), user.getPhoneSecret(), year, now, regionId, today, null, ssLog);
                });
            } else {
                List<OrgSnapshotEntity> allChildOrg = new ArrayList<>();
                List<UserSnapshotEntity> userList = new ArrayList<>();
                for (long orgId : orgIds) {
                    List<OrgSnapshotEntity> childOrg = this.orgService.getHistoryPartyBranch(orgId, year, true, null);
                    OrgSnapshotEntity historyOrgInfo = this.orgService.getHistoryOrgInfo(orgId, year, null);
                    allChildOrg.addAll(childOrg);
                    allChildOrg.add(historyOrgInfo);
                    List<UserSnapshotEntity> users = this.userService.getHistoryUserListByOrg(orgId, year);
                    userList.addAll(users);
                }
                allChildOrg.forEach(org -> {
                    if (this.orgTypeConfig.getCommunistAndGeneral().contains(org.getOrgTypeChild())) {
                        this.dssAsyncService.saveCommitteeInfo(year, regionId, org.getOrgId(), org.getOrgName(),
                                org.getOrgShortName(), org.getOrgLevel(), org.getOrgTypeChild(), today, now, null, ssLog);
                    } else if (this.orgTypeConfig.getBranchChild().contains(org.getOrgTypeChild())) {
                        this.dssAsyncService.saveBranchInfo(year, regionId, org.getOrgId(), org.getOrgName(),
                                org.getOrgShortName(), org.getOrgLevel(), org.getOrgTypeChild(), today, now, null, ssLog);
                    }
                });
                userList.forEach(user -> {
                    this.dssAsyncService.saveUserInfo(user.getUserId(), user.getUserName(), user.getPoliticalType(), user.getGender(),
                            user.getAge(), user.getPosition(), user.getProPartyTime(), user.getEducation(),
                            user.getBirthplace(), user.getPhoneSecret(), year, now, regionId, today, null, ssLog);
                });
            }
        }
    }

    public void buildDssOneUserInfo(long[] userIds, Long regionId, int[] years) {
        LogAspectHelper.SSLog ssLog = HttpLogAspect.getSSLog();
        LocalDateTime now = LocalDateTime.now();
        String today = DATE_FORMAT.format(now);
        for (int year : years) {
            for (long userId : userIds) {
                if (LocalDateTime.now().getYear() > year) {
                    UserSnapshotEntity userInfo = this.userService.getHistoryUserInfo(userId, year, null);
                    this.dssAsyncService.saveUserInfo(userInfo.getUserId(), userInfo.getUserName(), userInfo.getPoliticalType(),
                            userInfo.getGender(), userInfo.getAge(), userInfo.getPosition(), userInfo.getProPartyTime(),
                            userInfo.getEducation(), userInfo.getBirthplace(), userInfo.getPhoneSecret(), year, now, regionId, today, null, ssLog);
                } else {
                    UserEntity info = this.userService.getUserInfo(userId);
                    this.dssAsyncService.saveUserInfo(info.getUserId(), info.getName(), info.getPoliticalType(), info.getGender(),
                            info.getAge(), info.getPosition(), info.getJoiningTime(), info.getEducation(),
                            info.getBirthPlace(), info.getPhoneSecret(), year, now, regionId, today, null, ssLog);
                }
            }
        }
    }

    public void buildDssOneOrgInfo(long[] orgIds, Long regionId, int[] years) {
        LogAspectHelper.SSLog ssLog = HttpLogAspect.getSSLog();
        LocalDateTime now = LocalDateTime.now();
        String today = DATE_FORMAT.format(now);
        for (int year : years) {
            for (long orgId : orgIds) {
                if (year == now.getYear()) {
                    OrganizationEntity org = this.orgService.getById(orgId);
                    if (this.orgTypeConfig.getCommunistAndGeneral().contains(org.getOrgTypeChild())) {
                        this.dssAsyncService.saveCommitteeInfo(year, regionId, org.getOrganizationId(), org.getName(),
                                org.getShortName(), org.getOrgLevel(), org.getOrgTypeChild(), today, now, null, ssLog);
                    } else if (this.orgTypeConfig.getBranchChild().contains(org.getOrgTypeChild())) {
                        this.dssAsyncService.saveBranchInfo(year, regionId, org.getOrganizationId(), org.getName(),
                                org.getShortName(), org.getOrgLevel(), org.getOrgTypeChild(), today, now, null, ssLog);
                    }
                } else {
                    OrgSnapshotEntity org = this.orgService.getHistoryOrgInfo(orgId, year, null);
                    if (this.orgTypeConfig.getCommunistAndGeneral().contains(org.getOrgTypeChild())) {
                        this.dssAsyncService.saveCommitteeInfo(year, regionId, org.getOrgId(), org.getOrgName(),
                                org.getOrgShortName(), org.getOrgLevel(), org.getOrgTypeChild(), today, now, null, ssLog);
                    } else if (this.orgTypeConfig.getBranchChild().contains(org.getOrgTypeChild())) {
                        this.dssAsyncService.saveBranchInfo(year, regionId, org.getOrgId(), org.getOrgName(),
                                org.getOrgShortName(), org.getOrgLevel(), org.getOrgTypeChild(), today, now, null, ssLog);
                    }
                }
            }
        }
    }
}
