package com.goodsogood.ows.service.dss;

import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.model.mongodb.dss.DssOrgChild;
import com.goodsogood.ows.model.mongodb.dss.DssUserChild;
import com.goodsogood.ows.model.mongodb.dss.GrandMap;
import com.goodsogood.ows.model.mongodb.dss.UserGrandMap;
import com.goodsogood.ows.service.impl.DssPartyBranchBuilder;
import com.goodsogood.ows.service.impl.DssPartyCommitteeBuilder;
import com.goodsogood.ows.service.impl.DssUserBuilder;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 异步服务
 * <AUTHOR>
 */
@Service
@Log4j2
public class DssAsyncBatchService {

    private final List<DssPartyCommitteeBuilder> commitBuilderList;
    private final List<DssPartyBranchBuilder> branchBuilderList;
    private final List<DssUserBuilder> userBuilderList;
    private final DssBaseService dssBaseService;
    private final DssBufferTriggerService bufferTriggerService;

    @Autowired
    public DssAsyncBatchService(List<DssPartyCommitteeBuilder> commitBuilderList,
                                List<DssPartyBranchBuilder> branchBuilderList,
                                List<DssUserBuilder> userBuilderList,
                                DssBaseService dssBaseService,
                                DssBufferTriggerService bufferTriggerService) {
        this.commitBuilderList = commitBuilderList;
        this.branchBuilderList = branchBuilderList;
        this.userBuilderList = userBuilderList;
        this.dssBaseService = dssBaseService;
        this.bufferTriggerService = bufferTriggerService;
    }

    /**
     *  生层党委数据 - 批量
     * @param year 年份
     * @param infoList  list
     */
    public void saveBatchCommitteeInfo(Integer year, List<PartyCommitteeInfo> infoList, String trackerId) {
        List<Long> orgIds = infoList.stream().map(PartyCommitteeInfo::getOrganizationId).collect(Collectors.toList());
        List<PartyCommitteeInfo> resultList = new ArrayList<>();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        for(DssPartyCommitteeBuilder committeeBuilder : this.commitBuilderList) {
            try {
                resultList = committeeBuilder.buildPartyCommitteeList(infoList);
                log.debug("[DSS批量党委][{}], 模块[{}]调用完成, 耗时 -> [{}]", orgIds, committeeBuilder, stopWatch.toString());
            } catch (Exception e) {
                log.debug("[DSS批量党委报错]组织列表[{}] 年份 [{}] -> 调用[{}]报错", orgIds, year, committeeBuilder);
                log.error("党委生成数据报错 -> " ,e);
            }
        }
        List<DssOrgChild> allOrg = new ArrayList<>();
        resultList.stream().map(PartyCommitteeInfo::getOrgList).forEach(allOrg::addAll);
        // 封装组织列表数据
        List<DssOrgChild> dssOrgChild = this.dssBaseService.addOrgListInfo(allOrg, year, true);
        resultList.forEach(info -> {
            List<DssOrgChild> childList = new ArrayList<>();
            for (DssOrgChild org : info.getOrgList()) {
                childList.add(dssOrgChild.stream().filter(item -> org.getOrgId().equals(item.getOrgId())).findFirst().orElse(null));
            }
            childList = childList.stream().filter(Objects::nonNull).sorted(Comparator.comparing(DssOrgChild::getGrand, Comparator.nullsFirst(Double::compareTo)).reversed()).collect(Collectors.toList());
            info.setOrgList(childList);
        });

        List<DssOrgChild> allGrand = new ArrayList<>();
        resultList.stream().map(PartyCommitteeInfo::getGrandMap).forEach(item -> item.stream().map(GrandMap::getOrgList).forEach(allGrand::addAll));

        List<DssOrgChild> grandMapList = this.dssBaseService.addOrgListInfo(allGrand, year, true);
        resultList.forEach(info -> info.getGrandMap().forEach(grand -> {
            List<DssOrgChild> childList = new ArrayList<>();
            for (DssOrgChild org : grand.getOrgList()) {
                childList.add(grandMapList.stream().filter(item -> org.getOrgId().equals(item.getOrgId())).findFirst().orElse(null));
            }
            childList = childList.stream().filter(Objects::nonNull).sorted(Comparator.comparing(DssOrgChild::getGrand, Comparator.nullsFirst(Double::compareTo)).reversed()).collect(Collectors.toList());
            grand.setOrgList(childList);
        }));
        log.debug("[DSS批量党委]重新封装组织列表数据完成, 耗时 -> [{}]", stopWatch.toString());
        // 封装查询条件
        infoList.forEach(this.bufferTriggerService::addBuffer);
        log.debug("[DSS批量党委]数据保存调用完成, 耗时 -> [{}]", stopWatch.toString());
        stopWatch.stop();
        this.dssBaseService.rateAndDing(trackerId, String.format("辅助决策党委[%s]数据生成完毕", year), (long) resultList.size());
    }

    /**
     * 党支部保存数据
     * @param year 年份
     * @param infoList
     */
    public void saveBatchBranchInfo(Integer year, Long regionId, List<PartyBranchInfo> infoList, String trackerId) {
        List<Long> orgIds = infoList.stream().map(PartyBranchInfo::getOrganizationId).collect(Collectors.toList());
        List<PartyBranchInfo> resultList  = new ArrayList<>();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        for (DssPartyBranchBuilder branchBuilder : this.branchBuilderList) {
            try {
                resultList = branchBuilder.buildPartyBranchList(infoList);
                log.debug("[DSS批量党支部][{}], 模块[{}]调用完成, 耗时 -> [{}]", orgIds, branchBuilder, stopWatch.toString());
            } catch (Exception e) {
                log.debug("[DSS批量党支部报错]组织[{}] 年份 [{}] -> 调用[{}]报错", orgIds, year, branchBuilder);
                log.error("党支部生成数据报错 -> " ,e);
            }
        }

        // 封装组织列表数据
        List<DssUserChild> allUser = new ArrayList<>();
        resultList.stream().map(PartyBranchInfo::getGrandMap).forEach(item -> item.stream().map(UserGrandMap::getUserList).forEach(allUser::addAll));
        List<DssUserChild> dssUserChild = this.dssBaseService.addUserListInfo(allUser, year, regionId);
        resultList.forEach(info -> info.getGrandMap().forEach(userGrand -> {
            List<DssUserChild> childList = new ArrayList<>();
            userGrand.getUserList().forEach(user -> childList.add(dssUserChild.stream().filter(item -> user.getUserId().equals(item.getUserId())).findFirst().orElse(null)));
            userGrand.setUserList(childList);
        }));
        List<DssUserChild> allGrand = new ArrayList<>();
        resultList.stream().map(PartyBranchInfo::getUserList).forEach(allGrand::addAll);
        List<DssUserChild> userList = this.dssBaseService.addUserListInfo(allGrand, year, regionId);
        resultList.forEach(info -> {
            List<DssUserChild> childList = new ArrayList<>();
            info.getUserList().forEach(user -> childList.add(userList.stream().filter(item -> user.getUserId().equals(item.getUserId())).findFirst().orElse(null)));
            info.setUserList(childList);
        });
        log.debug("[DSS批量党支部]重新封装组织列表数据完成, 耗时 -> [{}]", stopWatch.toString());
        // 封装查询条件
        infoList.forEach(this.bufferTriggerService::addBuffer);
        log.debug("[DSS批量党支部]数据保存调用完成, 耗时 -> [{}]", stopWatch.toString());
        stopWatch.stop();
        this.dssBaseService.rateAndDing(trackerId, String.format("辅助决策党支部[%s]数据生成完毕", year), (long) resultList.size());
    }

    public void saveBatchUserInfo(List<UserInfo> infoList, Integer year, String trackerId) {
        List<Long> userIds = infoList.stream().map(UserInfo::getUserId).collect(Collectors.toList());
        List<UserInfo> resultList = new ArrayList<>();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        for (DssUserBuilder userBuilder : this.userBuilderList) {
            try {
                resultList = userBuilder.buildUserList(infoList);
                log.debug("[DSS批量用户][{}], 模块[{}]调用完成, 耗时 -> [{}]", userIds, userBuilder, stopWatch.toString());
            } catch (Exception e) {
                log.debug("[DSS批量用户报错]用户[{}] 年份 [{}] -> 调用[{}]报错", userIds, year, userBuilder);
                log.error("党员生成数据报错 -> " , e);
            }
        }
        // 封装查询条件
        infoList.forEach(this.bufferTriggerService::addBuffer);
        log.debug("[DSS批量用户]数据保存调用完成, 耗时 -> [{}]", stopWatch.toString());
        stopWatch.stop();
        this.dssBaseService.rateAndDing(trackerId, String.format("辅助决策数据党员[%s]年的数据完成", year), (long) resultList.size());
    }
}
