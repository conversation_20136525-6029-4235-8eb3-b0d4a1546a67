package com.goodsogood.ows.service.dss

import com.goodsogood.ows.model.mongodb.IndexInfo
import com.goodsogood.ows.model.mongodb.PartyBranchInfo
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo
import com.goodsogood.ows.model.mongodb.UserInfo
import com.goodsogood.ows.model.mongodb.dss.DssBaseInfo
import org.bson.types.ObjectId
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service

@Service
class DssAsyncBufferTriggerService(@Autowired val dssBaseService: DssBaseService) {

    val log: Logger = LoggerFactory.getLogger(DssBufferTriggerService::class.java)

    @Async("dssInsertExecutor")
    fun <T> insertAndUpdateData(list: List<T>, year: Number, entityClass: Class<T> ) {
        log.debug("插入类型 -> [${entityClass}], 数量 -> [${list.size}]")
        if (list.isNotEmpty()) {
            var queryMap: Map<String, Any?>
            var idList = mutableListOf<Long>()
            var name = "default"
            list.forEach {
                when (it) {
                    is UserInfo -> {
                        name = "userId"
                        idList.add(it.userId)
                    }
                    is PartyBranchInfo -> {
                        name = "organizationId"
                        idList.add(it.organizationId)
                    }
                    is PartyCommitteeInfo -> {
                        name = "organizationId"
                        idList.add(it.organizationId)
                    }
                    is IndexInfo -> {
                        name = "regionId"
                        idList.add(it.regionId)
                    }
                    else -> log.error("传入的参数有问题! -> [$it]")
                }
            }
            queryMap = mapOf(name to idList, "year" to year)
            dssBaseService.insertOrUpdateData(entityClass, list, queryMap)
        }
    }
}