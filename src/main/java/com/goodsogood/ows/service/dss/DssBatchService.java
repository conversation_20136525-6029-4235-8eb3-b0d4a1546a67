package com.goodsogood.ows.service.dss;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.aop.HttpLogAspect;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.RateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 辅助决策 - 多条数据处理
 * <AUTHOR>
 */
@Service
@Log4j2
public class DssBatchService {

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Value("${rabbit-value.dss-user.queue}")
    private String userQueue;

    @Value("${rabbit-value.dss-branch.queue}")
    private String branchQueue;

    @Value("${rabbit-value.dss-committee.queue}")
    private String committeeQueue;

    private final SimpleApplicationConfigHelper applicationConfigHelper;
    private final OrgService orgService;
    private final UserService userService;
    private final DssAsyncService asyncService;
    private final RabbitTemplate rabbitTemplate;

    @Autowired
    public DssBatchService(SimpleApplicationConfigHelper applicationConfigHelper,
                           OrgService orgService,
                           UserService userService,
                           DssAsyncService asyncService,
                           RabbitTemplate rabbitTemplate) {
        this.applicationConfigHelper = applicationConfigHelper;
        this.orgService = orgService;
        this.userService = userService;
        this.asyncService = asyncService;
        this.rabbitTemplate = rabbitTemplate;
    }

    /**
     * 党委、党总支数据批量创建
     * @param regionId
     * @param year
     */
    @Transactional(rollbackFor = Exception.class)
    public String buildCommitteeList(Long regionId, Integer year) {
        LogAspectHelper.SSLog ssLog = HttpLogAspect.getSSLog();
        int pageSize = 10;
        LocalDateTime dateTime = LocalDateTime.now();
        String today = DATE_FORMAT.format(dateTime);
        List<PartyCommitteeInfo> committeeInfoList;
        if (year.equals(dateTime.getYear())) {
            // 查询下级党委
            List<OrganizationEntity> officeCommittee = this.orgService.getCommunistAndGeneral(regionId, null);
            committeeInfoList = officeCommittee.stream()
                    .map(org -> this.asyncService.getCommitteeInfo(year, regionId, org.getOrganizationId(),
                            org.getName(), org.getShortName(), org.getOrgLevel(), org.getOrgTypeChild(), today, dateTime, ssLog))
                    .collect(Collectors.toList());
        } else {
            // 查询下级党委
            List<OrgSnapshotEntity> orgSnapshotList = this.orgService.getHistoryCommunistAndGeneral(regionId, year, null);
            committeeInfoList = orgSnapshotList.stream()
                    .map(org -> this.asyncService.getCommitteeInfo(year, regionId, org.getOrgId(),
                            org.getOrgName(), org.getOrgShortName(), org.getOrgLevel(), org.getOrgTypeChild(), today, dateTime, ssLog))
                    .collect(Collectors.toList());
        }
        RateUtils.build(committeeInfoList.size(), ssLog.getTrackerId());
        for(int i = 1; i <= ((committeeInfoList.size() + pageSize -1) / pageSize); i++) {
            List<PartyCommitteeInfo> committeeInfos = committeeInfoList.stream().skip((i - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            this.rabbitTemplate.convertAndSend(committeeQueue, committeeInfos);
        }
        return ssLog.getTrackerId();
    }

    /**
     * 党支部数据批量创建
     * @param regionId
     * @param year
     */
    public String buildBranchList(Long regionId, Integer year) {
        LogAspectHelper.SSLog ssLog = HttpLogAspect.getSSLog();
        String trackerId = ssLog.getTrackerId();
        LocalDateTime dateTime = LocalDateTime.now();
        String today = DATE_FORMAT.format(dateTime);
        int pageSize = 50;
        // 查询下级党支部
        Region.OrgData orgData = applicationConfigHelper.getOrgByRegionId(regionId);
        List<PartyBranchInfo> branchInfoList;
        if (year.equals(dateTime.getYear())) {
            List<OrganizationEntity> partyBranch = this.orgService.getPartyBranch(orgData.getOrgId(), null, true);
            branchInfoList = partyBranch.stream()
                    .map(organization ->
                            this.asyncService.getBranchInfo(year, regionId, organization.getOrganizationId(),
                                    organization.getName(), organization.getShortName(), organization.getOrgLevel(), organization.getOrgTypeChild(),
                                    today, dateTime, ssLog))
                    .collect(Collectors.toList());
        } else {
            List<OrgSnapshotEntity> historyPartyBranch = this.orgService.getHistoryPartyBranch(orgData.getOrgId(), year, true, null);
            branchInfoList = historyPartyBranch.stream()
                    .map(snapshotEntity ->
                            this.asyncService.getBranchInfo(year, regionId, snapshotEntity.getOrgId(),
                                    snapshotEntity.getOrgName(), snapshotEntity.getOrgShortName(), snapshotEntity.getOrgLevel(), snapshotEntity.getOrgTypeChild(),
                                    today, dateTime, ssLog))
                    .collect(Collectors.toList());
        }
        RateUtils.build(branchInfoList.size(), trackerId);
        for(int i = 1; i <= ((branchInfoList.size() + pageSize -1) / pageSize); i++) {
            List<PartyBranchInfo> branchInfos = branchInfoList.stream().skip((i - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            this.rabbitTemplate.convertAndSend(branchQueue, branchInfos);
        }
        return trackerId;
    }

    /**
     * 构建用户信息
     * @param regionId
     * @param year
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String buildUserInfoList(Long regionId, Integer year) {
        LogAspectHelper.SSLog ssLog = HttpLogAspect.getSSLog();
        String trackerId = ssLog.getTrackerId();
        LocalDateTime dateTime = LocalDateTime.now();
        String today = DATE_FORMAT.format(dateTime);
        int pageSize = 100;
        // 查询下级党委
        Region.OrgData orgData = applicationConfigHelper.getOrgByRegionId(regionId);
        List<UserInfo> userInfoList;
        int pageTotal = 100;
        if (year.equals(dateTime.getYear())) {
            for (int page = 1; page <= pageTotal; page++) {
                Page<UserEntity> pageUser = this.userService.getUserByOrg(orgData.getOrgId(), page, pageSize);
                if(pageUser.getPages() > 0) {
                    pageTotal = pageUser.getPages();
                    if (!RateUtils.hasKey(trackerId)) {
                        RateUtils.build(Math.toIntExact(pageUser.getTotal()), trackerId);
                    }
                    userInfoList = pageUser.getResult().stream()
                            .map(user -> this.asyncService.getUserInfo(user.getUserId(), user.getName(),
                                    user.getPoliticalType(), user.getGender(), user.getAge(),
                                    user.getPosition(), user.getJoiningTime(), user.getEducation(), user.getBirthPlace(),
                                    user.getPhoneSecret(), year, dateTime, regionId, today, ssLog))
                            .collect(Collectors.toList());
                    this.rabbitTemplate.convertAndSend(userQueue, userInfoList);
                }
            }
        } else {
            for (int page = 1; page <= pageTotal; page++) {
                Page<UserSnapshotEntity> pageUser = this.userService.getHistoryUserList(regionId, year, page, pageSize);
                if(pageUser.getPages() > 0) {
                    pageTotal = pageUser.getPages();
                    if (!RateUtils.hasKey(trackerId)) {
                        RateUtils.build(Math.toIntExact(pageUser.getTotal()), trackerId);
                    }
                    userInfoList = pageUser.getResult().stream()
                            .map(user -> this.asyncService.getUserInfo(user.getUserId(), user.getUserName(),
                                    user.getPoliticalType(), user.getGender(), user.getAge(),
                                    user.getPosition(), user.getProPartyTime(), user.getEducation(), user.getBirthplace(),
                                    user.getPhoneSecret(), year, dateTime, regionId, today, ssLog))
                            .collect(Collectors.toList());
                    this.rabbitTemplate.convertAndSend(userQueue, userInfoList);
                }
            }
        }
        return trackerId;
    }
}
