package com.goodsogood.ows.service.dss;

import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.helper.entity.Org;
import com.goodsogood.ows.helper.entity.User;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Pattern;

@Service
@Log4j2
public class DssTestService {

	private final OrgTypeConfig orgTypeConfig;
	private final MyMongoTemplate mongoTemplate;

	@Autowired
	public DssTestService(OrgTypeConfig orgTypeConfig,
						  MyMongoTemplate mongoTemplate) {
		this.orgTypeConfig = orgTypeConfig;
		this.mongoTemplate = mongoTemplate;
	}

	public String getReport(int type, long id, int year, long regionId) {
		String result;
		switch (type) {
			case 1 :
				IndexInfo index = this.getIndexInfo(regionId, year);
				result = JsonUtils.toJson(null == index ? new IndexInfo() : index);
				break;
			case 2 :
				List<PartyCommitteeInfo> offices = this.getCommitteeInfoList(regionId, year, id, null);
				result = CollectionUtils.isEmpty(offices) ? JsonUtils.toJson(new PartyCommitteeInfo()) : JsonUtils.toJson(offices.get(0));
				break;
			case 3 :
				List<PartyBranchInfo> branches = this.getBranchInfoList(regionId, year, id, null);
				result = CollectionUtils.isEmpty(branches) ? JsonUtils.toJson(new PartyBranchInfo()) : JsonUtils.toJson(branches.get(0));
				break;
			case 4 :
				List<UserInfo> users = this.getUserInfoList(regionId, year, id, null);
				result = CollectionUtils.isEmpty(users) ? JsonUtils.toJson(new UserInfo()) : JsonUtils.toJson(users.get(0));
				break;
			default:
				result = "传入type错误！";
				log.error("传入type错误！");
				break;
		}
		return result;
	}

	public String getUsers(String name, Integer year, long regionId) {
		List<UserInfo> userInfoList = this.getUserInfoList(regionId, year, null, name);
		List<User> users = new ArrayList<>();
		userInfoList.forEach(info -> {
			User user = new User(info.getUserId(), String.valueOf(info.getYear()), info.getName(),info.getPhoneSecret(), info.getBranch().getName(), info.getCompany().getName(), null);
			users.add(user);
		});
		return JsonUtils.toJsonNamingStrategy(users);
	}

	public String getOrgs(String name, Integer year, long regionId) {
		List<PartyCommitteeInfo> committeeInfoList = this.getCommitteeInfoList(regionId, year, null, name);
		List<PartyBranchInfo> branchInfoInfoList = this.getBranchInfoList(regionId, year, null, name);
		List<Org> orgs = new ArrayList<>();
		committeeInfoList.forEach(info -> {
			Org org = new Org(info.getOrganizationId(), String.valueOf(info.getYear()), info.getOrgName(), info.getOrgShortName(), 2, this.getOrgTypeStr(info.getOrgTypeChild()));
			orgs.add(org);
		});
		branchInfoInfoList.forEach(info -> {
			Org org = new Org(info.getOrganizationId(), String.valueOf(info.getYear()), info.getOrgName(), info.getOrgShortName(), 3, this.getOrgTypeStr(info.getOrgTypeChild()));
			orgs.add(org);
		});
		return JsonUtils.toJsonNamingStrategy(orgs);
	}


	/**
	 * 从mongoDB获取首页详情
	 * @param regionId
	 * @param year
	 * @return
	 */
	public IndexInfo getIndexInfo(Long regionId, Integer year) {
		Criteria criteria = Criteria.where("regionId").is(regionId).and("year").is(year);
		Query query = new Query(criteria);
		List<IndexInfo> infoList = this.mongoTemplate.find(query, IndexInfo.class);
		return infoList.size() > 0 ? infoList.stream().max(Comparator.comparing(IndexInfo::getCreateTime)).get() : null;
	}

	/**
	 * 从mongoDB获取党委详情
	 * @param regionId
	 * @param year
	 * @return
	 */
	public List<PartyCommitteeInfo> getCommitteeInfoList(Long regionId, Integer year, Long id, String name) {
		Criteria criteria = Criteria.where("regionId").is(regionId).and("year").is(year);
		if (null != id) {
			criteria.and("organizationId").is(id);
		}
		if (StringUtils.isNotBlank(name)) {
			Pattern pattern = Pattern.compile("^.*"+name+".*$", Pattern.CASE_INSENSITIVE);
			criteria.and("orgName").regex(pattern);
		}
		Query query = new Query(criteria);
		return this.mongoTemplate.find(query, PartyCommitteeInfo.class);
	}

	/**
	 * 从mongoDB获取党支部详情
	 * @param regionId
	 * @param year
	 * @return
	 */
	public List<PartyBranchInfo> getBranchInfoList(Long regionId, Integer year, Long id, String name) {
		Criteria criteria = Criteria.where("regionId").is(regionId).and("year").is(year);
		if (null != id) {
			criteria.and("organizationId").is(id);
		}
		if (StringUtils.isNotBlank(name)) {
			Pattern pattern = Pattern.compile("^.*"+name+".*$", Pattern.CASE_INSENSITIVE);
			criteria.and("orgName").regex(pattern);
		}
		Query query = new Query(criteria);
		return this.mongoTemplate.find(query, PartyBranchInfo.class);
	}

	/**
	 * 从mongoDB获取用户详情列表
	 * @param regionId
	 * @param year
	 * @return
	 */
	public List<UserInfo> getUserInfoList(Long regionId, Integer year, Long id, String name) {
		Criteria criteria = Criteria.where("regionId").is(regionId).and("year").is(year);
		if (null != id) {
			criteria.and("userId").is(id);
		}
		if (StringUtils.isNotBlank(name)) {
			Pattern pattern = Pattern.compile("^.*"+name+".*$", Pattern.CASE_INSENSITIVE);
			criteria.and("name").regex(pattern);
		}
		Query query = new Query(criteria);
		return this.mongoTemplate.find(query, UserInfo.class);
	}

	private String getOrgTypeStr(Integer orgTypeChild) {
		String orgType;
		if (this.orgTypeConfig.getCommunistChild().contains(orgTypeChild)) {
			orgType = "党委";
		} else if(this.orgTypeConfig.getGeneralBranchChild().contains(orgTypeChild)) {
			orgType = "党总支";
		} else {
			orgType = "党支部";
		}
		return orgType;
	}
}
