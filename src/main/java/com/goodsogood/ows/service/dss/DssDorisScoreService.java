package com.goodsogood.ows.service.dss;

import com.goodsogood.ows.configuration.ScoreManagerConfig;
import com.goodsogood.ows.mapper.doris.IndexOrgScoreMapper;
import com.goodsogood.ows.mapper.doris.IndexUserScoreMapper;
import com.goodsogood.ows.mapper.score.CemDorisIndexMapper;
import com.goodsogood.ows.mapper.score.ScoreDetailOtherMapper;
import com.goodsogood.ows.mapper.score.ScoreRuleExplainMapper;
import com.goodsogood.ows.mapper.user.PartyGroupMapper;
import com.goodsogood.ows.model.db.doris.IndexOrgScoreEntity;
import com.goodsogood.ows.model.db.doris.IndexUserScoreEntity;
import com.goodsogood.ows.model.db.score.CemDorisIndexEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.db.user.PartyGroupEntity;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Log4j2
public class DssDorisScoreService {
    private final ScoreRuleExplainMapper scoreRuleExplainMapper;
    private final IndexUserScoreMapper indexUserScoreMapper;
    private final IndexOrgScoreMapper indexOrgScoreMapper;
    private final OrgService orgService;
    private final UserService userService;
    private final ScoreDetailOtherMapper scoreDetailOtherMapper;
    private final CemDorisIndexMapper cemDorisIndexMapper;
    private final PartyGroupMapper partyGroupMapper;
    public final StringRedisTemplate redisTemplate;

    private final static Long regionId = 86L;

    private final ScoreManagerConfig scoreManagerConfig;

    @Autowired
    public DssDorisScoreService(ScoreRuleExplainMapper scoreRuleExplainMapper, IndexUserScoreMapper indexUserScoreMapper, IndexOrgScoreMapper indexOrgScoreMapper, OrgService orgService, UserService userService, ScoreDetailOtherMapper scoreDetailOtherMapper, CemDorisIndexMapper cemDorisIndexMapper, PartyGroupMapper partyGroupMapper, StringRedisTemplate redisTemplate,
                                ScoreManagerConfig scoreManagerConfig) {
        this.scoreRuleExplainMapper = scoreRuleExplainMapper;
        this.indexUserScoreMapper = indexUserScoreMapper;
        this.indexOrgScoreMapper = indexOrgScoreMapper;
        this.orgService = orgService;
        this.userService = userService;
        this.scoreDetailOtherMapper = scoreDetailOtherMapper;
        this.cemDorisIndexMapper = cemDorisIndexMapper;
        this.partyGroupMapper = partyGroupMapper;
        this.redisTemplate = redisTemplate;
        this.scoreManagerConfig = scoreManagerConfig;
    }


    public void insertUserHis(Integer year) {
        int maxMonth = year == LocalDate.now().getYear() ? LocalDate.now().getMonthValue() - 1 : 12;
        for (int i = 1; i <= maxMonth; i++) {
            String dataMonthStr = i <= 9 ? "0" + i : "" + i;
            insertHisUserMonth(year + "-" + dataMonthStr);
        }
    }


    /**
     * 跑人员历史数据-指定月 yyyy-mm
     */
    public void insertHisUserMonth(String dataMonthStr) {
        Integer dataMonth = Integer.valueOf(dataMonthStr.substring(0, 4) + dataMonthStr.substring(5, 7));
        List<IndexUserScoreEntity> scoreOtherList = scoreDetailOtherMapper.sumUserScore(dataMonthStr, dataMonth);
        if (!CollectionUtils.isEmpty(scoreOtherList)) {
            indexUserScoreMapper.insertList(scoreOtherList);
        }
    }


    public void insertOrgHis(Integer year) {
        int maxMonth = year == LocalDate.now().getYear() ? LocalDate.now().getMonthValue() - 1 : 12;
        for (int i = 1; i <= maxMonth; i++) {
            String dataMonthStr = i <= 9 ? "0" + i : "" + i;
            insertHisOrgMonth(year + "-" + dataMonthStr);
        }
    }

    /**
     * 跑机构历史数据-指定月
     */
    public void insertHisOrgMonth(String dataMonthStr) {
        Integer dataMonth = Integer.valueOf(dataMonthStr.substring(0, 4) + dataMonthStr.substring(5, 7));
        //党组织
        List<IndexOrgScoreEntity> scoreOrgList = scoreDetailOtherMapper.sumOrgScore(dataMonthStr, dataMonth);
        List<IndexOrgScoreEntity> scoreGroupList = scoreDetailOtherMapper.sumGroupScore(dataMonthStr, dataMonth);
        if (!CollectionUtils.isEmpty(scoreOrgList)) {
            indexOrgScoreMapper.insertList(scoreOrgList);
        }
        if (!CollectionUtils.isEmpty(scoreGroupList)) {
            indexOrgScoreMapper.insertList(scoreGroupList);
        }
    }


    //完整批量写入，适用于外部接口调用
    public void insertOuterOrgScore(List<IndexOrgScoreEntity> list) {
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(i -> {
                createOrgCommon(i);
            });
        }
        indexOrgScoreMapper.insertList(list);
    }

    /**
     * 机构-公共字段处理
     *
     * @param i
     */
    private void createOrgCommon(IndexOrgScoreEntity i) {
        CemDorisIndexEntity ruleExplainEntity = getParentScoreType(i.getRuleId());
        i.setScoreType(ruleExplainEntity == null ? 999 : ruleExplainEntity.getScoreType());
        i.setParentScoreType(ruleExplainEntity == null ? 999 : ruleExplainEntity.getParentScoreType());
        i.setOrgName(getOrgName(i.getOrgId(), i.getScoreOrgType()));
        i.setCreateTime(LocalDateTime.now());
    }


    //完整批量写入，适用于外部接口调用
    public void insertOutUserScore(List<IndexUserScoreEntity> list) {
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(i -> {
                createUserCommon(i);
            });
        }
        indexUserScoreMapper.insertList(list);
    }

    /**
     * 人员-公共字段处理
     *
     * @param i
     */

    private void createUserCommon(IndexUserScoreEntity i) {
        CemDorisIndexEntity ruleExplainEntity = getParentScoreType(i.getRuleId());
        i.setScoreType(ruleExplainEntity == null ? 999 : ruleExplainEntity.getScoreType());
        i.setParentScoreType(ruleExplainEntity == null ? 999 : ruleExplainEntity.getParentScoreType());
        UserInfoBase userInfoBase = null;
        try {
            userInfoBase = userService.userBaseInfo(i.getUserId(), null, null, regionId);

        } catch (Exception e) {
            log.error("获取用户基本信息失败:" + e.getMessage());
        }
        i.setUserName(userInfoBase == null ? "未知" : userInfoBase.getUserName());
        i.setOrgId(userInfoBase == null ? 999 : userInfoBase.getOrgId() == null ? 999 : userInfoBase.getOrgId());
        i.setCreateTime(LocalDateTime.now());
    }


    //批量写入
    public void batchInsertUserScore(List<IndexUserScoreEntity> list) {
        if (!CollectionUtils.isEmpty(list)) {
            indexUserScoreMapper.insertList(list);
        }
    }


    public void batchInsertOrgScore(List<IndexOrgScoreEntity> list) {
        if (!CollectionUtils.isEmpty(list)) {
            indexOrgScoreMapper.insertList(list);
        }
    }

    /**
     * @param ruleId    积分类型id
     * @param userId
     * @param score
     * @param dataMonth yyyyMM
     * @param ruleId
     */
    public IndexUserScoreEntity createUserScore(Integer ruleId, Long userId, Long score, Integer dataMonth) {
        IndexUserScoreEntity userIndex = new IndexUserScoreEntity();
        userIndex.setRuleId(ruleId);
        userIndex.setUserId(userId);
        userIndex.setScore(score);
        userIndex.setDataMonth(dataMonth);
        createUserCommon(userIndex);
        return userIndex;
    }


    /**
     * @param ruleId       积分类型id
     * @param orgId
     * @param score
     * @param dataMonth    yyyyMM
     * @param orgId
     * @param scoreOrgType 1.党树组织  2.党组  不传默认为1
     */
    public IndexOrgScoreEntity createOrgScore(Integer ruleId, Long orgId, Long score, Integer dataMonth, Integer scoreOrgType) {
        IndexOrgScoreEntity orgIndex = new IndexOrgScoreEntity();
        orgIndex.setRuleId(ruleId);
        orgIndex.setOrgId(orgId);
        orgIndex.setScore(score);
        orgIndex.setDataMonth(dataMonth);
        orgIndex.setScoreOrgType(scoreOrgType);
        createOrgCommon(orgIndex);
        return orgIndex;
    }

    //根据scoreType去找score_type parent_score_type
//    @Cacheable(cacheNames = {"score_rule_entity"}, key = "#root.methodName + '-' + #ruleId")
//    public ScoreRuleExplainEntity getParentScoreType(Integer ruleId) {
//        Example example = new Example(ScoreRuleExplainEntity.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("ruleExplainId", ruleId);
//        example.selectProperties("scoreType", "parentScoreType", "ruleId");
//        return scoreRuleExplainMapper.selectOneByExample(example);
//    }
    @Cacheable(cacheNames = {"score_rule_entity"}, key = "#root.methodName + '-' + #ruleId")
    public CemDorisIndexEntity getParentScoreType(Integer ruleId) {
        Example example = new Example(CemDorisIndexEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ruleId", ruleId);
        example.selectProperties("scoreType", "parentScoreType", "ruleId");
        return cemDorisIndexMapper.selectOneByExample(example);
    }

    /**
     * 根据id和机构类型获取其name，党组织和党组
     *
     * @param orgId
     * @param orgType 1-党组织  2-党组
     * @return
     */

    private String getOrgName(Long orgId, Integer orgType) {
        String key = "mt_get_org_name:" + orgId + ":" + orgType;
        if (redisTemplate.hasKey(key)) {
            return redisTemplate.opsForValue().get(key);
        }
        if (orgType == 1) {//党组织
            OrganizationEntity orgInfo = orgService.getById(orgId);
            if (orgInfo == null) {
                return "未知";
            }
            String name = orgInfo.getName() == null ? "未知" : orgInfo.getName();
            redisTemplate.opsForValue().set(key, name, 1, TimeUnit.DAYS);
            return name;
        }
        PartyGroupEntity partyGroupEntity = partyGroupMapper.selectByPrimaryKey(orgId);
        if (partyGroupEntity == null) {
            return "未知";
        }
        String name = partyGroupEntity.getName() == null ? "未知" : partyGroupEntity.getName();
        redisTemplate.opsForValue().set(key, name, 1, TimeUnit.DAYS);
        return name;

    }

    /**
     * 根据dataMonth或者ruleId或者组织或者用户id获取积分记录
     *
     * @param flag      1:组织 2:用户
     * @param dataMonth 时间 yyyyMM 为空则不按月份查询
     * @param ruleId    积分类型id 为空则不按积分类型查询
     * @param id        flag为1时为组织id，flag为2时为用户id
     * @return
     */
    public Integer removeScoreRecord(Integer flag, String dataMonth, Long ruleId, Long id) {
        Example example = new Example(flag == 1 ? IndexOrgScoreEntity.class : IndexUserScoreEntity.class);
        example.setTableName(flag == 1 ? scoreManagerConfig.getIndexOrgDorisTable() : scoreManagerConfig.getIndexUserDorisTable());
        Example.Criteria criteria = example.createCriteria();
        if (flag == 1) {
            criteria.andEqualTo("orgId", id);
        } else {
            criteria.andEqualTo("userId", id);
        }
        if (!dataMonth.isEmpty()) {
            criteria.andEqualTo("dataMonth", dataMonth);
        }
        if (ruleId != null) {
            criteria.andEqualTo("ruleId", ruleId);
        }
        return flag == 1 ? indexOrgScoreMapper.deleteByExample(example) : indexUserScoreMapper.deleteByExample(example);
    }
}
