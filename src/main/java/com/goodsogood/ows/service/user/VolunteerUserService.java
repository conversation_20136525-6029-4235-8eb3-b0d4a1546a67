package com.goodsogood.ows.service.user;

import com.goodsogood.ows.mapper.user.VolunteerUserMapper;
import com.goodsogood.ows.model.db.user.VolunteerUserEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 志愿者用户服务类
 *
 * <AUTHOR>
 * @date 2020/11/10
 */
@Service
@Log4j2
public class VolunteerUserService {

    private static final String CACHE_KEY = "USER_TO_VOLUNTEER_USER_ID:%s";
    private final VolunteerUserMapper volunteerUserMapper;
    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    public VolunteerUserService(VolunteerUserMapper volunteerUserMapper,
                                StringRedisTemplate stringRedisTemplate) {
        this.volunteerUserMapper = volunteerUserMapper;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 根据用户id换取志愿者id
     *
     * @param userId
     * @return
     */
    public Long getVolunteerUserId(Long userId) {
        if (userId == null) {
            return -1L;
        }
        String cacheKey = String.format(CACHE_KEY, userId);
        if (stringRedisTemplate.hasKey(cacheKey)) {
            return Long.valueOf(stringRedisTemplate.opsForValue().get(cacheKey));
        }
        Example example = new Example(VolunteerUserEntity.class);
        example.selectProperties("volunteerUserId").createCriteria().andEqualTo("userId", userId);
        VolunteerUserEntity volunteerUserEntity = volunteerUserMapper.selectOneByExample(example);
        if (volunteerUserEntity == null) {
            volunteerUserEntity = new VolunteerUserEntity();
            volunteerUserEntity.setVolunteerUserId(-1L);
        }
        stringRedisTemplate.opsForValue().set(cacheKey, volunteerUserEntity.getVolunteerUserId().toString(), 30, TimeUnit.MINUTES);
        return volunteerUserEntity.getVolunteerUserId();
    }

    /**
     * 批量获取志愿者id
     * key = volunteerUserId，value = userId
     *
     * @param userIds
     * @return
     */
    public Map<Long, Long> getVolunteerUserId(List<Long> userIds) {
        Example example = new Example(VolunteerUserEntity.class);
        example.selectProperties("volunteerUserId", "userId")
                .createCriteria()
                .andIn("userId", userIds);
        List<VolunteerUserEntity> volunteerUserEntities = volunteerUserMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(volunteerUserEntities)) {
            Map<Long, Long> collect = volunteerUserEntities.stream().collect(Collectors.toMap(VolunteerUserEntity::getVolunteerUserId, v -> v.getUserId()));
            return collect;
        }
        return null;
    }
}
