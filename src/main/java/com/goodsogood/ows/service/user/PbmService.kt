package com.goodsogood.ows.service.user

import com.goodsogood.ows.common.Constants
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.PbmWorkDataConfig
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper.SysHeader
import com.goodsogood.ows.mapper.sas.FusionItemMapper
import com.goodsogood.ows.mapper.user.UserOrgAndCorpMapper
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity
import com.goodsogood.ows.model.mongodb.fusion.*
import com.goodsogood.ows.model.mongodb.pbm.*
import com.goodsogood.ows.model.vo.pbm.PbmFusionVo
import com.goodsogood.ows.model.vo.tbcFusion.PbmFusionForm
import com.goodsogood.ows.model.vo.tbcFusion.TargetFusionVo
import com.goodsogood.ows.service.pbm.CalculatingTimeUtils
import com.goodsogood.ows.service.pbm.TimeRange
import com.goodsogood.ows.service.pbm.fusion.impl.Mt7PartyGroupImpl
import com.goodsogood.ows.utils.ArithUtils
import com.goodsogood.ows.utils.NewExcelUtil
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.isEqualTo
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Service
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.*
import java.util.regex.Pattern
import java.util.stream.Collectors
import javax.servlet.http.HttpServletResponse
import javax.validation.constraints.NotBlank
import kotlin.math.abs


/**
 *
 * <AUTHOR>
 * @Date 2022-06-21 19:19:00
 * @Description PbmService
 *
 */
@Service
class PbmService @Autowired constructor(
    private val myMongoTemplate: MyMongoTemplate,
    private val pbmWorkDataConfig: PbmWorkDataConfig,
    private val userOrgAndCorpMapper: UserOrgAndCorpMapper,
    private val userMongoService: UserMongoService,
    private val mt7PartyGroupImpl: Mt7PartyGroupImpl,
    private val fusionItemMapper: FusionItemMapper

) {
    val log: Logger = LogManager.getLogger(PbmService::class.java)

    @Value("\${user-option.politicalCode}")
    private val politicalCode: @NotBlank String? = null

    /**
     * 查询用户/组织业务工作详情列表
     * @param queryId 用户ID/组织ID
     * @param calDate yyyy-MM
     * @param queryType 1-用户 2-组织
     */
    fun findDetails(
        queryId: Long?,
        calDate: String?,
        queryType: Int?,
        type: Int?,
        header: SysHeader
    ): MutableList<*>? {
        val year = calDate.toString().substring(0, 4).toInt()
        val month = calDate.toString().substring(5).toInt()
        val whereKey: String = when (queryType) {
            1 -> "userId"
            else -> "orgId"
        }
        val criteria = Criteria.where(whereKey).`is`(queryId)
            .and("year").`is`(year)
            .and("month").`is`(month)
            .and("regionId").`is`(header.regionId)
            .and("type").`is`(type)
        // 党小组成员才展示 按时参加党小组会 指标项
        when (queryType) {
            1 -> {
                val isExist = this.userOrgAndCorpMapper.findOrgGroupMember(queryId, calDate)
                if (Objects.isNull(isExist)) criteria.and("workItemId").nin(this.pbmWorkDataConfig.workItemIds!!)
            }
        }
        val query = Query(criteria)
        query.fields().include(
            "regionId", "workItemId", "workItemName", "cycle", "year", "criterion", "week",
            "month", "rankValue", "workResult", "resultCompare", "remark"
        )
        query.with(Sort.by(Sort.Order(Sort.Direction.ASC, "workItemId")))
        return when (queryType) {
            1 -> this.myMongoTemplate.find(query, UserWorkDetailInfo::class.java)
            else -> this.myMongoTemplate.find(query, OrgWorkDetailInfo::class.java)
        }
    }

    /**
     * 党员散点图
     * @param type 所属序列 0-全部 1-卷烟营销
     * @param calDate  查询年月 yyyy-MM
     */
    fun findScatterPlot(
        type: Int?,
        calDate: String?,
        header: SysHeader
    ): ScatterPlotForm? {
        // 查询X轴党建积分min和max以及Y轴业务积分min和max，以及中位数
        val criteria = Criteria.where("date").`is`(calDate)
            .and("regionId").`is`(header.regionId)
            .and("type").`is`(type)
        val query = Query(criteria)
        val orgUserKitInfo = this.myMongoTemplate.findOne(query, PbmOrgUserKitInfo::class.java)
        // 查询具体党员业务积分和党建积分以及基本信息
        val politicalList = mutableListOf<Int>()
        this.politicalCode?.split(",")?.forEach { run { politicalList.add(it.toInt()) } }
        val userCriteria = Criteria.where("date").`is`(calDate)
            .and("regionId").`is`(header.regionId)
            .and("politicalType").`in`(politicalList)
        if (type == 1) userCriteria.and("sequence").`is`(type)
        val partyMemberQuery = Query(userCriteria)
        val partyMemberList = this.myMongoTemplate.find(partyMemberQuery, PbmUserKitInfo::class.java)
        // 构建返回值
        val result = ScatterPlotForm()
        // 党建积分中位数
        val pMedian = orgUserKitInfo?.allPartyMedian?.p ?: 0.0
        // 业务积分中位数
        val bMedian = orgUserKitInfo?.allPartyMedian?.b ?: 0.0
        // 党建积分最小值
        val pMin = orgUserKitInfo?.partyBuildExtreme?.min ?: 0
        // 党建积分最大值
        val pMax = orgUserKitInfo?.partyBuildExtreme?.max ?: 0
        // 业务积分最小值
        val bMin = orgUserKitInfo?.businessExtreme?.min ?: 0
        // 业务积分最大值
        val bMax = orgUserKitInfo?.businessExtreme?.max ?: 0
        // 党建积分最小值与中位数差值
        val pMinMinus = abs(pMedian - pMin)
        // 党建积分最大值与中位数差值
        val pMaxMinus = abs(pMedian - pMax)
        val x = mutableListOf<Long>()
        // 党建积分最大值与中位数差值 大于 党建积分最小值与中位的差值
        if (pMaxMinus > pMinMinus) {
            x.add((pMedian - pMaxMinus).toLong())
            x.add(pMax)
        } else {
            x.add(pMin)
            x.add((pMedian + pMaxMinus).toLong())
        }
        result.xAxis = x

        val y = mutableListOf<Long>()
        // 业务积分最小值与中位数差值
        val bMinMinus = abs(bMedian.minus(bMin))
        // 业务积分最大值与中位数差值
        val bMaxMinus = abs(bMedian.minus(bMax))
        // 党建积分最大值与中位数差值 大于 党建积分最小值与中位的差值
        if (bMaxMinus > bMinMinus) {
            y.add((bMedian - bMaxMinus).toLong())
            y.add(bMax)
        } else {
            y.add(bMin)
            y.add((bMedian + bMaxMinus).toLong())
        }
        result.yAxis = y

        // 中位数
        orgUserKitInfo!!.allPartyMedian.let {
            val median = mutableListOf<Long>()
            it.p?.toLong()?.let { p -> median.add(p) }
            it.b?.toLong()?.let { b -> median.add(b) }
            result.median = median
        }
        // 用户基本信息
        val pointList = mutableListOf<PointForm>()
        partyMemberList.forEach {
            run {
                val point = PointForm()
                point.name = it.userName
                point.phone = it.phone
                point.org = it.unitName
                point.value?.add(it.partyBuild)
                point.value?.add(it.business)
                pointList.add(point)
            }
        }
        result.point = pointList
        return result
    }

    /**
     * 党员-拟合度散点图
     * @param type 所属序列 0-本单位 1-卷烟营销
     * @param calDate  查询年月 yyyy-MM
     * @param unitId 单位ID
     */
    fun findFitScatterPlot(
        type: Int?,
        calDate: String?,
        unitId: Long?,
        header: SysHeader
    ): MutableList<FitScatterPlotForm>? {
        val result = mutableListOf<FitScatterPlotForm>()
        val key: String = when (type) {
            0 -> "unitId"
            else -> "sequence"
        }
        val value: Long? = when (type) {
            0 -> unitId
            else -> type?.toLong()
        }
        val criteria = Criteria.where("date").`is`(calDate)
            .and("regionId").`is`(header.regionId)
            .and(key).`is`(value)
        val query = Query(criteria)
        val userKitInfo = this.myMongoTemplate.find(query, PbmUserKitInfo::class.java)
        userKitInfo.forEach {
            run {
                val fitScatterPlotForm = FitScatterPlotForm()
                fitScatterPlotForm.name = it.userName
                fitScatterPlotForm.phone = it.phone
                fitScatterPlotForm.nhd = it.goodnessOfKit
                val chartData: MutableList<Double> = mutableListOf()
                it.partyBuildTotalLog.let { chartData.add(it) } // 党建工作积分
                it.businessTotalLog.let { chartData.add(it) } // 业务工作积分
                fitScatterPlotForm.chartData = chartData
                result.add(fitScatterPlotForm)
            }
        }
        return result
    }

    /**
     * 单位-拟合度散点图
     * @param calDate yyyy-MM
     */
    fun findUnitScatterPlot(
        calDate: String?,
        header: SysHeader
    ): MutableList<FitScatterPlotForm>? {
        val result = mutableListOf<FitScatterPlotForm>()
        val criteria = Criteria.where("regionId").`is`(header.regionId)
            .and("date").`is`(calDate)
        val query = Query(criteria)
        val unitKits = this.myMongoTemplate.find(query, PbmUnitKitInfo::class.java)
        unitKits.forEach {
            val unit = FitScatterPlotForm()
            unit.name = it.unitName
            unit.nhd = it.goodnessOfKit
            val chartData: MutableList<Double> = mutableListOf()
            it.partyBuildTotalLog.let { partyBuildTotalLog -> chartData.add(partyBuildTotalLog) } // 党建
            it.businessTotalLog.let { businessTotalLog -> chartData.add(businessTotalLog) } // 业务
            unit.chartData = chartData
            result.add(unit)
        }
        return result
    }

    /**
     * 单位-拟合度散点图
     * @param unitId 单位ID
     */
    fun findUnitScatterList(
        calDate: String?,
        unitId: Long?,
        header: SysHeader
    ): MutableList<UnitScatterPlotForm>? {
        val result = mutableListOf<UnitScatterPlotForm>()
        val criteria = Criteria.where("regionId").`is`(header.regionId)
            .and("date").`is`(calDate)
            .and("unitId").`is`(unitId)
        val query = Query(criteria)
        val unitKits = this.myMongoTemplate.findOne(query, PbmUnitKitInfo::class.java)
        when {
            Objects.nonNull(unitKits) -> {
                // 拟合度
                val kForm = UnitScatterPlotForm()
                kForm.type = Constants.TYPE_K
                kForm.sefValue = unitKits?.goodnessOfKit
                kForm.rankNum = unitKits?.rank?.k?.toInt()
                kForm.avgNum = unitKits?.avg?.k
                kForm.maxNum = unitKits?.high?.k
                kForm.sef = unitKits?.self?.k
                result.add(kForm)
                // 党建工作
                val pForm = UnitScatterPlotForm()
                pForm.type = Constants.TYPE_P
                pForm.sefValue = unitKits?.partyBuildTotalLog
                pForm.rankNum = unitKits?.rank?.p?.toInt()
                pForm.avgNum = unitKits?.avg?.p
                pForm.maxNum = unitKits?.high?.p
                pForm.sef = unitKits?.self?.p
                result.add(pForm)
                // 业务工作
                val bForm = UnitScatterPlotForm()
                bForm.type = Constants.TYPE_B
                bForm.sefValue = unitKits?.businessTotalLog
                bForm.rankNum = unitKits?.rank?.b?.toInt()
                bForm.avgNum = unitKits?.avg?.b
                bForm.maxNum = unitKits?.high?.b
                bForm.sef = unitKits?.self?.b
                result.add(bForm)
            }
        }
        return result
    }

    /**
     * 单位-拟合度柱状图
     * @param calDate yyyy-MM
     */
    fun findUnitColumnarChart(
        calDate: String?,
        header: SysHeader
    ): MutableList<ColumnarChartForm>? {
        val result = mutableListOf<ColumnarChartForm>()
        // 查询所有单位拟合度
        val criteria = Criteria.where("regionId").`is`(header.regionId)
            .and("date").`is`(calDate)
        val query = Query(criteria)
        val allFit = this.myMongoTemplate.find(query, PbmUnitKitInfo::class.java)
        allFit.forEach {
            val chart = ColumnarChartForm()
            chart.areaName = if (it.unitName?.endsWith("（分公司）") == true) {
                it.unitName?.replace("（分公司）", "")
            } else if (it.unitName?.endsWith("（公司）机关") == true) {
                it.unitName?.replace("（公司）机关", "")
            } else {
                it.unitName
            }
            chart.num = it.goodnessOfKit
            result.add(chart)
        }
        return result
    }

    /**
     * 获取各单位党务-业务工作情况对比数据
     * @param calDate 查询日期 yyyy-MM
     * @param header 请求头
     * @return List of FindUnitMixScatterVO
     */
    fun findUnitMixScatter(
        calDate: String,
        header: SysHeader
    ): List<FindUnitMixScatterVO> {
        val result = mutableListOf<FindUnitMixScatterVO>()
        // 查询所有单位拟合度
        val criteria = Criteria.where("regionId").`is`(header.regionId)
            .and("date").`is`(calDate)
        val query = Query(criteria)
        this.myMongoTemplate.find(query, PbmUnitKitInfo::class.java).forEach {
            val vo = FindUnitMixScatterVO()
            with(vo) {
                this.unitName = it.unitName
                this.nhd = it.goodnessOfKit
                this.chartData?.add(it.partyBuildTotalLog)
                this.chartData?.add(it.businessTotalLog)
            }
            result.add(vo)
        }
        return result
    }

    /**
     * 获取党务-业务工作趋势图
     * @param unitId 单位id
     * @param calDate 日期月份
     * @param header header
     * @return FindUnitMixLineBarVO
     */
    fun findUnitMixLineBar(
        unitId: Long,
        calDate: List<String>,
        header: SysHeader
    ): FindUnitMixLineBarVO {
        log.debug("findUnitMixLineBar -> unitId:${unitId} , calDate:${calDate}")
        val result = FindUnitMixLineBarVO()
        // 查询对应单位拟合度的趋势
        // 简单点的做法，多次循环查询
        calDate.forEach {
            val criteria = Criteria.where("regionId").`is`(header.regionId)
                .and("unitId").`is`(unitId).and("date").`is`(it)
            val data = this.myMongoTemplate.find(Query(criteria).limit(1), PbmUnitKitInfo::class.java).getOrNull(0)
            result.xData!!.add(it)
            result.p!!.add(data?.avg?.p)
            result.b!!.add(data?.avg?.b)
            result.k!!.add(data?.avg?.k)
        }
        return result
    }

    /**
     * 散点图了解详情
     * @param type 0-全部
     * @param calDate 日期月份
     * @param header header
     * @return List<ScatterListForm>
     */
    fun findScatterList(
        type: Int,
        calDate: String,
        header: SysHeader
    ): List<ScatterListForm> {
        val result = mutableListOf<ScatterListForm>()
        // 查询党建积分前100的党员
        val userCriteria = Criteria.where("date").`is`(calDate)
            .and("regionId").`is`(header.regionId)
        when {
            type > 0 -> userCriteria.and("sequence").`is`(type)
        }
        // 业务积分前100名
        val businessQuery = Query(userCriteria)
        businessQuery.with(Sort.by(Sort.Order(Sort.Direction.DESC, "business"))).limit(100)
        val businessList = this.myMongoTemplate.find(businessQuery, PbmUserKitInfo::class.java)
        // 党建积分前100名
        val partyQuery = Query(userCriteria)
        partyQuery.with(Sort.by(Sort.Order(Sort.Direction.DESC, "partyBuild"))).limit(100)
        val partyList = this.myMongoTemplate.find(businessQuery, PbmUserKitInfo::class.java)
        // 求交集
        val politicalList = mutableListOf<Int>()
        this.politicalCode?.split(",")?.forEach { run { politicalList.add(it.toInt()) } }
        val resultList = partyList.stream().filter(businessList::contains).collect(Collectors.toList())
        resultList.forEach {
            when {
                politicalList.contains(it.politicalType) -> {
                    val scatterListForm = ScatterListForm()
                    scatterListForm.userName = it.userName
                    scatterListForm.unitName = it.unitName
                    // 查询职位
                    val user = it.userId?.let { userId -> this.userMongoService.getUserById(userId) }
                    scatterListForm.title = user!!.title
                    result.add(scatterListForm)
                }
            }
        }
        return result
    }

    fun getUserKitTrend(
        userId: Long, year: Int, month: Int,
        headers: HttpHeaders
    ): GetUserKitResponse {
        val response = GetUserKitResponse()
        val beforeMonth = CalculatingTimeUtils.calculateBeforeMonth(year, month)
        beforeMonth.sort()
        response.xAxis = beforeMonth
        val criteria = Criteria.where("userId").`is`(userId).and("date").`in`(beforeMonth)
        val userKitInfos = this.myMongoTemplate.find(Query(criteria), PbmUserKitInfo::class.java)
        userKitInfos.sortBy { it.date }
        val index = userKitInfos.size - 1
        if (userKitInfos.isNotEmpty()) {
            response.kit = userKitInfos[index].goodnessOfKit
            userKitInfos.map { it.goodnessOfKit }
            // 查询本单位
            val criteria1 = Criteria.where("unitId").`is`(userKitInfos[index].unitId)
                .and("type").`is`(0)
                .and("date").`in`(beforeMonth)
            val orgUserKitInfos = this.myMongoTemplate.find(Query(criteria1), PbmOrgUserKitInfo::class.java)
            if (orgUserKitInfos.isNotEmpty()) {
                orgUserKitInfos.sortBy { it.date }
                response.unitTotal = orgUserKitInfos[index].selfKitAvg.beNum
                response.unitRank = userKitInfos[index].selfRank.k?.toInt()
                response.sequenceRank = userKitInfos[index].sequenceRank.k?.toInt()
                response.d3 = orgUserKitInfos.map { it.selfKitAvg.be }.toMutableList()
            }
            // 查询人员所属序列的数据
            val criteria2 = Criteria.where("unitId").`is`(userKitInfos[index].unitId)
                .and("type").`is`(userKitInfos[index].sequence)
                .and("date").`in`(beforeMonth)
            val orgUserKitInfos2 = this.myMongoTemplate.find(Query(criteria2), PbmOrgUserKitInfo::class.java)
            if (orgUserKitInfos2.isNotEmpty()) {
                orgUserKitInfos2.sortBy { it.date }
                response.d2 = orgUserKitInfos2.map { it.allKitAvg.be }.toMutableList()
                response.sequenceTotal = orgUserKitInfos2[orgUserKitInfos2.size - 1].allBusinessAvg.beNum
            }
        }
        response.d1 = userKitInfos.map { it.goodnessOfKit }.toMutableList()
        return response
    }

    /**
     * 查询详情
     */
    fun queryDetail(
        unitId: Long,
        itemId: Long,
        year: Int,
        month: Int?,
        isParty: Int?,
        headers: HttpHeaders
    ): PbmFusionForm {
        val form = PbmFusionForm()
        val timeRange = getBaseTimeRange(month)
        val unitIds = listOf(unitId)
        val clazz = getFusionClass(itemId)
        //查询详情
        val criteria = Criteria.where("unitId").`in`(unitIds).and("itemId").`is`(itemId).and("year").`is`(year)
        criteria.and("month").gte(timeRange.startMonth!!).lte(timeRange.endMonth!!)
        //党群绩效对比
        if (16 == itemId.toInt()) {
            if (isParty != null) {
                criteria.and("isPartyMember").`is`(isParty)
            }
        }
        val query = Query(criteria)
        //双重组织生活以及领导联系基层基础数据并未存是否满足要求
        if (9 == itemId.toInt()) {
            val detailList = myMongoTemplate.find(query, DualLifeDetail::class.java, pbmWorkDataConfig.fusionMongo)
            val userList = detailList.groupBy { it.userId }
                .map { (k, v) ->
                    val joinNum = v.sumOf { it.joinNum }
                    DualLifeDetail(userId = k, joinNum = joinNum, userName = v[0].userName, hasSatisfy = (joinNum >= 2))
                }
            form.dataList = userList
        } else if (10 == itemId.toInt()) {
            val detailList =
                myMongoTemplate.find(query, LeadersContactDetail::class.java, pbmWorkDataConfig.fusionMongo)
            val userList = detailList.groupBy { "" + it.userId + "_" + it.contactOrg }
                .map { (_, v) ->
                    val joinNum = v.sumOf { it.joinNum }
                    LeadersContactDetail(
                        userId = v[0].userId,
                        joinNum = joinNum,
                        userName = v[0].userName,
                        contactOrg = v[0].contactOrg,
                        hasSatisfy = (joinNum >= 1)
                    )
                }
            form.dataList = userList

        } else if (5 == itemId.toInt()) {
            // 云区组织建设
            val ecpOrgDetail = myMongoTemplate.find(query, EcpOrgDetail::class.java, pbmWorkDataConfig.fusionMongo)
            // 处理展示名称
            ecpOrgDetail.forEach {
                when (it.monopolyNum) {
                    0 -> it.monopolyViewName = "0"
                    else -> it.monopolyViewName = it.monopolyNum.toString().plus("(").plus(it.monopolyName).plus(")")
                }
                when (it.marketingNum) {
                    0 -> it.marketingViewName = "0"
                    else -> it.marketingViewName = it.marketingNum.toString().plus("(").plus(it.marketingName).plus(")")
                }
                when (it.synthesisNum) {
                    0 -> it.synthesisViewName = "0"
                    else -> it.synthesisViewName = it.synthesisNum.toString().plus("(").plus(it.synthesisName).plus(")")
                }
                when (it.tobaccoNum) {
                    0 -> it.tobaccoViewName = "0"
                    else -> it.tobaccoViewName = it.tobaccoNum.toString().plus("(").plus(it.tobaccoName).plus(")")
                }
            }
            form.dataList = ecpOrgDetail as List<FusionBaseData>?
        } else if (6 == itemId.toInt()) {
            //党员干部交流
            val partyTalkList =
                myMongoTemplate.find(query, MemberChangeDetail::class.java, pbmWorkDataConfig.fusionMongo)
            partyTalkList.forEach {
                it.userName = it.userName + "(" + it.phone + ")"
            }
            form.dataList = partyTalkList as List<FusionBaseData>?
        } else {
            val list = myMongoTemplate.find(query, clazz, pbmWorkDataConfig.fusionMongo)
            form.dataList = list
        }
        //查询指标得分
        val resultTime = getResTimeRange(month, year)
        val resCriteria = Criteria.where("unitId").`in`(unitIds)
            .and("itemId").`is`(itemId).and("year").`is`(resultTime.startYear)
            .and("month").isEqualTo(resultTime.month)
        val resQuery = Query(resCriteria)
        val resultList = myMongoTemplate.find(resQuery, FusionItemData::class.java)
        form.itemData = if (resultList.isEmpty()) FusionItemData() else resultList[0]
        return form
    }


    fun getFusionClass(itemId: Long): Class<out FusionBaseData> {
        when (itemId.toInt()) {
            4 -> return ExcellentDetail::class.java
            5 -> return EcpOrgDetail::class.java
            6 -> return MemberChangeDetail::class.java
            7 -> return MeetingDetail::class.java
            8 -> return LeaderDutyDetail::class.java
            9 -> return DualLifeDetail::class.java
            10 -> return LeadersContactDetail::class.java
            11 -> return MeetingDetail::class.java
            12 -> return MeetingDetail::class.java
            13 -> return EcpActivityDetail::class.java
            //需要修改 党建引领业务
            14 -> return FusionBaseData::class.java
            15 -> return EcpJoinDetail::class.java
            16 -> return PartyGroupCompareTargetDetail::class.java
            17 -> return SystemUsageDetail::class.java
        }
        return FusionBaseData::class.java
    }


    /**
     * month 1~12代表正常月份，null-年度 13-上半年 14-下半年 15-第一季度 16-第二季度 17-第三季度 18-第四季度
     */
    //查询不规范的数据，统一为1000年1月。比如2023年3月查询3月的数据。
    fun getResTimeRange(month: Int?, year: Int): TimeRange {
        val timeRange = TimeRange()
        val nowYear = LocalDate.now().year
        val nowMonth = LocalDate.now().monthValue
        if (month == null) {//年度
            // 如果查询的是上一年,则为当前年最新月份
            when (year) {
                nowYear -> {
                    timeRange.startYear = 1000
                    timeRange.month = 1
                    return timeRange
                }

                (nowYear - 1) -> {//不能是当前年
                    timeRange.startYear = year + 1
                    timeRange.month = LocalDate.now().monthValue
                    return timeRange
                }

                else -> {//如果查询的是上上年等，则为上年12月跑出的为准
                    timeRange.startYear = year + 1
                    timeRange.month = 12
                    return timeRange
                }
            }
        }
        //如果是月度，则查询下一个月的数据，因为下个月跑上个月的数
        if (month <= 12) {
            //月度
            if (year == nowYear && month >= nowMonth) {
                timeRange.startYear = 1000
                timeRange.month = 1
            } else if (month == 12) {
                timeRange.month = 1
                timeRange.startYear = year + 1

            } else {
                timeRange.startYear = year
                timeRange.month = month + 1
            }
            return timeRange
        }
        if (month == 13) {
            //上半年
            if (year == nowYear && nowMonth <= 6) {
                //上半年,不能是当前年
                timeRange.startYear = 1000
                timeRange.month = 1
            } else {
                timeRange.startYear = year
                timeRange.month = if (nowMonth < 12) nowMonth else 12
            }
            return timeRange
        }
        if (month == 14) {
            //下半年
            if (year >= LocalDate.now().year) {
                timeRange.startYear = 1000
                timeRange.month = 1
            } else {
                timeRange.startYear = year + 1
                timeRange.month = if (nowMonth >= 6) 6 else nowMonth
            }
            return timeRange
        }
        //第一季度
        if (month == 15) {
            val nowQuart =
                if (nowMonth % 3 == 0) nowMonth / 3 else nowMonth / 3 + 1
            if (year == nowYear) {
                if (nowQuart == 1) {
                    //如果当前也是1季度
                    timeRange.startYear = 1000
                    timeRange.month = 1
                } else {
                    timeRange.startYear = year
                    timeRange.month = if (nowMonth >= 6) 6 else nowMonth
                }

            } else {
                timeRange.startYear = year
                timeRange.month = 6
            }
            return timeRange
        }
        if (month == 16) {
            //第二季度
            val nowQuart =
                if (nowMonth % 3 == 0) nowMonth / 3 else nowMonth / 3 + 1
            if (year == LocalDate.now().year) {
                if (nowQuart <= 2) {
                    timeRange.startYear = 1000
                    timeRange.month = 1
                } else {
                    timeRange.startYear = year
                    timeRange.month = if (nowMonth >= 9) 9 else nowMonth
                }
            } else {
                timeRange.startYear = year
                timeRange.month = 9
            }
            return timeRange
        }
        if (month == 17) {
            //第三季度
            val nowQuart =
                if (nowMonth % 3 == 0) nowMonth / 3 else nowMonth / 3 + 1
            if (year == nowYear) {
                if (nowQuart <= 3) {
                    timeRange.startYear = 1000
                    timeRange.month = 1
                } else {
                    timeRange.startYear = year
                    timeRange.month = if (nowMonth <= 12) nowMonth else 12
                }
            } else {
                timeRange.startYear = year
                timeRange.month = 12
            }
            return timeRange
        }
        if (month == 18) {
            //第四季度
            if (nowMonth % 3 == 0) nowMonth / 3 else nowMonth / 3 + 1
            if (year == LocalDate.now().year) {
                timeRange.startYear = 1000
                timeRange.month = 1
            } else {
                timeRange.startYear = year + 1
                timeRange.month = if (nowMonth >= 3) 3 else nowMonth
            }
        }
        return timeRange
    }


    fun getBaseTimeRange(month: Int?): TimeRange {
        val timeRange = TimeRange()
        var startMonth = 0
        var endMonth = 0
        if (month == null) {
            startMonth = 1
            endMonth = 12
        } else if (month <= 12) {
            startMonth = month
            endMonth = month
        } else if (month == 13) {
            //上半年
            startMonth = 1
            endMonth = 6
        } else if (month == 14) {
            //下半年
            startMonth = 7
            endMonth = 12
        } else if (month == 15) {
            //第一季度
            startMonth = 1
            endMonth = 3
        } else if (month == 16) {
            //第二季度
            startMonth = 4
            endMonth = 6
        } else if (month == 17) {
            //第三季度
            startMonth = 7
            endMonth = 9
        } else if (month == 18) {
            //第四季度
            startMonth = 10
            endMonth = 12
        }
        timeRange.startMonth = startMonth
        timeRange.endMonth = endMonth
        return timeRange
    }

    fun queryTotalDetail(unitId: Long, yearMonth: String, headers: HttpHeaders): List<PbmFusionItemEntity> {
        val yearMonthStr = yearMonth + "-01"
        val format: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        val date = LocalDate.parse(yearMonthStr, format).plusMonths(1)
        val dateStr = format.format(date)
        val year = dateStr.substring(0, 4).toInt()
        val month = dateStr.substring(5, 7).toInt()
        val list = fusionItemMapper.selectAll()
        val resCriteria = Criteria.where("unitId").`in`(listOf(unitId)).and("year").`is`(year)
        resCriteria.and("month").`is`(month)
        val resQuery = Query(resCriteria)
        val resultList = myMongoTemplate.find(resQuery, FusionItemData::class.java)
        if (resultList.isEmpty()) {
            return list
        }
        val map = resultList.groupBy { it.itemId }
        list.forEach {
            if (map[it.fusionItemId]?.isNotEmpty() == true) {
                it.score = map[it.fusionItemId]!![0].score
                it.result = map[it.fusionItemId]!![0].result
            }
        }
        return list
    }

    fun targetFusion(year: Int, month: Int, headers: HttpHeaders): MutableList<TargetFusionVo> {
        val resultList = mutableListOf<TargetFusionVo>()
        val scoreList = queryScoreResult(year, month, null, null)
        val scoreMap = scoreList.groupBy { it.itemId }
        val itemList = fusionItemMapper.selectAll()
        itemList.forEach { item ->
            val vo = TargetFusionVo()
            vo.itemId = item.fusionItemId
            vo.name = item.name
            vo.baseScore = item.baseScore
            vo.cycle = item.cycle
            val fusionItemData = scoreMap[item.fusionItemId]
            vo.avgScore =
                if (fusionItemData.isNullOrEmpty()) 0.0 else ArithUtils.round(
                    fusionItemData.map { it.score }.average(),
                    2
                )
            if (fusionItemData != null) {
                vo.dataList = fusionItemData.sortedWith(
                    // 根据排序正序排，null放到后面
                    compareBy<FusionItemData> { it.rank == null }
                        // 根据itemScore倒序排序
                        .thenByDescending { it.itemScore ?: 0.0 }
                        // score
                        .thenByDescending { it.score })
                    .slice(0 until minOf(3, fusionItemData.size))
            } else {
                vo.dataList = listOf()
            }
            resultList.add(vo)
        }
        return resultList
    }

    private fun queryScoreResult(
        year: Int,
        month: Int,
        itemId: Long?,
        unitName: String?
    ): MutableList<FusionItemData> {
        val resCriteria = Criteria.where("year").`is`(year)
        resCriteria.and("month").`is`(month)
        if (itemId != null) {
            resCriteria.and("itemId").`is`(itemId)
        }
        if (unitName != null && unitName != "") {
            val yearPattern = Pattern.compile("^.*$unitName.*$")
            resCriteria.and("unitName").regex(yearPattern)
        }
        val resQuery = Query(resCriteria)
        return myMongoTemplate.find(resQuery, FusionItemData::class.java)
    }

    /**
     * month: 1~12代表正常月份，null-年度 13-上半年 14-下半年 15-第一季度 16-第二季度 17-第三季度 18-第四季度
     */
    fun targetFusionDetail(
        year: Int,
        month: Int?,
        itemId: Long,
        unitName: String?,
        headers: HttpHeaders
    ): TargetFusionVo {
        val vo = TargetFusionVo()
        val itemEntity = fusionItemMapper.selectByPrimaryKey(itemId)
        val timeRange = getResTimeRange(month, year)
        val list = queryScoreResult(timeRange.startYear!!, timeRange.month!!, itemId, null)
        //获取平均分
        val avgScore = if (list.isEmpty()) 0.0 else list.map { it.score }.average()
        vo.avgScore = ArithUtils.round(avgScore, 2)
        vo.name = itemEntity.name
        vo.baseScore = itemEntity.baseScore
        vo.description = itemEntity.description
        vo.rule = itemEntity.rule
        if (unitName.isNullOrBlank()) {
            vo.dataList = list.sortedWith(
                // 根据排序正序排，null放到后面
                compareBy<FusionItemData> { it.rank == null }
                    // 根据itemScore倒序排序
                    .thenByDescending { it.itemScore ?: 0.0 }
                    // score
                    .thenByDescending { it.score })
            return vo
        }
        vo.dataList = list.filter { it.unitName!!.contains(unitName) }.sortedWith(
            // 根据排序正序排，null放到后面
            compareBy<FusionItemData> { it.rank == null }
                // 根据itemScore倒序排序
                .thenByDescending { it.itemScore ?: 0.0 }
                // score
                .thenByDescending { it.score })
        return vo

    }


    fun targetFusionExport(
        year: Int,
        month: Int?,
        itemId: Long,
        unitName: String?,
        headers: HttpHeaders,
        response: HttpServletResponse
    ) {
        val vo = targetFusionDetail(year, month, itemId, unitName, headers)
        val dataList = vo.dataList
        try {
            //导出文件命名
            val bookName = createBookName(vo, year, month)
            NewExcelUtil.export(
                response, bookName, dataList
            ) { _: List<Any?>? ->
                val list: MutableList<MutableList<String>> =
                    ArrayList()
                val field = mutableListOf("单位名称", "结果", "排名")
                list.add(field)
                for (entity: FusionItemData in dataList) {
                    val innerList = mutableListOf<String>()
                    innerList.add(entity.unitName ?: "")
                    innerList.add(entity.result ?: "")
                    innerList.add(entity.score.toString())
                    list.add(innerList)
                }
                list
            }
        } catch (e: Exception) {
            log.error("导出失败" + e.message);
        }
    }

    /**
     * month 1~12代表正常月份，null-年度 13-上半年 14-下半年 15-第一季度 16-第二季度 17-第三季度 18-第四季度
     */
    private fun createBookName(vo: TargetFusionVo, year: Int, month: Int?): String {
        var name = vo.name
        if (month == null) {
            return name + "_" + year + "年"
        }
        if (month <= 12) {
            return name + "_" + year + "年" + month + "月"
        }
        if (month == 13) {
            return name + "_" + year + "年" + "上半年"
        }
        if (month == 14) {
            return name + "_" + year + "年" + "下半年"
        }
        if (month == 15) {
            return name + "_" + year + "年" + "第一季度"
        }
        if (month == 16) {
            return name + "_" + year + "年" + "第二季度"
        }
        if (month == 17) {
            return name + "_" + year + "年" + "第三季度"
        }
        if (month == 18) {
            return name + "_" + year + "年" + "第四季度"
        }
        return name
    }


    fun allFusion(year: Int, month: Int, headers: HttpHeaders): PbmFusionVo {
        val allFusion = PbmFusionVo()
        //提取最近12个月的融合度数据
        //时间跨度最大为2年，当前年  上年 上上年
        val yearList = listOf(year - 1, year)
        val dataList = findFusionData(yearList)
        if (dataList.isEmpty()) {
            return allFusion
        }
        val yearMap = dataList.groupBy { (it.year) }
        val curData = yearMap[year]
        allFusion.yearList = applyData(curData, 4)
        allFusion.maxList = applyData(curData, 2)
        allFusion.minList = applyData(curData, 3)

        // 上一年度平均分
        val fusionData = yearMap[year - 1]
        allFusion.lastYearList = applyData(fusionData, 4)
        // 更新时间
        val maxUpdateTime = dataList.mapNotNull { it.updateTime }.maxBy { it }
        allFusion.updateTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
            .format(maxUpdateTime.times(1000))
        return allFusion
    }

    /**
     * 查询计算融合度
     * @param unitId
     * @param year
     */
    private fun findFusionData(years: List<Int>): MutableList<FusionData> {
        val criteria = Criteria.where("year").`in`(years)
        val query = Query(criteria)
        return this.myMongoTemplate.find(query, FusionData::class.java)
    }


    /**
     * 查询数据更新时间
     * @param unitId
     * @param year
     */
    private fun findLastUpdateTime(year: Int, month: Int): String {
        val criteria = Criteria.where("year").`is`(year).and("month").`is`(month)
        val query = Query(criteria)
        val updateTime = this.myMongoTemplate.find(query, FusionData::class.java)[0].updateTime
        val format = SimpleDateFormat("yyyy-MM-dd HH:mm")
        //注意这里返回的是string类型
        return format.format(updateTime!! * 1000)
    }

    /**
     * 处理年度数据
     * @param yearData
     * @param type 1 - 求和， 2 - 求最大值，3 - 求最小值，4 - 求平均值
     */
    private fun applyData(yearData: List<FusionData>?, type: Int): List<String> {
        if (yearData.isNullOrEmpty()) {
            return mutableListOf()
        }
        val monthMap = yearData.sortedBy { it.month }.groupBy { it.month }
        return monthMap.mapValues { data ->
            when (type) {
                1 ->
                    ArithUtils.round(data.value.sumOf { it.fusion }, 2)

                2 ->
                    data.value.maxOf { it.fusion }

                3 ->
                    data.value.minOf { it.fusion }

                4 ->
                    ArithUtils.round(data.value.map { it.fusion }.average(), 2)

                else ->
                    throw ApiException("type有问题")
            }
        }.values.map { it.toString() }.toMutableList().apply {
            repeat(12 - this.size) {
                add("-")
            }
        }

    }

}

