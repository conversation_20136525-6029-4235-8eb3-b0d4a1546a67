package com.goodsogood.ows.service.user;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.common.MessageCommon;
import com.goodsogood.ows.common.MessageEnum;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.db.user.PartyBrandBase;
import com.goodsogood.ows.model.vo.MessageVO;
import com.goodsogood.ows.service.IMessageService;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Component(MessageCommon.BRAND)
public class PartyBuildingBrandService implements IMessageService {
    private final String keyName = "PARTY_BRAND_";
    private final String branchKey = "MESSAGE_BRANCH_";
    private final StringRedisTemplate redisTemplate;
    private final ObjectMapper objectMapper;
    private final OrgService orgService;

    public PartyBuildingBrandService(StringRedisTemplate redisTemplate, ObjectMapper objectMapper, OrgService orgService) {
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
        this.orgService = orgService;
    }

    @SneakyThrows
    @NotNull
    @Override
    public List<MessageVO> generateMessage(long regionId, long rootId, @Nullable Integer page, @Nullable Integer pageSize) {
        final String branchCacheKey = branchKey + regionId + "—" + page;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(branchCacheKey))) {
            final List<MessageVO> vos = objectMapper.readValue(redisTemplate.opsForValue().get(branchCacheKey), new TypeReference<List<MessageVO>>() {
            });
            if (CollectionUtils.isNotEmpty(vos)) {
                return vos;
            }
        }
        //查询所有下级orgIdList
        List<OrganizationEntity> organizationEntities = orgService.selectAllChildOrg(rootId);
        List<PartyBrandBase> tmpList = new ArrayList<>();
        if (Boolean.TRUE.equals(redisTemplate.hasKey(keyName + rootId))) {
            tmpList.add(objectMapper.readValue(redisTemplate.opsForValue().get(keyName + rootId), PartyBrandBase.class));
        }
        for (OrganizationEntity entity : organizationEntities) {
            if (Boolean.TRUE.equals(redisTemplate.hasKey(keyName + entity.getOrganizationId()))) {
                tmpList.add(objectMapper.readValue(redisTemplate.opsForValue().get(keyName + entity.getOrganizationId()), PartyBrandBase.class));
            }
        }
        List<PartyBrandBase> list = tmpList.subList((page - 1) * pageSize, (page - 1) * pageSize + pageSize);
        List<MessageVO> result = new ArrayList<>();
        list.forEach(base -> {
            MessageVO vo = new MessageVO();
            if (null != base.getPictureList().getBase().get(0).getUrl()) {
                List<String> file = new ArrayList<>();
                file.add(base.getPictureList().getBase().get(0).getUrl());
                vo.setFile(file);
            }
            vo.setText(base.getBrandName());
            vo.setType(MessageEnum.BRAND.getType());
            vo.setTypeName(MessageEnum.BRAND.getTypeName());
            Map map = new HashMap<>();
            map.put("id", base.getOrgId());
            vo.setExtend(map);
            result.add(vo);
        });
        // 保存缓存
        redisTemplate.opsForValue().set(branchCacheKey, objectMapper.writeValueAsString(result), 30, TimeUnit.MINUTES);
        return result;
    }
}
