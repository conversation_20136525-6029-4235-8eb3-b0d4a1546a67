package com.goodsogood.ows.service.user

import com.goodsogood.ows.config.SimpleApplicationConfigHelper
import com.goodsogood.ows.mapper.user.UserMapper
import com.goodsogood.ows.model.vo.ItemVO
import com.goodsogood.ows.model.vo.UserItemVO
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class UserStatisticalService(@Autowired val userMapper: UserMapper,
                             @Autowired val simpleApplicationConfigHelper: SimpleApplicationConfigHelper) {

    /**
     * 获取拥挤统计信息
     * @param reginId
     * @param type 1 -> 统计年龄维度
     *        type 2 -> 统计学历维度
     */
    fun getUserDistributed(regionId: Long, type: Int,excludeOrgIds:String) : MutableList<UserItemVO> {
        val result = mutableListOf<UserItemVO>()
        val orgData = simpleApplicationConfigHelper.getOrgByRegionId(regionId)
        var itemList = mutableListOf<ItemVO>()
        if (type == 1) {
            itemList = userMapper.selectUserAgeDistributed(orgData.orgId,excludeOrgIds)
        } else if (type == 2) {
            itemList = userMapper.selectUserEducationDistributed(orgData.orgId,excludeOrgIds)
        }
        val listMap = itemList.groupBy { it.item }
        listMap.forEach { (key, value) ->
            val list = value.filter { it.userId != null }.map { it.userId }.toMutableList()
            result.add(UserItemVO(key,null, list))
        }
        return result
    }
}