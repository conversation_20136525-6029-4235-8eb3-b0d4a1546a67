package com.goodsogood.ows.service.user

import com.goodsogood.ows.common.Utils
import com.goodsogood.ows.config.Region.OrgData
import com.goodsogood.ows.config.SimpleApplicationConfigHelper
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.OrgTypeConfig
import com.goodsogood.ows.mapper.user.OrganizationMapper
import com.goodsogood.ows.model.mongodb.user.Org
import com.goodsogood.ows.service.RegionService
import com.goodsogood.ows.utils.MapUtils
import org.apache.commons.lang.StringUtils
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service
import org.springframework.util.CollectionUtils
import java.util.concurrent.TimeUnit

/**
 *
 * <AUTHOR>
 * @createTime 2022年04月06日 14:13:00
 */
@Service
class OrgMongoService @Autowired constructor(
    private val mongoTemplate: MyMongoTemplate,
    private val redisTemplate: StringRedisTemplate,
    private val applicationConfigHelper: SimpleApplicationConfigHelper,
    private val organizationMapper: OrganizationMapper,
    private val regionService: RegionService,
    private val orgTypeConfig: OrgTypeConfig,
    private val orgService: OrgService
) {

    val log: Logger = LogManager.getLogger(OrgMongoService::class.java)

    companion object {
        const val SUPERVISE_CHECK_FIELDS = "SUPERVISE_CHECK_FIELDS_%s"
        const val CHECK_FIELDS = "'联系人','联系电话','党组织属地'"
        const val CACHE_TIME = 5L
    }

    /**
     * 动态字段相关判断逻辑
     */
    fun calOptionStr(orgId: Long, regionId: Long): String? {
        val resultList = mutableListOf<String>()
        val orgInfo = findOrgInMongoById(orgId, regionId)
        try {
            if (orgInfo != null) {
                log.debug(
                        "党支部:[{}] mongoInfo:[{}]",
                        orgId, Utils.toJson(orgInfo)
                )

                if (null == orgInfo.isRetire) {
                    resultList.add("是否离退休组织")
                }
                if (StringUtils.isBlank(orgInfo.orgAddress)) {
                    resultList.add("组织详细地址")
                }
                if (StringUtils.isBlank(orgInfo.orgCreateTime)) {
                    resultList.add("党组织建立时间")
                }

                val checkField: List<Map<*, *>?> = getCheckField(regionId)
                log.debug(
                        "OrgMongoService->党支部:[{}] 获取区县自定义检查字段[{}]",
                        orgId, Utils.toJson(checkField)
                )
                for (x in checkField) {
                    val fieldName = x?.get("fieldName") as String
                    val alias = x["alias"] as String
                    if (MapUtils.isEmpty(orgInfo.fields)
                            || StringUtils.isBlank(fieldName)
                            || StringUtils.isBlank(alias)
                            || StringUtils.isBlank(MapUtils.getString(orgInfo.fields, fieldName, null))
                    ) {
                        resultList.add(alias)
                    }
                }
                log.debug("OrgMongoService->党支部:[{}] 组织信息完整情况: {}", orgId, resultList)
            }
            return resultList.joinToString(separator = ",")
        }catch (ex:Exception){
            log.error("监督预警处理第一项发生异常",ex)
            return null
        }
    }


    /**
     * 获取区县检查对应的自定义字段
     */
    private fun getCheckField(regionId: Long): List<MutableMap<*, *>?> {
        val cacheKey = java.lang.String.format(SUPERVISE_CHECK_FIELDS, regionId)
        val value: String? = redisTemplate.opsForValue().get(cacheKey)
        if (StringUtils.isNotBlank(value)) {
            return Utils.fromJson(
                value,
                MutableList::class.java,
                MutableMap::class.java
            ) as MutableList<MutableMap<*, *>?>
        }
        val orgData: OrgData = applicationConfigHelper.getOrgByRegionId(regionId)
        val orgId = orgData.orgId
        val checkField: List<MutableMap<*, *>?> = organizationMapper.getCheckField(regionId, orgId, CHECK_FIELDS)
        if (CollectionUtils.isEmpty(checkField)) {
            return ArrayList()
        }
        redisTemplate.opsForValue().set(cacheKey, Utils.toJson(checkField), CACHE_TIME, TimeUnit.MINUTES)
        return checkField
    }

    private fun findOrgInMongoById(orgId: Long, regionId: Long): Org? {
        val query = Query.query(
            Criteria
                .where("regionId").`is`(regionId)
                .and("organizationId").`is`(orgId)
        )
        return mongoTemplate.findOne(query, Org::class.java)
    }
}