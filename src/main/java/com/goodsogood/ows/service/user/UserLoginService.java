package com.goodsogood.ows.service.user;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.ExcludeOrgConfig;
import com.goodsogood.ows.configuration.ExperienceConfig;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.mapper.experience.UserPartyEvalDetailMapper;
import com.goodsogood.ows.mapper.experience.UserPartyEvalPlanMapper;
import com.goodsogood.ows.mapper.user.MenuRouteMapper;
import com.goodsogood.ows.mapper.user.UserLoginLogMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.experience.UserPartyEvalDetailEntity;
import com.goodsogood.ows.model.db.experience.UserPartyEvalPlanEntity;
import com.goodsogood.ows.model.db.user.MenuRouteEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.model.mongodb.dss.*;
import com.goodsogood.ows.model.vo.GroupValue;
import com.goodsogood.ows.model.vo.LoginValue;
import com.goodsogood.ows.model.vo.user.UserLoginRankForm;
import com.goodsogood.ows.service.cilckSpy.ClickSpyService;
import com.goodsogood.ows.service.impl.DssIndexBuilder;
import com.goodsogood.ows.service.impl.DssPartyBranchBuilder;
import com.goodsogood.ows.service.impl.DssPartyCommitteeBuilder;
import com.goodsogood.ows.service.impl.DssUserBuilder;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 用户登录服务层
 *
 * <AUTHOR>
 */
@Service
@Log4j2
public class UserLoginService implements DssIndexBuilder, DssPartyCommitteeBuilder,
        DssPartyBranchBuilder, DssUserBuilder {

    static final SimpleDateFormat MAX_DAY_FORMAT = new SimpleDateFormat("MM月dd日");

    private final SimpleApplicationConfigHelper applicationConfigHelper;
    private final StringRedisTemplate redisTemplate;
    private final ObjectMapper objectMapper;
    private final UserLoginLogMapper userLoginLogMapper;
    private final ClickSpyService clickSpyService;
    private final MenuRouteMapper menuRouteMapper;
    private final UserMapper userMapper;
    private final OrgTypeConfig orgTypeConfig;
    private final ExcludeOrgConfig excludeOrgConfig;
    private final ExperienceConfig experienceConfig;
    private final UserPartyEvalPlanMapper userPartyEvalPlanMapper;
    private final UserPartyEvalDetailMapper userPartyEvalDetailMapper;

    @Autowired
    public UserLoginService(SimpleApplicationConfigHelper applicationConfigHelper,
                            StringRedisTemplate redisTemplate,
                            ObjectMapper objectMapper,
                            UserLoginLogMapper userLoginLogMapper,
                            ClickSpyService clickSpyService,
                            MenuRouteMapper menuRouteMapper,
                            UserMapper userMapper,
                            OrgTypeConfig orgTypeConfig,
                            ExcludeOrgConfig excludeOrgConfig, ExperienceConfig experienceConfig, UserPartyEvalPlanMapper userPartyEvalPlanMapper, UserPartyEvalDetailMapper userPartyEvalDetailMapper) {
        this.applicationConfigHelper = applicationConfigHelper;
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
        this.userLoginLogMapper = userLoginLogMapper;
        this.clickSpyService = clickSpyService;
        this.menuRouteMapper = menuRouteMapper;
        this.userMapper = userMapper;
        this.orgTypeConfig = orgTypeConfig;
        this.excludeOrgConfig = excludeOrgConfig;
        this.experienceConfig = experienceConfig;
        this.userPartyEvalPlanMapper = userPartyEvalPlanMapper;
        this.userPartyEvalDetailMapper = userPartyEvalDetailMapper;
    }

    /**
     * 生成决策辅助首页 - 生成用户活跃度
     *
     * @param info 决策辅助首页实体类
     * @return
     */
    @Override
    public IndexInfo buildIndex(IndexInfo info) {
        Integer year = info.getYear();
        Long orgId = info.getRootId();
        Long regionId = info.getRegionId();
        OrgUserActive orgUserActive = this.getOrgUserActive(orgId, regionId, year);
        info.setUserActive(orgUserActive);
        return info;
    }

    @Override
    public PartyCommitteeInfo buildPartyCommittee(PartyCommitteeInfo info) {
        Integer year = info.getYear();
        Long orgId = info.getOrganizationId();
        Long regionId = info.getRegionId();
        OrgUserActive orgUserActive = this.getOrgUserActive(orgId, regionId, year);
        info.setUserActive(orgUserActive);
        return info;
    }

    @Override
    public List<PartyCommitteeInfo> buildPartyCommitteeList(List<PartyCommitteeInfo> infoList) {
        List<Long> orgIds = infoList.stream().map(PartyCommitteeInfo::getOrganizationId).collect(Collectors.toList());
        Integer year = infoList.get(0).getYear();
        List<LoginValue> userLoginTotalList;
        List<UserLoginRankForm> maxDayLoginList;
        List<UserLoginRankForm> maxMonthLoginList;
        if (DateUtils.getYear(new Date()).equals(year)) {
            userLoginTotalList = this.userLoginLogMapper.getUserLoginTotalByOrgList(orgIds, year);
            maxDayLoginList = this.userLoginLogMapper.getMaxDayLoginByOrgList(orgIds, year);
            maxMonthLoginList = this.userLoginLogMapper.getMaxMonthLoginByOrgList(orgIds, year);
        } else {
            String dateMonth = year + "-12";
            userLoginTotalList = this.userLoginLogMapper.getHistoryUserLoginTotalByOrgList(orgIds, year, dateMonth);
            maxDayLoginList = this.userLoginLogMapper.getHistoryMaxDayLoginByOrgList(orgIds, year, dateMonth);
            maxMonthLoginList = this.userLoginLogMapper.getHistoryMaxMonthLoginByOrgList(orgIds, year, dateMonth);
        }
        infoList.forEach(
                info -> {
                    OrgUserActive active = new OrgUserActive();
                    LoginValue loginTotal =
                            userLoginTotalList.stream()
                                    .filter(item -> info.getOrganizationId().equals(item.getId()))
                                    .findFirst()
                                    .orElse(new LoginValue());
                    UserLoginRankForm maxDayForm =
                            maxDayLoginList.stream()
                                    .filter(item -> info.getOrganizationId().equals(item.getOrgId()))
                                    .findFirst()
                                    .orElse(new UserLoginRankForm());
                    UserLoginRankForm maxMonthForm =
                            maxMonthLoginList.stream()
                                    .filter(item -> info.getOrganizationId().equals(item.getOrgId()))
                                    .findFirst()
                                    .orElse(new UserLoginRankForm());
                    // 年度最高日访问量
                    MaxDayVisit maxDayVisit = new MaxDayVisit();
                    if (null != maxDayForm.getDate()) {
                        String maxDay = MAX_DAY_FORMAT.format(maxDayForm.getDate());
                        maxDayVisit.setDay(maxDay);
                        maxDayVisit.setNum(maxDayForm.getNum());
                    }
                    // 年度最高月访问量
                    MaxMonthVisit maxMonthVisit = new MaxMonthVisit();
                    if (null != maxMonthForm.getMonth()) {
                        maxMonthVisit.setMonth(maxMonthForm.getMonth());
                        maxMonthVisit.setNum(maxMonthForm.getNum());
                    }
                    // 最常访问模块
                    List<PieObject> visitModule = this.getOrgVisitModule(info.getOrganizationId(), year);
                    // 月均访问
                    int monthVisitNum =
                            DateUtils.getYear(new Date()).equals(year)
                                    ? Math.round(
                                    (float) loginTotal.getTotal() / LocalDateTime.now().getMonth().getValue())
                                    : Math.round((float) loginTotal.getTotal() / 12);
                    active.setYearVisitNum(loginTotal.getTotal());
                    active.setMaxDayVisit(maxDayVisit);
                    active.setMaxMonthVisit(maxMonthVisit);
                    active.setVisitModule(visitModule);
                    active.setAverageMonthVisitNum(monthVisitNum);
                    info.setUserActive(active);
                });
        return infoList;
    }

    @Override
    public PartyBranchInfo buildPartyBranch(PartyBranchInfo info) {
        Integer year = info.getYear();
        Long orgId = info.getOrganizationId();
        Long regionId = info.getRegionId();
        OrgUserActive orgUserActive = this.getOrgUserActive(orgId, regionId, year);
        info.setUserActive(orgUserActive);
        return info;
    }

    @Override
    public List<PartyBranchInfo> buildPartyBranchList(List<PartyBranchInfo> infoList) {
        List<Long> orgIds = infoList.stream().map(PartyBranchInfo::getOrganizationId).collect(Collectors.toList());
        Integer year = infoList.get(0).getYear();
        List<LoginValue> userLoginTotalList;
        List<UserLoginRankForm> maxDayLoginList;
        List<UserLoginRankForm> maxMonthLoginList;
        if (DateUtils.getYear(new Date()).equals(year)) {
            userLoginTotalList = this.userLoginLogMapper.getUserLoginTotalByOrgList(orgIds, year);
            maxDayLoginList = this.userLoginLogMapper.getMaxDayLoginByOrgList(orgIds, year);
            maxMonthLoginList = this.userLoginLogMapper.getMaxMonthLoginByOrgList(orgIds, year);
        } else {
            String dateMonth = year + "-12";
            userLoginTotalList = this.userLoginLogMapper.getHistoryUserLoginTotalByOrgList(orgIds, year, dateMonth);
            maxDayLoginList = this.userLoginLogMapper.getHistoryMaxDayLoginByOrgList(orgIds, year, dateMonth);
            maxMonthLoginList = this.userLoginLogMapper.getHistoryMaxMonthLoginByOrgList(orgIds, year, dateMonth);
        }
        infoList.forEach(info -> {
            OrgUserActive active = new OrgUserActive();
            LoginValue loginTotal = userLoginTotalList.stream().filter(item -> info.getOrganizationId().equals(item.getId())).findFirst().orElse(new LoginValue());
            UserLoginRankForm maxDayForm = maxDayLoginList.stream().filter(item -> info.getOrganizationId().equals(item.getOrgId())).findFirst().orElse(new UserLoginRankForm());
            UserLoginRankForm maxMonthForm = maxMonthLoginList.stream().filter(item -> info.getOrganizationId().equals(item.getOrgId())).findFirst().orElse(new UserLoginRankForm());
            // 年度最高日访问量
            MaxDayVisit maxDayVisit = new MaxDayVisit();
            if (null != maxDayForm.getDate()) {
                String maxDay = MAX_DAY_FORMAT.format(maxDayForm.getDate());
                maxDayVisit.setDay(maxDay);
                maxDayVisit.setNum(maxDayForm.getNum());
            }
            // 年度最高月访问量
            MaxMonthVisit maxMonthVisit = new MaxMonthVisit();
            if (null != maxMonthForm.getMonth()) {
                maxMonthVisit.setMonth(maxMonthForm.getMonth());
                maxMonthVisit.setNum(maxMonthForm.getNum());
            }
            // 最常访问模块
            List<PieObject> visitModule = this.getOrgVisitModule(info.getOrganizationId(), year);

            int monthVisitNum = DateUtils.getYear(new Date()).equals(year) ?
                    Math.round((float) loginTotal.getTotal() / LocalDateTime.now().getMonth().getValue()) :
                    Math.round((float) loginTotal.getTotal() / 12);
            active.setYearVisitNum(loginTotal.getTotal());
            active.setMaxDayVisit(maxDayVisit);
            active.setMaxMonthVisit(maxMonthVisit);
            active.setVisitModule(visitModule);
            active.setAverageMonthVisitNum(monthVisitNum);

            info.setUserActive(active);
        });
        return infoList;
    }

    @Override
    public UserInfo buildUser(UserInfo info) {
        UserActive userActive = new UserActive();
        int curYear = LocalDateTime.now().getYear();
        int month = LocalDateTime.now().getMonth().getValue();
        Integer year = info.getYear();
        Long userId = info.getUserId();
        // 查询年度累计访问量
        Integer userCount = this.userLoginLogMapper.getUserLoginCountByUser(userId, year);
        // 年度月均访问量
        int monthVisitNum;
        if (curYear == year) {
            monthVisitNum = Math.round((float) userCount / month);
        } else {
            monthVisitNum = Math.round((float) userCount / 12);
        }
        // 最常访问模块
        List<PieObject> visitModule = this.getUserVisitModule(userId, year);
        // 封装数据
        userActive.setYearVisitNum(userCount);
        userActive.setAverageMonthVisitNum(monthVisitNum);
        userActive.setVisitModule(visitModule);
        info.setUserActive(userActive);
        return info;
    }

    @Override
    public List<UserInfo> buildUserList(List<UserInfo> infoList) {
        List<Long> userIds = infoList.stream().map(UserInfo::getUserId).collect(Collectors.toList());
        Integer year = infoList.get(0).getYear();
        int curYear = LocalDateTime.now().getYear();
        int month = LocalDateTime.now().getMonth().getValue();
        List<LoginValue> userCountList = this.userLoginLogMapper.getUserLoginCountByUserList(userIds, year);
        infoList.forEach(info -> {
            UserActive userActive = new UserActive();
            Integer userCount = 0;
            LoginValue loginValue = userCountList.stream().filter(item -> info.getUserId().equals(item.getId())).findFirst().orElse(null);
            if (null != loginValue) {
                userCount = loginValue.getTotal();
            }
            // 年度月均访问量
            int monthVisitNum;
            if (curYear == year) {
                monthVisitNum = Math.round((float) userCount / month);
            } else {
                monthVisitNum = Math.round((float) userCount / 12);
            }
            // 最常访问模块
            List<PieObject> visitModule = this.getUserVisitModule(info.getUserId(), year);
            // 封装数据
            userActive.setYearVisitNum(userCount);
            userActive.setAverageMonthVisitNum(monthVisitNum);
            userActive.setVisitModule(visitModule);
            info.setUserActive(userActive);
        });
        return infoList;
    }

    /**
     * 获取组织内部访问
     *
     * @param orgId
     * @param year
     * @return
     */
    private OrgUserActive getOrgUserActive(Long orgId, Long regionId, Integer year) {
        Region.OrgData orgData = this.applicationConfigHelper.getOrgByRegionId(regionId);
        int month = LocalDateTime.now().getMonth().getValue();
        OrgUserActive active = new OrgUserActive();
        // 查询年度累计访问量
        Integer userLoginTotal;
        // 年度最高日访问量
        UserLoginRankForm maxDayRankForm;
        // 年度最高月访问量
        UserLoginRankForm maxMonthRankForm;
        // 年度月均访问量
        int monthVisitNum;
        if (orgId.equals(orgData.getOrgId())) {
            userLoginTotal = this.userLoginLogMapper.getUserLoginTotal(regionId, year);
            maxDayRankForm = this.userLoginLogMapper.getMaxDayLogin(regionId, year);
            maxMonthRankForm = this.userLoginLogMapper.getMaxMonthLogin(regionId, year);
            if (DateUtils.getYear(new Date()).equals(year)) {
                monthVisitNum = Math.round((float) userLoginTotal / month);
            } else {
                monthVisitNum = Math.round((float) userLoginTotal / 12);
            }
        } else {
            if (DateUtils.getYear(new Date()).equals(year)) {
                userLoginTotal = this.userLoginLogMapper.getUserLoginTotalByOrg(orgId, year);
                maxDayRankForm = this.userLoginLogMapper.getMaxDayLoginByOrg(orgId, year);
                maxMonthRankForm = this.userLoginLogMapper.getMaxMonthLoginByOrg(orgId, year);
                monthVisitNum = Math.round((float) userLoginTotal / month);
            } else {
                String dateMonth = year + "-12";
                userLoginTotal = this.userLoginLogMapper.getHistoryUserLoginTotalByOrg(orgId, year, dateMonth);
                maxDayRankForm = this.userLoginLogMapper.getHistoryMaxDayLoginByOrg(orgId, year, dateMonth);
                maxMonthRankForm = this.userLoginLogMapper.getHistoryMaxMonthLoginByOrg(orgId, year, dateMonth);
                monthVisitNum = Math.round((float) userLoginTotal / 12);
            }
        }
        // 年度最高日访问量
        MaxDayVisit maxDayVisit = new MaxDayVisit();
        if (null != maxDayRankForm) {
            String maxDay = MAX_DAY_FORMAT.format(maxDayRankForm.getDate());
            maxDayVisit.setDay(maxDay);
            maxDayVisit.setNum(maxDayRankForm.getNum());
        }
        // 年度最高月访问量
        MaxMonthVisit maxMonthVisit = new MaxMonthVisit();
        if (null != maxMonthRankForm) {
            maxMonthVisit.setMonth(maxMonthRankForm.getMonth());
            maxMonthVisit.setNum(maxMonthRankForm.getNum());
        }
        // 最常访问模块
        List<PieObject> visitModule = this.getOrgVisitModule(orgId, year);
        // 封装数据
        active.setYearVisitNum(userLoginTotal);
        active.setAverageMonthVisitNum(monthVisitNum);
        active.setMaxDayVisit(maxDayVisit);
        active.setMaxMonthVisit(maxMonthVisit);
        active.setVisitModule(visitModule);
        return active;
    }

    /**
     * 获取组织的统计访问模块数据
     *
     * @param orgId
     * @param year
     * @return
     */
    public List<PieObject> getOrgVisitModule(Long orgId, Integer year) {
        List<UserEntity> userList = this.userMapper.findUserList(orgId, 1, this.excludeOrgConfig.getOrgIds(), this.orgTypeConfig.getNoStatisticsChild(), null);
        Map<String, PieObject> map = new HashMap<>();
        userList.forEach(user -> {
            List<PieObject> visitList = this.getUserVisitModule(user.getUserId(), year);
            for (PieObject pie : visitList) {
                String key = pie.getName();
                if (map.containsKey(key)) {
                    Integer oldNum = map.get(key).getValue();
                    if (null != oldNum) {
                        pie.setValue(oldNum + pie.getValue());
                    }
                }
                map.put(key, pie);
            }
        });
        return map.values().stream().sorted(Comparator.comparingInt(PieObject::getValue).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取人员的登录模块统计数据
     *
     * @param userId
     * @param year
     * @return
     */
    public List<PieObject> getUserVisitModule(Long userId, Integer year) {
        List<PieObject> pieList = new ArrayList<>();
        String redisKey = Constants.DSS_USER_INFO_KEY + year + "_" + userId;
        try {
            if (this.redisTemplate.hasKey(redisKey)) {
                String json = this.redisTemplate.opsForValue().get(redisKey);
                pieList = this.objectMapper.readValue(json, new TypeReference<List<PieObject>>() {
                });
            } else {
                List<GroupValue> valueList = this.clickSpyService.getUserClickSpy(userId, year);
                List<MenuRouteEntity> entityList = this.getMenuRouteList();
                List<PieObject> finalPieList = pieList;
                entityList.forEach(menuRoute -> {
                    PieObject object = new PieObject();
                    object.setName(menuRoute.getName());
                    int total = 0;
                    for (GroupValue value : valueList) {
                        if (value.get_id().contains(menuRoute.getMenuRoute())) {
                            total += value.getTotal().intValue();
                        }
                    }
                    object.setValue(total);
                    finalPieList.add(object);
                });
                pieList = pieList.stream().sorted(Comparator.comparingInt(PieObject::getValue).reversed()).collect(Collectors.toList());
                String json = this.objectMapper.writeValueAsString(pieList);
                this.redisTemplate.opsForValue().set(redisKey, json, 10, TimeUnit.HOURS);
            }
        } catch (JsonProcessingException e) {
            log.error("JSON转换异常", e);
        }
        return pieList;
    }

    /**
     * 获取前端路由表
     *
     * @return
     */
    public List<MenuRouteEntity> getMenuRouteList() {
        List<MenuRouteEntity> menuRouteList = new ArrayList<>();
        try {
            if (this.redisTemplate.hasKey(Constants.MENU_ROUTE_KEY)) {
                String json = this.redisTemplate.opsForValue().get(Constants.MENU_ROUTE_KEY);
                menuRouteList = this.objectMapper.readValue(json, new TypeReference<List<MenuRouteEntity>>() {
                });
            } else {
                menuRouteList = this.menuRouteMapper.selectAll();
                String json = this.objectMapper.writeValueAsString(menuRouteList);
                this.redisTemplate.opsForValue().set(Constants.MENU_ROUTE_KEY, json, 10, TimeUnit.HOURS);
            }
        } catch (JsonProcessingException e) {
            log.error("JSON转换异常", e);
        }
        return menuRouteList;
    }

    /**
     * 计算联系登录天数
     *
     * @param userPartyEvalDetailEntity
     */
    public void calContinuouslyDays(UserPartyEvalDetailEntity userPartyEvalDetailEntity) {
        try {
            // 查询当月连续登录次数-坚持登录数智党建平台
            String queryMonth = userPartyEvalDetailEntity.getDateMonth().toString().substring(0, 4).concat("-")
                    .concat(userPartyEvalDetailEntity.getDateMonth().toString().substring(4, 6));
            // 查询当月连续登录次数
            Integer maxDays = this.userLoginLogMapper.findMaxByLoginId(userPartyEvalDetailEntity.getUserId(), queryMonth);
            // 默认1星
            AtomicReference<Integer> star = new AtomicReference<>(1);
            if (Objects.isNull(maxDays)) {
                maxDays = 0;
            } else if (maxDays >= this.experienceConfig.getMaxDay()) {
                star.set(this.experienceConfig.getMaxStar());
            }
            List<ExperienceConfig.StarLevel> starLevalList = this.experienceConfig.getStarLevels();
            Integer finalMaxDays = maxDays;
            starLevalList.forEach(days -> {
                if (days.getDays().contains(finalMaxDays)) {
                    star.set(days.getStar());
                }
            });
            // 设置星级
            userPartyEvalDetailEntity.setStar(star.get());
            // 如果没有达到5星，则设置锤炼计划
            if (!this.experienceConfig.getMaxStar().equals(star.get())) {
                UserPartyEvalPlanEntity plan = this.findPlanByRuleId(userPartyEvalDetailEntity.getRuleId(),
                        userPartyEvalDetailEntity.getRegionId());
                if (Objects.nonNull(plan)) {
                    userPartyEvalDetailEntity.setPlanId(plan.getPlanId());
                }
            }
            // 设置建议
            userPartyEvalDetailEntity.setParamType(Constants.PARAM_TYPE_EIGHT);
            // 查询建议参数
            userPartyEvalDetailEntity.setParamValue(this.userLoginLogMapper.findParamValue(userPartyEvalDetailEntity.getUserId(),
                    queryMonth,
                    this.experienceConfig.getStartTime(), this.experienceConfig.getEndTime()));
            // 设置状态为完成
            userPartyEvalDetailEntity.setCalStatus(Constants.CAL_STATUS_DONE);
            userPartyEvalDetailEntity.setUpdateTime(LocalDateTime.now());
            this.userPartyEvalDetailMapper.updateByPrimaryKey(userPartyEvalDetailEntity);
        }catch (Exception e){
            log.error("计算第26项出现异常", e);
        }
    }

    /**
     * 计算登录总天数
     *
     * @param userPartyEvalDetailEntity
     */
    public void calMonthTotalDays(UserPartyEvalDetailEntity userPartyEvalDetailEntity) {
        try {
            String month = userPartyEvalDetailEntity.getDateMonth().toString().substring(0, 4).concat("-")
                    .concat(userPartyEvalDetailEntity.getDateMonth().toString().substring(4, 6));
            // 查询当月登录总次数
            Integer totalDays = this.userLoginLogMapper.findTotalByLoginId(userPartyEvalDetailEntity.getUserId(), month);
            // 设置星级
            List<ExperienceConfig.StarLevel> starList = this.experienceConfig.getStarLevels();
            // 默认1星
            AtomicReference<Integer> starLevel = new AtomicReference<>(1);
            starList.stream().filter(s -> totalDays >= s.getMMin() && totalDays <= s.getMMax()).forEach(s -> {
                starLevel.set(s.getStar());
            });
            userPartyEvalDetailEntity.setStar(starLevel.get());
            // 如果没有达到5星，则设置锤炼计划
            if (!this.experienceConfig.getMaxStar().equals(starLevel.get())) {
                UserPartyEvalPlanEntity plan = this.findPlanByRuleId(userPartyEvalDetailEntity.getRuleId(),
                        userPartyEvalDetailEntity.getRegionId());
                if (Objects.nonNull(plan)) {
                    userPartyEvalDetailEntity.setPlanId(plan.getPlanId());
                }
            }
            // 设置建议
            userPartyEvalDetailEntity.setParamType(Constants.PARAM_TYPE_SEVEN);
            // 设置建议参数
            userPartyEvalDetailEntity.setParamValue(String.valueOf(totalDays));
            // 设置状态为完成
            userPartyEvalDetailEntity.setCalStatus(Constants.CAL_STATUS_DONE);
            userPartyEvalDetailEntity.setUpdateTime(LocalDateTime.now());
            this.userPartyEvalDetailMapper.updateByPrimaryKey(userPartyEvalDetailEntity);
        }catch (Exception e){
            log.error("计算第27项出现异常", e);
        }
    }

    /**
     * 计算当前季度登录天数
     *
     * @param userPartyEvalDetailEntity
     */
    public void calQuarterTotalDays(UserPartyEvalDetailEntity userPartyEvalDetailEntity) {
        try{
            // 年
            Integer year = Integer.valueOf(userPartyEvalDetailEntity.getDateMonth().toString().substring(0, 4));
            // 月
            Integer month = Integer.valueOf(userPartyEvalDetailEntity.getDateMonth().toString().substring(4, 6));
            // 属于那个季度
            List<Integer> months = new ArrayList<>();
            ExperienceConfig.Quarter quarter = this.experienceConfig.getQuarters();
            if (quarter.getFirst().contains(month)) {
                months = quarter.getFirst();
            } else if (quarter.getSecond().contains(month)) {
                months = quarter.getSecond();
            } else if (quarter.getThird().contains(month)) {
                months = quarter.getThird();
            } else if (quarter.getFour().contains(month)) {
                months = quarter.getFour();
            }
            // 查询当季度登录天数
            Integer quarTerDays = this.userLoginLogMapper.findQuarterAndYearTotalByLoginId(userPartyEvalDetailEntity.getUserId(),
                    year, months);
            // 设置星级
            List<ExperienceConfig.StarLevel> starList = this.experienceConfig.getStarLevels();
            // 默认1星
            AtomicReference<Integer> starLevel = new AtomicReference<>(1);
            starList.stream().filter(s -> quarTerDays >= s.getQMin() && quarTerDays <= s.getQMax()).forEach(s -> {
                starLevel.set(s.getStar());
            });
            userPartyEvalDetailEntity.setStar(starLevel.get());
            // 如果没有达到5星，则设置锤炼计划
            if (!this.experienceConfig.getMaxStar().equals(starLevel.get())) {
                UserPartyEvalPlanEntity plan = this.findPlanByRuleId(userPartyEvalDetailEntity.getRuleId(),
                        userPartyEvalDetailEntity.getRegionId());
                if (Objects.nonNull(plan)) {
                    userPartyEvalDetailEntity.setPlanId(plan.getPlanId());
                }
            }
            // 设置状态为完成
            userPartyEvalDetailEntity.setCalStatus(Constants.CAL_STATUS_DONE);
            userPartyEvalDetailEntity.setUpdateTime(LocalDateTime.now());
            log.debug("计算登录活跃程度[季度],param[{}]", userPartyEvalDetailEntity);
            this.userPartyEvalDetailMapper.updateByPrimaryKey(userPartyEvalDetailEntity);
        }catch (Exception e){
                log.error("计算第28项出现异常", e);
        }
    }

    /**
     * 计算当前年度登录天数
     *
     * @param userPartyEvalDetailEntity
     */
    public void calYearTotalDays(UserPartyEvalDetailEntity userPartyEvalDetailEntity) {
        try {
            // 年
            Integer year = Integer.valueOf(userPartyEvalDetailEntity.getDateMonth().toString().substring(0, 4));
            // 查询当前年度登录天数
            Integer yearDays = this.userLoginLogMapper.findQuarterAndYearTotalByLoginId(userPartyEvalDetailEntity.getUserId(),
                    year, null);
            // 设置星级
            List<ExperienceConfig.StarLevel> starList = this.experienceConfig.getStarLevels();
            // 默认1星
            AtomicReference<Integer> starLevel = new AtomicReference<>(1);
            starList.stream().filter(s -> yearDays >= s.getYMin() && yearDays <= s.getYMax()).forEach(s -> {
                starLevel.set(s.getStar());
            });
            userPartyEvalDetailEntity.setStar(starLevel.get());
            // 如果没有达到5星，则设置锤炼计划
            if (!this.experienceConfig.getMaxStar().equals(starLevel.get())) {
                UserPartyEvalPlanEntity plan = this.findPlanByRuleId(userPartyEvalDetailEntity.getRuleId(),
                        userPartyEvalDetailEntity.getRegionId());
                if (Objects.nonNull(plan)) {
                    userPartyEvalDetailEntity.setPlanId(plan.getPlanId());
                }
            }
            // 设置状态为完成
            userPartyEvalDetailEntity.setCalStatus(Constants.CAL_STATUS_DONE);
            userPartyEvalDetailEntity.setUpdateTime(LocalDateTime.now());
            log.debug("计算登录活跃程度[年度],param[{}]", userPartyEvalDetailEntity);
            this.userPartyEvalDetailMapper.updateByPrimaryKey(userPartyEvalDetailEntity);
        }catch (Exception e){
            log.error("计算第29项出现异常", e);
        }

    }

    /**
     * 根据ruleId查询锤炼计划ID
     *
     * @param ruleId
     * @param regionId
     * @return
     */
    private UserPartyEvalPlanEntity findPlanByRuleId(Integer ruleId, Long regionId) {
        Example example = new Example(UserPartyEvalPlanEntity.class);
        example.createCriteria().andEqualTo("regionId", regionId)
                .andEqualTo("ruleId", ruleId);
        return this.userPartyEvalPlanMapper.selectOneByExample(example);
    }
}
