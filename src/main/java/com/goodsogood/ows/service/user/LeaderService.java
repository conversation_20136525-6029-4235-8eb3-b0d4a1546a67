package com.goodsogood.ows.service.user;

import com.goodsogood.ows.mapper.user.LeaderMapper;
import com.goodsogood.ows.model.vo.sas.SasLeaderOrgForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 领导班子服务层
 * @date 2019/12/20
 */
@Service
@Log4j2
public class LeaderService {

    private final LeaderMapper leaderMapper;

    @Autowired
    public LeaderService(LeaderMapper leaderMapper) {
        this.leaderMapper = leaderMapper;
    }

    public List<SasLeaderOrgForm> findAllLeader() {
        return this.leaderMapper.findAllLeaderList();
    }
}
