package com.goodsogood.ows.service.user

import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import com.goodsogood.ows.config.SimpleApplicationConfigHelper
import com.goodsogood.ows.configuration.ExcludeOrgConfig
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.mapper.user.OrganizationMapper
import com.goodsogood.ows.mapper.user.UserMapper
import com.goodsogood.ows.model.db.PageNumber
import com.goodsogood.ows.model.db.user.OrganizationEntity
import com.goodsogood.ows.model.mongodb.pbm.PbmUserKitInfo
import com.goodsogood.ows.model.mongodb.user.User
import com.goodsogood.ows.model.vo.experience.OrgUserReportVO
import com.goodsogood.ows.model.vo.user.PbmUserInfo
import com.google.common.base.Preconditions
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.AggregationOperation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.stereotype.Service
import java.util.regex.Pattern

/**
 *
 * <AUTHOR>
 * @createTime 2022年06月22日 11:32:00
 */
@Service
class UserMongoService(val userMapper: UserMapper,
                       val mongoTemplate: MyMongoTemplate,
                       val organizationMapper: OrganizationMapper,
                       val excludeOrgConfig: ExcludeOrgConfig,
                       val applicationConfigHelper: SimpleApplicationConfigHelper) {

    private val log: Logger = LogManager.getLogger(UserMongoService::class.java)

    companion object{
        const val FIELD_SEQUENCE = "field_3525"
    }

    /**
     * @title 根据单位获取用户的userId
     * <AUTHOR>
     * @param   regionId    区域ID
     * @param   tree        tree    组织树 1-单位树， 2-党组织树
     * @param   unitId      单位ID   如果需要查询全系统人员 该字段传null
     * @param   isParty     1 - 党员，   2 - 全体
     * @param   sequence    0 -> 全体 1 -> 卷烟营销，2 -> 烟叶生成，3 -> 专卖管理，4 -> 综合管理
     * @updateTime 2022/6/22 11:35
     * @return
     * @throws
     */
    fun getUserIdList(regionId: Long, unitId: Long? = null, isParty: Int? = 1, sequence: Int? = 0): MutableList<Long?> {
        return userMapper.findPbmUserList(unitId, regionId, isParty, sequence, excludeOrgConfig.orgIds).map { it.userId }.toMutableList()
    }

    fun getUserIdList(regionId: Long, unitId: Long? = null, isParty: Int? = 1): MutableList<Long?> {
        return userMapper.findPbmUserList(unitId, regionId, isParty, 0, excludeOrgConfig.orgIds).map { it.userId }.toMutableList()
    }

    fun getUserList(regionId: Long, unitId: Long? = null, isParty: Int? = 1, sequence: Int? = 0): MutableList<PbmUserInfo> {
        return userMapper.findPbmUserList(unitId, regionId, isParty, sequence, excludeOrgConfig.orgIds)
    }

    fun getUserPage(regionId: Long, tree: Int = 2, unitId: Long? = null, isParty: Int? = 1, sequence: Int? = 0,
                      page: Int = 1, pageSize: Int = 100): Page<PbmUserInfo> {
        return PageHelper.startPage<PbmUserInfo>(page, pageSize).doSelectPage {
            userMapper.findPbmUserList(unitId, regionId, isParty, sequence, excludeOrgConfig.orgIds)
        }
    }

    /**
     * @title 根据所属序列查询用户ID集合
     * <AUTHOR>
     * @param   regionId    区域ID
     * @param   orgId       组织ID   如果需要查询全系统人员 该字段传null
     * @param   isParty     1 - 党员，   2 - 非党员
     * @param   sequence    0 -> 全体 1 -> 卷烟营销，2 -> 烟叶生成，3 -> 专卖管理，4 -> 综合管理
     * @updateTime 2022/6/22 15:49
     * @return
     * @throws
     */
    @Deprecated("因为查询Mongo时存在维度不一致的问题，所以使用 -> getUserList")
    fun getSequenceUserIdList(regionId: Long, orgId: Long? = null, isParty: Int? = 1, sequence: Int? = 0): MutableList<Long> {
        val results = getSequenceUserList(regionId, orgId, isParty, sequence)
        return results.map { it.userId }.toMutableList()
    }


    /**
     * @title 根据所属序列查询用户集合 - 分页
     * <AUTHOR>
     * @param   regionId    区域ID
     * @param   unitId       组织ID   如果需要查询全系统人员 该字段传null
     * @param   isParty     1 - 党员，   2 - 全体
     * @param   sequence    0 -> 全体 1 -> 卷烟营销，2 -> 烟叶生成，3 -> 专卖管理，4 -> 综合管理
     * @param   page        页码
     * @param   pageSize    一页大小
     * @updateTime 2022/6/22 15:49
     * @return
     * @throws
     */
    @Deprecated("因为查询Mongo时存在维度不一致的问题，所以使用 -> getUserList")
    fun getSequenceUserList(regionId: Long, unitId: Long? = null, isParty: Int? = 1, sequence: Int? = 0, page: Int = 1, pageSize: Int = 100): MutableList<User> {
        return getUserByMongo(regionId, unitId, isParty, sequence, PageNumber(page, pageSize))
    }

    /**
     * @title 根据所属序列查询用户集合
     * <AUTHOR>
     * @param   regionId    区域ID
     * @param   unitId       组织ID   如果需要查询全系统人员 该字段传null
     * @param   isParty     1 - 党员，   2 - 全体
     * @param   sequence    0 -> 全体 1 -> 卷烟营销，2 -> 烟叶生成，3 -> 专卖管理，4 -> 综合管理
     * @param   page        页码
     * @param   pageSize    一页大小
     * @updateTime 2022/6/22 15:49
     * @return
     * @throws
     */
    @Deprecated("因为查询Mongo时存在维度不一致的问题，所以使用 -> getUserList")
    fun getSequenceUserList(regionId: Long, unitId: Long? = null, isParty: Int? = 1, sequence: Int? = 0): MutableList<User> {
        return getUserByMongo(regionId, unitId, isParty, sequence, null)
    }

    private fun getUserByMongo(
        regionId: Long,
        orgId: Long? = null,
        isParty: Int? = 1,
        sequence: Int? = 0,
        pageNumber: PageNumber? = null
    ): MutableList<User> {
        val aggregationQueryList: MutableList<AggregationOperation> = mutableListOf()
        if (excludeOrgConfig.orgIds.isNotEmpty()) {
            aggregationQueryList.add(Aggregation.match(Criteria.where("orgList.orgId").nin(excludeOrgConfig.orgIds)))
            excludeOrgConfig.orgIds.forEach { id ->
                val pattern =
                    Pattern.compile("^.*-$id-.*$", Pattern.CASE_INSENSITIVE)
                aggregationQueryList.add(
                    Aggregation.match(
                        Criteria.where(
                            "orgList.orgId"
                        ).not().regex(pattern)
                    )
                )
            }
        }
        // 区域Id
        aggregationQueryList.add(Aggregation.match(Criteria.where("orgList.regionId").`is`(regionId)))
        // 组织ID
        if (orgId != null) {
            val orCriteria = Criteria()
            val pattern = Pattern.compile("^.*-$orgId-.*$", Pattern.CASE_INSENSITIVE)
            val criteriaOrgId = Criteria.where("orgList.orgId").`is`(orgId)
            val criteriaOrgLevel = Criteria.where("orgList.orgLevel").regex(pattern)
            orCriteria.orOperator(criteriaOrgId, criteriaOrgLevel)
            aggregationQueryList.add(Aggregation.match(orCriteria))
        }
        // 是否只查党员
        if (isParty == 1) {
            aggregationQueryList.add(Aggregation.match(Criteria.where("politicalType").`in`(listOf(1, 5, 17, 18))))
        }
        // 不能有自建用户
        aggregationQueryList.add(
            Aggregation.match(
                Criteria.where("sourceId").not().regex(Pattern.compile("^.*gsg.*$", Pattern.CASE_INSENSITIVE))
            )
        )
        // 所属序列 sequence = 1 -> 卷烟营销，2 -> 烟叶生成，3 -> 专卖管理，4 -> 综合管理
        if (sequence != 0) {
            aggregationQueryList.add(
                Aggregation.match(
                    Criteria.where("fields.$FIELD_SEQUENCE").`in`(mutableListOf(sequence.toString()))
                )
            )
        }
        // 是否需要分页
        if (pageNumber != null) {
            //设置分页属性
            val page = Preconditions.checkNotNull(pageNumber.page).toLong()
            val pageSize = Preconditions.checkNotNull(pageNumber.rows).toLong()
            val coordinate = (page - 1) * pageSize
            aggregationQueryList.add(Aggregation.skip(coordinate))
            aggregationQueryList.add(Aggregation.limit(pageSize))
        }
        val aggregation = Aggregation.newAggregation(aggregationQueryList)
        val results = mongoTemplate.aggregate(aggregation, User::class.java)
        return results.mappedResults
    }


    /**
     * @title 获取用户所属序列
     * <AUTHOR>
     * @param   userId
     * @updateTime 2022/6/22 17:54
     * @return  1 -> 卷烟营销，2 -> 烟叶生成，3 -> 专卖管理，4 -> 综合管理
     * @throws 可能会存在查询用户为空的情况
     */
    fun getUserSequence(userId: Long): Int? {
        val user = userMapper.selectByPrimaryKey(userId)
        if (user != null) {
           return user.sequence
        }
        return null
    }

    /**
     * @title 根据用户ID查询用户信息
     * <AUTHOR>
     * @param
     * @updateTime 2022/6/22 17:55
     * @return User
     * @throws 可能会存在查询用户为空的情况
     */
    fun getUserById(userId: Long): User? {
        val query = Query(Criteria.where("userId").`is`(userId))
        return mongoTemplate.findOne(query, User::class.java)
    }

    /**
     * 根据用户ID查询用户Kit信息
     */
    fun getUserKitById(yearMonth: String,userId: Long): PbmUserKitInfo? {
        val query = Query(Criteria.where("userId").`is`(userId).and("date").`is`(yearMonth))
        return mongoTemplate.findOne(query, PbmUserKitInfo::class.java)
    }

    /**
     * 根据用户ID集合查询用户Kit信息
     */
    fun getUserKitByIdList(yearMonth: String,userIds: Set<Long>): MutableList<PbmUserKitInfo>? {
        val query = Query(Criteria.where("userId").`in`(userIds).and("date").`is`(yearMonth))
        return mongoTemplate.find(query, PbmUserKitInfo::class.java)
    }

    /**
     * 获取单位组织ID
     */
    fun getUnitIdList(regionId: Long, unitId: Long? = null): MutableList<Long> {
        val units = organizationMapper.findUnits(regionId, unitId, excludeOrgConfig.orgIds)
        return units.map { it.unitId }.toMutableList()
    }

    /**
     * 获取单位组织
     */
    fun getUnitList(regionId: Long, unitId: Long? = null): MutableList<OrgUserReportVO> {
        return organizationMapper.findUnits(regionId, unitId, excludeOrgConfig.orgIds)
    }

    /**
     * 获取组织的所属单位
     */
    fun getUnitIdByOrgId(orgId: Long?): OrganizationEntity? {
        val entity = this.organizationMapper.selectByPrimaryKey(orgId)
        if (entity != null) {
            return if (entity.ownerId != null) {
                this.organizationMapper.selectByPrimaryKey(entity.ownerId)
            } else {
                getUnitIdByOrgId(entity.parentId)
            }
        }
        return null
    }
}