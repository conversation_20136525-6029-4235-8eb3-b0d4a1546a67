package com.goodsogood.ows.service.user;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.ExcludeOrgConfig;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.ScoreConfig;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.mapper.rank.UserScoreMapper;
import com.goodsogood.ows.mapper.user.*;
import com.goodsogood.ows.model.db.sas.StatisticalUserTempEntity;
import com.goodsogood.ows.model.db.user.*;
import com.goodsogood.ows.model.mongodb.dss.DssUserChild;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.rank.Month;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.model.vo.rank.TotalScoreVo;
import com.goodsogood.ows.model.vo.sas.FindOrgListForm;
import com.goodsogood.ows.model.vo.sas.MemberResultForm;
import com.goodsogood.ows.model.vo.sas.PartyOrgVo;
import com.goodsogood.ows.model.vo.sas.UserScoreVo;
import com.goodsogood.ows.model.vo.user.*;
import com.goodsogood.ows.service.autoScore.ComputeService;
import com.goodsogood.ows.utils.AgeUtils;
import com.goodsogood.ows.utils.ListUtils;
import com.goodsogood.ows.utils.NumberUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.validation.constraints.NotBlank;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 第三方接口服务
 *
 * <AUTHOR>
 * @create 2018-06-20 10:01
 **/
@Service
@Log4j2
public class UserService {

    private final int[] PARTY_TYPE = {1, 5, 17, 18};

    private final ObjectMapper OBJECTMAPPER = new ObjectMapper();

    private final StringRedisTemplate redisTemplate;
    private final UserMapper userMapper;
    private final ScoreConfig scoreConfig;
    private final OrganizationMapper organizationMapper;
    private final UserOrgAndCorpMapper userOrgAndCorpMapper;
    private final TagMapper tagMapper;
    private final UserSnapshotMapper userSnapshotMapper;
    private final ComputeService computeService;
    private final UserThirdMapper userThirdMapper;
    private final OrgService orgService;
    private final OptionMapper optionMapper;
    private final SimpleApplicationConfigHelper simpleApplicationConfigHelper;
    private final UserScoreMapper userScoreMapper;
    private final OrgTypeConfig orgTypeConfig;
    private final ExcludeOrgConfig excludeOrgConfig;
    private final PartyGroupMapper partyGroupMapper;
    private final TbcPartyMapper tbcPartyMapper;

    @Value("${tog-services.user-center}")
    private String userCenter;

    @Value("${user-option.politicalCode}")
    @NotBlank
    private String politicalCode;

    /**
     * 30岁以下
     */
    public static final String AGE_ONE = "u.age<=30";

    /**
     * 30-45 岁
     */
    public static final String AGE_TWO = "u.age>30 and u.age<=45";

    /**
     * 45-55 岁
     */
    public static final String AGE_THREE = "u.age>45 and u.age<=55";

    /**
     * 55岁以上
     */
    public static final String AGE_FOUR = "u.age>55";

    /**
     * 研究生
     */
    public static final String POSTGRADUATE =
            "u.education in(1013,1014,1015,1016,1017,1020,1021,1022,1028,1030,1035,1037,1043)";

    /**
     * 大学 本科
     */
    public static final String UNDERGRADUATE = "u.education in(1010,1011,1012,1019,1027,1029,1031,1036,1038,1042)";

    /**
     * 专科
     */
    public static final String COLLEGE = "u.education in(1005,1006,1007,1008,1009,1018,1025,1032,1034,1039,1040)";

    /**
     * 高中及以下
     */
    public static final String HIGH = "u.education in(1001,1002,1003,1004,1023,1024,1026,1033,1041,1044,1045)";

    /**
     * 党龄 10年以下
     */
    public static final String PARTY_AGE_ONE =
            "date_format(u.joining_time,'%Y-%m-%d') >= date_sub(curdate(),interval 10 year)";

    /**
     * 党龄 11年~20年
     */
    public static final String PARTY_AGE_TWO =
            "date_format(u.joining_time,'%Y-%m-%d') between date_sub(curdate(),interval 20 year) and date_sub(curdate(),interval 11 year)";

    /**
     * 党龄 21年~30年
     */
    public static final String PARTY_AGE_THREE =
            "date_format(u.joining_time,'%Y-%m-%d') between date_sub(curdate(),interval 30 year) and date_sub(curdate(),interval 21 year)";

    /**
     * 党龄 31年以上
     */
    public static final String PARTY_AGE_FOUR =
            "date_format(u.joining_time,'%Y-%m-%d') <= date_sub(curdate(),interval 31 year)";


    //以下为伯乐提出更新
    /**
     * 党龄 10年以下
     */
    public static final String PARTY_AGE_ONE_NEW = " TimeStampDiff(year,uoc.joining_time,now())<=10";

    /**
     * 党龄 11年~20年
     */
    public static final String PARTY_AGE_TWO_NEW =
            "TimeStampDiff(year,uoc.joining_time,now())>=11 and TimeStampDiff(year,uoc.joining_time,now())<=20";

    /**
     * 党龄 21年~30年
     */
    public static final String PARTY_AGE_THREE_NEW =
            "TimeStampDiff(year,uoc.joining_time,now())>=21 and TimeStampDiff(year,uoc.joining_time,now())<=30";

    /**
     * 党龄 31年以上
     */
    public static final String PARTY_AGE_FOUR_NEW = "TimeStampDiff(year,uoc.joining_time,now())>=31";

    /**
     * 民族 汉
     */
    public static final String ETHNIC_HAN = "u.ethnic = 1053";

    /**
     * 民族 土家族
     */
    public static final String ETHNIC_TUJIA = "u.ethnic = 1042";

    /**
     * 民族 苗族
     */
    public static final String ETHNIC_MIAO = "u.ethnic = 1024";

    /**
     * 民族 其他
     */
    public static final String ETHNIC_OTHER = "(u.ethnic not in(1053,1042,1024) OR u.ethnic IS NULL)";


    @Autowired
    public UserService(StringRedisTemplate redisTemplate,
                       UserMapper userMapper,
                       ScoreConfig scoreConfig,
                       OrganizationMapper organizationMapper,
                       UserOrgAndCorpMapper userOrgAndCorpMapper,
                       TagMapper tagMapper,
                       UserSnapshotMapper userSnapshotMapper,
                       ComputeService computeService,
                       UserThirdMapper userThirdMapper,
                       OrgService orgService,
                       OptionMapper optionMapper,
                       SimpleApplicationConfigHelper simpleApplicationConfigHelper,
                       UserScoreMapper userScoreMapper,
                       OrgTypeConfig orgTypeConfig,
                       ExcludeOrgConfig excludeOrgConfig, PartyGroupMapper partyGroupMapper, TbcPartyMapper tbcPartyMapper) {
        this.redisTemplate = redisTemplate;
        this.userMapper = userMapper;
        this.scoreConfig = scoreConfig;
        this.organizationMapper = organizationMapper;
        this.userOrgAndCorpMapper = userOrgAndCorpMapper;
        this.tagMapper = tagMapper;
        this.userSnapshotMapper = userSnapshotMapper;
        this.computeService = computeService;
        this.userThirdMapper = userThirdMapper;
        this.orgService = orgService;
        this.optionMapper = optionMapper;
        this.simpleApplicationConfigHelper = simpleApplicationConfigHelper;
        this.userScoreMapper = userScoreMapper;
        this.orgTypeConfig = orgTypeConfig;
        this.excludeOrgConfig = excludeOrgConfig;
        this.partyGroupMapper = partyGroupMapper;
        this.tbcPartyMapper = tbcPartyMapper;
    }

    /**
     * 通过用户id查询用户基本信息
     */
    public UserInfoBase getUserInfoByUserId(Long userId, Long regionId) {
        UserInfoBase base = new UserInfoBase();
        String baseInfoJson = null;
        if (null != userId) {
            baseInfoJson = redisTemplate.opsForValue().get(Global.CACHE_BASE_USER_ID_PREFIX + userId + "_" + regionId);
        }
        if (StringUtils.isBlank(baseInfoJson)) {
            try {
                base = this.userBaseInfo(userId, null, null, regionId);
                log.debug("查询用户基本信息完成 -> [{}]", base.getUserId());
            } catch (Exception e) {
                log.error("获取用户基本信息失败->" + e.getMessage(), e);
            }
        } else {
            try {
                base = OBJECTMAPPER.readValue(baseInfoJson, UserInfoBase.class);
                if (null == base.getOrgId()) {
                    this.redisTemplate.delete(Global.CACHE_BASE_USER_ID_PREFIX + userId + "_" + regionId);
                    return getUserInfoByUserId(userId, regionId);
                }
            } catch (JsonProcessingException e) {
                log.error("baseInfoJson Json转换失败->" + e.getMessage(), e);
            }
            log.debug("使用了userInfoBase缓存");
        }
        return base;
    }

    /**
     * 根据条件查询组织管理员
     *
     * @param orgId
     * @param roleType
     * @return
     */
    public List<UserInfoBase> findManagerByWhere(Long orgId, Integer roleType) {
        return this.userMapper.findManagerByWhere(Constants.STATUS_YES, roleType, orgId);
    }

    /**
     * 根据条件查询政治，自然生日党员
     *
     * @param orgId
     * @return
     */
    public MemberResultForm findMemberByDate(Long orgId) {
        MemberResultForm result = new MemberResultForm();
        // 获取当前日期
        String curMonth = Utils.dateToString(DateTime.now().toDate()).substring(4, 8);
        // 查询自然生日用户
        List<UserInfoBase> birthDayList = this.userMapper.findMemberByDate(curMonth, Constants.STATUS_YES, this.politicalCode,
                2, Constants.STATUS_YES, orgId);
        // 查询政治生日用户
        List<UserInfoBase> politicalList = this.userMapper.findMemberByDate(curMonth, Constants.STATUS_YES, this.politicalCode,
                1, Constants.STATUS_YES, orgId);
        result.setBirthdayList(birthDayList);
        result.setPoliticalList(politicalList);
        log.debug("根据条件查询政治生日，自然生日党员返回结果 result:[{}]", result);
        return result;
    }

    /**
     * 根据组织ID查询用户列表
     *
     * @param form 查询用户列表参数
     * @return
     */
    public List<UserInfoBase> findUserByOrgId(FindOrgListForm form) {
        return this.userOrgAndCorpMapper.findUserByOrgId(Constants.STATUS_YES, form.getIsEmployee(), form.getIdList(),
                form.getIsFilter(), this.politicalCode, form.getIsInclude());
    }

    /**
     * 根据组织查询组织内党员
     *
     * @param form 查询参数
     * @return List<UserPartyResultForm>
     */
    public Page<StatisticalUserTempEntity> findPartyUserByWhere(UserPartyQueryForm form) {
        return PageHelper.startPage(form.getPage().getPageNo(), form.getPage().getPageSize())
                .doSelectPage(() -> this.userOrgAndCorpMapper.findPartyUserByWhere(form, Constants.STATUS_YES, Constants.STATUS_YES,
                        Constants.STATUS_YES, this.politicalCode));
    }

    public List<Long> findWorkCommitManagerByWhere(Long orgId, int roleType) {
        return this.userOrgAndCorpMapper.findWorkCommitManagerByWhere(orgId, roleType);
    }

    /**
     * 根据组织查询下级所有党员
     *
     * @param orgId
     * @return
     */
    public List<UserEntity> getUserByOrg(Long orgId) {
        return this.userMapper.findUserList(orgId, 1, this.excludeOrgConfig.getOrgIds(), this.orgTypeConfig.getNoStatisticsChild(), null);
    }

    public Page<UserEntity> getUserByOrg(Long orgId, Integer page, Integer pageSize) {
        return PageHelper.startPage(page, pageSize).doSelectPage(() -> this.userMapper.findUserList(orgId, 1, this.excludeOrgConfig.getOrgIds(), this.orgTypeConfig.getNoStatisticsChild(), null));
    }

    public List<UserDssVo> getUserOrgByOrg(Long orgId, List<Long> userIds) {
        return this.userMapper.getUserOrgByOrgId(orgId, userIds);
    }

    public List<UserSnapshotEntity> getHistoryUserListByOrg(Long orgId, Integer year) {
        Example example = new Example(UserSnapshotEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dateMonth", year + "-12");
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andIn("politicalType", CollectionUtils.arrayToList(PARTY_TYPE));
        List<Integer> noStatisticsChild = this.orgTypeConfig.getNoStatisticsChild();
        criteria.andNotIn("orgTypeChild", noStatisticsChild);
        List<Long> excludeOrgIds = this.excludeOrgConfig.getOrgIds();
        excludeOrgIds.forEach(eOrgId -> {
            criteria.andNotEqualTo("orgId", eOrgId);
            criteria.andNotLike("orgLevel", "%-" + eOrgId + "-%");
        });
        Example.Criteria criteria2 = example.createCriteria();
        criteria2.andEqualTo("orgId", orgId);
        criteria2.orLike("orgLevel", "%-" + orgId + "-%");
        example.and(criteria2);
        return this.userSnapshotMapper.selectByExample(example);
    }

    public List<UserSnapshotEntity> getHistoryUserListByOrg(Long orgId, String dateMonth) {
        Example example = new Example(UserSnapshotEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dateMonth", dateMonth);
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andIn("politicalType", CollectionUtils.arrayToList(PARTY_TYPE));
        List<Integer> noStatisticsChild = this.orgTypeConfig.getNoStatisticsChild();
        criteria.andNotIn("orgTypeChild", noStatisticsChild);
        List<Long> excludeOrgIds = this.excludeOrgConfig.getOrgIds();
        excludeOrgIds.forEach(eOrgId -> {
            criteria.andNotEqualTo("orgId", eOrgId);
            criteria.andNotLike("orgLevel", "%-" + eOrgId + "-%");
        });
        Example.Criteria criteria2 = example.createCriteria();
        criteria2.andEqualTo("orgId", orgId);
        criteria2.orLike("orgLevel", "%-" + orgId + "-%");
        example.and(criteria2);
        return this.userSnapshotMapper.selectByExample(example);
    }

    public List<UserSnapshotEntity> getHistoryUserList(Long regionId, Integer year) {
        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        Example example = new Example(UserSnapshotEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dateMonth", year + "-12");
        criteria.andEqualTo("regionId", regionId);
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andIn("politicalType", CollectionUtils.arrayToList(PARTY_TYPE));
        List<Integer> noStatisticsChild = this.orgTypeConfig.getNoStatisticsChild();
        criteria.andNotIn("orgTypeChild", noStatisticsChild);
        List<Long> excludeOrgIds = this.excludeOrgConfig.getOrgIds();
        excludeOrgIds.forEach(eOrgId -> {
            criteria.andNotEqualTo("orgId", eOrgId);
            criteria.andNotLike("orgLevel", "%-" + eOrgId + "-%");
        });
        Example.Criteria criteria2 = example.createCriteria();
        criteria2.andEqualTo("orgId", orgData.getOrgId());
        criteria2.orLike("orgLevel", "%-" + orgData.getOrgId() + "-%");
        example.and(criteria2);
        return this.userSnapshotMapper.selectByExample(example);
    }

    public List<UserSnapshotEntity> getHistoryUserList(Long regionId, String dateMonth) {
        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        Example example = new Example(UserSnapshotEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dateMonth", dateMonth);
        criteria.andEqualTo("regionId", regionId);
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andIn("politicalType", CollectionUtils.arrayToList(PARTY_TYPE));
        List<Integer> noStatisticsChild = this.orgTypeConfig.getNoStatisticsChild();
        criteria.andNotIn("orgTypeChild", noStatisticsChild);
        List<Long> excludeOrgIds = this.excludeOrgConfig.getOrgIds();
        excludeOrgIds.forEach(eOrgId -> {
            criteria.andNotEqualTo("orgId", eOrgId);
            criteria.andNotLike("orgLevel", "%-" + eOrgId + "-%");
        });
        Example.Criteria criteria2 = example.createCriteria();
        criteria2.andEqualTo("orgId", orgData.getOrgId());
        criteria2.orLike("orgLevel", "%-" + orgData.getOrgId() + "-%");
        example.and(criteria2);
        return this.userSnapshotMapper.selectByExample(example);
    }

    public Page<UserSnapshotEntity> getHistoryUserList(Long regionId, Integer year, Integer page, Integer pageSize) {
        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        Example example = new Example(UserSnapshotEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dateMonth", year + "-12");
        criteria.andEqualTo("regionId", regionId);
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andIn("politicalType", CollectionUtils.arrayToList(PARTY_TYPE));
        List<Integer> noStatisticsChild = this.orgTypeConfig.getNoStatisticsChild();
        criteria.andNotIn("orgTypeChild", noStatisticsChild);
        List<Long> excludeOrgIds = this.excludeOrgConfig.getOrgIds();
        excludeOrgIds.forEach(eOrgId -> {
            criteria.andNotEqualTo("orgId", eOrgId);
            criteria.andNotLike("orgLevel", "%-" + eOrgId + "-%");
        });
        Example.Criteria criteria2 = example.createCriteria();
        criteria2.andEqualTo("orgId", orgData.getOrgId());
        criteria2.orLike("orgLevel", "%-" + orgData.getOrgId() + "-%");
        example.and(criteria2);
        return PageHelper.startPage(page, pageSize).doSelectPage(() -> this.userSnapshotMapper.selectByExample(example));
    }

    public Page<UserSnapshotEntity> getHistoryUserList(Long regionId, String dateMonth, Integer page, Integer pageSize) {
        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        Example example = new Example(UserSnapshotEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dateMonth", dateMonth);
        criteria.andEqualTo("regionId", regionId);
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andIn("politicalType", CollectionUtils.arrayToList(PARTY_TYPE));
        List<Integer> noStatisticsChild = this.orgTypeConfig.getNoStatisticsChild();
        criteria.andNotIn("orgTypeChild", noStatisticsChild);
        List<Long> excludeOrgIds = this.excludeOrgConfig.getOrgIds();
        excludeOrgIds.forEach(eOrgId -> {
            criteria.andNotEqualTo("orgId", eOrgId);
            criteria.andNotLike("orgLevel", "%-" + eOrgId + "-%");
        });
        Example.Criteria criteria2 = example.createCriteria();
        criteria2.andEqualTo("orgId", orgData.getOrgId());
        criteria2.orLike("orgLevel", "%-" + orgData.getOrgId() + "-%");
        example.and(criteria2);
        return PageHelper.startPage(page, pageSize).doSelectPage(() -> this.userSnapshotMapper.selectByExample(example));
    }

    public List<UserDssVo> getHistoryUserOrgList(Long orgId, Integer year, List<Long> userIds) {
        return this.userSnapshotMapper.getHistoryUserList(orgId, year + "-12", userIds);
    }

    /**
     * 查询标签名称
     *
     * @param userId
     * @return
     */
    public String getTagName(Long userId) {
        String publicTag = this.userOrgAndCorpMapper.getPublicTag(userId);
        if (StringUtils.isNotBlank(publicTag)) {
            String[] split = publicTag.split(",");
            List<Long> tagIds = Arrays.stream(split).map(Long::valueOf).collect(Collectors.toList());
            return this.tagMapper.getTagName(tagIds);
        } else {
            return "";
        }
    }

    public DssUserChild buildUserList(Long userId, Long regionId, Integer year) {
        DssUserChild user = new DssUserChild();
        UserInfoBase userInfoBase = this.getUserInfoByUserId(userId, regionId);
        user.setUserId(userId);
        user.setName(userInfoBase.getUserName());
        user.setGrand(this.getUserScore(userId, year));
        return user;
    }

    public UserSnapshotEntity getHistoryUserInfo(Long userId, int year, Integer month) {
        Example example = new Example(UserSnapshotEntity.class);
        Example.Criteria criteria = example.createCriteria();
        if (null != month && month < 10) {
            criteria.andEqualTo("dateMonth", year + "-0" + month);
        } else {
            criteria.andEqualTo("dateMonth", year + "-" + (null == month ? "12" : month));
        }
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("status", Constants.STATUS_YES);
        return this.userSnapshotMapper.selectOneByExample(example);
    }

    /**
     * 获取用户评分
     *
     * @param userId
     * @param year
     * @return
     */
    public Double getUserScore(Long userId, Integer year) {
        TotalScoreVo totalScoreVo = userScoreMapper.findSingleUserTotal(userId, year);
        return (null == totalScoreVo || null == totalScoreVo.getScore()) ? 0d : computeService.conversion(2L, totalScoreVo.getScore().doubleValue(), year);
    }


    /**
     * 得到组织下面的用户信息
     * 得到用户得分信息
     *
     * @param orgId
     * @param staDate
     * @return
     */
    public Double getUserScore(Long orgId, String staDate) {
        Map<String, Object> userScoreInfo = userMapper.getScoreUser(orgId, staDate);
        AtomicReference<Double> score = new AtomicReference<>(0.0);
        if (userScoreInfo != null && !userScoreInfo.isEmpty()) {
            Integer totalCount = Integer.parseInt(userScoreInfo.get("totalCount").toString());
            if (totalCount == 0) {
                return score.get();
            }
            userScoreInfo.forEach((key, value) -> {
                if ("totalCount".equals(key)) {
                    return;
                }
                if (!StringUtils.isEmpty(key) && null != value) {
                    Integer filedCount = Integer.parseInt(value.toString());
                    //保证与总数一致 才计算得分
                    if (totalCount.equals(filedCount)) {
                        String fieldValue = getFieldValueByFieldName(key, scoreConfig.getUserScore());
                        if (fieldValue != null && !StringUtils.isEmpty(fieldValue)) {
                            double calScore = Double.parseDouble(fieldValue);
                            score.set(NumberUtils.add(score.get(), calScore));
                        }
                    }
                }
            });
        }
        return score.get();
    }

    /**
     * 得到组织下面的用户信息
     * 得到用户得分信息
     *
     * @return
     */
    public ScoreResultVo getUserScoreList(List<Long> orgIds, int year) {
        List<List<Long>> listOrgIds = ListUtils.splitList(orgIds, 5);
        ScoreResultVo scoreResultVo = new ScoreResultVo();
        Map<Long, EnumMap<Month, Double>> scoreResultMap = new HashMap<>();
        //全部设置为0
        final EnumMap<Month, Double>[] enumMap = new EnumMap[]{null};
        listOrgIds.forEach(itemListOrg -> {
            List<UserScoreVo> userScoreInfo = userMapper.getScoreUserList(itemListOrg, year);
            itemListOrg.forEach(item -> {
                enumMap[0] = new EnumMap<>(Month.class);
                for (int i = 1; i <= 12; i++) {
                    enumMap[0].put(Month.getEnumKey(i), 0.0);
                }
                scoreResultMap.put(item, enumMap[0]);
                //计算查询到组织 重置结果
                List<UserScoreVo> orgScoreInfoResult = userScoreInfo.stream().filter(item1 -> item
                        .equals(item1.getOrgId())).collect(Collectors.toList());
                orgScoreInfoResult.forEach(itemInner -> {
                    AtomicReference<Double> score = new AtomicReference<>(0.0);
                    Integer totalCount = itemInner.getTotalCount();
                    if (totalCount.equals(itemInner.getName())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getUserScore().getName()));
                    }
                    if (totalCount.equals(itemInner.getPhone())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getUserScore().getPhone()));
                    }
                    if (totalCount.equals(itemInner.getCertNumber())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getUserScore().getCertNumber()));
                    }
                    if (totalCount.equals(itemInner.getEducation())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getUserScore().getEducation()));
                    }
                    if (totalCount.equals(itemInner.getGender())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getUserScore().getGender()));
                    }
                    if (totalCount.equals(itemInner.getPoliticalType())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getUserScore().getPoliticalType()));
                    }
                    if (totalCount.equals(itemInner.getPositionCode())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getUserScore().getPositionCode()));
                    }
                    if (totalCount.equals(itemInner.getJoiningTime())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getUserScore().getJoiningTime()));
                    }
                    enumMap[0].put(Month.getEnumKey(itemInner.getDateMonth()), score.get());
                });
                scoreResultMap.put(item, enumMap[0]);
            });
        });
        scoreResultVo.setScoreResultMap(scoreResultMap);
        return scoreResultVo;
    }


    /**
     * 根据属性名获取属性值
     *
     * @param fieldName
     * @param object
     * @return
     */
    private String getFieldValueByFieldName(String fieldName, Object object) {
        try {
            Field field = object.getClass().getDeclaredField(fieldName);
            //设置对象的访问权限，保证对private的属性的访问
            field.setAccessible(true);
            return field.get(object).toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据userId查询用户信息-内部接口
     * <p>
     * 调用前先进行缓存查询，如果没有，再进行查询后回写缓存
     * <p>
     * 其他模块在调用之前，先到缓存中获得userBaseInfo
     *
     * @param userId
     * @return
     */
    public UserInfoBase userBaseInfo(Long userId, Long oid, String openId, Long regionId) throws Exception {
        UserInfoBase base = new UserInfoBase();
        String baseInfoJson = null;
        if (null != userId) {
            // 获取baseinfo信息
            baseInfoJson = redisTemplate.opsForValue().get(Global.CACHE_BASE_USER_ID_PREFIX + userId + "_" + regionId);
        }

        if (null == baseInfoJson) {
            if (null == userId && !Strings.isBlank(openId)) {
                if (null == regionId) {
                    UserThirdEntity userThird = this.getUserThird(openId);
                    Region.RegionData region = this.simpleApplicationConfigHelper.getRegionByOrgId(userThird.getOId());
                    regionId = region.getRegionId();
                }
                // 根据openID查询用户ID
                String openIdJson = redisTemplate.opsForValue().get(Global.CACHE_USER_THTOKEN_PREFIX + openId);
                if (Strings.isBlank(openIdJson)) {
                    UserThirdEntity ute = this.getUserThird(openId);
                    if (ute != null && ute.getUserId() != null) {
                        userId = ute.getUserId();
                        try {
                            redisTemplate.opsForValue().set(Global.CACHE_USER_THTOKEN_PREFIX + openId, userId.toString(), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
                            log.debug("缓存userId成功");
                        } catch (Exception e) {
                            log.error("缓存userId出错:" + e.getMessage(), e);
                        }
                    } else {
                        return null;
                    }
                } else {
                    userId = OBJECTMAPPER.readValue(openIdJson, Long.class);
                    log.debug("使用了userId缓存");
                }
            }
            String userJson = redisTemplate.opsForValue().get(Global.CACHE_USER_ID_PREFIX + userId);

            UserEntity ue;
            if (null == userJson) {
                ue = new UserEntity();
                ue.setUserId(userId);
                ue = this.userMapper.selectByPrimaryKey(ue);
                try {
                    if (ue != null) {
                        this.redisTemplate.opsForValue().set(Global.CACHE_USER_ID_PREFIX + userId, Utils.toJson(ue), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
                        log.debug("缓存userEntity成功");
                    } else {
                        return null;
                    }
                } catch (Exception e) {
                    log.error("缓存userEntity出错:{}", e.getMessage(), e);
                }
            } else {
                ue = OBJECTMAPPER.readValue(userJson, UserEntity.class);
                log.debug("使用了userEntity缓存");
            }

            base.setUserId(userId);
            base.setUserName(ue.getName());
            base.setIsVerify(ue.getIsVerify());
            base.setPhoneSecret(ue.getPhoneSecret());
            base.setCertNumberSecret(ue.getCertNumberSecret());
            // 已实名认证
            if (ue.getIsVerify() == Constants.IS_VERIFY_YES) {
                try {
                    base.setPhone(NumEncryptUtils.decrypt(ue.getPhone(), ue.getPhoneSecret()));
                    base.setCertNumber(NumEncryptUtils.decrypt(ue.getCertNumber(), ue.getCertNumberSecret()));
                } catch (Exception e) {
                    log.error("身份证或手机号解码报错 ->", e);
                    base.setPhone(ue.getPhoneSecret());
                    base.setCertNumber(ue.getCertNumberSecret());
                }
                base.setAge(Utils.getAge(ue.getCertNumber(), ue.getCertNumberSecret()));
            } else {
                if (!Strings.isBlank(ue.getPhone()) && !Strings.isBlank(ue.getPhoneSecret())) {
                    base.setPhone(NumEncryptUtils.decrypt(ue.getPhone(), ue.getPhoneSecret()));
                } else {
                    base.setPhone(ue.getPhone());
                }
                base.setCertNumber(ue.getCertNumber());
                base.setAge(null);
            }
            base.setCertType(ue.getCertType());
            base.setGender(ue.getGender());
            base.setEducation(ue.getEducation());
            base.setEthnic(ue.getEthnic());

            // 设置用户所在组织信息
            this.setCommonBase(base, userId, regionId);
            // 设置用户所在组织以及党委信息
            this.buildPartyOrg(base, userId, regionId);

            // 多个公众号时
            Example thirdExample = new Example(UserThirdEntity.class);
            thirdExample.createCriteria().andEqualTo("userId", userId);
            //  huang_kangjie 2018-12-15 00:15 分级平台上预发布，由于分级系统支持一个用户拥有多个openid，
            //  暂时回显用户第一个openid对应的微信信息
            List<UserThirdEntity> uteList = this.userThirdMapper.selectByExample(thirdExample);
            if (!CollectionUtils.isEmpty(uteList)) {
                List<OpenBase> openList = new ArrayList<>();
                for (UserThirdEntity userThirdEntity : uteList) {
                    OpenBase openBase = new OpenBase();
                    openBase.setNickName(userThirdEntity.getNickName());
                    openBase.setHeadUrl(userThirdEntity.getHead());
                    openBase.setOrgId(userThirdEntity.getOId());
                    openBase.setOpenId(userThirdEntity.getThToken());
                    openList.add(openBase);
                }
                base.setOpenList(openList);
                base.setNickName(uteList.get(0).getNickName());
                base.setHeadUrl(uteList.get(0).getHead());
                base.setOpenId(uteList.get(0).getThToken());
            }
            if (oid != null) {
                Example example = new Example(UserOrgAndCorpEntity.class);
                example.createCriteria().andEqualTo("userId", userId)
                        .andEqualTo("organizationId", oid);
                UserOrgAndCorpEntity entity = this.userOrgAndCorpMapper.selectOneByExample(example);
                if (entity != null && Strings.isNotBlank(entity.getTagId())) {
                    String[] tags = entity.getTagId().split(",");
                    List<Long> tagList = new ArrayList<>();
                    for (String tag : tags) {
                        tagList.add(Long.parseLong(tag));
                    }
                    base.setTags(tagList);
                }
                // 查询组织类型字典
                if (this.redisTemplate.hasKey(Global.CACHE_ORG_ID_PREFIX + oid)) {
                    OrganizationEntity org = Utils.fromJson(this.redisTemplate.opsForValue().get(Global.CACHE_ORG_ID_PREFIX + oid), OrganizationEntity.class);
                    base.setOrgType(org.getOrgType());
                    base.setOrgTypeName(org.getOrgTypeName());
                    log.debug("使用了组织类型缓存");
                } else {
                    Example orgExample = new Example(OrganizationEntity.class);
                    orgExample.selectProperties("orgType");
                    orgExample.createCriteria().andEqualTo("organizationId", oid);
                    OrganizationEntity orgEntity = this.organizationMapper.selectOneByExample(orgExample);
                    // 查询组织类型字典名称
                    if (orgEntity != null && orgEntity.getOrgType() != null) {
                        Example optionExample = new Example(OptionEntity.class);
                        optionExample.selectProperties("opValue");
                        optionExample.createCriteria().andEqualTo("opKey", orgEntity.getOrgType())
                                .andEqualTo("code", "1028");
                        OptionEntity optionEntity = this.optionMapper.selectOneByExample(optionExample);
                        base.setOrgType(orgEntity.getOrgType());
                        if (optionEntity != null && Strings.isNotBlank(optionEntity.getOpValue())) {
                            base.setOrgTypeName(optionEntity.getOpValue());
                        }
                    }
                }
            }
            try {
                redisTemplate.opsForValue().set(Global.CACHE_BASE_USER_ID_PREFIX + userId + "_" + regionId, Utils.toJson(base), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
                log.debug("缓存userInfoBase成功");
            } catch (Exception e) {
                log.error("缓存userInfoBase出错:" + e.getMessage(), e);
            }
        } else {
            base = OBJECTMAPPER.readValue(baseInfoJson, UserInfoBase.class);
            log.debug("使用了userInfoBase缓存");
        }
        return base;
    }

    public UserThirdEntity getUserThird(String openId) {
        Example ex = new Example(UserThirdEntity.class);
        ex.createCriteria().andEqualTo("thToken", openId);
        return this.userThirdMapper.selectOneByExample(ex);
    }

    public void setCommonBase(UserInfoBase base, Long userId, Long regionId) {
        // 根据regionID查询顶级党委组织
        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        Long topOrgId = orgData.getOrgId();
        // 用户所在的组织（已激活组织）
        List<String> nameList = new ArrayList<>();
        List<Long> idList = new ArrayList<>();
        List<OrganizationEntity> orgList = this.userOrgAndCorpMapper.getOrgList(userId, Constants.STATUS_YES,
                Constants.ACTIVATE_STATUS_YES, regionId);
        if (!CollectionUtils.isEmpty(orgList)) {
            for (OrganizationEntity oe : orgList) {
                if (Strings.isBlank(oe.getOrgLevel())) {
                    nameList.add(oe.getName());
                    idList.add(oe.getOrganizationId());
                } else {
                    int len = oe.getOrgLevel().length();
                    if (len == 3 || len == 5) {
                        nameList.add(oe.getName());
                        idList.add(oe.getOrganizationId());
                    } else if (len > 5) {
                        String[] ol = oe.getOrgLevel().split("-");
                        if (Long.valueOf(ol[3]).equals(topOrgId)) {
                            nameList.add(getNameByOrgId(Long.valueOf(ol[3])));
                            idList.add(Long.valueOf(ol[3]));
                        }
                    }
                }
            }
            base.setNameList(nameList);
            base.setIdList(idList);
        }
        // 用户所在组织(所有组织)
        List<Long> orgIdList = new ArrayList<>();
        List<String> orgNameList = new ArrayList<>();
        List<Long> allIdList = new ArrayList<>();
        List<String> allNameList = new ArrayList<>();
        List<OrganizationEntity> allList = this.userOrgAndCorpMapper.getOrgList(userId, 0, 0, regionId);
        if (!CollectionUtils.isEmpty(allList)) {
            for (OrganizationEntity oe : allList) {
                orgIdList.add(oe.getOrganizationId());
                orgNameList.add(oe.getName());
                if (Strings.isBlank(oe.getOrgLevel())) {
                    allIdList.add(oe.getOrganizationId());
                    allNameList.add(oe.getName());
                } else {
                    int len = oe.getOrgLevel().length();
                    int topOrgLen = String.valueOf(topOrgId).length();
                    if (len == 5 || len == 6 + topOrgLen) {
                        allIdList.add(oe.getOrganizationId());
                        allNameList.add(oe.getName());
                    } else if (len > 6 + topOrgLen) {
                        String[] all = oe.getOrgLevel().split("-");
                        // 上级为 3-市级机关(顶级党组织),党委ID和党委名称
                        if (Long.valueOf(all[3]).equals(topOrgId)) {
                            allIdList.add(Long.valueOf(all[4]));
                            allNameList.add(getNameByOrgId(Long.valueOf(all[4])));
                        }
                    }
                }
            }
            base.setAllNameList(allNameList);
            base.setAllIdList(allIdList);
            base.setOrgIdList(orgIdList);
            base.setOrgNameList(orgNameList);
        }
    }

    public String getNameByOrgId(Long orgId) {
        String orgKey = Global.CACHE_ORG_ID_PREFIX + orgId;
        OrganizationEntity oe;
        if (this.redisTemplate.hasKey(orgKey)) {
            oe = Utils.fromJson(this.redisTemplate.opsForValue().get(orgKey), OrganizationEntity.class);
        } else {
            Example orgEx = new Example(OrganizationEntity.class);
            orgEx.selectProperties("name");
            orgEx.createCriteria().andEqualTo("organizationId", orgId);
            oe = this.organizationMapper.selectOneByExample(orgEx);
        }
        return oe == null ? "" : oe.getName();
    }

    /**
     * 设置用户所在组织以及组织所在党委信息
     *
     * @param base   base
     * @param userId userId
     */
    private void buildPartyOrg(UserInfoBase base, Long userId, Long regionId) {
        // 根据用户ID查询用户所在组织信息
        List<UserInfoBase> baseInfo = this.findUserByKey(userId, regionId);
        if (!CollectionUtils.isEmpty(baseInfo)) {
            // 根据组织ID查询组织所在党委组织
            PartyOrgVo orgVo = this.orgService.findPartyId(baseInfo.get(0).getOrgId());
            base.setOrgId(orgVo.getOrgId());
            base.setOrgName(orgVo.getOrgName());
            base.setPartyOrgId(orgVo.getPartyId());
            base.setPartyOrgName(orgVo.getPartyName());
            // 根据用户所在支部ID查询党总支ID
            List<OrganizationEntity> parentList = new ArrayList<>();
            this.orgService.getParentEntity(base.getOrgId(), baseInfo.get(0).getOrgType(), parentList, regionId);
            if (!CollectionUtils.isEmpty(parentList)) {
                parentList.forEach(org -> {
                    if (this.orgTypeConfig.getGeneralBranchChild().contains(org.getOrgTypeChild())) {
                        base.setGeneralBranchOrgId(org.getOrganizationId());
                    }
                });
            }
            // 根据行政单位ID查询党组ID
            if (Objects.nonNull(orgVo.getOwnerId())) {
                Example groupExample = new Example(PartyGroupEntity.class);
                groupExample.createCriteria().andEqualTo("orgId", orgVo.getOwnerId())
                        .andEqualTo("regionId", regionId)
                        .andEqualTo("status", Constants.STATUS_YES);
                PartyGroupEntity partyGroupEntity = this.partyGroupMapper.selectOneByExample(groupExample);
                if (Objects.nonNull(partyGroupEntity)) {
                    base.setGroupOrgId(partyGroupEntity.getGroupId());
                }
            }
        }
    }

    /**
     * 根据用户ID查询用户信息
     *
     * @param userId
     * @return
     */
    public List<UserInfoBase> findUserByKey(Long userId, Long regionId) {
        return this.userOrgAndCorpMapper.findUserByKey(Constants.STATUS_YES, Constants.STATUS_YES, userId, regionId);
    }

    public UserEntity getUserInfo(Long userId) {
        Example example = new Example(UserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        return this.userMapper.selectOneByExample(example);
    }


    /**
     * 根据组织id 以及年月 得到快照表用户信息
     * 这里到时候把这个方法加入缓存
     *
     * @param orgId
     * @return
     */
    public Set<Long> getOrgUserIds(Long orgId, String years, String months) {
        return userMapper.getUserInfoByOrgId(orgId, years, months);
    }


    /**
     * 根据组织id 以及年月 得到快照表用户信息
     * 这里到时候把这个方法加入缓存
     *
     * @param orgId
     * @return
     */
    public Set<Long> getOrgCurrentBranchMonthUserIds(Long orgId) {
        return userMapper.getCurrentMonthUserByOrgId(orgId);
    }

    /**
     * 查询当月党小组下面的用户
     *
     * @param orgId
     * @return
     */
    public Set<Long> getOrgCurrentMonthGroupUserIds(Long orgId) {
        return userMapper.getOrgCurrentMonthGroupUserIds(orgId);
    }

    /**
     * 查询当月党小组下面的用户
     *
     * @param orgIds
     * @return
     */
    public Set<Long> getOrgGroupUserIds(String orgIds) {
        return userMapper.getOrgGroupUserIds(orgIds);
    }

    /**
     * 查询当月所月用户数据
     *
     * @return
     */
    public Set<Long> getSuperviseUserIds(List<String> beforeSingleMonth, OrganizationEntity organizationForm) {
        if (orgTypeConfig.checkIsSingleBranch(organizationForm.getOrgTypeChild())) {
            return getOrgCurrentBranchMonthUserIds(organizationForm.getOrganizationId());
        } else if (orgTypeConfig.checkIsCommunistGroup(organizationForm.getOrgTypeChild())) {
            return getOrgCurrentMonthGroupUserIds(organizationForm.getOrganizationId());
        }
        return null;
    }


    /**
     * 根据组织id 得到组织的支委会成员
     *
     * @return
     */
    public Set<Long> getPeriodUserInfo(OrganizationEntity organizationForm) {
        return userMapper.getPeriodUserInfo(organizationForm.getOrganizationId());
    }


    /**
     * 得到用户基础信息
     *
     * @param userIds
     * @return
     */
    public List<UserEntity> getUserInfoByUserIds(Set<Long> userIds) {
        Example example = new Example(UserEntity.class);
        example.selectProperties("phoneSecret", "name", "userId", "phone");
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("userId", userIds);
        return this.userMapper.selectByExample(example);
    }

//    @Cacheable(cacheNames={"window-user"},key = "#root.methodName + '-' + #regionId + '-' + #orgId")
    public OrgUserOverviewInfo getUserOverviewInfo(Long orgId, HeaderHelper.SysHeader header,Long regionId) {
        if (orgId == null) {
            final Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(header.getRegionId());
            orgId = orgData.getOrgId();
        }
        OrgUserOverviewInfo overviewInfo = new OrgUserOverviewInfo();
        final OrgUserNumberInfo info = this.userMapper.getUserNumberInfo(orgId);
        if(null==info){
            return null;
        }
        // 正式党员
        final int officialMember = info.getOfficialMember();
        // 预备党员
        final int prepareMember = info.getPrepareMember();
        // 总党员数量
        final int total = officialMember + prepareMember;
        overviewInfo.setOfficialMember(new OverviewInfo(officialMember, Math.round((float)officialMember/ (total) * 100)));
        overviewInfo.setPrepareMember(new OverviewInfo(prepareMember, Math.round((float)prepareMember/ (total) * 100)));
        // 男党员
        final int man = info.getMan();
        // 女党员
        final int woman = info.getWoman();
        overviewInfo.setMan(new OverviewInfo(man, Math.round((float)man/ (total) * 100)));
        overviewInfo.setWoman(new OverviewInfo(woman, Math.round((float)woman/ (total) * 100)));
        overviewInfo.setTotal(total);
        // 政治生日
        final List<UserInfo> userInfoList = this.userMapper.getUserPoliticalBirthday(orgId);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        userInfoList.forEach(user -> {
            try {
                user.setPartyAge(AgeUtils.Companion.getAge(sdf.parse(user.getPoliticalBirthday())));
            } catch (ParseException e) {
                log.error("计算党龄出现错误");
            }
        });
        overviewInfo.setPoliticalBirthdayNum(userInfoList.size());
        overviewInfo.setUserList(userInfoList);
        return overviewInfo;
    }

//    @Cacheable(cacheNames={"window-user"},key = "#root.methodName + '-' + #regionId + '-' + #orgId")
    public PartyDistributedForm getPartyDistributed(Long orgId, HeaderHelper.SysHeader header,Long regionId) {
        PartyDistributedForm form = new PartyDistributedForm();
        // 年龄
        Long ageOne = tbcPartyMapper.getAgeNumber(orgId, AGE_ONE);
        Long ageTwo = tbcPartyMapper.getAgeNumber(orgId, AGE_TWO);
        Long ageThree = tbcPartyMapper.getAgeNumber(orgId, AGE_THREE);
        Long ageFour = tbcPartyMapper.getAgeNumber(orgId, AGE_FOUR);
        Long total = ageOne + ageTwo + ageThree + ageFour;
        if (total != 0L) {
            // 年龄分布
            form.getAge().add(
                    new DistributedForm(
                            "30岁及以下", ageOne, Math.round((float)ageOne / total * 100), 7
                    )
            );
            form.getAge().add(
                   new DistributedForm(
                            "31岁-45岁", ageTwo, Math.round((float)ageTwo / total * 100), 8
                    )
            );
            form.getAge().add(
                    new DistributedForm(
                            "46岁-55岁", ageThree, Math.round((float)ageThree / total * 100), 9
                    )
            );
            form.getAge().add(
                    new DistributedForm(
                            "55岁以上", ageFour, Math.round((float)ageFour / total * 100), 10
                    )
            );
        }
        // 学历分布
        Long postgraduate = tbcPartyMapper.getBackdropPages(orgId, POSTGRADUATE);
        Long undergraduate = tbcPartyMapper.getBackdropPages(orgId, UNDERGRADUATE);
        Long college = tbcPartyMapper.getBackdropPages(orgId, COLLEGE);
        Long high = tbcPartyMapper.getBackdropPages(orgId, HIGH);
        Long total2 = postgraduate + undergraduate + college + high;
        if (total2 != 0L) {
            form.getEdu().add(
                    new DistributedForm(
                            "硕士及以上", postgraduate, Math.round((float)postgraduate / total2* 100), 3
                    )
            );
            form.getEdu().add(
                    new DistributedForm(
                            "本科", undergraduate, Math.round((float)undergraduate / total2* 100), 4
                    )
            );
            form.getEdu().add(
                    new DistributedForm(
                            "专科", college, Math.round((float)college / total2 * 100), 5
                    )
            );
            form.getEdu().add(
                    new DistributedForm(
                            "高中及以下", high, Math.round((float)high / total2 * 100), 6
                    )
            );
        }
        // 党龄分布
        Long partyAgeOne = tbcPartyMapper.getPartyAgeNumber(orgId, PARTY_AGE_ONE_NEW);
        Long partyAgeTwo = tbcPartyMapper.getPartyAgeNumber(orgId, PARTY_AGE_TWO_NEW);
        Long partyAgeThree = tbcPartyMapper.getPartyAgeNumber(orgId, PARTY_AGE_THREE_NEW);
        Long partyAgeFour = tbcPartyMapper.getPartyAgeNumber(orgId, PARTY_AGE_FOUR_NEW);
        Long total3 = partyAgeOne + partyAgeTwo + partyAgeThree + partyAgeFour;
        if (total3 != 0L) {
            form.getPartyAge().add(
                    new  DistributedForm(
                            "10年及以下", partyAgeOne, Math.round((float)partyAgeOne / total3 * 100), 11
                    )
            );
            form.getPartyAge().add(
                    new DistributedForm(
                            "11年-20年", partyAgeTwo, Math.round((float)partyAgeTwo/ total3* 100), 12
                    )
            );
            form.getPartyAge().add(
                    new DistributedForm(
                            "21年-30年", partyAgeThree, Math.round((float)partyAgeThree/ total3 * 100), 13
                    )
            );
            form.getPartyAge().add(
                    new DistributedForm(
                            "30年以上", partyAgeFour, Math.round((float)partyAgeFour / total3* 100), 14
                    )
            );
        }
        // 民族
        Long ethHan = tbcPartyMapper.getEthnicNum(orgId, ETHNIC_HAN);
        Long ethMiao = tbcPartyMapper.getEthnicNum(orgId, ETHNIC_MIAO);
        Long ethTuJia = tbcPartyMapper.getEthnicNum(orgId, ETHNIC_TUJIA);
        Long ethOther = tbcPartyMapper.getEthnicNum(orgId, ETHNIC_OTHER);
        Long total4 = ethHan + ethMiao + ethTuJia + ethOther;
        if (total4 != 0L) {
            form.getEth().add(
                    new DistributedForm(
                            "汉族", ethHan, Math.round((float)ethHan/ total4* 100), 15
                    )
            );
            form.getEth().add(
                    new DistributedForm(
                            "土家族", ethTuJia, Math.round((float)ethTuJia / total4 * 100), 16
                    )
            );
            form.getEth().add(
                    new DistributedForm(
                            "苗族", ethMiao, Math.round((float)ethMiao/ total4 * 100), 17
                    )
            );

            form.getEth().add(
                    new DistributedForm(
                            "其他", ethOther, Math.round((float)ethOther/ total4 * 100), 18
                    )
            );
        }
        return form;
    }

}
