package com.goodsogood.ows.service.user;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.dss.DssOrgChild;
import com.goodsogood.ows.model.mongodb.dss.DssUserChild;
import com.goodsogood.ows.service.impl.DssPartyBranchBuilder;
import com.goodsogood.ows.service.impl.DssPartyCommitteeBuilder;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 保存下级组织和人员
 * <AUTHOR>
 */
@Service
@Log4j2
public class DssUserService implements DssPartyCommitteeBuilder, DssPartyBranchBuilder{

    private final OrgService orgService;
    private final UserService userService;

    @Autowired
    public DssUserService(OrgService orgService, UserService userService) {
        this.orgService = orgService;
        this.userService = userService;
    }

    @Override
    public PartyCommitteeInfo buildPartyCommittee(PartyCommitteeInfo info) {
        Integer year = info.getYear();
        Long orgId = info.getOrganizationId();
        List<DssOrgChild> dssOrgChild = this.getOrgList(orgId, year);
        info.setOrgList(dssOrgChild);
        info.setSecPartyBranchTotal(dssOrgChild.size());
        return info;
    }

    @Override
    public PartyBranchInfo buildPartyBranch(PartyBranchInfo info) {
        Integer year = info.getYear();
        Long orgId = info.getOrganizationId();
        List<DssUserChild> dssUserChild = this.getUserList(orgId, year);
        info.setUserList(dssUserChild);
        info.setPartyMemberTotal(dssUserChild.size());
        return info;
    }

    private List<DssOrgChild> getOrgList(Long orgId, Integer year) {
        LocalDateTime dateTime = LocalDateTime.now();
        List<DssOrgChild> dssOrgChild = new ArrayList<>();
        if (dateTime.getYear() == year) {
            List<OrganizationEntity> allChildOrg = this.orgService.getChildPartyOrg(orgId, Constants.STATUS_NO);
            if (!CollectionUtils.isEmpty(allChildOrg)) {
                allChildOrg.forEach(org -> {
                    dssOrgChild.add(DssOrgChild.builder().orgId(org.getOrganizationId()).build());
                });
            }
        } else {
            List<OrgSnapshotEntity> allChildOrg = this.orgService.getHistoryOrgChildInfo(orgId, year, null);
            if (!CollectionUtils.isEmpty(allChildOrg)) {
                allChildOrg.forEach(org -> {
                    dssOrgChild.add(DssOrgChild.builder().orgId(org.getOrgId()).build());
                });
            }
        }
        return dssOrgChild;
    }

    private List<DssUserChild> getUserList(Long orgId, Integer year){
        LocalDateTime dateTime = LocalDateTime.now();
        List<DssUserChild> dssUserChild = new ArrayList<>();
        if (dateTime.getYear() == year) {
            List<UserEntity> userEntities = this.userService.getUserByOrg(orgId);
            if (!CollectionUtils.isEmpty(userEntities)) {
                userEntities.forEach(user -> {
                    DssUserChild u = new DssUserChild();
                    u.setUserId(user.getUserId());
                    dssUserChild.add(u);
                });
            }
        } else {
            List<UserSnapshotEntity> userSnapshotEntities = this.userService.getHistoryUserListByOrg(orgId, year);
            if (!CollectionUtils.isEmpty(userSnapshotEntities)) {
                userSnapshotEntities.forEach(user -> {
                    DssUserChild u = new DssUserChild();
                    u.setUserId(user.getUserId());
                    dssUserChild.add(u);
                });
            }
        }
        return dssUserChild;
    }
}
