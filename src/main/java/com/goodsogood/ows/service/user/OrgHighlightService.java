package com.goodsogood.ows.service.user;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.MessageCommon;
import com.goodsogood.ows.common.MessageEnum;
import com.goodsogood.ows.mapper.user.BranchHighlightMapper;
import com.goodsogood.ows.model.db.user.BranchHighlightEntity;
import com.goodsogood.ows.model.vo.MessageVO;
import com.goodsogood.ows.service.IMessageService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2021-10-12 11:02
 **/
@Component(MessageCommon.STYLE)
@Log4j2
public class OrgHighlightService implements IMessageService {

    private final BranchHighlightMapper branchHighlightMapper;
    private final StringRedisTemplate stringRedisTemplate;
    private static final ObjectMapper OBJECTMAPPER = new ObjectMapper();


    public OrgHighlightService(BranchHighlightMapper branchHighlightMapper,
                               StringRedisTemplate stringRedisTemplate) {
        this.branchHighlightMapper = branchHighlightMapper;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @NotNull
    @Override
    public List<MessageVO> generateMessage(long regionId, long rootId,
                                           @Nullable Integer page, @Nullable Integer pageSize) {
        int type=1;
        String  OrgHighlightKey="ORGHIGHTLIGHT"+"_"+regionId+"_"+type+"_"+ page + "_" + pageSize;
        if(stringRedisTemplate.hasKey(OrgHighlightKey)){
            try {
                String json = stringRedisTemplate.opsForValue().get(OrgHighlightKey);
                log.debug("json -> [{}]", json);
                return OBJECTMAPPER.readValue(json, new TypeReference<List<MessageVO>>() {});
            } catch (JsonProcessingException e) {
                log.error("JSON转换异常",e);
            }
        }
        Example example=new Example(BranchHighlightEntity.class);
        example.selectProperties("branchHighlightId", "desc", "url", "nodeTime");
        example.createCriteria().andEqualTo("type", type)
                .andEqualTo("regionId",regionId)
                .andEqualTo("status", 1);
        Page<BranchHighlightEntity> pageResult = PageHelper.startPage(page, pageSize, "node_time desc")
                .doSelectPage(() -> branchHighlightMapper.selectByExample(example));
        List<MessageVO> listMessageVO = new ArrayList<>();
        List<BranchHighlightEntity> result = pageResult.getResult();
        result.forEach(item->{
            MessageVO messageVO = new MessageVO();
            messageVO.setText(item.getDesc());
            messageVO.setType(MessageEnum.STYLE.getType());
            messageVO.setTypeName(MessageEnum.STYLE.getTypeName());
            if(StringUtils.isNotBlank(item.getUrl())) {
                messageVO.setFile(Arrays.asList(item.getUrl().split(",")));
            }
            //扩展字段 节点时间 看前端有需要不
            Map<String, String> map = new HashMap<>();
            map.put("node_time", item.getNodeTime());
            messageVO.setExtend(map);
            listMessageVO.add(messageVO);
        });
        //不为空写缓存
        if(!CollectionUtils.isEmpty(listMessageVO)){
            try {
                String jsonStr = OBJECTMAPPER.writeValueAsString(listMessageVO);
                stringRedisTemplate.opsForValue().set(OrgHighlightKey, jsonStr, 30, TimeUnit.MINUTES);
            }catch (Exception ex){
                log.error("获取组织风彩写入数据出错",ex);
            }
        }
        return listMessageVO;
    }


}
