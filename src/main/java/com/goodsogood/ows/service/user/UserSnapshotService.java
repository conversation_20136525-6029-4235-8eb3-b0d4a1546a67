package com.goodsogood.ows.service.user;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.mapper.user.UserSnapshotMapper;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import com.goodsogood.ows.utils.SqlJointUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.goodsogood.ows.utils.DateUtils.dateFormat;

/**
 * <AUTHOR>
 * @date 2020/11/6
 */
@Log4j2
@Service
public class UserSnapshotService {

    /**
     * 用户快照缓存
     * %s = regionId
     * %s = dateMonth
     * %s = userId
     */
    public static String SNAPSHOT_USER = "SNAPSHOT_USER:%s:%s:%s";

    /**
     * 某个区县，某月的所有非离退休党员
     * %s = regionId
     * %s = dateMonth
     */
    public static String NOT_RETIRED_USER_IDS = "NOT_RETIRED_USER_IDS:%s:%s";

    /**
     * 某个组织，某月的所有非离退休党员（包含下级组织）
     * %s = orgId
     * %s = dateMonth
     */
    public static String ORG_NOT_RETIRED_USER_IDS = "ORG_NOT_RETIRED_USER_IDS:%s:%s";

    private final UserSnapshotMapper userSnapshotMapper;
    private final StringRedisTemplate redisTemplate;
    private final ObjectMapper objectMapper;
    private final Map<Integer, Integer> politicalCodeMap;
    private final Map<Integer, Integer> retireCodeMap;

    @Value("${user-option.retireCode}")
    private String retireCode;

    @Value("${user-option.politicalCode}")
    private String politicalCode;

    @Autowired
    public UserSnapshotService(StringRedisTemplate redisTemplate,
                               UserSnapshotMapper userSnapshotMapper,
                               ObjectMapper objectMapper,
                               @Value("${user-option.retireCode}") String retireCode,
                               @Value("${user-option.politicalCode}") String politicalCode) {
        this.userSnapshotMapper = userSnapshotMapper;
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
        this.politicalCodeMap = new HashMap<>();
        this.retireCodeMap = new HashMap<>();
        for (String s : politicalCode.split(",")) {
            this.politicalCodeMap.put(Integer.valueOf(s), Integer.valueOf(s));
        }
        for (String s : retireCode.split(",")) {
            this.retireCodeMap.put(Integer.valueOf(s), Integer.valueOf(s));
        }
    }

    /**
     * 根据组织获取非离退休人员id（包含下级）
     *
     * @param regionId
     * @param orgId
     * @param dateMonth
     * @return
     */
    public String getNotRetiredUserIdsByOrg(Long regionId, Long orgId, String dateMonth) {
        String cacheKey = String.format(ORG_NOT_RETIRED_USER_IDS, orgId, dateMonth);
        if (redisTemplate.hasKey(cacheKey)) {
            return redisTemplate.opsForValue().get(cacheKey);
        }
        Set<Long> ids = userSnapshotMapper.getNotRetiredUserIdsByOrg(orgId, dateMonth, regionId, retireCode, politicalCode);
        if (ids != null && !ids.isEmpty()) {
            String idStr = SqlJointUtil.collectionToStr(ids, k -> k.toString());
            redisTemplate.opsForValue().set(cacheKey, idStr, 3, TimeUnit.DAYS);
            return idStr;
        }
        return null;
    }

    /**
     * 获取用户快照信息
     *
     * @param regionId
     * @param userId
     * @param dateMonth
     * @return
     */
    public UserSnapshotEntity getUserSnapshot(Long regionId, Long userId, String dateMonth) {
        String cacheKey = String.format(SNAPSHOT_USER, regionId, dateMonth, userId);
        if (redisTemplate.hasKey(cacheKey)) {
            String json = redisTemplate.opsForValue().get(cacheKey);
            try {
                return objectMapper.readValue(json, UserSnapshotEntity.class);
            } catch (IOException e) {
                log.warn("反序列化出错", e);
            } catch (IllegalArgumentException e) {
                log.warn("并发访问问题,未查询到redis key[{}] 的数据",cacheKey, e);
            }
        }
        Example example = new Example(UserSnapshotEntity.class);
        example.createCriteria()
                .andEqualTo("userId", userId)
                .andEqualTo("dateMonth", dateMonth)
                .andEqualTo("regionId", regionId);
        UserSnapshotEntity userSnapshotEntity = userSnapshotMapper.selectOneByExample(example);
        if (userSnapshotEntity != null) {
            try {
                String json = objectMapper.writeValueAsString(userSnapshotEntity);
                redisTemplate.opsForValue().set(cacheKey, json, 30, TimeUnit.MINUTES);
            } catch (JsonProcessingException e) {
                log.warn("序列化出错", e);
            }
        }
        return userSnapshotEntity;
    }

    /**
     * 判断是否是非离退休党员
     * AND us.position_code NOT IN(${retireCode})
     * AND us.political_type IN(${politicalCode})
     */
    public boolean noRetirePartyMember(Long regionId, Long userId, String dateMonth) {
        return !isRetired(regionId, userId, dateMonth);
    }

    /**
     * 批量判断是否为非离退休党员
     *
     * @param regionId
     * @param userIds
     * @param dateMonth
     * @return
     */
    public Map<Long, Boolean> isRetired(Long regionId, List<Long> userIds, String dateMonth) {
        if (userIds == null) {
            return null;
        }
        String cacheKey = String.format(NOT_RETIRED_USER_IDS, regionId, dateMonth);
        Set<Long> userIdsSet = null;
        if (redisTemplate.hasKey(cacheKey)) {
            String cache = redisTemplate.opsForValue().get(cacheKey);
            try {
                userIdsSet = objectMapper.readValue(cache, new TypeReference<Set<Long>>() {
                });
            } catch (JsonProcessingException e) {
                log.error("反序列化失败", e);
            }
        }
        if (userIdsSet == null) {
            userIdsSet = userSnapshotMapper.getNotRetiredUserIds(regionId, dateMonth, retireCode, politicalCode);
            if (userIdsSet != null && !userIdsSet.isEmpty()) {
                try {
                    String json = objectMapper.writeValueAsString(userIdsSet);
                    redisTemplate.opsForValue().set(cacheKey, json, 3, TimeUnit.DAYS);
                } catch (JsonProcessingException e) {
                    log.error("序列化失败", e);
                }
            }
        }
        Map<Long, Boolean> result = new HashMap<>(userIds.size());
        Set<Long> finalUserIdsSet = userIdsSet;
        userIds.forEach(t -> {
            if (finalUserIdsSet == null) {
                result.put(t, Boolean.FALSE);
            } else {
                result.put(t, !finalUserIdsSet.contains(t));
            }
        });
        return result;
    }

    /**
     * 判断是否为非离退休党员
     *
     * @param regionId
     * @param userId
     * @param dateMonth
     * @return
     */
    public boolean isRetired(Long regionId, Long userId, String dateMonth) {
        Map<Long, Boolean> retired = isRetired(regionId, Arrays.asList(userId), dateMonth);
        return retired.get(userId);
    }

    /**
     * 返回非离退休党员的list
     *
     * @return UserSnapshotEntity
     */
    public Set<Long> noRetired(Long regionId, List<Long> userIdList, Integer year, Integer month) {
        Set<Long> noRetiredList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(userIdList)) {
            Map<Long, Boolean> map =
                    isRetired(regionId, userIdList, dateFormat(year, month));
            if (map != null && !map.isEmpty()) {
                map.forEach(
                        (k, v) -> {
                            if (!v) {
                                noRetiredList.add(k);
                            }
                        });
            }
        }
        return noRetiredList;
    }
}
