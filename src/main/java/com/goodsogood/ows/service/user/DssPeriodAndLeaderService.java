package com.goodsogood.ows.service.user;

import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.mapper.user.OrgPeriodMapper;
import com.goodsogood.ows.mapper.user.OrgPeriodMemberMapper;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.model.mongodb.dss.*;
import com.goodsogood.ows.service.impl.DssIndexBuilder;
import com.goodsogood.ows.service.impl.DssPartyBranchBuilder;
import com.goodsogood.ows.service.impl.DssPartyCommitteeBuilder;
import com.goodsogood.ows.service.impl.DssUserBuilder;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.ListUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 辅助决策中支委会与领导班子的返回
 */
@Service
@Log4j2
public class DssPeriodAndLeaderService implements DssIndexBuilder, DssPartyBranchBuilder, DssPartyCommitteeBuilder, DssUserBuilder {

    private final OrgPeriodMapper orgPeriodMapper;
    private final OrgPeriodMemberMapper orgPeriodMemberMapper;
    private final OrgTypeConfig orgTypeConfig;

    @Autowired
    public DssPeriodAndLeaderService(OrgPeriodMapper orgPeriodMapper,
                                     OrgPeriodMemberMapper orgPeriodMemberMapper,
                                     OrgTypeConfig orgTypeConfig) {
        this.orgPeriodMapper = orgPeriodMapper;
        this.orgPeriodMemberMapper = orgPeriodMemberMapper;
        this.orgTypeConfig = orgTypeConfig;
    }

    /**
     * @param info 决策辅助首页实体类
     *             info.rootId 顶级党组织ID
     *             info.year   生成年份
     * @return
     */
    @Override
    public IndexInfo buildIndex(IndexInfo info) {
        return info;
    }

    /**
     * @param info 决策辅助党支部详情页实体类
     *             info.organizationId 组织ID
     *             info.year   生成年份
     */
    @Override
    public PartyBranchInfo buildPartyBranch(PartyBranchInfo info) {
//        assert info != null && info.getRegionId() != null && info.getOrganizationId() != null && info.getYear() != null;
//        //党组织届次
//        info.setOrgPeriodInfo(orgPeriodMapper.getSecretaryInfo(info.getOrganizationId(), info.getYear()));
        return info;
//        OrgPeriodInfo orgPeriodInfo = info.getOrgPeriodInfo();
//        if(orgPeriodInfo==null){
//            orgPeriodInfo=new OrgPeriodInfo();
//            info.setOrgPeriodInfo(orgPeriodInfo);
//        }
//        //设置书记副书记
//        SecretaryForm secretaryInfo = orgPeriodMapper.getSecretaryInfo(info.getOrganizationId(), info.getYear());
//        if (secretaryInfo != null) {
//            orgPeriodInfo.setPartyLeader(secretaryInfo.getSecretary());
//            orgPeriodInfo.setPartyLeaderSec(secretaryInfo.getDeputySecretary());
//        }
        //设置领导班子
//        info.setLeaderGroups(orgPeriodMapper.getLeaderInfo(info.getOrganizationId(), info.getYear()));
//        return info;
    }

    /**
     * @param infoList 决策辅助党支部详情页实体类列表
     *                 info.organizationId 组织ID
     *                 info.year   生成年份
     */
    @Override
    public List<PartyBranchInfo> buildPartyBranchList(List<PartyBranchInfo> infoList) {
        assert !CollectionUtils.isEmpty(infoList) && infoList.get(0).getYear() != null;
        try {
            Integer year = infoList.get(0).getYear();
            String join = ListUtils.join(",", infoList.stream().map(PartyBranchInfo::getOrganizationId).collect(Collectors.toList()));
            //党组织届次信息
            List<OrgDssUser> nowSecretaryInfo = orgPeriodMapper.getNowSecretaryInfo(join, year);
            if (!CollectionUtils.isEmpty(nowSecretaryInfo)) {
                HashMap<Long, OrgPeriodInfo> map = new HashMap<>();
                for (OrgDssUser orgDssUser : nowSecretaryInfo) {
                    OrgPeriodInfo orgPeriodInfo = getOrgPeriodInfo(map, orgDssUser.getOrgId(), orgDssUser.getValidDate());
                    DssUser dssUser = new DssUser();
                    BeanUtils.copyProperties(orgDssUser, dssUser);
                    orgPeriodInfo.setOrgType(this.getOrgTypeStr(infoList.get(0).getOrgTypeChild()));
                    switch (orgDssUser.getType()) {
                        case 1:
                            orgPeriodInfo.getPartyLeader().add(dssUser);
                            break;
                        case 2:
                            orgPeriodInfo.getPartyLeaderSec().add(dssUser);
                            break;
                        case 3:
                            orgPeriodInfo.getLeaderOtherGroups().add(dssUser);
                            break;
                        default:
                            throw new NullPointerException();
                    }
                }
                infoList.forEach(x -> x.setOrgPeriodInfo(map.get(x.getOrganizationId())));
            }
            //领导干部信息
            List<OrgBaseLeaderInfo> orgBaseLeaderInfos = orgPeriodMapper.getBaseOrgLeaderInfo(year, DateUtils.getValidMonth(year), join);
            if (!CollectionUtils.isEmpty(orgBaseLeaderInfos)) {
                HashMap<Long, List<BranchLeaderInfo>> map = new HashMap<>();
                for (OrgBaseLeaderInfo orgBaseLeaderInfo : orgBaseLeaderInfos) {
                    List<BranchLeaderInfo> branchLeaderInfos = getOrgLeaderInfo(map, orgBaseLeaderInfo.getFromOrg());
                    BranchLeaderInfo branchLeaderInfo = new BranchLeaderInfo();
                    BeanUtils.copyProperties(orgBaseLeaderInfo, branchLeaderInfo);
                    branchLeaderInfos.add(branchLeaderInfo);
                }
                infoList.forEach(x -> x.setLeaderList(map.get(x.getOrganizationId())));
            }
            return infoList;
        } catch (Exception e) {
            log.error("DssPeriodAndLeaderService.buildPartyBranchList 出错[{}]", e.getMessage());
            throw e;
        }
    }


    /**
     * @param info 决策辅助党委详情页实体类
     *             info.organizationId 组织ID
     *             info.year   生成年份
     * @return
     */
    @Override
    public PartyCommitteeInfo buildPartyCommittee(PartyCommitteeInfo info) {
        assert info != null && info.getRegionId() != null && info.getOrganizationId() != null && info.getYear() != null && info.getOrgTypeChild() != null;
        try {
            //机关党委的领导班子
            List<LeaderInfo> leaderInfoNew = orgPeriodMapper.getLeaderInfoNew(info.getOrganizationId(), info.getYear(), info.getRegionId());
            if (!CollectionUtils.isEmpty(leaderInfoNew)) {
                info.setLeaderList(leaderInfoNew);
            }

            //党组织届次
            List<OrgDssUser> nowSecretaryInfo = orgPeriodMapper.getNowSecretaryInfo(info.getOrganizationId().toString(), info.getYear());
            if (!CollectionUtils.isEmpty(nowSecretaryInfo)) {
                OrgPeriodInfo orgPeriodInfo = new OrgPeriodInfo();
                orgPeriodInfo.setPartyLeaderSec(new ArrayList<>());
                orgPeriodInfo.setPartyLeader(new ArrayList<>());
                orgPeriodInfo.setLeaderOtherGroups(new ArrayList<>());
                orgPeriodInfo.setValidTime(nowSecretaryInfo.get(0).getValidDate());
                orgPeriodInfo.setOrgType(this.getOrgTypeStr(info.getOrgTypeChild()));
                info.setOrgPeriodInfo(orgPeriodInfo);
                nowSecretaryInfo.forEach(x -> {
                    DssUser dssUser = new DssUser();
                    BeanUtils.copyProperties(x, dssUser);
                    switch (x.getType()) {
                        case 1:
                            orgPeriodInfo.getPartyLeader().add(dssUser);
                            break;
                        case 2:
                            orgPeriodInfo.getPartyLeaderSec().add(dssUser);
                            break;
                        case 3:
                            orgPeriodInfo.getLeaderOtherGroups().add(dssUser);
                            break;
                        default:
                            throw new NullPointerException();
                    }
                });
            }
            return info;
        } catch (Exception e) {
            log.error("DssPeriodAndLeaderService.buildPartyCommittee 出错[{}]", e.getMessage());
            throw e;
        }
//        info.setOrgPeriodInfo(orgPeriodMapper.getSecretaryInfo(info.getOrganizationId(), info.getYear()));
//        OrgPeriodInfo orgPeriodInfo = info.getOrgPeriodInfo();
//        if (orgPeriodInfo == null) {
//            orgPeriodInfo = new OrgPeriodInfo();
//            info.setOrgPeriodInfo(orgPeriodInfo);
//        }
//        //设置书记副书记
//        SecretaryForm secretaryInfo = orgPeriodMapper.getSecretaryInfo(info.getOrganizationId(), info.getYear());
//        if (secretaryInfo != null) {
//            orgPeriodInfo.setPartyLeader(secretaryInfo.getSecretary());
//            orgPeriodInfo.setPartyLeaderSec(secretaryInfo.getDeputySecretary());
//        }
//        //设置领导班子
////        info.setLeaderOtherGroups(orgPeriodMapper.getLeaderInfo(info.getOrganizationId(), info.getYear()));
//        return info;
    }

    /**
     * @param info 决策辅助用户详情页实体类
     *             info.organizationId 组织ID
     *             info.year   生成年份
     * @return
     */
    @Override
    public UserInfo buildUser(UserInfo info) {
        assert info != null && info.getUserId() != null && info.getRegionId() != null && info.getYear() != null;
        info.setPartyPosition(orgPeriodMemberMapper.getPartyPosition(info.getYear(), info.getRegionId(), info.getUserId()));
        return info;
    }


    /**
     * 初始化届次对象
     *
     * @param map
     * @param orgId
     * @param validTime
     * @return
     */
    private OrgPeriodInfo getOrgPeriodInfo(HashMap<Long, OrgPeriodInfo> map, Long orgId, String validTime) {
        OrgPeriodInfo orgPeriodInfo = map.get(orgId);
        if (orgPeriodInfo == null) {
            orgPeriodInfo = new OrgPeriodInfo();
            orgPeriodInfo.setLeaderOtherGroups(new ArrayList<>());
            orgPeriodInfo.setPartyLeader(new ArrayList<>());
            orgPeriodInfo.setPartyLeaderSec(new ArrayList<>());
            orgPeriodInfo.setValidTime(validTime);
            map.put(orgId, orgPeriodInfo);
        }
        return orgPeriodInfo;
    }

    /**
     * 初始化党支部领导干部对象
     *
     * @param map
     * @param fromOrg
     * @return
     */
    private List<BranchLeaderInfo> getOrgLeaderInfo(HashMap<Long, List<BranchLeaderInfo>> map, Long fromOrg) {
        List<BranchLeaderInfo> branchLeaderInfos = map.get(fromOrg);
        if (CollectionUtils.isEmpty(branchLeaderInfos)) {
            branchLeaderInfos = new ArrayList<>();
            map.put(fromOrg, branchLeaderInfos);
        }
        return branchLeaderInfos;
    }

    private String getOrgTypeStr(Integer orgTypeChild) {
        String orgType;
        if (this.orgTypeConfig.getCommunistChild().contains(orgTypeChild)) {
            orgType = "党委";
        } else if (this.orgTypeConfig.getGeneralBranchChild().contains(orgTypeChild)) {
            orgType = "党总支";
        } else {
            orgType = "党支部";
        }
        return orgType;
    }
}
