package com.goodsogood.ows.service.user;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.common.MessageCommon;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.vo.MessageVO;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.service.IMessageService;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName : PartyPositionsMessageService
 * <AUTHOR> tc
 * @Date: 2021/10/12 16:58
 * @Description : 党建阵地资讯服务类
 */
@Component(MessageCommon.POSITION)
@Log4j2
public class PartyPositionsMessageService implements IMessageService {

    private final StringRedisTemplate redisTemplate;
    private final RestTemplate restTemplate;
    private final TogServicesConfig togServicesConfig;

    public PartyPositionsMessageService(StringRedisTemplate redisTemplate, RestTemplate restTemplate, TogServicesConfig togServicesConfig) {
        this.redisTemplate = redisTemplate;
        this.restTemplate = restTemplate;
        this.togServicesConfig = togServicesConfig;
    }


    @NotNull
    @Override
    public List<MessageVO> generateMessage(long regionId, long rootId, @Nullable Integer page, @Nullable Integer pageSize) {
        List<MessageVO> re = new ArrayList<>();
        String redisValue = "";
        try {
            //查询缓存是否存在
            //key 前缀_page_pageSize
            String redisKey = "SAS_PPGM_"+page+"_"+pageSize;
            redisValue = redisTemplate.opsForValue().get(redisKey);
            if(!StringUtils.isEmpty(redisValue)){
                re = (List<MessageVO>) JsonUtils.fromJson(redisValue,ArrayList.class,MessageVO.class);
            }else {
                //远程调用获取信息
                re = this.getMessageVO(regionId, page, pageSize);
                //补充缓存
                if(re!=null&&re.size()>0){
                    redisTemplate.opsForValue().set(redisKey,JsonUtils.toJson(re),30, TimeUnit.MINUTES);
                }
            }
        }catch (Exception e){
            log.error("获取党建阵地资讯报错！",e);
        }
        return re;
    }

    private List<MessageVO> getMessageVO(Long regionId,Integer page,Integer pageSize) {
        List<MessageVO> res=null;
        HttpHeaders headers = new HttpHeaders();
        //加入区县编号
        headers.add("_region_id",String.valueOf(regionId));
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        int count = 0;
        do {
            try {
                res = RemoteApiHelper.get(restTemplate, String.format("http://%s/partyPositions/generate/message/for/sas?region_id=%s&page_no=%s&page_size=%s",
                        togServicesConfig.getUserCenter(),regionId,page,pageSize), headers, new TypeReference<Result<List<MessageVO>>>() {});
            } catch (Exception e) {
                log.error("<获取党建阵地资讯> 失败！ regionId ={} page ={} pageSize={} 第{}次调用", regionId, page, pageSize,(count+1), e);
            }
            count++;
        } while ((null == res||res.size()==0) && count < 5);
        log.debug("<获取党建阵地资讯> 结果:查询 regionId ={} page ={} pageSize={}  结果 res ={} 调用数{}次", regionId, page, pageSize, res,count);
        return res;
    }
}
