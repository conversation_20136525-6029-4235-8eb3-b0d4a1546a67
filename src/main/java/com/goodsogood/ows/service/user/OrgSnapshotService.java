package com.goodsogood.ows.service.user;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.mapper.rank.SnapshotMapper;
import com.goodsogood.ows.mapper.user.OrgSnapshotMapper;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.SqlJointUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/11/6
 */
@Log4j2
@Service
public class OrgSnapshotService {

    /**
     * 组织快照缓存
     * %s = dateMonth
     * %s = orgId
     */
    public static String SNAPSHOT_ORG = "SNAPSHOT_ORG:%s:%s";

    /**
     * 组织下的用户总数
     * %s = dateMonth
     * %s = orgId
     */
    public static String USER_TOTAL_CACHE = "ORG_SNAPSHOT_USER_TOTAL:%s:%s";

    /**
     * 某个区县，某个月的所有基层党组织
     * %s = regionId
     * %s = monthDate
     */
    public static String BASE_ORG_IDS = "BASE_ORG_IDS:%s:%s";

    /**
     * 某个组织，某个月的所有基层党组织（包含下级）
     * %s = orgId
     * %s = monthDate
     */
    public static String BASE_ORG_INCLUDE_CHILD_ORG_IDS = "BASE_ORG_INCLUDE_CHILD_ORG_IDS:%s:%s";

    private final OrgSnapshotMapper orgSnapshotMapper;
    private final StringRedisTemplate redisTemplate;
    private final ObjectMapper objectMapper;
    private final Set<Integer> basicChildSet;
    private String orgTypeChild = "";
    private final SnapshotMapper snapshotMapper;
    @Value("${user-option.retireCode}")
    private String retireCode;

    @Value("${user-option.politicalCode}")
    private String politicalCode;

    private static final Integer START_YEAR = 2019;

    @Autowired
    public OrgSnapshotService(OrgSnapshotMapper orgSnapshotMapper,
                              StringRedisTemplate redisTemplate,
                              ObjectMapper objectMapper,
                              OrgTypeConfig orgTypeConfig,
                              SnapshotMapper snapshotMapper) {
        this.orgSnapshotMapper = orgSnapshotMapper;
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
        this.snapshotMapper = snapshotMapper;
        this.basicChildSet = new HashSet<>();
        orgTypeConfig.getBasicChild().forEach(t -> {
            orgTypeChild += "," + t;
            this.basicChildSet.add(t);
        });
        orgTypeChild = orgTypeChild.replaceFirst(",", "");
    }

    /**
     * 根据月份获取某个组织的总人数（不包含下级组织）
     *
     * @param orgId     组织id
     * @param dateMonth 月份
     * @return
     */
    public Long getOrgUserTotal(Long orgId, String dateMonth) {
        String cacheKey = String.format(USER_TOTAL_CACHE, dateMonth, orgId);
        if (redisTemplate.hasKey(cacheKey)) {
            return Long.valueOf(redisTemplate.opsForValue().get(cacheKey));
        }
        Long total = orgSnapshotMapper.getOrgUserTotal(3L, orgId, dateMonth, retireCode, politicalCode);
        if (total == null) {
            total = 0L;
        } else {
            redisTemplate.opsForValue().set(cacheKey, String.valueOf(total), 30, TimeUnit.MINUTES);
        }
        return total;
    }

    /**
     * 批量判断是否为基层党组织
     *
     * @param regionId  区县id
     * @param orgIds    组织id集合
     * @param dateMonth 月份
     * @return
     */
    public Map<Long, Boolean> isBaseOrg(Long regionId, List<Long> orgIds, String dateMonth) {
        if (orgIds == null || orgIds.isEmpty()) {
            return null;
        }
        String cacheKey = String.format(BASE_ORG_IDS, regionId, dateMonth);
        Set<Long> orgIdsSet = null;
        if (redisTemplate.hasKey(cacheKey)) {
            String cache = redisTemplate.opsForValue().get(cacheKey);
            try {
                orgIdsSet = objectMapper.readValue(cache, new TypeReference<Set<Long>>() {
                });
            } catch (JsonProcessingException e) {
                log.error("反序列化失败", e);
            }
        }
        if (orgIdsSet == null) {
            orgIdsSet = orgSnapshotMapper.getBaseOrgIds(regionId, dateMonth, orgTypeChild);
            if (orgIdsSet != null && !orgIdsSet.isEmpty()) {
                try {
                    String json = objectMapper.writeValueAsString(orgIdsSet);
                    redisTemplate.opsForValue().set(cacheKey, json, 3, TimeUnit.DAYS);
                } catch (JsonProcessingException e) {
                    log.error("序列化失败", e);
                }
            }
        }
        Map<Long, Boolean> result = new HashMap<>(orgIds.size());
        Set<Long> finalOrgIdsSet = orgIdsSet;
        orgIds.forEach(t -> {
            if (finalOrgIdsSet == null) {
                result.put(t, Boolean.FALSE);
            } else {
                result.put(t, finalOrgIdsSet.contains(t));
            }
        });
        return result;
    }

    /**
     * 是否基层党组织
     *
     * @param regionId  区县id
     * @param orgId     组织id
     * @param dateMonth 月份
     * @return
     */
    public Boolean isBaseOrg(Long regionId, Long orgId, String dateMonth) {
        Map<Long, Boolean> baseOrg = isBaseOrg(regionId, Arrays.asList(orgId), dateMonth);
        return baseOrg.get(orgId);
    }

    /**
     * 获取组织快照信息
     *
     * @param orgId     组织id
     * @param dateMonth 月份
     * @return
     */
    public OrgSnapshotEntity getOrgSnapshot(Long orgId, String dateMonth) {
        String cacheKey = String.format(SNAPSHOT_ORG, dateMonth, orgId);
        if (redisTemplate.hasKey(cacheKey)) {
            String json = redisTemplate.opsForValue().get(cacheKey);
            try {
                return objectMapper.readValue(json, OrgSnapshotEntity.class);
            } catch (IOException e) {
                log.warn("反序列化出错", e);
            }
        }
        Example example = new Example(UserSnapshotEntity.class);
        example.createCriteria()
                .andEqualTo("orgId", orgId)
                .andEqualTo("dateMonth", dateMonth);
        OrgSnapshotEntity orgSnapshotEntity = orgSnapshotMapper.selectOneByExample(example);
        if (orgSnapshotEntity != null) {
            try {
                String json = objectMapper.writeValueAsString(orgSnapshotEntity);
                redisTemplate.opsForValue().set(cacheKey, json, 30, TimeUnit.MINUTES);
            } catch (JsonProcessingException e) {
                log.warn("序列化出错", e);
            }
        }
        return orgSnapshotEntity;
    }

    /**
     * 某个组织，某个月的所有基层党组织（包含下级）
     *
     * @param regionId
     * @param dateMonth
     * @param orgId
     * @return
     */
    public String getBaseOrgIncludeChildOrgIds(Long regionId,
                                               String dateMonth,
                                               Long orgId) {
        String cacheKey = String.format(BASE_ORG_INCLUDE_CHILD_ORG_IDS, orgId, dateMonth);
        if (redisTemplate.hasKey(cacheKey)) {
            return redisTemplate.opsForValue().get(cacheKey);
        }
        Set<Long> ids = orgSnapshotMapper.getBaseOrgIncludeChildOrgIds(regionId, dateMonth, orgId);
        if (ids != null && !ids.isEmpty()) {
            String idStr = SqlJointUtil.collectionToStr(ids, k -> k.toString());
            redisTemplate.opsForValue().set(cacheKey, idStr, 3, TimeUnit.DAYS);
            return idStr;
        }
        return null;
    }

    /**
     * 返回非离退休党员的list
     *
     * @return Set<Long>
     */
    public Set<Long> baseOrg(Long regionId, List<Long> orgIdList, Integer year, Integer month) {
        Set<Long> baseOrg = new HashSet<>();
        if (CollectionUtils.isNotEmpty(orgIdList)) {
            Map<Long, Boolean> map =
                    isBaseOrg(regionId, orgIdList, DateUtils.dateFormat(year, month));
            if (map != null && !map.isEmpty()) {
                map.forEach(
                        (k, v) -> {
                            if (v) {
                                baseOrg.add(k);
                            }
                        });
            }
        }
        return baseOrg;
    }
}
