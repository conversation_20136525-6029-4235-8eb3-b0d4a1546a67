package com.goodsogood.ows.service.user;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.ExcludeOrgConfig;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.ScoreConfig;
import com.goodsogood.ows.mapper.rank.OrgScoreMapper;
import com.goodsogood.ows.mapper.user.*;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.db.user.PartyGroupEntity;
import com.goodsogood.ows.model.mongodb.MeetingInfo;
import com.goodsogood.ows.model.mongodb.dss.DssOrgChild;
import com.goodsogood.ows.model.mongodb.dss.OfficeOrg;
import com.goodsogood.ows.model.mongodb.dss.OrgDssUser;
import com.goodsogood.ows.model.mongodb.dss.YearGrand;
import com.goodsogood.ows.model.mongodb.report.MeetingReportInfo;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.rank.Month;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.model.vo.rank.TotalScoreVo;
import com.goodsogood.ows.model.vo.sas.OrgGroupForm;
import com.goodsogood.ows.model.vo.sas.OrgScoreVo;
import com.goodsogood.ows.model.vo.sas.OrganizationForm;
import com.goodsogood.ows.model.vo.sas.PartyOrgVo;
import com.goodsogood.ows.model.vo.user.OrgUserCountForm;
import com.goodsogood.ows.model.vo.user.OrgUserCountQueryForm;
import com.goodsogood.ows.service.RegionService;
import com.goodsogood.ows.service.autoScore.ComputeService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.ListUtils;
import com.goodsogood.ows.utils.NumberUtils;
import com.goodsogood.ows.utils.SqlJointUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 组织服务层
 * @date 2019/11/19
 */
@Service
@Log4j2
public class OrgService {

    private final OrganizationMapper organizationMapper;
    private final OrgGroupMapper orgGroupMapper;
    private final StringRedisTemplate redisTemplate;
    private final OrgTypeConfig orgTypeConfig;
    private final UserOrgAndCorpMapper userOrgAndCorpMapper;
    private final SimpleApplicationConfigHelper applicationConfigHelper;
    private final ExcludeOrgConfig excludeOrgConfig;
    private final OrgSnapshotMapper orgSnapshotMapper;
    private final OrgPeriodMapper orgPeriodMapper;
    private final ScoreConfig scoreConfig;
    private final ComputeService computeService;
    private final OrgScoreMapper orgScoreMapper;
    private final RegionService regionService;
    private final PartyGroupMapper partyGroupMapper;

    @Autowired
    public OrgService(OrganizationMapper organizationMapper, OrgGroupMapper orgGroupMapper,
                      StringRedisTemplate redisTemplate, OrgTypeConfig orgTypeConfig,
                      UserOrgAndCorpMapper userOrgAndCorpMapper,
                      SimpleApplicationConfigHelper applicationConfigHelper,
                      ExcludeOrgConfig excludeOrgConfig,
                      OrgSnapshotMapper orgSnapshotMapper,
                      OrgPeriodMapper orgPeriodMapper,
                      ScoreConfig scoreConfig,
                      ComputeService computeService, OrgScoreMapper orgScoreMapper,
                      RegionService regionService, PartyGroupMapper partyGroupMapper) {
        this.organizationMapper = organizationMapper;
        this.orgGroupMapper = orgGroupMapper;
        this.redisTemplate = redisTemplate;
        this.orgTypeConfig = orgTypeConfig;
        this.userOrgAndCorpMapper = userOrgAndCorpMapper;
        this.applicationConfigHelper = applicationConfigHelper;
        this.excludeOrgConfig = excludeOrgConfig;
        this.orgSnapshotMapper = orgSnapshotMapper;
        this.orgPeriodMapper = orgPeriodMapper;
        this.scoreConfig = scoreConfig;
        this.computeService = computeService;
        this.orgScoreMapper = orgScoreMapper;
        this.regionService = regionService;
        this.partyGroupMapper = partyGroupMapper;
    }

    /**
     * 根据组织ID查询所有下级组织
     *
     * @param orgId     查询ID
     * @param isInclude 是否包含本身
     * @return
     */
    public LinkedList<OrganizationEntity> findAllChildOrg(Long orgId, Integer isInclude, Long regionId, boolean excludeRetire) {
        LinkedList<OrganizationEntity> orgList = null;
        Example ex = new Example(OrganizationEntity.class);
        ex.createCriteria().andEqualTo("organizationId", orgId);
        OrganizationEntity oe = this.organizationMapper.selectOneByExample(ex);
        if (null != oe && null != oe.getOrganizationId()) {
            Example orgEx = new Example(OrganizationEntity.class);
            Example.Criteria criteria = orgEx.createCriteria();
            criteria.andLike("orgLevel", "%-" + orgId + "-%");
            if (regionId != null) {
                criteria.andEqualTo("regionId", regionId);
            }
            criteria.andEqualTo("status", Constants.STATUS_YES);
            if (Constants.STATUS_YES.equals(isInclude)) {
                criteria.orEqualTo("organizationId", orgId);
            }
            if (excludeRetire) {
                criteria.andCondition(" ( is_retire IS NULL OR is_retire != 1 ) ");
            }
            orgList = new LinkedList<>(this.organizationMapper.selectByExample(orgEx));
            log.debug("查询组织[{}], 下级总共[{}]个组织", orgId, orgList.size());
        }
        return orgList;
    }

    /**
     * 根据组织ID查询下一级组织
     *
     * @param orgId                     查询ID
     * @param isOnlyCommAndBranch       是否只查党委总支和支部
     * @return
     */
    public LinkedList<OrganizationEntity> findAllNextChildOrg(Long orgId, Long regionId, boolean excludeRetire,boolean isOnlyCommAndBranch,boolean isNext) {
        LinkedList<OrganizationEntity> orgList = null;
        Example ex = new Example(OrganizationEntity.class);
        ex.createCriteria().andEqualTo("organizationId", orgId);
        OrganizationEntity oe = this.organizationMapper.selectOneByExample(ex);
        if (null != oe && null != oe.getOrganizationId()) {
            Example orgEx = new Example(OrganizationEntity.class);
            Example.Criteria criteria = orgEx.createCriteria();
            if(isNext){
                criteria.andEqualTo("orgLevel", oe.getOrgLevel() + oe.getOrganizationId() + "-");
            }else{
                criteria.andLike("orgLevel", "%-" + orgId + "-%");
            }
            if (regionId != null) {
                criteria.andEqualTo("regionId", regionId);
            }
            if(isOnlyCommAndBranch){
                List<Integer> AllOrgTypeChild = new ArrayList<>();
                AllOrgTypeChild.addAll(orgTypeConfig.getCommunistChild());
                AllOrgTypeChild.addAll(orgTypeConfig.getGeneralBranchChild());
                AllOrgTypeChild.addAll(orgTypeConfig.getBranchChild());
                criteria.andIn("orgTypeChild",AllOrgTypeChild);
            }
            criteria.andEqualTo("status", Constants.STATUS_YES);
            if (excludeRetire) {
                criteria.andCondition(" ( is_retire IS NULL OR is_retire != 1 ) ");
            }
            orgList = new LinkedList<>(this.organizationMapper.selectByExample(orgEx));
            log.debug("查询组织[{}], 下级总共[{}]个组织", orgId, orgList.size());
        }
        return orgList;
    }

    /**
     * 根据Id查询对象
     *
     * @param orgId
     * @return
     */
    public OrganizationEntity getById(Long orgId) {
        String redisKey = Global.CACHE_ORG_ID_PREFIX + orgId;
        if (this.redisTemplate.hasKey(redisKey)) {
            return Utils.fromJson(this.redisTemplate.opsForValue().get(redisKey), OrganizationEntity.class);
        } else {
            return organizationMapper.selectByPrimaryKey(orgId);
        }
    }

    /**
     * 根据组织ID查询顶级党委组织ID
     *
     * @param orgId
     * @return
     */
    public PartyOrgVo findPartyId(long orgId) {
        OrganizationEntity orgEntity = this.getById(orgId);
        Region.OrgData orgData = this.applicationConfigHelper.getOrgByRegionId(orgEntity.getRegionId());
        if (null == orgEntity) {
            return new PartyOrgVo();
        }
        PartyOrgVo result = new PartyOrgVo();
        result.setOrgId(orgId);
        result.setOrgName(orgEntity.getName());
        result.setOwnerId(orgEntity.getOwnerId());
        if (orgData.getOrgId().equals(orgEntity.getParentId())) {
            result.setPartyId(orgEntity.getOrganizationId());
            result.setPartyName(orgEntity.getName());
            result.setPartyShortName(orgEntity.getShortName());
        } else if (orgData.getOrgId().equals(orgEntity.getParentId())) {
            result.setPartyId(orgEntity.getParentId());
            result.setPartyName(orgEntity.getParentId().toString());
            result.setPartyShortName(orgEntity.getShortName());
        } else if (orgData.getOrgId().equals(orgEntity.getOrganizationId())) {
            result.setPartyId(orgEntity.getOrganizationId());
            result.setPartyName(orgEntity.getName());
            result.setPartyShortName(orgEntity.getShortName());
        } else {
            OrganizationEntity partyEntity = this.getParentIdByOrgId(orgEntity.getParentId(), orgData.getOrgId());
            if (partyEntity != null) {
                result.setPartyId(partyEntity.getOrganizationId());
                result.setPartyName(partyEntity.getName());
                result.setPartyShortName(partyEntity.getShortName());
            }
        }
        return result;
    }

    /**
     * 根据组织查询父级组织
     *
     * @param orgId
     * @param rootOrgId
     * @return
     */
    private OrganizationEntity getParentIdByOrgId(long orgId, Long rootOrgId) {
        String orgKey = Global.CACHE_ORG_ID_PREFIX + orgId;
        if (this.redisTemplate.hasKey(orgKey)) {
            OrganizationEntity org = Utils.fromJson(this.redisTemplate.opsForValue().get(orgKey), OrganizationEntity.class);
            // 改为返回上级组织为3-市级机关的组织
            if (org.getOrganizationId().equals(rootOrgId)
                    || org.getParentId().equals(rootOrgId)) {
                return org;
            } else if (Constants.EX_ORG_ID.equals(org.getParentId())
                    || Constants.EX_ORG_ID.equals(org.getOrganizationId())) {
                org.setOrganizationId(Constants.EX_ORG_ID);
                org.setName(Constants.EX_ORG_ID.toString());
                return org;
            } else {
                return getParentIdByOrgId(org.getParentId(), rootOrgId);
            }
        } else {
            Example example = new Example(OrganizationEntity.class);
            example.createCriteria().andEqualTo("organizationId", orgId)
                    .andEqualTo("status", Constants.STATUS_YES);
            OrganizationEntity parentEntity = this.organizationMapper.selectOneByExample(example);
            if (parentEntity == null) {
                return null;
            } else {
                // 如果组织的上级组织是 3-市级机关，则返回
                if (rootOrgId.equals(parentEntity.getParentId())
                        || rootOrgId.equals(parentEntity.getOrganizationId())) {
                    return parentEntity;
                } else if (Constants.EX_ORG_ID.equals(parentEntity.getParentId())
                        || Constants.EX_ORG_ID.equals(parentEntity.getOrganizationId())) {
                    parentEntity.setName(Constants.EX_ORG_ID.toString());
                    parentEntity.setOrganizationId(Constants.EX_ORG_ID);
                    return parentEntity;
                } else {
                    return getParentIdByOrgId(parentEntity.getParentId(), rootOrgId);
                }
            }
        }
    }

    /**
     * 根据组织ID查询所有下级组织
     *
     * @param orgId
     * @return
     */
    public Integer findAllChildOrgCount(Long orgId, Integer orgType, Integer orgTypeChild, Integer isInclude) {
        // 根据组织ID查询所有下级组织ID
        Example ex = new Example(OrganizationEntity.class);
        ex.createCriteria().andEqualTo("organizationId", orgId);
        OrganizationEntity oe = this.organizationMapper.selectOneByExample(ex);
        Integer count = 0;
        if (oe != null && oe.getOrganizationId() != null) {
            Example orgEx = new Example(OrganizationEntity.class);
            Example.Criteria criteria = orgEx.createCriteria();
            criteria.andLike("orgLevel", "%-" + orgId + "-%");
            criteria.andNotEqualTo("parentId", -999L);
            criteria.andEqualTo("status", Constants.STATUS_YES);
            if (null != orgType) {
                criteria.andEqualTo("orgType", orgType);
            }
            if (null != orgTypeChild) {
                criteria.andEqualTo("orgTypeChild", orgTypeChild);
            }
            if (Constants.STATUS_YES.equals(isInclude)) {
                criteria.orEqualTo("organizationId", orgId);
            }
            count = this.organizationMapper.selectCountByExample(orgEx);
        }
        return count;
    }

    public MeetingReportInfo.OrgNum getOrgType(List<MeetingInfo> meetingInfoList) {
        MeetingReportInfo.OrgNum orgNum = new MeetingReportInfo().new OrgNum();
        // 党组织数量
        int orgCount = 0;
        // 党委、党总支部、党支部数量
        int otherOrgNumber = 0;
        int partyCommitteesNumber = 0;
        int branchNumber = 0;
        // 从配置文件获取类型列表
        List<Integer> communistChildList = this.orgTypeConfig.getCommunistChild();
        List<Integer> branchChildList = this.orgTypeConfig.getBranchChild();
        List<Long> orgIdList = new ArrayList<>();
        for (MeetingInfo meetingInfo : meetingInfoList) {
            Long orgId = meetingInfo.getOrgId();
            if (!orgIdList.contains(orgId)) {
                Integer orgType = meetingInfo.getOrgType();
                Integer orgTypeChild = meetingInfo.getOrgTypeChild();
                if (orgType.equals(Constants.ORG_TYPE_COMMUNIST_INT)) {
                    orgCount += 1;
                }
                if (communistChildList.contains(orgTypeChild)) {
                    partyCommitteesNumber += 1;
                } else if (branchChildList.contains(orgTypeChild)) {
                    branchNumber += 1;
                } else {
                    otherOrgNumber += 1;
                }
                orgIdList.add(orgId);
            }
        }
        orgNum.setCommunistNum(orgCount);
        orgNum.setPartyNum(partyCommitteesNumber);
        orgNum.setBranchNum(branchNumber);
        orgNum.setOtherNum(otherOrgNumber);

        return orgNum;
    }

    /**
     * 查询组织直接下级
     *
     * @param
     * @return java.util.List<com.goodsogood.ows.model.db.user.OrganizationEntity>
     * <AUTHOR>
     * @date 2019/11/27
     */
    public List<OrganizationEntity> getChildPartyOrg(Long orgId, Integer isInclude) {
        Example orgEx = new Example(OrganizationEntity.class);
        Example.Criteria criteria = orgEx.createCriteria();
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andEqualTo("parentId", orgId);
        if (Constants.STATUS_YES.equals(isInclude)) {
            criteria.orEqualTo("organizationId", orgId);
        }
        return this.organizationMapper.selectByExample(orgEx);
    }

    /**
     * 根据组织类型查询有效组织列表
     *
     * @param orgType
     * @param orgTypeChild
     * @return
     */
    public List<OrganizationForm> findOrgByType(Long orgType, Long orgTypeChild, Long regionId) {
        List<OrganizationForm> formList = this.organizationMapper.findOrgByType(orgType, orgTypeChild, regionId);
        for (OrganizationForm form : formList) {
            if (form.getGroupSum() > 0) {
                List<OrgGroupForm> orgGroupList = this.orgGroupMapper.list(form.getOrganizationId());
                form.setOrgGroups(orgGroupList);
            }
        }
        return formList;
    }

    /**
     * 批量查询组织党员数
     *
     * @param form
     * @return
     */
    public List<OrgUserCountForm> findUserCountByList(OrgUserCountQueryForm form) {
        List<OrgUserCountForm> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(form.getIdList())) {
            String[] politicalArray = form.getPoliticalType().split(",");
            form.getIdList().forEach(orgId -> {
                log.debug("ssss -> [{}, {}, {}]", form.getIsEmployee(), politicalArray, orgId);
                OrgUserCountForm resultForm = this.userOrgAndCorpMapper.findOrgUserCountByOrgId(Constants.STATUS_YES,
                        form.getIsEmployee(),
                        politicalArray, orgId);
                log.debug("查询结果 -> [{}]", resultForm);
                // 根据组织ID查询上级党委组织
                PartyOrgVo partyResultForm = this.findPartyId(orgId);
                if (resultForm != null && partyResultForm != null) {
                    resultForm.setPartyId(partyResultForm.getPartyId() == null ? partyResultForm.getOrgId() :
                            partyResultForm.getPartyId());
                    resultForm.setPartyName(partyResultForm.getPartyId() == null ? partyResultForm.getOrgName() :
                            partyResultForm.getPartyName());
                }
                if (resultForm != null) {
                    resultList.add(resultForm);
                }
            });
        }
        return resultList;
    }

    /**
     * 根据组织下属所有考核组织
     *
     * @param orgId 组织ID
     * @param type  1-考核， 2-管理， 3-考核+管理
     * @return
     */
    public List<OrganizationBase> findEvalOrgByOrgId(Long orgId, int type) {
        List<Integer> tagTypes = new ArrayList<>();
        if (type == 1) {
            tagTypes.add(Constants.EVAL_TAG_TYPE);
        } else if (type == 2) {
            tagTypes.add(Constants.MANAGER_TAG_TYPE);
        } else {
            tagTypes.add(Constants.EVAL_TAG_TYPE);
            tagTypes.add(Constants.MANAGER_TAG_TYPE);
        }
        return this.organizationMapper.findEvalOrgById(orgId, Constants.STATUS_YES, tagTypes);
    }

    /**
     * 查询组织
     *
     * @param orgId
     * @return
     * @Edit 2018-11-21 更改为查询组织直接下级
     */
    public List<OrganizationBase> findAllOrg(long orgId) {
        return this.organizationMapper.findChildByOrgId(orgId);
    }

    /**
     * 得到组织得分信息
     *
     * @param orgId
     * @param staDate
     * @return
     */
    public Double getOrgScore(Long orgId, String staDate) {
        Map<String, Object> orgScoreInfo = organizationMapper.getOrgScoreInfo(orgId, staDate);
        AtomicReference<Double> score = new AtomicReference<>(0.0);
        if (orgScoreInfo != null && !orgScoreInfo.isEmpty()) {
            orgScoreInfo.forEach((key, value) -> {
                if (!StringUtils.isEmpty(key) && null != value) {
                    String fieldValue = getFieldValueByFieldName(key, scoreConfig.getOrgScore());
                    if (fieldValue != null && !StringUtils.isEmpty(fieldValue)) {
                        double calScore = Double.parseDouble(fieldValue);
                        score.set(NumberUtils.add(score.get(), calScore));
                    }
                }
            });
        }
        return score.get();
    }

    /**
     * 得到组织得分信息
     *
     * @return
     */
    public ScoreResultVo getOrgScoreList(List<Long> orgIds, Integer year) {
        List<List<Long>> listOrgIds = ListUtils.splitList(orgIds, 20);
        ScoreResultVo scoreResultVo = new ScoreResultVo();
        Map<Long, EnumMap<Month, Double>> scoreResultMap = new HashMap<>();
        listOrgIds.forEach(itemListOrg -> {
            List<OrgScoreVo> orgScoreInfo = organizationMapper.getOrgScoreInfoList(itemListOrg, year);
            //全部设置为0
            final EnumMap<Month, Double>[] enumMap = new EnumMap[]{null};
            itemListOrg.forEach(item -> {
                enumMap[0] = new EnumMap<>(Month.class);
                for (int i = 1; i <= 12; i++) {
                    enumMap[0].put(Month.getEnumKey(i), 0.0);
                }
                scoreResultMap.put(item, enumMap[0]);
                //计算查询到组织 重置结果
                List<OrgScoreVo> orgScoreInfoResult = orgScoreInfo.stream().filter(item1 -> item
                        .equals(item1.getOrgId())).collect(Collectors.toList());
                orgScoreInfoResult.forEach(item1 -> {
                    AtomicReference<Double> score = new AtomicReference<>(0.0);
                    if (StringUtils.isNotBlank(item1.getName())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getOrgScore().getName()));
                    }
                    if (StringUtils.isNotBlank(item1.getOrgContacts())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getOrgScore().getOrgContacts()));
                    }
                    if (StringUtils.isNotBlank(item1.getOrgPhone())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getOrgScore().getOrgPhone()));
                    }
                    if (StringUtils.isNotBlank(item1.getOrgTypeChild())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getOrgScore().getOrgTypeChild()));
                    }
                    if (StringUtils.isNotBlank(item1.getShortName())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getOrgScore().getShortName()));
                    }
                    if (StringUtils.isNotBlank(item1.getIsRetire())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getOrgScore().getIsRetire()));
                    }
                    if (StringUtils.isNotBlank(item1.getParentId())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getOrgScore().getParentId()));
                    }
                    if (StringUtils.isNotBlank(item1.getOwnerId())) {
                        score.set(NumberUtils.add(score.get(), scoreConfig.getOrgScore().getOwnerId()));
                    }
                    enumMap[0].put(Month.getEnumKey(item1.getDateMonth()), score.get());
                });
                scoreResultMap.put(item, enumMap[0]);
            });
        });
        scoreResultVo.setScoreResultMap(scoreResultMap);
        return scoreResultVo;
    }

    /**
     * 根据属性名获取属性值
     *
     * @param fieldName
     * @param object
     * @return
     */
    private String getFieldValueByFieldName(String fieldName, Object object) {
        try {
            Field field = object.getClass().getDeclaredField(fieldName);
            //设置对象的访问权限，保证对private的属性的访问
            field.setAccessible(true);
            return field.get(object).toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 根据区域ID传机关党委
     *
     * @param rootId 顶级组织ID
     * @return
     */
    public List<OrganizationEntity> getOfficeCommittee(Long rootId) {
        Example example = new Example(OrganizationEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("parentId", rootId);
        criteria.andNotEqualTo("parentId", Constants.EX_ORG_ID);
        criteria.andEqualTo("orgType", Constants.ORG_TYPE_COMMUNIST);
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andNotIn("orgTypeChild", this.orgTypeConfig.getNoStatisticsChild());

        List<Long> orgIds = this.excludeOrgConfig.getOrgIds();
        if (!CollectionUtils.isEmpty(orgIds)) {
            orgIds.forEach(orgId -> {
                criteria.andNotEqualTo("organizationId", orgId);
                criteria.andNotLike("orgLevel", "%-" + orgId + "-%");
            });
        }
        return this.organizationMapper.selectByExample(example);
    }

    /**
     * 根据区域ID传机关党委
     *
     * @param rootId 顶级组织ID
     * @return
     */
    public List<OrgSnapshotEntity> getHistoryOfficeCommittee(Long rootId, String dateMonth) {
        Example example = new Example(OrgSnapshotEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgPid", rootId);
        criteria.andEqualTo("dateMonth", dateMonth);
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andNotIn("orgTypeChild", this.orgTypeConfig.getNoStatisticsChild());
        // 排除目标考核单位
        List<Long> orgIds = this.excludeOrgConfig.getOrgIds();
        if (!CollectionUtils.isEmpty(orgIds)) {
            orgIds.forEach(orgId -> {
                criteria.andNotEqualTo("orgId", orgId);
                criteria.andNotLike("orgLevel", "%-" + orgId + "-%");
            });
        }
        return this.orgSnapshotMapper.selectByExample(example);
    }

    /**
     * 查询当前的组织下级列表
     *
     * @param orgId
     * @param excludeRetire 是否排除离退休组织
     * @return
     */
    public List<OrganizationEntity> getPartyBranch(Long orgId, List<Long> orgIds, boolean excludeRetire) {
        Example example = new Example(OrganizationEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andLike("orgLevel", "%-" + orgId + "-%");
        criteria.andNotEqualTo("parentId", Constants.EX_ORG_ID);
        criteria.andIn("orgTypeChild", this.orgTypeConfig.getBranchChild());
        criteria.andEqualTo("orgType", Constants.ORG_TYPE_COMMUNIST);
        criteria.andEqualTo("status", Constants.STATUS_YES);
        if (excludeRetire) {
            criteria.andCondition(" ( is_retire IS NULL OR is_retire != 1 ) ");
        }
        if (!CollectionUtils.isEmpty(orgIds)) {
            criteria.andIn("organizationId", orgIds);
        }
        // 判断目标考核单位
        List<Long> excludeOrgIds = this.excludeOrgConfig.getOrgIds();
        if (!CollectionUtils.isEmpty(excludeOrgIds)) {
            excludeOrgIds.forEach(id -> {
                criteria.andNotEqualTo("organizationId", id);
                criteria.andNotLike("orgLevel", "%-" + id + "-%");
            });
        }

        return this.organizationMapper.selectByExample(example);
    }

    /**
     * 查询历史的组织下级列表
     *
     * @param orgId
     * @return
     */
    public List<OrgSnapshotEntity> getHistoryPartyBranch(Long orgId, Integer year, boolean excludeRetire, List<Long> orgIds) {
        Example example = new Example(OrgSnapshotEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andLike("orgLevel", "%-" + orgId + "-%");
        criteria.andNotEqualTo("orgPid", Constants.EX_ORG_ID);
        criteria.andEqualTo("dateMonth", year + "-12");
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andIn("orgTypeChild", this.orgTypeConfig.getBranchChild());
        criteria.andIsNotNull("orgPid");
        if (excludeRetire) {
            criteria.andCondition(" ( is_retire IS NULL OR is_retire != 1 ) ");
        }
        if (!CollectionUtils.isEmpty(orgIds)) {
            criteria.andIn("orgId", orgIds);
        }
        List<Long> excludeOrgConfigOrgIds = this.excludeOrgConfig.getOrgIds();
        if (!CollectionUtils.isEmpty(excludeOrgConfigOrgIds)) {
            excludeOrgConfigOrgIds.forEach(id -> {
                criteria.andNotEqualTo("orgId", id);
                criteria.andNotLike("orgLevel", "%-" + id + "-%");
            });
        }
        return this.orgSnapshotMapper.selectByExample(example);
    }

    /**
     * 查询历史的组织下级列表
     *
     * @param orgId
     * @param dateMonth 年月
     * @return
     */
    public List<OrgSnapshotEntity> getHistoryPartyBranch(Long orgId, String dateMonth, boolean excludeRetire, List<Long> orgIds) {
        Example example = new Example(OrgSnapshotEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andLike("orgLevel", "%-" + orgId + "-%");
        criteria.andNotEqualTo("orgPid", Constants.EX_ORG_ID);
        criteria.andEqualTo("dateMonth", dateMonth);
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andIn("orgTypeChild", this.orgTypeConfig.getBranchChild());
        criteria.andIsNotNull("orgPid");
        if (excludeRetire) {
            criteria.andCondition(" ( is_retire IS NULL OR is_retire != 1 ) ");
        }
        if (!CollectionUtils.isEmpty(orgIds)) {
            criteria.andIn("orgId", orgIds);
        }
        List<Long> excludeOrgConfigOrgIds = this.excludeOrgConfig.getOrgIds();
        if (!CollectionUtils.isEmpty(excludeOrgConfigOrgIds)) {
            excludeOrgConfigOrgIds.forEach(id -> {
                criteria.andNotEqualTo("orgId", id);
                criteria.andNotLike("orgLevel", "%-" + id + "-%");
            });
        }
        return this.orgSnapshotMapper.selectByExample(example);
    }

    /**
     * 查询当前系统中所有党委和党总支
     *
     * @param regionId
     * @return
     */
    public List<OrganizationEntity> getCommunistAndGeneral(Long regionId, List<Long> orgIds) {
        Region.OrgData orgData = this.applicationConfigHelper.getOrgByRegionId(regionId);
        Example example = new Example(OrganizationEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("orgTypeChild", this.orgTypeConfig.getCommunistAndGeneral());
        criteria.andNotEqualTo("parentId", Constants.EX_ORG_ID);
        criteria.andEqualTo("orgType", Constants.ORG_TYPE_COMMUNIST);
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andEqualTo("regionId", regionId);
        criteria.andLike("orgLevel", "%-" + orgData.getOrgId() + "-%");
        if (!CollectionUtils.isEmpty(orgIds)) {
            criteria.andIn("organizationId", orgIds);
        }
        List<Long> excludeOrgIds = this.excludeOrgConfig.getOrgIds();
        excludeOrgIds.forEach(orgId -> {
            criteria.andNotEqualTo("organizationId", orgId);
            criteria.andNotLike("orgLevel", "%-" + orgId + "-%");
        });

        return this.organizationMapper.selectByExample(example);
    }

    /**
     * 查询历史系统中所有党委和党总支
     *
     * @param year
     * @return
     */
    public List<OrgSnapshotEntity> getHistoryCommunistAndGeneral(Long regionId, Integer year, List<Long> orgIds) {
        Region.OrgData orgData = this.applicationConfigHelper.getOrgByRegionId(regionId);
        Example example = new Example(OrgSnapshotEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dateMonth", year + "-12");
        criteria.andNotEqualTo("orgPid", Constants.EX_ORG_ID);
        criteria.andIn("orgTypeChild", this.orgTypeConfig.getCommunistAndGeneral());
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andEqualTo("regionId", regionId);
        criteria.andIsNotNull("orgPid");
        criteria.andLike("orgLevel", "%-" + orgData.getOrgId() + "-%");
        if (!CollectionUtils.isEmpty(orgIds)) {
            criteria.andIn("orgId", orgIds);
        }

        List<Long> excludeOrgConfigOrgIds = this.excludeOrgConfig.getOrgIds();
        excludeOrgConfigOrgIds.forEach(orgId -> {
            criteria.andNotEqualTo("orgId", orgId);
            criteria.andNotLike("orgLevel", "%-" + orgId + "-%");
        });
        return this.orgSnapshotMapper.selectByExample(example);
    }

    /**
     * 查询历史系统中所有党委和党总支
     *
     * @param dateMonth
     * @return
     */
    public List<OrgSnapshotEntity> getHistoryCommunistAndGeneral(Long regionId, String dateMonth, List<Long> orgIds) {
        Region.OrgData orgData = this.applicationConfigHelper.getOrgByRegionId(regionId);
        Example example = new Example(OrgSnapshotEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dateMonth", dateMonth);
        criteria.andNotEqualTo("orgPid", Constants.EX_ORG_ID);
        criteria.andIn("orgTypeChild", this.orgTypeConfig.getCommunistAndGeneral());
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andEqualTo("regionId", regionId);
        criteria.andIsNotNull("orgPid");
        criteria.andLike("orgLevel", "%-" + orgData.getOrgId() + "-%");
        if (!CollectionUtils.isEmpty(orgIds)) {
            criteria.andIn("orgId", orgIds);
        }

        List<Long> excludeOrgConfigOrgIds = this.excludeOrgConfig.getOrgIds();
        excludeOrgConfigOrgIds.forEach(orgId -> {
            criteria.andNotEqualTo("orgId", orgId);
            criteria.andNotLike("orgLevel", "%-" + orgId + "-%");
        });
        return this.orgSnapshotMapper.selectByExample(example);
    }


    /**
     * 查询当前的组织下级ID列表
     * <p>
     * 当前月 取当前数据
     * 非当前月
     * 2019年之前的年份无数据
     * 2019年 取2019年12月份的数据
     * 其它取对应的月份数据
     *
     * @param orgId 组织id
     * @return List<OrganizationEntity>
     */
    public Set<Long> getPartyBranch(Long orgId, Integer year, Integer month) {
        int currentYear = LocalDateTime.now().getYear();
        if (year < currentYear) {
            List<OrgSnapshotEntity> orgSnapshotEntityList = getHistoryPartyBranch(orgId, year, true, null);
            return orgSnapshotEntityList.stream().map(OrgSnapshotEntity::getOrgId).collect(Collectors.toSet());
        } else if (year.equals(currentYear)) {
            List<OrganizationEntity> organizationEntityList = getPartyBranch(orgId, null, true);
            return organizationEntityList.stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toSet());
        } else {
            return new HashSet<>(0);
        }
    }

    /**
     * 根据区域ID传机关党委
     *
     * @param rootId 顶级组织ID
     * @return
     */
    public Set<Long> getOfficeCommittee(Long rootId, Integer year, Integer month) {
        int currentYear = LocalDateTime.now().getYear();
        if (year < currentYear) {
            List<OrgSnapshotEntity> orgSnapshotEntityList = getHistoryOfficeCommittee(rootId, DateUtils.dateFormat(year, 12));
            return orgSnapshotEntityList.stream().map(OrgSnapshotEntity::getOrgId).collect(Collectors.toSet());
        } else if (year.equals(currentYear)) {
            List<OrganizationEntity> organizationEntityList = getOfficeCommittee(rootId);
            return organizationEntityList.stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toSet());
        } else {
            return new HashSet<>(0);
        }
    }

    /**
     * 查询组织的历史数据
     *
     * @param orgId
     * @param year
     * @param month
     * @return
     */
    public OrgSnapshotEntity getHistoryOrgInfo(Long orgId, Integer year, Integer month) {
        Example example = new Example(OrgSnapshotEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", orgId);
        criteria.andEqualTo("status", Constants.STATUS_YES);
        if (null != month && month < 10) {
            criteria.andEqualTo("dateMonth", year + "-0" + month);
        } else {
            criteria.andEqualTo("dateMonth", year + "-" + (null == month ? "12" : month));
        }
        return this.orgSnapshotMapper.selectOneByExample(example);
    }

    /**
     * 查询历史组织的所属党委
     *
     * @param orgId
     * @param year
     * @param month
     * @return
     */
    public OrgSnapshotEntity getHistoryOrgParty(Long orgId, Integer year, Integer month, Long regionId) {
        OrgSnapshotEntity orgInfo = this.getHistoryOrgInfo(orgId, year, month);
        Region.OrgData orgData = this.applicationConfigHelper.getOrgByRegionId(regionId);
        Long rootId = orgData.getOrgId();
        if (null == orgInfo || null == orgInfo.getOrgPid()) {
            return new OrgSnapshotEntity();
        } else if (orgInfo.getOrgPid().equals(rootId)) {
            return orgInfo;
        } else {
            return this.getHistoryOrgParty(orgInfo.getOrgPid(), year, month, regionId);
        }
    }

    /**
     * 查询组织的历史直属下级数据
     *
     * @param orgId
     * @param year
     * @param month
     * @return
     */
    public List<OrgSnapshotEntity> getHistoryOrgChildInfo(Long orgId, Integer year, Integer month) {
        Example example = new Example(OrgSnapshotEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgPid", orgId);
        criteria.andEqualTo("status", Constants.STATUS_YES);
        criteria.andEqualTo("dateMonth", year + "-" + (null == month ? "12" : month));
        return this.orgSnapshotMapper.selectByExample(example);
    }

    public Integer getOrgType(Integer orgTypeChild) {
        Integer orgType;
        if (this.orgTypeConfig.getCommunistAndGeneral().contains(orgTypeChild)) {
            orgType = 2;
        } else {
            orgType = 3;
        }
        return orgType;
    }

    public DssOrgChild buildOrgList(Long orgId, Integer year, boolean flag) {
        DssOrgChild org = DssOrgChild.builder().build();
        if (year.equals(LocalDateTime.now().getYear())) {
            OrganizationEntity orgEntity = this.getById(orgId);
            org.setOrgId(orgId);
            if (null != orgEntity) {
                org.setName(orgEntity.getName());
                org.setShortName(orgEntity.getShortName());
                org.setOrgType(this.getOrgType(orgEntity.getOrgTypeChild()));
            }
        } else {
            OrgSnapshotEntity orgInfo = this.getHistoryOrgInfo(orgId, year, null);
            org.setOrgId(orgId);
            if (null != orgInfo) {
                org.setName(orgInfo.getOrgName());
                org.setShortName(orgInfo.getOrgShortName());
                org.setOrgType(this.getOrgType(orgInfo.getOrgTypeChild()));
            }
        }
        List<OrgDssUser> nowSecretaryInfo = this.orgPeriodMapper.getNowSecretaryInfo(String.valueOf(orgId), year);
        if (!CollectionUtils.isEmpty(nowSecretaryInfo)) {
            nowSecretaryInfo.stream().filter(info -> info.getType().equals(1)).findFirst().ifPresent(orgDssUser ->
                    org.setSecretary(orgDssUser.getName()));
        }
        if (flag) {
            org.setGrand(this.getOrgScore(orgId, year));
        }
        return org;
    }

    public OfficeOrg buildIndexOfficeList(Long orgId, Integer year) {
        OfficeOrg org = OfficeOrg.builder().build();
        if (year.equals(LocalDateTime.now().getYear())) {
            OrganizationEntity orgEntity = this.getById(orgId);
            org.setOrgId(orgId);
            if (null != orgEntity) {
                org.setName(orgEntity.getName());
                org.setShortName(orgEntity.getShortName());
                org.setOrgType(this.getOrgType(orgEntity.getOrgTypeChild()));
                Double longitude = orgEntity.getLongitude();
                Double latitude = orgEntity.getLatitude();
                Double[] coordinate = {longitude, latitude};
                org.setCoordinate(coordinate);
                org.setAddress(orgEntity.getOrgAddress());
            }
        } else {
            OrgSnapshotEntity orgInfo = this.getHistoryOrgInfo(orgId, year, null);
            org.setOrgId(orgId);
            if (null != orgInfo) {
                org.setName(orgInfo.getOrgName());
                org.setShortName(orgInfo.getOrgShortName());
                org.setOrgType(this.getOrgType(orgInfo.getOrgTypeChild()));
                Double longitude = orgInfo.getLongitude();
                Double latitude = orgInfo.getLatitude();
                Double[] coordinate = {longitude, latitude};
                org.setCoordinate(coordinate);
                org.setAddress(orgInfo.getAddress());
            }
        }
        List<OrgDssUser> nowSecretaryInfo = this.orgPeriodMapper.getNowSecretaryInfo(String.valueOf(orgId), year);
        if (!CollectionUtils.isEmpty(nowSecretaryInfo)) {
            nowSecretaryInfo.stream().filter(info -> info.getType().equals(1)).findFirst().ifPresent(orgDssUser ->
                    org.setSecretary(orgDssUser.getName()));
        }
        org.setGrand(this.getOrgScore(orgId, year));
        List<Integer> years = new ArrayList<>();
        years.add(LocalDateTime.now().getYear());
        years.add(LocalDateTime.now().getYear() - 1);
        List<YearGrand> yearGrandList = years.stream().map(y -> YearGrand.builder().year(y).
                grand(this.getOrgScore(orgId, y)).build()).collect(Collectors.toList());
        org.setYearGrandList(yearGrandList);
        return org;
    }

    /**
     * 获取组织评分
     *
     * @param orgId
     * @param year
     * @return
     */
    public Double getOrgScore(Long orgId, Integer year) {
        TotalScoreVo totalScoreVo = orgScoreMapper.findSingleOrgTotal(orgId, year);
        return (null == totalScoreVo || null == totalScoreVo.getScore()) ? 0d : computeService.conversion(1L, totalScoreVo.getScore().doubleValue(), year);
    }

    /**
     * 纪实获取党支部数量（非离退休组织&管理组织）从快照中获取组织
     *
     * @param orgId 顶级组织id
     * @param year  年
     */
    public List<Long> meetingGetHistoryPartyBranch(Long orgId, Integer year) {
        String orgTypeChild = SqlJointUtil.integerListToStr(this.orgTypeConfig.getBranchChild());
        return organizationMapper.meetingGetHistoryPartyBranch(orgId, year + "-12", orgTypeChild);
    }

    /**
     * 纪实获取党支部数量（非离退休组织&管理组织）从当前最新数据获取
     *
     * @param orgId 顶级组织id
     * @return
     */
    public List<Long> meetingGetPartyBranch(Long orgId) {
        String orgTypeChild = SqlJointUtil.integerListToStr(this.orgTypeConfig.getBranchChild());
        return organizationMapper.meetingGetPartyBranch(orgId, orgTypeChild);
    }

    /**
     * 查询当前组织的所有下级组织
     *
     * @param orgId
     * @return
     */
    public List<OrganizationEntity> selectAllChildOrg(Long orgId) {
        Example example = new Example(OrganizationEntity.class);
        example.createCriteria().andEqualTo("status", Constants.STATUS_YES)
                .andLike("orgLevel", "%-" + orgId + "-%");
        return this.organizationMapper.selectByExample(example);
    }

    /**
     * 缓存Key  SELECT_ORG_CHILD_区县id_组织Id
     */
    private static final String ALL_CHILD_ORG = "SELECT_ORG_CHILD_%s_%s";

    /**
     * 缓存时间
     */
    private static final Long CACHE_TIME = 5L;

    /**
     * 查询指定类型的的所有下级组织信息
     *
     * @param typeChild 为空时查询所有下级
     */
    public List<OrganizationEntity> selectAllChildOrgBYOrgAndType(Long regionId, Long orgId, List<Integer> typeChild) {
        String key = String.format(ALL_CHILD_ORG, regionId, orgId);
        String cache = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(cache)) {
            return filterOrg(regionId, Utils.fromJson(cache, ArrayList.class, OrganizationEntity.class), typeChild);
        }
        Example example = new Example(OrganizationEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("status", Constants.STATUS_YES)
                .andEqualTo("orgType", 102803)
                .andEqualTo("regionId", regionId)
                .andEqualTo("isRetire", 2);
        Example.Criteria criteria2 = example.createCriteria();
        criteria2.andEqualTo("organizationId", orgId);
        criteria2.orLike("orgLevel", "%-" + orgId + "-%");
        example.and(criteria2);
        //查询那些列
        example.selectProperties("organizationId", "regionId", "name", "orgType", "orgLevel", "orgTypeChild", "isRetire", "parentId");
        List<OrganizationEntity> organizationEntities = this.organizationMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(organizationEntities)) {
            return new ArrayList<>();
        }
        redisTemplate.opsForValue().set(key, Utils.toJson(organizationEntities), CACHE_TIME, TimeUnit.MINUTES);
        return filterOrg(regionId, organizationEntities, typeChild);
    }

    private List<OrganizationEntity> filterOrg(Long regionId, Collection<OrganizationEntity> list, List<Integer> child) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(child)) {
            return (List<OrganizationEntity>) list;
        }
        return list.stream()
                .filter(x -> child.contains(x.getOrgTypeChild()))
                .collect(Collectors.toList());
    }

    /**
     * 处理顶级组织信息
     *
     * @param organizationEntitiesCommittee
     * @param regionId
     */
    public void handlerTopOrgInfo(List<OrganizationEntity> organizationEntitiesCommittee,
                                  Long regionId) {
        //这里过滤topOrgId
        Region.RegionData regionData = regionService.regionData(regionId);
        Long topOrgId = regionData.getOrgData().getOrgId();
        List<OrganizationEntity> collect = organizationEntitiesCommittee.stream().
                filter(item -> item.getOrganizationId().equals(topOrgId)).collect(Collectors.toList());
        if (collect.size() > 0) {
            return;
        }
        Example example = new Example(OrganizationEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("status", Constants.STATUS_YES)
                .andEqualTo("orgType", 102803)
                .andEqualTo("regionId", regionId)
                .andEqualTo("isRetire", 2)
                .andEqualTo("organizationId", topOrgId);
        OrganizationEntity organizationEntity = this.organizationMapper.selectOneByExample(example);
        //添加顶级组织id信息
        organizationEntitiesCommittee.add(organizationEntity);
    }

    /**
     * @param pid
     * @return
     */
    public OrganizationEntity getParentEntity(Long pid, Integer orgType, List<OrganizationEntity> list, Long regionId) {
        String redisOrgKey = Global.CACHE_ORG_ID_PREFIX + pid;
        OrganizationEntity resultEntity;
        if (Boolean.TRUE.equals(this.redisTemplate.hasKey(redisOrgKey))) {
            resultEntity = Utils.fromJson(this.redisTemplate.opsForValue().get(redisOrgKey), OrganizationEntity.class);
        } else {
            Example example = new Example(OrganizationEntity.class);
            example.selectProperties("organizationId", "parentId", "name", "ownerId", "orgType", "orgTypeChild");
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("organizationId", pid)
                    .andEqualTo("regionId", regionId);
            if (!pid.equals(Constants.DEFAULT_ORG_ID)) {
                criteria.andEqualTo("orgType", orgType);
            }
            resultEntity = this.organizationMapper.selectOneByExample(example);
        }
        if (null != resultEntity && resultEntity.getParentId() != null) {
            list.add(resultEntity);
            return getParentEntity(resultEntity.getParentId(), orgType, list, regionId);
        }
        return null;
    }

    /**
     * @title 批量根据单位ID获取党组ID
     * <AUTHOR>
     * @param unitIds    单位ID
     * @updateTime 2022/6/25 10:28
     * @return Map key: 单位ID value: 党组ID
     */
    public Map<Long, Long> getPartyGroupIdByUnitIds(List<Long> unitIds){
        Map<Long, Long> resultMap = new HashMap<>();
        final Example example = new Example(PartyGroupEntity.class);
        final Example.Criteria criteria = example.createCriteria();
        criteria.andIn("orgId", unitIds);
        criteria.andEqualTo("status", Constants.STATUS_YES);
        final List<PartyGroupEntity> entities = this.partyGroupMapper.selectByExample(example);
        entities.forEach(entity -> {
            resultMap.put(entity.getOrgId(), entity.getGroupId());
        });
        return resultMap;
    }

    /**
     * @title 根据单位ID获取最高级组织ID
     * <AUTHOR>
     * @param unitIds    单位ID
     * @updateTime 2022/6/25 10:28
     * @return 最高级组织ID
     */
    public Map<Long, List<Long>> getTopOrgIdByUnitIds(List<Long> unitIds, Long regionId) {
        Region.OrgData orgData = applicationConfigHelper.getOrgByRegionId(regionId);
        Map<Long, List<Long>> resultMap = new HashMap<>();
        final Example example = new Example(OrganizationEntity.class);
        final Example.Criteria criteria = example.createCriteria();
        criteria.andIn("ownerId", unitIds);
        criteria.andIsNotNull("ownerId");
        if (orgData != null) {
            criteria.andEqualTo("parentId", orgData.getOrgId());
        }
        criteria.andEqualTo("status", Constants.STATUS_YES);
        final List<OrganizationEntity> entities = this.organizationMapper.selectByExample(example);
        final Map<Long, List<OrganizationEntity>> map = entities.stream().collect(Collectors.groupingBy(OrganizationEntity::getOwnerId));
        map.forEach((k, v) -> {
            List<Long> orgIds = new ArrayList<>();
            int size = 0;
            for (OrganizationEntity org : v) {
                if (orgIds.isEmpty()) {
                    orgIds.add(org.getOrganizationId());
                } else {
                    final String[] split = org.getOrgLevel().split("-");
                    final int length = split.length;
                    if (length <= 3) {
                        continue;
                    }
                    if (size == 0) {
                        size = length;
                    } else {
                        if (length < size) {
                            orgIds.clear();
                            orgIds.add(org.getOrganizationId());
                            size = length;
                        } else if(length == size) {
                            orgIds.add(org.getOrganizationId());
                        }
                    }
                }
            }
            resultMap.put(k, orgIds);
        });
        return resultMap;
    }

    /**
     * @title 根据单位ID获取最高级组织ID
     * <AUTHOR>
     * @param unitIds    单位ID
     * @updateTime 2022/6/25 10:28
     * @return 最高级组织ID
     */
    public Map<Long, List<Long>> getTopOrgIdByUnitIdsUseSql(List<Long> unitIds, Long regionId) {
        Region.OrgData orgData = applicationConfigHelper.getOrgByRegionId(regionId);
        Long topOrgId = 3L;
        if(orgData!=null){
            topOrgId = orgData.getOrgId();
        }
        final List<OrganizationEntity> entities = this.organizationMapper.findTopOrgIdByUnitIdsUseSql(regionId,topOrgId,unitIds);
        final Map<Long, List<Long>> resultMap = entities.stream().collect(Collectors.groupingBy(OrganizationEntity::getOwnerId,
                Collectors.mapping(OrganizationEntity::getOrganizationId,Collectors.toList())));
        return resultMap;
    }

    //获取所有党小组节点信息
    public List<Long> getAllGroupsIds(){
//        Example example = new Example(OrganizationBase.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("orgTypeChild", 10280306);
        return this.organizationMapper.selectAllGroup();
    }

    //根据组织id获取单位id
    public List<OrganizationEntity> getOwnerId(List<Long> orgIds){
        return this.organizationMapper.getOwnerId(orgIds);
//        Example example = new Example(OrganizationEntity.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("organizationId", orgIds);
//        example.selectProperties("organizationId", "ownerId");
//        List<OrganizationEntity> list = organizationMapper.selectByExample(example);
//        return list;
    }
}
