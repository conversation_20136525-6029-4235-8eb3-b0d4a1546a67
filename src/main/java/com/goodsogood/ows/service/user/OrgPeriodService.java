package com.goodsogood.ows.service.user;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.configuration.PeriodPositionConfig;
import com.goodsogood.ows.mapper.user.OrgPeriodMapper;
import com.goodsogood.ows.mapper.user.OrgPeriodMemberMapper;
import com.goodsogood.ows.model.db.sas.StatisticalOrgLifeEntity;
import com.goodsogood.ows.model.vo.sas.OrgPeriodWarningDetailForm;
import com.goodsogood.ows.model.vo.sas.PeriodFindOrgsResultForm;
import com.goodsogood.ows.model.vo.sas.PeriodForm;
import com.goodsogood.ows.model.vo.user.OrgUserPeriodForm;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 支委会
 * @date 2019/12/19
 */
@Service
@Log4j2
public class OrgPeriodService {

    private final OrgPeriodMapper orgPeriodMapper;
    private final OrgPeriodMemberMapper orgPeriodMemberMapper;
    private final PeriodPositionConfig periodPositionConfig;
    private final StringRedisTemplate stringRedisTemplate;
    private final ObjectMapper objectMapper;

    private static final String startTime = "-31 23:59:59";
    private static final String endTime = "-01 00:00:00";

    @Autowired
    public OrgPeriodService(OrgPeriodMapper orgPeriodMapper,
                            OrgPeriodMemberMapper orgPeriodMemberMapper,
                            PeriodPositionConfig periodPositionConfig,
                            StringRedisTemplate stringRedisTemplate,
                            ObjectMapper objectMapper) {
        this.orgPeriodMapper = orgPeriodMapper;
        this.orgPeriodMemberMapper = orgPeriodMemberMapper;
        this.periodPositionConfig = periodPositionConfig;
        this.stringRedisTemplate = stringRedisTemplate;
        this.objectMapper = objectMapper;
    }

    public List<PeriodFindOrgsResultForm> getPeriodInfo(List<PeriodForm> list) throws ParseException {

        if (!CollectionUtils.isEmpty(list)) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
            List<PeriodFindOrgsResultForm> result = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();
            String current = list.get(0).getPeriodQueryTime() + endTime;
            Date parse = null;
            for (PeriodForm p : list) {
                String periodQueryTime = p.getPeriodQueryTime();
                parse = simpleDateFormat.parse(periodQueryTime);
                calendar.setTime(parse);
                calendar.add(Calendar.MONTH, -1);
                result.add(orgPeriodMapper.findOrgs(p.getOrgId(),
                        simpleDateFormat.format(calendar.getTime()) + startTime,
                        periodQueryTime + endTime, current));
            }
            return result;

        } else {
            return null;
        }
    }

    /**
     * 移动端 h5 即将到期还未换届的支委会
     *
     * @param oid
     * @param type 1-本月到期　2-6个月到期
     * @return
     */
    public List<OrgPeriodWarningDetailForm> findExpireApp(Long oid, Integer type, Long regionId) {
        Date date = new Date();
        String createDate = new SimpleDateFormat("yyyy-MM-dd").format(date);
        return this.orgPeriodMapper.findExpireApp(createDate, oid, type == 1 ?
                new SimpleDateFormat("yyyy-M-").format(date) + "%" : null, regionId);
    }

    /**
     * 查询上个月的支部换届数量
     */
    public Long getChangeLastMonth(String orgIdList) {
        int year = new DateTime().getYear();
        int month = new DateTime().getMonthOfYear() - 1;
        if (month == 0) {
            month = 12;
            year--;
        }
        String sMonth = String.valueOf(month);
        if (sMonth.length() == 1) {
            sMonth = "0" + sMonth;
        }
        String time = year + "-" + sMonth;
        return this.orgPeriodMapper.getChangeLastMonth(time, orgIdList);
    }


    /**
     * 查询组织当前有效届次的党内职务的人员
     *
     * @param orgId 组织ID
     * @param type  1-党委、党总支，2-党支部
     * @return
     */
    public List<OrgUserPeriodForm> findOrgUserPeriod(Long orgId, int type) {
        List<String> positionList = new ArrayList<>();
        if (type == 1) {
            positionList = this.periodPositionConfig.getCommunist();
        } else {
            positionList = this.periodPositionConfig.getBranch();
        }
        return this.orgPeriodMemberMapper.findOrgPeriodUser(orgId, positionList);
    }

    /**
     * 本月应换届的党委
     */
    public Integer thisMonthPartyChange(Long orgId) {
        Calendar c = Calendar.getInstance();
        String today = DateUtils.toFormatDate(c.getTime(), "yyyy-MM-dd");
        String lastMonth = DateUtils.toFormatDate(c.getTime(), "yyyy-M");
        return this.orgPeriodMemberMapper.lastMonthPartyChange(orgId, today, lastMonth);
    }

    /**
     * 本月应换届的支部
     */
    public Integer thisMonthBranchChange(Long orgId) {
        Calendar c = Calendar.getInstance();
        String today = DateUtils.toFormatDate(c.getTime(), "yyyy-MM-dd");
        String lastMonth = DateUtils.toFormatDate(c.getTime(), "yyyy-M");
        return this.orgPeriodMemberMapper.thisMonthBranchChange(orgId, today, lastMonth);
    }


    public Integer getNotPeriodOrg(Long orgId) {
        return this.orgPeriodMemberMapper.getNotPeriodOrg(orgId);
    }

    /**
     * 添加缓存
     * @param orgId
     * @return
     */
    public Map<String,String> findPeriodLeaderByOrg(Long orgId) {
        try {
            String orgPeriodLeaderKey = "ORG-LEADER-" + orgId;
            if (stringRedisTemplate.hasKey(orgPeriodLeaderKey)) {
                String result = stringRedisTemplate.opsForValue().get(orgPeriodLeaderKey);
                return objectMapper.readValue(result,
                        new TypeReference<Map<String, String>>() {
                });
            }
            Map<String, String> periodLeaderByOrg = orgPeriodMapper.findPeriodLeaderByOrg(orgId);
            if(null==periodLeaderByOrg){
                return null;
            }
            String json = objectMapper.writeValueAsString(periodLeaderByOrg);
            //两个小时缓存时间
            stringRedisTemplate.opsForValue().set(orgPeriodLeaderKey,json,2, TimeUnit.HOURS);
            return periodLeaderByOrg;
        }catch (Exception ex){
            return null;
        }
    }
}
