package com.goodsogood.ows.service.user;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.mapper.user.OptionMapper;
import com.goodsogood.ows.model.db.user.OptionEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 字典表服务层
 * @date 2019/12/20
 */
@Service
@Log4j2
public class OptionService {

    private final ObjectMapper OBJECTMAPPER = new ObjectMapper();

    private final OptionMapper optionMapper;
    private final StringRedisTemplate redisTemplate;

    @Autowired
    public OptionService(OptionMapper optionMapper, StringRedisTemplate redisTemplate) {
        this.optionMapper = optionMapper;
        this.redisTemplate = redisTemplate;
    }

    public List<OptionEntity> getOptionList(String code){
        List<OptionEntity> list = new ArrayList<>();
        try {
            // redis已经存在
            if (this.redisTemplate.hasKey(code)) {
                list = OBJECTMAPPER.readValue( this.redisTemplate.opsForValue().get(code),new TypeReference<List<OptionEntity>>(){
                });
                log.debug("读取缓存成功 code -> {}",code);
            } else {
                list = this.getList(code);
                this.redisTemplate.opsForValue().set(code, OBJECTMAPPER.writeValueAsString(list),30, TimeUnit.DAYS);
                log.debug("写入缓存成功 code -> {}",code);
            }
        } catch (JsonProcessingException e) {
            log.error("JSON转换失败:" + e.getMessage(), e);
        } catch (NullPointerException | IOException ioe) {
            log.error("获取缓存失败:" + ioe.getMessage(), ioe);
        }
        return list;
    }

    /**
     * 根据code查询数据字典列表
     *
     * @param code
     * @return
     */
    public List<OptionEntity> getList(String code){
        Example example = new Example(OptionEntity.class);
        example.createCriteria().andEqualTo("code",code);
        example.setOrderByClause("seq ASC");
        return this.optionMapper.selectByExample(example);
    }

    public String getValueByKey(String code, Integer key){
        String value = "";
        try{
            List<OptionEntity> optionList = this.getOptionList(code);
            OptionEntity op = optionList.stream().filter(option -> key.equals(option.getOpKey())).findFirst().orElse(new OptionEntity());
            value = op.getOpValue();
        } catch (Exception e) {
            log.error("查询Option缓存Key报错 code -> [{}], key -> [{}]", code ,key);
        }
        return value;
    }
}
