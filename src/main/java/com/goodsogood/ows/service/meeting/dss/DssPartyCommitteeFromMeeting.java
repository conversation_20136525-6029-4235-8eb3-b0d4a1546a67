package com.goodsogood.ows.service.meeting.dss;

import com.goodsogood.ows.annotions.Logging;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.dss.MeetingCompletionMap;
import com.goodsogood.ows.model.mongodb.dss.MeetingInfo;
import com.goodsogood.ows.service.impl.DssPartyCommitteeBuilder;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: 决策辅助党委
 *
 * <AUTHOR>
 * @version 2020/11/9 11:12
 */
@Service
@Log4j2
public class DssPartyCommitteeFromMeeting implements DssPartyCommitteeBuilder {
  private final DssMeetingHandler dssMeetingHandler;

  public DssPartyCommitteeFromMeeting(
      DssMeetingHandler dssMeetingHandler) {
    this.dssMeetingHandler = dssMeetingHandler;
  }

  @Override
  @Logging
  public PartyCommitteeInfo buildPartyCommittee(PartyCommitteeInfo info) {
    MeetingInfo orgLifeInfo = new MeetingInfo();
    info.setOrgLifeInfo(orgLifeInfo);
    orgLifeInfo.setMeetingCompletionMap(new MeetingCompletionMap());
    /** 已完成 */
    List<Integer> complete = new ArrayList<>(12);
    orgLifeInfo.getMeetingCompletionMap().setComplete(complete);
    /** 未完成 */
    List<Integer> incomplete = new ArrayList<>(12);
    orgLifeInfo.getMeetingCompletionMap().setIncomplete(incomplete);
    for (int m = 1; m <= 12; m++) {
      DssMeetingHandler.UnfinishedBranchNumForm unfinishedBranchNum =
          dssMeetingHandler.unfinishedBranchNum(
              info.getYear(), m, info.getRegionId(), info.getOrganizationId());
      incomplete.add(unfinishedBranchNum.getUnfinishedBranchNum());
      complete.add(unfinishedBranchNum.getFinishedBranchNum());
    }
    return info;
  }

}
