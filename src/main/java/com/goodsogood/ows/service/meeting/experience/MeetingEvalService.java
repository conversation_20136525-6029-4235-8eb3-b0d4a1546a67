package com.goodsogood.ows.service.meeting.experience;

import com.aidangqun.log4j2cm.aop.HttpLogAspect;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.configuration.MeetingConfig;
import com.goodsogood.ows.helper.LogHelper;
import com.goodsogood.ows.mapper.experience.UserPartyEvalDetailMapper;
import com.goodsogood.ows.mapper.meeting.experience.MeetingEvalMapper;
import com.goodsogood.ows.model.db.experience.UserPartyEvalDetailEntity;
import com.goodsogood.ows.model.vo.meeting.experience.PrelectionVo;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.NumberUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName : MeetingEvalService
 * <AUTHOR> tc
 * @Date: 2022/3/16 9:28
 * @Description : 党性体检功能，相关组织生活星级评定服务类
 * <p>
 * 总评定
 * 5星：4个及以上5星
 * 4星：3个及以上4星或5星
 * 3星：3个及以上3星或4星或5星
 * 2星：3个及以上2星或4星或5星
 * 1星：余下均为1星
 */
@Service
@Log4j2
public class MeetingEvalService {

    private final UserPartyEvalDetailMapper userPartyEvalDetailMapper;
    private final MeetingEvalMapper meetingEvalMapper;
    private final MeetingConfig meetingConfig;
    private final StringRedisTemplate stringRedisTemplate;
    private LogAspectHelper.SSLog ssLog;
    private final LogHelper logHelper;

    @Autowired
    public MeetingEvalService(UserPartyEvalDetailMapper userPartyEvalDetailMapper, MeetingEvalMapper meetingEvalMapper, MeetingConfig meetingConfig, StringRedisTemplate stringRedisTemplate, LogHelper logHelper) {
        this.userPartyEvalDetailMapper = userPartyEvalDetailMapper;
        this.meetingEvalMapper = meetingEvalMapper;
        this.meetingConfig = meetingConfig;
        this.stringRedisTemplate = stringRedisTemplate;
        this.logHelper = logHelper;
    }

    /**
     * 党员主动讲党课
     * <p>
     * 周期: 每年
     * 计算规则: 讲课次数
     * 5星：排名前10%
     * 4星：10%-30%
     * 3星：30%-50%
     * 2星：50%-80%
     * 1星：80%-100%
     */
    //@Async("meetingEvalExecutor")
    public void prelection(UserPartyEvalDetailEntity userPartyEvalDetail) {
        LogAspectHelper.SSLog ssLogOld = HttpLogAspect.getSSLog();
        LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
        String logTxt = "<党员主动讲党课>";
        try {
            ssLog = logHelper.getSSlog(helper, "[" + logTxt + "]");
            helper.reSetContext(ssLog);
            log.debug(logTxt + " 入参 userPartyEvalDetail={}", JsonUtils.toJson(userPartyEvalDetail));
            Long regionId = userPartyEvalDetail.getRegionId();
            Long userId = userPartyEvalDetail.getUserId();
            Integer year = Integer.valueOf(userPartyEvalDetail.getDateMonth().toString().substring(0, 4));
            String redisKey = "SAAS_MEETING_EVAL_PRELECTION_" + regionId + "_" + year;
            PrelectionVo myPv = null;
            //记录前10%排名，需要的最低讲课次数
            Integer minPrelectionNum = 1;
            //先查询缓存
            if (stringRedisTemplate.hasKey(redisKey)) {
                Object obj = stringRedisTemplate.opsForHash().get(redisKey, userId.toString());
                if (obj != null) {
                    myPv = JsonUtils.fromJson(obj.toString(), PrelectionVo.class);
                }
            } else {
                //从数据库里获取，并补充缓存
                //获取配置
                Map<String, Integer> reParam = this.findParam(userPartyEvalDetail, MeetingConfig.PARTY_LECTURE);
                //查询所有党员讲党课次数
                List<PrelectionVo> pvList = meetingEvalMapper.
                        selectMeetingUserPrelectionNum(userPartyEvalDetail.getRegionId(), reParam.get("typeId"), year);
                if (pvList != null && pvList.size() > 0) {
                    //查询有效党员总数
                    Integer totalCount = meetingEvalMapper.selectRankUserCount(regionId);
                    log.debug(logTxt + " 查询有效党员总数 totalCount={}", totalCount);
                    //计算排名，同样讲课次数的并列名次,并列后的名次从实际人数后开始，例如:前5人并列第一名，他们的名次是1，第6人名次为第六名而不是第二名
                    //记录缓存结构
                    Map<String, String> redisMap = new HashMap<>(pvList.size());
                    //记录当前排名
                    Integer rank = 1;
                    //记录当前排名下讲课次数
                    Set<Integer> s = new HashSet<>();
                    //所需最低排名
                    double minRank = totalCount * 0.1;
                    for (int i = 0; i < pvList.size(); i++) {
                        PrelectionVo pv = pvList.get(i);
                        if (s.add(pv.getPrelectionNum())) {
                            //讲课次数和上一名用户不同，排名需要根据目前遍历到的人数填充
                            rank = (i + 1);
                            pv.setYear(year);
                            pv.setTotalNum(totalCount);
                            pv.setRanking(rank);
                        } else {
                            //讲课次数和上一个用户相同，并列名次
                            pv.setYear(year);
                            pv.setTotalNum(totalCount);
                            pv.setRanking(rank);
                        }
                        //计算达到五星的最小讲课次数
                        if (i == 0) {
                            //初始化最小讲课次数
                            minPrelectionNum = pv.getPrelectionNum();
                        } else {
                            //判断是否名次在前10%并且当前用户的讲课次数比记录的最小讲课次数小
                            if (rank < minRank && pv.getPrelectionNum() < minPrelectionNum) {
                                minPrelectionNum = pv.getPrelectionNum();
                            }
                        }
                        redisMap.put(pv.getUserId().toString(), JsonUtils.toJson(pv));
                        //如果是查询目标保存下来
                        if (userId.equals(pv.getUserId())) {
                            myPv = pv;
                        }
                    }
                    //结果存入缓存，保存五分钟
                    stringRedisTemplate.opsForHash().putAll(redisKey, redisMap);
                    stringRedisTemplate.expire(redisKey, 5L, TimeUnit.MINUTES);
                }
            }
            //根据结果计算星数,默认一星 用户没有讲党课，一星
            Integer userScore = 1;
            if (myPv != null) {
                if (myPv.getRanking() < NumberUtils.multiply(myPv.getTotalNum(), 0.1)) {
                    userScore = 5;
                } else if (myPv.getRanking() < NumberUtils.multiply(myPv.getTotalNum(), 0.3)) {
                    userScore = 4;
                } else if (myPv.getRanking() < NumberUtils.multiply(myPv.getTotalNum(), 0.5)) {
                    userScore = 3;
                } else if (myPv.getRanking() < NumberUtils.multiply(myPv.getTotalNum(), 0.8)) {
                    userScore = 2;
                }
            }
            log.debug(logTxt + " 计算得分 userScore={}", userScore);
            //填充星数
            userPartyEvalDetail.setStar(userScore);
            //如果不是五星，提供锤炼计划
            if (userScore < 5) {
                //12  保证每季度按时参加党员大会
                userPartyEvalDetail.setPlanId(11);
                userPartyEvalDetail.setParamType((byte) 13);
                //达到五星的最低次数
                userPartyEvalDetail.setParamValue(minPrelectionNum.toString());
            } else {
                //如果达到五星，提供报告所需变量
                userPartyEvalDetail.setParamType((byte) 9);
                //本年度讲党课次数
                userPartyEvalDetail.setParamValue(myPv.getPrelectionNum().toString());
            }
            //更新状态为完成
            userPartyEvalDetail.setCalStatus((byte) 1);
            //修改考核结果细项表
            userPartyEvalDetailMapper.updateByPrimaryKeySelective(userPartyEvalDetail);
        } catch (Exception e) {
            log.error("计算第18项出现异常", e);
            log.error(logTxt + "处理报错！ userPartyEvalDetail={}", userPartyEvalDetail, e);
        } finally {
            //还原日志
            logHelper.reSetContext(ssLogOld);
        }
    }

    /**
     * 按时参加党员大会
     * <p>
     * 周期: 每季度
     * 计算规则: 考勤
     * 5星：按时参加
     * 4星：请假后并在规定时间内补学
     * 3星：请假后超期补学
     * 2星：请假后未补学
     * 1星：未请假
     */
    //@Async("meetingEvalExecutor")
    public void partyPlenary(UserPartyEvalDetailEntity userPartyEvalDetail) {
        LogAspectHelper.SSLog ssLogOld = HttpLogAspect.getSSLog();
        LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
        String logTxt = "<按时参加党员大会>";
        try {
            ssLog = logHelper.getSSlog(helper, "[" + logTxt + "]");
            helper.reSetContext(ssLog);
            log.debug(logTxt + " 入参 userPartyEvalDetail={}", JsonUtils.toJson(userPartyEvalDetail));
            //计算用户星数
            Map<String, Integer> reParam = this.findParam(userPartyEvalDetail, MeetingConfig.PARTY_MEMBER);
            Integer userScore = meetingEvalMapper.selectMeetingUserScore(userPartyEvalDetail.getRegionId(), userPartyEvalDetail.getUserId(),
                    reParam.get("typeId"), reParam.get("start"), reParam.get("end"));
            log.debug(logTxt + " 计算得分 userScore={}", userScore);
            //填充星数
            userPartyEvalDetail.setStar(userScore);
            //如果不是五星，提供锤炼计划
            if (userScore < 5) {
                //12  保证每季度按时参加党员大会
                userPartyEvalDetail.setPlanId(12);
            }
            //更新状态为完成
            userPartyEvalDetail.setCalStatus((byte) 1);
            //修改考核结果细项表
            userPartyEvalDetailMapper.updateByPrimaryKeySelective(userPartyEvalDetail);
        } catch (Exception e) {
            log.error(logTxt + " 处理报错！ userPartyEvalDetail={}", userPartyEvalDetail, e);
        } finally {
            //还原日志
            logHelper.reSetContext(ssLogOld);
        }
    }

    /**
     * 按时参加党小组会
     * <p>
     * 周期: 每月
     * 计算规则: 考勤
     * 5星：按时参加
     * 4星：请假后并在规定时间内补学
     * 3星：请假后超期补学
     * 2星：请假后未补学
     * 1星：未请假
     */
    // @Async("meetingEvalExecutor")
    public void groupPlenary(UserPartyEvalDetailEntity userPartyEvalDetail) {
        LogAspectHelper.SSLog ssLogOld = HttpLogAspect.getSSLog();
        LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
        String logTxt = "<按时参加党小组会>";
        try {
            ssLog = logHelper.getSSlog(helper, "[" + logTxt + "]");
            helper.reSetContext(ssLog);
            log.debug(logTxt + " 入参 userPartyEvalDetail={}", JsonUtils.toJson(userPartyEvalDetail));
            //计算用户星数
            Map<String, Integer> reParam = this.findParam(userPartyEvalDetail, MeetingConfig.PARTY_GROUP);
            Integer userScore = meetingEvalMapper.selectMeetingUserScore(userPartyEvalDetail.getRegionId(),
                    userPartyEvalDetail.getUserId(),
                    reParam.get("typeId"), reParam.get("start"), reParam.get("end"));
            log.debug(logTxt + " 计算得分 userScore={}", userScore);
            //填充星数
            userPartyEvalDetail.setStar(userScore);
            //如果不是五星，提供锤炼计划
            if (userScore < 5) {
                //13	保证每月按时参加党小组会
                userPartyEvalDetail.setPlanId(13);
            } else {
                //如果达到五星，提供报告所需变量
                userPartyEvalDetail.setParamType((byte) 6);
                Date joinDate = meetingEvalMapper.selectMeetingUserLastJoinDate(userPartyEvalDetail.getRegionId(),
                        userPartyEvalDetail.getUserId(),
                        reParam.get("typeId"), reParam.get("start"), reParam.get("end"));
                userPartyEvalDetail.setParamValue(DateUtils.dateFormat(joinDate, "MM月dd日"));
            }
            //更新状态为完成
            userPartyEvalDetail.setCalStatus((byte) 1);
            //修改考核结果细项表
            userPartyEvalDetailMapper.updateByPrimaryKeySelective(userPartyEvalDetail);
        } catch (Exception e) {
            log.error("计算第20项出现异常", e);
            log.error(logTxt + " 处理报错！ userPartyEvalDetail={}", userPartyEvalDetail, e);
        } finally {
            //还原日志
            logHelper.reSetContext(ssLogOld);
        }
    }

    /**
     * 按时参加党课
     * <p>
     * 周期: 每季度
     * 计算规则: 考勤
     * 5星：按时参加
     * 4星：请假后并在规定时间内补学
     * 3星：请假后超期补学
     * 2星：请假后未补学
     * 1星：未请假
     */
    //@Async("meetingEvalExecutor")
    public void partyClass(UserPartyEvalDetailEntity userPartyEvalDetail) {
        LogAspectHelper.SSLog ssLogOld = HttpLogAspect.getSSLog();
        LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
        String logTxt = "<按时参加党课>";
        try {
            ssLog = logHelper.getSSlog(helper, "[" + logTxt + "]");
            helper.reSetContext(ssLog);
            log.debug(logTxt + " 入参 userPartyEvalDetail={}", JsonUtils.toJson(userPartyEvalDetail));
            //计算用户星数
            Map<String, Integer> reParam = this.findParam(userPartyEvalDetail, MeetingConfig.PARTY_LECTURE);
            Integer userScore = meetingEvalMapper.selectMeetingUserScore(userPartyEvalDetail.getRegionId(),
                    userPartyEvalDetail.getUserId(),
                    reParam.get("typeId"), reParam.get("start"), reParam.get("end"));
            log.debug(logTxt + " 计算得分 userScore={}", userScore);
            //填充星数
            userPartyEvalDetail.setStar(userScore);
            //如果不是五星，提供锤炼计划
            if (userScore < 5) {
                //14	保证每季度按时参加党课
                userPartyEvalDetail.setPlanId(14);
            }
            //更新状态为完成
            userPartyEvalDetail.setCalStatus((byte) 1);
            //修改考核结果细项表
            userPartyEvalDetailMapper.updateByPrimaryKeySelective(userPartyEvalDetail);
        } catch (Exception e) {
            log.error("计算第21项出现异常", e);
            log.error(logTxt + " 处理报错！ userPartyEvalDetail={}", userPartyEvalDetail, e);
        } finally {
            //还原日志
            logHelper.reSetContext(ssLogOld);
        }
    }

    /**
     * 按时参加主题党日活动
     * <p>
     * 周期: 每月
     * 计算规则: 考勤
     * 5星：按时参加
     * 4星：请假后并在规定时间内补学
     * 3星：请假后超期补学
     * 2星：请假后未补学
     * 1星：未请假
     */
    //@Async("meetingEvalExecutor")
    public void thematicPartyDay(UserPartyEvalDetailEntity userPartyEvalDetail) {
        LogAspectHelper.SSLog ssLogOld = HttpLogAspect.getSSLog();
        LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
        String logTxt = "<按时参加主题党日活动>";
        try {
            ssLog = logHelper.getSSlog(helper, "[" + logTxt + "]");
            helper.reSetContext(ssLog);
            log.debug(logTxt + " 入参 userPartyEvalDetail={}", JsonUtils.toJson(userPartyEvalDetail));
            //计算用户星数
            Map<String, Integer> reParam = this.findParam(userPartyEvalDetail, MeetingConfig.PARTY_THEME_DAY);
            Integer userScore = meetingEvalMapper.selectMeetingUserScore(userPartyEvalDetail.getRegionId(),
                    userPartyEvalDetail.getUserId(),
                    reParam.get("typeId"), reParam.get("start"), reParam.get("end"));
            log.debug(logTxt + " 计算得分 userScore={}", userScore);
            //填充星数
            userPartyEvalDetail.setStar(userScore);
            //如果不是五星，提供锤炼计划
            if (userScore < 5) {
                //15	保证每月按时参加主题党日活动
                userPartyEvalDetail.setPlanId(15);
            }
            //更新状态为完成
            userPartyEvalDetail.setCalStatus((byte) 1);
            //修改考核结果细项表
            userPartyEvalDetailMapper.updateByPrimaryKeySelective(userPartyEvalDetail);
        } catch (Exception e) {
            log.error("计算第22项出现异常", e);
            log.error(logTxt + " 处理报错！ userPartyEvalDetail={}", userPartyEvalDetail, e);
        } finally {
            //还原日志
            logHelper.reSetContext(ssLogOld);
        }
    }

    /**
     * 获取查询参数
     *
     * @param userPartyEvalDetail
     * @param meetingType
     * @return
     */
    private Map<String, Integer> findParam(UserPartyEvalDetailEntity userPartyEvalDetail, int meetingType) throws Exception {
        Map<String, Integer> re = new HashMap<>();
        List<MeetingConfig.MeetingType> typeList = meetingConfig.getType().get(userPartyEvalDetail.getRegionId());
        MeetingConfig.MeetingType mt = typeList.stream().filter(t -> t.getMappingType() == meetingType).findFirst().orElse(null);
        if (mt != null) {
            //根据活动周期，确定查询开始时间和结束时间
            Map<String, Integer> reCycle = this.findCycle(mt.getPeriod(), userPartyEvalDetail.getDateMonth());
            re.put("start", reCycle.get("start"));
            re.put("end", reCycle.get("end"));
            re.put("typeId", mt.getTypeId().intValue());
        }
        return re;
    }

    /**
     * @param cycle     周期  1 月度，2季度，3 年度
     * @param queryDate 查询日期  格式:yyyyMM
     * @return
     * @throws Exception
     */
    private Map<String, Integer> findCycle(Integer cycle, Integer queryDate) throws Exception {
        Map<String, Integer> re = new HashMap<>();
        if (cycle == 1) {
            //月度周期
            //计算查询时间，月度的获取上月数据
            Integer calculate = Integer.valueOf(DateUtils.getDateByMonths(-1, "yyyyMM", queryDate.toString()));
            re.put("start", calculate);
            re.put("end", calculate);
        } else if (cycle == 2) {
            //季度周期
            //计算查询时间，月度的获取上月数据
            Integer calculate = Integer.valueOf(DateUtils.getDateByMonths(-3, "yyyyMM", queryDate.toString()));
            int month = Integer.valueOf(calculate.toString().substring(4));
            if (month % 3 == 0) {
                re.put("start", Integer.valueOf(DateUtils.getDateByMonths(-2, "yyyyMM", calculate.toString())));
                re.put("end", calculate);
            } else if (month % 3 == 1) {
                re.put("start", calculate);
                re.put("end", Integer.valueOf(DateUtils.getDateByMonths(2, "yyyyMM", calculate.toString())));
            } else if (month % 3 == 2) {
                re.put("start", Integer.valueOf(DateUtils.getDateByMonths(-1, "yyyyMM", calculate.toString())));
                re.put("end", Integer.valueOf(DateUtils.getDateByMonths(1, "yyyyMM", calculate.toString())));
            }
        }
        return re;
    }
}
