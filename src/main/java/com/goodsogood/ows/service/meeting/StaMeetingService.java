package com.goodsogood.ows.service.meeting;

import com.goodsogood.ows.mapper.meeting.StatisticsMapper;
import com.goodsogood.ows.mapper.meeting.TypeMapper;
import com.goodsogood.ows.mapper.user.LeaderMapper;
import com.goodsogood.ows.model.db.sas.TypeEntity;
import com.goodsogood.ows.model.vo.meeting.TypeListForm;
import com.goodsogood.ows.model.vo.sas.StatisticsMeetingForm;
import com.goodsogood.ows.model.vo.sas.TypeAllResultForm;
import com.goodsogood.ows.model.vo.user.LeaderContactForm;
import com.goodsogood.ows.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 党务看板组织生活服务类
 * @date 2019/12/19
 */
@Service
public class StaMeetingService {

    private final StatisticsMapper statisticsMapper;
    private final TypeMapper typeMapper;
    private final LeaderMapper leaderMapper;

    @Autowired
    public StaMeetingService(StatisticsMapper statisticsMapper, TypeMapper typeMapper, LeaderMapper leaderMapper) {
        this.statisticsMapper = statisticsMapper;
        this.typeMapper = typeMapper;
        this.leaderMapper = leaderMapper;
    }


    /**
     * 调用组织生活模块查询组织生活信息
     * 组织id
     * 不传staCode 默认查询6个月的数据
     * @return
     */
    public List<StatisticsMeetingForm> getOrganizeStaInfo(Long orgId, Long staCode,
                                                          Long queryStatusCode,
                                                          Long regionId){
        return statisticsMapper.statisticsOrganizeInfo(orgId, getQueryTime(staCode),
                                                            getQueryStatus(queryStatusCode),regionId);
    }

    /**
     * 得到所有的活动类型信息
     * @return
     */
    public List<TypeAllResultForm> getMeetingTypeInfo() {
        TypeListForm typeListFrom = new TypeListForm();
        List<TypeEntity> typeEntities = this.typeMapper.listAll(typeListFrom);
        List<Long> categoryIds = typeEntities.stream().map(TypeEntity::getCategoryId).distinct().sorted().collect(Collectors.toList());
        List<TypeAllResultForm> typeAllResultFroms = new ArrayList<>();
        categoryIds.forEach(cId -> {
            TypeAllResultForm typeAllResultFrom = new TypeAllResultForm();
            typeAllResultFrom.setTypes(typeEntities.stream().filter(typeEntity ->
                    typeEntity.getCategoryId().equals(cId)).collect(Collectors.toList()));
            typeAllResultFrom.setCategoryId(cId);
            typeAllResultFrom.setCategory(typeAllResultFrom.getTypes().get(0).getCategory());
            typeAllResultFroms.add(typeAllResultFrom);
        });
        return typeAllResultFroms;
    }

    /**
     * 查询用户参与党组织生活的情况
     * 不传staCode 默认查询6个月的数据
     * @return
     */
    public List<StatisticsMeetingForm> getStatisticsOrganizeUserInfo(Long regionId, Long userId,Long staCode, String signStatus){
        List<LeaderContactForm> leaderContacts = this.leaderMapper.getLeaderContact(regionId, userId);
        Set<Long> orgIds = new HashSet<>();
        leaderContacts.forEach(leaderContact -> {
            orgIds.add(leaderContact.getOrganizationId());
            orgIds.add(leaderContact.getOrgId());
        });
        return statisticsMapper.statisticsOrganizeUserInfo(regionId,orgIds, userId,
                getQueryTime(staCode), ListUtils.intStringToList(signStatus));
    }

    /**
     * 根据查询时间code 返回那些查询时间
     * @param staTimeCode
     * @return
     */
    public String getQueryTime(Long staTimeCode){
        if(null == staTimeCode) {
            return  "INTERVAL  6 MONTH";
        }
        //查询所有时间
        if(staTimeCode==1){
            return  "INTERVAL 5 YEAR";
            //查询一年
        }else  if(staTimeCode==2){
            return  "INTERVAL  1 YEAR";
            //查询6个月
        }else if(staTimeCode==3){
            return  "INTERVAL  6 MONTH";
            //查询1个月
        }else if(staTimeCode==4){
            return  "INTERVAL  1 MONTH";
            //默认查询半年
        }else {
            return  "INTERVAL  6 MONTH";
        }
    }

    /**
     * 查询状态码
     * @return
     */
    private String getQueryStatus(Long queryStatusCode){
        if(queryStatusCode==null){
            return  "7,8,10,11,12,13,14";
        }
        if(queryStatusCode==0){
            return  "7,8,10,11,12,13,14";
        }else if(queryStatusCode==1){
            return  "9";
        }else {
            return  "7,8,10,11,12,13,14";
        }
    }
}
