package com.goodsogood.ows.service.meeting;

import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.model.mongodb.MeetingInfo;
import com.goodsogood.ows.model.mongodb.report.MeetingReportInfo;
import com.goodsogood.ows.model.vo.meeting.MeetingForm;
import com.goodsogood.ows.model.vo.report.PersonElectronicReport;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议服务层
 * @date 2019/11/21
 */
@Service
@Log4j2
public class MeetingReportService {

    private final OrgService orgService;
    private final AsyncMeetingReportservice asyncMeetingReportservice;
    private final MyMongoTemplate mongoTemplate;
    private final MeetingMapper meetingMapper;

    @Autowired
    public MeetingReportService(OrgService orgService, AsyncMeetingReportservice asyncMeetingReportservice,
                                MyMongoTemplate mongoTemplate, MeetingMapper meetingMapper) {
        this.orgService = orgService;
        this.asyncMeetingReportservice = asyncMeetingReportservice;
        this.mongoTemplate = mongoTemplate;
        this.meetingMapper = meetingMapper;
    }

    public void meetingReport(String queryTime) {
        // 按照会议进行计算
        LinkedList<MeetingForm> meetingList = new LinkedList<>(this.meetingMapper.selectMeetingByDate(queryTime));
        if (!CollectionUtils.isEmpty(meetingList)) {
            Date date = new Date();
            CountDownLatch latch = new CountDownLatch(meetingList.size());
            meetingList.forEach(meeting -> {
                this.asyncMeetingReportservice.getMeetingReport(meeting, queryTime, date);
            });
            try {
                meetingList.clear();
            } catch (Exception e) {
                log.error("统计组织生活异常", e);
            }
        }
    }

    /**
     * 根据用户和时间查询用户在时间段参加的会议
     *
     * @param userId
     * @param queryTime
     * @return java.util.List<com.goodsogood.ows.model.mongodb.MeetingInfo>
     * <AUTHOR>
     * @date 2019/11/22
     */
    public List<MeetingInfo> getUserMeetingList(Long userId, String queryTime) {
        Criteria criteria = new Criteria();
        criteria.andOperator(
                Criteria.where("userList.userId").is(userId),
                Criteria.where("statsDate").is(queryTime)
        );
        Query query = new Query(criteria);
        List<MeetingInfo> meetingInfos =
                this.mongoTemplate.find(query, MeetingInfo.class);
        log.debug("查询结果 -> [{}]", meetingInfos);
        return meetingInfos;
    }


    public List<MeetingInfo> getMeetingList(Long regionId,Long userId, String queryTime) {
        Criteria criteria = new Criteria();
        criteria.and("userList.userId").is(userId);
        criteria.and("regionId").is(regionId);
        if (!StringUtils.isEmpty(queryTime)) {
            criteria.and("statsDate").is(queryTime);
        }

        Query query = new Query(criteria);
        return this.mongoTemplate.find(query, MeetingInfo.class);
    }

    /**
     * 查询人员的党务报告
     *
     * @param userId
     * @param queryTime
     * @return com.goodsogood.ows.model.vo.report.PersonElectronicReport.MeetingJoinInfo
     * <AUTHOR>
     * @date 2019/11/26
     */
    public PersonElectronicReport.MeetingJoinInfo getUserMeetingReport(Long userId, String queryTime, List<MeetingInfo> meetingInfos) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        PersonElectronicReport.MeetingJoinInfo meetingJoinInfo = new PersonElectronicReport.MeetingJoinInfo();
        log.debug("查询结果 -> [{}]", meetingInfos);
        List<PersonElectronicReport.MeetingJoinInfo.MeetingJoinList> meetingJoinList = new ArrayList<>(meetingInfos.size());
        if (!CollectionUtils.isEmpty(meetingInfos)) {
            meetingInfos.stream().filter(x -> queryTime.equals(x.getStatsDate())).forEach(meetingInfo -> {
                PersonElectronicReport.MeetingJoinInfo.MeetingJoinList meetingJoin = new PersonElectronicReport.MeetingJoinInfo.MeetingJoinList();
                meetingJoin.setJoinDate(format.format(meetingInfo.getMeetingDate()));
                meetingJoin.setOrgName(meetingInfo.getOrgName());
                List<String> typeList = meetingInfo.getMeetingType().stream().map(MeetingInfo.MeetingType::getName).collect(Collectors.toList());
                meetingJoin.setTypeName(typeList);
                meetingJoinList.add(meetingJoin);
            });
        }
        meetingJoinInfo.setJoinNum(meetingJoinList.size());
        meetingJoinInfo.setMeetingJoinLists(meetingJoinList);
        return meetingJoinInfo;
    }

    /**
     * 获取组织的组织生活统计数据
     *
     * @param orgId
     * @param queryTime yyyy-MM  2019-10
     * @return com.goodsogood.ows.model.mongodb.OrgReportInfo.MeetingInfo
     * <AUTHOR>
     * @date 2019/11/26
     */
    public MeetingReportInfo getOrgMeetingInfo(Long orgId, String queryTime) {
        MeetingReportInfo meetingReport = new MeetingReportInfo();
        // 查询字段
        Criteria criteria = new Criteria();
        // 匹配
        Pattern pattern = Pattern.compile("^.*-" + orgId + "-.*$", Pattern.CASE_INSENSITIVE);
        criteria.orOperator(
                Criteria.where("orgId").is(orgId),
                Criteria.where("orgLevel").regex(pattern)
        );
        criteria.andOperator(
                Criteria.where("statsDate").is(queryTime)
        );
        Query query = new Query(criteria);
        List<MeetingInfo> infoList = this.mongoTemplate.find(query, MeetingInfo.class);
        MeetingReportInfo.MeetingNum meetingNum = this.getMeetingNum(infoList);
        meetingReport.setMeetingNum(meetingNum);
        MeetingReportInfo.OrgNum orgNum = this.orgService.getOrgType(infoList);
        meetingReport.setOrgNum(orgNum);
        return meetingReport;
    }

    /**
     * 按照会议List计算MeetingNum
     *
     * @param infoList
     * @return java.util.List<com.goodsogood.ows.model.mongodb.OrgReportInfo.Type>
     * <AUTHOR>
     * @date 2019/11/25
     */
    public MeetingReportInfo.MeetingNum getMeetingNum(List<MeetingInfo> infoList) {
        List<MeetingReportInfo.MeetingNum.Type> typeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(infoList)) {
            Set<Long> typeSet = new HashSet<>();
            for (MeetingInfo meeting : infoList) {
                List<MeetingInfo.MeetingType> meetingTypeList = meeting.getMeetingType();
                for (MeetingInfo.MeetingType meetingType : meetingTypeList) {
                    if (typeSet.contains(meetingType.getId())) {
                        for (int i = 0; i < typeList.size(); i++) {
                            MeetingReportInfo.MeetingNum.Type type = typeList.get(i);
                            if (type.getId().equals(meetingType.getId())) {
                                type.setNum(type.getNum() + 1);
                                typeList.set(i, type);
                            }
                        }
                    } else {
                        typeSet.add(meetingType.getId());
                        MeetingReportInfo.MeetingNum.Type type = new MeetingReportInfo().new MeetingNum().new Type();
                        type.setId(meetingType.getId());
                        type.setType(meetingType.getName());
                        type.setNum(1);
                        typeList.add(type);
                    }
                }
            }
        }
        // 封装返回类
        MeetingReportInfo.MeetingNum meetingNum = new MeetingReportInfo().new MeetingNum();
        meetingNum.setMeetingCount(infoList.size());
        meetingNum.setTypeList(typeList);
        return meetingNum;
    }
}
