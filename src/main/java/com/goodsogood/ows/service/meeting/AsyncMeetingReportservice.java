package com.goodsogood.ows.service.meeting;

import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.mapper.meeting.MeetingOrgChangeLogMapper;
import com.goodsogood.ows.mapper.meeting.MeetingTypeMapper;
import com.goodsogood.ows.model.db.meeting.MeetingOrgChangeLogEntity;
import com.goodsogood.ows.model.db.meeting.MeetingTypeEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.MeetingInfo;
import com.goodsogood.ows.model.vo.meeting.MeetingForm;
import com.goodsogood.ows.model.vo.meeting.MeetingUserForm;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 异步执行组织生活服务层
 * @date 2019/11/29
 */
@Service
@Log4j2
public class AsyncMeetingReportservice {

    private final MeetingMapper meetingMapper;
    private final MyMongoTemplate mongoTemplate;
    private final MeetingOrgChangeLogMapper meetingOrgChangeLogMapper;
    private final OrgService orgService;
    private final MeetingTypeMapper meetingTypeMapper;

    @Autowired
    public AsyncMeetingReportservice(MeetingMapper meetingMapper, MyMongoTemplate mongoTemplate,
                                     MeetingOrgChangeLogMapper meetingOrgChangeLogMapper, OrgService orgService,
                                     MeetingTypeMapper meetingTypeMapper) {
        this.meetingMapper = meetingMapper;
        this.mongoTemplate = mongoTemplate;
        this.meetingOrgChangeLogMapper = meetingOrgChangeLogMapper;
        this.orgService = orgService;
        this.meetingTypeMapper = meetingTypeMapper;
    }

    /**
     * 获取组织生活报表
     *
     * @param meetingForm
     * @param queryTime
     * @param date
     * @return java.util.List<com.goodsogood.ows.model.mongodb.MeetingInfo>
     * <AUTHOR>
     * @date 2019/11/21
     */
    @Transactional(rollbackFor = Exception.class)
    public Future<List<MeetingInfo>> getMeetingReport(MeetingForm meetingForm, String queryTime, Date date) {
        List<MeetingInfo> infoList = new ArrayList<>();
        MeetingInfo meetingInfo = new MeetingInfo();
        try {
            meetingInfo.setOrgId(meetingForm.getOrgId());
            meetingInfo.setMeetingId(meetingForm.getMeetingId());
            meetingInfo.setOrgName(meetingForm.getOrgName());
            meetingInfo.setMeetingName(meetingForm.getName());
            meetingInfo.setMeetingDate(meetingForm.getStartTime());
            meetingInfo.setStatsDate(queryTime);
            meetingInfo.setCreateTime(date);
            // 查询组织当时的层级关系
            MeetingOrgChangeLogEntity logEntity = this.meetingOrgChangeLogMapper.selectChangeLogByDate(meetingForm.getOrgId(),
                    DateUtils.dateFormat(meetingForm.getStartTime(), "yyyy-MM-dd HH-mm-ss"));
            OrganizationEntity org = this.orgService.getById(meetingForm.getOrgId());
            if (null == logEntity) {
                meetingInfo.setParentId(org.getParentId());
                meetingInfo.setOrgType(org.getOrgType());
                meetingInfo.setOrgTypeChild(org.getOrgTypeChild());
                meetingInfo.setOrgLevel(org.getOrgLevel());
                meetingInfo.setOrgCreateTime(org.getOrgCreateTime());
            } else {
                String orgLevel = logEntity.getOrgLevel();
                Long parentId = 0L;
                try {
                    String[] split = orgLevel.split("-");
                    parentId = Long.valueOf(split[split.length-1]);
                } catch (Exception e) {
                    log.error("分解层级关系出错", e);
                    parentId = org.getParentId();
                }
                meetingInfo.setOrgLevel(orgLevel);
                meetingInfo.setParentId(parentId);
                meetingInfo.setOrgType(org.getOrgType());
                meetingInfo.setRegionId(org.getRegionId());
                meetingInfo.setOrgTypeChild(logEntity.getOrgTypeChild());
                meetingInfo.setOrgCreateTime(org.getOrgCreateTime());
            }
            // 查询会议的类型
            List<MeetingTypeEntity> typeEntityList = this.meetingTypeMapper.selectMeetingTypeByMeetingId(meetingForm.getMeetingId());
            List<MeetingInfo.MeetingType> typeList = new ArrayList<>(typeEntityList.size());
            for (MeetingTypeEntity type : typeEntityList) {
                MeetingInfo.MeetingType t = new MeetingInfo().new MeetingType();
                t.setId(type.getTypeId());
                t.setName(type.getType());
                typeList.add(t);
            }
            meetingInfo.setMeetingType(typeList);
            // 查询参加会议的人
            List<MeetingUserForm> meetingUserForms = this.meetingMapper.selectUserByMeetingId(meetingInfo.getMeetingId());
            List<MeetingInfo.User> userList = new ArrayList<>(meetingUserForms.size());
            for (MeetingUserForm form : meetingUserForms) {
                MeetingInfo.User user = new MeetingInfo().new User();
                user.setUserId(form.getUserId());
                user.setUserName(form.getUserName());
                user.setSignStatus(form.getSignStatus());
                List<Integer> collect = Arrays.asList(form.getTag().split(",")).stream().map(str -> Integer.valueOf(str)).collect(Collectors.toList());
                user.setTag(collect);
                userList.add(user);
            }
            meetingInfo.setUserList(userList);
            this.saveMeetingReport(meetingInfo);
            infoList.add(meetingInfo);
        } catch (Exception e) {
            log.error(e);
        }
        return new AsyncResult<List<MeetingInfo>>(infoList);
    }

    /**
     * 对List进行分类，同一会议归类
     *
     * @param meetingForms
     * @return java.util.List<java.util.List < com.goodsogood.ows.model.vo.meeting.MeetingForm>>
     * <AUTHOR>
     * @date 2019/11/21
     */
    public List<List<MeetingForm>> sortMeetingList(List<MeetingForm> meetingForms) {
        List<List<MeetingForm>> list = new ArrayList<>();
        List<Long> idList = new ArrayList<>();
        //对查询数据进行分组
        for (MeetingForm i : meetingForms) {
            List<MeetingForm> formList = new ArrayList<>();
            Long meetingId = i.getMeetingId();
            if (!idList.contains(meetingId)) {
                idList.add(meetingId);
                for (MeetingForm j : meetingForms) {
                    if (meetingId.equals(j.getMeetingId())) {
                        formList.add(j);
                    }
                }
                list.add(formList);
            }
        }
        return list;
    }

    /**
     * 保存数据到MongoDB
     *
     * @param reportInfo
     * <AUTHOR>
     * @date 2019/11/21
     */
    public void saveMeetingReport(MeetingInfo reportInfo) {
        // 需要判断之前是否有数据存在
        List<MeetingInfo> infoList = this.getMeetingInfo(reportInfo.getOrgId(), reportInfo.getMeetingId(), reportInfo.getStatsDate());
        if (infoList.size() == 0) {
            this.mongoTemplate.save(reportInfo);
        } else {
            this.updateMeetingInfo(reportInfo);
        }
    }

    /**
     * 从mongoDB查询组织ID在当前月份数据
     *
     * @param orgId
     * @param meetingId
     * @param queryTime
     * @return java.util.List<com.goodsogood.ows.model.mongodb.MeetingInfo>
     * <AUTHOR>
     * @date 2019/11/21
     */
    private List<MeetingInfo> getMeetingInfo(Long orgId, Long meetingId, String queryTime) {
        Criteria criteria = new Criteria();
        criteria.andOperator(
                Criteria.where("orgId").is(orgId),
                Criteria.where("meetingId").is(meetingId),
                Criteria.where("statsDate").is(queryTime)
        );
        Query query = new Query(criteria);
        List<MeetingInfo> meetingInfos =
                this.mongoTemplate.find(query, MeetingInfo.class);
        log.debug("查询结果 -> [{}]", meetingInfos);
        return meetingInfos;
    }

    /**
     * 更新会议统计数据
     *
     * @param meetingInfo
     * <AUTHOR>
     * @date 2019/11/21
     */
    private void updateMeetingInfo(MeetingInfo meetingInfo) {
        // 查询字段
        Criteria criteria = new Criteria();
        criteria.andOperator(
                Criteria.where("orgId").is(meetingInfo.getOrgId()),
                Criteria.where("meetingId").is(meetingInfo.getMeetingId()),
                Criteria.where("statsDate").is(meetingInfo.getStatsDate())
        );
        Query query = new Query(criteria);
        // 更新字段
        Update update = new Update();
        update.set("orgName", meetingInfo.getOrgName());
        update.set("parentId", meetingInfo.getParentId());
        update.set("orgType", meetingInfo.getOrgType());
        update.set("orgTypeChild", meetingInfo.getOrgTypeChild());
        update.set("orgLevel", meetingInfo.getOrgLevel());
        update.set("meetingName", meetingInfo.getMeetingName());
        update.set("meetingDate", meetingInfo.getMeetingDate());
        update.set("meetingType", meetingInfo.getMeetingType());
        update.set("updateTime", new Date());
        if (!CollectionUtils.isEmpty(meetingInfo.getUserList())) {
            update.set("userList", meetingInfo.getUserList());
        }
        // 更新
        this.mongoTemplate.upsert(query, update, MeetingInfo.class);
    }
}
