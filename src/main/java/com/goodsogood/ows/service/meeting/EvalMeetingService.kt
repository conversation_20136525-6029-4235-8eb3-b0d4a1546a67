package com.goodsogood.ows.service.meeting

import com.goodsogood.ows.mapper.meeting.MeetingMapper
import com.goodsogood.ows.mapper.meeting.MeetingOrgLeftMapper
import com.goodsogood.ows.mapper.meeting.MeetingWorkReportMapper
import com.goodsogood.ows.mapper.meeting.PracticableMapper
import com.goodsogood.ows.model.db.meeting.MeetingEntity
import com.goodsogood.ows.model.db.meeting.OrgLifeEntity
import com.goodsogood.ows.model.db.meeting.PracticableEntity
import com.goodsogood.ows.model.db.meeting.ReportEntity
import org.apache.ibatis.annotations.Param
import org.springframework.stereotype.Service
import tk.mybatis.mapper.entity.Example
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * meeting相关Service
 * 烟草考核2.0时，添加
 * <AUTHOR>
 * @date 2023/12/4
 * @description class EvalMeetingService
 */
@Service
class EvalMeetingService(
    val meetingMapper: MeetingMapper,
    val meetingWorkReportMapper: MeetingWorkReportMapper,
    val meetingOrgLifeEntity: MeetingOrgLeftMapper,
    val practicableMapper: PracticableMapper,
) {
    /**
     * 通过会议标签和会议类型获取会议列表
     * @param year 年份
     * @param orgId 组织id
     * @param tags 会议标签
     * @param type 会议类型
     * @return 会议列表
     */
    fun getMeetingListByTagAndType(
        year: Int, orgId: Long,
        tags: List<Long>, type: List<String>
    ): List<MeetingEntity> {
        val startTime = "$year-01-01 00:00:00"
        val endTime = "$year-12-31 23:59:59"
        return meetingMapper.getMeetingListByTagAndType(orgId, startTime, endTime, tags, type)
    }

    /**
     * 通过会议标签和会议类型获取会议列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orgId 组织id
     * @param tags 会议标签
     * @param type 会议类型
     * @return 会议列表
     */
    fun getMeetingListByTagAndType(
        startTime: String, endTime: String,
        orgId: Long,
        tags: List<Long>? = null, type: List<String>? = null
    ): List<MeetingEntity> {
        return meetingMapper.getMeetingListByTagAndType(orgId, startTime, endTime, tags, type)
    }

    /**
     * 通过领导id，查询本年是否听取过汇报 / 通过单位id，查询本年意识形态
     * @param year 年份
     * @param reportType 汇报类型 1.工作汇报，2.意识形态
     * @param ids reportType = 1 时 为 领导id数组，reportType = 2 时 为 单位id数组
     * @return 汇报记录列表
     */
    fun getMeetingWorkReportListByLeaderIds(year: Int, reportType: Int, ids: List<Long>): List<ReportEntity> {
        return when (reportType) {
            1 -> {
                meetingWorkReportMapper.getReportListByLeaderIds(year, reportType, ids = ids)
            }

            2 -> {
                meetingWorkReportMapper.getReportListByLeaderIds(year, reportType, unitIds = ids)
            }

            else -> {
                listOf()
            }
        }
    }

    /**
     * 根据单位、年份和状态获取对应的组织生活会列表（t_meeting_org_left）
     * @param year 年份
     * @param orgId 组织id
     * @param status 状态
     * @return 组织生活会列表
     */
    fun getOrgLifeMeetingListByOrgIdAndYearAndStatus(
        year: Int,
        orgId: Long,
        status: Int
    ): List<OrgLifeEntity> {
        val example = Example(OrgLifeEntity::class.java)
        example.createCriteria()
            .andEqualTo("orgId", orgId)
            .andEqualTo("years", year)
            .andEqualTo("status", status)
        return meetingOrgLifeEntity.selectByExample(example)
    }

    /**
     * 验证当前组织是否有对应的会议任务
     * @param orgId 组织id
     * @param startTime 开始时间
     * @param types 会议类型数组
     */
    fun getTaskUserCount(orgId: Long, startTime: String, types: List<String>): Long {
        return meetingMapper.getTaskUserCount(orgId, startTime, types)
    }

    /**
     * 通过单位id、领导id和年份获取对应的深入基层联系点列表
     * @param unitId 单位id
     * @param leaderId 领导id
     * @param year 年份
     */
    fun getPracticableListByUnitIdAndLeaderIdAndYear(
        unitId: Long,
        leaderId: Long,
        year: Int
    ): List<PracticableEntity> {
        val example = Example(PracticableEntity::class.java)
        example.createCriteria()
            .andEqualTo("unitId", unitId)
            .andEqualTo("leaderId", leaderId)
            .andBetween("interviewTime", LocalDate.of(year, 1, 1), LocalDate.of(year, 12, 31))
        return practicableMapper.selectByExample(example)
    }

    /**
     * 通过单位id、领导id和年份获取对应的深入基层联系点列表
     * @param unitId 单位id
     * @param leaderIds 领导id数组
     * @param year 年份
     */
    fun getPracticableListByUnitIdAndLeaderIdsAndYear(
        unitId: Long?,
        leaderIds: List<Long>,
        year: Int
    ): List<PracticableEntity> {
        val example = Example(PracticableEntity::class.java)
        // 单位id unitId为空的时候，不生成单位id的条件
        if (unitId != null) {
            example.createCriteria().andEqualTo("unitId", unitId)
        }
        example.createCriteria()
            .andIn("leaderId", leaderIds)
            .andBetween("interviewTime", LocalDate.of(year, 1, 1), LocalDate.of(year, 12, 31))
        return practicableMapper.selectByExample(example)
    }

    /**
     * 获取会议提交时间和开展时间超过5天的数据
     * 需要处理的会议包含 党员大会、支委会、党小组会、党课、主题党日、中心组学习、党组会学习
     */
    fun getMeetingCount(
        @Param("orgId") orgId: Long,
        @Param("startTime") startTime: LocalDateTime,
        @Param("endTime") endTime: LocalDateTime,
        @Param("types") types: List<Int>
    ): Long {
        return meetingMapper.getMeetingCount(orgId, startTime, endTime, types)
    }
}