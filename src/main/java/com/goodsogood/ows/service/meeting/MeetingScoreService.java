package com.goodsogood.ows.service.meeting;

import com.goodsogood.ows.configuration.MeetingConfig;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.ScoreConfig;
import com.goodsogood.ows.mapper.meeting.MeetingOrgScoreMapper;
import com.goodsogood.ows.mapper.meeting.MeetingUserScoreMapper;
import com.goodsogood.ows.mapper.user.LeaderMapper;
import com.goodsogood.ows.mapper.user.OrgGroupMapper;
import com.goodsogood.ows.mapper.user.OrgPeriodMapper;
import com.goodsogood.ows.model.db.user.OrgLeaderEntity;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import com.goodsogood.ows.model.vo.ScoreResult;
import com.goodsogood.ows.model.vo.rank.Month;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.service.user.OrgSnapshotService;
import com.goodsogood.ows.service.user.UserSnapshotService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.QuarterUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 纪实打分
 *
 * <AUTHOR>
 * @version 2020/11/6 15:03
 */
@Service
@Log4j2
public class MeetingScoreService {
    private final MeetingOrgScoreMapper meetingOrgScoreMapper;
    private final MeetingUserScoreMapper meetingUserScoreMapper;
    private final LeaderMapper leaderMapper;
    private final OrgGroupMapper orgGroupMapper;
    private final OrgPeriodMapper orgPeriodMapper;
    private final OrgSnapshotService orgSnapshotService;
    private final UserSnapshotService userSnapshotService;
    private final MeetingConfig meetingConfig;
    private final OrgTypeConfig orgTypeConfig;
    private final ScoreConfig scoreConfig;

    public MeetingScoreService(
            MeetingOrgScoreMapper meetingOrgScoreMapper,
            MeetingUserScoreMapper meetingUserScoreMapper,
            LeaderMapper leaderMapper,
            OrgGroupMapper orgGroupMapper,
            OrgPeriodMapper orgPeriodMapper,
            OrgSnapshotService orgSnapshotService,
            UserSnapshotService userSnapshotService,
            MeetingConfig meetingConfig,
            OrgTypeConfig orgTypeConfig,
            ScoreConfig scoreConfig) {
        this.meetingOrgScoreMapper = meetingOrgScoreMapper;
        this.meetingUserScoreMapper = meetingUserScoreMapper;
        this.leaderMapper = leaderMapper;
        this.orgGroupMapper = orgGroupMapper;
        this.orgPeriodMapper = orgPeriodMapper;
        this.orgSnapshotService = orgSnapshotService;
        this.userSnapshotService = userSnapshotService;
        this.meetingConfig = meetingConfig;
        this.orgTypeConfig = orgTypeConfig;
        this.scoreConfig = scoreConfig;
    }

    /**
     * 组织得分 /* 2.1优秀组织 奖惩登记 所有基层党组织 是否有奖励登记 5 判断当前组织在时间段内是否有奖励登记，每有一条增加该项分 所有基层党组织 是否有惩罚登记 -5
     * 判断当前组织在时间段内是否有惩罚登记，每有一条扣除该项分
     *
     * @param regionId  区县id
     * @param year      年份
     * @param orgIdList 组织id
     * @return double 分值
     */
    public ScoreResultVo orgCommendPenalizeScore(Long regionId, Integer year, List<Long> orgIdList) {
        // 封装查询条件
        List<MeetingOrgScoreMapper.OrgCommendPenalizeScoreForm> formList = new ArrayList<>();
        for (Month month : Month.values()) {
            int m = month.value();
            Set<Long> orgIdSet = orgSnapshotService.baseOrg(regionId, orgIdList, year, m);
            if (CollectionUtils.isNotEmpty(orgIdSet)) {
                MeetingOrgScoreMapper.OrgCommendPenalizeScoreForm form =
                        MeetingOrgScoreMapper.OrgCommendPenalizeScoreForm.builder()
                                .orgIdSet(orgIdSet)
                                .commendScore(scoreConfig.getOrgScore().getOrgCommend().getScore())
                                .penalizeScore(scoreConfig.getOrgScore().getOrgPenalize().getScore())
                                .currentMonth(dateFormat(year, m))
                                .build();
                formList.add(form);
            }
        }
        // 查询分值
        List<ScoreResult> scoreList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(formList)) {
            scoreList = meetingOrgScoreMapper.orgCommendPenalizeScore(formList);
        }
        ScoreResultVo scoreResultVo = ScoreResult.getScoreResultVo(year, orgIdList, scoreList);
        log.info("奖惩得分:{}", JsonUtils.toJson(scoreResultVo));
        return scoreResultVo;
    }

    /**
     * 组织得分
     *
     * <p>分数 基础工作 开展组织生活得分 参加组织生活得分 党支部 1.1基础工作 党支部组织生活开展情况 党支部
     * 是否达到规定次数（主题党日、支委会、党小组会每月不少于1次，党课、党员大会每季度不少于1次） 20
     * 判断当前组织开展次数是否全部满足，是则得该项分；按季度的在每季度的最后一月进行判断，其他月份不判断
     *
     * @param regionId  区县id
     * @param orgIdList 组织id
     * @param year      年份
     * @return double 分值
     */
    public ScoreResultVo orgBasicWorkScore(Long regionId, Integer year, List<Long> orgIdList) {
        List<MeetingOrgScoreMapper.OrgMeetingScoreForm> formList = new ArrayList<>();
        for (Month month : Month.values()) {
            int m = month.value();
            //  是党支部的组织id
            Set<Long> orgIdSet = new HashSet<>();
            String dateMonth = DateUtils.dateFormat(year, m);
            for (Long oid : orgIdList) {
                OrgSnapshotEntity orgSnapshotEntity = orgSnapshotService.getOrgSnapshot(oid, dateMonth);
                if (branchOrg(orgSnapshotEntity)) {
                    orgIdSet.add(oid);
                }
            }
            if (CollectionUtils.isNotEmpty(orgIdSet)) {
                // 获取季度
                QuarterUtils.Quarter quarter = QuarterUtils.getQuarterByMonth(m);
                // 查询当月是否有任务
                List<MeetingConfig.MeetingType> typeList = meetingConfig.getType().get(regionId);
                // 季度的typeId
                Set<Long> quarterTypeSet =
                        typeList.stream()
                                .filter(t -> t.getPeriod().equals(MeetingConfig.PERIOD_QUARTER))
                                .map(MeetingConfig.MeetingType::getTypeId)
                                .collect(Collectors.toSet());
                // 月度的typeId
                Set<Long> monthTypeSet =
                        typeList.stream()
                                .filter(t -> t.getPeriod().equals(MeetingConfig.PERIOD_MONTH))
                                .map(MeetingConfig.MeetingType::getTypeId)
                                .collect(Collectors.toSet());
                MeetingOrgScoreMapper.OrgMeetingScoreForm form =
                        MeetingOrgScoreMapper.OrgMeetingScoreForm.builder()
                                .score(scoreConfig.getOrgScore().getMeetingTask().getScore())
                                .deductScore(0.0)
                                .orgIdSet(orgIdSet)
                                .currentMonth(dateFormat(year, m))
                                .lastMonthOfQuarter(quarter.getEndMonth() == m)
                                .startMonthOfQuarter(dateFormat(year, quarter.getStartMonth()))
                                .monthTypeIds(StringUtils.join(monthTypeSet, ","))
                                .quarterTypeIds(StringUtils.join(quarterTypeSet, ","))
                                .build();
                formList.add(form);
            }
        }
        List<ScoreResult> scoreList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(formList)) {
            scoreList = meetingOrgScoreMapper.orgMeetingScore(formList);
        }
        ScoreResultVo scoreResultVo = ScoreResult.getScoreResultVo(year, orgIdList, scoreList);
        log.info("组织开展组织生活得分:{}", JsonUtils.toJson(scoreResultVo));
        return scoreResultVo;
    }

    /**
     * 组织得分 参加组织生活得分 1.2.基础工作 述职评议结果 所有基层党组织 上一年度述职评议结果等次 4,3,2,1
     * 判断当前组织上一年度的述职评议结果等次，优得4分，良得3分，中得2分，差得1分； 每年12月判断一次，其他月份不判断也不得该项分 3
     *
     * @param regionId  区县id
     * @param orgIdList 组织id
     * @param year      年份
     * @return 分数 基础工作 述职评议分数
     * <AUTHOR>
     */
    public ScoreResultVo orgDebriefReviewScore(Long regionId, Integer year, List<Long> orgIdList) {
        List<MeetingOrgScoreMapper.OrgDebriefReviewScoreForm> formList = new ArrayList<>();
        for (Month month : Month.values()) {
            int m = month.value();
            Set<Long> orgIdSet = orgSnapshotService.baseOrg(regionId, orgIdList, year, m);
            if (CollectionUtils.isNotEmpty(orgIdSet)) {
                // 非12月份或非基层党组织 不计算此项分值
                if (month != Month.DECEMBER) {
                    continue;
                }
                MeetingOrgScoreMapper.OrgDebriefReviewScoreForm form =
                        MeetingOrgScoreMapper.OrgDebriefReviewScoreForm.builder()
                                .orgIdSet(orgIdSet)
                                .year(year - 1)
                                .currentMonth(dateFormat(year, m))
                                .rating1Score(scoreConfig.getOrgScore().getDebriefReviewRating1().getScore())
                                .rating2Score(scoreConfig.getOrgScore().getDebriefReviewRating2().getScore())
                                .rating3Score(scoreConfig.getOrgScore().getDebriefReviewRating3().getScore())
                                .rating4Score(scoreConfig.getOrgScore().getDebriefReviewRating4().getScore())
                                .ratingOtherScore(0.0)
                                .build();
                formList.add(form);
            }
        }
        List<ScoreResult> scoreList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(formList)) {
            scoreList = meetingOrgScoreMapper.orgDebriefReviewScore(formList);
        }
        ScoreResultVo scoreResultVo = ScoreResult.getScoreResultVo(year, orgIdList, scoreList);
        log.info("组织述职评议得分:{}", JsonUtils.toJson(scoreResultVo));
        return scoreResultVo;
    }

    /**
     * 用户得分 因为每一个用户每一个月的查询条件都不一样，此方法不改为union all 的sql
     *
     * @param regionId   区县id
     * @param userIdList 用户id
     * @param year       年份
     * @return 分数 基础工作 参加组织生活得分
     */
    public ScoreResultVo userBasicWorkScore(Long regionId, Integer year, List<Long> userIdList) {
        List<ScoreResult> scoreList = new ArrayList<>();
        if (year != null && CollectionUtils.isNotEmpty(userIdList)) {
            for (Long uid : userIdList) {
                for (Month month : Month.values()) {
                    int m = month.value();
                    // concat(uid,'-',#{item.currentMonth})
                    String key = uid + "-" + dateFormat(year, month.value());
                    ScoreResult scoreResult = new ScoreResult();
                    scoreList.add(scoreResult);
                    scoreResult.setK(key);
                    scoreResult.setScore(userBasicWorkScore(regionId, uid, DateUtils.dateFormat(year, m)));
                }
            }
        }
        ScoreResultVo scoreResultVo = ScoreResult.getScoreResultVo(year, userIdList, scoreList);
        log.info("用户参加组织生活得分:{}", JsonUtils.toJson(scoreResultVo));
        return scoreResultVo;
    }

    /**
     * 用户得分
     *
     * @param regionId  区县id
     * @param userId    用户id
     * @param queryDate 月份 yyyy-mm
     * @return 分数 基础工作 参加组织生活得分
     */
    private Double userBasicWorkScore(Long regionId, Long userId, String queryDate) {
        double score = 0.0;
        // 入参为空 返回0
        if (regionId == null || userId == null || StringUtils.isBlank(queryDate)) {
            return score;
        }
        int year = year(queryDate);
        int month = month(queryDate);
        // 查询用户的快照信息
        UserSnapshotEntity userInfo = userInfo(regionId, userId, year, month);
        // 未查询到组织信息 返回0
        if (userInfo == null || userInfo.getOrgId() == null || userInfo.getDateMonth() == null) {
            return score;
        }
        // 参加组织生活得分
        score += userMeetingScore(regionId, userInfo, year, month);
        return score;
    }

    /**
     * 用户得分 奖惩登记 非离退休党员 是否有奖励登记（可按每条设置分值） 3 判断当前党员在当月是否有奖励登记，每有一条增加该项分 非离退休党员 是否有惩罚登记（可按每条设置分值） -3
     * 判断当前党员在当月是否有惩罚登记，每有一条扣减该项分
     *
     * @param regionId   区县id
     * @param userIdList 用户信息
     * @param year       年份
     * @return 分数 优秀党员 奖惩得分
     */
    public ScoreResultVo excellentPartyMemberScore(
            Long regionId, Integer year, List<Long> userIdList) {
        List<MeetingUserScoreMapper.UserCommendPenalizeScoreForm> formList = new ArrayList<>();
        for (Month month : Month.values()) {
            int m = month.value();
            // 查询非离退休党员的id
            Set<Long> userIdSet = userSnapshotService.noRetired(regionId, userIdList, year, m);
            if (CollectionUtils.isNotEmpty(userIdSet)) {
                MeetingUserScoreMapper.UserCommendPenalizeScoreForm form =
                        MeetingUserScoreMapper.UserCommendPenalizeScoreForm.builder()
                                .userIdSet(userIdSet)
                                .regionId(regionId)
                                .commendScore(scoreConfig.getUserScore().getUserCommend().getScore())
                                .penalizeScore(scoreConfig.getUserScore().getUserPenalize().getScore())
                                .currentMonth(dateFormat(year, m))
                                .build();
                formList.add(form);
            }
        }
        List<ScoreResult> scoreList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(formList)) {
            scoreList = meetingUserScoreMapper.userCommendPenalizeScore(formList);
        }
        ScoreResultVo scoreResultVo = ScoreResult.getScoreResultVo(year, userIdList, scoreList);
        log.info("优秀党员奖惩得分:{}", JsonUtils.toJson(scoreResultVo));
        return scoreResultVo;
    }

    /**
     * 用户得分
     *
     * @param regionId   区县id
     * @param userIdList 用户id
     * @param year       年份 yyyy
     * @return 分数 领导干部示范带头 双重组织生活得分
     */
    public ScoreResultVo userLeadScore(Long regionId, Integer year, List<Long> userIdList) {
        List<ScoreResult> scoreList = new ArrayList<>();
        if (year != null && CollectionUtils.isNotEmpty(userIdList)) {
            for (Long uid : userIdList) {
                for (Month month : Month.values()) {
                    int m = month.value();
                    // concat(uid,'-',#{item.currentMonth})
                    String key = uid + "-" + dateFormat(year, month.value());
                    ScoreResult scoreResult = new ScoreResult();
                    scoreList.add(scoreResult);
                    scoreResult.setK(key);
                    scoreResult.setScore(userLeadScore(regionId, uid, DateUtils.dateFormat(year, m)));
                }
            }
        }
        ScoreResultVo scoreResultVo = ScoreResult.getScoreResultVo(year, userIdList, scoreList);
        log.info("领导干部示范带头,双重组织生活得分:{}", JsonUtils.toJson(scoreResultVo));
        return scoreResultVo;
    }

    /**
     * 用户得分
     *
     * @param regionId  区县id
     * @param userId    用户id
     * @param queryDate 月份 yyyy-mm
     * @return 分数 领导干部示范带头 双重组织生活得分
     */
    private Double userLeadScore(Long regionId, Long userId, String queryDate) {
        double score = 0.0;
        // 入参为空 返回0
        if (regionId == null || userId == null || StringUtils.isBlank(queryDate)) {
            return score;
        }
        int year = year(queryDate);
        int month = month(queryDate);
        // 查询用户的快照信息
        UserSnapshotEntity userInfo = userInfo(regionId, userId, year, month);
        // 未查询到组织信息 返回0
        if (userInfo == null) {
            return score;
        }
        // 双重组织生活的分
        score += userDualLifeScore(regionId, userInfo, year, month);
        return score;
    }

    /**
     * 组织生活 非离退休党员 党课是否参加（按季度统计） 35 判断当前党员参加次数是否全部满足，是则得该项分； 按季度的在每季度的最后一月进行判断，其他月份不判断 非离退休党员
     * 党员大会是否参加（按季度统计） 非离退休党员 主题党日是否参加（按月统计） 非离退休党员
     *
     * <p>党小组会是否参加（按月统计，仅统计有党小组的支部）
     *
     * <p>非离退休党员 支委会是否参加（按月统计，仅统计支委会成员的人）
     *
     * @param regionId 区县id
     * @param userInfo 组织信息
     * @param year     年份
     * @param month    月份
     * @return double 分值
     */
    private double userMeetingScore(
            Long regionId, UserSnapshotEntity userInfo, Integer year, Integer month) {
        double score = 0.0;
        // 非离退休党员才计算此项
        if (!userSnapshotService.noRetirePartyMember(
                regionId, userInfo.getUserId(), DateUtils.dateFormat(year, month))) {
            return score;
        }
        List<MeetingConfig.MeetingType> typeList = meetingConfig.getType().get(regionId);
        // 季度的typeId
        Set<Long> quarterTypeSet =
                typeList.stream()
                        .filter(t -> t.getPeriod().equals(MeetingConfig.PERIOD_QUARTER))
                        .map(MeetingConfig.MeetingType::getTypeId)
                        .collect(Collectors.toSet());
        //  查询用户所在组织的党小组数量
        Integer partyGroupNum =
                orgGroupMapper.partyGroupNumByOrgId(userInfo.getOrgId(), userInfo.getDateMonth());
        partyGroupNum = partyGroupNum == null ? 0 : partyGroupNum;
        //  查询用户所在支委会的数量
        Integer periodNum =
                orgPeriodMapper.periodNumByUserId(userInfo.getUserId(), userInfo.getDateMonth());
        periodNum = periodNum == null ? 0 : periodNum;
        // 月度的typeId
        int finalPartyGroupNum = partyGroupNum;
        int finalPeriodNum = periodNum;
        Set<Long> monthTypeSet =
                typeList.stream()
                        .filter(
                                t -> {
                                    if (t.getPeriod().equals(MeetingConfig.PERIOD_MONTH)) {
                                        if (t.getMappingType().equals(MeetingConfig.PARTY_GROUP)) {
                                            // 党小组会 判断是否有党小组
                                            return finalPartyGroupNum > 0;
                                        } else if (t.getMappingType().equals(MeetingConfig.PARTY_COMMITTEE)) {
                                            // 支委会 判断是否是支委会成员 所在支委会数量大于0
                                            return finalPeriodNum > 0;
                                        } else {
                                            return true;
                                        }
                                    }
                                    return false;
                                })
                        .map(MeetingConfig.MeetingType::getTypeId)
                        .collect(Collectors.toSet());
        // 获取季度
        QuarterUtils.Quarter quarter = QuarterUtils.getQuarterByMonth(month);
        int typeNum = monthTypeSet.size();
        if (quarter.getEndMonth() == month) {
            typeNum += quarterTypeSet.size();
        }
        MeetingUserScoreMapper.UserMeetingScoreForm form =
                MeetingUserScoreMapper.UserMeetingScoreForm.builder()
                        .score(scoreConfig.getUserScore().getMeeting().getScore())
                        .min(scoreConfig.getUserScore().getMeeting().getMin())
                        .deductScore(0.0)
                        .userId(userInfo.getUserId())
                        .regionId(regionId)
                        .currentMonth(dateFormat(year, month))
                        .lastMonthOfQuarter(quarter.getEndMonth() == month)
                        .startMonthOfQuarter(dateFormat(year, quarter.getStartMonth()))
                        .monthTypeIds(StringUtils.join(monthTypeSet, ","))
                        .quarterTypeIds(StringUtils.join(quarterTypeSet, ","))
                        .typeNum(typeNum)
                        .status(scoreConfig.getUserScore().getMeeting().getStatus())
                        .signStatus(scoreConfig.getUserScore().getMeeting().getSignStatus())
                        .build();
        List<MeetingUserScoreMapper.UserJoinMeetingRecord> userJoinMeetingRecords = meetingUserScoreMapper.userMeetingScore(form);
        if (CollectionUtils.isNotEmpty(userJoinMeetingRecords)) {
            Map<Long, Long> joinResult = new HashMap<>();
            Map<Long, List<MeetingUserScoreMapper.UserJoinMeetingRecord>> meetingMap = userJoinMeetingRecords.stream()
                    .collect(Collectors.groupingBy(MeetingUserScoreMapper.UserJoinMeetingRecord::getMeetingId));
            meetingMap.forEach((k, v) -> {
                boolean isJoin = true;
                for (MeetingUserScoreMapper.UserJoinMeetingRecord userJoinMeetingRecord : v) {
                    if (!userJoinMeetingRecord.getSignStatus().equals(1)) {
                        isJoin = false;
                        break;
                    }
                }
                if (isJoin) {
                    v.forEach(t -> {
                        if (joinResult.containsKey(t.getTypeId())) {
                            joinResult.put(t.getTypeId(), joinResult.get(t.getTypeId()) + 1);
                        } else {
                            joinResult.put(t.getTypeId(), 1L);
                        }
                    });
                }
            });
            Boolean monthMeetJoin = true;
            for (Long type : monthTypeSet) {
                if (!joinResult.containsKey(type)) {
                    monthMeetJoin = false;
                    break;
                }
            }
            Boolean quarterMeetJoin = true;
            if (form.getLastMonthOfQuarter()) {
                for (Long type : quarterTypeSet) {
                    if (!joinResult.containsKey(type)) {
                        quarterMeetJoin = false;
                        break;
                    }
                }
            }
            if (monthMeetJoin && quarterMeetJoin) {
                return scoreConfig.getUserScore().getMeeting().getScore();
            }
        }
        return score;
    }

    /**
     * 双重组织生活 机关单位领导干部 双重组织生活次数是否满足规定，每年大于2次 5 判断当前领导干部的双重组织生活次数是否满足规定，每年12月判断，其他月份不判断也不得该项分
     *
     * @param regionId 区县id
     * @param userInfo 用户信息
     * @param year     年份
     * @param month    月份
     * @return double 分值
     */
    private double userDualLifeScore(
            Long regionId, UserSnapshotEntity userInfo, Integer year, Integer month) {
        double score = 0.0;
        // 非季度最后一月 或 非领导班子 不计算此项
        // 获取季度
        QuarterUtils.Quarter quarter = QuarterUtils.getQuarterByMonth(month);
        if (month != quarter.getEndMonth() || !leader(userInfo)) {
            return score;
        }
        List<MeetingConfig.MeetingType> typeList = meetingConfig.getType().get(regionId);
        // typeId
        Set<Long> typeSet =
                typeList.stream().map(MeetingConfig.MeetingType::getTypeId).collect(Collectors.toSet());
        MeetingUserScoreMapper.UserMeetingScoreForm form =
                MeetingUserScoreMapper.UserMeetingScoreForm.builder()
                        .score(scoreConfig.getUserScore().getDualLife().getScore())
                        .min(scoreConfig.getUserScore().getDualLife().getMin())
                        .deductScore(0.0)
                        .userId(userInfo.getUserId())
                        .regionId(regionId)
                        .currentMonth(dateFormat(year, month))
                        .startMonthOfQuarter(dateFormat(year, quarter.getStartMonth()))
                        .typeIds(StringUtils.join(typeSet, ","))
                        .status(scoreConfig.getUserScore().getMeeting().getStatus())
                        .signStatus(scoreConfig.getUserScore().getMeeting().getSignStatus())
                        .build();
        score = meetingUserScoreMapper.userDualLifeScore(form);
        log.info("双重组织生活:{}", score);
        return score;
    }

    /**
     * 用户的快照信息
     *
     * @return UserSnapshotEntity
     */
    private UserSnapshotEntity userInfo(Long regionId, Long userId, Integer year, Integer month) {
        return userSnapshotService.getUserSnapshot(regionId, userId, dateFormat(year, month));
    }

    /**
     * 判断是否是党支部
     */
    private boolean branchOrg(OrgSnapshotEntity orgInfo) {
        if (orgInfo == null) {
            return false;
        }
        return orgTypeConfig.getBranchChild().contains(orgInfo.getOrgTypeChild());
    }

    /**
     * 判断是否是 机关单位领导干部
     *
     * @param userInfo 用户信息
     * @return true 是领导班子
     */
    private boolean leader(UserSnapshotEntity userInfo) {
        List<OrgLeaderEntity> leaderEntityList =
                leaderMapper.findLeaderInfoByUserId(userInfo.getUserId(), userInfo.getDateMonth());
        return CollectionUtils.isNotEmpty(leaderEntityList);
    }

    /**
     * 年月转换为 yyyy-MM
     *
     * @param year  年份
     * @param month 月份
     * @return String yyyy-MM
     */
    private String dateFormat(int year, int month) {
        if (month > 9) {
            return year + "-" + month;
        } else {
            return year + "-0" + month;
        }
    }

    /**
     * 截取年份 yyyy-MM
     *
     * @param date yyyy-MM
     * @return year
     */
    private int year(String date) {
        String[] split = date.split("-");
        return Integer.parseInt(split[0]);
    }

    /**
     * 截取月份 yyyy-MM
     *
     * @param date yyyy-MM
     * @return month
     */
    private int month(String date) {
        String[] split = date.split("-");
        return Integer.parseInt(split[1]);
    }
}
