package com.goodsogood.ows.service.meeting.dss;

import com.goodsogood.ows.configuration.MeetingConfig;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.model.mongodb.dss.MeetingDevelopInfo;
import com.goodsogood.ows.model.vo.meeting.MonthMeetingForm;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.QuarterUtils;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.goodsogood.ows.configuration.MeetingConfig.PERIOD_MONTH;
import static com.goodsogood.ows.configuration.MeetingConfig.PERIOD_QUARTER;

/**
 * Description: 查询执行类
 *
 * <AUTHOR>
 * @version 2020/11/10 9:30
 */
@Service
@Log4j2
public class DssMeetingHandler {
  private final MeetingMapper meetingMapper;
  private final OrgService orgService;
  private final MeetingConfig meetingConfig;

  public DssMeetingHandler(
      MeetingMapper meetingMapper, OrgService orgService, MeetingConfig meetingConfig) {
    this.meetingMapper = meetingMapper;
    this.orgService = orgService;
    this.meetingConfig = meetingConfig;
  }

  /**
   * 查询党委下未完成的支部数量
   *
   * @param year 年
   * @param month 月
   * @param orgId 党委id
   * @return int unfinishedBranchNum
   */
  public UnfinishedBranchNumForm unfinishedBranchNum(
      int year, int month, Long regionId, Long orgId) {
    UnfinishedBranchNumForm unfinishedBranchNumForm = new UnfinishedBranchNumForm();
    // 获取支部id
    Set<Long> branchIdSet = orgService.getPartyBranch(orgId,year,month);
    unfinishedBranchNumForm.setBranchNum(branchIdSet.size());
    // 查询未完成会议的支部数量
    QuarterUtils.Quarter quarter = QuarterUtils.getQuarterByMonth(month);
    int unfinishedBranchNum = 0;
    if (CollectionUtils.isNotEmpty(branchIdSet)) {
      unfinishedBranchNum =
          meetingMapper.unfinishedBranchNum(
              DateUtils.dateFormat(year, month),
              month == quarter.getEndMonth(),
              DateUtils.dateFormat(year, quarter.getStartMonth()),
              StringUtils.join(branchIdSet,","),
              monthTypeIds(regionId),
              quarterTypeIds(regionId));
    }
    unfinishedBranchNumForm.setUnfinishedBranchNum(unfinishedBranchNum);
    unfinishedBranchNumForm.setFinishedBranchNum(
        unfinishedBranchNumForm.branchNum - unfinishedBranchNum);
    return unfinishedBranchNumForm;
  }

  List<MonthMeetingForm> getMonthOrgMeeting( Integer year,Long regionId,Long orgId){
   return meetingMapper.getMonthOrgMeeting(year, orgId, allTypeIds(regionId));
  }
  List<MeetingDevelopInfo> getMonthUserMeeting(Integer year, Integer month, Long regionId, Long userId){
    return meetingMapper.getMonthUserMeeting(year, month, allTypeIds(regionId),userId);
  }
  @Data
  static class UnfinishedBranchNumForm {
    private Integer branchNum;
    private Integer unfinishedBranchNum;
    private Integer finishedBranchNum;
  }

  /**
   * 月度会议类型
   *
   * @param regionId 区县id
   * @return String monthTypeIds
   */
  private String monthTypeIds(Long regionId) {
    return typeIds(regionId,PERIOD_MONTH);
  }
  /**
   * 季度会议类型
   *
   * @param regionId 区县id
   * @return String quarterTypeIds
   */
  private String quarterTypeIds(Long regionId) {
    return typeIds(regionId,PERIOD_QUARTER);
  }
  /**
   * 会议类型
   *
   * @param regionId 区县id
   * @return String quarterTypeIds
   */
  public MeetingConfig.MeetingType getMeetingType(Long regionId,Long typeId) {
    List<MeetingConfig.MeetingType> list = meetingConfig.getType().get(regionId);
    if (CollectionUtils.isNotEmpty(list)) {
     for(MeetingConfig.MeetingType mt:list){
       if(mt.getTypeId().equals(typeId)){
         return mt;
       }
     }
    }
    return null;
  }
  /**
   * 所有会议类型
   *
   * @param regionId 区县id
   * @return String quarterTypeIds
   */
  private String allTypeIds(Long regionId) {
    List<MeetingConfig.MeetingType> list = meetingConfig.getType().get(regionId);
    if (CollectionUtils.isNotEmpty(list)) {
      return StringUtils.join(
              list.stream()
                      .map(MeetingConfig.MeetingType::getTypeId)
                      .collect(Collectors.toSet()),
              ",");
    }
    return "";
  }

  private String typeIds(Long regionId,int period) {
    List<MeetingConfig.MeetingType> list = meetingConfig.getType().get(regionId);
    if (CollectionUtils.isNotEmpty(list)) {
      return StringUtils.join(
              list.stream()
                      .filter(t -> t.getPeriod().equals(period))
                      .map(MeetingConfig.MeetingType::getTypeId)
                      .collect(Collectors.toSet()),
              ",");
    }
    return "";
  }
}
