package com.goodsogood.ows.service.meeting.dss;

import com.goodsogood.ows.annotions.Logging;
import com.goodsogood.ows.configuration.MeetingConfig;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.mapper.user.OrgPeriodMapper;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.dss.MeetingCompletionMap;
import com.goodsogood.ows.model.mongodb.dss.OrgLifeInfo;
import com.goodsogood.ows.model.vo.meeting.YearMeetingForm;
import com.goodsogood.ows.service.impl.DssIndexBuilder;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.SqlJointUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Description: 决策辅助首页
 *
 * <AUTHOR>
 * @version 2020/11/9 11:10
 */
@Service
@Log4j2
public class DssIndexFromMeeting implements DssIndexBuilder {

    private final MeetingMapper meetingMapper;
    private final OrgPeriodMapper orgPeriodMapper;
    private final MeetingConfig meetingConfig;
    private final OrgTypeConfig orgTypeConfig;
    private final OrgService orgService;
    private final DssMeetingHandler dssMeetingHandler;

    public DssIndexFromMeeting(
            MeetingMapper meetingMapper,
            OrgPeriodMapper orgPeriodMapper,
            MeetingConfig meetingConfig,
            OrgTypeConfig orgTypeConfig,
            OrgService orgService,
            DssMeetingHandler dssMeetingHandler) {
        this.meetingMapper = meetingMapper;
        this.orgPeriodMapper = orgPeriodMapper;
        this.meetingConfig = meetingConfig;
        this.orgTypeConfig = orgTypeConfig;
        this.orgService = orgService;
        this.dssMeetingHandler = dssMeetingHandler;
    }

    /**
     * 首页组织生活信息
     *
     * @param info 决策辅助首页实体类 info.rootId 顶级党组织ID info.year 生成年份
     * @return info
     */
    @Override
    @Logging
    public IndexInfo buildIndex(IndexInfo info) {
        // 初始化化组织生活信息
        info.setOrgLifeInfo(new OrgLifeInfo());
        OrgLifeInfo orgLifeInfo = info.getOrgLifeInfo();
        yearOverview(info, orgLifeInfo);
        monthOverview(info, orgLifeInfo);
        return info;
    }

    /**
     * 月度概况
     *
     * @param info        首页信息
     * @param orgLifeInfo 组织生活信息
     */
    public void monthOverview(IndexInfo info, OrgLifeInfo orgLifeInfo) {
        // 初始化完成数量
        MeetingCompletionMap meetingCompletionMap = new MeetingCompletionMap();
        orgLifeInfo.setMeetingCompletionMap(meetingCompletionMap);
        List<Integer> complete = new ArrayList<>(12);
        List<Integer> incomplete = new ArrayList<>(12);
        meetingCompletionMap.setComplete(complete);
        meetingCompletionMap.setIncomplete(incomplete);
        // 分别计算每月完成的党委数量
        for (int m = 1; m <= 12; m++) {
            // 查询党委信息
            Set<Long> orgIdSet = orgService.getOfficeCommittee(info.getRootId(), info.getYear(), m);
            // 未完成的党委数量
            int incompleteNum = 0;
            Set<Long> incompleteSet = new HashSet<>();
            if (CollectionUtils.isNotEmpty(orgIdSet)) {
                for (Long oid : orgIdSet) {
                    DssMeetingHandler.UnfinishedBranchNumForm unfinishedBranchNum =
                            dssMeetingHandler.unfinishedBranchNum(
                                    info.getYear(), m, info.getRegionId(), oid);
                    // 存在未完成的支部 未完成支委数量+1
                    if (unfinishedBranchNum.getUnfinishedBranchNum() > 0) {
                        incompleteNum++;
                        incompleteSet.add(oid);
                    }
                }
            }
            // 党委总数-未完成总数 是完成的党委数量
            complete.add(orgIdSet.size() - incompleteNum);
            log.info("{}未完成党委{}个：{}", DateUtils.dateFormat(info.getYear(), m), incompleteSet.size(), JsonUtils.toJson(incompleteSet));
            incomplete.add(incompleteNum);
        }
    }

    /**
     * 年度概况
     *
     * @param info        首页信息
     * @param orgLifeInfo 组织生活信息
     */
    private void yearOverview(IndexInfo info, OrgLifeInfo orgLifeInfo) {
        // 查询非离退休支部数量
        Integer noRetireBranchNum;
        List<Long> noRetireBranchOrgList;
        // 当前年或之后的年份 获取当前的支部数量
        if (info.getYear() >= DateTime.now().getYear()) {
            noRetireBranchOrgList = orgService.meetingGetPartyBranch(info.getRootId());
//            noRetireBranchNum = orgService.getPartyBranch(info.getRootId(), null, true).size();
        } else {
            // 其它年份 获取历史数据
            noRetireBranchOrgList = orgService.meetingGetHistoryPartyBranch(info.getRootId(), info.getYear());
//          noRetireBranchNum = orgService.getHistoryPartyBranch(info.getRootId(), info.getYear(), true, null).size();
        }
        if (noRetireBranchOrgList == null) {
            noRetireBranchOrgList = new ArrayList<>();
        }
        noRetireBranchNum = noRetireBranchOrgList.size();
        String noRetireBranchOrgIds = SqlJointUtil.longListToStr(noRetireBranchOrgList);
        noRetireBranchOrgIds = StringUtils.isBlank(noRetireBranchOrgIds) ? "-1" : noRetireBranchOrgIds;

        // 查询有支委会的支部数量
        Integer hasPeriodBranchNum;
        String dateMonth = "";
        if (info.getYear().equals(LocalDate.now().getYear())) {
            int month = LocalDate.now().getMonth().getValue();
            String monthStr = month <= 9 ? "0" + month : month + "";
            dateMonth = LocalDate.now().getYear() + "-" + monthStr;
        } else {
            dateMonth = info.getYear() + "-12";
        }
        List<Long> hasPeriodBranchOrgList =
                orgPeriodMapper.hasPeriodBranchNum(
                        info.getRegionId(),
                        dateMonth,
                        StringUtils.join(orgTypeConfig.getBranchChild(), ","));
        if (hasPeriodBranchOrgList == null) {
            hasPeriodBranchOrgList = new ArrayList<>();
        }
        hasPeriodBranchOrgList.retainAll(noRetireBranchOrgList);
        hasPeriodBranchNum = hasPeriodBranchOrgList.size();
        String hasPeriodBranchOrgIds = SqlJointUtil.longListToStr(hasPeriodBranchOrgList);
//        hasPeriodBranchOrgIds = StringUtils.isBlank(hasPeriodBranchOrgIds) ? "-1" : hasPeriodBranchOrgIds;

        // 查询全年会议次数
        YearMeetingForm yearMeetingForm = meetingMapper.getYearMeeting(
                info.getYear(),
                meetingConfig.getType().get(info.getRegionId()),
                noRetireBranchOrgIds,
//                hasPeriodBranchOrgIds
                noRetireBranchOrgIds
        );

        // 年度共开展支部组织生活次数
        orgLifeInfo.setMeetingNum(yearMeetingForm.getTotal());

        if (noRetireBranchNum == 0) {
            orgLifeInfo.setPartyMemberMeeting(0);
            orgLifeInfo.setPartyThemeMeeting(0);
        } else {
            // 党员大会平均召开次数
            orgLifeInfo.setPartyMemberMeeting(Math.round(yearMeetingForm.getPartyMember().floatValue() / noRetireBranchNum.floatValue()));
            // 主题党日平均召开次数
            orgLifeInfo.setPartyThemeMeeting(Math.round(yearMeetingForm.getPartyThemeDay() / noRetireBranchNum.floatValue()));
        }

        if (hasPeriodBranchNum == 0) {
            orgLifeInfo.setPartyCommitteeMeeting(0);
        } else {
            // 党支部委员会会议平均召开次数
            orgLifeInfo.setPartyCommitteeMeeting(
                    Math.round(yearMeetingForm.getPartyCommittee() / hasPeriodBranchNum.floatValue()));
        }
    }
}
