package com.goodsogood.ows.service.meeting.dss;

import com.goodsogood.ows.annotions.Logging;
import com.goodsogood.ows.configuration.MeetingConfig;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.dss.BranchMeetingInfo;
import com.goodsogood.ows.model.mongodb.dss.MeetingJoin;
import com.goodsogood.ows.model.vo.meeting.MonthMeetingForm;
import com.goodsogood.ows.service.impl.DssPartyBranchBuilder;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: 决策辅助党支部
 *
 * <AUTHOR>
 * @version 2020/11/9 11:11
 */
@Service
@Log4j2
public class DssPartyBranchFromMeeting implements DssPartyBranchBuilder {
  private final DssMeetingHandler dssMeetingHandler;
  private final MeetingConfig meetingConfig;

  public DssPartyBranchFromMeeting(
      DssMeetingHandler dssMeetingHandler, MeetingConfig meetingConfig) {
    this.dssMeetingHandler = dssMeetingHandler;
    this.meetingConfig = meetingConfig;
  }

  @Override
  @Logging
  public PartyBranchInfo buildPartyBranch(PartyBranchInfo info) {
    // 初始化信息
    info.setOrgLifeInfo(new BranchMeetingInfo());
    List<MeetingJoin> meetingJoin = new ArrayList<>(10);
    info.getOrgLifeInfo().setMeetingJoin(meetingJoin);
    List<MonthMeetingForm> monthMeetingFormList =
        dssMeetingHandler.getMonthOrgMeeting(
            info.getYear(), info.getRegionId(), info.getOrganizationId());
    Map<String, Integer> map = new HashMap<>(monthMeetingFormList.size());
    int total = 0;
    for (MonthMeetingForm mf : monthMeetingFormList) {
      String mk = mf.getMonth() + ":" + mf.getTypeId();
      map.put(mk, mf.getTotal());
      total += mf.getTotal();
    }
    info.getOrgLifeInfo().setMeetingNum(total);
    List<MeetingConfig.MeetingType> typeList = meetingConfig.getType().get(info.getRegionId());
    for (MeetingConfig.MeetingType type : typeList) {
      MeetingJoin mj = new MeetingJoin();
      List<Integer> data = new ArrayList<>(12);
      mj.setData(data);
      mj.setName(type.getType());
      meetingJoin.add(mj);
      for(int m=1;m<=12;m++){
        int num = 0;
        String key = m + ":" + type.getTypeId();
        if (map.containsKey(key)) {
          num = map.get(key);
        }
        data.add(num);
      }
    }
    return info;
  }
}
