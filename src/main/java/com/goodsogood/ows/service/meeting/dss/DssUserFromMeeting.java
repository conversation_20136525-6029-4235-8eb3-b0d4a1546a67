package com.goodsogood.ows.service.meeting.dss;

import com.goodsogood.ows.annotions.Logging;
import com.goodsogood.ows.configuration.MeetingConfig;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.model.mongodb.dss.MeetingDevelopInfo;
import com.goodsogood.ows.model.mongodb.dss.MeetingJoin;
import com.goodsogood.ows.model.mongodb.dss.MonthMeetingDevelopInfo;
import com.goodsogood.ows.model.mongodb.dss.UserMeetingInfo;
import com.goodsogood.ows.service.impl.DssUserBuilder;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Description: 决策辅助用户
 *
 * <AUTHOR>
 * @version 2020/11/9 11:12
 */
@Service
@Log4j2
public class DssUserFromMeeting implements DssUserBuilder {
  private final DssMeetingHandler dssMeetingHandler;
  private final MeetingConfig meetingConfig;

  public DssUserFromMeeting(DssMeetingHandler dssMeetingHandler, MeetingConfig meetingConfig) {
    this.dssMeetingHandler = dssMeetingHandler;
    this.meetingConfig = meetingConfig;
  }

  @Override
  @Logging
  public UserInfo buildUser(UserInfo info) {
    info.setOrgLifeInfo(new UserMeetingInfo());
    info.getOrgLifeInfo().setMeetingMonth(new ArrayList<>(12));
    List<MeetingJoin> meetingJoin = new ArrayList<>(10);
    info.getOrgLifeInfo().setMeetingJoin(meetingJoin);

    // k 类型  v 每月次数
    Map<Long, List<Integer>> dataMap = new HashMap<>(16);
    // 考核类型
    List<MeetingConfig.MeetingType> list = meetingConfig.getType().get(info.getRegionId());
    int total = 0;
    for (int m = 1; m <= 12; m++) {
      MonthMeetingDevelopInfo monthMeetingDevelopInfo = new MonthMeetingDevelopInfo();
      monthMeetingDevelopInfo.setMonth(m);

      // 查询用户会议记录
      List<MeetingDevelopInfo> meetingDevelopInfoList =
          dssMeetingHandler.getMonthUserMeeting(
              info.getYear(), m, info.getRegionId(), info.getUserId());
      monthMeetingDevelopInfo.setMeetingList(meetingDevelopInfoList);
      info.getOrgLifeInfo().getMeetingMonth().add(monthMeetingDevelopInfo);
      // 每种类型的次数
      Map<Long, Integer> numMap = new HashMap<>(16);
      if (CollectionUtils.isNotEmpty(meetingDevelopInfoList)) {
        total += meetingDevelopInfoList.size();
        for (MeetingDevelopInfo mi : meetingDevelopInfoList) {
          MeetingConfig.MeetingType mt =
              dssMeetingHandler.getMeetingType(info.getRegionId(), mi.getTypeId());
          if (mt != null) {
            numMap.put(
                mt.getTypeId(),
                (numMap.get(mt.getTypeId()) == null ? 1 : numMap.get(mt.getTypeId()) + 1));
          }
        }
      }
      for(MeetingConfig.MeetingType type: list){
        dataMap.computeIfAbsent(type.getTypeId(), k -> new ArrayList<>());
        if(numMap.get(type.getTypeId()) == null){
          dataMap.get(type.getTypeId()).add(0);
        }else{
          dataMap.get(type.getTypeId()).add(numMap.get(type.getTypeId()));
        }
      }
    }
    for(MeetingConfig.MeetingType type: list){
      MeetingJoin mj = new MeetingJoin();
      mj.setName(type.getType());
      if(dataMap.get(type.getTypeId()) == null){
        mj.setData(zeroList());
      }else{
        mj.setData(dataMap.get(type.getTypeId()));
      }
      meetingJoin.add(mj);
    }
    info.getOrgLifeInfo().setMeetingNum(total);
    return info;
  }

  private List<Integer> zeroList(){
    return Arrays.asList(0,0,0,0,0,0,0,0,0,0,0,0);
  }

}
