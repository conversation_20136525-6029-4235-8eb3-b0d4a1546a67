package com.goodsogood.ows.service.sas;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.StorageVariableEnum;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.mapper.sas.StatisticalLeaderOrgLifeMapper;
import com.goodsogood.ows.mapper.sas.StatisticalTempActivityMapper;
import com.goodsogood.ows.model.db.sas.StatisticalTempActivityEntity;
import com.goodsogood.ows.model.vo.sas.*;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-04-22 16:01
 **/
@Service
@Log4j2
public class StatisticalLeaderOrgLifeService {

    private final StatisticalLeaderOrgLifeMapper statisticalLeaderOrgLifeMapper;
    private final StatisticalOrgLifeService statisticalOrgLifeService;
    private final StatisticalTempActivityMapper statisticalTempActivityMapper;
    private final SimpleApplicationConfigHelper applicationConfigHelper;
    private final ObjectMapper mapper = new ObjectMapper();


    @Autowired
    public StatisticalLeaderOrgLifeService(StatisticalLeaderOrgLifeMapper statisticalLeaderOrgLifeMapper,
                                           StatisticalOrgLifeService statisticalOrgLifeService,
                                           StatisticalTempActivityMapper statisticalTempActivityMapper,
                                           SimpleApplicationConfigHelper applicationConfigHelper) {
        this.statisticalLeaderOrgLifeMapper = statisticalLeaderOrgLifeMapper;
        this.statisticalOrgLifeService=statisticalOrgLifeService;
        this.statisticalTempActivityMapper=statisticalTempActivityMapper;
        this.applicationConfigHelper = applicationConfigHelper;
    }

    /**
     * 统计领导组织生活信息
     * @param conditionForm
     * @return
     */
    public Page<List<StaLeaderOrgLife>> getLeadOrgLife(SasOrgLifeConditionForm conditionForm) {
//        Region.OrgData orgData = this.applicationConfigHelper.getOrgByRegionId(conditionForm.getRegionId());
        String[] dateArray= getLimitDate(conditionForm.getDateStr());
        Page<List<StaLeaderOrgLife>> page = PageHelper.
                startPage(conditionForm.getPageNo(), conditionForm.getPageSize()).
                doSelectPage(() -> this.statisticalLeaderOrgLifeMapper.
                        getLeadOrgLife(conditionForm.getOrgId(),
                                3L,
                                conditionForm.getYear(),
                                conditionForm.getDateStr(),
                                conditionForm.getIsRetire(),
                                conditionForm.getShowOrgType(),
                                conditionForm.getRegionId(),
                                conditionForm.getQueryLimit(),
                        ""+conditionForm.getYear()+"-"+dateArray[0],
                        ""+conditionForm.getYear()+"-"+dateArray[1]
                        ));
                 return  page;
    }

    /**
     * 导出领导组织生活Excel
     * @param conditionForm
     * @return
     */
    public List<StaLeaderOrgLife> exportLeadOrgLife(SasOrgLifeConditionForm conditionForm) {
        Region.OrgData orgData = this.applicationConfigHelper.getOrgByRegionId(conditionForm.getRegionId());
        String[] dateArray= getLimitDate(conditionForm.getDateStr());
        return this.statisticalLeaderOrgLifeMapper.
                getLeadOrgLife(conditionForm.getOrgId(),
                        orgData.getOrgId(),
                        conditionForm.getYear(),
                        conditionForm.getDateStr(),
                        conditionForm.getIsRetire(),
                        conditionForm.getShowOrgType(),
                        conditionForm.getRegionId(),
                        conditionForm.getQueryLimit(),
                        ""+conditionForm.getYear()+"-"+dateArray[0],
                        ""+conditionForm.getYear()+"-"+dateArray[1]);
    }

    /**
     * 这里保证时间字符串不能为空
     * @param dateStr
     * @return
     */
    private static String[] getLimitDate( String dateStr){
        String[] dateStrArray = dateStr.split(",");
        int dateStrStart = Integer.parseInt(dateStrArray[0]);
        String str1=dateStrStart>9 ? String.valueOf(dateStrStart) : ("0" + dateStrStart);
        int dateStrEnd = Integer.parseInt(dateStrArray[dateStrArray.length-1]);
        String str2=dateStrEnd>9 ? String.valueOf(dateStrEnd) : ("0" + dateStrEnd);
        return  new String[]{str1,str2};
    }



    /**
     * 导出excel
     * @param staLeaderOrgLives
     * @return
     */
    public  HSSFWorkbook exportPortExcel(List<StaLeaderOrgLife> staLeaderOrgLives,SasOrgLifeConditionForm conditionForm){
        HSSFWorkbook workBook = new HSSFWorkbook();
        //创建字体
        HSSFFont font = workBook.createFont();
        font.setFontHeightInPoints((short) 12);
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        //普通样式
        HSSFCellStyle style = workBook.createCellStyle();
        //style.setAlignment(HorizontalAlignment.ALIGN_CENTER);
        //style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        style.setFont(font);

        //头部字体
        HSSFFont headFont = workBook.createFont();
        headFont.setFontHeightInPoints((short)20 );
        headFont.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        //表头样式
        HSSFCellStyle headStyle = workBook.createCellStyle();
        //headStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        //headStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        headStyle.setAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headStyle.setFont(headFont);


        //得到文件名称
        String sheetName = bulidSheetName(1, conditionForm);
        HSSFSheet sheet = workBook.createSheet(sheetName);
        //遍历的最后一行数据
        //处理第二数据
        handlerWorkbookFirst(sheet,staLeaderOrgLives,style,conditionForm);
        // 用于处理合并请求数据
        List<StraCellRangeCol> listCellRang = new ArrayList<>();
        //处理第三列数据
        Integer traversingLastCol = handlerWorkbookSecondRow(sheet, staLeaderOrgLives,
                conditionForm, style, listCellRang);
        //根据数据来合并表头信息
        handlerWorkbookHead(sheet,traversingLastCol,headStyle,sheetName);
        //遍历数据
        try {
            handlerShowDetailData(sheet,staLeaderOrgLives,conditionForm,style);
        }catch (Exception ex){
            log.error("导出领导组织生活,遍历数据时发生异常，{}",ex);
        }
        // 合并日期占两行(4个参数，分别为起始行，结束行，起始列，结束列)
        // 行和列都是从0开始计数，且起始结束都会合并
        CellRangeAddress region = new CellRangeAddress(1 ,2, 0, 0);
        CellRangeAddress region1 = new CellRangeAddress( 1, 2, 1,1);
        CellRangeAddress region2 = new CellRangeAddress( 1,2 , 2,2);
        listCellRang.forEach(item->{
            CellRangeAddress regionItem = new CellRangeAddress( 1, 1, item.getFirstCol(),item.getLastCol());
            sheet.addMergedRegion(regionItem);
        });
        sheet.addMergedRegion(region);
        sheet.addMergedRegion(region1);
        sheet.addMergedRegion(region2);
        //设置列的宽度
        double[] colWidth = {12, 24, 30};
        setColumn(sheet, colWidth);
        return workBook;
    }


    /**
     * 处理表头信息
     * @param sheet
     * @param traversingLastCol
     * @param style
     */
    private  void  handlerWorkbookHead(HSSFSheet sheet,Integer traversingLastCol,HSSFCellStyle style,
                                       String sheetName){
        HSSFRow row0 = sheet.createRow(0);
        HSSFCell cell_01 = row0.createCell(0);
        cell_01.setCellStyle(style);
        cell_01.setCellValue(sheetName);
        CellRangeAddress regionHeader = new CellRangeAddress( 0, 0, 0,traversingLastCol);
        sheet.addMergedRegion(regionHeader);
    }

    /**
     * 处理第二行信息
     * @param staLeaderOrgLives
     */
    private  void  handlerWorkbookFirst(HSSFSheet sheet,List<StaLeaderOrgLife> staLeaderOrgLives,HSSFCellStyle style,SasOrgLifeConditionForm form){
        StaLeaderOrgLife staLeaderOrgLife = staLeaderOrgLives.get(0);
        //第一行数据
        HSSFRow row1 = sheet.createRow(1);
        HSSFCell cell_01 = row1.createCell(0);
        cell_01.setCellStyle(style);
        cell_01.setCellValue("序号");
        HSSFCell cell_02 = row1.createCell(1);
        cell_02.setCellStyle(style);
        cell_02.setCellValue("领导干部姓名");
        HSSFCell cell_03 = row1.createCell(2);
        cell_03.setCellStyle(style);
        cell_03.setCellValue("所在组织名称");
        //总那列开始计算
        int startCol=3;
        //取的活动类型
        List<StaMonth> list = staLeaderOrgLife.getListWrapper().getList();
        //从第四行开始添加统计年月信息
        //总的要循环的次数
        String [] staMonth = form.getDateStr().split(",");
        for (StaMonth aList : list) {
            for (int j = 0; j < staMonth.length + 1; j++) {
                if (j == 0) {
                    HSSFCell cell = row1.createCell(startCol);
                    cell.setCellStyle(style);
                    cell.setCellValue(aList.getActivityTypeName());
                }
                startCol++;
            }
        }

        HSSFCell cell = row1.createCell(startCol);
        cell.setCellStyle(style);
        cell.setCellValue("合计");
        CellRangeAddress regionLast = new CellRangeAddress(1, 2, startCol, startCol);
        sheet.addMergedRegion(regionLast);
        startCol++;

    }

    /**
     * 设置列的宽度； 256 的倍数
     * @param sheet
     * @param colWith
     */
    public void setColumn(HSSFSheet sheet, double[] colWith) {
        // 设置列宽
        for (int k = 0; k < colWith.length; k++) {
            sheet.setColumnWidth((short) k, (short) colWith[k] * 256);
        }
    }

    /**
     * 处理第三行数据
     * @param staLeaderOrgLives
     */
    private  Integer  handlerWorkbookSecondRow(HSSFSheet sheet,List<StaLeaderOrgLife> staLeaderOrgLives,SasOrgLifeConditionForm form,
                                               HSSFCellStyle style, List<StraCellRangeCol> listCellRang){
        List<StaMonth> list = staLeaderOrgLives.get(0).getListWrapper().getList();
        //总那列开始计算
        int startCol=3;
        //总的要循环的次数
        String [] staMonth = form.getDateStr().split(",");
        HSSFRow row2 = sheet.createRow(2);
        //从第四行开始添加统计年月信息
        for(int i=0;i<list.size();i++){
            StraCellRangeCol straCellRangeCol= new StraCellRangeCol();
            straCellRangeCol.setFirstCol(startCol);
            for(int j=0;j<staMonth.length+1;j++){
                HSSFCell cell = row2.createCell(startCol);
                //如果最后一条数据
                cell.setCellStyle(style);
                if(j==staMonth.length){
                    cell.setCellValue("小计");
                    straCellRangeCol.setLastCol(startCol);
                    listCellRang.add(straCellRangeCol);
                }else{
                    cell.setCellValue(staMonth[j] + "月");
                }
                startCol++;
            }
        }
        return startCol;
    }

    //开始显示数据
    private  void  handlerShowDetailData(HSSFSheet sheet,List<StaLeaderOrgLife> staLeaderOrgLives,SasOrgLifeConditionForm form,
                                         HSSFCellStyle style) throws Exception {
        for(int i=0;i<staLeaderOrgLives.size();i++){
            //第三行开始输出数据
            HSSFRow row = sheet.createRow(i+3);
            //得到遍历数据
            StaLeaderOrgLife staLeaderOrgLife = staLeaderOrgLives.get(i);
            //序号
            HSSFCell cell_01 = row.createCell(0);
            cell_01.setCellStyle(style);
            cell_01.setCellValue(i+1);
            //得到领导名称
            HSSFCell cell_02 = row.createCell(1);
            cell_02.setCellStyle(style);
            cell_02.setCellValue(staLeaderOrgLife.getLeaderName());
            //得到组织名称
            HSSFCell cell_03 = row.createCell(2);
            cell_03.setCellStyle(style);
            cell_03.setCellValue(staLeaderOrgLife.getOrgName());
            //总那列开始计算
            int startCol=3;
            int sumTotal=0;
            //总的要循环的次数
            String [] staMonth = form.getDateStr().split(",");
            //从第四行开始添加统计年月信息
            List<StaMonth> list = staLeaderOrgLife.getListWrapper().getList();
            for (StaMonth staMonthInfo : list) {
                //查询那些统计月份
                Map<String, Object> map = JsonUtils.bean2map(staMonthInfo);
                for (int j = 0; j < staMonth.length + 1; j++) {
                    HSSFCell cell = row.createCell(startCol);
                    //如果最后一条数据
                    cell.setCellStyle(style);
                    if (j == staMonth.length) {
                        cell.setCellValue(map.get("total").toString());
                        sumTotal = sumTotal + Integer.parseInt(map.get("total").toString());
                    } else {
                        cell.setCellValue(map.get("month" + staMonth[j]).toString());
                    }
                    startCol++;
                }
            }
            //最后一列合计所有统计
            HSSFCell cellLast = row.createCell(startCol);
            cellLast.setCellStyle(style);
            cellLast.setCellValue(sumTotal);
        }
    }

    /**
     * StaType:
     * 1.领导干部双重组织生活统计
     * 2.党支部组织生活统计
     * 3.党费交纳完成情况统计
     * 构建sheetName
     * @return
     */
    public   String bulidSheetName(Integer staType,SasOrgLifeConditionForm sasOrgLifeConditionForm){
            StringBuilder sb=new StringBuilder();
            String tableName="";
            String staProjectName=null;
            if(staType==1){
                staProjectName="领导干部双重组织生活统计";
                tableName="t_statistical_leader_org_life";
            }else if(staType==2){
                staProjectName="党支部组织生活统计";
                tableName="t_statistical_org_life";
            }else if(staType==3){
                staProjectName="党费交纳完成情况统计";
                tableName="t_statistical_party_fee";
            }
            //String orgName=statisticalOrgLifeService.getOrgName(sasOrgLifeConditionForm.getOrgId(),tableName);
            sb.append(sasOrgLifeConditionForm.getOrgName() == null ? "" : sasOrgLifeConditionForm.getOrgName()).append(sasOrgLifeConditionForm.getYear()).append("年");
            //查询时间类型
            //1-上半年，2-下半年，3-第一季度，4-第二季度，5-第三季度，6-第四季度
            Integer time = sasOrgLifeConditionForm.getTime();
            String staTime="";
            if (!sasOrgLifeConditionForm.getTimeType().equals(Constants.SELECT_TIME_TYPE_YEAR)){
                if(time==1){
                    staTime="上半年";
                }else if(time==2){
                    staTime="下半年";
                }else if(time==3){
                    staTime="第一季度";
                }else if(time==4){
                    staTime="第二季度";
                }else if(time==5){
                    staTime="第三季度";
                }else if(time==6){
                    staTime="第四季度";
                }else{
                   if(sasOrgLifeConditionForm.getTimeType().equals(Constants.SELECT_TIME_TYPE_MONTH)) {
                       staTime=sasOrgLifeConditionForm.getStartMonth()+"月-"+sasOrgLifeConditionForm.getEndMonth()+"月";
                   }
                }
                sb.append(staTime);
            }
            sb.append(staProjectName);
            return  sb.toString();
    }
    /**
     * 重写里面的参数
     * @param leadOrgLife
     * @return
     */
    public List<StaLeaderOrgLife> exportLeadOrgLifeDetailInfo(List<StaLeaderOrgLife> leadOrgLife,SasOrgLifeConditionForm conditionForm ) {
        //重写统计信息
        SqlParam sqlParam = statisticalOrgLifeService.buildSql(conditionForm);
        String totalParam=sqlParam.getTotalParam();
        String rowParam = sqlParam.getRowParam();
        leadOrgLife.forEach(item->{
               List<StaMonth> leadOrgLifeDetailInfo = statisticalLeaderOrgLifeMapper.getLeadOrgLifeDetailInfo(item.getLeaderUserId(),conditionForm.getOrgId(),conditionForm.getYear(),
                                                                                                              conditionForm.getDateStr(), totalParam, rowParam,conditionForm.getActivityTypeIds(),
                                                                                                                conditionForm.getIsRetire(),conditionForm.getShowOrgType(),item.getIds());
               StaMonthWrapper staMonthWrapper=new StaMonthWrapper();
               int total=0;
               for(int i=0;i<leadOrgLifeDetailInfo.size();i++){
                   total=leadOrgLifeDetailInfo.get(i).getTotal()+total;
               }
               staMonthWrapper.setTotality(total);
               staMonthWrapper.setList(leadOrgLifeDetailInfo);
               item.setListWrapper(staMonthWrapper);
           });
       return  leadOrgLife;
    }

    /**
     * 重写里面的参数
     * @param leadOrgLife
     * @return
     */
    public Page<List<StaLeaderOrgLife>> getLeadOrgLifeDetailInfo(Page<List<StaLeaderOrgLife>> leadOrgLife,SasOrgLifeConditionForm conditionForm ) {
        //重写统计信息
        List<StaLeaderOrgLife> result =(List) leadOrgLife.getResult();
        SqlParam sqlParam = statisticalOrgLifeService.buildSql(conditionForm);
        String totalParam=sqlParam.getTotalParam();
        String rowParam = sqlParam.getRowParam();
        result.forEach(item->{
            List<StaMonth> leadOrgLifeDetailInfo = statisticalLeaderOrgLifeMapper.getLeadOrgLifeDetailInfo(item.getLeaderUserId(),conditionForm.getOrgId(),conditionForm.getYear(),
                    conditionForm.getDateStr(), totalParam, rowParam,conditionForm.getActivityTypeIds(),conditionForm.getIsRetire(),conditionForm.getShowOrgType(),item.getIds());
            StaMonthWrapper staMonthWrapper=new StaMonthWrapper();
            int total=0;
            for(int i=0;i<leadOrgLifeDetailInfo.size();i++){
                total=leadOrgLifeDetailInfo.get(i).getTotal()+total;
            }
            staMonthWrapper.setTotality(total);
            staMonthWrapper.setList(leadOrgLifeDetailInfo);
            item.setListWrapper(staMonthWrapper);
        });
        return  leadOrgLife;
    }

    /**
     * 统计组织生活类型
     * @return
     */
    public List<StaMeetingType> staMeetingType() {
       return statisticalLeaderOrgLifeMapper.staMeetingType();
    }

    /**
     * 处理变化
     * @param item
     */
    public void handlerMeetingTypeChange(StaMeetingType item) {
        Example example = new Example(StatisticalTempActivityEntity.class);
        example.createCriteria()
                .andEqualTo("activityId", item.getActivityId());
        StatisticalTempActivityEntity tempActivityEntity = statisticalTempActivityMapper.selectOneByExample(example);
        if(tempActivityEntity!=null){
            if(!tempActivityEntity.getTypeName().equals(item.getTypeName())){
                tempActivityEntity.setTypeName(item.getTypeName());
                statisticalTempActivityMapper.updateByPrimaryKey(tempActivityEntity);
            }
        }else{
            tempActivityEntity = new StatisticalTempActivityEntity();
            BeanUtils.copyProperties(item,tempActivityEntity);
            statisticalTempActivityMapper.insert(tempActivityEntity);
        }
    }

    /**
     * 考核系统拉取数据信息
     * @param pageNo
     * @param pageSize
     * @param orgId
     * @param staTimeCode
     * @return
     */
    public Page<EvalPullDataForm> evalPullData(Long pageNo, Long pageSize, Long orgId, Long staTimeCode,Long regionId) {

        Page<EvalPullDataForm> page = PageHelper.startPage(pageNo.intValue(), pageSize.intValue()).
                doSelectPage(() -> this.statisticalLeaderOrgLifeMapper.evalPullData(orgId,getQueryTime(staTimeCode),regionId));
        return  page;
    }

    /**
     * 根据查询时间code 返回那些查询时间
     * @param staTimeCode
     * @return
     */
    public String getQueryTime(Long staTimeCode){
        //查询所有时间
        if(staTimeCode==1){
            return  "INTERVAL 5 YEAR";
            //查询一年
        }else  if(staTimeCode==2){
            return  "INTERVAL  1 YEAR";
            //查询3个月
        }else if(staTimeCode==3){
            return  "INTERVAL  3 MONTH";
            //查询1个月
        }else if(staTimeCode==4){
            return  "INTERVAL  1 MONTH";
            //查询2个月
        }else if(staTimeCode==5){
            return  "INTERVAL  2 MONTH";
            //查询4个月
        }else if(staTimeCode==6){
            return  "INTERVAL  4 MONTH";
        }else {
            return  "INTERVAL  6 MONTH";
        }
    }



    /**
     * 目标考核领导干部参加组织生活情况
     * @param queryDate
     * @param regionId
     * @return
     */
    public List<EvalLeaderMeeting> getLeaderMeeting(String queryDate, Long regionId){
        return this.statisticalLeaderOrgLifeMapper.getLeaderMeeting(queryDate,regionId);
    }



    /**
     * 得到返回一个json 字条串的党小组数量
     * @param orgId
     * @return
     */
    public String getStorageVariable(Long orgId, StorageVariableEnum storageVariableEnum)   {
        try {
            List<Map<String, Object>> listMap = statisticalLeaderOrgLifeMapper.getStorageVariable(orgId,
                    storageVariableEnum.getCode());
            if (CollectionUtils.isEmpty(listMap)) {
                return "";
            } else {
                return mapper.writeValueAsString(listMap);
            }
        }catch (JsonProcessingException ex){
            log.error("getStorageVariable-发生异常-exMsg-{}",ex);
            return "";
        }
    }




}
