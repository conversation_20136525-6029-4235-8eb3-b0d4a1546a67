package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.PropertiesConfig;
import com.goodsogood.ows.configuration.RegionListConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 拉取每月20号工作提醒
 * <AUTHOR>
 */
@Service
@Log4j2
public class WorkRemindService {

    private static final int PageSize = 1000;

    private final StringRedisTemplate redisTemplate;
    private final MonthCollectService monthCollectService;
    private final OrgTypeConfig orgTypeConfig;
    private final PropertiesConfig propertiesConfig;
    private final AsyncWorkRemindService asyncWorkRemindService;
    private final SimpleApplicationConfigHelper configHelper;
    private final RegionListConfig regionListConfig;

    @Autowired
    public WorkRemindService(StringRedisTemplate redisTemplate, MonthCollectService monthCollectService,
                             OrgTypeConfig orgTypeConfig, PropertiesConfig propertiesConfig,
                             AsyncWorkRemindService asyncWorkRemindService, SimpleApplicationConfigHelper configHelper, RegionListConfig regionListConfig) {
        this.redisTemplate = redisTemplate;
        this.monthCollectService = monthCollectService;
        this.orgTypeConfig = orgTypeConfig;
        this.propertiesConfig = propertiesConfig;
        this.asyncWorkRemindService = asyncWorkRemindService;
        this.configHelper = configHelper;
        this.regionListConfig = regionListConfig;
    }

    public void pullBeforeWorkRemind(){
        // 清理Redis原数据
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REMIND_SENIOR);
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REMIND_GENERA);
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REMIND_WORK_COMMIT);
        // 获取党支部
        List<Integer> branchChild = this.orgTypeConfig.getBranchChild();
        // 获取区县列表
        List<Long> remindList = this.regionListConfig.getRemind();
        for (Long regionId : remindList) {
            Region.OrgData orgData = this.configHelper.getOrgByRegionId(regionId);
            //获取考核组织列表
            List<OrganizationBase> evalOrg = this.monthCollectService.getEvalOrg(orgData.getOrgId());
            if (CollectionUtils.isEmpty(evalOrg)){
                log.error("获取考核组织列表失败 -> [{}]", evalOrg);
                throw new ApiException("获取考核组织列表失败！");
            }
            this.generateWorkRemind(evalOrg, branchChild,orgData.getOrgId(),regionId);
        }
    }

    public void testBeforeWorkRemind(List<OrganizationBase> orgList) {
        // 清理Redis原数据
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REMIND_SENIOR);
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REMIND_GENERA);
        //获取考核组织列表
        List<OrganizationBase> evalOrg = orgList;
        if (CollectionUtils.isEmpty(evalOrg)){
            log.error("获取考核组织列表失败 -> [{}]", evalOrg);
            throw new ApiException("获取考核组织列表失败！");
        }

        // 获取党支部
        List<Integer> branchChild = this.orgTypeConfig.getBranchChild();
        this.generateWorkRemind(evalOrg, branchChild,3L,3L);
    }

    public void pullBeforeWorkRemindByRegion(Long regionId){
        // 清理Redis原数据
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REMIND_SENIOR);
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REMIND_GENERA);
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REMIND_WORK_COMMIT);
        // 获取党支部
        List<Integer> branchChild = this.orgTypeConfig.getBranchChild();
        // 获取区县列表
        Region.RegionData region = configHelper.getRegionByRegionId(regionId);
        Long orgId = region.getOrgData().getOrgId();
                //获取考核组织列表
        List<OrganizationBase> evalOrg = this.monthCollectService.getEvalOrg(orgId);
        if (CollectionUtils.isEmpty(evalOrg)){
            log.error("获取考核组织列表失败 -> [{}]", evalOrg);
            throw new ApiException("获取考核组织列表失败！");
        }
        this.generateWorkRemind(evalOrg, branchChild,orgId,regionId);

    }

    public void generateWorkRemind(List<OrganizationBase> evalOrg, List<Integer> branchChild,Long orgId,Long regionId) {

        //上月工作提醒（工委管理员）
        this.asyncWorkRemindService.workingCommitteeCollectInfo(orgId,regionId);

        for (int k = 1; k < evalOrg.size(); k ++) {
            List<OrganizationBase> baseList = this.startPage(evalOrg, k, PageSize);
            CountDownLatch latch = new CountDownLatch(baseList.size());
            for (int i = 0; i < baseList.size(); i++) {
                OrganizationBase org = baseList.get(i);
                // 排除考核组织
                if (propertiesConfig.getExcludeOrg().contains(org.getOrgId())) {
                    log.debug("当前组织[{}]排除在外，不发报告。", org.getOrgId());
                    latch.countDown();
                    continue;
                }
                this.asyncWorkRemindService.getOrgCollectInfo(org, branchChild, latch);
            }
            // 等待所有线程与完毕
            try {
                log.debug("等待前面执行完毕!");
                latch.await();
                log.debug("前面已经执行完毕!");
            } catch (InterruptedException e) {
                log.error("程序出现异常: ", e);
                Thread.currentThread().interrupt();
            }
            if (baseList.size() < PageSize) {
                log.debug("当前数据已运行完毕");
                break;
            }
        }
    }



    /**
     * 开始分页
     *
     * @param list
     * @param pageNum  页码
     * @param pageSize 每页多少条数据
     * @return
     */
    public <T> List<T> startPage(List<T> list, Integer pageNum, Integer pageSize) {
        if(list == null){
            return null;
        }
        if(list.size() == 0){
            return null;
        }

        Integer count = list.size(); //记录总数
        Integer pageCount = 0; //页数
        if (count % pageSize == 0) {
            pageCount = count / pageSize;
        } else {
            pageCount = count / pageSize + 1;
        }

        int fromIndex = 0; //开始索引
        int toIndex = 0; //结束索引

        if(pageNum > pageCount){
            pageNum = pageCount;
        }
        if (!pageNum.equals(pageCount)) {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = fromIndex + pageSize;
        } else {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = count;
        }

        List pageList = list.subList(fromIndex, toIndex);

        return pageList;
    }

}
