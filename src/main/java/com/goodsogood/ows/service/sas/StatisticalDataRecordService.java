package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.mapper.sas.StatisticalDataRecordMapper;
import com.goodsogood.ows.model.db.sas.StatisticalDataRecordEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2019-04-22 16:06
 **/
@Service
@Log4j2
public class StatisticalDataRecordService {

    private final StatisticalDataRecordMapper statisticalDataRecordMapper;

    @Autowired
    public StatisticalDataRecordService(StatisticalDataRecordMapper statisticalDataRecordMapper) {
        this.statisticalDataRecordMapper = statisticalDataRecordMapper;
    }

    public void insert(StatisticalDataRecordEntity entity){
        this.statisticalDataRecordMapper.insertSelective(entity);
    }

}