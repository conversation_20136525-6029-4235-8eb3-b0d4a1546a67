package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.sas.*;
import com.goodsogood.ows.model.vo.user.OrgUserPeriodForm;
import com.goodsogood.ows.service.ppmd.PpmdReportService;
import com.goodsogood.ows.service.user.OrgPeriodService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Log4j2
public class AsyncWorkRemindService {

    private final StringRedisTemplate redisTemplate;
    private final UserCenterService userCenterService;
    private final MonthCollectService monthCollectService;
    private final OrgPeriodService orgPeriodService;
    private final PpmdReportService ppmdReportService;
    private final OrgService orgService;

    @Autowired
    public AsyncWorkRemindService(StringRedisTemplate redisTemplate, UserCenterService userCenterService,
                                  MonthCollectService monthCollectService, OrgPeriodService orgPeriodService,
                                  PpmdReportService ppmdReportService, OrgService orgService) {
        this.redisTemplate = redisTemplate;
        this.userCenterService = userCenterService;
        this.monthCollectService = monthCollectService;
        this.orgPeriodService = orgPeriodService;
        this.ppmdReportService = ppmdReportService;
        this.orgService = orgService;
    }

    /**
     * 获取组织《20号工作提醒》推送数据
     *
     * @param org
     * @return
     */
    @Async("workReportExecutor")
    public void getOrgCollectInfo(OrganizationBase org, List<Integer> branchChild, CountDownLatch latch){
        // 高级管理员
        List<UserInfoBase> seniorAdministrator = this.userCenterService.findManagerByWhere(org.getOrgId(),
                Constants.ROLE_TYPE_DEFINED_USER);
        // 一般管理员
        List<UserInfoBase> generalAdministrator = this.userCenterService.findManagerByWhere(org.getOrgId(),
                Constants.ROLE_TYPE_DEFINED_ROOT);
        // 获取组织推送数据
        PushWorkRemindVo remindInfo = this.getOrgRemindInfo(org, branchChild);
        if (branchChild.contains(org.getOrgTypeChild())) {
            List<OrgUserPeriodForm> orgUserPeriod = this.orgPeriodService.findOrgUserPeriod(org.getOrgId(), 2);
            Set<Long> userSet = generalAdministrator.stream().map(UserInfoBase::getUserId).collect(Collectors.toSet());
            Set<Long> periodUserSet = orgUserPeriod.stream().map(OrgUserPeriodForm::getUserId).collect(Collectors.toSet());
            userSet.addAll(periodUserSet);
            // 如果是党支部
            if (!CollectionUtils.isEmpty(userSet)) {
                userSet.forEach(userId -> {
                    remindInfo.setUserId(userId);
                    this.saveRedis(remindInfo,2);
                });
            }
        } else {
            List<OrgUserPeriodForm> orgUserPeriod = this.orgPeriodService.findOrgUserPeriod(org.getOrgId(), 1);
            Set<Long> userSet = seniorAdministrator.stream().map(UserInfoBase::getUserId).collect(Collectors.toSet());
            Set<Long> periodUserSet = orgUserPeriod.stream().map(OrgUserPeriodForm::getUserId).collect(Collectors.toSet());
            userSet.addAll(periodUserSet);
            if (!CollectionUtils.isEmpty(userSet)) {
                userSet.forEach(userId -> {
                    remindInfo.setUserId(userId);
                    this.saveRedis(remindInfo,1);
                });
            }
        }
        latch.countDown();
    }

    public PushWorkRemindVo getOrgRemindInfo(OrganizationBase org, List<Integer> branchChild){
        PushWorkRemindVo remindVo = new PushWorkRemindVo();
        remindVo.setDate(this.getMonth(0));
        // 写入区县ID
        remindVo.setRegionId(org.getRegionId());
        remindVo.setOrgId(org.getOrgId());
        // 写入组织简称
        remindVo.setShortName(
                StringUtils.isNotBlank(
                        org.getShortName())? org.getShortName() : org.getOrgName());
        if (branchChild.contains(org.getOrgTypeChild())) {
            // 如果是党支部
            this.getBranchChildRemindInfo(org.getOrgId(), remindVo);
        } else {
            this.getParentOrgRemindInfo(org.getOrgId(), remindVo, branchChild);
        }
        return remindVo;
    }

    public void getParentOrgRemindInfo(Long orgId, PushWorkRemindVo remindVo, List<Integer> branchChild) {
        log.debug("获取党委组织 -> [{}] 本月工作提醒！", orgId);
        // 查询下属考核支部信息
        List<OrganizationBase> evalOrgById = this.monthCollectService.getEvalOrgById(orgId);
        // 查询未交齐党费支部数量
        AtomicInteger oweNum = new AtomicInteger(0);
        if (!CollectionUtils.isEmpty(evalOrgById)) {
            evalOrgById.forEach(org -> {
                if (branchChild.contains(org.getOrgTypeChild())) {
                    List<StatsResultVo> statsList = this.monthCollectService.getOrgPPMDStats(org.getOrgId(), remindVo.getDate(), 2);
                    if (!CollectionUtils.isEmpty(statsList)) {
                        StatsResultVo state = statsList.get(0);
                        if (!state.getNumOwing().equals(0)) {
                            oweNum.getAndIncrement();
                        }
                    }
                }
            });
        }
        remindVo.setOweNum(oweNum.get());
        // 查询组织生活情况
        List<OrgLiftRemindVo> remindList = this.monthCollectService.getOrgLiftRemind(orgId, remindVo.getDate());
        remindVo.setRemindList(remindList);
    }

    public void getBranchChildRemindInfo(Long orgId, PushWorkRemindVo remindVo) {
        log.debug("获取党支部组织 -> [{}] 本月工作提醒！", orgId);
        // 获取党费缴纳情况
        List<StatsResultVo> ppmdStats = this.monthCollectService.getOrgPPMDStats(orgId, remindVo.getDate(), 2);
        if (!CollectionUtils.isEmpty(ppmdStats)) {
            StatsResultVo resultVo = ppmdStats.get(0);
            remindVo.setOweNum(resultVo.getNumOwing());
        }
        //获取组织生活情况
        OrgLiftCollectVo orgLiftCollect = this.monthCollectService.getOrgLiftCollect(orgId, remindVo.getDate(), 2);
        remindVo.setUnFinishType(orgLiftCollect.getUnList());
    }

    /**
     * 获取月份
     * @param type 0-本月, 1-上月
     * @return
     */
    private String getMonth(int type) {
        LocalDate today = LocalDate.now();
        today = today.minusMonths(type);
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyy-MM");
        return formatters.format(today);
    }

    /**
     * 压入数据到redis列表中, 从右侧压入
     * @param type 1-高级管理员, 2-一般管理员
     */
    private void saveRedis(PushWorkRemindVo remindVo, int type){
        log.debug("接收到数据 -> [{}], type -> [{}]", remindVo, type);
        String s = JsonUtils.toJson(remindVo);
        String redisKey = null;
        switch (type) {
            case 1:
                redisKey = Constants.REDIS_KEY_PUSH_WORK_REMIND_SENIOR; break;
            case 2:
                redisKey = Constants.REDIS_KEY_PUSH_WORK_REMIND_GENERA; break;
            case 3:
                redisKey = Constants.REDIS_KEY_PUSH_WORK_REMIND_WORK_COMMIT; break;
            default:
                return;
        }
        log.debug("需要保存进Redis -> [{}]", redisKey);
        redisTemplate.opsForList().rightPush(redisKey, s);
        redisTemplate.expire(redisKey, 2, TimeUnit.DAYS);
    }

    public void workingCommitteeCollectInfo(Long orgId,Long regionId) {
        //查询工委管理员
        List<Long> workCommitAdmin = this.userCenterService.
                findWorkCommitManagerByWhere(orgId,Constants.ROLE_TYPE_Work_Committee);
        PushWorkRemindVo remindVo = new PushWorkRemindVo();
        remindVo.setListUserIds(workCommitAdmin);
        String date = DateUtils.toFormatDate(new Date(), "yyyy-M");
        // 拆分时间
        assert date != null;
        String[] strings = date.split("-");
        Integer year = Integer.valueOf(strings[0]);
        Integer month = Integer.valueOf(strings[1]);
        // 查询组织的组织生活情况
        List<EvalMobileDataCollectionForm> orgDataCollect = monthCollectService.getOrgDataCollect(orgId, year, month);
        List<OrgLiftRemindVo> remindList = new ArrayList<>();
        orgDataCollect.forEach(item->{
            OrgLiftRemindVo orgLiftRemindVo = new OrgLiftRemindVo();
            orgLiftRemindVo.setTypeName(item.getTypeName());
            //period 等于2时候 data 里面只有一条数据
            if(item.getPeriod()==2){
                orgLiftRemindVo.setOweNum(item.getData().get(0).getTotal());
                remindList.add(orgLiftRemindVo);
            }else {
                Optional<EvalMobileDataCollectionForm.Detail> first = item.getData().stream().filter(innerItem -> {
                    return innerItem.getMonth().equals(month);
                }).findFirst();
                if(first.isPresent()){
                    orgLiftRemindVo.setOweNum(first.get().getTotal());
                    remindList.add(orgLiftRemindVo);
                }
            }
        });
        remindVo.setOrgId(orgId);
        remindVo.setRemindList(remindList);
        remindVo.setRegionId(regionId);
        remindVo.setShortName(orgService.getById(orgId).getShortName());
        //查询党费交纳情况
        Integer ownOrgCount = ppmdReportService.getOwnOrgCount(regionId);
        remindVo.setOweNum(ownOrgCount);
        //单位组织届次到期未换届
        Integer notPeriodOrgNum = orgPeriodService.getNotPeriodOrg(orgId);
        remindVo.setPeriodNum(notPeriodOrgNum);
        remindVo.setDate(DateUtils.toFormatDate(new Date(), "yyyy-MM"));
        saveRedis(remindVo, 3);
    }
}
