package com.goodsogood.ows.service.sas;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.*;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.message.MessageForm;
import com.goodsogood.ows.model.vo.sas.OrgLiftRemindVo;
import com.goodsogood.ows.model.vo.sas.PushWorkRemindVo;
import com.goodsogood.ows.model.vo.sas.PushWorkReportVo;
import com.goodsogood.ows.model.vo.sas.WorkReportPushDataForm;
import com.goodsogood.ows.push.template.wechat.WorkNoticeTemplate;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.google.common.collect.Lists;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 消息推送服务层
 *
 * <AUTHOR>
 */
@Service
@Log4j2
public class PushDataService {

    private final PropertiesConfig propertiesConfig;
    private final RestTemplate restTemplate;
    private final TogServicesConfig togServicesConfig;
    private final StringRedisTemplate redisTemplate;
    private final WorkReportMessageConfig workReportMessageConfig;
    private final WorkRemindMessageConfig workRemindMessageConfig;
    private final WechatMeaasgeConfig wechatMeaasgeConfig;
    private final SimpleApplicationConfigHelper configHelper;
    private final RegionListConfig regionConfig;
    private final OrgService orgService;
    private final MeetingMapper meetingMapper;

    @Autowired
    public PushDataService(PropertiesConfig propertiesConfig,
                           RestTemplate restTemplate,
                           TogServicesConfig togServicesConfig,
                           StringRedisTemplate redisTemplate,
                           WorkReportMessageConfig workReportMessageConfig,
                           WorkRemindMessageConfig workRemindMessageConfig,
                           WechatMeaasgeConfig wechatMeaasgeConfig,
                           SimpleApplicationConfigHelper configHelper,
                           RegionListConfig regionConfig, OrgService orgService, MeetingMapper meetingMapper) {
        this.propertiesConfig = propertiesConfig;
        this.restTemplate = restTemplate;
        this.togServicesConfig = togServicesConfig;
        this.redisTemplate = redisTemplate;
        this.workReportMessageConfig = workReportMessageConfig;
        this.workRemindMessageConfig = workRemindMessageConfig;
        this.wechatMeaasgeConfig = wechatMeaasgeConfig;
        this.configHelper = configHelper;
        this.regionConfig = regionConfig;
        this.orgService = orgService;
        this.meetingMapper = meetingMapper;
    }

    /**
     * 每月1日工作报告发送主体方法
     */
    public void pushWorkReportData() {
        List<MessageForm> pushData = Lists.newArrayList();
        while (true) {
            PushWorkReportVo reportVo = this.reportPop();
            if (null == reportVo) {
                break;
            }
            if (reportVo.getPushType() == 1) {
                this.pushSenior(reportVo, pushData);
            } else if (reportVo.getPushType() == 2) {
                this.pushGenera(reportVo, pushData);
            } else if (reportVo.getPushType() == 3) {
                this.pushUser(reportVo, pushData);
            } else if (reportVo.getPushType() == 4) {
                this.pushWorkCommittee(reportVo, pushData);
            }
            if (pushData.size() >= 3000) {
                //调用消息中心推送接口
                this.pushToPushCenter(pushData, this.workReportMessageConfig.getTemplateId(), this.propertiesConfig.getWechatChannel());
                pushData.clear();
            }
        }

        if (!CollectionUtils.isEmpty(pushData)) {
            //调用消息中心推送接口
            this.pushToPushCenter(pushData, this.workReportMessageConfig.getTemplateId(), this.propertiesConfig.getWechatChannel());
        }

    }

    /**
     * 推送工作报告给工委管理员
     *
     * @param reportVo
     * @param pushData
     */
    private void pushWorkCommittee(PushWorkReportVo reportVo, List<MessageForm> pushData) {
        {
            WorkReportMessageConfig.LastMonthWorkCollect workCollect = this.workReportMessageConfig.getLastMonthWorkCollect();
            // 封装消息推送数据
            List<Long> listUserIds = reportVo.getListUserIds();
            MessageForm dataInfo = setMsgContent(workCollect, reportVo);
            listUserIds.forEach(item -> {
                MessageForm data = new MessageForm();
                BeanUtils.copyProperties(dataInfo, data);
                data.setUserId(item);
                pushData.add(data);
            });
        }
    }

    /**
     * 得到消息内容
     *
     * @param workCollect
     * @param reportVo
     * @return
     */
    private MessageForm setMsgContent(WorkReportMessageConfig.LastMonthWorkCollect workCollect, PushWorkReportVo reportVo) {
        MessageForm data = new MessageForm();
        StringBuilder content = new StringBuilder();
        content.append(String.format(workCollect.getOrgMemberBirthdayCollect(), reportVo.getBirthDayNum(), reportVo.getPoliticalNum()));
        content.append(String.format(workCollect.getOrgPeriodInfoCollect(), reportVo.getThisMonthPartyNum(), reportVo.getThisMonthPeriodNum()));
        data.setContent(content.toString());
        data.setContentColor(workCollect.getContentColor());
        //设置标题
        //data.setTitle(workCollect.getTitle());
        data.setTitle(reportVo.getShortName() + "工作报告");
        data.setTimeColor(workCollect.getTitleColor());
        // 本月时间
        data.setTime(reportVo.getDate());
        data.setTimeColor(workCollect.getTimeColor());
        StringBuilder remark = new StringBuilder();
        remark.append(" 上月报告:");
        remark.append(String.format(workCollect.getOrgRank(), reportVo.getFirstOrg()));
        data.setRemark(remark.toString());
        data.setRemarkColor(workCollect.getRemarkColor());
        data.setLinkUrl(this.getLinkUrl(reportVo.getOrgId(), reportVo.getRegionId(), workCollect.getLinkUrl()));
        data.setRegionId(reportVo.getRegionId());
        return data;
    }

    /**
     * 每月20日工作提醒发送主体方法
     */
    public void pushWorkRemindData() {
        List<MessageForm> pushData = Lists.newArrayList();
        while (true) {
            PushWorkRemindVo remindVo = this.remindPop();
            if (null == remindVo) {
                break;
            }
            if (remindVo.getPushType() == 1) {
                this.pushSeniorRemind(remindVo, pushData);
            } else if (remindVo.getPushType() == 2) {
                this.pushGeneraRemind(remindVo, pushData);
            } else if (remindVo.getPushType() == 3) {
                this.pushWorkCommittee(remindVo, pushData);
            }
            if (pushData.size() >= 3000) {
                //调用消息中心推送接口
                this.pushToPushCenter(pushData, this.workRemindMessageConfig.getTemplateId(), this.propertiesConfig.getWechatChannel());
                pushData.clear();
            }
        }

        if (!CollectionUtils.isEmpty(pushData)) {
            //调用消息中心推送接口
            this.pushToPushCenter(pushData, this.workRemindMessageConfig.getTemplateId(), this.propertiesConfig.getWechatChannel());
        }

    }


    /**
     * 推送工作提醒给工委管理员
     *
     * @param reportVo
     * @param pushData
     */
    private void pushWorkCommittee(PushWorkRemindVo reportVo, List<MessageForm> pushData) {
        {
            WorkRemindMessageConfig.WorkCommitteeWorkRemind workRemind = this.workRemindMessageConfig.getWorkCommitteeWorkRemind();
            // 封装消息推送数据
            List<Long> listUserIds = reportVo.getListUserIds();

            String date = DateUtils.toFormatDate(new Date(), "yyyy-M");
            // 拆分时间
            String[] strings = date.split("-");
            Integer year = Integer.valueOf(strings[0]);
            Integer month = Integer.valueOf(strings[1]);
            // 判断是否为季度月末
            boolean isEnd = false;
            if (month % 3 == 0) {
                isEnd = true;
            }
            MessageForm dataInfo = setMsgContent(reportVo, workRemind, isEnd);
            for (Long item : listUserIds) {
                MessageForm data = new MessageForm();
                BeanUtils.copyProperties(dataInfo, data);
                data.setUserId(item);
                pushData.add(data);
            }
        }
    }

    /**
     * 设置消息主体内容
     */
    private MessageForm setMsgContent(PushWorkRemindVo reportVo,
                                      WorkRemindMessageConfig.WorkCommitteeWorkRemind workRemind, boolean isEnd) {
        MessageForm data = new MessageForm();
        List<OrgLiftRemindVo> remindList = reportVo.getRemindList();
        StringBuilder content = new StringBuilder();
        content.append(String.format(workRemind.getPpmdCollect(), reportVo.getOweNum()));
        //content.append(String.format(workRemind.getOrgLiftCollect(), periodMeetingCount.get(), themeMeetingCount.get()));
        List<OrgLiftRemindVo> remindListCommon = remindList.stream().filter(itemInner ->
                        !itemInner.getTypeName().equals("党支部党员大会") && !itemInner.getTypeName().equals("党课")).
                sorted(Comparator.comparing(OrgLiftRemindVo::getTypeName)).collect(Collectors.toList());
        List<OrgLiftRemindVo> remindListSpecial = remindList.stream().filter(itemInner ->
                        itemInner.getTypeName().equals("党支部党员大会") || itemInner.getTypeName().equals("党课")).
                sorted(Comparator.comparing(OrgLiftRemindVo::getTypeName)).collect(Collectors.toList());
        //这个是季度最后一个月展示
        StringBuilder contentOrgLife = new StringBuilder();
        remindListCommon.forEach(itemCommon -> {
            if (itemCommon.getOweNum() != 0) {
                contentOrgLife.append("【").append(itemCommon.getTypeName()).append("】").
                        append(itemCommon.getOweNum()).append("个单位未完成,");
            }
        });
        if (isEnd) {
            remindListSpecial.forEach(itemCommon -> {
                if (itemCommon.getOweNum() != 0) {
                    contentOrgLife.append("【").append(itemCommon.getTypeName()).append("】").
                            append(itemCommon.getOweNum()).append("个单位未完成,");
                }
            });
        }
        //content.append(String.format(workRemind.getPeriodCollect(), reportVo.getPeriodNum()));
        if (!StringUtils.isEmpty(contentOrgLife.toString())) {
            String lastContent = content.toString() + "2.组织生活：" +
                    contentOrgLife.deleteCharAt(contentOrgLife.toString().lastIndexOf(",")).toString() + ";";
            data.setContent(lastContent);
        } else {
            data.setContent(content.toString());
        }
        data.setContentColor(workRemind.getContentColor());
        // 本月时间
        data.setTime(reportVo.getDate());
        data.setTimeColor(workRemind.getTimeColor());
        StringBuilder remark = new StringBuilder();
        remark.append(" 更多监督预警内容，请点击详情查询");
        data.setTitle(String.format(workRemind.getTitle(), reportVo.getShortName()));
        data.setTimeColor(workRemind.getTitleColor());
        data.setRemark(remark.toString());
        data.setRemarkColor(workRemind.getRemarkColor());
        data.setLinkUrl(this.getLinkUrl(reportVo.getOrgId(), reportVo.getRegionId(), workRemind.getLinkUrl()));
        data.setRegionId(reportVo.getRegionId());
        return data;
    }

    /**
     * 测试推送队列
     *
     * @param userIds
     * @param size
     */
    public void testPushReportData(List<Long> userIds, int size, int type) {
        List<MessageForm> pushData = Lists.newArrayList();
        Random random = new Random();
        for (int i = 0; i < size; i++) {
            String redisKey = null;
            PushWorkReportVo reportVo = null;
            if (type == 1) {
                redisKey = Constants.REDIS_KEY_PUSH_WORK_REPORT_SENIOR;
            } else if (type == 2) {
                redisKey = Constants.REDIS_KEY_PUSH_WORK_REPORT_GENERA;
            } else if (type == 3) {
                redisKey = Constants.REDIS_KEY_PUSH_WORK_REPORT_ALL;
            } else if (type == 4) {
                redisKey = Constants.REDIS_KEY_PUSH_WORK_REPORT_WORK_COMMIT;
            }

            if (StringUtils.isNotBlank(redisKey)) {
                reportVo = JsonUtils.fromJson(this.redisTemplate.opsForList().leftPop(redisKey), PushWorkReportVo.class);
            }
            if (null == reportVo) {
                break;
            }
            reportVo.setPushType(type);
            reportVo.setUserId(userIds.get(random.nextInt(userIds.size())));
            if (reportVo.getPushType() == 1) {
                this.pushSenior(reportVo, pushData);
            } else if (reportVo.getPushType() == 2) {
                this.pushGenera(reportVo, pushData);
            } else if (reportVo.getPushType() == 3) {
                this.pushUser(reportVo, pushData);
            } else if (reportVo.getPushType() == 4) {
                this.pushWorkCommittee(reportVo, pushData);
            }
        }
        this.pushToPushCenter(pushData, this.workReportMessageConfig.getTemplateId(), this.propertiesConfig.getWechatChannel());
    }

    /**
     * 测试推送队列
     *
     * @param userIds
     * @param size
     */
    public void testPushRemindData(List<Long> userIds, int size, int type) {
        List<MessageForm> pushData = Lists.newArrayList();
        Random random = new Random();
        for (int i = 0; i < size; i++) {
            String redisKey = null;
            PushWorkRemindVo remindVo = null;
            if (type == 1) {
                redisKey = Constants.REDIS_KEY_PUSH_WORK_REMIND_SENIOR;
            } else if (type == 2) {
                redisKey = Constants.REDIS_KEY_PUSH_WORK_REMIND_GENERA;
            } else if (type == 3) {
                redisKey = Constants.REDIS_KEY_PUSH_WORK_REMIND_WORK_COMMIT;
            }
            if (StringUtils.isNotBlank(redisKey)) {
                remindVo = JsonUtils.fromJson(this.redisTemplate.opsForList().leftPop(redisKey), PushWorkRemindVo.class);
            }
            if (null == remindVo) {
                break;
            }
            remindVo.setPushType(type);
            remindVo.setUserId(userIds.get(random.nextInt(userIds.size())));
            if (remindVo.getPushType() == 1) {
                this.pushSeniorRemind(remindVo, pushData);
            } else if (remindVo.getPushType() == 2) {
                this.pushGeneraRemind(remindVo, pushData);
            } else if (remindVo.getPushType() == 3) {
                this.pushWorkCommittee(remindVo, pushData);
            }
        }
        this.pushToPushCenter(pushData, this.workRemindMessageConfig.getTemplateId(), this.propertiesConfig.getWechatChannel());
    }


    /**
     * 测试消息发送
     *
     * @param reportVo
     */
    public void testPush(PushWorkReportVo reportVo) {
        List<MessageForm> pushData = Lists.newArrayList();
        if (reportVo.getPushType() == 1) {
            this.pushSenior(reportVo, pushData);
        } else if (reportVo.getPushType() == 2) {
            this.pushGenera(reportVo, pushData);
        } else if (reportVo.getPushType() == 3) {
            this.pushUser(reportVo, pushData);
        }
        //调用消息中心推送接口
        this.pushToPushCenter(pushData, this.workReportMessageConfig.getTemplateId(), this.propertiesConfig.getWechatChannel());
    }

    /**
     * 发送高级管理员工作报告
     *
     * @param reportVo
     * @param pushData
     */
    private void pushSenior(PushWorkReportVo reportVo, List<MessageForm> pushData) {
        MessageForm data = new MessageForm();

        // 封装消息推送数据
        WorkReportMessageConfig.SeniorWorkCollect seniorWorkCollect = this.workReportMessageConfig.getSeniorWorkCollect();
        data.setTitle(reportVo.getShortName());
        data.setTitleColor(seniorWorkCollect.getTitleColor());
        // 本月提醒内容
        String content = seniorWorkCollect.getContent();
        String contentWork = String.format(content, reportVo.getBirthDayNum(), reportVo.getPoliticalNum(), reportVo.getThisMonthPeriodNum());
        if (reportVo.getBirthDayNum() == 0 && reportVo.getPoliticalNum() == 0 && reportVo.getThisMonthPeriodNum() == 0) {
            contentWork = "暂无待办事项";
        }
        data.setContent(contentWork);
        data.setContentColor(seniorWorkCollect.getContentColor());
        // 本月时间
        data.setTime(reportVo.getDate());
        data.setTimeColor(seniorWorkCollect.getTimeColor());
        // 交纳排行
        // 备注
        StringBuffer sb = new StringBuffer();
        sb.append("\u2605 本单位上月");
        String orgRank = seniorWorkCollect.getOrgRank();
        String userRank = seniorWorkCollect.getUserRank();
        // sb.append(String.format("%s月", reportVo.getLastDate()));
        if (StringUtils.isNotBlank(reportVo.getFirstOrg())) {
            sb.append(String.format(orgRank, reportVo.getFirstOrg()));
        }
        if (StringUtils.isNotBlank(reportVo.getFirstOrg()) && StringUtils.isNotBlank(reportVo.getFirstUser())) {
            sb.append("，");
        }
        if (StringUtils.isNotBlank(reportVo.getFirstUser())) {
            sb.append(String.format(userRank, reportVo.getFirstUser()));
        }
        sb.append("。\r\n");
        /*sb.append(String.format(seniorWorkCollect.getPpmdCollect(), reportVo.getTotalMoney(),
                reportVo.getOweNum() == 0 ? "各支部党费均已交纳" : String.format("未交齐党费的支部有%s个", reportVo.getOweNum())));
        sb.append(String.format(seniorWorkCollect.getOrgLiftCollect(),
                (null == reportVo.getUnFinishNum() || reportVo.getUnFinishNum() == 0) ? "上月各支部均已开展" :
                        String.format("上月有%s个支部已开展，还有%s个支部未完成", reportVo.getFinishNum(), reportVo.getUnFinishNum())));
        if(reportVo.getBeforePeriodNum() != 0){
            sb.append(String.format(seniorWorkCollect.getOrgPeriodCollect(), reportVo.getBeforePeriodNum()));
        }*/
        data.setRemark(sb.toString());
        data.setRemarkColor(seniorWorkCollect.getRemarkColor());
        data.setUserId(reportVo.getUserId());
        data.setLinkUrl(this.getLinkUrl(reportVo.getOrgId(), reportVo.getRegionId(), seniorWorkCollect.getLinkUrl()));
        // 封装区区域ID
        data.setRegionId(reportVo.getRegionId());
        pushData.add(data);
    }

    /**
     * 发送一般管理员工作报告
     *
     * @param reportVo
     * @param pushData
     */
    private void pushGenera(PushWorkReportVo reportVo, List<MessageForm> pushData) {
        MessageForm data = new MessageForm();

        // 封装消息推送数据
        WorkReportMessageConfig.GeneraWorkCollect generaWorkCollect = this.workReportMessageConfig.getGeneraWorkCollect();
        data.setTitle(reportVo.getShortName());
        data.setTitleColor(generaWorkCollect.getTitleColor());
        StringBuffer content = new StringBuffer();
        // 本月提醒内容
        StringBuffer sb = new StringBuffer();
        int i = 1;
        if (!CollectionUtils.isEmpty(reportVo.getBirthPersonName())) {
            sb.append(String.format("本月自然生日的党员有【%s】", String.join("、", reportVo.getBirthPersonName())));
        }
        if (!CollectionUtils.isEmpty(reportVo.getPoliticalPersonName())) {
            sb.append(String.format("，政治生日的党员有【%s】", String.join("、", reportVo.getPoliticalPersonName())));
        }
        if (sb.length() != 0) {
            content.append(String.format("%s. ", i++));
            content.append(String.format(generaWorkCollect.getBirthCollect(), sb.toString()));
        }
        if (StringUtils.isNotBlank(reportVo.getPeriodNum())) {
            content.append(String.format("%s. ", i++));
            content.append(String.format(generaWorkCollect.getPeriodCollect(), reportVo.getPeriodNum().replace("-", "年")));
        }
        if (content.length() == 0) {
            content.append("暂无待办事项");
        }
        data.setContent(content.toString());
        data.setContentColor(generaWorkCollect.getContentColor());
        // 本月时间
        data.setTime(reportVo.getDate());
        data.setTimeColor(generaWorkCollect.getTimeColor());
        // 备注
        StringBuffer remark = new StringBuffer();
        remark.append("\u2605 本单位上月");
        String orgRank = generaWorkCollect.getOrgRank();
        String userRank = generaWorkCollect.getUserRank();
        if (StringUtils.isNotBlank(reportVo.getFirstOrg())) {
            remark.append(String.format(orgRank, reportVo.getFirstOrg()));
        }
        if (StringUtils.isNotBlank(reportVo.getFirstOrg()) && StringUtils.isNotBlank(reportVo.getFirstUser())) {
            remark.append("，");
        }
        if (StringUtils.isNotBlank(reportVo.getFirstUser())) {
            remark.append(String.format(userRank, reportVo.getFirstUser()));
        }
        remark.append("。\r\n");
        /*remark.append(String.format(generaWorkCollect.getPpmdCollect(), reportVo.getTotalMoney(),
                (null == reportVo.getOweName() ||reportVo.getOweName().size() == 0) ? "上月党费已交齐" :
                        String.format("还有%s未交纳", String.join(",", reportVo.getOweName()))));
        remark.append(String.format(generaWorkCollect.getOrgLiftCollect(),
                (null == reportVo.getUnFinishType() || reportVo.getUnFinishType().size() == 0) ? "上月所有支部组织生活均已开展" :
                String.format("还有%s", String.join("&nbsp;未完成 &nbsp;", reportVo.getUnFinishType()))));;*/
        data.setRemark(remark.toString());
        data.setRemarkColor(generaWorkCollect.getRemarkColor());
        data.setUserId(reportVo.getUserId());
        data.setLinkUrl(this.getLinkUrl(reportVo.getOrgId(), reportVo.getRegionId(), generaWorkCollect.getLinkUrl()));
        data.setRegionId(reportVo.getRegionId());

        pushData.add(data);
    }

    /**
     * 封装1号普通党员工作报告
     *
     * @param reportVo
     * @param pushData
     */
    private void pushUser(PushWorkReportVo reportVo, List<MessageForm> pushData) {
        MessageForm data = new MessageForm();
        // 封装消息推送数据
        WorkReportMessageConfig.UserWorkCollect userWorkCollect = this.workReportMessageConfig.getUserWorkCollect();
        // 标题
        data.setTitle(userWorkCollect.getTitle());
        data.setTitleColor(userWorkCollect.getTitleColor());
        // 提醒内容
        StringBuffer content = new StringBuffer();
        // 前言
        content.append(String.format(userWorkCollect.getPrefaceCollect(), reportVo.getUserName(), reportVo.getLastDate().replace("-", "年")));
        // 党费交纳
        Double totalMoney = reportVo.getTotalMoney();
        String ppmdCollect = userWorkCollect.getPpmdCollect();
        content.append(String.format(ppmdCollect, totalMoney));
        //List<StatisticalUserOrgLifeEntity> orgLiftList = reportVo.getOrgLiftList();
        int i = 1;
        // 支部活动
        Integer joinListNum = reportVo.getJoinListNum() == null ? 0 : reportVo.getJoinListNum();
        content.append("\r\n");
        content.append(String.format(userWorkCollect.getOrgLiftCollect(), ++i, joinListNum));
        // 学习积分
        Long studyScore = reportVo.getStudyScore() == null ? 0L : reportVo.getStudyScore();
        content.append("\r\n");
        content.append(String.format(userWorkCollect.getStudyCollect(), ++i, studyScore));

        // 积分变动
        Integer inScore = reportVo.getInScore() == null ? 0 : reportVo.getInScore();
        Integer outScore = reportVo.getOutScore() == null ? 0 : reportVo.getOutScore();
        content.append("\r\n");
        content.append(String.format(userWorkCollect.getCreditCollect(), ++i, inScore, outScore));

        data.setContent(content.toString());
        data.setContentColor(userWorkCollect.getContentColor());

        // 本月时间
        data.setTime(reportVo.getDate());
        data.setTimeColor(userWorkCollect.getTimeColor());

        StringBuffer remark = new StringBuffer();
        remark.append("\u2605 本单位上月");
        String orgRank = userWorkCollect.getOrgRank();
        String userRank = userWorkCollect.getUserRank();
        if (StringUtils.isNotBlank(reportVo.getFirstOrg())) {
            remark.append(String.format(orgRank, reportVo.getFirstOrg()));
        }
        if (StringUtils.isNotBlank(reportVo.getFirstOrg()) && StringUtils.isNotBlank(reportVo.getFirstUser())) {
            remark.append("，");
        }
        if (StringUtils.isNotBlank(reportVo.getFirstUser())) {
            remark.append(String.format(userRank, reportVo.getFirstUser()));
        }
        remark.append("。\r\n");

        data.setRemark(remark.toString());
        data.setRemarkColor(userWorkCollect.getRemarkColor());
        data.setUserId(reportVo.getUserId());
        data.setLinkUrl(this.getLinkUrl(reportVo.getOrgId(), reportVo.getRegionId(), userWorkCollect.getLinkUrl()));
        data.setRegionId(reportVo.getRegionId());

        pushData.add(data);
    }

    /**
     * 封装20号高级管理员工作提醒
     *
     * @param remindVo
     * @param pushData
     */
    private void pushSeniorRemind(PushWorkRemindVo remindVo, List<MessageForm> pushData) {
        MessageForm data = new MessageForm();
        WorkRemindMessageConfig.SeniorWorkRemind seniorWorkRemind = this.workRemindMessageConfig.getSeniorWorkRemind();
        // 头部数据
        data.setTitle(String.format(seniorWorkRemind.getTitle(), remindVo.getShortName()));
        data.setTitleColor(seniorWorkRemind.getTitleColor());
        // 提醒数据
        StringBuffer content = new StringBuffer();
        int i = 1;
        if (remindVo.getOweNum() != 0) {
            content.append(String.format(seniorWorkRemind.getPpmdCollect(), i++, remindVo.getOweNum()));
        }
        List<OrgLiftRemindVo> remindList = remindVo.getRemindList();
        if (!CollectionUtils.isEmpty(remindList)) {
            StringBuilder orgLift = new StringBuilder();
            for (int j = 0; j < remindList.size(); j++) {
                OrgLiftRemindVo orgLiftRemindVo = remindList.get(j);
                String str;
                if (!Objects.equals(orgLiftRemindVo.getTypeName(), "中心组学习")) {
                    str = String.format("【%s】%s个支部未完成%s",
                            orgLiftRemindVo.getTypeName(),
                            orgLiftRemindVo.getOweNum(),
                            j < (remindList.size() - 1) ? "，" : ";");
                } else {
                    str = String.format("【%s】%s个党委未完成。",
                            orgLiftRemindVo.getTypeName(),
                            orgLiftRemindVo.getOweNum());
                }
                orgLift.append(str);
            }
            content.append(String.format(seniorWorkRemind.getOrgLiftCollect(), i++, orgLift));
        }
        if (content.length() == 0) {
            content.append("暂无待办事项");
        }
        data.setContent(content.toString());
        data.setContentColor(seniorWorkRemind.getContentColor());
        // 时间
        data.setTime(remindVo.getDate());
        data.setTimeColor(seniorWorkRemind.getTimeColor());
        // 备注
        data.setRemark(seniorWorkRemind.getRemark());
        data.setRemarkColor(seniorWorkRemind.getRemarkColor());
        // 链接地址
        data.setLinkUrl(this.getLinkUrl(remindVo.getOrgId(), remindVo.getRegionId(), seniorWorkRemind.getLinkUrl()));
        // 发送人
        data.setUserId(remindVo.getUserId());
        // 封装区区域ID
        data.setRegionId(remindVo.getRegionId());

        pushData.add(data);
    }

    /**
     * 封装20号一般管理员工作提醒
     *
     * @param remindVo
     * @param pushData
     */
    private void pushGeneraRemind(PushWorkRemindVo remindVo, List<MessageForm> pushData) {
        MessageForm data = new MessageForm();
        WorkRemindMessageConfig.GeneraWorkRemind generaWorkRemind = this.workRemindMessageConfig.getGeneraWorkRemind();
        // 头部数据
        data.setTitle(String.format(generaWorkRemind.getTitle(), remindVo.getShortName()));
        data.setTitleColor(generaWorkRemind.getTitleColor());
        // 提醒数据
        StringBuffer content = new StringBuffer();
        int i = 1;
        if (null != remindVo.getOweNum() && remindVo.getOweNum() != 0) {
            content.append(String.format(generaWorkRemind.getPpmdCollect(), i++, remindVo.getOweNum()));
        }
        List<String> unFinishType = remindVo.getUnFinishType();
        if (!CollectionUtils.isEmpty(unFinishType)) {
            StringBuffer orgLift = new StringBuffer();
            for (int j = 0; j < unFinishType.size(); j++) {
                String str = String.format("【%s】未完成%s",
                        unFinishType.get(j),
                        j < (unFinishType.size() - 1) ? "，" : "");
                orgLift.append(str);
            }
            content.append(String.format(generaWorkRemind.getOrgLiftCollect(), i++, orgLift));
        }
        if (content.length() == 0) {
            content.append("暂无待办事项");
        }
        data.setContent(content.toString());
        data.setContentColor(generaWorkRemind.getContentColor());
        // 时间
        data.setTime(remindVo.getDate());
        data.setTimeColor(generaWorkRemind.getTimeColor());
        // 备注
        data.setRemark(generaWorkRemind.getRemark());
        data.setRemarkColor(generaWorkRemind.getRemarkColor());
        // 链接地址
        data.setLinkUrl(this.getLinkUrl(remindVo.getOrgId(), remindVo.getRegionId(), generaWorkRemind.getLinkUrl()));
        // 发送人
        data.setUserId(remindVo.getUserId());
        // 封装区区域ID
        data.setRegionId(remindVo.getRegionId());

        pushData.add(data);
    }


    /**
     * 向消息推送中心推送消息，如果大于配置的消息量就分批推送
     *
     * @param workReportList
     * @param templateId
     * @param channelType
     */
    private Integer pushToPushCenter(List<MessageForm> workReportList, Integer templateId, Integer channelType) {
        log.debug("向消息推送中心推送消息开始 templateId={} channelType={} size ={}", templateId, channelType, workReportList.size());
        Integer pushId = null;
        Integer pushSize = this.propertiesConfig.getPushSize();
        //按照区县分类
        List<Long> regionList = this.regionConfig.getRemind();
        for (Long regionId : regionList) {
            List<WorkNoticeTemplate.WorkNotice> workNoticeList = workReportList.stream().filter(messageForm -> messageForm.getRegionId().equals(regionId)).map(messageForm -> (WorkNoticeTemplate.WorkNotice) messageForm).collect(Collectors.toList());
            log.debug("向消息推送中心推送消息开始 regionId={} size ={}", regionId, workNoticeList.size());
            int size = workNoticeList.size();
            if (size > 0) {
                if (size > pushSize) {
                    //计算分批次数
                    int c = size % pushSize;
                    int s = size / pushSize;
                    int page = c != 0 ? (s + 1) : s;
                    //分批次发送
                    for (int i = 0; i < page; i++) {
                        int startIndex = i * pushSize;
                        int x = (i + 1) * pushSize;
                        int endIndex = x <= workNoticeList.size() ? x : size;
                        List<WorkNoticeTemplate.WorkNotice> pageList = workNoticeList.subList(startIndex, endIndex);
                        //推送
                        pushId = callPushCenter(pageList, pushId, templateId, channelType, regionId);
                        if (pushId == null) {
                            break;
                        }
                    }
                } else {
                    callPushCenter(workNoticeList, pushId, templateId, channelType, regionId);
                }
            }
            log.debug("向消息推送中心推送消息结束 regionId = {} templateId={} channelType={} size ={}", regionId, templateId, channelType, workReportList.size());
        }
        return pushId;
    }

    /**
     * 调用消息发送中心，发送信息
     *
     * @param data        发送消息
     * @param pushId      推送编号
     * @param templateId  模板编号
     * @param channelType 渠道类型  1:短信 2:微信
     * @param regionId    区县ID
     * @return
     */
    private Integer callPushCenter(List<WorkNoticeTemplate.WorkNotice> data, Integer pushId, Integer templateId, Integer channelType, Long regionId) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("_tk", "-1");
        headers.set("_region_id", String.valueOf(regionId));
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        // 封装数据
        WorkReportPushDataForm requestBody = new WorkReportPushDataForm();
        requestBody.setTemplateId(null == templateId ? null : templateId.longValue());
        requestBody.setChannelType(null == channelType ? null : channelType.byteValue());
        requestBody.setPushId(null == pushId ? null : pushId.longValue());
        requestBody.setSource(this.propertiesConfig.getSource());
        requestBody.setData(data);
        log.debug("推送数据 -> [{}]", requestBody);
        Integer re = null;
        int count = 0;
        do {
            try {
                re = RemoteApiHelper.post(this.restTemplate, String.format("http://%s/global/push/diff",
                        this.togServicesConfig.getPushCenter()), requestBody, headers, new TypeReference<Result<Integer>>() {
                });
            } catch (Exception e) {
                log.error("调用消息发送中心失败！ regionId = {} pushId ={} templateId ={} channelType={} 第{}次调用", regionId, pushId, templateId, channelType, (count + 1), e);
            }
            count++;
        } while (null == re && count < 5);
        log.debug("调用消息发送中心返回结果:查询 regionId = {} pushId ={} templateId ={} channelType={}  结果 re ={} 调用数{}次", regionId, pushId, templateId, channelType, re, count);
        return re;
    }

    /**
     * 1号工作报告，从redis列表中获取数据，从左侧弹出
     *
     * @return
     */
    private PushWorkReportVo reportPop() {
        Long seniorSize = this.redisTemplate.opsForList().size(Constants.REDIS_KEY_PUSH_WORK_REPORT_SENIOR);
        Long generaSize = this.redisTemplate.opsForList().size(Constants.REDIS_KEY_PUSH_WORK_REPORT_GENERA);
        Long allSize = this.redisTemplate.opsForList().size(Constants.REDIS_KEY_PUSH_WORK_REPORT_ALL);
        Long workCommittee = this.redisTemplate.opsForList().size(Constants.REDIS_KEY_PUSH_WORK_REPORT_WORK_COMMIT);
        String redisKey = null;
        PushWorkReportVo reportVo = null;
        Integer type = 0;
        if (seniorSize != 0) {
            redisKey = Constants.REDIS_KEY_PUSH_WORK_REPORT_SENIOR;
            type = 1;
        } else if (generaSize != 0) {
            redisKey = Constants.REDIS_KEY_PUSH_WORK_REPORT_GENERA;
            type = 2;
        } else if (allSize != 0) {
            redisKey = Constants.REDIS_KEY_PUSH_WORK_REPORT_ALL;
            type = 3;
        } else if (workCommittee != 0) {
            redisKey = Constants.REDIS_KEY_PUSH_WORK_REPORT_WORK_COMMIT;
            type = 4;
        }
        if (StringUtils.isNotBlank(redisKey)) {
            reportVo = JsonUtils.fromJson(this.redisTemplate.opsForList().leftPop(redisKey), PushWorkReportVo.class);
            reportVo.setPushType(type);
        }
        return reportVo;
    }

    /**
     * 20号工作提醒，从redis列表中获取数据，从左侧弹出
     *
     * @return
     */
    private PushWorkRemindVo remindPop() {
        Long seniorSize = this.redisTemplate.opsForList().size(Constants.REDIS_KEY_PUSH_WORK_REMIND_SENIOR);
        Long generaSize = this.redisTemplate.opsForList().size(Constants.REDIS_KEY_PUSH_WORK_REMIND_GENERA);
        Long workCommittee = this.redisTemplate.opsForList().size(Constants.REDIS_KEY_PUSH_WORK_REMIND_WORK_COMMIT);
        String redisKey = null;
        PushWorkRemindVo remindVo = null;
        Integer type = 0;
        if (seniorSize != 0) {
            redisKey = Constants.REDIS_KEY_PUSH_WORK_REMIND_SENIOR;
            type = 1;
        } else if (generaSize != 0) {
            redisKey = Constants.REDIS_KEY_PUSH_WORK_REMIND_GENERA;
            type = 2;
        } else if (workCommittee != 0) {
            redisKey = Constants.REDIS_KEY_PUSH_WORK_REMIND_WORK_COMMIT;
            type = 3;
        }
        if (StringUtils.isNotBlank(redisKey)) {
            remindVo = JsonUtils.fromJson(this.redisTemplate.opsForList().leftPop(redisKey), PushWorkRemindVo.class);
            remindVo.setPushType(type);
        }
        return remindVo;
    }

    public String getLinkUrl(Long oid, Long regionId, String redirectUrl) {
        Region.RegionData regionData = this.configHelper.getRegionByRegionId(regionId);
        boolean isSaaSVersion = regionData.wechatIsSaasVersion();
        String appId = regionData.getAppId();
        Long orgId = regionData.getOrgData().getOrgId();
        String linkUrl = "";
        if (isSaaSVersion) {
            String redirect = this.wechatMeaasgeConfig.getRedirect()
                    .replace("{oid}", orgId.toString())
                    .replace("{orgId}", oid.toString())
                    .replace("{loginType}", this.wechatMeaasgeConfig.getLoginType())
                    .replace("{regionId}", regionId.toString())
                    .replace("{appId}", appId)
                    .replace("{redirectUrl}", redirectUrl);
            // encode链接地址
            try {
                redirect = URLEncoder.encode(redirect, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                log.error("生成地址失败, encode失败 -> [{}]", e);
            }

            linkUrl = this.wechatMeaasgeConfig.getSaasUrl()
                    .replace("{projectName}", this.wechatMeaasgeConfig.getProjectName())
                    .replace("{appId}", appId)
                    .replace("{regionId}", regionId.toString())
                    .replace("{scope}", this.wechatMeaasgeConfig.getScope())
                    .replace("{version}", this.wechatMeaasgeConfig.getVersion())
                    .replace("{orgId}", orgId.toString())
                    .replace("{routerType}", this.wechatMeaasgeConfig.getRouterType())
                    .replace("{redirect}", redirect);
        } else {
            linkUrl = this.wechatMeaasgeConfig.getOldUrl()
                    .replace("{oid}", orgId.toString())
                    .replace("{orgId}", oid.toString())
                    .replace("{regionId}", regionId.toString())
                    .replace("{redirectUrl}", redirectUrl);
        }
        return linkUrl;
    }
}
