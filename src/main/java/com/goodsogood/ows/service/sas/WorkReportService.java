package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.PropertiesConfig;
import com.goodsogood.ows.configuration.RegionListConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.sas.*;
import com.goodsogood.ows.service.electronicreport.OrgReportService;
import com.goodsogood.ows.service.user.OrgService;
import com.google.common.collect.Lists;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * 拉取《1号工作报告》数据
 *
 * <AUTHOR>
 */
@Service
@Log4j2
public class WorkReportService {

    private static final int PageSize = 1000;

    private final MonthCollectService monthCollectService;
    private final UserCenterService userCenterService;
    private final StringRedisTemplate redisTemplate;
    private final OrgTypeConfig orgTypeConfig;
    private final PropertiesConfig propertiesConfig;
    private final OrgReportService orgReportService;
    private final OrgService orgService;
    private final AsyncWorkReportService asyncWorkReportService;
    private final SimpleApplicationConfigHelper configHelper;
    private final RegionListConfig regionListConfig;
    private final SimpleApplicationConfigHelper simpleApplicationConfigHelper;

    @Autowired
    public WorkReportService(MonthCollectService monthCollectService,
                             UserCenterService userCenterService,
                             StringRedisTemplate redisTemplate,
                             OrgTypeConfig orgTypeConfig,
                             PropertiesConfig propertiesConfig, OrgReportService orgReportService,
                             OrgService orgService, AsyncWorkReportService asyncWorkReportService,
                             SimpleApplicationConfigHelper configHelper, RegionListConfig regionListConfig,
                             SimpleApplicationConfigHelper simpleApplicationConfigHelper) {
        this.monthCollectService = monthCollectService;
        this.userCenterService = userCenterService;
        this.redisTemplate = redisTemplate;
        this.orgTypeConfig = orgTypeConfig;
        this.propertiesConfig = propertiesConfig;
        this.orgReportService = orgReportService;
        this.orgService = orgService;
        this.asyncWorkReportService = asyncWorkReportService;
        this.configHelper = configHelper;
        this.regionListConfig = regionListConfig;
        this.simpleApplicationConfigHelper = simpleApplicationConfigHelper;
    }

    /**
     * 拉取《1号工作报告》数据，保存进缓存
     */
    public void pullBeforeWorkReport() {
        // 清理Redis原数据
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REPORT_SENIOR);
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REPORT_GENERA);
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REPORT_ALL);
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REPORT_WORK_COMMIT);
        // 获取区县列表
        List<Long> remindList = this.regionListConfig.getRemind();
        for (Long regionId : remindList) {
            Region.OrgData orgData = this.configHelper.getOrgByRegionId(regionId);
            //获取考核组织列表
            List<OrganizationBase> evalOrg = this.monthCollectService.getEvalOrg(orgData.getOrgId());
            if (CollectionUtils.isEmpty(evalOrg)) {
                log.error("获取考核组织列表失败 -> [{}]", evalOrg);
                throw new ApiException("获取考核组织列表失败！");
            }
            // 获取党支部类型列表
            List<Integer> branchChild = this.orgTypeConfig.getBranchChild();
            this.generateWorkReport(evalOrg, branchChild,regionId);
        }
    }

    /**
     * 测试方法
     *
     * @param orgId
     * @param shortName
     * @param orgType
     * @param date
     * @param lastDate
     * @return
     */
    public PushWorkReportVo getOrgWordReportInfo(Long orgId, String shortName, Integer orgType, String date, String lastDate) {
        PushWorkReportVo reportVo = new PushWorkReportVo();
        reportVo.setDate(date);
        reportVo.setLastDate(lastDate);
        reportVo.setShortName(shortName);
        // 获取党支部
        List<Integer> branchChild = this.orgTypeConfig.getBranchChild();
        if (branchChild.contains(orgType)) {
            // 如果是党支部
            this.asyncWorkReportService.branchChildData(orgId, reportVo);
        } else {
            this.asyncWorkReportService.parentOrgData(orgId, reportVo);
        }
        return reportVo;
    }

    /**
     * 测试方法
     *
     * @param orgId
     * @param shortName
     * @param date
     * @param lastDate
     * @return
     */
    public PushWorkReportVo getUserReportInfo(Long orgId, Long userId, String userName, String shortName, String date, String lastDate) {
        PushWorkReportVo reportVo = new PushWorkReportVo();
        reportVo.setDate(date);
        reportVo.setLastDate(lastDate);
        reportVo.setShortName(shortName);
        List<UserInfoBase> userList = this.monthCollectService.getUserByOrgId(Lists.newArrayList(orgId));
        if (!CollectionUtils.isEmpty(userList)) {
            HashSet<UserInfoBase> userSet = new HashSet<>(userList);
            List<Long> idList = userSet.stream().map(UserInfoBase::getUserId).collect(Collectors.toList());
            // 查询党费详情
            List<UserPayInfoVo> payInfoList = this.monthCollectService.getPayInfoByUserId(idList, reportVo.getLastDate());
            OrganizationEntity org = this.orgService.getById(orgId);
            // 查询积分详情
            List<UserScoreInfoVo> scoreList = this.monthCollectService.getScoreInfoByUserId(org.getRegionId(), idList);
            // 获取积极缴纳党费情况
            this.asyncWorkReportService.getActivePayment(orgId, reportVo);

            this.asyncWorkReportService.getUserData(userId, userName, reportVo, payInfoList, scoreList);
        }
        return reportVo;
    }

    /**
     * 测试方法
     *
     * @param orgList
     * @return
     */
    public void testOrgReport(List<OrganizationBase> orgList) {
        // 清理Redis原数据
//        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REPORT_SENIOR);
//        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REPORT_GENERA);
//        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REPORT_ALL);
        // 获取党支部
        List<Integer> branchChild = this.orgTypeConfig.getBranchChild();
        //获取考核组织列表
        List<OrganizationBase> evalOrg = orgList;
        if (CollectionUtils.isEmpty(evalOrg)) {
            log.error("获取考核组织列表失败 -> [{}]", evalOrg);
            throw new ApiException("获取考核组织列表失败！");
        }
        this.generateWorkReport(evalOrg, branchChild,3L);
    }

    public void pullBeforeWorkReportByRegion(Long regionId) {
        // 清理Redis原数据
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REPORT_SENIOR);
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REPORT_GENERA);
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REPORT_ALL);
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REPORT_WORK_COMMIT);
        // 获取区县列表
        Region.RegionData region = configHelper.getRegionByRegionId(regionId);
        Long orgId = region.getOrgData().getOrgId();
            //获取考核组织列表
            List<OrganizationBase> evalOrg = this.monthCollectService.getEvalOrg(orgId);
            if (CollectionUtils.isEmpty(evalOrg)) {
                log.error("获取考核组织列表失败 -> [{}]", evalOrg);
                throw new ApiException("获取考核组织列表失败！");
            }
            // 获取党支部类型列表
            List<Integer> branchChild = this.orgTypeConfig.getBranchChild();
            this.generateWorkReport(evalOrg, branchChild,regionId);
    }

    /**
     * 只推送个人报告
     * @param orgIds
     */
    public void testUserReport(List<Long> orgIds){
        redisTemplate.delete(Constants.REDIS_KEY_PUSH_WORK_REPORT_ALL);
        List<OrganizationBase> orgList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orgIds)) {
            for (Long orgId : orgIds) {
                OrganizationEntity org = this.orgService.getById(orgId);
                OrganizationBase orgBase = new OrganizationBase();
                BeanUtils.copyProperties(org, orgBase);
                orgList.add(orgBase);
            }
        }
        if (CollectionUtils.isEmpty(orgList)) {
            // 获取区县列表
            Region regions = configHelper.getRegions();
            for (Region.RegionData region : regions.getRegions()) {
                Long orgId = region.getOrgData().getOrgId();
                //获取考核组织列表
                orgList = this.monthCollectService.getEvalOrg(orgId);
                if (CollectionUtils.isEmpty(orgList)) {
                    log.error("获取考核组织列表失败 -> [{}]", orgList);
                    throw new ApiException("获取考核组织列表失败！");
                }
            }
        }
        // 获取党支部类型列表
        List<Integer> branchChild = this.orgTypeConfig.getBranchChild();
        this.generateUserDate(orgList,  branchChild);
    }

    public void generateWorkReport(List<OrganizationBase> evalOrg, List<Integer> branchChild,Long regionId) {
        //上月工作报告（工委管理员）
        this.asyncWorkReportService.workingCommitteeCollectInfo(regionId);

        for (int k = 1; k < evalOrg.size(); k ++) {
            List<OrganizationBase> baseList = this.startPage(evalOrg, k, PageSize);
            CountDownLatch latch = new CountDownLatch(baseList.size());
            for (int i = 0; i < baseList.size(); i++) {
                OrganizationBase org = baseList.get(i);
                // 排除考核组织
                if (propertiesConfig.getExcludeOrg().contains(org.getOrgId())) {
                    log.debug("当前组织[{}]排除在外，不发报告。", org.getOrgId());
                    latch.countDown();
                    continue;
                }
                // 获取组织推送数据
                this.asyncWorkReportService.getOrgCollectInfo(org, branchChild, latch);
            }
            // 等待所有线程与完毕
            try{
                log.debug("等待前面执行完毕!");
                latch.await();
                log.debug("前面已经执行完毕!");
            } catch (InterruptedException e) {
                log.error("程序出现异常: ", e);
                Thread.currentThread().interrupt();
            }
            if (baseList.size() < PageSize) {
                log.debug("当前数据已运行完毕");
                break;
            }
        }
    }

    public void generateUserDate(List<OrganizationBase> evalOrg, List<Integer> branchChild) {

        for (int k = 1; k < evalOrg.size(); k ++) {
            List<OrganizationBase> baseList = this.startPage(evalOrg, k, PageSize);
            CountDownLatch latch = new CountDownLatch(baseList.size());
            for (int i = 0; i < baseList.size(); i++) {
                OrganizationBase org = baseList.get(i);
                // 排除考核组织
                if (propertiesConfig.getExcludeOrg().contains(org.getOrgId())) {
                    log.debug("当前组织[{}]排除在外，不发报告。", org.getOrgId());
                    latch.countDown();
                    continue;
                }
                // 获取组织推送数据
                this.asyncWorkReportService.userData(org, latch);
            }
            // 等待所有线程与完毕
            try {
                log.debug("等待前面执行完毕!");
                latch.await();
                log.debug("前面已经执行完毕!");
            } catch (InterruptedException e) {
                log.error("程序出现异常: ", e);
                Thread.currentThread().interrupt();
            }
            if (baseList.size() < PageSize) {
                log.debug("当前数据已运行完毕");
                break;
            }
        }
    }

    /**
     * 开始分页
     *
     * @param list
     * @param pageNum  页码
     * @param pageSize 每页多少条数据
     * @return
     */
    public <T> List<T> startPage(List<T> list, Integer pageNum, Integer pageSize) {
        if(list == null){
            return null;
        }
        if(list.size() == 0){
            return null;
        }

        Integer count = list.size(); //记录总数
        Integer pageCount = 0; //页数
        if (count % pageSize == 0) {
            pageCount = count / pageSize;
        } else {
            pageCount = count / pageSize + 1;
        }

        int fromIndex = 0; //开始索引
        int toIndex = 0; //结束索引

        if(pageNum > pageCount){
            pageNum = pageCount;
        }
        if (!pageNum.equals(pageCount)) {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = fromIndex + pageSize;
        } else {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = count;
        }

        List pageList = list.subList(fromIndex, toIndex);

        return pageList;
    }
}
