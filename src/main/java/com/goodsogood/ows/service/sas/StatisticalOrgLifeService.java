package com.goodsogood.ows.service.sas;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.mapper.sas.StatisticalOrgLifeMapper;
import com.goodsogood.ows.model.db.sas.StatisticalOrgLifeEntity;
import com.goodsogood.ows.model.vo.sas.DateResult;
import com.goodsogood.ows.model.vo.sas.ElectronicConditionForm;
import com.goodsogood.ows.model.vo.sas.ElectronicOrgLifeData;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.sas.SasOrgLifeConditionForm;
import com.goodsogood.ows.model.vo.sas.SqlParam;
import com.goodsogood.ows.model.vo.sas.StaMonth;
import com.goodsogood.ows.model.vo.sas.StasticReport;
import com.goodsogood.ows.utils.DateUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import lombok.extern.log4j.Log4j2;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @create 2019-04-22 15:57
 **/
@Service
@Log4j2
public class StatisticalOrgLifeService {

    private final Errors errors;
    private final StatisticalOrgLifeMapper statisticalOrgLifeMapper;
    private final StasticConfigInfoService configInfoService;
    private final MeetingMapper meetingMapper;

    @Autowired
    public StatisticalOrgLifeService(Errors errors,
                                     StatisticalOrgLifeMapper statisticalOrgLifeMapper,
                                     StasticConfigInfoService configInfoService,
                                     MeetingMapper meetingMapper) {
        this.errors = errors;
        this.statisticalOrgLifeMapper = statisticalOrgLifeMapper;
        this.configInfoService = configInfoService;
        this.meetingMapper = meetingMapper;
    }

    /**
     * 查询条件封装
     *
     * <AUTHOR>
     * @date 2019/4/23 20:03
     */

    public SasOrgLifeConditionForm buildParam(SasOrgLifeConditionForm form) {
        //查询类型
        //不按年查询，则查询时间不能为空
        DateResult result = new DateResult();
        if (form.getTimeType().equals(Constants.SELECT_TIME_TYPE_YEAR)) {
            result = DateUtils.getDate(form.getYear(), 1, 12);
            result.setMonthStr("1,2,3,4,5,6,7,8,9,10,11,12");
        } else if (form.getTimeType().equals(Constants.SELECT_TIME_TYPE_MONTH)) {
            if (null == form.getStartMonth() || null == form.getEndMonth()) {
                throw new ApiException("开始月份或结束月份不能为空", new Result(errors, 4708, HttpStatus.OK.value()));
            }
            result = DateUtils.getDate(form.getYear(), form.getStartMonth(), form.getEndMonth());
            String dateStr = "";
            for (int i = form.getStartMonth(); i <= form.getEndMonth(); i++) {
                dateStr += i + ",";
            }
            result.setMonthStr(dateStr.substring(0, dateStr.length() - 1));
        } else {
            if (null == form.getTime()) {
                throw new ApiException("查询时间不能为空", new Result(errors, 4707, HttpStatus.OK.value()));
            }
            //查询时间,1-上半年，2-下半年，3-第一季度，4-第二季度，5-第三季度，6-第四季度

            switch (form.getTime()) {
                case 1:
                    result = DateUtils.getDate(form.getYear(), 1, 6);
                    result.setMonthStr("1,2,3,4,5,6");
                    break;
                case 2:
                    result = DateUtils.getDate(form.getYear(), 7, 12);
                    result.setMonthStr("7,8,9,10,11,12");
                    break;
                case 3:
                    result = DateUtils.getDate(form.getYear(), 1, 3);
                    result.setMonthStr("1,2,3");
                    break;
                case 4:
                    result = DateUtils.getDate(form.getYear(), 4, 6);
                    result.setMonthStr("4,5,6");
                    break;
                case 5:
                    result = DateUtils.getDate(form.getYear(), 7, 9);
                    result.setMonthStr("7,8,9");
                    break;
                case 6:
                    result = DateUtils.getDate(form.getYear(), 10, 12);
                    result.setMonthStr("10,11,12");
                    break;
            }

        }
        form.setStartTime(result.getStartTime());
        form.setEndTime(result.getEndTime());
        form.setDateStr(result.getMonthStr());
        return form;
    }


    /**
     * 异步计算结果
     * @param entity
     * @param activityTypeIds
     * @param regionId
     */
    @Async("sasOrgFeeExecutor")
    public void synResult(StatisticalOrgLifeEntity entity,
                          String activityTypeIds, Long regionId,
                          String redisKey, ObjectMapper objectMapper,
                          StringRedisTemplate redisTemplate){
        if (StringUtils.isEmpty(entity.getActivityTypeIds())) {
            throw new ApiException("选择导出的活动类型不能为空", new Result(errors,
                    4709, HttpStatus.OK.value()));
        }
        //如果已经生成key,没有30分种内过期就返回不用重新计算
        if(redisTemplate.hasKey(redisKey)){
            return;
        }
        String[] activityId = activityTypeIds.split(",");
        List<Map<String,List<StatisticalOrgLifeEntity>>> allList = new ArrayList<>();
        for (String id : activityId) {
            Map<String,List<StatisticalOrgLifeEntity>> map = new HashMap<>();
            List<StatisticalOrgLifeEntity> dataset = new ArrayList<>();
            entity.setActivityTypeId(Integer.valueOf(id));
            dataset = exportOrgLifeList(regionId,entity);
            //查询组织生活统计信息
            StaMonth orgLifeCountList = orgLifeCount(entity);
            StatisticalOrgLifeEntity counLife = new StatisticalOrgLifeEntity();
            if (null != orgLifeCountList){
                BeanUtils.copyProperties(orgLifeCountList, counLife);
                dataset.add(0, counLife);
                //查询活动名称
                map.put(getActivityName(Integer.valueOf(id)), dataset);
            }
            allList.add(map);
        }
        if(CollectionUtils.isEmpty(allList)){
            return;
        }
        try {
            String content=objectMapper.writeValueAsString(allList);
            //设置key 过期时间为半个小时
            for (Map<String, List<StatisticalOrgLifeEntity>> stringListMap : allList) {
                redisTemplate.opsForList().leftPush(redisKey,
                        objectMapper.writeValueAsString(stringListMap));
            }
        } catch (Exception e) {
            log.error("synResult--展出组织生活设置key报错,ex-{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 查询组织生活列表
     *
     * <AUTHOR>
     * @date 2019/4/23 20:04
     */

    @Transactional
    public Page<StatisticalOrgLifeEntity> pageOrgLifeList(StatisticalOrgLifeEntity conditionForm,
                                                          Integer pageNo, Integer pageSize, HeaderHelper.SysHeader sysHeader) {
        conditionForm.setRegionId(sysHeader.getRegionId());
        conditionForm.setPartBranchType(statisticalOrgLifeMapper.getMeetingTypeId(3,sysHeader.getRegionId()));
        conditionForm.setPartGroupType(statisticalOrgLifeMapper.getMeetingTypeId(4,sysHeader.getRegionId()));
        Page<StatisticalOrgLifeEntity> pages = PageHelper.startPage(pageNo, pageSize)
            .doSelectPage(() -> statisticalOrgLifeMapper.findPageOrgLifepage(conditionForm));
        return pages;
    }


    public Integer updateMeetingStaInfo(Long orgLifeId, Long participateNum,
                                        String meetingStartTime,String meetingIds) {
      //meetingStartTime=  meetingMapper.getMeetingTime(meetingIds);
      return   statisticalOrgLifeMapper.updateMeetingStaInfo(orgLifeId, participateNum,
              meetingStartTime,meetingIds);
    }

    public Integer updateMeetingStaInfoByStatus(Long orgLifeId, Long participateNum) {
        return   statisticalOrgLifeMapper.updateMeetingStaInfoByStatus(orgLifeId, participateNum,2L);
    }

    /**
     * 新增统计设置信息
     *
     * <AUTHOR>
     * @date 2019/4/22 9:15
     */
    public List<StasticReport> getStaReportInfo() {
        return statisticalOrgLifeMapper.getStaReportInfo();
    }

    public Page<StasticReport> getStaReportInfoPage(Integer pageNo, Integer pageSize) {
        Page<StasticReport> page = PageHelper.startPage(pageNo, pageSize)
            .doSelectPage(() -> this.statisticalOrgLifeMapper.getStaReportInfo());
        return page;
    }

    /**
     * 统计总数
     *
     * <AUTHOR>
     * @date 2019/4/23 20:32
     */

    public StaMonth orgLifeCount(StatisticalOrgLifeEntity conditionForm) {
        StaMonth result = this.statisticalOrgLifeMapper.orgLifeCount(conditionForm);
        return result;
    }

    /**
     * sql拼装公用方法
     *
     * <AUTHOR>
     * @date 2019/4/25 9:28
     */

    public SqlParam buildSql(SasOrgLifeConditionForm form) {
        SqlParam sqlParam = new SqlParam();
        String dateStr = form.getDateStr();
        List<String> monthList = Arrays.asList(dateStr.split(","));
        StringBuffer totalParam = new StringBuffer();
        StringBuffer rowParam = new StringBuffer();
        StringBuffer payFeeParam = new StringBuffer();
        monthList.forEach(s -> {
            totalParam.append("`month" + s + "`+");
            rowParam.append("MAX(CASE WHEN `month` = " + s + " AND participate_num !=0  " +
                    "THEN participate_num ELSE 0 END ) as  `month" + s + "`,\n");
            payFeeParam.append("CONCAT(MAX(CASE WHEN `month` = " + s + " AND payable_person_num !=0 " +
                    " THEN payable_person_num ELSE 0 END),'-'," +
                    "MAX(CASE WHEN `month` = " + s + " AND unpaid_person_num !=0  " +
                    "THEN unpaid_person_num ELSE 0 END)) as `month" + s + "`,\n");
        });
        sqlParam.setTotalParam(totalParam.substring(0, totalParam.length() - 1));
        sqlParam.setRowParam(rowParam.substring(0, rowParam.length() - 2));
        sqlParam.setPayFeeParam(payFeeParam.substring(0, payFeeParam.length() - 2));
        return sqlParam;
    }

    /**
     * 导出组织生活
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/4/25 16:52
     */

    @Transactional
    public List<StatisticalOrgLifeEntity> exportOrgLifeList(Long regionId,StatisticalOrgLifeEntity conditionForm) {
        conditionForm.setPartBranchType(statisticalOrgLifeMapper.getMeetingTypeId(3,regionId));
        conditionForm.setPartGroupType(statisticalOrgLifeMapper.getMeetingTypeId(4,regionId));
        List<StatisticalOrgLifeEntity> orgLifeEntityList = statisticalOrgLifeMapper.findPageOrgLifepage(conditionForm);
        return orgLifeEntityList;
    }


    public String getOrgName(Long orgId,String tableName) {
        return   this.statisticalOrgLifeMapper.getOrgName(orgId,tableName);
    }

    public String getActivityName(Integer activityId) {
        return   this.statisticalOrgLifeMapper.getActivityName(activityId);
    }


    public int updateOrgRelationShip(StatisticalOrgLifeEntity statisticalOrgLifeEntity) {
        return   this.statisticalOrgLifeMapper.updateOrgRelationShip(statisticalOrgLifeEntity);
    }

    /**
     * 补录组织信息
     * @param createTime
     * @return
     */
    public int supplementOrgInfo( Date createTime,Long orgId,Integer periodSum,Long regionId) {
        return   this.statisticalOrgLifeMapper.supplementOrgInfo(createTime,orgId,periodSum,regionId);
    }



    /**
     * 电子报表统计服务
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/6/12 17:51
     */

    public List<ElectronicOrgLifeData> electronicSastic(ElectronicConditionForm conditionForm){
        List<ElectronicOrgLifeData> electronicOrgLifeData = new ArrayList<>();
        StatisticalOrgLifeEntity statisticalOrgLifeEntity = new StatisticalOrgLifeEntity();
        statisticalOrgLifeEntity.setOrgId(conditionForm.getOrgId());
        statisticalOrgLifeEntity.setStatisticalYear(conditionForm.getYear());
        statisticalOrgLifeEntity.setDateStr(conditionForm.getMonth());
        statisticalOrgLifeEntity.setPartBranchType(statisticalOrgLifeMapper.
                getMeetingTypeId(3,conditionForm.getRegionId()));
        statisticalOrgLifeEntity.setPartGroupType(statisticalOrgLifeMapper.
                getMeetingTypeId(4,conditionForm.getRegionId()));
        //当前配置的组织类型,是否需要过滤，默认需要过滤，其他访问如果不需要可设置未false
        if (conditionForm.getOrgTypeIdFlag()) {
            statisticalOrgLifeEntity.setShowOrgType(configInfoService.getConfig(Constants.
                    ORG_LIFE_TYPE,conditionForm.getRegionId()).getShowOrganizationTypes());
        }

        electronicOrgLifeData = this.statisticalOrgLifeMapper.selectOrgLifeData(statisticalOrgLifeEntity);
        return electronicOrgLifeData;

    }

    /**
     * 电子报表统计基本信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/6/13 9:06
     */

    public ElectronicOrgLifeData electronicBasicData(ElectronicConditionForm condition){
        StatisticalOrgLifeEntity statisticalOrgLifeEntity = new StatisticalOrgLifeEntity();
        statisticalOrgLifeEntity.setOrgId(condition.getOrgId());
        statisticalOrgLifeEntity.setDateStr(condition.getMonth());
        statisticalOrgLifeEntity.setStatisticalYear(condition.getYear());
        //当前配置的组织类型,是否需要过滤，默认需要过滤，其他访问如果不需要可设置未false
        if (condition.getOrgTypeIdFlag()) {
            statisticalOrgLifeEntity.setShowOrgType(configInfoService.getConfig(Constants.ORG_LIFE_TYPE,
                    condition.getRegionId()).getShowOrganizationTypes());
        }
        ElectronicOrgLifeData orgLifeData = this.statisticalOrgLifeMapper.selectOrgLifeBasicData(statisticalOrgLifeEntity);
        return orgLifeData;
    }


    /**
     * 根据组织id 和月份得到参与活动信息
     * @param orgId
     * @param months
     * @return
     */
    public Integer getActivityInfo(Long orgId,Integer activityId,List<String> months){
        Example example = new Example(StatisticalOrgLifeEntity.class);
        example.createCriteria().andEqualTo("orgId", orgId)
                .andEqualTo("activityTypeId", activityId)
                .andIn("statisticalDate", months);
        return this.statisticalOrgLifeMapper.selectCountByExample(example);
    }

}
