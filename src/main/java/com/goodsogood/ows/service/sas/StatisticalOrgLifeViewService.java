package com.goodsogood.ows.service.sas;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.bean.PageBean;
import com.goodsogood.ows.common.bean.TimeBean;
import com.goodsogood.ows.configuration.SasOrgLifeConfig;
import com.goodsogood.ows.mapper.sas.StatisticalOrgLifeMapper;
import com.goodsogood.ows.mapper.sas.StatisticalOrgLifeViewMapper;
import com.goodsogood.ows.model.db.sas.StatisticalOrgLifeViewEntity;
import com.goodsogood.ows.model.vo.sas.MeetingTypeForm;
import com.goodsogood.ows.model.vo.sas.SasOrgLifeForm;
import com.goodsogood.ows.model.vo.sas.StasticConfigInfoForm;
import com.goodsogood.ows.utils.ExclExportUtils;
import com.goodsogood.ows.utils.PageUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-07-23 15:15
 */
@Service
@Log4j2
public class StatisticalOrgLifeViewService {

    private final StatisticalOrgLifeViewMapper statisticalOrgLifeViewMapper;
    private final StasticConfigInfoService stasticConfigInfoService;
    private final SasOrgLifeConfig sasOrgLifeConfig;
    private final UserCenterService userCenterService;
    private final StatisticalOrgLifeMapper statisticalOrgLifeMapper;

    @Autowired
    public StatisticalOrgLifeViewService(
            StatisticalOrgLifeViewMapper statisticalOrgLifeViewMapper,
            StasticConfigInfoService stasticConfigInfoService,
            SasOrgLifeConfig sasOrgLifeConfig,
            UserCenterService userCenterService, StatisticalOrgLifeMapper statisticalOrgLifeMapper) {
        this.statisticalOrgLifeViewMapper = statisticalOrgLifeViewMapper;
        this.stasticConfigInfoService = stasticConfigInfoService;
        this.sasOrgLifeConfig = sasOrgLifeConfig;
        this.userCenterService = userCenterService;
        this.statisticalOrgLifeMapper = statisticalOrgLifeMapper;
    }

    /**
     * 查询党组织生活统计列表 查询配置中的所有类型
     *
     * @param timeBean 查询时间
     * @param pageBean 分页信息
     * @param regionId
     * @return SasOrgLifeForm
     */
    public SasOrgLifeForm sasOrgLifeView(TimeBean timeBean, PageBean pageBean, Long regionId) {
        // 统计看板配置信息
        List<StasticConfigInfoForm> stasticConfigInfoForms =
                stasticConfigInfoService.findConfigDetail(regionId);
        // 组织生活配置
        StasticConfigInfoForm orgLifeConf =
                stasticConfigInfoForms
                        .stream()
                        .filter(conf -> conf.getStatisticalType().equals(Constants.ORG_LIFE_TYPE))
                        .findFirst()
                        .orElse(null);
        // 组织生活统计类型
        List<MeetingTypeForm> orgLifeTypes = stasticConfigInfoService.findConfigDetailByOrgLife(orgLifeConf, null);
        // 组织生活统计类型添加双重组织生活
        orgLifeTypes.add(this.getDualOrgLifeTypeForm());
        Page<Map<String, Object>> page =
                PageHelper.startPage(pageBean.getPageNo(), pageBean.getPageSize())
                        .doSelectPage(
                                () -> this.statisticalOrgLifeViewMapper.findList(timeBean.getYear(), timeBean.getMonths(), orgLifeTypes,regionId));
        // 封装返回对象
        SasOrgLifeForm sasOrgLifeViewForm = new SasOrgLifeForm();
        sasOrgLifeViewForm.setTypes(orgLifeTypes);
        sasOrgLifeViewForm.setListDetail(page);
        return sasOrgLifeViewForm;
    }
    /**
     * 统计结果导出
     *
     * @param response HttpServletResponse
     * @param request  HttpServletRequest
     * @param timeBean 查询时间
     * @return boolean
     */
    public boolean export(HttpServletResponse response, HttpServletRequest request, TimeBean timeBean,Long regionId) {
        // 查询全量数据
        PageBean pageBean = PageUtils.page(1, sasOrgLifeConfig.getMaxSize());
        SasOrgLifeForm sasOrgLifeForm = sasOrgLifeView(timeBean, pageBean, regionId);
        String sheetName = bulidSheetName(timeBean);
        StatisticalOrgLifeViewExportService
                export = new StatisticalOrgLifeViewExportService(sheetName, sasOrgLifeForm);
        HSSFWorkbook hssfWorkbook = export.export03();
        return ExclExportUtils.export03(request, response, hssfWorkbook, sheetName);
    }

    /**
     * 获取表头
     *
     * @param timeBean 查询时间
     * @return String  表头
     */
    private String bulidSheetName(TimeBean timeBean) {
        StringBuilder sb = new StringBuilder();
        sb.append(timeBean.getYear()).append("年");
        if (timeBean.getTimeType() == TimeBean.TIME_TYPE_HALF_YEAR) {
            if (timeBean.getTime() == TimeBean.TIME_FIRST_HALF_YEAR) {
                sb.append("上半年");
            } else {
                sb.append("下半年");
            }
        } else if (timeBean.getTimeType() == TimeBean.TIME_TYPE_QUARTER) {
            if (timeBean.getTime() == TimeBean.TIME_FIRST_QUARTER) {
                sb.append("第一季度");
            } else if (timeBean.getTime() == TimeBean.TIME_SECOND_QUARTER) {
                sb.append("第二季度");
            } else if (timeBean.getTime() == TimeBean.TIME_THIRD_QUARTER) {
                sb.append("第三季度");
            } else {
                sb.append("第四季度");
            }
        } else if (timeBean.getTimeType() == TimeBean.TIME_TYPE_MONTH) {
            sb.append(timeBean.getTime()).append("月");
        }
        sb.append("组织生活一览表");
        return sb.toString();
    }

    /**
     * 双重组织生活
     */
    private MeetingTypeForm getDualOrgLifeTypeForm() {
        return MeetingTypeForm.builder()
                .typeId(StatisticalOrgLifeViewEntity.TYPE_DUAL_ORG_LIFE)
                .typeName(StatisticalOrgLifeViewEntity.TYPE_DUAL_ORG_LIFE_NAME)
                .build();
    }

}
