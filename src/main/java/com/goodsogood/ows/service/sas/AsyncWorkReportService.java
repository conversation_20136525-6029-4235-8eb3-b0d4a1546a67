package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.ppmd.PayRankVo;
import com.goodsogood.ows.model.vo.report.OrgReportForm;
import com.goodsogood.ows.model.vo.report.PersonElectronicReport;
import com.goodsogood.ows.model.vo.sas.*;
import com.goodsogood.ows.model.vo.user.OrgUserPeriodForm;
import com.goodsogood.ows.service.electronicreport.OrgReportService;
import com.goodsogood.ows.service.user.OrgPeriodService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AsyncWorkReportService {

    private final StringRedisTemplate redisTemplate;
    private final UserCenterService userCenterService;
    private final OrgReportService orgReportService;
    private final MonthCollectService monthCollectService;
    private final OrgPeriodService orgPeriodService;
    private final OrgService orgService;
    private final SimpleApplicationConfigHelper simpleApplicationConfigHelper;


    @Autowired
    public AsyncWorkReportService(StringRedisTemplate redisTemplate, UserCenterService userCenterService,
                                  OrgReportService orgReportService, MonthCollectService monthCollectService,
                                  OrgPeriodService orgPeriodService, OrgService orgService,
                                  SimpleApplicationConfigHelper simpleApplicationConfigHelper) {
        this.redisTemplate = redisTemplate;
        this.userCenterService = userCenterService;
        this.orgReportService = orgReportService;
        this.monthCollectService = monthCollectService;
        this.orgPeriodService = orgPeriodService;
        this.orgService = orgService;
        this.simpleApplicationConfigHelper = simpleApplicationConfigHelper;
    }

    /**
     * 获取组织《1号工作报告》推送数据
     *
     * @param org
     * @return
     */
    @Async("workReportExecutor")
    public void getOrgCollectInfo(OrganizationBase org, List<Integer> branchChild, CountDownLatch latch){
        // 高级管理员
        List<UserInfoBase> seniorAdministrator = this.userCenterService.findManagerByWhere(org.getOrgId(), Constants.ROLE_TYPE_DEFINED_USER);
        // 一般管理员
        List<UserInfoBase> generalAdministrator = this.userCenterService.findManagerByWhere(org.getOrgId(), Constants.ROLE_TYPE_DEFINED_ROOT);
        // 查询组织下所有党员
        List<UserInfoBase> userList = this.monthCollectService.getUserByOrgId(Lists.newArrayList(org.getOrgId()));

        PushWorkReportVo reportVo = new PushWorkReportVo();
        reportVo.setDate(this.getMonth(0));
        reportVo.setLastDate(this.getMonth(1));

        // 写入组织简称
        reportVo.setShortName(StringUtils.isNotBlank(org.getShortName())? org.getShortName() : org.getOrgName());
        // 写入区县ID
        reportVo.setRegionId(org.getRegionId());
        reportVo.setOrgId(org.getOrgId());
        if (branchChild.contains(org.getOrgTypeChild())) {
            // 如果是党支部
            this.branchChildData(org.getOrgId(), reportVo);
            List<OrgUserPeriodForm> orgUserPeriod = this.orgPeriodService.findOrgUserPeriod(org.getOrgId(), 2);
            Set<Long> userSet = generalAdministrator.stream().map(UserInfoBase::getUserId).collect(Collectors.toSet());
            Set<Long> periodUserSet = orgUserPeriod.stream().map(OrgUserPeriodForm::getUserId).collect(Collectors.toSet());
            userSet.addAll(periodUserSet);
            if (!CollectionUtils.isEmpty(userSet)) {
                userSet.forEach(userId -> {
                    reportVo.setUserId(userId);
                    this.saveRedis(reportVo,2);
                });
            }
        } else {
            this.parentOrgData(org.getOrgId(), reportVo);
            List<OrgUserPeriodForm> orgUserPeriod = this.orgPeriodService.findOrgUserPeriod(org.getOrgId(), 1);
            Set<Long> userSet = seniorAdministrator.stream().map(UserInfoBase::getUserId).collect(Collectors.toSet());
            Set<Long> periodUserSet = orgUserPeriod.stream().map(OrgUserPeriodForm::getUserId).collect(Collectors.toSet());
            userSet.addAll(periodUserSet);
            if (!CollectionUtils.isEmpty(userSet)) {
                userSet.forEach(userId -> {
                    reportVo.setUserId(userId);
                    this.saveRedis(reportVo,1);
                });
            }
        }
        // 生成组织党员数据
        if (!CollectionUtils.isEmpty(userList)) {
            HashSet<UserInfoBase> userSet = new HashSet<>(userList);
            List<Long> idList = userSet.stream().map(UserInfoBase::getUserId).collect(Collectors.toList());
            // 查询党费详情
            List<UserPayInfoVo> payInfoList = this.monthCollectService.getPayInfoByUserId(idList, reportVo.getLastDate());
            // 查询积分详情
            List<UserScoreInfoVo> scoreList = this.monthCollectService.getScoreInfoByUserId(org.getRegionId(), idList);
            // 获取积极缴纳党费情况
            this.getActivePayment(org.getOrgId(), reportVo);
            userList.forEach(user -> {
                this.getUserData(user.getUserId(), user.getUserName(), reportVo, payInfoList, scoreList);
                //保存到redis
                this.saveRedis(reportVo,3);
            });
        }

        latch.countDown();
    }

    /**
     * 获取党委《1号工作报告》推送数据
     *
     * @param orgId
     * @param reportVo
     */
    public void parentOrgData(Long orgId, PushWorkReportVo reportVo){
        // 获取本月生日的党员
        MemberResultForm memberResultForm = this.userCenterService.findMemberByDate(orgId);
        log.debug("获取本月生日数据 -> [{}]", memberResultForm);
        reportVo.setBirthDayNum(memberResultForm.getBirthdayList().size());
        reportVo.setPoliticalNum(memberResultForm.getPoliticalList().size());
        // 查询下属考核支部信息
        //List<OrganizationBase> evalOrgById = this.monthCollectService.getEvalOrgById(orgId);
        // 查询当前组织下本月是否要换届的组织
        List<OrgPeriodWarningDetailForm> expireOrg = this.userCenterService.findExpireApp(orgId, 1, reportVo.getRegionId());
        reportVo.setThisMonthPeriodNum(expireOrg.size());
        // 获取积极缴纳党费情况
        this.getActivePayment(orgId, reportVo);
        // 获取党费缴纳情况
        /*List<StatsResultVo> ppmdStats = this.monthCollectService.getOrgPPMDStats(orgId, reportVo.getLastDate(), 1);
        if (!CollectionUtils.isEmpty(ppmdStats)) {
            StatsResultVo resultVo = ppmdStats.get(0);
            reportVo.setTotalMoney(resultVo.getPayAlready());
            //查询未交齐党费支部数量
            AtomicInteger oweNum = new AtomicInteger(0);
            evalOrgById.forEach(org -> {
                if (this.orgTypeConfig.getBranchChild().contains(org.getOrgTypeChild())) {
                    List<StatsResultVo> statsList = this.monthCollectService.getOrgPPMDStats(org.getOrgId(), reportVo.getLastDate(), 2);
                    if (!CollectionUtils.isEmpty(statsList)) {
                        StatsResultVo state = statsList.get(0);
                        if (!state.getNumOwing().equals(0)) {
                            oweNum.getAndIncrement();
                        }
                    }
                }
            });
            reportVo.setOweNum(oweNum.get());
        }*/
        //获取组织生活情况
        /*OrgLiftCollectVo orgLiftCollect = this.monthCollectService.getOrgLiftCollect(orgId, reportVo.getLastDate(), 1);
        reportVo.setFinishNum(orgLiftCollect.getFinishNum());
        reportVo.setUnFinishNum(orgLiftCollect.getUnFinishNum());*/
        //查询上个月支部换届情况
        /*String orgIds = evalOrgById.stream().map(org -> org.getOrgId().toString()).collect(Collectors.joining(","));
        Integer count = StringUtils.isEmpty(orgIds) ? 0 : this.userCenterService.lastMonthChange(orgIds);
        reportVo.setBeforePeriodNum(count);*/
    }


    /**
     * 获取党支部《1号工作报告》推送数据
     *
     * @param orgId         组织ID
     * @param reportVo      实体类
     */
    public void branchChildData(Long orgId, PushWorkReportVo reportVo){
        // 获取本月生日的党员
        MemberResultForm memberResultForm = this.userCenterService.findMemberByDate(orgId);
        reportVo.setBirthPersonName(memberResultForm.getBirthdayList()
                .stream()
                .map(UserInfoBase::getName)
                .collect(Collectors.toList()));
        reportVo.setPoliticalPersonName(memberResultForm.getPoliticalList()
                .stream()
                .map(UserInfoBase::getName)
                .collect(Collectors.toList()));
        // 查询当前组织未来6个月是否要换届
        try {
            List<OrgPeriodWarningDetailForm> periodWarningDetailForms = this.userCenterService.findExpireApp(orgId, 2, reportVo.getRegionId());
            if (!CollectionUtils.isEmpty(periodWarningDetailForms)) {
                String expireDate = periodWarningDetailForms.get(0).getExpireDate();
                if (StringUtils.isNotBlank(expireDate)) {
                    reportVo.setPeriodNum(expireDate.substring(0, 7));
                }
            }
        } catch (Exception e) {
            log.error("获取[{}]组织换届数据失败", orgId, e);
        }
        // 获取积极缴纳党费情况
        this.getActivePayment(orgId, reportVo);
        // 获取积极缴纳党费情况
        // this.getActivePayment(orgId, reportVo);
        // 获取党费缴纳情况
        /*List<StatsResultVo> ppmdStats = this.monthCollectService.getOrgPPMDStats(orgId, reportVo.getLastDate(), 2);
        if (!CollectionUtils.isEmpty(ppmdStats)) {
            StatsResultVo resultVo = ppmdStats.get(0);
            reportVo.setTotalMoney(resultVo.getPayAlready());
            if (!resultVo.getNumOwing().equals(0)) {
                List<PayDetailsAppVo> ppmdDetail = this.monthCollectService.getBranchPPMDDetail(orgId, reportVo.getLastDate(), "2,4,5");
                reportVo.setOweName(ppmdDetail.stream().map(PayDetailsAppVo::getUserName).collect(Collectors.toList()));
            }
        }*/
        //获取组织生活情况
        /*OrgLiftCollectVo orgLiftCollect = this.monthCollectService.getOrgLiftCollect(orgId, reportVo.getLastDate(), 2);
        reportVo.setUnFinishType(orgLiftCollect.getUnList());*/
    }

    /**
     * 获取单个人员《1号工作报告》推送数据
     * @param userId
     * @param userName
     * @param reportVo
     * @param payInfoList
     * @param scoreList
     */
    public void getUserData(Long userId, String userName, PushWorkReportVo reportVo, List<UserPayInfoVo> payInfoList, List<UserScoreInfoVo> scoreList){

        List<UserPayInfoVo> payInfoVoList = payInfoList.stream().filter(
                userPayInfoVo -> userPayInfoVo.getUserId().equals(userId)).collect(Collectors.toList());

        List<UserScoreInfoVo> scoreInfoVoList = scoreList.stream().filter(
                userScoreInfoVo -> userScoreInfoVo.getUserId().equals(userId)).collect(Collectors.toList());

        reportVo.setUserId(userId);
        reportVo.setUserName(userName);
        reportVo.setTotalMoney((null != payInfoVoList && payInfoVoList.size() > 0) ?
                (payInfoVoList.get(0).getType() == 1 ? (null == payInfoVoList.get(0).getMoney() ? 0 : payInfoVoList.get(0).getMoney()) : 0) : 0);
        UserScoreInfoVo scoreInfo = (null != scoreInfoVoList && scoreInfoVoList.size() > 0) ?
                scoreInfoVoList.get(0) : new UserScoreInfoVo(userId,0,0);
        reportVo.setInScore(scoreInfo.getInScore());
        reportVo.setOutScore(scoreInfo.getOutScore());
        // 查询人员学习情况和组织生活情况
        PersonElectronicReport userStaInfo = new PersonElectronicReport();
        //得到区县Id ligy-7
        OrganizationEntity orgInfo = orgService.getById(reportVo.getOrgId());
        try {
            userStaInfo = this.monthCollectService.getUserStaInfo(orgInfo.getRegionId(),userId, reportVo.getLastDate());
            reportVo.setStudyScore(userStaInfo.getStudyInfo().getDetail().getReceiveScoreNum());
            reportVo.setJoinListNum(userStaInfo.getMeetingJoinInfo().getJoinNum());
        } catch (NoSuchElementException e) {
            log.error("查询人员学习和组织情况", e);
        }
        // 查询组织生活情况
        /*List<StatisticalUserOrgLifeEntity> orgLiftList =
                this.userOrgLifeService.getOrgLiftByUserId(userId, reportVo.getLastDate());
        reportVo.setOrgLiftList(orgLiftList);*/
    }

    /**
     * 获取党员《1号工作报告》推送数据
     * @param org
     */
    @Async("workReportUserExecutor")
    public void userData(OrganizationBase org, CountDownLatch latch){
        PushWorkReportVo reportVo = new PushWorkReportVo();
        reportVo.setDate(this.getMonth(0));
        reportVo.setLastDate(this.getMonth(1));
        reportVo.setShortName(org.getShortName());
        // 查询组织下所有党员
        List<UserInfoBase> userList = this.monthCollectService.getUserByOrgId(Lists.newArrayList(org.getOrgId()));
        if (!CollectionUtils.isEmpty(userList)) {
            HashSet<UserInfoBase> userSet = new HashSet<>(userList);
            List<Long> idList = userSet.stream().map(UserInfoBase::getUserId).collect(Collectors.toList());
            // 查询党费详情
            List<UserPayInfoVo> payInfoList = this.monthCollectService.getPayInfoByUserId(idList, reportVo.getLastDate());
            // 查询积分详情
            List<UserScoreInfoVo> scoreList = this.monthCollectService.getScoreInfoByUserId(org.getRegionId(), idList);
            // 获取积极缴纳党费情况
            this.getActivePayment(org.getOrgId(), reportVo);
            userList.forEach(user -> {
                this.getUserData(user.getUserId(), user.getUserName(), reportVo, payInfoList, scoreList);
                //保存到redis
                this.saveRedis(reportVo,3);
            });
        }
        latch.countDown();
    }

    /**
     * 获取积极缴纳党费情况
     * @return
     */
    public void getActivePayment(Long orgId, PushWorkReportVo reportVo){
        // 从电子党务报告里面获取党费排行信息
        OrgReportForm orgReportInfo = this.orgReportService.getOrgReportInfo(orgId, reportVo.getLastDate(), reportVo.getLastDate());
        if (null != orgReportInfo) {
            String orgName = orgReportInfo.getPpmdReport().getFirstOrg();
            if (StringUtils.isNotBlank(orgName) && !(orgName.equals("null"))) {
                reportVo.setFirstOrg(orgName);
            }
            String userName = orgReportInfo.getPpmdReport().getFirstUser();
            if (StringUtils.isNotBlank(userName) && !(userName.equals("null"))) {
                reportVo.setFirstUser(userName);
            }
        }
    }

    /**
     * 获取月份
     * @param type 0-本月, 1-上月
     * @return
     */
    private String getMonth(int type) {
        LocalDate today = LocalDate.now();
        today = today.minusMonths(type);
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyy-MM");
        return formatters.format(today);
    }

    /**
     * 压入数据到redis列表中, 从右侧压入
     * @param type 1-高级管理员, 2-一般管理员, 3-党员
     */
    public void saveRedis(PushWorkReportVo reportVo, int type){
        log.debug("接收到数据 -> [{}], type -> [{}]", reportVo, type);
        String s = JsonUtils.toJson(reportVo);
        String redisKey = null;
        switch (type) {
            case 1:
                redisKey = Constants.REDIS_KEY_PUSH_WORK_REPORT_SENIOR; break;
            case 2:
                redisKey = Constants.REDIS_KEY_PUSH_WORK_REPORT_GENERA; break;
            case 3:
                redisKey = Constants.REDIS_KEY_PUSH_WORK_REPORT_ALL; break;
            case 4:
                redisKey = Constants.REDIS_KEY_PUSH_WORK_REPORT_WORK_COMMIT; break;
            default:
                return;
        }
        log.debug("需要保存进Redis -> [{}]", redisKey);
        redisTemplate.opsForList().rightPush(redisKey, s);
        redisTemplate.expire(redisKey, 2, TimeUnit.DAYS);
    }


    /**
     * 工委报告收集信息
     * @param regionId
     */
    public void workingCommitteeCollectInfo(Long regionId) {
        Region.OrgData orgData = simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        Long orgId = orgData.getOrgId();
        //查询工委管理员
        List<Long> workCommitAdmin = this.userCenterService.
                findWorkCommitManagerByWhere(orgId,Constants.ROLE_TYPE_Work_Committee);
        PushWorkReportVo reportVo = new PushWorkReportVo();
        //本月换届的党委
        Integer thisMonthPartyChange = this.userCenterService.thisMonthPartyChange(orgId);
        reportVo.setThisMonthPartyNum(thisMonthPartyChange);
        reportVo.setRegionId(regionId);
        //本月换届的支部
        Integer thisMonthBranchChange = this.userCenterService.thisMonthBranchChange(orgId);
        reportVo.setThisMonthPeriodNum(thisMonthBranchChange);
        // 获取本月生日的党员
        MemberResultForm memberResultForm = this.userCenterService.findMemberByDate(orgId);
        log.debug("获取本月生日数据 -> [{}]", memberResultForm);
        reportVo.setBirthDayNum(memberResultForm.getBirthdayList().size());
        reportVo.setPoliticalNum(memberResultForm.getPoliticalList().size());
        reportVo.setShortName(orgService.getById(orgId).getShortName());
        //统计时间
        reportVo.setDate(DateUtils.toFormatDate(new Date(),"yyyy-MM"));
        reportVo.setPushType(4);
        reportVo.setOrgId(orgId);
        //上月第一个完成党费交纳的单位
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH,-1);
        String redisKey = Constants.REDIS_KEY_REPORT_PPMD_ACTIVE_PARTY_PAY + orgId+"_"+DateUtils.toFormatDate(c.getTime(),"yyyy-MM");
        String jsonStr = this.redisTemplate.opsForList().range(redisKey, 0L, -1L).toString();
        List<PayRankVo> listRank = (List<PayRankVo>) JsonUtils.fromJson(jsonStr, ArrayList.class, PayRankVo.class);
        assert listRank != null;
        Optional<PayRankVo> reduce = listRank.stream().reduce((s1, s2) -> s1.getFinishTime().compareTo(s2.getFinishTime())>0 ? s2 : s1);
        if(reduce.isPresent()) {
            String orgName = reduce.get().getOrgName();
            reportVo.setFirstOrg(orgName);

        }
        reportVo.setListUserIds(workCommitAdmin);
        saveRedis(reportVo,4);
    }

}
