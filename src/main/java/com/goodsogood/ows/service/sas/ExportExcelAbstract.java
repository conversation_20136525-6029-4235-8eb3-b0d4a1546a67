package com.goodsogood.ows.service.sas;


import com.goodsogood.ows.common.Utils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.scheduling.annotation.Async;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 统计结果导出接口
 *
 * <AUTHOR>
 * @create 2018-04-04 17:07
 **/
@Log4j2
public abstract class ExportExcelAbstract<T> {
    private final HSSFWorkbook workBook = new HSSFWorkbook();

    //创建样式 表头样式
    private HSSFCellStyle titleStyle;

    // 行样式
    private HSSFCellStyle colStyle;

    private HSSFSheet sheet;

    public HSSFSheet getSheet() {
        return sheet;
    }

    public static AtomicInteger counterInteger=new AtomicInteger(0);

    public ExportExcelAbstract(String header, String[] title, String sheetName) {
        // 设置默认样式
        setTitleStyle(null);
        setColStyle(null);
        //defaultSheet(header, title, sheetName);
    }

    public HSSFCellStyle getTitleStyle() {
        return titleStyle;
    }

    public void setTitleStyle(HSSFCellStyle titleStyle) {
        if (titleStyle == null) {
            //创建样式
            titleStyle = workBook.createCellStyle();
            //创建字体
            HSSFFont font = workBook.createFont();
            font.setFontHeightInPoints((short) 14);
            font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            titleStyle.setFont(font);
            //单元格垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            //设置背景
            titleStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.RED.getIndex());
        }
        this.titleStyle = titleStyle;
    }

    public HSSFCellStyle getColStyle() {
        return colStyle;
    }

    /**
     * 行格式
     *
     * @param colStyle HSSFCellStyle
     */
    private void setColStyle(HSSFCellStyle colStyle) {
        if (colStyle == null) {
            //创建样式
            colStyle = workBook.createCellStyle();
            //创建字体
            HSSFFont font = workBook.createFont();
            colStyle.setFont(font);
            //单元格垂直居中
            colStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            colStyle.setAlignment(HorizontalAlignment.CENTER);
        }
        this.colStyle = colStyle;
    }

    /**
     * 主要是用于满足基本统计数据结构的统计类型 生成导出统计的excl对象 03版本
     *
     * @param response HttpServletResponse
     */
    void export03(HttpServletResponse response, String fileName, List<Map<String,List<T>>> data, String dateStr,String[] rowsName,String sheetName,String title) throws Exception {
        try {
            createRows(data, dateStr,rowsName,sheetName,title);
            //设置头信息
            setHeader(response, fileName);
            // 使用响应的输出流导出excel文件
            workBook.write(response.getOutputStream());
        } catch (UnsupportedEncodingException e) {
            log.error("导出文件出错：不支持编码格式", e);
            throw e;
        } catch (IOException e) {
            log.error("导出文件出错：IO异常", e);
            throw e;
        } catch (Exception e) {
            log.error("导出文件出错", e);
            throw e;
        }
    }


    /**
     * 构建相同的excl表头
     *
     * @param header 统计标题
     * @param title  表头
     */
    private void defaultSheet(String header, String[] title, String sheetName) throws UnsupportedEncodingException {
        sheet = workBook.createSheet(sheetName);
        if (StringUtils.isNotBlank(header)) {
            //创建合并单元格对象
            CellRangeAddress rangeAddress = new CellRangeAddress(0, 0, 0, title.length - 1);
            //添加合并区域
            sheet.addMergedRegion(rangeAddress);
            //.创建行
            HSSFRow headerRow = sheet.createRow(0);
            //.创建单元格
            HSSFCell headerCell = headerRow.createCell(0);
            headerCell.setCellValue( URLDecoder.decode(header,"utf-8"));
            headerCell.setCellStyle(titleStyle);
        }
        if (title != null && title.length > 0) {
            //.创建title
            HSSFRow titleRow = sheet.createRow(1);
            for (int i = 0; i < title.length; i++) {
                HSSFCell cell = titleRow.createCell(i);
                cell.setCellValue(title[i]);
                cell.setCellStyle(titleStyle);
            }
        }
    }

    /**
     * 设置头信息
     *
     * @param response HttpServletResponse
     * @param fileName 文件名
     */
    private static void setHeader(HttpServletResponse response, String fileName) {
        try {
            // 设置响应头的文件名称信息
            String filename = URLEncoder.encode(fileName.replaceAll(" ", "") + ".xls", "utf-8");
            // 设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename="
                + new String(filename.getBytes(), StandardCharsets.UTF_8));
            // 设置响应头导出文件格式
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
        } catch (Exception e) {
            log.error("导出设置头信息出错", e);
        }
    }

    /**
     * 设置列的宽度； 256 的倍数
     *
     * @param sheet   HSSFSheet
     * @param colWith double[]
     */
    void setColumn(HSSFSheet sheet, double[] colWith) {
        // 设置列宽
        for (int k = 0; k < colWith.length; k++) {
            sheet.setColumnWidth((short) k, (short) colWith[k] * 256);
        }
    }

    /**
     * 添加数据
     *
     * @param data List<T>
     */
    private void createRows(List<Map<String,List<T>>> data, String dateStr,String[] rowsName,String sheetName,String title) throws Exception {
        if (data != null && !data.isEmpty()) {
            //异步写数据
            //一个活动名称对应一个列表
            for (Map<String, List<T>> map : data) {
                for (String key : map.keySet()) {
                    this.writeExcel(map.get(key),dateStr,rowsName,key,title);
                }
            }
        }
    }
    /**
     * 异步写数据
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/5/9 15:51
     */
    @Async("sasOrglifeExecutor")
    public void writeExcel(List<T> t, String dateStr,String[] rowsName,
                           String sheetName,String title) throws Exception {
        defaultSheet(title, rowsName, sheetName);
        otherSet();
        for (int i = 0; i < t.size(); i++) {
            createRow(sheet, t.get(i), dateStr, i);
        }
    }






    /**
     * 行数据
     *
     * @param t T
     */
    public abstract void createRow(HSSFSheet sheet, T t, String dateStr, int i) throws Exception;

    /**
     * 对excel进行其它设置
     */
    public abstract void otherSet();

}
