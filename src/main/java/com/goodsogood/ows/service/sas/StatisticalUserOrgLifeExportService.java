package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.model.vo.sas.MeetingTypeForm;
import com.goodsogood.ows.model.vo.sas.StraCellRangeCol;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 统计结果导出接口
 *
 * <AUTHOR>
 * @create 2018-04-04 17:07
 **/
@Log4j2
class StatisticalUserOrgLifeExportService {
    private final HSSFWorkbook workBook = new HSSFWorkbook();

    /**
     * 创建样式 表头样式
     */
    private HSSFCellStyle titleStyle;

    /**
     * 行样式
     */
    private HSSFCellStyle colStyle;

    private HSSFSheet sheet;


    /**
     * 导出类型
     */
    private final List<MeetingTypeForm> types;
    private final List<Map<String, Object>> data;
    List<String> months;

    StatisticalUserOrgLifeExportService(String sheetName, List<Map<String, Object>> data, List<String> months, List<MeetingTypeForm> types) {
        // 设置默认样式
        setTitleStyle();
        setColStyle();
        this.data = data;
        this.months = months;
        this.types = types;
        defaultSheet(sheetName, bulidTitle(), sheetName);
    }

    /**
     * 获取表头
     *
     * @return String  title
     */
    private String[] bulidTitle() {
        List<String> titles = new ArrayList<>();
        titles.add("序号");
        titles.add("组织名称");
        titles.addAll(types.stream().map(MeetingTypeForm::getTypeName).collect(Collectors.toList()));
        return titles.toArray(new String[0]);
    }


    public void setTitleStyle() {
        //创建样式
        titleStyle = workBook.createCellStyle();
        //创建字体
        HSSFFont font = workBook.createFont();
        font.setFontHeightInPoints((short) 14);
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        titleStyle.setFont(font);
        //单元格垂直居中
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //水平居中
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        //设置背景
        titleStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.RED.getIndex());
    }

    /**
     * 行格式
     */
    private void setColStyle() {
        //创建样式
        colStyle = workBook.createCellStyle();
        //创建字体
        HSSFFont font = workBook.createFont();
        colStyle.setFont(font);
        //单元格垂直居中
        colStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //水平居中
        colStyle.setAlignment(HorizontalAlignment.CENTER);
    }

    /**
     * 主要是用于满足基本统计数据结构的统计类型
     */
    public HSSFWorkbook export03() {
        createRows(data);
        return workBook;
    }

    /**
     * 构建相同的excl表头
     *
     * @param header 统计标题
     * @param title  表头
     */
    private void defaultSheet(String header, String[] title, String sheetName) {
        sheet = workBook.createSheet(sheetName);
        //创建字体
        HSSFFont font = workBook.createFont();
        font.setFontHeightInPoints((short) 12);
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        //普通样式
        HSSFCellStyle style = workBook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFont(font);
        //头部字体
        HSSFFont headFont = workBook.createFont();
        headFont.setFontHeightInPoints((short) 20);
        headFont.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        //表头样式
        HSSFCellStyle headStyle = workBook.createCellStyle();
        headStyle.setAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headStyle.setFont(headFont);
        //遍历的最后一行数据
        //处理第二数据
        // 用于处理合并请求数据
        handlerWorkbookFirst(sheet, style);
        // 用于处理合并请求数据
        List<StraCellRangeCol> listCellRang = new ArrayList<>();
        //处理第三列数据
        Integer traversingLastCol = handlerWorkbookSecondRow(sheet, style, listCellRang);
        //根据数据来合并表头信息
        handlerWorkbookHead(sheet, traversingLastCol, headStyle, sheetName);
        // 合并日期占两行(4个参数，分别为起始行，结束行，起始列，结束列)
        // 行和列都是从0开始计数，且起始结束都会合并
        CellRangeAddress region = new CellRangeAddress(1, 2, 0, 0);
        CellRangeAddress region1 = new CellRangeAddress(1, 2, 1, 1);
        CellRangeAddress region2 = new CellRangeAddress(1, 2, 2, 2);
        listCellRang.forEach(item -> {
            CellRangeAddress regionItem = new CellRangeAddress(1, 1, item.getFirstCol(), item.getLastCol());
            sheet.addMergedRegion(regionItem);
        });
        sheet.addMergedRegion(region);
        sheet.addMergedRegion(region1);
        sheet.addMergedRegion(region2);
        //设置列的宽度
        double[] colWidth = {12, 24, 30};
        setColumn(sheet, colWidth);
    }

    /**
     * 处理第三行数据
     */
    private Integer handlerWorkbookSecondRow(HSSFSheet sheet, HSSFCellStyle style, List<StraCellRangeCol> listCellRang) {
        //总那列开始计算
        int startCol = 3;
        HSSFRow row2 = sheet.createRow(2);
        //从第四行开始添加统计年月信息
        for (int i = 0; i < types.size(); i++) {
            StraCellRangeCol straCellRangeCol = new StraCellRangeCol();
            straCellRangeCol.setFirstCol(startCol);
            for (int j = 0; j < months.size() + 1; j++) {
                HSSFCell cell = row2.createCell(startCol);
                //如果最后一条数据
                cell.setCellStyle(style);
                if (j == months.size()) {
                    cell.setCellValue("小计");
                    straCellRangeCol.setLastCol(startCol);
                    listCellRang.add(straCellRangeCol);
                } else {
                    cell.setCellValue(months.get(j) + "月");
                }
                startCol++;
            }
        }
        return startCol;
    }

    /**
     * 处理表头信息
     *
     * @param sheet
     * @param traversingLastCol
     * @param style
     */
    private void handlerWorkbookHead(HSSFSheet sheet, Integer traversingLastCol, HSSFCellStyle style,
                                     String sheetName) {
        HSSFRow row0 = sheet.createRow(0);
        HSSFCell cell1 = row0.createCell(0);
        cell1.setCellStyle(style);
        cell1.setCellValue(sheetName);
        CellRangeAddress regionHeader = new CellRangeAddress(0, 0, 0, traversingLastCol);
        sheet.addMergedRegion(regionHeader);
    }

    /**
     * 处理第二行信息
     */
    private void handlerWorkbookFirst(HSSFSheet sheet, HSSFCellStyle style) {
        //第一行数据
        HSSFRow row1 = sheet.createRow(1);
        HSSFCell cell1 = row1.createCell(0);
        cell1.setCellStyle(style);
        cell1.setCellValue("序号");
        HSSFCell cell2 = row1.createCell(1);
        cell2.setCellStyle(style);
        cell2.setCellValue("党员姓名");
        HSSFCell cell3 = row1.createCell(2);
        cell3.setCellStyle(style);
        cell3.setCellValue("所在组织名称");
        //总那列开始计算
        int startCol = 3;

        //从第四行开始添加统计年月信息
        //总的要循环的次数
        for (MeetingTypeForm meetingTypeForm : types) {
            HSSFCell cell = row1.createCell(startCol);
            cell.setCellStyle(style);
            cell.setCellValue(meetingTypeForm.getTypeName());
            startCol = startCol + months.size() + 1;
        }
        HSSFCell cell = row1.createCell(startCol);
        cell.setCellStyle(style);
        cell.setCellValue("合计");
        CellRangeAddress regionLast = new CellRangeAddress(1, 2, startCol, startCol);
        sheet.addMergedRegion(regionLast);
    }

    /**
     * 设置列的宽度； 256 的倍数
     *
     * @param sheet
     * @param colWith
     */
    public void setColumn(HSSFSheet sheet, double[] colWith) {
        // 设置列宽
        for (int k = 0; k < colWith.length; k++) {
            sheet.setColumnWidth((short) k, (short) colWith[k] * 256);
        }
    }

    /**
     * 添加数据
     *
     * @param data List<T>
     */
    private void createRows(List<Map<String, Object>> data) {
        if (data != null && !data.isEmpty()) {
            // 序号
            int i = 1;
            for (Map<String, Object> t : data) {
                createRow(sheet, t, i);
                i++;
            }
        }
    }

    /**
     * 行数据
     *
     * @param data 行数据
     */
    private void createRow(HSSFSheet sheet, Map<String, Object> data, int i) {
        if (data != null) {
            // 新建行
            HSSFRow row = sheet.createRow(sheet.getLastRowNum() + 1);

            int cellNum = 0;
            // 序号
            HSSFCell ci = row.createCell(cellNum++);
            ci.setCellStyle(colStyle);
            ci.setCellValue(i);
            // 党员姓名
            HSSFCell c0 = row.createCell(cellNum++);
            c0.setCellStyle(colStyle);
            c0.setCellValue(data.get("user_name").toString());
            // 组织名称
            HSSFCell c1 = row.createCell(cellNum++);
            c1.setCellStyle(colStyle);
            c1.setCellValue(data.get("org_name").toString());
            // 统计项
            int totality = 0;
            for (MeetingTypeForm meetingTypeForm : types) {
                int total = 0;
                for (String m : months) {
                    int num = Integer.valueOf(data.get(meetingTypeForm.getTypeId() + "_" + m).toString());
                    total = total + num;
                    HSSFCell cell = row.createCell(cellNum++);
                    cell.setCellStyle(colStyle);
                    cell.setCellValue(num);
                }
                HSSFCell cellTotal = row.createCell(cellNum++);
                cellTotal.setCellStyle(colStyle);
                cellTotal.setCellValue(total);
                totality = totality + total;
            }
            HSSFCell cellTotality = row.createCell(cellNum);
            cellTotality.setCellStyle(colStyle);
            cellTotality.setCellValue(totality);
        }
    }

}
