package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.mapper.sas.StatisticalLeaderOrgLifeMapper;
import com.goodsogood.ows.mapper.sas.StatisticalOrgLifeMapper;
import com.goodsogood.ows.mapper.sas.StatisticalPartyFeeMapper;
import com.goodsogood.ows.model.vo.sas.EvalIdForm;
import com.goodsogood.ows.model.vo.sas.EvalIdRequestForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-07-23 17:05
 */
@Service
@Log4j2
public class EvalIdListService {

    private final Errors errors;
    private final StatisticalPartyFeeMapper statisticalPartyFeeMapper;
    private final StatisticalOrgLifeMapper statisticalOrgLifeMapper;
    private final StatisticalLeaderOrgLifeMapper statisticalLeaderOrgLifeMapper;

    @Autowired
    public EvalIdListService(
            Errors errors,
            StatisticalPartyFeeMapper statisticalPartyFeeMapper,
            StatisticalOrgLifeMapper statisticalOrgLifeMapper,
            StatisticalLeaderOrgLifeMapper statisticalLeaderOrgLifeMapper) {
        this.errors = errors;
        this.statisticalPartyFeeMapper = statisticalPartyFeeMapper;
        this.statisticalOrgLifeMapper = statisticalOrgLifeMapper;
        this.statisticalLeaderOrgLifeMapper = statisticalLeaderOrgLifeMapper;
    }

    public List<EvalIdForm> findList(EvalIdRequestForm form, Integer type) {
        switch (type) {
            case 1:
                return statisticalLeaderOrgLifeMapper.findLeaderList(form);
            case 7:
                return statisticalPartyFeeMapper.findOrgList(form);
            default:
                return statisticalOrgLifeMapper.findOrgList(form);
        }
    }
}
