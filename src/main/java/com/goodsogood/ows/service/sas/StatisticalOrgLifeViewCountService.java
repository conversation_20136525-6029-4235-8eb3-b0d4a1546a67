package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.config.ApplicationConfigHelper;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.configuration.SasOrgLifeConfig;
import com.goodsogood.ows.mapper.sas.StatisticalOrgLifeViewMapper;
import com.goodsogood.ows.model.db.sas.StatisticalOrgLifeViewEntity;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.sas.MeetingTypeForm;
import com.goodsogood.ows.model.vo.sas.StasticConfigInfoForm;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-07-23 15:15
 */
@Service
@Log4j2
public class StatisticalOrgLifeViewCountService {

    private final StatisticalOrgLifeViewMapper statisticalOrgLifeViewMapper;
    private final StasticConfigInfoService stasticConfigInfoService;
    private final SasOrgLifeConfig sasOrgLifeConfig;
    private final UserCenterService userCenterService;
    private final ApplicationConfigHelper applicationConfigHelper;

    @Value("${region-label}")
    private String label;

    @Autowired
    public StatisticalOrgLifeViewCountService(
            StatisticalOrgLifeViewMapper statisticalOrgLifeViewMapper,
            StasticConfigInfoService stasticConfigInfoService,
            SasOrgLifeConfig sasOrgLifeConfig,
            UserCenterService userCenterService, ApplicationConfigHelper applicationConfigHelper) {
        this.statisticalOrgLifeViewMapper = statisticalOrgLifeViewMapper;
        this.stasticConfigInfoService = stasticConfigInfoService;
        this.sasOrgLifeConfig = sasOrgLifeConfig;
        this.userCenterService = userCenterService;
        this.applicationConfigHelper = applicationConfigHelper;
    }


    /**
     * 党组生活一览 统计
     */
    @Transactional(rollbackFor = Exception.class)
    public void sasOrgLifeViewCount() {
        StopWatch stopWatch = new StopWatch("党组生活一览统计");
        stopWatch.start();
        // 删除历史统计
        statisticalOrgLifeViewMapper.deleteAll();
        //得到所有区县配置
        Region regions = applicationConfigHelper.getRegions(label);
        List<Region.RegionData> regionsList = regions.getRegions();
        regionsList.forEach(item->{
            // 统计看板配置信息
            List<StasticConfigInfoForm> stasticConfigInfoForms =
                    stasticConfigInfoService.findConfigDetail(item.getRegionId());
            if(CollectionUtils.isEmpty(stasticConfigInfoForms)){
                return;
            }
            // 组织生活配置
            StasticConfigInfoForm orgLifeConf =
                    stasticConfigInfoForms
                            .stream()
                            .filter(conf -> conf.getStatisticalType().equals(Constants.ORG_LIFE_TYPE))
                            .findFirst()
                            .orElse(null);
            // 组织生活统计类型
            List<MeetingTypeForm> orgLifeTypes = stasticConfigInfoService.
                    findConfigDetailByOrgLife(orgLifeConf, null);
            // 组织生活统计类型添加双重组织生活
            orgLifeTypes.add(this.getDualOrgLifeTypeForm());
            // 双重组织生活配置
            StasticConfigInfoForm dualOrgLifeConf =
                    stasticConfigInfoForms
                            .stream()
                            .filter(conf -> conf.getStatisticalType().equals(Constants.LEADER_ORG_MEETING_TYPE))
                            .findFirst()
                            .orElse(null);
            //以前查询3的直接下级 这里根据区县id查询数据
            //List<OrganizationBase> organizationBaseList =
            //userCenterService.findAllOrg(sasOrgLifeConfig.getOrgRoot());
            List<OrganizationBase> organizationBaseList =
                    userCenterService.findAllOrg(item.getOrgData().getOrgId());
            // 查询单个组织每个月举办会议情况
            if (!CollectionUtils.isEmpty(organizationBaseList)) {
                int thisYear = DateTime.now().getYear();
                for (int i = 0; i < organizationBaseList.size(); i++) {
                    OrganizationBase organizationBase = organizationBaseList.get(i);
                    for (int y = sasOrgLifeConfig.getMinYear(); y <= thisYear; y++) {
                        for (int m = 1; m <= 12; m++) {
                            // 查询单个组织的举办会议情况 并且入库t_statistical_org_life_view表
                            statisticalOrgLifeViewMapper.count(
                                    organizationBase.getOrgId(),
                                    organizationBase.getName(),
                                    i,
                                    orgLifeConf == null ? null : orgLifeConf.getShowActivityTypes(),
                                    orgLifeConf == null ? null : orgLifeConf.getShowOrganizationTypes(),
                                    dualOrgLifeConf == null ? null : dualOrgLifeConf.getShowActivityTypes(),
                                    dualOrgLifeConf == null ? null : dualOrgLifeConf.getShowOrganizationTypes(),
                                    y,
                                    m);
                        }
                    }
                }
            }
        });
        stopWatch.stop();
        log.debug("党组生活一览统计耗时：\n{}", stopWatch.prettyPrint());
    }

    /**
     * 双重组织生活
     */
    private MeetingTypeForm getDualOrgLifeTypeForm() {
        return MeetingTypeForm.builder()
                .typeId(StatisticalOrgLifeViewEntity.TYPE_DUAL_ORG_LIFE)
                .typeName(StatisticalOrgLifeViewEntity.TYPE_DUAL_ORG_LIFE_NAME)
                .build();
    }
}
