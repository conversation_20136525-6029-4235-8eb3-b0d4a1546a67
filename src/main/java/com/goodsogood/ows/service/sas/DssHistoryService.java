package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.mapper.sas.DssHistoryMapper;
import com.goodsogood.ows.model.db.sas.DssHistoryEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 辅助决策服务层
 * <AUTHOR>
 */
@Service
@Log4j2
public class DssHistoryService {

    private final DssHistoryMapper dssHistoryMapper;

    @Autowired
    public DssHistoryService(DssHistoryMapper dssHistoryMapper) {
        this.dssHistoryMapper = dssHistoryMapper;
    }

    public void insert(Long userId, Date date, String ip, Long regionId, Integer type, String url) {
        DssHistoryEntity entity = new DssHistoryEntity();
        entity.setUserId(userId);
        entity.setTime(date);
        entity.setIp(ip);
        entity.setRegionId(regionId);
        entity.setType(type);
        entity.setUrl(url);
        this.dssHistoryMapper.insert(entity);
    }
}
