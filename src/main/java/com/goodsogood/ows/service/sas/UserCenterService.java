package com.goodsogood.ows.service.sas;

import com.github.pagehelper.Page;
import com.goodsogood.ows.common.bean.PageBean;
import com.goodsogood.ows.model.db.sas.StatisticalUserTempEntity;
import com.goodsogood.ows.model.vo.sas.MemberResultForm;
import com.goodsogood.ows.model.vo.sas.OrgPeriodWarningDetailForm;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.user.UserPartyQueryForm;
import com.goodsogood.ows.service.user.OrgPeriodService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-19 16:21
 */
@Service
@Log4j2
public class UserCenterService {
    @Value("${tog-services.user-center}")
    private String userCenter;

    private final RestTemplate restTemplate;
    private final UserService userService;
    private final OrgService orgService;
    private final OrgPeriodService orgPeriodService;

    public UserCenterService(RestTemplate restTemplate, UserService userService,
                             OrgService orgService, OrgPeriodService orgPeriodService) {
        this.restTemplate = restTemplate;
        this.userService = userService;
        this.orgService = orgService;
        this.orgPeriodService = orgPeriodService;
    }

    /**
     * 批量获取用户信息
     *
     * @param includeLevel  是否包含下级 1:包含 2：不包含 3：仅包含 默认1
     * @param includeRetire 是否包含离退休组织 1:包含 2：不包含 3：仅包含 默认1
     * @param includeAll    是否包含所有人 1-是 2-否，默认2
     * @param filterStatus  是否包含删除状态信息 1-是 2-否，默认1
     */
    public Page<StatisticalUserTempEntity> findPartyUser(Long orgId,
                                                         Integer includeRetire,
                                                         Integer includeLevel,
                                                         Integer includeAll,
                                                         Integer filterStatus, PageBean page) {
        UserPartyQueryForm queryForm = new UserPartyQueryForm();
        queryForm.setPage(page);
        queryForm.setOrgId(orgId);
        queryForm.setIncludeRetire(includeRetire);
        queryForm.setIncludeLevel(includeLevel);
        queryForm.setIncludeAll(includeAll);
        queryForm.setFilterStatus(filterStatus);
        return this.userService.findPartyUserByWhere(queryForm);
    }


    /**
     * 查询组织直接下级
     */
    public List<OrganizationBase> findAllOrg(Long orgId) {
        return this.orgService.findAllOrg(orgId);
    }

    /**
     * 根据条件查询政治、自然生日党员
     */
    public MemberResultForm findMemberByDate(Long orgId) {
        return this.userService.findMemberByDate(orgId);
    }

    /**
     * 根据条件查询组织管理员
     */
    public List<UserInfoBase> findManagerByWhere(Long orgId, Integer roleType) {
        return this.userService.findManagerByWhere(orgId, roleType);
    }

    /**
     * 查询党组织换届
     */
    public List<OrgPeriodWarningDetailForm> findExpireApp(Long orgId, Integer type, Long regionId) {
        return this.orgPeriodService.findExpireApp(orgId, type, regionId);
    }

    /**
     * 查询上个月支部换届数量
     */
    public Integer lastMonthChange(String orgIdList) {
        return this.orgPeriodService.getChangeLastMonth(orgIdList).intValue();
    }

    /**
     * 本月应换届的党委
     */
    public Integer thisMonthPartyChange(Long orgId) {
        return this.orgPeriodService.thisMonthPartyChange(orgId);
    }

    /**
     * 本月应换届的党支部
     */
    public Integer thisMonthBranchChange(Long orgId) {
        return this.orgPeriodService.thisMonthBranchChange(orgId);
    }

    /**
     * 查询那些是工委管理员
     * @param roleType
     * @return
     */
    public List<Long> findWorkCommitManagerByWhere(Long orgId,int roleType) {
        return this.userService.findWorkCommitManagerByWhere(orgId,roleType);
    }
}
