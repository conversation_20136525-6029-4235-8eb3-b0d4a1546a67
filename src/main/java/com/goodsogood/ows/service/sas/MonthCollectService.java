package com.goodsogood.ows.service.sas;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.db.score.ScoreOrganizeEntity;
import com.goodsogood.ows.model.enumpackage.OrganizeAccountTypeEnum;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.report.PersonElectronicReport;
import com.goodsogood.ows.model.vo.sas.*;
import com.goodsogood.ows.service.electronicreport.ElectronicReportServices;
import com.goodsogood.ows.service.ppmd.PpmdStatisticsService;
import com.goodsogood.ows.service.score.ScoreOrganizeService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 月度汇总
 * <AUTHOR>
 */
@Service
@Log4j2
public class MonthCollectService {

    private final RestTemplate restTemplate;
    private final StringRedisTemplate redisTemplate;
    private final TogServicesConfig togServicesConfig;
    private final OrgService orgService;
    private final PpmdStatisticsService statisticsService;
    private final UserService userService;
    private final ElectronicReportServices electronicReportService;
    private final ScoreOrganizeService scoreOrganizeService;
    private final SimpleApplicationConfigHelper configHelper;

    @Autowired
    public MonthCollectService(RestTemplate restTemplate, StringRedisTemplate redisTemplate,
                               TogServicesConfig togServicesConfig, OrgService orgService,
                               PpmdStatisticsService statisticsService, UserService userService,
                               ElectronicReportServices electronicReportService,
                               ScoreOrganizeService scoreOrganizeService,
                               SimpleApplicationConfigHelper configHelper){
        this.restTemplate = restTemplate;
        this.redisTemplate = redisTemplate;
        this.togServicesConfig = togServicesConfig;
        this.orgService = orgService;
        this.statisticsService = statisticsService;
        this.userService = userService;
        this.electronicReportService = electronicReportService;
        this.scoreOrganizeService = scoreOrganizeService;
        this.configHelper = configHelper;
    }

    /**
     * 查询组织的组织生活开展情况
     * @param orgId     组织ID
     * @param date      月份
     * @param type      查询类型(1-党委(高级管理员), 2-党支部(一般管理员))
     */
    public OrgLiftCollectVo getOrgLiftCollect(Long orgId, String date, int type){
        log.debug("查询组织的组织生活开展情况 -> 【 orgId : {}, date : {} 】", orgId, date);
        OrgLiftCollectVo resultVo = new OrgLiftCollectVo();
        // 拆分时间
        String[] strings = date.split("-");
        Integer year = Integer.valueOf(strings[0]);
        Integer month = Integer.valueOf(strings[1]);
        // 判断是否为季度月末
        boolean is_end = month % 3 == 0;
        // 查询组织的组织生活情况
        List<EvalMobileDataCollectionForm> orgDataCollect = this.getOrgDataCollect(orgId, year, month);
        // 查询组织下属的考核组织
        List<OrganizationBase> childEvalOrgList = this.orgService.findEvalOrgByOrgId(orgId, 3);
        // 未完成支部数量
        Integer unFinishNum = 0;
        List<String> unFinishType = new ArrayList<>();
        // 判断查询类型
        if (!CollectionUtils.isEmpty(orgDataCollect)) {
            AtomicReference<Integer> atomicReference = new AtomicReference<>(0);
            boolean finalIs_end = is_end;
            orgDataCollect.forEach(form ->{
                Integer before = atomicReference.get();
                // 判断是月度考核任务，还是季度考核任务
                List<EvalMobileDataCollectionForm.Detail> dataList = form.getData();
                if (!CollectionUtils.isEmpty(dataList)) {
                    if (form.getPeriod() == 1) {
                        dataList.forEach(data -> {
                            if (data.getMonth().equals(month)) {
                                atomicReference.updateAndGet(v -> v + data.getTotal());
                            }
                        });
                    } else {
                        if(finalIs_end) {
                            dataList.forEach(data -> {
                                atomicReference.updateAndGet(v -> v + data.getTotal());
                            });
                        }
                    }
                }
                Integer later = atomicReference.get();
                if (!before.equals(later)) {
                    unFinishType.add(form.getTypeName());
                }
            });
            // 未完成支部数量
            unFinishNum = atomicReference.get();
        }
        // 计算完成支部数量
        int finishNum = (childEvalOrgList == null ? 0 : childEvalOrgList.size()) - unFinishNum;
        if (type == 1) {
            resultVo.setFinishNum(finishNum);
            resultVo.setUnFinishNum(unFinishNum);
        } else {
            resultVo.setUnList(unFinishType);
        }
        return resultVo;
    }

    /**
     * 查询党委的组织生活情况
     * @param orgId
     * @param date
     * @return
     */
    public List<OrgLiftRemindVo> getOrgLiftRemind(Long orgId, String date){
        log.debug("查询党委的组织生活开展情况 -> 【 orgId : {}, date : {} 】", orgId, date);
        List<OrgLiftRemindVo> orgLiftRemindList = new ArrayList<>();
        // 拆分时间
        String[] strings = date.split("-");
        Integer year = Integer.valueOf(strings[0]);
        Integer month = Integer.valueOf(strings[1]);
        // 判断是否为季度月末
        boolean is_end = month % 3 == 0;
        // 查询组织的组织生活情况
        List<EvalMobileDataCollectionForm> orgDataCollect = this.getOrgDataCollect(orgId, year, month);
        // 封装数据
        if (!CollectionUtils.isEmpty(orgDataCollect)) {
            boolean finalIs_end = is_end;
            orgDataCollect.forEach(form ->{
                AtomicReference<Integer> atomicReference = new AtomicReference<>(0);
                OrgLiftRemindVo remindVo = new OrgLiftRemindVo();
                List<EvalMobileDataCollectionForm.Detail> dataList = form.getData();
                if (!CollectionUtils.isEmpty(dataList)) {
                    if (form.getPeriod() == 1) {
                        dataList.forEach(data -> {
                            if (data.getMonth().equals(month)) {
                                atomicReference.updateAndGet(v -> v + data.getTotal());
                            }
                        });
                    } else {
                        if(finalIs_end) {
                            dataList.forEach(data -> {
                                atomicReference.updateAndGet(v -> v + data.getTotal());
                            });
                        }
                    }
                }
                Integer total = atomicReference.get();
                if (!total.equals(0)) {
                    remindVo.setTypeName(form.getTypeName());
                    remindVo.setOweNum(total);
                    orgLiftRemindList.add(remindVo);
                }
            });
        }
        return orgLiftRemindList;
    }

    /**
     * 查询所有考核组织
     * @param orgId 区县顶级组织ID
     */
    public List<OrganizationBase> getEvalOrg(Long orgId){
        return this.orgService.findEvalOrgByOrgId(orgId, 3);
    }

    /**
     * 查询下级考核组织
     * @param orgId 组织ID
     */
    public List<OrganizationBase> getEvalOrgById(Long orgId){
        return  this.orgService.findEvalOrgByOrgId(orgId, 3);
    }

    /**
     * 获取组织党费缴纳情况
     * @param orgId     组织ID
     * @param date      查询月份
     * @param type      查询组织类型 1-党委， 2-党支部
     */
    public List<StatsResultVo> getOrgPPMDStats(Long orgId, String date, int type){
        log.debug("查询组织党费缴纳情况 -> 【 orgId : {}, date : {}, type : {} 】", orgId, date, type);
        // 封装请求参数
        StatsParamForm form = new StatsParamForm();
        form.setOrgId(orgId);
        form.setStartDate(date);
        form.setEndDate(date);
        List<StatsResultVo> ppmdStats = null;
        if(type == 1) {
            ppmdStats = this.statisticsService.unitpayMonth(form);
        } else {
            ppmdStats = this.statisticsService.branchpayMonth(form);
        }
        return ppmdStats;
    }


    /**
     * 调用远程接口查询组织的组织生活开展情况
     * @param orgId
     * @param year
     * @param month 1690-\Atyuiolp;['
     *
     */
    public List<EvalMobileDataCollectionForm> getOrgDataCollect(Long orgId, int year, int month){
        log.debug("查询组织的组织生活开展情况 -> 【 orgId : {}, year : {}, month : {} 】", orgId, year, month);
        // 根绝月份判断季度
        int quarter = (month + 2) / 3;
        List<EvalMobileDataCollectionForm> list = null;
        // 从缓存里面查询
        String redisKey = Constants.EVAL_SUPERVISE_EARLY_WARNING + orgId + "_" + year + "_" + quarter;
        if (redisTemplate.hasKey(redisKey)) {
            String json = redisTemplate.opsForValue().get(redisKey);
            list = JsonUtils.fromJson(json, new TypeReference<List<EvalMobileDataCollectionForm>>() {
            });
        } else {
            // 接口地址
            String url = String.format("http://%s/mobile/data-collect/list?org_id=%s&year=%s&time=%s", this.togServicesConfig.getEval(), orgId, year, quarter);
            HttpHeaders headers = new HttpHeaders();
            this.restTemplate.setErrorHandler(new ClientExceptionHandler());
            try {
                list = RemoteApiHelper.get(restTemplate, url, headers, new TypeReference<Result<List<EvalMobileDataCollectionForm>>>() {
                });
                log.debug("远程调用Eval查询组织的组织生活开展情况: [{}]", list);
            } catch (Exception e) {
                log.debug("调用远程服务失败, 错误内容:", e);
            }
        }
        return list;
    }

    /**
     * 根据组织ID查询上级党委
     */
    public PartyOrgVo getActivePayment(Long orgId){
        log.debug("根据组织ID查询上级党委 -> orgId");
        return this.orgService.findPartyId(orgId);
    }

    /**
     * 根据用户和时间查询党费详情
     * @param idList
     * @param date
     */
    public List<UserPayInfoVo> getPayInfoByUserId(List<Long> idList, String date){
        log.debug("查询党费详情: userId -> [{}], date -> [{}]", idList, date);
        QueryUserInfoForm form = new QueryUserInfoForm();
        form.setUserIdList(idList.stream().map(id -> id.toString()).collect(Collectors.joining(",")));
        form.setDate(date);
        return this.statisticsService.findPayInfo(form);
    }

    /**
     * 查询用户获取和消费积分,使用缓存
     * @param regionId
     * @param userList
     * @return
     */
    public List<UserScoreInfoVo> getScoreInfoByUserId(Long regionId, List<Long> userList){
        //根据区域编号，获取顶级组织编号
        Long topOrgId = this.configHelper.getOrgByRegionId(regionId).getOrgId();
        //根据顶级组织编号获取appid
        ScoreOrganizeEntity scoreOrganizeEntity = this.scoreOrganizeService.getScoreOrganizeByOrgIdCache(topOrgId, OrganizeAccountTypeEnum.ORGANIZE_ACCOUNT.getKey());
        Long appId = scoreOrganizeEntity.getAppId();
        List<UserScoreInfoVo> re = new ArrayList<>();
        List<Object> list = userList.stream().map(Object::toString).collect(Collectors.toList());
        if(userList.size()>0){
            //从缓存获取上月，用户在市直机关获取和消费的积分数量
            List<Object> s = redisTemplate.opsForHash().multiGet(Constants.USER_SCORE_INFO_KEY + appId, list);
            //去空
            s = s.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if(s.size()>0){
                re = (List<UserScoreInfoVo>)JsonUtils.fromJson(String.valueOf(s),ArrayList.class, UserScoreInfoVo.class);
            }
        }
        return re;

    }

    /**
     * 根据组织ID查询组织下的党员
     * @param idList 组织IDList
     * @return
     */
    public List<UserInfoBase> getUserByOrgId(List<Long> idList){
        log.debug("根据组织ID查询组织下的党员: userId -> [{}]", idList);
        FindOrgListForm form = new FindOrgListForm();
        form.setIdList(idList);
        form.setIsEmployee(Constants.STATUS_YES);
        form.setIsFilter(Constants.STATUS_YES);
        return this.userService.findUserByOrgId(form);
    }

    /**
     * 根据电子党务报告获取制定月学习积分
     * @param userId
     * @param queryTime
     * @return
     */
    public PersonElectronicReport getUserStaInfo(Long regionId,Long userId, String queryTime){
        return this.electronicReportService.getPersonElectronicReport(regionId,userId, queryTime);
    }
}
