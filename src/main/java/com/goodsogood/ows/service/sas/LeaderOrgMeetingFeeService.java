package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.mapper.sas.StatisticalDataRecordMapper;
import com.goodsogood.ows.mapper.sas.StatisticalLeaderOrgLifeMapper;
import com.goodsogood.ows.model.db.sas.StatisticalDataRecordEntity;
import com.goodsogood.ows.model.db.sas.StatisticalLeaderOrgLifeEntity;
import com.goodsogood.ows.model.vo.sas.SasLeaderOrgForm;
import com.goodsogood.ows.model.vo.sas.StatisticsMeetingForm;
import com.goodsogood.ows.service.meeting.StaMeetingService;
import com.goodsogood.ows.service.user.LeaderService;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 领导干部组织双重组织生活统计服务层
 * <AUTHOR>
 */
@Service
@Log4j2
public class LeaderOrgMeetingFeeService {

    @Value("${leader.meeting.sign.status}")
    private String signStatus;

    private final StaMeetingService meetingService;
    private final LeaderService leaderService;
    private final StatisticalLeaderOrgLifeMapper leaderOrgLifeMapper;
    private final StatisticalDataRecordMapper dataRecordMapper;
    private final OrgService orgService;

    private final Calendar calendar = Calendar.getInstance();
    private final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM");

    @Autowired
    public LeaderOrgMeetingFeeService(StaMeetingService meetingService, LeaderService leaderService, StatisticalLeaderOrgLifeMapper leaderOrgLifeMapper,
                                      StatisticalDataRecordMapper dataRecordMapper, OrgService orgService){
        this.meetingService = meetingService;
        this.leaderService = leaderService;
        this.leaderOrgLifeMapper = leaderOrgLifeMapper;
        this.dataRecordMapper = dataRecordMapper;
        this.orgService = orgService;
    }

    @Transactional(rollbackFor = ParseException.class)
    public void leaderOrgMeetingFeeMain(Date date, StopWatch stopWatch, Long queryDate) throws ParseException {
        // 查询领导班子成员列表
        List<SasLeaderOrgForm> leaderOrgList = this.leaderService.findAllLeader();
        log.info("获取领导班子成员列表耗时 -> [{}]", stopWatch.toString());
        if (!CollectionUtils.isEmpty(leaderOrgList)) {
            for (SasLeaderOrgForm leaderOrg : leaderOrgList) {
                this.queryLeaderMeetingInfo(leaderOrg, date, stopWatch, queryDate);
            }
        }
    }

    /**
     * 查询领导干部组织生活情况，并判断入库
     * @param leaderOrg
     * @param date
     * @throws ParseException
     */
    private void queryLeaderMeetingInfo(SasLeaderOrgForm leaderOrg, Date date, StopWatch stopWatch, Long queryDate) throws ParseException {
        Long userId = leaderOrg.getUserId();
        Long regionId = leaderOrg.getRegionId();
        // 调用纪实系统查询人员参加活动情况
        List<StatisticsMeetingForm> userInfo = this.meetingService.
                getStatisticsOrganizeUserInfo(regionId, userId, queryDate, signStatus);
        log.info("获取领导[{}]的组织生活情况[{}],耗时 -> [{}]",leaderOrg.getUserId(), userInfo,
                stopWatch.toString());
        // 按照用户ID和时间查询统计的数据
        List<StatisticalLeaderOrgLifeEntity> lifeEntities = this.findByUserAndDate(userId, queryDate,regionId);
        if (!CollectionUtils.isEmpty(userInfo)) {
            // 循环数据，判断数据是否新增和更新
            for (StatisticsMeetingForm form : userInfo) {
                // 获取统计数据的活动类型
                Long typeId = form.getTypeId();
                // 设定标识符
                boolean flag = true;
                if (!CollectionUtils.isEmpty(lifeEntities)) {
                    for (StatisticalLeaderOrgLifeEntity entity : lifeEntities) {
                        // 判断原本统计出来的活动类型和当前纪实的活动类型是否存在，存在及更新
                        if ((entity.getActivityTypeId().equals(typeId.intValue()) ||
                                typeId.intValue() == entity.getActivityTypeId())
                                && (entity.getStatisticalDate().equals(form.getStaTime()))) {
                            flag = false;
                            this.insertRecordTable(leaderOrg, form, date);
                            this.updateOrgLift(entity, leaderOrg, form, date);
                            break;
                        }
                    }
                }
                // 当前纪实的活动类型数据在统计中不存在
                if (flag) {
                    //插入数据库
                    this.insertOrgLift(leaderOrg, form, date);
                    this.insertRecordTable(leaderOrg, form, date);
                }
            }
        }
        // 反向判断，
        if (!CollectionUtils.isEmpty(lifeEntities)) {
            for (StatisticalLeaderOrgLifeEntity entity : lifeEntities) {
                // 判断之前统计的数据在本次纪实查询数据中是否存在
                boolean flag = true;
                if (!CollectionUtils.isEmpty(userInfo)) {
                    for (StatisticsMeetingForm form : userInfo) {
                        if ((entity.getActivityTypeId().equals(form.getTypeId().intValue()) ||
                                entity.getActivityTypeId() == form.getTypeId().intValue())
                        && (entity.getStatisticalDate().equals(form.getStaTime()))){
                            flag = false;
                            break;
                        }
                    }
                }
                if (flag) {
                    StatisticsMeetingForm form = new StatisticsMeetingForm();
                    form.setStaTime(entity.getStatisticalDate());
                    form.setTypeId(entity.getActivityTypeId().longValue());
                    form.setTypeName(entity.getActivityTypeName());
                    form.setCountNum(0L);
                    this.insertRecordTable(leaderOrg, form, date);
                    this.updateOrgLift(entity, leaderOrg, form, date);
                }
            }
        }

        log.info("保存领导[{}]的组织生活情况完成,耗时 -> [{}]",leaderOrg.getUserId(), stopWatch.toString());
    }

    /**
     * 通过人员、时间查询数据
     * @param userId
     * @return
     */
    private List<StatisticalLeaderOrgLifeEntity> findByUserAndDate(Long userId, Long queryCode,Long regionId) {
        List<String> dateList = this.leaderOrgLifeMapper.
                findDateByCode(this.meetingService.getQueryTime(queryCode));
        Example example = new Example(StatisticalLeaderOrgLifeEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("leaderUserId", userId);
        criteria.andEqualTo("regionId", regionId);
        criteria.andIn("statisticalDate", dateList);
        return this.leaderOrgLifeMapper.selectByExample(example);
    }



    /**
     * 保存进领导班子组织生活统计
     * @param leaderOrg
     * @param userMeetingInfo
     * @param date
     * @throws ParseException
     */
    private void insertOrgLift(SasLeaderOrgForm leaderOrg, StatisticsMeetingForm userMeetingInfo, Date date) throws ParseException {
        int year = this.getDateInfo(userMeetingInfo.getStaTime(), Calendar.YEAR);
        int month = this.getDateInfo(userMeetingInfo.getStaTime(), Calendar.MONTH);
        StatisticalLeaderOrgLifeEntity entity = new StatisticalLeaderOrgLifeEntity();
        entity.setOrgId(leaderOrg.getOrgId());
        //添加组织区县id
        entity.setRegionId(leaderOrg.getRegionId());
        entity.setOrgName(leaderOrg.getOrgName());
        entity.setOrgTypeId(leaderOrg.getOrgType());
        entity.setParentOrgId(leaderOrg.getParentOrgId());
        entity.setOrgLevel(leaderOrg.getOrgLevel());
        entity.setIsRetire(leaderOrg.getIsRetire());
        entity.setLeaderUserId(leaderOrg.getUserId());
        entity.setLeaderName(leaderOrg.getUserName());
        entity.setActivityTypeId(userMeetingInfo.getTypeId().intValue());
        entity.setActivityTypeName(userMeetingInfo.getTypeName());
        entity.setStatisticalYear(year);
        entity.setStatisticalMonth(month);
        entity.setStatisticalDate(userMeetingInfo.getStaTime());
        entity.setParticipateNum(userMeetingInfo.getCountNum().intValue());
        entity.setCreateTime(date);
        entity.setStatus(Constants.STATUS_YES);
        this.leaderOrgLifeMapper.insert(entity);
    }

    /**
     * 更新导班子组织生活统计
     * @param leaderOrg
     * @param userMeetingInfo
     * @param date
     * @throws ParseException
     */
    private void updateOrgLift(StatisticalLeaderOrgLifeEntity entity, SasLeaderOrgForm leaderOrg, StatisticsMeetingForm userMeetingInfo, Date date) {
        // 判断是否是当月数据, 若是，则需要更新组织信息，若为历史数据，则不更新组织信息数据
        String formatDate = formatter.format(date);
        if (formatDate.equals(entity.getStatisticalDate())) {
            entity.setOrgId(leaderOrg.getOrgId());
            entity.setOrgName(leaderOrg.getOrgName());
            entity.setOrgTypeId(leaderOrg.getOrgType());
            entity.setParentOrgId(leaderOrg.getParentOrgId());
            entity.setOrgLevel(leaderOrg.getOrgLevel());
            entity.setIsRetire(leaderOrg.getIsRetire());
            entity.setLeaderUserId(leaderOrg.getUserId());
            entity.setLeaderName(leaderOrg.getUserName());
            entity.setActivityTypeName(userMeetingInfo.getTypeName());
        }
        entity.setParticipateNum(userMeetingInfo.getCountNum().intValue());
        entity.setUpdateTime(date);
        this.leaderOrgLifeMapper.updateByPrimaryKey(entity);
    }

    /**
     * 保存进大数据表
     * @param leaderOrg
     * @param userMeetingInfo
     * @param date
     * @throws ParseException
     */
    private void insertRecordTable(SasLeaderOrgForm leaderOrg, StatisticsMeetingForm userMeetingInfo, Date date) throws ParseException {
        StatisticalDataRecordEntity data = new StatisticalDataRecordEntity();
        data.setOrgId(leaderOrg.getOrgId());
        data.setOrgName(leaderOrg.getOrgName());
        data.setOrgTypeId(leaderOrg.getOrgType());
        data.setParentOrgId(leaderOrg.getParentOrgId());
        data.setOrgLevel(leaderOrg.getOrgLevel());
        data.setIsRetire(leaderOrg.getIsRetire());
        data.setUserId(leaderOrg.getUserId());
        data.setUserName(leaderOrg.getUserName());
        data.setDataType(Constants.LEADER_ORG_MEETING_TYPE);
        data.setActivityTypeId(userMeetingInfo.getTypeId().intValue());
        data.setActivityTypeName(userMeetingInfo.getTypeName());
        data.setStatisticalYear(getDateInfo(userMeetingInfo.getStaTime(), Calendar.YEAR));
        data.setStatisticalMonth(getDateInfo(userMeetingInfo.getStaTime(), Calendar.MONTH));
        data.setParticipateNum(userMeetingInfo.getCountNum().intValue());
        data.setCreateTime(date);
        data.setStatus(Constants.STATUS_YES);
        this.dataRecordMapper.insert(data);
    }

    /**
     * 拆分时间
     * @param date
     * @param field 1-年份, 2-月份
     * @return
     * @throws ParseException
     */
    private int getDateInfo(String date, int field) throws ParseException {
        // 获取当前年月
        calendar.setTime(formatter.parse(date));
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        if (field == Calendar.YEAR) {
            return year;
        } else if (field == Calendar.MONTH) {
            return month;
        } else {
            return 0;
        }
    }
}
