package com.goodsogood.ows.service.sas;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.mapper.sas.StatisticalPartyFeeMapper;
import com.goodsogood.ows.model.db.sas.StatisticalPartyFeeEntity;
import com.goodsogood.ows.model.vo.sas.ElectronicConditionForm;
import com.goodsogood.ows.model.vo.sas.ElectronicPartyFeeData;
import com.goodsogood.ows.model.vo.sas.SasOrgPayFeeForm;
import com.goodsogood.ows.model.vo.sas.StatisticalPartyFeeForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

/**
 * 党委统计情况-服务层
 * <AUTHOR>
 */
@Service
@Log4j2
public class StatisticalPartyFeeService {


    private final StatisticalPartyFeeMapper statisticalPartyFeeMapper;
    private final StasticConfigInfoService stasticConfigInfoService;
    private final StatisticalLeaderOrgLifeService statisticalLeaderOrgLifeService;

    @Autowired
    public StatisticalPartyFeeService(StatisticalPartyFeeMapper statisticalPartyFeeMapper, StasticConfigInfoService stasticConfigInfoService, StatisticalOrgLifeService statisticalOrgLifeService, StatisticalLeaderOrgLifeService statisticalLeaderOrgLifeService) {
        this.statisticalPartyFeeMapper = statisticalPartyFeeMapper;
        this.stasticConfigInfoService = stasticConfigInfoService;
        this.statisticalLeaderOrgLifeService = statisticalLeaderOrgLifeService;
    }

    /**
     * 新增数据
     * @param entity
     */
    public void insert(StatisticalPartyFeeEntity entity){
        this.statisticalPartyFeeMapper.insertSelective(entity);
    }
    /**
     * 更新数据
     * @param entity
     */
    public void update(StatisticalPartyFeeEntity entity){
        this.statisticalPartyFeeMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 根据月份来查询当前信息
     * @param orgId 组织ID
     * @param date  yyyy-MM
     * @return
     */
    public StatisticalPartyFeeEntity getInfoByMouth(Long orgId, String date){
        // 查询组织在月份中的数据
        Example example = new Example(StatisticalPartyFeeEntity.class);
        example.createCriteria()
                .andEqualTo("orgId", orgId)
                .andEqualTo("statisticalDate",date);
        StatisticalPartyFeeEntity data = this.statisticalPartyFeeMapper.selectOneByExample(example);
        log.debug("查询组织[{}], 在[{}]时间的数据情况 -> {}", orgId, date, data);
        return data;
    }

    /**
     * 获取之前欠交的组织信息
     * @param startTime 开始查询时间
     * @param timeLimit 查询之前多少个月
     * @return
     * @throws ParseException
     */
    public List<StatisticalPartyFeeEntity> getUnPayInfo(String startTime, Integer timeLimit) throws ParseException {
        // 获取结束时间
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(formatter.parse(startTime));
        calendar.add(Calendar.MONTH, -timeLimit);
        String endTime = formatter.format(calendar.getTime());
        // 封装查询类
        Example example = new Example(StatisticalPartyFeeEntity.class);
        example.createCriteria()
                .andEqualTo("status", Constants.STATUS_YES)
                .andNotEqualTo("unpaidPersonNum", 0)
                .andLessThan("statisticalDate", startTime)
                .andGreaterThanOrEqualTo("statisticalDate", endTime);
        List<StatisticalPartyFeeEntity> payInfo = this.statisticalPartyFeeMapper.selectByExample(example);
        return payInfo;
    }

    /**
     * 分页按条件查询党费统计信息
     * @param form
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Page<StatisticalPartyFeeForm> findOrgPayFeeList(SasOrgPayFeeForm form, Integer pageNo, Integer pageSize){
        form.setShowOrgType(this.stasticConfigInfoService.getConfig(Constants.PARTY_PAY_TYPE,form.getRegionId()).getShowOrganizationTypes());
        // 封装查询类
        Page<StatisticalPartyFeeForm> payFeeList = PageHelper.startPage(pageNo, pageSize).doSelectPage(()
                -> this.statisticalPartyFeeMapper.findPayFeeList(form));
        return payFeeList;
    }

    /**
     * 导出需要全量数据，不需要分页
     * @param form
     * @return
     */
    public List<StatisticalPartyFeeForm> findAllOrgPayFeeList(SasOrgPayFeeForm form){
        form.setShowOrgType(this.stasticConfigInfoService.getConfig(Constants.PARTY_PAY_TYPE,form.getRegionId()).getShowOrganizationTypes());
        return this.statisticalPartyFeeMapper.findPayFeeList(form);
    }


    /**
     * 考核二期拉取数据查询方法
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/5/8 14:05
     */


    public Page<StatisticalPartyFeeForm> findPayFeeList(Long pageNo, Long pageSize, Long orgId, Long staTimeCode,Long regionId){
        Page<StatisticalPartyFeeForm> page = PageHelper.startPage(pageNo.intValue(), pageSize.intValue()).
            doSelectPage(() -> this.statisticalPartyFeeMapper.findForCondition(orgId,statisticalLeaderOrgLifeService.getQueryTime(staTimeCode),regionId));
        return  page;
    }


    /**
     * 电子报表统计党费情况
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/6/13 9:59
     */

    public ElectronicPartyFeeData electronicPartyfFeeStastic(ElectronicConditionForm form) {
        ElectronicPartyFeeData partyFeeData = new ElectronicPartyFeeData();
        SasOrgPayFeeForm sasOrgPayFeeForm = new SasOrgPayFeeForm();
        sasOrgPayFeeForm.setDateStr(form.getMonth());
        sasOrgPayFeeForm.setStatisticalYear(form.getYear());
        sasOrgPayFeeForm.setOrgId(form.getOrgId());
        //当前配置的组织类型,是否需要过滤，默认需要过滤，其他访问如果不需要可设置未false
        if (form.getOrgTypeIdFlag()) {
            sasOrgPayFeeForm.setShowOrgType(stasticConfigInfoService.getConfig(Constants.PARTY_PAY_TYPE,form.getRegionId()).getShowOrganizationTypes());
        }
        partyFeeData = this.statisticalPartyFeeMapper.stasticPartyFee(sasOrgPayFeeForm);
        return partyFeeData;
    }


}
