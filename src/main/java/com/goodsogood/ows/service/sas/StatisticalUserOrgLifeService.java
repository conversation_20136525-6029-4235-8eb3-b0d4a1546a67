package com.goodsogood.ows.service.sas;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.bean.PageBean;
import com.goodsogood.ows.configuration.SasOrgLifeConfig;
import com.goodsogood.ows.mapper.sas.StatisticalUserOrgLifeMapper;
import com.goodsogood.ows.mapper.sas.StatisticalUserTempMapper;
import com.goodsogood.ows.model.db.sas.StasticConfigInfoEntity;
import com.goodsogood.ows.model.db.sas.StatisticalUserOrgLifeEntity;
import com.goodsogood.ows.model.db.sas.StatisticalUserTempEntity;
import com.goodsogood.ows.model.db.sas.TypeEntity;
import com.goodsogood.ows.model.vo.sas.*;
import com.goodsogood.ows.service.RegionService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.ExclExportUtils;
import com.goodsogood.ows.utils.ListUtils;
import com.goodsogood.ows.utils.PageUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

import static com.goodsogood.ows.mapper.sas.StatisticalUserTempMapper.INCLUDE_ALL_NO;

/**
 * <AUTHOR>
 * @create 2019-07-23 17:05
 */
@Service
@Log4j2
public class StatisticalUserOrgLifeService {
    @Value("${leader.meeting.sign.status}")
    private String signStatus;
    private final StatisticalUserOrgLifeMapper statisticalUserOrgLifeMapper;
    private final MeetingService meetingService;
    private final UserCenterService userCenterService;
    private final SasOrgLifeConfig sasOrgLifeConfig;
    private final StasticConfigInfoService stasticConfigInfoService;
    private final StatisticalUserTempMapper statisticalUserTempMapper;
    private final StatisticalOrgLifeService statisticalOrgLifeService;
    private final RegionService regionService;

    @Autowired
    public StatisticalUserOrgLifeService(
            StatisticalUserOrgLifeMapper statisticalUserOrgLifeMapper,
            MeetingService meetingService,
            UserCenterService userCenterService,
            SasOrgLifeConfig sasOrgLifeConfig,
            StasticConfigInfoService stasticConfigInfoService,
            StatisticalUserTempMapper statisticalUserTempMapper,
            StatisticalOrgLifeService statisticalOrgLifeService, RegionService regionService) {
        this.statisticalUserOrgLifeMapper = statisticalUserOrgLifeMapper;
        this.meetingService = meetingService;
        this.userCenterService = userCenterService;
        this.sasOrgLifeConfig = sasOrgLifeConfig;
        this.stasticConfigInfoService = stasticConfigInfoService;
        this.statisticalUserTempMapper = statisticalUserTempMapper;
        this.statisticalOrgLifeService = statisticalOrgLifeService;
        this.regionService = regionService;
    }

    /**
     * 查询党员参加组织生活信息
     *
     * @param form 查询条件
     */
    public Page<StaLeaderOrgLife> partyMemberOrgLife(SasOrgLifeConditionForm form) {
        StopWatch stopWatch = new StopWatch("查询党员参加组织生活信息");
        SasOrgLifeConditionForm conditionForm = statisticalOrgLifeService.buildParam(form);
        // 党支部组织生活统计配置
        StasticConfigInfoEntity stasticConfigInfoEntity =
                stasticConfigInfoService.getConfig(Constants.ORG_LIFE_TYPE,form.getRegionId());
        if (StringUtils.isBlank(conditionForm.getActivityTypeIds())) {
            conditionForm.setActivityTypeIds(stasticConfigInfoEntity.getShowActivityTypes());
        } else {
            stasticConfigInfoEntity.setShowActivityTypes(conditionForm.getActivityTypeIds());
        }
        List<String> months = Arrays.asList(conditionForm.getDateStr().split(","));
        PageBean pageBean = PageUtils.page(form.getPageNo(), form.getPageSize());
        stopWatch.start("分页查询");
        //sass 新增区县id查询用户
        Page<StatisticalUserTempEntity> pageUser =
                PageHelper.startPage(pageBean.getPageNo(), pageBean.getPageSize())
                        .doSelectPage(
                                () ->
                                        this.statisticalUserTempMapper.findUser(
                                                form.getOrgId(),
                                                stasticConfigInfoEntity.getShowOrganizationTypes(),
                                                form.getIsRetire(),
                                                INCLUDE_ALL_NO,
                                                DateUtils.firstDayOfMonthByDate(conditionForm.getStartTime()),
                                                DateUtils.lastDayOfMonthByDate(conditionForm.getEndTime()),
                                                form.getRegionId()));
        List<Map<String, Object>> pageData = new ArrayList<>();
        if (!CollectionUtils.isEmpty(pageUser)) {
            pageData =
                    this.statisticalUserOrgLifeMapper.findListByPageUser(
                            pageUser, form.getYear(), months, stasticConfigInfoEntity.getShowActivityTypes(),form.getRegionId());
        }
        stopWatch.stop();
        stopWatch.start("拼装返回对象");
        Page<StaLeaderOrgLife> page =
                buildDualOrgLifeDetailInfo(pageUser, pageData, stasticConfigInfoEntity, months);
        stopWatch.stop();
        log.debug("查询党员参加组织生活信息耗时:\n{}", stopWatch.prettyPrint());
        return page;
    }

    /**
     * 构建返回数据
     *
     * @param pageData 改页显示的数据
     */
    private Page<StaLeaderOrgLife> buildDualOrgLifeDetailInfo(
            Page<StatisticalUserTempEntity> pageUser,
            List<Map<String, Object>> pageData,
            StasticConfigInfoEntity stasticConfigInfoEntity,
            List<String> months) {
        Page<StaLeaderOrgLife> page = new Page<>();
        // 分页信息
        page.setPages(pageUser.getPages());
        page.setPageSize(pageUser.getPageSize());
        page.setPageNum(pageUser.getPageNum());
        page.setTotal(pageUser.getTotal());
        // 所有的活动类型
        List<TypeEntity> typeEntityList = stasticConfigInfoService.getActivityList();
        // 展示的活动类型
        List<TypeEntity> types = new ArrayList<>();
        for (String type : stasticConfigInfoEntity.getShowActivityTypes().split(",")) {
            for (TypeEntity typeEntity : typeEntityList) {
                if (typeEntity.getTypeId().equals(Long.valueOf(type))) {
                    types.add(typeEntity);
                }
            }
        }
        for (Map<String, Object> map : pageData) {
            StaLeaderOrgLife staLeaderOrgLife = new StaLeaderOrgLife();
            page.add(staLeaderOrgLife);
            Long userId = Long.valueOf(map.get("user_id").toString());
            String userName = map.get("user_name").toString();
            String orgName = map.get("org_name").toString();
            staLeaderOrgLife.setLeaderUserId(userId);
            staLeaderOrgLife.setLeaderName(userName);
            staLeaderOrgLife.setOrgName(orgName);
            StaMonthWrapper listWrapper = new StaMonthWrapper();
            staLeaderOrgLife.setListWrapper(listWrapper);

            List<StaMonth> list = new ArrayList<>();
            listWrapper.setList(list);
            int totality = 0;
            for (TypeEntity type : types) {
                StaMonth staMonth = new StaMonth();
                list.add(staMonth);
                staMonth.setActivityTypeId(type.getTypeId().intValue());
                staMonth.setActivityTypeName(type.getType());
                int total = 0;
                for (String m : months) {
                    try {
                        int num = Integer.valueOf(map.get(type.getTypeId() + "_" + m).toString());
                        total = total + num;
                        // 得到方法对象
                        String methodSetter = "setMonth" + m;
                        Method setter = staMonth.getClass().getMethod(methodSetter, Integer.class);
                        setter.invoke(staMonth, num);
                    } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                        log.error("设置StaMonth月份出错！{}", e.getMessage());
                    }
                }
                staMonth.setTotal(total);
                totality = totality + total;
            }
            listWrapper.setTotality(totality);
        }
        return page;
    }

    /**
     * 统计结果导出
     *
     * @param response HttpServletResponse
     * @param request  HttpServletRequest
     * @param form     SasOrgLifeConditionForm
     * @return boolean
     */
    public boolean export(
            HttpServletResponse response, HttpServletRequest request, SasOrgLifeConditionForm form,
            Long regionId) {
        boolean flag = false;
        StopWatch stopWatch = new StopWatch("党员组织生活情况统计导出");
        SasOrgLifeConditionForm conditionForm = statisticalOrgLifeService.buildParam(form);
        List<String> months = Arrays.asList(conditionForm.getDateStr().split(","));
        StasticConfigInfoForm orgLifeConf = getStasticConfigInfoForm(regionId);
        if (orgLifeConf != null) {
            // 查询全量数据
            // 3组织从缓存中获取数据
            stopWatch.start("查询数据");
            List<Map<String, Object>> data =
                    this.statisticalUserOrgLifeMapper.findListAll(
                            form.getOrgId(),
                            orgLifeConf.getShowOrganizationTypes(),
                            form.getIsRetire(),
                            INCLUDE_ALL_NO,
                            DateUtils.firstDayOfMonthByDate(conditionForm.getStartTime()),
                            DateUtils.lastDayOfMonthByDate(conditionForm.getEndTime()),
                            form.getYear(),
                            months,
                            StringUtils.isBlank(form.getActivityTypeIds())
                                    ? orgLifeConf.getShowActivityTypes()
                                    : form.getActivityTypeIds(),
                            regionId);
            stopWatch.stop();
            log.debug("导出条数:{}", data.size());
            stopWatch.start("写入excel");
            String sheetName = bulidSheetName(conditionForm);
            // 组织生活统计类型
            List<MeetingTypeForm> orgLifeTypes =
                    stasticConfigInfoService.findConfigDetailByOrgLife(
                            orgLifeConf, form.getActivityTypeIds());
            StatisticalUserOrgLifeExportService export =
                    new StatisticalUserOrgLifeExportService(sheetName, data, months, orgLifeTypes);
            HSSFWorkbook hssfWorkbook = export.export03();
            stopWatch.stop();
            flag = ExclExportUtils.export03(request, response, hssfWorkbook, sheetName);
            log.debug("党员组织生活情况统计导出耗时：\n{}", stopWatch.prettyPrint());
        }
        return flag;
    }

    /**
     * 获取组织生活配置
     *
     * @return StasticConfigInfoForm
     */
    private StasticConfigInfoForm getStasticConfigInfoForm(Long regionId) {
        // 统计看板配置信息
        List<StasticConfigInfoForm> stasticConfigInfoForms =
                stasticConfigInfoService.findConfigDetail(regionId);
        // 组织生活配置
        return stasticConfigInfoForms
                .stream()
                .filter(conf -> conf.getStatisticalType().equals(Constants.ORG_LIFE_TYPE))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取表头
     *
     * @param conditionForm 条件
     * @return String 表头
     */
    private String bulidSheetName(SasOrgLifeConditionForm conditionForm) {
        StringBuilder sb = new StringBuilder();
        sb.append(conditionForm.getOrgName()).append(conditionForm.getYear()).append("年");
        if (conditionForm.getTimeType().equals(Constants.SELECT_TIME_TYPE_HALF_YEAR)) {
            if (conditionForm.getTime().equals(Constants.SELECT_TIME_FIRST_HALF_YEAR)) {
                sb.append("上半年");
            } else {
                sb.append("下半年");
            }
        } else if (conditionForm.getTimeType().equals(Constants.SELECT_TIME_TYPE_QUARTER)) {
            if (conditionForm.getTime().equals(Constants.SELECT_TIME_FIRST_QUARTER)) {
                sb.append("第一季度");
            } else if (conditionForm.getTime().equals(Constants.SELECT_TIME_SECOND_QUARTER)) {
                sb.append("第二季度");
            } else if (conditionForm.getTime().equals(Constants.SELECT_TIME_THIRD_QUARTER)) {
                sb.append("第三季度");
            } else {
                sb.append("第四季度");
            }
        } else if (conditionForm.getTimeType().equals(Constants.SELECT_TIME_TYPE_MONTH)) {
            sb.append(conditionForm.getStartMonth())
                    .append("月")
                    .append("-")
                    .append(conditionForm.getEndMonth())
                    .append("月");
        }
        sb.append("党员组织生活情况统计");
        return sb.toString();
    }

    /**
     * 从纪实统计用户参加会议的次数
     *
     * @param now 定时任务执行时间
     */
    @Transactional(rollbackFor = Exception.class)
    public void sasUserOrgLifeCount(Long regionId, Date now) {

        StopWatch stopWatch = new StopWatch("统计用户参加会议的次数");
        stopWatch.start();
        // 删除统计的历史数据
        statisticalUserOrgLifeMapper.deleteAll(regionId);
        statisticalUserTempMapper.deleteAll(regionId);

        // 分页统计用户数据
        int pageNo = 1;
        int pageSize = sasOrgLifeConfig.getBatchNum();

        try {

            for (; ; ) {
                PageBean page = PageUtils.page(pageNo, pageSize);
                // 查询所有用户信息
                Page<StatisticalUserTempEntity> userInfoList = userCenterService
                        .findPartyUser(regionService.bindingOrgId(regionId), 1, 1, 1, 1, page);
                pageNo++;
                if (!CollectionUtils.isEmpty(userInfoList)) {
                    // 保存用户信息
                    saveUserInfo(userInfoList, now);
                    // 2.调纪实（meeting）统计
                    SasUserOrgLifeForm sasUserOrgLifeForm =
                            SasUserOrgLifeForm
                                    .builder()
                                    .regionId(regionId)
                                    .signStatus(ListUtils.intStringToList(signStatus))
                                    .uids(userInfoList.stream().map(StatisticalUserTempEntity::getUserId).collect(Collectors.toList()))
                                    .build();
                    List<StatisticalUserOrgLifeEntity> statisticalUserOrgLifeEntityList = meetingService.sasUserOrgLife(sasUserOrgLifeForm);
                    if (!CollectionUtils.isEmpty(statisticalUserOrgLifeEntityList)) {
                        List<StatisticalUserOrgLifeEntity> insertList = new ArrayList<>();
                        for (StatisticalUserOrgLifeEntity userOrgLifeEntity : statisticalUserOrgLifeEntityList) {
                            userOrgLifeEntity.setCreateTime(now);
                            insertList.add(userOrgLifeEntity);
                            if (insertList.size() > sasOrgLifeConfig.getBatchNum()) {
                                statisticalUserOrgLifeMapper.insertList(insertList);
                                insertList.clear();
                            }
                        }
                        // 3.保存统计信息
                        if (!insertList.isEmpty()) {
                            statisticalUserOrgLifeMapper.insertList(insertList);
                        }
                    }
                    userInfoList.clear();
                } else {
                    break;
                }
            }
            stopWatch.stop();
            log.debug("统计用户参加会议的次数耗时:\n{}", stopWatch.prettyPrint());
        }catch (Exception ex){
            log.error("sasUserOrgLifeCount发生异常",ex);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }

    private void saveUserInfo(List<StatisticalUserTempEntity> userInfoList, Date now) {
        if (!CollectionUtils.isEmpty(userInfoList)) {
            List<StatisticalUserTempEntity> insertList = new ArrayList<>();
            // 2018 年第一天
            Date date = DateUtils.firstDayOfYear(sasOrgLifeConfig.getMinYear());
            for (StatisticalUserTempEntity userTempEntity : userInfoList) {
                userTempEntity.setCreateTime(now);
                if (userTempEntity.getOrgCreateTime() == null) {
                    userTempEntity.setOrgCreateTime(date);
                }
                if (userTempEntity.getUserCreateTime() == null) {
                    userTempEntity.setUserCreateTime(date);
                }
                insertList.add(userTempEntity);
                if (insertList.size() > sasOrgLifeConfig.getBatchNum()) {
                    statisticalUserTempMapper.insertList(insertList);
                    insertList.clear();
                }
            }
            if (!insertList.isEmpty()) {
                statisticalUserTempMapper.insertList(insertList);
            }
        }
    }

    /**
     * 根据人员和时间查询组织生活统计
     * @param userId    用户编号
     * @param date      yyyy-MM
     */
    public List<StatisticalUserOrgLifeEntity> getOrgLiftByUserId(Long userId, String date){
        String[] s = date.split("-");
        Example example = new Example(StatisticalUserOrgLifeEntity.class);
        example.createCriteria().andEqualTo("userId", userId).
                andEqualTo("statisticalYear", s[0]).
                andEqualTo("statisticalMonth", s[1]);
        List<StatisticalUserOrgLifeEntity> list = this.statisticalUserOrgLifeMapper.selectByExample(example);
        return list;
    }


    /**
     * 得到连续几个月未参加组织生活的用户信息
     * @param activityId
     * @param year
     * @param month
     * @param userIds
     * @return
     */
    public Set<Long> getActivityUserInfo(Integer activityId, Set<Integer> year, Set<Integer> month, Set<Long> userIds) {
        Example example = new Example(StatisticalUserOrgLifeEntity.class);
        example.createCriteria().andEqualTo("typeId", activityId).
                andIn("statisticalYear", year).
                andIn("statisticalMonth", month).
                andIn("userId", userIds);
        List<StatisticalUserOrgLifeEntity> statisticalUserOrgLifeEntities = this.statisticalUserOrgLifeMapper.selectByExample(example);
        return statisticalUserOrgLifeEntities.stream().map(StatisticalUserOrgLifeEntity::getUserId)
                .collect(Collectors.toSet());
    }
}
