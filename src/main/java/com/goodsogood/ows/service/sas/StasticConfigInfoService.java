package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.sas.StasticConfigInfoMapper;
import com.goodsogood.ows.model.db.sas.StasticConfigInfoEntity;
import com.goodsogood.ows.model.db.sas.TypeEntity;
import com.goodsogood.ows.model.db.user.OptionEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.sas.*;
import com.goodsogood.ows.service.meeting.StaMeetingService;
import com.goodsogood.ows.service.user.OptionService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 党务看板统计
 *
 * <AUTHOR>
 * @date 2019/4/19 11:08
 */
@Service
@Log4j2
public class StasticConfigInfoService {
    private final Errors errors;
    private final StasticConfigInfoMapper stasticConfigInfoMapper;
    private final StaMeetingService meetingService;
    private final OptionService optionService;


    public StasticConfigInfoService(Errors errors, StasticConfigInfoMapper stasticConfigInfoMapper,
                                    StaMeetingService meetingService, OptionService optionService) {
        this.errors = errors;
        this.stasticConfigInfoMapper = stasticConfigInfoMapper;
        this.meetingService = meetingService;
        this.optionService = optionService;
    }

    /**
     * 新增统计设置信息
     *
     * <AUTHOR>
     * @date 2019/4/22 9:15
     */
    @Transactional
    public int addConfigInfo(List<StasticConfigInfoForm> infoFormList, HeaderHelper.SysHeader header) {
        if (CollectionUtils.isEmpty(infoFormList)) {
            throw new ApiException("设置的统计信息不能为空", new Result(errors, 4701, HttpStatus.OK.value()));
        }
        for (StasticConfigInfoForm infoForm : infoFormList) {
            //校验参数是否为空
            if (infoForm.getStatisticalType() == null) {
                throw new ApiException("设置统计信息的统计类型不能为空", new Result(errors, 4704, HttpStatus.OK.value()));
            }
            if (StringUtils.isEmpty(infoForm.getShowOrganizationTypes())) {
                throw new ApiException("设置统计信息的组织类型不能为空", new Result(errors, 4705, HttpStatus.OK.value()));
            }
            if (infoForm.getStatisticalType() != 3 && StringUtils.isEmpty(infoForm.getShowActivityTypes())) {
                throw new ApiException("设置统计信息的活动类型不能为空", new Result(errors, 4706, HttpStatus.OK.value()));
            }
            //判断当前类型的信息是否存在;
            Example configInfo = new Example(StasticConfigInfoEntity.class);
            configInfo.createCriteria().
                    andEqualTo("statisticalType", infoForm.getStatisticalType()).
                    andEqualTo("regionId", header.getRegionId());
            List<StasticConfigInfoEntity> configEntityList = this.stasticConfigInfoMapper.selectByExample(configInfo);
            if (!CollectionUtils.isEmpty(configEntityList)) {
                throw new ApiException("当前设置的信息" + infoForm.getStatisticalType() +
                        "类型已经存在", new Result(errors, 4700, HttpStatus.OK.value()));
            }
            //新增
            StasticConfigInfoEntity stasticConfigInfoEntity = new StasticConfigInfoEntity();
            stasticConfigInfoEntity.setShowActivityTypes(infoForm.getShowActivityTypes());
            stasticConfigInfoEntity.setStatisticalType(infoForm.getStatisticalType());
            stasticConfigInfoEntity.setShowOrganizationTypes(infoForm.getShowOrganizationTypes());
            stasticConfigInfoEntity.setLastChangeUser(header.getUserId());
            stasticConfigInfoEntity.setRegionId(header.getRegionId());
            //获取全量的组织类型和活动类型（暂时没有用，以后需要的时候在维护）
            /*stasticConfigInfoEntity.setAllOrganizationTypes(this.buildAllType().getAllOrganizationTypes());
            stasticConfigInfoEntity.setAllActivityTypes(this.buildAllType().getAllActivityTypes());*/
            stasticConfigInfoEntity.setCreateTime(new Date());
            stasticConfigInfoEntity.setUpdateTime(new Date());
            stasticConfigInfoMapper.insert(stasticConfigInfoEntity);
        }
        return 1;
    }

    /**
     * 修改统计设置信息
     *
     * <AUTHOR>
     * @date 2019/4/22 15:05
     */

    @Transactional
    public int editConfigInfo(List<StasticConfigInfoForm> infoFormList, HeaderHelper.SysHeader header) {
        if (CollectionUtils.isEmpty(infoFormList)) {
            throw new ApiException("修改的统计信息不能为空", new Result(errors, 4701, HttpStatus.OK.value()));
        }
        for (StasticConfigInfoForm infoForm : infoFormList) {
            if (null == infoForm.getConfigInfoId()) {
                throw new ApiException("修改的统计信息ID不能为空", new Result(errors, 4702, HttpStatus.OK.value()));
            }
            //判断当前类型的信息是否存在;
            StasticConfigInfoEntity stasticConfigInfoEntity = this.stasticConfigInfoMapper.selectByPrimaryKey(infoForm.getConfigInfoId());
            if (null == stasticConfigInfoEntity) {
                throw new ApiException("当前修改的信息" + infoForm.getConfigInfoId() + "不存在", new Result(errors, 4703, HttpStatus.OK.value()));
            }
            if (infoForm.getStatisticalType() == null) {
                throw new ApiException("设置统计信息的统计类型不能为空", new Result(errors, 4704, HttpStatus.OK.value()));
            }
            if (infoForm.getStatisticalType() != 3 && StringUtils.isEmpty(infoForm.getShowActivityTypes())) {
                throw new ApiException("设置统计信息的活动类型不能为空", new Result(errors, 4706, HttpStatus.OK.value()));
            }
            if (StringUtils.isEmpty(infoForm.getShowOrganizationTypes())) {
                throw new ApiException("设置统计信息的组织类型不能为空", new Result(errors, 4705, HttpStatus.OK.value()));
            }
            //修改
            stasticConfigInfoEntity.setStatisticalType(infoForm.getStatisticalType());
            stasticConfigInfoEntity.setShowActivityTypes(infoForm.getShowActivityTypes());
            stasticConfigInfoEntity.setShowOrganizationTypes(infoForm.getShowOrganizationTypes());
            stasticConfigInfoEntity.setLastChangeUser(header.getUserId());
            stasticConfigInfoEntity.setUpdateTime(new Date());
            stasticConfigInfoEntity.setRegionId(header.getRegionId());
            //获取全量的组织类型和活动类型（暂时没有用，以后需要的时候在维护）
/*            stasticConfigInfoEntity.setAllOrganizationTypes(this.buildAllType().getAllOrganizationTypes());
            stasticConfigInfoEntity.setAllActivityTypes(this.buildAllType().getAllActivityTypes());*/
            stasticConfigInfoMapper.updateByPrimaryKeySelective(stasticConfigInfoEntity);
        }
        return 1;
    }


    /**
     * 查询配置信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/4/23 13:35
     */

    @Transactional
    public List<StasticConfigInfoForm> findConfigDetail(Long regionId) {
        //查询列表
        List<StasticConfigInfoEntity> stasticConfigInfoList = this.stasticConfigInfoMapper.selectAll();
        //通过区县配置 得到不同区县的配置信息
        stasticConfigInfoList = stasticConfigInfoList.stream().
                filter(item -> item.getRegionId().equals(regionId)).collect(Collectors.toList());

        List<StasticConfigInfoForm> configInfoFormList = new ArrayList<StasticConfigInfoForm>();
        if (CollectionUtils.isEmpty(stasticConfigInfoList)) {
            return null;
        }
        //组装组织类型的名字和活动类型的名字
        stasticConfigInfoList.forEach(stasticConfigInfoEntity -> {
            StasticConfigInfoForm stasticConfigInfoForm = new StasticConfigInfoForm();
            BeanUtils.copyProperties(stasticConfigInfoEntity, stasticConfigInfoForm);

            List<OrganizationTypeForm> showOrgType = new ArrayList<OrganizationTypeForm>();
            List<ActivityTypeForm> showActivityType = new ArrayList<ActivityTypeForm>();
            //获取组织类型对应的名字
            List<OptionEntity> optionEntityList = getOgrList();
            String orgStr = stasticConfigInfoEntity.getShowOrganizationTypes();
            List<String> showOrgList = Arrays.asList(orgStr.split(","));
            if (!CollectionUtils.isEmpty(showOrgList) && !CollectionUtils.isEmpty(optionEntityList)) {
                //循环对比所选组织的名称
                showOrgList.forEach(s -> {
                    optionEntityList.forEach(optionEntity -> {
                        if (Integer.valueOf(s).equals(optionEntity.getOpKey())) {
                            OrganizationTypeForm typeForm = new OrganizationTypeForm();
                            typeForm.setOpKey(s);
                            typeForm.setOpValue(optionEntity.getOpValue());
                            showOrgType.add(typeForm);
                        }
                    });
                });
                stasticConfigInfoForm.setShowOrganizationTypeList(showOrgType);//组织对应的名称
            }

            //获取活动类型的名字
            String activityStr = stasticConfigInfoEntity.getShowActivityTypes();
            if (!StringUtils.isEmpty(activityStr)) {
                List<String> showActivityList = Arrays.asList(activityStr.split(","));
                //所有的活动类型
                List<TypeEntity> typeEntityList = getActivityList();
                if (!CollectionUtils.isEmpty(showActivityList) && !CollectionUtils.isEmpty(typeEntityList)) {
                    showActivityList.forEach(s -> {
                        //循环对比所有活动的名字
                        typeEntityList.forEach(typeEntity -> {
                            if (Long.parseLong(s) == typeEntity.getTypeId()) {
                                ActivityTypeForm activityTypeForm = new ActivityTypeForm();
                                activityTypeForm.setActivityId(s);
                                activityTypeForm.setActivityName(typeEntity.getType());
                                showActivityType.add(activityTypeForm);
                            }
                        });
                    });
                    stasticConfigInfoForm.setShowActivityTypeList(showActivityType);
                }
            }
            configInfoFormList.add(stasticConfigInfoForm);
        });
        return configInfoFormList;

    }

    /**
     * 组装全量的直至类型和活动类型
     *
     * <AUTHOR>
     * @date 2019/4/22 18:50
     */

    public StasticConfigInfoEntity buildAllType() {
        StasticConfigInfoEntity infoEntity = new StasticConfigInfoEntity();
        List<OptionEntity> optionEntityList = getOgrList();
        List<Integer> orgTypeList = new ArrayList<Integer>();
        if (!CollectionUtils.isEmpty(optionEntityList)) {
            optionEntityList.stream().forEach(optionEntity -> {
                orgTypeList.add(optionEntity.getOpKey());
            });
            infoEntity.setAllOrganizationTypes(orgTypeList.toString());
        }
        //调用活动中心
        List<Long> activityTypeStr = new ArrayList<Long>();
        List<TypeEntity> activityTypeList = getActivityList();
        if (!CollectionUtils.isEmpty(activityTypeList)) {
            activityTypeList.forEach(typeEntity -> {
                activityTypeStr.add(typeEntity.getTypeId());
            });
            infoEntity.setAllActivityTypes(activityTypeStr.toString());
        }
        return infoEntity;
    }


    /**
     * 获取所有组织类型
     *
     * <AUTHOR>
     * @date 2019/4/22 20:40
     */

    public List<OptionEntity> getOgrList() {
        //调用用户中心全量的组织类型
        List<OptionEntity> optionEntityList = this.optionService.getOptionList(String.valueOf(Constants.ORG_TYPE_COMMUNIST));
        return optionEntityList;
    }

    /**
     * 获取所有的活动类型（活动可能有多个分类）
     *
     * <AUTHOR>
     * @date 2019/4/22 20:38
     */

    public List<TypeEntity> getActivityList() {
        List<TypeAllResultForm> activityTypeList = this.meetingService.getMeetingTypeInfo();
        List<TypeEntity> typeEntityList = new ArrayList<TypeEntity>();
        if (!CollectionUtils.isEmpty(activityTypeList)) {
            activityTypeList.forEach(activityType -> {
                typeEntityList.addAll(activityType.getTypes());
            });
        }
        return typeEntityList;
    }

    /**
     * 获取党费缴纳情况配置
     * @param statisticalType
     * @return
     */
    public StasticConfigInfoEntity getConfig(int statisticalType,Long regionId){
        // 查询配置需要展示的组织类型
        Example example = new Example(StasticConfigInfoEntity.class);
        example.createCriteria()
                .andEqualTo("statisticalType", statisticalType)
                .andEqualTo("regionId", regionId);
        return this.stasticConfigInfoMapper.selectOneByExample(example);
    }

    /**
     *
     * 获取统计类型
     * @param stasticConfigInfoForm 党务看板配置
     * @param activityTypes 显示活动类型 null 显示全部
     */
    public List<MeetingTypeForm> findConfigDetailByOrgLife(
            StasticConfigInfoForm stasticConfigInfoForm, String activityTypes) {
        List<MeetingTypeForm> orgLifeTypes = new ArrayList<>();
        if (stasticConfigInfoForm != null
                && !CollectionUtils.isEmpty(stasticConfigInfoForm.getShowActivityTypeList())) {
            if (org.apache.commons.lang.StringUtils.isBlank(activityTypes)) {
                activityTypes = stasticConfigInfoForm.getShowActivityTypes();
            }
            List<String> showActivityTypesTypes = Arrays.asList(activityTypes.split(","));
            for (ActivityTypeForm activityTypeForm : stasticConfigInfoForm.getShowActivityTypeList()) {
                if (showActivityTypesTypes.contains(activityTypeForm.getActivityId())) {
                    MeetingTypeForm meetingTypeForm =
                            MeetingTypeForm.builder()
                                    .typeId(Long.valueOf(activityTypeForm.getActivityId()))
                                    .typeName(activityTypeForm.getActivityName())
                                    .build();
                    orgLifeTypes.add(meetingTypeForm);
                }
            }
        }
        return orgLifeTypes;
    }
}
