package com.goodsogood.ows.service.sas;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.db.user.OptionEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.datav.others.PartyPositionsForm;
import com.goodsogood.ows.model.vo.sas.*;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;
import lombok.val;
import org.apache.arrow.flatbuf.Int;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 远程调用其他服务接口-服务层
 *
 * <AUTHOR>
 */
@Service
@Log4j2
public class OpenService {

    @Value("${tog-services.user-center}")
    private String userCenter;

    @Value("${tog-services.meeting}")
    private String meetingCenter;

    @Value("${tog-services.ppmd}")
    private String ppmd;

    @Value("${open.retry-times}")
    private Integer RETRY_TIMES = 1;

    private Integer TIMES = 0;

    @Value("${tog-services.user-center}")
    @NotNull
    private String user;


    private final RestTemplate restTemplate;
    private final StringRedisTemplate redisTemplate;

    @Autowired
    public OpenService(RestTemplate restTemplate, StringRedisTemplate redisTemplate) {
        this.restTemplate = restTemplate;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 调用用户中心查询组织列表
     * @param orgType
     * @param orgTypeChild
     * @return
     */
    /*public List<OrganizationForm> findOrgByType(Long orgType, Long orgTypeChild){
        List<OrganizationForm> orgList = new ArrayList<>();
        StringBuffer sb = new StringBuffer("http://%s/org/find-org-by-type");
        String url = null;
        if (null != orgType) {
            sb.append("?org_type=%s");
            if (null != orgTypeChild) {
                sb.append("&org_type_child=%s");
                url = String.format(sb.toString(),userCenter, orgType, orgTypeChild);
            } else {
                url = String.format(sb.toString(),userCenter, orgType);
            }
        } else {
            if (null != orgTypeChild) {
                sb.append("?org_type_child=%s");
            }
            url = String.format(sb.toString(),userCenter, orgTypeChild);
        }
        HttpHeaders headers = new HttpHeaders();
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        // 调用远程方法
        try {
            orgList = RemoteApiHelper.get(this.restTemplate, url, headers, new TypeReference<Result<List<OrganizationForm>>>(){});
            log.debug("远程调用UserCenter查询组织类型为[{},{}]的组织列表: [{}]", orgType, orgTypeChild, orgList);
        } catch (Exception e) {
            log.error("调用远程服务失败, 错误内容:", e);
            // 失败有重试机制
            if(this.queryTimes()){
                log.debug("重试次数还剩下: [{}]", RETRY_TIMES-TIMES);
                this.findOrgByType(orgType, orgTypeChild);
            }
        }
        return orgList;
    }*/

    /**
     * 查询领导板子所有成员
     * @return
     */
/*    public List<SasLeaderOrgForm> findAllLeader(){
        List<SasLeaderOrgForm> list = new ArrayList<>();
        // 远程调用地址
        String url = String.format("http://%s/leader/findAllLeader", userCenter);
        // 请求头部
        HttpHeaders headers = new HttpHeaders();
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        // 调用远程方法
        try {
            list = RemoteApiHelper.get(this.restTemplate, url, headers, new TypeReference<Result<List<SasLeaderOrgForm>>>(){});
            log.debug("远程调用USER-CENTER查询领导干部列表: [{}]", list);
        } catch (Exception e) {
            log.error("调用远程服务失败, 错误内容:", e);
            // 失败有重试机制
            if(this.queryTimes()){
                log.debug("重试次数还剩下: [{}]", RETRY_TIMES-TIMES);
                this.findAllLeader();
            }
        }
        return list;
    }*/

    /**
     * 得到支委会信息
     * @return
     */
/*    public List<PeriodFindOrgsResultForm> getPeriodInfo(List<PeriodForm> form){
        List<PeriodFindOrgsResultForm> list = new ArrayList<>();
        // 远程调用地址
        String url = String.format("http://%s/period/find-orgs", userCenter);
        // 请求头部
        HttpHeaders headers = new HttpHeaders();
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        // 调用远程方法
        try {
            list = RemoteApiHelper.post(this.restTemplate, url,form, headers, new TypeReference<Result<List<PeriodFindOrgsResultForm>>>(){});
            log.debug("远程调用USER-CENTER查询支委会信息: [{}]", list);
        } catch (Exception e) {
            log.error("远程调用USER-CENTER查询支委会信息, 错误内容:", e);
            // 失败有重试机制
            if(this.queryTimes()){
                log.debug("重试次数还剩下: [{}]", RETRY_TIMES-TIMES);
                this.findAllLeader();
            }
        }
        return list;
    }*/

    /**
     * 查询组织党费交纳情况
     * @param form
     * @return
     */
/*        public List<SasOrgPaySituationForm> getOrgPaySituation(SasOrgPayForm form){
            List<SasOrgPaySituationForm> list = new ArrayList<>();
            // 远程调用地址
            String url = String.format("http://%s/ppmd/stats/org/pay", ppmd);
            // 请求头部
            HttpHeaders headers = new HttpHeaders();
            this.restTemplate.setErrorHandler(new ClientExceptionHandler());
            // 调用远程方法
            try {
                list = RemoteApiHelper.post(this.restTemplate, url, form, headers, new TypeReference<Result<List<SasOrgPaySituationForm>>>(){});
                log.debug("远程调用PPMD查询组织党费情况: [{}]", list);
            } catch (Exception e) {
                log.error("调用远程服务失败, 错误内容:", e);
                // 失败有重试机制
                if(this.queryTimes()){
                    log.debug("重试次数还剩下: [{}]", RETRY_TIMES-TIMES);
                    this.getOrgPaySituation(form);
                }
            }
            return list;
        }*/


    /**
     * 调用组织生活模块查询组织生活信息
     * 组织id
     * 不传staCode 默认查询6个月的数据
     * @return
     */
/*    public List<StatisticsMeetingForm> getOrganizeStaInfo(Long orgId,Long staCode,Long queryStatusCode){
        List<StatisticsMeetingForm> meetingList = new ArrayList<>();
        String formatUrl = String.format("http://%s/statistics/staOrganize?org_id=%s&sta_code=%s&query_status_code=%s",meetingCenter,orgId,staCode==null?0:staCode,queryStatusCode);
        HttpHeaders headers = new HttpHeaders();
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        // 调用远程方法
        try {
            meetingList = RemoteApiHelper.get(restTemplate, formatUrl, headers, new TypeReference<Result<List<StatisticsMeetingForm>>>(){});
            log.debug("调用组织生活查询组织生活信息[{}]的统计列表: [{}]", orgId, meetingList);
        } catch (Exception e) {
            log.error("调用远程服务失败, 错误内容:", e);
        }
        return meetingList;
    }*/

    /**
     * 得到所有的活动类型信息
     * @return
     */
/*    public List<TypeAllResultForm> getMeetingTypeInfo(){
        List<TypeAllResultForm> meegtingTypes = new ArrayList<>();
        String formatUrl = String.format("http://%s/type/list-all",meetingCenter);
        HttpHeaders headers = new HttpHeaders();
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        // 调用远程方法
        try {
            meegtingTypes = RemoteApiHelper.get(restTemplate, formatUrl, headers, new TypeReference<Result<List<TypeAllResultForm>>>(){});
            log.debug("查询组活动类型[{}]的统计列表: [{}]", meegtingTypes);
        } catch (Exception e) {
            log.error("调用查询组活动类型远程服务失败, 错误内容:", e);
        }
        return meegtingTypes;
    }*/


    /**
     * 查询用户参与党组织生活的情况
     * 不传staCode 默认查询6个月的数据
     * @return
     */
/*    public List<StatisticsMeetingForm> getStatisticsOrganizeUserInfo(Long userId,Long staCode, String signStatus){
        List<StatisticsMeetingForm> meegtingList = new ArrayList<>();
        String requestUrl = String.format("http://%s/statistics/staOrganizeUser?user_id=%s&sta_code=%s&sign_status=%s", meetingCenter, userId, staCode==null?0:staCode, signStatus);
        HttpHeaders headers = new HttpHeaders();
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        try {
            meegtingList = RemoteApiHelper.get(restTemplate, requestUrl, headers, new TypeReference<Result<List<StatisticsMeetingForm>>>(){});
            log.debug("调用用户[{}]参与组织生活的统计列表: [{}]",userId, meegtingList);
        } catch (Exception e) {
            log.error("调用远程服务失败, 错误内容:", e);
        }
        return meegtingList;
    }*/

    /**
     * 查询用户中心组织列表
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/4/22 16:56
     */

/*    public List<OptionEntity> getOptionList(){
        List<OptionEntity> list = new ArrayList<>();
        StringBuffer sb = new StringBuffer("http://%s/uc/op/list?code=%s");
        String url = String.format(sb.toString(),userCenter, Constants.ORG_TYPE_COMMUNIST);
        HttpHeaders headers = new HttpHeaders();
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        // 调用远程方法
        try {
            list = RemoteApiHelper.get(restTemplate, url, headers, new TypeReference<Result<List<OptionEntity>>>(){});
            log.debug("远程调用UserCenter查询组织类列表[{}]",list);
        } catch (Exception e) {
            log.debug("调用远程服务失败, 错误内容:", e);
        }
        return list;
    }*/

/*    public List<TypeAllResultForm> getActivityList(){
        List<TypeAllResultForm> list =null;
        StringBuffer sb = new StringBuffer("http://%s/type/list-all");
        String url = String.format(sb.toString(),meetingCenter);
        HttpHeaders headers = new HttpHeaders();
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        // 调用远程方法
        try {
            list = RemoteApiHelper.get(restTemplate, url, headers, new TypeReference<Result<List<TypeAllResultForm>>>(){});
            log.debug("远程调用活动中心查询活动列表[{}]",list);
        } catch (Exception e) {
            log.error("调用远程服务失败, 错误内容:", e);
        }
        return list;
    }*/
    private boolean queryTimes() {
        if (TIMES >= RETRY_TIMES) {
            TIMES = 0;
            return false;
        } else {
            TIMES++;
            return true;
        }
    }

    /**
     * 调用用户中心，根据支部编号获取党员你列表
     * regionId 区县编号
     * branchId   支部编号
     *
     * <AUTHOR>
     */
    public List<UserInfoBase> getUserListByOrgId(Long orgId, Integer isEmployee, HttpHeaders headers) {
        log.debug("调用用户中心，根据支部编号获取党员你列表  orgId=${orgId}");
        val header = HeaderHelper.buildMyHeader(headers);
        List<UserInfoBase> re = new ArrayList<>();
        FindOrgListForm form = new FindOrgListForm();
        form.setIdList(Arrays.asList(orgId));
        form.setIsEmployee(isEmployee);
        form.setIsFilter(1);
        form.setIsInclude(1);
        form.setRegionId(header.getRegionId());
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        int count = 0;
        do {
            try {
                re = RemoteApiHelper.post(restTemplate, String.format(
                        "http://%s/find-user-by-org-id",
                        userCenter
                ), form, headers, new TypeReference<>() {
                });
            } catch (Exception e) {
                log.error("<根据支部编号获取党员你列表>失败！ orgId=${orgId} 第${count + 1}次调用", e);
            }
            count++;
        } while (re.isEmpty() && count < 5);
        log.debug("<根据支部编号获取党员你列表>结果:查询 orgId=${orgId} 结果 re =${re} 调用数${count}次");
        return re;
    }


    /**
     * 获取组织信息
     *
     * @param orgId
     * @param headers
     * @return org
     */
    public OrganizationBase findOrgById(Long orgId, HttpHeaders headers) {
        OrganizationBase organizationBase = new OrganizationBase();
        // 远程调用地址
        String url = String.format("http://%s/find-org-by-id?org_id=%s&timestamp=%d", user, orgId, System.currentTimeMillis());
        log.debug("请求用户中心URL -> [{}]", url);
        // 调用远程方法
        try {
            organizationBase = RemoteApiHelper.getAndResult(restTemplate, url, headers, new TypeReference<Result<OrganizationBase>>() {
            }).getData();
        } catch (IOException e) {
            throw new ApiException(e.getMessage(), e, null);
        }
        return organizationBase;
    }

    /**
     * 批量获取组织信息
     *
     * @param orgIds
     * @param headers
     * @return list of org
     */
    public List<OrganizationBase> findOrgByIds(List<Long> orgIds, HttpHeaders headers) {
        List<OrganizationBase> organizationList;
        // 远程调用地址
        String url = String.format("http://%s/find-org-by-ids", user);
        // 调用远程方法
        try {
            organizationList = RemoteApiHelper.postAndResult(restTemplate, url, orgIds, headers, new TypeReference<Result<List<OrganizationBase>>>() {
            }).getData();
        } catch (IOException e) {
            throw new ApiException(e.getMessage(), e, null);
        }
        return organizationList;
    }

    /**
     * 获取当前组织id下的第一层组织id、名称、所属单位id和名称
     *
     * @param orgId   组织id
     * @param headers 请求头
     * @return orgs
     */
    public List<OrganizationBase> findLevelOneOrgs(Long orgId, HttpHeaders headers) {
        List<OrganizationBase> organizationBaseList;

        // 远程调用地址
        String url = String.format("http://%s/org/find-child-org-by-id?org_id=%s&year=%d&month=%d", user, orgId, LocalDate.now().getYear(), LocalDate.now().getMonthValue());
        // 调用远程方法
        try {
            organizationBaseList = RemoteApiHelper.getAndResult(restTemplate, url, headers, new TypeReference<Result<List<OrganizationBase>>>() {
            }).getData();
        } catch (IOException e) {
            throw new ApiException(e.getMessage(), e, null);
        }
        return organizationBaseList;
    }

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrgBaseInfo {
        public Long orgId = null;
        public String name = null;
        public String shortName = null;
        public Integer seq = 0;
    }

    public List<OrgBaseInfo> getCorps(HttpHeaders headers) {
        List<OrgBaseInfo> orgBaseInfoList = new ArrayList<>();
        // 远程调用地址
        String url = String.format("http://%s/corp/query-list?page=%d&page_size=%d", user, 1, 9999);
        // 调用远程方法
        try {
            orgBaseInfoList = RemoteApiHelper.getAndResult(restTemplate, url, headers, new TypeReference<Result<List<OrgBaseInfo>>>() {
            }).getData();
        } catch (IOException e) {
            throw new ApiException(e.getMessage(), e, null);
        }
        return orgBaseInfoList;
    }

    @ApiModel
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OrgByCorpForm {
        @JsonProperty("all_org")
        List<Long> allOrg = new ArrayList<>();

        @JsonProperty("branch_org")
        List<Long> branchOrg = new ArrayList<>();

        public List<Long> getAllOrg() {
            return allOrg;
        }

        public void setAllOrg(List<Long> allOrg) {
            this.allOrg = allOrg;
        }

        public List<Long> getBranchOrg() {
            return branchOrg;
        }

        public void setBranchOrg(List<Long> branchOrg) {
            this.branchOrg = branchOrg;
        }
    }

    /**
     * /org/find-org-by-corp
     *
     * @param corpId  单位id
     * @param headers 请求头
     * @return list of OrgByCorpForm
     */
    public OrgByCorpForm getOrgByCorp(Long corpId, HttpHeaders headers) {
        OrgByCorpForm orgs = new OrgByCorpForm();
        // 远程调用地址
        String url = String.format("http://%s/org/find-org-by-corp?corpId=%s", user, corpId);
        // 调用远程方法
        try {
            orgs = RemoteApiHelper.getAndResult(restTemplate, url, headers, new TypeReference<Result<OrgByCorpForm>>() {
            }).getData();
        } catch (IOException e) {
            throw new ApiException(e.getMessage(), e, null);
        }
        return orgs;
    }


    /**
     * 通过支部id获取党小组
     *
     * @param branchIds 支部id数组
     * @param headers   请求头
     * @return list of 党小组id
     */
    public List<Long> getOrgIdByBranchIds(List<Long> branchIds, HttpHeaders headers) {
        List<Long> orgIds;
        // 拼凑查询条件   branch_ids=1&branch_ids=2&branch_ids=3
        var query = branchIds.stream().map(it -> "branch_ids=" + it).collect(Collectors.joining("&"));
        // 远程调用地址
        String url = String.format("http://%s/org-group/find/orgId/by/branchIds?%s", user, query);
        // 调用远程方法
        try {
            orgIds = RemoteApiHelper.getAndResult(restTemplate, url, headers, new TypeReference<Result<List<Long>>>() {
            }).getData();
        } catch (IOException e) {
            throw new ApiException(e.getMessage(), e, null);
        }
        return orgIds;
    }

    public PartyPositionsForm getPartyPosition(Long orgId, HttpHeaders headers) {
        //党建阵地 https://dangjian.cq.tobacco.gov.cn/zuul/owsz/user/partyPositions/selectExcellent?org_id=3&type=1&page=1&page_size=6
        var urlPosition =
                "http://" + user + "/partyPositions/selectExcellent?org_id=" + orgId + "&type=1&page=1&page_size=1";
        // 调用远程方法
        try {
            return RemoteApiHelper.getAndResult(restTemplate, urlPosition, headers, new TypeReference<Result<PartyPositionsForm>>() {
            }).getData();
        } catch (IOException e) {
            throw new ApiException(e.getMessage(), e, null);
        }
    }
}
