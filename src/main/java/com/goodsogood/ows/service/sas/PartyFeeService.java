package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.model.db.sas.StatisticalDataRecordEntity;
import com.goodsogood.ows.model.db.sas.StatisticalPartyFeeEntity;
import com.goodsogood.ows.model.vo.sas.OrganizationForm;
import com.goodsogood.ows.model.vo.sas.SasOrgPayForm;
import com.goodsogood.ows.model.vo.sas.SasOrgPaySituationForm;
import com.goodsogood.ows.service.ppmd.OrgPayService;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 党费统计定时任务服务端
 * <AUTHOR>
 */
@Service
@Log4j2
public class PartyFeeService {

    /**  党费补交统计时间(month) */
    @Value("${ppmd.un-pay.time}")
    private Integer timeLimit = 12;

    /** 起始下标 */
    private static final Integer START = 0;

    /** 每次查询条数 */
    @Value("${ppmd.un-pay.size}")
    private Integer SIZE = 10;

    private final OrgPayService orgPayService;
    private final OrgService orgService;
    private final StatisticalDataRecordService recordService;
    private final StatisticalPartyFeeService partyFeeService;

    private List<OrganizationForm> ORG_LIST;

    private final Calendar calendar = Calendar.getInstance();
    private final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM");

    @Autowired
    public PartyFeeService(OrgPayService orgPayService,
                           OrgService orgService, StatisticalDataRecordService recordService,
                           StatisticalPartyFeeService partyFeeService){
        this.orgPayService = orgPayService;
        this.orgService = orgService;
        this.recordService = recordService;
        this.partyFeeService = partyFeeService;
    }

    /**
     * 组织党费缴纳请款统计主方法
     * @param queryMouth
     * @param date
     * @param stopWatch
     * @throws ParseException
     */
    public void OrgPayMain(String queryMouth, Date date, boolean isOneMonth,
                           StopWatch stopWatch, int pageSize, Long regionId) throws ParseException {
        // 查询党组织列表
        log.debug("长度: [{}]", SIZE);
        ORG_LIST = this.orgService.findOrgByType(Constants.ORG_TYPE_COMMUNIST, null, regionId);
        log.debug("获取党组织耗时 -> [{}]", stopWatch.toString());
        List<Long> orgIds = new ArrayList<>(ORG_LIST.size());
        // 初始化需要更改每次查询条数
        if (pageSize != 0) {
            SIZE = pageSize;
        }
        // 循环组织查询组织党费交纳情况
        if (!CollectionUtils.isEmpty(ORG_LIST)) {
            for (OrganizationForm org :ORG_LIST) {
                orgIds.add(org.getOrganizationId());
            }
            // 分批对组织数据进行查询党费信息
            this.batchQueryOrgPay(orgIds, START, queryMouth, date, isOneMonth, stopWatch);
        }
        log.debug("循环查询党组织党费情况完成，耗时 -> [{}]", stopWatch.toString());
        if(!isOneMonth) {
            // 查询党费欠交组织
            this.updateUnPayData(queryMouth, date, stopWatch);
        }
    }

    /**
     * 组织党费缴纳请款统计主方法 补某个组织党费纪录
     * @param queryMouth
     * @param date
     * @param stopWatch
     * @throws ParseException
     */
    public void OrgPayMain(String queryMouth, Date date, boolean isOneMonth,
                           StopWatch stopWatch, int pageSize, Long regionId,
                           Long orgId) throws ParseException {
        // 查询党组织列表
        log.debug("长度: [{}]", SIZE);
        ORG_LIST = this.orgService.findOrgByType(Constants.ORG_TYPE_COMMUNIST, null, regionId);
        ORG_LIST = ORG_LIST.stream().filter(item -> orgId.
                equals(item.getOrganizationId())).collect(Collectors.toList());
        log.debug("获取党组织耗时 -> [{}]", stopWatch.toString());
        List<Long> orgIds = new ArrayList<>(ORG_LIST.size());
        // 初始化需要更改每次查询条数
        if (pageSize != 0) {
            SIZE = pageSize;
        }
        // 循环组织查询组织党费交纳情况
        if (!CollectionUtils.isEmpty(ORG_LIST)) {
            for (OrganizationForm org :ORG_LIST) {
                orgIds.add(org.getOrganizationId());
            }
            // 分批对组织数据进行查询党费信息
            this.batchQueryOrgPay(orgIds, START, queryMouth, date, isOneMonth, stopWatch);
        }
        log.debug("循环查询党组织党费情况完成，耗时 -> [{}]", stopWatch.toString());
        if(!isOneMonth) {
            // 查询党费欠交组织
            this.updateUnPayData(queryMouth, date, stopWatch);
        }
    }


    /**
     * 为了防止数据量过大，接口超时，对数据进行分批操作
     * @param orgIds
     * @param start
     * @param queryMouth
     * @param date
     * @param stopWatch
     */
    private void batchQueryOrgPay(List<Long> orgIds, int start, String queryMouth, Date date, boolean isOneMonth, StopWatch stopWatch) throws ParseException {
        // 本次循环最大值
        int end = start + SIZE;
        // 总条数
        int size = orgIds.size();
        if (end >= size) {
            end = size;
        }
        List<Long> orgIdList = new ArrayList<>(SIZE);
        // 循环操作
        for (int i = start; i < end; i++) {
            orgIdList.add(orgIds.get(i));
            start = i + 1;
        }
        //调用查询
        this.getPayMonthData(orgIdList, queryMouth, date, isOneMonth, stopWatch);
        // 判断是否运行完毕
        if (start != size){
            this.batchQueryOrgPay(orgIds, start, queryMouth, date, isOneMonth, stopWatch);
        }
    }

    /**
     * 获取当前数据，并写入大数据表,统计后新增或更新到最终库中
     * @param orgIds
     * @param queryMouth
     * @param date
     */
    private void getPayMonthData(List<Long> orgIds, String queryMouth, Date date, boolean isOneMonth, StopWatch stopWatch) throws ParseException {
        log.info("本次查询组织IDS -> [{}], 耗时 -> [{}]", orgIds, stopWatch.toString());
        // 查询当前月份的组织党费交纳情况
        SasOrgPayForm form = new SasOrgPayForm();
        form.setOrgIds(orgIds);
        form.setDate(queryMouth);
        form.setOneMonth(isOneMonth);
        List<SasOrgPaySituationForm> paySituations = this.orgPayService.getOrgPaySituation(form);
        log.debug("获取党组织党费缴纳信息耗时 -> [{}]", stopWatch.toString());
        // 循环存库
        if (!CollectionUtils.isEmpty(paySituations)) {
            log.debug("当前时间为 -> [{}]", date);
            // 需要新增列表
            for (SasOrgPaySituationForm paySit : paySituations) {
                Long orgId = paySit.getOrgId();
                String sitDate = paySit.getDate();
                int year = this.getYearOrgMonth(sitDate, Calendar.YEAR);
                int month = this.getYearOrgMonth(sitDate, Calendar.MONTH);
                int payAbleNum = paySit.getPayAbleNum();
                int unPayNum = paySit.getUnPayNum();
                int evalUnPayNum = paySit.getEvalUnPayNum();
                log.debug("组织[{}]的党费缴纳情况 -> 应交人数 : [{}], 未交人数 : [{}]", orgId, payAbleNum, unPayNum);
                StatisticalDataRecordEntity data = new StatisticalDataRecordEntity();
                // 获取组织详细信息
                OrganizationForm org = this.getOrgInfo(ORG_LIST, orgId);
                log.debug("组织[{}]的详细信息 -> [{}]", orgId, org);
                if (org != null) {
                    // 新增流水表
                    data.setOrgId(org.getOrganizationId());
                    data.setOrgName(org.getName());
                    data.setOrgTypeId(org.getOrgTypeChild());
                    data.setParentOrgId(org.getParentId());
                    data.setOrgLevel(org.getOrgLevel());
                    data.setIsRetire(org.getIsRetire());
                    data.setDataType(Constants.PARTY_PAY_TYPE);
                    data.setStatisticalYear(year);
                    data.setStatisticalMonth(month);
                    data.setStatisticalDate(queryMouth);
                    data.setPayablePersonNum(payAbleNum);
                    data.setUnpaidPersonNum(unPayNum);
                    data.setCreateTime(date);
                    data.setStatus(Constants.STATUS_YES);
                    this.recordService.insert(data);
                    // 党费统计数据入库
                    this.storage(org, sitDate, unPayNum, payAbleNum, evalUnPayNum, date, year, month);
                }
            }
            log.debug("统计数据, 并且存库. 耗时 -> [{}]", stopWatch.toString());
        }
    }

    /**
     * 党费统计数据入库
     * @param org
     * @param queryMouth
     * @param unPayNum
     * @param ableNum
     * @param date
     * @param year
     * @param month
     */
    private void storage(OrganizationForm org, String queryMouth, int unPayNum, int ableNum, int evalUnPayNum, Date date, int year, int month){
        // 查询党费统计表是否存在当月数据
        StatisticalPartyFeeEntity feeEntity = this.partyFeeService.getInfoByMouth(org.getOrganizationId(), queryMouth);
        // 判断组织是否存在党费数据
        if (null != feeEntity && null != feeEntity.getCreateTime()) {
            // 数据库存在, 数据库进行更新操作
            // 判断是否是当月数据, 若是，则需要更新组织信息，若为历史数据，则不更新组织信息数据
            String formatDate = formatter.format(date);
            if (formatDate.equals(queryMouth)) {
                feeEntity.setOrgName(org.getName());
                feeEntity.setOrgLevel(org.getOrgLevel());
                feeEntity.setOrgTypeId(org.getOrgTypeChild());
                feeEntity.setParentOrgId(org.getParentId());
                feeEntity.setIsRetire(org.getIsRetire());
            }
            feeEntity.setOrgCreateTime(org.getOrgCreateTime());
            feeEntity.setPayablePersonNum(ableNum);
            feeEntity.setUnpaidPersonNum(unPayNum);
            feeEntity.setEvalUnpaidPersonNum(evalUnPayNum);
            feeEntity.setUpdateTime(date);
            this.partyFeeService.update(feeEntity);
        } else {
            // 数据库不存在，需要新增操作
            feeEntity = new StatisticalPartyFeeEntity();
            feeEntity.setOrgId(org.getOrganizationId());
            //存入区县Id
            feeEntity.setRegionId(orgService.getById(org.getOrganizationId()).getRegionId());
            feeEntity.setOrgName(org.getName());
            feeEntity.setOrgTypeId(org.getOrgTypeChild());
            feeEntity.setOrgLevel(org.getOrgLevel());
            feeEntity.setParentOrgId(org.getParentId());
            feeEntity.setIsRetire(org.getIsRetire());
            feeEntity.setOrgCreateTime(org.getOrgCreateTime());
            feeEntity.setPayablePersonNum(ableNum);
            feeEntity.setUnpaidPersonNum(unPayNum);
            feeEntity.setStatisticalYear(year);
            feeEntity.setStatisticalMonth(month);
            feeEntity.setStatisticalDate(queryMouth);
            feeEntity.setStatus(Constants.STATUS_YES);
            feeEntity.setCreateTime(date);
            feeEntity.setEvalUnpaidPersonNum(evalUnPayNum);
            this.partyFeeService.insert(feeEntity);
        }
    }

    /**
     * 重新统计之前欠交党费情况
     * @param queryMouth
     * @param date
     * @throws ParseException
     */
    private void updateUnPayData(String queryMouth, Date date, StopWatch stopWatch) throws ParseException {
        // 查询党费欠交组织
        List<StatisticalPartyFeeEntity> unPayInfo = this.partyFeeService.getUnPayInfo(queryMouth, timeLimit);
        log.debug("查询欠交党费组织信息 -> [{}], 耗时 -> [{}]", unPayInfo, stopWatch.toString());
        // 循环查询欠交组织的缴纳情况
        if (!CollectionUtils.isEmpty(unPayInfo)) {
            for (StatisticalPartyFeeEntity entity : unPayInfo) {
                // 查询组织欠缴情况
                List<Long> orgs = new ArrayList<>(1);
                orgs.add(entity.getOrgId());
                SasOrgPayForm form = new SasOrgPayForm();
                form.setOrgIds(orgs);
                form.setDate(entity.getStatisticalDate());
                form.setOneMonth(true);
                List<SasOrgPaySituationForm> paySituations = this.orgPayService.getOrgPaySituation(form);
                if (null != paySituations && paySituations.size() > 0) {
                    SasOrgPaySituationForm sitForm = paySituations.get(0);
                    log.debug("查询组织[{}]在[{}]时间的党费缴纳情况 -> 应交人数 : [{}], " +
                                    "未交人数 : [{}]", entity.getOrgId(),
                            entity.getStatisticalDate(), sitForm.getPayAbleNum(), sitForm.getUnPayNum());
                    // 判断数据是否有更新
                    if (!entity.getUnpaidPersonNum().equals(sitForm.getUnPayNum())) {
                        entity.setPayablePersonNum(sitForm.getPayAbleNum());
                        entity.setUnpaidPersonNum(sitForm.getUnPayNum());
                        entity.setEvalUnpaidPersonNum(sitForm.getEvalUnPayNum());
                        entity.setUpdateTime(date);
                        this.partyFeeService.update(entity);
                    }
                }
            }
        }
        log.info("处理欠交数据, 并且更新数据. 耗时 -> [{}]", stopWatch.toString());
    }

    /**
     * 查询组织详细信息
     * @param orgList
     * @param orgId
     * @return
     */
    private OrganizationForm getOrgInfo(List<OrganizationForm> orgList, Long orgId){
        for (OrganizationForm org : orgList) {
            if (org.getOrganizationId().equals(orgId)) {
                return org;
            }
        }
        return null;
    }

    /**
     * 获取当前
     * @param queryDate
     * @param field 1-年份, 2-月份
     * @return
     * @throws ParseException
     */
    private int getYearOrgMonth(String queryDate, int field) throws ParseException{
        // 获取当前年月
        calendar.setTime(formatter.parse(queryDate));
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        if (field == Calendar.YEAR) {
            return year;
        } else if (field == Calendar.MONTH) {
            return month;
        }
        return 0;
    }
}
