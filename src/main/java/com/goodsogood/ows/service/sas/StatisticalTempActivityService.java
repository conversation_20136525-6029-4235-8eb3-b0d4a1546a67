package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.mapper.sas.StatisticalTempActivityMapper;
import com.goodsogood.ows.model.db.sas.StatisticalTempActivityEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @create 2019-05-05 09:05
 **/
@Service
@Log4j2
public class StatisticalTempActivityService {

    private final StatisticalTempActivityMapper statisticalTempActivityMapper;

    @Autowired
    public StatisticalTempActivityService(StatisticalTempActivityMapper statisticalTempActivityMapper) {
        this.statisticalTempActivityMapper = statisticalTempActivityMapper;
    }

    /**
     * 得到活动类型名称
     * @param activityId
     * @return
     */
    public  String getActivityName(Integer activityId){
        Example example = new Example(StatisticalTempActivityEntity.class);
        example.createCriteria()
                .andEqualTo("activityId", activityId);
        StatisticalTempActivityEntity tempActivityEntity = statisticalTempActivityMapper.selectOneByExample(example);
        if(tempActivityEntity==null){
            return  null;
        }else {
            return tempActivityEntity.getTypeName();
        }
    }

}