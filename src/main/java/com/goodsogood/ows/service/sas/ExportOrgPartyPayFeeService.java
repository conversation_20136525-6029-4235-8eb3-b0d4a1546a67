package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.model.vo.sas.SasOrgLifeConditionForm;
import com.goodsogood.ows.model.vo.sas.StatisticalPartyFeeForm;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 导出组织党费缴纳情况
 * <AUTHOR>
 */
@Service
@Log4j2
public class ExportOrgPartyPayFeeService {

    private static final String excelTitle = "%s%s年%s党费交纳完成情况统计";
    private static final String XLS_SUFFIX = "xls";

    /**
     *
     * @param response
     * @param conditionForm
     * @param list
     */
    public void exportPayFee(HttpServletResponse response, SasOrgLifeConditionForm conditionForm, List<StatisticalPartyFeeForm> list){
        // 查询几月份
        String dateStr = conditionForm.getDateStr();
        String[] months = dateStr.split(",");
        //获取标题
        String title = this.getTitle(conditionForm);
        // 计算总共多少列 (展示数据时，一个月份需要展示 -> "应交人数", "缴纳情况", 因此占位2个单元格, 表态还包含序号和组织)
        int colSize = months.length * 2 + 2;
        // 保存第一列表头
        List<String> headOne = new ArrayList<>(colSize);
        // 保存第二列表头
        List<String> headTwo = new ArrayList<>(colSize);
        headOne.add("序号");
        headTwo.add("序号");
        headOne.add("组织名称");
        headTwo.add("组织名称");
        for (String month : months) {
            for (int i = 0; i < 2; i++) {
                headOne.add(month + "月");
            }
            headTwo.add("应交人数");
            headTwo.add("缴纳情况");
        }
        try {
            this.generateExcel(response, title, colSize, headOne, headTwo, list, months);
        } catch (Exception e) {
            log.error("导出党费统计信息失败，", e);
        }
    }

    /**
     * 生成Excel
     * @param response
     * @param title
     * @param colSize
     * @param headOne
     * @param headTwo
     */
    private void generateExcel(HttpServletResponse response, String title, int colSize, List<String> headOne, List<String> headTwo,
                               List<StatisticalPartyFeeForm> list, String[] months) throws Exception {
        Workbook wb = new HSSFWorkbook();
        // 创建一个表
        Sheet sheet = wb.createSheet();
        this.setTableHead(wb, sheet, title, headOne, headTwo, colSize);
        this.setDate(list, wb, sheet, months, colSize);
        try {
            if (wb != null) {
                // 设置响应头导出文件格式
//                String contentDisposition = "attachment;filename=" + new String(cName.getBytes("UTF-8"),"ISO-8859-1")+"." + ExcelUtils.XLSX_SUFFIX;
                String contentDisposition = "attachment;filename=" + URLEncoder.encode(title, "UTF-8") + "." + XLS_SUFFIX;
                // 设置响应头的文件名称信息
                response.setHeader("Content-Disposition", contentDisposition);
                // 设置响应头导出文件格式vnd.ms-excel
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("UTF-8");
                // 使用响应的输出流导出excel文件
                wb.write(response.getOutputStream());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void setDate(List<StatisticalPartyFeeForm> list, Workbook wb, Sheet sheet, String[] months, int colSize) throws Exception {
        // 设置正文样式
        CellStyle textStyle = this.setStyle(wb, "宋体", 12, false);
        Row row;
        Cell cell;
        for (int i = 0; i < list.size(); i++) {
            int col = 0;
            StatisticalPartyFeeForm entity = list.get(i);
            row = sheet.createRow(i + 3);
            Map<String, Object> map = JsonUtils.bean2map(entity);
            // 序号
            cell = row.createCell(col++);
            cell.setCellStyle(textStyle);
            cell.setCellValue(i + 1);
            // 组织名称
            cell = row.createCell(col++);
            cell.setCellStyle(textStyle);
            cell.setCellValue(entity.getOrgName());
            // 数据
            for (int j = 0; j < months.length; j++) {
                String fees = (String)map.get("month" + months[j]);
                if (!StringUtils.isEmpty(fees)) {
                    String[] fee = fees.split("-");
                    for (int k = 0; k < fee.length; k++) {
                        cell = row.createCell(col++);
                        cell.setCellStyle(textStyle);
                        if (k == 0) {
                            cell.setCellValue(fee[k]);
                        } else {
                            cell.setCellValue(fee[k] + "人未交");
                        }
                    }
                } else {
                    for (int k = 0; k < 2; k++) {
                        cell = row.createCell(col++);
                        cell.setCellStyle(textStyle);
                        if (k == 0) {
                            cell.setCellValue(0);
                        } else {
                            cell.setCellValue("0人未交");
                        }
                    }
                }
            }
        }
    }
    /**
     * 设置表格头部
     * @param wb
     * @param sheet
     * @param title
     * @param headOne
     * @param headTwo
     * @param colSize
     */
    private void setTableHead(Workbook wb, Sheet sheet, String title, List<String> headOne, List<String> headTwo, int colSize){
        // 设置大标题样式
        CellStyle bigTitleStyle = this.setStyle(wb, "宋体", 16, true);
        // 设置大标题样式
        CellStyle smallTitleStyle = this.setStyle(wb, "宋体", 12, true);
        // 设置大标题
        Row bigTitleRow = sheet.createRow(0);
        Cell bigTitleCell = bigTitleRow.createCell(0);
        bigTitleCell.setCellValue(title);
        bigTitleCell.setCellStyle(bigTitleStyle);
        // 表头合并单元格{"开始行,结束行,开始列,结束列"}
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, colSize-1));
        // 小标题-1
        Row smallTitleRowOne = sheet.createRow(1);
        for (int i = 0; i < headOne.size(); i++) {
            String head = headOne.get(i);
            Cell smallTitleCell = smallTitleRowOne.createCell(i);
            smallTitleCell.setCellStyle(smallTitleStyle);
            smallTitleCell.setCellValue(head);
            if (i > 1 && i % 2 == 0) {
                sheet.addMergedRegion(new CellRangeAddress(1, 1,i,i + 1));
            }
            if (i == 1) {
                sheet.setColumnWidth(i, 15000);
            }
        }
        // 小标题-2
        Row smallTitleRowTwo = sheet.createRow(2);
        for (int i = 0; i < headTwo.size(); i++) {
            String head = headTwo.get(i);
            Cell smallTitleCell = smallTitleRowTwo.createCell(i);
            smallTitleCell.setCellStyle(smallTitleStyle);
            smallTitleCell.setCellValue(head);
            if (i > 1) {
                sheet.setColumnWidth(i, 4000);
            }
        }
        sheet.addMergedRegion(new CellRangeAddress(1, 2,0,0));
        sheet.addMergedRegion(new CellRangeAddress(1, 2,1,1));
    }

    /**
     * 设置单元格样式
     * @param wb        表格
     * @param fontName  字体
     * @param point     字号
     * @param bold      是否加粗
     * @return
     */
    private CellStyle setStyle(Workbook wb, String fontName, int point, boolean bold){
        // 获取字体
        Font font = wb.createFont();
        font.setFontName(fontName);
        //font.setBoldweight(Font.BOLDWEIGHT_BOLD);
        //font.setBoldweight(Font.BOLDWEIGHT_NORMAL);
        font.setBold(bold);
        font.setFontHeightInPoints((short) point);
        // 获取样式
        CellStyle style = wb.createCellStyle();
//        // 下边框
//        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
//        // 左边框
//        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
//        //上边框
//        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
//        //右边框
//        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setFont(font);
        // 左右居中
        style.setAlignment(HorizontalAlignment.CENTER);
        // 上下居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setLocked(true);
        return style;
    }

    /**
     * 获取表格标题
     * @param conditionForm
     * @return
     */
    private String getTitle(SasOrgLifeConditionForm conditionForm) {
        //获取前段查询方式
        StringBuffer quarter = new StringBuffer();
        if (conditionForm.getTimeType().equals(Constants.SELECT_TIME_FIRST_HALF_YEAR)) {
        }else if (conditionForm.getTimeType().equals(Constants.SELECT_TIME_TYPE_MONTH)) {
            Integer startMonth = conditionForm.getStartMonth();
            Integer endMonth = conditionForm.getEndMonth();
            quarter.append(String.format("%s月~%s月", startMonth, endMonth));
        } else {
            switch (conditionForm.getTime()) {
                case 1:
                    quarter.append("上半年");
                    break;
                case 2:
                    quarter.append("下半年");
                    break;
                case 3:
                    quarter.append("第一季度");
                    break;
                case 4:
                    quarter.append("第二季度");
                    break;
                case 5:
                    quarter.append("第三季度");
                    break;
                case 6:
                    quarter.append("第四季度");
                    break;
                default:
                    break;
            }
        }
        //组织名称
        String orgName = conditionForm.getOrgName();
        // 获取年份
        Integer year = conditionForm.getYear();
        // 拼接返回
        return String.format(excelTitle, orgName, year, quarter);
    }
}
