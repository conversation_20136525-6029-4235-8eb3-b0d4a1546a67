package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.model.vo.sas.MeetingTypeForm;
import com.goodsogood.ows.model.vo.sas.SasOrgLifeForm;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 统计结果导出接口
 *
 * <AUTHOR>
 * @create 2018-04-04 17:07
 **/
@Log4j2
class StatisticalOrgLifeViewExportService {
    private final HSSFWorkbook workBook = new HSSFWorkbook();

    /**
     * 创建样式 表头样式
     */
    private HSSFCellStyle titleStyle;

    /**
     * 行样式
     */
    private HSSFCellStyle colStyle;

    private HSSFSheet sheet;

    /**
     * 导出类型
     */
    private final List<MeetingTypeForm> types;
    private final List<Map<String, Object>> data;


    StatisticalOrgLifeViewExportService(String sheetName, SasOrgLifeForm sasOrgLifeForm) {
        // 设置默认样式
        setTitleStyle();
        setColStyle();
        data = sasOrgLifeForm.getListDetail();
        types = sasOrgLifeForm.getTypes();
        defaultSheet(sheetName, bulidTitle(), sheetName);
    }

    /**
     * 获取表头
     *
     * @return String  title
     */
    private String[] bulidTitle() {
        List<String> titles = new ArrayList<>();
        titles.add("序号");
        titles.add("组织名称");
        titles.addAll(types.stream().map(MeetingTypeForm::getTypeName).collect(Collectors.toList()));
        return titles.toArray(new String[0]);
    }


    public void setTitleStyle() {
            //创建样式
            titleStyle = workBook.createCellStyle();
            //创建字体
            HSSFFont font = workBook.createFont();
            font.setFontHeightInPoints((short) 14);
            font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            titleStyle.setFont(font);
            //单元格垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            //设置背景
            titleStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.RED.getIndex());
    }

    /**
     * 行格式
     *
     */
    private void setColStyle() {
            //创建样式
            colStyle = workBook.createCellStyle();
            //创建字体
            HSSFFont font = workBook.createFont();
            colStyle.setFont(font);
            //单元格垂直居中
            colStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            colStyle.setAlignment(HorizontalAlignment.CENTER);
    }

    /**
     * 主要是用于满足基本统计数据结构的统计类型
     */
    public HSSFWorkbook export03() {
        createRows(data);
        setColumn();
        return workBook;
    }

    /**
     * 构建相同的excl表头
     *
     * @param header 统计标题
     * @param title  表头
     */
    private void defaultSheet(String header, String[] title, String sheetName) {
        sheet = workBook.createSheet(sheetName);
        if (StringUtils.isNotBlank(header)) {
            //创建合并单元格对象
            CellRangeAddress rangeAddress = new CellRangeAddress(0, 0, 0, title.length - 1);
            //添加合并区域
            sheet.addMergedRegion(rangeAddress);
            //.创建行
            HSSFRow headerRow = sheet.createRow(0);
            //.创建单元格
            HSSFCell headerCell = headerRow.createCell(0);
            headerCell.setCellValue(header);
            headerCell.setCellStyle(titleStyle);
        }
        if (title != null && title.length > 0) {
            //.创建title
            HSSFRow titleRow = sheet.createRow(1);
            for (int i = 0; i < title.length; i++) {
                HSSFCell cell = titleRow.createCell(i);
                cell.setCellValue(title[i]);
                cell.setCellStyle(titleStyle);
            }
        }
    }

    /**
     * 设置列的宽度； 256 的倍数
     */
    private void setColumn() {
        // 设置列宽
        for (int k = 0; k < types.size() + 2; k++) {
            int width;
            if (k == 1) {
                width = 60;
            } else {
                width = 24;
            }
            sheet.setColumnWidth((short) k, (short) width * 256);
        }
    }

    /**
     * 添加数据
     *
     * @param data List<T>
     */
    private void createRows(List<Map<String, Object>> data) {
        if (data != null && !data.isEmpty()) {
            // 序号
            int i = 1;
            for (Map<String, Object> t : data) {
                createRow(sheet, t, i);
                i++;
            }
        }
    }

    /**
     * 行数据
     *
     * @param data 行数据
     */
    private void createRow(HSSFSheet sheet, Map<String, Object> data, int i) {
        if (data != null) {
            // 新建行
            HSSFRow row = sheet.createRow(sheet.getLastRowNum() + 1);

            int cellNum = 0;
            // 序号
            HSSFCell ci = row.createCell(cellNum++);
            ci.setCellStyle(colStyle);
            ci.setCellValue(i);

            // 组织名称
            HSSFCell c0 = row.createCell(cellNum++);
            c0.setCellStyle(colStyle);
            c0.setCellValue(data.get("org_name").toString());
            // 统计项
            for (MeetingTypeForm meetingTypeForm : types) {
                HSSFCell cell = row.createCell(cellNum++);
                cell.setCellStyle(colStyle);
                cell.setCellValue(data.get(meetingTypeForm.getTypeId().toString()).toString());
            }
        }
    }

}
