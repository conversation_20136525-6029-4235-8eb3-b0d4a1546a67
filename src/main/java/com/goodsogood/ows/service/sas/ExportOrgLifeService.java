package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.model.db.sas.StatisticalOrgLifeEntity;
import com.goodsogood.ows.utils.JsonUtils;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import lombok.extern.log4j.Log4j2;

/**
 * 统计结果导出接口
 *
 * <AUTHOR>
 * @create 2018-04-04 17:07
 **/
@Log4j2
public class ExportOrgLifeService<T extends StatisticalOrgLifeEntity> extends ExportExcelAbstract<T> {
    /**
     * excel 表头
     */
    private static final String[] TITLE = new String[]{"序号", "组织名称", "一月", "二月", "三月", "四月", "五月", "六月"};

    /**
     * header
     */
    private static final String HEADER = "组织生活";

    /**
     * sheetName
     */
    private static final String SHEET_NAME = "组织生活";


    /**
     * 文件名称
     */
    private static final String FILE_NAME = "组织生活";


    public ExportOrgLifeService(String header, String[] titles, String sheet) {
        super(header, titles, sheet);
    }

    public ExportOrgLifeService() {
        super(HEADER, TITLE, SHEET_NAME);
    }

    /**
     * 导出expor03
     *
     * @param response HttpServletResponse
     * @param data     数据
     */
    public void export03(HttpServletResponse response, List<Map<String,List<T>>> data, String dateStr,String fileName,String[] rowsName,String sheetName) throws Exception {
        super.export03(response, fileName, data, dateStr,rowsName,sheetName,fileName);
    }

    @Override
    @Async("sasOrglifeExecutor")
    public void createRow(HSSFSheet sheet, T t, String dateStr, int i) throws Exception {
        if (t != null) {
            HSSFRow row = sheet.createRow(sheet.getLastRowNum() + 1);// 新建行

            int cellNum = 0;
            Map<String, Object> lifeEntity = JsonUtils.bean2map(t);
            String[] monthList = dateStr.split(",");

            // 组织名称
            HSSFCell c0 = row.createCell(cellNum++);
            c0.setCellStyle(this.getColStyle());
            if (StringUtils.isEmpty(t.getOrgName())) {
                c0.setCellValue("合计");
            } else {
                c0.setCellValue(i);
            }

            HSSFCell c1 = row.createCell(cellNum++);
            c1.setCellStyle(this.getColStyle());
            c1.setCellValue(t.getOrgName());

            for (String month : monthList) {
                String name = "month" + month;
                HSSFCell cell = row.createCell(cellNum++);
                cell.setCellStyle(this.getColStyle());
                cell.setCellValue((Integer) lifeEntity.get(name));
            }
            HSSFCell last = row.createCell(cellNum++);
            last.setCellStyle(this.getColStyle());
            last.setCellValue((Integer) lifeEntity.get("total"));

        }
    }

    @Override
    public void otherSet() {
        // 设计列宽
        setColumn(this.getSheet(), new double[]{20,60});
    }
}
