package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.common.StorageVariableEnum;
import com.goodsogood.ows.mapper.sas.StatisticalStorageVariableMapper;
import com.goodsogood.ows.model.vo.sas.StatisticalStorageVariableEntity;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2019-05-16 08:39
 **/
@Service
@Log4j2
public class StatisticalStorageVariableService {

    private final StatisticalStorageVariableMapper statisticalStorageVariableMapper;

    @Autowired
    public StatisticalStorageVariableService(StatisticalStorageVariableMapper statisticalStorageVariableMapper) {
        this.statisticalStorageVariableMapper = statisticalStorageVariableMapper;
    }


    /**
     * 存储临时变量
     */
  public   void  handlerStorageVariable(Integer partyGroupNum,Long orgId ,String statisticalDate){
        Example example = new Example(StatisticalStorageVariableEntity.class);
        example.createCriteria()
              .andEqualTo("orgId", orgId)
              .andEqualTo("statisticalDate", statisticalDate)
              .andEqualTo("storageType", StorageVariableEnum.PARTY_GROUP_NUM.getCode());
        StatisticalStorageVariableEntity statisticalStorageVariableEntity = statisticalStorageVariableMapper.selectOneByExample(example);
        StatisticalStorageVariableEntity storageVariableEntity=new StatisticalStorageVariableEntity();
        storageVariableEntity.setStatisticalDate(statisticalDate);
        storageVariableEntity.setStorageVal(NumberUtils.toLong(partyGroupNum+""));
        storageVariableEntity.setStorageType(StorageVariableEnum.PARTY_GROUP_NUM.getCode().intValue());
        storageVariableEntity.setOrgId(orgId);
        if(statisticalStorageVariableEntity==null){
            storageVariableEntity.setCreateTime(new Date());
            statisticalStorageVariableMapper.insert(storageVariableEntity);
        }else {
            storageVariableEntity.setStorageValId(statisticalStorageVariableEntity.getStorageValId());
            storageVariableEntity.setUpdateTime(new Date());
            statisticalStorageVariableMapper.updateByPrimaryKey(storageVariableEntity);
        }

    }
}