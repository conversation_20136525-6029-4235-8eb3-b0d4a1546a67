package com.goodsogood.ows.service.sas;

import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.model.db.sas.StatisticalUserOrgLifeEntity;
import com.goodsogood.ows.model.vo.sas.SasUserOrgLifeForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-19 16:21
 */
@Service
@Log4j2
public class MeetingService {
    private final MeetingMapper meetingMapper;

    @Autowired
    public MeetingService(MeetingMapper meetingMapper) {
        this.meetingMapper = meetingMapper;
    }

    /**
     * 从纪实统计用户参加会议的次数
     */
    public List<StatisticalUserOrgLifeEntity> sasUserOrgLife(SasUserOrgLifeForm sasUserOrgLifeForm) {
        return meetingMapper.sasUserOrgLife(sasUserOrgLifeForm);
    }
}
