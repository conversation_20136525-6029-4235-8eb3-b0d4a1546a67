package com.goodsogood.ows.service.dataworks;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.configuration.DssIndexScoreConstant;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.mapper.dataworks.PartyBusinessCalResultMapper;
import com.goodsogood.ows.mapper.dataworks.PartyBusinessRuleMapper;
import com.goodsogood.ows.mapper.dataworks.PartyCountyInfoMapper;
import com.goodsogood.ows.mapper.dataworks.CrmMcsVisitOverviewMapper;
import com.goodsogood.ows.mapper.dataworks.PartyPersonInfoMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.doris.IndexOrgScoreEntity;
import com.goodsogood.ows.model.db.doris.IndexUserScoreEntity;
import com.goodsogood.ows.model.db.tbcFusion.PartyBusinessCalResultEntity;
import com.goodsogood.ows.model.db.tbcFusion.PartyBusinessRuleEntity;
import com.goodsogood.ows.model.db.tbcFusion.PartyPersonInfoEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.score.ScoreConsumeForm;
import com.goodsogood.ows.model.vo.tbcFusion.UserInfoVO;
import com.goodsogood.ows.service.dss.DssDorisScoreService;
import com.goodsogood.ows.service.rank.RestTemplateHelper;
import com.goodsogood.ows.service.score.ScoreAddService;
import com.goodsogood.ows.service.score.ScoreService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.RedisLockUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 党业融合业务指标分数处理
 *
 * @Author: mengting
 * @Date: 2022/3/30 11:44
 */
@Log4j2
@Service
public class TbcBusinessService {
    private final static String LOCKKEY_MONTH = "party_business_month_lock";
    private final static String LOCKKEY_WEEK = "party_business_week_lock";
    private final StringRedisTemplate stringRedisTemplate;
    private final ScoreAddService scoreAddService;
    private final RestTemplate restTemplate;
    private final PartyCountyInfoMapper partyCountyInfoMapper;
    private final PartyBusinessCalResultMapper partyBusinessCalResultMapper;
    private final PartyBusinessRuleMapper partyBusinessRuleMapper;
    private final PartyPersonInfoMapper partyPersonInfoMapper;
    private final UserMapper userMapper;
    private final ScoreService scoreService;
    private final DssDorisScoreService dssDorisScoreService;
    private static final String SALERLOCK = "business-SALER-LOCK";

    /**
     * 不计算分数的指标，因为有部分简单规则已在dataworks中计算了分数
     */
    List<Integer> excludeIndex = Arrays.asList(5,6,7,8,9,10,11,12);
    /**
     * 通用规则（排名前20%-5；20%-50%-3；50-80%-1）
     */
    List<Integer> usualIndex = Arrays.asList(1, 2, 3);
    @Value("${scheduler.business-month.run}")
    private boolean monthRun;
    @Value("${scheduler.business-week.run}")
    private boolean weekRun;
    @Value("${scheduler.business-saler.run}")
    private boolean salerRun;
    @Value("${tog-services.user-center}")
    private String userCenter;
    private Long regionId = 86L;

    @Autowired
    public TbcBusinessService(StringRedisTemplate stringRedisTemplate, ScoreAddService scoreAddService,
                              RestTemplate restTemplate, PartyCountyInfoMapper partyCountyInfoMapper,
                              PartyBusinessCalResultMapper partyBusinessCalResultMapper, PartyBusinessRuleMapper partyBusinessRuleMapper,
                              CrmMcsVisitOverviewMapper crmMcsVisitOverviewMapper, PartyPersonInfoMapper partyPersonInfoMapper, UserMapper userMapper, ScoreService scoreService, DssDorisScoreService dssDorisScoreService) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.scoreAddService = scoreAddService;
        this.restTemplate = restTemplate;
        this.partyCountyInfoMapper = partyCountyInfoMapper;
        this.partyBusinessCalResultMapper = partyBusinessCalResultMapper;
        this.partyBusinessRuleMapper = partyBusinessRuleMapper;
        this.partyPersonInfoMapper = partyPersonInfoMapper;
        this.userMapper = userMapper;
        this.scoreService = scoreService;
        this.dssDorisScoreService = dssDorisScoreService;
    }


    //历史数据处理
    //月指标定时任务-每月1号八点
    @Scheduled(cron = "${scheduler.business-month.cron}")
    public void calBusinessMonthScore() {
        if (!monthRun) {
            log.debug("EvalScheduler->run 定时任务关闭");
            return;
        }
        String uuid = "";
        try {
            Thread.sleep((new Random().nextInt(10) + 1) * 1000);
            uuid = UUID.randomUUID().toString();
            boolean b = RedisLockUtil.tryGetDistributedLock(stringRedisTemplate, LOCKKEY_MONTH, uuid, 60 * 1000);
            if (!b) {
                log.debug("monthScheduler->run 未获取到锁,结束执行!");
                return;
            }
            //获取上月
            String dateMonthStr = DateUtils.getBeforeTimeOfMonth(-1);
            int dateMonth = Integer.parseInt(dateMonthStr.substring(0,4)+dateMonthStr.substring(5,7));
            log.debug("月指标分数计算" + dateMonth + "月");
//            List<PartyBusinessCalResultEntity> list = calMonthScore(19L, dateMonth);
            log.debug("积分处理" + dateMonth + "月");
//            sendToScoreCenter(list);
            //计算人员业务积分，并上报积分中心  tc 2022-06-28
            scoreService.calUserBusinessScore(regionId,dateMonth);
            //计算机构业务积分
            scoreService.calOrgBusinessScore(regionId,dateMonth);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("党业融合业务月度指标跑doris失败");
        } finally {
            RedisLockUtil.releaseDistributedLock(stringRedisTemplate, LOCKKEY_MONTH, uuid);
        }
    }


    //周指标定时任务-每周七点
    @Scheduled(cron = "${scheduler.business-week.cron}")
    public void calBusinessWeekScore() {
        if (!weekRun) {
            log.debug("weekScheduler->run 定时任务关闭");
            return;
        }
        String uuid = "";
        try {
            Thread.sleep((new Random().nextInt(10) + 1) * 1000);
            uuid = UUID.randomUUID().toString();
            boolean b = RedisLockUtil.tryGetDistributedLock(stringRedisTemplate, LOCKKEY_WEEK, uuid, 60 * 1000);
            if (!b) {
                log.debug("Scheduler->run 未获取到锁,结束执行!");
                return;
            }
            //获取当前是多少周
            Calendar calendar = Calendar.getInstance();
            calendar.setFirstDayOfWeek(Calendar.MONDAY);//设置星期一为一周开始的第一天
            //可以不用设置  这一周算上一年还是下一年，取决于这一周的大多数日期（4天以上）在哪一年。算在前一年，就是前一年的最后一周；算在后一年就是后一年的第一周
            calendar.setMinimalDaysInFirstWeek(4);
            calendar.setTimeInMillis(System.currentTimeMillis());//获得当前的时间戳
            int weekYear = calendar.get(Calendar.YEAR);//获得当前的年
            int weekOfYear = calendar.get(Calendar.WEEK_OF_YEAR);//获得当前日期属于今年的第几周
            log.debug("周指标分数计算" + (weekOfYear - 1) + "周");
            List<PartyBusinessCalResultEntity> list = calWeekScore(regionId, weekOfYear - 1, weekYear);
            log.debug("积分处理" + (weekOfYear - 1) + "周");
//            sendToScoreCenter(list);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("党业融合业务月度指标定时任务失败");
        } finally {
            RedisLockUtil.releaseDistributedLock(stringRedisTemplate, LOCKKEY_WEEK, uuid);
        }
    }


    //月度指标的分数计算
    public List<PartyBusinessCalResultEntity> calMonthScore(Long regionId, Integer dateMonth) {
        //查询本月所有指标-排除已在dataworks中计算过的
        Example example = new Example(PartyBusinessCalResultEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("regionId", regionId);
        criteria.andNotIn("ruleId", excludeIndex);
        criteria.andEqualTo("calDate", dateMonth);
        example.orderBy("ruleId").orderBy("calResult").desc();
        List<PartyBusinessCalResultEntity> resultEntity = partyBusinessCalResultMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(resultEntity)) {
            return Collections.emptyList();
        }
        Map<Integer, List<PartyBusinessCalResultEntity>> map = resultEntity.stream()
                .collect(Collectors.groupingBy(PartyBusinessCalResultEntity::getRuleId, LinkedHashMap::new, Collectors.toList()));
        map.forEach((k, v) -> {
            if (usualIndex.contains(k)) {//适用通用规则
                //处理排名,1、2、2、2、5、5、5、8
                //总人数*占比=排名，排名取排名范围内的。
                int rank = 0;//名次，可并列
                int number = 0;//中间变量，记录非并列排名
                Double scoreCopy = 0.00;//中间变量
                int total = v.size();
                int percent20 = (int) (total * 0.2);
                int percent50 = (int) (total * 0.5);
                int percent80 = (int) (total * 0.8);
                for (PartyBusinessCalResultEntity result : v) {
                    number += 1;
                    String explain = queryExplain(k);
                    if (result.getCalResult().compareTo(scoreCopy) != 0) {
                        rank = number;
                    }
                    if (rank <= percent20) {
                        result.setScore(5.00);
                        result.setExplainStr(String.format(explain, "20%"));
                    } else if (rank <= percent50) {
                        result.setScore(3.00);
                        result.setExplainStr(String.format(explain, "50%"));
                    } else if (rank <= percent80) {
                        result.setScore(1.00);
                        result.setExplainStr(String.format(explain, "80%"));
                    } else {
                        result.setScore(0.00);
                    }
                    result.setRankNum(rank);
                    scoreCopy = result.getCalResult();
                }
            }
        });
        //将score字段更新至数据库
        List<PartyBusinessCalResultEntity> list = new ArrayList<>();
        map.values().forEach(list::addAll);
        if (!CollectionUtils.isEmpty(list)) {
            partyBusinessCalResultMapper.updateScore(list);
        }
        return list;
    }


    //周指标-周均毛利率(注意周跨年问题：1.本年第一周包含上年数据  2.去年最后一周包含今年数据)
    public List<PartyBusinessCalResultEntity> calWeekScore(Long regionId, Integer weekNum, Integer year) {
        //计算全市平均周均毛利率
        Double avgProfile = 0.00;
        //获取当年1号是属于星期几
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);//设置星期一为一周开始的第一天
        calendar.setMinimalDaysInFirstWeek(4);
        calendar.set(year,Calendar.JANUARY,1);
        int dayOfWeek =  calendar.get(Calendar.DAY_OF_WEEK);
        log.debug("当前年1号所在星期数为"+dayOfWeek+"_"+year+"_注意2代表星期一");
        //获取上一年12.31号是多少周
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setFirstDayOfWeek(Calendar.MONDAY);//设置星期一为一周开始的第一天
        calendar1.setMinimalDaysInFirstWeek(4);
        calendar1.set(year-1,Calendar.DECEMBER,31);
        int lastWeek = calendar.get(Calendar.WEEK_OF_YEAR);
        log.debug("上年12.31号属于第"+lastWeek+"周"+"_"+(year-1));
        if(0==weekNum){//如果第一周之前的日期算去年的上一周，则此处就是计算去年上一周的数据，year包含上年+当前年，周为上一年最后一走
            avgProfile = partyBusinessCalResultMapper.queryAvgProfileSpecial(regionId, lastWeek,year,year-1);
        }else{
            //如果是第一周，并且不是该年的第一天不是周一，存在某一周跨年，要特殊处理（查询包含上年数据）
            if(1==weekNum && (dayOfWeek>2 && dayOfWeek<=5) ){//1号周二周三周四，该周都存在加上一年数据
                avgProfile = partyBusinessCalResultMapper.queryAvgProfileSpecial(regionId, weekNum,year,year-1);
            }else{
                avgProfile = partyBusinessCalResultMapper.queryAvgProfile(regionId, weekNum,year);
            }
        }
        if(null == avgProfile) return Collections.emptyList();
        BigDecimal avgProfileCal = new BigDecimal(Double.toString(avgProfile));
        //2个百分点
        BigDecimal percentUp20 = avgProfileCal.add(avgProfileCal.multiply(new BigDecimal("0.02")));
        BigDecimal percentDown20 = avgProfileCal.subtract(avgProfileCal.multiply(new BigDecimal("0.02")));
        //4个百分点
        BigDecimal percentUp40 = avgProfileCal.add(avgProfileCal.multiply(new BigDecimal("0.04")));
        BigDecimal percentDown40 = avgProfileCal.subtract(avgProfileCal.multiply(new BigDecimal("0.04")));
        //查询本周毛利率数据
        Example example = new Example(PartyBusinessCalResultEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("regionId", regionId);
        criteria.andEqualTo("ruleId", 4);
        if(0==weekNum){
            criteria.andEqualTo("calDate", lastWeek);
            criteria.andIn("year",Arrays.asList(year,year-1));
        }else{
            criteria.andEqualTo("calDate", weekNum);
            if(1==weekNum && (dayOfWeek>2 && dayOfWeek<=5)){
                criteria.andIn("year",Arrays.asList(year,year-1));
            }else{
                criteria.andEqualTo("year",year);
            }
        }
        example.orderBy("ruleId").orderBy("calResult").desc();
        List<PartyBusinessCalResultEntity> resultEntity = partyBusinessCalResultMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(resultEntity)) {
            return Collections.emptyList();
        }
        for (PartyBusinessCalResultEntity result : resultEntity) {
            BigDecimal calResult = new BigDecimal(Double.toString(result.getCalResult()));
            if (calResult.compareTo(percentDown20) >= 0 && calResult.compareTo(percentUp20) <= 0) {
                result.setScore(2.00);
                String explain = queryExplain(4);
                result.setExplainStr(String.format(explain, "2"));
            } else if (calResult.compareTo(percentDown20) < 0 && calResult.compareTo(percentDown40) >= 0
                    || calResult.compareTo(percentUp20) > 0 && calResult.compareTo(percentUp40) <= 0) {
                result.setScore(1.00);
                String explain = queryExplain(4);
                result.setExplainStr(String.format(explain, "2-4"));
            } else {
                result.setScore(0.00);
            }
        }
        //将score字段更新至数据库
        if (!CollectionUtils.isEmpty(resultEntity)) {
            partyBusinessCalResultMapper.updateScore(resultEntity);
        }
        return resultEntity;
    }


    //将score发送至积分中心-手动（用于测试）
    public void sendScoreCenterByIds(List<String> resultIds) {
        Example example = new Example(PartyBusinessCalResultEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("resultId",resultIds);
        List<PartyBusinessCalResultEntity> list = partyBusinessCalResultMapper.selectByExample(example);
        sendToScoreCenter(list);
    }



    /**
     * 将score发送至积分中心-手动（用于补数据）dateMonth：yyyyMM或者x周
     * @param dateMonth  yyyyMM或者x周
     * @param ruleIds  指定要发送积分中心的rule，不指定则为改月全部
     * @Param year 如果要跑的是周指标，则year必输
     */
    public void sendScoreCenterByDate(Integer dateMonth, Integer year, List<Integer>  ruleIds) {
        Example example = new Example(PartyBusinessCalResultEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("calDate",dateMonth);
        if(CollectionUtils.isEmpty(ruleIds)) {
            criteria.andIn("ruleId",ruleIds);
        }
        if(dateMonth.compareTo(2000)<0){//周指标
            if(null!= year){
                criteria.andEqualTo("year",year);
            }
        }
        List<PartyBusinessCalResultEntity> list = partyBusinessCalResultMapper.selectByExample(example);
        sendToScoreCenter(list);
    }


    //将score发送到积分中心
    public void sendToScoreCenter(List<PartyBusinessCalResultEntity> list) {
//        if(CollectionUtils.isEmpty(list)){
//            return;
//        }
//        List<PartyBusinessCalResultEntity> userIndexEntity = list.stream().filter(i -> 1 == i.getObjectType()
//                && !StringUtils.isEmpty(i.getCalObject())).collect(Collectors.toList());
//        HttpHeaders headers = new HttpHeaders();
//        headers.add("access_key", "ows");
//        headers.add("_region_id", "19");
////        log.debug("个人指标分值发送积分中心处理");
////        handleUserScore(userIndexEntity, headers);
//        List<PartyBusinessCalResultEntity> orgIndexEntity = list.stream().filter(i -> 1 != i.getObjectType()
//                && !StringUtils.isEmpty(i.getCalObject())).collect(Collectors.toList());
//        log.debug("组织指标分值发送积分中心处理");
//        handleOrgScore(headers, orgIndexEntity);
    }


    //组织指标分值发送积分中心处理
    private void handleOrgScore(List<PartyBusinessCalResultEntity> list) {
//        HttpHeaders headers = new HttpHeaders();
//        headers.add("access_key", "ows");
//        headers.add("_region_id", "19");
//        List<PartyBusinessCalResultEntity> orgIndexEntity = list.stream().filter(i -> 1 != i.getObjectType()
//                && !StringUtils.isEmpty(i.getCalObject())).collect(Collectors.toList());
//        log.debug("组织指标分值发送积分中心处理");
//        for (PartyBusinessCalResultEntity orgIndex : orgIndexEntity) {
//            //获取单位id
//            Long orgId = partyCountyInfoMapper.queryOrgId(orgIndex.getCalObject());
//            List<IndexOrgScoreEntity> dataList = new ArrayList();
//            log.debug("一级党组织积分处理"+orgIndex);
//            handleFirstPartyScore(headers,orgIndex,orgId,dataList);
//            log.debug("党组积分处理"+orgIndex);
//            handlePartyGroupScore(headers,orgIndex,orgId,dataList);
//            dssDorisScoreService.batchInsertOrgScore(dataList);
//        }
    }

    //党组积分处理
    private void handlePartyGroupScore(HttpHeaders headers, PartyBusinessCalResultEntity orgIndex,Long orgId,List<IndexOrgScoreEntity> dataList){
        log.debug("根据单位id获取党组");
        String groupUrl = String.format("http://%s/party-group/find-group-by-org?org_id=%s", userCenter,orgId);
        Long orgGroupId = RestTemplateHelper.get(
                HeaderHelper.buildMyHeader(headers), groupUrl, new TypeReference<Result<Long>>() {
                });
        if (!ObjectUtils.isEmpty(orgGroupId) && orgIndex.getScore().compareTo(0.00)!=0) {
//            ScoreConsumeForm scoreConsumeForm1 = new ScoreConsumeForm();
//            scoreConsumeForm1.setOrgId(3L);
//            scoreConsumeForm1.setScoreOrgId(orgGroupId);
//            scoreConsumeForm1.setScore(orgIndex.getScore().longValue());
//            scoreConsumeForm1.setOperType(0);
//            scoreConsumeForm1.setRegionId(19L);
//            scoreConsumeForm1.setScoreType(31);
//            scoreConsumeForm1.setScoreOrgType(2);
//            String token = orgId + "_" + orgGroupId + "_" + orgIndex.getYear() + "_" + orgIndex.getCalDate();
//            scoreConsumeForm1.setToken(token);
//            scoreConsumeForm1.setExplainTxt(orgIndex.getExplainStr());
//            scoreAddService.addScoreByOrgId(19L, scoreConsumeForm1);
            dataList.add(dssDorisScoreService.createOrgScore(DssIndexScoreConstant.SALEPARTY,orgGroupId,orgIndex.getScore().longValue(),orgIndex.getCalDate(),2));
        }
    }

    //一级党组织积分处理
    private void handleFirstPartyScore(HttpHeaders headers, PartyBusinessCalResultEntity orgIndex, Long orgId,List<IndexOrgScoreEntity> dataList){
        ScoreConsumeForm scoreConsumeForm = new ScoreConsumeForm();
        log.debug("根据单位id获取一级党组织" + "_" + orgId);
        log.debug("调用沈健方法获取top_org_id" + orgId);
        String url = String.format("http://%s/org/find-org-by-unit?org_id=%s", userCenter, orgId);
        // 调用远程方法
        List<OrganizationBase> rsList = null;
        try {
            rsList = RemoteApiHelper.get(restTemplate, url, headers, new TypeReference<Result<List<OrganizationBase>>>() {
            });
            if (rsList != null) {
                log.debug("根据党组织党训所对应的单位对应的一级党组织, 结果：" + rsList);
            }
        } catch (Exception e) {
            log.error("根据党组织党训所对应的单位对应的一级党组织, 错误内容:", e);
        }
        if (!CollectionUtils.isEmpty(rsList)) {
            log.debug("插入到doris库");
            for(OrganizationBase rs : rsList ){
                if(orgIndex.getScore().compareTo(0.00)!=0){
//                    scoreConsumeForm.setScoreOrgId(rs.getOrganizationId());
//                    scoreConsumeForm.setOrgId(3L);
//                    scoreConsumeForm.setScore(orgIndex.getScore().longValue());
//                    scoreConsumeForm.setRegionId(19L);
//                    scoreConsumeForm.setOperType(0);
//                    scoreConsumeForm.setScoreType(31);
//                    scoreConsumeForm.setScoreOrgType(1);
//                    scoreConsumeForm.setExplainTxt(orgIndex.getExplainStr());
//                    String token = orgId + "_" + rs.getOrganizationId() + "_" + orgIndex.getYear() + "_" + orgIndex.getCalDate();
//                    scoreConsumeForm.setToken(token);
//                    log.debug("调用李国勇方法增加积分"+scoreConsumeForm);
//                    scoreAddService.addScoreByOrgId(19L, scoreConsumeForm);
                    dataList.add(dssDorisScoreService.createOrgScore(DssIndexScoreConstant.SALEORG,rs.getOrganizationId(),orgIndex.getScore().longValue(),orgIndex.getCalDate(),1));
                }
            }
        }
    }

    //根据rule_id查询出对应的Explain
//    @Cacheable(value = "business-rule", key = "#root.methodName + '-' + #ruleId")
    private String queryExplain(Integer ruleId) {
        Example example = new Example(PartyBusinessRuleEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ruleId", ruleId);
        example.selectProperties("explainStr");
        PartyBusinessRuleEntity ruleEntity = partyBusinessRuleMapper.selectOneByExample(example);
        return ruleEntity.getExplainStr();
    }


    //个人指标分值发送积分中心处理
    private void handleUserScore(List<PartyBusinessCalResultEntity> userIndexEntity, HttpHeaders headers) {
        for (PartyBusinessCalResultEntity userIndex : userIndexEntity) {
            Long phone = Long.parseLong(userIndex.getCalObject());
            //根据phone获取userid以及orgid,调用用户中心后只返回党员id
            Integer dateMonth = userIndex.getCalDate();
            String url = String.format("http://%s/org/user/find-user-by-phone?phone=%s&date_month=%s", userCenter, phone, dateMonth);
            log.debug("调用用户中心"+url);
            UserInfoVO userInfo = RestTemplateHelper.get(
                    headers, url, new TypeReference<Result<UserInfoVO>>() {
                    });
            log.debug("调用用户中心结果"+userInfo);
            if(null==userInfo) continue;
            if(userIndex.getScore().compareTo(0.00)!=0){
                ScoreConsumeForm scoreConsumeForm = new ScoreConsumeForm();
                scoreConsumeForm.setUserId(userInfo.getUserId());
                scoreConsumeForm.setScoreType(31);
                scoreConsumeForm.setOrgId(userInfo.getOrgId());
                scoreConsumeForm.setOperType(0);
                scoreConsumeForm.setScore(userIndex.getScore().longValue());
                log.debug("业务指标分值：" + userIndex.getScore().longValue());
                String token = userInfo.getUserId() + "_" + userIndex.getYear() + "_" + userIndex.getCalDate();
                scoreConsumeForm.setToken(token);
                scoreConsumeForm.setExplainTxt(queryExplain(userIndex.getRuleId()));
                log.debug("调用李国勇方法增加积分"+scoreConsumeForm);
                scoreAddService.addScoreByUserId(regionId, scoreConsumeForm);
            }

        }
    }


    //加密手机号定时任务--更新t_party_person_info表（基础数据表）
    //每月27号 10点同步
    @Scheduled(cron = "${scheduler.business-saler.cron}")
    public void phoneSecretPerson() throws InterruptedException {
        log.error("mt更新营销人员基本信息0");
        log.debug("scheduler.course-push.cron");
        if (!salerRun) {
            log.error("更新营销人员基本信息-未开启定时任务");
            return;
        }
        log.error("mt更新营销人员基本信息00");
        String uuid = null;
        try {
            Thread.sleep((new Random().nextInt(10) + 1) * 1000);
            uuid = UUID.randomUUID().toString();
            boolean b = RedisLockUtil.tryGetDistributedLock(stringRedisTemplate, SALERLOCK, uuid, 60 * 1000);
            if (!b) {
                log.error("MTSALERLOCK->run 未获取到锁,结束执行!");
                return;
            }
            log.error("mt更新营销人员基本信息");
            List<PartyPersonInfoEntity> crm = partyPersonInfoMapper.selectAll();
            log.error("mt更新营销人员基本信息1");
            //将营销客户经理的电话号码加密匹配党建系统的电话，入股哦匹配上则将党建的该用户与其对应\
            for(PartyPersonInfoEntity personEntity : crm){
                String phoneSecret = "";
                //加密
                try{
                    phoneSecret = NumEncryptUtils.encrypt(personEntity.getMasterMobile(), 2);
                    personEntity.setPhoneSecret(phoneSecret);
                }catch(Exception e){
                    continue;
                }
            }
            log.error("mt更新营销人员基本信息2");
            Map<String,Long> map = queryUserIdByPhoneSecret(crm);
            crm.forEach(i->i.setUserId(map.get(i.getPhoneSecret())));
            //更新数据库
            log.error("mt更新营销人员基本信息3");
            partyPersonInfoMapper.updateUserId(crm);
            log.error("mt更新营销人员基本信息4");
            //将对应不上党建系统的人员删除掉
            Example example = new Example(PartyPersonInfoEntity.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIsNull("userId");
            partyPersonInfoMapper.deleteByExample(example);
            log.error("mt更新营销人员基本信息5");
        }catch (Exception e){
            log.error("更新营销人员基本信息失败:"+e);
            throw e;
        }finally {
            RedisLockUtil.releaseDistributedLock(stringRedisTemplate, SALERLOCK, uuid);
        }
    }


    //添加设置失效时间
    private Map<String,Long> queryUserIdByPhoneSecret(List<PartyPersonInfoEntity> phones) {
        Map<String,Long> map = new HashMap<>();
        //通过加密后的手机号匹配用户
        List<UserEntity> userEntity = userMapper.queryUserByPhone(phones);
        for(UserEntity user: userEntity){
            map.put(user.getPhone(),user.getUserId());
        }
        return map;
    }


//    //加密手机号定时任务--更新t_party_person_info表（基础数据表）
//    public void phoneSecretCrm() {
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
//        String ds = LocalDate.now().minusMonths(1).format(formatter);
//        Example example = new Example(CrmMcsVisitOverviewEntity.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("ds",ds);
//        example.selectProperties("masterMobile");
//        List<CrmMcsVisitOverviewEntity> crm = crmMcsVisitOverviewMapper.selectByExample(CrmMcsVisitOverviewEntity.class);
//        for(CrmMcsVisitOverviewEntity crmEntity : crm){
//            //加密
//            String phoneSecret = NumEncryptUtils.encrypt(crmEntity.getMasterMobile(), 2);
//            crmEntity.setPhoneSecret(phoneSecret);
//            crmMcsVisitOverviewMapper.updateByExample(crmEntity,crmEntity);
//        }
//    }
}

