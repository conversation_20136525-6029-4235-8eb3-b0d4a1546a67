package com.goodsogood.ows.service.finereport;

import com.goodsogood.ows.mapper.finereport.FineReportOrgInfoMapper;
import com.goodsogood.ows.mapper.finereport.FineReportOrgMemberInfoMapper;
import com.goodsogood.ows.model.db.finereport.FineReportOrgMemberInfo;
import com.goodsogood.ows.model.db.finereport.FinereportOrgInfo;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.dss.DssBaseInfo;
import com.goodsogood.ows.model.mongodb.dss.PartyMemberInfo;
import com.goodsogood.ows.model.mongodb.dss.PieObject;
import com.goodsogood.ows.service.impl.DssIndexBuilder;
import com.goodsogood.ows.service.impl.DssPartyBranchBuilder;
import com.goodsogood.ows.service.impl.DssPartyCommitteeBuilder;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: ows-sas
 * @description: 组织模拟统计
 * @author: Mr.LiGuoYong
 * @create: 2020-11-06 16:25
 **/
@Service
@Log4j2
public class OrgInfoReportService implements DssIndexBuilder, DssPartyBranchBuilder,DssPartyCommitteeBuilder {

    private final FineReportOrgInfoMapper fineReportOrgInfoMapper;
    private final FineReportOrgMemberInfoMapper fineReportOrgMemberInfoMapper;

    public OrgInfoReportService(FineReportOrgInfoMapper fineReportOrgInfoMapper,
                                FineReportOrgMemberInfoMapper fineReportOrgMemberInfoMapper) {
        this.fineReportOrgInfoMapper = fineReportOrgInfoMapper;
        this.fineReportOrgMemberInfoMapper = fineReportOrgMemberInfoMapper;
    }


    @Override
    public IndexInfo buildIndex(IndexInfo info) {
        FinereportOrgInfo finereportOrgInfo = fineReportOrgInfoMapper.
                getOrgInfoIndex(info.getRootId(), info.getYear());
        //统计组织信息
        setOrgInfo(info, finereportOrgInfo);
        //统计人员
        FineReportOrgMemberInfo orgMemberInfoIndex = fineReportOrgMemberInfoMapper.
                getOrgMemberInfoIndex(info.getRootId(), info.getYear());
        setPartyMemberInfo(info, orgMemberInfoIndex);
        return info;
    }

    @Override
    public PartyBranchInfo buildPartyBranch(PartyBranchInfo info) {
        //统计人员
        FineReportOrgMemberInfo orgMemberInfo = fineReportOrgMemberInfoMapper.
                getOrgMemberInfo(info.getOrganizationId(), info.getYear());
        if(null==orgMemberInfo){
            orgMemberInfo=new FineReportOrgMemberInfo();
            orgMemberInfo.setStaYear(""+info.getYear());
        }
        setPartyMemberInfo(info, orgMemberInfo);
        return info;
    }

    @Override
    public List<PartyBranchInfo> buildPartyBranchList(List<PartyBranchInfo> infoList) {
        List<Long> listOrgIds=  infoList.stream().map(PartyBranchInfo::getOrganizationId).
              collect(Collectors.toList());
        Integer staYear = infoList.get(0).getYear();
        List<FineReportOrgMemberInfo> listFineReportOrgInfo = fineReportOrgMemberInfoMapper.
                getOrgMemberInfoList(listOrgIds, staYear);

        infoList.forEach(item->{
            Optional<FineReportOrgMemberInfo> optionOrgInfo=listFineReportOrgInfo.stream().
                    filter(innerItem -> innerItem.getOrgId()
                    .equals(item.getOrganizationId())).findFirst();
            FineReportOrgMemberInfo orgMemberInfo = null;
            if(!optionOrgInfo.isPresent()){
                orgMemberInfo = new FineReportOrgMemberInfo();
                orgMemberInfo.setStaYear(""+staYear);
            }else {
                orgMemberInfo = optionOrgInfo.get();
            }
            setPartyMemberInfo(item, orgMemberInfo);
        });
        return infoList;
    }


    @Override
    public PartyCommitteeInfo buildPartyCommittee(PartyCommitteeInfo info) {
        FinereportOrgInfo finereportOrgInfo = fineReportOrgInfoMapper.
                getOrgInfoByOrgId(info.getOrganizationId(), info.getYear());
        if(null==finereportOrgInfo){
            finereportOrgInfo=new FinereportOrgInfo();
            finereportOrgInfo.setStaYear(""+info.getYear());
        }
        //统计组织信息
        setOrgInfo(info, finereportOrgInfo);
        //统计人员
        FineReportOrgMemberInfo orgMemberInfo = fineReportOrgMemberInfoMapper.
                getOrgMemberInfo(info.getOrganizationId(), info.getYear());
        if(null==orgMemberInfo){
            orgMemberInfo=new FineReportOrgMemberInfo();
            orgMemberInfo.setStaYear(""+info.getYear());
        }
        setPartyMemberInfo(info, orgMemberInfo);
        return info;
    }

    @Override
    public List<PartyCommitteeInfo> buildPartyCommitteeList(List<PartyCommitteeInfo> infoList) {
        List<Long> listOrgIds=  infoList.stream().map(PartyCommitteeInfo::getOrganizationId).
                collect(Collectors.toList());
        Integer staYear = infoList.get(0).getYear();
        List<FineReportOrgMemberInfo> listFineReportOrgInfo = fineReportOrgMemberInfoMapper.
                getOrgMemberInfoList(listOrgIds, staYear);

        infoList.forEach(item->{
            Optional<FineReportOrgMemberInfo> optionOrgInfo=listFineReportOrgInfo.stream().
                    filter(innerItem -> innerItem.getOrgId()
                            .equals(item.getOrganizationId())).findFirst();
            FineReportOrgMemberInfo orgMemberInfo = null;
            if(!optionOrgInfo.isPresent()){
                orgMemberInfo = new FineReportOrgMemberInfo();
                orgMemberInfo.setStaYear(""+staYear);
            }else {
                orgMemberInfo = optionOrgInfo.get();
                FinereportOrgInfo finereportOrgInfo = fineReportOrgInfoMapper.
                        getOrgInfoByOrgId(orgMemberInfo.getOrgId(),staYear);
                Optional<PartyCommitteeInfo> optionInfo=infoList.stream().
                        filter(it->item.getOrganizationId().equals(it.getOrganizationId())).findFirst();
                //统计组织信息
                optionInfo.ifPresent(partyCommitteeInfo -> setOrgInfo(partyCommitteeInfo, finereportOrgInfo));
            }
            setPartyMemberInfo(item, orgMemberInfo);
        });
        return infoList;
    }


    /***
     * 设置组织类别以及组织类型
     */
    private void setOrgInfo(DssBaseInfo dssBaseInfo,
                            FinereportOrgInfo finereportOrgInfo){
        if(dssBaseInfo instanceof IndexInfo){
            List<PieObject> pieObjects = setOrgCategoryDetail(finereportOrgInfo);
            ((IndexInfo) dssBaseInfo).setPartyOrgCategory(pieObjects);
            List<PieObject> pieObjects1 = setOrgTypeDetail(finereportOrgInfo);
            ((IndexInfo) dssBaseInfo).setPartyOrgType(pieObjects1);
        }
        if(dssBaseInfo instanceof PartyCommitteeInfo){
            List<PieObject> pieObjects = setOrgCategoryDetail(finereportOrgInfo);
            ((PartyCommitteeInfo) dssBaseInfo).setPartyOrgCategory(pieObjects);
            List<PieObject> pieObjects1 = setOrgTypeDetail(finereportOrgInfo);
            ((PartyCommitteeInfo) dssBaseInfo).setPartyOrgType(pieObjects1);
        }
    }

    /**
     * 设值组织类别细节
     * @param finereportOrgInfo
     */
    private List<PieObject>  setOrgCategoryDetail(FinereportOrgInfo finereportOrgInfo){
        List<PieObject> pieObjectSet = new LinkedList<>();
        PieObject pieObject = null;
        for(int i=0;i<5;i++) {
            pieObject= new PieObject();
            if(i==0){
                pieObject.setName("机关党组织");
                pieObject.setValue(finereportOrgInfo.getPartyOrgNumber());
            }
            if(i==1){
                pieObject.setName("事业单位党组织");
                pieObject.setValue(finereportOrgInfo.getCareerOrgNumber());
            }
            if(i==2){
                pieObject.setName("企业党组织");
                pieObject.setValue(finereportOrgInfo.getCompanyOrgNumber());
            }
            if(i==3){
                pieObject.setName("其它党组织");
                pieObject.setValue(finereportOrgInfo.getOtherOrgNumber());
            }
            if(i==4){
                pieObject.setName("社会组织党组织");
                pieObject.setValue(finereportOrgInfo.getSocietyOrgNumber());
            }
            pieObjectSet.add(pieObject);
        }
        return pieObjectSet;
    }


    /**
     * 设置组织类型细节
     * @param finereportOrgInfo
     */
    private List<PieObject> setOrgTypeDetail(FinereportOrgInfo finereportOrgInfo){
        List<PieObject> pieObjectSet = new LinkedList<>();
        PieObject pieObject = null;
        for(int i=0;i<3;i++) {
            pieObject= new PieObject();
            if(i==0){
                pieObject.setName("党委");
                pieObject.setValue(finereportOrgInfo.getPeriodNum());
            }
            if(i==1){
                pieObject.setName("党总支");
                pieObject.setValue(finereportOrgInfo.getPartyBranchTotal());
            }
            if(i==2){
                pieObject.setName("党支部");
                pieObject.setValue(finereportOrgInfo.getPartyBranchNumber());
            }
            pieObjectSet.add(pieObject);
        }
        return pieObjectSet;
    }


    /**
     * 设置党员数据
     */
    private void  setPartyMemberInfo(DssBaseInfo dssBaseInfo,
                                     FineReportOrgMemberInfo fineReportOrgMemberInfo){
        List<PartyMemberInfo> partyMemberInfos0 = setStaResultCycleForThree(fineReportOrgMemberInfo);
        List<PartyMemberInfo> partyMemberInfos1 = setStaEducation(fineReportOrgMemberInfo);
        List<PartyMemberInfo> partyMemberInfos2 = setStaJoinTime(fineReportOrgMemberInfo);
        List<PartyMemberInfo> partyMemberInfos3 = setStaAge(fineReportOrgMemberInfo);
        List<PartyMemberInfo> partyMemberInfos4 = setProfession(fineReportOrgMemberInfo);
        List<PartyMemberInfo> setNew = mergeSet(partyMemberInfos0, partyMemberInfos1,
                partyMemberInfos2, partyMemberInfos3,partyMemberInfos4);
        if(dssBaseInfo instanceof IndexInfo){
            ((IndexInfo) dssBaseInfo).setPartyMemberInfo(setNew);
        }
        if(dssBaseInfo instanceof PartyCommitteeInfo){
            ((PartyCommitteeInfo) dssBaseInfo).setPartyMemberInfo(setNew);
        }
        if(dssBaseInfo instanceof PartyBranchInfo){
            ((PartyBranchInfo) dssBaseInfo).setPartyMemberInfo(setNew);
        }
    }



    /**
     * 合併多個數據
     * @param partyMemberInfos
     * @return
     */
    private List<PartyMemberInfo> mergeSet(List<PartyMemberInfo> ... partyMemberInfos){
        List<PartyMemberInfo> setNew = new ArrayList<>();
        Arrays.stream(partyMemberInfos).forEach(item->{
            item.forEach(innerItem->{
                setNew.add(innerItem);
            });
        });
        return setNew.stream().sorted(Comparator.comparing(PartyMemberInfo::getOrder)).
                collect(Collectors.toCollection(LinkedList::new));
    }

    /**
     * 设置统计结果为3列
     * 包括正式党员、性别、民族
     */
    private List<PartyMemberInfo>  setStaResultCycleForThree(FineReportOrgMemberInfo fineReportOrgMemberInfo){
        List<PartyMemberInfo> partyMemberInfos = new LinkedList<>();
        List<PieObject> value1 = new LinkedList<>();
        List<PieObject> value2 = new LinkedList<>();
        List<PieObject> value3 = new LinkedList<>();
        PieObject pieObject1 = null;
        PieObject pieObject2 = null;
        PieObject pieObject3 = null;
        for(int i=0;i<3;i++){
            pieObject1= new PieObject();
            pieObject2= new PieObject();
            pieObject3= new PieObject();
            if(i==0){
                //党员
                pieObject1.setName("正式党员");
                pieObject1.setValue(fineReportOrgMemberInfo.getFormalPartyMember());
                //党员性别
                pieObject2.setName("男性党员");
                pieObject2.setValue(fineReportOrgMemberInfo.getMaleMember());
                //党员民族
                pieObject3.setName("汉族");
                pieObject3.setValue(fineReportOrgMemberInfo.getNationalityPartyMember());
            }
            if(i==1){
                //党员
                pieObject1.setName("预备党员");
                pieObject1.setValue(fineReportOrgMemberInfo.getPrePartyMember());
                //党员性别
                pieObject2.setName("女性党员");
                pieObject2.setValue(fineReportOrgMemberInfo.getFemaleMember());
                //党员民族
                pieObject3.setName("少数民族");
                pieObject3.setValue(fineReportOrgMemberInfo.getMinorityPartyMember());
            }
            if(i==2){
                //党员
                pieObject1.setName("未知");
                pieObject1.setValue(fineReportOrgMemberInfo.getOtherPartyMember());
                //党员性别
                pieObject2.setName("未知");
                pieObject2.setValue(fineReportOrgMemberInfo.getOtherSexMember());
                //党员民族
                pieObject3.setName("未知");
                pieObject3.setValue(fineReportOrgMemberInfo.getOtherNationalityPartyMember());
            }
            value1.add(pieObject1);
            value2.add(pieObject2);
            value3.add(pieObject3);
        }
        PartyMemberInfo partyMemberInfo1 = new PartyMemberInfo();
        partyMemberInfo1.setType("政治面貌");
        partyMemberInfo1.setOrder(1);
        partyMemberInfo1.setValue(value1);

        PartyMemberInfo partyMemberInfo2 = new PartyMemberInfo();
        partyMemberInfo2.setType("性别");
        partyMemberInfo2.setOrder(2);
        partyMemberInfo2.setValue(value2);

        PartyMemberInfo partyMemberInfo3 = new PartyMemberInfo();
        partyMemberInfo3.setType("民族");
        partyMemberInfo3.setOrder(3);
        partyMemberInfo3.setValue(value3);

        partyMemberInfos.add(partyMemberInfo1);
        partyMemberInfos.add(partyMemberInfo2);
        partyMemberInfos.add(partyMemberInfo3);
        return partyMemberInfos;
    }

    /**
     * 设置学历
     * @param fineReportOrgMemberInfo
     */
    private List<PartyMemberInfo>  setStaEducation( FineReportOrgMemberInfo fineReportOrgMemberInfo){
        List<PieObject> value= new LinkedList<>();
        List<PartyMemberInfo> list = new LinkedList<>();
        PieObject pieObject = null;
        for (int i=0;i<7;i++){
            pieObject = new PieObject();
            if(i==0){
                pieObject.setName("研究生");
                pieObject.setValue(fineReportOrgMemberInfo.getEducation1());
            }
            if(i==1){
                pieObject.setName("大学本科");
                pieObject.setValue(fineReportOrgMemberInfo.getEducation2());
            }
            if(i==2){
                pieObject.setName("大学专科");
                pieObject.setValue(fineReportOrgMemberInfo.getEducation3());
            }
            if(i==3){
                pieObject.setName("中专");
                pieObject.setValue(fineReportOrgMemberInfo.getEducation4());
            }
            if(i==4){
                pieObject.setName("高中、中技");
                pieObject.setValue(fineReportOrgMemberInfo.getEducation5());
            }
            if(i==5){
                pieObject.setName("初中及以下");
                pieObject.setValue(fineReportOrgMemberInfo.getEducation6());
            }
            if(i==6){
                pieObject.setName("未知");
                pieObject.setValue(fineReportOrgMemberInfo.getEducation7());
            }
            value.add(pieObject);
        }

        PartyMemberInfo partyMemberInfo = new PartyMemberInfo();
        partyMemberInfo.setType("学历");
        partyMemberInfo.setOrder(4);
        partyMemberInfo.setValue(value);
        list.add(partyMemberInfo);
        return list;
    }

    /**
     * 设置入党时间
     * @param fineReportOrgMemberInfo
     */
    private List<PartyMemberInfo> setStaJoinTime( FineReportOrgMemberInfo fineReportOrgMemberInfo){
        List<PieObject> value= new LinkedList<>();
        List<PartyMemberInfo> list = new LinkedList<>();
        PieObject pieObject = null;
        for (int i=0;i<5;i++){
            pieObject = new PieObject();
            if(i==0){
                pieObject.setName("新中国成立前");
                pieObject.setValue(fineReportOrgMemberInfo.getTime1());
            }
            if(i==1){
                pieObject.setName("新中国成立后至党的十一届三中全会前");
                pieObject.setValue(fineReportOrgMemberInfo.getTime2());
            }
            if(i==2){
                pieObject.setName("党的十一届三中全会后至党的十八大前");
                pieObject.setValue(fineReportOrgMemberInfo.getTime3());
            }
            if(i==3){
                pieObject.setName("党的十八大以来");
                pieObject.setValue(fineReportOrgMemberInfo.getTime4());
            }
            if(i==4){
                pieObject.setName("其它");
                pieObject.setValue(fineReportOrgMemberInfo.getTime5());
            }
            value.add(pieObject);
        }

        PartyMemberInfo partyMemberInfo = new PartyMemberInfo();
        partyMemberInfo.setType("入党时间 ");
        partyMemberInfo.setOrder(5);
        partyMemberInfo.setValue(value);
        list.add(partyMemberInfo);
        return list;
    }

    /**
     * 设置年龄
     * @param fineReportOrgMemberInfo
     */
    private List<PartyMemberInfo> setStaAge( FineReportOrgMemberInfo fineReportOrgMemberInfo){
        List<PieObject> value= new LinkedList<>();
        List<PartyMemberInfo> list = new LinkedList<>();
        PieObject pieObject = null;
        for (int i=0;i<9;i++){
            pieObject = new PieObject();
            if(i==0){
                pieObject.setName("30岁以下");
                pieObject.setValue(fineReportOrgMemberInfo.getAge1());
            }
            if(i==1){
                pieObject.setName("31-35岁");
                pieObject.setValue(fineReportOrgMemberInfo.getAge2());
            }
            if(i==2){
                pieObject.setName("36-40岁");
                pieObject.setValue(fineReportOrgMemberInfo.getAge3());
            }
            if(i==3){
                pieObject.setName("41-45岁");
                pieObject.setValue(fineReportOrgMemberInfo.getAge4());
            }
            if(i==4){
                pieObject.setName("46-50岁");
                pieObject.setValue(fineReportOrgMemberInfo.getAge5());
            }
            if(i==5){
                pieObject.setName("51-55岁");
                pieObject.setValue(fineReportOrgMemberInfo.getAge6());
            }
            if(i==6){
                pieObject.setName("56-60岁");
                pieObject.setValue(fineReportOrgMemberInfo.getAge7());
            }
            if(i==7){
                pieObject.setName("61岁以上");
                pieObject.setValue(fineReportOrgMemberInfo.getAge8());
            }
            if(i==8){
                pieObject.setName("未知");
                pieObject.setValue(0);
            }
            value.add(pieObject);
        }

        PartyMemberInfo partyMemberInfo = new PartyMemberInfo();
        partyMemberInfo.setType("年龄");
        partyMemberInfo.setOrder(6);
        partyMemberInfo.setValue(value);
        list.add(partyMemberInfo);
        return list;
    }

    /**
     * 设置职业
     * @param fineReportOrgMemberInfo
     */
    private List<PartyMemberInfo>  setProfession( FineReportOrgMemberInfo fineReportOrgMemberInfo){
        List<PieObject> value= new LinkedList<>();
        List<PartyMemberInfo> list = new LinkedList<>();
        PieObject pieObject = null;
        for (int i=0;i<9;i++){
            pieObject = new PieObject();
            if(i==0){
                pieObject.setName("党政机关工作人员");
                pieObject.setValue(fineReportOrgMemberInfo.getProfession1());
            }
            if(i==1){
                pieObject.setName("企事业单位管理人员");
                pieObject.setValue(fineReportOrgMemberInfo.getProfession2());
            }
            if(i==2){
                pieObject.setName("企事业单位专业技术人员");
                pieObject.setValue(fineReportOrgMemberInfo.getProfession3());
            }
            if(i==3){
                pieObject.setName("工勤技能人员");
                pieObject.setValue(fineReportOrgMemberInfo.getProfession4());
            }
            if(i==4){
                pieObject.setName("学生");
                pieObject.setValue(fineReportOrgMemberInfo.getProfession5());
            }
            if(i==5){
                pieObject.setName("农牧渔民");
                pieObject.setValue(fineReportOrgMemberInfo.getProfession6());
            }
            if(i==6){
                pieObject.setName("离退休人员");
                pieObject.setValue(fineReportOrgMemberInfo.getProfession7());
            }
            if(i==7){
                pieObject.setName("解放军武警");
                pieObject.setValue(fineReportOrgMemberInfo.getProfession8());
            }
            if(i==8){
                pieObject.setName("其它职业");
                pieObject.setValue(fineReportOrgMemberInfo.getProfession9());
            }
            value.add(pieObject);
        }

        PartyMemberInfo partyMemberInfo = new PartyMemberInfo();
        partyMemberInfo.setType("职业");
        partyMemberInfo.setOrder(7);
        partyMemberInfo.setValue(value);
        list.add(partyMemberInfo);
        return list;
    }


}