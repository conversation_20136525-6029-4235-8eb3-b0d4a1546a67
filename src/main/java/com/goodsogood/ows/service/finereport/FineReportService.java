package com.goodsogood.ows.service.finereport;

import com.goodsogood.ows.mapper.finereport.FineReportOrgInfoMapper;
import com.goodsogood.ows.mapper.finereport.FineReportOrgMemberInfoMapper;
import com.goodsogood.ows.model.db.finereport.FineReportOrgMemberInfo;
import com.goodsogood.ows.model.db.finereport.FinereportOrgInfo;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

/**
 * 报表服务层
 * <AUTHOR>
 */
@Service
@Log4j2
public class FineReportService {

    private final FineReportOrgInfoMapper fineReportOrgInfoMapper;
    private final FineReportOrgMemberInfoMapper fineReportOrgMemberInfoMapper;

    @Autowired
    public  FineReportService(FineReportOrgInfoMapper fineReportOrgInfoMapper,
                              FineReportOrgMemberInfoMapper fineReportOrgMemberInfoMapper) {
        this.fineReportOrgInfoMapper = fineReportOrgInfoMapper;
        this.fineReportOrgMemberInfoMapper = fineReportOrgMemberInfoMapper;
    }

    /**
     * 获取组织概况
     * @param orgId
     * @return
     */
    public FinereportOrgInfo getFineReportOrgInfo(Long orgId) {
        Example orgEx = new Example(FinereportOrgInfo.class);
        Example.Criteria criteria = orgEx.createCriteria();
        criteria.andEqualTo("orgId", orgId);
        return this.fineReportOrgInfoMapper.selectOneByExample(orgEx);
    }

    /**
     * 获取党员统计数据
     * @param orgId
     * @return
     */
    public FineReportOrgMemberInfo getFineReportOrgMemberInfo(Long orgId) {
        Example orgEx = new Example(FineReportOrgMemberInfo.class);
        Example.Criteria criteria = orgEx.createCriteria();
        criteria.andEqualTo("orgId", orgId);
        return this.fineReportOrgMemberInfoMapper.selectOneByExample(orgEx);
    }
}
