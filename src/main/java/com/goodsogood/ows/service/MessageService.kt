package com.goodsogood.ows.service

import com.goodsogood.ows.common.MessageCommon
import com.goodsogood.ows.common.MessageEnum
import com.goodsogood.ows.config.SimpleApplicationConfigHelper
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.MessageVO
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class MessageService(@Autowired val messageFactory: MessageFactory,
                     @Autowired val applicationConfigHelper: SimpleApplicationConfigHelper) {

    private val logger: Logger = LogManager.getLogger(MessageService::class.java)
    fun message(page: Int? = 1, header: HeaderHelper.SysHeader) : MutableList<MessageVO>{
        val resultList: MutableList<MessageVO> = mutableListOf()
        val orgData = this.applicationConfigHelper.getOrgByRegionId(header.regionId)
        val newsMessage = this.messageFactory.getMessage(MessageEnum.NEWS.component).generateMessage(header.regionId, orgData.orgId, page, 20)
        logger.debug("党内资讯 -> [${newsMessage}]")
        var n = 0
        for (index in 0 until newsMessage.size) {
            n ++
            resultList.add(newsMessage[index])
            if (n == 5) {
                // 其他模块随机
                val randomMessage = this.getRandomMessage(page, 0, header)
                logger.debug("随机的类型数据 -> [${randomMessage}]")
                if (randomMessage.size != 0)
                    resultList.addAll(randomMessage)
                n = 0
            }
        }
        if (newsMessage.size == 0) {
            for (index in 1..20) {
                val randomMessage = this.getRandomMessage(page, 0, header)
                logger.debug("随机的类型数据 -> [${randomMessage}]")
                if (randomMessage.size != 0)
                    resultList.addAll(index, randomMessage)
            }
        }
        return resultList
    }

    private fun getRandomMessage(page: Int? = 1, count: Int = 0, header: HeaderHelper.SysHeader) : MutableList<MessageVO> {
        val component = MessageEnum.randomType().component
        logger.debug("随机模块 -> [${component}]")
        var randomMessage: MutableList<MessageVO>
        try {
            randomMessage = messageFactory.getMessage(component).generateMessage(header.regionId, header.oid, page, 1)
        } catch (e: Exception) {
            logger.error("工厂类报错", e)
            randomMessage = this.getRandomMessage(page,count + 1, header)
        }
        if (count == 3) {
            return randomMessage
        }
        if (randomMessage.size == 0) {
            randomMessage = this.getRandomMessage(page,count + 1, header)
        }
        return randomMessage
    }
}