package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * @create 2018-07-19 16:13
 **/
@Log4j2
@Service
public class ThirdService {

     private final RestTemplate restTemplate;

     private final TogServicesConfig togServicesConf;

     private final UserMapper userMapper;

     @Autowired
     public ThirdService(RestTemplate restTemplate, TogServicesConfig togServicesConf, UserMapper userMapper) {
          this.restTemplate = restTemplate;
          this.togServicesConf = togServicesConf;
          this.userMapper = userMapper;
     }

     /**
      * 根据条件查询指定用户信息
      * name 用户姓名
      * phone  用户手机号
      * <AUTHOR>
      */
     public UserInfoBase findUserByWhere(String name,String phone) {
          UserInfoBase re = null;
          HttpHeaders headers = new HttpHeaders();
          restTemplate.setErrorHandler(new ClientExceptionHandler());
          int count = 0;
          do {
               try {
                    re = RemoteApiHelper.get(restTemplate, String.format("http://%s/find-user-by-where?name=%s&phone=%s",
                            togServicesConf.getUserCenter(),name,phone), headers, new TypeReference<Result<UserInfoBase>>() {});
               } catch (Exception e) {
                    log.error("根据条件查询指定用户信息失败！ name ={} phone={} 第{}次调用", name,phone,(count+1), e);
               }
               count++;
          } while (null == re && count < 5);
          log.debug("根据条件查询指定用户信息结果:查询 name ={} phone={}  结果 res ={} 调用数{}次", name,phone, re,count);
          return re;
     }

     /**
      * 根据用户ID查询用户组织关系基本信息
      * userId 用户编号
      * <AUTHOR>
      */
     public List<UserInfoBase> findUserByKey(Long userId) {
          List<UserInfoBase> re = null;
          HttpHeaders headers = new HttpHeaders();
          restTemplate.setErrorHandler(new ClientExceptionHandler());
          int count = 0;
          do {
               try {
                    re = RemoteApiHelper.get(restTemplate, String.format("http://%s/find-user-by-key?user_id=%s&isEmployee=1",
                            togServicesConf.getUserCenter(),userId), headers, new TypeReference<Result<List<UserInfoBase>>>() {});
               } catch (Exception e) {
                    log.error("根据用户ID查询用户组织关系基本信息失败！ userId ={}  第{}次调用", userId,(count+1), e);
               }
               count++;
          } while (null == re && count < 5);
          log.debug("根据用户ID查询用户组织关系基本信息结果:查询 userId ={}  结果 res ={} 调用数{}次", userId, re,count);
          return re;
     }

     /**
      * 根据组织ID查询组织信息
      * orgId 组织编号
      * <AUTHOR>
      */
     public OrganizationBase findOrgInfoByOrgId(Long orgId) {
          OrganizationBase re = null;
          HttpHeaders headers = new HttpHeaders();
          restTemplate.setErrorHandler(new ClientExceptionHandler());
          int count = 0;
          do {
               try {
                    re = RemoteApiHelper.get(restTemplate, String.format("http://%s/find-org-by-id?org_id=%s",
                            togServicesConf.getUserCenter(),orgId), headers, new TypeReference<Result<OrganizationBase>>() {});
               } catch (Exception e) {
                    log.error("根据组织ID查询组织信息失败！ orgId ={}  第{}次调用", orgId,(count+1), e);
               }
               count++;
          } while (null == re && count < 5);
          log.debug("根据组织ID查询组织信息结果:查询 orgId ={}  结果 res ={} 调用数{}次", orgId, re,count);
          return re!=null&&re.getOrganizationId()!=null?re:null;
     }

     /**
      * 根据组织ID查询所有下级组织
      * @param orgId      组织编号
      * @param isInclude  是否包含查询组织编号 默认0不包含
      * @param filterType 1-党委 2-党总支 3-党支部，默认0不筛选
      * <AUTHOR>
      */
     public List<OrganizationBase> findAllChildOrg(Long orgId,Integer isInclude,Integer filterType) {
          List<OrganizationBase> re = null;
          HttpHeaders headers = new HttpHeaders();
          restTemplate.setErrorHandler(new ClientExceptionHandler());
          if(isInclude==null){
               isInclude = 0;
          }
          if(filterType==null){
               filterType = 0;
          }
          int count = 0;
          do {
               try {
                    re = RemoteApiHelper.get(restTemplate, String.format("http://%s/org/find-all-child-org?org_id=%s&is_include=%s&filter_type=%s",
                            togServicesConf.getUserCenter(),orgId,isInclude,filterType), headers, new TypeReference<Result<List<OrganizationBase>>>() {});
               } catch (Exception e) {
                    log.error("根据组织ID查询所有下级组织信息失败！ orgId ={} isInclude={} filterType={}  第{}次调用", orgId,isInclude,filterType,(count+1), e);
               }
               count++;
          } while (null == re && count < 5);
          log.debug("根据组织ID查询所有下级组织信息结果:查询 orgId ={} isInclude={} filterType={} 结果 res ={} 调用数{}次", orgId,isInclude,filterType, re,count);
          return re;
     }

     /**
      * 根据用户编号用户所在组织的信息
      * @param userIdList 用户编号集合
      * <AUTHOR>
      */
     public List<UserInfoBase> findOrgInfoByUserId(Long regionId,List<Long> userIdList) {
          List<UserInfoBase> re = null;
          HttpHeaders headers = new HttpHeaders();
          headers.set("_region_id",regionId.toString());
          restTemplate.setErrorHandler(new ClientExceptionHandler());
          Map<String, Object> body = new HashMap<>();
          body.put("id_list", userIdList);
          body.put("is_employee", 1);
          int count = 0;
          do {
               try {
                    re = RemoteApiHelper.post(restTemplate, String.format("http://%s/org/find-user-party-by-list",
                            togServicesConf.getUserCenter()),body,headers, new TypeReference<Result<List<UserInfoBase>>>() {});
               } catch (Exception e) {
                    log.error("根据用户编号用户所在组织的信息失败！ userIdList ={}  第{}次调用", userIdList,(count+1), e);
               }
               count++;
          } while (null == re && count < 5);
          log.debug("根据用户编号用户所在组织的信息结果:查询 userIdList ={}  结果 re ={} 调用数{}次", userIdList, re,count);
          return re;
     }


     /**
      * @param orgIdList 组织编号集合
      * @param isUnit 是否查询行政单位人员	1-是 0-否，默认0
      * @param isParty  是否只返回政治面貌为党员的人员	    1-是 0-否，默认0
      * @Author: tc
      * @Description  通过组织编号，获取组织下所有用户编号
      */
     public List<UserInfoBase> getUserListByOrgId(Long regionId,List<Long> orgIdList,Integer isUnit,Integer isParty) {
          log.debug("根据组织编号获取组织下所有用户  查询参数 regionId ={} orgIdList ={} isUnit ={} isParty={} ", regionId,orgIdList,isUnit,isParty);
          String url = String.format("http://%s/find-user-by-org-or-corp-id", togServicesConf.getUserCenter());
          List<UserInfoBase> userList = new ArrayList<>();
          Map<String, Object> map = new HashMap<>();
          map.put("id_list", orgIdList);
          if(isUnit!=null){
               map.put("is_unit", isUnit);
          }
          if(isParty!=null){
               map.put("is_party", isParty);
          }
          HttpHeaders headers = new HttpHeaders();
          headers.put("_tk", Collections.singletonList("-1"));
          headers.add("_region_id",String.valueOf(regionId));
          try {
               userList = RemoteApiHelper.post(restTemplate, url, map, headers,
                       new TypeReference<Result<List<UserInfoBase>>>(){});
          } catch (Exception e) {
               log.error("获取用户积分列表，远程获取组织用户出错 regionId ={} orgIdList ={} isUnit={} isParty={} ",regionId,orgIdList,isUnit,isParty, e);
          }
          log.debug("根据组织编号获取组织下所有用户  结果:查询 regionId ={} orgIdList ={} isUnit ={} isParty={}  结果 userList ={}", regionId,orgIdList,isUnit,isParty,userList);
          return userList;
     }

     /**
      * @param userId 组织编号
      * @Author: tc
      * @Description  通过用户编号，获取用户基本信息
      */
     public UserInfoBase getUserBaseInfoById(Long regionId,Long userId) {
          String url = String.format("http://%s/uc/org/user-base-info?user_id=%s", togServicesConf.getUserCenter(),userId);
          UserInfoBase userInfoBase = new UserInfoBase();
          HttpHeaders headers = new HttpHeaders();
          headers.put("_tk", Collections.singletonList("-1"));
          headers.add("_region_id",String.valueOf(regionId));
          log.debug("根据用户编号，获取用户基本信息  开始查询 regionId ={}  userId ={} ", regionId, userId);
          try {
               userInfoBase = RemoteApiHelper.get(restTemplate, url, headers,
                       new TypeReference<Result<UserInfoBase>>(){});
          } catch (Exception e) {
               log.error("获取用户基本信息，远程获取组织用户出错 regionId ={}  userId ={} ",regionId,userId,e);
          }
          log.debug("根据用户编号，获取用户基本信息  结果:查询 regionId ={}  userId ={} 结果 userInfoBase ={}", regionId, userId,userInfoBase);
          return userInfoBase;
     }

     /**
      * @param orgId 组织编号
      * @Author: tc
      * @Description  通过组织编号查询同等级组织
      */
     public List<OrganizationBase> findGradeOrgListByOrgId(HttpHeaders headers,Long orgId,Integer page,Integer pageSize) {
          log.debug("通过组织编号查询同等级组织  查询参数 orgId ={} page ={} pageSize={} ",orgId,page,pageSize);
          if(page==null){
               page = 1;
          }
          if(pageSize==null){
               pageSize = 999999;
          }
          String url = String.format("http://%s/org/find-grade-by-org-id?orgId=%s&page=%s&pageSize=%s", togServicesConf.getUserCenter(),orgId,page,pageSize);
          List<OrganizationBase> orgList = new ArrayList<>();
          try {
               orgList = RemoteApiHelper.get(restTemplate, url, headers,
                       new TypeReference<Result<List<OrganizationBase>>>(){});
          } catch (Exception e) {
               log.error("通过组织编号查询同等级组织出错! orgId ={} page ={} pageSize={} ",orgId,page,pageSize, e);
          }
          log.debug("通过组织编号查询同等级组织  结果:查询  orgId ={} page ={} pageSize={}  结果 orgList ={}", orgId,page,pageSize,orgList);
          return orgList;
     }

//     public List<UserInfoBase> findUserPlaintext(Long regionId,Long orgId) {
//          String url = String.format("http://%s/org/user/find-user-plaintext?orgId=%s", togServicesConf.getUserCenter(),orgId);
//          List<UserInfoBase> userInfoBase = new ArrayList<>();
//          HttpHeaders headers = new HttpHeaders();
//          headers.put("_tk", Collections.singletonList("-1"));
//          headers.add("_region_id",String.valueOf(regionId));
//          log.debug("根据组织id查询用户明文信息，  开始查询 regionId ={}  orgId ={} ", regionId, orgId);
//          try {
//               userInfoBase = RemoteApiHelper.get(restTemplate, url, headers, new TypeReference<Result<List<UserInfoBase>>>(){});
//          } catch (Exception e) {
//               log.error("根据组织id查询用户明文信息出错 regionId ={}  userId ={} ",regionId,orgId,e);
//          }
//          log.debug("根据组织id查询用户明文信息结果:查询 regionId ={}  orgId ={} 结果 userInfoBase ={}",
//                  regionId, orgId,userInfoBase);
//          return userInfoBase;
//     }
          public List<UserInfoBase> findUserPlaintext() {
               List<UserInfoBase> result = this.userMapper.findUserPlaintext();
               if (!CollectionUtils.isEmpty(result)) {
                    result.forEach(base -> {
               // 解密，生成明文
                         try {
                              String phonePlaintext = NumEncryptUtils.decrypt(base.getPhone(), base.getPhoneSecret());
                              base.setPhone(phonePlaintext);
                              String certPlaintext = NumEncryptUtils.decrypt(base.getCertNumber(), base.getCertNumberSecret());
                              base.setCertNumber(certPlaintext);
                         } catch (Exception e) {
                              log.error("解密失败[{}]", e.getMessage(), e);
                         }
                    });
           }
               return result;
          }
}
