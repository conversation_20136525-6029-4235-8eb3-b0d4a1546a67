package com.goodsogood.ows.service.score;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.ScoreMasterIndexProperty;
import com.goodsogood.ows.mapper.score.ScoreDetailOtherMapper;
import com.goodsogood.ows.mapper.score.ScoreOrderCommodityMapper;
import com.goodsogood.ows.model.mongodb.ScoreInfo;
import com.goodsogood.ows.model.vo.score.BookCommodity;
import com.goodsogood.ows.model.vo.score.Books;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/11/20
 * Description: consume score buy the books data upload mongo
 */
@Service
@Log4j2
public class ScoreDetailBookService {

    private final MyMongoTemplate mongoTemplate;
    private final ScoreDetailOtherMapper scoreDetailOtherMapper;
    private final ScoreMasterIndexProperty scoreMasterIndexProperty;
    private final ScoreOrderCommodityMapper scoreOrderCommodityMapper;

    @Autowired
    public ScoreDetailBookService(MyMongoTemplate mongoTemplate, ScoreDetailOtherMapper scoreDetailOtherMapper, ScoreMasterIndexProperty scoreMasterIndexProperty, ScoreOrderCommodityMapper scoreOrderCommodityMapper) {
        this.mongoTemplate = mongoTemplate;
        this.scoreDetailOtherMapper = scoreDetailOtherMapper;
        this.scoreMasterIndexProperty = scoreMasterIndexProperty;
        this.scoreOrderCommodityMapper = scoreOrderCommodityMapper;
    }

    /**
     * 积分换书情况
     *
     * @param date 需要处理的月
     */
    public void toBooksUserReport(Date date) {
        String format = DateUtils.dateFormat(date, "yyyy-MM");
        buyBooksScoreDataUploadMongodbNow(format, format.concat(Constants.START_MONTH_TIME), format.concat(Constants.END_MONTH_TIME));
    }

    public void buyBooksScoreDataUploadMongodbNow(String queryTime, String startDate, String endDate) {
        Integer skip = 0;
        while (true) {
            List<Books> list = scoreDetailOtherMapper.findScoreBuyBooks(startDate, endDate, scoreMasterIndexProperty.getLimitSize(), skip);

            if (CollectionUtils.isEmpty(list)) {
                break;
            }

            List<BookCommodity> commodityByOrderIds = scoreOrderCommodityMapper.getCommodityByOrderIds(list.stream().map(Books::getOrderId).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(commodityByOrderIds)) {
                HashMap<Long, List<BookCommodity>> map = new HashMap<>(list.size());
                commodityByOrderIds.forEach(x -> {
                    long tempOrderId = x.getOrderId();
                    x.setOrderId(null);
                    List<BookCommodity> bookCommodities = map.get(tempOrderId);
                    if (!CollectionUtils.isEmpty(bookCommodities)) {
                        bookCommodities.add(x);
                    } else {
                        bookCommodities = new ArrayList<>();
                        bookCommodities.add(x);
                        map.put(tempOrderId, bookCommodities);
                    }
                });
                list.forEach(x -> x.setCommoditys(map.get(x.getOrderId())));
            }

            updateOrInsertNow(queryTime, list);

            skip += scoreMasterIndexProperty.getLimitSize();
        }
    }

    private void updateOrInsertNow(String queryTime, List<Books> list) {
        list.forEach(x -> {
            Query query = new Query(Criteria.where("userId").is(x.getTempUserId()).and("queryTime").is(queryTime));
            Update update = new Update().addToSet("books", x);
            x.setTempUserId(null);
            mongoTemplate.upsert(query, update, ScoreInfo.class);
        });
    }

//    /**
//     * 积分换书 学习情况
//     *
//     * @param queryTime 查询数据的月份
//     * @param startDate 开始时间
//     * @param endDate   结束时间
//     */
//    public void buyBooksScoreDataUploadMongodb(String queryTime, String startDate, String endDate) {
//
//        Integer skip = 0;
//        while (true) {
//            List<Books> list = scoreDetailOtherMapper.findScoreBuyBooks(startDate, endDate, limitSize, skip);
//
//            if (CollectionUtils.isEmpty(list)) {
//                break;
//            }
//
//            List<Books> tempList = new ArrayList<>();
//            Long tempUserId = null;
//            for (int i = 0; i < list.size(); i++) {
//                Books books = list.get(i);
//
//                /* 第一次循环判断mongodb中是否存在该books数据用于追加上次循环最后的数据 */
//                if (i == 0) {
//                    Query query = new Query(Criteria.where("user_id").is(books.getTempUserId()).and("query_time").is(queryTime).and("books").exists(true));
////                    boolean exists = mongoTemplate.exists(query, ScoreInfo.class);
////                    if (exists) {
////                        List<ScoreInfo> scoreInfos = mongoTemplate.find(new Query(Criteria.where("user_id").is(books.getTempUserId()).and("query_time").is(queryTime)), ScoreInfo.class);
////                        if (scoreInfos.size() > 1) {
////                            throw new ApiException(String.format("ScoreDetailBookService.buyBooksScoreDataUploadMongodb 查询到多条数据 user_id:[%s] query_time:[%s]", books.getTempUserId(), queryTime));
////                        }
////                        tempList = scoreInfos.get(0).getBooks();
////                        tempUserId = books.getTempUserId();
////                    } else {
////
////                    }
//                    tempUserId = books.getTempUserId();
//                }
//
//                /* 判断当前books对象user_id是否与上条user_id 相等 如果相等则追加 如果不相等则把tempList数据插入到mongo中 */
//                if (tempUserId.equals(books.getTempUserId())) {
//                    books.setTempUserId(null);
//                    tempList.add(books);
//                } else {
//                    updateOrInsert(tempUserId, queryTime, tempList);
//                    tempUserId = books.getTempUserId();
//                    tempList = new ArrayList<>();
//                    books.setTempUserId(null);
//                    tempList.add(books);
//                }
//
//                /* 本轮循环结束 需要手动保存数据到mongodb */
//                if (i == list.size() - 1) {
//                    updateOrInsert(tempUserId, queryTime, tempList);
//                }
//            }
//
//            skip += limitSize;
//        }
//    }
//
//    private void updateOrInsert(Long userId, String queryTime, List<Books> list) {
//        Query query = new Query(Criteria.where("user_id").is(userId).and("query_time").is(queryTime));
//        Update update = new Update().addToSet("books").each(list);
//        mongoTemplate.upsert(query, update, ScoreInfo.class);
//    }


}
