package com.goodsogood.ows.service.score;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.configuration.DssIndexScoreConstant;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.mapper.score.SasScoreManagerFlowMapper;
import com.goodsogood.ows.model.db.doris.IndexOrgScoreEntity;
import com.goodsogood.ows.model.db.doris.IndexUserScoreEntity;
import com.goodsogood.ows.model.db.score.SasScoreManagerFlowEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.score.ScoreConsumeForm;
import com.goodsogood.ows.service.dss.DssDorisScoreService;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 上报积分到积分中心服务类
 *
 * @ClassName : ScoreAddService
 * <AUTHOR> tc
 * @Date: 2021/12/17 10:24
 * @Description :
 */
@Service
@Log4j2
public class ScoreAddService {
    private final RestTemplate restTemplate;
    private final TogServicesConfig togServicesConf;
    private final SasScoreManagerFlowMapper sasScoreManagerFlowMapper;
    private final SimpleApplicationConfigHelper simpleApplicationConfigHelper;
    private final DssDorisScoreService dssDorisScoreService;

    public ScoreAddService(RestTemplate restTemplate, TogServicesConfig togServicesConf, SasScoreManagerFlowMapper sasScoreManagerFlowMapper, SimpleApplicationConfigHelper simpleApplicationConfigHelper, DssDorisScoreService dssDorisScoreService) {
        this.restTemplate = restTemplate;
        this.togServicesConf = togServicesConf;
        this.sasScoreManagerFlowMapper = sasScoreManagerFlowMapper;
        this.simpleApplicationConfigHelper = simpleApplicationConfigHelper;
        this.dssDorisScoreService = dssDorisScoreService;
    }

    /**
     * 上报到积分中心任务
     */
    @Async("scoreManagerExecutor")
    public void addScoreByScheduler(Long regionId) {
        //查需要上报积分的信息
        List<SasScoreManagerFlowEntity> scoreInfoList = sasScoreManagerFlowMapper.findAddSourceInfo(regionId);
        //组合上报信息
        scoreInfoList.forEach(scoreInfo -> {
            /**
             * 上报到积分中心
             */
            if (StringUtils.isEmpty(scoreInfo.getToken())) {
                scoreInfo.setToken(UUID.randomUUID().toString().replaceAll("-", ""));
            }
            // 根据regionID查询顶级党委组织
            Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
            Long topOrgId = orgData.getOrgId();
            String explainTxt = scoreInfo.getTypeStr().substring(scoreInfo.getTypeStr().indexOf("-") + 1);
            //增加积分任务标记  tc 2022-06-06
            String remark = null;
            if (scoreInfo.getType() == 16) {
                //党建品牌
                remark = "积分任务_建设本单位特色品牌";
            } else if (scoreInfo.getType() == 17) {
                //党建阵地
                remark = "积分任务_建设党建阵地";
            }
            ScoreConsumeForm scoreConsumeForm = new ScoreConsumeForm(regionId, scoreInfo.getToken(), scoreInfo.getScore(),
                    scoreInfo.getScoreType(), scoreInfo.getSendType(), topOrgId, explainTxt, remark);
            Long re = null;
            if (scoreInfo.getDataType() == 2) {
                /**人员积分*/
                if (scoreInfo.getScoreType() == 31 && scoreInfo.getHandleTime() != null) {//业务积分
                    String consumMonth = scoreInfo.getHandleTime() + "-01 00:00:00";
                    java.time.format.DateTimeFormatter timeFormatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime firstDayTime = LocalDateTime.parse(consumMonth, timeFormatter);
                    int day = firstDayTime.getMonth().maxLength();
                    LocalDateTime lastTimeDate = LocalDateTime.of(firstDayTime.getYear(), firstDayTime.getMonth(), day, 0, 0, 0);
                    scoreConsumeForm.setConsumeTime(lastTimeDate.format(timeFormatter));
                }
                scoreConsumeForm.setUserId(scoreInfo.getDataId());
                re = this.addScoreByUserId(regionId, scoreConsumeForm);
            } else if (scoreInfo.getDataType() == 1 || scoreInfo.getDataType() == 3) {
                /**组织和党组*/
                scoreConsumeForm.setScoreOrgId(scoreInfo.getDataId());
                if (scoreInfo.getDataType() == 1) {
                    scoreConsumeForm.setScoreOrgType(1);
                } else {
                    scoreConsumeForm.setScoreOrgType(2);
                }
                //调用接口
                re = this.addScoreByOrgId(regionId, scoreConsumeForm);

                // dorirs上报积分
                // TODO doris上报积分
                Long score = scoreInfo.getScore();
                if (scoreInfo.getSendType() == 1) { // 0-增加积分 1-减少积分
                    score = -score;
                }
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                IndexOrgScoreEntity obj = dssDorisScoreService.createOrgScore(
                        DssIndexScoreConstant.DorisRuleId.getRuleId(scoreInfo.getType()),
                        scoreInfo.getDataId(),
                        score,
                        scoreInfo.getHandleTime() == null ? Integer.parseInt(sdf.format(new Date())) :
                                Integer.parseInt(scoreInfo.getHandleTime().substring(0, 4) + scoreInfo.getHandleTime().substring(5, 7)),
                        scoreInfo.getDataType()
                );
                log.debug("addScoreByScheduler=>doris上报积分断点一" + JsonUtils.toJson(obj));
                dssDorisScoreService.batchInsertOrgScore(Collections.singletonList(obj));
            }
            //更新返回信息
            //发送时间
            scoreInfo.setSendTime(LocalDateTime.now());
            if (re != null) {
                scoreInfo.setStatus(2);
            } else {
                scoreInfo.setStatus(3);
            }
            sasScoreManagerFlowMapper.updateByPrimaryKeySelective(scoreInfo);
        });
    }

    /**
     * 根据用户编号增加个人积分
     * scoreConsumeForm 积分封装信息
     *
     * <AUTHOR>
     */
    public Long addScoreByUserId(Long regionId, ScoreConsumeForm scoreConsumeForm) {
        //返回的用户编号
        Long re = null;
        HttpHeaders headers = new HttpHeaders();
        headers.add("access_key", "ows");
        headers.add("_region_id", regionId.toString());
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        Map<String, Object> body = new HashMap<>();
        body.put("token", scoreConsumeForm.getToken());
        body.put("org_id", scoreConsumeForm.getOrgId());
        body.put("user_id", scoreConsumeForm.getUserId());
        //积分类型
        body.put("score_type", scoreConsumeForm.getScoreType());
        //操作类型 0：新增，1：扣分
        body.put("oper_type", scoreConsumeForm.getOperType());
        body.put("score", scoreConsumeForm.getScore());
        body.put("explain_txt", scoreConsumeForm.getExplainTxt());
        body.put("consume_time", scoreConsumeForm.getConsumeTime());
        body.put("remark", scoreConsumeForm.getRemark());
        int count = 0;
        do {
            try {
                re = RemoteApiHelper.post(restTemplate, String.format("http://%s/score/consume/nocheck/nopwd",
                        togServicesConf.getCreditCenter()), JsonUtils.toJson(body), headers, new TypeReference<Result<Long>>() {
                });
            } catch (Exception e) {
                log.error("<根据用户编号增加个人积分>失败！scoreConsumeForm ={}  第{}次调用", scoreConsumeForm, (count + 1), e);
            }
            count++;
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
            }
        } while (null == re && count < 5);
        log.debug("<根据用户编号增加个人积分>结果:查询 scoreConsumeForm ={}  结果 res ={} 调用数{}次", scoreConsumeForm, re, count);
        return re;
    }

    /**
     * 根据组织编号增加组织积分
     * scoreConsumeForm 积分封装信息
     *
     * <AUTHOR>
     */
    public Long addScoreByOrgId(Long regionId, ScoreConsumeForm scoreConsumeForm) {
        //返回的用户编号
        Long re = null;
        HttpHeaders headers = new HttpHeaders();
        headers.add("access_key", "ows");
        headers.add("_region_id", regionId.toString());
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        Map<String, Object> body = new HashMap<>();
        body.put("token", scoreConsumeForm.getToken());
        body.put("org_id", scoreConsumeForm.getOrgId());
        body.put("score_org_id", scoreConsumeForm.getScoreOrgId());
        //积分类型
        body.put("score_type", scoreConsumeForm.getScoreType());
        //操作类型 0：新增，1：扣分
        body.put("oper_type", scoreConsumeForm.getOperType());
        body.put("score", scoreConsumeForm.getScore());
        body.put("explain_txt", scoreConsumeForm.getExplainTxt());
        String url = "";
        if (scoreConsumeForm.getScoreOrgType() == 1) {
            url = String.format("http://%s/score/consume/nocheck/org", togServicesConf.getCreditCenter());
        } else if (scoreConsumeForm.getScoreOrgType() == 2) {
            url = String.format("http://%s/score/consume/nocheck/partyGroup", togServicesConf.getCreditCenter());
        }
        int count = 0;
        do {
            try {
                re = RemoteApiHelper.post(restTemplate, url, JsonUtils.toJson(body), headers, new TypeReference<Result<Long>>() {
                });
            } catch (Exception e) {
                log.error("<根据组织编号增加组织积分>失败！scoreConsumeForm ={}  第{}次调用", scoreConsumeForm, (count + 1), e);
            }
            count++;
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
            }
        } while (null == re && count < 5);
        log.debug("<根据组织编号增加组织积分>结果:查询 scoreConsumeForm ={}  结果 res ={} 调用数{}次", scoreConsumeForm, re, count);
        return re;
    }
}
