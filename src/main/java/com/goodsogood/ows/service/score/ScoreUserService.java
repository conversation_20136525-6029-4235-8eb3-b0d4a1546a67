package com.goodsogood.ows.service.score;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.mapper.score.ScoreUserMapper;
import com.goodsogood.ows.model.db.score.ScoreUserEntity;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/10
 */
@Service
@Log4j2
public class ScoreUserService {

    private static final String CACHE_KEY = "USER_TO_SCORE_USER_ID:%s";
    private static final String ALL_SCORE_USER_IDS = "ALL_SCORE_USER_IDS";
    private final ScoreUserMapper scoreUserMapper;
    private final StringRedisTemplate stringRedisTemplate;
    private final ObjectMapper objectMapper;

    @Autowired
    public ScoreUserService(ScoreUserMapper scoreUserMapper,
                            StringRedisTemplate stringRedisTemplate,
                            ObjectMapper objectMapper) {
        this.scoreUserMapper = scoreUserMapper;
        this.stringRedisTemplate = stringRedisTemplate;
        this.objectMapper = objectMapper;
    }


    /**
     * 根据用户id换取志愿者id
     *
     * @param userId
     * @return
     */
    public Long getScoreUserId(Long userId) {
        if (userId == null) {
            return -1L;
        }
        String cacheKey = String.format(CACHE_KEY, userId);
        if (stringRedisTemplate.hasKey(cacheKey)) {
            return Long.valueOf(stringRedisTemplate.opsForValue().get(cacheKey));
        }
        Example example = new Example(ScoreUserEntity.class);
        example.selectProperties("scoreUserId").createCriteria().andEqualTo("userId", userId);
        ScoreUserEntity scoreUserEntity = scoreUserMapper.selectOneByExample(example);
        if (scoreUserEntity == null) {
            scoreUserEntity = new ScoreUserEntity();
            scoreUserEntity.setScoreUserId(-1L);
        }
        stringRedisTemplate.opsForValue().set(cacheKey, scoreUserEntity.getScoreUserId().toString(), 30, TimeUnit.MINUTES);
        return scoreUserEntity.getScoreUserId();
    }

    /**
     * 用户id转积分用户id
     *
     * @param userIds
     * @return
     */
    public List<Long> userToScoreUserIds(String userIds) {
        if (StringUtils.isBlank(userIds)) {
            return null;
        }
        String[] ids = userIds.split(",");
        Map<Long, Long> allScoreUserIds = getAllScoreUserIds();
        List<Long> result = new ArrayList<>(ids.length);
        if (!CollectionUtils.isEmpty(allScoreUserIds)) {
            for (String id : ids) {
                if (allScoreUserIds.containsKey(Long.valueOf(id))) {
                    result.add(allScoreUserIds.get(Long.valueOf(id)));
                }
            }
            return result;
        }
        return null;
    }

    /**
     * 批量换取积分用户id
     * key = scoreUserId，value = userId
     *
     * @param userIds
     * @return
     */
    public Map<Long, Long> userToScoreUserIds(List<Long> userIds) {
        Example example = new Example(ScoreUserEntity.class);
        example.selectProperties("userId", "scoreUserId")
                .createCriteria()
                .andIn("userId", userIds);
        List<ScoreUserEntity> scoreUserEntityList = scoreUserMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(scoreUserEntityList)) {
            Map<Long, Long> collect = scoreUserEntityList.stream().collect(Collectors.toMap(ScoreUserEntity::getScoreUserId, v -> v.getUserId()));
            return collect;
        }
        return null;
    }

    /**
     * 获取所有积分用户id
     *
     * @return
     */
    public Map<Long, Long> getAllScoreUserIds() {
        if (stringRedisTemplate.hasKey(ALL_SCORE_USER_IDS)) {
            String json = stringRedisTemplate.opsForValue().get(ALL_SCORE_USER_IDS);
            try {
                return objectMapper.readValue(json, new TypeReference<Map<Long, Long>>() {
                });
            } catch (JsonProcessingException e) {
                log.error("反序列化出错", e);
            }
        }
        List<ScoreUserEntity> allScoreUserIds = scoreUserMapper.getAllScoreUserIds();
        Map<Long, Long> map = new HashMap<>();
        if (allScoreUserIds != null && !allScoreUserIds.isEmpty()) {
            allScoreUserIds.forEach(t -> {
                map.put(t.getUserId(), t.getScoreUserId());
            });
            try {
                String json = objectMapper.writeValueAsString(map);
                stringRedisTemplate.opsForValue().set(ALL_SCORE_USER_IDS, json, 3, TimeUnit.DAYS);
            } catch (JsonProcessingException e) {
                log.error("序列化出错", e);
            }
        }
        return map;
    }

}
