package com.goodsogood.ows.service.score;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.ScoreMasterIndexProperty;
import com.goodsogood.ows.mapper.score.PovertyCommodityMapper;
import com.goodsogood.ows.mapper.score.PovertyOrderMapper;
import com.goodsogood.ows.model.mongodb.ScoreInfo;
import com.goodsogood.ows.model.vo.score.Poverty;
import com.goodsogood.ows.model.vo.score.PovertyCommodity;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/11/19
 * Description: to t_score_poverty_order poverty data upload mongo
 */
@Service
@Log4j2
public class ScorePovertyService {


    private final MyMongoTemplate mongoTemplate;
    private final PovertyOrderMapper povertyOrderMapper;
    private final ScoreMasterIndexProperty scoreMasterIndexProperty;
    private final PovertyCommodityMapper povertyCommodityMapper;


    @Autowired
    public ScorePovertyService(MyMongoTemplate mongoTemplate, PovertyOrderMapper povertyOrderMapper, ScoreMasterIndexProperty scoreMasterIndexProperty, PovertyCommodityMapper povertyCommodityMapper) {
        this.mongoTemplate = mongoTemplate;
        this.povertyOrderMapper = povertyOrderMapper;
        this.scoreMasterIndexProperty = scoreMasterIndexProperty;
        this.povertyCommodityMapper = povertyCommodityMapper;
    }


    /**
     * 扶贫商城订单情况
     *
     * @param date 需要处理的月
     */
    public void toPovertyUserReport(Date date) {
        String format = DateUtils.dateFormat(date, "yyyy-MM");
        toPovertyUserReport(format, format.concat(Constants.START_MONTH_TIME), format.concat(Constants.END_MONTH_TIME));
    }


    /**
     * 扶贫商城订单情况
     *
     * @param queryTime 查询数据的月份
     * @param startDate 开始时间
     * @param endDate   结束时间
     */
    public void toPovertyUserReport(String queryTime, String startDate, String endDate) {
        Integer skip = 0;
        while (true) {
            List<Poverty> list = povertyOrderMapper.findPovertyInfo(startDate, endDate, scoreMasterIndexProperty.getLimitSize(), skip);

            if (CollectionUtils.isEmpty(list)) {
                break;
            }

            List<PovertyCommodity> povertyCommodityByOrderIds = povertyCommodityMapper.getPovertyCommodityByOrderIds(list.stream().map(Poverty::getOrderId).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(povertyCommodityByOrderIds)) {
                HashMap<String, List<PovertyCommodity>> map = new HashMap<>(list.size());
                povertyCommodityByOrderIds.forEach(x->{
                    String orderId = x.getOrderId();
                    x.setOrderId(null);
                    List<PovertyCommodity> povertyCommodities = map.get(orderId);
                    if(!CollectionUtils.isEmpty(povertyCommodities)){
                        povertyCommodities.add(x);
                    }else{
                        povertyCommodities=new ArrayList<>();
                        povertyCommodities.add(x);
                        map.put(orderId,povertyCommodities);
                    }
                });
                list.forEach(x->x.setCommoditys(map.get(x.getOrderId())));
            }

            updateOrInsert(list, queryTime);
            skip += scoreMasterIndexProperty.getLimitSize();
        }
    }

    private void updateOrInsert(List<Poverty> list, String queryTime) {
        list.forEach(x -> {
            Query query = new Query(Criteria.where("userId").is(x.getTempUserId()).and("queryTime").is(queryTime));
            Update update = new Update().addToSet("povertys", x);
            x.setTempUserId(null);
            mongoTemplate.upsert(query, update, ScoreInfo.class);
        });
    }

}
