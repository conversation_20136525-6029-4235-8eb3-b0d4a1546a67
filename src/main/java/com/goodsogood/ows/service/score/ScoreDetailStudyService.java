package com.goodsogood.ows.service.score;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.ScoreMasterIndexProperty;
import com.goodsogood.ows.mapper.score.ScoreDetailMapper;
import com.goodsogood.ows.model.mongodb.ScoreInfo;
import com.goodsogood.ows.model.vo.score.Study;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @date 2019/11/19
 * Description: to t_score_detail study data up mongo 七一书院 学习情况
 */
@Service
@Log4j2
public class ScoreDetailStudyService {


    private final ScoreDetailMapper scoreDetailMapper;
    private final MyMongoTemplate myMongoTemplate;
    private final ScoreMasterIndexProperty scoreMasterIndexProperty;


    @Autowired
    public ScoreDetailStudyService(ScoreDetailMapper scoreDetailMapper, MyMongoTemplate myMongoTemplate, ScoreMasterIndexProperty scoreMasterIndexProperty) {
        this.scoreDetailMapper = scoreDetailMapper;
        this.myMongoTemplate = myMongoTemplate;
        this.scoreMasterIndexProperty = scoreMasterIndexProperty;
    }


    /**
     * 七一书院 学习情况
     *
     * @param date 需要处理的月
     */
    public void toStudyUserReport(Date date) {

        String format = DateUtils.dateFormat(date, "yyyy-MM");

        ExecutorService executorService = Executors.newFixedThreadPool(6);
        executorService.execute(() ->
                toStudyUserReportNow(format, format.concat(Constants.START_MONTH_TIME), format.concat("-05 23:59:59"))
        );
        executorService.execute(() ->
                toStudyUserReportNow(format, format.concat("-06 00:00:00"), format.concat("-10 23:59:59"))
        );
        executorService.execute(() ->
                toStudyUserReportNow(format, format.concat("-11 00:00:00"), format.concat("-15 23:59:59"))
        );
        executorService.execute(() ->
                toStudyUserReportNow(format, format.concat("-16 00:00:00"), format.concat("-20 23:59:59"))
        );
        executorService.execute(() ->
                toStudyUserReportNow(format, format.concat("-21 00:00:00"), format.concat("-25 23:59:59"))
        );
        executorService.execute(() ->
                toStudyUserReportNow(format, format.concat("-26 00:00:00"), format.concat(Constants.END_MONTH_TIME))
        );

//        toStudyUserReportNow(format, format.concat(Constants.START_MONTH_TIME), format.concat("-05 23:59:59"));
//        toStudyUserReportNow(format, format.concat("-06 00:00:00"), format.concat("-10 23:59:59"));
//        toStudyUserReportNow(format, format.concat("-11 00:00:00"), format.concat("-15 23:59:59"));
//        toStudyUserReportNow(format, format.concat("-16 00:00:00"), format.concat("-20 23:59:59"));
//        toStudyUserReportNow(format, format.concat("-21 00:00:00"), format.concat("-25 23:59:59"));
//        toStudyUserReportNow(format, format.concat("-26 00:00:00"), format.concat(Constants.END_MONTH_TIME));
    }

    public void toStudyUserReportNow(String queryTime, String queryDay) {
        Integer skip = 0;
        while (true) {
            List<Study> list = scoreDetailMapper.findStudyInfo2(queryDay, scoreMasterIndexProperty.getLimitSize(), skip);

            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            updateOrInsertNow(queryTime, list);

            skip += scoreMasterIndexProperty.getLimitSize();
        }
    }

    public void toStudyUserReportNow(String queryTime, String startDate, String endDate, int skip, int numx) {
        while (true) {
            List<Study> list = scoreDetailMapper.findStudyInfo(startDate, endDate, 20000, skip);

            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            updateOrInsertNow(queryTime, list);

            skip += 20000;
        }
    }

    public void toStudyUserReportNow(String queryTime, String startDate, String endDate) {
        Integer skip = 0;
        while (true) {
            List<Study> list = scoreDetailMapper.findStudyInfo(startDate, endDate, scoreMasterIndexProperty.getLimitSize(), skip);

            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            updateOrInsertNow(queryTime, list);

            skip += scoreMasterIndexProperty.getLimitSize();
        }
    }

    private void updateOrInsertNow(String queryTime, List<Study> list) {
        list.forEach(x -> {
            Query query = new Query(Criteria.where("userId").is(x.getTempUserId()).and("queryTime").is(queryTime));
            Update update = new Update().addToSet("studys", x);
            x.setTempUserId(null);
            myMongoTemplate.upsert(query, update, ScoreInfo.class);
        });
    }


//    /**
//     * 七一书院 学习情况
//     *
//     * @param queryTime 查询数据的月份
//     * @param startDate 开始时间
//     * @param endDate   结束时间
//     */
//    private void toStudyUserReport(String queryTime, String startDate, String endDate) {
//
//        Integer skip = 0;
//        while (true) {
//            List<Study> list = scoreDetailMapper.findStudyInfo(startDate, endDate, limitSize, skip);
//
//            if (CollectionUtils.isEmpty(list)) {
//                break;
//            }
//
//            List<Study> tempList = new ArrayList<>();
//            Long tempUserId = null;
//            for (int i = 0; i < list.size(); i++) {
//                Study study = list.get(i);
//
//                /* 第一次循环判断mongodb中是否存在该study数据用于追加上次循环最后的数据 */
//                if (i == 0) {
//                    Query query = new Query(Criteria.where("user_id").is(study.getTempUserId()).and("query_time").is(queryTime).and("studys").exists(true));
//                    boolean exists = mongoTemplate.exists(query, ScoreInfo.class);
//                    if (exists) {
//                        List<ScoreInfo> scoreInfos = mongoTemplate.find(new Query(Criteria.where("user_id").is(study.getTempUserId()).and("query_time").is(queryTime)), ScoreInfo.class);
//                        if (scoreInfos.size() > 1) {
//                            throw new ApiException(String.format("ScoreDetailStudyService.toStudyUserReport 查询到多条数据 user_id:[%s] query_time:[%s]", study.getTempUserId(), queryTime));
//                        }
//                        tempList = scoreInfos.get(0).getStudys();
//                    }
//                    tempUserId = study.getTempUserId();
//                }
//
//                /* 判断当前study对象user_id是否与上条user_id 相等 如果相等则追加 如果不相等则把tempList学习数据插入到mongo中 */
//                if (tempUserId.equals(study.getTempUserId())) {
//                    study.setTempUserId(null);
//                    tempList.add(study);
//                } else {
//                    updateOrInsert(tempUserId, queryTime, tempList);
//                    tempUserId = study.getTempUserId();
//                    tempList = new ArrayList<>();
//                    study.setTempUserId(null);
//                    tempList.add(study);
//                }
//
//                /* 本轮循环结束 需要手动保存数据到mongodb */
//                if (i == list.size() - 1) {
//                    updateOrInsert(tempUserId, queryTime, tempList);
//                }
//            }
//
//            skip += limitSize;
//        }
//    }
//
//    public void updateOrInsert(Long userId, String queryTime, List<Study> list) {
//        Query query = new Query(Criteria.where("user_id").is(userId).and("query_time").is(queryTime));
//        Update update = new Update().addToSet("studys").each(list);
//        mongoTemplate.upsert(query, update, ScoreInfo.class);
//    }

}
