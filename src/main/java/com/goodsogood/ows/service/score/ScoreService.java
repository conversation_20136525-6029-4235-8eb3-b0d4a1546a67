package com.goodsogood.ows.service.score;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.ScoreManagerEnum;
import com.goodsogood.ows.common.TbcScoreConstant;
import com.goodsogood.ows.configuration.DssIndexScoreConstant;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.ScoreMasterIndexProperty;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.dataworks.PartyBusinessCalResultMapper;
import com.goodsogood.ows.mapper.dataworks.ScoreUserBusinessMapper;
import com.goodsogood.ows.mapper.score.SasScoreManagerFlowMapper;
import com.goodsogood.ows.mapper.score.ScoreMidDetailMapper;
import com.goodsogood.ows.model.db.doris.IndexOrgScoreEntity;
import com.goodsogood.ows.model.db.doris.IndexUserScoreEntity;
import com.goodsogood.ows.model.db.score.SasScoreManagerFlowEntity;
import com.goodsogood.ows.model.mongodb.ScoreInfo;
import com.goodsogood.ows.model.mongodb.report.UserScoreDonateInfo;
import com.goodsogood.ows.model.mongodb.report.UserScoreStudyInfo;
import com.goodsogood.ows.model.vo.score.*;
import com.goodsogood.ows.service.dss.DssDorisScoreService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.NumberUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLIntegrityConstraintViolationException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletionService;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @program: ows-sas
 * @description: 统计组织学习以及公益扶贫情况
 * @author: Mr.LiGuoYong
 * @create: 2019-11-25 09:00
 **/
@Service
@Log4j2
public class ScoreService {
    private final StringRedisTemplate redisTemplate;

    private final MyMongoTemplate mongoTemplate;
    private final ScoreMasterIndexProperty scoreMasterIndexProperty;
    private final ScoreUserBusinessMapper scoreUserBusinessMapper;
    private final PartyBusinessCalResultMapper partyBusinessCalResultMapper;
    private final SasScoreManagerFlowMapper flowMapper;
    private final ScoreMidDetailMapper scoreMidDetailMapper;
    private final DssDorisScoreService dssDorisScoreService;
    private final UserService userService;
    @Value("${scheduler.business-month.addUserBusinessScore}")
    private boolean addUserBusinessScore;

    public ScoreService(StringRedisTemplate redisTemplate, MyMongoTemplate mongoTemplate, ScoreMasterIndexProperty scoreMasterIndexProperty, ScoreUserBusinessMapper scoreUserBusinessMapper, PartyBusinessCalResultMapper partyBusinessCalResultMapper, SasScoreManagerFlowMapper flowMapper, ScoreMidDetailMapper scoreMidDetailMapper, DssDorisScoreService dssDorisScoreService, UserService userService) {
        this.redisTemplate = redisTemplate;
        this.mongoTemplate = mongoTemplate;
        this.scoreMasterIndexProperty = scoreMasterIndexProperty;
        this.scoreUserBusinessMapper = scoreUserBusinessMapper;
        this.partyBusinessCalResultMapper = partyBusinessCalResultMapper;
        this.flowMapper = flowMapper;
        this.scoreMidDetailMapper = scoreMidDetailMapper;
        this.dssDorisScoreService = dssDorisScoreService;
        this.userService = userService;
    }

    /**
     * 统计学习积分的情况
     *
     * @param orgId     queryDate:yyyy-MM
     * @param queryTime
     */
    public ScoreOrgElectronicReport getOrgStudyPovertyInfo(Long orgId, String queryTime) throws Exception {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.debug("根据组织id-" + orgId + "查询组织学习以及扶贫信息-startTime = {}", stopWatch.toString());
        String orgQueryLevel = "-" + orgId + "-";
        Query query = new Query(Criteria.where("queryTime").is(queryTime)
                .orOperator(Criteria.where("orgId").is(orgId), Criteria.where("orgLevel").regex(orgQueryLevel))
        );
        //统计用户学习积分以及兑换图书
        ScoreOrgElectronicReport scoreOrgElectronicReport = new ScoreOrgElectronicReport();
        UserScoreStudyInfo userScoreStudyInfo = new UserScoreStudyInfo();
        UserScoreDonateInfo userScoreDonateInfo = new UserScoreDonateInfo();
        ExecutorService executor = Executors.newFixedThreadPool(2);
        // 构建完成服务
        CompletionService completionService = new ExecutorCompletionService(executor);
        completionService.submit(() -> {
            scoreOrgElectronicReport.setUserName(getTopStudyUser(orgId, queryTime));
            return 2;
        });
        // 提到获取活动信息
        completionService.submit(() -> {
            List<ScoreInfo> activityReportVos = mongoTemplate.find(query, ScoreInfo.class);
            if (CollectionUtils.isEmpty(activityReportVos)) {
                scoreOrgElectronicReport.setUserScoreStudyInfo(UserScoreStudyInfo.getInitEntity());
                scoreOrgElectronicReport.setUserScoreDonateInfo(UserScoreDonateInfo.getInitEntity());
                return -1;
            }
            activityReportVos.forEach(item -> {
                if (!CollectionUtils.isEmpty(item.getStudys())) {
                    //统计多少人参与了学习
                    Integer studyNum = CollectionUtils.isEmpty(item.getStudys()) ? 0 : 1;
                    studyNum = userScoreStudyInfo.getStudyNum() + studyNum;
                    userScoreStudyInfo.setStudyNum(studyNum);
                    //统计学习积分数量
                    Long scoreNum = item.getStudys().stream().mapToLong(p -> p.getScore() == null ? 0 : p.getScore()).sum();
                    scoreNum = userScoreStudyInfo.getReceiveScoreNum() + scoreNum;
                    userScoreStudyInfo.setReceiveScoreNum(scoreNum);
                }
                //统计兑换图书数量
                if (!CollectionUtils.isEmpty(item.getBooks())) {
                    Long bookNum = item.getBooks().stream().filter(p -> !"积分消费退款".equals(p.getRemark())).mapToLong(p -> p.getCommodityCount() == null ? 0 : p.getCommodityCount()).sum();
                    bookNum = userScoreStudyInfo.getExchangeBookNum() + bookNum;
                    userScoreStudyInfo.setExchangeBookNum(bookNum);
                }

                if (!CollectionUtils.isEmpty(item.getDonates())) {
                    //捐赠积分总数
                    Long donateScoreNum = item.getDonates().stream().filter(p -> scoreMasterIndexProperty.getDonateId().equals(p.getActivityId())).mapToLong(p -> p.getDonateNum() == null ? 0 : p.getDonateNum()).sum();
                    donateScoreNum = userScoreDonateInfo.getDonateScore() + donateScoreNum;
                    userScoreDonateInfo.setDonateScore(donateScoreNum);
                    //一元捐的数量
                    Integer donateNum = donateScoreNum == 0 ? 0 : 1;
                    donateNum = userScoreDonateInfo.getDonateNum() + donateNum;
                    userScoreDonateInfo.setDonateNum(donateNum);
                }

                if (!CollectionUtils.isEmpty(item.getPovertys())) {
                    //购买扶贫产品的人数
                    Integer povertyPersonNum = CollectionUtils.isEmpty(item.getPovertys()) ? 0 : 1;
                    povertyPersonNum = userScoreDonateInfo.getPovertyPersonNum() + povertyPersonNum;
                    userScoreDonateInfo.setPovertyPersonNum(povertyPersonNum);
                    //在消费扶贫商城交易单数
//                    Integer povertyNum = item.getPovertys().stream().mapToInt(p -> p.getCountCommodity() == null ? 0 : p.getCountCommodity()).sum();
                    Integer povertyNum = item.getPovertys().size();
                    povertyNum = userScoreDonateInfo.getPovertyNum() + povertyNum;
                    userScoreDonateInfo.setPovertyNum(povertyNum);
                    //交易金额
                    BigDecimal povertyMoneyBigDecimal = NumberUtils.divide(item.getPovertys().stream().mapToLong(p -> p.getTotalPrice() == null ? 0 : p.getTotalPrice()).sum(), 100, 2, RoundingMode.DOWN);
                    userScoreDonateInfo.setPovertyMoney(povertyMoneyBigDecimal.add(BigDecimal.valueOf(userScoreDonateInfo.getPovertyMoney())).doubleValue());
                    //公益宝贝成交数量
                    Integer charityNum = item.getPovertys().stream().mapToInt(p -> p.getSizeDonate() == null ? 0 : p.getSizeDonate()).sum();
                    charityNum = userScoreDonateInfo.getCharityNum() + charityNum;
                    userScoreDonateInfo.setCharityNum(charityNum);
                    //配捐金额
                    BigDecimal charityMoneyCharityMoney = NumberUtils.divide(item.getPovertys().stream().mapToLong(p -> p.getDonation() == null ? 0 : p.getDonation()).sum(), 100, 2, RoundingMode.DOWN);
                    userScoreDonateInfo.setCharityMoney(charityMoneyCharityMoney.add(BigDecimal.valueOf(userScoreDonateInfo.getCharityMoney())).doubleValue());
                }
            });
            scoreOrgElectronicReport.setUserScoreStudyInfo(userScoreStudyInfo);
            scoreOrgElectronicReport.setUserScoreDonateInfo(userScoreDonateInfo);
            return 1;
        });
        Object r1 = completionService.take().get();
        Object r2 = completionService.take().get();
        log.info("根据组织id-{},月份数据-{},执行结果r1-{},r2-{},用时-{}", orgId, queryTime, r1, r2, stopWatch.toString());
        log.info("根据组织id-" + orgId + "查询组织学习以及扶贫信息-endTime = {}", stopWatch.toString());
        //关闭线程池
        executor.shutdown();
        return scoreOrgElectronicReport;
    }


    /**
     * 获取指定月 指定组织 及其下级最高学习积分的人员
     *
     * @param orgId     查询组织 及其下级
     * @param queryTime 查询月
     *
     * @return 用户名称
     */
    public String getTopStudyUser(Long orgId, String queryTime) {

        long tempUserId = 0L;
        String tempUserName = "";
        Long tempScore = 0L;
        String tempOrgLevel = "-" + orgId + "-";
        /* 查询月获取积分时的时间戳之和  用于判断两个相等tempScore 第二判断条件 */
        double tempTime = 0.00;

        Query query = new Query(Criteria
                .where("queryTime").is(queryTime)
                .orOperator(Criteria.where("orgId").is(orgId), Criteria.where("orgLevel").regex(tempOrgLevel))
        );
        query.fields().include("userId").include("studys").include("userName");

        List<ScoreInfo> scoreInfos = mongoTemplate.find(query, ScoreInfo.class);
        if (CollectionUtils.isEmpty(scoreInfos)) {
            return tempUserName;
        }

        for (ScoreInfo scoreInfo : scoreInfos) {
            if (!CollectionUtils.isEmpty(scoreInfo.getStudys())) {
                long sum = scoreInfo.getStudys().stream().map(Study::getScore).mapToLong(Long::valueOf).sum();
                double timeStamp = scoreInfo.getStudys().stream().map(x -> x.getConsumeTime().getTime()).mapToLong(Long::valueOf).average().getAsDouble();
                boolean isUpdate = (tempScore < sum) || (tempScore.equals(sum) && timeStamp < tempTime);
                if (isUpdate) {
                    tempUserId = scoreInfo.getUserId();
                    tempUserName = scoreInfo.getUserName();
                    tempScore = sum;
                    tempTime = timeStamp;
                }
            }
        }

        return tempUserName;

//        String tempOrgLevel = "-" + orgId + "-";
//        Query query = new Query(Criteria
//                .where("queryTime").is(queryTime)
//                .orOperator(Criteria.where("orgId").is(orgId), Criteria.where("orgLevel").regex(tempOrgLevel))
//        );
//        query.fields().include("userId").include("studys").include("userName");
//        List<ScoreInfo> scoreInfos = mongoTemplate.find(query, ScoreInfo.class);
//        if (!CollectionUtils.isEmpty(scoreInfos)) {
//            Optional<ScoreInfo> reduce = scoreInfos.stream().reduce((s1, s2) -> {
//                if(CollectionUtils.isEmpty(s1.getStudys())&&CollectionUtils.isEmpty(s2.getStudys())){
//                    return s1;
//                }
//                if(!CollectionUtils.isEmpty(s1.getStudys())&&CollectionUtils.isEmpty(s2.getStudys())){
//                    return s1;
//                }
//                if(CollectionUtils.isEmpty(s1.getStudys())&&!CollectionUtils.isEmpty(s2.getStudys())){
//                    return s2;
//                }
//                return s1.getStudys().stream().mapToLong(Study::getScore).sum() >=
//                        s2.getStudys().stream().mapToLong(Study::getScore).sum() ? s1 : s2;
//            });
//            ScoreInfo scoreInfoMax = reduce.get();
//            return scoreInfoMax.getUserName();
//        }
//        return "";
    }

    /**
     * 人员业务积分每月增加
     * yearMonth  YYYYmm
     */
    public void calUserBusinessScore(Long regionId, Integer yearMonth) {
        //获取各项业务数据
        List<ScoreUserBusinessVo> relist = scoreUserBusinessMapper.findUserBusiness(regionId, yearMonth);
        //各项业务分组
        Map<Integer, List<ScoreUserBusinessVo>> bMap = new HashMap<>();
        for (ScoreUserBusinessVo re : relist) {
            List<ScoreUserBusinessVo> bList = bMap.get(re.getRuleId());
            if (bList == null) {
                bList = new ArrayList<>();
            }
            bList.add(re);
            bMap.put(re.getRuleId(), bList);
        }
        //各项业务数据排序并计算得分
        Map<String, Map> cMap = this.calRuleScore(bMap);
//        log.debug("测试日志: 各项业务数据排序并计算得分 结果 cMap.userIdMap.size={} cMap.userIdMap={}",cMap.get("userIdMap").size(),cMap.get("userIdMap"));
//        log.debug("测试日志: 各项业务数据排序并计算得分 结果 cMap.calResultMap.size={} cMap.calResultMap={}",cMap.get("calResultMap").size(),cMap.get("calResultMap"));
        //上报积分开关
        if (addUserBusinessScore) {
            //各项业务积分总和排序并计算得分
            List<ScoreUserBusinessVo> list = this.calUserScore(cMap.get("userIdMap"));
            //添加到积分上报表，等待上报
//            this.insertInto(regionId, list);
            log.debug("发送到doris");
            this.insertIntoUserDoris(list);
        }
        //修改t_party_business_cal_result表
        this.updatePartyBusinessCalResult(cMap.get("calResultMap"));
    }


    /**
     * 机构业务积分每月增加
     * yearMonth  YYYYmm
     */
    public void calOrgBusinessScore(Long regionId, Integer yearMonth) {
        //获取各项业务数据
        List<ScoreUserBusinessVo> relist = scoreUserBusinessMapper.findOrgBusiness(regionId, yearMonth);
        //各项业务分组
        Map<Integer, List<ScoreUserBusinessVo>> bMap = new HashMap<>();
        for (ScoreUserBusinessVo re : relist) {
            List<ScoreUserBusinessVo> bList = bMap.get(re.getRuleId());
            if (bList == null) {
                bList = new ArrayList<>();
            }
            bList.add(re);
            bMap.put(re.getRuleId(), bList);
        }
        //各项业务数据排序并计算得分
        Map<String, Map> cMap = this.calRuleScore(bMap);
//        log.debug("测试日志: 各项业务数据排序并计算得分 结果 cMap.userIdMap.size={} cMap.userIdMap={}",cMap.get("userIdMap").size(),cMap.get("userIdMap"));
//        log.debug("测试日志: 各项业务数据排序并计算得分 结果 cMap.calResultMap.size={} cMap.calResultMap={}",cMap.get("calResultMap").size(),cMap.get("calResultMap"));
        //上报积分开关
        if (addUserBusinessScore) {
            //各项业务积分总和排序并计算得分
            List<ScoreUserBusinessVo> list = this.calUserScore(cMap.get("userIdMap"));
            //添加到积分上报表，等待上报
//            this.insertInto(regionId, list);
            log.debug("发送到doris");
            this.insertIntoOrgDoris(list);
        }
        //修改t_party_business_cal_result表
        this.updatePartyBusinessCalResult(cMap.get("calResultMap"));
    }

    /**
     * 人员业务指标积分
     * @param list
     */
    public void insertIntoUserDoris(List<ScoreUserBusinessVo> list){
        List<IndexUserScoreEntity> dataList = new ArrayList();
        for (ScoreUserBusinessVo sub : list) {
            dataList.add(dssDorisScoreService.createUserScore(DssIndexScoreConstant.SALEUSER,sub.getUserId(),sub.getReportScore(),sub.getCalDate()));
        }
        dssDorisScoreService.batchInsertUserScore(dataList);
    }

    /**
     * 机构业务指标积分,这里ScoreUserBusinessVo是为了适配以前的人员逻辑，此处的userId是机构id
     * @param list
     */
    public void insertIntoOrgDoris(List<ScoreUserBusinessVo> list){
        List<IndexOrgScoreEntity> dataList = new ArrayList();
        for (ScoreUserBusinessVo sub : list) {
            dataList.add(dssDorisScoreService.createOrgScore(DssIndexScoreConstant.SALEORG,sub.getUserId(),sub.getReportScore(),sub.getCalDate(),1));
        }
        dssDorisScoreService.batchInsertOrgScore(dataList);
    }

    /**
     * 计算各项得分和排名
     * 各项业务指标先排名，排名之后赋分；排名（1%），得分=满分（100）*（1-排名）；例如排名10%，则得分为100*90%=90分；
     */
    private Map<String, Map> calRuleScore(Map<Integer, List<ScoreUserBusinessVo>> bMap) {
        Map<String, Map> re = new HashMap<>();
        //用户各项排名和得分(用户编号,用于下一步计算)
        Map<Long, Map<Integer, ScoreUserBusinessVo>> userIdMap = new HashMap<>(bMap.keySet().size());
        re.put("userIdMap", userIdMap);
        //用户各项排名和得分(计算对象编号，用于修改t_party_business_cal_result)
        Map<String, List<ScoreUserBusinessVo>> calResultMap = new HashMap<>(bMap.keySet().size());
        re.put("calResultMap", calResultMap);
        bMap.keySet().stream().forEach(key -> {
            List<ScoreUserBusinessVo> ub = bMap.get(key);
            Integer count = ub.size();
            AtomicInteger rank = new AtomicInteger(1);
            //存储上一个排序值和排名
            Double lastOne = 0.0;
            Integer rk = 1;
            for (ScoreUserBusinessVo sbv : ub) {
                if (!sbv.getCalResult().equals(lastOne)) {
                    sbv.setRank(rank.getAndAdd(1));
                } else {
                    sbv.setRank(rk);
                    rank.addAndGet(1);
                }
                //计算得分
                if (sbv.getCalResult() != null && sbv.getCalResult() != 0) {
                    Double rankRate = NumberUtils.divideCeiling(sbv.getRank(), count);
                    sbv.setScore(NumberUtils.multiplyInteger(NumberUtils.subtract(1, rankRate), 100.0));
                } else {
                    sbv.setScore(0);
                }
                //记录数据
                rk = sbv.getRank();
                lastOne = sbv.getCalResult();
                //设置返回的集合
                Map<Integer, ScoreUserBusinessVo> um = userIdMap.get(sbv.getUserId());
                if (um == null) {
                    um = new HashMap<>();
                }
                um.put(sbv.getRuleId(), sbv);
                userIdMap.put(sbv.getUserId(), um);
                List<ScoreUserBusinessVo> subList = calResultMap.get(sbv.getCalObject());
                if (subList == null) {
                    subList = new ArrayList<>();
                }
                subList.add(sbv);
                calResultMap.put(sbv.getCalObject(), subList);
            }
        });
        return re;
    }

    /**
     * 计算总得分和排名
     * 每个月的所有业务指标得分求和
     * 月度业务指标得分排名，排名之后奖励积分；排名（1%），积分=满分（300）*（1-排名）；
     */
    private List<ScoreUserBusinessVo> calUserScore(Map<Long, Map<Integer, ScoreUserBusinessVo>> userIdMap) {
        //合并用户每个规则的的积分
        List<ScoreUserBusinessVo> tmpList = new ArrayList<>();
        userIdMap.keySet().stream().forEach(um -> {
            Map<Integer, ScoreUserBusinessVo> userMap = userIdMap.get(um);
            if (userMap != null && userMap.size() > 0) {
                //获取基础信息
                ScoreUserBusinessVo info = userMap.values().stream().findFirst().get();
                info.setRuleId(null);
                Integer score = userMap.values().stream().mapToInt(s -> s.getScore() == null ? 0 : s.getScore()).sum();
                info.setScore(score);
                tmpList.add(info);
            }
        });
        //根据汇总值重新排序并计算得分
        List<ScoreUserBusinessVo> list = tmpList.stream().sorted(Comparator.comparing(ScoreUserBusinessVo::getScore).reversed().thenComparing(ScoreUserBusinessVo::getUserId)).collect(Collectors.toList());
        //计算排名
        Integer count = list.size();
        AtomicInteger rank = new AtomicInteger(1);
        //存储上一个排序值和排名
        Integer lastOne = 0;
        Integer rk = 1;
        for (ScoreUserBusinessVo sbv : list) {
            if (!sbv.getScore().equals(lastOne)) {
                sbv.setRank(rank.getAndAdd(1));
            } else {
                sbv.setRank(rk);
                rank.addAndGet(1);
            }
            //计算上报积分中心的积分
            if (sbv.getScore().intValue() != 0) {
                Double rankRate = NumberUtils.divideCeiling(sbv.getRank(), count);
                sbv.setReportScore(NumberUtils.multiplyLong(NumberUtils.subtract(1, rankRate), 300.0));
            } else {
                sbv.setReportScore(0L);
            }
            //记录数据
            rk = sbv.getRank();
            lastOne = sbv.getScore();
        }
        return list;
    }

    /**
     * 每月计算人员业务积分
     *
     * @param regionId
     * @param list     用户增加积分的信息集合
     */
    private void insertInto(Long regionId, List<ScoreUserBusinessVo> list) {
        try {
            for (ScoreUserBusinessVo sub : list) {
                ScoreManagerEnum managerEnum = ScoreManagerEnum.PARTY_POSITION_BUILDING;
                SasScoreManagerFlowEntity flowEntity = new SasScoreManagerFlowEntity();
                flowEntity.setRegionId(regionId);
                flowEntity.setDataId(sub.getUserId());
                flowEntity.setDataType(managerEnum.getDataType());
                flowEntity.setSourceDataId(-1L);
                //t_party_business_cal_result表来源，数据年月_计算目标编号
                flowEntity.setSourceDataIdStr(sub.getCalDate() + "_" + sub.getCalObject());
                flowEntity.setType(managerEnum.getTypeId());
                flowEntity.setTypeStr(managerEnum.getTypeStr());
                flowEntity.setStatus(Constants.STATUS_YES);
                flowEntity.setIsDel(Constants.STATUS_YES);
                //转换年月整数为字符串
                String handleTime = sub.getCalDate().toString().substring(0, 4) + "-" + sub.getCalDate().toString().substring(4);
                flowEntity.setHandleTime(handleTime);
                flowEntity.setScoreType(managerEnum.getScoreType());
                //分值
                flowEntity.setScore(sub.getReportScore());
                //判断是否需要发送
                if (null != flowEntity.getScore() && flowEntity.getScore() == 0L) {
                    flowEntity.setStatus(0);
                }
                flowEntity.setSendType(managerEnum.getSendType());
                flowEntity.setCreateUser(-1L);
                flowEntity.setCreateTime(LocalDateTime.now());
                flowEntity.setToken("business_score" + "_" + managerEnum.getScoreType() + sub.getCalDate() + sub.getCalObject());
                flowMapper.insertIgnore(flowEntity);
            }
        } catch (Exception e) {
            /* 唯一主键索引约束错误 可以不用处理 */
            if (e.getCause() instanceof SQLIntegrityConstraintViolationException) {
                log.error("唯一主键Id错误:[{}]", e.getMessage(), e);
            } else { /* 其他错误 */
                log.error("每月计算人员业务积分 插入flow数据报错:", e);
            }
        }
    }

    //修改指标最终计算结果表，回写各指标和排名
    private void updatePartyBusinessCalResult(Map<String, List<ScoreUserBusinessVo>> map) {
        try {
            for (List<ScoreUserBusinessVo> m : map.values()) {
                for (ScoreUserBusinessVo sbv : m) {
                    partyBusinessCalResultMapper.updateScoreAndRank(sbv);
                }
            }
        } catch (Exception e) {
            log.error("修改指标最终计算结果表，回写各指标和排名报错！", e);
        }
    }

    /**
     * 查询各行政单位年度通用积分平均值
     *
     * @param headers
     *
     * @return
     */
    public List<UnitScoreVo> queryUnitYearScore(HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return scoreMidDetailMapper.queryUnitYearScore(sysHeader.getRegionId(), LocalDate.now().getYear());
    }


    public ReportVo queryAvgAndMax() {
        ReportVo yearVo = queryMaxScore(2, String.valueOf(LocalDate.now().getYear()));
        ReportVo vo = queryMaxScore(3, "0");
        vo.setYearAvg(yearVo.getAvgScore());
        vo.setYearMax(yearVo.getMaxScore());
        return vo;
    }


    private ReportVo queryMaxScore(Integer type, String date) {
        ReportVo vo = new ReportVo();
        if (Boolean.TRUE.equals(redisTemplate.hasKey(TbcScoreConstant.SYSTEM_AVG_SCORE + date))) {
            int avgScore = Integer.parseInt(redisTemplate.opsForValue().get(TbcScoreConstant.SYSTEM_AVG_SCORE + date));
            vo.setAvgScore(avgScore);
        } else {
            return scoreMidDetailMapper.querySystemMax(type, date);
        }
        if (Boolean.TRUE.equals(redisTemplate.hasKey(TbcScoreConstant.SYSTEM_MAX_SCORE + date))) {
            int maxScore = Integer.parseInt(redisTemplate.opsForValue().get(TbcScoreConstant.SYSTEM_MAX_SCORE + date));
            vo.setMaxScore(maxScore);
        } else {
            return scoreMidDetailMapper.querySystemMax(type, date);
        }
        return vo;
    }

    public LineChartVo queryMonthScore(HttpHeaders headers) {
        LineChartVo vo = new LineChartVo();
        List<String> x = queryLast12Month();//x轴
        List<Double> y = scoreMidDetailMapper.queryLast12Month(x);
        vo.setMonth(x);
        vo.setScore(y);
        return vo;
    }


    /**
     * 查询近12个月,不含当月
     *
     * @return
     */
    private List<String> queryLast12Month() {
        List<String> list = new LinkedList<>();

        for (int i = 1; i <= 12; i++) {
            list.add(dateToStr(LocalDate.now().minusMonths(i)));
        }
        return list;
    }

    private String dateToStr(LocalDate date) {
        DateTimeFormatter format2 = DateTimeFormatter.ofPattern("yyyy-MM");
        return format2.format(date);
    }

}
