package com.goodsogood.ows.service.score;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.mapper.score.ScoreOrganizeMapper;
import com.goodsogood.ows.model.db.score.ScoreOrganizeEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;

/**
 * 组织渠道账
 * <AUTHOR>
 * */
@Service
@Log4j2
public class ScoreOrganizeService {

    private static final ObjectMapper om = new ObjectMapper();

    private final ScoreOrganizeMapper scoreOrganizeMapper;
    private final StringRedisTemplate redisTemplate;

    @Autowired
    public ScoreOrganizeService(ScoreOrganizeMapper scoreOrganizeMapper, StringRedisTemplate redisTemplate) {
        this.scoreOrganizeMapper = scoreOrganizeMapper;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 获取组织账户的渠道信息，不会维护组织余额字段
     *
     * @param orgId 组织id
     * @return
     */
    public ScoreOrganizeEntity getScoreOrganizeByOrgIdCache(long orgId, int accountType) {
        String key = Constants.ORG_APP_CACHE + orgId + "_" + accountType;
        if (this.redisTemplate.hasKey(key)) {
            String cache = this.redisTemplate.opsForValue().get(key);
            try {
                return om.readValue(cache, ScoreOrganizeEntity.class);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        ScoreOrganizeEntity scoreOrganizeEntity = this.getScoreOrganizeByOrgId(orgId, accountType);
        if (scoreOrganizeEntity != null) {
            try {
                this.redisTemplate.opsForValue().set(key, om.writeValueAsString(scoreOrganizeEntity));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        return scoreOrganizeEntity;
    }


    /**
     * 获取组织账户的渠道信息
     *
     * @param orgId 组织id
     * @return
     */
    public ScoreOrganizeEntity getScoreOrganizeByOrgId(long orgId, int accountType) {
        Example example = new Example(ScoreOrganizeEntity.class);
        example.createCriteria()
                .andEqualTo("orgId", orgId)
                .andEqualTo("accountType", accountType);
        ScoreOrganizeEntity scoreOrganizeEntity = this.scoreOrganizeMapper.selectOneByExample(example);
        return scoreOrganizeEntity;
    }
}
