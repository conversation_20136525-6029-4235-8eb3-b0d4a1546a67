package com.goodsogood.ows.service.score;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.configuration.MongoCollectionNameConfig;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.ScoreMasterIndexProperty;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.mongodb.ScoreInfo;
import com.goodsogood.ows.service.activity.ActivityDetailDonateService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexInfo;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/11/21
 * Description:  党员学习与公益扶贫 主线程
 */
@Service
@Log4j2
public class ScoreMasterService {


    private final MyMongoTemplate mongoTemplate;
    private final ScoreMasterIndexProperty scoreMasterIndexProperty;
    private final ScoreDetailStudyService scoreDetailStudyService;
    private final ScorePovertyService scorePovertyService;
    private final ScoreDetailBookService scoreDetailBookService;
    private final ActivityDetailDonateService activityDetailDonateService;
    private final UserMapper userMapper;
    private final MongoCollectionNameConfig mongoCollectionNameConfig;

    @Autowired
    public ScoreMasterService(MyMongoTemplate mongoTemplate, ScoreMasterIndexProperty scoreMasterIndexProperty, ScoreDetailStudyService scoreDetailStudyService, ScorePovertyService scorePovertyService, ScoreDetailBookService scoreDetailBookService, ActivityDetailDonateService activityDetailDonateService, UserMapper userMapper, MongoCollectionNameConfig mongoCollectionNameConfig) {
        this.mongoTemplate = mongoTemplate;
        this.scoreMasterIndexProperty = scoreMasterIndexProperty;
        this.scoreDetailStudyService = scoreDetailStudyService;
        this.scorePovertyService = scorePovertyService;
        this.scoreDetailBookService = scoreDetailBookService;
        this.activityDetailDonateService = activityDetailDonateService;
        this.userMapper = userMapper;
        this.mongoCollectionNameConfig = mongoCollectionNameConfig;
    }


    public void runOfScore(Date date) throws InterruptedException {
        long startTime = System.currentTimeMillis();
        String format = DateUtils.dateFormat(date, "yyyy-MM");
        createAndIndex();
        removeOfMonthData(format);
        log.debug(String.format("ScoreMasterService.runOfScore 开始处理 [%s] 月积分相关的数据 ", format));
        String monthStartTime = format.concat(Constants.START_MONTH_TIME);
        String monthEndTime = format.concat(Constants.END_MONTH_TIME);

        /* 创建固定线程 和 执行节点 */
        ExecutorService executorService = Executors.newFixedThreadPool(4);
//        CountDownLatch countDownLatch = new CountDownLatch(10);
        executorService.execute(() -> {
            scoreDetailStudyService.toStudyUserReportNow(format, monthStartTime, monthEndTime, 0, Integer.MAX_VALUE);
        });


//        for (int i = 1; i <= 31; i++) {
//            int finalI = i;
//            executorService.execute(()->{
//                if(finalI <10){
//                    scoreDetailStudyService.toStudyUserReportNow(format, format.concat("-0"+ finalI));
//                }else{
//                    scoreDetailStudyService.toStudyUserReportNow(format, format.concat("-"+ finalI));
//                }
//            });
//        }

        /* 调用学习积分  共7个线程 */
//        log.debug("ScoreMasterService.runOfScore 调用学习积分 线程1................................................ ");
//        executorService.execute(() -> {
//            scoreDetailStudyService.toStudyUserReportNow(format, monthStartTime, format.concat("-05 23:59:59"));
//            log.debug("ScoreMasterService.runOfScore 学习积分 thread name [{}]处理完成................................................ ", Thread.currentThread().getName());
//            countDownLatch.countDown();
//        });
//
//        log.debug("ScoreMasterService.runOfScore 调用学习积分 线程2................................................ ");
//        executorService.execute(() -> {
//            scoreDetailStudyService.toStudyUserReportNow(format, format.concat("-06 00:00:00"), format.concat("-10 23:59:59"));
//            log.debug("ScoreMasterService.runOfScore 学习积分 thread name [{}]处理完成................................................ ",Thread.currentThread().getName());
//            countDownLatch.countDown();
//        });
//
//        log.debug("ScoreMasterService.runOfScore 调用学习积分 线程3................................................ ");
//        executorService.execute(() -> {
//            scoreDetailStudyService.toStudyUserReportNow(format, format.concat("-11 00:00:00"), format.concat("-15 23:59:59"));
//            log.debug("ScoreMasterService.runOfScore 学习积分 thread name [{}]处理完成................................................ ",Thread.currentThread().getName());
//            countDownLatch.countDown();
//        });
//
//        log.debug("ScoreMasterService.runOfScore 调用学习积分 线程4................................................ ");
//        executorService.execute(() -> {
//            scoreDetailStudyService.toStudyUserReportNow(format, format.concat("-16 00:00:00"), format.concat("-20 23:59:59"));
//            log.debug("ScoreMasterService.runOfScore 学习积分 thread name [{}]处理完成................................................ ",Thread.currentThread().getName());
//            countDownLatch.countDown();
//        });
//
//        log.debug("ScoreMasterService.runOfScore 调用学习积分 线程5................................................ ");
//        executorService.execute(() -> {
//            scoreDetailStudyService.toStudyUserReportNow(format, format.concat("-21 00:00:00"), format.concat("-25 23:59:59"));
//            log.debug("ScoreMasterService.runOfScore 学习积分 thread name [{}]处理完成................................................ ",Thread.currentThread().getName());
//            countDownLatch.countDown();
//        });
//
//        log.debug("ScoreMasterService.runOfScore 调用学习积分 线程6................................................ ");
//        executorService.execute(() -> {
//            scoreDetailStudyService.toStudyUserReportNow(format, format.concat("-26 00:00:00"), format.concat("-28 23:59:59"));
//            log.debug("ScoreMasterService.runOfScore 学习积分 thread name [{}]处理完成................................................ ",Thread.currentThread().getName());
//            countDownLatch.countDown();
//        });
//
//        log.debug("ScoreMasterService.runOfScore 调用学习积分 线程7................................................ ");
//        executorService.execute(() -> {
//            scoreDetailStudyService.toStudyUserReportNow(format, format.concat("-29 00:00:00"), monthEndTime);
//            log.debug("ScoreMasterService.runOfScore 学习积分 thread name [{}]处理完成................................................ ",Thread.currentThread().getName());
//            countDownLatch.countDown();
//        });

        /* 调用消费扶贫 一个线程 */
        log.debug("ScoreMasterService.runOfScore 调用消费扶贫 线程8................................................ ");
        executorService.execute(() -> {
            scorePovertyService.toPovertyUserReport(format, monthStartTime, monthEndTime);
            log.debug("ScoreMasterService.runOfScore 消费扶贫 thread name [{}]处理完成................................................ ", Thread.currentThread().getName());
//            countDownLatch.countDown();
        });

        /* 调用积分换书 一个线程 */
        log.debug("ScoreMasterService.runOfScore 调用积分换书 线程9................................................ ");
        executorService.execute(() -> {
            scoreDetailBookService.buyBooksScoreDataUploadMongodbNow(format, monthStartTime, monthEndTime);
            log.debug("ScoreMasterService.runOfScore 积分换书 thread name [{}]处理完成................................................ ", Thread.currentThread().getName());
//            countDownLatch.countDown();
        });

        /* 调用一元捐 一个线程 */
        log.debug("ScoreMasterService.runOfScore 调用一元捐 线程10................................................ ");
        executorService.execute(() -> {
            activityDetailDonateService.buyDonateDataUploadMongodbNow(format, monthStartTime, monthEndTime);
            log.debug("ScoreMasterService.runOfScore 一元捐 thread name [{}]处理完成................................................ ", Thread.currentThread().getName());
//            countDownLatch.countDown();
        });

        /* 主线程等待 */
        log.debug("ScoreMasterService.runOfScore 主线程开始等待................................................ ");

        executorService.shutdown();
        while (!executorService.isTerminated()) {
            Thread.sleep(1000);
        }

//        countDownLatch.await(scoreMasterIndexProperty.getAwaitTime(), TimeUnit.MINUTES);

        log.debug("ScoreMasterService.runOfScore 主线程等待结束................................................ ");
        log.debug("ScoreMasterService.runOfScore 开始拼接人员数据................................................ ");

        appendUserInfo(format);
        executorService.shutdown();

        long endTime = System.currentTimeMillis();

        log.debug(String.format("ScoreMasterService.runOfScore.SUCCESS 执行时间:[%s] 执行月份:[%s] .......................................... ", endTime - startTime, format));
    }

    /**
     * 刷新用户详细信息
     *
     * @param date 指定月份
     */
    private void appendUserInfo(String date) {
        Integer skip = 0;
        Date currentTime = new Date();
        while (true) {
            Query queryTime = new Query(Criteria.where("queryTime").is(date))
                    .skip(skip).limit(scoreMasterIndexProperty.getLimitSize());
            queryTime.fields().include("userId");

            List<ScoreInfo> scoreInfos = mongoTemplate.find(queryTime, ScoreInfo.class);
            if (CollectionUtils.isEmpty(scoreInfos)) {
                break;
            }

            List<ScoreInfo> scoreMongoUser1 = userMapper.getScoreMongoUser(scoreInfos.stream().map(ScoreInfo::getUserId).collect(Collectors.toList()));


            if (!CollectionUtils.isEmpty(scoreMongoUser1)) {
                scoreMongoUser1.forEach(x -> {
                    if (!StringUtils.isBlank(x.getUserName())) {
                        Update update = new Update()
                                .set("userName", x.getUserName())
                                .set("orgId", x.getOrgId())
                                .set("orgName", x.getOrgName())
                                .set("parentId", x.getParentId())
                                .set("orgType", x.getOrgType())
                                .set("orgTypeChild", x.getOrgTypeChild())
                                .set("orgLevel", x.getOrgLevel())
                                .set("orgCreateTime", x.getOrgCreateTime())
                                .set("createTime", currentTime);
                        mongoTemplate.updateFirst(new Query(Criteria.where("userId").is(x.getUserId()).and("queryTime").is(date)), update, ScoreInfo.class);
                    }
                });
            }

            skip += scoreMasterIndexProperty.getLimitSize();
        }
    }


    /**
     * 删除指定月的数据
     *
     * @param month yyyy-MM
     */
    private void removeOfMonthData(String month) {
        Query queryTime = new Query(Criteria.where("queryTime").is(month));
        mongoTemplate.remove(queryTime, ScoreInfo.class);
    }


    /**
     * 手动创建doc并生成索引
     */
    private void createAndIndex() {
        boolean b = mongoTemplate.collectionExists(ScoreInfo.class);
        if (!b) {
            mongoTemplate.createCollection(ScoreInfo.class);
        }
        IndexOperations indexOperations = mongoTemplate.indexOps(ScoreInfo.class);
        if (!CollectionUtils.isEmpty(indexOperations.getIndexInfo())) {
            List<String> collect = indexOperations.getIndexInfo().stream().map(IndexInfo::getName).collect(Collectors.toList());
            scoreMasterIndexProperty.getIndex().forEach(x -> {
                if (!collect.contains(x)) {
                    indexOperations.ensureIndex(new Index(x, Sort.Direction.ASC));
                }
            });
        } else {
            scoreMasterIndexProperty.getIndex().forEach(x ->
                    indexOperations.ensureIndex(new Index(x, Sort.Direction.ASC))
            );
        }
    }
}
