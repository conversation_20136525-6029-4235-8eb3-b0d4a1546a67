package com.goodsogood.ows.service.score;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.annotions.Logging;
import com.goodsogood.ows.mapper.activity.DonateUserMapper;
import com.goodsogood.ows.mapper.score.ScoreAidDecisionMapper;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.model.mongodb.dss.*;
import com.goodsogood.ows.model.vo.score.JoinVO;
import com.goodsogood.ows.model.vo.score.ScoreVo;
import com.goodsogood.ows.service.impl.DssIndexBuilder;
import com.goodsogood.ows.service.impl.DssPartyBranchBuilder;
import com.goodsogood.ows.service.impl.DssPartyCommitteeBuilder;
import com.goodsogood.ows.service.impl.DssUserBuilder;
import com.goodsogood.ows.service.volunteer.VolunteerReportService;
import com.goodsogood.ows.utils.SqlJointUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 辅助决策积分报表
 *
 * <AUTHOR> tc
 * @date 2020/11/9
 */
@Service
@Log4j2
public class ScoreAidDecisionService implements DssIndexBuilder, DssPartyCommitteeBuilder,
        DssPartyBranchBuilder, DssUserBuilder {

    private final StringRedisTemplate stringRedisTemplate;
    private final ScoreAidDecisionMapper scoreAidDecisionMapper;
    private final DonateUserMapper donateUserMapper;
    private final ScoreUserService scoreUserService;
    private final ObjectMapper objectMapper;

    @Autowired
    public ScoreAidDecisionService(StringRedisTemplate stringRedisTemplate,
                                   ScoreAidDecisionMapper scoreAidDecisionMapper,
                                   DonateUserMapper donateUserMapper,
                                   ScoreUserService scoreUserService, ObjectMapper objectMapper) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.scoreAidDecisionMapper = scoreAidDecisionMapper;
        this.donateUserMapper = donateUserMapper;
        this.scoreUserService = scoreUserService;
        this.objectMapper = objectMapper;
    }

    @AllArgsConstructor
    enum CacheKey {
        /**
         * 某年中，工委的所有积分用户id
         * %s = 工委id
         * %s = year
         */
        WORKING_COMMITTEE_YEAR_SCORE_USER_IDS("WORKING_COMMITTEE_YEAR_SCORE_USER_IDS:%s:%s"),
        /**
         * 某年中，工委的所有用户id
         * %s = 工委id
         * %s = year
         */
        WORKING_COMMITTEE_YEAR_USER_IDS("WORKING_COMMITTEE_YEAR_USER_IDS:%s:%s"),
        /**
         * 某年中。工委的学习总次数
         * %s = 工委id
         * %s = year
         */
        WORKING_COMMITTEE_YEAR_STUDY_NUMBER("WORKING_COMMITTEE_YEAR_STUDY_NUMBER:%s:%s"),
        /**
         * 某年中。区县下的学习总分数
         * %s = 工委id
         * %s = year
         */
        WORKING_COMMITTEE_YEAR_STUDY_SCORE("WORKING_COMMITTEE_YEAR_STUDY_SCORE:%s:%s"),
        /**
         * 某年中。区县下的积分捐赠总分数
         * %s = 工委id
         * %s = year
         */
        WORKING_COMMITTEE_YEAR_DONATE_SCORE("WORKING_COMMITTEE_YEAR_DONATE_SCORE:%s:%s"),


        // ------------------------------------------------------------------------
        /**
         * 某年中，党委下的所有积分用户id
         * %s = 党委ID
         * %s = year
         */
        PARTY_COMMITTEE_YEAR_SCORE_USER_IDS("PARTY_COMMITTEE_YEAR_SCORE_USER_IDS:%s:%s"),
        /**
         * 某年中，工委的所有用户id
         * %s = 党委ID
         * %s = year
         */
        PARTY_COMMITTEE_YEAR_USER_IDS("PARTY_COMMITTEE_YEAR_USER_IDS:%s:%s"),
        /**
         * 某年中。党委下的学习总次数
         * %s = 党委ID
         * %s = year
         */
        PARTY_COMMITTEE_YEAR_STUDY_NUMBER("PARTY_COMMITTEE_YEAR_STUDY_NUMBER:%s:%s"),
        /**
         * 某年中。党委下的学习总分数
         * %s = 党委ID
         * %s = year
         */
        PARTY_COMMITTEE_YEAR_STUDY_SCORE("PARTY_COMMITTEE_YEAR_STUDY_SCORE:%s:%s"),
        /**
         * 某年中。党委下的积分捐赠总分数
         * %s = 党委ID
         * %s = year
         */
        PARTY_COMMITTEE_YEAR_DONATE_SCORE("PARTY_COMMITTEE_YEAR_DONATE_SCORE:%s:%s"),


        // ------------------------------------------------------------------------
        /**
         * 某年中，党支部下的所有积分用户id
         * %s = 党委ID
         * %s = year
         */
        PARTY_BRANCH_YEAR_SCORE_USER_IDS("PARTY_BRANCH_YEAR_SCORE_USER_IDS:%s:%s"),
        /**
         * 某年中，党支部下的所有用户id
         * %s = 党支部ID
         * %s = year
         */
        PARTY_BRANCH_YEAR_USER_IDS("PARTY_BRANCH_YEAR_USER_IDS:%s:%s"),
        /**
         * 某年中。党支部下的学习总次数
         * %s = 党支部ID
         * %s = year
         */
        PARTY_BRANCH_YEAR_STUDY_NUMBER("PARTY_BRANCH_YEAR_STUDY_NUMBER:%s:%s"),
        /**
         * 某年中。党支部下的学习总分数
         * %s = 党支部ID
         * %s = year
         */
        PARTY_BRANCH_YEAR_STUDY_SCORE("PARTY_BRANCH_YEAR_STUDY_SCORE:%s:%s"),
        /**
         * 某年中。党支部下的积分捐赠总分数
         * %s = 党支部ID
         * %s = year
         */
        PARTY_BRANCH_YEAR_DONATE_SCORE("PARTY_BRANCH_YEAR_DONATE_SCORE:%s:%s");

        private String key;

    }

    class Context {

        private Integer year;
        private String startTime;
        private String endTime;
        private String dateMonth;

        Context(Integer year) {
            setYear(year);
        }

        public void setYear(Integer year) {
            this.year = year;
            // 开始时间
            this.startTime = VolunteerReportService.getYearStartTime(this.year);
            // 结束时间
            this.endTime = VolunteerReportService.getYearEndTime(this.year);
            // 快照时间
            this.dateMonth = this.year + "-" + getMonth(this.year);
        }
    }

    /**
     * 抽象策略
     */
    abstract class AbstractStrategy {

        private final Integer year;
        private final StudyInfo studyInfo;

        protected AbstractStrategy(Integer year, StudyInfo studyInfo) {
            this.year = year;
            this.studyInfo = studyInfo;
        }

        abstract String getScoreUserIds(Integer year,
                                        String dateMonth);

        abstract String getUserIds(Integer year,
                                   String dateMonth);

        abstract Integer curStudyTimes(Integer year,
                                       String scoreUserIds,
                                       String startTime,
                                       String endTime);

        abstract Integer curStudyScore(Integer year,
                                       String scoreUserIds,
                                       String startTime,
                                       String endTime);

        abstract Integer curDonateScore(Integer year,
                                        String startTime,
                                        String endTime,
                                        Long activityId,
                                        String userIds);

        void build() {
            // 开始时间
            String startTime = VolunteerReportService.getYearStartTime(year);
            // 结束时间
            String endTime = VolunteerReportService.getYearEndTime(year);
            // 快照时间
            String dateMonth = year + "-" + getMonth(year);

            // 获取满足条件的积分用户id
            String scoreUserIds = getScoreUserIds(year, dateMonth);

            // 本年学习人次
            setCurStudyTimes(
                    studyInfo,
                    () -> curStudyTimes(year, scoreUserIds, startTime, endTime)
            );

            // 累计学习人次
            Integer totalMember = addUp(
                    (k) -> {
                        String scoreUserIdsCache = getScoreUserIds(
                                k.year,
                                k.dateMonth
                        );
                        Integer curStudyTimes = curStudyTimes(k.year, scoreUserIdsCache, k.startTime, k.endTime);
                        return curStudyTimes;
                    },
                    year,
                    2019,
                    new Context(year)
            );
            studyInfo.setTotalMember(totalMember);

            // 本年学习积分
            setCurStudyScore(
                    studyInfo,
                    () -> curStudyScore(year, scoreUserIds, startTime, endTime)
            );

            // 累计学习积分
            Integer totalScore = addUp(
                    (k) -> {
                        String scoreUserIdsCache = getScoreUserIds(
                                k.year,
                                k.dateMonth
                        );
                        Integer curStudyScore = curStudyScore(k.year, scoreUserIdsCache, k.startTime, k.endTime);
                        return curStudyScore;
                    },
                    year,
                    2019,
                    new Context(year)
            );
            studyInfo.setTotalScore(totalScore);

            // 累计捐赠积分（一元捐）
            Integer totalDonateScore = addUp(
                    (k) -> {
                        String userIds = getUserIds(
                                k.year,
                                k.dateMonth
                        );
                        Integer curDonateScore = curDonateScore(k.year, k.startTime, k.endTime, 82L, userIds);
                        return curDonateScore;
                    },
                    year,
                    2019,
                    new Context(year)
            );
            studyInfo.setDonateScore(totalDonateScore);

            List<String> sql = new ArrayList<>(12);

            Integer month = Integer.valueOf(getMonth(year));
            for (int i = 1; i <= month; i++) {
                String firstDayOfMonth = getFirstDayOfMonth(year, i);
                String lastDayOfMonth = getLastDayOfMonth(year, i);
                sql.add(scoreAidDecisionMapper.joinSql(firstDayOfMonth, lastDayOfMonth, scoreUserIds));
            }
            // 年度学习情况走势
            setStudyTrendMap(studyInfo, () -> scoreAidDecisionMapper.studySituationInfo(sql));
        }
    }

    /**
     * 获取缓存
     *
     * @param key
     * @param supplier
     * @return
     */
    private <T> T getAndSet(String key, Supplier<T> supplier, TypeReference<T> typeReference) {
        if (stringRedisTemplate.hasKey(key)) {
            String json = stringRedisTemplate.opsForValue().get(key);
            try {
                return objectMapper.readValue(json, typeReference);
            } catch (IOException e) {
                log.error("反序列化出错， key = {}，value = {}", key, json, e);
            }
        }
        T result = supplier.get();
        if (result != null) {
            try {
                String jsonStr = objectMapper.writeValueAsString(result);
                stringRedisTemplate.opsForValue().set(key, jsonStr, 1, TimeUnit.DAYS);
            } catch (JsonProcessingException e) {
                log.error("序列化出错， key = {}，value = {}", key, result, e);
            }
        }
        return result;
    }

    /**
     * 生成缓存key
     *
     * @param cacheKey 缓存key
     * @param params   参数
     * @return
     */
    private String genCacheKey(CacheKey cacheKey, Object... params) {
        String key = String.format(cacheKey.key, params);
        return key;
    }

    /**
     * @param info 决策辅助首页实体类
     *             info.rootId 顶级党组织ID
     *             info.year   生成年份
     * @return
     */
    @Override
    @Logging
    public IndexInfo buildIndex(IndexInfo info) {
        /**
         * 初始化
         */
        if (info.getStudyInfo() == null) {
            info.setStudyInfo(new StudyInfo());
        }
        if (info.getConsumeInfo() == null) {
            info.setConsumeInfo(new ConsumeInfo());
        }
        this.findOrgInfo(info.getRegionId(), info.getYear(), info.getRootId(), info, IndexInfo.class);
        AbstractStrategy indexStrategy = new AbstractStrategy(info.getYear(), info.getStudyInfo()) {

            @Override
            String getScoreUserIds(Integer year, String dateMonth) {
                // 获取满足条件的积分用户id
                String key = genCacheKey(CacheKey.WORKING_COMMITTEE_YEAR_SCORE_USER_IDS, info.getRootId(), year);
                String scoreUserId = getAndSet(
                        key,
                        () -> {
                            List<Long> scoreUserIdsByUserSnapshot = scoreAidDecisionMapper.getScoreUserIdsByUserSnapshot(info.getRegionId(), info.getRootId(), true, dateMonth);
                            return SqlJointUtil.longListToStr(scoreUserIdsByUserSnapshot);
                        },
                        new TypeReference<String>() {
                        }
                );
                if (StringUtils.isBlank(scoreUserId)) {
                    scoreUserId = "-1";
                }
                return scoreUserId;
            }

            @Override
            String getUserIds(Integer year, String dateMonth) {
                // 获取满足条件的积分用户id
                String key = genCacheKey(CacheKey.WORKING_COMMITTEE_YEAR_USER_IDS, info.getRootId(), year);
                String userIds = getAndSet(
                        key,
                        () -> {
                            List<Long> userIdsByUserSnapshot = scoreAidDecisionMapper.getUserIdsByUserSnapshot(info.getRegionId(), info.getRootId(), true, dateMonth);
                            return SqlJointUtil.longListToStr(userIdsByUserSnapshot);
                        },
                        new TypeReference<String>() {
                        }
                );
                if (StringUtils.isBlank(userIds)) {
                    userIds = "-1";
                }
                return userIds;
            }

            @Override
            Integer curStudyTimes(Integer year, String scoreUserIds, String startTime, String endTime) {
                // 本年学习人次
                String key = genCacheKey(CacheKey.WORKING_COMMITTEE_YEAR_STUDY_NUMBER, info.getRootId(), year);
                return getAndSet(
                        key,
                        () -> {
                            Integer temp = scoreAidDecisionMapper.curStudyTimes(scoreUserIds, startTime, endTime);
                            return temp == null ? 0 : temp;
                        },
                        new TypeReference<Integer>() {
                        }
                );
            }

            @Override
            Integer curStudyScore(Integer year, String scoreUserIds, String startTime, String endTime) {
                String key = genCacheKey(CacheKey.WORKING_COMMITTEE_YEAR_STUDY_SCORE, info.getRootId(), year);
                return getAndSet(
                        key,
                        () -> {
                            Integer temp = scoreAidDecisionMapper.curStudyScore(scoreUserIds, startTime, endTime);
                            return temp == null ? 0 : temp;
                        },
                        new TypeReference<Integer>() {
                        }
                );
            }

            @Override
            Integer curDonateScore(Integer year, String startTime, String endTime, Long activityId, String userIds) {
                String donateCacheKey = genCacheKey(CacheKey.WORKING_COMMITTEE_YEAR_DONATE_SCORE, info.getRootId(), year);
                Integer score = getAndSet(
                        donateCacheKey,
                        () -> {
                            Integer temp = donateUserMapper.donateScore(startTime, endTime, 82L, userIds);
                            return temp == null ? 0 : temp;
                        },
                        new TypeReference<Integer>() {
                        }
                );
                return score;
            }
        };
        indexStrategy.build();
        return info;
    }

    /**
     * @param info 决策辅助党支部详情页实体类
     *             info.organizationId 组织ID
     *             info.year   生成年份
     * @return
     */
    @Override
    @Logging
    public PartyBranchInfo buildPartyBranch(PartyBranchInfo info) {
        /**
         * 初始化
         */
        if (info.getStudyInfo() == null) {
            info.setStudyInfo(new StudyInfo());
        }
        if (info.getConsumeInfo() == null) {
            info.setConsumeInfo(new ConsumeInfo());
        }
        this.findOrgInfo(info.getRegionId(), info.getYear(), info.getOrganizationId(), info, PartyBranchInfo.class);

        AbstractStrategy partyBranchStrategy = new AbstractStrategy(info.getYear(), info.getStudyInfo()) {

            @Override
            String getScoreUserIds(Integer year, String dateMonth) {
                String key = genCacheKey(CacheKey.PARTY_BRANCH_YEAR_SCORE_USER_IDS, info.getOrganizationId(), year);
                String scoreUserIds = getAndSet(
                        key,
                        () -> {
                            List<Long> scoreUserIdsByUserSnapshot = scoreAidDecisionMapper.getScoreUserIdsByUserSnapshot(info.getRegionId(), info.getOrganizationId(), false, dateMonth);
                            return SqlJointUtil.longListToStr(scoreUserIdsByUserSnapshot);
                        },
                        new TypeReference<String>() {
                        }
                );
                if (StringUtils.isBlank(scoreUserIds)) {
                    scoreUserIds = "-1";
                }
                return scoreUserIds;
            }

            @Override
            String getUserIds(Integer year, String dateMonth) {
                // 获取满足条件的积分用户id
                String key = genCacheKey(CacheKey.PARTY_BRANCH_YEAR_USER_IDS, info.getOrganizationId(), year);
                String userIds = getAndSet(
                        key,
                        () -> {
                            List<Long> userIdsByUserSnapshot = scoreAidDecisionMapper.getUserIdsByUserSnapshot(info.getRegionId(), info.getOrganizationId(), false, dateMonth);
                            return SqlJointUtil.longListToStr(userIdsByUserSnapshot);
                        },
                        new TypeReference<String>() {
                        }
                );
                if (StringUtils.isBlank(userIds)) {
                    userIds = "-1";
                }
                return userIds;
            }

            @Override
            Integer curStudyTimes(Integer year, String scoreUserIds, String startTime, String endTime) {
                String key = genCacheKey(CacheKey.PARTY_BRANCH_YEAR_STUDY_NUMBER, info.getOrganizationId(), year);
                return getAndSet(
                        key,
                        () -> {
                            Integer temp = scoreAidDecisionMapper.curStudyTimes(scoreUserIds, startTime, endTime);
                            return temp == null ? 0 : temp;
                        },
                        new TypeReference<Integer>() {
                        }
                );
            }

            @Override
            Integer curStudyScore(Integer year, String scoreUserIds, String startTime, String endTime) {
                String key = genCacheKey(CacheKey.PARTY_BRANCH_YEAR_STUDY_SCORE, info.getOrganizationId(), year);
                return getAndSet(
                        key,
                        () -> {
                            Integer temp = scoreAidDecisionMapper.curStudyScore(scoreUserIds, startTime, endTime);
                            return temp == null ? 0 : temp;
                        },
                        new TypeReference<Integer>() {
                        }
                );
            }

            @Override
            Integer curDonateScore(Integer year, String startTime, String endTime, Long activityId, String userIds) {
                String donateCacheKey = genCacheKey(CacheKey.PARTY_BRANCH_YEAR_DONATE_SCORE, info.getOrganizationId(), year);
                Integer score = getAndSet(
                        donateCacheKey,
                        () -> {
                            Integer temp = donateUserMapper.donateScore(startTime, endTime, 82L, userIds);
                            return temp == null ? 0 : temp;
                        },
                        new TypeReference<Integer>() {
                        }
                );
                return score;
            }
        };
        partyBranchStrategy.build();
        return info;
    }

    /**
     * @param info 决策辅助党委详情页实体类
     *             info.organizationId 组织ID
     *             info.year   生成年份
     * @return
     */
    @Override
    @Logging
    public PartyCommitteeInfo buildPartyCommittee(PartyCommitteeInfo info) {
        /**
         * 初始化
         */
        if (info.getStudyInfo() == null) {
            info.setStudyInfo(new StudyInfo());
        }
        if (info.getConsumeInfo() == null) {
            info.setConsumeInfo(new ConsumeInfo());
        }
        this.findOrgInfo(info.getRegionId(), info.getYear(), info.getOrganizationId(), info, PartyCommitteeInfo.class);

        AbstractStrategy partyCommitteeStrategy = new AbstractStrategy(info.getYear(), info.getStudyInfo()) {

            @Override
            String getScoreUserIds(Integer year, String dateMonth) {
                String key = genCacheKey(CacheKey.PARTY_COMMITTEE_YEAR_SCORE_USER_IDS, info.getOrganizationId(), year);
                String scoreUserIds = getAndSet(
                        key,
                        () -> {
                            List<Long> scoreUserIdsByUserSnapshot = scoreAidDecisionMapper.getScoreUserIdsByUserSnapshot(info.getRegionId(), info.getOrganizationId(), true, dateMonth);
                            return SqlJointUtil.longListToStr(scoreUserIdsByUserSnapshot);
                        },
                        new TypeReference<String>() {
                        }
                );
                if (StringUtils.isBlank(scoreUserIds)) {
                    scoreUserIds = "-1";
                }
                return scoreUserIds;
            }

            @Override
            String getUserIds(Integer year, String dateMonth) {
                String key = genCacheKey(CacheKey.PARTY_COMMITTEE_YEAR_USER_IDS, info.getOrganizationId(), year);
                String userIds = getAndSet(
                        key,
                        () -> {
                            List<Long> userIdsByUserSnapshot = scoreAidDecisionMapper.getUserIdsByUserSnapshot(info.getRegionId(), info.getOrganizationId(), true, dateMonth);
                            return SqlJointUtil.longListToStr(userIdsByUserSnapshot);
                        },
                        new TypeReference<String>() {
                        }
                );
                if (StringUtils.isBlank(userIds)) {
                    userIds = "-1";
                }
                return userIds;
            }

            @Override
            Integer curStudyTimes(Integer year, String scoreUserIds, String startTime, String endTime) {
                String key = genCacheKey(CacheKey.PARTY_COMMITTEE_YEAR_STUDY_NUMBER, info.getOrganizationId(), year);
                return getAndSet(
                        key,
                        () -> {
                            Integer temp = scoreAidDecisionMapper.curStudyTimes(scoreUserIds, startTime, endTime);
                            return temp == null ? 0 : temp;
                        },
                        new TypeReference<Integer>() {
                        }
                );
            }

            @Override
            Integer curStudyScore(Integer year, String scoreUserIds, String startTime, String endTime) {
                String key = genCacheKey(CacheKey.PARTY_COMMITTEE_YEAR_STUDY_SCORE, info.getOrganizationId(), year);
                return getAndSet(
                        key,
                        () -> {
                            Integer temp = scoreAidDecisionMapper.curStudyScore(scoreUserIds, startTime, endTime);
                            return temp == null ? 0 : temp;
                        },
                        new TypeReference<Integer>() {
                        }
                );
            }

            @Override
            Integer curDonateScore(Integer year, String startTime, String endTime, Long activityId, String userIds) {
                String donateCacheKey = genCacheKey(CacheKey.PARTY_COMMITTEE_YEAR_DONATE_SCORE, info.getOrganizationId(), year);
                Integer score = getAndSet(
                        donateCacheKey,
                        () -> {
                            Integer temp = donateUserMapper.donateScore(startTime, endTime, 82L, userIds);
                            return temp == null ? 0 : temp;
                        },
                        new TypeReference<Integer>() {
                        }
                );
                return score;
            }
        };
        partyCommitteeStrategy.build();
        return info;
    }

    /**
     * @param info 决策辅助用户详情页实体类
     *             info.organizationId 组织ID
     *             info.year   生成年份
     * @return
     */
    @Override
    @Logging
    public UserInfo buildUser(UserInfo info) {
        return buildUserList(Collections.singletonList(info)).get(0);
    }

    @Override
    @Logging
    public List<UserInfo> buildUserList(List<UserInfo> infoList) {
        if (CollectionUtils.isEmpty(infoList)) {
            return infoList;
        }
        List<Long> userIds = new ArrayList<>(infoList.size());
        Map<Long, UserInfo> userInfoMap = new HashMap<>();
        infoList.forEach(t -> {
            /**
             * 初始化
             */
            if (t.getStudyInfo() == null) {
                UserStudyInfo userStudyInfo = new UserStudyInfo();
                userStudyInfo.setStudyNum(0);
                userStudyInfo.setStudyInScore(0);
                userStudyInfo.setBook(0);
                userStudyInfo.setStudyOutScore(0);
                userStudyInfo.setDonateScore(0);
                t.setStudyInfo(userStudyInfo);
            }
            if (t.getDonateInfo() == null) {
                UserDonateInfo userDonateInfo = new UserDonateInfo();
                userDonateInfo.setOrderNum(0);
                userDonateInfo.setAmount(0.0D);
                userDonateInfo.setPreference(Lists.newArrayList());
                t.setDonateInfo(userDonateInfo);
            }
            userIds.add(t.getUserId());
            userInfoMap.put(t.getUserId(), t);
        });
        //消费扶贫和积分换书
        this.findUserInfoBatch(infoList.get(0).getYear(),userIds,userInfoMap);

        // 结束时间
        String endTime = VolunteerReportService.getYearEndTime(infoList.get(0).getYear());
        // 批量换取积分用户id，key = scoreUserId，value = userId
        Map<Long, Long> scoreUserIdMap = scoreUserService.userToScoreUserIds(userIds);
        if (CollectionUtils.isEmpty(scoreUserIdMap)) {
            scoreUserIdMap = new HashMap<>();
            scoreUserIdMap.put(-1L, -1L);
        }
        String scoreUserIds = SqlJointUtil.collectionToStr(scoreUserIdMap.entrySet(), k -> k.getKey().toString());

        // 累计在七一书院参加学习次数
        List<JoinVO> joinNum = scoreAidDecisionMapper.studySituationInfoUser(scoreUserIds, endTime);
        if (!CollectionUtils.isEmpty(joinNum)) {
            Map<Long, Long> finalScoreUserIdMap = scoreUserIdMap;
            joinNum.forEach(t -> {
                Long userId = finalScoreUserIdMap.get(t.getScoreUserId());
                userInfoMap.get(userId).getStudyInfo().setStudyNum(t.getJoinTotal() == null ? 0 : t.getJoinTotal());
            });
        }

        // 累计获得学分
        List<JoinVO> totalScore = scoreAidDecisionMapper.totalScoreUser(scoreUserIds, endTime);
        if (!CollectionUtils.isEmpty(totalScore)) {
            Map<Long, Long> finalScoreUserIdMap1 = scoreUserIdMap;
            totalScore.forEach(t -> {
                Long userId = finalScoreUserIdMap1.get(t.getScoreUserId());
                userInfoMap.get(userId).getStudyInfo().setStudyInScore(t.getScore() == null ? 0 : t.getScore());
            });
        }

        // 一元捐累计捐赠积分
        List<JoinVO> donateScore = donateUserMapper.donateScoreUser(SqlJointUtil.longListToStr(userIds), endTime, 82L);
        if (!CollectionUtils.isEmpty(donateScore)) {
            donateScore.forEach(t -> {
                Long userId = t.getUserId();
                userInfoMap.get(userId).getStudyInfo().setDonateScore(t.getScore() == null ? 0 : t.getScore());
            });
        }
        return infoList;
    }

    /**
     * 根据年份确定月份值,如果是当年月份为当前月份，历史年份使用12月
     */
    private String getMonth(Integer year) {
        Integer yearNow = LocalDate.now().getYear();
        String month = "12";
        if (yearNow.equals(year)) {
            Integer m = LocalDate.now().getMonthValue();
            month = m < 10 ? "0" + m : "" + m;
        }
        return month;
    }

    /**
     * 累加
     *
     * @param function
     * @param nowYear
     * @param endYear
     * @param context
     * @return
     */
    private Integer addUp(Function<Context, Integer> function,
                          int nowYear,
                          int endYear,
                          Context context) {
        Integer total = 0;
        while (endYear <= nowYear) {
            Integer apply = function.apply(context);
            apply = apply == null ? 0 : apply;
            total += apply;
            nowYear--;
            context.setYear(nowYear);
        }
        return total;
    }

    /**
     * 本年学习人次
     *
     * @param studyInfo 学习信息对象
     * @param supplier  获取数据函数
     */
    private void setCurStudyTimes(StudyInfo studyInfo,
                                  Supplier<Integer> supplier) {
        Integer total = supplier.get();
        total = total == null ? 0 : total;
        studyInfo.setCurStudyTimes(total);
    }

    /**
     * 本年学习积分
     *
     * @param studyInfo
     * @param supplier
     */
    private void setCurStudyScore(StudyInfo studyInfo,
                                  Supplier<Integer> supplier) {
        Integer total = supplier.get();
        total = total == null ? 0 : total;
        studyInfo.setCurStudyScore(total);
    }

    /**
     * 年度学习情况走势
     *
     * @param studyInfo
     * @param supplier
     */
    private void setStudyTrendMap(StudyInfo studyInfo,
                                  Supplier<List<StudySituationInfo>> supplier) {
        List<StudySituationInfo> studySituationInfos = supplier.get();
        studySituationInfos = studySituationInfos == null ? new ArrayList<>(24) : studySituationInfos;
        for (int i = 0; i < studySituationInfos.size(); i++) {
            if (studySituationInfos.get(i) == null || studySituationInfos.get(i).getTime() == null) {
                studySituationInfos.remove(i);
            }
        }
        HashMap<Integer, Integer> tempMonthNum = new HashMap<>(24);
        if (!CollectionUtils.isEmpty(studySituationInfos)) {
            studySituationInfos.forEach(x -> tempMonthNum.put(x.getTime(), x.getTotal() == null ? 0 : x.getTotal()));
        }
        List<Integer> result = new ArrayList<>(12);
        for (int i = 0; i < 24; i++) {
            result.add(tempMonthNum.getOrDefault(i, 0));
        }
        List<Integer> studyTrend = new ArrayList<>(12);
        for (int i = 0; i <= result.size(); i++) {
            if ((i + 1) % 2 == 0) {
                studyTrend.add((result.get(i) + result.get(i - 1)) / 2);
            }
        }
        studyInfo.setStudyTrendMap(studyTrend);
    }

    /**
     * 首页，党委，党支部页面 通用的设置数据方法
     */
    private void findOrgInfo(Long regionId, Integer year, Long orgId, DssBaseInfo info, Class className) {
        try {
            //累计时的终止年份
            Integer endYear = 2019;
            /**
             * 反射获取要设置的属性
             */
            StudyInfo studyInfo = (StudyInfo) ReflectionUtils.findMethod(className, "getStudyInfo").invoke(info);
            ConsumeInfo consumeInfo = (ConsumeInfo) ReflectionUtils.findMethod(className, "getConsumeInfo").invoke(info);
            /**
             * 累计兑换图书，累计兑换积分
             */
            Integer totalBook = 0;
            Integer totalExchangeScore = 0;
            for (int i = year; i >= endYear; i--) {
                StudyInfo vo1 = scoreAidDecisionMapper.orgBookOrder(regionId, String.valueOf(i), this.getMonth(i), orgId);
                totalBook += vo1.getTotalBook();
                totalExchangeScore += vo1.getTotalExchangeScore();
            }
            //设置积分换书数量
            studyInfo.setTotalBook(totalBook);
            //设置积分换书兑换积分
            studyInfo.setTotalExchangeScore(totalExchangeScore);
            /**
             * 扶贫消费累计
             */
            Integer buyTotal = 0;
            Double payTotal = 0.0;
            Integer userTotal = 0;
            Double donateTotal = 0.0;
            for (int i = year; i >= endYear; i--) {
                ConsumeInfo vo2 = scoreAidDecisionMapper.orgPovertyOrder(regionId, String.valueOf(i), this.getMonth(i), orgId);
                buyTotal += vo2.getBuyTotal();
                payTotal += vo2.getPayTotal();
                userTotal += vo2.getUserTotal();
                donateTotal += vo2.getDonateTotal();
                //遇到今年的就设置一下对应的值
                if (i == year) {
                    /**
                     * 扶贫消费年度
                     */
                    consumeInfo.setYearBuyTotal(vo2.getBuyTotal());
                    consumeInfo.setYearPayTotal(vo2.getPayTotal());
                    consumeInfo.setYearUserTotal(vo2.getUserTotal());
                    consumeInfo.setYearDonateTotal(vo2.getDonateTotal());
                }
            }
            consumeInfo.setBuyTotal(buyTotal);
            consumeInfo.setPayTotal(payTotal);
            consumeInfo.setUserTotal(userTotal);
            consumeInfo.setDonateTotal(donateTotal);

            /**
             * 消费扶贫产品类别饼图
             */
            List<PieObject> vo4 = scoreAidDecisionMapper.orgPovertyPie(regionId, year.toString(), this.getMonth(year), orgId);
            consumeInfo.setConsumeMap(vo4);
        } catch (Exception e) {
            log.error("<积分模块> 首页，党委，党支部通用的设置数据方法,报错！ year={} orgId={} className ={}", year, orgId, className, e);
        }
    }

    /**
     * 用户页面设置数据
     *
     * @param info
     */
    private void findUserInfo(UserInfo info) {
        try {
            UserStudyInfo userStudyInfo = info.getStudyInfo();
            UserDonateInfo userDonateInfo = info.getDonateInfo();
            UserStudyInfo vo1 = scoreAidDecisionMapper.userBookOrder(info.getYear().toString(), info.getUserId());
            userStudyInfo.setBook(vo1.getBook());
            userStudyInfo.setStudyOutScore(vo1.getStudyOutScore());
            UserDonateInfo vo2 = scoreAidDecisionMapper.userPovertyOrder(info.getYear().toString(), info.getUserId());
            userDonateInfo.setOrderNum(vo2.getOrderNum());
            userDonateInfo.setAmount(vo2.getAmount());
            List<PieObject> vo3 = scoreAidDecisionMapper.userPovertyPie(info.getYear().toString(), info.getUserId());
            userDonateInfo.setPreference(vo3);
        } catch (Exception e) {
            log.error("<积分模块> 用户页面设置数据，报错！ info={}", info, e);
        }
    }

    /**
     * 批量用户页面设置数据
     *
     * @param userIds
     * @param userInfoMap
     */
    private void findUserInfoBatch(Integer year,List<Long> userIds,Map<Long, UserInfo> userInfoMap) {
        try {
            String userIdString = userIds.stream().map(id->id.toString()).collect(Collectors.joining(","));
            //党员累计兑换图书，累计兑换积分(批量查询)
            List<ScoreVo> vo1 = scoreAidDecisionMapper.userBookOrderBatch(year.toString(), userIdString);
            if (!CollectionUtils.isEmpty(vo1)) {
                vo1.forEach(v -> {
                    Long userId = v.getUserId();
                    userInfoMap.get(userId).getStudyInfo().setBook(v.getBook());
                    userInfoMap.get(userId).getStudyInfo().setStudyOutScore(v.getUseScore());
                });
            }
            //党员扶贫消费年度统计(批量)
            List<ScoreVo> vo2 = scoreAidDecisionMapper.userPovertyOrderBatch(year.toString(), userIdString);
            if (!CollectionUtils.isEmpty(vo2)) {
                vo2.forEach(v -> {
                    Long userId = v.getUserId();
                    userInfoMap.get(userId).getDonateInfo().setOrderNum(v.getOrderNum());
                    userInfoMap.get(userId).getDonateInfo().setAmount(v.getAmount());
                });
            }
            //党员消费扶贫产品类别饼图(批量)
            List<ScoreVo> vo3 = scoreAidDecisionMapper.userPovertyPieBatch(year.toString(), userIdString);
            if (!CollectionUtils.isEmpty(vo3)) {
                //记录上一个用户编号，初始化为第一个用户编号
                Long historyUserId = vo3.get(0).getUserId();
                List<PieObject> p = new ArrayList<>(5);
                for(ScoreVo v:vo3){
                    Long userId = v.getUserId();
                    //如果用户编号切换，加入
                    if(!userId.equals(historyUserId)){
                        //记录上一个用户记录
                        userInfoMap.get(historyUserId).getDonateInfo().setPreference(p);
                        //从新初始化
                        p = new ArrayList<>(5);
                        historyUserId = userId;
                    }
                    PieObject po = new PieObject();
                    po.setName(v.getName());
                    po.setValue(v.getValue());
                    p.add(po);
                }
                //记录最后一个用户的信息
                userInfoMap.get(historyUserId).getDonateInfo().setPreference(p);
            }
        } catch (Exception e) {
            log.error("<积分模块> 批量用户页面设置数据，报错！ userIds={}", userIds, e);
        }
    }

    /**
     * 获得该月第一天
     *
     * @param year
     * @param month
     * @return
     */
    public static String getFirstDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最小天数
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String firstDayOfMonth = sdf.format(cal.getTime());
        return firstDayOfMonth;
    }

    /**
     * 获得该月最后一天
     *
     * @param year
     * @param month
     * @return
     */
    public static String getLastDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String lastDayOfMonth = sdf.format(cal.getTime());
        return lastDayOfMonth;
    }

}

