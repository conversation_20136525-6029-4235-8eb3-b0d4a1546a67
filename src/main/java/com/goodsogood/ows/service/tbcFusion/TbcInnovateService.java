package com.goodsogood.ows.service.tbcFusion;

import com.goodsogood.ows.configuration.TbcMonitorsTagConfig;
import com.goodsogood.ows.mapper.tbcFusion.TbcBasicsMapper;
import com.goodsogood.ows.model.vo.tbcFusion.TbcInnovateForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 创新情况统计
 *
 * <AUTHOR>
 * @date 2021/12/03
 */
@Service
public class TbcInnovateService {

    private final TbcBasicsMapper tbcBasicsMapper;
    private final TbcMonitorsTagConfig tbcMonitorsTagConfig;

    @Autowired
    public TbcInnovateService(TbcBasicsMapper tbcBasicsMapper, TbcMonitorsTagConfig tbcMonitorsTagConfig) {
        this.tbcBasicsMapper = tbcBasicsMapper;
        this.tbcMonitorsTagConfig = tbcMonitorsTagConfig;
    }

    /**
     * 创新情况统计(词云)
     *
     * @return
     */
    public List<TbcInnovateForm> getTbcInnovate() {
        List<TbcInnovateForm> list = new ArrayList<>();
        List<String> cloudWord = tbcBasicsMapper.getCloudWord(tbcMonitorsTagConfig.getExcludeOrgIds());
        if (!CollectionUtils.isEmpty(cloudWord)){
            DecimalFormat df = new DecimalFormat("#.00");
            for (String s : cloudWord) {
                TbcInnovateForm form = new TbcInnovateForm();
                form.setName(getCloudWord().get((int) (Math.random() * 8)));
                form.setValue(df.format(Math.random() * 23.1));
                form.setColorField((int) (Math.random() * 5));
                list.add(form);
            }
        }
        return list;
    }

    // 获取假词云
    public Map<Integer,String> getCloudWord(){
        Map<Integer,String> map = new HashMap<>();
        map.put(1,"营销创新");
        map.put(2,"流程创新");
        map.put(3,"管理创新");
        map.put(4,"工具创新");
        map.put(5,"技术创新");
        map.put(6,"活动创新");
        map.put(7,"营销创新");
        map.put(8,"活动创新");
        return map;
    }

    /**
     * 词云标题
     * @return
     */
    public String getOrgPioneerTitle() {
        // 任务完成数
        Integer cloudTaskNumber = tbcBasicsMapper.getCloudTaskNumber(tbcMonitorsTagConfig.getExcludeOrgIds());
        // 党员参与人数
        Integer partyMemberNumber = tbcBasicsMapper.getPartyMemberNumber(tbcMonitorsTagConfig.getExcludeOrgIds());
        // 非党员参与人数
        Integer notPartyMemberNumber = tbcBasicsMapper.getNotPartyMemberNumber();
        return String.format(tbcMonitorsTagConfig.getTarget().get(1), cloudTaskNumber, partyMemberNumber, notPartyMemberNumber);
    }
}
