package com.goodsogood.ows.service.tbcFusion

import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.OrgTypeConfig
import com.goodsogood.ows.configuration.TbcMonitorsTagConfig
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.mapper.sas.FusionItemMapper
import com.goodsogood.ows.mapper.tbcFusion.TbcBasicsMapper
import com.goodsogood.ows.model.mongodb.fusion.FusionItemData
import com.goodsogood.ows.model.vo.fusion.*
import com.goodsogood.ows.utils.ArithUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.stereotype.Service

/**
 *
 * <AUTHOR>
 * @createTime 2023年04月06日 10:36:00
 */
@Service
class FusionScreenService @Autowired constructor(
    private val orgTypeConfig: OrgTypeConfig,
    private val tbcBasicsMapper: TbcBasicsMapper,
    private val mongoTemplate: MyMongoTemplate,
    private val fusionItemMapper: FusionItemMapper,
    private val tbcMonitorsTagConfig: TbcMonitorsTagConfig
) {


    /**
     * 2指数
     */
    fun getScreenTwoIndex(year: Int?, month: Int?): FusionScreenVO {
        val staMonth = if (month in 1..9) {
            "${year}0${month}"
        } else if (month in 10..12) {
            "${year}${month}"
        } else {
            throw ApiException("传入月份数据不对。")
        }
        return FusionScreenVO(
            // 支部堡垒指数
            orgFortress = this.tbcBasicsMapper.queryFortressStat(staMonth,
                    tbcMonitorsTagConfig.excludeOrgIds,
                    orgTypeConfig.branchChild),
            // 党员先锋指数
            memberPioneer = this.tbcBasicsMapper.queryIndexStat(staMonth, tbcMonitorsTagConfig.excludeOrgIds)
        )
    }

    /**
     * 3指标
     */
    fun getScreenThreeIndicators(year: Int?, month: Int?): ThreeIndicatorsVO {
        val staMonth = if (month in 1..9) {
            "${year}0${month}"
        } else if (month in 10..12) {
            "${year}${month}"
        } else {
            throw ApiException("传入月份数据不对。")
        }
        return ThreeIndicatorsVO(
            org = IndicatorsVO(
                build = this.tbcBasicsMapper.queryBuildFortressStat(staMonth, orgTypeConfig.branchChild),
                business = this.tbcBasicsMapper.queryBusinessFortressStat(staMonth, orgTypeConfig.branchChild),
                innovate = this.tbcBasicsMapper.queryInnovationFortressStat(staMonth, orgTypeConfig.branchChild)
            ),
            user = IndicatorsVO(
                build = this.tbcBasicsMapper.queryBuildIndexStat(staMonth),
                business = this.tbcBasicsMapper.queryBusinessIndexStat(staMonth),
                innovate = this.tbcBasicsMapper.queryInnovationIndexStat(staMonth)
            )
        )
    }

    fun getFourFusion(year: Int?, month: Int?): FourFusionVO {
        val itemEntities = this.fusionItemMapper.selectAll()
        val map = itemEntities.groupBy { it.category }.mapValues { (_, v) ->
            v.map { it.fusionItemId }
        }
        val itemData = findFusionItemData(year, month)
        val values = itemData.groupBy { it.unitId }.mapValues { (_, v) ->
            FusionVO(
                target = v.filter { map[1]!!.contains(it.itemId) }.sumOf { it.score },
                org = v.filter { map[2]!!.contains(it.itemId) }.sumOf { it.score },
                work = v.filter { map[3]!!.contains(it.itemId) }.sumOf { it.score },
                data = v.filter { map[4]!!.contains(it.itemId) }.sumOf { it.score }
            )
        }
        val targetList = values.values.map { it.target }
        val orgList = values.values.map { it.org }
        val workList = values.values.map { it.work }
        val dataList = values.values.map { it.data }
        return FourFusionVO(
            target = IndexVO(avg = ArithUtils.round(targetList.average(), 2), max = targetList.max()),
            org = IndexVO(avg = ArithUtils.round(orgList.average(), 2), max = orgList.max()),
            work = IndexVO(avg = ArithUtils.round(workList.average(), 2), max = workList.max()),
            data = IndexVO(avg = ArithUtils.round(dataList.average(), 2), max = dataList.max()),
        )
    }

    private fun findFusionItemData(year: Int?, month: Int?): MutableList<FusionItemData> {
        val criteria = Criteria.where("year").`is`(year)
            .and("month").`is`(month)
        val query = Query(criteria)
        return this.mongoTemplate.find(query, FusionItemData::class.java)
    }
}