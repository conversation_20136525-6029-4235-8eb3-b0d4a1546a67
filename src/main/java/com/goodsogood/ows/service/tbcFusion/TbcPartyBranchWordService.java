package com.goodsogood.ows.service.tbcFusion;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.TbcMonitorsTagConfig;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.UserItemVO;
import com.goodsogood.ows.model.vo.UserSeqVO;
import com.goodsogood.ows.model.vo.tbcFusion.TbcPartyIndexForm;
import com.goodsogood.ows.model.vo.tbcFusion.TbcPartyMemberWorkForm;
import com.goodsogood.ows.model.vo.tbcFusion.TbcUserIndexForm;
import com.goodsogood.ows.service.user.UserStatisticalService;
import com.goodsogood.ows.utils.NumberUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 党员"党建+业务"工作情况
 *
 * <AUTHOR>
 * @date 2021/12/03
 */
@Service
public class TbcPartyBranchWordService {
    private final TbcBasicsService tbcBasicsService;
    private final Errors errors;
    private final TbcMonitorsTagConfig tbcMonitorsTagConfig;
    private final UserStatisticalService userStatisticalService;
    private static final Map<String, Integer> mapAge=new LinkedHashMap<>();
    private static final Map<String, Integer> mapEducation=new LinkedHashMap<>();

    static {
        //设置年龄
        mapAge.put("30岁及以下", 0);
        mapAge.put("31岁~45岁", 1);
        mapAge.put("46岁~55岁", 2);
        mapAge.put("55岁以上", 3);
        //设置学历
        mapEducation.put("高中生及以下", 0);
        mapEducation.put("专科", 1);
        mapEducation.put("本科", 2);
        mapEducation.put("研究生及以上", 3);
    }

    public TbcPartyBranchWordService(TbcBasicsService tbcBasicsService, Errors errors,
                                     TbcMonitorsTagConfig tbcMonitorsTagConfig,
                                     UserStatisticalService userStatisticalService) {
        this.tbcBasicsService = tbcBasicsService;
        this.errors = errors;
        this.tbcMonitorsTagConfig = tbcMonitorsTagConfig;
        this.userStatisticalService = userStatisticalService;
    }

    /**
     * 党员”党建+业务”工作情况柱状图
     *
     * @return
     */
    public TbcPartyMemberWorkForm getPartyMember() {
        TbcPartyMemberWorkForm form = new TbcPartyMemberWorkForm();
        List<List<Integer>> list = new ArrayList<>();
        List<String> sData = new ArrayList<>();
        sData.add("综合管理");
        sData.add("烟叶生产");
        sData.add("专卖管理");
        sData.add("卷烟销售");
        List<String> xData = new ArrayList<>();
        xData.add("堡垒指数");
        xData.add("拟合度");
        xData.add("党建指标");
        xData.add("业务指标");
        for (int i = 1; i <= 4; i++) {
            List<Integer> array = new ArrayList<>();
            for (int j = 1; j <= 4; j++) {
                int num = (int) (Math.random() * 100);
                array.add(num);
            }
            list.add(array);
        }
        for (int i = 0; i < list.size(); i++) {
            switch (i) {
                case 0:
                    form.setD1(list.get(i));
                    continue;
                case 1:
                    form.setD2(list.get(i));
                    continue;
                case 2:
                    form.setD3(list.get(i));
                    continue;
                case 3:
                    form.setD4(list.get(i));
                    break;
            }
        }
        form.setSData(sData);
        form.setXData(xData);
        return form;
    }

    /**
     * 党员”党建+业务”工作情况柱状图
     * @return
     */
    public TbcPartyMemberWorkForm getPartyMemberNew(int type) {
        String excludeOrgIds = tbcMonitorsTagConfig.getExcludeOrgIds();
        TbcPartyMemberWorkForm tbcOrgPartyMemberForm = new TbcPartyMemberWorkForm();
        List<String> listXData = new LinkedList<>();
        listXData.add("业务指标");
        listXData.add("党建指标");
        listXData.add("创新指标");
        listXData.add("先锋指数");
        tbcOrgPartyMemberForm.setXData(listXData);
        List<String> listSData = new LinkedList<>();
        tbcOrgPartyMemberForm.setSData(listSData);
        if (type == 1) {
            List<UserSeqVO> userSeq1 = tbcBasicsService.getUserSeq(null,excludeOrgIds);
            listSData.add("综合管理");
            listSData.add("烟叶生产");
            listSData.add("专卖管理");
            listSData.add("卷烟销售");
            if (!CollectionUtils.isEmpty(userSeq1)) {
                List<UserSeqVO> userSeq = userSeq1.stream()
                        .sorted(Comparator.comparing(UserSeqVO::getType).reversed()).collect(Collectors.toList());
                //重写type 满足数组强对应关系
                userSeq.stream().forEach(item->{
                    item.setType(item.getType()-1);
                });
                userSeq.stream().forEach(item -> {
                    resetTbcPartyMemberWorkForm(tbcOrgPartyMemberForm, item, null);
                });
            }
            return tbcOrgPartyMemberForm;
        } else if (type == 2) {
            List<UserItemVO> userDistributed = userStatisticalService.getUserDistributed(86L, 1,excludeOrgIds);
            mapAge.forEach((k,v)->{
                Optional<UserItemVO> optional = userDistributed.stream().filter(item -> {
                    return Objects.equals(item.getItem(), k);
                }).findFirst();
                if(optional.isPresent()){
                    UserItemVO userItemVO = optional.get();
                    userItemVO.setType(v);
                    listSData.add(userItemVO.getItem());
                    resetTbcPartyMemberWorkForm(tbcOrgPartyMemberForm, null, userItemVO);
                }
            });
            return tbcOrgPartyMemberForm;
        } else if (type == 3) {
            List<UserItemVO> userDistributed = userStatisticalService.getUserDistributed(86L, 2,excludeOrgIds);
            mapEducation.forEach((k,v)->{
                Optional<UserItemVO> optional = userDistributed.stream().filter(item -> {
                    return Objects.equals(item.getItem(), k);
                }).findFirst();
                if(optional.isPresent()){
                    UserItemVO userItemVO = optional.get();
                    userItemVO.setType(v);
                    listSData.add(userItemVO.getItem());
                    resetTbcPartyMemberWorkForm(tbcOrgPartyMemberForm, null, userItemVO);
                }
            });
            return tbcOrgPartyMemberForm;
        } else {
            return null;
        }
    }


    /**
     * 重置信息 序列
     *
     * @param tbcOrgPartyMemberForm
     */
    private void resetTbcPartyMemberWorkForm(TbcPartyMemberWorkForm tbcOrgPartyMemberForm, UserSeqVO vo1, UserItemVO vo2) {
        TbcMonitorsTagConfig.CalPercentage tbc = tbcMonitorsTagConfig.getMemberParty();
        Map<Integer, List<Integer>> mapList = new LinkedHashMap<>();
        mapList.put(0, tbcOrgPartyMemberForm.getD1());
        mapList.put(1, tbcOrgPartyMemberForm.getD2());
        mapList.put(2, tbcOrgPartyMemberForm.getD3());
        mapList.put(3, tbcOrgPartyMemberForm.getD4());

        int index = 0;
        List<Long> useIds = null;
        if (null != vo1) {
            index = vo1.getType();
            useIds = vo1.getUseIds();
        } else {
            index = vo2.getType();
            useIds = vo2.getUserIds();
        }
        List<TbcUserIndexForm> userIndex = tbcBasicsService.getUserIndex(useIds, null);
        double sumBusinessIndex = NumberUtils.divide(userIndex.stream().mapToDouble(TbcPartyIndexForm::getBusinessIndex).
                sum(), userIndex.size(), 1);
        double sumPartyIndex = NumberUtils.divide(userIndex.stream().mapToDouble(TbcPartyIndexForm::getPartyIndex).
                sum(), userIndex.size(), 1);
        double sumInnovationIndex = NumberUtils.divide(userIndex.stream().mapToDouble(TbcPartyIndexForm::getInnovationIndex).
                sum(), userIndex.size(), 1);
        //计算先峰指数
        double xianFengIndex = NumberUtils.divide((sumPartyIndex * tbc.getPartyIndexProportion() +
                sumBusinessIndex * tbc.getBusinessIndexProportion()
                + sumInnovationIndex * tbc.getInnovationIndexProportion()), 1, 1);
        mapList.get(index).set(0, (int) sumBusinessIndex);
        mapList.get(index).set(1, (int) sumPartyIndex);
        mapList.get(index).set(2, (int) sumInnovationIndex);
        mapList.get(index).set(3, (int) xianFengIndex);
        }

}

