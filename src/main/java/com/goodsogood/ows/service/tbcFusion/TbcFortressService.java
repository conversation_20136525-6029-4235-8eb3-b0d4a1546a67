package com.goodsogood.ows.service.tbcFusion;

import com.goodsogood.ows.configuration.TbcMonitorsTagConfig;
import com.goodsogood.ows.mapper.tbcFusion.TbcBasicsMapper;
import com.goodsogood.ows.mapper.tbcFusion.TbcFortressMapper;
import com.goodsogood.ows.model.vo.tbcFusion.TbcFortressForm;
import com.goodsogood.ows.model.vo.tbcFusion.TbcMapForm;
import com.goodsogood.ows.model.vo.tbcFusion.TbcPartyIndexForm;
import com.goodsogood.ows.model.vo.tbcFusion.TbcUserIndexForm;
import com.goodsogood.ows.utils.DateUtils;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.arrow.flatbuf.Int;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.logging.SimpleFormatter;
import java.util.stream.Collectors;

/**
 * 支部平均堡垒指数
 *
 * <AUTHOR>
 * @date 2021/12/03
 */
@Log4j2
@Service
public class TbcFortressService {

    private final TbcFortressMapper tbcFortressMapper;
    private final TbcBasicsMapper tbcBasicsMapper;
    private final TbcBasicsService tbcBasicsService;
    private final TbcMonitorsTagConfig tbcMonitorsTagConfig;

    public TbcFortressService(TbcFortressMapper tbcFortressMapper, TbcBasicsMapper tbcBasicsMapper, TbcBasicsService tbcBasicsService, TbcMonitorsTagConfig tbcMonitorsTagConfig) {
        this.tbcFortressMapper = tbcFortressMapper;
        this.tbcBasicsMapper = tbcBasicsMapper;
        this.tbcBasicsService = tbcBasicsService;
        this.tbcMonitorsTagConfig = tbcMonitorsTagConfig;
    }


    /**
     * 支部堡垒指数统计
     *
     * @return
     */
    public TbcFortressForm getTbcFortress(Integer type) {
        TbcFortressForm form = new TbcFortressForm();
        TbcPartyIndexForm indexForm;
//        DecimalFormat df = new DecimalFormat("0.0");
        String staMonth =  new SimpleDateFormat("yyyyMM").format(new Date());
        String num1;
        String num2;
        String num3;
        String num4;
        switch (type) {
            case 1:
                // 所有党支部的先锋指数
                indexForm = tbcFortressMapper.getAvgPartyOrgIndex(tbcMonitorsTagConfig.getExcludeOrgIds(),Integer.parseInt(staMonth));
                num1 = indexForm.getPartyIndex().toString();
                num2 = indexForm.getBusinessIndex().toString();
                num3 = indexForm.getInnovationIndex().toString();
                num4 = indexForm.getFortressIndex().toString();
                form.setName(String.format(tbcMonitorsTagConfig.getTarget().get(0),
                        retainDouble(num1),
                        retainDouble(num2),
                        retainDouble(num3)));
                form.setValue(retainDouble(num4));
                break;
            case 2:
                // 所有党员的先锋指数
                indexForm = tbcFortressMapper.getAvgPartyUserIndex(tbcMonitorsTagConfig.getExcludeOrgIds(),Integer.parseInt(staMonth));
                num1 = indexForm.getPartyIndex().toString();
                num2 = indexForm.getBusinessIndex().toString();
                num3 = indexForm.getInnovationIndex().toString();
                num4 = indexForm.getPartyMembersNumber().toString();
                form.setName(String.format(tbcMonitorsTagConfig.getTarget().get(0),
                        retainDouble(num1),
                        retainDouble(num2),
                        retainDouble(num3)));
                form.setValue(retainDouble(num4));
                break;
        }
        return form;
    }

    private String retainDouble(String s1) {
        if (s1.equals(".0")) {
            return "0";
        } else if (s1.endsWith(".0")) {
            return s1.substring(0, s1.indexOf("."));
        } else {
            return s1;
        }
    }


    /**
     * 获取前三的名称
     *
     * @param type 1:获取前三的党支部名称
     *             2:获取前三的党员名称
     * @return
     */
    public List<TbcUserIndexForm> getSortOrgName(Integer type, Integer page) {
        List<TbcUserIndexForm> forms = new ArrayList<>();
        String staMonth =  new SimpleDateFormat("yyyyMM").format(new Date());
        switch (type) {
            case 1:
                //获取全部数据
                forms = tbcBasicsMapper.getTopThreeMsg(tbcMonitorsTagConfig.getExcludeOrgIds(),page,Integer.parseInt(staMonth));
                break;
            case 2:
                forms = tbcBasicsMapper.getTopThreeUserOrgMsg( tbcMonitorsTagConfig.getExcludeOrgIds(),page,Integer.parseInt(staMonth));
                break;
        }
        if (!CollectionUtils.isEmpty(forms)) {
            return forms;
        } else {
            return new ArrayList<>();
        }
    }

//    /**
//     * 获取前十的名称
//     *
//     * @param type
//     * @return
//     */
//    public List<TbcUserIndexForm> getTop(Integer type) {
//        List<TbcUserIndexForm> forms = new ArrayList<>();
//        switch (type) {
//            case 1:
//                forms = tbcBasicsMapper.getTopThreeMsg( tbcMonitorsTagConfig.getExcludeOrgIds());
//                break;
//            case 2:
//                forms = tbcBasicsMapper.getTopThreeUserOrgMsg( tbcMonitorsTagConfig.getExcludeOrgIds());
//                break;
//        }
//        return forms;
//    }

    /**
     * 地图
     */
    public List<TbcMapForm> getMap(Integer type) {
        List<TbcMapForm> mapForms = new ArrayList<>();
        switch (type) {
            case 1:
                return tbcBasicsMapper.getOrgMap(DateUtils.getCurrentMonth());
            case 2:
                return tbcBasicsMapper.getUserMap(DateUtils.getCurrentMonth());
        }
        return mapForms;
    }

    /**
     * 获取支部堡垒指数 map
     * key: org_id
     * value: 支部堡垒指数
     *
     * @param indexForms
     * @return
     */
    public Map<Long, Double> getOrgExponent(List<TbcPartyIndexForm> indexForms) {
        Map<Long, Double> map = new HashMap<>();
        for (TbcPartyIndexForm x : indexForms) {
            double num1 = x.getPartyIndex();//党建指标
            double num2 = x.getBusinessIndex();//业务指标
            double num3 = x.getInnovationIndex();//创新指标
            double sum = (num1 * 0.4) + (num2 * 0.4) + (num3 * 0.2);// 支部堡垒指数
            map.put(x.getOrgId(), sum);
        }
        return map;
    }

    @Data
    public static class LongitudeAndLatitude {
        Long orgId;
        String longitude;
        String latitude;
    }
}
