package com.goodsogood.ows.service.tbcFusion;

import com.goodsogood.ows.common.Constants;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 * @description: 从redis中获取取党业融合大屏数据
 * @author: zhangtao
 * @create: 2021-12-15 15:36
 */
@Log4j2
@Service
public class TbcGetService {


    private final StringRedisTemplate redisTemplate;
    private final TbcBasicsService tbcBasicsService;
    private final TbcFortressService tbcFortressService;
    private final TbcInnovateService tbcInnovateService;
    private final TbcMatchingService tbcMatchingService;
    private final TbcPartyBranchWordService tbcPartyBranchWordService;
    private final TbcPartyMemberWorkService tbcPartyMemberWorkService;
    private final TbcPioneerIndexService tbcPioneerIndexService;

    @Autowired
    public TbcGetService(StringRedisTemplate redisTemplate,
                         TbcBasicsService tbcBasicsService,
                         TbcFortressService tbcFortressService,
                         TbcInnovateService tbcInnovateService,
                         TbcMatchingService tbcMatchingService,
                         TbcPartyBranchWordService tbcPartyBranchWordService,
                         TbcPartyMemberWorkService tbcPartyMemberWorkService,
                         TbcPioneerIndexService tbcPioneerIndexService) {
        this.redisTemplate = redisTemplate;
        this.tbcBasicsService = tbcBasicsService;
        this.tbcFortressService = tbcFortressService;
        this.tbcInnovateService = tbcInnovateService;
        this.tbcMatchingService = tbcMatchingService;
        this.tbcPartyBranchWordService = tbcPartyBranchWordService;
        this.tbcPartyMemberWorkService = tbcPartyMemberWorkService;
        this.tbcPioneerIndexService = tbcPioneerIndexService;
    }

//    String s1 = "TBC-FUSION-%s";
//
//    /**
//     * 获取党支部党树统计信息
//     *
//     * @param type 1-党支部个数 2-云上党支部个数 3-党员人数 4-职工人数
//     */
//    public String TbcOrgBasics(Integer type) {
//        switch (type) {
//            case 1:
//                String tbcKey1 = String.format(s1, Constants.BASICS);
//                return redisTemplate.opsForValue().get(tbcKey1);
//            case 2:
//                String tbcKey2 = String.format(s1, Constants.BASICS);
//                return redisTemplate.opsForValue().get(tbcKey2);
//            case 3:
//                String tbcKey3 = String.format(s1, Constants.BASICS);
//                return redisTemplate.opsForValue().get(tbcKey3);
//            case 4:
//                String tbcKey4 = String.format(s1, Constants.BASICS);
//                return redisTemplate.opsForValue().get(tbcKey4);
//        }
//        return null;
//    }
//
//    /**
//     * 党支部基础信息统计
//     *
//     * @return
//     */
//    public String TbcBasics() {
//        String key = String.format(s1, Constants.ORG_BASICS_COUNT);
//        return redisTemplate.opsForValue().get(key);
//    }
//
//    /**
//     * 支部堡垒指数和党员先锋指数统计
//     */
//    public String TbcFortress(Integer type) {
//        String key = String.format(s1, Constants.TBC_FORTRESS + type);
//        return redisTemplate.opsForValue().get(key);
//    }
//
//    /**
//     * 支部工作情况(散点图)
//     */
//    public String TbcPartyMemberWork() {
//        String key = String.format(s1, Constants.ORG_SPLASHES);
//        return redisTemplate.opsForValue().get(key);
//    }
//
//    /**
//     * 党员”党建+业务”工作情况(柱状图)
//     */
//    public String TbcPartyBranchWord(Integer type) {
//        String key = String.format(s1, Constants.ORG_PARTY_MEMBER);
//        System.out.println(key);
//        return redisTemplate.opsForValue().get(key);
//    }
//
//    /**
//     * 党员先锋指数前20%(饼状图)
//     *
//     * @param type 1-所属序列统计 2-学历统计
//     */
//    public String TbcPioneerIndex(Integer type) {
//        // TODO
//        String key1 = String.format(s1, Constants.ORG_PIONEER_INDEX1);
//        return redisTemplate.opsForValue().get(key1);
//    }
//
//    /**
//     * 创新情况统计(词云)
//     */
//    public String TbcInnovate() {
//        String key = String.format(s1, Constants.TBC_INNOVATE);
//        return redisTemplate.opsForValue().get(key);
//    }
//
//    /**
//     * 词云统计标题
//     */
//    public String TbcPioneerTitle() {
//        String key = String.format(s1, Constants.ORG_PIONEER_TITLE);
//        return redisTemplate.opsForValue().get(key);
//    }
//
//    /**
//     * 党页拟合度(条形图)
//     */
//    public String TbcMatching(Integer type) {
//        String key = String.format(s1, Constants.TBC_MATCHING1);
//        return redisTemplate.opsForValue().get(key);
//    }

}
