package com.goodsogood.ows.service.tbcFusion;

import com.goodsogood.ows.configuration.TbcMonitorsTagConfig;
import com.goodsogood.ows.mapper.tbcFusion.TbcPartyMemberWorkMapper;
import com.goodsogood.ows.model.vo.tbc.UserRankVo;
import com.goodsogood.ows.model.vo.tbc.XFRankStaVo;
import com.goodsogood.ows.model.vo.tbcFusion.TbcPCSplashesForm;
import com.goodsogood.ows.model.vo.tbcFusion.TbcPartyIndexForm;
import com.goodsogood.ows.model.vo.tbcFusion.TbcSplashesForm;
import com.goodsogood.ows.service.tbcFusion.test.TbcFalseDataService;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 支部"党建+业务"工作情况（散点图）
 *
 * <AUTHOR>
 * @date 2021/12/03
 */
@Log4j2
@Service
public class TbcPartyMemberWorkService {


    private final TbcBasicsService tbcBasicsService;
    private final TbcMonitorsTagConfig tbcMonitorsTagConfig;
    private final TbcFalseDataService tbcFalseDataService;
    private final TbcPartyMemberWorkMapper tbcPartyMemberWorkMapper;

    public TbcPartyMemberWorkService(TbcBasicsService tbcBasicsService, TbcMonitorsTagConfig tbcMonitorsTagConfig, TbcFalseDataService tbcFalseDataService, TbcPartyMemberWorkMapper tbcPartyMemberWorkMapper) {
        this.tbcBasicsService = tbcBasicsService;
        this.tbcMonitorsTagConfig = tbcMonitorsTagConfig;
        this.tbcFalseDataService = tbcFalseDataService;
        this.tbcPartyMemberWorkMapper = tbcPartyMemberWorkMapper;
    }

    /**
     * 支部"党建+业务"工作情况散点图
     *
     * @return
     */
    public TbcSplashesForm getSplashes() {
        TbcSplashesForm splashesForm = new TbcSplashesForm();
//        List<List<Double>> l1 = new ArrayList<>();// 分组x,y轴 操作
        List<List<Double>> l1 = tbcFalseDataService.getSplashes();
//
//        List<TbcPartyIndexForm> list = tbcBasicsService.getPartyIndex(2, new ArrayList<>());
        List<List<Double>> color1 = new ArrayList<>();//color1 红
        List<List<Double>> color2 = new ArrayList<>();//color2 黄
        List<List<Double>> color3 = new ArrayList<>();//color3 蓝
        List<List<Double>> color4 = new ArrayList<>();//color4 橙
//
//        for (TbcPartyIndexForm lists : list) {
//            List<Double> l2 = new ArrayList<>();
//            DecimalFormat df = new DecimalFormat("#.00");
//            double num1 = (lists.getPartyIndex());
//            String s1 = df.format(num1);
//            l2.add(Double.valueOf(s1));
//            double num2 = (lists.getBusinessIndex());
//            String s2 = df.format(num2);
//            l2.add(Double.valueOf(s2));
//            l1.add(l2);
//        }

        if (!CollectionUtils.isEmpty(l1)) {
            Integer max = tbcMonitorsTagConfig.getMedian();
            log.debug("获取配置中的中位点->{}", max);
            l1.forEach(info -> {
                double x = info.get(0) == null ? 0 : info.get(0);//横轴
                double y = info.get(1) == null ? 0 : info.get(1);//纵轴
                if (x > max && y > max) {
                    // 红色
                    color1.add(info);
                } else if (x < max && y > max) {
                    // 黄色
                    color2.add(info);
                } else if (x < max && y < max) {
                    // 蓝色
                    color3.add(info);
                } else {
                    // 橙色
                    color4.add(info);
                }
            });
        }
        splashesForm.setColor1(color1);
        splashesForm.setColor2(color2);
        splashesForm.setColor3(color3);
        splashesForm.setColor4(color4);
        return splashesForm;
    }

    /**
     * 支部"党建+业务"工作情况pc端散点图
     *
     * @return
     */
    public TbcPCSplashesForm getPCSplashes() {
        TbcPCSplashesForm splashesForm = new TbcPCSplashesForm();
        List<List<String>> l1 = new ArrayList<>();// 分组x,y轴 操作
        List<TbcPartyIndexForm> list = tbcPartyMemberWorkMapper.getPcPartyMember(DateUtils.getCurrentMonth());
        List<List<String>> color1 = new ArrayList<>();//color1 红
        List<List<String>> color2 = new ArrayList<>();//color2 黄
        List<List<String>> color3 = new ArrayList<>();//color3 蓝
        List<List<String>> color4 = new ArrayList<>();//color4 橙
        for (TbcPartyIndexForm lists : list) {
            List<String> l2 = new ArrayList<>();
            DecimalFormat df = new DecimalFormat("#.00");
            double num1 = lists.getPartyIndex();
            double num2 = lists.getBusinessIndex();
            if (num1 <= 0.00 && num2 <= 0.00) continue;
            String s1 = ".00".equals(df.format(num1)) ? "0.00" : df.format(num1);
            String s2 = ".00".equals(df.format(num2)) ? "0.00" : df.format(num2);
            String s3 = lists.getOrgName();
            l2.add(s1);
            l2.add(s2);
            l2.add(s3);
            l1.add(l2);
        }
        if (!CollectionUtils.isEmpty(l1)) {
            Integer max = tbcMonitorsTagConfig.getMedian();
            log.debug("获取配置中的中位点->{}", max);
            l1.forEach(info -> {
                String x = info.get(0) == null ? "0" : info.get(0);//横轴
                String y = info.get(1) == null ? "0" : info.get(1);//纵轴

                if (Double.parseDouble(x) > max && Double.parseDouble(y) > max) {
                    // 红色
                    color1.add(info);
                } else if (Double.parseDouble(x) < max && Double.parseDouble(y) > max) {
                    // 黄色
                    color2.add(info);
                } else if (Double.parseDouble(x) < max && Double.parseDouble(y) < max) {
                    // 蓝色
                    color3.add(info);
                } else {
                    // 橙色
                    color4.add(info);
                }
            });
        }
        splashesForm.setColor1(color1);
        splashesForm.setColor2(color2);
        splashesForm.setColor3(color3);
        splashesForm.setColor4(color4);
        return splashesForm;
    }





}
