package com.goodsogood.ows.service.tbcFusion;


import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.mapper.tbcFusion.TbcBasicsMapper;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Collections;


/**
 * @description: 党业融合大屏数据存入缓存
 * @author: zhangtao
 * @create: 2021-12-15 11:30
 */
@Log4j2
@Service
public class TbcSaveService {

    private final StringRedisTemplate redisTemplate;
    private final TbcBasicsService tbcBasicsService;
    private final TbcFortressService tbcFortressService;
    private final TbcInnovateService tbcInnovateService;
    private final TbcPartyMemberWorkService tbcPartyMemberWorkService;
    private final TbcPartyBranchWordService tbcPartyBranchWordService;
    private final TbcMatchingService tbcMatchingService;
    private final TbcPioneerIndexService tbcPioneerIndexService;

    @Autowired
    public TbcSaveService(StringRedisTemplate redisTemplate,
                          TbcBasicsService tbcBasicsService,
                          TbcFortressService tbcFortressService,
                          TbcInnovateService tbcInnovateService, TbcPartyMemberWorkService tbcPartyMemberWorkService, TbcPartyBranchWordService tbcPartyBranchWordService, TbcMatchingService tbcMatchingService, TbcPioneerIndexService tbcPioneerIndexService) {
        this.redisTemplate = redisTemplate;
        this.tbcBasicsService = tbcBasicsService;
        this.tbcFortressService = tbcFortressService;
        this.tbcInnovateService = tbcInnovateService;
        this.tbcPartyMemberWorkService = tbcPartyMemberWorkService;
        this.tbcPartyBranchWordService = tbcPartyBranchWordService;
        this.tbcMatchingService = tbcMatchingService;
        this.tbcPioneerIndexService = tbcPioneerIndexService;
    }

    /**
     * 党支部党树统计信息
     */
    @Async("tbcFusionExecutor")
    public void TbcOrgBasics() {
        String key1 = String.format(Constants.BASICS, 1);
        redisTemplate.opsForValue().set(key1, JsonUtils.toJson(tbcBasicsService.getOrgBasics(1)));
        String key2 = String.format(Constants.BASICS, 2);
        redisTemplate.opsForValue().set(key2, JsonUtils.toJson(tbcBasicsService.getOrgBasics(2)));
        String key3 = String.format(Constants.BASICS, 3);
        redisTemplate.opsForValue().set(key3, JsonUtils.toJson(tbcBasicsService.getOrgBasics(3)));
        String key4 = String.format(Constants.BASICS, 4);
        redisTemplate.opsForValue().set(key4, JsonUtils.toJson(tbcBasicsService.getOrgBasics(4)));
    }

    /**
     * 党支部党树统计信息
     */
    @Async("tbcFusionExecutor")
    public void TbcBasics() {
        String key = Constants.ORG_BASICS_COUNT;
        redisTemplate.opsForValue().set(key, JsonUtils.toJson(tbcBasicsService.getTbcBasics()));
    }

    /**
     * 支部堡垒指数和党建先锋指数统计
     * <p>
     * 1.支部堡垒指数
     * 2.党员先锋指数
     */
    @Async("tbcFusionExecutor")
    public void TbcFortress() {
        String key = String.format(Constants.TBC_FORTRESS, 1);
        redisTemplate.opsForValue().set(key, JsonUtils.toJson(tbcFortressService.getTbcFortress(1)));
        String key1 = String.format(Constants.TBC_FORTRESS, 2);
        redisTemplate.opsForValue().set(key1, JsonUtils.toJson(tbcFortressService.getTbcFortress(2)));
    }

    /**
     * 支部堡垒指数和党建先锋指数前三统计
     */
    @Async("tbcFusionExecutor")
    public void TbcThreeFortress() {
        String key = String.format(Constants.PARTY_FORTRESS, 1);
        redisTemplate.opsForValue().set(key, JsonUtils.toJson(tbcFortressService.getSortOrgName(1,3)));
        String key1 = String.format(Constants.PARTY_FORTRESS, 2);
        redisTemplate.opsForValue().set(key1, JsonUtils.toJson(tbcFortressService.getSortOrgName(2,3)));
    }

    /**
     * 支部工作情况(散点图)
     */
    @Async("tbcFusionExecutor")
    public void TbcPartyMemberWork() {
        String key = Constants.ORG_SPLASHES;
        redisTemplate.opsForValue().set(key, JsonUtils.toJson(tbcPartyMemberWorkService.getSplashes()));
    }

    /**
     * 党员”党建+业务”工作情况(柱状图)
     */
    @Async("tbcFusionExecutor")
    public void TbcPartyBranchWord() {
        String key1 = String.format(Constants.ORG_PARTY_MEMBER, 1);
        //序列
        redisTemplate.opsForValue().set(key1, JsonUtils.toJson(Collections.
                singletonList(tbcPartyBranchWordService.getPartyMemberNew(1))));
        String key2 = String.format(Constants.ORG_PARTY_MEMBER, 2);
        //教育程序
        redisTemplate.opsForValue().set(key2, JsonUtils.toJson(Collections.
                singletonList(tbcPartyBranchWordService.getPartyMemberNew(3))));
        //年龄
        String key3 = String.format(Constants.ORG_PARTY_MEMBER, 3);
        redisTemplate.opsForValue().set(key3, JsonUtils.toJson(Collections.
                singletonList(tbcPartyBranchWordService.getPartyMemberNew(2))));
    }

    /**
     * 党员先锋指数(饼状图)
     */
    @Async("tbcFusionExecutor")
    public void TbcPioneerIndex() {
        String key = Constants.ORG_PIONEER_INDEX;
        redisTemplate.opsForValue().set(key, JsonUtils.toJson(tbcPioneerIndexService.getPioneerIndex()));
    }

    /**
     * 创新情况统计(词云)
     */
    @Async("tbcFusionExecutor")
    public void TbcInnovate() {
        String key = Constants.TBC_INNOVATE;
        redisTemplate.opsForValue().set(key, JsonUtils.toJson(tbcInnovateService.getTbcInnovate()));
    }

    /**
     * 词云统计标题
     */
    @Async("tbcFusionExecutor")
    public void TbcPioneerTitle() {
        String key = Constants.ORG_PIONEER_TITLE;
        redisTemplate.opsForValue().set(key, tbcInnovateService.getOrgPioneerTitle());
    }

    /**
     * 党页拟合度(条形图)
     */
    @Async("tbcFusionExecutor")
    public void TbcMatching() {
        //党页拟合度(条形图) -堡垒指数排序
        String key1 = String.format(Constants.TBC_MATCHING);
        redisTemplate.opsForValue().set(key1, JsonUtils.toJson(tbcMatchingService.getTbcMatchingNew( 1)));
    }

    /**
     * 党页地图区
     */
    @Async("tbcFusionExecutor")
    public void TbcMap() {
        String key = String.format(Constants.TBC_MAP, 1);
        redisTemplate.opsForValue().set(key, JsonUtils.toJson(tbcFortressService.getMap(1)));
        String key1 = String.format(Constants.TBC_MAP, 2);
        redisTemplate.opsForValue().set(key1, JsonUtils.toJson(tbcFortressService.getMap(2)));
    }

    /**
     * 支部堡垒指数和党建先锋指数前十统计
     */
    @Async("tbcFusionExecutor")
    public void TbcThreeFortressTOPTen() {
        String key = String.format(Constants.PARTY_FORTRESS_TOP_TEN, 1);
        redisTemplate.opsForValue().set(key, JsonUtils.toJson(tbcFortressService.getSortOrgName(1,10)));
        String key1 = String.format(Constants.PARTY_FORTRESS_TOP_TEN, 2);
        redisTemplate.opsForValue().set(key1, JsonUtils.toJson(tbcFortressService.getSortOrgName(2,10)));
    }
}
