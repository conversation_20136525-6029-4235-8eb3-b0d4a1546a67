package com.goodsogood.ows.service.tbcFusion;

import com.goodsogood.ows.configuration.TbcMonitorsTagConfig;
import com.goodsogood.ows.mapper.tbcFusion.TbcBasicsMapper;
import com.goodsogood.ows.model.vo.UserSeqVO;
import com.goodsogood.ows.model.vo.tbcFusion.TbcPioneerIndexForm;
import com.goodsogood.ows.model.vo.tbcFusion.TbcUserIndexForm;
import com.goodsogood.ows.service.user.UserStatisticalService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 党页先锋指数前%20分析
 *
 * <AUTHOR>
 * @date 2021/12/03
 */
@Log4j2
@Service
public class TbcPioneerIndexService {

    private final TbcBasicsService tbcBasicsService;
    private final UserStatisticalService userStatisticalService;
    private final TbcBasicsMapper tbcBasicsMapper;
    private final TbcMonitorsTagConfig tbcMonitorsTagConfig;

    @Autowired
    public TbcPioneerIndexService(TbcBasicsService tbcBasicsService, UserStatisticalService userStatisticalService, TbcBasicsMapper tbcBasicsMapper, TbcMonitorsTagConfig tbcMonitorsTagConfig) {
        this.tbcBasicsService = tbcBasicsService;
        this.userStatisticalService = userStatisticalService;
        this.tbcBasicsMapper = tbcBasicsMapper;
        this.tbcMonitorsTagConfig = tbcMonitorsTagConfig;
    }

    /**
     * 党页先锋指数前%20分析
     *
     */
    public TbcPioneerIndexForm getPioneerIndex() {
        TbcPioneerIndexForm form = new TbcPioneerIndexForm();
        List<TbcPioneerIndexForm.Van> vanList = new ArrayList<>();
        // 前百分之20的人
        List<TbcUserIndexForm> getIndexForms = tbcBasicsMapper.getOrderByIndex(null,tbcMonitorsTagConfig.getExcludeOrgIds());
        log.debug("前百分之20的人1->{}", getIndexForms);
        if (!CollectionUtils.isEmpty(getIndexForms)) {
            Integer limit = (int) (0.2 * getIndexForms.size());
            Map<Integer, String> map = new HashMap<>();
            map.put(1, "卷烟营销党员");
            map.put(2, "烟叶生成党员");
            map.put(3, "专卖管理党员");
            map.put(4, "综合管理党员");
            List<UserSeqVO> list = tbcBasicsService.getUserSeq(new ArrayList<>(),tbcMonitorsTagConfig.getExcludeOrgIds());
            log.debug("获取对比维度的人员信息->{}", list);
            if (CollectionUtils.isEmpty(list)) {
                for (int i = 1; i < 5; i++) {
                    TbcPioneerIndexForm.Van van = new TbcPioneerIndexForm.Van();
                    van.setName(map.get(i));
                    van.setValue(0);
                    vanList.add(van);
                }
                form.setVanValue(vanList);
                return form;
            }
            log.debug("前百分之20的人2->{}", list);
            List<TbcUserIndexForm> forms = tbcBasicsMapper.getOrderByIndex(limit,tbcMonitorsTagConfig.getExcludeOrgIds());
            List<Long> userIds = forms.stream().map(TbcUserIndexForm::getUserId).collect(Collectors.toList());
            for (UserSeqVO userSeqVO : list) {
                int number = 0;
                TbcPioneerIndexForm.Van van = new TbcPioneerIndexForm.Van();
                List<Long> users = userSeqVO.getUseIds();
                log.debug("前百分之20的人3->{}", users);
                // 如果user不为空 并且包含这个人就 +1
                if (!CollectionUtils.isEmpty(users)) for (Long u : users) if (userIds.contains(u)) number += 1;
                van.setName(map.get(userSeqVO.getType()));
                van.setValue(number);
                vanList.add(van);
            }
            form.setVanValue(vanList);
        }
        return form;
    }

}
