package com.goodsogood.ows.service.tbcFusion;

import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.UserSeqEnum;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.ScoreManagerConfig;
import com.goodsogood.ows.configuration.TbcMonitorsTagConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.doris.IndexOrgScoreMapper;
import com.goodsogood.ows.mapper.doris.IndexUserScoreMapper;
import com.goodsogood.ows.mapper.sas.TbcOrgInfoMapper;
import com.goodsogood.ows.mapper.sas.TbcUserInfoMapper;
import com.goodsogood.ows.mapper.score.ScoreUserMapper;
import com.goodsogood.ows.mapper.tbcFusion.TbcBasicsMapper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.doris.IndexOrgScoreEntity;
import com.goodsogood.ows.model.db.doris.IndexUserScoreEntity;
import com.goodsogood.ows.model.db.tbc.TbcOrgInfoEntity;
import com.goodsogood.ows.model.db.tbc.TbcUserInfoEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.pbm.PbmOrgUserKitInfo;
import com.goodsogood.ows.model.mongodb.pbm.PbmUserKitInfo;
import com.goodsogood.ows.model.mongodb.pbm.ScoreInfo;
import com.goodsogood.ows.model.mongodb.user.User;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.UserSeqVO;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.tbc.*;
import com.goodsogood.ows.model.vo.tbcFusion.TbcBasicsForm;
import com.goodsogood.ows.model.vo.tbcFusion.TbcPartyIndexForm;
import com.goodsogood.ows.model.vo.tbcFusion.TbcUserIndexForm;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserMongoService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.ListUtils;
import com.goodsogood.ows.utils.NumberUtils;
import com.google.common.base.Joiner;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.bson.Document;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 基础信息
 *
 * <AUTHOR>
 * @date 2021/12/03
 */
@Service
@Log4j2
public class TbcBasicsService {

    private final TbcBasicsMapper tbcBasicsMapper;
    private final MyMongoTemplate mongoTemplate;
    private final OrgTypeConfig orgTypeConfig;
    private final TbcMonitorsTagConfig tbcMonitorsTagConfig;
    private final UserMapper userMapper;
    private final TbcUserInfoMapper tbcUserInfoMapper;
    private final TbcOrgInfoMapper tbcOrgInfoMapper;
    private final OrgService orgService;
    private final Errors errors;

    private final UserMongoService userMongoService;

    private final ScoreUserMapper scoreUserMapper;

    private final OrganizationMapper organizationMapper;

    private final IndexUserScoreMapper indexUserScoreMapper;

    private final IndexOrgScoreMapper indexOrgScoreMapper;

    private final ScoreManagerConfig scoreManagerConfig;

    @Autowired
    public TbcBasicsService(TbcBasicsMapper tbcBasicsMapper,
                            MyMongoTemplate mongoTemplate,
                            OrgTypeConfig orgTypeConfig,
                            TbcMonitorsTagConfig tbcMonitorsTagConfig,
                            UserMapper userMapper,
                            TbcUserInfoMapper tbcUserInfoMapper,
                            TbcOrgInfoMapper tbcOrgInfoMapper,
                            OrgService orgService,
                            Errors errors,
                            UserMongoService userMongoService,
                            ScoreUserMapper scoreUserMapper,
                            OrganizationMapper organizationMapper,
                            IndexUserScoreMapper indexUserScoreMapper,
                            IndexOrgScoreMapper indexOrgScoreMapper,
                            ScoreManagerConfig scoreManagerConfig) {
        this.tbcBasicsMapper = tbcBasicsMapper;
        this.mongoTemplate = mongoTemplate;
        this.orgTypeConfig = orgTypeConfig;
        this.tbcMonitorsTagConfig = tbcMonitorsTagConfig;
        this.userMapper = userMapper;
        this.tbcUserInfoMapper = tbcUserInfoMapper;
        this.tbcOrgInfoMapper = tbcOrgInfoMapper;
        this.orgService = orgService;
        this.errors = errors;
        this.userMongoService = userMongoService;
        this.scoreUserMapper = scoreUserMapper;
        this.organizationMapper = organizationMapper;
        this.indexUserScoreMapper = indexUserScoreMapper;
        this.indexOrgScoreMapper = indexOrgScoreMapper;
        this.scoreManagerConfig = scoreManagerConfig;
    }

    /**
     * 党支部基础信息统计
     *
     * @return list of OrgUserDetails
     */
    public List<TbcBasicsForm.OrgUserDetails> getTbcBasics() {
        List<TbcBasicsForm.OrgUserDetails> detailsList = new ArrayList<>();
        Map<Integer, String> map = new HashMap<>();
        map.put(1, "卷烟营销党员");
        map.put(2, "烟叶生成党员");
        map.put(3, "专卖管理党员");
        map.put(4, "综合管理党员");
        List<UserSeqVO> list = getUserSeq(new ArrayList<>(), tbcMonitorsTagConfig.getExcludeOrgIds());
        if (!CollectionUtils.isEmpty(list)) {
            for (UserSeqVO userSeqVO : list) {
                TbcBasicsForm.OrgUserDetails details = new TbcBasicsForm.OrgUserDetails();
                details.setName(map.get(userSeqVO.getType()));
                details.setValue(userSeqVO.getUseIds() == null ? 0 : userSeqVO.getUseIds().size());
                detailsList.add(details);
            }
        } else {
            for (int i = 1; i < 5; i++) {
                TbcBasicsForm.OrgUserDetails details = new TbcBasicsForm.OrgUserDetails();
                details.setName(map.get(i));
                details.setValue(0);
                detailsList.add(details);
            }
        }
        return detailsList;
    }

    /**
     * 党支部党树统计信息
     *
     * @param type 1:党支部 2:云上党支部 3:党员 4:职工
     * @return
     */
    public Integer getOrgBasics(Integer type) {
        //  根据type 获取对应的人数
        return switch (type) {
            case 1 ->//党支部
                    tbcBasicsMapper.getPartyBranch(tbcMonitorsTagConfig.getExcludeOrgIds());
            case 2 ->//云上支部
                    tbcBasicsMapper.getCloudOrgNumber(tbcMonitorsTagConfig.getExcludeOrgIds());
            case 3 ->//党员
                    tbcBasicsMapper.getPartyMember(tbcMonitorsTagConfig.getExcludeOrgIds());
            case 4 ->//职工
                    tbcBasicsMapper.getEmployeeNumber();
            default -> 0;
        };
    }

    /**
     * 统计用户所属序列
     *
     * @return List<UserSeqVO>
     * type = 1 -> 卷烟营销党员
     * type = 2 -> 烟叶生成党员
     * type = 3 -> 专卖管理党员
     * type = 4 -> 综合管理党员
     */
    public List<UserSeqVO> getUserSeq(List<Long> userIds, String excludeOrgIds) {
        List<Integer> type = Arrays.asList(1, 5, 17, 18);
        List<UserSeqVO> seqList = initUserSeq();
        List<AggregationOperation> aggregationQueryList = new ArrayList<>();
        if (userIds != null && !userIds.isEmpty()) {
            aggregationQueryList.add(Aggregation.match(Criteria.where("userId").in(userIds)));
        }
        if (StringUtils.isNotBlank(excludeOrgIds)) {
            final String[] orgIds = excludeOrgIds.split(",");
            aggregationQueryList.add(Aggregation.match(Criteria.where("orgList.orgId").nin(Arrays.asList(orgIds))));
            Arrays.stream(orgIds).forEach(orgId -> {
                Pattern pattern = Pattern.compile("^.*-" + orgId + "-.*$", Pattern.CASE_INSENSITIVE);
                aggregationQueryList.add(Aggregation.match(Criteria.where("orgList.orgLevel").not().regex(pattern)));
            });
        }
        aggregationQueryList.add(Aggregation.match(Criteria.where("politicalType").in(type)));
        aggregationQueryList.add(Aggregation.group("fields.field_3525").push("userId").as("userIds"));
        Aggregation aggregation = Aggregation.newAggregation(aggregationQueryList);
        log.debug("[mt临时查询语句] -> [{}]", aggregation);
        AggregationResults<User> usersResult = mongoTemplate.aggregate(aggregation, User.class);
        final List<Document> results = usersResult.getRawResults().getList("results", Document.class);
        log.debug("[mt临时查询结果] -> [{}]", results);
        results.forEach(doc -> {
            final List<String> id = doc.getList("_id", String.class);
            if (id != null && !id.isEmpty()) {
                seqList.stream().filter(s -> s.getType().equals(Integer.valueOf(id.get(0))))
                        .forEach(o -> o.setUseIds(doc.getList("userIds", Long.class)));
            }
        });
        log.debug(usersResult);
        return seqList;
    }

    public List<UserSeqVO> initUserSeq() {
        List<UserSeqVO> userSeqList = new ArrayList<>();
        userSeqList.add(new UserSeqVO(1));
        userSeqList.add(new UserSeqVO(2));
        userSeqList.add(new UserSeqVO(3));
        userSeqList.add(new UserSeqVO(4));
        return userSeqList;
    }


    /**
     * type 1=党支部 2=党委 3.党组 目前只用党支部 统一 type 传1
     * listOrgIds 查询组织 多个逗号分开
     *
     * @param type
     * @return
     */
    public List<TbcPartyIndexForm> getPartyIndex(Integer type, List<Long> listOrgIds) {
        if (type == 1) {
            String orgTypeChild = orgTypeConfig.getBranchChild().stream().map(String::valueOf)
                    .collect(Collectors.joining(","));
            String orgIds = listOrgIds.stream().map(String::valueOf)
                    .collect(Collectors.joining(","));
            return tbcBasicsMapper.getTbcPartyIndex(orgTypeChild, orgIds, tbcMonitorsTagConfig.getExcludeOrgIds());
        } else if (type == 2) {
            String orgTypeChild = orgTypeConfig.getCommunistAndGeneral().stream().map(String::valueOf)
                    .collect(Collectors.joining(","));
            String orgIds = listOrgIds.stream().map(String::valueOf)
                    .collect(Collectors.joining(","));
            return tbcBasicsMapper.getTbcPartyIndex(orgTypeChild, orgIds, tbcMonitorsTagConfig.getExcludeOrgIds());
        }

        return null;
    }


    /**
     * 查询用户 党业融合指数
     *
     * @return
     */
    public List<TbcUserIndexForm> getUserIndex(List<Long> listUserIds, List<Long> listOrgIds) {
        String userIds = null;
        String orgIds = null;
        if (!CollectionUtils.isEmpty(listUserIds)) {
            userIds = listUserIds.stream().map(String::valueOf)
                    .collect(Collectors.joining(","));
        }
        if (!CollectionUtils.isEmpty(listOrgIds)) {
            orgIds = listOrgIds.stream().map(String::valueOf)
                    .collect(Collectors.joining(","));
        }
        return tbcBasicsMapper.getUserIndex(orgIds, userIds, tbcMonitorsTagConfig.getExcludeOrgIds());
    }

    /**
     * 得到清洗数据用户指标信息
     *
     * @return
     */
    public List<TbcUserIndexForm> getClearUserIndex(String staMonth, List<Long> listUserIds) {
        String userIds = null;
        if (!CollectionUtils.isEmpty(listUserIds)) {
            userIds = listUserIds.stream().map(String::valueOf)
                    .collect(Collectors.joining(","));
        }
        return tbcBasicsMapper.getClearUserIndex(staMonth, userIds, tbcMonitorsTagConfig.getExcludeOrgIds());
    }

    /**
     * 清洗数据
     *
     * @param type
     * @return
     */
    public String getTbcBaseInfo(Integer type) {
        if (type == 1) {
            handUser();
            handUserXianFengIndx(DateUtils.getCurrentMonth());
            return "suc";
        }
        if (type == 2) {
            handOrg();
            handOrgFortressIndex(DateUtils.getCurrentMonth());
            return "suc";
        }
        return "error";
    }


    /**
     * 拉取基础信息
     *
     * @param type
     * @return
     */
    public String clearTbcBaseInfo(Integer type, String staMonth, Long userId, String scoreStaMonth) {
        if (type == 1) {
            clearTbcUser(staMonth, userId, scoreStaMonth);
            handUserXianFengIndx(staMonth);
            return "suc";
        }
        return "error";
    }


    public String calcXiangfengIndex(String staMonth) {
        handUserXianFengIndx(staMonth);
        return "suc";
    }

    /**
     * 重置基础信息
     * 这边是重算之前的历史数据
     *
     * @param type
     * @return
     */
    public String resetTbcBaseInfo(Integer type, List<Long> userIds) {
        if (type == 1) {
            resetUserLog(userIds);
            handUserXianFengIndx(null);
            return "suc";
        }
        if (type == 2) {
            restOrg();
            handOrgFortressIndex(null);
            return "suc";
        }
        return "error";
    }

    private void resetUserLog(List<Long> userIds) {
        Example example = new Example(TbcUserInfoEntity.class);
        if (!CollectionUtils.isEmpty(userIds)) {
            example.createCriteria().andIn("userId", userIds);
        }
        List<TbcUserInfoEntity> tbcUserInfoEntities = tbcUserInfoMapper.selectByExample(example);
        TbcMonitorsTagConfig.CalPercentage tbc = tbcMonitorsTagConfig.getTbcMatching();

        if (!CollectionUtils.isEmpty(tbcUserInfoEntities)) { // TODO !!!
            tbcUserInfoEntities.forEach(item -> {
                double xianFengIndex = item.getPartyIndex() * tbc.getPartyIndexProportion() +
                        item.getBusinessIndex() * tbc.getBusinessIndexProportion()
                        + item.getInnovationIndex() * tbc.getInnovationIndexProportion();
                double logXianFengIndex = 0;
                if (xianFengIndex > 0) {
                    logXianFengIndex = Math.log10(xianFengIndex);
                }
                // 获取用户的积分（三指标新计算）
                var usersScore = indexUserScoreMapper
                        .findUserScoreByMonthAndUserId(scoreManagerConfig.getIndexUserDorisTable(), item.getStaMonth(), item.getUserId());
                log.debug("查询到doris的积分数据-用户:{}-月份:{}->{}", item.getUserId(), item.getStaMonth(), usersScore);
                //更新分数开始
                //党建积分 parent_score_type = 1
//                Integer partyScore = scoreUserMapper.getUserByMonth(1, item.getUserId(), item.getStaMonth());
                item.setPartyScore(Double.valueOf(usersScore.stream().filter(s -> s.getParentScoreType().equals(1)).map(IndexUserScoreEntity::getScore).findFirst().orElse(0L)));
                //业务积分 parent_score_type = 2
//                Integer businessScore = scoreUserMapper.getUserByMonth(2, item.getUserId(), item.getStaMonth());
                item.setBusinessScore(Double.valueOf(usersScore.stream().filter(s -> s.getParentScoreType().equals(2)).map(IndexUserScoreEntity::getScore).findFirst().orElse(0L)));
                //创新积分 parent_score_type = 3
//                Integer innovationScore = scoreUserMapper.getUserByMonth(3, item.getUserId(), item.getStaMonth());
                item.setInnovationScore(Double.valueOf(usersScore.stream().filter(s -> s.getParentScoreType().equals(3)).map(IndexUserScoreEntity::getScore).findFirst().orElse(0L)));

                item.setSeqNumber(queryUserSeqNumber(item.getUserId()));
                item.setLogXianfengIndex(logXianFengIndex);
                tbcUserInfoMapper.updateLongXianFengIndex(item);
            });
        }
    }

    private void restOrg() {
        TbcMonitorsTagConfig.CalPercentage tbc = tbcMonitorsTagConfig.getTbcMatching();
        Example example = new Example(TbcOrgInfoEntity.class);
        List<TbcOrgInfoEntity> tbcOrgInfoEntities = tbcOrgInfoMapper.selectByExample(example);
        // 获取组织积分（三指标新计算）
        var orgScore = indexOrgScoreMapper.findOrgScoreByMonth(scoreManagerConfig.getIndexOrgDorisTable(), DateUtils.getCurrentMonth());
        log.debug("查询到doris的积分数据-组织->{}", orgScore);
        tbcOrgInfoEntities.forEach(item -> {
            double fortressIndex = item.getPartyIndex() * tbc.getPartyIndexProportion() +
                    item.getBusinessIndex() * tbc.getBusinessIndexProportion()
                    + item.getInnovationIndex() * tbc.getInnovationIndexProportion();
            double logFortressIndex = 0;
            if (fortressIndex > 0) {
                logFortressIndex = Math.log10(fortressIndex);
            }
            item.setLogFortressIndex(logFortressIndex);
            //党建积分
//            Integer partyScore = scoreUserMapper.getOrgScoreByMonth(1, item.getOrgId(), item.getStaMonth());
            item.setPartyScore(Double.valueOf(orgScore.stream().filter(s -> s.getParentScoreType().equals(1)).map(IndexOrgScoreEntity::getScore).findFirst().orElse(0L)));
            //业务
//            Integer businessScore = scoreUserMapper.getOrgScoreByMonth(2, item.getOrgId(), item.getStaMonth());
            item.setBusinessScore(Double.valueOf(orgScore.stream().filter(s -> s.getParentScoreType().equals(2)).map(IndexOrgScoreEntity::getScore).findFirst().orElse(0L)));
            //创新积分
//            Integer innovationScore = scoreUserMapper.getOrgScoreByMonth(3, item.getOrgId(), item.getStaMonth());
            item.setBusinessScore(Double.valueOf(orgScore.stream().filter(s -> s.getParentScoreType().equals(3)).map(IndexOrgScoreEntity::getScore).findFirst().orElse(0L)));
            tbcOrgInfoMapper.updateLogFortressIndex(item);
        });
    }


    /**
     * 开始计算用户的先峰指数
     */
    private void handUserXianFengIndx(String currentMonth) {
        Double minX = 0.0;
        Double maxX = 0.0;
        if (StringUtils.isNotBlank(currentMonth)) {
            minX = tbcUserInfoMapper.selectUserLogMinNew(currentMonth);
            maxX = tbcUserInfoMapper.selectUserLogMaxNew(currentMonth);
        } else {
            minX = tbcUserInfoMapper.selectUserLogMin();
            maxX = tbcUserInfoMapper.selectUserLogMax();
        }
        Example example = new Example(TbcUserInfoEntity.class);
        if (StringUtils.isNotBlank(currentMonth)) {
            example.createCriteria().andEqualTo("staMonth", currentMonth);
        }
        List<TbcUserInfoEntity> tbcUserInfoEntities = tbcUserInfoMapper.selectByExample(example);
        try {
            for (TbcUserInfoEntity tbcUserInfoEntity : tbcUserInfoEntities) {
                Double logXianfengIndex = tbcUserInfoEntity.getLogXianfengIndex();
                Double divideLogIndex = NumberUtils.divide((logXianfengIndex - minX), (maxX - minX), 3);
                tbcUserInfoEntity.setXianfengIndex(divideLogIndex * 100);
                tbcUserInfoEntity.setUpdateTime(new Date());
                tbcUserInfoMapper.updateByPrimaryKeySelective(tbcUserInfoEntity);
            }
        } catch (Exception e) {
            log.error("handUserXianFengIndx处理异常", e);
        }

    }

    /**
     * 处理组织堡垒指数
     */
    private void handOrgFortressIndex(String currentMonth) {
        List<Integer> committee = orgTypeConfig.getCommittee();
        List<Integer> branch = orgTypeConfig.getBranch();
        Example example = new Example(TbcOrgInfoEntity.class);
        if (StringUtils.isNotBlank(currentMonth)) {
            example.createCriteria().andEqualTo("staMonth", currentMonth);
        }
        List<TbcOrgInfoEntity> tbcOrgInfoEntities = tbcOrgInfoMapper.selectByExample(example);
        Double minX = 0.0;
        Double maxX = 0.0;
        try {
            for (TbcOrgInfoEntity tbcOrgInfoEntity : tbcOrgInfoEntities) {
                //党委党总支
                if (orgTypeConfig.checkIsCommunist(tbcOrgInfoEntity.getOrgTypeChild())) {
                    minX = tbcOrgInfoMapper.selectUserLogMin(Joiner.on(",").join(committee));
                    maxX = tbcOrgInfoMapper.selectUserLogMax(Joiner.on(",").join(committee));
                } else {
                    minX = tbcOrgInfoMapper.selectUserLogMin(Joiner.on(",").join(branch));
                    maxX = tbcOrgInfoMapper.selectUserLogMax(Joiner.on(",").join(branch));
                }
                Double logFortressIndex = tbcOrgInfoEntity.getLogFortressIndex();
                Double divideLogIndex = NumberUtils.divide((logFortressIndex - minX), (maxX - minX), 3);
                tbcOrgInfoEntity.setFortressIndex(divideLogIndex * 100);
                tbcOrgInfoEntity.setUpdateTime(new Date());
                tbcOrgInfoMapper.updateByPrimaryKeySelective(tbcOrgInfoEntity);
            }
        } catch (Exception e) {
            log.error("handOrgFortressIndex处理异常", e);
        }
    }

//    /**
//     * 处理用户
//     */
//    private void handUser(){
//        TbcMonitorsTagConfig.CalPercentage tbc = tbcMonitorsTagConfig.getTbcMatching();
//        Set<TbcBaseVo> allUserInfo = userMapper.getAllUserInfoByTbc();
//        log.info("allUserInfo总的人数{}",allUserInfo.size());
//        List<TbcUserInfoEntity> list = new ArrayList<>();
//        allUserInfo.forEach(item->{
//            TbcUserInfoEntity tbcUserInfoEntity = new TbcUserInfoEntity();
//            tbcUserInfoEntity.setCreateTime(new Date());
//            tbcUserInfoEntity.setStaMonth(DateUtils.getCurrentMonth());
//            BeanUtils.copyProperties(item,tbcUserInfoEntity);
//            //如果查询没有数据 而且已经入库直接放过
//            List<TbcUserIndexForm> userIndex = getUserIndex(Collections.singletonList(item.getUserId()), null);
//            Example example = new Example(TbcUserInfoEntity.class);
//            example.createCriteria().andEqualTo("userId", item.getUserId())
//                    .andEqualTo("staMonth", DateUtils.getCurrentMonth());
//            TbcUserInfoEntity tbcUserInfo = tbcUserInfoMapper.selectOneByExample(example);
//            if(CollectionUtils.isEmpty(userIndex)&&null!=tbcUserInfo){
//                userIndex = new ArrayList<>();
//                TbcUserIndexForm tbcUserIndexForm = new TbcUserIndexForm();
//                tbcUserIndexForm.setPartyIndex(0.0);
//                tbcUserIndexForm.setBusinessIndex(0.0);
//                tbcUserIndexForm.setInnovationIndex(0.0);
//                tbcUserIndexForm.setUserId(item.getUserId());
//                tbcUserIndexForm.setOrgName(item.getOrgName());
//                userIndex.add(tbcUserIndexForm);
//                log.info("userIndex数据为空的信息-userId={}",item.getUserId());
//                //return;
//            }
//            Double xianfengIndex=0.0;
//            if(null!=tbcUserInfo) {
//                 xianfengIndex = tbcUserInfo.getXianfengIndex();
//            }
//            if(!CollectionUtils.isEmpty(userIndex)){
//                TbcUserIndexForm tbcUserIndexForm = userIndex.get(0);
//                tbcUserInfoEntity.setPartyIndex(tbcUserIndexForm.getPartyIndex());
//                tbcUserInfoEntity.setBusinessIndex(tbcUserIndexForm.getBusinessIndex());
//                tbcUserInfoEntity.setInnovationIndex(tbcUserIndexForm.getInnovationIndex());
//                double xianFengIndex = tbcUserIndexForm.getPartyIndex() * tbc.getPartyIndexProportion() +
//                        tbcUserIndexForm.getBusinessIndex() * tbc.getBusinessIndexProportion()
//                        + tbcUserIndexForm.getInnovationIndex() * tbc.getInnovationIndexProportion();
//                double logXianFengIndex=0;
//                if(xianFengIndex>0){
//                    logXianFengIndex = Math.log10(xianFengIndex);
//                }
//                tbcUserInfoEntity.setLogXianfengIndex(logXianFengIndex);
//                tbcUserInfoEntity.setPosition(userMapper.getUserTitle(item.getUserId()));
//                //党建积分
//                Integer partyScore = scoreUserMapper.getUserByMonth(1, item.getUserId(), DateUtils.getCurrentMonth());
//                tbcUserInfoEntity.setPartyScore(Double.valueOf(partyScore));
//                //业务
//                Integer businessScore = scoreUserMapper.getUserByMonth(2, item.getUserId(), DateUtils.getCurrentMonth());
//                tbcUserInfoEntity.setBusinessScore(Double.valueOf(businessScore));
//                //创新积分
//                Integer innovationScore = scoreUserMapper.getUserByMonth(3, item.getUserId(), DateUtils.getCurrentMonth());
//                tbcUserInfoEntity.setBusinessScore(Double.valueOf(innovationScore));
//                //写入序列与新的new_owner_id
//                tbcUserInfoEntity.setSeqNumber(queryUserSeqNumber(item.getUserId()));
//                tbcUserInfoEntity.setNewOwnerId(item.getOwnerId());
//                if(null!=tbcUserInfo){
//                    BeanUtils.copyProperties(tbcUserInfoEntity,tbcUserInfo,"tbcUserInfoId");
//                    tbcUserInfo.setUpdateTime(new Date());
//                    tbcUserInfo.setXianfengIndex(xianfengIndex);
//                    tbcUserInfoMapper.updateByPrimaryKeySelective(tbcUserInfo);
//                    return;
//                }
//            }
//            list.add(tbcUserInfoEntity);
//            if(list.size()>=20){
//                tbcUserInfoMapper.insertList(list);
//                list.clear();
//            }
//        });
//        if(!CollectionUtils.isEmpty(list)){
//            tbcUserInfoMapper.insertList(list);
//        }
//    }

    /**
     * 处理用户
     */
    private void handUser() {
        Set<TbcBaseVo> allUserInfo = userMapper.getAllUserInfoByTbc();
        log.debug("handUser-allUserInfo总的人数{}", allUserInfo.size());
        List<TbcUserInfoEntity> list = new ArrayList<>();
        allUserInfo.forEach(item -> {
            TbcUserInfoEntity tbcUserInfoEntity = new TbcUserInfoEntity();
            tbcUserInfoEntity.setStaMonth(DateUtils.getCurrentMonth());
            BeanUtils.copyProperties(item, tbcUserInfoEntity);
            Example example = new Example(TbcUserInfoEntity.class);
            example.createCriteria().andEqualTo("userId", item.getUserId())
                    .andEqualTo("staMonth", DateUtils.getCurrentMonth());
            TbcUserInfoEntity tbcUserInfo = tbcUserInfoMapper.selectOneByExample(example);
            //写入序列与新的new_owner_id
            tbcUserInfoEntity.setSeqNumber(queryUserSeqNumber(item.getUserId()));
            tbcUserInfoEntity.setNewOwnerId(item.getOwnerId());
            if (null != tbcUserInfo) {
                tbcUserInfoEntity.setTbcUserInfoId(tbcUserInfo.getTbcUserInfoId());
                tbcUserInfoEntity.setUpdateTime(new Date());
                tbcUserInfoEntity.setPartyIndex(tbcUserInfo.getPartyIndex());
                tbcUserInfoEntity.setBusinessIndex(tbcUserInfo.getBusinessIndex());
                tbcUserInfoEntity.setInnovationIndex(tbcUserInfo.getInnovationIndex());
                tbcUserInfoEntity.setXianfengIndex(tbcUserInfo.getXianfengIndex());
                tbcUserInfoEntity.setPartyScore(tbcUserInfo.getPartyScore());
                tbcUserInfoEntity.setBusinessScore(tbcUserInfo.getBusinessScore());
                tbcUserInfoEntity.setInnovationScore(tbcUserInfo.getInnovationScore());
                tbcUserInfoMapper.updateByPrimaryKeySelective(tbcUserInfoEntity);
            } else {
                tbcUserInfoEntity.setCreateTime(new Date());
                list.add(tbcUserInfoEntity);
            }
            if (list.size() >= 20) {
                tbcUserInfoMapper.insertList(list);
                list.clear();
            }
        });
        if (!CollectionUtils.isEmpty(list)) {
            tbcUserInfoMapper.insertList(list);
        }
        //最后洗数据
        String staMonth = DateUtils.getCurrentMonth();
        String scoreStaMonth = DateUtils.getCurrentMonth("yyyy-MM");
        log.debug("调用clearTbcUser,staMonth={},scoreStaMonth={}", staMonth, scoreStaMonth);
        //最后调用算积分与指数方法
        clearTbcUser(DateUtils.getCurrentMonth(), null, DateUtils.getCurrentMonth("yyyy-MM"));
    }

    /**
     * 清洗用户数据
     */
    private void clearTbcUser(String staMonth, Long userId, String scoreStaMonth) {
        TbcMonitorsTagConfig.CalPercentage tbc = tbcMonitorsTagConfig.getTbcMatching();
        //查询要洗数据的用户信息
        Example exampleUser = new Example(TbcUserInfoEntity.class);
        exampleUser.createCriteria().andEqualTo("staMonth", staMonth);
        if (!Objects.isNull(userId)) {
            exampleUser.and().andEqualTo("userId", userId);
        }
        List<TbcUserInfoEntity> clearUserInfos = tbcUserInfoMapper.selectByExample(exampleUser);
        log.info("clearTbcUser总的人数{}", clearUserInfos.size());
        if (CollectionUtils.isEmpty(clearUserInfos)) {
            return;
        }
        //每100条查询一下指标信息
        List<TbcUserIndexForm> listAll = new ArrayList<>(new ArrayList<>());
        List<List<TbcUserInfoEntity>> lists = ListUtils.splitList(clearUserInfos, 100);
        lists.forEach(item -> {
            List<Long> listUserId = item.stream().map(TbcUserInfoEntity::getUserId).collect(Collectors.toList());
            List<TbcUserIndexForm> clearUserIndex = getClearUserIndex(scoreStaMonth, listUserId);
            listAll.addAll(clearUserIndex);
        });
        // 获取用户的积分（三指标新计算）
        var usersScore = indexUserScoreMapper.findUsersScoreByMonth(scoreManagerConfig.getIndexUserDorisTable(), staMonth);
        log.debug("查询到doris的积分数据-用户->{}", usersScore);
        //对每条数据进行清洗
        clearUserInfos.forEach(item -> {
            //如果查询没有数据 而且已经入库直接放过
            Optional<TbcUserIndexForm> optional = listAll.stream().filter(it -> it.getUserId().equals(item.getUserId())).findFirst();
            if (!optional.isPresent()) {
                log.info("用户id={},没有查询到指标数据", item.getUserId());
                return;
            }
            TbcUserIndexForm tbcUserIndexForm = optional.get();
            //更改三个指标
            item.setPartyIndex(tbcUserIndexForm.getPartyIndex());
            item.setBusinessIndex(tbcUserIndexForm.getBusinessIndex());
            item.setInnovationIndex(tbcUserIndexForm.getInnovationIndex());
            // 获取用户的积分
            var scoreMap = usersScore.stream().filter(
                            it -> it.getUserId() != null && it.getUserId().longValue() == item.getUserId().longValue())
                    .collect(Collectors.toMap(IndexUserScoreEntity::getParentScoreType, IndexUserScoreEntity::getScore));
            log.debug("用户id={}，查询到doris的积分数据->{}", item.getUserId(), scoreMap);
            //党建积分 type = 1 / parent_score_type = 1
//            Integer partyScore = scoreUserMapper.getUserByMonth(1, item.getUserId(), staMonth);
            item.setPartyScore(scoreMap.get(1) != null ? scoreMap.get(1) : 0.0);
            //业务积分 parent_score_type = 2
//            Integer businessScore = scoreUserMapper.getUserByMonth(2, item.getUserId(), staMonth);
            item.setBusinessScore(scoreMap.get(2) != null ? scoreMap.get(2) : 0.0);
            //创新积分 parent_score_type = 3
//            Integer innovationScore = scoreUserMapper.getUserByMonth(3, item.getUserId(),staMonth);
            item.setInnovationScore(scoreMap.get(3) != null ? scoreMap.get(3) : 0.0);
            //更新数据
            double xianFengIndex = item.getPartyIndex() * tbc.getPartyIndexProportion() +
                    item.getBusinessIndex() * tbc.getBusinessIndexProportion()
                    + item.getInnovationIndex() * tbc.getInnovationIndexProportion();
            double logXianFengIndex = 0;
            if (xianFengIndex > 0) {
                logXianFengIndex = log100(xianFengIndex, 100);
            }
            item.setXianfengIndex(xianFengIndex);
            item.setLogXianfengIndex(logXianFengIndex);
            item.setUpdateTime(new Date());
            tbcUserInfoMapper.updateByPrimaryKeySelective(item);
        });
    }

    public double log100(double value, double base) {
        return Math.log(value) / Math.log(base);
    }


    /**
     * 处理组织
     */
    private void handOrg() {
        TbcMonitorsTagConfig.CalPercentage tbc = tbcMonitorsTagConfig.getTbcMatching();
        Set<TbcBaseVo> allOrgInfo = userMapper.getAllOrgInfo();
        List<TbcOrgInfoEntity> list = new ArrayList<>();
        // 获取组织积分（三指标新计算）
        var orgScore = indexOrgScoreMapper.findOrgScoreByMonth(scoreManagerConfig.getIndexOrgDorisTable(), DateUtils.getCurrentMonth());
        log.debug("查询到doris的积分数据-组织->{}", orgScore);
        allOrgInfo.forEach(item -> {
            int type = 1;
            //党委党总支
            if (orgTypeConfig.checkIsCommunist(item.getOrgTypeChild())) {
                type = 2;
            }
            TbcOrgInfoEntity tbcOrgInfoEntity = new TbcOrgInfoEntity();
            tbcOrgInfoEntity.setCreateTime(new Date());
            tbcOrgInfoEntity.setStaMonth(DateUtils.getCurrentMonth());
            BeanUtils.copyProperties(item, tbcOrgInfoEntity);
            //如果查询没有数据 而且已经入库直接放过
            List<TbcPartyIndexForm> partyIndex = getPartyIndex(type, Collections.singletonList(item.getOrgId()));
            Example example = new Example(TbcOrgInfoEntity.class);
            example.createCriteria().andEqualTo("orgId", item.getOrgId())
                    .andEqualTo("staMonth", DateUtils.getCurrentMonth());
            TbcOrgInfoEntity tbcOrgInfo = tbcOrgInfoMapper.selectOneByExample(example);
            if (CollectionUtils.isEmpty(partyIndex) && null != tbcOrgInfo) {
                return;
            }
            if (!CollectionUtils.isEmpty(partyIndex)) {
                TbcPartyIndexForm tbcPartyIndexForm = partyIndex.get(0);
                tbcOrgInfoEntity.setPartyIndex(tbcPartyIndexForm.getPartyIndex());
                tbcOrgInfoEntity.setBusinessIndex(tbcPartyIndexForm.getBusinessIndex());
                tbcOrgInfoEntity.setInnovationIndex(tbcPartyIndexForm.getInnovationIndex());
                double fortressIndex = tbcPartyIndexForm.getPartyIndex() * tbc.getPartyIndexProportion() +
                        tbcPartyIndexForm.getBusinessIndex() * tbc.getBusinessIndexProportion()
                        + tbcPartyIndexForm.getInnovationIndex() * tbc.getInnovationIndexProportion();
                double logFortressIndex = 0;
                if (fortressIndex > 0) {
                    logFortressIndex = Math.log10(fortressIndex);
                }
                tbcOrgInfoEntity.setLogFortressIndex(logFortressIndex);

                // 获取组织积分
                var scoreMap = orgScore.stream().filter(
                                it -> it.getOrgId() != null && it.getOrgId().longValue() == item.getOrgId().longValue())
                        .collect(Collectors.toMap(IndexOrgScoreEntity::getParentScoreType, IndexOrgScoreEntity::getScore));
                log.debug("组织id={}，查询到doris的积分数据->{}", item.getOrgId(), scoreMap);
                //党建积分 type = 1 / parent_score_type = 1
                // Integer partyScore = scoreUserMapper.getOrgScoreByMonth(1, item.getOrgId(), DateUtils.getCurrentMonth());
                tbcOrgInfoEntity.setPartyScore(scoreMap.get(1) != null ? scoreMap.get(1) : 0.0);
                //业务积分 parent_score_type = 2
                // Integer businessScore = scoreUserMapper.getOrgScoreByMonth(2, item.getOrgId(), DateUtils.getCurrentMonth());
                tbcOrgInfoEntity.setBusinessScore(scoreMap.get(2) != null ? scoreMap.get(2) : 0.0);
                //创新积分 parent_score_type = 3
                // Integer innovationScore = scoreUserMapper.getOrgScoreByMonth(3, item.getOrgId(), DateUtils.getCurrentMonth());
                tbcOrgInfoEntity.setBusinessScore(scoreMap.get(3) != null ? scoreMap.get(3) : 0.0);
                if (null != tbcOrgInfo) {
                    BeanUtils.copyProperties(tbcOrgInfoEntity, tbcOrgInfo, "tbcOrgInfoId");
                    tbcOrgInfo.setUpdateTime(new Date());
                    tbcOrgInfoMapper.updateByPrimaryKeySelective(tbcOrgInfo);
                    return;
                }
            }
            list.add(tbcOrgInfoEntity);
            if (list.size() >= 20) {
                tbcOrgInfoMapper.insertList(list);
                list.clear();
            }
        });
        if (!CollectionUtils.isEmpty(list)) {
            tbcOrgInfoMapper.insertList(list);
        }
    }


    /**
     * 处理组织视图信息
     *
     * @param headers
     * @return
     */
    public ResponseEntity<Result<?>> tbcOrg(HttpHeaders headers, Long orgId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //如果url后面跟了org_id 就取
        if (!Objects.isNull(orgId)) {
            sysHeader.setOid(orgId);
        }
        OrgTabViewVo orgTabViewVo = new OrgTabViewVo();
        int overviewType = getOverviewType(sysHeader);
        //设置组织类型
        orgTabViewVo.setType(overviewType);
        //设置组织指标参数
        setOrgFortressIndex(orgTabViewVo, sysHeader, overviewType);
        //第一块数据
        List<TbcBaseIndexForm> tbcBaseUserOrg = tbcBasicsMapper.getTbcBaseUserOrg(sysHeader.getOid(), DateUtils.getCurrentMonth());
        orgTabViewVo.setLisTbcBaseIndex(tbcBaseUserOrg);
        return new ResponseEntity<>(new Result<>(orgTabViewVo, errors), HttpStatus.OK);
    }


    /**
     * 设置组织指标参数
     *
     * @return
     */
    private void setOrgFortressIndex(OrgTabViewVo orgTabViewVo, HeaderHelper.SysHeader sysHeader, int overviewType) {
        String currentMonth = DateUtils.getCurrentMonth();
        Example example = new Example(TbcOrgInfoEntity.class);
        example.createCriteria().andEqualTo("orgId", sysHeader.getOid())
                .andEqualTo("staMonth", currentMonth);
        TbcOrgInfoEntity tbcOrgInfoEntity = tbcOrgInfoMapper.selectOneByExample(example);
        if (null == tbcOrgInfoEntity) {
            throw new ApiException("组织信息不存在", new Result(errors, 9404,
                    HttpStatus.OK.value(), "组织信息不存在"));
        }

        orgTabViewVo.setOrgName(tbcOrgInfoEntity.getOrgName());
        //当月堡垒指数
        Double currentMonthFortressIndex = tbcOrgInfoEntity.getFortressIndex();
        orgTabViewVo.setFortressIndex(currentMonthFortressIndex);
        //如果是支部 第一块数据
        if (overviewType == 2) {
            example.clear();
            //得到支部平均值
            Integer avgIndex = tbcOrgInfoMapper.allBranchAvg(currentMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
            orgTabViewVo.setAvgIndex(avgIndex);
            //算上个月所有支部的排名
            String lastMonth = DateUtils.getBeforeMonth(1).get(0).replace("-", "");
            List<UserRankVo> lastMonthRank = tbcOrgInfoMapper.allBranchRank(lastMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
            Optional<UserRankVo> optionLastMonth = lastMonthRank.stream().filter(item -> {
                long[] a = Arrays.stream(item.getOrgIds().split(",")).mapToLong(Long::valueOf).toArray();
                List<Long> list = Arrays.stream(a).boxed().toList();
                return list.contains(sysHeader.getOid());
            }).findFirst();
            //算这个月的支部排名
            List<UserRankVo> currentMonthRank = tbcOrgInfoMapper.allBranchRank(currentMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
            Optional<UserRankVo> optionCurrentMonth = currentMonthRank.stream().filter(item -> {
                long[] a = Arrays.stream(item.getOrgIds().split(",")).mapToLong(Long::valueOf).toArray();
                List<Long> list = Arrays.stream(a).boxed().toList();
                return list.contains(sysHeader.getOid());
            }).findFirst();
            if (optionCurrentMonth.isPresent()) {
                UserRankVo userRankVo = optionCurrentMonth.get();
                //当月的排名
                Long row = userRankVo.getRow();
                //设置排名
                // Integer totalCount = tbcOrgInfoMapper.allBranchRankCount(currentMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
                // orgTabViewVo.setCurrentRank(NumberUtils.divide(userRankVo.getRow(),totalCount,5));
                orgTabViewVo.setCurrentRank(row.intValue());
                if (optionLastMonth.isPresent()) {
                    UserRankVo userRankVoLast = optionLastMonth.get();
                    //排名排名 正数为上升 负数 为下降 0表示没有变化
                    Long l = userRankVoLast.getRow() - row;
                    orgTabViewVo.setRankGo(l.intValue());
                } else {
                    orgTabViewVo.setRankGo(1);
                }
            }
        }
        //第二块数据
        setOrgSecondIndex(orgTabViewVo, sysHeader, overviewType);
        //第三块数据
        setOrgThreeIndex(orgTabViewVo, sysHeader, overviewType);
    }

    /**
     * 设置第三块数据
     *
     * @return
     */
    private void setOrgThreeIndex(OrgTabViewVo orgTabViewVo, HeaderHelper.SysHeader sysHeader, int overviewType) {
        //如果是党委统计 统计下面支部的信息
        Example example = new Example(TbcUserInfoEntity.class);
        int limitSize = 5;
        //如果是党委就查询他下面支部包括它
        if (overviewType == 1) {
            limitSize = 10;
            example.createCriteria().andLike("orgLevel", "%-" + sysHeader.getOid() + "-%")
                    .orEqualTo("orgId", sysHeader.getOid())
                    .andNotIn("orgId", Arrays.asList(tbcMonitorsTagConfig.getExcludeOrgIds().split(",")));
        } else {
            example.createCriteria().andEqualTo("orgId", sysHeader.getOid())
                    .andNotIn("orgId", Arrays.asList(tbcMonitorsTagConfig.getExcludeOrgIds().split(",")));
        }
        OrgTabViewVo.TbcOrgUserAvgScoreVo tbcOrgUserAvgScoreVo = new OrgTabViewVo.TbcOrgUserAvgScoreVo();
        Example exampleAll = new Example(TbcUserInfoEntity.class);
        exampleAll.createCriteria().andNotIn("orgId",
                        Arrays.asList(tbcMonitorsTagConfig.getExcludeOrgIds().split(","))).
                andEqualTo("staMonth", DateUtils.getCurrentMonth());
        exampleAll.selectProperties("xianfengIndex");
        //全市平均
        OptionalDouble allAverage = tbcUserInfoMapper.
                selectByExample(exampleAll).stream().mapToDouble(TbcUserInfoEntity::getXianfengIndex).average();
        if (allAverage.isPresent()) {
            tbcOrgUserAvgScoreVo.setAllUserAvgScore((int) allAverage.getAsDouble());
        }
        List<TbcUserInfoEntity> tbcUserInfoEntities = tbcUserInfoMapper.
                branchPersonRankAvg(overviewType, sysHeader.getOid(), DateUtils.getCurrentMonth(),
                        tbcMonitorsTagConfig.getExcludeOrgIds());
        //组织平均
        OptionalDouble orgAverage = tbcUserInfoEntities.stream().mapToDouble(TbcUserInfoEntity::getXianfengIndex).average();
        if (orgAverage.isPresent()) {
            tbcOrgUserAvgScoreVo.setOrgUserAvgScore((int) orgAverage.getAsDouble());
        }
        List<OrgTabViewVo.TbcOrgUserAvgScoreVo.UserScore> listUser = new ArrayList<>();
        //党委显示10条 党支部5条
        tbcUserInfoEntities.stream().sorted(Comparator.comparing(TbcUserInfoEntity::getXianfengIndex).reversed())
                .limit(limitSize).forEach(item -> {
                    OrgTabViewVo.TbcOrgUserAvgScoreVo.UserScore user = new OrgTabViewVo.TbcOrgUserAvgScoreVo.UserScore();
                    user.setUserId(item.getUserId());
                    user.setUserName(item.getUserName());
                    user.setUserOrgName(item.getOrgName());
                    user.setXiangFengIndex(item.getXianfengIndex());
                    listUser.add(user);
                });
        tbcOrgUserAvgScoreVo.setListUser(listUser);
        tbcOrgUserAvgScoreVo.setOrgUserCount(tbcUserInfoEntities.size());
        orgTabViewVo.setUserAvgScoreVo(tbcOrgUserAvgScoreVo);
    }


    /**
     * 设置第二块数据
     *
     * @return
     */
    private void setOrgSecondIndex(OrgTabViewVo orgTabViewVo, HeaderHelper.SysHeader sysHeader, int overviewType) {
        //如果是支部统计它下面历史记录
        if (overviewType == 1) {
            OrgTabViewVo.TbcOrgIndexVo tbcOrgIndexVo = new OrgTabViewVo.TbcOrgIndexVo();
            Example example = new Example(TbcOrgInfoEntity.class);
            example.orderBy("fortressIndex");
            example.createCriteria().andLike("orgLevel", "%-" + sysHeader.getOid() + "-%")
                    .andEqualTo("staMonth", DateUtils.getCurrentMonth())
                    .andIn("orgTypeChild", orgTypeConfig.getBranchChild());
            List<TbcOrgInfoEntity> tbcOrgInfoEntities = tbcOrgInfoMapper.selectByExample(example);
            if (!CollectionUtils.isEmpty(tbcOrgInfoEntities)) {
                double avgFortressIndex = tbcOrgInfoEntities.stream().mapToDouble(TbcOrgInfoEntity::getFortressIndex)
                        .average().orElse(0.0);
                tbcOrgIndexVo.setAvgOrgIndex((int) avgFortressIndex);
            }
            List<OrgTabViewVo.TbcOrgIndexVo.TbcOrgInfoVo> listOrgInfo = new ArrayList<>();
            tbcOrgInfoEntities.stream().sorted(Comparator.comparing(TbcOrgInfoEntity::getFortressIndex).reversed())
                    .limit(3).forEach(item -> {
                        OrgTabViewVo.TbcOrgIndexVo.TbcOrgInfoVo orgInfoVo = new OrgTabViewVo.TbcOrgIndexVo.TbcOrgInfoVo();
                        orgInfoVo.setOrgName(item.getOrgName());
                        orgInfoVo.setFortressIndex(item.getFortressIndex().intValue());
                        orgInfoVo.setOrgId(item.getOrgId());
                        listOrgInfo.add(orgInfoVo);
                    });
            tbcOrgIndexVo.setOrgCount(tbcOrgInfoEntities.size());
            tbcOrgIndexVo.setListOrgInfo(listOrgInfo);
            orgTabViewVo.setOrgIndexVo(tbcOrgIndexVo);
            //如果是党委统计 统计下面支部的信息
        } else {
            //得到前6个月
            List<String> beforeMonth = DateUtils.getContinuousMonth(5, "yyyyMM");
            Example example = new Example(TbcOrgInfoEntity.class);
            example.createCriteria().andEqualTo("orgId", sysHeader.getOid())
                    .andIn("staMonth", beforeMonth);
            List<TbBranchIndexVo> tbBranchIndexList = new ArrayList<>();
            List<TbcOrgInfoEntity> tbcOrgInfoEntities = tbcOrgInfoMapper.selectByExample(example);
            tbcOrgInfoEntities.forEach(item -> {
                TbBranchIndexVo tbBranchIndexVo = new TbBranchIndexVo();
                tbBranchIndexVo.setPartyIndex(item.getPartyIndex());
                tbBranchIndexVo.setBusinessIndex(item.getBusinessIndex());
                tbBranchIndexVo.setInnovationIndex(item.getInnovationIndex());
                tbBranchIndexVo.setStaMonth(item.getStaMonth());
                tbBranchIndexVo.setFortressIndex(item.getFortressIndex());
                tbBranchIndexList.add(tbBranchIndexVo);
            });
            tbBranchIndexList.forEach(item -> {
                beforeMonth.remove(item.getStaMonth());
            });
            //拼装前面几个月的空数据
            beforeMonth.forEach(item -> {
                TbBranchIndexVo tbBranchIndexVo = new TbBranchIndexVo();
                tbBranchIndexVo.setStaMonth(item);
                tbBranchIndexList.add(tbBranchIndexVo);
            });
            //然后对年月进行排序
            List<List<Integer>> list = setHistogramData(tbBranchIndexList.stream().
                    sorted(Comparator.comparing(TbBranchIndexVo::getStaMonth)).collect(Collectors.toList()));
            orgTabViewVo.setListIndexHistory(list);
        }
    }


    /**
     * 设置柱状图数据
     */
    private List<List<Integer>> setHistogramData(List<TbBranchIndexVo> tbBranchIndexList) {
        List<List<Integer>> list = new LinkedList<>();
        //党建指标
        List<Integer> d1 = Arrays.asList(0, 0, 0, 0, 0, 0);
        //业务指标
        List<Integer> d2 = Arrays.asList(0, 0, 0, 0, 0, 0);
        //创建指标
        List<Integer> d3 = Arrays.asList(0, 0, 0, 0, 0, 0);
        //支部的堡垒指数
        List<Integer> d4 = Arrays.asList(0, 0, 0, 0, 0, 0);
        //月份
        List<Integer> d5 = Arrays.asList(0, 0, 0, 0, 0, 0);
        for (int i = 0; i < tbBranchIndexList.size(); i++) {
            TbBranchIndexVo tbBranchIndexVo = tbBranchIndexList.get(i);
            d1.set(i, tbBranchIndexVo.getPartyIndex() == null ? 0 : tbBranchIndexVo.getPartyIndex().intValue());
            d2.set(i, tbBranchIndexVo.getBusinessIndex() == null ? 0 : tbBranchIndexVo.getBusinessIndex().intValue());
            d3.set(i, tbBranchIndexVo.getInnovationIndex() == null ? 0 : tbBranchIndexVo.getInnovationIndex().intValue());
            d4.set(i, tbBranchIndexVo.getFortressIndex() == null ? 0 : tbBranchIndexVo.getFortressIndex().intValue());
            d5.set(i, Integer.parseInt(tbBranchIndexVo.getStaMonth().substring(4, 6)));
        }
        list.add(d1);
        list.add(d2);
        list.add(d3);
        list.add(d4);
        list.add(d5);
        return list;
    }

    /**
     * 得到组织类型
     */
    private Integer getOverviewType(HeaderHelper.SysHeader sysHeader) {
        OrganizationEntity organizationEntity = orgService.getById(sysHeader.getOid());
        if (null == organizationEntity) {
            throw new RuntimeException("未查询到组织信息");
        }
        //党委党总支
        if (orgTypeConfig.checkIsCommunist(organizationEntity.getOrgTypeChild())) {
            return 1;
        }
        //党小组与党支部
        orgTypeConfig.checkIsBranch(organizationEntity.getOrgTypeChild());
        //默认返回支部信息
        return 2;
    }

    /**
     * 获取 (组织 || 人员) 积分流水
     *
     * @param orgId  组织id
     * @param userId 用户id
     * @return
     */
    public List<TbcScoreForm> getScoreLog(Long orgId, Long userId, Integer page, Integer pageSize) {
        if (!ObjectUtils.isEmpty(orgId)) {
            return PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> tbcBasicsMapper.getTbcOrgScoreRecord(orgId));
        } else if (!ObjectUtils.isEmpty(userId)) {
            return PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> tbcBasicsMapper.getTbcUserScoreRecord(userId));
        }
        return new ArrayList<>();
    }

    /**
     * 用户信息社稷
     *
     * @param headers
     * @return
     */
    public ResponseEntity<Result<?>> tbcUser(HttpHeaders headers, Long userId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        UserTabViewVo userTabViewVo = new UserTabViewVo();
        Example exampleUser = new Example(TbcUserInfoEntity.class);
        exampleUser.createCriteria().andEqualTo("userId", userId)
                .andEqualTo("staMonth", DateUtils.getCurrentMonth());
        TbcUserInfoEntity tbcUserInfo = tbcUserInfoMapper.selectOneByExample(exampleUser);
        if (null == tbcUserInfo) {
            throw new ApiException("用户信息不存在", new Result<>(errors, 9404,
                    HttpStatus.OK.value(), "用户信息不存在"));
        }
        userTabViewVo.setUserName(tbcUserInfo.getUserName());
        userTabViewVo.setOrgName(tbcUserInfo.getOrgName());
        userTabViewVo.setDepartment(tbcUserInfo.getDepartment());
        userTabViewVo.setPosition(tbcUserInfo.getPosition());
        userTabViewVo.setPartyPosition(tbcUserInfo.getPartyPosition());
        userTabViewVo.setXianFengIndwx("" + tbcUserInfo.getXianfengIndex());
        //查询当前月的排名
        String currentMonth = DateUtils.getCurrentMonth();
        //处理人员排名
        handRank(userId, currentMonth, userTabViewVo);
        //处理图形全市
        List<String> lastSixMonth = DateUtils.getContinuousMonth(5, "yyyyMM");
        List<UserRankVo> allCityRankAvg = new ArrayList<>();
        lastSixMonth.forEach(item -> {
            UserRankVo UserRankVoResult = tbcUserInfoMapper.allCityRankAvg(userId, item,
                    tbcMonitorsTagConfig.getExcludeOrgIds());
            if (null == UserRankVoResult) {
                UserRankVo userRankVo = new UserRankVo();
                userRankVo.setStaMonth(item);
                userRankVo.setXianFengIndex("0");
                allCityRankAvg.add(userRankVo);
            } else {
                allCityRankAvg.add(UserRankVoResult);
            }
        });
        //处理图形 支部平均
        List<UserRankVo> branchRankAvg = new ArrayList<>();
        //拼装前面几个月的空数据
        lastSixMonth.forEach(item -> {
            UserRankVo branchRankAvgResult = tbcUserInfoMapper.
                    branchRankAvg(userId, item, tbcMonitorsTagConfig.getExcludeOrgIds());
            if (null == branchRankAvgResult) {
                UserRankVo userRankVo = new UserRankVo();
                userRankVo.setStaMonth(item);
                userRankVo.setXianFengIndex("0");
                branchRankAvg.add(userRankVo);
            } else {
                branchRankAvg.add(branchRankAvgResult);
            }
        });
        //查询当前用户几个月的记录
        String staMonth = String.join(",", lastSixMonth);
        List<UserRankVo> userRankAvg = tbcUserInfoMapper.userRankAvg(userId, staMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
        userRankAvg.removeAll(Collections.singleton(null));
        if (!CollectionUtils.isEmpty(userRankAvg)) {
            userRankAvg.forEach(item -> {
                lastSixMonth.remove(item.getStaMonth());
            });
        }
        //拼装前面几个月的空数据
        lastSixMonth.forEach(item -> {
            UserRankVo userRankVo = new UserRankVo();
            userRankVo.setStaMonth(item);
            userRankVo.setXianFengIndex("0");
            userRankAvg.add(userRankVo);
        });
        //处理图形界面
        List<List<Integer>> list = setBarGraph(allCityRankAvg, branchRankAvg, userRankAvg);
        userTabViewVo.setListIndexHistory(list);
        //处理个人中心图表
        userTabViewVo.setPersonHistory(setPersonBarGraph(userId));
        //用户指数变化
        //userTabViewVo.setUserIndexMonth(setUserIndexMonth(userId,sysHeader.getOid()));
        //用户指数变化
        userTabViewVo.setRankGo(setRankGO(userId));
        //处理三个报表块
        List<TbcBaseIndexForm> tbcBaseUserOrg = tbcBasicsMapper.getTbcBaseUserScore(userId);
        userTabViewVo.setLisTbcBaseIndex(tbcBaseUserOrg);
        return new ResponseEntity<>(new Result<>(userTabViewVo, errors), HttpStatus.OK);
    }


    /**
     * 用户指数变化
     *
     * @param userId
     * @return
     */
    private Integer setRankGO(Long userId) {
        List<String> staMonth = DateUtils.getContinuousMonth(1, "yyyyMM");
        Example example = new Example(TbcUserInfoEntity.class);
        example.createCriteria().andIn("staMonth", staMonth).andEqualTo("userId", userId);
        example.setOrderByClause("sta_month Desc");
        List<TbcUserInfoEntity> tbcUserInfoEntities = tbcUserInfoMapper.selectByExample(example);
        if (tbcUserInfoEntities.size() < 2) {
            return 0;
        }
        TbcUserInfoEntity currentMontXF = tbcUserInfoEntities.get(0);
        TbcUserInfoEntity lastMonthXF = tbcUserInfoEntities.get(1);
        UserTabViewVo.UserIndex userIndex = new UserTabViewVo.UserIndex();
        userIndex.setIndexName("个人先峰指数");
        userIndex.setIndex(currentMontXF.getXianfengIndex());
        double v = currentMontXF.getXianfengIndex() - lastMonthXF.getXianfengIndex();
        if (v > 0) {
            return 1;
        } else if (v == 0) {
            return 0;
        } else {
            return -1;
        }
    }


//    private List<UserTabViewVo.UserIndex> setUserIndexMonth(Long userId,Long orgId) {
//        List<String> staMonth = DateUtils.getContinuousMonth(1, "yyyyMM");
//        Example example=new Example(TbcUserInfoEntity.class);
//        example.createCriteria().andIn("staMonth", staMonth).andEqualTo("userId", userId);
//        example.setOrderByClause("sta_month Desc");
//        List<TbcUserInfoEntity> tbcUserInfoEntities = tbcUserInfoMapper.selectByExample(example);
//        List<UserTabViewVo.UserIndex> list = new ArrayList<>();
//        TbcUserInfoEntity currentMontXF = tbcUserInfoEntities.get(0);
//        TbcUserInfoEntity lastMonthXF = tbcUserInfoEntities.get(1);
//        UserTabViewVo.UserIndex userIndex = new UserTabViewVo.UserIndex();
//        userIndex.setIndexName("个人先峰指数");
//        userIndex.setIndex(currentMontXF.getXianfengIndex().intValue());
//        userIndex.setAmplitude(NumberUtils.divide(currentMontXF.getXianfengIndex() - lastMonthXF.getXianfengIndex(),
//                lastMonthXF.getXianfengIndex().intValue()==0?1:lastMonthXF.getXianfengIndex(), 2));
//        list.add(userIndex);
//
//
//        Example exampleFor=new Example(TbcOrgInfoEntity.class);
//        exampleFor.createCriteria().andIn("staMonth", staMonth).andEqualTo("orgId", orgId);
//        exampleFor.setOrderByClause("sta_month Desc");
//        List<TbcOrgInfoEntity> tbcOrgInfoEntities = tbcOrgInfoMapper.selectByExample(exampleFor);
//        TbcOrgInfoEntity currentMontFor = tbcOrgInfoEntities.get(0);
//        TbcOrgInfoEntity lastMonthFor = tbcOrgInfoEntities.get(1);
//        UserTabViewVo.UserIndex orgIndex = new UserTabViewVo.UserIndex();
//        orgIndex.setIndexName("组织堡垒指数");
//        orgIndex.setIndex(currentMontFor.getFortressIndex().intValue());
//        orgIndex.setAmplitude(NumberUtils.divide(currentMontFor.getFortressIndex() - lastMonthFor.getFortressIndex(),
//                lastMonthFor.getFortressIndex().intValue()==0?1:lastMonthFor.getFortressIndex(), 2));
//        list.add(orgIndex);
//        return list;
//    }

    /**
     * 处理排名
     *
     * @param userId
     * @param currentMonth
     * @param userTabViewVo
     */
    private void handRank(Long userId, String currentMonth, UserTabViewVo userTabViewVo) {
        //全市排名
        List<UserRankVo> allCityRank = tbcUserInfoMapper.allCityRank(userId, currentMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
        Optional<UserRankVo> firstAllCity = findUserRow(allCityRank, userId);
        if (firstAllCity.isPresent()) {
            UserRankVo userRankVo = firstAllCity.get();
            Long row = userRankVo.getRow();
            //int rowTotal =  tbcUserInfoMapper.allCityRankCount(userId, currentMonth,tbcMonitorsTagConfig.getExcludeOrgIds());
            //userTabViewVo.setCityRank(NumberUtils.divide(row.doubleValue(), rowTotal, 5));
            userTabViewVo.setCityRank(row.intValue());
        }
        //全市平均
        Integer avgIndex = tbcUserInfoMapper.allCityAvg(currentMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
        userTabViewVo.setAvgIndex(avgIndex);

        //单位排名
        List<UserRankVo> unitRank = tbcUserInfoMapper.unitRank(userId, currentMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
        Optional<UserRankVo> firstUnitCity = findUserRow(unitRank, userId);
        if (firstUnitCity.isPresent()) {
            UserRankVo userRankVo = firstUnitCity.get();
            Long row = userRankVo.getRow();
            //int rowTotal = tbcUserInfoMapper.unitRankCount(userId, currentMonth,tbcMonitorsTagConfig.getExcludeOrgIds());
            //userTabViewVo.setUnitRank(NumberUtils.divide(row.doubleValue(), rowTotal, 5).intValue());
            userTabViewVo.setUnitRank(row.intValue());
        }
        //支部排名
        List<UserRankVo> branchRank = tbcUserInfoMapper.branchRank(userId, currentMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
        Optional<UserRankVo> firstBranch = findUserRow(branchRank, userId);
        if (firstBranch.isPresent()) {
            UserRankVo userRankVo = firstBranch.get();
            Long row = userRankVo.getRow();
            //int rowTotal = tbcUserInfoMapper.branchRankCount(userId, currentMonth,tbcMonitorsTagConfig.getExcludeOrgIds());
            //userTabViewVo.setBranchRank(NumberUtils.divide(row.doubleValue(), rowTotal, 5));
            userTabViewVo.setBranchRank(row.intValue());
        }
    }

    /**
     * 得到用户的排名
     *
     * @return
     */
    private Optional<UserRankVo> findUserRow(List<UserRankVo> listUserRankVo, Long userId) {
        return listUserRankVo.stream().filter(item -> {
            long[] a = Arrays.stream(item.getUserIds().split(",")).mapToLong(Long::valueOf).toArray();
            List<Long> list = Arrays.stream(a).boxed().collect(Collectors.toList());
            return list.contains(userId);
        }).findFirst();
    }


    /**
     * 处理条形图
     */
    private List<List<Integer>> setBarGraph(List<UserRankVo> allCityRankAvg, List<UserRankVo> branchRankAvg,
                                            List<UserRankVo> userRankAvg) {
        List<Integer> allCity = getXianFendIndex(allCityRankAvg);
        List<Integer> branch = getXianFendIndex(branchRankAvg);
        List<Integer> user = getXianFendIndex(userRankAvg);
        List<Integer> listMonth = new ArrayList<>();
        DateUtils.getContinuousMonth(5, "yyyyMM").forEach(item -> {
            listMonth.add(Integer.parseInt(item.substring(4, 6)));
        });
        List<List<Integer>> list = new LinkedList<>();
        list.add(allCity);
        list.add(branch);
        list.add(user);
        list.add(listMonth);
        return list;
    }

    /**
     * 个人中心条形图
     */
    private List<List<Integer>> setPersonBarGraph(Long userId) {
        List<List<Integer>> list = new LinkedList<>();
        List<Integer> partyIndex = new LinkedList<>();
        List<Integer> businessIndex = new LinkedList<>();
        List<Integer> innovationIndex = new LinkedList<>();
        List<Integer> xianFengIndex = new LinkedList<>();
        List<Integer> listMonth = new ArrayList<>();
        DateUtils.getContinuousMonth(5, "yyyyMM").forEach(item -> {
            Example example = new Example(TbcUserInfoEntity.class);
            example.createCriteria().andEqualTo("staMonth", item).andEqualTo("userId", userId);
            TbcUserInfoEntity tbcUserInfoEntity = tbcUserInfoMapper.selectOneByExample(example);
            listMonth.add(Integer.parseInt(item.substring(4, 6)));
            if (null == tbcUserInfoEntity) {
                partyIndex.add(0);
                businessIndex.add(0);
                innovationIndex.add(0);
                xianFengIndex.add(0);
            } else {
                partyIndex.add(tbcUserInfoEntity.getPartyIndex().intValue());
                businessIndex.add(tbcUserInfoEntity.getBusinessIndex().intValue());
                innovationIndex.add(tbcUserInfoEntity.getInnovationIndex().intValue());
                xianFengIndex.add(tbcUserInfoEntity.getXianfengIndex().intValue());
            }
        });
        list.add(partyIndex);
        list.add(businessIndex);
        list.add(innovationIndex);
        list.add(xianFengIndex);
        list.add(listMonth);
        return list;
    }


    /**
     * 得到先锋指数
     *
     * @param listRankAvg
     * @return
     */
    private List<Integer> getXianFendIndex(List<UserRankVo> listRankAvg) {
        List<Integer> list = new LinkedList<>();
        List<UserRankVo> collect = listRankAvg.stream().sorted(Comparator.comparing(UserRankVo::getStaMonth))
                .collect(Collectors.toList());
        collect.forEach(item -> {
            String xianFengIndex = item.getXianFengIndex();
            list.add(Double.valueOf(xianFengIndex).intValue());
        });
        return list;
    }


    /**
     * 党委或党总支 查看支部的统计
     *
     * @param headers
     * @param orgId
     * @param page
     * @param pageSize
     * @return
     */
    public ResponseEntity<Result<?>> tbcOrgMore(HttpHeaders headers, Long orgId, Integer page, Integer pageSize) {
        Example example = new Example(TbcOrgInfoEntity.class);
        example.createCriteria().andLike("orgLevel", "%-" + orgId + "-%")
                .andEqualTo("staMonth", DateUtils.getCurrentMonth())
                .andIn("orgTypeChild", orgTypeConfig.getBranchChild());
        example.selectProperties("orgId", "orgName", "fortressIndex");
        example.setOrderByClause("fortress_index desc");
        return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                .doSelectPage(() -> tbcOrgInfoMapper.selectByExample(example)), errors), HttpStatus.OK);
    }

    /**
     * 支部查看人员的统计信息
     *
     * @param headers
     * @param orgId
     * @param page
     * @param pageSize
     * @return
     */
    public ResponseEntity<Result<?>> tbcUserMore(HttpHeaders headers, Long orgId, Integer page, Integer pageSize) {
        Example example = new Example(TbcUserInfoEntity.class);
        example.createCriteria().andEqualTo("orgId", orgId)
                .andEqualTo("staMonth", DateUtils.getCurrentMonth());
        example.setOrderByClause("xianfeng_index desc");
        example.selectProperties("userId", "userName", "orgName", "xianfengIndex");
        return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                .doSelectPage(() -> tbcUserInfoMapper.selectByExample(example)), errors), HttpStatus.OK);
    }

    /**
     * 拉取基础跑指数
     *
     * @param type
     * @return
     */
    public String getTbcBaseIndex(Integer type) {
        if (type == 1) {
            handUserXianFengIndx(DateUtils.getCurrentMonth());
            return "suc";
        }
        if (type == 2) {
            handOrgFortressIndex(DateUtils.getCurrentMonth());
            return "suc";
        }
        return "error";
    }


    /**
     * 融合概况先锋指数排名
     *
     * @return
     */
    public XFRankStaVo xfRankSta(HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Long userId = sysHeader.getUserId();
        XFRankStaVo xfRankStaVo = new XFRankStaVo();
        String currentMonth = DateUtils.getCurrentMonth();
        //全市排名
        List<UserRankVo> allCityRank = tbcUserInfoMapper.allCityRank(userId, currentMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
        Optional<UserRankVo> firstAllCity = findUserRow(allCityRank, userId);
        if (firstAllCity.isPresent()) {
            UserRankVo userRankVo = firstAllCity.get();
            int rowTotal = tbcUserInfoMapper.allCityRankCount(userId, currentMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
            long row = rowTotal - userRankVo.getRow();
            xfRankStaVo.setCityRank(NumberUtils.divide((double) row, rowTotal, 3));
            //全市排名名次
            xfRankStaVo.setCityRankNumber(userRankVo.getRow().intValue());
            //全市党员的人数
            xfRankStaVo.setCityNumber(rowTotal);
        }
        //单位排名
        List<UserRankVo> unitRank = tbcUserInfoMapper.unitRank(userId, currentMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
        Optional<UserRankVo> firstUnitCity = findUserRow(unitRank, userId);
        if (firstUnitCity.isPresent()) {
            UserRankVo userRankVo = firstUnitCity.get();
            Long row = userRankVo.getRow();
            int rowTotal = tbcUserInfoMapper.unitRankCount(userId, currentMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
            xfRankStaVo.setUnitRank(row.intValue());
            xfRankStaVo.setUnitTotal(rowTotal);
        }
        //个人的先锋指数
        Example example = new Example(TbcUserInfoEntity.class);
        example.createCriteria().andEqualTo("staMonth", currentMonth).andEqualTo("userId", userId);
        TbcUserInfoEntity tbcUserInfoEntity = tbcUserInfoMapper.selectOneByExample(example);
        if (null != tbcUserInfoEntity) {
            xfRankStaVo.setXfIndex(tbcUserInfoEntity.getXianfengIndex());
        }
        //查询
        Integer userSeqNumber = queryUserSeqNumber(sysHeader.getUserId());
        if (null != userSeqNumber) {
            List<UserRankVo> userRankVos = tbcUserInfoMapper.rankBySeqNumber(userId, currentMonth,
                    tbcMonitorsTagConfig.getExcludeOrgIds(), userSeqNumber);
            Optional<UserRankVo> findUserSeq = findUserRow(userRankVos, userId);
            if (findUserSeq.isPresent()) {
                UserRankVo userRankVo = findUserSeq.get();
                int rowTotal = userRankVos.size();
                long row = rowTotal - userRankVo.getRow();
                xfRankStaVo.setSequenceRank(NumberUtils.divide((double) row, rowTotal, 3));
            }
            xfRankStaVo.setSeqNumber(userSeqNumber);
        }
        return xfRankStaVo;
    }


    /**
     * 查询用户所属序列
     *
     * @param userId
     * @return
     */
    private Integer queryUserSeqNumber(Long userId) {
        return userMongoService.getUserSequence(userId);
    }

    /**
     * @param headers
     * @param type    1.先锋指数 2.党建积分3.业务积分4.创新积分
     * @return
     */
    public XfStitchingVo xfStitching(HttpHeaders headers, Long unitId, Integer type) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        XfStitchingVo xfStitchingVo = new XfStitchingVo();
        String colName = "xianfeng_index";
        if (type == 2) {
            colName = "party_score";
        } else if (type == 3) {
            colName = "business_score";
        } else if (type == 4) {
            colName = "innovation_score";
        }
        List<String> beforeMonth = DateUtils.getContinuousMonth(6, "yyyy-MM");
        String startTime = beforeMonth.get(0) + "-01 00:00:00";
        String endTime = beforeMonth.get(beforeMonth.size() - 1) + "-01 00:00:00";
        //统计年月
        List<String> beforeMonths = DateUtils.getContinuousMonth(6, null)
                .stream().limit(6).collect(Collectors.toList());
        //设置时间序列
        List<String> dateList = beforeMonths;
        xfStitchingVo.setXData(dateList);
        //得到我的指标
        List<String> d1 = tbcUserInfoMapper.getUserIndex(colName, sysHeader.getUserId(), startTime, endTime);
        xfStitchingVo.setD1(d1);
        //系统均值
        List<String> d2 = tbcUserInfoMapper.getSystemIndex(colName, startTime, endTime);
        xfStitchingVo.setD2(d2);
        Integer seqNumber = queryUserSeqNumber(sysHeader.getUserId());
        if (null != seqNumber) {
            //系统均值
            List<String> d3 = tbcUserInfoMapper.getSeqIndex(colName, seqNumber, startTime, endTime);
            xfStitchingVo.setD3(d3);
            //更新坐标值
            xfStitchingVo.getSData().set(2, UserSeqEnum.getSeqInfo(seqNumber).getSeqAvgName());
        } else {
            //如果等于空情况下 不显示序列信息
            String[] strArray = {"我的", "系统均值", "本单位均值", "本单位最高"};
            xfStitchingVo.setSData(new ArrayList<>(Arrays.asList(strArray)));
        }
        //单位均值
        TbcBaseVo tbcBaseVo = userMapper.getAllUserInfoByUserId(sysHeader.getUserId());
        List<String> d4 = tbcUserInfoMapper.getUnitIndex(colName, tbcBaseVo.getOwnerId(), startTime, endTime);
        xfStitchingVo.setD4(d4);
        //本单位最高指数
        List<String> d5 = tbcUserInfoMapper.getUnitMaxIndex(colName, startTime, endTime, unitId);
        if (CollectionUtils.isEmpty(d5)) {
            xfStitchingVo.setD5(Arrays.asList("0", "0", "0", "0", "0", "0"));
        } else {
            xfStitchingVo.setD5(d5);
        }
        return xfStitchingVo;
    }

    /**
     * 管理员页面-支部堡垒指数
     *
     * @param headers
     * @return
     */
    public BranchFortressIndexVo fortressIndex(HttpHeaders headers, String staMonth, Long orgId) {
        BranchFortressIndexVo branchFortressIndexVo = new BranchFortressIndexVo();
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //计算支部排名
        List<UserRankVo> branchRank = tbcOrgInfoMapper.allBranchRank(staMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
        Optional<UserRankVo> optionCurrentMonth = branchRank.stream().filter(item -> {
            long[] a = Arrays.stream(item.getOrgIds().split(",")).mapToLong(Long::valueOf).toArray();
            List<Long> list = Arrays.stream(a).boxed().collect(Collectors.toList());
            return list.contains(orgId);
        }).findFirst();
        if (optionCurrentMonth.isPresent()) {
            UserRankVo userRankVo = optionCurrentMonth.get();
            branchFortressIndexVo.setRank(userRankVo.getRow().intValue());
        }
        //我的支部堡垒
        Example example = new Example(TbcOrgInfoEntity.class);
        example.createCriteria().andEqualTo("staMonth", staMonth).andEqualTo("orgId", orgId);
        TbcOrgInfoEntity tbcOrgInfoEntity = tbcOrgInfoMapper.selectOneByExample(example);
        if (null != tbcOrgInfoEntity) {
            branchFortressIndexVo.setBranchFortressIndex(tbcOrgInfoEntity.getFortressIndex());
        }
        //得到系统平均与系统最高
        branchFortressIndexVo.setSystemAverage(tbcOrgInfoMapper.systemAvg(staMonth, tbcMonitorsTagConfig.getExcludeOrgIds()));
        branchFortressIndexVo.setSystemMax(tbcOrgInfoMapper.systemMax(staMonth, tbcMonitorsTagConfig.getExcludeOrgIds()));
        //得到支部堡垒指数排行榜
        List<BranchFortressIndexVo.BranchFortress> branchFortresses = tbcOrgInfoMapper.branchRank(staMonth,
                tbcMonitorsTagConfig.getExcludeOrgIds(), 3000);
        branchFortressIndexVo.setList(branchFortresses);
        return branchFortressIndexVo;
    }

    /**
     * 管理员页面-支部堡垒指数-详情
     *
     * @param headers
     * @return
     */
    public List<BranchFortressIndexVo.BranchFortress> fortressIndexDetail(HttpHeaders headers, String staMonth) {
        //得到支部堡垒指数排行榜
        return tbcOrgInfoMapper.branchRank(staMonth, tbcMonitorsTagConfig.getExcludeOrgIds(), 5000);
    }

    /**
     * 管理员页面-党员先锋指数
     *
     * @param headers
     * @return
     */
    public PartyMemberXFIndexVo partyMemberXfindex(HttpHeaders headers, String staMonth, Long unitId) {
        try {
            HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
            PartyMemberXFIndexVo partyMemberXFIndexVo = tbcUserInfoMapper
                    .unitRankLimitInfo(unitId, staMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
            //得到系统极限值情况信息
            PartyMemberXFIndexVo systemRankLimitInfo = tbcUserInfoMapper
                    .systemRankLimitInfo(sysHeader.getUserId(), staMonth, tbcMonitorsTagConfig.getExcludeOrgIds());
            partyMemberXFIndexVo.setSystemAverage(systemRankLimitInfo.getSystemAverage());
            partyMemberXFIndexVo.setSystemMax(systemRankLimitInfo.getSystemMax());
            //返回前100名的信息
            List<PartyMemberXFIndexVo.XianFengIndex> xianFengIndex = tbcUserInfoMapper.rankTop(staMonth,
                    tbcMonitorsTagConfig.getExcludeOrgIds(), unitId, 100);
            partyMemberXFIndexVo.setList(xianFengIndex);
            return partyMemberXFIndexVo;
        } catch (Exception exception) {
            log.error("partyMemberXfindex计算数据发生异常", exception);
            return null;
        }
    }

    /**
     * 管理员页面人员先峰指数-详情
     * 1.本单位 2.全市top100
     *
     * @param headers
     * @return
     */
    public List<PartyMemberXFIndexVo.XianFengIndex> partyMemberXfindexDetail(HttpHeaders headers,
                                                                             String staMonth, Integer type, Long unitId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //返回前100名的信息
        if (type == 1) {
            return tbcUserInfoMapper.unitRankDetail(sysHeader.getUserId(), staMonth,
                    tbcMonitorsTagConfig.getExcludeOrgIds(), unitId);
        } else {
            return tbcUserInfoMapper.rankTop(staMonth, tbcMonitorsTagConfig.getExcludeOrgIds(), null, 100);
        }

    }

    public HSSFWorkbook exportFortressIndexExcel(List<BranchFortressIndexVo.BranchFortress> branchFortresses,
                                                 String excelName, String[] colName) {
        HSSFWorkbook workBook = new HSSFWorkbook();
        HSSFSheet sheet = setHSSFWorkbook(workBook, excelName, colName);
        //设置列宽
        sheet.setColumnWidth(0, 6 * 256);
        sheet.setColumnWidth(1, 65 * 256);
        sheet.setColumnWidth(2, 12 * 256);
        sheet.setColumnWidth(3, 12 * 256);
        sheet.setColumnWidth(4, 12 * 256);
        sheet.setColumnWidth(5, 12 * 256);
        //从那列开始计算
        int startRow = 2;
        if (!CollectionUtils.isEmpty(branchFortresses)) {
            for (BranchFortressIndexVo.BranchFortress item : branchFortresses) {
                HSSFRow row = sheet.createRow(startRow);
                for (int i = 0; i < colName.length; i++) {
                    HSSFCell cell = row.createCell(i);
                    //{"排名","支部名称", "堡垒指数","党建积分","业务积分","创新积分"}
                    if (i == 0) {
                        cell.setCellValue(null == item.getRank() ? "" : String.valueOf(item.getRank()));
                    } else if (i == 1) {
                        cell.setCellValue(null == item.getOrgName() ? "" : item.getOrgName());
                    } else if (i == 2) {
                        cell.setCellValue(null == item.getFortressIndex() ? "" : String.valueOf(item.getFortressIndex()));
                    } else if (i == 3) {
                        cell.setCellValue(null == item.getPartyIndex() ? "" : String.valueOf(item.getPartyIndex()));
                    } else if (i == 4) {
                        cell.setCellValue(null == item.getBusinessIndex() ? "" : String.valueOf(item.getBusinessIndex()));
                    } else if (i == 5) {
                        cell.setCellValue(null == item.getInnovationIndex() ? "" : String.valueOf(item.getInnovationIndex()));
                    }
                }
                startRow++;
            }
            //合并第一行 两列数据
            CellRangeAddress cellAddresses = new CellRangeAddress(0, 0, 0, colName.length);
            sheet.addMergedRegion(cellAddresses);
        }
        return workBook;
    }

    /**
     * 设置表格的样式
     */
    private HSSFSheet setHSSFWorkbook(HSSFWorkbook workBook, String excelName, String[] colName) {
        //创建字体
        HSSFFont font = workBook.createFont();
        font.setFontHeightInPoints((short) 12);
        //普通样式
        HSSFCellStyle style = workBook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFont(font);
        style.setWrapText(true);
        //头部字体
        HSSFFont headFont = workBook.createFont();
        headFont.setFontHeightInPoints((short) 20);
        //表头样式
        HSSFCellStyle headStyle = workBook.createCellStyle();
        headStyle.setAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headStyle.setFont(headFont);
        HSSFSheet sheet = workBook.createSheet(excelName);
        //第一行数据
        HSSFRow row0 = sheet.createRow(0);
        HSSFCell cell_00 = row0.createCell(0);
        cell_00.setCellValue(excelName);
        cell_00.setCellStyle(headStyle);
        //第二行数据开始配置表头
        HSSFRow row1 = sheet.createRow(1);
        for (int i = 0; i < colName.length; i++) {
            HSSFCell cell = row1.createCell(i);
            cell.setCellValue(colName[i]);
            cell.setCellStyle(style);
        }
        return sheet;
    }


    public HSSFWorkbook exportXianFengIndexExcel(List<PartyMemberXFIndexVo.XianFengIndex> xianFengIndices,
                                                 String excelName, String[] colName) {
        HSSFWorkbook workBook = new HSSFWorkbook();
        HSSFSheet sheet = setHSSFWorkbook(workBook, excelName, colName);
        //设置列宽
        sheet.setColumnWidth(0, 6 * 256);
        sheet.setColumnWidth(1, 12 * 256);
        sheet.setColumnWidth(2, 65 * 256);
        sheet.setColumnWidth(3, 12 * 256);
        sheet.setColumnWidth(4, 12 * 256);
        sheet.setColumnWidth(5, 12 * 256);
        sheet.setColumnWidth(6, 12 * 256);
        //从那列开始计算
        int startRow = 2;
        if (!CollectionUtils.isEmpty(xianFengIndices)) {
            for (PartyMemberXFIndexVo.XianFengIndex item : xianFengIndices) {
                HSSFRow row = sheet.createRow(startRow);
                for (int i = 0; i < colName.length; i++) {
                    HSSFCell cell = row.createCell(i);
                    //{"排名","党员姓名", "所属支部","先锋指数","党建积分","业务积分","创新积分"};
                    if (i == 0) {
                        cell.setCellValue(null == item.getRank() ? "" : String.valueOf(item.getRank()));
                    } else if (i == 1) {
                        cell.setCellValue(null == item.getName() ? "" : item.getName());
                    } else if (i == 2) {
                        cell.setCellValue(null == item.getOrgName() ? "" : item.getOrgName());
                    } else if (i == 3) {
                        cell.setCellValue(null == item.getXainFengIndex() ? "" : String.valueOf(item.getXainFengIndex()));
                    } else if (i == 4) {
                        cell.setCellValue(null == item.getPartyIndex() ? "" : String.valueOf(item.getPartyIndex()));
                    } else if (i == 5) {
                        cell.setCellValue(null == item.getBusinessIndex() ? "" : String.valueOf(item.getBusinessIndex()));
                    } else if (i == 6) {
                        cell.setCellValue(null == item.getInnovationIndex() ? "" : String.valueOf(item.getInnovationIndex()));
                    }
                }
                startRow++;
            }
            //合并第一行 两列数据
            CellRangeAddress cellAddresses = new CellRangeAddress(0, 0, 0, colName.length);
            sheet.addMergedRegion(cellAddresses);
        }
        return workBook;
    }

    /**
     * * 清洗 new_owner_id 和 seq_number 两个字段信息
     *
     * @return
     */
    public void cleaningTbcBaseInfo() {
        List<Long> tbcUserInfoEntities = tbcUserInfoMapper.getDistinctUserInfo();
        if (!CollectionUtils.isEmpty(tbcUserInfoEntities)) {
            tbcUserInfoEntities.forEach(item -> {
                try {
                    Integer seqNumber = queryUserSeqNumber(item);
                    TbcBaseVo allUserInfoByUserId = userMapper.getAllUserInfoByUserId(item);
                    Long newOwnerId = -1L;
                    if (null != allUserInfoByUserId) {
                        newOwnerId = userMapper.getAllUserInfoByUserId(item).getOwnerId();
                    }
                    tbcUserInfoMapper.cleaningTbcBaseInfo(seqNumber, newOwnerId, item);
                } catch (Exception ex) {
                    log.error("清洗数据发生异常", ex);
                    log.info("清洗数据异常用户id={}", item);
                }
            });
        }
    }

    /**
     * 党业融合 V4.0泳道图实现
     *
     * @param type
     * @param staMonth
     * @return
     */
    public SwimLaneDiagramVo swimLaneDiagram(Long unitId, Integer type, String staMonth) {
        try {
            SwimLaneDiagramVo swimLaneDiagramVo = new SwimLaneDiagramVo(0.0, 0.0);
            //List<Integer> politicalTypeFormal = new ArrayList<>(Arrays.asList(1, 5, 17,18));
            PbmOrgUserKitInfo userStaInfo1 = getUserStaInfo(unitId, staMonth, type);
            if (userStaInfo1 != null) {
                ScoreInfo allBusinessAvg = userStaInfo1.getAllBusinessAvg();
                //设置顶部统计信息
                swimLaneDiagramVo.setAllPartyAvg(allBusinessAvg.getBe());
                swimLaneDiagramVo.setNonPartyAvg(allBusinessAvg.getNon());
            }

            List<SwimLaneDiagramVo.DetailResult> detailResult = new LinkedList<>();
            //算到所有分信息
            List<OrganizationBase> companyInfo = organizationMapper.getCompanyInfo();
            List<String> listName = new LinkedList<>();
            companyInfo.forEach(item -> {
                SwimLaneDiagramVo.DetailResult detailResultSub = new SwimLaneDiagramVo.DetailResult();
                //从mongo里面查询统计数据
                PbmOrgUserKitInfo userStaInfo = getUserStaInfo(item.getOrganizationId(), staMonth, type);
                if (null == userStaInfo) {
                    return;
                }
                //设置分公司信息
                listName.add(item.getName());
                //设置用户最高信息
                List<PbmUserKitInfo> maxUserKitInfo = userStaInfo.getMaxUserKitInfo();
                List<SwimLaneDiagramVo.TopUserInfo> topIfoS = new LinkedList<>();
                maxUserKitInfo.forEach(it -> {
                    SwimLaneDiagramVo.TopUserInfo topUserInfo = new SwimLaneDiagramVo.TopUserInfo();
                    topUserInfo.setName(it.getUserName());
                    topUserInfo.setPhone(it.getPhone());
                    Integer politicalType = it.getPoliticalType();
                    if (null == politicalType) {
                        topUserInfo.setPoliticsName("非党员");
                        return;
                    }
                    if (politicalType == 1) {
                        topUserInfo.setPoliticsName("正式党员");
                    } else if (politicalType == 5) {
                        topUserInfo.setPoliticsName("正式党员");
                    } else {
                        topUserInfo.setPoliticsName("非党员");
                    }
                    topUserInfo.setValue(it.getPoliticalType());
                    topUserInfo.setUnitName(it.getUnitName());
                    topUserInfo.setValue((int) it.getBusiness());
                    topIfoS.add(topUserInfo);
                });
                //设置分公区的党员与非党员业务积分信息
                List<SwimLaneDiagramVo.BranchAverage> branchAveragesList = new LinkedList<>();
                //设置本单位均值
                ScoreInfo selfBusinessAvg = userStaInfo.getSelfBusinessAvg();
                for (int i = 1; i <= 2; i++) {
                    SwimLaneDiagramVo.BranchAverage branchAverageDetail = getBranchAverage(i, selfBusinessAvg);
                    branchAveragesList.add(branchAverageDetail);
                }
                detailResultSub.setBranchAverages(branchAveragesList);
                detailResultSub.setTopIfoS(topIfoS);
                detailResult.add(detailResultSub);
            });
            //设置报表信息
            swimLaneDiagramVo.setListName(listName);
            swimLaneDiagramVo.setDetailResult(detailResult);
            //得到最高分数以及所在组织信息
            List<SwimLaneDiagramVo.TopUserInfo> lisTops = new LinkedList<>();
            List<SwimLaneDiagramVo.DetailResult> detailResult1 = swimLaneDiagramVo.getDetailResult();
            detailResult1.forEach(item -> {
                if (!CollectionUtils.isEmpty(item.getTopIfoS())) {
                    lisTops.addAll(item.getTopIfoS());
                }
            });
            if (!CollectionUtils.isEmpty(lisTops)) {
                SwimLaneDiagramVo.TopUserInfo maxResult = lisTops.stream()
                        .max(Comparator.comparingInt(SwimLaneDiagramVo.TopUserInfo::getValue)).get();
                swimLaneDiagramVo.setSystemTop(maxResult.getValue());
                swimLaneDiagramVo.setSystemTopCompany(maxResult.getUnitName());
            }
            return swimLaneDiagramVo;
        } catch (Exception ex) {
            log.error("swimLaneDiagram出错,type={},staMonth={}", type, staMonth);
            log.error("swimLaneDiagram出错异常", ex);
            return null;
        }
    }

    @NotNull
    private static SwimLaneDiagramVo.BranchAverage getBranchAverage(int i, ScoreInfo selfBusinessAvg) {
        SwimLaneDiagramVo.BranchAverage branchAverageDetail = new SwimLaneDiagramVo.BranchAverage();
        branchAverageDetail.setPoliticsType(i);
        if (i == 1) {
            branchAverageDetail.setNumber(selfBusinessAvg.getBeNum());
            branchAverageDetail.setValue(selfBusinessAvg.getBe());
        } else {
            branchAverageDetail.setNumber(selfBusinessAvg.getNonNum());
            branchAverageDetail.setValue(selfBusinessAvg.getNon());
        }
        return branchAverageDetail;
    }

    /**
     * 从mongo里面查询统计数据
     *
     * @param staMonth
     * @param type     类型 0 -> 全体，1 -> 卷烟营销，2 -> 烟叶生成，3 -> 专卖管理，4 -> 综合管理
     * @return
     */
    private PbmOrgUserKitInfo getUserStaInfo(Long uintId, String staMonth, Integer type) {
        Criteria criteria = new Criteria();
        if (uintId != null) {
            criteria.andOperator(
                    Criteria.where("unitId").is(uintId),
                    Criteria.where("date").is(staMonth),
                    Criteria.where("type").is(type)
            );
        } else {
            criteria.andOperator(
                    Criteria.where("date").is(staMonth),
                    Criteria.where("type").is(type)
            );
        }
        Query query = new Query(criteria);
        try {
            return this.mongoTemplate.findOne(query, PbmOrgUserKitInfo.class);
        } catch (Exception ex) {
            log.info("TbcBasicsService.getUserStaInfo查询异常", ex);
            return null;
        }
    }


    /**
     * 泳道图详情
     *
     * @param unitId
     * @param type
     * @param staMonth
     * @return
     */
    public List<SwimLaneDiagramDetailVo> swimLaneDiagramDetail(Long unitId, Integer type, String staMonth) {
        Criteria criteria = new Criteria();
        if (type == 0) {
            criteria.andOperator(
                    Criteria.where("date").is(staMonth)
            );
        } else {
            criteria.andOperator(
                    Criteria.where("date").is(staMonth),
                    Criteria.where("sequence").is(type)
            );
        }
        Query query = new Query(criteria);
        query.with(Sort.by(new Sort.Order(Sort.Direction.DESC, "business"))).limit(100);
        List<SwimLaneDiagramDetailVo> list = new ArrayList<>();
        try {
            List<PbmUserKitInfo> pbmUserKitInfos = this.mongoTemplate.find(query, PbmUserKitInfo.class);
            List<PbmUserKitInfo> collectSort = pbmUserKitInfos.stream().filter(item ->
                            (Objects.equals(item.getPoliticalType(), 1) ||
                                    Objects.equals(item.getPoliticalType(), 5) ||
                                    Objects.equals(item.getPoliticalType(), 17) ||
                                    Objects.equals(item.getPoliticalType(), 18)))
                    .sorted(Comparator.comparingLong(PbmUserKitInfo::getBusiness).reversed()).toList();
            if (!CollectionUtils.isEmpty(collectSort)) {
                collectSort.forEach(item -> {
                    SwimLaneDiagramDetailVo swimLaneDiagramDetailVo = new SwimLaneDiagramDetailVo();
                    swimLaneDiagramDetailVo.setName(item.getUserName());
                    swimLaneDiagramDetailVo.setUnitName(item.getUnitName());
                    swimLaneDiagramDetailVo.setPost(item.getTitle());
                    list.add(swimLaneDiagramDetailVo);
                });
            }
            return list;
        } catch (Exception ex) {
            log.error("TbcBasicsService.swimLaneDiagramDetail查询异常", ex);
            return null;
        }
    }

    /**
     * 得到用户所属序列号
     *
     * @param userId
     * @return
     */
    public Integer getSeq(Long userId) {
        return queryUserSeqNumber(userId);
    }


}
