package com.goodsogood.ows.service.tbcFusion.test;

import com.goodsogood.ows.model.vo.tbcFusion.*;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 假数据接口
 *
 * <AUTHOR>
 * @date 2021/12/03
 */
@Service
public class TbcFalseDataService {
    //基础信息===================================================================================

    /**
     * 党支部基础信息统计
     *
     * @return
     */
    public List<TbcBasicsForm.OrgUserDetails> getTbcBasics() {
        List<TbcBasicsForm.OrgUserDetails> detailsList = new ArrayList<>();
        Map<Integer, String> map = new HashMap<>();
        map.put(1, "综合管理党员");
        map.put(2, "烟叶生成党员");
        map.put(3, "专卖管理党员");
        map.put(4, "卷烟营销党员");
        for (int i = 1; i <= 4; i++) {
            TbcBasicsForm.OrgUserDetails details = new TbcBasicsForm.OrgUserDetails();
            details.setName(map.get(i));
            details.setValue((int) (Math.random() * 100));
            detailsList.add(details);
        }
        return detailsList;
    }

    /**
     * 党支部党树统计信息
     *
     * @param type 1:党支部 2:云上党支部 3:党员 4:职工
     * @return
     */
    public Integer getOrgBasics(Integer type) {
        // TODO 根据type 获取对应的人数
        switch (type) {
            case 1://党支部
                return (int) (150 + Math.random() * 200 - 150 + 1);
            case 2://云上支部
                return (int) (180 + Math.random() * 300 - 180 + 1);
            case 3://党员
                return (int) (1800 + Math.random() * 3000 - 1800 + 1);
            case 4://职工
                return (int) (4321 + Math.random() * 10000 - 4321 + 1);
            default:
                return 0;
        }
    }

    //支部平均堡垒指数===================================================================================

    /**
     * 支部堡垒指数统计
     *
     * @return
     */
    public TbcFortressForm getTbcFortress() {
        TbcFortressForm form = new TbcFortressForm();
        form.setName("党建: 92  业务：91  创新：84");
        form.setValue("100");
        return form;
    }

    //创新情况统计===================================================================================

    /**
     * 创新情况统计(词云)
     *
     * @return
     */
    public List<TbcInnovateForm> getTbcInnovate() {
        List<TbcInnovateForm> list = new ArrayList<>();
        Map<Integer, String> map = new HashMap<>();
        map.put(0, "营销创新");
        map.put(1, "管理创新");
        map.put(2, "技术创新");
        map.put(3, "活动创新");
        map.put(4, "工具创新");
        map.put(5, "流程创新");
        map.put(6, "管理创新");
        map.put(7, "工具创新");
        map.put(8, "基础创新");
        DecimalFormat df = new DecimalFormat("#.0");
        for (int i = 0; i < 8; i++) {
            TbcInnovateForm form = new TbcInnovateForm();
            form.setName(map.get(i));
            form.setValue(df.format(Math.random() * 9));
            form.setColorField((int) (Math.random() * 5));
            list.add(form);
        }
        return list;
    }

    /**
     * 词云标题
     *
     * @return
     */
    public String getOrgPioneerTilte() {
        String s1 = "完成任务%s项 党员参与%s人次 非党员参与%s人次";
        String result = String.format(s1, (int) (Math.random() * 100), (int) (Math.random() * 100), (int) (Math.random() * 100));
        return result;
    }

    //党业拟合度===================================================================================

    /**
     * 党页拟合度(条形图)
     *
     * @param type 1:
     * @return
     */
    public List<List<String>> getTbcMatching(Integer type) {
        Integer NUMBER = 10;


        String s1 = "涪陵区县、渝中区县、开州区县、梁平区县、万州区县、江津区县、合川区县、黔江区县、永川区县、南川区县、大渡口区县、" +
                "九龙坡区县、南岸区县、北碚区县、江北区县、璧山区县、铜梁区县、沙坪坝区县、渝北区县、巴南区县、长寿区县、綦江区县、大足区县、潼南区县、荣昌区县、武隆区县";
        String[] strings = s1.split("、");
        //1
        List<List<String>> list = new ArrayList<>();
        //2
        List<String> colorTitle = new ArrayList<>();
        //3
        List<String> listTitle = new ArrayList<>();
        //4
        List<String> list1;

        colorTitle.add("党建指标");
        colorTitle.add("业务指标");
        colorTitle.add("堡垒指标");
        colorTitle.add("拟合度");

        for (int i = 0; i < NUMBER; i++) {
            listTitle.add(strings[(int) (Math.random() * 25)]);
        }
        list.add(colorTitle);
        list.add(listTitle);
        DecimalFormat df = new DecimalFormat("#.00");
        DecimalFormat df1 = new DecimalFormat("#.0");

        type = type > 1 ? 2 : 1;
        switch (type) {
            case 1:
                for (int i = 0; i < 4; i++) {
                    list1 = new ArrayList<>();
                    for (int j = 0; j < NUMBER; j++) {
                        if (j == 1) {
                            list1.add(String.valueOf(df.format(Math.random() * 10)));
                            continue;
                        } else if (j == 3) {
                            list1.add(String.valueOf(df1.format(Math.random() * 100.00)));
                            continue;
                        }
                        list1.add(String.valueOf((int) (Math.random() * 100)));
                        listTitle.add(strings[(int) (Math.random() * 25)]);
                    }
                    list.add(list1);
                }
                break;
            case 2:
                for (int i = 0; i < 4; i++) {
                    list1 = new ArrayList<>();
                    for (int j = 0; j < NUMBER; j++) {
                        if (j == ((int) (Math.random() * 10))) {
                            list1.add(String.valueOf(df1.format(Math.random() * 100.00)));
                            continue;
                        }
                        list1.add(String.valueOf((int) (Math.random() * 100)));
                    }
                    list.add(list1);
                }
                break;
        }
        return list;
    }

    //支部"党建+业务"工作情况===================================================================================

    /**
     * 党员”党建+业务”工作情况柱状图
     *
     * @return
     */
    public List<TbcPartyMemberWorkForm> getPartyMember(int type) {
        List<TbcPartyMemberWorkForm> list111 = new ArrayList<>(1);
        TbcPartyMemberWorkForm form = new TbcPartyMemberWorkForm();
        List<List<Integer>> list = new ArrayList<>();
        List<String> sData = new ArrayList<>();
        sData.add("综合管理");
        sData.add("烟叶生产");
        sData.add("专卖管理");
        sData.add("卷烟销售");
        List<String> sData1 = new ArrayList<>();
        sData1.add("30岁以下");
        sData1.add("30岁～45岁");
        sData1.add("45岁～55岁");
        sData1.add("55岁以上");
        List<String> xData = new ArrayList<>();
        xData.add("业务指标");
        xData.add("党建指标");
        xData.add("创新指标");
        xData.add("先锋指数");
        for (int i = 1; i <= 4; i++) {
            List<Integer> array = new ArrayList<>();
            for (int j = 1; j <= 4; j++) {
                int num = (int) (Math.random() * 100);
                array.add(num);
            }
            list.add(array);
        }
        for (int i = 0; i < list.size(); i++) {
            switch (i) {
                case 0:
                    form.setD1(list.get(i));
                    continue;
                case 1:
                    form.setD2(list.get(i));
                    continue;
                case 2:
                    form.setD3(list.get(i));
                    continue;
                case 3:
                    form.setD4(list.get(i));
                    break;
            }
        }
        if (type == 1) {
            form.setSData(sData1);
        } else {
            form.setSData(sData1);
        }
        form.setXData(xData);
        list111.add(form);
        return list111;
    }

    //党员"党建+业务"工作情况===================================================================================

    /**
     * 散点图
     *
     * @return
     */
    public List<List<Double>> getSplashes() {
        List<List<Double>> l1 = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            List<Double> l2 = new ArrayList<>();
            for (int j = 0; j < 2; j++) {
                DecimalFormat df = new DecimalFormat("#.00");
                double num = (Math.random() * (100 - 40 + 1) + 40) + 1.22;
                String s1 = df.format(num);

                l2.add(Double.valueOf(s1));
            }
            l1.add(l2);
        }
        return l1;
    }

    //党页先锋指数前%20分析===================================================================================

    /**
     * 党页先锋指数前%20分析
     *
     * @return
     */
    public TbcPioneerIndexForm getPioneerIndex(int type) {
        TbcPioneerIndexForm form = new TbcPioneerIndexForm();
        List<TbcPioneerIndexForm.Van> vanList = new ArrayList<>();
        Map<Integer, String> map = new HashMap<>();
        if (type == 1) {
            map.put(0, "综合管理党员");
            map.put(1, "烟叶生成党员");
            map.put(2, "专卖管理党员");
            map.put(3, "卷烟营销党员");
        } else {
            map.put(0, "30岁以下");
            map.put(1, "30岁～45岁");
            map.put(2, "45岁～55岁");
            map.put(3, "55岁以上");
        }
        int number = 100;
        for (int i = 0; i < 4; i++) {
            TbcPioneerIndexForm.Van van = new TbcPioneerIndexForm.Van();
            van.setName(map.get(i));
            int number1 = (int) (Math.random() * number);
            number -= number1;
            van.setValue(number);
            vanList.add(van);
        }
        form.setVanValue(vanList);
        return form;
    }

    public List<TbcMapForm> getMap(Integer type) {
        List<TbcMapForm> forms = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("#.00000");
        DecimalFormat df1 = new DecimalFormat("#.000000");
        for (int i = 0; i < 40; i++) {
            TbcMapForm form = new TbcMapForm();
            form.setLng(df.format((Math.random() * (110.195637 - 105.289838 + 1) + 105.289838)));
            form.setLat(df1.format((Math.random() * (32.204171 - 28.164706 + 1) + 28.164706)));
            form.setValue((int) (Math.random() * 100));
            forms.add(form);
        }
        return forms;
    }
}
