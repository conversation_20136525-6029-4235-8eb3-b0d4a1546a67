package com.goodsogood.ows.service.tbcFusion;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.TbcMonitorsTagConfig;
import com.goodsogood.ows.mapper.sas.TbcOrgInfoMapper;
import com.goodsogood.ows.mapper.sas.TbcUserInfoMapper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.vo.sas.OrganizationForm;
import com.goodsogood.ows.model.vo.tbcFusion.TbcPartyIndexForm;
import com.goodsogood.ows.utils.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 党业拟合度
 *
 * <AUTHOR>
 * @date 2021/12/03
 */
@Service
public class TbcMatchingService {

    private final OrganizationMapper organizationMapper;
    private final TbcBasicsService tbcBasicsService;
    //排除组织id
    private final String excludeOrgIds = "5";
    private final TbcMonitorsTagConfig tbcMonitorsTagConfig;
    private final Errors errors;
    private final UserMapper userMapper;
    private final TbcOrgInfoMapper tbcOrgInfoMapper;
    private final TbcUserInfoMapper tbcUserInfoMapper;
    private final OrgTypeConfig orgTypeConfig;



    public TbcMatchingService(OrganizationMapper organizationMapper,
                              TbcBasicsService tbcBasicsService,
                              TbcMonitorsTagConfig tbcMonitorsTagConfig,
                              Errors errors, UserMapper userMapper,
                              TbcOrgInfoMapper tbcOrgInfoMapper,
                              TbcUserInfoMapper tbcUserInfoMapper,
                              OrgTypeConfig orgTypeConfig) {
        this.organizationMapper = organizationMapper;
        this.tbcBasicsService = tbcBasicsService;
        this.tbcMonitorsTagConfig = tbcMonitorsTagConfig;
        this.errors = errors;
        this.userMapper = userMapper;
        this.tbcOrgInfoMapper = tbcOrgInfoMapper;
        this.tbcUserInfoMapper = tbcUserInfoMapper;
        this.orgTypeConfig = orgTypeConfig;
    }

    /**
     * 党页拟合度(条形图)
     *
     * @param type 1:
     * @return
     */
    public List<List<String>> getTbcMatching(Integer type) {
        Integer NUMBER = 10;
        String s1 = "涪陵区县、渝中区县、开州区县、梁平区县、万州区县、江津区县、合川区县、黔江区县、永川区县、南川区县、大渡口区县、" +
                "九龙坡区县、南岸区县、北碚区县、江北区县、璧山区县、铜梁区县、沙坪坝区县、渝北区县、巴南区县、长寿区县、綦江区县、大足区县、潼南区县、荣昌区县、武隆区县";
        String[] strings = s1.split("、");
        //1
        List<List<String>> list = new ArrayList<>();
        //2
        List<String> colorTitle = new ArrayList<>();
        //3
        List<String> listTitle = new ArrayList<>();
        //4
        List<String> list1 = new ArrayList<>();

        colorTitle.add("党建指标");
        colorTitle.add("业务指标");
        colorTitle.add("堡垒指标");
        colorTitle.add("拟合度");

        for (int i = 0; i < 40; i++) {
            listTitle.add(strings[(int) (Math.random() * 25)]);
        }
        list.add(colorTitle);
        list.add(listTitle);
        DecimalFormat df = new DecimalFormat("#.00");
        DecimalFormat df1 = new DecimalFormat("#.0");

        type = type > 1 ? 2 : 1;
        switch (type) {
            case 1:
                for (int i = 0; i < 4; i++) {
                    for (int j = 0; j < NUMBER; j++) {
                        if (j == 1) {
                            list1.add(String.valueOf(df.format(Math.random() * 10)));
                            continue;
                        } else if (j == 3) {
                            list1.add(String.valueOf(df1.format(Math.random() * 100.00)));
                            continue;
                        }
                        list1.add(String.valueOf((int) (Math.random() * 100)));
                    }
                    list.add(list1);
                }
                break;
            case 2:
                for (int i = 0; i < 4; i++) {
                    for (int j = 0; j < NUMBER; j++) {
                        if (j == ((int) (Math.random() * 10))) {
                            list1.add(String.valueOf(df1.format(Math.random() * 100.00)));
                            continue;
                        }
                        list1.add(String.valueOf((int) (Math.random() * 100)));
                    }
                    list.add(list1);
                }
                break;
        }
        return list;
    }

    /**
     * 党页拟合度(条形图)
     * 按照堡垒指数排序
     * @param type 1:
     * @return
     */
    public List<List<String>> getTbcMatchingNew(int type) {
        return getFortressIndex();
    }

    /**
     * 按堡垒指排序
     * @return
     */
    private List<List<String>> getFortressIndex(){
        List<TbcPartyIndexForm> listNew = new LinkedList<>();
        TbcMonitorsTagConfig.CalPercentage tbc = tbcMonitorsTagConfig.getTbcMatching();
        List<OrganizationForm> branchOfficeInfo = organizationMapper.getBranchOfficeInfo(excludeOrgIds);
        branchOfficeInfo.forEach(item->{
            List<Long> subOrgInfo = organizationMapper.getSubOrgInfo(item.getOrganizationId());
            TbcPartyIndexForm tbcPartyIndexForm = new TbcPartyIndexForm();
            tbcPartyIndexForm.setOrgName(item.getName());
            tbcPartyIndexForm.setOrgId(item.getOrganizationId());
            tbcPartyIndexForm.setPartyIndex(0.0);
            tbcPartyIndexForm.setBusinessIndex(0.0);
            tbcPartyIndexForm.setInnovationIndex(0.0);
            tbcPartyIndexForm.setFortressIndex(0.0);
            tbcPartyIndexForm.setLikeIndex(0.0);
            if(CollectionUtils.isEmpty(subOrgInfo)){
                listNew.add(tbcPartyIndexForm);
                return;
            }
            List<TbcPartyIndexForm> partyIndex = tbcBasicsService.getPartyIndex(1, subOrgInfo);
            if(CollectionUtils.isEmpty(partyIndex)){
                listNew.add(tbcPartyIndexForm);
                return;
            }
            //如果有数据开始计算 这里计算下面的所有支部
            double size = subOrgInfo.size();
            double sumPartyIndex = partyIndex.stream().mapToDouble(TbcPartyIndexForm::getPartyIndex).sum();
            tbcPartyIndexForm.setPartyIndex( NumberUtils.divide(sumPartyIndex, size,1));
            double sumBusinessIndex = partyIndex.stream().mapToDouble(TbcPartyIndexForm::getBusinessIndex).sum();
            tbcPartyIndexForm.setBusinessIndex( NumberUtils.divide(sumBusinessIndex, size,1));
            double sumInnovationIndex = partyIndex.stream().mapToDouble(TbcPartyIndexForm::getInnovationIndex).sum();
            tbcPartyIndexForm.setInnovationIndex(NumberUtils.divide(sumInnovationIndex, size,1));
            //计算堡垒计算
            tbcPartyIndexForm.setFortressIndex(tbcPartyIndexForm.getPartyIndex()*tbc.getPartyIndexProportion()+
                    tbcPartyIndexForm.getBusinessIndex()*tbc.getBusinessIndexProportion()
                    +tbcPartyIndexForm.getInnovationIndex()*tbc.getInnovationIndexProportion());
            BigDecimal b   = BigDecimal.valueOf(tbcPartyIndexForm.getFortressIndex());
            //向上
            tbcPartyIndexForm.setFortressIndex(b.setScale( 1, RoundingMode.HALF_UP).doubleValue());
            listNew.add(tbcPartyIndexForm);
        });
        //保证数据有顺序
        List<String> lisStaIndex = new LinkedList<>();
        lisStaIndex.add("党建指标");
        lisStaIndex.add("业务指标");
        lisStaIndex.add("堡垒指数");
        lisStaIndex.add("拟合度");
        List<List<String>> listTotal = new LinkedList<>();
        //区县
        List<String> lisStaRegion = new LinkedList<>();
        //党建指标
        List<String> lisStaPartyIndex = new LinkedList<>();
        //业务指标
        List<String> lisStaBusinessIndex = new LinkedList<>();
        //堡垒指标
        List<String> lisStaFortressIndex = new LinkedList<>();
        //拟合度
        List<String> lisStaLikeIndex = new LinkedList<>();
        //对计算过后数据进行重新排序
        List<TbcPartyIndexForm> newList = listNew.stream().
                sorted(Comparator.comparing(TbcPartyIndexForm::getFortressIndex).reversed())
                .collect(Collectors.toList());
        newList.forEach(item->{
            lisStaRegion.add(item.getOrgName());
            lisStaPartyIndex.add(String.valueOf(item.getPartyIndex()));
            lisStaBusinessIndex.add(String.valueOf(item.getBusinessIndex()));
            lisStaFortressIndex.add(String.valueOf(item.getFortressIndex()));
            lisStaLikeIndex.add(String.valueOf(item.getLikeIndex()));
        });
        listTotal.add(lisStaIndex);
        listTotal.add(lisStaRegion);
        listTotal.add(lisStaPartyIndex);
        listTotal.add(lisStaBusinessIndex);
        listTotal.add(lisStaFortressIndex);
        listTotal.add(lisStaLikeIndex);
        return listTotal;
    }

}
