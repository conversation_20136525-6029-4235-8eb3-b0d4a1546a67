package com.goodsogood.ows.service.tbcFusion;

import com.goodsogood.ows.common.TbcScoreConstant;
import com.goodsogood.ows.common.redisUtil.Config;
import com.goodsogood.ows.mapper.doris.PbmScoreMapper;
import com.goodsogood.ows.mapper.tbcFusion.TbcScoreMapper;
import com.goodsogood.ows.model.mongodb.pbm.PbmUnitKitInfo;
import com.goodsogood.ows.model.mongodb.pbm.PbmUserKitInfo;
import com.goodsogood.ows.model.vo.tbcFusion.TbcPartyAndBusinessScoreVo;
import com.goodsogood.ows.model.vo.tbcFusion.TbcScoreRankBusinessVo;
import com.goodsogood.ows.model.vo.tbcFusion.TbcScoreRankPartyVo;
import com.goodsogood.ows.model.vo.tbcFusion.TbcScoreUserRankVo;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserMongoService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.NumberUtils;
import com.goodsogood.ows.utils.RedisLockUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @ClassName : TbcScoreService
 * <AUTHOR> tc
 * @Date: 2022/6/20 18:21
 * @Description : 党业融合积分中心服务类
 */
@Log4j2
@Service
public class TbcScoreService {

    private final MongoTemplate mongoTemplate;
    private final StringRedisTemplate redisTemplate;
    private final TbcScoreMapper tbcScoreMapper;
    private final UserMongoService userMongoService;
    private final OrgService orgService;
    private final PbmScoreMapper pbmScoreMapper;

    @Value("${user-option.testBranch}")
    private String testBranch;

    @Autowired
    public TbcScoreService(MongoTemplate mongoTemplate, StringRedisTemplate redisTemplate, TbcScoreMapper tbcScoreMapper, UserMongoService userMongoService, OrgService orgService, PbmScoreMapper pbmScoreMapper) {
        this.mongoTemplate = mongoTemplate;
        this.redisTemplate = redisTemplate;
        this.tbcScoreMapper = tbcScoreMapper;
        this.userMongoService = userMongoService;
        this.orgService = orgService;
        this.pbmScoreMapper = pbmScoreMapper;
    }

    /**
     * 根据年月查询/生成党建积分总排行榜
     *
     * @param regionId
     * @param orgId
     * @param yearMonth
     * @return
     */
    public List<TbcScoreUserRankVo> findPartyTotal(Long regionId, Long orgId, String yearMonth) {
        String logTxt = "根据年月查询/生成党建积分总排行榜";
        List<TbcScoreUserRankVo> result = new ArrayList<>();
        //上redis锁
        String requestId = UUID.randomUUID().toString();
        String lockKey = "SAS_SCORE_RANK_PARTY_TOTAL_LOCK_" + regionId + "_" + orgId + "_" + yearMonth;
        //获取分布式锁
        boolean lock = RedisLockUtil.tryGetDistributedLock(redisTemplate, lockKey, requestId, Config.USER_CENTER_LOCK_EXPIRE);
        if (!lock) {
            log.debug(logTxt + " regionId={} orgId={} yearMonth={} 未获得锁", regionId, orgId, yearMonth);
            return result;
        }
//        log.debug(logTxt+" 参数: regionId={} orgId={} yearMonth={}",regionId, orgId, yearMonth);
        try {
            String partyAllKey = TbcScoreConstant.RANK_PARTY_ALL + orgId + "_" + yearMonth;
            Object obj = redisTemplate.opsForValue().get(partyAllKey);
            if (obj != null) {
                result = (List<TbcScoreUserRankVo>) JsonUtils.fromJson(obj.toString(), ArrayList.class, TbcScoreUserRankVo.class);
            } else {
                //查询要排除的测试人员
                String excludeUserIds = "";
                //查询排行数据
                result = tbcScoreMapper.findUserRank(regionId, orgId, TbcScoreConstant.PARENT_SCORE_TYPE_PARTY, yearMonth, excludeUserIds);
                //生成总榜缓存数据
                redisTemplate.opsForValue().set(partyAllKey, JsonUtils.toJson(result), 32, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            log.error(logTxt + " 报错！", e);
        } finally {
            //解锁
            log.debug(logTxt + " 执行完成 释放锁");
            RedisLockUtil.releaseDistributedLock(redisTemplate, lockKey, requestId);
        }
        return result;
    }

    /**
     * 根据年月查询/生成业务积分总排行榜
     *
     * @param regionId
     * @param orgId
     * @param yearMonth
     * @return
     */
    public List<TbcScoreUserRankVo> findBusinessTotal(Long regionId, Long orgId, String yearMonth) {
        String logTxt = "根据年月查询/生成业务积分总排行榜";
        List<TbcScoreUserRankVo> result = new ArrayList<>();
        //上redis锁
        String requestId = UUID.randomUUID().toString();
        String lockKey = "SAS_SCORE_RANK_BUSINESS_TOTAL_LOCK_" + regionId + "_" + orgId + "_" + yearMonth;
        //获取分布式锁
        boolean lock = RedisLockUtil.tryGetDistributedLock(redisTemplate, lockKey, requestId, Config.USER_CENTER_LOCK_EXPIRE);
        if (!lock) {
            log.debug(logTxt + " regionId={} orgId={} yearMonth={} 未获得锁", regionId, orgId, yearMonth);
            return result;
        }
//        log.debug(logTxt+" 参数: regionId={} orgId={} yearMonth={}",regionId, orgId, yearMonth);
        try {
            String businessAllKey = TbcScoreConstant.RANK_BUSINESS_ALL + orgId + "_" + yearMonth;
            Object obj = redisTemplate.opsForValue().get(businessAllKey);
            if (obj != null) {
                result = (List<TbcScoreUserRankVo>) JsonUtils.fromJson(obj.toString(), ArrayList.class, TbcScoreUserRankVo.class);
            } else {
                //查询要排除的测试人员
                String excludeUserIds = "";
                //查询排行数据
                result = tbcScoreMapper.findUserRank(regionId, orgId, TbcScoreConstant.PARENT_SCORE_TYPE_BUSINESS, yearMonth, excludeUserIds);
                //生成总榜缓存数据
                redisTemplate.opsForValue().set(businessAllKey, JsonUtils.toJson(result), 32, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            log.error(logTxt + " 报错！", e);
        } finally {
            //解锁
            log.debug(logTxt + " 执行完成 释放锁");
            RedisLockUtil.releaseDistributedLock(redisTemplate, lockKey, requestId);
        }
        return result;
    }

    /**
     * 根据年月生成党建积分排行榜
     */
    public Map<Long, TbcScoreRankPartyVo> createPartyRank(Long regionId, Long orgId, String yearMonth) {

        Map<Long, TbcScoreRankPartyVo> resultMap = new HashMap<>();
        //上redis锁
        String requestId = UUID.randomUUID().toString();
        String lockKey = "SAS_SCORE_RANK_PARTY_LOCK_" + regionId + "_" + orgId + "_" + yearMonth;
        //获取分布式锁
        boolean lock = RedisLockUtil.tryGetDistributedLock(redisTemplate, lockKey, requestId, Config.USER_CENTER_LOCK_EXPIRE);
        if (!lock) {
            log.debug("根据年月生成党建积分排行榜 regionId={} orgId={} yearMonth={} 未获得锁", regionId, orgId, yearMonth);
            return resultMap;
        }
//        log.debug("根据年月生成党建积分排行榜 参数: regionId={} orgId={} yearMonth={}",regionId, orgId, yearMonth);
        try {
            //查询排行数据
            List<TbcScoreUserRankVo> list = this.findPartyTotal(regionId, orgId, yearMonth);
            //生成用户独立的缓存数据
            Map<Long, TbcScoreUserRankVo> userScoreMap = list.stream().collect(Collectors.toMap(TbcScoreUserRankVo::getUserId, userScore -> userScore, (key1, key2) -> key2));
            //批量查询用户序列信息
            //获取用户序列信息
            List<PbmUserKitInfo> ubList = userMongoService.getUserKitByIdList(yearMonth, userScoreMap.keySet());
            //生成用户序列独立的缓存数据
            Map<Long, PbmUserKitInfo> ubMap = ubList.stream().collect(Collectors.toMap(PbmUserKitInfo::getUserId, ub -> ub, (key1, key2) -> key2));
            //计算全市系统党员中序列
            List<Long> partyAllUser = userMongoService.getUserIdList(regionId, null, 1);
//            log.debug("根据年月生成党建积分排行榜 计算全市系统党员中序列: partyAllUser.size={} partyAllUser={}",partyAllUser.size(),partyAllUser);
            //计算全市系统党员中排名
            this.rankByScope(resultMap, null, TbcScoreConstant.RESULT_TYPE_PARTY, userScoreMap, partyAllUser, TbcScoreConstant.SERIAL_TYPE_PARTY_ALL);

            //缓存已查询过的序列,查询过的序列会一次性处理序列里所有的人员，所以每个序列只用处理一次
            Map<String, Integer> queryCache = new HashMap<>(4);
            for (Long userId : userScoreMap.keySet()) {
                //获取用户信息
                PbmUserKitInfo ub = ubMap.get(userId);
                if (ub != null) {
                    if (ub.getUnitId() != null) {
                        String q1 = regionId + "_" + ub.getUnitId() + "_" + 1 + "_" + 0;
                        if (queryCache.get(q1) == null) {
                            List<Long> partyUnitUser = userMongoService.getUserIdList(regionId, ub.getUnitId(), 1, 0);
//                        log.debug("根据年月生成党建积分排行榜 计算本单位党员中序列:userId={} partyUnitUser.size={} partyUnitUser={} ",userId,partyUnitUser.size(),partyUnitUser);
                            //计算本单位党员中排名
                            this.rankByScope(resultMap, null, TbcScoreConstant.RESULT_TYPE_PARTY, userScoreMap, partyUnitUser, TbcScoreConstant.SERIAL_TYPE_PARTY_UNIT);
                            queryCache.put(q1, 1);
                        }
                    }
                    if (ub.getSequence() != null && ub.getSequence() != 0) {
                        String q2 = regionId + "_null_" + 1 + "_" + ub.getSequence();
                        if (queryCache.get(q2) == null) {
                            //计算用户所在序列党员中序列
                            List<Long> partyMarketingUser = userMongoService.getUserIdList(regionId, null, 1, ub.getSequence());
//                        log.debug("根据年月生成党建积分排行榜 计算用户所在序列党员中序列: userId={} Sequence ={} partyMarketingUser.size={} partyMarketingUser={}",
//                                userId,ub.getSequence(),partyMarketingUser.size(),partyMarketingUser);
                            //计算用户所在序列党员中排名
                            this.rankByScope(resultMap, null, TbcScoreConstant.RESULT_TYPE_PARTY, userScoreMap, partyMarketingUser, TbcScoreConstant.SERIAL_TYPE_PARTY_MARKETING);
                            queryCache.put(q2, 1);
                        }
                    } else {
                        log.debug("<根据年月生成党建积分排行榜> 用户没有所属序列！ub={}", ub);
                    }
                } else {
                    log.debug("<根据年月生成党建积分排行榜> 获取用户信息 失败！userId={}", userId);
                }
            }
//            log.debug("根据年月生成党建积分排行榜 resultMap={}",resultMap);
            //写入用户独立的缓存数据
            Map<String, String> redisMap = new HashMap<>();
            resultMap.forEach((k, v) -> {
                redisMap.put(k.toString(), JsonUtils.toJson(v));
            });
//            log.debug("根据年月生成党建积分排行榜 redisMap={}",redisMap);
            redisTemplate.opsForHash().putAll(TbcScoreConstant.RANK_PARTY_USER + orgId + "_" + yearMonth, redisMap);
            redisTemplate.expire(TbcScoreConstant.RANK_PARTY_USER + orgId + "_" + yearMonth, 32, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("根据年月生成党建积分排行榜报错!", e);
        } finally {
            //解锁
            log.debug("根据年月生成党建积分排行榜 执行完成 释放锁");
            RedisLockUtil.releaseDistributedLock(redisTemplate, lockKey, requestId);
        }
        return resultMap;
    }

    /**
     * 根据年月生成业务积分排行榜
     */
    public Map<Long, TbcScoreRankBusinessVo> createBusinessRank(Long regionId, Long orgId, String yearMonth) {
        Map<Long, TbcScoreRankBusinessVo> resultMap = new HashMap<>();
        //上redis锁
        String requestId = UUID.randomUUID().toString();
        String lockKey = "SAS_SCORE_RANK_BUSINESS_LOCK_" + regionId + "_" + orgId + "_" + yearMonth;
        //获取分布式锁
        boolean lock = RedisLockUtil.tryGetDistributedLock(redisTemplate, lockKey, requestId, Config.USER_CENTER_LOCK_EXPIRE);
        if (!lock) {
            log.debug("根据年月生成业务积分排行榜 regionId={} orgId={} yearMonth={} 未获得锁", regionId, orgId, yearMonth);
            return resultMap;
        }
//        log.debug("根据年月生成业务积分排行榜 参数: regionId={} orgId={} yearMonth={}",regionId, orgId, yearMonth);
        try {
            //查询排行数据
            List<TbcScoreUserRankVo> list = this.findBusinessTotal(regionId, orgId, yearMonth);
            //生成用户独立的缓存数据
            Map<Long, TbcScoreUserRankVo> userScoreMap = list.stream().collect(Collectors.toMap(TbcScoreUserRankVo::getUserId, userScore -> userScore, (key1, key2) -> key2));
            //获取用户序列信息
            List<PbmUserKitInfo> ubList = userMongoService.getUserKitByIdList(yearMonth, userScoreMap.keySet());
            //生成用户序列独立的缓存数据
            Map<Long, PbmUserKitInfo> ubMap = ubList.stream().collect(Collectors.toMap(PbmUserKitInfo::getUserId, ub -> ub, (key1, key2) -> key2));
            //缓存已查询过的序列,查询过的序列会一次性处理序列里所有的人员，所以每个序列只用处理一次
            Map<String, Integer> queryCache = new HashMap<>(4);
            for (Long userId : userScoreMap.keySet()) {
                //获取用户信息
                PbmUserKitInfo ub = ubMap.get(userId);
//                log.debug("根据年月生成业务积分排行榜 获取用户信息: userId={} regionId={} 返回 ub={}",userId,regionId,ub);
                if (ub != null) {
                    if (ub.getSequence() != null && ub.getSequence() != 0) {
                        if (ub.getUnitId() != null) {
                            String q1 = regionId + "_" + ub.getUnitId() + "_" + 2 + "_" + ub.getSequence();
                            if (queryCache.get(q1) == null) {
                                //计算本单位用户所属序列员工
                                List<Long> businessUnitUser = userMongoService.getUserIdList(regionId, ub.getUnitId(), 2, ub.getSequence());
                                //计算本单位用户所属序列员工中排名
                                this.rankByScope(null, resultMap, TbcScoreConstant.RESULT_TYPE_BUSINESS, userScoreMap, businessUnitUser, TbcScoreConstant.SERIAL_TYPE_BUSINESS_UNIT);
                                queryCache.put(q1, 1);
                            }
                        }
                        String q2 = regionId + "_null_" + 2 + "_" + ub.getSequence();
                        if (queryCache.get(q2) == null) {
                            //计算全市系统用户所属序列员工
                            List<Long> businessMarketingUser = userMongoService.getUserIdList(regionId, null, 2, ub.getSequence());
                            //计算全市系统用户所属序列员工中排名
                            this.rankByScope(null, resultMap, TbcScoreConstant.RESULT_TYPE_BUSINESS, userScoreMap, businessMarketingUser, TbcScoreConstant.SERIAL_TYPE_BUSINESS_MARKETING);
                            queryCache.put(q2, 1);
                        }
                        String q3 = regionId + "_null_" + 1 + "_" + ub.getSequence();
                        if (queryCache.get(q3) == null) {
                            //计算全市系统用户所属序列党员中
                            List<Long> businessAllUser = userMongoService.getUserIdList(regionId, null, 1, ub.getSequence());
                            //计算全市系统用户所属序列党员中排名
                            this.rankByScope(null, resultMap, TbcScoreConstant.RESULT_TYPE_BUSINESS, userScoreMap, businessAllUser, TbcScoreConstant.SERIAL_TYPE_BUSINESS_ALL);
                            queryCache.put(q3, 1);
                        }
                    } else {
                        log.debug("<根据年月生成业务积分排行榜> 用户没有所属序列! ub={}", ub);
                    }
                } else {
                    log.debug("<根据年月生成业务积分排行榜> 获取用户信息失败! userId={}", userId);
                }
            }
//            log.debug("根据年月生成业务积分排行榜 resultMap={}",resultMap);
            //写入用户独立的缓存数据
            Map<String, String> redisMap = new HashMap<>();
            resultMap.forEach((k, v) -> {
                redisMap.put(k.toString(), JsonUtils.toJson(v));
            });
//            log.debug("根据年月生成业务积分排行榜 redisMap={}",redisMap);
            redisTemplate.opsForHash().putAll(TbcScoreConstant.RANK_BUSINESS_USER + orgId + "_" + yearMonth, redisMap);
            redisTemplate.expire(TbcScoreConstant.RANK_BUSINESS_USER + orgId + "_" + yearMonth, 32, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("根据年月生成业务积分排行榜报错!", e);
        } finally {
            //解锁
            log.debug("根据年月生成业务积分排行榜 执行完成 释放锁");
            RedisLockUtil.releaseDistributedLock(redisTemplate, lockKey, requestId);
        }
        return resultMap;
    }

    /**
     * 查询个人党建积分
     *
     * @param orgId
     * @param userId
     * @return
     */
    public TbcScoreRankPartyVo findScoreRankParty(Long regionId, Long orgId, Long userId) {
        log.debug("查询个人上月党建积分 参数: regionId={} orgId={} userId={}", regionId, orgId, userId);
        TbcScoreRankPartyVo re = new TbcScoreRankPartyVo();
        try {
            //上一个月
            String yearMonth = DateUtils.getDateByMonths(-1, "yyyy-MM", null);
            //查询缓存
            String redisKey = TbcScoreConstant.RANK_PARTY_USER + orgId + "_" + yearMonth;
            if (redisTemplate.hasKey(redisKey)) {
                Object obj = redisTemplate.opsForHash().get(redisKey, userId.toString());
                if (obj != null) {
                    re = JsonUtils.fromJson(obj.toString(), TbcScoreRankPartyVo.class);
                }
            } else {
                //生成缓存
                Map<Long, TbcScoreRankPartyVo> resultMap = this.createPartyRank(regionId, orgId, yearMonth);
                re = resultMap.get(userId);
            }
        } catch (Exception e) {
            log.error("查询个人上月党建积分 报错！", e);
        }
        //返回结果
        return re;
    }

    /**
     * 查询个人业务积分
     *
     * @param orgId
     * @param userId
     * @return
     */
    public TbcScoreRankBusinessVo findScoreRankBusiness(Long regionId, Long orgId, Long userId) {
        log.debug("查询个人上月业务积分 参数: regionId={} orgId={} userId={}", regionId, orgId, userId);
        TbcScoreRankBusinessVo re = new TbcScoreRankBusinessVo();
        try {
            //当前年月
            String yearMonth = DateUtils.getDateByMonths(-1, "yyyy-MM", null);
            //查询缓存
            String redisKey = TbcScoreConstant.RANK_BUSINESS_USER + orgId + "_" + yearMonth;
            if (redisTemplate.hasKey(redisKey)) {
                Object obj = redisTemplate.opsForHash().get(redisKey, userId.toString());
                if (obj != null) {
                    re = JsonUtils.fromJson(obj.toString(), TbcScoreRankBusinessVo.class);
                }
            } else {
                //生成缓存
                Map<Long, TbcScoreRankBusinessVo> resultMap = this.createBusinessRank(regionId, orgId, yearMonth);
                re = resultMap.get(userId);
            }
        } catch (Exception e) {
            log.error("查询个人上月业务积分 报错！", e);
        }

        //返回结果
        return re;
    }

    /**
     * 查询人员党建积分和业务积分
     */
    public Map<Long, TbcPartyAndBusinessScoreVo> findUserPartyAndBusiness(Long regionId, List<Long> userIdList, String startTime, String endTime) {
//        List<TbcPartyAndBusinessScoreVo> re = tbcScoreMapper.findUserPartyAndBusinessScore(userIdList, startTime, endTime);
        Integer startMonth = Integer.valueOf(startTime.substring(0,4)+startTime.substring(5,7));
        Integer endMonth = Integer.valueOf(endTime.substring(0,4)+endTime.substring(5,7));
        List<TbcPartyAndBusinessScoreVo> re = pbmScoreMapper.findUserPartyAndBusinessScore(userIdList, startMonth, endMonth);
        Map<Long, TbcPartyAndBusinessScoreVo> reMap = new HashMap<>();
        re.forEach(r -> {
            reMap.put(r.getUserId(), r);
        });
        userIdList.forEach(userId -> {
            if (!reMap.containsKey(userId)) {
                TbcPartyAndBusinessScoreVo tmp = new TbcPartyAndBusinessScoreVo();
                tmp.setBusinessScore(0L);
                tmp.setPartyScore(0L);
                tmp.setUserId(userId);
                reMap.put(userId, tmp);
            }
        });

        return reMap;
    }

    /**
     * 查询单位党建积分和业务积分
     * 党建积分=单位对应的党组党建积分+与单位关联的最高级党组织党建积分；
     * 业务积分=与单位关联的最高级党组织业务积分；
     */
    public Map<Long, TbcPartyAndBusinessScoreVo> findUnitPartyAndBusiness(Long regionId, List<Long> unitIdList, String startTime, String endTime) {
        Map<Long, List<Long>> topOrgMap = orgService.getTopOrgIdByUnitIdsUseSql(unitIdList, regionId);
//        log.debug("测试日志:根据单位ID获取最高级组织ID unitIdList={} 结果 topOrgMap={}",unitIdList,topOrgMap);
        Map<Long, Long> groupMap = orgService.getPartyGroupIdByUnitIds(unitIdList);
//        log.debug("测试日志:批量根据单位ID获取党组ID unitIdList={} 结果 groupMap={}",unitIdList,groupMap);
        //反转一下单位和组织/党组的Map集合
        //key 顶级组织编号，value 单位编号
        Map<Long, Long> topOrgUnitMap = new HashMap<>(topOrgMap.size());
        //key 党组编号，value 单位编号
        Map<Long, Long> groupUnitMap = new HashMap<>(groupMap.size());
        //用于单位下累加的组织数量计数  key 单位编号，value 包含的顶级组织数量
        Map<Long, Long> countMap = new HashMap<>();
        topOrgMap.forEach((k, v) -> {
            v.forEach(vv -> {
                topOrgUnitMap.put(vv, k);
            });
            //如果单位存在多个顶级组织，记录一下
            if (v.size() > 1) {
                countMap.put(k, Long.valueOf(v.size()));
            }
        });
        groupMap.forEach((k, v) -> {
            groupUnitMap.put(v, k);
        });
        //查询最高级党组织积分
        List<Long> topOrgList = new ArrayList<>();
        topOrgMap.values().stream().forEach(v -> v.stream().forEach(vv -> {
            topOrgList.add(vv);
        }));
//        log.debug("测试日志:查询单位党建/业务积分 topOrgList={}",topOrgList);
        Integer startMonth = Integer.valueOf(startTime.substring(0,4)+startTime.substring(5,7));
        Integer endMonth = Integer.valueOf(endTime.substring(0,4)+endTime.substring(5,7));
        List<TbcPartyAndBusinessScoreVo> re1 = pbmScoreMapper.findOrgPartyAndBusinessScore(TbcScoreConstant.SCORE_ORG_TYPE_ORG, topOrgList, startMonth,endMonth, testBranch);
//        List<TbcPartyAndBusinessScoreVo> re1 = tbcScoreMapper.findOrgPartyAndBusinessScore(TbcScoreConstant.SCORE_ORG_TYPE_ORG, topOrgList, startTime, endTime, testBranch);
//        log.debug("测试日志:查询单位党建/业务积分 结果 re1={}",re1);
        Map<Long, TbcPartyAndBusinessScoreVo> reMap1 = new HashMap<>();

        for (TbcPartyAndBusinessScoreVo re : re1) {
            Long unitId = topOrgUnitMap.get(re.getOrgId());
            if (reMap1.containsKey(unitId)) {
                //累加积分
                TbcPartyAndBusinessScoreVo tmp = reMap1.get(unitId);
                Long ps = NumberUtils.addLong(re.getPartyScore(), tmp.getPartyScore());
                re.setPartyScore(ps);
            }
            reMap1.put(unitId, re);
        }
//        log.debug("测试日志: reMap1={}  countMap={}",reMap1,countMap);
        //单位下包含多个顶级组织的党建积分计算为平均值
        countMap.forEach((k, v) -> {
            TbcPartyAndBusinessScoreVo tmp = reMap1.get(k);
            if (null != tmp) {
                tmp.setPartyScore(NumberUtils.divideLong(tmp.getPartyScore(), v));
                tmp.setBusinessScore(NumberUtils.divideLong(tmp.getBusinessScore(), v));
                reMap1.put(k, tmp);
            }
        });
        //查询对应党组积分
        List<Long> groupList = groupMap.values().stream().collect(Collectors.toList());
//        log.debug("测试日志:查询党组党建/业务积分 groupList={}",groupList);
//        List<TbcPartyAndBusinessScoreVo> re2 = tbcScoreMapper.findOrgPartyAndBusinessScore(TbcScoreConstant.SCORE_ORG_TYPE_GROUP, groupList, startTime, endTime, null);
//        log.debug("测试日志:查询党组党建/业务积分 结果 re2={}",re2);
        List<TbcPartyAndBusinessScoreVo> re2 = pbmScoreMapper.findOrgPartyAndBusinessScore(TbcScoreConstant.SCORE_ORG_TYPE_GROUP, groupList, startMonth, endMonth,null);
        Map<Long, TbcPartyAndBusinessScoreVo> reMap2 = new HashMap<>();
        re2.forEach(re -> {
            reMap2.put(groupUnitMap.get(re.getOrgId()), re);
        });
//        log.debug("测试日志: reMap2={} ",reMap2);
        //组合返回结果
        Map<Long, TbcPartyAndBusinessScoreVo> re = new HashMap<>(unitIdList.size());
        unitIdList.forEach(uid -> {
            TbcPartyAndBusinessScoreVo tabsv = re.get(uid);
            if (tabsv == null) {
                tabsv = new TbcPartyAndBusinessScoreVo();
                tabsv.setOrgId(uid);
            }
            //计算党建积分
            Long topOrgPartyScore = reMap1.get(uid) != null ? reMap1.get(uid).getPartyScore() : 0L;
            Long groupPartyScore = reMap2.get(uid) != null ? reMap2.get(uid).getPartyScore() : 0L;
            tabsv.setPartyScore(NumberUtils.addLong(topOrgPartyScore, groupPartyScore));
            //计算业务积分
            Long groupBusinessScore = reMap1.get(uid) != null ? reMap1.get(uid).getBusinessScore() : 0L;
            tabsv.setBusinessScore(groupBusinessScore);
            re.put(uid, tabsv);
        });
        return re;
    }

    /**
     * 查询单位党建积分和业务积分以及排名
     */
    public TbcPartyAndBusinessScoreVo findUnitScoreAndRank(Long regionId, Long unitId) {
        TbcPartyAndBusinessScoreVo re = new TbcPartyAndBusinessScoreVo();
        try {
            //当前年月
            String yearMonth = DateUtils.getDateByMonths(-1, "yyyy-MM", null);
            //查询所有单位年度积分
            Criteria criteria = Criteria.where("regionId").is(regionId).and("date").is(yearMonth);
            Query query = new Query(criteria);
            List<PbmUnitKitInfo> infoList = this.mongoTemplate.find(query, PbmUnitKitInfo.class);
            Long lastScore = null;
            Integer lastRank = 1;
            Integer rank = 1;
            //党建积分排序,按照同分同排名，后面占位递增 例如1,1,3,4,5,5,5,8,9,10
            infoList = infoList.stream().sorted(Comparator.comparing(PbmUnitKitInfo::getPartyBuildTotal).reversed()).collect(Collectors.toList());
            for (PbmUnitKitInfo p : infoList) {
                if (lastScore == null || !lastScore.equals(p.getPartyBuildTotal())) {
                    lastRank = rank;
                }
                lastScore = p.getPartyBuildTotal();
                rank++;
                if (unitId.equals(p.getUnitId())) {
                    //需要返回的信息
                    re.setOrgId(p.getUnitId());
                    re.setPartyScore(p.getPartyBuildTotal());
                    re.setBusinessScore(p.getBusinessTotal());
                    //党建积分的排名
                    re.setPartyRank(lastRank);
                    re.setCount(infoList.size());
                }
            }

            //业务积分排序,按照同分同排名，后面占位递增 例如1,1,3,4,5,5,5,8,9,10
            infoList = infoList.stream().sorted(Comparator.comparing(PbmUnitKitInfo::getBusinessTotal).reversed()).collect(Collectors.toList());
            //重置
            lastScore = null;
            lastRank = 1;
            rank = 1;
            for (PbmUnitKitInfo p : infoList) {
                if (lastScore == null || !lastScore.equals(p.getBusinessTotal())) {
                    lastRank = rank;
                }
                lastScore = p.getBusinessTotal();
                rank++;
                if (unitId.equals(p.getUnitId())) {
                    //业务积分的排名
                    re.setBusinessRank(lastRank);
                }
            }
        } catch (Exception e) {
            log.error("查询单位党建积分和业务积分以及排名报错！", e);
        }
        return re;
    }

    /**
     * 根据指定的用户范围返回排序结果
     *
     * @param partyResultMap    党建积分结果集合
     * @param businessResultMap 业务积分结果集合
     * @param resultType        结果类型  party或者business
     * @param userScoreMap      总榜Map集合
     * @param serialList        排序序列的用户编号集合(排序的用户范围)
     * @param serialType        序列类型:  party_unit 党建积分本单位党员   party_marketing 党建积分卷烟营销党员 party_all 党建积分全市党员
     *                          business_unit 业务积分本单位营销线员工   business_marketing 业务积分全市营销线员工 business_all 业务积分全市营销线党员
     * @throws Exception
     */
    private void rankByScope(Map<Long, TbcScoreRankPartyVo> partyResultMap, Map<Long, TbcScoreRankBusinessVo> businessResultMap,
                             String resultType, Map<Long, TbcScoreUserRankVo> userScoreMap, List<Long> serialList, String serialType) throws Exception {
        //排序的临时集合
        List<TbcScoreUserRankVo> tmp = new ArrayList<>();
        //验证是否序列中存在用户
        for (Long ui : serialList) {
            TbcScoreUserRankVo tsurv = userScoreMap.get(ui);
            if (tsurv != null) {
                tmp.add(tsurv);
            } else {
                TbcScoreUserRankVo t = new TbcScoreUserRankVo();
                //如果序列里的人没有积分记录，则补充记录
                t.setUserId(ui);
                t.setScore(0L);
                tmp.add(t);
            }
        }
        //根据临时集合排序
        if (tmp.size() > 1) {
            List<TbcScoreUserRankVo> list = tmp.stream().sorted(Comparator.comparing(TbcScoreUserRankVo::getScore).reversed().thenComparing(TbcScoreUserRankVo::getUserId)).collect(Collectors.toList());
            //根据排序顺序设置排名
            AtomicInteger rankNum = new AtomicInteger(1);
            for (TbcScoreUserRankVo l : list) {
                Integer rk = rankNum.getAndIncrement();
                if (TbcScoreConstant.RESULT_TYPE_PARTY.equals(resultType)) {
                    //党建积分
                    TbcScoreRankPartyVo vo = partyResultMap.get(l.getUserId());
                    if (vo == null) {
                        vo = new TbcScoreRankPartyVo();
                        vo.setUserId(l.getUserId());
                        vo.setPartyScore(l.getScore());
                    }
                    //根据序列类型填充对应的排名
                    if (TbcScoreConstant.SERIAL_TYPE_PARTY_UNIT.equals(serialType)) {
                        //党建积分本单位党员
                        vo.setUnitNum(serialList.size());
                        vo.setUnitRank(rk);
                    } else if (TbcScoreConstant.SERIAL_TYPE_PARTY_MARKETING.equals(serialType)) {
                        //党建积分卷烟营销党员
                        vo.setMarketingNum(serialList.size());
                        vo.setMarketingRank(rk);
                    } else if (TbcScoreConstant.SERIAL_TYPE_PARTY_ALL.equals(serialType)) {
                        //党建积分全市党员
                        vo.setAllNum(serialList.size());
                        vo.setAllRank(rk);
                    }
                    partyResultMap.put(l.getUserId(), vo);
                } else if (TbcScoreConstant.RESULT_TYPE_BUSINESS.equals(resultType)) {
                    //业务积分
                    TbcScoreRankBusinessVo vo = businessResultMap.get(l.getUserId());
                    if (vo == null) {
                        vo = new TbcScoreRankBusinessVo();
                        vo.setUserId(l.getUserId());
                        vo.setBusinessScore(l.getScore());
                    }
                    //根据序列类型填充对应的排名
                    if (TbcScoreConstant.SERIAL_TYPE_BUSINESS_UNIT.equals(serialType)) {
                        //业务积分本单位营销线员工
                        vo.setUnitNum(serialList.size());
                        vo.setUnitRank(rk);
                    } else if (TbcScoreConstant.SERIAL_TYPE_BUSINESS_MARKETING.equals(serialType)) {
                        //业务积分全市营销线党员
                        vo.setMarketingNum(serialList.size());
                        vo.setMarketingRank(rk);
                    } else if (TbcScoreConstant.SERIAL_TYPE_BUSINESS_ALL.equals(serialType)) {
                        //业务积分全市营销线党员
                        vo.setAllNum(serialList.size());
                        vo.setAllRank(rk);
                    }
                    businessResultMap.put(l.getUserId(), vo);
                }
            }
        }
    }
}
