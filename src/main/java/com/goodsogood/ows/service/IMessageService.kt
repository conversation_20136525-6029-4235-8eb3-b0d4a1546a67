package com.goodsogood.ows.service

import com.goodsogood.ows.model.vo.MessageVO

/**
 * 党内资讯: 倒序排列，从最新的新闻开始
 * 其他穿插项在党内资讯里每5条资讯穿插一条其他的(党建品牌/党建阵地、线上互动、云区动态、支部风采)
 */
interface IMessageService {

    /**
     * 需要实现此接口，统一入参和出参
     * @param regionId  区县ID
     * @param rootId    顶级组织ID
     * @param page      页码
     * @param pageSize  一页大小
     */
    fun generateMessage(regionId: Long, rootId: Long, page: Int? = 1 , pageSize : Int? = 5) : MutableList<MessageVO>
}