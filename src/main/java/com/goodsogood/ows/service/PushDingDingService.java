package com.goodsogood.ows.service;

import com.goodsogood.ows.common.DingDingMessage;
import com.goodsogood.ows.common.DingDingConstant;
import com.goodsogood.ows.common.HttpClientUtil;
import com.goodsogood.ows.configuration.DingDingProperty;
import com.goodsogood.ows.utils.JSONUtil;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * 推送消息到钉钉的服务
 * <AUTHOR>
 * @date 2019-11-21 5:30 下午
 * @since 1.6.0
 **/
@Service
@Log4j2
public class PushDingDingService {

     private final DingDingProperty dingdingProperty;
     private final RestTemplate restTemplate;
     private final StringRedisTemplate redisTemplate;

     @Autowired
     public PushDingDingService(DingDingProperty dingdingProperty, RestTemplate restTemplate, StringRedisTemplate redisTemplate) {
          this.dingdingProperty = dingdingProperty;
          this.restTemplate = restTemplate;
          this.redisTemplate = redisTemplate;
     }

     /**
      * 添加消息
      * 追加消息到redis中，然后通过定时任务把这些消息推送到钉钉
      *
      * reides的存储格式map -> key为每个区县自己的名称
      *                       field 需要推送的消息的id
      *                       value 需要推送的消息的value
      * @param dingDingMessage     钉钉消息
      */
     public void addMessage(DingDingMessage dingDingMessage) {
          try {
               //防止在同一个trackerId下有多次错误的发生
               int random = new Random().nextInt(99999999);
               String trackerId = dingDingMessage.getTrackId() + "_" + random;
               String json = JsonUtils.toJson(dingDingMessage);

               this.redisTemplate.opsForHash().put(this.dingdingProperty.getServer(), trackerId, json);
          } catch (Exception e) {
               log.error("存放消息到redis出错：" + e.getMessage(), e);
          }
     }

     /**
      * 推送消息
      *
      * 由各个定时任务入口进来
      * @param server    局点
      */
     @Async(value = "dingDingExecutor")
     public void push(String server){
          log.debug("推送错误消息 server = {}", server);
          try {
               if(StringUtils.isEmpty(server)) {
                    log.error("推送错误消息，server为空，退出！！！");
                    return;
               }
               Set errorSet = this.redisTemplate.opsForHash().keys(server);
               Iterator it = errorSet.iterator();
               while (it.hasNext()) {
                    String field = String.valueOf(it.next());
                    Object val = this.redisTemplate.opsForHash().get(server, field);
                    if(val != null) {
                         String errorMsg = String.valueOf(val);
                         boolean flag = this.push(server, errorMsg);
                         //删除已经推送了的消息
                         if(flag) {
                              this.redisTemplate.opsForHash().delete(server, field);
                         }
                    }
               }
          } catch (Exception e) {
               log.error("获取错误消息出错：" + e.getMessage(), e);
          }

     }

     /**
      * 推送消息到钉钉
      *
      * @param source  来源报错的应用
      * @param message 推送消息
      */
     private boolean push(String source, String message) {
          // 获得推送的url地址
          String url = "https://oapi.dingtalk.com/robot/send?access_token=" + dingdingProperty.getAccessToken();
          // 获得需要推送人的数据
          StringBuilder users = new StringBuilder();
          StringBuilder userList = new StringBuilder();
          for (String user : dingdingProperty.getAtUsers()) {
               users.append("@").append(user).append(" ");
               userList.append("\"").append(user).append("\",");
          }
          log.debug("model->\n{}", dingdingProperty.getPushModel());
          DingDingMessage dingDingMessage = JsonUtils.fromJson(message, DingDingMessage.class);
          String content = dingDingMessage.getContent().replaceAll("\"","");
          String json = dingdingProperty.getPushModel()
                  .replaceAll(DingDingConstant.KEY_SOURCE, source)
                  .replaceAll(DingDingConstant.KEY_TRACK_ID, dingDingMessage.getTrackId())
                  .replaceAll(DingDingConstant.KEY_TIME, dingDingMessage.getTime())
                  .replaceAll(DingDingConstant.KEY_MSG, content);
          log.debug("json->\n{}", json);
          HttpHeaders headers = new HttpHeaders();
          MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
          headers.setContentType(type);
          HttpEntity<String> entity = new HttpEntity<>(json, headers);
          String result = restTemplate.postForObject(url, entity, String.class);

          try {
               Map rsMap = JsonUtils.fromJson(result, Map.class);
               int code = Integer.valueOf(String.valueOf(rsMap.get("errcode")));
               return code == 0;
          } catch (Exception e) {
               log.error("推送钉钉消息后，处理返回消息出错：" + e.getMessage(), e);
          }
          log.debug("推送钉钉消息反馈->{}", result);
          return false;
     }

     /**
      * 推送消息到钉钉
      *
      * @param message 推送消息
      */
     @Async("dingDingExecutor")
     public void push(DingDingMessage message) {
          // 获得推送的url地址
          String url = "https://oapi.dingtalk.com/robot/send?access_token=" + dingdingProperty.getAccessToken();
          // 获得需要推送人的数据
          StringBuilder users = new StringBuilder();
          StringBuilder userList = new StringBuilder();
          for (String user : dingdingProperty.getAtUsers()) {
               users.append("@").append(user).append(" ");
               userList.append("\"").append(user).append("\",");
          }
          log.debug("model->\n{}", dingdingProperty.getPushModel());
          String content = message.getContent().replaceAll("\"","");
          String json = dingdingProperty.getPushModel()
                  .replaceAll(DingDingConstant.KEY_SOURCE, dingdingProperty.getServer())
                  .replaceAll(DingDingConstant.KEY_TRACK_ID, message.getTrackId())
                  .replaceAll(DingDingConstant.KEY_TIME, message.getTime())
                  .replaceAll(DingDingConstant.KEY_MSG, content);
          log.debug("json->\n{}", json);
          String result = null;
          try {
               Map<String, String> headers = new HashMap<>();
               headers.put(HttpHeaders.CONTENT_TYPE, "application/json; charset=UTF-8");
               Map params = JsonUtils.fromJson(json, Map.class);
               HttpClientUtil.doPost(url, headers, params);
          } catch (Exception e) {
               log.error("推送钉钉消息后，处理返回消息出错：" + e.getMessage(), e);
          }
          log.debug("推送钉钉消息反馈->{}", result);
     }
}
