package com.goodsogood.ows.service.overview;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.Init;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.LocalDateTimeUtils;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.mapper.activity.PartyAffairsMapper;
import com.goodsogood.ows.mapper.activity.RevisitMapper;
import com.goodsogood.ows.mapper.ecp.EcpOrgMapper;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.mapper.meeting.StatisticsMapper;
import com.goodsogood.ows.mapper.ppmd.PpmdStatisticsMapper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.db.user.OrgGroupVo;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.PartyBrandBase;
import com.goodsogood.ows.model.mongodb.PartyPositions;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.activity.PartyAffairs;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.overview.*;
import com.goodsogood.ows.model.vo.tbc.TbcBaseVo;
import com.goodsogood.ows.service.rank.RestTemplateUserService;
import com.goodsogood.ows.service.sas.OpenService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.ExcelUtils;
import com.goodsogood.ows.utils.NumberUtils;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 组织概况与组织生活概况
 */
@Service
@Log4j2
public class OverviewService {


    private final SimpleApplicationConfigHelper applicationConfigHelper;
    private final OrgTypeConfig orgTypeConfig;
    private final OrgService orgService;
    private final UserMapper userMapper;
    private final PartyAffairsMapper partyAffairsMapper;
    private final OrganizationMapper organizationMapper;
    private final Errors errors;
    private final MeetingMapper meetingMapper;
    private final PpmdStatisticsMapper ppmdStatisticsMapper;
    private final StatisticsMapper statisticsMapper;
    private final RestTemplateUserService restTemplateUserService;
    private final RevisitMapper revisitMapper;
    private final OpenService openService;

    private final MyMongoTemplate mongoTemplate;

    private final StringRedisTemplate stringRedisTemplate;

    private final EcpOrgMapper ecpOrgMapper;

    @Value("${overview.exclude-org-ids}")
    private String excludeOrgIds = "1294,5,2409,2417,1222,1255";


    @Value("${overview.exclude-org-ids}")
    private List<Long> listExcludeOrgIds;


    //要除以支部数的会议类型，组织生活会
    @Value("${overview.branch-rate}")
    public List<Integer> meetingTypeId;

    @Value("${ecs.run:false}")
    public Boolean ecsRun = false;

    public OverviewService(SimpleApplicationConfigHelper applicationConfigHelper,
                           OrgTypeConfig orgTypeConfig, OrgService orgService,
                           UserMapper userMapper, PartyAffairsMapper partyAffairsMapper,
                           OrganizationMapper organizationMapper, Errors errors,
                           MeetingMapper meetingMapper,
                           PpmdStatisticsMapper ppmdStatisticsMapper, StatisticsMapper statisticsMapper,
                           RevisitMapper revisitMapper,
                           RestTemplateUserService restTemplateUserService,
                           OpenService openService, MyMongoTemplate mongoTemplate,
                           StringRedisTemplate stringRedisTemplate,
                           EcpOrgMapper ecpOrgMapper) {
        this.applicationConfigHelper = applicationConfigHelper;
        this.orgTypeConfig = orgTypeConfig;
        this.orgService = orgService;
        this.userMapper = userMapper;
        this.partyAffairsMapper = partyAffairsMapper;
        this.organizationMapper = organizationMapper;
        this.errors = errors;
        this.meetingMapper = meetingMapper;
        this.ppmdStatisticsMapper = ppmdStatisticsMapper;
        this.statisticsMapper = statisticsMapper;
        this.restTemplateUserService = restTemplateUserService;
        this.revisitMapper = revisitMapper;
        this.openService = openService;
        this.mongoTemplate = mongoTemplate;
        this.stringRedisTemplate = stringRedisTemplate;
        this.ecpOrgMapper = ecpOrgMapper;
    }

    /**
     * 获取当前区县的顶级组织id
     *
     * @param httpHeaders
     * @return
     */
    private Long getOrgIdByHeader(HttpHeaders httpHeaders) {
        Long regionId = getRegionId(httpHeaders);
        Region.OrgData orgData = applicationConfigHelper.getOrgByRegionId(regionId);
        if (orgData == null) {
            Result<?> result = new Result<>(Global.Errors.ERROR_HEADER.getCode(),
                    "请求头中未发现有效的regionId", HttpStatus.BAD_REQUEST.value(), false);
            throw new ApiException(result.getMessage(), result);
        }
        return orgData.getOrgId();
    }

    /**
     * 获取区县id
     *
     * @param httpHeaders
     * @return
     */
    private static Long getRegionId(HttpHeaders httpHeaders) {
        if (httpHeaders.containsKey(HeaderHelper.OPERATOR_REGION)) {
            List<String> ids = httpHeaders.get(HeaderHelper.OPERATOR_REGION);
            for (String id : ids) {
                return Long.valueOf(id);
            }
        }
        Result<?> result = new Result<>(Global.Errors.ERROR_HEADER.getCode(),
                "请求头中未发现有效的regionId", HttpStatus.BAD_REQUEST.value(), false);
        throw new ApiException(result.getMessage(), result);
    }

    /**
     * 得到对应概况类型
     */
    private Integer getOverviewType(HttpHeaders headers) {
        Long orgId = getOrgIdByHeader(headers);
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        OrganizationEntity organizationEntity = orgService.getById(sysHeader.getOid());
        //全市
        if (orgId.equals(sysHeader.getOid())) {
            return 1;
        }

        if (null == organizationEntity) {
            throw new RuntimeException("未查询到组织信息");
        }
        //党委党总支
        if (orgTypeConfig.checkIsCommunist(organizationEntity.getOrgTypeChild())) {
            return 2;
        }
        //党小组与党支部
        orgTypeConfig.checkIsBranch(organizationEntity.getOrgTypeChild());
        //默认返回支部信息
        return 3;
    }

    /**
     * 组织概况-换届情况
     * 全市 与（党委与党总支）显示一校准、（党小组与党支部）显示一样
     * 一共有两种格式
     * @return
     */
//    public ResponseEntity<Result<?>> orgOverviewPeriod(HttpHeaders headers) {
//        int overviewType = getOverviewType(headers);
//        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
//        if(overviewType==3){
//            String periodInfo = userMapper.getPeriodInfo(sysHeader.getOid(), sysHeader.getRegionId(),excludeOrgIds);
//            PeriodPartyBranchVo periodPartyBranchForm = new PeriodPartyBranchVo();
//            periodPartyBranchForm.setType(3);
//            periodPartyBranchForm.setPeriodInfo(periodInfo);
//            return new ResponseEntity<>(new Result<>(periodPartyBranchForm, errors), HttpStatus.OK);
//        }
//        if(overviewType==2||overviewType==1){
//            PeriodPartyCommitteeVo periodPartyCommitteeForm = new PeriodPartyCommitteeVo();
//            periodPartyCommitteeForm.setType(overviewType);
//            periodPartyCommitteeForm.setNoSetPeriod(userMapper.getNoSetPeriod(sysHeader.getOid(),
//                    sysHeader.getRegionId(),excludeOrgIds));
//            periodPartyCommitteeForm.setSixMonthSetPeriod(userMapper.getSixMonthSetPeriod(sysHeader.getOid(),
//                    sysHeader.getRegionId()));
//            periodPartyCommitteeForm.setCurrentMonthSetPeriod(userMapper.
//                    getCurrentMonthSetPeriod(sysHeader.getOid(),sysHeader.getRegionId()));
//            periodPartyCommitteeForm.setOverdueSetPeriod(userMapper.getOverdueSetPeriod(sysHeader.getOid(), sysHeader.getRegionId()));
//            return new ResponseEntity<>(new Result<>(periodPartyCommitteeForm, errors), HttpStatus.OK);
//        }
//        return null;
//    }


    /**
     * 组织概况-换届情况
     * 全市 与（党委与党总支）显示一校准、（党小组与党支部）显示一样
     * 一共有两种格式
     *
     * @return
     */
    public ResponseEntity<Result<?>> orgOverviewPeriod(HttpHeaders headers) {
        int overviewType = getOverviewType(headers);
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        if (overviewType == 3) {
            String periodInfo = userMapper.getPeriodInfo(sysHeader.getOid(), sysHeader.getRegionId(), excludeOrgIds);
            PeriodPartyBranchVo periodPartyBranchForm = new PeriodPartyBranchVo();
            periodPartyBranchForm.setType(3);
            periodPartyBranchForm.setPeriodInfo(periodInfo);
            List<PeriodPartyBranchVo.LeaderInfo> list = userMapper.getLeaderInfo(sysHeader.getOid());
            if (!CollectionUtils.isEmpty(list)) {
                periodPartyBranchForm.setList(list);
            }
            return new ResponseEntity<>(new Result<>(periodPartyBranchForm, errors), HttpStatus.OK);
        }
        if (overviewType == 2 || overviewType == 1) {
            List<PeriodPartyCommitteeVo> lists = new ArrayList<>();
            for (int i = 1; i <= 3; i++) {
                PeriodInfoVo periodInfoVo = null;
                if (i == 1) {
                    periodInfoVo = userMapper.getCurrentMonthSetPeriodInfo(sysHeader.getOid(), sysHeader.getRegionId(), excludeOrgIds);
                } else if (i == 2) {
                    periodInfoVo = userMapper.getSixMonthSetPeriodInfo(sysHeader.getOid(), sysHeader.getRegionId(), excludeOrgIds);
                } else {
                    periodInfoVo = userMapper.getNoPeriodInfo(sysHeader.getOid(), sysHeader.getRegionId(), excludeOrgIds);
                }
                PeriodPartyCommitteeVo periodPartyCommitteeVo = new PeriodPartyCommitteeVo();
                periodPartyCommitteeVo.setType(overviewType);
                periodPartyCommitteeVo.setStaType(i);
                periodPartyCommitteeVo.setCommittee(periodInfoVo.getParty());
                periodPartyCommitteeVo.setTotalBranch(periodInfoVo.getTotal());
                periodPartyCommitteeVo.setBranch(periodInfoVo.getBranch());
                lists.add(periodPartyCommitteeVo);
            }
            return new ResponseEntity<>(new Result<>(lists, errors), HttpStatus.OK);
        }
        return null;
    }

    public ResponseEntity<Result<?>> orgOverviewPeriodDetail(HttpHeaders headers, Integer type, Integer orgType,
                                                             Integer page, Integer pageSize) {
        int overviewType = getOverviewType(headers);
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        if (overviewType == 3) {
            return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
        }
        if (overviewType == 2 || overviewType == 1) {
            //type 1.未设置换届信息组织 2.本月应换届支部 3.6个月内应换届支部 4.已超期支部
//            if(type==1){
//                return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
//                        .doSelectPage(() -> userMapper.getNoSetPeriodOrgDetail(sysHeader.getOid(),
//                                sysHeader.getRegionId(), excludeOrgIds)), errors), HttpStatus.OK);
//            }else if(type==2){
//                return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
//                        .doSelectPage(() -> userMapper.getCurrentMonthSetPeriodDetail(sysHeader.getOid(),
//                                sysHeader.getRegionId())), errors), HttpStatus.OK);
//            }else if(type==3){
//                return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
//                        .doSelectPage(() -> userMapper.getSixMonthSetPeriodDetail(sysHeader.getOid(),
//                                sysHeader.getRegionId())), errors), HttpStatus.OK);
//            }else if(type==4){
//                return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
//                        .doSelectPage(() -> userMapper.getOverdueSetPeriodDetail(sysHeader.getOid(),
//                                sysHeader.getRegionId())), errors), HttpStatus.OK);
//            }
            String orgTypeSqlPart = "";
            if (orgType == 1) {
                orgTypeSqlPart = "10280301, 10280310, 10280322, 10280344";
            } else if (orgType == 2) {
                orgTypeSqlPart = "10280303, 10280308, 10280311, 10280318, 10280342";
            } else if (orgType == 3) {
                orgTypeSqlPart = "10280304, 10280309, 10280314, 10280315, 10280319";
            } else {
                orgTypeSqlPart = "1=1";
            }
            String finalOrgTypeSqlPart = orgTypeSqlPart;
            //1.本月应换届 2.6月内应换届 3.超期未换届
            if (type == 1) {
                return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                        .doSelectPage(() -> userMapper.getCurrentMonthSetPeriodDetail(sysHeader.getOid(),
                                sysHeader.getRegionId(), finalOrgTypeSqlPart, excludeOrgIds, listExcludeOrgIds)), errors), HttpStatus.OK);
            } else if (type == 2) {
                return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                        .doSelectPage(() -> userMapper.getSixMonthSetPeriodDetail(sysHeader.getOid(),
                                sysHeader.getRegionId(), finalOrgTypeSqlPart, excludeOrgIds, listExcludeOrgIds)), errors), HttpStatus.OK);
            } else if (type == 3) {
                return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                        .doSelectPage(() -> userMapper.getOverdueSetPeriodDetail(sysHeader.getOid(),
                                sysHeader.getRegionId(), finalOrgTypeSqlPart, excludeOrgIds, listExcludeOrgIds)), errors), HttpStatus.OK);
            }
        }
        return null;
    }

    /**
     * 组织概况- 组织生活转接
     *
     * @param headers
     * @return
     */
    public ResponseEntity<Result<?>> orgOverviewOrgRelationship(HttpHeaders headers) {
        int overviewType = getOverviewType(headers);
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        OrgRelationshipVo orgRelationshipForm = new OrgRelationshipVo();
        orgRelationshipForm.setType(overviewType);
        List<OrgRelationshipVo.RelationshipInfo> list = new ArrayList<>();
        for (int i = 1; i <= 2; i++) {
            OrgRelationshipVo.RelationshipInfo relationshipInfo = new OrgRelationshipVo.RelationshipInfo();
            String salDataPart = "";
            if (i == 1) {
                salDataPart = "DATE_FORMAT(utm.process_time,'%Y-%m') = DATE_FORMAT(SYSDATE(),'%Y-%m')";
            } else {
                salDataPart = "YEAR(utm.process_time) = YEAR(SYSDATE())";
            }
            List<OverviewUserVo> tranInUser = userMapper.
                    transferInCountDetail(sysHeader.getOid(), sysHeader.getRegionId(), salDataPart,
                            null, excludeOrgIds, listExcludeOrgIds);
            List<OverviewUserVo> tranOutUser = userMapper.
                    transferOutCountDetail(sysHeader.getOid(), sysHeader.getRegionId(), salDataPart, null, excludeOrgIds, listExcludeOrgIds);
            relationshipInfo.setStaType(i);
            relationshipInfo.setTransferIn(tranInUser.size());
            relationshipInfo.setTransferOut(tranOutUser.size());
            list.add(relationshipInfo);
        }
        orgRelationshipForm.setList(list);
        return new ResponseEntity<>(new Result<>(orgRelationshipForm, errors), HttpStatus.OK);
    }

    /**
     * 组织概况- 组织生活转接
     *
     * @param headers
     * @return
     */
    public ResponseEntity<Result<?>> orgOverviewOrgRelationshipDetail(HttpHeaders headers, Integer staType, Integer type,
                                                                      Integer page, Integer pageSize) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //type:1 转入 2，转出
        String salDataPart = "";
        if (staType == 1) {
            salDataPart = "DATE_FORMAT(utm.process_time,'%Y-%m') = DATE_FORMAT(SYSDATE(),'%Y-%m')";
        } else {
            salDataPart = "YEAR(utm.process_time) = YEAR(SYSDATE())";
        }
        String finalSalDataPart = salDataPart;
        if (type == 1) {
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.transferInCountDetail(sysHeader.getOid(),
                            sysHeader.getRegionId(), finalSalDataPart,
                            null,
                            excludeOrgIds, listExcludeOrgIds)), errors), HttpStatus.OK);
        } else if (type == 2) {
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.transferOutCountDetail(sysHeader.getOid(),
                            sysHeader.getRegionId(),
                            finalSalDataPart,
                            null,
                            excludeOrgIds, listExcludeOrgIds)), errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
        }

    }


    /**
     * 组织概况-党务公开
     *
     * @return
     */
    public ResponseEntity<Result<?>> orgOverviewPartyAffairs(HttpHeaders headers, HeaderHelper.SysHeader sysHeader) {
        int overviewType = getOverviewType(headers);
        PartyAffairsVoVo partyAffairsVo = new PartyAffairsVoVo();
        partyAffairsVo.setType(overviewType);
        PartyResultForm partyInfo = restTemplateUserService.findPartyInfo(sysHeader);
        Long orgId = partyInfo.getPartyId();
        //查询是不是一经管理组织
        Long topOrgIg = getOrgIdByHeader(headers);
        List<PartyAffairs> partyAffairs = null;
        //如果是顶级单独处理 或者他本来请求头里面的组织id 就是3
        if (orgId.equals(topOrgIg) || sysHeader.getOid().equals(3L)) {
            partyAffairs = partyAffairsMapper.showListSpecial(orgId, excludeOrgIds);
        } else {
            partyAffairs = partyAffairsMapper.showList(orgId, excludeOrgIds);
        }
        partyAffairsVo.setList(partyAffairs);
        return new ResponseEntity<>(new Result<>(partyAffairsVo, errors), HttpStatus.OK);
    }

    /**
     * 组织概况
     *
     * @param headers
     * @return
     */
    public ResponseEntity<Result<?>> orgOverviewOrg(HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        int overviewType = getOverviewType(headers);
        //全市
        if (overviewType == 1) {
            OverviewStandardVo overviewStandardVo = new OverviewStandardVo();
            overviewStandardVo.setType(overviewType);
            //OverviewStandardVo
            List<KeyValVo> keyValVos = userMapper.orgCityWide(sysHeader.getOid(), excludeOrgIds);
            // 处理云上组织
//            Optional<KeyValVo> optional = keyValVos.stream().filter(keyValVo -> "云上组织".equals(keyValVo.name)).findFirst();
//            if (optional.isPresent()) {
//                KeyValVo kv = optional.get();
//                kv.setValue(this.totalEcpOrg().intValue());
//            } else {
//                keyValVos.add(new KeyValVo("云上组织", this.totalEcpOrg().intValue(), null, null));
//            }

            overviewStandardVo.setTotal(keyValVos.stream().mapToInt(KeyValVo::getValue).sum());
            overviewStandardVo.setListKeyValue(keyValVos);
            return new ResponseEntity<>(new Result<>(overviewStandardVo, errors), HttpStatus.OK);
        }
        //党委与党总支
        if (overviewType == 2) {
            List<KeyValVo> keyValVos = userMapper.orgOverviewOrgCommittee(sysHeader.getOid(), excludeOrgIds);
            OrgPartyCommitteeVo orgPartyCommitteeVo = new OrgPartyCommitteeVo();
            orgPartyCommitteeVo.setType(overviewType);
            orgPartyCommitteeVo.setList(keyValVos);
            return new ResponseEntity<>(new Result<>(orgPartyCommitteeVo, errors), HttpStatus.OK);
        }
        //党支部与党小组
        if (overviewType == 3) {
            OrganizationEntity organizationEntity = orgService.getById(sysHeader.getOid());
            OrgPartyBranchVo orgPartyBranchVo = new OrgPartyBranchVo();
            orgPartyBranchVo.setType(overviewType);
            orgPartyBranchVo.setOrgName(organizationEntity.getName());
            //如果是党小组 返回的支部信息
            if (orgTypeConfig.checkIsCommunistGroup(organizationEntity.getOrgTypeChild())) {
                OrganizationEntity organization = orgService.getById(organizationEntity.getParentId());
                orgPartyBranchVo.setOrgName(organization.getName());
            }
            //得到党小组数量
            List<String> partyGroupName = organizationMapper.getPartyGroupName(sysHeader.getOid(), excludeOrgIds);
            orgPartyBranchVo.setPartyGroupName(partyGroupName);
            return new ResponseEntity<>(new Result<>(orgPartyBranchVo, errors), HttpStatus.OK);
        }
        return null;
    }

    /**
     * 详情列表
     *
     * @param headers
     * @param aliasName
     * @return
     */
    public ResponseEntity<Result<?>> orgOverviewOrgDetail(HttpHeaders headers, String aliasName,
                                                          int page, int pageSize) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        int overviewType = getOverviewType(headers);
        String sql = null;
        //全市
        if (overviewType == 1) {
            Integer detailId = userMapper.getOverviewOptionType(5, aliasName);
            if (detailId == null) {
                return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
            }
            if (detailId == 1) {
                //行政单位
                sql = "select  `name` ,organization_id as orgId FROM t_organization \n" +
                        "where `status` = 1 and org_type = 102807 and parent_id = 2 and organization_id not in(" + excludeOrgIds + ")";
            }
            if (detailId == 4) {
                //党组
                sql = "SELECT g1.`name`,o1.organization_id as orgId FROM t_organization o1 \n" +
                        "INNER JOIN\n" +
                        "\t\tt_party_group g1 \n" +
                        "\t\t\t\tON o1.owner_id=g1.org_id \n" +
                        "\t\t\t\tAND g1.`status`=1 \n" +
                        "WHERE\n" +
                        "\t\to1.`status`=1 \n" +
                        "\t\tand o1.organization_id not in(" + excludeOrgIds + ") \n" +
                        "\t\tAND (\n" +
                        "\t\t\t\to1.organization_id=3 \n" +
                        "\t\t\t\tOR o1.org_level LIKE '%-3-%' \n" +
                        "\t\t) \n" +
                        "group by\n" +
                        "\t\tg1.org_id";
            } else if (detailId == 2) {
                //党委
                sql = "\t\tSELECT `name` ,organization_id as orgId FROM t_organization \n" +
                        "\t\t\t\tWHERE `status` = 1  AND org_type = 102803 \n" +
                        "\t\t\t\tand org_type_child in (10280301,10280310,10280322)\n" +
                        "\t\t\t\tand organization_id not in(" + excludeOrgIds + ") \n" +
                        "\t\t\t\tAND (organization_id = 3 OR org_level LIKE '%-3-%')\t";
            } else if (detailId == 3) {
                //党总支
                sql = "\t\tSELECT `name`,organization_id as orgId   FROM t_organization \n" +
                        "\t\t\t\tWHERE `status` = 1  AND org_type = 102803 \n" +
                        "\t\t\t\tand org_type_child in (10280303,10280308,10280311,10280318)\n" +
                        "\t\t\t\tand organization_id not in(" + excludeOrgIds + ") \n" +
                        "\t\t\t\tAND (organization_id = 3 OR org_level LIKE '%-3-%')\t";
            } else if (detailId == 5) {
                //党支部
                sql = "\t\tSELECT `name`,organization_id as orgId  FROM t_organization \n" +
                        "\t\t\t\tWHERE `status` = 1  AND org_type = 102803 \n" +
                        "\t\t\t\tand org_type_child in (10280304,10280309,10280314,10280315,10280319)\n" +
                        "\t\t\t\tand organization_id not in(" + excludeOrgIds + ") \n" +
                        "\t\t\t\tAND (organization_id = 3 OR org_level LIKE '%-3-%')\t";
            } else if (detailId == 6) {
                //党小组
                sql = "\t\tSELECT `name`,organization_id as orgId  FROM t_organization \n" +
                        "\t\t\t\tWHERE `status` = 1  AND org_type = 102803 \n" +
                        "\t\t\t\tand org_type_child in (10280306)\n" +
                        "\t\t\t\tand organization_id not in(" + excludeOrgIds + ") \n" +
                        "\t\t\t\tAND (organization_id = 3 OR org_level LIKE '%-3-%')\t";
            } else if (detailId == 7) {
                // 云上组织
                sql = "SELECT ecp_org_name AS `name`,ecp_org_id AS orgId FROM `t_ecp_org` WHERE is_del != 1 AND `status` IS NULL";
            }
        }
        //党委与党总支
        if (overviewType == 2) {
            if ("党支部".equals(aliasName)) {
                sql = "\tSELECT `name` ,organization_id as orgId  FROM t_organization \n" +
                        "\tWHERE `status` = 1 \n" +
                        "\tAND org_type = 102803 and organization_id not in(" + excludeOrgIds + ") " +
                        "  and org_type_child IN(10280304)\n" +
                        "  AND (organization_id = " + sysHeader.getOid() + " OR org_level LIKE '%-" + sysHeader.getOid() + "-%')";
            }

            if ("党小组".equals(aliasName)) {
                sql = "\tSELECT `name` ,organization_id as orgId  FROM t_organization \n" +
                        "\tWHERE `status` = 1 \n" +
                        "\tAND org_type = 102803 and organization_id not in(" + excludeOrgIds + ") " +
                        "  and org_type_child IN(10280306)\n" +
                        "  AND (organization_id = " + sysHeader.getOid() + " OR org_level LIKE '%-" + sysHeader.getOid() + "-%')";
            }
        }
        //党支部是最后一级数据
        if (overviewType == 3) {
            return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
        }
        String finalSql = sql;
        return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                .doSelectPage(() -> userMapper.getOrgNameBySql(finalSql)), errors), HttpStatus.OK);
    }

    //组织概况-人员统计
    public ResponseEntity<Result<?>> orgOverviewUserSta(HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        int overviewType = getOverviewType(headers);
        PartyMemberStaVo partyMemberStaVo = new PartyMemberStaVo();
        partyMemberStaVo.setType(overviewType);
        //正式党务与预备党员
        List<KeyValVo> keyValVos = userMapper.staPartyMember(sysHeader.getOid(), excludeOrgIds);
        Optional<KeyValVo> option1 = keyValVos.stream().filter(item -> item.getName().equals("正式党员")).findFirst();
        option1.ifPresent(keyValVo -> partyMemberStaVo.setFormalPartyMember(keyValVo.getValue()));
        Optional<KeyValVo> option2 = keyValVos.stream().filter(item -> item.getName().equals("预备党员")).findFirst();
        option2.ifPresent(keyValVo -> partyMemberStaVo.setPrePartyMember(keyValVo.getValue()));
        Optional<KeyValVo> option3 = keyValVos.stream().filter(item -> item.getName().equals("其它")).findFirst();
        option3.ifPresent(keyValVo -> partyMemberStaVo.setOtherPartyMember(keyValVo.getValue()));
        List<KeyValVo> keyValVosGender = userMapper.staPartyMemberGender(sysHeader.getOid(), excludeOrgIds);
        //党员性别
        Optional<KeyValVo> option4 = keyValVosGender.stream().filter(item -> item.getName().equals("男")).findFirst();
        option4.ifPresent(keyValVo -> partyMemberStaVo.setMaleMember(keyValVo.getValue()));
        Optional<KeyValVo> option5 = keyValVosGender.stream().filter(item -> item.getName().equals("女")).findFirst();
        option5.ifPresent(keyValVo -> partyMemberStaVo.setFemaleMember(keyValVo.getValue()));
        Optional<KeyValVo> option6 = keyValVosGender.stream().filter(item -> item.getName().equals("未知")).findFirst();
        option6.ifPresent(keyValVo -> partyMemberStaVo.setOtherGenderMember(keyValVo.getValue()));
        //党员总数
        List<Long> partyMemberList = userMapper.staPartyMemberTotal(sysHeader.getOid(), excludeOrgIds);
        int total = CollectionUtils.isEmpty(partyMemberList) ? 0 : partyMemberList.size();
        //全市
        if (overviewType == 1) {
            List<Long> excludeOrgList = Arrays.stream(this.excludeOrgIds.split(","))
                    .map(Long::valueOf).collect(Collectors.toList());
            //非党员数量
            List<Long> normalStaff = this.userMapper.staNoPartyMemberTotal(null, excludeOrgList);
            partyMemberStaVo.setNormalStaff(CollectionUtils.isEmpty(normalStaff) ? 0 : normalStaff.size());
            //普通职工总人数 等于党员总数+非党员数量
            partyMemberStaVo.setCityWideStaff(partyMemberStaVo.getNormalStaff() + total);
        }
        partyMemberStaVo.setTotal(total);
        return new ResponseEntity<>(new Result<>(partyMemberStaVo, errors), HttpStatus.OK);
    }

    //组织概况-人员统计
    //type:1 正式党员 2.预备党员 3.男党员 4.女党员 5.党员 6 非党员
    public ResponseEntity<Result<?>> orgOverviewUserStaDetail(HttpHeaders headers, Integer type, Integer page, Integer pageSize) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        int overviewType = getOverviewType(headers);
        PartyMemberStaVo partyMemberStaVo = new PartyMemberStaVo();
        partyMemberStaVo.setType(overviewType);
        //正式党务与预备党员
        if (type == 1) {
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.staPartyMemberDetail(sysHeader.getOid(), excludeOrgIds, 1)),
                    errors), HttpStatus.OK);
        } else if (type == 2) {
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.staPartyMemberDetail(sysHeader.getOid(), excludeOrgIds, 5)),
                    errors), HttpStatus.OK);
        } else if (type == 3) {
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.staPartyMemberGenderDetail(sysHeader.getOid(), excludeOrgIds, 1)),
                    errors), HttpStatus.OK);
        } else if (type == 4) {
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.staPartyMemberGenderDetail(sysHeader.getOid(), excludeOrgIds, 2)),
                    errors), HttpStatus.OK);
        } else if (type == 5) {
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.staPartyMemberTotalDetail(sysHeader.getOid(), excludeOrgIds)),
                    errors), HttpStatus.OK);
        } else if (type == 6) {
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.staNoPartyMemberTotalDetail(sysHeader.getOid(), excludeOrgIds)),
                    errors), HttpStatus.OK);
        } else if (type == 7) {
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.staPartyMemberNoGenderDetail(sysHeader.getOid(), excludeOrgIds)),
                    errors), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    //组织概况-学历统率
    public ResponseEntity<Result<?>> orgOverviewEducation(HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(orgOverviewEducationVo(headers), errors), HttpStatus.OK);
    }

    public OverviewStandardVo orgOverviewEducationVo(HttpHeaders headers) {
        OverviewStandardVo overviewStandardVo = new OverviewStandardVo();
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        int overviewType = getOverviewType(headers);
        overviewStandardVo.setType(overviewType);
        //OverviewStandardVo
        List<KeyValVo> keyValVos = userMapper.staOverviewEducation(sysHeader.getOid(), excludeOrgIds);
        overviewStandardVo.setTotal(keyValVos.stream().mapToInt(KeyValVo::getValue).sum());
        overviewStandardVo.setListKeyValue(keyValVos);
        return overviewStandardVo;
    }

    //组织概况-学历统率 1 研究生及以上 2.本科 3.专科 4高中生及以下 5.未知
    public ResponseEntity<Result<?>> orgOverviewEducationDetail(HttpHeaders headers, String aliasName,
                                                                Integer page, Integer pageSize) {
        Integer detailId = userMapper.getOverviewOptionType(1, aliasName);
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        if (detailId == 1) {
            String education = "103901,103902";
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.staOverviewEducationDetail(sysHeader.getOid(), excludeOrgIds, education)),
                    errors), HttpStatus.OK);
        } else if (detailId == 2) {
            String education = "1003,103903";
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.staOverviewEducationDetail(sysHeader.getOid(), excludeOrgIds, education)),
                    errors), HttpStatus.OK);
        } else if (detailId == 3) {
            String education = "103904,103905,1004";
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.staOverviewEducationDetail(sysHeader.getOid(), excludeOrgIds, education)),
                    errors), HttpStatus.OK);
        } else if (detailId == 4) {
            String education = "1005,103909,103907,103908,103909,03910";
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.staOverviewEducationDetail(sysHeader.getOid(), excludeOrgIds, education)),
                    errors), HttpStatus.OK);
        } else if (detailId == 5) {
            String education = "103901,103902,1003,103903,103904,103905,1004,103906,103907,103908,103909,03910";
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.staOverviewEducationOtherDetail(sysHeader.getOid(), excludeOrgIds, education)),
                    errors), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    //组织概况-年龄分布
    public ResponseEntity<Result<?>> orgOverviewAgeDistributed(HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(orgOverviewAgeDistributedVo(headers), errors), HttpStatus.OK);
    }

    public OverviewStandardVo orgOverviewAgeDistributedVo(HttpHeaders headers) {
        OverviewStandardVo overviewStandardVo = new OverviewStandardVo();
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        int overviewType = getOverviewType(headers);
        overviewStandardVo.setType(overviewType);
        //OverviewStandardVo
        List<KeyValVo> keyValVos = userMapper.orgOverviewAgeDistributed(sysHeader.getOid(), excludeOrgIds);
        overviewStandardVo.setTotal(keyValVos.stream().mapToInt(KeyValVo::getValue).sum());
        overviewStandardVo.setListKeyValue(keyValVos);
        return overviewStandardVo;
    }

    //组织概况-年龄分布
    public ResponseEntity<Result<?>> orgOverviewAgeDistributedDetail(HttpHeaders headers, String aliasName,
                                                                     Integer page, Integer pageSize) {
        Integer detailId = userMapper.getOverviewOptionType(2, aliasName);
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //30岁及以下
        if (detailId == 1) {
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.orgOverviewAgeDistributedDetail(sysHeader.getOid(),
                            excludeOrgIds, 1, 30)), errors), HttpStatus.OK);
            //31岁~45岁
        } else if (detailId == 2) {
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.orgOverviewAgeDistributedDetail(sysHeader.getOid(),
                            excludeOrgIds, 31, 45)), errors), HttpStatus.OK);
            //46岁~55岁
        } else if (detailId == 3) {
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.orgOverviewAgeDistributedDetail(sysHeader.getOid(),
                            excludeOrgIds, 46, 55)), errors), HttpStatus.OK);
            //55岁以上
        } else if (detailId == 4) {
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.orgOverviewAgeDistributedDetail(sysHeader.getOid(),
                            excludeOrgIds, 56, 200)), errors), HttpStatus.OK);
        } else if (detailId == 5) {
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(page, pageSize)
                    .doSelectPage(() -> userMapper.orgOverviewAgeDistributedOtherDetail(sysHeader.getOid(),
                            excludeOrgIds, 56, 200)), errors), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    //组织概况-党龄分布
    public ResponseEntity<Result<?>> orgOverviewPartyAgeDistributed(HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(orgOverviewPartyAgeDistributedVo(headers), errors), HttpStatus.OK);
    }

    public OverviewStandardVo orgOverviewPartyAgeDistributedVo(HttpHeaders headers) {
        OverviewStandardVo overviewStandardVo = new OverviewStandardVo();
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        int overviewType = getOverviewType(headers);
        overviewStandardVo.setType(overviewType);
        //OverviewStandardVo
        List<KeyValVo> keyValVos = userMapper.orgOverviewPartyAgeDistributed(sysHeader.getOid(), excludeOrgIds);
        overviewStandardVo.setTotal(keyValVos.stream().mapToInt(KeyValVo::getValue).sum());
        overviewStandardVo.setListKeyValue(keyValVos);
        return overviewStandardVo;
    }

    //组织概况-党龄分布详情
    public ResponseEntity<Result<?>> orgOverviewPartyAgeDistributedDetail(HttpHeaders headers, String aliasName,
                                                                          Integer pageNum, Integer pageSize) {
        Integer detailId = userMapper.getOverviewOptionType(3, aliasName);
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        if (detailId == 1) {
            //10年及以下
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(pageNum, pageSize)
                    .doSelectPage(() -> userMapper.orgOverviewPartyAgeDistributedDetail(sysHeader.getOid(),
                            excludeOrgIds, 0, 10)), errors), HttpStatus.OK);
        } else if (detailId == 2) {
            //11年~20年
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(pageNum, pageSize)
                    .doSelectPage(() -> userMapper.orgOverviewPartyAgeDistributedDetail(sysHeader.getOid(),
                            excludeOrgIds, 11, 20)), errors), HttpStatus.OK);
        } else if (detailId == 3) {
            //21年~30年
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(pageNum, pageSize)
                    .doSelectPage(() -> userMapper.orgOverviewPartyAgeDistributedDetail(sysHeader.getOid(),
                            excludeOrgIds, 21, 30)), errors), HttpStatus.OK);
        } else if (detailId == 4) {
            //30年以上
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(pageNum, pageSize)
                    .doSelectPage(() -> userMapper.orgOverviewPartyAgeDistributedDetail(sysHeader.getOid(),
                            excludeOrgIds, 31, 100)), errors), HttpStatus.OK);
        } else if (detailId == 5) {
            //30年以上
            return new ResponseEntity<>(new Result<>(PageHelper.startPage(pageNum, pageSize)
                    .doSelectPage(() -> userMapper.orgOverviewPartyAgeDistributedOtherDetail(sysHeader.getOid(),
                            excludeOrgIds, 31, 100)), errors), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    // 组织概况-发展党员情况
    public ResponseEntity<Result<?>> orgOverviewDevelopPartyMembers(HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(orgOverviewDevelopPartyMembersVo(headers), errors), HttpStatus.OK);
    }

    public OverviewStandardVo orgOverviewDevelopPartyMembersVo(HttpHeaders headers) {
        OverviewStandardVo overviewStandardVo = new OverviewStandardVo();
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        int overviewType = getOverviewType(headers);
        overviewStandardVo.setType(overviewType);
        //查询组织与它下级这样为详细信息保持一致
        LinkedList<OrganizationEntity> allChildOrg = orgService.findAllChildOrg(sysHeader.getOid(), 1,
                sysHeader.getRegionId(), true);
        List<Long> collect = allChildOrg.stream().map(OrganizationEntity::getOrganizationId).collect(Collectors.toList());
        //OverviewStandardVo
        List<KeyValVo> keyValVos = userMapper.orgOverviewDevelopPartyMembers(collect);
        overviewStandardVo.setTotal(keyValVos.stream().mapToInt(KeyValVo::getValue).sum());
        overviewStandardVo.setListKeyValue(keyValVos);
        return overviewStandardVo;
    }

    //组织生活概况-组织生活统计
    public ResponseEntity<Result<?>> orgLifeOverviewOrgLife(HttpHeaders headers) {
        OverviewStandardVo overviewStandardVo = new OverviewStandardVo();
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        resetSysHeaderInfo(sysHeader);
        List<KeyValVo> keyValVos = meetingMapper.orgLifeOverviewOrgLife(sysHeader.getOid());
        overviewStandardVo.setTotal(keyValVos.stream().mapToInt(KeyValVo::getValue).sum());
        overviewStandardVo.setListKeyValue(keyValVos);
        return new ResponseEntity<>(new Result<>(overviewStandardVo, errors), HttpStatus.OK);
    }

    //组织生活概况-民主评议统计
    public ResponseEntity<Result<?>> orgLifeOverviewDemocraticReview(HttpHeaders headers) {
        OverviewStandardVo overviewStandardVo = new OverviewStandardVo();
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        resetSysHeaderInfo(sysHeader);
        List<KeyValVo> keyValVos = meetingMapper.orgLifeOverviewDemocraticReview(sysHeader.getOid(),
                DateUtils.getYear(new Date()) - 1);
        overviewStandardVo.setTotal(keyValVos.stream().mapToInt(KeyValVo::getValue).sum());
        overviewStandardVo.setListKeyValue(keyValVos);
        return new ResponseEntity<>(new Result<>(overviewStandardVo, errors), HttpStatus.OK);
    }

    //组织生活概况-民主评议统计-细项
    public ResponseEntity<Result<?>> orgLifeOverviewDemocraticReviewType(HttpHeaders headers, Integer type, Integer year, Integer pageSize, Integer pageNum) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (type != 5) {//未参与人
            Page<KeyValVo> page = PageHelper.startPage(pageNum, pageSize).doSelectPage(
                    () -> meetingMapper.orgLifeOverviewDemocraticReviewType(header.getRegionId(), Collections.singletonList(type), year, header.getOid()));
            return new ResponseEntity<>(new Result<>(page, errors), HttpStatus.OK);
        }
        //所有党员
        List<UserInfoBase> list = openService.getUserListByOrgId(header.getOid(), null, headers);
        List<Long> listAll = list.stream().map(UserInfoBase::getUserId).collect(Collectors.toList());
        log.debug("所有党员人数:" + list.size() + listAll);
        List<Integer> types = Arrays.asList(1, 2, 3, 4);
        List<KeyValVo> listHad = meetingMapper.orgLifeOverviewDemocraticReviewType(header.getRegionId(), types, year, header.getOid());
        List<Long> listHadIds = listHad.stream().map(KeyValVo::getId).collect(Collectors.toList());
        log.debug("完成人数:" + listHadIds.size() + listHadIds);
        List<KeyValVo> listNotJoin = new ArrayList<>();
        list.stream().filter(i -> !listHadIds.contains(i.getUserId())).forEach(i -> {
            KeyValVo vo = new KeyValVo();
            vo.setName(i.getOrgName());
            vo.setValueStr(i.getUserName());
            listNotJoin.add(vo);
        });
        Page<KeyValVo> page = createPage(listNotJoin, pageSize, pageNum);
        return new ResponseEntity<>(new Result<>(page, errors), HttpStatus.OK);
    }


    public Page<KeyValVo> createPage(List<KeyValVo> list, Integer pageSize, Integer pageNum) {
        Page<KeyValVo> pageInfo = new Page<>();
        pageInfo.setPageSize(pageSize);
        pageInfo.setPageNum(pageNum);
        int total = list.size();
        pageInfo.setTotal(total);//总记录数
        int page = (total % pageSize == 0 ? total / pageSize : (total / pageSize + 1));
        pageInfo.setPages(page);//总页数
        int startRow = (pageNum - 1) * pageSize + 1;
        int endRow = (pageNum != page ? pageNum * pageSize : (int) (total / pageSize) * pageSize + total % pageSize);
        List<KeyValVo> data = list.stream().skip(startRow - 1).limit(pageSize).toList();
        pageInfo.addAll(data);
        return pageInfo;
    }


    // 组织生活概况-党费交纳统计
    public ResponseEntity<Result<?>> orgLifeOverviewPpmd(HttpHeaders headers) {

        return new ResponseEntity<>(new Result<>(getOrgLifeOverviewPpmd(headers), errors), HttpStatus.OK);
    }

    // 组织生活概况-党费交纳统计
    public PpmdStaVo getOrgLifeOverviewPpmd(HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        resetSysHeaderInfo(sysHeader);
        PpmdStaVo ppmdStaVo = ppmdStatisticsMapper.getOverviewPpmdSta(sysHeader.getOid(), LocalDateTimeUtils.toDay(LocalDate.now()));
        //设置未交人数
        ppmdStaVo.setNoPayNum(Math.max(ppmdStaVo.getTotalNum() - ppmdStaVo.getPayNum(), 0));
        //本月统计
        List<PpmdStaVo.Series> listSeries = new ArrayList<>();
        List<PpmdStaResultVo> overviewCurrentMonth = ppmdStatisticsMapper.
                getOverviewCurrentMonth(sysHeader.getOid(), "now()");
        PpmdStaVo.Series seriesCurrentMonth = new PpmdStaVo.Series();
        seriesCurrentMonth.setName("本月");
        int currentYear = DateUtils.getCurrentYear();
        int month = DateUtils.getMonth(new Date());
        //int daysByYearMonth = DateUtils.getDaysByYearMonth(currentYear, month);
        int daysByYearMonth = Calendar.getInstance().get(Calendar.DAY_OF_MONTH);

        List<Integer> listCurrentMonth = createListData(daysByYearMonth);
        List<Double> listCurrentData = new LinkedList<>();
        listCurrentMonth.forEach(item -> {
            List<PpmdStaResultVo> collect = overviewCurrentMonth.stream().filter(it -> it.getPayDate() <= item)
                    .collect(Collectors.toList());
            double sumResult = collect.stream().mapToDouble(PpmdStaResultVo::getPayAlready).sum();
            listCurrentData.add(NumberUtils.divide(sumResult, 1, 2));
        });

        seriesCurrentMonth.setData(listCurrentData);
        listSeries.add(seriesCurrentMonth);
        //上个月统计
        List<PpmdStaResultVo> overviewLastMonth = ppmdStatisticsMapper.
                getOverviewCurrentMonth(sysHeader.getOid(), "date_sub(now(),interval 1 month)");
        PpmdStaVo.Series seriesLastMonth = new PpmdStaVo.Series();
        seriesLastMonth.setName("上月");
        if (month == 1) {
            currentYear = currentYear - 1;
            month = 12;
        } else {
            month = month - 1;
        }
        daysByYearMonth = DateUtils.getDaysByYearMonth(currentYear, month);
        List<Integer> listLastMonth = createListData(daysByYearMonth);
        List<Double> listLastData = new LinkedList<>();
        listLastMonth.forEach(item -> {
            List<PpmdStaResultVo> collect = overviewLastMonth.stream().filter(it -> it.getPayDate() <= item)
                    .collect(Collectors.toList());
            double sumResult = collect.stream().mapToDouble(PpmdStaResultVo::getPayAlready).sum();
            listLastData.add(NumberUtils.divide(sumResult, 1, 2));
        });
        seriesLastMonth.setData(listLastData);

        listSeries.add(seriesLastMonth);
        ppmdStaVo.setListSeries(listSeries);
        return ppmdStaVo;
    }

    // 组织生活概况-党费交纳统计 钻取详情
    public Page<PpmdStaDetailsVo> orgLifeOverviewPpmdDetails(HttpHeaders headers, Integer isPay, Integer pageNo, Integer pageSize) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //设置分页属性
        PageNumber pageNumber = new PageNumber(pageNo, pageSize);
        int p = Preconditions.checkNotNull(pageNumber.getPage());
        int r = Preconditions.checkNotNull(pageNumber.getRows());
        //查询
        return PageHelper.startPage(p, r).doSelectPage(() -> ppmdStatisticsMapper.getOverviewPpmdStaDetails(sysHeader.getOid(), isPay));
    }

    /**
     * 生成一个时间的List
     *
     * @param days
     * @return
     */
    private List<Integer> createListData(int days) {
        List<Integer> list = new LinkedList<>();
        for (int i = 1; i <= days; i++) {
            list.add(i);
        }
        return list;
    }


    /**
     * 开展活动情况
     *
     * @param headers
     * @return
     */
    public ResponseEntity<Result<?>> orgLifeOverviewActivity(HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        int overviewType = getOverviewType(headers);
        OverviewActivityVo overviewActivityVo = new OverviewActivityVo();
        overviewActivityVo.setType(overviewType);
        int currentQuarterly = DateUtils.getCurrentQuarterly();
        String startTime = DateUtils.getCurrentQuarterlyStartTime(currentQuarterly);
        String endTime = DateUtils.getCurrentQuarterlyEndTime(currentQuarterly);
        //党委与党总支
        if (overviewType == 2 || overviewType == 1) {
            List<OverviewActivityVo.Series> overviewActivityVos = statisticsMapper.
                    staOverviewMeetingByCommittee(startTime, endTime, sysHeader.getOid());
            overviewActivityVos.forEach(item -> {
                item.setName(String.format(item.getName(), item.getNumber()));
            });
            overviewActivityVo.setListSeries(overviewActivityVos);
            return new ResponseEntity<>(new Result<>(overviewActivityVo, errors), HttpStatus.OK);
        }
        Map<String, String> listMap = new LinkedHashMap<>();
        //党支部与党小组
        if (overviewType == 3) {
            resetSysHeaderInfo(sysHeader);
            //本季度党会大会
            OverviewActivityFinishVo overviewActivityFinishVo = statisticsMapper.
                    staOverviewMeetingBranch1(startTime, endTime, sysHeader.getOid());
            listMap.put("本季度党员大会", packInfo(overviewActivityFinishVo.getFinished1()));
            //本季度党课
            OverviewActivityFinishVo overviewActivityFinishVo2 = statisticsMapper.
                    staOverviewMeetingBranch2(startTime, endTime, sysHeader.getOid());
            listMap.put("本季度党课", packInfo(overviewActivityFinishVo2.getFinished2()));
            //本月党支部委员会会议
            OverviewActivityFinishVo overviewActivityFinishVo3 = statisticsMapper.
                    staOverviewMeetingBranch3(startTime, endTime, sysHeader.getOid());
            listMap.put("本月支委会", packInfo(overviewActivityFinishVo3.getFinished3()));
            //本月主题党日
            OverviewActivityFinishVo overviewActivityFinishVo4 = statisticsMapper.
                    staOverviewMeetingBranch4(startTime, endTime, sysHeader.getOid());
            //本月党小组会
            listMap.put("本月党小组会", packInfo(overviewActivityFinishVo4.getFinished4()));
            //本月主题党日
            OverviewActivityFinishVo overviewActivityFinishVo5 = statisticsMapper.
                    staOverviewMeetingBranch5(startTime, endTime, sysHeader.getOid());
            listMap.put("本月主题党日", packInfo(overviewActivityFinishVo5.getFinished5()));
            //今年组织生活会
            Integer orgLifeBranchCount = statisticsMapper.staOverviewOrgLifeBranch(sysHeader.getOid());
            listMap.put("今年组织生活会", orgLifeBranchCount > 0 ? "已完成" : "未完成");


            Integer meetingLifeBranchCount = statisticsMapper.staOverviewMeetingLifeBranch(sysHeader.getOid());
            listMap.put("今年民主生活会", meetingLifeBranchCount > 0 ? "已完成" : "未完成");

            Integer talkCount = statisticsMapper.staOverviewMeetingTalk(sysHeader.getOid());
            listMap.put("谈心谈话共开展", "" + talkCount + "次");
            OverviewStandardVo overviewStandardVo = new OverviewStandardVo();
            overviewStandardVo.setType(overviewType);
            List<KeyValVo> list = new ArrayList<>();
            listMap.forEach((key, value) -> {
                KeyValVo keyValVo = new KeyValVo();
                keyValVo.setName(key);
                keyValVo.setValueStr(value);
                list.add(keyValVo);
            });
            overviewStandardVo.setListKeyValue(list);
            return new ResponseEntity<>(new Result<>(overviewStandardVo, errors), HttpStatus.OK);
        }
        return null;
    }


    /**
     * 开展活动情况-新
     *
     * @param headers
     * @return
     */
    public ResponseEntity<Result<?>> orgLifeOverviewActivityNew1(HttpHeaders headers, Long orgId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = (orgId == null ? sysHeader.getOid() : orgId);
        int overviewType = getOverviewType(headers);
        OverviewActivityVo overviewActivityVo = new OverviewActivityVo();
        overviewActivityVo.setType(overviewType);
        int currentQuarterly = DateUtils.getCurrentQuarterly();
        String startTime = DateUtils.getCurrentQuarterlyStartTime(currentQuarterly);
        String endTime = DateUtils.getCurrentQuarterlyEndTime(currentQuarterly);
        DecimalFormat df = new DecimalFormat("###.##");
        //党委与党总支
        if (overviewType == 2 || overviewType == 1) {
            List<OverviewActivityVo.Series> overviewActivityVos = statisticsMapper.
                    staOverviewMeetingByCommitteeNew1(startTime, endTime, orgId);
            Integer totalBranchNum = organizationMapper.countBranchNum(orgId);//支部数量
//            Integer totalPartyNum = statisticsMapper.countPartyOrgNum(startTime,endTime,orgId);//党小组数量
            overviewActivityVos.forEach(item -> {
                if (meetingTypeId.contains(item.getType())) {//如果是组织生活会或民主生活会，直接除以支部数量
                    item.setTotal(totalBranchNum);
                }
                double rate = item.getTotal() == 0 ? 0.00 : BigDecimal.valueOf((double) item.getNumber() / item.getTotal() * 100)
                        .setScale(4, RoundingMode.HALF_UP).doubleValue();
                item.setRate(df.format(rate) + "%");

            });
            overviewActivityVo.setListSeries(overviewActivityVos);
            return new ResponseEntity<>(new Result<>(overviewActivityVo, errors), HttpStatus.OK);
        }
        Map<String, String> listMap = new LinkedHashMap<>();
        //党支部与党小组
        if (overviewType == 3) {
            resetSysHeaderInfo(sysHeader);
            //本季度党会大会
            OverviewActivityFinishVo overviewActivityFinishVo = statisticsMapper.
                    staOverviewMeetingBranch1(startTime, endTime, orgId);
            listMap.put("党员大会(本季度)", packInfoNew1(overviewActivityFinishVo.getFinished1()));
            //本季度党课
            OverviewActivityFinishVo overviewActivityFinishVo2 = statisticsMapper.
                    staOverviewMeetingBranch2(startTime, endTime, orgId);
            listMap.put("党课(本季度)", packInfoNew1(overviewActivityFinishVo2.getFinished2()));
            //本月党支部委员会会议
            OverviewActivityFinishVo overviewActivityFinishVo3 = statisticsMapper.
                    staOverviewMeetingBranch3(startTime, endTime, orgId);
            listMap.put("支委会(本月)", packInfoNew1(overviewActivityFinishVo3.getFinished3()));
            //本月党小组会-特殊处理
            OverviewActivityFinishVo overviewActivityFinishVo4 = statisticsMapper.
                    staOverviewMeetingBranch4New(startTime, endTime, orgId);
            log.debug("mt查询党小组会" + overviewActivityFinishVo4);
            boolean a = (overviewActivityFinishVo4 == null || overviewActivityFinishVo4.getFinished4() == null);
            log.debug("mt查询党小组会" + a);
            String partyOrgMeeting = ((overviewActivityFinishVo4 == null || overviewActivityFinishVo4.getFinished4() == null) ? "-" :
                    df.format(Double.valueOf(overviewActivityFinishVo4.getFinished4())) + "%");
            log.debug("mt查询党小组会" + partyOrgMeeting);
            listMap.put("党小组会(本月)", partyOrgMeeting);
            //本月主题党日
            OverviewActivityFinishVo overviewActivityFinishVo5 = statisticsMapper.
                    staOverviewMeetingBranch5(startTime, endTime, orgId);
            listMap.put("主题党日(本月)", packInfoNew1(overviewActivityFinishVo5.getFinished5()));
            //今年组织生活会
            Integer orgLifeBranchCount = statisticsMapper.staOverviewOrgLifeBranch(orgId);
            listMap.put("组织生活会(本年)", orgLifeBranchCount > 0 ? "100%" : "0%");


//            Integer meetingLifeBranchCount = statisticsMapper.staOverviewMeetingLifeBranch(sysHeader.getOid());
//            listMap.put("今年民主生活会", meetingLifeBranchCount>0?"100%":"0%");

//            Integer talkCount = statisticsMapper.staOverviewMeetingTalk(sysHeader.getOid());
//            listMap.put("谈心谈话共开展", ""+talkCount+"次");
            OverviewStandardVo overviewStandardVo = new OverviewStandardVo();
            overviewStandardVo.setType(overviewType);
            List<KeyValVo> list = new ArrayList<>();
            listMap.forEach((key, value) -> {
                KeyValVo keyValVo = new KeyValVo();
                keyValVo.setName(key);
                keyValVo.setValueStr(value);
                list.add(keyValVo);
            });
            overviewStandardVo.setListKeyValue(list);
            return new ResponseEntity<>(new Result<>(overviewStandardVo, errors), HttpStatus.OK);
        }
        return null;
    }

    /**
     * 包装信息
     *
     * @return
     */
    private String packInfo(String value) {
        try {
            int result = Integer.parseInt(value);
            return result > 0 ? "已完成" : "未完成";
        } catch (Exception ex) {
            return value;
        }
    }

    /**
     * 包装信息
     *
     * @return
     */
    private String packInfoNew1(String value) {
        try {
            int result = Integer.parseInt(value);
            return result > 0 ? "100%" : "0%";
        } catch (Exception ex) {
            return "-";
        }
    }

    /**
     * 重置信息
     */
    private void resetSysHeaderInfo(HeaderHelper.SysHeader sysHeader) {
        OrganizationEntity organizationEntity = orgService.getById(sysHeader.getOid());
        boolean isOrgGroup = orgTypeConfig.checkIsCommunistGroup(organizationEntity.getOrgTypeChild());
        //如果党小组查询它所在的支部
        if (isOrgGroup) {
            OrgGroupVo orgBranch = organizationMapper.getOrgBranch(sysHeader.getOid());
            if (null == orgBranch) {
                throw new ApiException("未查询到党小组所在的支部！");
            }
            //重置org_id
            sysHeader.setOid(orgBranch.getOrgId());
        }
    }


    /**
     * 组织概况-重温入党誓词概况
     *
     * @param headers
     * @param type
     * @return
     */
    public ResponseEntity<Result<?>> orgOverviewRevisit(HttpHeaders headers, Integer type) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        int overviewType = getOverviewType(headers);
        Long oid = sysHeader.getOid();
        //党委与党总支
        List<KeyValVo> keyValVos = revisitMapper.orgOverviewRevisit(type, getSqlPart(overviewType, oid));
        return new ResponseEntity<>(new Result<>(keyValVos, errors), HttpStatus.OK);
    }


    /**
     * 我的支部- 志愿书与入党誓词统计
     *
     * @param headers
     * @param type
     * @return
     */
    public ResponseEntity<Result<?>> myBranchRevisit(HttpHeaders headers, Integer type, Long orgId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Integer todayCount = revisitMapper.todayCount(orgId, type);
        Integer thisYearCount = revisitMapper.thisYearCount(orgId, type);

        Map<String, Object> map = new HashMap<>();
        map.put("today_count", todayCount);
        map.put("this_year_count", thisYearCount);
        return new ResponseEntity<>(new Result<>(map, errors), HttpStatus.OK);
    }

    /**
     * 得到sql片段
     *
     * @return
     */
    private String getSqlPart(int overviewType, Long oid) {
        if (overviewType > 2) {
            return " org_id= " + oid;
        } else if (overviewType == 1 || overviewType == 2) {
            return " (org_id = " + oid + " OR org_level LIKE  '%-" + oid + "-%' )";
        } else {
            return "1=1";
        }
    }


    /**
     * 组织概况-重温入党誓词概况与志愿书 按天拆线图
     * 显示一样
     * type=1 入党誓词
     * type=2 入党志愿书
     */
    public ResponseEntity<Result<?>> orgOverviewStitching(HttpHeaders headers, Integer type) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        int overviewType = getOverviewType(headers);
        Long oid = sysHeader.getOid();
        RevisitStitchingVo revisitStitchingVo = new RevisitStitchingVo();
        //今天是几号
        int dayOfMonth = Calendar.getInstance().get(Calendar.DAY_OF_MONTH);
        RevisitStitchingVo.Series series1 = new RevisitStitchingVo.Series();
        List<Integer> data1 = new ArrayList<>();
        series1.setName("当月");
        List<KeyValVo> keyValVosCurrentMonth = revisitMapper.getCurrentMonthRevisit(type, getSqlPart(overviewType, oid));
        for (int i = 1; i <= dayOfMonth; i++) {
            int finalI = i;
            Optional<KeyValVo> first = keyValVosCurrentMonth.stream().filter(item -> item.getName().equals(finalI + "")).findFirst();
            if (first.isPresent()) {
                data1.add(first.get().getValue());
            } else {
                data1.add(0);
            }
        }
        series1.setData(data1);
        //得到上个月的天数
        RevisitStitchingVo.Series series2 = new RevisitStitchingVo.Series();
        series2.setName("上月");
        int daysOfLastMonth = DateUtils.getDaysOfMonth(new Date(), -1);
        List<Integer> data2 = new ArrayList<>();
        List<KeyValVo> keyValVosLastMonth = revisitMapper.getLastMonthRevisit(type, getSqlPart(overviewType, oid));
        for (int i = 1; i <= daysOfLastMonth; i++) {
            int finalI = i;
            Optional<KeyValVo> first = keyValVosLastMonth.stream().filter(item -> item.getName().equals(finalI + "")).findFirst();
            if (first.isPresent()) {
                data2.add(first.get().getValue());
            } else {
                data2.add(0);
            }
        }
        series1.setData(data1);
        series2.setData(data2);
        revisitStitchingVo.getListSeries().add(series1);
        revisitStitchingVo.getListSeries().add(series2);
        return new ResponseEntity<>(new Result<>(revisitStitchingVo, errors), HttpStatus.OK);
    }

    public ResponseEntity<Result<?>> orgOverviewYearStitching(HttpHeaders headers, Integer type) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        int overviewType = getOverviewType(headers);
        Long oid = sysHeader.getOid();
        RevisitStitchingVo revisitStitchingVo = new RevisitStitchingVo();
        String[] legend = {"今年", "去年"};
        revisitStitchingVo.setLegend(legend);
        RevisitStitchingVo.Series series1 = new RevisitStitchingVo.Series();
        List<Integer> data1 = new ArrayList<>();
        series1.setName("今年");
        List<KeyValVo> keyValVosCurrentYear = revisitMapper.getCurrentYearRevisit(type, getSqlPart(overviewType, oid));
        DateUtils.getCurrentMonth();
        Calendar cal = Calendar.getInstance();
        int month = cal.get(Calendar.MONTH) + 1;
        for (int i = 1; i <= month; i++) {
            int finalI = i;
            Optional<KeyValVo> first = keyValVosCurrentYear.stream().filter(item -> item.getName().equals(finalI + "")).findFirst();
            if (first.isPresent()) {
                data1.add(first.get().getValue());
            } else {
                data1.add(0);
            }
        }
        series1.setData(data1);
        //得到去年
        RevisitStitchingVo.Series series2 = new RevisitStitchingVo.Series();
        series2.setName("去年");
        List<Integer> data2 = new ArrayList<>();
        List<KeyValVo> keyValVosLastYear = revisitMapper.getLastYearRevisit(type, getSqlPart(overviewType, oid));
        for (int i = 1; i <= 12; i++) {
            int finalI = i;
            Optional<KeyValVo> first = keyValVosLastYear.stream().filter(item -> item.getName().equals(finalI + "")).findFirst();
            if (first.isPresent()) {
                data2.add(first.get().getValue());
            } else {
                data2.add(0);
            }
        }
        series1.setData(data1);
        series2.setData(data2);
        revisitStitchingVo.getListSeries().add(series1);
        revisitStitchingVo.getListSeries().add(series2);
        return new ResponseEntity<>(new Result<>(revisitStitchingVo, errors), HttpStatus.OK);
    }

    /**
     * 党员重温排行
     *
     * @param headers type=1 入党誓词 type=2 入党志愿书
     *                sta_data 1.月度 2.季度 3.年度
     *                sta_type 1.支部 2.单位 3.全市
     * @return
     */
    public ResponseEntity<Result<?>> revisitUserRank(HttpHeaders headers, Integer type, Integer staData,
                                                     Integer staType) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        TbcBaseVo userInfoByUserId = userMapper.getAllUserInfoByUserId(sysHeader.getUserId());
        List<RevisitRankVo> revisitRankVos;
        if (staType == 1) {
            List<Integer> branch = orgTypeConfig.getBranch();
            String orgTypeChild = Joiner.on(",").join(branch);
            Long orgId = userInfoByUserId.getOrgId();
            revisitRankVos = revisitMapper.partyMemberRankByOrg(type, orgId, getSqlDatePart(staData), orgTypeChild);
        } else if (staType == 2) {
            Long ownerId = userInfoByUserId.getOwnerId();
            revisitRankVos = revisitMapper.partyMemberRankByOwnName(type, ownerId, getSqlDatePart(staData));
        } else {
            revisitRankVos = revisitMapper.partyMemberRankByUserId(type, getSqlDatePart(staData));
        }
        if (ecsRun) {
            revisitRankVos = revisitRankVos.stream().peek(item -> {
                try {
                    item.setUserName(NumEncryptUtils.decrypt(item.getUserName(), "-1"));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }).collect(Collectors.toList());
        }
        return new ResponseEntity<>(new Result<>(revisitRankVos, errors), HttpStatus.OK);
    }

    /**
     * 党员重温排行导出
     *
     * @return
     */
    public void revisitUserRankReport(HttpHeaders headers,
                                      HttpServletResponse httpServletResponse, Integer type,
                                      Integer staData, Integer staType) {
        String[] header = null;
        List<RevisitRankVo> listResult = null;
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        TbcBaseVo userInfoByUserId = userMapper.getAllUserInfoByUserId(sysHeader.getUserId());
        if (staType == 1) {
            header = new String[]{"排名", "党员", "所属组织", "次数"};
            List<Integer> branch = orgTypeConfig.getBranch();
            String orgTypeChild = Joiner.on(",").join(branch);
            listResult = revisitMapper.partyMemberRankByOrg(type, userInfoByUserId.getOrgId(), getSqlDatePart(staData), orgTypeChild);
        } else if (staType == 2) {
            header = new String[]{"排名", "党员", "所属组织", "次数"};
            listResult = revisitMapper.partyMemberRankByOwnName(type, userInfoByUserId.getOwnerId(), getSqlDatePart(staData));
        } else {
            header = new String[]{"排名", "党员", "所属组织", "次数"};
            listResult = revisitMapper.partyMemberRankByUserId(type, getSqlDatePart(staData));
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        HSSFWorkbook workbook = new HSSFWorkbook();
        List<List<String>> list = new ArrayList<>();
        for (RevisitRankVo revisitRankVo : listResult) {
            List<String> strList = new ArrayList<>();
            strList.add("" + revisitRankVo.getRank());
            strList.add(revisitRankVo.getUserName());
            strList.add(revisitRankVo.getOrgName());
            strList.add("" + revisitRankVo.getCountNum());
            list.add(strList);
        }
        try {
            ExcelUtils.exportExcel(workbook, 0, "党员重温排行", header, list, out);
            //设置输出头
            httpServletResponse.setHeader("content-disposition", "attachment;filename=" +
                    java.net.URLEncoder.encode("党员重温排行" + ".xls", "UTF-8"));
            httpServletResponse.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            httpServletResponse.setContentType("application/octet-stream;charset=UTF-8");
            workbook.write(httpServletResponse.getOutputStream());
        } catch (Exception e) {
            log.error("党员重温排行导出报错", e);
        }
    }

    /**
     * 组织重温排行
     *
     * @param headers type=1 入党誓词 type=2 入党志愿书
     *                sta_data 1.月度 2.季度 3.年度
     *                sta_type 1.支部 2.单位
     * @return
     */
    public ResponseEntity<Result<?>> revisitOrgRankOld(HttpHeaders headers,
                                                       Integer type,
                                                       Integer staData,
                                                       Integer staType) {
        List<RevisitRankVo> revisitRankVos;
        if (staType == 1) {
            List<Integer> branch = orgTypeConfig.getBranch();
            String orgTypeChild = Joiner.on(",").join(branch);
            revisitRankVos = revisitMapper.revisitOrgRankByOrg(type, getSqlDatePartOrg(staData),
                    getSqlDatePart(staData), orgTypeChild);
        } else {
            revisitRankVos = revisitMapper.revisitOrgRankByOwnName(type,
                    getSqlDatePartOrg(staData), getSqlDatePart(staData));
        }
        return new ResponseEntity<>(new Result<>(revisitRankVos, errors), HttpStatus.OK);
    }

    /**
     * 组织重温排行
     *
     * @param headers type=1 入党誓词 type=2 入党志愿书
     *                sta_data 1.月度 2.季度 3.年度
     *                sta_type 1.支部 2.单位
     * @return
     */
    public ResponseEntity<Result<?>> revisitOrgRank(HttpHeaders headers,
                                                    Integer type,
                                                    Integer staData,
                                                    Integer staType) {
        List<RevisitRankVo> filterList = revisitOrgRankInfo(type, staData, staType);
        return new ResponseEntity<>(new Result<>(filterList, errors), HttpStatus.OK);
    }


    private String listMonthsToStr(List<Integer> listMonth) {
        return listMonth.stream().map(String::valueOf)
                .collect(Collectors.joining(","));
    }

    /**
     * 获取信息
     *
     * @param
     * @param type
     * @param staData
     * @param staType
     * @return
     */
    private List<RevisitRankVo> revisitOrgRankInfo(Integer type, Integer staData, Integer staType) {
        List<RevisitRankVo> revisitRankVos;
        List<Integer> listMonth = new ArrayList<>();
        //添加一个默认值
        listMonth.add(-1);
        String currentMonth = DateUtils.getCurrentMonth("yyyy-MM");
        Integer currentYear = DateUtils.getCurrentYear();
        if (staData == 1) {
            listMonth.add(-1);
        } else if (staData == 2) {
            Integer month = DateUtils.getMonth(new Date());
            int currentQuarterly = DateUtils.getCurrentQuarterly();
            if (month == 2 || month == 5 || month == 8 || month == 11) {
                listMonth.add((currentQuarterly - 1) * 3 + 1);
            }
            if (month == 3 || month == 6 || month == 9 || month == 10) {
                listMonth.add((currentQuarterly - 1) * 3 + 1);
                listMonth.add((currentQuarterly - 1) * 3 + 2);
            }
        } else {
            Integer month = DateUtils.getMonth(new Date());
            for (int i = 1; i <= month - 1; i++) {
                listMonth.add(i);
            }
        }
        if (staType == 1) {
            revisitRankVos = revisitMapper.revisitOrgRankByOrgName(type, currentMonth, currentYear, listMonth);
        } else {
            revisitRankVos = revisitMapper.revisitOrgRankByOwnerId(type, currentMonth, currentYear, listMonth);
        }
        return revisitRankVos.stream().
                filter(item -> item.getRate() > 0).limit(100).collect(Collectors.toList());

    }


    private Double handlerRate(Double rate) {
        return NumberUtils.divide(rate * 100, 1.0, 2);
    }

    public void revisitOrgRankReport(HttpHeaders headers,
                                     HttpServletResponse httpServletResponse,
                                     Integer type, Integer staData, Integer staType) {
        String[] header = null;
        List<RevisitRankVo> listResult = revisitOrgRankInfo(type, staData, staType);
        ;
        if (staType == 1) {
            header = new String[]{"排名", "组织名称", "完成率"};
        } else if (staType == 2) {
            header = new String[]{"排名", "所属单位", "完成率"};
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        HSSFWorkbook workbook = new HSSFWorkbook();
        List<List<String>> list = new ArrayList<>();
        for (RevisitRankVo revisitRankVo : listResult) {
            List<String> strList = new ArrayList<>();
            strList.add("" + revisitRankVo.getRank());
            if (staType == 1) {
                strList.add(revisitRankVo.getOrgName());
            } else if (staType == 2) {
                strList.add(revisitRankVo.getOwnName());
            }
            strList.add("" + revisitRankVo.getRate() + "%");
            list.add(strList);
        }
        try {
            ExcelUtils.exportExcel(workbook, 0, "组织重温排行", header, list, out);
            //设置输出头
            httpServletResponse.setHeader("content-disposition", "attachment;filename=" +
                    java.net.URLEncoder.encode("组织重温排行" + ".xls", "UTF-8"));
            httpServletResponse.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            httpServletResponse.setContentType("application/octet-stream;charset=UTF-8");
            workbook.write(httpServletResponse.getOutputStream());
        } catch (Exception e) {
            log.error("组织重温排行导出报错", e);
        }
    }

    /**
     * 得到时间拼装片段sql
     * 1.月度 2.季度 3.年度
     *
     * @return
     */
    private String getSqlDatePart(int staData) {
        if (staData == 1) {
            return " DATE_FORMAT(create_time,'%Y%m') = DATE_FORMAT(NOW(),'%Y%m')  ";
        } else if (staData == 2) {
            return " quarter(create_time) = quarter(curdate())  ";
        } else if (staData == 3) {
            return " DATE_FORMAT(create_time,'%Y') = DATE_FORMAT(NOW() ,'%Y') ";
        } else if (staData == 4) {
            return " to_days(create_time) = to_days( now() ) ";
        } else {
            return " 1=1 ";
        }
    }

    /**
     * 组织拼装sql
     *
     * @return
     */
    private String getSqlDatePartOrg(int staData) {
        if (staData == 1) {
            String currentMonth = DateUtils.getCurrentMonth("yyyy-MM");
            return " us.date_month  >='" + currentMonth + "' and us.date_month  <='" + currentMonth + "' ";
        } else if (staData == 2) {
            int currentQuarterly = DateUtils.getCurrentQuarterly();
            String startTime = DateUtils.getCurrentQuarterlyStartTime(currentQuarterly);
            String endTime = DateUtils.getCurrentQuarterlyEndTime(currentQuarterly);
            return " us.date_month >='" + startTime + "' and us.date_month <='" + endTime + "' ";
        } else if (staData == 3) {
            int currentYear = DateUtils.getCurrentYear();
            String startTime = currentYear + "-01";
            String endTime = currentYear + "-12";
            return " us.date_month >='" + startTime + "' and us.date_month <='" + endTime + "' ";
        } else {
            return " 1=1 ";
        }
    }

    /**
     * 补录数据
     *
     * @param headers
     * @return
     */
    public ResponseEntity<Result<?>> supplementaryRecord(HttpHeaders headers) {
        Set<TbcBaseVo> allUserInfo = userMapper.getAllUserInfoByTbc();
        List<Long> userIds = revisitMapper.supplementaryUserInfo();
        userIds.forEach(item -> {
            Optional<TbcBaseVo> first = allUserInfo.stream().filter(it -> it.getUserId().equals(item)).findFirst();
            if (first.isPresent()) {
                TbcBaseVo tbcBaseVo = first.get();
                revisitMapper.updateSupplementaryRecord(tbcBaseVo);
            }
        });
        return new ResponseEntity<>(new Result<>("SUC", errors), HttpStatus.OK);
    }


    public ResponseEntity<Result<?>> singleUserSta(HttpHeaders headers, Integer type) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //当天统计
        Integer todayCount = revisitMapper.singleUserSta(sysHeader.getUserId(), type, getSqlDatePart(4));
        //累计
        Integer totalCount = revisitMapper.singleUserSta(sysHeader.getUserId(), type, getSqlDatePart(-1));
        Map<String, Integer> map = new HashMap<>();
        map.put("today_count", todayCount);
        map.put("total_count", totalCount);
        return new ResponseEntity<>(new Result<>(map, errors), HttpStatus.OK);
    }

    /**
     * 党建品牌
     *
     * @param headers
     * @return
     */
    public ResponseEntity<Result<?>> brand(HttpHeaders headers) {
        int overviewType = getOverviewType(headers);
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        OverviewBrandVo brandVo = new OverviewBrandVo();
        brandVo.setType(overviewType);
        if (overviewType == 2 || overviewType == 1) {
            //阵地查询
            Query query = new Query();
            query.addCriteria(Criteria.where("regionId").is(sysHeader.getRegionId()).and("orgId").in(sysHeader.getOid()));
            long reFront = mongoTemplate.count(query, PartyPositions.class);
            brandVo.setFront(reFront);
            //品牌查询
            Query queryBrand = new Query();
            queryBrand.addCriteria(Criteria.where("orgId").in(sysHeader.getOid()));
            long reBrand = mongoTemplate.count(queryBrand, PartyBrandBase.class);
            brandVo.setBranch(reBrand);
            return new ResponseEntity<>(new Result<>(brandVo, errors), HttpStatus.OK);
        }
        if (overviewType == 3) {
            log.info("如果是支部,现在直接调用的接口");
        }
        return null;
    }

    //查看组织生活统计细项
    public ResponseEntity<Result<?>> orgLifeOverviewActivityDetail(HttpHeaders headers, Integer type, Long orgId, Integer pageNum, Integer pageSize) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        orgId = (orgId == null ? header.getOid() : orgId);
        Long finalOrgId = orgId;
        int currentQuarterly = DateUtils.getCurrentQuarterly();
        String startTime = DateUtils.getCurrentQuarterlyStartTime(currentQuarterly);
        String endTime = DateUtils.getCurrentQuarterlyEndTime(currentQuarterly);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String dateMonth = LocalDate.now().format(formatter);
        Page<String> page = null;
        switch (type) {
            case 1:
            case 4:
                page = PageHelper.startPage(pageNum, pageSize).doSelectPage(
                        () -> statisticsMapper.queryOrgActivityDetailQuart(finalOrgId, type, startTime, endTime));
                break;
            case 3:
                page = PageHelper.startPage(pageNum, pageSize).doSelectPage(
                        () -> statisticsMapper.queryOrgActivityDetailMonth3(finalOrgId, type, dateMonth));
                break;
            case 2:
            case 5:
                page = PageHelper.startPage(pageNum, pageSize).doSelectPage(
                        () -> statisticsMapper.queryOrgActivityDetailMonth(finalOrgId, type, dateMonth));
                break;
            case 6:
                page = PageHelper.startPage(pageNum, pageSize).doSelectPage(
                        () -> statisticsMapper.queryOrgLifeYear(finalOrgId, type, LocalDate.now().getYear()));
                break;
            default:
                page = null;
        }
        return new ResponseEntity<>(new Result<>(page, errors), HttpStatus.OK);
    }

    //查看组织生活统计细项-全状态
    public ResponseEntity<Result<?>> orgLifeOverviewActivityDetailAll(HttpHeaders headers, Integer type, Long orgId, Integer pageNum, Integer pageSize) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        orgId = (orgId == null ? header.getOid() : orgId);
        Long finalOrgId = orgId;
        int currentQuarterly = DateUtils.getCurrentQuarterly();
        String startTime = DateUtils.getCurrentQuarterlyStartTime(currentQuarterly);
        String endTime = DateUtils.getCurrentQuarterlyEndTime(currentQuarterly);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String dateMonth = LocalDate.now().format(formatter);
        Page<String> page = null;
        switch (type) {
            case 1:
            case 4:
                page = PageHelper.startPage(pageNum, pageSize).doSelectPage(
                        () -> statisticsMapper.queryOrgActivityDetailQuartAll(finalOrgId, type, startTime, endTime));
                break;
            case 3:
                page = PageHelper.startPage(pageNum, pageSize).doSelectPage(
                        () -> statisticsMapper.queryOrgActivityDetailMonth3All(finalOrgId, type, dateMonth));
                break;
            case 2:
            case 5:
                page = PageHelper.startPage(pageNum, pageSize).doSelectPage(
                        () -> statisticsMapper.queryOrgActivityDetailMonthAll(finalOrgId, type, dateMonth));
                break;
            case 6:
                page = PageHelper.startPage(pageNum, pageSize).doSelectPage(
                        () -> statisticsMapper.queryOrgLifeYearAll(finalOrgId, type, LocalDate.now().getYear(), header.getRegionId()));
                break;
            default:
                page = null;
        }
        return new ResponseEntity<>(new Result<>(page, errors), HttpStatus.OK);
    }

    /**
     * 通过组织id获取当年的谈心谈话
     *
     * @param orgId         组织id
     * @param excludeOrgIds 需要排除的组织id
     * @return 统计结果
     */
    public int getStaOverviewMeetingTalk(Long orgId, List<Long> excludeOrgIds) {
        return statisticsMapper.staOverviewMeetingTalkAndExclude(orgId, excludeOrgIds);
    }

    /**
     * 获取年份获取评议和应评议的数据
     *
     * @param year          查询年费，yyyy
     * @param regionId      region id
     * @param orgId         查询的组织id
     * @param excludeOrgIds 排除组织id
     * @return map -> 列 : 值
     */
    public Map<String, Object> countOrgMeetingComment(int year, long regionId, long orgId, List<Long> excludeOrgIds) {
        return meetingMapper.countOrgMeetingComment(
                year, regionId, orgId, excludeOrgIds
        );
    }

    /**
     * 序列分布
     *
     * @param headers headers
     * @return OverviewStandardVo
     */
    public OverviewStandardVo sequenceDistributed(HttpHeaders headers) {
        OverviewStandardVo overviewStandardVo = new OverviewStandardVo();
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        int overviewType = getOverviewType(headers);
        overviewStandardVo.setType(overviewType);
        //OverviewStandardVo
        List<KeyValVo> keyValVos = userMapper.orgOverviewSequenceDistributed(sysHeader.getOid(), excludeOrgIds);
        overviewStandardVo.setTotal(keyValVos.stream().mapToInt(KeyValVo::getValue).sum());
        overviewStandardVo.setListKeyValue(keyValVos);
        return overviewStandardVo;
    }

    /**
     * 获取单位的经纬度
     * 因为调用方本来就要循环处理，因此不处理格式了
     * <p>
     * 数据会缓存5分钟（因为地图会多次调用）
     *
     * @param excludeOrgIds 需要排除的组织
     * @return 封装对象 list of map  unit_id / lng / lat
     */
    public List<Map<String, Object>> unitLngAndLat(List<Long> excludeOrgIds) {
        List<Map<String, Object>> list = null;
        try {
            if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(Constants.DATAV_UNIT_LNG_LAT_CACHE))) {
                list = Init.INSTANCE.getObjectMapper().readValue(
                        stringRedisTemplate.opsForValue().get(Constants.DATAV_UNIT_LNG_LAT_CACHE),
                        new TypeReference<List<Map<String, Object>>>() {
                        }
                );
            }
        } catch (Exception e) {
            log.error("unitLngAndLat 反序列化对象失败->" + e.getMessage(), e);
        }
        if (list == null) {
            list = userMapper.unitLngAndLat(excludeOrgIds);
            if (list != null) {
                try {
                    stringRedisTemplate.opsForValue().set(
                            Constants.DATAV_UNIT_LNG_LAT_CACHE,
                            Init.INSTANCE.getObjectMapper().writeValueAsString(list),
                            5,
                            TimeUnit.MINUTES
                    );
                } catch (JsonProcessingException e) {
                    log.error("unitLngAndLat 序列化对象失败->" + e.getMessage(), e);
                }
            }
        }
        return list;
    }

    public Double totalPayThisYear(int year) {
        // 本年最后一天
        LocalDate lastDayOfYear = LocalDate.of(year, 12, 31);
        // 本年第一天第一分第一秒
        LocalDateTime lastSecondOfYear = lastDayOfYear.atTime(23, 59, 59);
        // 去年最后一天最后一分最后一秒
        LocalDateTime lastSecondOfLastYear = lastDayOfYear.minusYears(1).atTime(23, 59, 59);
        return ppmdStatisticsMapper.totalPayByDate(lastSecondOfLastYear, lastSecondOfYear);
    }

    public Long totalEcpOrg() {
        return ecpOrgMapper.totalEcpOrg();
    }
}
