package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.db.meeting.MeetingEntity;
import com.goodsogood.ows.model.db.user.BranchHighlightEntity;
import com.goodsogood.ows.model.mongodb.PartyBrandBase;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.user.OrgExpandVO;
import com.goodsogood.ows.model.vo.user.OrgInfoForm;
import com.goodsogood.ows.model.vo.user.UserExpandInfoForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 支部之窗远程调用
 */
@Service
@Log4j2
public class WindowThirdService {
    private final RestTemplate restTemplate;

    private final TogServicesConfig togServicesConf;

    @Autowired
    public WindowThirdService(RestTemplate restTemplate, TogServicesConfig togServicesConf) {
        this.restTemplate = restTemplate;
        this.togServicesConf = togServicesConf;
    }

    /**
     * 查询组织届次信息和人数
     * name 用户姓名
     * phone  用户手机号
     *
     * <AUTHOR>
     */
    public OrgExpandVO getOrgPeriodInfo(HttpHeaders headers, Long orgId) {
        OrgExpandVO re = null;
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        int count = 0;
        do {
            try {
                re = RemoteApiHelper.get(restTemplate, String.format("http://%s/org-expand/get-org-period-info?org_id=%s",
                        togServicesConf.getUserCenter(), orgId), headers, new TypeReference<Result<OrgExpandVO>>() {
                });
            } catch (Exception e) {
                log.error("查询组织届次信息和人数！ orgId={} 第{}次调用", orgId, (count + 1), e);
            }
            count++;
        } while (null == re && count < 3);
        log.debug("查询组织届次信息和人数:查询 orgId={}   结果 res ={} 调用数{}次", orgId, re, count);
        return re;
    }

    /**
     * 查询组织信息
     * @param headers
     * @param orgId
     * @return
     */
    public Map<String,Object> getOrgProfile(HttpHeaders headers, Long orgId) {
        Map<String,Object> re = null;
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        int count = 0;
        do {
            try {
                re = RemoteApiHelper.get(restTemplate, String.format("http://%s/org/info?org_id=%s&tree_type=2",
                        togServicesConf.getUserCenter(), orgId), headers, new TypeReference<Result<Map<String,Object>>>() {
                });
            } catch (Exception e) {
                log.error("查询组织信息！ orgId={} 第{}次调用", orgId, (count + 1), e);
            }
            count++;
        } while (null == re && count < 3);
        log.debug("查询组织信息:查询 orgId={}   结果 res ={} 调用数{}次", orgId, re, count);
        return re;
    }

    public List<UserExpandInfoForm> getUserList(Long orgId, HttpHeaders headers) {
        List<UserExpandInfoForm> re = new ArrayList<>();
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        int count = 0;
        do {
            try {
                re = RemoteApiHelper.get(restTemplate, String.format("http://%s/user-expand/get-user-list-by-org?org_id=%s",
                        togServicesConf.getUserCenter(), orgId), headers, new TypeReference<Result<List<UserExpandInfoForm>>>() {
                });
            } catch (Exception e) {
                log.error("查询组织信息！ orgId={} 第{}次调用", orgId, (count + 1), e);
            }
            count++;
        } while (null == re && count < 3);
        log.debug("查询组织信息:查询 orgId={}   结果 res ={} 调用数{}次", orgId, re, count);
        return re;
    }

    public Page<MeetingEntity> getMeetingList(HttpHeaders headers, Long regionId, Long orgId, LocalDate startTime,LocalDate endTime,Integer pageSize) {
        Page<MeetingEntity> re = null;
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        int count = 0;
        do {
            try {
                re = RemoteApiHelper.get(restTemplate, String.format("http://%s/meeting/review?org_ids=%s&start_time=%s&end_time=%s&page_size=%s",
                        togServicesConf.getMeeting(), orgId,startTime,endTime,pageSize), headers, new TypeReference<Result<Page<MeetingEntity>>>() {
                });
            } catch (Exception e) {
                log.error("查询组织信息！ orgId={} 第{}次调用", orgId, (count + 1), e);
            }
            count++;
        } while (null == re && count < 3);
        log.debug("查询组织信息:查询 orgId={}   结果 res ={} 调用数{}次", orgId, re, count);
        return re;
    }

    public PartyBrandBase getBrandList(Long regionId, Long orgId,HttpHeaders headers) {
        PartyBrandBase re = null;
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        int count = 0;
        do {
            try {
                re = RemoteApiHelper.get(restTemplate, String.format("http://%s/partyBrand/selectSelf?org_id=%s",
                        togServicesConf.getUserCenter(), orgId), headers, new TypeReference<Result<PartyBrandBase>>() {
                });
            } catch (Exception e) {
                log.error("查询组织信息！ orgId={} 第{}次调用", orgId, (count + 1), e);
            }
            count++;
        } while (null == re && count < 3);
        log.debug("查询组织信息:查询 orgId={}   结果 res ={} 调用数{}次", orgId, re, count);
        return re;
    }

    /**
     *
     * @param orgId
     * @param type 1-风采  2-发展史
     * @param page
     * @param pageSize
     * @param headers
     * @return
     */
    public List<BranchHighlightEntity> getOrgHighligh(Long orgId, Integer type, Integer page, Integer pageSize, HttpHeaders headers) {
        List<BranchHighlightEntity> re = null;
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        int count = 0;
        do {
            try {
                re = RemoteApiHelper.get(restTemplate, String.format("http://%s/person-center/org/highlight/list?org_id=%s&type=%s&page=%s&page_size=%s",
                        togServicesConf.getUserCenter(), orgId,type,page,pageSize), headers, new TypeReference<Result<List<BranchHighlightEntity>>>() {
                });
            } catch (Exception e) {
                log.error("查询组织信息！ orgId={} 第{}次调用", orgId, (count + 1), e);
            }
            count++;
        } while (null == re && count < 3);
        log.debug("查询组织信息:查询 orgId={}   结果 res ={} 调用数{}次", orgId, re, count);
        return re;
    }
}
