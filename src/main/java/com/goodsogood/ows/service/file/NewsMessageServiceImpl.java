package com.goodsogood.ows.service.file;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.common.MessageCommon;
import com.goodsogood.ows.common.MessageEnum;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.mongodb.dss.MeetingCompletionMap;
import com.goodsogood.ows.model.vo.MessageVO;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.news.NewsAppForm;
import com.goodsogood.ows.model.vo.news.NewsImgForm;
import com.goodsogood.ows.service.IMessageService;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/10/12
 * 移动端首页获取党内资讯新闻信息
 */
@Component(MessageCommon.NEWS)
@Log4j2
public class NewsMessageServiceImpl implements IMessageService {

    private static final List<String> NEWS_COLUMN_NAMES = Arrays.asList("时政要闻", "行业资讯", "渝烟新闻", "单位动态");
    private static final String QUERY_URL = "http://%s/news/find-list-app?page=%s&pagesize=%s&column_name=%s";
    private final RestTemplate restTemplate;


    @Value("${tog-services.news}")
    private String newsCenter;

    @Autowired
    public NewsMessageServiceImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @NotNull
    @Override
    /**
     * 烟草获取党内资讯
     */
    public List<MessageVO> generateMessage(long regionId, long rootId, Integer page, Integer pageSize) {
        List<MessageVO> result = new ArrayList<>(pageSize);
        List<NewsAppForm> convertList = new ArrayList<>(pageSize * NEWS_COLUMN_NAMES.size());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("_region_id", String.valueOf(regionId));
        httpHeaders.set("_oid", String.valueOf(rootId));
        NEWS_COLUMN_NAMES.forEach(x -> {
            List<NewsAppForm> newsAppForms = remoteRequestFindNewsByColumnName(httpHeaders, page, pageSize, x);
            if (!CollectionUtils.isEmpty(newsAppForms)) {
                newsAppForms.forEach(y -> {
                    y.setNewsColumnName(x);
                    convertList.add(y);
                });
            }
        });
        convertList.stream()
                .sorted((o2, o1) -> o1.getOrderNumber() > o2.getOrderNumber() ? -1 : 1)
                .limit(pageSize)
                .forEach(x -> {
                    MessageVO messageVO = new MessageVO();
                    result.add(messageVO);
                    if (!CollectionUtils.isEmpty(x.getImgs())) {
                        messageVO.setFile(x.getImgs().stream().map(NewsImgForm::getUrl).collect(Collectors.toList()));
                    }
                    messageVO.setText(x.getTitle());
                    messageVO.setSource(x.getSource());
                    messageVO.setTime(simpleDateFormat.format(x.getCreateTime()));
                    messageVO.setType(MessageEnum.NEWS.getType());
                    messageVO.setTypeName(MessageEnum.NEWS.getTypeName());
                    messageVO.setColumnName(x.getNewsColumnName());
                    Map<String, Object> map = new HashMap<>(1);
                    map.put("news_id", x.getNewsId());
                    messageVO.setExtend(map);
                });
        return result;
    }


    private List<NewsAppForm> remoteRequestFindNewsByColumnName(HttpHeaders httpHeaders, Integer page, Integer pageSize, String columnName) {
        List<NewsAppForm> list = new ArrayList<>(pageSize);
        String queryUrl = String.format(QUERY_URL, newsCenter, page, pageSize, columnName);
        try {
            List<NewsAppForm> newsAppForms = RemoteApiHelper.get(this.restTemplate, queryUrl, httpHeaders,
                    new TypeReference<Result<List<NewsAppForm>>>() {
                    });
            if (!CollectionUtils.isEmpty(newsAppForms)) {
                list.addAll(newsAppForms);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("NewsMessageServiceImpl.remoteRequestFindNewsByColumnName()错误:[{}]", e.getMessage(), e);
        }

        return list;
    }
}

