package com.goodsogood.ows.service.mongodb;

import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.model.vo.mongodb.QueryMongoInfoForm;
import com.goodsogood.ows.utils.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.index.IndexInfo;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/28
 * Description: 查询mongoDB 中的数据
 */
@Service
public class QueryMongoInfoService {

    private final MyMongoTemplate mongoTemplate;

    @Autowired
    public QueryMongoInfoService(MyMongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public Object queryMongoInfo(QueryMongoInfoForm queryMongoInfoForm) throws Exception {
        Integer queryType = queryMongoInfoForm.getType();

        switch (queryType) {
            /* 查询信息 */
            case 1:
                return getMongoDetailInfo(queryMongoInfoForm);
            /* 查询条数 */
            case 2:
                return getMongoCountInfo(queryMongoInfoForm);
            /* 获取索引数 */
            case 3:
                return getMongoIndexInfo(queryMongoInfoForm);
            default:
                throw new IllegalStateException("Unexpected value: " + queryType);
        }
    }

    /**
     * 获取详细信息
     */
    private List<?> getMongoDetailInfo(QueryMongoInfoForm queryMongoInfoForm) throws Exception {
        Object o1 = Class.forName(queryMongoInfoForm.getClassName()).newInstance();
        Query query = new Query();
        getMongoQuery(queryMongoInfoForm, query);
        return mongoTemplate.find(query, o1.getClass(), queryMongoInfoForm.getCollection());
    }

    /**
     * 获取条数
     */
    private Long getMongoCountInfo(QueryMongoInfoForm queryMongoInfoForm) throws Exception {
        Query query = new Query();
        getMongoQuery(queryMongoInfoForm, query);
        return mongoTemplate.count(query,queryMongoInfoForm.getCollection());
    }

    /**
     * 获取collection 表索引
     */
    private List<IndexInfo> getMongoIndexInfo(QueryMongoInfoForm queryMongoInfoForm) throws Exception {

        IndexOperations indexOperations = mongoTemplate.indexOps(Class.forName(queryMongoInfoForm.getClassName()));
        if (indexOperations != null) {
            return indexOperations.getIndexInfo();
        } else {
            return null;
        }
    }

    private void getMongoQuery(QueryMongoInfoForm queryMongoInfoForm, Query query) {
        /* 新增Limit */
        query.limit(queryMongoInfoForm.getLimit());

        /* 新增Skip */
        if (queryMongoInfoForm.getSkip() != null) {
            query.skip(queryMongoInfoForm.getSkip());
        }

        /* 新增Include */
        if (!CollectionUtils.isEmpty(queryMongoInfoForm.getQueryResultParam())) {
            queryMongoInfoForm.getQueryResultParam().forEach(x -> query.fields().include(x));
        }

        /* 新增参数 */
        if (MapUtils.isNotEmpty(queryMongoInfoForm.getQueryCondition())) {
            Criteria criteria = new Criteria();
            queryMongoInfoForm.getQueryCondition().forEach((k, v) -> addCriteria(criteria, k, v));
            query.addCriteria(criteria);
        }
    }

    private Criteria addCriteria(Criteria criteria, String key, QueryMongoInfoForm.QueryConditionMap queryConditionMap) {
        String command = queryConditionMap.getCommand();
        Object value = queryConditionMap.getValue();
        switch (command) {
            case "is":
                criteria = criteria != null ? criteria.and(key).is(value) : Criteria.where(key).is(value);
                break;
            case "gt":
                criteria = criteria != null ? criteria.and(key).gt(value) : Criteria.where(key).gt(value);
                break;
            case "gte":
                criteria = criteria != null ? criteria.and(key).gte(value) : Criteria.where(key).gte(value);
                break;
            case "lt":
                criteria = criteria != null ? criteria.and(key).lt(value) : Criteria.where(key).lt(value);
                break;
            case "lte":
                criteria = criteria != null ? criteria.and(key).lte(value) : Criteria.where(key).lte(value);
                break;
            default:
                break;
        }
        return criteria;
    }
}
