package com.goodsogood.ows.service.mongodb;

import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.configuration.ScoreMasterIndexProperty;
import com.goodsogood.ows.model.mongodb.ScoreInfo;
import com.goodsogood.ows.model.mongodb.report.UserScoreDonateInfo;
import com.goodsogood.ows.model.mongodb.report.UserScoreStudyInfo;
import com.goodsogood.ows.model.mongodb.report.UserScoreStudyReport;
import com.goodsogood.ows.model.vo.activity.Donate;
import com.goodsogood.ows.model.vo.score.Books;
import com.goodsogood.ows.model.vo.score.Poverty;
import com.goodsogood.ows.model.vo.score.Study;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.NumberUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/11/22
 * Description: 根据uid 在mongo 获取score 相关数据
 */
@Service
@Log4j2
public class UserScoreReportService {

    private final MyMongoTemplate mongoTemplate;
    private final ScoreMasterIndexProperty scoreMasterIndexProperty;

    @Autowired
    public UserScoreReportService(MyMongoTemplate mongoTemplate, ScoreMasterIndexProperty scoreMasterIndexProperty) {
        this.mongoTemplate = mongoTemplate;
        this.scoreMasterIndexProperty = scoreMasterIndexProperty;
    }

    public UserScoreStudyReport getUserStudyByMonth(Long regionId,Long userId, String queryTime) {
        Assert.notNull(userId, " userId must not be null ");
        Assert.notNull(queryTime, " queryTime must not be null ");

        Query query = Query.query(Criteria.where("userId").is(userId));
        /* 获得userId 所有数据 */
        List<ScoreInfo> scoreInfos = mongoTemplate.find(query, ScoreInfo.class);
        if (CollectionUtils.isEmpty(scoreInfos)) {
            return null;
        }
        return getUserStudyByMonth(regionId,userId, queryTime, scoreInfos);
    }

    public UserScoreStudyReport getUserStudyByMonth(Long regionId,Long userId, String queryTime, List<ScoreInfo> scoreInfos) {
        Assert.notNull(userId, " userId must not be null ");
        Assert.notNull(queryTime, " queryTime must not be null ");
        if (CollectionUtils.isEmpty(scoreInfos)) {
            return null;
        }

        UserScoreStudyReport initEntity = UserScoreStudyReport.getInitEntity();

        /* 遍历每个月 */
        scoreInfos.stream().filter(x -> DateUtils.isInclude(x.getQueryTime(), queryTime)).forEach(x -> {
            List<Study> studys = !CollectionUtils.isEmpty(x.getStudys())?x.getStudys().stream().filter(item -> item.getRegionId().equals(regionId)).collect(Collectors.toList()):null;
            List<Books> books = !CollectionUtils.isEmpty(x.getBooks())?x.getBooks().stream().filter(item -> item.getRegionId().equals(regionId)).collect(Collectors.toList()):null;
            List<Donate> donates = !CollectionUtils.isEmpty(x.getDonates())?x.getDonates().stream().filter(item -> item.getRegionId().equals(regionId)).collect(Collectors.toList()):null;
            List<Poverty> povertys = !CollectionUtils.isEmpty(x.getPovertys())?x.getPovertys().stream().filter(item -> item.getRegionId().equals(regionId)).collect(Collectors.toList()):null;
            /* 学习次数 */
            Integer studyNum = !CollectionUtils.isEmpty(studys) ? studys.size() : 0;
            /* 学习总积分 */
            Long studyScoreNum = !CollectionUtils.isEmpty(studys) ? studys.stream().map(Study::getScore).mapToLong(Long::valueOf).sum() : 0;
            /* 兑换的图书 */
            Long commodityCount = !CollectionUtils.isEmpty(books) ? books.stream().filter(book -> !"积分消费退款".equals(book.getRemark())).map(Books::getCommodityCount).mapToLong(Long::valueOf).sum() : 0;
            /* 捐赠次数 */
            Integer donateNum = Math.toIntExact(!CollectionUtils.isEmpty(donates) ? donates.stream().filter(y -> scoreMasterIndexProperty.getDonateId().equals(y.getActivityId())).count() : 0);
            /* 捐赠积分 */
            Long donateScores = !CollectionUtils.isEmpty(donates) ? donates.stream().filter(y -> scoreMasterIndexProperty.getDonateId().equals(y.getActivityId())).map(Donate::getDonateNum).mapToLong(Long::valueOf).sum() : 0;
            /* 扶贫商城交易单数 */
            Integer povertyNum = !CollectionUtils.isEmpty(povertys) ? povertys.size() : 0;
            /* 扶贫商城交易金额 */
            BigDecimal povertyMoneyBigDecimal = BigDecimal.valueOf(!CollectionUtils.isEmpty(povertys) ? povertys.stream().map(Poverty::getTotalPrice).mapToLong(Long::valueOf).sum() : 0);
            povertyMoneyBigDecimal = NumberUtils.divide(povertyMoneyBigDecimal, 100, 2, RoundingMode.DOWN);
            /* 购买公益宝贝件数 */
            Integer sizeDonate = !CollectionUtils.isEmpty(povertys) ? povertys.stream().map(Poverty::getSizeDonate).mapToInt(Integer::valueOf).sum() : 0;
            /* 购买公益宝贝金额 */
            BigDecimal donationBigDecimal = BigDecimal.valueOf(!CollectionUtils.isEmpty(povertys) ? povertys.stream().map(Poverty::getDonation).mapToLong(Long::valueOf).sum() : 0);
            donationBigDecimal = NumberUtils.divide(donationBigDecimal, 100, 2, RoundingMode.DOWN);

            /* 汇总每个月数据 */
            appendUserScoreStudyData(studyNum, studyScoreNum, commodityCount, initEntity.getStudyTotal());
            appendUserScoreDonateData(donateNum, donateScores, povertyMoneyBigDecimal, povertyNum, sizeDonate, donationBigDecimal, initEntity.getDonateTotal());

            /* 如果为指定获取的月份数据 */
            if (queryTime.equals(x.getQueryTime())) {

                /* 封装查询月党员学习信息 */
                appendUserScoreStudyData(studyNum, studyScoreNum, commodityCount, initEntity.getStudyDetail());

                /* 封装查询月扶贫信息 */
                appendUserScoreDonateData(donateNum, donateScores, povertyMoneyBigDecimal, povertyNum, sizeDonate, donationBigDecimal, initEntity.getDonateDetail());
            }
        });

        return initEntity;
    }

    public List<ScoreInfo> getScoreUserInfo(Long userId) {
        Assert.notNull(userId, " userId must not be null ");
        Query query = Query.query(Criteria.where("userId").is(userId));
        /* 获得userId 所有数据 */
        return mongoTemplate.find(query, ScoreInfo.class);
    }

    private void appendUserScoreStudyData(Integer studyNum, Long studyScoreNum, Long commodityCount, UserScoreStudyInfo studyDetail) {
        studyDetail.setStudyNum(studyDetail.getStudyNum() + studyNum);
        studyDetail.setReceiveScoreNum(studyDetail.getReceiveScoreNum() + studyScoreNum);
        studyDetail.setExchangeBookNum(studyDetail.getExchangeBookNum() + commodityCount);
    }

    private void appendUserScoreDonateData(Integer donateNum, Long donateScores, BigDecimal povertyMoney, Integer povertyNum, Integer sizeDonate, BigDecimal donation, UserScoreDonateInfo donateDetail) {
        donateDetail.setDonateNum(donateDetail.getDonateNum() + donateNum);
        donateDetail.setDonateScore(donateDetail.getDonateScore() + donateScores);
        donateDetail.setPovertyMoney(povertyMoney.add(BigDecimal.valueOf(donateDetail.getPovertyMoney())).doubleValue());
        donateDetail.setPovertyNum(donateDetail.getPovertyNum() + povertyNum);
        donateDetail.setCharityNum(donateDetail.getCharityNum() + sizeDonate);
        donateDetail.setCharityMoney(donation.add(BigDecimal.valueOf(donateDetail.getCharityMoney())).doubleValue());
    }
}
