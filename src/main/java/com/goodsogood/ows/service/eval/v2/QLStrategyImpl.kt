package com.goodsogood.ows.service.eval.v2

import cn.hutool.core.collection.CollectionUtil
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.goodsogood.ows.common.Constants
import com.goodsogood.ows.common.redisUtil.RedisLockUtil
import com.goodsogood.ows.configuration.MeetingTypeConfig
import com.goodsogood.ows.configuration.OrgTypeConfig
import com.goodsogood.ows.configuration.TagConfig
import com.goodsogood.ows.mapper.activity.RevisitMapper
import com.goodsogood.ows.mapper.meeting.MeetingMapper
import com.goodsogood.ows.mapper.ppmd.PayLogMapper
import com.goodsogood.ows.mapper.user.OrgGroupMapper
import com.goodsogood.ows.mapper.user.OrgPeriodMapper
import com.goodsogood.ows.mapper.user.OrganizationMapper
import com.goodsogood.ows.mapper.user.UserMapper
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.service.adb.QLService
import com.goodsogood.ows.utils.DateUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.BeansException
import org.springframework.data.domain.PageRequest
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.stereotype.Service
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.math.abs

/**
 * 获取轻流上报的得分项实现类
 * <AUTHOR>
 * @date 2023/11/26
 * @description class
 */
@Service("qlStrategyImpl")
class QLStrategyImpl(
    val qlService: QLService,
    val redisTemplate: StringRedisTemplate,
    val objectMapper: ObjectMapper,
) : Strategy {
    private val log = LoggerFactory.getLogger(QLStrategyImpl::class.java)
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        log.debug(
            "考核2.0,轻流获取数据的时候->org=>{},unit=>{},metricEntity=>{},year=>{},params=>{}",
            org,
            unit,
            metricEntity,
            year,
            params
        )
        val sqlName = if (metricEntity.scoreType == 1) {
            if (metricEntity.type?.toInt() == 2) {
                "query001" // 2、线下指标（考评组清流读取）
            } else {
                "query003" // 4.线下指标(党建处清流读取)
            }
        } else {
            "query004" // 004 考核组加分
        }
        // 1.获取轻流数据
        val data = if (redisTemplate.hasKey(Constants.METRIC_TASK_QL_CACHE_KEY + sqlName)) {
            log.debug("考核2.0,轻流获取数据的时候->从缓存中获取")
            // 1.1 缓存中有的情况，直接从缓存中获取
            objectMapper.readValue(
                redisTemplate.opsForValue().get(Constants.METRIC_TASK_QL_CACHE_KEY + sqlName),
                object : TypeReference<List<SortedMap<String, Any?>>>() {}
            )
        } else {
            log.debug("考核2.0,轻流获取数据的时候->从ADB中获取")
            // 1.2 缓存中没有的情况，通过ADB获取,
            var n = 3;// 等3次（1次10秒）
            var data: List<SortedMap<String, Any?>>? = null
            while (n > 0) {
                val tempData = try {
                    if (redisTemplate.hasKey(Constants.METRIC_TASK_QL_CACHE_KEY + sqlName)) {
                        objectMapper.readValue(
                            redisTemplate.opsForValue().get(Constants.METRIC_TASK_QL_CACHE_KEY + sqlName),
                            object : TypeReference<List<SortedMap<String, Any?>>>() {}
                        )
                    } else if (RedisLockUtil.tryGetDistributedLock(
                            redisTemplate,
                            Constants.METRIC_TASK_QL_CACHE_KEY + sqlName + "LOCK",
                            Constants.METRIC_TASK_QL_CACHE_KEY + sqlName + "LOCK",
                            3600000
                        )
                    ) {
                        // 从数据库中获取
                        val temp = qlService.findAll(sqlName, listOf(), PageRequest.of(0, 99999)).toList()
                        log.debug("考核2.0,轻流获取数据的时候->从ADB中获取到的数据为->{}", temp)
                        // 写入缓存
                        redisTemplate.opsForValue()
                            .set(
                                Constants.METRIC_TASK_QL_CACHE_KEY + sqlName,
                                objectMapper.writeValueAsString(temp),
                                2,
                                TimeUnit.HOURS
                            )
                        temp
                    } else {
                        null
                    }
                } catch (e: Exception) {
                    log.error("考核2.0,轻流获取数据的时候发生错误->" + e.localizedMessage, e)
                    null
                } finally {
                    RedisLockUtil.releaseDistributedLock(
                        redisTemplate,
                        Constants.METRIC_TASK_QL_CACHE_KEY + sqlName + "LOCK",
                        Constants.METRIC_TASK_QL_CACHE_KEY + sqlName + "LOCK"
                    )
                }
                n--
                if (tempData == null) {
                    Thread.sleep(10000)
                } else {
                    data = tempData
                    break
                }
            }
            data
        }
        if (data == null) {
            log.error("考核2.0,轻流获取数据的时候发生错误-> data is null")
            return 0.0
        }
        log.debug("考核2.0,轻流获取数据的时候->data=> {}", data)
        // 1.1 获取当前单位的数据
        val unitData = data.firstOrNull { it["评价单位"] == unit.name }
        if (unitData == null) {
            log.warn("考核2.0,轻流获取数据的时候,获取评价单位：${unit.name}-> unitData is null")
            return 0.0
        }
        // 2.计算得分
        // 2.1 需要计算的指标名称
        val name = metricEntity.name
        // 2.2 通过循环unitData，找到指标名称对应的列,map's value = name,然后获取下一列的值
        val index = unitData.values.indexOf(name)
        if (index < 0) {
            log.warn("考核2.0,轻流获取数据的时候,指标：${name}未查询到数据")
            return 0.0
        }
        // 2.2.1 反向通过index找到对应的列名 map's key (指标_n)
        val key = unitData.keys.toList()[index]
        log.debug("考核2.0,轻流获取数据的时候,指标：[${name}]的列名为->${key}")
        if (key.split("_").size < 2) {
            log.warn("考核2.0,轻流获取数据的时候,指标：${name}列名不符合规范")
            return 0.0
        }
        // 2.3 获取对应的扣分情况 （列名为：加扣分情况_n）
        val scoreKey = "加扣分情况_" + key.split("_")[1]
        val score = try {
            unitData.getOrDefault(scoreKey, "0.0").toString().toDouble()
        } catch (ce: ClassCastException) {
            log.error("考核2.0,轻流获取数据的时候,指标：${name}的扣分情况不是数字")
            0.0
        }
        log.debug("考核2.0,轻流获取数据的时候,单位：${unit.organizationId}=${unit.shortName},指标：[${name}]的得分为->${score}")
        // 转换成正分
        return if (score < 0) {
            abs(score)
        } else {
            score
        }
    }
}


/**
 * 重温入党志愿书、重温入党誓词：党员每季度至少重温1次
 *   <AUTHOR>
 *   @date 2023/11/29
 *   考核对象 党员 按未完成人次扣分（考核周期计算第四季度） 每个人次扣0.01
 */
@Service("revisitStrategyImpl")
class RevisitStrategyImpl(
    val revisitMapper: RevisitMapper,
    val userMapper: UserMapper
) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        val quarter = DateUtils.getQuarter(Date())
        var countNumber = 0;
        return if (quarter.quarter == 4) {
            //如果组织下面没有用户信息 直接返回0
            val listUserInfo = userMapper.getAllUserInfoByOrgId(org.regionId, org.organizationId, year);
            if (CollectionUtil.isEmpty(listUserInfo)) {
                return 0.0
            }
            //得到用户Id
            val listUserIds = listUserInfo.map { it.userId }.toList()
            for (i in 1..4) {
                val quarterly = ("" + year + i).toInt()
                //重温入党志愿书
                val listUserIdPartyOne =
                    revisitMapper.getRevisitByQuarterlyAndOrgId(1, quarterly, org.organizationId, org.regionId)
                if (CollectionUtil.isNotEmpty(listUserIdPartyOne)) {
                    val subtract = listUserIds.subtract(listUserIdPartyOne.toSet())
                    if (subtract.isNotEmpty()) {
                        countNumber += subtract.size
                    }
                }
                //重温入党誓词
                val listUserIdPartyTwo =
                    revisitMapper.getRevisitByQuarterlyAndOrgId(2, quarterly, org.organizationId, org.regionId)
                if (CollectionUtil.isNotEmpty(listUserIdPartyTwo)) {
                    val subtract = listUserIds.subtract(listUserIdPartyTwo.toSet())
                    if (subtract.isNotEmpty()) {
                        countNumber += subtract.size
                    }
                }
            }
            return metricEntity.score?.times(countNumber) ?: 0.0
        } else {
            0.0
        }
    }
}

/**
 * 党组会、中心组学习无读书班记录直接扣分
 * 党组会、中心组学习无读书班 意思必须两个同时都有 就不扣分
 */
@Service("studyClassStrategyImpl")
class StudyClassStrategyImpl(
    val userMapper: UserMapper,
    val tagConfig: TagConfig,
    val meetingTypeConfig: MeetingTypeConfig,
    val meetingMapper: MeetingMapper

) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        //党组会记录
        val studyClassCount = meetingMapper.getStudyClassCount(
            meetingTypeConfig.partyMeeting,
            tagConfig.readClass,
            year,
            org.organizationId
        )
        //中心组学习
        val centerStudyCount = meetingMapper.getStudyClassCount(
            meetingTypeConfig.centerStudy,
            tagConfig.readClass,
            year,
            org.organizationId
        )
        // 任意一个会开了都不扣分
        if (studyClassCount == 0 && centerStudyCount == 0) {
            return metricEntity.score!!
        }
        return 0.0
    }
}


/**
 ** 补学了不扣分
 ** 预备党员、新的外部转入的党员不纳入考核
 */
@Service("theoryStudyClassStrategyImpl")
class TheoryStudyClassStrategyImpl(
    val adbJdbcTemplate: JdbcTemplate,
    val userMapper: UserMapper,
    val meetingMapper: MeetingMapper
) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        val listUserInfo =
            userMapper.getAllUserInfoByOrgIdExcludeDeveloper(org.regionId, org.organizationId, year)
        if (CollectionUtil.isEmpty(listUserInfo)) {
            return 0.0
        }
        val listUserIds = listUserInfo.map { it.userId }.toList()
        //得到理论学习时长超过32个小时用户
        val theoryStudyUser = meetingMapper.getTheoryStudyUser(listUserIds, year)
        //取差集 得到没有超过32小时候用户的id
        val subtract = listUserIds.subtract(theoryStudyUser.toSet())
        return subtract.size * metricEntity.score!!
    }
}


/**
 ** 规范设置党组织
 ** 支部50人以上、支部正式党员7人以上未设置支委会、支部20人以上未划分党小组，出现以上情况扣分
 * 当出现其中三种情况之一 就要扣分
 */
@Service("settingOrgInfoStrategyImpl")
class SettingOrgInfoStrategyImpl(
    val organizationMapper: OrganizationMapper,
    val meetingMapper: MeetingMapper,
    val orgGroupMapper: OrgGroupMapper,
    val orgPeriodMapper: OrgPeriodMapper,
    val orgTypeConfig: OrgTypeConfig,
) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        // 当前组织不是支部，不计算
        if (!orgTypeConfig.branchChild.contains(org.orgTypeChild)) {
            return 0.0
        }
        //支部20人以上未划分党小组
        val number = orgGroupMapper.findExistGroupNumber(org.regionId, org.organizationId)
        if (number > 0) {
            return metricEntity.score!!
        }
        //支部50人以上
        val orgUserExceedFifty = organizationMapper.getOrgUserExceedFifty(org.organizationId, org.regionId)
        if (orgUserExceedFifty > 0) {
            return metricEntity.score!!
        }
        //支部正式党员7人以上未设置支委会
        val noSettingBranchCommittee =
            orgPeriodMapper.getNoSettingBranchCommittee(org.organizationId, year, org.regionId)
        if (noSettingBranchCommittee > 0) {
            return metricEntity.score!!
        }
        return 0.0;
    }
}


/**
 *
 ** 规范开展组织换届选举
 ** 考核对象 机关党委、党总支、党支部、支委会、党小组 各党组织（含下级支部）
 * 按换届不及时（届次到期6个月内未进行换届）的党组织数量扣分
 */
@Service("settingOrgInfoPeriodStrategyImpl")
class SettingOrgInfoPeriodStrategyImpl(
    val orgPeriodMapper: OrgPeriodMapper
) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        val settingOrgInfoPeriodSCount = orgPeriodMapper.getSettingOrgInfoPeriodSCount(org.organizationId, year)
        if (settingOrgInfoPeriodSCount > 0) {
            return metricEntity.score!!
        }
        return 0.0
    }
}










