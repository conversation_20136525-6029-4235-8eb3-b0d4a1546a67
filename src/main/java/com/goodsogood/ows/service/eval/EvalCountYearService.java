package com.goodsogood.ows.service.eval;

import com.goodsogood.ows.mapper.eval.EvalCountYearMapper;
import com.goodsogood.ows.model.db.eval.EvalCountYearEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Log4j2
public class EvalCountYearService {

    private final EvalCountYearMapper evalCountYearMapper;

    @Autowired
    public EvalCountYearService(EvalCountYearMapper evalCountYearMapper) {
        this.evalCountYearMapper = evalCountYearMapper;
    }

    /**
     * 工委查询扣分的党委
     * @param year
     * @return
     */
    public List<Long> getDeductOrgList(Integer year) {
        Example example = new Example(EvalCountYearEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andLessThan("realDeduct",0);
        criteria.andEqualTo("year", year);
        example.selectProperties("orgId");
        List<EvalCountYearEntity> evalCountYearEntities = this.evalCountYearMapper.selectByExample(example);
        List<Long> orgIdList = evalCountYearEntities.stream().map(EvalCountYearEntity::getOrgId).collect(Collectors.toList());
        return orgIdList;
    }
}
