package com.goodsogood.ows.service.eval.dss;

import com.goodsogood.ows.annotions.Logging;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.mapper.eval.EvalCountYearMapper;
import com.goodsogood.ows.model.db.eval.EvalCountYearEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.dss.DssOrgChild;
import com.goodsogood.ows.model.mongodb.dss.GrandMap;
import com.goodsogood.ows.service.impl.DssIndexBuilder;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: 决策辅助首页
 *
 * <AUTHOR>
 * @version 2020/11/9 11:10
 */
@Service
@Log4j2
public class DssIndexFromEval implements DssIndexBuilder {

  private final EvalCountYearMapper evalCountYearMapper;
  private final OrgService orgService;
  private final OrgTypeConfig orgTypeConfig;

  public DssIndexFromEval(EvalCountYearMapper evalCountYearMapper, OrgService orgService, OrgTypeConfig orgTypeConfig) {
    this.evalCountYearMapper = evalCountYearMapper;
    this.orgService = orgService;
    this.orgTypeConfig = orgTypeConfig;
  }

  /**
   * 首页组织生活信息
   *
   * @param info 决策辅助首页实体类 info.rootId 顶级党组织ID info.year 生成年份
   * @return info
   */
  @Override
  @Logging
  public IndexInfo buildIndex(IndexInfo info) {
    // 初始化化信息
    List<GrandMap> list = new ArrayList<>();
    info.setEvalInfo(list);
    // 未扣分的组织信息
    GrandMap noDeduct = new GrandMap();
    noDeduct.setName("无扣分组织");
    noDeduct.setValue(0);
    noDeduct.setOrgList(new ArrayList<>());
    list.add(noDeduct);
    // 扣分的组织
    GrandMap deduct = new GrandMap();
    deduct.setName("其他党组织");
    deduct.setValue(0);
    deduct.setOrgList(new ArrayList<>());
    list.add(deduct);
    // 查询党委信息
    List<OrganizationEntity> organizationEntityList =
        orgService.getOfficeCommittee(info.getRootId());
    // 查询扣分的组织id
    Map<Long,EvalCountYearEntity> deductMap= getDeductOrgList(info.getYear());
    for(OrganizationEntity org : organizationEntityList){
      DssOrgChild dssOrgChild = DssOrgChild.builder().build();
      dssOrgChild.setOrgId(org.getOrganizationId());
      dssOrgChild.setName(org.getName());
      dssOrgChild.setShortName(org.getShortName());
      dssOrgChild.setOrgType(getOrgType(org.getOrgTypeChild()));
      // 默认扣0分
      dssOrgChild.setGrand(0.0);
      if(deductMap.containsKey(org.getOrganizationId())){
        // 被扣分了
        EvalCountYearEntity ce = deductMap.get(org.getOrganizationId());
        dssOrgChild.setGrand(Double.valueOf(ce.getRealDeduct().toString()));
        deduct.getOrgList().add(dssOrgChild);
      }else{
        // 没有扣分
        noDeduct.getOrgList().add(dssOrgChild);
      }
    }
    noDeduct.setValue(noDeduct.getOrgList().size());
    deduct.setValue(deduct.getOrgList().size());
    return info;
  }
  public Integer getOrgType(Integer orgTypeChild) {
    Integer orgType;
    if (this.orgTypeConfig.getCommunistAndGeneral().contains(orgTypeChild)) {
      orgType = 2;
    } else {
      orgType = 3;
    }
    return orgType;
  }
  /**
   * 工委查询扣分的党委
   *
   * @param year 年份
   * @return Map<Long,EvalCountYearEntity>
   */
  public Map<Long,EvalCountYearEntity> getDeductOrgList(Integer year) {
    Example example = new Example(EvalCountYearEntity.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andLessThan("realDeduct", 0);
    criteria.andEqualTo("year", year);
    List<EvalCountYearEntity> evalCountYearEntities =
        this.evalCountYearMapper.selectByExample(example);
    Map<Long,EvalCountYearEntity> map = new HashMap<>();
    if(CollectionUtils.isNotEmpty(evalCountYearEntities)){
      for(EvalCountYearEntity e: evalCountYearEntities){
        map.put(e.getOrgId(),e);
      }
    }
    return map;
  }
}
