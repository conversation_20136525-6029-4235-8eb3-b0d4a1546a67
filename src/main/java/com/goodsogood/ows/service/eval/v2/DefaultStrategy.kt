package com.goodsogood.ows.service.eval.v2

import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2023/12/7
 * @description class DefaultStrategy
 */
@Service("defaultStrategy")
class DefaultStrategy : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        return 0.0
    }
}