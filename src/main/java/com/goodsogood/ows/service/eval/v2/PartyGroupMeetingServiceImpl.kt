package com.goodsogood.ows.service.eval.v2

import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.service.meeting.EvalMeetingService
import org.springframework.stereotype.Service

/**
 * 每半年至少召开1次党建工作领导小组会研究党建工作，每半年计算1次，未开展则扣分
 * 单位对应的一级党组织
 * 会议类型“党建工作领导小组会”
 * <AUTHOR>
 * @date 2023/12/4
 * @description class PartyGroupMeetingServiceImpl
 */
@Service("partyGroupMeetingServiceImpl")
class PartyGroupMeetingServiceImpl(val evalMeetingService: EvalMeetingService) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId:String,
        params: Map<String, Any>
    ): Double {
        // 上半年
        val firstHalfYear = evalMeetingService.getMeetingListByTagAndType(
            "$year-01-01 00:00:00",
            "$year-06-30 23:59:59",
            org.organizationId,
            listOf(),
            listOf("党建工作领导小组会议")
        ).isNotEmpty()
        // 下半年
        val secondHalfYear = evalMeetingService.getMeetingListByTagAndType(
            "$year-07-01 00:00:00",
            "$year-12-31 23:59:59",
            org.organizationId,
            listOf(),
            listOf("党建工作领导小组会议")
        ).isNotEmpty()
        // 上下半年各开一次以上
        return (if (firstHalfYear) {
            0.0
        } else {
            metricEntity.score ?: 0.0
        }) + (if (secondHalfYear) {
            0.0
        } else {
            metricEntity.score ?: 0.0
        })
    }
}