package com.goodsogood.ows.service.eval

import com.goodsogood.ows.mapper.eval.EvalOrgScoreMapper
import com.goodsogood.ows.mapper.eval.EvalUnitScoreMapper
import com.goodsogood.ows.model.db.eval.v2.OrgScoreEntity
import com.goodsogood.ows.model.db.eval.v2.UnitScoreEntity
import org.springframework.stereotype.Service
import tk.mybatis.mapper.entity.Example
import java.time.LocalDateTime

@Service
class EvalOrgScoreService(var evalOrgScoreMapper: EvalOrgScoreMapper) {
    fun selectAll(): List<OrgScoreEntity> {
        return evalOrgScoreMapper.selectAll()
    }

    // 通过单位id查询
    fun selectByUnitId(unitId: Long): List<OrgScoreEntity> {
        return evalOrgScoreMapper.select(OrgScoreEntity().apply { this.unitId = unitId })
    }

    // 插入新记录
    fun insertSelective(orgScoreEntity: OrgScoreEntity): Int {
        return evalOrgScoreMapper.insertSelective(orgScoreEntity)
    }

    /**
     * 删除n天前的数据
     * @param year 年份
     * @param day 天数
     */
    fun deleteByDay(year: Int, day: Int): Int {
        // 创建Example 查询条件 createTime 为n天前的时间（含n天）
        val date = LocalDateTime.now().minusDays(day.toLong())
        val example = Example(OrgScoreEntity::class.java)
        example.createCriteria().andEqualTo("year", year).andLessThanOrEqualTo("createTime", date)
        return evalOrgScoreMapper.deleteByExample(example)
    }

    /**
     * 通过taskId获取积分结果
     */
    fun selectByTaskId(taskId: String): List<OrgScoreEntity> {
        return evalOrgScoreMapper.select(OrgScoreEntity().apply { this.taskId = taskId })
    }

    /**
     * 通过taskId获取积分结果，结果通过metric_class_id,metric_id,unit_id汇总
     */
    fun selectByTaskIdGroup(taskId: String): List<OrgScoreEntity> {
        return evalOrgScoreMapper.selectByTaskIdGroup(taskId)
    }
}

@Service
class EvalUnitScoreService(var evalUnitScoreMapper: EvalUnitScoreMapper) {
    fun selectAll(): List<UnitScoreEntity> {
        return evalUnitScoreMapper.selectAll()
    }

    fun deleteAll(): Long {
        return evalUnitScoreMapper.deleteAll()
    }

    fun deleteByYear(year: Int): Int {
        val example = Example(UnitScoreEntity::class.java)
        example.createCriteria().andEqualTo("year", year)
        return evalUnitScoreMapper.deleteByExample(example)
    }

    // 插入新记录
    fun insertSelective(unitScoreEntity: UnitScoreEntity): Int {
        return evalUnitScoreMapper.insertSelective(unitScoreEntity)
    }

    // 批量插入
    fun insertBatch(unitScores: List<UnitScoreEntity>): Long {
        return evalUnitScoreMapper.insertBatch(unitScores)
    }

    /**
     * 删除n天前的数据
     * @param year 年份
     * @param day 天数
     */
    fun deleteByDay(year: Int, day: Int): Int {
        // 创建Example 查询条件 createTime 为n天前的时间（含n天）
        val date = LocalDateTime.now().minusDays(day.toLong())
        val example = Example(UnitScoreEntity::class.java)
        example.createCriteria().andEqualTo("year", year).andLessThanOrEqualTo("createTime", date)
        return evalUnitScoreMapper.deleteByExample(example)
    }
}