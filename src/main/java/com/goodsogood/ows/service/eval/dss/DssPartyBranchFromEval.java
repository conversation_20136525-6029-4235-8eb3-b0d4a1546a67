package com.goodsogood.ows.service.eval.dss;

import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.service.impl.DssPartyBranchBuilder;
import org.springframework.stereotype.Service;

/**
 * <p>Description: 决策辅助党支部</p>
 *
 * <AUTHOR>
 * @version 2020/11/9 11:11
 */
@Service
public class DssPartyBranchFromEval implements DssPartyBranchBuilder {
    @Override
    public PartyBranchInfo buildPartyBranch(PartyBranchInfo info) {
        return info;
    }
}
