package com.goodsogood.ows.service.eval.v2

import com.goodsogood.ows.configuration.OrgTypeConfig
import com.goodsogood.ows.configuration.TagConfig
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.service.meeting.EvalMeetingService
import org.springframework.stereotype.Service

/**
 * 至少开展1次党章学习
 * <AUTHOR>
 * @date 2023/12/4
 * @description  至少开展1次党章学习
 * 包含下级
 * 党委、党总支：党组会、中心组学习；
 * 党支部：党组会、中心组学习、党员大会、党小组会、主题党日、党课；
 */
@Service("partyCharterLearningServiceImpl")
class PartyCharterLearningServiceImpl(
    val tagConfig: TagConfig,
    val orgTypeConfig: OrgTypeConfig,
    val evalMeetingService: EvalMeetingService
) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId:String,
        params: Map<String, Any>
    ): Double {
        // 判断是否已学
        val hasLearning = when (org.orgTypeChild) {
            //判断党组织类型
            in orgTypeConfig.communistChild, in orgTypeConfig.generalBranchChild -> {
                //党委、党总支
                //党组会议、党组理论学习中心组学习会议
                evalMeetingService.getMeetingListByTagAndType(
                    year, org.organizationId,
                    listOf(tagConfig.constitution ?: 47), listOf("党组会议", "党组理论学习中心组学习会议")
                ).isNotEmpty()
            }

            in orgTypeConfig.branchChild -> {
                //党支部
                //党组会议、党组理论学习中心组学习会议、党支部党员大会、主题党日、党课
                evalMeetingService.getMeetingListByTagAndType(
                    year,
                    org.organizationId,
                    listOf(tagConfig.constitution ?: 47),
                    listOf("党组会议", "党组理论学习中心组学习会议", "党支部党员大会", "主题党日", "党课")
                ).isNotEmpty()
            }

            else -> {
                true
            }
        }
        // 已学不扣分
        return if (hasLearning) {
            0.0
        } else {
            // 未学一个组织扣对应的分
            metricEntity.score ?: 0.0
        }
    }
}