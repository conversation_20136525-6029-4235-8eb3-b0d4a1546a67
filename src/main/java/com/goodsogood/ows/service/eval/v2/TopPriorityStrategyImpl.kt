package com.goodsogood.ows.service.eval.v2

import com.goodsogood.ows.configuration.MongoCollectionNameConfig4Region
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.mongodb.meeting.ConfigRepositoryCustom
import com.goodsogood.ows.model.mongodb.meeting.TopPriorityEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.model.vo.eval.v2.TopPriorityUnitStatisticsVO
import org.checkerframework.checker.units.qual.m
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation.*
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset

/**
 * 党组会、理论中心组学习会议，是否覆盖所有的市局发布的第一议题内容，按缺失内容条数扣分（媒体发布时间10月13日（含）以后的，本月发布的不纳入）
 * <AUTHOR>
 * @date 2023/12/1
 */
@Service("unfinishedTopPriorityStrategyImpl")
class UnfinishedTopPriorityStrategyImpl(
    mongoTemplate: MongoTemplate,
    mongoCollectionNameConfig4Region: MongoCollectionNameConfig4Region
) : Strategy, TopPriorityQueryUtil(mongoTemplate, mongoCollectionNameConfig4Region) {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        val monthAndDay = monthAndDay(year)
        // 本月发布的不纳入
        val time = if (LocalDate.now().year == year) {
            LocalDate.now().minusMonths(1)
        } else {
            LocalDate.of(year - 1, 12, 31)
        }
        val count = this.count(year)
        val data =
            this.queryMyTopPriority(
                unit.organizationId,
                year,
                monthAndDay.first,
                monthAndDay.second,
                time.year,
                time.monthValue,
                time.dayOfMonth,
                false
            )
                .firstOrNull { it.unitOrgId == unit.organizationId }
        return this.score(count.toLong(), data?.priorityNum ?: 0, metricEntity.score ?: 0.0)
    }
}


/**
 * 党组会、理论中心组学习会议第一议题学习时效性，按超时次数扣分（媒体发布时间10月13日（含）以后的）
 * <AUTHOR>
 * @date 2023/12/1
 * @description
 */
@Service("untimelyTopPriorityStrategyImpl")
class UntimelyTopPriorityStrategyImpl(
    mongoTemplate: MongoTemplate,
    mongoCollectionNameConfig4Region: MongoCollectionNameConfig4Region
) : Strategy, TopPriorityQueryUtil(mongoTemplate, mongoCollectionNameConfig4Region) {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        val monthAndDay = monthAndDay(year)
        // 本月发布的不纳入
        val time = if (LocalDate.now().year == year) {
            LocalDate.now().minusMonths(1)
        } else {
            LocalDate.of(year - 1, 12, 31)
        }
        val data =
            this.queryMyTopPriority(
                unit.organizationId,
                year,
                monthAndDay.first,
                monthAndDay.second,
                time.year,
                time.monthValue,
                time.dayOfMonth,
                true
            )
                .firstOrNull { it.unitOrgId == unit.organizationId }
        // 为学的情况已经在林外一条规则扣分了，这里只计算已学部分
        return if ((data?.priorityNum ?: 0) > 0) {
            (data?.latePriorityNum ?: 0) * (metricEntity.score ?: 0.0)
        } else {
            0.0
        }
    }
}

/**
 * 第一议题查询装饰者
 */
abstract class TopPriorityQueryUtil(
    val mongoTemplate: MongoTemplate,
    private val mongoCollectionNameConfig4Region: MongoCollectionNameConfig4Region,
) {
    private val collectionName = ConfigRepositoryCustom.topPriority + "-" + mongoCollectionNameConfig4Region.region

    /**
     * 计算得分
     * @param count 第一议题数据总数
     * @param num 需要扣除的条数
     * @param score 加/扣分基数
     */
    fun score(count: Long, num: Int, score: Double): Double {
        val s = (count - num) * score
        return if (s < 0) {
            0.0
        } else {
            s
        }
    }

    fun createCriteria(
        year: Int,
        month: Int? = null,
        day: Int? = null,
        year1: Int? = null,
        month1: Int? = null,
        day1: Int? = null
    ): MutableList<Criteria> {
        // 创建查询
        val criteriaList = mutableListOf<Criteria>()
        // 初始化空条件
        criteriaList.add(Criteria())
        // 这里只查询市局发布的
        criteriaList.add(Criteria.where("orgId").`is`(3))
        // 查询第一议题sourceTime的年份时间为year,如果月份不为空，需要查询月份（以后的数据）
        criteriaList.add(
            Criteria.where("sourceTime").let {
                if (month != null) {
                    val startDate =
                        LocalDateTime.of(
                            year,
                            month,
                            day ?: 1,
                            0,
                            0,
                            0
                        ) //.atOffset(ZoneOffset.ofHours(8)).toLocalDateTime()
                    val endDate =
                        LocalDateTime.of(
                            year1 ?: year,
                            month1 ?: 12,
                            day1 ?: 31,
                            23,
                            59,
                            59
                        ) // .atOffset(ZoneOffset.ofHours(8)).toLocalDateTime()
                    it.gte(startDate).lte(endDate)
                } else {
                    val startDate =
                        LocalDateTime.of(year, 1, 1, 0, 0, 0).atOffset(ZoneOffset.ofHours(8)).toLocalDateTime()
                    val endDate =
                        LocalDateTime.of(year, 12, 31, 23, 59, 59).atOffset(ZoneOffset.ofHours(8)).toLocalDateTime()
                    it.gte(startDate).lte(endDate)
                }
            }
        )
        return criteriaList
    }

    /**
     * 通过MongoDB查询对应的第一议题情况
     * @param unitId 当前单位id
     * @param year 当前年份
     * @param month 月份
     */
    fun queryMyTopPriority(
        unitId: Long,
        year: Int,
        month: Int? = null,
        day: Int? = null,
        year1: Int? = null,
        month1: Int? = null,
        day1: Int? = null,
        addTypeQuery: Boolean? = false
    ): List<TopPriorityUnitStatisticsVO> {
        // 创建查询
        val criteriaList = createCriteria(year, month, day, year1, month1, day1)
        // 匹配单位
        criteriaList.add(Criteria.where("meetings.associatedUnitOrgId").`is`(unitId))
        if (addTypeQuery == true) {
            // 添加会议类型为 党组会议 和 党组理论学习中心组学习会议 的条件，meetingTypeNames是用逗号分割的文本
            criteriaList.add(
                Criteria.where("meetings.meetingTypeNames").let {
                    it.regex("党组会议").orOperator(it.regex("党组理论学习中心组学习会议"))
                }
            )
        }
        // 创建aggregate查询
        val aggregate = newAggregation(
            TopPriorityEntity::class.java,
            // 展开 meetings
            unwind("meetings"),
            // 匹配查询条件
            match(Criteria().andOperator(*criteriaList.toTypedArray())),
            // group
            group(
                "meetings.associatedUnitOrgId"
            ).push(
                mapOf(
                    "topPriorityId" to "\$_id",
                    "title" to "\$title",
                    "sourceTime" to "\$sourceTime",
                    "associatedOrg" to "\$meetings.associatedOrg",
                    "associatedName" to "\$meetings.associatedName",
                    "associatedUnitOrgId" to "\$meetings.associatedUnitOrgId",
                    "meetingId" to "\$meetings.meetingId",
                    "meetingTitle" to "\$meetings.meetingTitle",
                    "meetingTime" to "\$meetings.meetingTime",
                    "meetingTypes" to "\$meetings.meetingTypes",
                    "meetingTypeNames" to "\$meetings.meetingTypeNames",
                    // 计算 meetingTime 和 sourceTime 时间差
                    "diffDays" to mapOf(
                        "\$divide" to listOf(
                            mapOf(
                                "\$subtract" to listOf("\$meetings.meetingTime", "\$sourceTime")
                            ),
                            86400000 // 将毫秒转换为天
                        )
                    )
                )
            ).`as`("meetings"),
            //         $project: {
            //            _id: 1,
            //            meetings: 1
            //        }
            project()
                .and("_id").`as`("unitOrgId")
                .and("meetings").`as`("meetings"),
        )
        val results = mongoTemplate.aggregate(
            aggregate,
            collectionName,
            TopPriorityUnitStatisticsVO::class.java
        )
        val now = LocalDateTime.now()
        results.forEach {
            // 处理results 生成汇总数据
            // priority_num	number	N/A	Y	所有已学议题数
            //party_group_num	number	N/A	Y	党组会学习数量
            //leading_group_num	number	N/A	Y	中心组学习数量
            //completed_priority_num	number	N/A	Y	正常已学议题数
            //late_priority_num	number	N/A	Y	逾期学习议题数

            // 所有已学议题数
            it.priorityNum = it.meetings?.distinctBy { m -> m.topPriorityId }?.size ?: 0
            val meetingIds = mutableSetOf<Long>()
            // meetings通过 议题id 和 组织id 去重（取老的那条）然后计算统计结果
            // it.meetings?.distinctBy { m -> m.topPriorityId }
            it.meetings?.groupBy { m -> "${m.topPriorityId}-${m.associatedUnitOrgId}" }?.mapValues { entry ->
                entry.value.minByOrNull { m -> m.meetingTime ?: now }
            }?.values?.toList()?.forEach { meeting ->
                // 正常已学议题数(diffDays <= 30)
                it.completedPriorityNum += (if (meeting?.diffDays?.let { diffDays ->
                        diffDays <= 30
                    } == true) 1 else 0)
                // 逾期学习议题数(diffDays > 30)
                it.latePriorityNum += (if (meeting?.diffDays?.let { diffDays ->
                        diffDays > 30
                    } == true) 1 else 0)
                // 党组会学习数量 //  这里需要确认 会议类型的名称 是否时 党组会议
                it.partyGroupNum += (if (meeting?.meetingTypeNames?.split(",")?.contains("党组会议") == true) 1 else 0)
                // 中心组学习数量 //  这里需要确认 会议类型的名称 是否时 党组理论学习中心组学习会议
                it.leadingGroupNum += (
                        if (meeting?.meetingTypeNames?.split(",")
                                ?.contains("党组理论学习中心组学习会议") == true
                        ) 1 else 0
                        )
                meetingIds.add(meeting?.meetingId ?: -1)
            }
            // 专题读书班是会议标签，需要t_meeting_tag中关联查询
            // 这里不处理专题读书班的统计
            it.specialReadingNum = 0
        }
        return results.toList()
    }

    /**
     * 获取当前year,市局（orgId = 3）发布的第一议题总数
     * @param year 年份
     * @param month 月份
     */
    private fun countAllTopPriority(
        year: Int, month: Int, day: Int? = 1, year1: Int? = null,
        month1: Int? = null,
        day1: Int? = null
    ): Int {
        // 创建查询
        val criteriaList = createCriteria(year, month, day, year1, month1, day1)
        // 创建aggregate查询
        val aggregate = newAggregation(
            TopPriorityEntity::class.java,
            // 匹配查询条件
            match(Criteria().andOperator(*criteriaList.toTypedArray())),
            // group
            group("orgId").count().`as`("count")
        )
        // 执行查询
        val result = mongoTemplate.aggregate(aggregate, collectionName, Map::class.java)
        // 获取结果
        return (result.uniqueMappedResult?.get("count") as Int?) ?: 0
    }

    fun count(year: Int): Int {
        val monthAndDay = monthAndDay(year)
        // 本月发布的不纳入
        val time = if (LocalDate.now().year == year) {
            LocalDate.now().minusMonths(1)
        } else {
            LocalDate.of(year - 1, 12, 31)
        }
        val count = this.countAllTopPriority(
            year,
            monthAndDay.first,
            monthAndDay.second,
            time.year,
            time.monthValue,
            time.dayOfMonth
        )
        return count
    }

    fun monthAndDay(year: Int): Pair<Int, Int> {
        // 2023年特殊情况，媒体发布时间10月13日（含）以后的
        val month = if (year == 2023) {
            10
        } else {
            1
        }
        val day = if (year == 2023) {
            13
        } else {
            31
        }
        return Pair(month, day)
    }
}