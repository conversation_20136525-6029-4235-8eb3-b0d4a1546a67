package com.goodsogood.ows.service.eval.v2

import cn.hutool.core.date.DateUtil
import com.goodsogood.ows.configuration.MeetingTypeConfig
import com.goodsogood.ows.mapper.meeting.MeetingMapper
import com.goodsogood.ows.mapper.meeting.MeetingWorkPointsMapper
import com.goodsogood.ows.mapper.user.OrgGroupMapper
import com.goodsogood.ows.mapper.user.PartyGroupMapper
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.db.meeting.MeetingWorkPointEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import tk.mybatis.mapper.entity.Example
import java.util.*

/**
 * 领导班子成员参加所在党组织组织生活
 * <AUTHOR>
 * @date 2023-11-30 09:37:14
 * @description LeaderMeetingStrategyImpl
 *
 */
@Service("leaderMeetingStrategyImpl")
class LeaderMeetingStrategyImpl(
    @Autowired val partyGroupMapper: PartyGroupMapper,
    @Autowired val meetingTypeConfig: MeetingTypeConfig,
    @Autowired val meetingMapper: MeetingMapper
) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        // 最终得分
        var resultScore = 0.0
        // 查询党组班子成员
        val userList = partyGroupMapper.findPartyGroupUser(unit.organizationId)
        when {
            userList.isNotEmpty() -> {
                // 计算党支部大会，党支部委员会，党小组会，主题党日
                val typeList = listOf(
                    meetingTypeConfig.partyId, meetingTypeConfig.groupId,
                    meetingTypeConfig.committeeId, meetingTypeConfig.dayId
                )
                userList.forEach {
                    // 党员大会、支委会、党小组会、主题党日每季度不少于2次
                    val quarterList = mutableListOf<Int>()
                    // 计算当前时间所在的季度
                    val quarter = DateUtil.quarter(Date())
                    (quarter downTo 1).forEach { q ->
                        quarterList.add(q)
                    }
                    quarterList.forEach { qt ->
                        // 根据党组班子成员查询参加组织生活次数
                        val cnt = meetingMapper.findMeetingCount(year, it, typeList, qt)
                        when {
                            cnt < meetingTypeConfig.times -> {
                                when (cnt) {
                                    0 -> {
                                        resultScore += meetingTypeConfig.times * metricEntity.score!!
                                    }

                                    1 -> {
                                        resultScore += metricEntity.score!!
                                    }
                                }
                            }
                        }
                    }
                    // 未参加组织生活会
                    val meetingTypeCount = meetingMapper.findMeetingTypeCount(meetingTypeConfig.meetingTypeId, it, year)
                    when {
                        meetingTypeCount <= 0 -> {
                            resultScore += metricEntity.score!!
                        }
                    }
                }
            }
        }
        return resultScore
    }
}

/**
 * 领导班子成员讲党课至少1次
 * <AUTHOR>
 * @date 2023-11-30 09:37:14
 * @description LeaderLessonsStrategyImpl
 *
 */
@Service("leaderLessonsStrategyImpl")
class LeaderLessonsStrategyImpl(
    @Autowired val partyGroupMapper: PartyGroupMapper,
    @Autowired val orgGroupMapper: OrgGroupMapper,
    @Autowired val meetingTypeConfig: MeetingTypeConfig
) : Strategy {

    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        // 最终得分
        var resultScore = 0.0
        // 查询党组班子成员
        val userList = partyGroupMapper.findPartyGroupUser(unit.organizationId)
        when {
            userList.isNotEmpty() -> {
                // 根据党组班子成员查询
                val lessonList = orgGroupMapper.findLessionsByUser(year, meetingTypeConfig.lessonId, userList)
                val numberMap = lessonList.associate { it.userId to it.number }
                when {
                    numberMap.isNotEmpty() -> {
                        userList.forEach {
                            val number = numberMap[it]
                            when (number) {
                                null, 0 -> {
                                    resultScore += metricEntity.score!!
                                }
                            }
                        }
                    }

                    else ->
                        repeat(userList.size) {
                            resultScore += metricEntity.score!!
                        }

                }
            }
        }
        return resultScore
    }
}

/**
 * 党的建设工作领导小组工作要点
 * <AUTHOR>
 * @date 2023-11-30 09:37:14
 * @description LeaderGroupStrategyImpl
 *
 */
@Service("leaderGroupStrategyImpl")
class LeaderGroupStrategyImpl(
    @Autowired val meetingWorkPointsMapper: MeetingWorkPointsMapper
) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        // 查询是否存在附件，一年一条
        var resultScore = 0.0
        val example = Example(MeetingWorkPointEntity::class.java)
        example.createCriteria().andEqualTo("year", year)
            .andEqualTo("ownerId", unit.organizationId)
            .andNotEqualTo("orgId", -999)
            .andIsNotNull("publishTime")
            .andNotEqualTo("publishTime", "")
            .andIsNotNull("attachment")
            .andNotEqualTo("attachment", "")
        val cnt = meetingWorkPointsMapper.selectCountByExample(example)
        when {
            cnt <= 0 -> {
                resultScore = metricEntity.score!!
            }
        }
        return resultScore
    }
}