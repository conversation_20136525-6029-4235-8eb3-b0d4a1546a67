package com.goodsogood.ows.service.eval.v2

import com.aidangqun.log4j2cm.helper.LogAspectHelper
import com.goodsogood.ows.configuration.OrgTypeConfig
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.service.meeting.EvalMeetingService
import org.springframework.stereotype.Service
import java.time.LocalDate

// 严格执行系列Service
// 严格落实“三会一课”制度、严格落实主题党日制度和严格组织召开组织生活会

/**
 * 严格落实“三会一课”制度，党员大会每季度1次、支委会和党小组会每月1次
 * 按缺少次数扣分
 *  党员大会（支部）：每季度一次
 *  支委会（支部）：每月一次
 *  党小组会（党小组）：每月一次
 */
@Service("strictThreeMeetingAndOneStudyServiceImpl")
class ThreeMeetingAndOneStudyServiceImpl(
    val orgTypeConfig: OrgTypeConfig,
    private val evalMeetingService: EvalMeetingService,
) : Strategy, StrictDecorator() {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        // 获取组织类型，分别判断对应的会
        // 1.党员大会（支部）：每季度一次
        // 2.支委会（支部）：每月一次
        // 3.党小组会（党小组）：每月一次
        // 获取当前时间，判断应该有几个季度和几个月
        val month = LocalDate.now().monthValue
        val quarter = month / 3
        // 获取每个月和每个季度的开始结束时间
        val monthMap = monthMap(year)
        val quarterMap = quarterMap(year)

        val score = when (org.orgTypeChild) {
            in orgTypeConfig.branchChild -> {
                // 党支部 判断 党员大会和支委会
                var score = 0.0
                // 循环第一月到当前月
                for (i in 1..month) {
                    // 判断当前组织在当前时间是否开这个会
                    if (evalMeetingService.getTaskUserCount(
                            org.organizationId,
                            monthMap[i]?.get(0) ?: "",
                            listOf("党支部委员会会议")
                        ) > 0L
                    ) {
                        // 获取支委会（党支部委员会会议）
                        val branchMeeting = evalMeetingService.getMeetingListByTagAndType(
                            monthMap[i]?.get(0) ?: "",
                            monthMap[i]?.get(1) ?: "",
                            org.organizationId,
                            listOf(),
                            listOf("党支部委员会会议")
                        )
                        if (branchMeeting.isEmpty()) {
                            score += (metricEntity.score ?: 0.0)
                        }
                    }
                }
                // 循环第一个季度到当前季度
                for (i in 1..quarter) {
                    // 判断当前组织在当前时间是否开这个会
                    if (evalMeetingService.getTaskUserCount(
                            org.organizationId,
                            quarterMap[i]?.get(0) ?: "",
                            listOf("党支部党员大会")
                        ) > 0L
                    ) {
                        // 获取党员大会（党支部党员大会）
                        val branchMeeting = evalMeetingService.getMeetingListByTagAndType(
                            quarterMap[i]?.get(0) ?: "",
                            quarterMap[i]?.get(1) ?: "",
                            org.organizationId,
                            listOf(),
                            listOf("党支部党员大会")
                        )
                        if (branchMeeting.isEmpty()) {
                            score += (metricEntity.score ?: 0.0)
                        }
                    }
                }
                score
            }

            in orgTypeConfig.communistGroup -> {
                // 党小组 判断 党小组会
                var score = 0.0
                // 循环第一月到当前月
                for (i in 1..month) {
                    // 判断当前组织在当前时间是否开这个会
                    if (evalMeetingService.getTaskUserCount(
                            org.organizationId,
                            monthMap[i]?.get(0) ?: "",
                            listOf("党小组会")
                        ) > 0L
                    ) {
                        // 获取党小组会（党小组会议）
                        val branchMeeting = evalMeetingService.getMeetingListByTagAndType(
                            monthMap[i]?.get(0) ?: "",
                            monthMap[i]?.get(1) ?: "",
                            org.organizationId,
                            listOf(),
                            listOf("党小组会")
                        )
                        if (branchMeeting.isEmpty()) {
                            score += (metricEntity.score ?: 0.0)
                        }
                    }
                }
                score
            }

            else -> {
                // 其他类型组织 判断 党员大会和支委会
                0.0
            }
        }
        return score
    }
}

/**
 * 严格落实主题党日制度，每月1次
 * 按缺少次数扣分
 */
@Service("strictPartyDayServiceImpl")
class PartyDayActivityServiceImpl(
    val orgTypeConfig: OrgTypeConfig,
    val evalMeetingService: EvalMeetingService,
) : Strategy, StrictDecorator() {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        val month = LocalDate.now().monthValue
        // 获取每个月和每个季度的开始结束时间
        val monthMap = monthMap(year)
        val score = when (org.orgTypeChild) {
            in orgTypeConfig.branchChild -> {
                // 党支部 主题党日
                var scope = 0.0
                // 循环第一月到当前月
                for (i in 1..month) {
                    // 判断当前组织在当前时间是否开这个会
                    if (evalMeetingService.getTaskUserCount(
                            org.organizationId,
                            monthMap[i]?.get(0) ?: "",
                            listOf("主题党日")
                        ) > 0L
                    ) {
                        // 获取主题党日（党支部主题党日）
                        val branchMeeting = evalMeetingService.getMeetingListByTagAndType(
                            monthMap[i]?.get(0) ?: "",
                            monthMap[i]?.get(1) ?: "",
                            org.organizationId,
                            listOf(),
                            listOf("主题党日")
                        )
                        if (branchMeeting.isEmpty()) {
                            scope += (metricEntity.score ?: 0.0)
                        }
                    }
                }
                scope
            }

            else -> 0.0
        }
        return score
    }
}

/**
 * 严格组织召开组织生活会,组织生活会
 * 会前、会中、会后流程不完整则扣分
 */
@Service("strictOrganizationLifeMeetingServiceImpl")
class OrganizationLifeMeetingServiceImpl(
    val orgTypeConfig: OrgTypeConfig,
    val evalMeetingService: EvalMeetingService,
) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        val myYear = year - 1
        // 只计算党支部
        return if (orgTypeConfig.branchChild.contains(org.orgTypeChild)) {
            // 获取组织生活开展情况
            val num = evalMeetingService.getOrgLifeMeetingListByOrgIdAndYearAndStatus(
                myYear,
                org.organizationId,
                // 1：新建，2：保存了准备草稿，3：已结束，4：保存会后梳理草稿，5：已上报或归档
                5
            ).size
            if (num > 0) {
                0.0
            } else {
                metricEntity.score ?: 0.0
            }
        } else {
            0.0
        }
    }
}

/**
 * 严格系列的装饰者
 */
abstract class StrictDecorator() {
    /**
     * 获取季度的开始结束时间
     * @param year 年份
     * @return 季度的开始结束时间
     */
    fun quarterMap(year: Int) = mapOf(
        1 to listOf("$year-01-01 00.00.00", "$year-03-31 23.59.59"),
        2 to listOf("$year-04-01 00.00.00", "$year-06-30 23.59.59"),
        3 to listOf("$year-07-01 00.00.00", "$year-09-30 23.59.59"),
        4 to listOf("$year-10-01 00.00.00", "$year-12-31 23.59.59"),
    )

    /**
     * 获取每个月的开始结束时间
     * @param year 年份
     * @return 季度的开始结束时间
     */
    fun monthMap(year: Int) = mapOf(
        1 to listOf("$year-01-01 00.00.00", "$year-01-31 23.59.59"),
        2 to listOf("$year-02-01 00.00.00", "$year-02-28 23.59.59"),
        3 to listOf("$year-03-01 00.00.00", "$year-03-31 23.59.59"),
        4 to listOf("$year-04-01 00.00.00", "$year-04-30 23.59.59"),
        5 to listOf("$year-05-01 00.00.00", "$year-05-31 23.59.59"),
        6 to listOf("$year-06-01 00.00.00", "$year-06-30 23.59.59"),
        7 to listOf("$year-07-01 00.00.00", "$year-07-31 23.59.59"),
        8 to listOf("$year-08-01 00.00.00", "$year-08-31 23.59.59"),
        9 to listOf("$year-09-01 00.00.00", "$year-09-30 23.59.59"),
        10 to listOf("$year-10-01 00.00.00", "$year-10-31 23.59.59"),
        11 to listOf("$year-11-01 00.00.00", "$year-11-30 23.59.59"),
        12 to listOf("$year-12-01 00.00.00", "$year-12-31 23.59.59"),
    )
}
