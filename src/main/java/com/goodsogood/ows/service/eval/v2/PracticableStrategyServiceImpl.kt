package com.goodsogood.ows.service.eval.v2

import com.goodsogood.ows.mapper.user.PartyGroupMapper
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.service.meeting.EvalMeetingService
import org.springframework.stereotype.Service

/**
 * 领导成员每年至少深入基层联系点1次,党组
 * <AUTHOR>
 * @date 2023/12/10
 * @description class PracticableStrategyServiceImpl
 */
@Service("practicableStrategyServiceImpl")
class PracticableStrategyServiceImpl(
    private val partyGroupMapper: PartyGroupMapper,
    private val evalMeetingService: EvalMeetingService,
) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        // 先获取当前单位的领导班子
        val leaders = partyGroupMapper.findPartyGroupUser(unit.organizationId)
        // 通过领导班子去获取应该有的深入基层联系点数据（这里不判断单位id）
        val practicableList = evalMeetingService.getPracticableListByUnitIdAndLeaderIdsAndYear(
            null, leaders, year
        )
        // 通过leaders 和 practicableList.leader 进行比较，取出缺少人数
        val practicableLeaders = practicableList.map { it.leaderId }
        val lackLeaders = leaders.filter { !practicableLeaders.contains(it) }
        // 缺少人数 * 分值
        return lackLeaders.size * (metricEntity.score ?: 0.0)
    }
}