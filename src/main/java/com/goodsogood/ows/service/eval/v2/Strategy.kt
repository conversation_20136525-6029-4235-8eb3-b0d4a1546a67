package com.goodsogood.ows.service.eval.v2

import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.utils.SpringContextUtil
import org.apache.logging.log4j.ThreadContext
import org.springframework.beans.BeansException

/**
 * <AUTHOR>
 * @date 2023/11/26
 * @description 烟草考核2.0策略接口
 */
interface Strategy {
    companion object {
        /**
         * 这里对工厂模式做了一个简化，直接在Strategy接口中定义了一个静态方法，通过beanName获取对应的Strategy子类
         */
        @Throws(BeansException::class)
        fun createStrategy(beanName: String): Strategy? {
            return SpringContextUtil.getBean(beanName) as Strategy?
        }
    }

    /**
     * 重新设置上下文
     */
    fun setTrackId(trackId: String) {
        if (ThreadContext.get("tracker_id") != null) {
            ThreadContext.remove("tracker_id")
        }
        ThreadContext.put("tracker_id", trackId)
    }

    /**
     * 执行策略，计算得分
     * @param org 当前 组织信息
     * @param unit 当前 单位信息
     * @param metricEntity 当前指标对象
     * @param year 当前年份
     * @param  trackId 日志跟踪id
     * @param params 参数
     * @return 得分
     */
    fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double

}