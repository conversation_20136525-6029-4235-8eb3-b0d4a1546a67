package com.goodsogood.ows.service.eval.v2

import com.aidangqun.log4j2cm.helper.LogAspectHelper
import com.goodsogood.ows.configuration.OrgTypeConfig
import com.goodsogood.ows.mapper.meeting.MeetingCommentMapper
import com.goodsogood.ows.mapper.meeting.MeetingCommentMemberComplexMapper
import com.goodsogood.ows.mapper.meeting.MeetingCommentMemberMapper
import com.goodsogood.ows.mapper.meeting.MeetingCommentStatisticsMapper
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.db.meeting.*
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import tk.mybatis.mapper.entity.Example

/**
 * 民主评议策略实现类
 */
@Service("democraticStrategyImpl")
class DemocraticStrategyImpl @Autowired constructor(
    private val orgTypeConfig: OrgTypeConfig,
    private val meetingCommentMapper: MeetingCommentMapper,
    private val meetingCommentMemberMapper: MeetingCommentMemberMapper,
    private val meetingCommentMemberComplexMapper: MeetingCommentMemberComplexMapper,
    private val meetingCommentStatisticsMapper: MeetingCommentStatisticsMapper
) : Strategy {

    private val log = LoggerFactory.getLogger(DemocraticStrategyImpl::class.java)
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        log.debug(
            "考核2.0,民主评议策略实现类->org=>{},unit=>{},metricEntity=>{},year=>{},params=>{}",
            org,
            unit,
            metricEntity,
            year,
            params
        )
        // 判断是否是党支部
        if (!orgTypeConfig.checkIsOnlyBranch(org.orgTypeChild)) {
            log.debug("${org.organizationId}组织不是党支部，不需要计算民主评议")
            return 0.0
        }
        // 通过org的organizationId去meeting_comment表中查询
        // 查询组织去年审定通过的数据
        val example = Example(MeetingCommentEntity::class.java)
        example.createCriteria().andEqualTo("orgId", org.organizationId)
            .andEqualTo("year", year - 1)
            .andEqualTo("status", CommentStatusEnum.APPROVED.key)
        val commentList = meetingCommentMapper.selectByExample(example)
        // 判断commentList是否为空
        if (commentList.isEmpty()) {
            log.debug("${org.organizationId}组织在[${year - 1}]年没有审定通过民主评议")
            return metricEntity.score ?: 0.0
        }
        // 有审定通过的数据
        // 获取第一条数据
        val comment = commentList[0]
        // 查询统计数据
        val complexEx = Example(MeetingCommentStatisticsEntity::class.java)
        complexEx.createCriteria().andEqualTo("commentId", comment.commentId)
            .andEqualTo("year", year - 1)
            .andEqualTo("rating", CommentRatingEnum.EXCELLENT.key)
        val commentMemberComplexList = meetingCommentStatisticsMapper.selectByExample(complexEx)
        if (commentMemberComplexList.size == 0) {
            log.debug("${org.organizationId}组织在[${year - 1}]年没有民主评议统计数据")
            return metricEntity.score ?: 0.0
        }
        val statisticsEntity = commentMemberComplexList[0]
        // 查询党员总数
        val total = statisticsEntity.partyNumber?: 0
        // 记录优秀评议党员数量
        val excellent = statisticsEntity.ratingNumber?: 0
        // 判断优秀党员是否超过33%直接扣分
        if (excellent > total * (1.toDouble() / 3.toDouble())) {
            log.debug("${org.organizationId}组织在[${year - 1}]年优秀评议党员数量超过33%，直接扣分")
            return metricEntity.score ?: 0.0
        }
        return 0.0
    }
}