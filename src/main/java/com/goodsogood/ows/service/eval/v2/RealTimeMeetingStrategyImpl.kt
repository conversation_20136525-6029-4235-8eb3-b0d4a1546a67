package com.goodsogood.ows.service.eval.v2

import com.goodsogood.ows.configuration.MeetingTypeConfig
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.service.meeting.EvalMeetingService
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * <AUTHOR>
 * @date 2024/1/4
 * @description class RealTimeMeetingStrategyImpl
 */
@Service("realTimeMeetingStrategyImpl")
class RealTimeMeetingStrategyImpl(
    val evalMeetingService: EvalMeetingService,
    val meetingTypeConfig: MeetingTypeConfig
) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        // 2023年的数据从12月01日开始计算
        val startTime = if (year == 2023) {
            LocalDateTime.of(2023, 12, 1, 0, 0, 0)
        } else {
            LocalDateTime.of(year, 1, 1, 0, 0, 0)
        }
        val endTime = LocalDateTime.of(year, 12, 31, 23, 59, 59)
        val count = evalMeetingService.getMeetingCount(
            org.organizationId,
            startTime,
            endTime,
            listOf(
                meetingTypeConfig.partyId!!,
                meetingTypeConfig.committeeId!!,
                meetingTypeConfig.groupId!!,
                meetingTypeConfig.lessonId!!,
                meetingTypeConfig.dayId!!,
                meetingTypeConfig.partyMeeting!!,
                meetingTypeConfig.centerStudy!!
            ),
        )
        return (count ?: 0) * (metricEntity.score ?: 0.0)
    }

}