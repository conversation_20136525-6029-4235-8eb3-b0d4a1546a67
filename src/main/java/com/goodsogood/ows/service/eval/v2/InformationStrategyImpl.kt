package com.goodsogood.ows.service.eval.v2

import com.aidangqun.log4j2cm.helper.LogAspectHelper
import com.goodsogood.ows.mapper.supervise.SuperviseExtendMapper
import com.goodsogood.ows.mapper.supervise.SuperviseMapper
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.db.supervise.SuperviseEntity
import com.goodsogood.ows.model.db.supervise.SuperviseExtendEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import tk.mybatis.mapper.entity.Example

/**
 * 信息完整度策略实现类
 */
@Service("informationStrategyImpl")
class InformationStrategyImpl @Autowired constructor(
    private val superviseExtendMapper: SuperviseExtendMapper
) : Strategy {

    private val log = LoggerFactory.getLogger(InformationStrategyImpl::class.java)
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        log.debug(
            "考核2.0,信息完整度策略实现类->org=>{},unit=>{},metricEntity=>{},year=>{},params=>{}",
            org,
            unit,
            metricEntity,
            year,
            params
        )
        // 通过组织ID查询监督预警表
        val example = Example(SuperviseExtendEntity::class.java)
        example.createCriteria().andEqualTo("subOrgId", org.organizationId)
            .andEqualTo("optionKey", "option1")
        val superviseList = superviseExtendMapper.selectByExample(example)
        // 判断superviseList是否为空
        if (superviseList.isEmpty()) {
            log.debug("${org.organizationId}组织没有监督预警数据")
            return 0.0
        }
        // 有监督预警数据
        // 获取第一条数据
        val supervise = superviseList[0]
        // 获取缺少项
        val detailContent = supervise.detailContent
        val items = detailContent?.replace("，", ",")
            ?.replace("、", ",")
            ?.split(",")
        return (metricEntity.score ?: 0.0).times(items?.size ?: 0)
    }
}