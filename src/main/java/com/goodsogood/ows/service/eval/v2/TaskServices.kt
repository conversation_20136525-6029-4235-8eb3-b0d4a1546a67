package com.goodsogood.ows.service.eval.v2

import com.aidangqun.log4j2cm.helper.LogAspectHelper
import com.goodsogood.ows.configuration.EvalV2Config
import com.goodsogood.ows.helper.LocalDateTimeUtils
import com.goodsogood.ows.mapper.task.TaskMapper
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * 党组理论学习中心组学习年度计划、总结
 * 未完成扣分
 * 任务id 通过配置文件获取
 * <AUTHOR>
 * @date 2023/12/4
 * @description class TaskServiceImpl
 */
@Service("task4CentralGroupMeetingServiceImpl")
class Task4CentralGroupMeetingServiceImpl(
    private val taskMapper: TaskMapper,
    private val evalV2Config: EvalV2Config
) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        // 操作状态（1：待填报，2：草稿，3：已提交，4：已转派，5：已撤回，6：已退回，7：未通过，8：已通过，9：逾期未提交，10：已取消）
        return taskMapper.getExamineOperateStatus(
            evalV2Config.taskId,
            null,
            3,
            org.organizationId,
            "2023-01-01 00:00:00",
            "3023-12-31 23:59:59"
        ).filter { it.operateStatus != 8 }.size * (metricEntity.score ?: 0.0)
    }
}


/**
 * 线上任务完成情况。任务勾选了“考核任务”的
 * 未完成扣分
 *
 * t_task_sub 中的 is_examine 为1的是考核任务
 */
@Service("examineTaskServiceImpl")
class ExamineTaskServiceImpl(private val taskMapper: TaskMapper) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        val endTime = if (year == LocalDate.now().year) {
            LocalDateTimeUtils.toTime(LocalDateTime.now())
        } else {
            "$year-12-31 23:59:59"
        }
        // 操作状态（1：待填报，2：草稿，3：已提交，4：已转派，5：已撤回，6：已退回，7：未通过，8：已通过，9：逾期未提交，10：已取消）
        return taskMapper.getExamineOperateStatus(
            null,
            1, // is_examine = 1
            3,
            org.organizationId,
            "$year-01-01 00:00:00",
            endTime
        ).filter { it.operateStatus != 8 }.size * (metricEntity.score ?: 0.0)
    }
}