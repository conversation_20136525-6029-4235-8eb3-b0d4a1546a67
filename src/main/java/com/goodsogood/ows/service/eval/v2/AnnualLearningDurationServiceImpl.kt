package com.goodsogood.ows.service.eval.v2

import com.aidangqun.log4j2cm.helper.LogAspectHelper
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.service.meeting.EvalMeetingService
import org.springframework.stereotype.Service

/**
 * 党组理论学习中心组全年集中学习时间少于12天的
 * <AUTHOR>
 * @date 2023/12/4
 * @description 党组理论学习中心组全年集中学习时间少于12天的
 */
@Service("annualLearningDurationServiceImpl")
class AnnualLearningDurationServiceImpl(val evalMeetingService: EvalMeetingService) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        // 获得学习时长，使用会议开始时间和结束时间间隔小时计算
        val learningDuration = evalMeetingService.getMeetingListByTagAndType(
            year,
            org.organizationId,
            listOf(),
            listOf("党组理论学习中心组学习会议")
        ).sumOf {
            // it.theoryLearn ?: 0.0
            // 开始时间和结束时间的小时间隔
            val durationInMillis = it.endTime?.time?.minus(it.startTime?.time ?: 0) ?: 0
            durationInMillis / (1000 * 3600)
        }
        return if (learningDuration < 48) {
            metricEntity.score ?: 0.0
        } else {
            0.0
        }
    }
}