package com.goodsogood.ows.service.eval

import com.goodsogood.ows.mapper.eval.MetricClassMapper
import com.goodsogood.ows.mapper.eval.MetricMapper
import com.goodsogood.ows.model.db.eval.v2.MetricClassEntity
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import org.springframework.stereotype.Service
import tk.mybatis.mapper.entity.Example

@Service
class MetricClassMapperService(
    var metricClassMapper: MetricClassMapper,
) {
    fun selectAll(): List<MetricClassEntity> {
        return metricClassMapper.selectAll()
    }

    fun selectByPrimaryKey(id: Int): MetricClassEntity? {
        return metricClassMapper.selectByPrimaryKey(id)
    }

    /**
     * 使用selectByExample对year列进行查询
     * @param year 年份
     * @return List<EvalMetricEntity>
     */
    fun selectByYear(year: Int): List<MetricClassEntity> {
        val example = Example(MetricClassEntity::class.java)
        example.createCriteria().andEqualTo("year", year)
        return metricClassMapper.selectByExample(example)
    }

}

@Service
class MetricService(
    var metricMapper: MetricMapper
) {
    fun selectAll(): List<MetricEntity> {
        return metricMapper.selectAll()
    }

    fun selectByPrimaryKey(id: Long): MetricEntity? {
        return metricMapper.selectByPrimaryKey(id)
    }

    /**
     * 使用selectByExample对year列进行查询(只查询enabled = 1的)
     * @param year 年份
     * @return List<EvalMetricEntity>
     */
    fun selectByYear(year: Int): List<MetricEntity> {
        val example = Example(MetricEntity::class.java)
        example.createCriteria().andEqualTo("year", year).andEqualTo("enabled", 1)
        return metricMapper.selectByExample(example)
    }

    /**
     * 通过metricClassId获取对应的指标
     * @param metricClassId 指标分类id
     * @return List<EvalMetricEntity>
     */
    fun selectByMetricClassId(metricClassId: Long): List<MetricEntity> {
        val example = Example(MetricEntity::class.java)
        example.createCriteria().andEqualTo("metricClassId", metricClassId).andEqualTo("enabled", 1)
        return metricMapper.selectByExample(example)
    }
}