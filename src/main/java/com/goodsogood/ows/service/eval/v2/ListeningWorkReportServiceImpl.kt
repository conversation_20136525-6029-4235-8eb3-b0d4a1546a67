package com.goodsogood.ows.service.eval.v2

import com.goodsogood.ows.mapper.user.PartyGroupMapper
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.service.meeting.EvalMeetingService
import org.springframework.stereotype.Service

/**
 * 领导班子成员每半年听取党建工作汇报不少于1次
 * 每半年计算1次，按缺少人次扣分
 * <AUTHOR>
 * @date 2023/12/4
 * @description class ListeningPartyReportServiceImpl
 */

@Service("listeningPartyReportServiceImpl")
class ListeningPartyReportServiceImpl(
    private val partyGroupMapper: PartyGroupMapper,
    private val evalMeetingService: EvalMeetingService
) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        // 先获取当前单位的领导班子
        val leaders = partyGroupMapper.findPartyGroupUser(unit.organizationId)
        // 获取领导班子所有的汇报
        val reports = evalMeetingService.getMeetingWorkReportListByLeaderIds(year, 1, leaders)
        // 计算上下半年的汇报次数，按缺少人次扣分
        val firstHalfYearLeaders = reports.asSequence().filter { it.halfYearInterval == 1 }.map { it.leaderIds ?: "-1" }
            .flatMap { it.split(",").map { num -> num.toLong() } }.filter { num -> num > 0 }.distinct().toList()
        val secondHalfYearLeaders =
            reports.asSequence().filter { it.halfYearInterval == 2 }.map { it.leaderIds ?: "-1" }
                .flatMap { it.split(",").map { num -> num.toLong() } }.filter { num -> num > 0 }.distinct().toList()
        val firstHalfYear = leaders.size - firstHalfYearLeaders.size
        val secondHalfYear = leaders.size - secondHalfYearLeaders.size
        return (if (firstHalfYear > 0) {
            firstHalfYear * (metricEntity.score ?: 0.0)
        } else {
            0.0
        }) + (if (secondHalfYear > 0) {
            secondHalfYear * (metricEntity.score ?: 0.0)
        } else {
            0.0
        })
    }
}

/**
 * 党组专题研究意识形态工作每半年不少于1次
 */
@Service("ideologyResearchServiceImpl")
class IdeologyResearchServiceImpl(
    private val evalMeetingService: EvalMeetingService
) : Strategy {
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        // 获取本单位意思意识形态汇报
        val reports = evalMeetingService.getMeetingWorkReportListByLeaderIds(year, 2, listOf(unit.organizationId))
        // 计算上下半年的汇报次数
        val firstHalfYear = reports.filter { it.halfYearInterval == 1 }.size
        val secondHalfYear = reports.filter { it.halfYearInterval == 2 }.size
        return (if (firstHalfYear <= 0) {
            metricEntity.score ?: 0.0
        } else {
            0.0
        }) + (if (secondHalfYear <= 0) {
            metricEntity.score ?: 0.0
        } else {
            0.0
        })
    }
}