package com.goodsogood.ows.service.eval.dss;

import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.service.impl.DssUserBuilder;
import org.springframework.stereotype.Service;

/**
 * <p>Description: 决策辅助用户</p>
 *
 * <AUTHOR>
 * @version 2020/11/9 11:12
 */
@Service
public class DssUserFromEval implements DssUserBuilder {
    @Override
    public UserInfo buildUser(UserInfo info) {
        return info;
    }
}
