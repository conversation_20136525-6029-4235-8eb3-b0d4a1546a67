package com.goodsogood.ows.service.eval.v2

import com.fasterxml.jackson.databind.ObjectMapper
import com.goodsogood.ows.common.Constants
import com.goodsogood.ows.mapper.user.OrganizationMapper
import com.goodsogood.ows.model.db.eval.v2.MetricClassEntity
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.db.eval.v2.UnitScoreEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.model.vo.eval.v2.MetricTaskBean
import com.goodsogood.ows.model.vo.eval.v2.TaskInfo
import com.goodsogood.ows.queue.eval.v2.Producer
import com.goodsogood.ows.service.adb.QLService
import com.goodsogood.ows.service.eval.MetricClassMapperService
import com.goodsogood.ows.service.eval.MetricService
import com.goodsogood.ows.service.eval.EvalOrgScoreService
import com.goodsogood.ows.service.eval.EvalUnitScoreService
import com.goodsogood.ows.service.sas.OpenService
import org.slf4j.LoggerFactory
import org.springframework.data.domain.PageRequest
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.http.HttpHeaders
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.*
import kotlin.math.abs


/**
 * <AUTHOR>
 * @date 2023/11/27
 * @description class MetricTaskService 构造和处理烟草考核2.0的计算任务 Service
 */
@Service
class MetricTaskService(
    val restTemplate: RestTemplate,
    val redisTemplate: StringRedisTemplate,
    val producer: Producer,
    val openService: OpenService,
    val metricService: MetricService,
    val metricClassMapperService: MetricClassMapperService,
    val evalOrgScoreService: EvalOrgScoreService,
    val evalUnitScoreService: EvalUnitScoreService,
    val qlService: QLService,
    val organizationMapper: OrganizationMapper,
    val objectMapper: ObjectMapper,
) {
    private val log = LoggerFactory.getLogger(MetricTaskService::class.java)

    companion object {
        data class UnitInfo(
            // 单位
            var unit: OrganizationBase? = null,
            // 单位对应的党组织
            var topOrg: OrganizationBase? = null,
            // 单位下所有组织（包含党小组）
            var allOrg: MutableList<OrganizationBase>? = null,
        )
    }

    /**
     * 计算指标
     * @param year 年份
     * @param headers 请求头
     * @param id 任务id
     * @param trackerId 跟踪id
     * @param unitId 如果单位id不为空，只计算当前单位
     */
    @JvmOverloads
    fun computeMetric(
        year: Int,
        headers: HttpHeaders,
        id: String? = null,
        trackerId: String? = null,
        unitId: Long? = null
    ) {
        // 生成任务id和时间
        val taskId = id ?: UUID.randomUUID().toString().replace("-", "")
        val createTime = LocalDateTime.now()
        log.debug("考核2.0,开始计算指标->headers:{},taskId:{}", headers, taskId)
        // 生成SSLog
        val tk = trackerId ?: taskId
        // 0. 缓存所有的组织
        val unitIds = openService.getCorps(headers)?.map { it.orgId }?.filter {
            if (unitId != null) {
                it == unitId
            } else {
                true
            }
        }
//        log.debug("考核2.0,当前单位id->{}", unitIds)
        val units = openService.findOrgByIds(unitIds, headers)
        val map = mutableMapOf<Long, UnitInfo>()
        // 处理排除的单位id（如果有）...
        for (unit in units) {
            // 通过单位id获取当前单位对应的党组织
            val topOrg = organizationMapper.getLevelOneOrg(unit.organizationId)
            if (topOrg == null || topOrg.organizationId == null) {
                log.warn("考核2.0,单位id：${unit.organizationId}，没有对应的关联党组织")
                continue
            }
//            // 通过单位id获取单位下党委和支部
//            val form = openService.getOrgByCorp(unit.organizationId, headers)
//            // 通过支部id获取支部下的党小组
//            val groupIds = openService.getOrgIdByBranchIds(form?.branchOrg, headers)
//            // 合并党委、支部、党小组id
//            val orgIds =
//                form.branchOrg?.plus(groupIds ?: mutableListOf())?.plus(form?.allOrg ?: mutableListOf())?.distinct()
//            // 通过党委、支部、党小组id获取所有党组织
//            val allOrg =
//                if (!orgIds.isNullOrEmpty()) {
//                    openService.findOrgByIds(orgIds, headers).filter { it.organizationId != null }
//
//                } else listOf()
            val allOrg = this.getAllOrgs(unit.organizationId, topOrg, headers)
            map[unit.organizationId!!] = UnitInfo(unit, topOrg, allOrg.toMutableList())
        }
//        log.debug("考核2.0,当前map->{}", map)
        // 1. 通过 MetricClassEntity 获取所有指标细项 list<metricEntity>
        val metricClassEntities = metricClassMapperService.selectByYear(year)
        val metricList = mutableListOf<MetricEntity>()
        for (metricClassEntity in metricClassEntities) {
            val metrics = getMetrics(metricClassEntity)
            metricList.addAll(metrics)
            metrics.forEach {
//                if (it.type?.toInt() == 3) {
////                    log.debug("考核2.0,指标类型为3(手动处理)，不进行计算，指标id：${it.id}")
//                    return@forEach
//                }
//                log.debug("考核2.0,指标名称为${it.name}，指标类型为${it.type}，指标id：${it.id}")
                try {
                    // 2. 通过策略配置获取对应的党组织（可能含下级党组织）
                    map.forEach { (t, u) ->
//                        log.debug("考核2.0,指标名称为${it.name}，指标类型为${it.type}，指标id：${it.id}，单位id：$t")
                        val myOrg = if (it.includeSubOrg?.toInt() == 1) { // 包含下级党组织
                            //      u.allOrg?.also { l -> u.topOrg?.let { it1 -> l.add(it1) } }
                            //                                ?.distinctBy { it1 -> it1.organizationId }
                            // 为allOrg添加topOrg，并通过organizationId进行去重
                            if (u.topOrg != null) {
                                u.allOrg?.add(u.topOrg!!)
                            }
                            u.allOrg?.distinctBy { o -> o.organizationId }
                        } else {
                            mutableListOf(u.topOrg)
                        }
//                        log.debug(
//                            "考核2.0,指标名称为{}，指标类型为{}，指标id：{}，单位id：{}，组织：{}",
//                            it.name,
//                            it.type,
//                            it.id,
//                            t,
//                            myOrg
//                        )
                        myOrg?.forEach { o ->
//                            log.debug("考核2.0,指标名称为${it.name}，指标类型为${it.type}，指标id：${it.id}，单位id：$t，组织id：${o?.organizationId}")
                            if (o != null && o.organizationId != null) {
                                // 3. 生成计算任务到redis,redis需要有两个hash存储
                                // 第一个 hash存储存的是任务的信息（策略id、单位、组织等）
                                // 第二个 hash存储的是任务的状态
                                // 生成任务信息
                                val taskInfo = TaskInfo(
                                    year = year,
                                    taskId = taskId,
                                    metricClassId = metricClassEntity.id,
                                    metricId = it.id,
                                    metric = it,
                                    unit = map[t]?.unit,
                                    org = o,
                                    time = createTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli(),
                                    trackerId = tk,
                                )
                                val subTaskId = UUID.randomUUID().toString()
                                // 存储taskInfo
                                redisTemplate.opsForHash<String, String>()
                                    .put(
                                        "${Constants.METRIC_TASK_CACHE_KEY}$taskId",
                                        subTaskId,
                                        objectMapper.writeValueAsString(taskInfo)
                                    )
                                // 存储taskStatus
                                redisTemplate.opsForHash<String, String>()
                                    .put(
                                        "${Constants.METRIC_TASK_STATUS_CACHE_KEY}$taskId",
                                        subTaskId,
                                        MetricTaskBean.TaskStatus.NotStarted.value.toString()
                                    )
                                // 4. 把任务id通过mq发送
                                producer.send(MetricTaskBean(taskId, subTaskId))
                            }
                        }
                    }
                } catch (e: Exception) {
                    log.error("考核2.0,生成策略和创建任务时候，发送错误：" + e.localizedMessage, e)
                }
            }
        }
        // 6. 循环等待所有的任务是否完成，3小时超时
        var count = 0
        while (true) {
            val status =
                redisTemplate.opsForHash<String, String>().entries("${Constants.METRIC_TASK_STATUS_CACHE_KEY}$taskId")
            if (status.isNullOrEmpty()) {
                break
            }
            val notStarted = status.filter { it.value == MetricTaskBean.TaskStatus.NotStarted.value.toString() }
            val inProgress = status.filter { it.value == MetricTaskBean.TaskStatus.InProgress.value.toString() }
            val completed = status.filter { it.value == MetricTaskBean.TaskStatus.Completed.value.toString() }
            val canceled = status.filter { it.value == MetricTaskBean.TaskStatus.Canceled.value.toString() }
            if (notStarted.isEmpty() && inProgress.isEmpty()) {
                log.debug("考核2.0,任务完成->${status.size},已完成->${completed.size},已取消->${canceled.size}")
                break
            }
            if (count > 3600) {
                log.error("考核2.0,计算任务超时，任务id：$taskId")
                break
            }
            // 每5分钟输出一次状态
            if (count % 60 == 0) {
                log.debug("考核2.0,任务状态->${status.size},未开始->${notStarted.size},进行中->${inProgress.size},已完成->${completed.size},已取消->${canceled.size}")
            }
            count++
            Thread.sleep(5000)
        }
        // 6.1 再等待5分钟，等所有业务清空
        Thread.sleep(300000)
        logErrors()
        // 7. 清除任务状态和任务信息缓存
        redisTemplate.delete("${Constants.METRIC_TASK_CACHE_KEY}$taskId")
        redisTemplate.delete("${Constants.METRIC_TASK_STATUS_CACHE_KEY}$taskId")
        log.debug(
            "考核2.0,缓存清除完成-> cache: ${redisTemplate.hasKey("${Constants.METRIC_TASK_CACHE_KEY}$taskId")}\ninfo:${
                redisTemplate.hasKey(
                    "${Constants.METRIC_TASK_STATUS_CACHE_KEY}$taskId"
                )
            }"
        )
        // 8. 检查数据，汇总到对应的积分表中
        // 8.1 获取当前taskId的积分结果
        val orgScoreEntities = evalOrgScoreService.selectByTaskIdGroup(taskId)
        if (orgScoreEntities.isEmpty()) {
            log.warn("考核2.0,当前任务没有积分结果，任务id：$taskId")
            return
        }
//        log.debug("考核2.0,当前任务积分结果数量->{}", orgScoreEntities.size)
        // 9. 整理数据
        // 循环构建单位积分结果
        // 先获取现在的
        val oldUnitScores = evalUnitScoreService.selectAll()
        val unitScores = orgScoreEntities.map {
            val metric = metricList.find { m -> m.id == it.metricId } ?: return@map null
            val oldScore = oldUnitScores.find { u -> u.unitId == it.unitId && u.metricId == it.metricId }
            UnitScoreEntity(
                year = year,
                metricClassId = it.metricClassId,
                metricId = it.metricId,
                metricName = metric.name ?: "",
                orgId = map[it.unitId]?.topOrg?.organizationId ?: -1,
                orgName = map[it.unitId]?.topOrg?.name ?: "",
                orgLevel = map[it.unitId]?.topOrg?.orgLevel ?: "",
                unitId = it.unitId,
                unitName = map[it.unitId]?.unit?.name ?: "",
                sysScore = if (metric.type?.toInt() == 1) { // 系统计算
                    it.score
                } else {
                    null
                },
                assmtScore = if (metric.type?.toInt() == 2) { // 考核组打分
                    it.score
                } else {
                    null
                },
                partyScore = if (oldScore?.modifiedFlag == 1) {// 如果modifiedFlag == 1 那么就使用以前的值
                    oldScore.partyScore ?: 0.0
                } else {
                    null // 在后面更新 partyScore // 党建处审定结果
                },
                modifiedFlag = oldScore?.modifiedFlag ?: 0,
                createTime = oldScore?.createTime ?: createTime,
                lastChangeUser = oldScore?.lastChangeUser ?: -999,
                updateTime = createTime,
            )
        }.filterNotNull()
//        log.debug("考核2.0,单位积分结果抽样->{}", unitScores[0].unitName)
        // 10. 通过轻流获取考核组“线下抽查线上考核指标其他应扣分情形”
        val penalty = qlService.findAll("query002", listOf(), PageRequest.of(0, 99999)).toList()
//        log.debug("考核2.0,轻流获取考核组->{}", penalty)
        // 10.1 先通过“评价单位”进行分组,获取到所有的指标扣分
        val unitPenaltyList = penalty.filter { it["评价单位"] != null }
            .map { it["评价单位"].toString() to it["apply_id"].toString() }
            .map {
                it.first to penalty.filter { p -> p["apply_id"]?.toString() == it.second && p["指标"] != null }
            }
//        log.debug("考核2.0,考核组评价单位分组结果->{}", unitPenaltyList)
        // 10.2 循环处理每个单位的线上考核指标扣分情形,更新对应的assmtScore（考核组打分）
        unitPenaltyList.forEach { (unitName, penaltyList) ->
            // 10.2.1 获取单位积分结果
            val uss = unitScores.filter { it.unitName == unitName }
            if (uss.isNotEmpty()) {
//                log.debug("考核2.0,从轻流获取的单位积分结果->{}:{}", unitName, uss)
                // 10.2.2 循环处理每个扣分情形
                penaltyList.forEach { p ->
                    // 10.2.3 获取扣分指标对应的记录
                    val unitScore = uss.find { it.metricName == p["指标"].toString() }
                    // 10.2.4 获取扣分情况
                    val score = p["扣分情况"]?.toString()?.toDoubleOrNull()
                    // 10.2.5 更新unitScore中对应的指标中的assmtScore字段(考核组打分)
                    unitScore?.assmtScore = score
                }
            }
        }
        // 10.3 计算对应大类的final_score
        // 10.3.1 先更新所有的partyScore（党建处打分）和 final_score
        // TODO 党建处打分需要从轻流流程 query005 中获取，要先判断modifiedFlag是否为1，如果为1，那么就使用以前的值
        unitScores.forEach {
            it.partyScore = it.partyScore ?: it.assmtScore ?: it.sysScore ?: 0.0
            it.finalScore = it.partyScore
        }
        // 10.3.2 在通过大类和单位id汇总finalScore，得到单位大类指标的map
        val unitScoreMap = unitScores.groupBy { it.metricClassId to it.unitId }
            .map { it.key to it.value.sumOf { u -> u.finalScore ?: 0.0 } }
            .toMap()
//        log.debug("考核2.0,单位大类指标的map->{}", unitScoreMap)
        // 10.3.3 再通过单位和大类id更新所有对应的finalScore为汇总值（不能超过大类的上限）
        unitScores.forEach {
            it.finalScore = unitScoreMap[it.metricClassId to it.unitId] ?: 0.0
            val scoreLimit = metricClassEntities.find { m -> m.id == it.metricClassId }?.scoreLimit ?: 0.0
            val scoreType = metricList.find { m -> m.id == it.metricId }?.scoreType ?: 0
            it.finalScore =
                if (scoreType == 1 && it.finalScore != null && it.finalScore!! <= 0 && abs(it.finalScore!!) < scoreLimit) {
                    scoreLimit - abs(it.finalScore!!) // 扣分方式
                } else if (scoreType == 2 && it.finalScore != null) { // 加分方式
                    if (it.finalScore!! > scoreLimit) {
                        scoreLimit
                    } else {
                        it.finalScore
                    }
                } else {
                    0.0
                }
        }

        log.debug("考核2.0,单位积分最终结果size->{}", unitScores.size)
        // 入库
        // 删除之前所有的单位积分
        if (unitScores.isNotEmpty()) {
            evalUnitScoreService.deleteByYear(year)
            // 保存新的单位积分，每100条提交一次
            val size = unitScores.size
            val batchSize = 100
            val batch = size / batchSize
            for (i in 0..batch) {
                val start = i * batchSize
                val end = if (i == batch) {
                    size
                } else {
                    (i + 1) * batchSize
                }
                evalUnitScoreService.insertBatch(unitScores.subList(start, end))
            }
        }
        // 清理数据库中三天前的数据
        evalOrgScoreService.deleteByDay(year, 3)
    }

    /**
     * 获取当前大类的所有指标项
     * @param metricClassEntity 分类对象
     */
    private fun getMetrics(metricClassEntity: MetricClassEntity): List<MetricEntity> {
        return metricService.selectByMetricClassId(metricClassEntity.id!!)
    }

    /**
     * 记录错误的子任务
     */
    private fun logErrors() {
        // 暂不实现
    }

    fun getAllOrgs(unitId: Long, topOrg: OrganizationBase, headers: HttpHeaders): List<OrganizationBase> {
        // 通过单位id获取单位下党委和支部
        val form = openService.getOrgByCorp(unitId, headers)
        // 通过支部id获取支部下的党小组
        val groupIds = openService.getOrgIdByBranchIds(form?.branchOrg, headers)
        // 合并党委、支部、党小组id
        val orgIds =
            form.branchOrg?.plus(groupIds ?: mutableListOf())?.plus(form?.allOrg ?: mutableListOf())?.distinct()
        // 通过党委、支部、党小组id获取所有党组织
        val allOrg =
            if (!orgIds.isNullOrEmpty()) {
                openService.findOrgByIds(orgIds, headers).filter { it.organizationId != null }

            } else listOf()
        return allOrg
    }
}