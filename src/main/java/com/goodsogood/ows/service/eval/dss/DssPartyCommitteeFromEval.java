package com.goodsogood.ows.service.eval.dss;

import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.service.impl.DssPartyCommitteeBuilder;
import org.springframework.stereotype.Service;

/**
 * <p>Description: 决策辅助党委 </p>
 *
 * <AUTHOR>
 * @version 2020/11/9 11:12
 */
@Service
public class DssPartyCommitteeFromEval implements DssPartyCommitteeBuilder {
    @Override
    public PartyCommitteeInfo buildPartyCommittee(PartyCommitteeInfo info) {
        return info;
    }
}
