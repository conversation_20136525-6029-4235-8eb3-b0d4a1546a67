package com.goodsogood.ows.service.eval.v2

import com.goodsogood.ows.mapper.ppmd.PayLogMapper
import com.goodsogood.ows.mapper.user.UserMapper
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.utils.DateUtils
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.util.*

/**
 * 党费计算规则信息
 */
@Service("ppmdStrategyImpl")
class PPMDStrategyImpl(
    val userMapper: UserMapper,
    val payLogMapper: PayLogMapper
) : Strategy {
    private val log = LoggerFactory.getLogger(PPMDStrategyImpl::class.java)
    override fun compute(
        org: OrganizationBase,
        unit: OrganizationBase,
        metricEntity: MetricEntity,
        year: Int,
        trackId: String,
        params: Map<String, Any>
    ): Double {
        val startMonth = if (year == 2023) {
            // 2023是特殊情况，12月开始
            12
        } else {
            1
        }

        var countNumber = 0
        // 获取当前月 的上一个月
        val endTime = if (year == LocalDate.now().year) {
            LocalDate.now().minusMonths(1)
        } else {
            LocalDate.of(year, 12, 1)
        }
        val endMonth = endTime.month.value
//        val calYear = year
//        if (endMonth == 1) {
//            endMonth = 12
//            calYear = year - 1
//        }
        for (i in startMonth..endMonth) {
            var payFor = "" + year;
            payFor += if (i < 10) {
                "-0$i"
            } else {
                "-$i"
            }
            try {
                countNumber += payLogMapper.getOwePayNum(org.organizationId, payFor) ?: 0
            } catch (ex: Exception) {
                log.error("考核2.0,ppmdStrategyImpl获取信息失败", ex)
            }
        }
        return metricEntity.score?.times(countNumber) ?: 0.0
    }
}