package com.goodsogood.ows.service.experience;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.ExperienceConfig;
import com.goodsogood.ows.configuration.ExperienceRuleTagConfig;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.mapper.experience.UserPartyEvalDetailMapper;
import com.goodsogood.ows.mapper.experience.UserPartyEvalMapper;
import com.goodsogood.ows.mapper.experience.UserPartyEvalResultMapper;
import com.goodsogood.ows.mapper.experience.UserPartyEvalRuleMapper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.db.experience.UserPartyEvalDetailEntity;
import com.goodsogood.ows.model.db.experience.UserPartyEvalEntity;
import com.goodsogood.ows.model.db.experience.UserPartyEvalResultEntity;
import com.goodsogood.ows.model.db.experience.UserPartyEvalRuleEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.experience.*;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.ExcelUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.NumberUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-03-11 10:52
 **/
@Service
@Log4j2
public class UserPartyEvalService {

    private final UserPartyEvalMapper userPartyEvalMapper;
    private final UserPartyEvalDetailMapper userPartyEvalDetailMapper;
    private final UserPartyEvalRuleMapper userPartyEvalRuleMapper;
    private final UserPartyEvalResultMapper userPartyEvalResultMapper;
    private final UserMapper userMapper;
    private final Errors errors;
    private final UserPartyEvalDetailService userPartyEvalDetailService;
    private final ExperienceConfig experienceConfig;
    private final ExperienceRuleTagConfig experienceRuleTagConfig;
    private final OrgTypeConfig orgTypeConfig;
    private final OrgService orgService;
    private final OrganizationMapper organizationMapper;
    private final UserService userService;
    private final SimpleApplicationConfigHelper simpleApplicationConfigHelper;


    @Autowired
    public UserPartyEvalService(UserPartyEvalMapper userPartyEvalMapper,
                                UserPartyEvalDetailMapper userPartyEvalDetailMapper,
                                UserPartyEvalRuleMapper userPartyEvalRuleMapper,
                                UserPartyEvalResultMapper userPartyEvalResultMapper, UserMapper userMapper,
                                Errors errors, UserPartyEvalDetailService userPartyEvalDetailService,
                                ExperienceConfig experienceConfig, ExperienceRuleTagConfig experienceRuleTagConfig, OrgTypeConfig orgTypeConfig, OrgService orgService, OrganizationMapper organizationMapper, UserService userService, SimpleApplicationConfigHelper simpleApplicationConfigHelper) {
        this.userPartyEvalMapper = userPartyEvalMapper;
        this.userPartyEvalDetailMapper = userPartyEvalDetailMapper;
        this.userPartyEvalRuleMapper = userPartyEvalRuleMapper;
        this.userPartyEvalResultMapper = userPartyEvalResultMapper;
        this.userMapper = userMapper;
        this.errors = errors;
        this.userPartyEvalDetailService = userPartyEvalDetailService;
        this.experienceConfig = experienceConfig;
        this.experienceRuleTagConfig = experienceRuleTagConfig;
        this.orgTypeConfig = orgTypeConfig;
        this.orgService = orgService;
        this.organizationMapper = organizationMapper;
        this.userService = userService;
        this.simpleApplicationConfigHelper = simpleApplicationConfigHelper;
    }


    /**
     * 手动体验
     *
     * @param sysHeader
     * @param type
     */
    @RepeatedCheck
    public ResponseEntity<Result<?>> clickExperience(HeaderHelper.SysHeader sysHeader, Integer type) throws InterruptedException {
        if (valIsClick(sysHeader.getUserId(), type)) {
            return new ResponseEntity<>(new Result<>("suc", errors), HttpStatus.OK);
        }
        String batchNumber = UUID.randomUUID().toString().replace("-", "");
        autoExperience(sysHeader.getRegionId(), sysHeader.getUserId(), (byte) 2, batchNumber);
        Thread.sleep(7000);
        //开始计算
        calStar(2);
        return new ResponseEntity<>(new Result<>("suc", errors), HttpStatus.OK);
    }

    /**
     * 校验是不是已经点击了
     *
     * @param sysHeader
     * @param
     * @return
     */
    public ResponseEntity<Result<?>> valClickExperience(HeaderHelper.SysHeader sysHeader, int type) {
        return new ResponseEntity<>(new Result<>(valIsClick(sysHeader.getUserId(), type), errors), HttpStatus.OK);
    }

    /**
     * 校验今天是不是已经点击
     *
     * @param userId
     * @param type
     * @return
     */
    private Boolean valIsClick(Long userId, Integer type) {
        Example example = new Example(UserPartyEvalEntity.class);
        String evalDate = DateUtils.toFormatDate(new Date(), "yyyy-MM-dd");
        example.createCriteria().andEqualTo("evalDate", evalDate)
                .andEqualTo("userId", userId).andEqualTo("type", type);
        List<UserPartyEvalEntity> list = userPartyEvalMapper.selectByExample(example);
        return !CollectionUtils.isEmpty(list);
    }


    /**
     * 自动体验
     *
     * @param
     */
    public void autoExperience(Long regionId, Long userId, byte type, String batchNumber) {
        LocalDateTime curTime = LocalDateTime.now();
        List<ExperienceUser> experienceUserInfo = userMapper.getExperienceUserInfo(userId);
        String currentMonth = DateUtils.getCurrentMonth();
        Example example = new Example(UserPartyEvalRuleEntity.class);
        example.createCriteria().andNotEqualTo("parentId", 0).andEqualTo("isShow",1);
        List<UserPartyEvalRuleEntity> userPartyEvalRuleEntities = userPartyEvalRuleMapper.selectByExample(example);
        List<UserPartyEvalEntity> list = new ArrayList<>();
        List<UserPartyEvalDetailEntity> listDetail = new ArrayList<>();
        String evalDate = DateUtils.toFormatDate(new Date(), "yyyy-MM-dd");
        experienceUserInfo.forEach(item -> {
            String taskId = UUID.randomUUID().toString().replace("-", "");
            UserPartyEvalEntity userPartyEvalEntity = new UserPartyEvalEntity();
            BeanUtils.copyProperties(item, userPartyEvalEntity);
            userPartyEvalEntity.setDateMonth(Integer.valueOf(currentMonth));
            userPartyEvalEntity.setUserId(item.getUserId());
            userPartyEvalEntity.setBatchNumber(batchNumber);
            userPartyEvalEntity.setUserName(item.getName());
            userPartyEvalEntity.setOrgId(item.getOrganizationId());
            userPartyEvalEntity.setAvatar(item.getAvatar());
            userPartyEvalEntity.setPosition(item.getPosition());
            //自动评测
            userPartyEvalEntity.setType(type);
            userPartyEvalEntity.setRegionId(regionId);
            userPartyEvalEntity.setTaskId(taskId);
            userPartyEvalEntity.setEvalDate(evalDate);
            userPartyEvalEntity.setCreateTime(curTime);
            userPartyEvalEntity.setCalStatus((byte) 0);
            userPartyEvalRuleEntities.forEach(it -> {
                UserPartyEvalDetailEntity detailEntity = new UserPartyEvalDetailEntity();
                detailEntity.setRuleId(it.getRuleId());
                detailEntity.setUserId(item.getUserId());
                detailEntity.setRegionId(regionId);
                detailEntity.setTaskId(taskId);
                detailEntity.setDateMonth(Integer.valueOf(currentMonth));
                detailEntity.setCalStatus((byte) 0);
                detailEntity.setCreateTime(LocalDateTime.now());
                if (experienceConfig.getExcludeRuleIds().contains(it.getRuleId())) {
                    detailEntity.setCalStatus((byte) 1);
                    detailEntity.setStar(0);
                }
                listDetail.add(detailEntity);
            });
            list.add(userPartyEvalEntity);
            if (list.size() >= 8) {
                userPartyEvalMapper.insertList(list);
                list.clear();
            }
            if (listDetail.size() >= 100) {
                userPartyEvalDetailMapper.insertList(listDetail);
                listDetail.clear();
            }
        });
        if (!CollectionUtils.isEmpty(list)) {
            userPartyEvalMapper.insertList(list);
        }
        if (!CollectionUtils.isEmpty(listDetail)) {
            userPartyEvalDetailMapper.insertList(listDetail);
        }
        //开始调用计算星级
        userPartyEvalDetailService.startCalStart(batchNumber);
    }

    /**
     * 历史体验报告
     *
     * @param pageNumber
     * @param sysHeader
     * @param startTime
     * @param endTime
     * @return
     */
    public List<UserPartyEvalEntity> report(PageNumber pageNumber,
                                            HeaderHelper.SysHeader sysHeader,
                                            String startTime, String endTime) {
        //拼装正确的时间格式
        String finalStartTime = startTime + " 00:00:00";
        ;
        String finalEndTime = endTime + " 23:59:59";
        ;
        return PageHelper.startPage(pageNumber.getPage(), pageNumber.getRows())
                .doSelectPage(() -> userPartyEvalMapper.report(sysHeader.getUserId(), finalStartTime, finalEndTime)
                );
    }


    /**
     * 历史体验报告
     *
     * @return
     */
    public List<UserPartyEvalEntity> reportByYear(Integer year, Long userId) {
        return userPartyEvalMapper.reportByYear(year, userId);
    }

    /**
     * 计算体检结果
     *
     * @param sysHeader
     * @return
     */
    public ResponseEntity<Result<?>> experienceResult(HeaderHelper.SysHeader sysHeader) {
        Example example = new Example(UserPartyEvalEntity.class);
//        example.createCriteria().andEqualTo("userId", sysHeader.getUserId())
//                .andEqualTo("type", 2)
//                .andEqualTo("calStatus", 0);
//        //如果存在还在当中
//        int countNumber = userPartyEvalMapper.selectCountByExample(example);
//        if (countNumber > 0) {
//            return new ResponseEntity<>(new Result<>(-1, errors), HttpStatus.OK);
//        }
        example.clear();
        example.selectProperties("avatar", "userName", "orgName", "evalDate", "dateMonth",
                "totalStar", "tagAdd", "tagReduce", "evalId", "position");
        example.createCriteria().andEqualTo("userId", sysHeader.getUserId())
                .andEqualTo("calStatus", 1);
        example.setOrderByClause(" create_time desc limit 1");
        UserPartyEvalEntity userPartyEvalEntity = userPartyEvalMapper.selectOneByExample(example);
        if (null == userPartyEvalEntity) {
            return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
        }

        //查询系统最后生成标签
        Example exampleTag = new Example(UserPartyEvalEntity.class);
        exampleTag.setOrderByClause("  create_time desc limit 1 ");
        exampleTag.createCriteria().andEqualTo("userId", sysHeader.getUserId())
                .andEqualTo("type", 1)
                .andEqualTo("calStatus", 1);
        UserPartyEvalEntity userPartyEvalSystem = userPartyEvalMapper.selectOneByExample(exampleTag);
        //重写标签
        if (null != userPartyEvalSystem && !StringUtils.isEmpty(userPartyEvalSystem.getTagAdd())) {
            userPartyEvalEntity.setTagAdd(userPartyEvalSystem.getTagAdd());
        }
        if (null != userPartyEvalSystem && !StringUtils.isEmpty(userPartyEvalSystem.getTagReduce())) {
            userPartyEvalEntity.setTagReduce(userPartyEvalSystem.getTagReduce());
        }

        Example exampleResult = new Example(UserPartyEvalResultEntity.class);
        exampleResult.createCriteria().andEqualTo("evalId", userPartyEvalEntity.getEvalId());
        List<UserPartyEvalResultEntity> list = userPartyEvalResultMapper.selectIdsByEvalId(userPartyEvalEntity.getEvalId());
//        List<Integer> ListStart = userPartyEvalResultMapper.selectByExample(exampleResult)
        List<Integer> ListStart = list.stream().map(UserPartyEvalResultEntity::getStar).collect(Collectors.toList());
        userPartyEvalEntity.setExperienceResult(ListStart);
        log.debug("柱状图处理开始");
        List<UserPartyEvalEntity.TypeMonthStarForm> histogramImg = createHistogramImg(sysHeader.getUserId());
        userPartyEvalEntity.setTypeMonthStar(histogramImg);
        log.debug("柱状图处理结束");
        userPartyEvalEntity.setTypeMonthStar(histogramImg);
        return new ResponseEntity<>(new Result<>(userPartyEvalEntity, errors), HttpStatus.OK);
    }

    /**
     * 根据用户id查询近5个月的各项评级
     *
     * @param userId 用户id
     * @return 柱状图
     */
    public List<UserPartyEvalEntity.TypeMonthStarForm> createHistogramImg(Long userId) {
        //查询最近的五个月的task_id t_user_party_eval
        List<UserPartyEvalEntity> lastFiveTotalList = userPartyEvalMapper.queryLastFiveMonth(userId);
        return getTypeMonthStarForms(lastFiveTotalList);
    }

    private List<UserPartyEvalEntity.TypeMonthStarForm> getTypeMonthStarForms(List<UserPartyEvalEntity> lastFiveTotalList) {
        if (CollectionUtils.isEmpty(lastFiveTotalList)) {
            return Collections.emptyList();
        }
        List<UserPartyEvalEntity.TypeMonthStarForm> list = new ArrayList<>();
        log.debug("综合评级处理");
        createMonStar(list, lastFiveTotalList, "综合评级");
        log.debug("各项评级处理");
        List<String> taskIds = lastFiveTotalList.stream().map(UserPartyEvalEntity::getTaskId).collect(Collectors.toList());
        Collections.reverse(taskIds);
        List<UserPartyEvalEntity> lastFiveList = userPartyEvalResultMapper.queryLastFiveMonthStar(taskIds);
        if (CollectionUtils.isEmpty(taskIds)) {
            return Collections.emptyList();
        }
        Map<String, List<UserPartyEvalEntity>> map = lastFiveList.stream().collect(
                Collectors.groupingBy(UserPartyEvalEntity::getRuleName, LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<String, List<UserPartyEvalEntity>> entry : map.entrySet()) {
            createMonStar(list, entry.getValue(), entry.getKey());
        }
        return list;
    }

    /**
     * @param list         最终返回，各项rule近五个月的星级
     * @param lastFiveList 数据库查询出的list列表
     * @param ruleName     各项rule的名称
     */
    private void createMonStar(List<UserPartyEvalEntity.TypeMonthStarForm> list,
                               List<UserPartyEvalEntity> lastFiveList, String ruleName) {
        UserPartyEvalEntity.TypeMonthStarForm typeMonthTotal = new UserPartyEvalEntity.TypeMonthStarForm();
        typeMonthTotal.setTypeName(ruleName);
        List<UserPartyEvalEntity.MonthStarVO> monthStar = new ArrayList<>();
        lastFiveList.forEach(total -> {
            UserPartyEvalEntity.MonthStarVO monthStarVO = new UserPartyEvalEntity.
                    MonthStarVO(total.getEvalDate(), total.getTotalStar());
            monthStar.add(monthStarVO);
        });
        Collections.reverse(monthStar);
        typeMonthTotal.setMonthStar(monthStar);
        list.add(typeMonthTotal);
    }


    /**
     * 计算汇总星级
     *
     * @param
     * @return
     */
    public void calStar(Integer type) {
        String currentMonth = DateUtils.getCurrentMonth();
        Example example = new Example(UserPartyEvalEntity.class);
        example.createCriteria().andEqualTo("calStatus", 0).
                andEqualTo("dateMonth", currentMonth).andEqualTo("type", type);
        List<UserPartyEvalEntity> list = userPartyEvalMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            log.info("计算汇总星级数据为空！");
            return;
        }
        Map<Integer, ExperienceConfig.Option> ruleOption = experienceConfig.getRuleOption();
        List<UserPartyEvalResultEntity> listEvalResult = new ArrayList<>();
        //得到那些没有成功
        list.forEach(item -> {
            List<UserCalResult> waitCalcData = userPartyEvalDetailMapper.getCalcData(item.getBatchNumber(), item.getUserId());
            //是否计算完成
            if (isCalSuc(item.getTaskId())) {
                return;
            }
            //得到这个批量总共有多少待计算数据
            int size = waitCalcData.size();
            log.info("1-计算星级用户id=" + item.getUserId() + "" + ",size=" + size);
            long count = waitCalcData.stream().filter(it -> it.getCalStatus() == 1).count();
            log.info("1-计算星级用户id=" + item.getUserId() + "" + ",count=" + count);
            if (count != size) {
                return;
            }
            //开始计算汇总数据
            Map<Integer, List<UserCalResult>> collect = waitCalcData.stream()
                    .collect(Collectors.groupingBy(UserCalResult::getTopRuleId));
            log.debug("开始计算汇总数据,{}",JsonUtils.toJson(collect));
            collect.forEach((k, v) -> {
                UserPartyEvalResultEntity userPartyEvalResultEntity = new UserPartyEvalResultEntity();
                ExperienceConfig.Option option = ruleOption.get(k);
                userPartyEvalResultEntity.setEvalId(item.getEvalId());
                userPartyEvalResultEntity.setTaskId(item.getTaskId());
                userPartyEvalResultEntity.setRuleId(option.getRuleId());
                userPartyEvalResultEntity.setRuleName(option.getRuleName());
                listEvalResult.add(userPartyEvalResultEntity);
                //这个ruleId为4写死为0 后期有数据放开
                if (option.getRuleId() == 4) {
                    userPartyEvalResultEntity.setStar(0);
                    return;
                }
                //计算大项的星级
                log.debug("计算大项的星级开始");
                int totalNum = v.size();
                //5星
                int fiveStar = (int) v.stream().filter(it -> it.getStar() == 5).count();
                if (NumberUtils.divide(fiveStar, totalNum, 2) >= option.getFiveStarRule()) {
                    userPartyEvalResultEntity.setStar(5);
                    log.debug("计算大项的星级5星");
                    return;
                }
                //4星
                int fourStar = (int) v.stream().filter(it -> it.getStar() >= 4).count();
                if (NumberUtils.divide(fourStar, totalNum, 2) >= option.getFourStarRule()) {
                    userPartyEvalResultEntity.setStar(4);
                    log.debug("计算大项的星级4星");
                    return;
                }
                //3星
                int threeStar = (int) v.stream().filter(it -> it.getStar() >= 3).count();
                if (NumberUtils.divide(threeStar, totalNum, 2) >= option.getThreeStarRule()) {
                    userPartyEvalResultEntity.setStar(3);
                    log.debug("计算大项的星级3星");
                    return;
                }
                //2星
                int twoStar = (int) v.stream().filter(it -> it.getStar() >= 2).count();
                if (NumberUtils.divide(twoStar, totalNum, 2) >= option.getTwoStarRule()) {
                    userPartyEvalResultEntity.setStar(2);
                    log.debug("计算大项的星级2星");
                    return;
                }
                //默认1星
                log.debug("计算大项的星级1星");
                userPartyEvalResultEntity.setStar(1);
            });
            if (listEvalResult.size() > 100) {
                userPartyEvalResultMapper.insertList(listEvalResult);
                listEvalResult.clear();
            }
        });
        //写入t_user_party_eval_result数据
        if (!CollectionUtils.isEmpty(listEvalResult)) {
            log.debug("写入t_user_party_eval_result数据，{}", JsonUtils.toJson(listEvalResult));
            userPartyEvalResultMapper.insertList(listEvalResult);
        }
        //更新主表数据
        Map<Integer, ExperienceConfig.OptionRuleTag> ruleRuleTag = experienceConfig.getRuleRuleTag();
        list.forEach(item -> {
            List<UserCalResult> waitCalcData = userPartyEvalDetailMapper.getCalcData(item.getBatchNumber(), item.getUserId());
            //得到这个批量总共有多少待计算数据
            int size = waitCalcData.size();
            log.info("2-计算星级用户id=" + item.getUserId() + "" + ",size=" + size);
            long count = waitCalcData.stream().filter(it -> it.getCalStatus() == 1).count();
            log.info("2-计算星级用户id=" + item.getUserId() + "" + ",count=" + count);
            if (count != size) {
                return;
            }
            Set<String> tagAdd = new HashSet<>();
            Set<String> tagReduce = new HashSet<>();
            try {
                //自动评测才算标签
                if (item.getType() == 1) {
                    ruleOption.forEach((k, v) -> {
                        //目前对业务实绩不做计算
                        if (k == 4) {
                            return;
                        }
                        //得到这个用户ruleId对应的汇总
                        List<UserCalStarResult> calcResult = userPartyEvalDetailMapper.getCalcResult(item.getUserId(), k);
                        //当月是不是5星
                        List<UserCalStarResult> collectCurrent = calcResult.stream().
                                filter(it -> currentMonth.equals("" + it.getDateMonth()) && it.getStar() == 5)
                                .collect(Collectors.toList());
                        //连接3个月
                        List<String> lastThreeMonth = DateUtils.getContinuousMonth(2, "yyyyMM");
                        Example example1 = new Example(UserPartyEvalEntity.class);
                        example1.createCriteria().andEqualTo("userId", item.getUserId())
                                .andIn("dateMonth", lastThreeMonth).andEqualTo("type", 1);
                        List<UserPartyEvalEntity> list1 = userPartyEvalMapper.selectByExample(example1);
                        //这里之前置灰的标签 可能多个
                        List<String> collectTagReduce = list1.stream().map(UserPartyEvalEntity::getTagReduce)
                                .filter(it -> !StringUtils.isEmpty(it))
                                .collect(Collectors.toList());
                        //得到置灰的新标签
                        Set<String> collectTagReduceNew = new HashSet<>();
                        collectTagReduce.forEach(it -> {
                            collectTagReduceNew.addAll(Arrays.asList(it.split(",")));
                        });
                        //得到这个rule选项下面的置灰的key
                        List<String> reduceTagResult = collectTagReduceNew.stream().filter(it ->
                                ruleRuleTag.get(k).getTagName().contains(it)).collect(Collectors.toList());
                        Set<String> collectTagAddNew = new HashSet<>();
                        list1.stream().map(UserPartyEvalEntity::getTagAdd)
                                .filter(it -> !StringUtils.isEmpty(it))
                                .collect(Collectors.toList()).forEach(it -> {
                                    collectTagAddNew.addAll(Arrays.asList(it.split(",")));
                                });
                        List<String> addTagResult = collectTagAddNew.stream().filter(it -> ruleRuleTag.get(k).getTagName().contains(it))
                                .collect(Collectors.toList());
                        //当月不是5星
                        if (CollectionUtils.isEmpty(collectCurrent)) {
                            //如果连续三个月都未五星
                            List<UserCalStarResult> threeMonthNoGetFiveStar = calcResult.stream().
                                    filter(it -> lastThreeMonth.contains("" + it.getDateMonth()) && it.getStar() != 5)
                                    .collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(threeMonthNoGetFiveStar) && threeMonthNoGetFiveStar.size() == 3) {
                                //连续3个月未获取五星置灰标签消失
                                tagReduce.add("");
                            } else {
                                if (!CollectionUtils.isEmpty(addTagResult)) {
                                    String tagName1 = userPartyEvalDetailMapper.getTagName(addTagResult, k);
                                    tagReduce.add(tagName1);
                                }
                                if (!CollectionUtils.isEmpty(reduceTagResult)) {
                                    tagReduce.add(reduceTagResult.get(0));
                                }
                            }
                            //当月是5星
                        } else {
                            //连续3个月5星
                            List<UserCalStarResult> collectThreeMonth = calcResult.stream().
                                    filter(it -> lastThreeMonth.contains("" + it.getDateMonth()) && it.getStar() == 5)
                                    .collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(collectThreeMonth) && collectThreeMonth.size() == 3) {
                                tagAdd.add(ruleRuleTag.get(k).getTag3());
                                return;
                            }
                            //连续2个月5星
                            List<String> lastTwoMonth = DateUtils.getContinuousMonth(1, "yyyyMM");
                            List<UserCalStarResult> collectTwoMonth = calcResult.stream().
                                    filter(it -> lastTwoMonth.contains("" + it.getDateMonth()) && it.getStar() == 5)
                                    .collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(collectTwoMonth) && collectTwoMonth.size() == 2) {
                                if (CollectionUtils.isEmpty(addTagResult)) {
                                    tagAdd.add(ruleRuleTag.get(k).getTag2());
                                } else {
                                    String tagName1 = userPartyEvalDetailMapper.getTagName(addTagResult, k);
                                    int i = ruleRuleTag.get(k).getTagName().indexOf(tagName1);
                                    tagAdd.add(ruleRuleTag.get(k).getTagName().get(i));
                                }
                                return;
                            }
                            //当月5星
                            Example example2 = new Example(UserPartyEvalEntity.class);
                            example2.createCriteria().andEqualTo("userId", item.getUserId())
                                    .andEqualTo("dateMonth",
                                            Objects.requireNonNull(DateUtils.getLastMonth(DateUtils.getCurrentMonth("yyyy-MM")))
                                                    .replace("-", ""))
                                    .andEqualTo("type", 1);
                            UserPartyEvalEntity userPartyEvalEntity = userPartyEvalMapper.selectOneByExample(example2);
                            if (userPartyEvalEntity == null) {
                                //取默认tag1
                                tagAdd.add(ruleRuleTag.get(k).getTag1());
                            } else {
                                String tagReduceName = userPartyEvalEntity.getTagReduce();
                                if (StringUtils.isEmpty(tagReduceName)) {
                                    tagAdd.add(ruleRuleTag.get(k).getTag1());
                                } else {
                                    List<String> tagNames = ruleRuleTag.get(k).getTagName();
                                    Optional<String> first = Arrays.stream(tagReduceName.split(","))
                                            .filter(tagNames::contains).findFirst();
                                    if (first.isPresent()) {
                                        tagAdd.add(first.get());
                                    } else {
                                        tagAdd.add(ruleRuleTag.get(k).getTag1());
                                    }
                                }
                            }

                        }
                    });
                }
            }catch (Exception ex){
                log.info("计算星级用户id发生异常,item={}", item);
                log.info("计算星级用户id发生异常", ex);
                return;
            }
            //最后标签也要顺序展示
            if (!CollectionUtils.isEmpty(tagAdd)) {
                item.setTagAdd(userPartyEvalDetailMapper.getLastTagName(tagAdd));
            }
            if (!CollectionUtils.isEmpty(tagReduce)) {
                item.setTagReduce(userPartyEvalDetailMapper.getLastTagName(tagReduce));
            }
            item.setCalStatus((byte) 1);
            item.setTotalStar(calTotalStar(item.getEvalId()));
            item.setUpdateTime(LocalDateTime.now());
            userPartyEvalMapper.updateByPrimaryKey(item);
            //更新evalResult数据
            updateResultAdviceId(item.getRegionId(), item.getTaskId());
        });
    }


    /**
     * 是否计算成功
     *
     * @param taskId
     * @return
     */
    private boolean isCalSuc(String taskId) {
        //查询
        Example example = new Example(UserPartyEvalResultEntity.class);
        example.createCriteria().andEqualTo("taskId", taskId);
        List<UserPartyEvalResultEntity> userPartyEvalResultEntities = userPartyEvalResultMapper.selectByExample(example);
        return !CollectionUtils.isEmpty(userPartyEvalResultEntities);
    }


    /**
     * 得到最后计算评星结果
     *
     * @param evalId
     * @return
     */
    private Integer calTotalStar(Long evalId) {
        Example example = new Example(UserPartyEvalResultEntity.class);
        example.createCriteria().andEqualTo("evalId", evalId);
        List<UserPartyEvalResultEntity> calcResult = userPartyEvalResultMapper.selectByExample(example);
        long count = calcResult.stream().filter(it -> it.getStar() == 5).count();
        if (count >= 4) {
            return 5;
        }
        count = calcResult.stream().filter(it -> it.getStar() == 4 || it.getStar() == 5).count();
        if (count >= 3) {
            return 4;
        }
        count = calcResult.stream().filter(it -> it.getStar() == 3 || it.getStar() == 4 || it.getStar() == 5).count();
        if (count >= 3) {
            return 3;
        }
        count = calcResult.stream().filter(it -> it.getStar() == 2 || it.getStar() == 3 ||
                it.getStar() == 4 || it.getStar() == 5).count();
        if (count >= 3) {
            return 2;
        }
        return 1;
    }


    /**
     * 更新t_user_party_eval_result表的advice_id
     *
     * @param regionId regionId
     * @param taskId   taskId
     *                 如果所有细项都没有五星，则直接批量将result表中的advice_id对应默认建议
     *                 大项中如果有细项是五星，并且有对应的建议，则随机取一个；如果没有对应的建议，则取默认建议
     */
    public void updateResultAdviceId(Long regionId, String taskId) {
        List<UserPartyEvalResultEntity> adviceInfos = userPartyEvalDetailMapper.queryFiveStarAdvice(taskId);
        Map<Integer, List<UserPartyEvalResultEntity>> map = adviceInfos.stream().collect(Collectors.groupingBy(UserPartyEvalResultEntity::getRuleId));
        if (null == map || map.isEmpty()) {//每一项都没有五星
            //将默认建议更新至result表-批量
            userPartyEvalResultMapper.updateAdviceId(taskId, null);
            return;
        }
        //获取一共有哪些大项
        List<Integer> bigRuleIds = userPartyEvalRuleMapper.queryBigRuleId(regionId);
        for (Integer ruleId : bigRuleIds) {
            //找不到对应的建议或者该大项都是五星以下
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(map.get(ruleId))) {
                //更新为默认建议
                userPartyEvalResultMapper.updateAdviceId(taskId, ruleId);
            } else {
                List<UserPartyEvalResultEntity> list = map.get(ruleId);
                int random = new Random().nextInt(list.size());
                Integer adviceId = list.get(random).getAdviceId();
                String paramValue = list.get(random).getParamValue();
                //根据task_id和rule_id更新result表
                userPartyEvalResultMapper.updateAdviceIdFiveStar(taskId, ruleId, adviceId, paramValue);
            }
        }
    }


    public OrgTimesVO sumExperienceTimes(Long orgId, Integer dateMonth, HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        String year = dateMonth.toString().substring(0, 4);
        List<UserPartyEvalEntity> list = userPartyEvalMapper.queryOrgTimes(header.getRegionId(), orgId, year, dateMonth);
        OrgTimesVO orgTimesVO = new OrgTimesVO();
        if (!CollectionUtils.isEmpty(list)) {
            //设置本年相关统计
            orgTimesVO.setYearTotalTimes(list.size());
            long yearSystemTimes = list.parallelStream().filter(i -> 1 == i.getType()).count();
            orgTimesVO.setYearSystemTimes((int) yearSystemTimes);
            long yearSystemPersons = list.parallelStream().filter(i -> 1 == i.getType()).collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator
                            .comparing(UserPartyEvalEntity::getUserId))), ArrayList::new)).size();
            orgTimesVO.setYearSystemPersons((int)yearSystemPersons);
            List<UserPartyEvalEntity> planList = userPartyEvalMapper.queryPlanTimes(header.getRegionId(), orgId, year, dateMonth);
            orgTimesVO.setYearPlanTimes(planList.size());
            long yearSelfTimes = list.parallelStream().filter(i -> 2 == i.getType()).count();
            orgTimesVO.setYearSelfTimes((int) yearSelfTimes);
            long yearSelfPersons = list.parallelStream().filter(i -> 2 == i.getType()).collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(UserPartyEvalEntity::getUserId))), ArrayList::new)).size();
            orgTimesVO.setYearSelfPersons((int) yearSelfPersons);
            //设置月份相关数据
            List<UserPartyEvalEntity> monthList = list.parallelStream().filter(i -> i.getDateMonth().equals(dateMonth)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(monthList)) {
                long monthTimes = monthList.size();
                orgTimesVO.setMonthTimes((int) monthTimes);
                long monthSelfTimes = monthList.parallelStream().filter(i -> 2 == i.getType()).count();
                orgTimesVO.setMonthSelfTimes((int) monthSelfTimes);
                int monthSystemTimes = (int) (monthTimes - monthSelfTimes);
                orgTimesVO.setMonthSystemTimes(monthSystemTimes);
                int monthSystemPersons = monthList.parallelStream()
                        .filter(i->1==i.getType()).collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(UserPartyEvalEntity::getUserId))), ArrayList::new)).size();
                orgTimesVO.setMonthSystemPersons(monthSystemPersons);
                long monthSelfPersons = list.parallelStream().filter(i ->
                        2 == i.getType()).collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator
                                .comparing(UserPartyEvalEntity::getUserId))), ArrayList::new)).size();
                orgTimesVO.setMonthSelfPersons((int) monthSelfPersons);
            }
            List<UserPartyEvalEntity> monthPlanList = planList.parallelStream().filter(
                    i -> i.getDateMonth().equals(dateMonth)).collect(Collectors.toList());
            orgTimesVO.setMonthPlanTimes(CollectionUtils.isEmpty(monthPlanList) ? 0 : monthPlanList.size());
        }
        return orgTimesVO;
    }


    public OrgReportChartVO reportChart(Long orgId, Integer dateMonth, HttpHeaders headers) {
        OrgReportChartVO orgReportChartVO = new OrgReportChartVO();
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("查询rule名字");
        Map<Integer, String> ruleMap = queryRuleName(header.getRegionId());
        log.debug("处理星级饼图");
        List<UserPartyReportVO.TypeStarVO> starVOs = userPartyEvalMapper.queryStarPieChart(header.getRegionId(), orgId, dateMonth);
        if (CollectionUtils.isEmpty(starVOs)) {
            return null;
        }
        List<UserPartyReportVO.TypeStarVO> starPieChart = handlePieChart(starVOs);
        orgReportChartVO.setStarPieChart(starPieChart);
        log.debug("处理柱状图");
        List<UserPartyReportVO.TypeStarVO> PillarChart = new ArrayList<>();
        List<UserPartyReportVO.TypeStarVO> pillarVOs = userPartyEvalMapper.queryPillarChart(header.getRegionId(), orgId, dateMonth);
        Map<Integer, List<UserPartyReportVO.TypeStarVO>> map = pillarVOs.stream().collect(
                Collectors.groupingBy(UserPartyReportVO.TypeStarVO::getRuleId, LinkedHashMap::new, Collectors.toList()));
        for (int i = 1; i <= 5; i++) {//5项
            if (null == map.get(i)) {//无某项时也要展示-此处特殊处理，全部为0
                List<UserPartyReportVO.TypeStarVO> vo = new ArrayList<>();
                for (int j = 1; j <= 5; j++) {
                    vo.add(new UserPartyReportVO.TypeStarVO(i, 0));
                }
                UserPartyReportVO.TypeStarVO innerVo = new UserPartyReportVO.TypeStarVO(ruleMap.get(i), vo);
                PillarChart.add(innerVo);
            } else {
                List<UserPartyReportVO.TypeStarVO> innerVo = map.get(i);
                List<UserPartyReportVO.TypeStarVO> innerPieChart = handlePieChart(innerVo);
                PillarChart.add(new UserPartyReportVO.TypeStarVO(ruleMap.get(i), innerPieChart));
            }
        }
        orgReportChartVO.setPillarChart(PillarChart);
        log.debug("标签处理");
        List<UserPartyReportVO.TypeStarVO> tagPillarChart = new ArrayList<>();
        for (int i = 1; i <= 5; i++) {
            List<String> tags = experienceRuleTagConfig.getRuleTag().get(i);
            List<UserPartyReportVO.TypeStarVO> tagVo = new ArrayList<>();
            for (String tag : tags) {
                Long tagNUm = userPartyEvalMapper.queryTag(header.getRegionId(), orgId, dateMonth, tag);
                UserPartyReportVO.TypeStarVO vo = new UserPartyReportVO.TypeStarVO();
                vo.setType(tag);
                vo.setNum(tagNUm == null ? 0 : tagNUm.intValue());
                tagVo.add(vo);
            }
            //处理暂无标签
            Integer noTagNUm = userPartyEvalMapper.queryNoTag(header.getRegionId(), orgId, dateMonth, i);
            tagVo.add(new UserPartyReportVO.TypeStarVO("暂无标签", noTagNUm));
            tagPillarChart.add(new UserPartyReportVO.TypeStarVO(ruleMap.get(i), tagVo));
        }
        orgReportChartVO.setTagPieChart(tagPillarChart);
        return orgReportChartVO;
    }

    //补齐5个星级
    private List<UserPartyReportVO.TypeStarVO> handlePieChart(List<UserPartyReportVO.TypeStarVO> starVOs) {
        int j = 0;
        for (int i = 1; i <= 5; i++) {
            if (j < starVOs.size()) {
                UserPartyReportVO.TypeStarVO vo = starVOs.get(j);
                if (vo.getStar() != i) {
                    starVOs.add(new UserPartyReportVO.TypeStarVO(i, 0));
                } else {
                    j++;
                }
            } else {
                starVOs.add(new UserPartyReportVO.TypeStarVO(i, 0));
            }
        }
        return starVOs.stream()
                .sorted(Comparator.comparing(UserPartyReportVO.TypeStarVO::getStar)).collect(Collectors.toList());
    }


    //党员体检详情
    public List<OrgUserReportVO> reportDetail(Long orgId, Integer dateMonth, HttpHeaders headers, Integer num) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("查询机构是不是支部");
        Integer code = orgService.getById(orgId).getOrgTypeChild();
        if (!orgTypeConfig.getBranchChild().contains(code)) {
            return Collections.emptyList();
        }
        //支部只展示10条，星级由高到低
        List<OrgUserReportVO> list = userPartyEvalMapper.queryUserReport(header.getRegionId(), orgId, dateMonth, num);
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        queryDetailStar(header, list);
        return list;
    }

    //根据主表查附表-各细项
    private void queryDetailStar(HeaderHelper.SysHeader header, List<OrgUserReportVO> list) {
        Map<Integer, String> ruleMap = queryRuleName(header.getRegionId());
        Set<Long> evalIds = list.stream().map(OrgUserReportVO::getEvalId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(evalIds)){
            return;
        }
        Example example = new Example(UserPartyEvalResultEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("evalId", evalIds);
        example.selectProperties("evalId", "ruleId", "star");
        List<UserPartyEvalResultEntity> resultAllEntities = userPartyEvalResultMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(resultAllEntities)){
            return;
        }
        Map<Long, List<UserPartyEvalResultEntity>> resultMap = resultAllEntities.stream().collect(Collectors.groupingBy(UserPartyEvalResultEntity::getEvalId));
        for (OrgUserReportVO vo : list) {
            String[] tag = (vo.getTag() == null ? null : vo.getTag().split(","));
            List<UserPartyReportVO.TypeStarVO> innerList = new ArrayList<>();
            List<UserPartyEvalResultEntity> resultEntities = resultMap.get(vo.getEvalId());
            ruleMap.forEach((k, v) -> {
                //根据eval_id和rule-id查询出星级及标签
                Integer star = Objects.requireNonNull(resultEntities.parallelStream().filter(i ->
                        Objects.equals(k, i.getRuleId())).findFirst().orElse(null)).getStar();
                UserPartyReportVO.TypeStarVO starVo = new UserPartyReportVO.TypeStarVO();
                starVo.setStar(null == star ? 0 : star);
                starVo.setType(v);
                if (null != tag) {
                    for (String ruleTag : tag) {//获取的标签去匹配rule
                        if (experienceRuleTagConfig.queryRuleTag(k).contains(ruleTag)) {
                            starVo.setTag(ruleTag);
                            break;//一个rule一个标签，因此是break
                        }
                    }
                }
                innerList.add(starVo);
            });
            vo.setDetail(innerList);
        }
    }

    public  Optional<Page<OrgUserReportVO>> reportPcDetailPageConditionNullPoint(HttpHeaders headers, ReportChooseForm chooseForm) {
        try {
            return Optional.of(reportPcDetailPageCondition(headers,chooseForm));
        } catch (BadSqlGrammarException | NullPointerException e) {
            e.printStackTrace();
            log.error("空指针异常或空指针引起的sql语法异常:"+e);
            return Optional.empty();
        }
    }

    //党员体检详情-各种筛选条件
    public List<OrgUserReportVO> reportDetailCondition(HttpHeaders headers, ReportChooseForm chooseForm) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        chooseForm.setRegionId(header.getRegionId());
        if (!StringUtils.isEmpty(chooseForm.getUserName())) {
            List<OrgUserReportVO> list = userPartyEvalMapper.queryUserReportByName(chooseForm);
            List<OrgUserReportVO> orderedList = orderQuery(chooseForm.getOrderType(), list, 50);
            queryDetailStar(header, orderedList);
            return orderedList;
        }
        if (0 == chooseForm.getType()) {//为0查询综合评级,综合评级不允许筛选标签
            List<OrgUserReportVO> list = userPartyEvalMapper.queryUserReportMore(chooseForm.getRegionId(),
                    chooseForm.getOrgId(), chooseForm.getDateMonth(), chooseForm.getDateMonthStr(), null);
            if (CollectionUtils.isEmpty(chooseForm.getStar())) {//为空表示查询所有星级
                List<OrgUserReportVO> orderedList = orderQuery(chooseForm.getOrderType(), list, 50);
                queryDetailStar(header, orderedList);
                return orderedList;
            } else {
                List<OrgUserReportVO> list1 = list.parallelStream().filter(i -> chooseForm.getStar().contains(i.getStar())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(list1)){
                    return Collections.emptyList();
                }
                List<OrgUserReportVO> orderedList = orderQuery(chooseForm.getOrderType(), list1, 50);
                queryDetailStar(header, orderedList);
                return orderedList;
            }
        }
        //根据筛选评级查出所有后根据条件进行过滤
        chooseForm.setRegionId(header.getRegionId());
        List<OrgUserReportVO> list = userPartyEvalMapper.queryReportByCondition(chooseForm);
        //移动端只展示该维度的标签--后前端处理了
//        list.forEach(i->{
//            List<String> tagDb = experienceRuleTagConfig.queryRuleTag(chooseForm.getType());
//            if(!StringUtils.isEmpty(i.getTag())){
//                boolean hasTag = false;
//                String[] tag = i.getTag().split(",");
//                for(String t : tag){
//                    if(tagDb.contains(t)){
//                        i.setTag(t);
//                        hasTag = true;
//                        break;//一个rule只有一个标签
//                    }
//                }
//                if(!hasTag){
//                    i.setTag(null);
//                }
//            }
//        });
        List<OrgUserReportVO> orderedList = orderQuery(chooseForm.getOrderType(), list, 50);
        queryDetailStar(header, orderedList);
        return orderedList;
    }


    private List<OrgUserReportVO> orderQuery(Integer orderType, List<OrgUserReportVO> list, Integer num) {
        switch (orderType) {
            case 0://综合
                if (null == num) {
                    return list.parallelStream().sorted(Comparator.comparingLong(OrgUserReportVO::getUserId)).collect(Collectors.toList());
                }
                return list.parallelStream().sorted(Comparator.comparingLong(OrgUserReportVO::getUserId)).limit(num).collect(Collectors.toList());

            case 1://星级最高
                if (null == num) {
                    return list.parallelStream().sorted(Comparator.comparing(OrgUserReportVO::getStar)
                            .thenComparing(OrgUserReportVO::getUserId).reversed()).collect(Collectors.toList());
                }else{
                    return list.parallelStream().sorted(Comparator.comparing(OrgUserReportVO::getStar)
                            .thenComparing(OrgUserReportVO::getUserId).reversed()).limit(num).collect(Collectors.toList());
                }
            case 2:
                Comparator<OrgUserReportVO> comparator = (a, b) -> {
                    if (Objects.equals(a.getStar(), b.getStar())) {//星级相同
                        return Long.compare(b.getUserId(), a.getUserId());//降序
                    } else {
                        return Integer.compare(a.getStar(), b.getStar());
                    }
                };
                if (null == num) {
                    return list.parallelStream().sorted(comparator).collect(Collectors.toList());

                }
                return list.parallelStream().sorted(comparator).limit(num).collect(Collectors.toList());

        }
        return Collections.emptyList();
    }

    private Map<Integer, String> queryRuleName(Long regionId) {
        Map<Integer, String> map = new HashMap<>();
        Example example = new Example(UserPartyEvalRuleEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("parentId", 0);
        criteria.andEqualTo("regionId",regionId);
        example.selectProperties("ruleId", "ruleName");
        example.orderBy("ruleId").asc();
        List<UserPartyEvalRuleEntity> list = userPartyEvalRuleMapper.selectByExample(example);
        for (UserPartyEvalRuleEntity ruleEntity : list) {
            map.put(ruleEntity.getRuleId(), ruleEntity.getRuleName());
        }
        return map;
    }

    //查询最小跑数月份
    public Integer queryMinMonth(HttpHeaders headers, Long orgId) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return userPartyEvalMapper.queryMinMonth(header.getRegionId(), orgId);
    }


    //五星党员占比排行榜
    public LeaderBoardVO queryLeaderBoard(HttpHeaders headers, Long orgId, Integer dateMonth) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(header.getRegionId());
        LeaderBoardVO boardVO = new LeaderBoardVO();
        if (orgId.equals(orgData.getOrgId())) {//如果是顶级党组织
            List<LeaderBoardVO> unitBoard = new ArrayList<>();
            log.debug("处理单位排行榜");
            List<OrgUserReportVO> orgInfoVOs = organizationMapper.findUnitOrg(header.getRegionId());
            Map<Long, List<OrgUserReportVO>> unitMap = orgInfoVOs.stream().collect(Collectors.groupingBy(OrgUserReportVO::getUnitId));
            List<OrgUserReportVO> evalList = userPartyEvalMapper.queryUserReportByOrgIds(header.getRegionId(), dateMonth);
            unitMap.forEach((k, v) -> {
                List<Long> orgIds = v.stream().map(OrgUserReportVO::getOrgId).collect(Collectors.toList());
                List<OrgUserReportVO> orgList = evalList.stream().filter(i -> orgIds.contains(i.getOrgId())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(orgList)){
                    unitBoard.add(new LeaderBoardVO(k,v.get(0).getUnitName(), "0.00", 0.00));
                }else{
                    BigDecimal totalNum = new BigDecimal(String.valueOf(orgList.size()));
                    BigDecimal fiveNum = new BigDecimal(String.valueOf(orgList.parallelStream().filter(i -> 5 == i.getStar()).count()));//注意
                    double proportion = fiveNum.divide(totalNum, 4, RoundingMode.CEILING).setScale(4, RoundingMode.CEILING).doubleValue();
                    unitBoard.add(new LeaderBoardVO(k,v.get(0).getUnitName(), doubleToPercent(proportion), proportion));
                }
            });
            List<LeaderBoardVO> unitOrderBoard = unitBoard.parallelStream().sorted().collect(Collectors.toList());
            boardVO.setUnitBoard(unitOrderBoard);
        }
        log.debug("处理党支部排行榜");
        List<LeaderBoardVO> orgOrderBoard = getLeaderBoardVOS(header.getRegionId(), orgId, dateMonth);
        boardVO.setOrgBoard(orgOrderBoard);
        return boardVO;
    }


    private String doubleToPercent(double percent) {
        NumberFormat nf = java.text.NumberFormat.getPercentInstance();
        nf.setMinimumFractionDigits(2);// 小数点后保留2位
        return nf.format(percent);
    }

    private List<LeaderBoardVO> getLeaderBoardVOS(Long regionId, Long orgId, Integer dateMonth) {
        String dateMonthStr = (dateMonth.toString().substring(0, 4) + "-" + dateMonth.toString().substring(4, 6));
        List<OrgUserReportVO> orgAllList = userPartyEvalMapper.queryUserReportMore(regionId, orgId, dateMonth, dateMonthStr, null);
        //过滤掉党委那些，只要支部
        log.debug("查询机构是不是支部");
        List<OrgUserReportVO> orgList = orgAllList.stream().filter(i->{
            Integer code = orgService.getById(i.getOrgId()).getOrgTypeChild();
            return orgTypeConfig.getBranchChild().contains(code);
        }).collect(Collectors.toList());
        List<LeaderBoardVO> orgBoard = new ArrayList<>();
        Map<Long, List<OrgUserReportVO>> orgMap = orgList.stream().collect(Collectors.groupingBy(OrgUserReportVO::getOrgId));
        orgMap.forEach((k, v) -> {
            LeaderBoardVO vo = null;
            if(CollectionUtils.isEmpty(v)){
                vo = new LeaderBoardVO(k,v.get(0).getOrgName(), "0.00", 0.00);
            }else{
                BigDecimal totalNum = new BigDecimal(String.valueOf(v.size()));
                BigDecimal fiveNum = new BigDecimal(String.valueOf(v.stream().filter(i -> 5 == i.getStar()).count()));//注意
                double proportion = fiveNum.divide(totalNum, 4, RoundingMode.CEILING).setScale(4, RoundingMode.CEILING).doubleValue();
                vo = new LeaderBoardVO(k,v.get(0).getOrgName(), doubleToPercent(proportion), proportion);
            }
            orgBoard.add(vo);
        });
        return orgBoard.stream().sorted().collect(Collectors.toList());
    }

    //党员体检详情-各种筛选条件-pc端
    private List<OrgUserReportVO> reportPcDetailListCondition(HttpHeaders headers, ReportChooseForm chooseForm) {
        if(1==chooseForm.getItem()){
            return Collections.emptyList();
        }
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        chooseForm.setRegionId(header.getRegionId());
        String dateMonthStr = chooseForm.getDateMonth().toString();
        chooseForm.setDateMonthStr(dateMonthStr.substring(0, 4) + "-" + dateMonthStr.substring(4, 6));
        List<OrgUserReportVO> conditionList = userPartyEvalMapper.queryReportByCondition(chooseForm);
        if(CollectionUtils.isEmpty(conditionList)){
            return Collections.emptyList();
        }
        Map<Long, List<UserEntity>> userInfoMap = setPhoneSecret(conditionList);
        if (!StringUtils.isEmpty(chooseForm.getPhone())) {
            //对手机号进行过滤
            List<OrgUserReportVO> phoneList = filterPhone(chooseForm, conditionList, userInfoMap);
            return getOrgUserReportVOS(chooseForm, phoneList);
        }
        return orderQuery(chooseForm.getOrderType(),conditionList,null);
    }



    private List<OrgUserReportVO> filterPhone(ReportChooseForm chooseForm, List<OrgUserReportVO> list, Map<Long, List<UserEntity>> userInfoMap) {
        log.debug("对手机号进行过滤");
        return list.parallelStream().filter(i -> {
            String phone = "";
            try {
                if(CollectionUtils.isEmpty(userInfoMap.get(i.getUserId()))){
                    return false;
                }
                phone = NumEncryptUtils.decrypt(userInfoMap.get(i.getUserId()).get(0).getPhone(),
                        userInfoMap.get(i.getUserId()).get(0).getPhoneSecret());
            } catch (Exception e) {
                e.printStackTrace();
            }
            return phone.contains(chooseForm.getPhone());
        }).collect(Collectors.toList());
    }

    private List<OrgUserReportVO> getOrgUserReportVOS(ReportChooseForm chooseForm, List<OrgUserReportVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        if (0 == chooseForm.getType()) {
            return starAndTagFilter(chooseForm, list);
        }
        List<Long> evalIds = list.stream().map(OrgUserReportVO::getEvalId).collect(Collectors.toList());
        chooseForm.setEvalIds(evalIds);
        List<OrgUserReportVO> conditionList = userPartyEvalMapper.queryReportByCondition(chooseForm);
        if (CollectionUtils.isEmpty(conditionList)) {
            return Collections.emptyList();
        }
        setPhoneSecret(conditionList);
        return orderQuery(chooseForm.getOrderType(), list, null);

    }


    private List<OrgUserReportVO> starAndTagFilter(ReportChooseForm chooseForm, List<OrgUserReportVO> list) {
        if (!CollectionUtils.isEmpty(chooseForm.getStar())) {
            List<OrgUserReportVO> starList = list.stream().filter(i -> chooseForm.getStar().contains(i.getStar())).collect(Collectors.toList());
            return orderQuery(chooseForm.getOrderType(), starList, null);
        }
        return orderQuery(chooseForm.getOrderType(), list, null);
    }

    private Map<Long, List<UserEntity>> setPhoneSecret(List<OrgUserReportVO> list) {
        List<UserEntity> userInfo = queryPhone(list);
        Map<Long, List<UserEntity>> userInfoMap = userInfo.stream().collect(Collectors.groupingBy(UserEntity::getUserId));
        list.forEach(i -> {
            if(null!=userInfoMap.get(i.getUserId())){
                i.setPhoneSecret(userInfoMap.get(i.getUserId()).get(0) == null ? "" : userInfoMap
                        .get(i.getUserId()).get(0).getPhoneSecret());
            }
        });
        return userInfoMap;
    }

    private List<UserEntity> queryPhone(List<OrgUserReportVO> list) {
        Set<Long> userIds = list.stream().map(OrgUserReportVO::getUserId).collect(Collectors.toSet());
        return userService.getUserInfoByUserIds(userIds);
    }

    public Page<OrgUserReportVO> reportPcDetailPageCondition(HttpHeaders headers, ReportChooseForm chooseForm) {
        List<OrgUserReportVO> allList = reportPcDetailListCondition(headers, chooseForm);
        if(CollectionUtils.isEmpty(allList)) return null;
        return this.splitPage(allList, chooseForm.getPageSize(), chooseForm.getPageNum());
    }

    //将查询结果分页
    public Page<OrgUserReportVO> splitPage(List<OrgUserReportVO> list, Integer pageSize, Integer pageNum) {
        Page<OrgUserReportVO> pageInfo = new Page<>();
        pageInfo.setPageSize(pageSize);
        pageInfo.setPageNum(pageNum);
        int total = list.size();
        pageInfo.setTotal(total);//总记录数
        int page = (total % pageSize == 0 ? total / pageSize : (total / pageSize + 1));
        pageInfo.setPages(page);//总页数
        int starRow = (pageNum - 1) * pageSize + 1;
        int endRow = (pageNum != page ? pageNum * pageSize : (int)(total / pageSize)*pageSize+total % pageSize);
        for (int i = starRow - 1; i < endRow; i++) {
            pageInfo.add(list.get(i));
        }
        return pageInfo;
    }


    //党员体检细项详情-详情(pc端)
    public List<UserPartyReportVO.TypeStarVO> reportPcItemDetail(HttpHeaders headers, Long evalId) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        UserPartyEvalEntity userPartyEvalEntity = userPartyEvalMapper.selectByPrimaryKey(evalId);
        OrgUserReportVO vo = new OrgUserReportVO();
        BeanUtils.copyProperties(userPartyEvalEntity, vo);
        List<OrgUserReportVO> list = new ArrayList<>();
        list.add(vo);
        queryDetailStar(header, list);
        return list.get(0).getDetail();
    }


    //下载查询结果
    public void reportDownLoad(HttpHeaders headers, ReportChooseForm chooseForm, HttpServletResponse httpServletResponse) {
        List<OrgUserReportVO> allList = reportPcDetailListCondition(headers, chooseForm);
        List<List<String>> list = new ArrayList<>();
        for (OrgUserReportVO reportVO : allList) {
            List<String> strList = new ArrayList<>();
            strList.add(reportVO.getUserName());
            strList.add(reportVO.getPhoneSecret());
            strList.add(reportVO.getDateMonth());
            strList.add(reportVO.getItem());
            strList.add(reportVO.getType());
            strList.add(reportVO.getStar() + "星");
            strList.add(reportVO.getTag());
            list.add(strList);
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        HSSFWorkbook workbook = new HSSFWorkbook();
        String[] header = new String[]{"姓名", "手机号", "体检月份", "体检类别", "体检维度", "体检结果", "体检标签"};
        try {
            ExcelUtils.exportExcel(workbook, 0, "党员政治体检详情表", header, list, out);
            //设置输出头
            httpServletResponse.setHeader("content-disposition", "attachment;filename=" +
                    java.net.URLEncoder.encode("党员政治体检详情表" + ".xls", "UTF-8"));
            httpServletResponse.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            httpServletResponse.setContentType("application/octet-stream;charset=UTF-8");
            workbook.write(httpServletResponse.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public HistoryReportVO reportByMonth(Integer startMonth, Integer endMonth, Long userId) {
        HistoryReportVO reportVO = new HistoryReportVO();
        List<UserPartyEvalEntity> report = userPartyEvalMapper.reportByMonth(startMonth, endMonth, userId);
        reportVO.setReport(report);
        List<UserPartyEvalEntity> lastFiveTotalList = userPartyEvalMapper.queryMonthReport(userId, startMonth, endMonth);
        List<UserPartyEvalEntity.TypeMonthStarForm> pillarChart = getTypeMonthStarForms(lastFiveTotalList);
        reportVO.setPillarChart(pillarChart);
        return reportVO;
    }

    //标签查询
    public Map<Integer, List<String>> queryTag() {
        return experienceRuleTagConfig.getRuleTag();
    }

    //头部查询
    public UserPartyEvalEntity queryHead(Long userId) {
        Example example = new Example(UserPartyEvalEntity.class);
        example.selectProperties("avatar", "userName", "orgName", "evalDate", "dateMonth",
                "totalStar", "tagAdd", "tagReduce", "evalId", "position");
        example.createCriteria().andEqualTo("userId", userId)
                .andEqualTo("calStatus", 1);
        example.setOrderByClause(" create_time desc limit 1");
        UserPartyEvalEntity userPartyEvalEntity = userPartyEvalMapper.selectOneByExample(example);
        if (null == userPartyEvalEntity) {
            return null;
        }

        //查询系统最后生成标签
        Example exampleTag = new Example(UserPartyEvalEntity.class);
        exampleTag.setOrderByClause("  create_time desc limit 1 ");
        exampleTag.createCriteria().andEqualTo("userId", userId)
                .andEqualTo("type", 1)
                .andEqualTo("calStatus", 1);
        UserPartyEvalEntity userPartyEvalSystem = userPartyEvalMapper.selectOneByExample(exampleTag);
        //重写标签
        if (null != userPartyEvalSystem && !StringUtils.isEmpty(userPartyEvalSystem.getTagAdd())) {
            userPartyEvalEntity.setTagAdd(userPartyEvalSystem.getTagAdd());
        }
        if (null != userPartyEvalSystem && !StringUtils.isEmpty(userPartyEvalSystem.getTagReduce())) {
            userPartyEvalEntity.setTagReduce(userPartyEvalSystem.getTagReduce());
        }
        return userPartyEvalEntity;
    }

    public UserPartyEvalEntity queryLastMonthEval(Long userId, Integer dateMonth) {
        if(null==dateMonth){
            DateTimeFormatter format2 = DateTimeFormatter.ofPattern("yyyyMM");
            String dateMonthStr = format2.format(LocalDate.now().minusMonths(1));
            dateMonth = Integer.parseInt(dateMonthStr);
        }
        Example example = new Example(UserPartyEvalEntity.class);
        example.selectProperties("evalId","userName",
                "totalStar", "tagAdd", "tagReduce", "evalId");
        example.createCriteria().andEqualTo("userId", userId).andEqualTo("dateMonth",dateMonth)
                .andEqualTo("type",1)
                .andEqualTo("calStatus", 1);
        example.setOrderByClause(" create_time desc limit 1");
        UserPartyEvalEntity userPartyEvalEntity = userPartyEvalMapper.selectOneByExample(example);
        if(null == userPartyEvalEntity){
            return null;
        }
        userPartyEvalEntity.setTagAdd(StringUtils.replace(userPartyEvalEntity.getTagAdd(),",","、"));
        userPartyEvalEntity.setTagReduce(StringUtils.replace(userPartyEvalEntity.getTagReduce(),",","、"));
        return userPartyEvalEntity;

    }
}