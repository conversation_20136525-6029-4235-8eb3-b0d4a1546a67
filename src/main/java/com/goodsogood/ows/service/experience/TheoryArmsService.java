package com.goodsogood.ows.service.experience;

import com.goodsogood.ows.mapper.learn.TheoryArmsMapper;
import com.goodsogood.ows.mapper.experience.UserPartyEvalDetailMapper;
import com.goodsogood.ows.mapper.meeting.experience.MeetingEvalMapper;
import com.goodsogood.ows.model.db.experience.UserPartyEvalDetailEntity;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * @description: 党性体检-理论武装
 * @author: zhangtao
 * @create: 2022-03-16 09:52
 */
@Service
@Log4j2
public class TheoryArmsService {

    private final UserPartyEvalDetailMapper userPartyEvalDetailMapper;
    private final TheoryArmsMapper theoryArmsMapper;
    private final MeetingEvalMapper meetingEvalMapper;

    @Autowired
    public TheoryArmsService(UserPartyEvalDetailMapper userPartyEvalDetailMapper,
                             TheoryArmsMapper theoryArmsMapper,
                             MeetingEvalMapper meetingEvalMapper) {
        this.userPartyEvalDetailMapper = userPartyEvalDetailMapper;
        this.theoryArmsMapper = theoryArmsMapper;
        this.meetingEvalMapper = meetingEvalMapper;
    }

    /**
     * 学习时长和学习天数
     * 累计学习时长总排名：
     * 5星：排名前10%
     * 4星：10%-30%
     * 3星：30%-50%
     * 2星：50%-80%
     * 1星：80%-100%
     */
    //@Async("meetingEvalExecutor")
    public void LearnTime(UserPartyEvalDetailEntity w) {
        try {
            log.debug("开始计算学习时长和学习天数:" + w);
            //调用学习系统计算
            int star = 1;
            //建议
            String advice = null;
            //总人数
            int sum = this.meetingEvalMapper.selectRankUserCount(w.getRegionId());
            //锤炼计划
            String value = null;
            if (w.getRuleId() == 7) {
                log.debug("开始计算学习时长:" + w);
                //学习时长

                Integer time = this.theoryArmsMapper.getLearnDay(w.getUserId());
                log.debug("学习时长为：" + time);
                if (!Objects.isNull(time) && time != 0) {
                    //总数人
                    log.debug("总人数:" + sum);
                    //排名
                    int row = this.theoryArmsMapper.getRow(time);
                    log.debug("学习时长排名：" + row);
                    double s = (double) row / sum;
                    log.debug("学习时长排名百分比:" + s);
                    if (s < 0.1) {
                        star = 5;
                    } else if (s < 0.3) {
                        star = 4;
                    } else if (s < 0.5) {
                        star = 3;
                    } else if (s < 0.8) {
                        star = 2;
                    } else {
                        star = 1;
                    }
                } else {
                    star = 1;
                }
            } else if (w.getRuleId() == 8) {
                log.debug("计算学习天数：");
                //学习天数
                Long learnDay = this.theoryArmsMapper.continuationDay(w.getUserId());
                log.debug("学习天数为：" + learnDay);
                if (!Objects.isNull(learnDay)) {
                    if (learnDay >= 7) {
                        star = 5;
                        advice = learnDay.toString();
                    } else if (learnDay >= 5) {
                        star = 4;
                    } else if (learnDay >= 3) {
                        star = 3;
                    } else if (learnDay == 2) {
                        star = 2;
                    } else {
                        star = 1;
                    }
                } else {
                    star = 1;
                }
            }
            //更新数据
            //星级
            log.debug("调试计算后星级：" + star);
            w.setStar(star);
            //星级等于5加建议，小于5加锤炼计划
            log.debug("开始判断星级是否为5星");
            if (star == 5) {
//                log.debug("如果为五星");
//                if (w.getRuleId() == 7) {
//                    log.debug("如果为学习时长");
//                    w.setParamType((byte) 10);
//                    if (StringUtils.isNotBlank(advice)) {
//                        log.debug("学习时长五星建议:" + advice);
//                        w.setParamValue(advice);
//                    }
               if (w.getRuleId() == 8) {
                    log.debug("如果为学习天数");
                    w.setParamType((byte) 1);
                    if (StringUtils.isNotBlank(advice)) {
                        log.debug("学习天数五星建议:" + advice);
                        w.setParamValue(advice);
                    }
                }
            } else {
                log.debug("如果不为五星");
                //排名前10%的学习时长
                Integer v = this.theoryArmsMapper.getTenDay(sum);
                log.debug("排名前10%的时长" + v);
                if (!Objects.isNull(v) && v != 0) {
                    w.setParamValue(v.toString());
                } else {
                    Integer n = 1;
                    w.setParamValue(n.toString());
                }
                if (w.getRuleId() == 7) {
                    w.setParamType((byte) 10);
                    w.setPlanId(2);
                } else if (w.getRuleId() == 8) {
                    w.setParamType((byte) 10);
                    w.setPlanId(3);
                }
            }
            //完成状态
            log.debug("更改其他参数");
            w.setCalStatus((byte) 1);
            w.setUpdateTime(LocalDateTime.now());
            log.debug("更改后最终的对象" + w);
            this.userPartyEvalDetailMapper.updateByPrimaryKeySelective(w);
        } catch (Exception exception) {
            log.error("计算第7、8项出现异常", exception);
        }
    }

    /**
     * 考试次数
     * 参加考试次数排名：
     * 5星：排名前10%
     * 4星：10%-30%
     * 3星：30%-50%
     * 2星：50%-80%
     * 1星：80%-100%
     */
    //@Async("meetingEvalExecutor")
    public void ExamNum(UserPartyEvalDetailEntity w) {
        try {
            log.debug("开始计算考试次数:" + w);
            int star = 0;
            //用户考试次数
            Integer examCount = this.theoryArmsMapper.getExamCount(w.getUserId());
            log.debug("用户考试次数:" + examCount);
            if (!Objects.isNull(examCount) && examCount != 0) {
                //总数人
                int sum = this.meetingEvalMapper.selectRankUserCount(w.getRegionId());
                //排名
                int row = this.theoryArmsMapper.getExam(examCount);
                double s = (double) row / sum;
                if (s < 0.1) {
                    star = 5;
                } else if (s < 0.3) {
                    star = 4;
                } else if (s < 0.5) {
                    star = 3;
                } else if (s < 0.8) {
                    star = 2;
                } else {
                    star = 1;
                }
            } else {
                star = 1;
            }
            //更新detail表
            //星级
            w.setStar(star);
            //完成状态
            w.setCalStatus((byte) 1);
            w.setUpdateTime(LocalDateTime.now());
            this.userPartyEvalDetailMapper.updateByPrimaryKeySelective(w);
        }catch (Exception exception){
            log.error("计算第9项出现异常", exception);
        }
    }

    /**
     * 考试成绩排名:
     * 5星：98分-100分
     * 4星：96分-98分
     * 3星：94分-96分
     * 2星：90分-94分
     * 1星：90分以下
     */
    //@Async("meetingEvalExecutor")
    public void ExamRanking(UserPartyEvalDetailEntity w) {
        try {
            log.debug("开始计算考试成绩排名:" + w);
            int star = 1;
            //建议
            String advice = null;
            //计算用户平均成绩
            String year = w.getDateMonth().toString().substring(0, 4);
            Double score = this.theoryArmsMapper.getAvgScore(w.getUserId(), year);
            log.debug("计算用户平均成绩:" + score);
            if (!Objects.isNull(score)) {
                if (score >= 98) {
                    star = 5;
                } else if (score >= 96) {
                    star = 4;
                } else if (score >= 94) {
                    star = 3;
                } else if (score >= 90) {
                    star = 2;
                } else {
                    star = 1;
                }
            } else {
                star = 1;
            }
            //星级
            w.setStar(star);
            if (star < 5) {
                w.setPlanId(4);
            }
            //完成状态
            w.setCalStatus((byte) 1);
            w.setUpdateTime(LocalDateTime.now());
            this.userPartyEvalDetailMapper.updateByPrimaryKeySelective(w);
        }catch (Exception exception){
            log.error("计算第10项出现异常", exception);
        }
    }

    /**
     * 学习强国积分排名
     * 年度积分排名：
     * 5星：排名前10%
     * 4星：10%-30%
     * 3星：30%-50%
     * 2星：50%-80%
     * 1星：80%-100%
     */
//    public void StudyPowerNation(UserPartyEvalDetailEntity w) {
//        int star = 1;
//        //建议
//        String advice = null;
//        //调用学习系统计算
//
//        //更新detail表
//        //星级
//        w.setStar(star);
//        w.setParamValue(advice);
//        if (star == 5) {
//            w.setParamType((byte) 11);
//        } else {
//            w.setPlanId(5);
//        }
//        //完成状态
//        w.setCalStatus((byte) 1);
//        w.setUpdateTime(LocalDateTime.now());
//        this.userPartyEvalDetailMapper.updateByPrimaryKeySelective(w);
//    }
//
//    /**
//     * 学习《中国烟草网络学院》积分排名
//     * 5星：排名前10%
//     * 4星：10%-30%
//     * 3星：30%-50%
//     * 2星：50%-80%
//     * 1星：80%-100%
//     */
//    public void StadyTobacco(UserPartyEvalDetailEntity w) {
//        int star = 1;
//        //五星学时
//        String advice = null;
//        //调用学习系统计算
//
//        //更新detail表
//        //星级
//        w.setStar(star);
//        w.setParamValue(advice);
//        if (star == 5) {
//            w.setParamType((byte) 12);
//        } else {
//            w.setPlanId(6);
//        }
//        //完成状态
//        w.setCalStatus((byte) 1);
//        w.setUpdateTime(LocalDateTime.now());
//        this.userPartyEvalDetailMapper.updateByPrimaryKeySelective(w);
//    }
}
