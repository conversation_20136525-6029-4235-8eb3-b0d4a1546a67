package com.goodsogood.ows.service.experience;

import com.goodsogood.ows.mapper.experience.UserPartyEvalDetailMapper;
import com.goodsogood.ows.model.db.experience.UserPartyEvalDetailEntity;
import com.goodsogood.ows.service.meeting.experience.MeetingEvalService;
import com.goodsogood.ows.service.user.UserLoginService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-03-11 10:52
 **/
@Service
@Log4j2
public class UserPartyEvalDetailService {

    private final UserPartyEvalDetailMapper userPartyEvalDetailMapper;
    private final MeetingEvalService meetingEvalService;
    private final UserTaskService userTaskService;
    private final TheoryArmsService theoryArmsService;
    private final UserLoginService userLoginService;

    @Autowired
    public UserPartyEvalDetailService(UserPartyEvalDetailMapper userPartyEvalDetailMapper,
                                      MeetingEvalService meetingEvalService,
                                      UserTaskService userTaskService,
                                      TheoryArmsService theoryArmsService, UserLoginService userLoginService) {
        this.userPartyEvalDetailMapper = userPartyEvalDetailMapper;
        this.meetingEvalService = meetingEvalService;
        this.userTaskService = userTaskService;
        this.theoryArmsService = theoryArmsService;
        this.userLoginService = userLoginService;
    }

    /**
     * 开始调用计算星级开始
     *
     * @param batchNumber
     */
    @Async("experienceExecutor")
    public void startCalStart(String batchNumber) {
        List<UserPartyEvalDetailEntity> waitCalcData = userPartyEvalDetailMapper.getWaitCalcData(batchNumber);
        log.info("细项计算分值开始====总数" + waitCalcData.size());
        try {
            // 各细项计算
            waitCalcData.forEach(w -> {
                switch (w.getRuleId()) {
                    case 6:
                        userTaskService.newsStudyDays(w);
                        break;
                    case 7:
                        //学习时长
                        theoryArmsService.LearnTime(w);
                        break;
                    case 8:
                        //连续学习天数
                        theoryArmsService.LearnTime(w);
                        break;
                    case 9:
                        //考试次数
                        theoryArmsService.ExamNum(w);
                        break;
                    case 10:
                        //考试成绩
                        theoryArmsService.ExamRanking(w);
                        break;
                    case 11:
                        //《学习强国》
                        break;
                    case 12:
                        //《中国烟草网络学习》
                        break;
                    case 13:
                    case 14:
                    case 15:
                        userTaskService.partyConstructionTask(w);
                        break;
                    case 16:
                        userTaskService.cloudPartyBranchTasks(w);
                        break;
                    case 17:
                        userTaskService.bearTheTask(w);
                        break;
                    case 18:
                        //党员主动讲党课
                        meetingEvalService.prelection(w);
                        break;
                    case 19:
                        //按时参加党员大会
                        meetingEvalService.partyPlenary(w);
                        break;
                    case 20:
                        //按时参加党小组会
                        meetingEvalService.groupPlenary(w);
                        break;
                    case 21:
                        //按时参加党课
                        meetingEvalService.partyClass(w);
                        break;
                    case 22:
                        //按时参加主题党日活动
                        meetingEvalService.thematicPartyDay(w);
                        break;
                    case 23:
                        break;
                    case 24:
                        break;
                    case 25:
                        break;
                    case 26:
                        // 查询当月连续登录次数-坚持登录数智党建平台
                        userLoginService.calContinuouslyDays(w);
                        break;
                    case 27:
                        // 本月登录数智党建平台天数
                        userLoginService.calMonthTotalDays(w);
                        break;
                    case 28:
                        // 本季度登录数智党建平台天数
                        userLoginService.calQuarterTotalDays(w);
                        break;
                    case 29:
                        // 本年度登录数智党建平台天数
                        userLoginService.calYearTotalDays(w);
                        break;
                    default:
                        log.error("无效的指标类型:" + w.getRuleId());
                }
            });
        } catch (Exception ex) {
            log.error("细项计算分值异常:", ex);
        }
    }

}