package com.goodsogood.ows.service.experience;

import com.goodsogood.ows.mapper.experience.UserPartyEvalAdviceMapper;
import com.goodsogood.ows.mapper.experience.UserPartyEvalResultMapper;
import com.goodsogood.ows.model.vo.experience.UserPartyListVO;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-03-11 10:52
 **/
@Service
@Log4j2
public class UserPartyEvalAdviceService {

    private final UserPartyEvalResultMapper userPartyEvalResultMapper;


    @Autowired
    public UserPartyEvalAdviceService(UserPartyEvalResultMapper userPartyEvalResultMapper) {
        this.userPartyEvalResultMapper = userPartyEvalResultMapper;
    }

    /**
     * 意见建议
     * @param headers head
     * @param evalId 主表id
     * @return 意见建议list
     */
    public List<UserPartyListVO> queryAdviceList(HttpHeaders headers, Long evalId) {
        List<UserPartyListVO> result = userPartyEvalResultMapper.queryAdvice(evalId);
        //拼接参数
        result.forEach(r->{
            if(StringUtils.isNotBlank(r.getParamValue())){
                String[] params = r.getParamValue().split(",");
                r.setPlanStr(String.format(r.getPlanStr(), params));//注意这里不能使用建议的(Object)强转！！
                r.setParamValue(null);
            }
        });
        return result;
    }
}