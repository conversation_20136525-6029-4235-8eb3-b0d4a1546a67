package com.goodsogood.ows.service.experience;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.experience.*;
import com.goodsogood.ows.model.db.experience.UserPartyEvalEntity;
import com.goodsogood.ows.model.db.experience.UserPartyEvalResultEntity;
import com.goodsogood.ows.model.vo.experience.UserPartyListVO;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-03-11 10:52
 **/
@Service
@Log4j2
public class UserPartyEvalPlanService {

    private final UserPartyEvalPlanMapper userPartyEvalPlanMapper;
    private final UserPartyEvalMapper userPartyEvalMapper;
    private final UserPartyEvalDetailMapper userPartyEvalDetailMapper;
    private final UserPartyEvalResultMapper userPartyEvalResultMapper;
    private final UserPartyEvalRuleMapper userPartyEvalRuleMapper;

    @Autowired
    public UserPartyEvalPlanService(UserPartyEvalPlanMapper userPartyEvalPlanMapper, UserPartyEvalMapper userPartyEvalMapper, UserPartyEvalDetailMapper userPartyEvalDetailMapper, UserPartyEvalResultMapper userPartyEvalResultMapper, UserPartyEvalRuleMapper userPartyEvalRuleMapper) {
        this.userPartyEvalPlanMapper = userPartyEvalPlanMapper;
        this.userPartyEvalMapper = userPartyEvalMapper;
        this.userPartyEvalDetailMapper = userPartyEvalDetailMapper;
        this.userPartyEvalResultMapper = userPartyEvalResultMapper;
        this.userPartyEvalRuleMapper = userPartyEvalRuleMapper;
    }


    /**
     * 锤炼计划列表查询
     * @param evalId 主表id
     * @return 锤炼计划list
     */
    public List<UserPartyListVO> queryPlanList(HttpHeaders headers, Long evalId) {
        List<UserPartyListVO> plans = new ArrayList<>();
        if(null==evalId){//直接点击锤炼计划，展示该用户最近一次的锤炼计划
            HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
            log.debug("查询最近一次的月度评测的task_id");
            String taskId = userPartyEvalMapper.queryLastMonthEval(header.getUserId());
            queryPlanVo(taskId,plans);
        }else{
            log.debug("根据eval_id查询task_id："+evalId);
            UserPartyEvalEntity userPartyEvalEntity = userPartyEvalMapper.selectByPrimaryKey(evalId);
            if(userPartyEvalEntity.getType()==2){//手动测评没有锤炼计划
                return null;
            }
            String taskId = userPartyEvalMapper.selectByPrimaryKey(evalId).getTaskId();
            queryPlanVo(taskId,plans);
        }
        return plans;
    }


   //根据taskid查询锤炼计划，没有用group_concat，锤炼计划不确定分隔符
    public void queryPlanVo(String taskId, List<UserPartyListVO> plans) {
        if(StringUtils.isBlank(taskId)){
            return;
        }
        List<UserPartyListVO> list = userPartyEvalDetailMapper.queryPlanList(taskId);
        Map<String,List<UserPartyListVO>> map = list.stream().collect(Collectors.groupingBy(UserPartyListVO::getType));
        for (Map.Entry<String, List<UserPartyListVO>> entry : map.entrySet()) {
            UserPartyListVO vo = new UserPartyListVO();
            vo.setType(entry.getKey());
            //处理plan参数拼接
            List<String> plan = entry.getValue().stream().map(planVo->{
                String param = planVo.getParamValue();
                if(StringUtils.isNotBlank(param)){
                    return String.format(planVo.getPlanStr(), param);
                }
                return planVo.getPlanStr();
            }).distinct().collect(Collectors.toList());
            vo.setPlan(plan);
            plans.add(vo);
        }
    }

}