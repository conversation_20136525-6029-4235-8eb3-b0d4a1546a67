package com.goodsogood.ows.service.experience;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.experience.UserPartyEvalMapper;
import com.goodsogood.ows.mapper.experience.UserPartyEvalResultMapper;
import com.goodsogood.ows.model.db.experience.UserPartyEvalEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.experience.UserPartyListVO;
import com.goodsogood.ows.model.vo.experience.UserPartyReportVO;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-03-11 10:52
 **/
@Service
@Log4j2
public class UserPartyEvalResultService {

    private final UserPartyEvalResultMapper userPartyEvalResultMapper;
    private final UserPartyEvalMapper userPartyEvalMapper;
    private final UserPartyEvalPlanService userPartyEvalPlanService;
    private final Errors errors;

    @Autowired
    public UserPartyEvalResultService(UserPartyEvalResultMapper userPartyEvalResultMapper, UserPartyEvalMapper userPartyEvalMapper, UserPartyEvalPlanService userPartyEvalPlanService, Errors errors) {
        this.userPartyEvalResultMapper = userPartyEvalResultMapper;
        this.userPartyEvalMapper = userPartyEvalMapper;
        this.userPartyEvalPlanService = userPartyEvalPlanService;
        this.errors = errors;
    }

    /**
     * 查询体检报告
     * @param headers 头
     * @return 报告详情
     */
    public ResponseEntity<Result<?>> queryReport(HttpHeaders headers, Long evalId)  {
        UserPartyEvalEntity evalEntity = null;
        UserPartyReportVO userPartyReportVO = new UserPartyReportVO();
        if(null==evalId){
            try {
                TimeUnit.SECONDS.sleep(2);
            } catch (InterruptedException e) {
                log.error("interruptException"+e);
                Thread.currentThread().interrupt();
            }
            log.debug("mt开始查询0");
            HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
            Example example = new Example(UserPartyEvalEntity.class);
            example.createCriteria().andEqualTo("userId", header.getUserId())
                    .andEqualTo("type", 2)
                    .andEqualTo("calStatus", 0);
            //如果存在还在当中
            int countNumber = userPartyEvalMapper.selectCountByExample(example);
            if (countNumber > 0) {
                return new ResponseEntity<Result<?>>(new Result<>(-1, errors), HttpStatus.OK);
            }
            log.debug("mt开始查询");
            example.clear();
            example.createCriteria().andEqualTo("userId", header.getUserId())
                    .andEqualTo("calStatus", 1).andEqualTo("type",2);
            example.setOrderByClause(" create_time desc limit 1");
            evalEntity = userPartyEvalMapper.selectOneByExample(example);
            log.debug("mt开始查询:"+evalEntity);
            evalId = evalEntity.getEvalId();
            log.debug("mt开始查询:"+evalId);
        }else{
            evalEntity = userPartyEvalMapper.selectByPrimaryKey(evalId);
        }
        BeanUtils.copyProperties(evalEntity,userPartyReportVO);
        log.debug("mt查询"+userPartyReportVO);
        log.debug("处理雷达图");
        userPartyReportVO.setTypeStar(userPartyEvalResultMapper.queryRedaChart(evalId));//雷达图
        log.debug("处理项目展示");
        userPartyReportVO.setRule(queryRuleList(evalId));
        if(2==evalEntity.getType()){
            return new ResponseEntity<Result<?>>(new Result<>(userPartyReportVO,errors),HttpStatus.OK);
        }
        log.debug("处理锤炼计划");
        String taskId = evalEntity.getTaskId();
        List<UserPartyListVO> plans = new ArrayList<>();
        userPartyEvalPlanService.queryPlanVo(taskId,plans);
        userPartyReportVO.setPlan(plans);
        log.debug("mt查询ss_"+userPartyReportVO);
        return new ResponseEntity<Result<?>>(new Result<>(userPartyReportVO,errors),HttpStatus.OK);
    }

    /**
     * 查询大项下的细项规则
     * @param evalId 主表id
     * @return 细项规则
     */
    private List<UserPartyReportVO.RuleVO> queryRuleList(Long evalId){
        List<UserPartyReportVO.RuleVO> ruleVOS =  userPartyEvalResultMapper.queryRuleList(evalId);
        List<UserPartyReportVO.RuleVO> rules = new ArrayList<>();
        //分组后将各项细则与大则对应
        Map<String,List<UserPartyReportVO.RuleVO>> map = ruleVOS.stream().collect(
                Collectors.groupingBy(UserPartyReportVO.RuleVO::getType,LinkedHashMap::new,Collectors.toList()));
        for(Map.Entry<String, List<UserPartyReportVO.RuleVO>> entry : map.entrySet()){
            UserPartyReportVO.RuleVO rule = new UserPartyReportVO.RuleVO();
            rule.setType(entry.getKey());
            rule.setStar(entry.getValue()==null?0:entry.getValue().get(0).getStar());
            List<UserPartyReportVO.RuleVO> inner = new ArrayList<>();
            entry.getValue().forEach(i->inner.add(new UserPartyReportVO.RuleVO(i.getRuleName(),i.getRuleStar())));
            rule.setRules(inner);
            rules.add(rule);
        }
        return rules;
    }
}