package com.goodsogood.ows.service.experience;

import com.goodsogood.ows.mapper.experience.UserPartyEvalRuleMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2022-03-11 10:52
 **/
@Service
@Log4j2
public class UserPartyEvalRuleService {

    private final UserPartyEvalRuleMapper userPartyEvalRuleMapper;

    @Autowired
    public UserPartyEvalRuleService(UserPartyEvalRuleMapper userPartyEvalRuleMapper) {
        this.userPartyEvalRuleMapper = userPartyEvalRuleMapper;
    }

}