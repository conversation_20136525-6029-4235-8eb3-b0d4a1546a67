package com.goodsogood.ows.service.experience

import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.mapper.experience.UserPartyEvalDetailMapper
import com.goodsogood.ows.mapper.meeting.experience.MeetingEvalMapper
import com.goodsogood.ows.mapper.task.TaskAccessMapper
import com.goodsogood.ows.mapper.user.UserMapper
import com.goodsogood.ows.model.db.experience.UserPartyEvalDetailEntity
import com.goodsogood.ows.model.mongodb.clickSpy.NewsListEntity
import com.goodsogood.ows.model.vo.news.NewsListFrom
import com.goodsogood.ows.utils.JsonUtils
import com.goodsogood.ows.utils.MongoAggProjectUtils.dateToISODate
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.AggregationOptions
import org.springframework.data.mongodb.core.aggregation.DateOperators
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.util.*
import kotlin.math.roundToInt

/**
 * 用户担当作为 数据统计
 * <AUTHOR>
 * @date 2022/3/16
 */
@Service
class UserTaskService(
    @Autowired val taskAccessMapper: TaskAccessMapper,
    @Autowired val meetingEvalMapper: MeetingEvalMapper,
    @Autowired val userPartyEvalDetailMapper: UserPartyEvalDetailMapper,
    @Autowired val userMapper: UserMapper,
    @Autowired val mongoTemplate: MyMongoTemplate,
) {
    private val log = LoggerFactory.getLogger(UserTaskService::class.java)

    /**
     * 参与完成任务系统派发的“党建任务”后获得评价
     * 参与完成任务系统派发的“业务任务”后获得评价
     * 参与完成任务系统派发的“创新任务”后获得评价
     * 5星：总体评价获五星评价
     * 4星：总体评价获四星评价
     * 3星：总体评价获三星评价
     * 2星：总体评价获二星评价
     * 1星：总体评价获一星评价
     */
    fun partyConstructionTask(entity: UserPartyEvalDetailEntity) {
        try {
            val taskClass = when(entity.ruleId){
                13 -> 13 //党建任务
                14 -> 15 // 业务任务
                else -> 14 // 创新任务
            }
            log.debug("开始任务体检断点一=>【${JsonUtils.toJson(entity)}】")
            val year = entity.dateMonth.toString().substring(0, 4)
            val taskFroms = taskAccessMapper.partyConstructionTask(taskClass, entity.userId, year)
            log.debug("开始任务体检断点二=>【${JsonUtils.toJson(taskFroms)}】")
            // 该用户的排名
            val number = taskAccessMapper.getNumberOfStars(taskClass, entity.userId, year)
            val rank = if (taskFroms.isEmpty()) {
                // 查看用户是否存在列表
                if (taskAccessMapper.findUserExist(taskClass, entity.userId, year) > 0) 1 else 0
            } else {
                taskFroms.size + 1
            }
            if (null != entity.star && entity.star == 5){
                if(null != entity.planId){
                    entity.planId = null
                }
                entity.paramValue = null
            }
            log.debug("开始任务体检断点三=>【${rank}】,【${number}】")
            countStar(entity, rank,  number)
            log.debug("开始任务体检断点四=>【${JsonUtils.toJson(entity)}】")
        }catch (e: Exception) {
            log.error("计算第15项出现异常", e)
        }

    }

    /**
     * 主动报名参与云上党支部任务
     * 完成次数排名
     * 5星：排名前10%
     * 4星：10%-30%
     * 3星：30%-50%
     * 2星：50%-80%
     * 1星：80%-100%
     */
    fun cloudPartyBranchTasks(entity: UserPartyEvalDetailEntity) {
        try {
            log.debug("主动报名参与云上党支部任务断点一=>【${JsonUtils.toJson(entity)}】")
            val year = entity.dateMonth.toString().substring(0, 4)
            val taskFroms = taskAccessMapper.ecpUserTask(year, entity.userId)
            log.debug("主动报名参与云上党支部任务断点二=>【${JsonUtils.toJson(taskFroms)}】")
            // 该用户的排名
            val number: Int? = taskAccessMapper.findEcpTaskExist(year, entity.userId)//完成任务次数
            val rank = if (taskFroms.isEmpty()) {
                // 查看用户是否存在列表
                if (null != number && number > 0) 1 else 0
            } else {
                taskFroms.size + 1
            }
            log.debug("主动报名参与云上党支部任务断点三=>【${rank}】")
            log.debug("开始任务体检断点五=>【${number}】")
            countStar(entity = entity, rank = rank, number = number)
            log.debug("主动报名参与云上党支部任务断点四=>【${JsonUtils.toJson(entity)}】")
        }catch (e:Exception){
            log.error("计算第16项出现异常", e)
        }
    }

    /**
     * 主动担当任务系统派发的各级任务
     * 5星
     */
    fun bearTheTask(entity: UserPartyEvalDetailEntity) {
        //如果该用户是管理员则 = 5 星
        val flag = (null !=userMapper.isAdmin(entity.userId) || null!= userMapper.isPartyGroupAdmin(entity.userId))
        if (flag) {
            //是管理员
            entity.star = 5
        } else {
            entity.star = 0
        }
        entity.calStatus = 1
        entity.updateTime = LocalDateTime.now()
        userPartyEvalDetailMapper.updateByPrimaryKeySelective(entity)
    }

    /**
     * 在数智党建平台上学习习近平新时代中国特色社会主义思想和党的创新理念
     */
    fun newsStudyDays(entity: UserPartyEvalDetailEntity) {
        try {
            log.debug("查询新闻中心用户看新闻的数据断点一【${JsonUtils.toJson(entity)}】")
            // 给出的月开始时间
            val beginTime = getLastMonthStartTime(entity.dateMonth.toString(), 1)
            // 给出的月结束时间
            val endTime = getLastMonthStartTime(entity.dateMonth.toString(), 2)
            log.debug("查询新闻中心用户看新闻的数据断点二【${beginTime},${endTime}】")
            val criteria = Criteria.where("type").`is`("A")
                .and("regionId").`is`(entity.regionId)
                .and("prefix").`in`("1", "4", "13", "18")
                .and("userId").`is`(entity.userId.toString())
                .and("transferTime").gt(dateToISODate(beginTime)).lt(dateToISODate(endTime))
            val aggregationOptions = AggregationOptions.builder()
                .allowDiskUse(true)
                .build()
            val aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.project("transferTime")
                    .and(DateOperators.DateToString.dateOf("transferTime").toString("%Y-%m-%d"))
                    .`as`("date"),
                Aggregation.group("date").count().`as`("count"),
                Aggregation.project("date", "count").and("date").previousOperation()
            ).withOptions(aggregationOptions)
            log.debug("查询新闻中心用户看新闻的数据断点三【${aggregation}】")
            val newsList = mongoTemplate.aggregate(
                aggregation,
                mongoTemplate.getCollectionName(NewsListEntity::class.java),
                NewsListFrom::class.java).mappedResults
            log.debug("查询新闻中心用户看新闻的数据断点四【${newsList}】")
            if (newsList.isNotEmpty()) {
                // 获取这个月的最后一天日期号
                val year = entity.dateMonth.toString().substring(0, 4).toInt()
                //从0开始计算所以减一
                val month = entity.dateMonth.toString().substring(4, 6).toInt() - 1
                val calendar = Calendar.getInstance() // 获取当前日期
                calendar[Calendar.YEAR] = year
                calendar[Calendar.MONTH] = month
                val day = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
                log.debug("查询新闻中心用户看新闻的数据断点六【${JsonUtils.toJson(newsList)}】")
                when (newsList.size) {
                    day -> entity.star = 5
                    in 20.until(day) -> entity.star = 4
                    in 15..19 -> entity.star = 3
                    in 10..14 -> entity.star = 2
                    in 5..9 -> entity.star = 1
                    else -> entity.star = 0
                }
            } else {
                entity.star = 0
            }
            entity.calStatus = 1
            entity.planId = 1
            entity.updateTime = LocalDateTime.now()
            userPartyEvalDetailMapper.updateByPrimaryKeySelective(entity)
            log.debug("查询新闻中心用户看新闻的数据断点五【${JsonUtils.toJson(entity)}】")
        } catch (e: Exception) {
            entity.calStatus = 1
            entity.planId = 1
            entity.updateTime = LocalDateTime.now()
            userPartyEvalDetailMapper.updateByPrimaryKeySelective(entity)
            log.error("计算第6项出现异常", e);
        }
    }


    /**
     * 计算星级
     * @param rank 排名名次
     * @param number 次数
     */
    fun countStar(entity: UserPartyEvalDetailEntity, rank: Int, number: Int? = null) {
        val count = meetingEvalMapper.selectRankUserCount(entity.regionId)
        val num1 = (count * 0.1).roundToInt()
        val num2 = (count * 0.3).roundToInt()
        val num3 = (count * 0.5).roundToInt()
        val num4 = (count * 0.8).roundToInt()
        val num5 = (count * 1)
        if (rank == 0) {
            //没有就默认1星
            entity.star = 1
        } else {
            when (rank) {
                in 1..num1 -> {
                    //设置建议
                    entity.star = 5
                    entity.paramValue = number?.toString()
                }
                in num1..num2 -> entity.star = 4
                in num2..num3 -> entity.star = 3
                in num3..num4 -> entity.star = 2
                in num4..num5 -> entity.star = 1
            }
        }
        //修改考核结果细项表
        if (entity.star != 5) {
            when (entity.ruleId) {
                13 -> {
                    entity.planId = 7
                }
                14 -> {
                    entity.planId = 9
                }
                15 -> {
                    entity.planId = 8
                }
                16 -> {
                    entity.planId = 10
                }
            }
        }
        entity.calStatus = 1
        entity.updateTime = LocalDateTime.now()
        userPartyEvalDetailMapper.updateByPrimaryKey(entity)
    }


    @Throws(Exception::class)
    fun getLastMonthStartTime(dateTime: String, flag: Int): Date {
        val year = dateTime.substring(0, 4).toInt()
        //从0开始计算所以减一
        var month = dateTime.substring(4, 6).toInt() - 1
        month = if(month != 1) 12 else month - 1
        val calendar = Calendar.getInstance() // 获取当前日期
        calendar[Calendar.YEAR] = year
        calendar[Calendar.MONTH] = month
        when (flag) {
            1 -> {
                calendar[Calendar.DAY_OF_MONTH] = 1 // 设置为1号,当前日期既为本月第一天
                calendar[Calendar.HOUR_OF_DAY] = 0
                calendar[Calendar.MINUTE] = 0
                calendar[Calendar.SECOND] = 0
                calendar[Calendar.MILLISECOND] = 0
            }
            2 -> {
                calendar[Calendar.DAY_OF_MONTH] = calendar.getActualMaximum(Calendar.DAY_OF_MONTH) // 当前日期既为本月最后一天
                calendar[Calendar.HOUR_OF_DAY] = 23
                calendar[Calendar.MINUTE] = 59
                calendar[Calendar.SECOND] = 59
                calendar[Calendar.MILLISECOND] = 0
            }
        }
        return Date(calendar.timeInMillis)
    }
}