package com.goodsogood.ows.service

import com.goodsogood.ows.configuration.OrgTypeConfig
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.helper.LocalDateTimeUtils
import com.goodsogood.ows.mapper.activity.DataVActivityMapper
import com.goodsogood.ows.mapper.meeting.DataVMeetingMapper
import com.goodsogood.ows.mapper.ppmd.PpmdStatisticsMapper
import com.goodsogood.ows.mapper.user.OrganizationMapper
import com.goodsogood.ows.mapper.user.UserMapper
import com.goodsogood.ows.model.vo.overview.KeyValVo
import com.goodsogood.ows.model.vo.overview.PpmdStaResultVo
import com.goodsogood.ows.model.vo.overview.PpmdStaVo
import com.goodsogood.ows.model.vo.user.DataVUserMapper
import com.goodsogood.ows.repository.PartyBrandRepository
import com.goodsogood.ows.service.sas.OpenService
import com.goodsogood.ows.utils.DateUtils
import com.goodsogood.ows.utils.NumberUtils
import com.google.common.math.DoubleMath.roundToLong
import org.apache.ibatis.annotations.Param
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Service
import java.io.IOException
import java.time.LocalDate
import java.util.*
import java.util.stream.Collectors
import javax.validation.constraints.NotNull
import kotlin.math.roundToLong

/**
 * <AUTHOR>
 * @date 2024/2/1
 * @description class DataVService
 */
@Service
class DataVService(
    val userMapper: UserMapper,
    val organizationMapper: OrganizationMapper,
    val ppmdStatisticsMapper: PpmdStatisticsMapper,
    val datavUserMapper: DataVUserMapper,
    val dataVMeetingMapper: DataVMeetingMapper,
    val dataVActivityMapper: DataVActivityMapper,
//    val mongoTemplate: MongoTemplate,
    val partyBrandRepository: PartyBrandRepository,
    val openService: OpenService,
    val orgTypeConfig: OrgTypeConfig,
) {
    private var log = LoggerFactory.getLogger(DataVService::class.java)

    @Value("\${overview.exclude-org-ids}")
    private val excludeOrgIds = "1294,5,2409,2417,1222,1255"

    /**
     * 通过unitId和regionId换取单位对应的顶级组织id
     * @param unitId 单位id
     * @param regionId 区域id
     * @return 组织id
     */
    fun getOrgIdByUnitId(unitId: Long, regionId: Long): Long? {
        return organizationMapper.findLevelOneOrgByOwner(unitId, regionId)?.organizationId
    }

    /**
     * 通过adcode和regionId换取单位对应的顶级组织id
     * @param adcode 区域编码
     * @param regionId 区域id
     */
    fun getOrgIdsByAdCode(adcode: String, regionId: Long): List<Long> {
        return organizationMapper.findOrgByUnitADCode(adcode, regionId).map { org ->
            val o = org?.organizationId?.let { oid ->
                organizationMapper.findLevelOneOrgByOwner(oid, regionId)?.organizationId ?: -1
            }
            o ?: -1
        }.filter { oid -> oid != -1L }
    }

    /**
     * 通过adcode和regionId换取单位对应的单位id
     * @param adcode 区域编码
     * @param regionId 区域id
     */
    fun getUnitByInitADCode(adcode: String, regionId: Long): List<Long> {
        return organizationMapper.findOrgByUnitADCode(adcode, regionId).map {
            it?.organizationId ?: -1
        }.filter { oid -> oid != -1L }
    }

    /**
     * 获取大屏数据
     *  @param unitId 单位id
     *  @param orgId 组织id
     *  @return map
     */
    fun findPartyMember(unitId: Long, orgId: Long): Map<String, Any> {
        // 水滴图 - 党员人数 / 党员 + 非党员 人数
        // 党员翻牌器
        // 非党员翻牌器
        val partyMember = datavUserMapper.userPartyMember(orgId, excludeOrgIds.split(","), unitId)
        val total = partyMember.sumOf { it.second }
        val party = partyMember.sumOf {
            if (it.first == "正式党员" || it.first == "预备党员") {
                it.second
            } else {
                0
            }
        }
        partyMember.add("总人数" to total)
        partyMember.add("党员" to party)
        return partyMember.toMap()
    }

    /**
     * 获取党员性别数据
     * @param orgId 组织id
     * @return list
     */
    fun partyMemberGender(orgId: Long): List<KeyValVo> {
        return userMapper.staPartyMemberGender(orgId, excludeOrgIds)
    }

    /**
     * 获取党员学历分布
     * @param orgId 组织id
     * @param year 新发展党员的年份
     * @return map
     */
    fun education(orgId: Long, year: Int?): Map<String, List<Pair<String, Any>>> {
        // 党员的学历分布
        val partyMember = datavUserMapper.userEducation(orgId, excludeOrgIds.split(","), null)
        // 新发展的党员的学历分布
        val partyMemberNew =
            datavUserMapper.userEducation(orgId, excludeOrgIds.split(","), year ?: LocalDate.now().year)
        // 领导的学历分布
        val partyLeader = datavUserMapper.userEducation(orgId, excludeOrgIds.split(","), null, true)
        return mapOf(
            "partyMember" to partyMember,
            "partyMemberNew" to partyMemberNew,
            "partyLeader" to partyLeader
        )
    }

    /**
     * 获取党员的序列分别
     * @param orgId 组织id
     * @param year 新发展党员的年份
     * @return map
     */
    fun sequence(orgId: Long, year: Int?): Map<String, List<Pair<String, Any>>> {
        // 党员的序列分布
        val partyMember = datavUserMapper.userSequence(orgId, excludeOrgIds.split(","), null)
        // 新发展的党员的序列分布
        val partyMemberNew =
            datavUserMapper.userSequence(orgId, excludeOrgIds.split(","), year ?: LocalDate.now().year)
        // 领导的序列分布
        val partyLeader = datavUserMapper.userSequence(orgId, excludeOrgIds.split(","), null, true)
        return mapOf(
            "partyMember" to partyMember,
            "partyMemberNew" to partyMemberNew,
            "partyLeader" to partyLeader
        )
    }

    /**
     * 获取发展党员数据
     * @param unitId 单位id，可以为空，为空查询全部
     * @param year 年份id，可以为空，默认位本年
     * @param regionId 区域id
     * @return map
     */
    fun partyMemberDevelop(unitId: Long?, year: Int?, regionId: Long): Map<String, Any> {
        val data = datavUserMapper.userDevelopCount(unitId, excludeOrgIds.split(","), year ?: LocalDate.now().year)
        // 发展党员总数
        val total = data.sumOf { it.second }
        // 连续两年未发展的党员单位
        val noDevelop = userOrgPeriodMemberNotDeveloped(regionId, 2).size
        return mapOf(
            "total" to total,
            "noDevelop" to noDevelop,
            "data" to data
        )
    }

    /**
     * 获取 承诺践诺/责任区/志愿服务/示范岗 数量
     * @param unitId 单位id
     * @return list of Pair
     */
    fun partyMemberPromise(unitId: Long?): List<Pair<String, Long>> {
        return datavUserMapper.userPromiseCount(unitId)
    }

    /**
     * 领导干部年龄分布
     */
    fun userOrgPeriodMemberAgeDistributed(
        @Param("orgId") orgId: Long,
    ): List<Pair<String, Long>> {
        return datavUserMapper.userOrgPeriodMemberAgeDistributed(orgId, excludeOrgIds.split(","))
    }

    /**
     * 查询连续n年内未发展党员的单位
     * @param regionId regionId
     * @param year year(连续多少年)
     */
    fun userOrgPeriodMemberNotDeveloped(
        @Param("regionId") regionId: Long,
        @Param("year") year: Int,
    ): List<Long> {
        // 通过year的值，获取当前年往前推year年的年份（含当前年）
        val years = (0 until year).map { LocalDate.now().year - it }
        return datavUserMapper.getNoDevelopOrgIdsByYears(
            regionId,
            excludeOrgIds.split(","),
            orgTypeConfig.branchChild,
            years
        )
    }

    /**
     * 通过adcode和unitId获取实际的组织id和单位id
     * @param unitId 单位id
     * @param adcode 区域编码
     * @param myHeaders 请求头
     * @return unitId : orgId
     */
    fun getOrgIdAndUnitId(unitId: Long?, adcode: String?, myHeaders: HeaderHelper.SysHeader): Pair<Long?, Long?> {
        var unid: Long? = unitId ?: -1L
        val orgIds = adcode?.let {
            if (adcode != "500000") { // 只有不是重庆市全区的时候，才需要去获取区县的组织id和对应的单位id
                unid = getUnitByInitADCode(it, myHeaders.regionId).firstOrNull()
                getOrgIdsByAdCode(it, myHeaders.regionId)
            } else {
                unid = 2L
                listOf()
            }
        }
        // TODO 这里不太对，应该是获取当前adcode下所有的组织id的数据求和，但是这样改动太大了，先放这里吧
        val orgId = if ((orgIds?.size ?: 0) > 0) {
            orgIds?.get(0) ?: -1
        } else {
            unid?.let { getOrgIdByUnitId(it, myHeaders.regionId) ?: -1 }
        }
        return Pair(unid, orgId)
    }

    /**
     * 获取党费交纳情况
     * @param orgId 组织id
     */
    fun getPpmdPayRate(orgId: Long): PpmdStaVo {
        // 先获得本月和上月需要交纳的人数
        var thisMonth =
            ppmdStatisticsMapper.getOverviewPpmdSta(orgId, LocalDateTimeUtils.toDay(LocalDate.now()))?.totalNum ?: 1
        // 本月应交人数
        thisMonth = if (thisMonth <= 0) {
            1
        } else {
            thisMonth
        }
        var lastMonth =
            ppmdStatisticsMapper.getOverviewPpmdSta(
                orgId,
                LocalDateTimeUtils.toDay(LocalDate.now().minusMonths(1))
            )?.totalNum
                ?: 1
        // 上月应交人数
        lastMonth = if (lastMonth <= 0) {
            1
        } else {
            lastMonth
        }
        // 本月交纳情况
        val overviewCurrentMonth =
            ppmdStatisticsMapper.getPayNumByDate(orgId, LocalDateTimeUtils.toDay(LocalDate.now()))
        // 上月交纳情况
        val overviewLastMonth =
            ppmdStatisticsMapper.getPayNumByDate(orgId, LocalDateTimeUtils.toDay(LocalDate.now().minusMonths(1)))
        val listSeries: MutableList<PpmdStaVo.Series> = ArrayList()

//        //本月统计
//        val seriesCurrentMonth = PpmdStaVo.Series()
//        seriesCurrentMonth.name = "本月"
//        var currentYear = DateUtils.getCurrentYear()
//        var month = DateUtils.getMonth(Date())
//
//        var daysByYearMonth = LocalDate.now().dayOfMonth
//
//        val listCurrentMonth: List<Int> = createListData(daysByYearMonth)
//        val listCurrentData: MutableList<Double> = LinkedList()
//        listCurrentMonth.forEach(Consumer { item: Int ->
//            val collect = overviewCurrentMonth.stream()
//                .filter { it: PpmdStaResultVo -> it.payDate <= item }
//                .collect(Collectors.toList())
//            val sumResult = collect.stream()
//                .mapToDouble { obj: PpmdStaResultVo -> obj.payAlready }.sum()
//            listCurrentData.add(NumberUtils.divide(sumResult, thisMonth.toDouble(), 2))
//        })
//
//        seriesCurrentMonth.data = listCurrentData
//        listSeries.add(seriesCurrentMonth)
//        // 上月统计
//        val seriesLastMonth = PpmdStaVo.Series()
//        seriesLastMonth.name = "上月"
//        if (month == 1) {
//            currentYear -= 1
//            month = 12
//        } else {
//            month -= 1
//        }
//        daysByYearMonth = DateUtils.getDaysByYearMonth(currentYear, month)
//        val listLastMonth = createListData(daysByYearMonth)
//        val listLastData: MutableList<Double> = LinkedList()
//        listLastMonth.forEach(Consumer { item: Int ->
//            val collect = overviewLastMonth.stream()
//                .filter { it: PpmdStaResultVo -> it.payDate <= item }
//                .collect(Collectors.toList())
//            val sumResult = collect.stream()
//                .mapToDouble { obj: PpmdStaResultVo -> obj.payAlready }.sum()
//            listLastData.add(NumberUtils.divide(sumResult, lastMonth.toDouble(), 2))
//        })
//        seriesLastMonth.data = listLastData
        // 本月统计
        val seriesCurrentMonth = PpmdStaVo.Series()
        seriesCurrentMonth.name = "本月"
        var currentYear = DateUtils.getCurrentYear()
        var month = DateUtils.getMonth(Date())
        var daysByYearMonth = LocalDate.now().dayOfMonth
        calculateData(overviewCurrentMonth, daysByYearMonth, thisMonth.toDouble(), seriesCurrentMonth)
        listSeries.add(seriesCurrentMonth)

        // 上月统计
        val seriesLastMonth = PpmdStaVo.Series()
        seriesLastMonth.name = "上月"
        if (month == 1) {
            currentYear -= 1
            month = 12
        } else {
            month -= 1
        }
        daysByYearMonth = DateUtils.getDaysByYearMonth(currentYear, month)
        calculateData(overviewLastMonth, daysByYearMonth, lastMonth.toDouble(), seriesLastMonth)
        listSeries.add(seriesLastMonth)

        return PpmdStaVo().also {
            it.listSeries = listSeries
        }
    }


    /**
     * 生成一个时间的List
     *
     * @param days
     * @return
     */
    private fun createListData(days: Int): List<Int> {
        val list: MutableList<Int> = LinkedList()
        for (i in 1..days) {
            list.add(i)
        }
        return list
    }

    private fun calculateData(
        overviewData: List<PpmdStaResultVo>,
        daysByYearMonth: Int,
        divisor: Double,
        series: PpmdStaVo.Series,
    ) {
        val listData: MutableList<Double> = LinkedList()
        createListData(daysByYearMonth).forEach { item: Int ->
            val collect = overviewData.stream()
                .filter { it: PpmdStaResultVo -> it.payDate <= item }
                .collect(Collectors.toList())
            val sumResult = collect.stream()
                .mapToDouble { obj: PpmdStaResultVo -> obj.payAlready }.sum()
            listData.add(NumberUtils.divide(sumResult * 100.0, divisor, 2))
        }
        series.data = listData
    }

    /**
     * 通过单位id，获取n个月前（含本月）的组织领导班子换届数据
     *
     * @param unitId 单位id 可以为空
     * @param month  月份
     */
    fun getOrgLeaderChange(
        unitId: Long?,
        month: @NotNull Int,
        regionId: @NotNull Long,
    ): List<Pair<String, Long>> {
        return datavUserMapper.getOrgLeaderChange(unitId, month, excludeOrgIds.split(","), regionId)
    }

    /**
     * 获取领导调研相关统计数据
     *
     */
    fun getLeaderResearchData(): List<Pair<String, Long>> {
        return dataVMeetingMapper.getLeaderResearchStatistics()
    }

    /**
     * 获取领导调研分类统计数据
     */
    fun getLeaderResearchTypeData(): List<Pair<String, Int>> {
        return dataVMeetingMapper.getLeaderResearchTypeStatistics()
    }

    /**
     * 获取奖惩登记相关数据
     */
    fun getRewardPunishStatistics(unitId: Long?, regionId: Long): Map<String, Any> {
        val map = mutableMapOf<String, Any>()
        val ocp = dataVMeetingMapper.getRewardPunishStatistics(unitId, excludeOrgIds.split(","), regionId)
        // 26.国家级荣誉 level is 10480201 10480202
        map["国家级荣誉-list"] =
            ocp.filter { it.level == 10480201 || it.level == 10480202 }
        map["国家级荣誉-total"] = map["国家级荣誉-list"]?.let { (it as List<*>).size } ?: 0
        // 27.省部级荣誉 level is 10480203 10480204
        map["省部级荣誉-list"] =
            ocp.filter { it.level == 10480203 || it.level == 10480204 }
        map["省部级荣誉-total"] = map["省部级荣誉-list"]?.let { (it as List<*>).size } ?: 0
        // 28.地市级荣誉 level is 10480205 10480206
        map["地市级荣誉-list"] =
            ocp.filter { it.level == 10480205 || it.level == 10480206 }
        map["地市级荣誉-total"] = map["地市级荣誉-list"]?.let { (it as List<*>).size } ?: 0
        return map
    }

    /**
     * 党委公开柱状图相关数据
     */
    fun getPartyAffairsOpenData(): List<Pair<String, Int>> {
        return dataVActivityMapper.getPartyAffairsOpenData()
    }

    /**
     * 获取党建品牌数据
     */
    fun getPartyBrandBaseData(orgId: Long, unitId: Long, headers: HttpHeaders): Pair<String, Long> {
        return Pair(
            "党建品牌",
            if (orgId == 3.toLong()) {
                partyBrandRepository.countByStatus(2)
            } else {
                // 通过单位id获取单位下党委和支部
                val ids =
                    openService.getOrgByCorp(unitId, headers).allOrg - (excludeOrgIds.split(",")
                        .map { it.toLong() }).toSet()
                partyBrandRepository.countByStatusAndOrgIdIn(2, ids.toMutableList())
            }
        )
    }

    /**
     * 党建阵地数据
     * @param orgId 组织id
     */
    fun getPartyPositionsData(orgId: Long, headers: HttpHeaders): List<Pair<String, Long>> {
        val positions = mutableListOf<Pair<String, Long>>()
        try {
            val form = openService.getPartyPosition(orgId, headers)
            // 34.党建文化长廊 - 翻牌器
            positions.add("党建文化长廊" to (form.numberAddForm?.corridorNumber ?: 0.0).roundToLong())
            // 35.党员活动室 - 翻牌器
            positions.add("党员活动室" to (form.numberAddForm?.liveNumber ?: 0).toLong())
            // 36.支部园地 - 翻牌器
            positions.add("支部园地" to (form.numberAddForm?.gardenNumber ?: 0).toLong())
            // 37.党建VR - 翻牌器
            positions.add("党建VR" to (form.numberAddForm?.vrNumber ?: 0).toLong())
        } catch (e: ApiException) {
            log.error(
                "品牌阵地->获取阵地发生异常: ${e.localizedMessage}->${e.result?.code} : ${e.result?.message}",
                e
            )
        } catch (e: IOException) {
            log.error("品牌阵地->获取阵地发生网络异常: ${e.localizedMessage}", e)
        } catch (e: Exception) {
            log.error("品牌阵地->获取阵地发生其他异常: ${e.localizedMessage}", e)
        }
        return positions
    }


}