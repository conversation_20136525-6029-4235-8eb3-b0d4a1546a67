package com.goodsogood.ows.service;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.vo.FileCommonForm;
import com.goodsogood.ows.model.vo.Result;
import lombok.extern.log4j.Log4j2;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

/**
 * 通过文件处理
 */
@Log4j2
@Service
public class FileService {
    private final StringRedisTemplate stringRedisTemplate;
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public FileService(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 通用上传文件类型
     *
     * @param fileCenter   文件中心服务名称
     * @param path         上传文件 所在服务器全路径地址
     * @param token        token的值 建议使用uuid
     * @param errors       errors
     * @param headers      headers
     * @param restTemplate restTemplate
     */
    public  void sendFileCenter(String fileCenter,
                                String path, String token,
                                Errors errors, HttpHeaders headers,
                                RestTemplate restTemplate) {
        MediaType type = MediaType.parseMediaType("multipart/form-data");
        // 设置请求的格式类型
        headers.setContentType(type);
        FileSystemResource fileSystemResource = new FileSystemResource(path);
        MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
        form.add("upfile", fileSystemResource);
        form.add("token", token);
        String url = "http://" + fileCenter + "/file/upload/common";
        try {
            RemoteApiHelper.post(restTemplate, url, form, headers, new TypeReference<Result<Void>>() {});
        } catch (Exception e) {
            log.error("上传文件中心服务器失败", e);
            FileCommonForm fileCommonForm = new FileCommonForm();
            fileCommonForm.setStatus(-1);
            fileCommonForm.setErrorMsg("上传文件中心服务器失败");
            setFileCommonByToken(token, fileCommonForm);
        }
    }

    /**
     * 更新文件信息
     */
    public void setFileCommonByToken(String token, FileCommonForm fileCommonForm) {
        try {
            stringRedisTemplate.opsForValue().set(token,
                    objectMapper.writeValueAsString(fileCommonForm),30, TimeUnit.MINUTES);
        } catch (JsonProcessingException e) {
            log.error("setFileCommonByToken，写入标记缓存发生异常", e);
        }
    }



}
