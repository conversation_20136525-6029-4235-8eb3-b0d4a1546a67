package com.goodsogood.ows.service;

/**
 * 支部之窗服务
 */

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.configuration.WindowConfiguration;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.mapper.user.OrgGroupMapper;
import com.goodsogood.ows.mapper.user.OrgPeriodMapper;
import com.goodsogood.ows.model.db.meeting.MeetingEntity;
import com.goodsogood.ows.model.db.user.BranchHighlightEntity;
import com.goodsogood.ows.model.mongodb.PartyBrandBase;
import com.goodsogood.ows.model.vo.meeting.MeetingPlanCountVO;
import com.goodsogood.ows.model.vo.user.OrgExpandVO;
import com.goodsogood.ows.model.vo.user.OrgGroupVo;
import com.goodsogood.ows.model.vo.user.UserExpandInfoForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Log4j2
public class WindowService {
    private final RedisTemplate redisTemplate;
    private final OrgPeriodMapper orgPeriodMapper;
    private final WindowThirdService windowThirdService;
    private final MeetingMapper meetingMapper;
    private final WindowConfiguration windowConfiguration;
    private final OrgGroupMapper orgGroupMapper;

    public WindowService(RedisTemplate redisTemplate, OrgPeriodMapper orgPeriodMapper, WindowThirdService windowThirdService, MeetingMapper meetingMapper, WindowConfiguration windowConfiguration, OrgGroupMapper orgGroupMapper) {
        this.redisTemplate = redisTemplate;
        this.orgPeriodMapper = orgPeriodMapper;
        this.windowThirdService = windowThirdService;
        this.meetingMapper = meetingMapper;
        this.windowConfiguration = windowConfiguration;
        this.orgGroupMapper = orgGroupMapper;
    }


//    @Cacheable(cacheNames={"window-org"},key = "#root.methodName + '-' + #regionId + '-' + #orgId")
    public OrgExpandVO getOrgPeriodInfo(HttpHeaders headers, Long orgId, Long regionId) {
        return windowThirdService.getOrgPeriodInfo(headers, orgId);
    }

    /**
     * 组织生活开展情况
     * flag 0:本月
     * 1:上月
     */
//    @Cacheable(cacheNames={"window-meeting"},key = "#root.methodName + '-' + #regionId + '-' + #orgId+ '-' + #flag")
    public List<MeetingPlanCountVO> getMeetingPlanList(Long regionId, Long orgId, Integer flag, Integer page, Integer pageSize) {
        Page<MeetingPlanCountVO> pageList = PageHelper.startPage(page, pageSize).doSelectPage(() -> orgPeriodMapper.getMeetingPlanList(orgId, flag));
        return pageList.getResult();
    }

    public Map<String,Object> getOrgProfile(HttpHeaders headers, Long orgId, Long regionId) {
        return windowThirdService.getOrgProfile(headers, orgId);
    }


//    @Cacheable(cacheNames={"window-user"},key = "#root.methodName + '-' + #regionId + '-' + #orgId")
    public List<?> getUserList(Long orgId, HttpHeaders headers, Long regionId) {
        //先判斷有没有党小组，如果有，则返回党小组信息
        List<OrgGroupVo>  list = orgGroupMapper.queryGroupuser(orgId);
        if(!CollectionUtils.isEmpty(list)){
            List<OrgGroupVo> returnList = new ArrayList<>();
            Map<String,List<OrgGroupVo>> map = list.stream().collect(Collectors.groupingBy(OrgGroupVo::getOrgGroupName,LinkedHashMap::new,Collectors.toList()));
            for(Map.Entry<String, List<OrgGroupVo>> entry : map.entrySet()){
                OrgGroupVo vo = new OrgGroupVo();
                vo.setOrgGroupName(entry.getKey());
                List<UserExpandInfoForm> userList = new ArrayList<>();
                entry.getValue().forEach(i->{
                    UserExpandInfoForm form = new UserExpandInfoForm();
                    form.setName(i.getName());
                    form.setHeadUrl(i.getHeadUrl());
                    form.setOrgPeriodName(i.getOrgPeriodName());
                    userList.add(form);
                });
                vo.setUserList(userList);
                returnList.add(vo);
            }
            return returnList;
        }
        List<UserExpandInfoForm> userList =  windowThirdService.getUserList(orgId, headers);
        List<OrgGroupVo> returnList = new ArrayList<>();
        OrgGroupVo vo = new OrgGroupVo();
        vo.setOrgGroupName("党员介绍");
        vo.setUserList(userList);
        returnList.add(vo);
        return returnList;

    }

//    @Cacheable(cacheNames={"window-meeting"},key = "#root.methodName + '-' + #regionId + '-' + #orgId")
    public List<MeetingEntity> getMeetingList(HttpHeaders headers, Long regionId, Long orgId, Integer num) {
        WindowConfiguration.TypeStatus typeStatus = windowConfiguration.getMap().get(regionId);
        if(typeStatus==null){
            log.debug("未配置改区县支部之窗组织生活动态查询范围，请配置window-of-org");
            throw new ApiException("未配置改区县支部之窗组织生活动态查询范围，请配置window-of-org region_id:"+regionId);
        }
       return meetingMapper.queryOrgMeetingList(orgId,typeStatus.getTypeIds(),typeStatus.getStatus(),num);
    }

//    @Cacheable(cacheNames={"window-meeting"},key = "#root.methodName + '-' + #regionId + '-' + #orgId")
    public Integer getMeetingNum(HttpHeaders headers, Long regionId, Long orgId) {
        WindowConfiguration.TypeStatus typeStatus = windowConfiguration.getMap().get(regionId);
        if(typeStatus==null){
            log.debug("未配置改区县支部之窗组织生活动态查询范围，请配置window-of-org");
            throw new ApiException("未配置改区县支部之窗组织生活动态查询范围，请配置window-of-org region_id:"+regionId);
        }
        int year = LocalDate.now().getYear();
        return meetingMapper.queryOrgMeetingNum(orgId,typeStatus.getTypeIds(),typeStatus.getStatus(),year);
    }

//    @Cacheable(cacheNames={"window-brand"},key = "#root.methodName + '-' + #regionId + '-' + #orgId")
    public PartyBrandBase getBrand(Long regionId, Long orgId, HttpHeaders headers) {
        PartyBrandBase base = windowThirdService.getBrandList(regionId, orgId, headers);
        if (base.getPictureList() != null && base.getPictureList().getIsShow() == 0) {
            base.setPictureList(null);
        }
        if (base.getVideoList() != null && base.getVideoList().getIsShow() == 0) {
            base.setVideoList(null);
        }
        if (base.getVrList() != null && base.getVrList().getIsShow() == 0) {
            base.setVrList(null);
        }
        return base;
    }


    /**
     * @param orgId
     * @param type     type=1 风采  ntype=2 支部发展史
     * @param page
     * @param pageSize
     * @param headers
     * @return
     */
//    @Cacheable(cacheNames={"window-highlight"},key = "#root.methodName + '-' + #regionId + '-' + #orgId +'-'+ #type")
    public List<BranchHighlightEntity> getOrgHighligh(Long regionId, Long orgId, Integer type, Integer page, Integer pageSize, HttpHeaders headers) {
        return windowThirdService.getOrgHighligh(orgId, type, page, pageSize, headers);
    }

    public void delCache(Long orgId,String type){
        String keyLike = "";
        if(type == null){//代表删除该机构支部之窗所有数据
            keyLike = "window*"+ orgId +"*";
        }else{
            keyLike = "window-"+ type+"*"+orgId+"*";
        }
        deletKeys(keyLike);
    }


    private void deletKeys(String keyLike) {
        Set<String> keys = redisTemplate.keys(keyLike);
        redisTemplate.delete(keys);
    }

}
