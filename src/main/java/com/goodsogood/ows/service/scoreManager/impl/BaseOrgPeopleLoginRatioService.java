package com.goodsogood.ows.service.scoreManager.impl;

import com.goodsogood.ows.annotions.MqConsumerErrorProcess;
import com.goodsogood.ows.common.ScoreManagerEnum;
import com.goodsogood.ows.configuration.DssIndexScoreConstant;
import com.goodsogood.ows.mapper.score.SasScoreManagerRatioMapper;
import com.goodsogood.ows.model.vo.scoreManager.ScoreManagerMqVo;
import com.goodsogood.ows.service.scoreManager.ScoreManagerHelper;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * 党员积极登录登录数智党建平台
 * 支部下的党员登录平台情况，按月积分，按日平均登录率：
 * 90%以上，加5分；
 * 70%以上，加3分；
 * 50%以上，加1分；
 * 50%以下，不加分
 *
 * <AUTHOR> ruoyu
 * @date : 2021/12/14
 */
@Service
@Log4j2
public class BaseOrgPeopleLoginRatioService {

    private final ScoreManagerHelper scoreManagerHelper;
    private final SasScoreManagerRatioMapper sasScoreManagerRatioMapper;

    @Autowired
    public BaseOrgPeopleLoginRatioService(ScoreManagerHelper scoreManagerHelper,
                                          SasScoreManagerRatioMapper sasScoreManagerRatioMapper) {
        this.scoreManagerHelper = scoreManagerHelper;
        this.sasScoreManagerRatioMapper = sasScoreManagerRatioMapper;
    }

    /**
     * 党员积极登录登录数智党建平台
     * 支部下的党员登录平台情况，按月积分，按日平均登录率：
     */
    @RabbitListener(queues = "#{@ScoreManagerConfig.getBaseOrgPeopleLoginRatio()}")
    @MqConsumerErrorProcess
    public void baseOrgPeopleLoginRatio(String vo) {
        log.debug("baseOrgPeopleLoginRatio-> [{}]", vo);
        ScoreManagerMqVo mqVo = JsonUtils.fromJson(vo, ScoreManagerMqVo.class);
        /* 前置判断与初始化枚举 */
        if (!scoreManagerHelper.checkEnumInfoAndExist(ScoreManagerEnum.BASE_ORG_PEOPLE_LOGIN_RATIO, mqVo)) {
            return;
        }

        DateTimeFormatter ofPattern = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate computeDate = LocalDate.from(ofPattern.parse(mqVo.getExecuteTime() + "-01"));
        LocalDate nowDate = LocalDate.now();
        //如果时间大于当前月 则是错误的时间 这里终止
        if (computeDate.isAfter(nowDate)) {
            return;
        }
        List<String> month;
        String queryMonth;
        //如果是当前月
        if (computeDate.getYear() == nowDate.getYear()
                && computeDate.getMonthValue() == nowDate.getMonthValue()) {
            month = scoreManagerHelper.findTimeJetLag(computeDate, nowDate, ofPattern);
            queryMonth = computeDate.plusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
        } else { /* 否则就是其他小于当前月的月份 */
            month = scoreManagerHelper.findTimeJetLag(computeDate, computeDate.with(TemporalAdjusters.lastDayOfMonth()), ofPattern);
            queryMonth = mqVo.getExecuteTime();
        }
        if (CollectionUtils.isEmpty(month)) {
            return;
        }
        //新增日平均
        for (String time : month) {
            sasScoreManagerRatioMapper.addBaseOrgPeopleLoginRatio(mqVo.getRegionId(), mqVo.getId(), time, mqVo.getChangeUserId());
        }
        /* 计算月分数 */
        mqVo.setSingleHandleData(sasScoreManagerRatioMapper.findAvgRatio(mqVo.getId(), 1, queryMonth));
        mqVo.setRuleId(DssIndexScoreConstant.SYSTEM_LOG);
        scoreManagerHelper.addScoreManagerInfo(mqVo);
//        scoreManagerHelper.sendToOrgDoris(mqVo);
    }
}
