package com.goodsogood.ows.service.scoreManager.impl;

import com.goodsogood.ows.annotions.MqConsumerErrorProcess;
import com.goodsogood.ows.common.ScoreManagerEnum;
import com.goodsogood.ows.configuration.DssIndexScoreConstant;
import com.goodsogood.ows.mapper.meeting.UserCommendPenalizeMapper;
import com.goodsogood.ows.model.vo.scoreManager.ScoreManagerMqVo;
import com.goodsogood.ows.service.scoreManager.ScoreManagerHelper;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 支部内党员受处罚
 *
 * <AUTHOR> ruoyu
 * @date : 2021/12/14
 */
@Service
@Log4j2
public class BaseOrgPeoplePunishService {

    private final UserCommendPenalizeMapper userCommendPenalizeMapper;
    private final ScoreManagerHelper scoreManagerHelper;

    @Autowired
    public BaseOrgPeoplePunishService(UserCommendPenalizeMapper userCommendPenalizeMapper,
                                      ScoreManagerHelper scoreManagerHelper) {
        this.userCommendPenalizeMapper = userCommendPenalizeMapper;
        this.scoreManagerHelper = scoreManagerHelper;
    }

    /**
     * 支部内党员受处罚
     * 支部内有受党纪处分、政务处分、组织处理的，每1人次积分-5
     */
    @RabbitListener(queues = "#{@ScoreManagerConfig.getBaseOrgPeoplePunish()}")
    @MqConsumerErrorProcess
    public void baseOrgPeoplePunish(String vo) {
        log.debug("baseOrgPeoplePunish-> [{}]", vo);
        ScoreManagerMqVo mqVo = JsonUtils.fromJson(vo, ScoreManagerMqVo.class);
        mqVo.setManagerEnum(scoreManagerHelper.findByTypeId(mqVo.getTypeId()));
        //校验类型是否匹配
        scoreManagerHelper.checkScoreManagerEnum(ScoreManagerEnum.BASE_ORG_PEOPLE_PUNISH,
                mqVo.getManagerEnum());
//        mqVo.setExecuteTime(ScoreManagerEnum.Constant.FINAL_TIME);
        //先获取指定月指定组织的惩罚
        List<PeoplePunishVo> orgPeoplePunishInfo = userCommendPenalizeMapper.getOrgPeoplePunishInfo(mqVo.getId(),mqVo.getExecuteTime());
        if (CollectionUtils.isEmpty(orgPeoplePunishInfo)) {
            return;
        }
        //获取积分管理表是否存在记录
//        List<Long> sourceDataIdExistData = scoreManagerHelper.findSourceDataIdExistData(mqVo.getRegionId(), mqVo.getManagerEnum().getDataType(),
//                mqVo.getId(), mqVo.getExecuteTime(), mqVo.getManagerEnum().getTypeId());
        //与库中的数据对比 获取差集
//        if (!CollectionUtils.isEmpty(sourceDataIdExistData)) {
//            orgPeoplePunishInfo = orgPeoplePunishInfo.stream()
//                    .filter(x -> !sourceDataIdExistData.contains(x.getMeetingUserCommendPenalizeId()))
//                    .collect(Collectors.toList());
//        }
        //如果存在数据 则需要入库
//        if (!CollectionUtils.isEmpty(orgPeoplePunishInfo)) {
        List<ScoreManagerMqVo.TransitScoreManagerVo> list = new ArrayList<>(orgPeoplePunishInfo.size());
        mqVo.setHandleData(list);
        for (PeoplePunishVo punishVo : orgPeoplePunishInfo) {
            list.add(
                    ScoreManagerMqVo.TransitScoreManagerVo.builder()
                            .sourceDataId(punishVo.getMeetingUserCommendPenalizeId())
                            .sourceDataIdStr(punishVo.getMeetingUserCommendPenalizeId().toString())
                            .var1(punishVo.getUserId().toString())
                            .var2(punishVo.getType())
                            .var3(punishVo.getTypeName())
                            .var4(punishVo.getBasisDescription())
                            .var5(punishVo.getEffectiveTime()).build());
        }
//        scoreManagerHelper.addScoreManagerInfo(mqVo);
        mqVo.setRuleId(DssIndexScoreConstant.PERIOD_PUNISH);
        scoreManagerHelper.sendToOrgDoris(mqVo);
//        }
    }

    @Data
    public static class PeoplePunishVo {

        /**
         * 主键id
         */
        private Long meetingUserCommendPenalizeId;

        /**
         * 用户id
         */
        private Long userId;

        /**
         * 用户名称
         */
        private String userName;

        /**
         * 类型
         */
        private String type;

        /**
         * 具体类型的名称
         */
        private String typeName;

        /**
         * 依据
         */
        private String basisDescription;

        /**
         * 生效时间
         */
        private String effectiveTime;

        /**
         * 组织id
         */
        private String orgId;
    }
}
