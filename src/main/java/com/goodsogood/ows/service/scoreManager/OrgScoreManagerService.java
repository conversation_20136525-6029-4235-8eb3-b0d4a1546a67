package com.goodsogood.ows.service.scoreManager;

import com.goodsogood.ows.common.ScoreManagerEnum;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.ScoreManagerConfig;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.mapper.user.PartyGroupMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.vo.scoreManager.ScoreManagerAddVo;
import com.goodsogood.ows.model.vo.scoreManager.ScoreManagerMqVo;
import com.goodsogood.ows.service.scoreManager.ding.DingTalkHelper;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/12/11
 */
@Service
@Log4j2
public class OrgScoreManagerService {

    private final ScoreManagerConfig scoreManagerConfig;
    private final SimpleApplicationConfigHelper configHelper;
    private final OrganizationMapper organizationMapper;
    private final PartyGroupMapper partyGroupMapper;
    private final ScoreManagerHelper scoreManagerHelper;
    private final RabbitTemplate rabbitTemplate;
    private final UserMapper userMapper;
    private final DingTalkHelper dingTalkHelper;

    @Autowired
    public OrgScoreManagerService(ScoreManagerConfig scoreManagerConfig,
                                  SimpleApplicationConfigHelper configHelper, OrganizationMapper organizationMapper, PartyGroupMapper partyGroupMapper,
                                  ScoreManagerHelper scoreManagerHelper, RabbitTemplate rabbitTemplate,
                                  UserMapper userMapper, DingTalkHelper dingTalkHelper) {
        this.scoreManagerConfig = scoreManagerConfig;
        this.configHelper = configHelper;
        this.organizationMapper = organizationMapper;
        this.partyGroupMapper = partyGroupMapper;
        this.scoreManagerHelper = scoreManagerHelper;
        this.rabbitTemplate = rabbitTemplate;
        this.userMapper = userMapper;
        this.dingTalkHelper = dingTalkHelper;
    }

    /**
     * 定时任务调用
     *
     * @param executeTime 处理时间yyyy-MM
     */
    public void execute(String executeTime, Long regionId, List<ScoreManagerEnum> processItem) {
        Region.OrgData regionData = configHelper.getOrgByRegionId(regionId);
        if (null == regionData) {
            scoreManagerHelper.throwMyException("label区县配置不存在:" + regionId);
        }
        Long topOrgId = regionData.getOrgId();

        Map<ScoreManagerEnum, List<Long>> map = new HashMap<>(processItem.size());
        for (ScoreManagerEnum managerEnum : processItem) {
            //属于查询党组织数据的
            if (1 <= managerEnum.getTypeId() && 8 >= managerEnum.getTypeId()) {
                List<Long> ids = organizationMapper.getOrgIdsByRegionAndTypeAndOrgId(regionId, topOrgId,
                        managerEnum.getOrgTypeChild().stream().map(Object::toString).collect(Collectors.joining(",")));
                if (CollectionUtils.isEmpty(ids)) {
                    continue;
                }
                map.put(managerEnum, ids);
                /* 属于党组类型查询  */
            } else if (8 < managerEnum.getTypeId() && 12 >= managerEnum.getTypeId()) {
                List<Long> groupIds = partyGroupMapper.findGroupIdByRegion(regionId);
                if (CollectionUtils.isEmpty(groupIds)) {
                    continue;
                }
                map.put(managerEnum, groupIds);
                /* 属于党员 */
            } else if (18 == managerEnum.getTypeId()) {
                List<Long> list = userMapper.getPartyUserByOrgId(topOrgId, regionId,
                        managerEnum.getOrgTypeChild().stream().map(Object::toString).collect(Collectors.joining(",")));
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                map.put(managerEnum, list);
            } else {
                scoreManagerHelper.throwMyException("未配置TypeId:" + managerEnum.getTypeId());
            }
        }

        if (!CollectionUtils.isEmpty(map)) {
            sendMessage(executeTime, regionId, map, null);
            /* 发送钉钉消息 */
//            dingTalkHelper.addMasterMsg(regionId, map, executeTime, scoreManagerConfig.getExchangesSuffix());
        }


    }

    /**
     * 接口调用
     *
     * @param executeTime 处理时间yyyy-MM
     * @param processData key:想要处理的项 value:想要处理的组织或者其他主键id
     */
    public void execute(String executeTime, Long regionId, Map<Integer, List<Long>> processData,
                        HeaderHelper.SysHeader header) {
        //验证是否为指yyyy-MM 格式
        LocalDate parse = LocalDate.parse(executeTime + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        //验证区县
        List<Integer> integers = scoreManagerConfig.getRegions().get(regionId);
        if (CollectionUtils.isEmpty(integers)) {
            scoreManagerHelper.throwMyException("未获取到区县:[" + regionId + "]的积分配置");
        }
        Map<ScoreManagerEnum, List<Long>> data = new HashMap<>(processData.size());
        for (Map.Entry<Integer, List<Long>> entry : processData.entrySet()) {
            ScoreManagerEnum managerEnum = ScoreManagerEnum.findByTypeId(entry.getKey());
            if (null == managerEnum
                    || !managerEnum.getHandleType().equals(1)) {
                scoreManagerHelper.throwMyException("错误的TypeId:[" + entry.getKey() + "]");
            }
            data.put(managerEnum, entry.getValue());
        }
        if (CollectionUtils.isEmpty(data)) {
            scoreManagerHelper.throwMyException("不存在需要执行类型数据");
        }
        sendMessage(executeTime, regionId, data, header);
        /* 发送钉钉消息 */
        dingTalkHelper.addMasterMsg(regionId, data, executeTime, scoreManagerConfig.getExchangesSuffix());
    }

    /**
     * 暂时只兼容固定分值的积分处理
     * 根据阶段比例实际获取分值暂未兼容
     */
    public void addScore(ScoreManagerAddVo addVo) {
        ScoreManagerEnum managerEnum = ScoreManagerEnum.findByTypeId(addVo.getTypeId());
        if (null == managerEnum
                || !managerEnum.getHandleType().equals(2)) {
            scoreManagerHelper.throwMyException("错误的新增类型:[" + addVo.getTypeId() + "]");
        }
        //组装  ScoreManagerMqVo
        ScoreManagerMqVo scoreManagerMqVo = new ScoreManagerMqVo();
        scoreManagerMqVo.setManagerEnum(managerEnum);
        scoreManagerMqVo.setExecuteTime(addVo.getExecuteTime());
        scoreManagerMqVo.setRegionId(addVo.getRegionId());
        scoreManagerMqVo.setId(addVo.getId());
        scoreManagerMqVo.setChangeUserId(addVo.getChangeUser());
        scoreManagerMqVo.setSingleHandleData(addVo.getSourceDataId(),
                addVo.getSourceDataIdStr(),
                addVo.getVar1(), addVo.getVar2(), addVo.getVar3(), addVo.getVar4(), addVo.getVar5());
        scoreManagerHelper.addScoreManagerInfo(scoreManagerMqVo);
    }

    /**
     * 发送消息到mq
     *
     * @param executeTime 处理月份 yyyy-MM
     * @param processData 需要处理的数据
     */
    private void sendMessage(String executeTime, Long regionId, Map<ScoreManagerEnum, List<Long>> processData, HeaderHelper.SysHeader header) {
        Long changeUserId = null != header && null != header.getUserId() ? header.getUserId() : -1L;
        for (Map.Entry<ScoreManagerEnum, List<Long>> entry : processData.entrySet()) {
            for (Long id : entry.getValue()) {
                ScoreManagerMqVo scoreManagerMqVo = new ScoreManagerMqVo();
                scoreManagerMqVo.setExecuteTime(executeTime);
                scoreManagerMqVo.setRegionId(regionId);
                scoreManagerMqVo.setId(id);
                scoreManagerMqVo.setChangeUserId(changeUserId);
                /* 减少占用存入id 业务被动获取enum */
                scoreManagerMqVo.setTypeId(entry.getKey().getTypeId());
                rabbitTemplate.convertAndSend(ScoreManagerConfig.SCORE_MANAGER_TOPIC_EXCHANGE_KEY,
                        entry.getKey().getQueueName(), JsonUtils.toJson(scoreManagerMqVo));
            }
        }
    }
}
