package com.goodsogood.ows.service.scoreManager.ding;

import com.goodsogood.ows.common.ScoreManagerEnum;
import com.goodsogood.ows.configuration.ScoreManagerConfig;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/12/27
 */
@Service
@Log4j2
public class DingTalkHelper {

    @Resource(name = "outerRestTemplate")
    private RestTemplate restTemplate;

    private final ScoreManagerConfig scoreManagerConfig;

    @Autowired
    public DingTalkHelper(ScoreManagerConfig scoreManagerConfig) {
        this.scoreManagerConfig = scoreManagerConfig;
    }

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final String MASTER_MSG_TEMPLATE = "## 积分提醒:已发送积分管理任务到MQ\n" +
            "#### 执行时间:[nowTime] \n" +
            "#### 处理月份:[executiveTime] \n";

    private static final String ERROR_MSG_TEMPLATE = "## 积分提醒: 消费者消费异常\n" +
            "#### 执行时间:[executeTime] \n" +
            "#### 执行方法:[executeMethod] \n" +
            "#### 执行参数:[executeArges]\n" +
            " - error msg:[errorMsg]\n";

    @Async("scoreManagerExecutor")
    public void addMasterMsg(Long regionId, Map<ScoreManagerEnum, List<Long>> map, String executeTime, String exchangesSuffix) {
        if (Objects.isNull(regionId) || CollectionUtils.isEmpty(map)) {
            return;
        }
        Map<Long, Map<Integer, Integer>> dingMsg = new HashMap<>(1);
        Map<Integer, Integer> data = new HashMap<>(map.size());
        dingMsg.put(regionId, data);
        map.forEach((k, v) -> data.put(k.getTypeId(), v.size()));
        addMasterMsg(dingMsg, executeTime, exchangesSuffix);
    }


    /**
     * 消费者消费异常时发送异常信息
     */
    @Async("scoreManagerExecutor")
    public void addErrorMsg(DingTalkErrorForm errorForm) {
        StringBuilder errorStr = new StringBuilder(ERROR_MSG_TEMPLATE
                .replace("executeTime", LocalDateTime.now().format(DATE_TIME_FORMATTER))
                .replace("executeMethod", errorForm.getExecuteMethod())
                .replace("executeArges", errorForm.getExecuteArges())
                .replace("errorMsg", errorForm.getThrowable().getMessage()));

        StackTraceElement[] stackTrace = errorForm.getThrowable().getStackTrace();
        for (int i = 0; i < Math.min(3, stackTrace.length); i++) {
            errorStr.append(" - Stack").append(i).append("[")
                    .append(stackTrace[i])
                    .append("]\n");
        }
        sendMsg(DingTalkFormHelper
                .generateMarkdownMsg("积分提醒: 消费者消费异常", errorStr.toString()));
    }

    /**
     * 主程积分发送完毕后 发送消息
     *
     * @param map           Map<{区县Id}, Map<{类型id}, {消息数量}>>
     * @param executiveTime 发送消息处理的时间
     */
    @Async("scoreManagerExecutor")
    public void addMasterMsg(Map<Long, Map<Integer, Integer>> map, String executiveTime, String exchangesSuffix) {
        if (CollectionUtils.isEmpty(map)) {
            return;
        }
        String replace = MASTER_MSG_TEMPLATE
                .replace("nowTime", LocalDateTime.now().format(DATE_TIME_FORMATTER))
                .replace("executiveTime", executiveTime);
        StringBuilder stringBuilder = new StringBuilder(replace);
        map.forEach((k1, v1) -> {
            stringBuilder.append("### 区县[").append(k1).append("] ")
                    .append("环境[").append(exchangesSuffix).append("] \n");
            v1.forEach((k2, v2) -> stringBuilder.append("- 类型id:[").append(k2).append("] 消息数量:[").append(v2).append("]\n"));
        });
        sendMsg(DingTalkFormHelper
                .generateMarkdownMsg("积分提醒:已发送积分管理任务到MQ", stringBuilder.toString()));
    }

    private void sendMsg(DingTalkFormHelper.ScoreManagerDingTalkForm dingTalkForm) {
        String content = JsonUtils.toJson(dingTalkForm);
        log.debug("发送消息到钉钉-> 开始准备发送消息[{}]", content);
        String requestUrl = String.format(ScoreManagerConfig.dingDingPushUrl, scoreManagerConfig.getDingTalkAccessKey());
        try {
            String result = restTemplate.postForObject(requestUrl, dingTalkForm, String.class);
            if (result.contains("ok")) {
                log.debug("发送消息到钉钉-> 消息发送成功");
            } else {
                log.debug("发送消息到钉钉-> 消息发送失败[{}]", result);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.debug("发送消息到钉钉-> 消息发送失败[{}]", e.getMessage(), e);
        }
    }
}
