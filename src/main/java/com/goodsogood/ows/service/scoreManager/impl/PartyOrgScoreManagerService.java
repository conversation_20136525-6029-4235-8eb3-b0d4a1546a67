package com.goodsogood.ows.service.scoreManager.impl;

import com.goodsogood.ows.annotions.MqConsumerErrorProcess;
import com.goodsogood.ows.common.ScoreManagerEnum;
import com.goodsogood.ows.configuration.DssIndexScoreConstant;
import com.goodsogood.ows.mapper.meeting.UserCommendPenalizeMapper;
import com.goodsogood.ows.mapper.user.OrgPeriodMapper;
import com.goodsogood.ows.model.vo.scoreManager.ScoreManagerMqVo;
import com.goodsogood.ows.service.scoreManager.ScoreManagerHelper;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 健全组织机构，配强组织力量  配齐配强书记（副书记），加5个积分
 * 未督促提醒所属党支部按期开展换届 下属党支部未按期换届，一次扣2分，可累加
 * 组织内有党员受处分 组织内有党员受党纪处分、政务处分、组织处理的，每1人次扣5分
 *
 * <AUTHOR> ruoyu
 * @date : 2021/12/16
 */
@Service
@Log4j2
public class PartyOrgScoreManagerService {

    private final ScoreManagerHelper scoreManagerHelper;
    private final OrgPeriodMapper orgPeriodMapper;
    private final UserCommendPenalizeMapper userCommendPenalizeMapper;

    @Autowired
    public PartyOrgScoreManagerService(ScoreManagerHelper scoreManagerHelper,
                                       OrgPeriodMapper orgPeriodMapper,
                                       UserCommendPenalizeMapper userCommendPenalizeMapper) {
        this.scoreManagerHelper = scoreManagerHelper;
        this.orgPeriodMapper = orgPeriodMapper;
        this.userCommendPenalizeMapper = userCommendPenalizeMapper;
    }

    /**
     * 健全组织机构，配强组织力量  配齐配强书记（副书记），加5个积分
     */
    @RabbitListener(queues = "#{@ScoreManagerConfig.getPartyOrgExistPeriodLeader()}")
    @MqConsumerErrorProcess
    public void partyOrgExistPeriodLeaderService(String vo) {
        log.debug("partyOrgExistPeriodLeaderService-> [{}]", vo);
        ScoreManagerMqVo mqVo = JsonUtils.fromJson(vo, ScoreManagerMqVo.class);
        /* 前置判断与初始化枚举 */
        if (!scoreManagerHelper.checkEnumInfoAndExist(ScoreManagerEnum.PARTY_ORG_EXIST_PERIOD_LEADER, mqVo)) {
            return;
        }

        Long periodId = orgPeriodMapper.isExistPeriodLeaderByTimeAndOrg(mqVo.getId(),
                mqVo.getExecuteTime() + "-01");
        if (null == periodId) {
            return;
        }

        mqVo.setSingleHandleData(periodId, periodId.toString());
        mqVo.setRuleId(DssIndexScoreConstant.PERIOD_SECTARY_COMMIT);
        scoreManagerHelper.addScoreManagerInfo(mqVo);
//        scoreManagerHelper.sendToOrgDoris(mqVo);
    }

    /**
     * 党委党总支-未督促提醒所属党支部按期开展换届  下属党支部未按期换届，一次扣2分，可累加
     */
    @RabbitListener(queues = "#{@ScoreManagerConfig.getPartyOrgPeriodExpireNotCreate()}")
    @MqConsumerErrorProcess
    public void partyOrgPeriodExpireNotCreate(String vo) {
        System.out.println("成功消费:" + vo);
        ScoreManagerMqVo mqVo = JsonUtils.fromJson(vo, ScoreManagerMqVo.class);
        /* 前置判断与初始化枚举 */
        if (!scoreManagerHelper.checkEnumInfoAndExist(ScoreManagerEnum.PARTY_ORG_PERIOD_EXPIRE_NOT_CREATE, mqVo)) {
            return;
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String nextQueryMonth = LocalDate.parse(mqVo.getExecuteTime() + "-01", dateTimeFormatter)
                .plusMonths(1).format(dateTimeFormatter);
        List<Long> orgIds = orgPeriodMapper.dueThisMonthNextNotExistByLowerLevel(mqVo.getRegionId(), mqVo.getId(),
                mqVo.getExecuteTime(), nextQueryMonth, ScoreManagerEnum.Constant.BRANCH_CHILD_TYPE_CHILD_STR);
        if (CollectionUtils.isEmpty(orgIds)) {
            return;
        }

        List<ScoreManagerMqVo.TransitScoreManagerVo> list = new ArrayList<>(orgIds.size());
        for (Long orgId : orgIds) {
            list.add(ScoreManagerMqVo.TransitScoreManagerVo.builder()
                    .sourceDataId(orgId)
                    .sourceDataIdStr(orgId.toString())
                    .build());
        }

        mqVo.setHandleData(list);
        mqVo.setRuleId(DssIndexScoreConstant.PERIOD_NOTRANSF_COMMIT);
        scoreManagerHelper.addScoreManagerInfo(mqVo);
//        scoreManagerHelper.sendToOrgDoris(mqVo);
    }

    /**
     * 组织内有党员受处分  组织内有党员受党纪处分、政务处分、组织处理的，每1人次扣5分
     */
    @RabbitListener(queues = "#{@ScoreManagerConfig.getPartyOrgPeoplePunish()}")
    @MqConsumerErrorProcess
    public void partyOrgPeoplePunish(String vo) {
        log.debug("partyOrgPeoplePunish-> [{}]", vo);
        ScoreManagerMqVo mqVo = JsonUtils.fromJson(vo, ScoreManagerMqVo.class);
        /* 前置判断与初始化枚举 */
        if (!scoreManagerHelper.checkEnumInfoAndExist(ScoreManagerEnum.PARTY_ORG_PEOPLE_PUNISH, mqVo)) {
            return;
        }

//        mqVo.setExecuteTime(ScoreManagerEnum.Constant.FINAL_TIME);
        List<BaseOrgPeoplePunishService.PeoplePunishVo> orgPeoplePunishInfo =
                userCommendPenalizeMapper.getOrgPeoplePunishInfoByLowerOrg(mqVo.getId(),mqVo.getExecuteTime(),
                        ScoreManagerEnum.Constant.BRANCH_CHILD_TYPE_CHILD_STR, mqVo.getRegionId());

        if (CollectionUtils.isEmpty(orgPeoplePunishInfo)) {
            return;
        }

        List<ScoreManagerMqVo.TransitScoreManagerVo> list = new ArrayList<>(orgPeoplePunishInfo.size());
        mqVo.setHandleData(list);
        for (BaseOrgPeoplePunishService.PeoplePunishVo punishVo : orgPeoplePunishInfo) {
            list.add(ScoreManagerMqVo.TransitScoreManagerVo.builder()
                    .sourceDataId(punishVo.getMeetingUserCommendPenalizeId())
                    .sourceDataIdStr(punishVo.getMeetingUserCommendPenalizeId().toString())
                    .var1(punishVo.getUserId().toString())
                    .var2(punishVo.getType())
                    .var3(punishVo.getTypeName())
                    .var4(punishVo.getBasisDescription())
                    .var5(punishVo.getEffectiveTime()).build());
        }
        mqVo.setRuleId(DssIndexScoreConstant.PERIOD_PUNISH);
        scoreManagerHelper.addScoreManagerInfo(mqVo);
//        scoreManagerHelper.sendToOrgDoris(mqVo);
    }
}
