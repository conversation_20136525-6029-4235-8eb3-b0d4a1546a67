package com.goodsogood.ows.service.scoreManager;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.ScoreManagerEnum;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.DssIndexScoreConstant;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.mapper.score.SasScoreManagerFlowMapper;
import com.goodsogood.ows.model.db.doris.IndexOrgScoreEntity;
import com.goodsogood.ows.model.db.score.SasScoreManagerFlowEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.scoreManager.ScoreManagerMqVo;
import com.goodsogood.ows.service.dss.DssDorisScoreService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.SQLIntegrityConstraintViolationException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/12/13
 */
@Service
@Log4j2
public class ScoreManagerHelper {

    private final SasScoreManagerFlowMapper flowMapper;
    private final DssDorisScoreService dssDorisScoreService;
    private final Errors errors;

    @Autowired
    public ScoreManagerHelper(SasScoreManagerFlowMapper flowMapper, DssDorisScoreService dssDorisScoreService, Errors errors) {
        this.flowMapper = flowMapper;
        this.dssDorisScoreService = dssDorisScoreService;
        this.errors = errors;
    }

    /**
     * 处理到doris
     * @param mqVo
     */
    public void sendToOrgDoris(ScoreManagerMqVo mqVo){
        Integer dataMonth = Integer.valueOf(mqVo.getExecuteTime().substring(0,4)+mqVo.getExecuteTime().substring(5,7));
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");
//        Integer dataMonth = Integer.valueOf(dateFormat.format(new Date()));
        List<IndexOrgScoreEntity> dataList = new ArrayList<>();
        for (ScoreManagerMqVo.TransitScoreManagerVo handleDatum : mqVo.getHandleData()) {
            ScoreManagerEnum managerEnum = mqVo.getManagerEnum();
            Long dorisScore = 0L;
            if (managerEnum.getScoreProperties().getType().equals(1)) {
                dorisScore = managerEnum.getScoreProperties().getScore();
                if(managerEnum.getSendType()==1){//如果是减分，传负数
                    if(dorisScore>0){
                        dorisScore = (-1)* dorisScore;
                    }
                }
            }else if (managerEnum.getScoreProperties().getType().equals(2)) {//阶梯分值
                /* 阶梯扣分 */
                for (ScoreManagerEnum.Ladder ladder : managerEnum.getScoreProperties().getLadderScore()) {
                    if (ladder.getStartRatio() <= handleDatum.getRatio()
                            && ladder.getEndRatio() >= handleDatum.getRatio()) {
                        dorisScore = ladder.getScore();
                        break;
                    }
                }
            }

            //managerEnum  1:组织id 2:人员id 3:党组id
            Integer scoreOrgType = managerEnum.getDataType()==3? 2: managerEnum.getDataType();
            //注意这里的data-month，不确定是不是指当前时间对应的月份
            IndexOrgScoreEntity orgScoreEntity = dssDorisScoreService.createOrgScore(mqVo.getRuleId(), mqVo.getId(),dorisScore,dataMonth,scoreOrgType);
            dataList.add(orgScoreEntity);
        }
        dssDorisScoreService.batchInsertOrgScore(dataList);
    }

    /**
     * 统一的数据库积分管理新增数据新增方法
     * !!! 这里只适用一个月确定只新增一条数据
     *
     * @param mqVo 业务方封装好的方法
     */
    @Async("scoreManagerExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void addScoreManagerInfo(ScoreManagerMqVo mqVo) {
        if (CollectionUtils.isEmpty(mqVo.getHandleData())) {
            return;
        }
        try {
            for (ScoreManagerMqVo.TransitScoreManagerVo handleDatum : mqVo.getHandleData()) {
                ScoreManagerEnum managerEnum = mqVo.getManagerEnum();
                SasScoreManagerFlowEntity flowEntity = new SasScoreManagerFlowEntity();
                flowEntity.setRegionId(mqVo.getRegionId());
                flowEntity.setDataId(mqVo.getId());
                flowEntity.setDataType(managerEnum.getDataType());
                flowEntity.setSourceDataId(handleDatum.getSourceDataId());
                flowEntity.setSourceDataIdStr(handleDatum.getSourceDataIdStr());
                flowEntity.setType(managerEnum.getTypeId());
                flowEntity.setTypeStr(managerEnum.getTypeStr());
                flowEntity.setStatus(Constants.STATUS_YES);
                flowEntity.setIsDel(Constants.STATUS_YES);
                flowEntity.setHandleTime(mqVo.getExecuteTime());
                flowEntity.setScoreType(managerEnum.getScoreType());
                //如果是单个分值
                if (managerEnum.getScoreProperties().getType().equals(1)) {
                    flowEntity.setScore(managerEnum.getScoreProperties().getScore());
                } else if (managerEnum.getScoreProperties().getType().equals(2)) {
                    /* 阶梯扣分 */
                    Long tempScore = null;
                    for (ScoreManagerEnum.Ladder ladder : managerEnum.getScoreProperties().getLadderScore()) {
                        if (ladder.getStartRatio() <= handleDatum.getRatio()
                                && ladder.getEndRatio() >= handleDatum.getRatio()) {
                            tempScore = ladder.getScore();
                            break;
                        }
                    }
                    if (null == tempScore) {
                        /* TODO 发起钉钉消息 */
                        String content = "typeId:[" +
                                mqVo.getTypeId() +
                                "],id:[" +
                                mqVo.getId() +
                                "],time:[" +
                                mqVo.getExecuteTime() +
                                "],错误的ratio:[" +
                                handleDatum.getRatio() +
                                "]";
                        throwMyException(content);
                    }
                    flowEntity.setScore(tempScore);
                } else {/* TODO 发起钉钉消息 */
                    String content = "未知的Type:[" + managerEnum.getScoreProperties().getType() + "]";
                    throwMyException(content);
                }
                if (null != flowEntity.getScore() && flowEntity.getScore() == 0L) {
                    flowEntity.setStatus(0);
                }
                flowEntity.setSendType(managerEnum.getSendType());
                flowEntity.setCreateUser(mqVo.getChangeUserId());
                flowEntity.setCreateTime(LocalDateTime.now());
                flowEntity.setVar1(handleDatum.getVar1());
                flowEntity.setVar2(handleDatum.getVar2());
                flowEntity.setVar3(handleDatum.getVar3());
                flowEntity.setVar4(handleDatum.getVar4());
                flowEntity.setVar5(handleDatum.getVar5());
                flowMapper.insertIgnore(flowEntity);
            }
        } catch (Exception e) {
            e.printStackTrace();
            /* 唯一主键索引约束错误 可以不用处理 */
            if (e.getCause() instanceof SQLIntegrityConstraintViolationException) {
                log.error("唯一主键Id错误:[{}]", e.getMessage(), e);
            } else { /* 其他错误 TODO 发起钉钉消息 */
                throwMyException("插入flow数据错误:[" + e.getMessage() + "]");
            }
        }
    }

    /**
     * 判断库是否已存在处理过的数据
     * 逻辑上如果已经存在 则不会再次处理该条数据
     *
     * @param regionId   区县
     * @param dataType   id数据类型 与t_sas_score_manager_flow 表 data_type 对应1:组织id 2:人员id 3:党组id
     * @param id         实际处理单位值id
     * @param handleTime 指定查询月
     * @param typeId     类型id see:ScoreManagerEnum.typeId
     */
    public Long countExistData(Long regionId, Integer dataType, Long id, String handleTime, Integer typeId) {
        return flowMapper.countExistData(regionId, dataType, id, handleTime, typeId);
    }

    /**
     * 获取数据库中已处理过的业务数据主键id
     *
     * @param regionId   区县
     * @param dataType   id数据类型 与t_sas_score_manager_flow 表 data_type 对应1:组织id 2:人员id 3:党组id
     * @param id         实际处理单位值id
     * @param handleTime 指定查询月
     * @param typeId     类型id see:ScoreManagerEnum.typeId
     */
    public List<Long> findSourceDataIdExistData(Long regionId, Integer dataType, Long id, String handleTime, Integer typeId) {
        return flowMapper.findSourceDataIdExistData(regionId, dataType, id, handleTime, typeId);
    }

    /**
     * 获取数据库中已处理过的业务数据主键id
     *
     * @param regionId   区县
     * @param dataType   id数据类型 与t_sas_score_manager_flow 表 data_type 对应1:组织id 2:人员id 3:党组id
     * @param id         实际处理单位值id
     * @param handleTime 指定查询月
     * @param typeId     类型id see:ScoreManagerEnum.typeId
     */
    public List<String> findSourceDataStrIdExistData(Long regionId, Integer dataType, Long id, String handleTime, Integer typeId) {
        return flowMapper.findSourceDataStrIdExistData(regionId, dataType, id, handleTime, typeId);
    }

    /**
     * 错误抛出
     */
    public void throwMyException(String content) {
        throw new ApiException("组织积分管理错误"+content, new Result(errors, 4751, HttpStatus.INTERNAL_SERVER_ERROR.value(), content));
    }


    /**
     * 获取 startDay 至 endDay 数据: yyyy-MM-dd 左闭右开
     */
    public List<String> findTimeJetLag(LocalDate startDay, LocalDate endDay,
                                       DateTimeFormatter ofPattern) {
        List<String> list = new ArrayList<>();
        LocalDate tempDate = startDay;
        list.add(ofPattern.format(tempDate));
        while (true) {
            if (tempDate.isBefore(endDay)) {
                tempDate = tempDate.plusDays(1);
                list.add(ofPattern.format(tempDate));
            } else {
                break;
            }
        }
        return list;
    }


    /**
     * mq校验 mq过来的枚举类型与方法所属枚举类型是否匹配
     *
     * @param nowEnum   业务逻辑方所属枚举类型
     * @param checkEnum mq传过来的枚举数据
     */
    public void checkScoreManagerEnum(ScoreManagerEnum nowEnum, ScoreManagerEnum checkEnum) {
        //如果不匹配
        if (!nowEnum.getTypeId().equals(checkEnum.getTypeId())) {
            throwMyException("枚举类型与方法传入typeId不匹配 methodTypeId:[" + nowEnum.getTypeId() +
                    "],mqSourceTypeId:[" + checkEnum.getTypeId() + "]");
            //TODO 调用钉钉消息发送
        }
    }

    public ScoreManagerEnum findByTypeId(Integer typeId) {
        ScoreManagerEnum byTypeId = ScoreManagerEnum.findByTypeId(typeId);
        if (null == byTypeId) {
            throwMyException("未找到对应ScoreManagerEnum:[" + typeId + "]");
        }
        return byTypeId;
    }

    public boolean checkEnumInfoAndExist(ScoreManagerEnum anEnum, ScoreManagerMqVo mqVo) {
        mqVo.setManagerEnum(findByTypeId(mqVo.getTypeId()));
        //校验类型是否匹配
        checkScoreManagerEnum(anEnum, mqVo.getManagerEnum());

        //判断积分表是否存在指定月的数据
        Long aLong = countExistData(mqVo.getRegionId(), mqVo.getManagerEnum().getDataType(),
                mqVo.getId(), mqVo.getExecuteTime(), mqVo.getManagerEnum().getTypeId());
        //如果已经处理过了 则跳过
        return aLong <= 0;
    }
}
