package com.goodsogood.ows.service.scoreManager.impl;

import com.goodsogood.ows.annotions.MqConsumerErrorProcess;
import com.goodsogood.ows.common.ScoreManagerEnum;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.mapper.score.SasScoreManagerRatioMapper;
import com.goodsogood.ows.mapper.user.UserMapper;
import com.goodsogood.ows.model.db.score.SasScoreManagerRatioEntity;
import com.goodsogood.ows.model.mongodb.clickSpy.NewsListEntity;
import com.goodsogood.ows.model.vo.scoreManager.ScoreManagerMqVo;
import com.goodsogood.ows.service.scoreManager.ScoreManagerHelper;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.NumberUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 党员学习习近平新时代中国特色社会主义思想和新闻报道
 * "支部下的党员阅读新闻学习情况，按月积分，按日平均完成率：
 * 90%以上，加5分；
 * 70%以上，加3分；
 * 50%以上，加1分；
 * 50%以下，不加分"
 *
 * <AUTHOR> ruoyu
 * @date : 2021/12/15
 */
@Service
@Log4j2
public class BaseOrgNewsReadRatioService {

    private final ScoreManagerHelper scoreManagerHelper;
    private final SasScoreManagerRatioMapper sasScoreManagerRatioMapper;
    private final MyMongoTemplate mongoTemplate;
    private final UserMapper userMapper;

    @Autowired
    public BaseOrgNewsReadRatioService(ScoreManagerHelper scoreManagerHelper,
                                       SasScoreManagerRatioMapper sasScoreManagerRatioMapper,
                                       MyMongoTemplate mongoTemplate, UserMapper userMapper) {
        this.scoreManagerHelper = scoreManagerHelper;
        this.sasScoreManagerRatioMapper = sasScoreManagerRatioMapper;
        this.mongoTemplate = mongoTemplate;
        this.userMapper = userMapper;
    }

    @RabbitListener(queues = "#{@ScoreManagerConfig.getBaseOrgPeopleNewsRead()}")
    @MqConsumerErrorProcess
    public void baseOrgNewsReadRatio(String vo) {
        log.debug("baseOrgNewsReadRatio-> [{}]", vo);
        ScoreManagerMqVo mqVo = JsonUtils.fromJson(vo, ScoreManagerMqVo.class);
        /* 前置判断与初始化枚举 */
        if (!scoreManagerHelper.checkEnumInfoAndExist(ScoreManagerEnum.BASE_ORG_PEOPLE_NEWS_READ, mqVo)) {
            return;
        }

        DateTimeFormatter ofPattern = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate computeDate = LocalDate.from(ofPattern.parse(mqVo.getExecuteTime() + "-01"));
        LocalDate nowDate = LocalDate.now();
        //如果时间大于当前月 则是错误的时间 这里终止
        if (computeDate.isAfter(nowDate)) {
            return;
        }
        //判断指定月是否存在积分数据处理记录
        Long aLong = scoreManagerHelper.countExistData(mqVo.getRegionId(), mqVo.getManagerEnum().getDataType(),
                mqVo.getId(), mqVo.getExecuteTime(), mqVo.getManagerEnum().getTypeId());
        if (aLong > 0L) {
            return;
        }

        List<String> monthDay;
        String queryMonth;
        //如果是当前月
        if (computeDate.getYear() == nowDate.getYear()
                && computeDate.getMonthValue() == nowDate.getMonthValue()) {
            monthDay = scoreManagerHelper.findTimeJetLag(computeDate, nowDate, ofPattern);
            queryMonth = computeDate.plusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
        } else { /* 否则就是其他小于当前月的月份 */
            monthDay = scoreManagerHelper.findTimeJetLag(computeDate, computeDate.with(TemporalAdjusters.lastDayOfMonth()), ofPattern);
            queryMonth = mqVo.getExecuteTime();
        }
        if (CollectionUtils.isEmpty(monthDay)) {
            return;
        }
        //查出组织的人员
        List<Long> listUser = userMapper.getCqycPartyUserByOrgId(mqVo.getId());
        //如果为空则只保存一次无效记录
        if (CollectionUtils.isEmpty(listUser)) {
            for (String time : monthDay) {
                SasScoreManagerRatioEntity ratioEntity = new SasScoreManagerRatioEntity();
                ratioEntity.setRegionId(mqVo.getRegionId());
                ratioEntity.setOrgId(mqVo.getId());
                ratioEntity.setType(2);
                ratioEntity.setTotal(0);
                ratioEntity.setFind(-1);
                ratioEntity.setDate(LocalDate.parse(time, ofPattern));
                ratioEntity.setRatio(-1d);
                ratioEntity.setCreateUser(mqVo.getChangeUserId());
                ratioEntity.setCreateTime(LocalDateTime.now());
                sasScoreManagerRatioMapper.insertIgnore(ratioEntity);
            }
        } else {
            for (String time : monthDay) {
                Integer readCount = findMongoReadInfo(time, mqVo.getRegionId(), mqVo.getId(), listUser);
                int size = listUser.size();
                BigDecimal divide = NumberUtils.divide(readCount, size, 2, RoundingMode.HALF_UP);
                SasScoreManagerRatioEntity ratioEntity = new SasScoreManagerRatioEntity();
                ratioEntity.setRegionId(mqVo.getRegionId());
                ratioEntity.setOrgId(mqVo.getId());
                ratioEntity.setType(2);
                ratioEntity.setTotal(size);
                ratioEntity.setFind(readCount);
                ratioEntity.setDate(LocalDate.parse(time, ofPattern));
                ratioEntity.setRatio(divide.doubleValue());
                ratioEntity.setCreateUser(mqVo.getChangeUserId());
                ratioEntity.setCreateTime(LocalDateTime.now());
                sasScoreManagerRatioMapper.insertIgnore(ratioEntity);
            }
        }
        /* 计算月分数 */
        mqVo.setSingleHandleData(sasScoreManagerRatioMapper.findAvgRatio(mqVo.getId(), 2, queryMonth));
        scoreManagerHelper.addScoreManagerInfo(mqVo);
    }


    private Integer findMongoReadInfo(String time, Long regionId, Long id, List<Long> listUser) {
        Date[] dates = dateToISODateRange(time);

        Criteria criteria = Criteria
                .where("type").is("A")
                .and("regionId").is(regionId)
                .and("orgId").is(id.toString())
                .and("userId").in(listUser.stream().map(Object::toString).collect(Collectors.toList()))
                .and("transferTime").gte(dates[0]).lt(dates[1]);

        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.project("userId"),
                Aggregation.group("userId").count().as("sum1"),
                Aggregation.match(Criteria.where("sum1").gte(5)));
        AggregationResults<Object> aggregate = mongoTemplate.aggregate(aggregation, mongoTemplate.getCollectionName(NewsListEntity.class), Object.class);

        return CollectionUtils.isEmpty(aggregate.getMappedResults())?0:aggregate.getMappedResults().size();
    }

    private Date[] dateToISODateRange(String date) {
        Date[] dates = new Date[2];
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        try {
            dates[0] = format.parse(date + "T00:00:00.000Z");
            dates[1] = format.parse(date + "T23:59:59.999Z");
        } catch (ParseException e) {
            e.printStackTrace();
            scoreManagerHelper.throwMyException("时间格式化错误:[" + e.getMessage() + "]");
        }
        return dates;
    }
}
