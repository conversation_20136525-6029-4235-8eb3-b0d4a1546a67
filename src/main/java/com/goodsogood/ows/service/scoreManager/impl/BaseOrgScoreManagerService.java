package com.goodsogood.ows.service.scoreManager.impl;

import com.goodsogood.ows.annotions.MqConsumerErrorProcess;
import com.goodsogood.ows.common.ScoreManagerEnum;
import com.goodsogood.ows.configuration.DssIndexScoreConstant;
import com.goodsogood.ows.mapper.user.OrgPeriodMapper;
import com.goodsogood.ows.model.vo.scoreManager.ScoreManagerMqVo;
import com.goodsogood.ows.service.dss.DssDorisScoreService;
import com.goodsogood.ows.service.scoreManager.ScoreManagerHelper;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;


/**
 * 党支部积分管理
 *
 * <AUTHOR> ruoyu
 * @date : 2021/12/13
 */
@Service
@Log4j2
public class BaseOrgScoreManagerService {

    private final OrgPeriodMapper orgPeriodMapper;
    private final ScoreManagerHelper scoreManagerHelper;
    private final DssDorisScoreService dorisScoreService;

    @Autowired
    public BaseOrgScoreManagerService(OrgPeriodMapper orgPeriodMapper,
                                      ScoreManagerHelper scoreManagerHelper, DssDorisScoreService dorisScoreService) {
        this.orgPeriodMapper = orgPeriodMapper;
        this.scoreManagerHelper = scoreManagerHelper;
        this.dorisScoreService = dorisScoreService;
    }

    /**
     * 未按期开展换届工作 组织届次到期未换届，积分-5
     */
    @RabbitListener(queues = "#{@ScoreManagerConfig.getBaseOrgPeriodExpireNotCreate()}")
    @MqConsumerErrorProcess
    public void periodExpireNotCreate(String vo) {
        log.debug("periodExpireNotCreate-> [{}]", vo);
        ScoreManagerMqVo mqVo = JsonUtils.fromJson(vo, ScoreManagerMqVo.class);
        /* 前置判断与初始化枚举 */
        if (!scoreManagerHelper.checkEnumInfoAndExist(ScoreManagerEnum.BASE_ORG_PERIOD_EXPIRE_NOT_CREATE, mqVo)) {
            return;
        }

        //判断是否指定月到期下个月没有接着的届次
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate queryMonth = LocalDate.parse(mqVo.getExecuteTime() + "-01", dateTimeFormatter);
        String nextQueryMonth = dateTimeFormatter.format(queryMonth.plusMonths(1));
        Long periodId = orgPeriodMapper.dueThisMonthNextNotExist(mqVo.getRegionId(), mqVo.getId(), mqVo.getExecuteTime(), nextQueryMonth);
        //不存在则跳过
        if (null == periodId) {
            return;
        }
        mqVo.setSingleHandleData(periodId, periodId.toString());
        mqVo.setRuleId(DssIndexScoreConstant.PERIOD_NOTRANSF);
        scoreManagerHelper.addScoreManagerInfo(mqVo);
//        scoreManagerHelper.sendToOrgDoris(mqVo);
    }





    /**
     * 配齐组织书记 届次管理中设置书记，加5分
     * 队列名称 "BASE_ORG_PERIOD_EXIST_LEADER"
     */
    @RabbitListener(queues = "#{@ScoreManagerConfig.getBaseOrgPeriodExistLeader()}")
    @MqConsumerErrorProcess
    public void periodExistLeader(String vo) {
        log.debug("periodExistLeader-> [{}]", vo);
        ScoreManagerMqVo mqVo = JsonUtils.fromJson(vo, ScoreManagerMqVo.class);
        /* 前置判断与初始化枚举 */
        if (!scoreManagerHelper.checkEnumInfoAndExist(ScoreManagerEnum.BASE_ORG_PERIOD_EXIST_LEADER, mqVo)) {
            return;
        }

        String queryTime = mqVo.getExecuteTime() + "-01";// 2023-05-01
        Long periodMemberId = orgPeriodMapper.periodExistLeader(mqVo.getRegionId(), queryTime, mqVo.getId());
        //不存在则跳过
        if (null == periodMemberId) {
            return;
        }
        mqVo.setSingleHandleData(periodMemberId, periodMemberId.toString());
        mqVo.setRuleId(DssIndexScoreConstant.PERIOD_SECTARY);
        scoreManagerHelper.addScoreManagerInfo(mqVo);
        // 这里不需要发送到doris ScoreAddService -> 109行
//        scoreManagerHelper.sendToOrgDoris(mqVo);
    }
}
