package com.goodsogood.ows.service.scoreManager.impl;

import com.goodsogood.ows.annotions.MqConsumerErrorProcess;
import com.goodsogood.ows.common.ScoreManagerEnum;
import com.goodsogood.ows.configuration.DssIndexScoreConstant;
import com.goodsogood.ows.mapper.sas.CemUniqueListMapper;
import com.goodsogood.ows.mapper.user.PartyGroupMapper;
import com.goodsogood.ows.model.db.sas.CemUniqueListEntity;
import com.goodsogood.ows.model.vo.scoreManager.ScoreManagerMqVo;
import com.goodsogood.ows.service.scoreManager.ScoreManagerHelper;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

/**
 * 党组积分相关
 * 成立党的建设工作领导小组 成立党的建设工作领导小组，加2分
 * 本单位有受处分 本单位有受党纪处分、政务处分、组织处理的，每1人次扣5分，应累加
 * 建立党支部工作联系点制度 每个党组成员均设置了支部联系点，加2分
 * 建立干部基层联系点制度 每个党组成员均设置了基层联系点，加2分
 *
 * <AUTHOR> ruoyu
 * @date : 2021/12/16
 */
@Service
@Log4j2
public class PartyGroupScoreManagerService {
    private final CemUniqueListMapper cemUniqueListMapper;
    private final ScoreManagerHelper scoreManagerHelper;
    private final PartyGroupMapper partyGroupMapper;

    @Autowired
    public PartyGroupScoreManagerService(CemUniqueListMapper cemUniqueListMapper, ScoreManagerHelper scoreManagerHelper,
                                         PartyGroupMapper partyGroupMapper) {
        this.cemUniqueListMapper = cemUniqueListMapper;
        this.scoreManagerHelper = scoreManagerHelper;
        this.partyGroupMapper = partyGroupMapper;
    }

    /**
     * 一个党组永远只加一次
     * 成立党的建设工作领导小组 成立党的建设工作领导小组，加2分
     */
    @RabbitListener(queues = "#{@ScoreManagerConfig.getPartyGroupExistLeaderGroup()}")
    @MqConsumerErrorProcess
    public void partyGroupExistLeaderGroup(String vo) {
        log.debug("partyGroupExistLeaderGroup-> [{}]", vo);
        ScoreManagerMqVo mqVo = JsonUtils.fromJson(vo, ScoreManagerMqVo.class);
        /* 前置判断与初始化枚举 */
        if (!scoreManagerHelper.checkEnumInfoAndExist(ScoreManagerEnum.PARTY_GROUP_EXIST_LEADER_GROUP, mqVo)) {
            return;
        }

//        mqVo.setExecuteTime(ScoreManagerEnum.Constant.FINAL_TIME);
        Long primaryId = partyGroupMapper.existsPartyWorkLeaderGroup(mqVo.getId(),mqVo.getExecuteTime());

        if (null == primaryId) {
            return;
        }

        mqVo.setSingleHandleData(primaryId, primaryId.toString());
//        scoreManagerHelper.addScoreManagerInfo(mqVo);
        mqVo.setRuleId(DssIndexScoreConstant.GROUP_LEADER);
        scoreManagerHelper.sendToOrgDoris(mqVo);
    }

    /**
     * 本单位有受处分 本单位有受党纪处分、政务处分、组织处理的，每1人次扣5分，应累加
     */
    @RabbitListener(queues = "#{@ScoreManagerConfig.getPartyGroupExistPunish()}")
    @MqConsumerErrorProcess
    public void partyGroupExistPunish(String vo) {
        log.debug("partyGroupExistPunish-> [{}]", vo);
        ScoreManagerMqVo mqVo = JsonUtils.fromJson(vo, ScoreManagerMqVo.class);
        /* 前置判断与初始化枚举 */
        if (!scoreManagerHelper.checkEnumInfoAndExist(ScoreManagerEnum.PARTY_GROUP_EXIST_PUNISH, mqVo)) {
            return;
        }

//        mqVo.setExecuteTime(ScoreManagerEnum.Constant.FINAL_TIME);
        List<Long> penalizeId = partyGroupMapper.partyGroupExistPunish(mqVo.getId(), mqVo.getExecuteTime());

        if (CollectionUtils.isEmpty(penalizeId)) {
            return;
        }

        List<ScoreManagerMqVo.TransitScoreManagerVo> list = new ArrayList<>(penalizeId.size());
        mqVo.setHandleData(list);
        for (Long id : penalizeId) {
            list.add(ScoreManagerMqVo.TransitScoreManagerVo.builder()
                    .sourceDataId(id)
                    .sourceDataIdStr(id.toString())
                    .build()
            );
        }
//        scoreManagerHelper.addScoreManagerInfo(mqVo);
        mqVo.setRuleId(DssIndexScoreConstant.GROUP_PUNISH);
        scoreManagerHelper.sendToOrgDoris(mqVo);
    }

    /**
     * 一个党组永远只加一次
     * 建立党支部工作联系点制度 每个党组成员均设置了支部联系点，加2分
     */
    @RabbitListener(queues = "#{@ScoreManagerConfig.getPartyGroupExistWorkContact()}")
    @MqConsumerErrorProcess
    public void partyGroupExistWorkContact(String vo) {
        log.debug("partyGroupExistWorkContact-> [{}]", vo);
        ScoreManagerMqVo mqVo = JsonUtils.fromJson(vo, ScoreManagerMqVo.class);
        /* 前置判断与初始化枚举 */
        if (!scoreManagerHelper.checkEnumInfoAndExist(ScoreManagerEnum.PARTY_GROUP_EXIST_WORK_CONTACT, mqVo)) {
            return;
        }

//        mqVo.setExecuteTime(ScoreManagerEnum.Constant.FINAL_TIME);
        Boolean add = partyGroupMapper.partyGroupExistWorkContact(mqVo.getId(), 2);

        if (!add) {
            return;
        }
        //判断是否已经加过了
        CemUniqueListEntity cemUniqueListEntity = new CemUniqueListEntity();
        cemUniqueListEntity.setRuleId(DssIndexScoreConstant.GROUP_WORKSPACE);
        cemUniqueListEntity.setUniqueCode(String.valueOf(mqVo.getId()));
        List<CemUniqueListEntity> hisList = cemUniqueListMapper.select(cemUniqueListEntity);
        if(!CollectionUtils.isEmpty(hisList)){
            return;
        }

        mqVo.setSingleHandleDefaultData();
//        scoreManagerHelper.addScoreManagerInfo(mqVo);
        mqVo.setRuleId(DssIndexScoreConstant.GROUP_WORKSPACE);
        scoreManagerHelper.sendToOrgDoris(mqVo);
        //保存到记录，防止重复加
        cemUniqueListMapper.insert(cemUniqueListEntity);
    }

    /**
     * 建立干部基层联系点制度 每个党组成员均设置了基层联系点，加2分
     */
    @RabbitListener(queues = "#{@ScoreManagerConfig.getPartyGroupExistBaseContact()}")
    @MqConsumerErrorProcess
    public void partyGroupExistBaseContact(String vo) {
        log.debug("partyGroupExistBaseContact-> [{}]", vo);
        ScoreManagerMqVo mqVo = JsonUtils.fromJson(vo, ScoreManagerMqVo.class);
        /* 前置判断与初始化枚举 */
        if (!scoreManagerHelper.checkEnumInfoAndExist(ScoreManagerEnum.PARTY_GROUP_EXIST_BASE_CONTACT, mqVo)) {
            return;
        }

        mqVo.setExecuteTime(ScoreManagerEnum.Constant.FINAL_TIME);
        Boolean add = partyGroupMapper.partyGroupExistWorkContact(mqVo.getId(), 3);

        if (!add) {
            return;
        }
        //判断是否已经加过了
        CemUniqueListEntity cemUniqueListEntity = new CemUniqueListEntity();
        cemUniqueListEntity.setRuleId(DssIndexScoreConstant.GROUP_BASESPACE);
        cemUniqueListEntity.setUniqueCode(String.valueOf(mqVo.getId()));
        List<CemUniqueListEntity> hisList = cemUniqueListMapper.select(cemUniqueListEntity);
        if(!CollectionUtils.isEmpty(hisList)){
            return;
        }

        mqVo.setSingleHandleDefaultData();
//        scoreManagerHelper.addScoreManagerInfo(mqVo);
        mqVo.setRuleId(DssIndexScoreConstant.GROUP_BASESPACE);
        scoreManagerHelper.sendToOrgDoris(mqVo);
        //保存到记录，防止重复加
        cemUniqueListMapper.insert(cemUniqueListEntity);
    }
}
