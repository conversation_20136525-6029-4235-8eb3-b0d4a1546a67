package com.goodsogood.ows.service.scoreManager.ding;

import lombok.Data;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/12/27
 */
public class DingTalkFormHelper {

    public static ScoreManagerDingTalkForm generateMarkdownMsg(String titel, String text) {
        ScoreManagerDingTalkForm scoreManagerDingTalkForm = new ScoreManagerDingTalkForm();
        scoreManagerDingTalkForm.setMarkdown(new ScoreManagerDingTalkInnerForm());
        scoreManagerDingTalkForm.getMarkdown().setTitle(titel);
        scoreManagerDingTalkForm.getMarkdown().setText(text);
        return scoreManagerDingTalkForm;
    }

    @Data
    public static class ScoreManagerDingTalkForm  {
        private String msgtype = "markdown";
        private ScoreManagerDingTalkInnerForm markdown;
    }

    @Data
    public static class ScoreManagerDingTalkInnerForm {
        private String title;
        private String text;
    }
}
