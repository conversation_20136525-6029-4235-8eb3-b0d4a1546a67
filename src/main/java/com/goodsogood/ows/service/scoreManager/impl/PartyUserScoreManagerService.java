package com.goodsogood.ows.service.scoreManager.impl;

import com.goodsogood.ows.annotions.MqConsumerErrorProcess;
import com.goodsogood.ows.common.ScoreManagerEnum;
import com.goodsogood.ows.mapper.user.UserLoginLogMapper;
import com.goodsogood.ows.model.vo.scoreManager.ScoreManagerMqVo;
import com.goodsogood.ows.service.scoreManager.ScoreManagerHelper;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 党员-登录数智党建平台 每天登录+1 每日上线 1
 *
 * <AUTHOR> ruoyu
 * @date : 2021/12/21
 */
@Service
@Log4j2
public class PartyUserScoreManagerService {

    private final ScoreManagerHelper scoreManagerHelper;
    private final UserLoginLogMapper userLoginLogMapper;

    @Autowired
    public PartyUserScoreManagerService(ScoreManagerHelper scoreManagerHelper,
                                        UserLoginLogMapper userLoginLogMapper) {
        this.scoreManagerHelper = scoreManagerHelper;
        this.userLoginLogMapper = userLoginLogMapper;
    }

    /**
     * 党员-登录数智党建平台 每天登录+1 每日上线 1
     * 党员-登录数智党建平台 积分改为登录实时上报
     * @param vo
     */
   /* @RabbitListener(queues = "#{@ScoreManagerConfig.getPartyMemberLogin()}")
    @MqConsumerErrorProcess
    public void partyUserLoginScoreManager(String vo) {
        log.debug("partyUserLoginScoreManager-> [{}]", vo);
        ScoreManagerMqVo mqVo = JsonUtils.fromJson(vo, ScoreManagerMqVo.class);
        if (!scoreManagerHelper.checkEnumInfoAndExist(ScoreManagerEnum.PARTY_MEMBER_LOGIN, mqVo)) {
            return;
        }

        //获取指定月每天的日期  默认把指定月的上月数据也跑次
        //为满足每月第一天跑不到上一月最后一天的情况 数据库有设置唯一联合索引 可以保证不会重复插入
        DateTimeFormatter ofPattern = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate endDate = LocalDate.from(ofPattern.parse(mqVo.getExecuteTime() + "-01"));
        LocalDate starDate = endDate.minusMonths(1);
        LocalDate nowDate = LocalDate.now();
        //如果时间大于当前月 则是错误的时间 这里终止
        if (endDate.isAfter(nowDate)) {
            return;
        }

        List<String> timeJetLag = scoreManagerHelper.findTimeJetLag(starDate,
                endDate.with(TemporalAdjusters.lastDayOfMonth()), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        if (CollectionUtils.isEmpty(timeJetLag)) {
            return;
        }

        List<ScoreManagerMqVo.TransitScoreManagerVo> handleData = new ArrayList<>(timeJetLag.size());
        mqVo.setHandleData(handleData);
        for (String day : timeJetLag) {
            Long time = userLoginLogMapper.checkIsLoginBy(mqVo.getId(), day);
            if (null == time) {
                continue;
            }
            handleData.add(ScoreManagerMqVo.TransitScoreManagerVo.builder()
                    .sourceDataId(time)
                    .sourceDataIdStr(time.toString()).build());
        }

        if (CollectionUtils.isEmpty(handleData)) {
            return;
        }
        // 防止把上个月的数据当成当月的数据，这里需要数据进行处理
        DateTimeFormatter ofPattern2 = DateTimeFormatter.ofPattern("yyyyMMdd");
        // 本月数据
        final List<ScoreManagerMqVo.TransitScoreManagerVo> currentMonthDataList =
                mqVo.getHandleData().stream().filter(data -> LocalDate.from(ofPattern2.parse(data.getSourceDataIdStr())).getMonth().equals(nowDate.getMonth())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(currentMonthDataList)) {
            ScoreManagerMqVo mqVo1 = new ScoreManagerMqVo();
            BeanUtils.copyProperties(mqVo, mqVo1);
            mqVo1.setHandleData(currentMonthDataList);
            scoreManagerHelper.addScoreManagerInfo(mqVo1);
        }
        // 上月数据
        final List<ScoreManagerMqVo.TransitScoreManagerVo> lastMonthDataList =
                mqVo.getHandleData().stream().filter(data -> LocalDate.from(ofPattern2.parse(data.getSourceDataIdStr())).getMonth().equals(starDate.getMonth())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(lastMonthDataList)) {
            ScoreManagerMqVo mqVo2 = new ScoreManagerMqVo();
            BeanUtils.copyProperties(mqVo, mqVo2);
            mqVo2.setHandleData(lastMonthDataList);
            mqVo2.setExecuteTime(starDate.format(DateTimeFormatter.ofPattern("yyyy-MM")));
            scoreManagerHelper.addScoreManagerInfo(mqVo2);
        }
    }*/
}
