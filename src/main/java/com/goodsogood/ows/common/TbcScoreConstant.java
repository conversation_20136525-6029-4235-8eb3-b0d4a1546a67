package com.goodsogood.ows.common;

/**
 * @ClassName : TbcScoreConfig
 * <AUTHOR> tc
 * @Date: 2022/6/22 10:23
 * @Description : 党业融合积分相关常量
 */
public class TbcScoreConstant {
    public final static String SYSTEM_AVG_SCORE="SYSTEM_AVG_SCORE_";
    public final static String SYSTEM_MAX_SCORE="SYSTEM_MAX_SCORE_";

    //党组织积分类型
    public static final Integer SCORE_ORG_TYPE_ORG = 1;

    //党组积分类型
    public static final Integer SCORE_ORG_TYPE_GROUP = 2;

    //党建积分类型
    public static final Integer PARENT_SCORE_TYPE_PARTY = 1;

    //业务积分类型
    public static final Integer PARENT_SCORE_TYPE_BUSINESS = 2;

    //结果类型 党建积分
    public static final String RESULT_TYPE_PARTY ="party";

    //结果类型 业务积分
    public static final String RESULT_TYPE_BUSINESS ="business";

    //序列类型 党建积分
    //党建积分本单位党员
    public static final String SERIAL_TYPE_PARTY_UNIT = "party_unit";
    //党建积分用户所属序列党员
    public static final String SERIAL_TYPE_PARTY_MARKETING = "party_marketing";
    //党建积分全市党员
    public static final String SERIAL_TYPE_PARTY_ALL = "party_all";

    //序列类型 业务积分
    //业务积分本单位用户所属员工
    public static final String SERIAL_TYPE_BUSINESS_UNIT = "business_unit";
    //业务积分全市用户所属员工
    public static final String SERIAL_TYPE_BUSINESS_MARKETING = "business_marketing";
    //业务积分全市用户所属党员
    public static final String SERIAL_TYPE_BUSINESS_ALL = "business_all";

    //党建积分总排行榜缓存
    public static final String RANK_PARTY_ALL = "SAS_TBC_SCORE_RANK_PARTY_ALL_";

    //业务积分总排行榜缓存
    public static final String RANK_BUSINESS_ALL = "SAS_TBC_SCORE_RANK_BUSINESS_ALL_";

    //个人党建积分排行缓存
    public static final String RANK_PARTY_USER = "SAS_TBC_SCORE_RANK_PARTY_USER_";

    //个人业务积分排行缓存
    public static final String RANK_BUSINESS_USER = "SAS_TBC_SCORE_RANK_BUSINESS_USER_";
}
