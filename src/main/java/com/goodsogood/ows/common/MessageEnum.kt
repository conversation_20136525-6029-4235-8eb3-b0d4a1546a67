package com.goodsogood.ows.common

class MessageCommon {
    companion object {
        const val NEWS: String = "news"
        const val BRAND: String = "brand"
        const val POSITION: String = "position"
        const val ACTIVITY: String = "activity"
        const val ECP: String = "ecp"
        const val STYLE: String = "style"
    }
}

enum class MessageEnum(val type: Int, val typeName: String, val component: String) {

    NEWS(1, "党内资讯", MessageCommon.NEWS),
    BRAND(2, "党建品牌", MessageCommon.BRAND),
    POSITION(3, "党建阵地", MessageCommon.POSITION),
    ACTIVITY(4, "线上互动", MessageCommon.ACTIVITY),
    ECP(5, "云区动态", MessageCommon.ECP),
    STYLE(6, "支部风采", MessageCommon.STYLE);

    companion object {
        fun randomType(): MessageEnum {
            // 坐标
            return values()[arrayOf(1,2,4,5).random()]
        }
    }
}