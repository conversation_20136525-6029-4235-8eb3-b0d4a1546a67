package com.goodsogood.ows.common;

import com.goodsogood.ows.configuration.ScoreManagerConfig;
import lombok.Getter;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/12/8
 */
@Getter
public enum ScoreManagerEnum {

    /**
     * 党支部-未按期开展换届工作
     */
    BASE_ORG_PERIOD_EXPIRE_NOT_CREATE(1, "党支部-未按期开展换届工作",
            Constant.BRANCH_CHILD_TYPE_CHILD,
            new ScoreProperties(1, 5L), 1, 1, 1,
            ScoreManagerConfig.BASE_ORG_PERIOD_EXPIRE_NOT_CREATE, 24),
    /**
     * 党支部-配齐组织书记
     */
    BASE_ORG_PERIOD_EXIST_LEADER(2, "党支部-配齐组织书记",
            Constant.BRANCH_CHILD_TYPE_CHILD,
            new ScoreProperties(1, 5L), 0, 1, 1,
            ScoreManagerConfig.BASE_ORG_PERIOD_EXIST_LEADER, 24),
    /**
     * 党支部-支部内党员受处罚
     */
    BASE_ORG_PEOPLE_PUNISH(3, "党支部-支部内党员受处罚",
            Constant.BRANCH_CHILD_TYPE_CHILD,
            new ScoreProperties(1, 5L), 1, 1, 1,
            ScoreManagerConfig.BASE_ORG_PEOPLE_PUNISH, 20),
    /**
     * 党支部-党员积极登录登录数智党建平台
     */
    BASE_ORG_PEOPLE_LOGIN_RATIO(4, "党支部-党员积极登录登录数智党建平台",
            Constant.BRANCH_CHILD_TYPE_CHILD, ((Supplier<ScoreProperties>) () -> {
        List<Ladder> list = new ArrayList<>(4);
        list.add(new Ladder(0.0, 49.99, 0L));
        list.add(new Ladder(50.0, 69.99, 1L));
        list.add(new Ladder(70.0, 89.99, 3L));
        list.add(new Ladder(90.0, 100.00, 5L));
        return new ScoreProperties(2, list);
    }).get(), 0, 1, 1, ScoreManagerConfig.BASE_ORG_PEOPLE_LOGIN_RATIO, 17),

    /**
     * 党支部-党员学习习近平新时代中国特色社会主义思想和新闻报道
     */
    BASE_ORG_PEOPLE_NEWS_READ(5, "党支部-党员学习习近平新时代中国特色社会主义思想和新闻报道",
            Constant.BRANCH_CHILD_TYPE_CHILD, ((Supplier<ScoreProperties>) () -> {
        List<Ladder> list = new ArrayList<>(4);
        list.add(new Ladder(0.0, 49.99, 0L));
        list.add(new Ladder(50.0, 69.99, 1L));
        list.add(new Ladder(70.0, 89.99, 3L));
        list.add(new Ladder(90.0, 100.00, 5L));
        return new ScoreProperties(2, list);
    }).get(), 0, 1, 1, ScoreManagerConfig.BASE_ORG_PEOPLE_NEWS_READ, 17),

    /**
     * 党委党总支-健全组织机构，配强组织力量
     */
    PARTY_ORG_EXIST_PERIOD_LEADER(6, "党委党总支-健全组织机构，配强组织力量",
            Constant.COMMUNIST_GENERAL_TYPE_CHILD,
            new ScoreProperties(1, 5L), 0, 1, 1,
            ScoreManagerConfig.PARTY_ORG_EXIST_PERIOD_LEADER, 24),

    /**
     * 党委党总支-未督促提醒所属党支部按期开展换届
     */
    PARTY_ORG_PERIOD_EXPIRE_NOT_CREATE(7, "党委党总支-未督促提醒所属党支部按期开展换届",
            Constant.COMMUNIST_GENERAL_TYPE_CHILD,
            new ScoreProperties(1, 2L), 1, 1, 1,
            ScoreManagerConfig.PARTY_ORG_PERIOD_EXPIRE_NOT_CREATE, 24),

    /**
     * 党委党总支-组织内有党员受处分
     */
    PARTY_ORG_PEOPLE_PUNISH(8, "党委党总支-组织内有党员受处分",
            Constant.COMMUNIST_GENERAL_TYPE_CHILD,
            new ScoreProperties(1, 5L), 1, 1, 1,
            ScoreManagerConfig.PARTY_ORG_PEOPLE_PUNISH, 20),

    /**
     * 党组加分对象-成立党的建设工作领导小组
     */
    PARTY_GROUP_EXIST_LEADER_GROUP(9, "党组-成立党的建设工作领导小组",
            new ArrayList<>(0),
            new ScoreProperties(1, 2L), 0, 3, 1,
            ScoreManagerConfig.PARTY_GROUP_EXIST_LEADER_GROUP, 19),

    /**
     * 党组加分对象-本单位有受处分
     */
    PARTY_GROUP_EXIST_PUNISH(10, "党组-本单位有受处分",
            new ArrayList<>(0),
            new ScoreProperties(1, 5L), 1, 3, 1,
            ScoreManagerConfig.PARTY_GROUP_EXIST_PUNISH, 20),

    /**
     * 党组加分对象--建立党支部工作联系点制度
     */
    PARTY_GROUP_EXIST_WORK_CONTACT(11, "党组-建立党支部工作联系点制度",
            new ArrayList<>(0),
            new ScoreProperties(1, 2L), 0, 3, 1,
            ScoreManagerConfig.PARTY_GROUP_EXIST_WORK_CONTACT, 21),

    /**
     * 建立干部基层联系点制度 每个党组成员均设置了基层联系点，加2分
     */
    PARTY_GROUP_EXIST_BASE_CONTACT(12, "党组-建立干部基层联系点制度",
            new ArrayList<>(0),
            new ScoreProperties(1, 2L), 0, 3, 1,
            ScoreManagerConfig.PARTY_GROUP_EXIST_BASE_CONTACT, 21),

    /* 以下只单独加减积分 */

    /**
     * 党员每月主动、按期、足额缴纳党费 按时足额缴纳党费的，加2分
     */
    PARTY_MEMBER_PAY_ON_TIME(13, "党员-党员每月主动、按期、足额缴纳党费",
            new ArrayList<>(0),
            new ScoreProperties(1, 2L), 0, 2, 2, null, 18),

    /**
     * 党员-连续6个月未缴党费的 无正当理由扣10分，并按相关规定处理
     */
    PARTY_MEMBER_SIX_NOT_PAY(14, "党员-连续6个月未缴党费的",
            new ArrayList<>(0),
            new ScoreProperties(1, 10L), 1, 2, 2, null, 18),

    /**
     * 党支部-党员主动、按期、足额缴纳党费 党员主动、按期、足额缴纳党费 +5分
     */
    BASE_ORG_PAY_ON_TIME(15, "党支部-党员主动、按期、足额缴纳党费",
            Constant.BRANCH_CHILD_TYPE_CHILD,
            new ScoreProperties(1, 5L), 0, 1, 2, null, 25),

    /**
     * 党组-建设本单位特色品牌 有党建品牌，积分+2
     */
    PARTY_GROUP_BRAND_BUILDING(16, "党组-建设本单位特色品牌",
            new ArrayList<>(0),
            new ScoreProperties(1, 2L), 0, 3, 2,
            null, 22),

    /**
     * 党组-党建阵地建设 有党建阵地，积分+2
     */
    PARTY_GROUP_POSITION_BUILDING(17, "党组-党建阵地建设",
            new ArrayList<>(0),
            new ScoreProperties(1, 2L), 0, 3, 2,
            null, 23),
    /* 以上只单独加减积分 */


    /**
     * 党员-登录数智党建平台 每天登录+1 每日上线 1
     * 改为登录即时调用 by shenjian
     */
    PARTY_MEMBER_LOGIN(18, "党员-登录数智党建平台",
            Constant.BRANCH_CHILD_TYPE_CHILD,
            new ScoreProperties(1, 1L), 0, 2, 1,
            ScoreManagerConfig.PARTY_MEMBER_LOGIN, 34),

    /**
     * 人员-业务积分 每月计算上月 根据排名计算积分，此项为自处理的特殊规则积份，在此只是为了占用一个typeId类型
     * 第一步：各项业务指标先排名，排名之后赋分；排名（1%），得分=满分（100）*（1-排名）；例如排名10%，则得分为100*90%=90分；
     *
     * 第二步：每个月的所有业务指标得分求和；
     *
     * 第三步：月度业务指标得分排名，排名之后奖励积分；排名（1%），积分=满分（300）*（1-排名）；
     */
    PARTY_POSITION_BUILDING(19, "人员-业务积分",
            new ArrayList<>(0),
            new ScoreProperties(1, 0L), 0, 2, 2,
            null, 31);

    /**
     * 积分处理逻辑类型Id
     */
    private final Integer typeId;

    /**
     * 类型说明
     */
    private final String typeStr;

    /**
     * 限制的组织类型
     */
    private final List<Integer> orgTypeChild;

    /**
     * 每次扣加分 分数
     */
    private final ScoreProperties scoreProperties;

    /**
     * 0:加分 1:减分
     */
    private final Integer sendType;

    /**
     * dataType 与t_sas_score_manager_flow 表 data_type 对应
     * 1:组织id 2:人员id 3:党组id
     */
    private final Integer dataType;

    /**
     * 数据处理类型 1:自身定时任务接口来处理该数据 2:只接存储到积分数据库中
     */
    private final Integer handleType;

    /**
     * 队列名称
     */
    private final String queueName;

    /**
     * 发送积分时记录的积分类型
     */
    private final Integer scoreType;

    ScoreManagerEnum(Integer typeId, String typeStr, List<Integer> orgTypeChild,
                     ScoreProperties scoreProperties, Integer sendType, Integer dataType, Integer handleType,
                     String queueName, Integer scoreType) {
        this.typeId = typeId;
        this.typeStr = typeStr;
        this.orgTypeChild = orgTypeChild;
        this.scoreProperties = scoreProperties;
        this.sendType = sendType;
        this.dataType = dataType;
        this.handleType = handleType;
        this.queueName = queueName;
        this.scoreType = scoreType;
    }

    private volatile static Map<Integer, ScoreManagerEnum> typeEnumMap;

    public static ScoreManagerEnum findByTypeId(Integer typeId) {
        if (null == typeEnumMap) {
            synchronized (ScoreManagerEnum.class) {
                if (null == typeEnumMap) {
                    ScoreManagerEnum[] values = ScoreManagerEnum.values();
                    typeEnumMap = new HashMap<>(values.length);
                    for (ScoreManagerEnum item : ScoreManagerEnum.values()) {
                        typeEnumMap.put(item.getTypeId(), item);
                    }
                }
            }
        }
        return typeEnumMap.get(typeId);
    }

    public interface Constant {
        /**
         * 党委党总支类型
         */
        List<Integer> COMMUNIST_GENERAL_TYPE_CHILD =
                Arrays.asList(10280301, 10280310, 10280322, 10280303, 10280308, 10280311, 10280318);

        String COMMUNIST_GENERAL_TYPE_CHILD_STR = COMMUNIST_GENERAL_TYPE_CHILD.stream().map(Object::toString).collect(Collectors.joining(","));

        /**
         * 党支部组织类型
         */
        List<Integer> BRANCH_CHILD_TYPE_CHILD =
                Arrays.asList(10280304, 10280309, 10280314, 10280315, 10280319);

        String BRANCH_CHILD_TYPE_CHILD_STR = BRANCH_CHILD_TYPE_CHILD.stream().map(Object::toString).collect(Collectors.joining(","));

        /* 业务使用 固定时间 */
        String FINAL_TIME = "1970-01";

    }

//    public interface Queue {
//        String BASE_ORG_PERIOD_EXPIRE_NOT_CREATE = "BASE_ORG_PERIOD_EXPIRE_NOT_CREATE";
//        String BASE_ORG_PERIOD_EXIST_LEADER = "BASE_ORG_PERIOD_EXIST_LEADER";
//        String BASE_ORG_PEOPLE_PUNISH = "BASE_ORG_PEOPLE_PUNISH";
//        String BASE_ORG_PEOPLE_LOGIN_RATIO = "BASE_ORG_PEOPLE_LOGIN_RATIO";
//        String BASE_ORG_PEOPLE_NEWS_READ = "BASE_ORG_PEOPLE_NEWS_READ";
//        String PARTY_ORG_EXIST_PERIOD_LEADER = "PARTY_ORG_EXIST_PERIOD_LEADER";
//        String PARTY_ORG_PERIOD_EXPIRE_NOT_CREATE = "PARTY_ORG_PERIOD_EXPIRE_NOT_CREATE";
//        String PARTY_ORG_PEOPLE_PUNISH = "PARTY_ORG_PEOPLE_PUNISH";
//        String PARTY_GROUP_EXIST_LEADER_GROUP = "PARTY_GROUP_EXIST_LEADER_GROUP";
//        String PARTY_GROUP_EXIST_PUNISH = "PARTY_GROUP_EXIST_PUNISH";
//        String PARTY_GROUP_EXIST_WORK_CONTACT = "PARTY_GROUP_EXIST_WORK_CONTACT";
//        String PARTY_GROUP_EXIST_BASE_CONTACT = "PARTY_GROUP_EXIST_BASE_CONTACT";
//    }

    @Getter
    public static class ScoreProperties {

        /**
         * 1:单个数值加减  2:阶梯加减
         */
        private final Integer type;

        /**
         * type=1 固定扣分加分 的分值
         */
        private Long score;

        /**
         * 阶梯条件分值
         *
         * @param type=2 时存在
         */
        private List<Ladder> ladderScore;

        public ScoreProperties(Integer type, Long score) {
            this.type = type;
            this.score = score;
        }

        public ScoreProperties(Integer type, List<Ladder> ladderScore) {
            this.type = type;
            this.ladderScore = ladderScore;
        }
    }

    @Getter
    public static class Ladder {
        /**
         * 比例起始
         */
        private final double startRatio;

        /**
         * 比例结束
         */
        private final double endRatio;

        /**
         * 分值
         */
        private final Long score;

        public Ladder(double startRatio, double endRatio, Long score) {
            this.startRatio = startRatio;
            this.endRatio = endRatio;
            this.score = score;
        }
    }
}
