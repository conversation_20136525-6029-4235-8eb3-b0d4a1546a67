package com.goodsogood.ows.common;

/**
 * 积分中心自动打分SQL模板
 *
 * <AUTHOR> tc
 * @date 2020/11/24
 */
public class CreditAutonmaSqlTemplate {

    /**
     * 组织当月换书订单率，换书人数/总人数 向下取整保留整数位
     */
    public static final String orgOrderRate = "select '${dateMonth}' dateMonth,${orgId} orgId,buyCount joinTotal from (select count(1) buyCount from (select buyer_id from t_score_order where status=1" +
            " and DATE_FORMAT(deal_date,'%Y-%m') = '${dateMonth}' and buyer_id in (${userIds}) GROUP BY buyer_id) t) tmp";

    /**
     * 组织当月扶贫消费订单率，扶贫人数/总人数  向下取整保留整数位
     */
    public static final String orgPovertyOrderRate = "select '${dateMonth}' dateMonth,${orgId} orgId,buyCount joinTotal" +
            " from (select count(1) buyCount from (select user_id from t_score_poverty_order where oid=3 and " +
            " DATE_FORMAT(trading_time,'%Y-%m') = '${dateMonth}' and user_id in (${userIds}) GROUP BY user_id) t) tmp";

    /**
     * 党员当月换书订单数
     */
    public static final String orderCount = "select buyer_id user_id,count(1) cn" +
            " from t_score_order where status=1 and DATE_FORMAT(deal_date,'%Y-%m')" +
            " ='${dateMonth}' and buyer_id in (${userIds}) group by buyer_id";

    /**
     * 党员当月消费扶贫订单数
     */
    public static final String povertyOrderCount = "select user_id,count(1) cn" +
            " from t_score_poverty_order where oid=3 and DATE_FORMAT(trading_time,'%Y-%m')" +
            " ='${dateMonth}' and user_id in (${userIds}) group by user_id";
}
