package com.goodsogood.ows.common.redisUtil;

/**
 * 通用常量配置
 * <AUTHOR>
public class Config {

    /**
     *  锁的过期时间(ms)
     */
    public static final int USER_CENTER_LOCK_EXPIRE = 23 * 60 * 60 * 1000;

    /**
     * 如果锁被占有，则间隔该时间再次重新去获取锁(ms)
     */
    public static final int TRY_GET_LOCK_INTERVAL = 100;

    /**
     * 用户中心锁
     */
    public static final String USER_CENTER_LOCK_PREFIX = "USER_CENTER_LOCK_PREFIX_";
}
