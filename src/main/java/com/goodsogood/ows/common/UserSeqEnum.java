package com.goodsogood.ows.common;

/**
 * 用户序列信息
 * sequence = 1 -> 卷烟营销党员，2 -> 烟叶生产党员，3 -> 专卖管理党员，4 -> 综合管理党员
 */
public enum UserSeqEnum {

    UserSeqEnum_1(1,"卷烟营销线均值","卷烟营销党员"),
    UserSeqEnum_2(2,"烟叶生产线均值","烟叶生产党员"),
    UserSeqEnum_3(3,"专卖管理线均值","专卖管理党员"),
    UserSeqEnum_4(4,"综合管理线均值","综合管理党员");

    UserSeqEnum(Integer _seqNumber,String _seqAvgName, String _seqName) {
        seqNumber = _seqNumber;
        seqName = _seqName;
        seqAvgName = _seqAvgName;
    };

    private Integer seqNumber;
    private String  seqName;

    private String seqAvgName;



    public String getSeqAvgName() {
        return seqAvgName;
    }

    public void setSeqAvgName(String seqAvgName) {
        this.seqAvgName = seqAvgName;
    }


    public Integer getSeqNumber() {
        return seqNumber;
    }

    public void setSeqNumber(Integer seqNumber) {
        this.seqNumber = seqNumber;
    }

    public String getSeqName() {
        return seqName;
    }

    public void setSeqName(String seqName) {
        this.seqName = seqName;
    }

    public static UserSeqEnum getSeqInfo(int seqNumber) {
        switch (seqNumber) {
            case 1:
                return UserSeqEnum.UserSeqEnum_1;
            case 2:
                return UserSeqEnum.UserSeqEnum_2;
            case 3:
                return UserSeqEnum.UserSeqEnum_3;
            case 4:
                return UserSeqEnum.UserSeqEnum_4;
            default:
                return null;
        }
    }
}

