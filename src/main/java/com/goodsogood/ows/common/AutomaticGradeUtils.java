package com.goodsogood.ows.common;

import com.goodsogood.ows.model.vo.rank.Month;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;

import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自动打分辅助工具
 *
 * <AUTHOR> tc
 * @date 2020/11/24
 */
public class AutomaticGradeUtils {
    /**
     * 根据传入userId/orgId集合,初始化每月ScoreResultVo数据
     */
    public static ScoreResultVo initScoreResultVo( List<Long> ids){
        ScoreResultVo vo = new ScoreResultVo();
        Map<Long, EnumMap<Month, Double>> scoreResultMap = new HashMap<>(ids.size());
        ids.stream().forEach(id->{
            //每月分数
            EnumMap<Month, Double> m = new EnumMap<Month, Double>(Month.class);
            for(int i =1;i<= 12;i++){
                m.put(Month.getEnumKey(i),0.0);
            }
            scoreResultMap.put(id,m);
        });
        vo.setScoreResultMap(scoreResultMap);
        return vo;
    }

    /**
     * 累加集合里的得分
     * @param list
     * @return
     */
    public static ScoreResultVo sumScoreResultVo(List<ScoreResultVo> list){
        ScoreResultVo vo = new ScoreResultVo();
        for(int i=0;i<list.size();i++){
            ScoreResultVo tmp = list.get(i);
            if(i==0){
                //第一个直接赋值
                vo=tmp;
            }else{
                //其余的对应累加
                vo.getScoreResultMap().entrySet().stream().forEach(entry->{
                    entry.getValue().entrySet().stream().forEach(e->{
                        //累加计算
                        Double value = e.getValue()+tmp.getScoreResultMap().get(entry.getKey()).get(e.getKey());
                        e.setValue(value);
                    });
                });
            }
        }
        return vo;
    }
}
