package com.goodsogood.ows.common;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.google.common.base.Strings;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utils
 *
 * <AUTHOR>
 * @date 2018-04-09
 */
@Log4j2
public class Utils {

    private static final ObjectMapper OBJECTMAPPER = new ObjectMapper();

    /**
     * 获取四位随机数
     *
     * @return
     */
    public static String getSendCode() throws NoSuchAlgorithmException {
        Random random = SecureRandom.getInstanceStrong();
        int code = random.nextInt(9999 - 1000 + 1) + 1000;
        return String.valueOf(code);
    }

    /**
     * 转换成json字符串
     *
     * @param obj
     * @return
     */
    public static String toJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return OBJECTMAPPER.writeValueAsString(obj);
        } catch (IOException e) {
            log.error(String.format("obj=[%s]", obj), e);
        }
        return null;
    }

    /**
     * 将json转化为对象
     *
     * @param json
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (json == null) {
            return null;
        }
        try {
            return OBJECTMAPPER.readValue(json, clazz);
        } catch (IOException e) {
            log.error(String.format("json=[%s]", json), e);
        }
        return null;
    }

    /**
     * 将json对象转化为集合类型
     *
     * @param json            json对象
     * @param collectionClazz 具体的集合类的class，如：ArrayList.class
     * @param clazz           集合内存放的对象的class
     * @return
     */
    public static <T> Collection<T> fromJson(String json, Class<? extends Collection> collectionClazz, Class<T> clazz) {
        if (json == null) {
            return null;
        }
        try {
            return OBJECTMAPPER.readValue(json, getCollectionType(collectionClazz, clazz));
        } catch (IOException e) {
            log.error(String.format("json=[%s]", json), e);
        }
        return null;
    }

    /**
     * 获取JavaType
     *
     * @param collectionClass 集合的class
     * @param elementClasses  元素的class
     * @return
     */
    private static JavaType getCollectionType(Class<?> collectionClass, Class<?>... elementClasses) {
        return OBJECTMAPPER.getTypeFactory().constructParametricType(collectionClass, elementClasses);
    }

    /**
     * 发送请求
     *
     * @param url
     * @param map
     * @return
     */
    public static String doPost(String url, Map<String, Object> map) {
        try {
            if (map == null) {
                map = new HashMap<>();
            }
            return sendPost(url, toJson(map));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 发送HttpPost请求
     *
     * @param strURL 服务地址
     * @param params json字符串,例如: "{ \"id\":\"12345\" }" ;其中属性名必须带双引号<br/>
     * @return 成功:返回json字符串<br/>
     */
    public static String sendPost(String strURL, String params) {
        try {
            URL url = new URL(strURL);// 创建连接
            HttpURLConnection connection = (HttpURLConnection) url
                    .openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestMethod("POST"); // 设置请求方式
            connection.setRequestProperty("Accept", "application/json"); // 设置接收数据的格式
            connection.setRequestProperty("Content-Type", "text/html;charset=UTF-8"); // 设置发送数据的格式
            connection.connect();
            OutputStreamWriter out = new OutputStreamWriter(
                    connection.getOutputStream(), StandardCharsets.UTF_8); // utf-8编码
            out.append(params);
            out.flush();
            out.close();
            // 读取响应
            BufferedReader in = new BufferedReader(
                    new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));// utf-8编码
            String line;
            String result = "";
            while ((line = in.readLine()) != null) {
                result += line;
            }
            out.close();
            in.close();
            return result;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "error";
    }

    /**
     * 向指定的URL发送请求
     *
     * @param url
     * @param paraMap 参数
     * @return
     * @throws Exception
     */
    public static String postParamsToUrl(String url, Map<String, String> paraMap) throws Exception {
        if (paraMap == null) {
            paraMap = new HashMap<>();
        }
        return HttpClientUtil.doPost(url, paraMap, new HashMap<>(), "UTF-8");
    }

    /**
     * 根据身份证计算年龄，截止当前年
     *
     * @param certNumber       身份证密文
     * @param certNumberSecret 身份证脱敏文
     * @return
     */
    public static int getAge(String certNumber, String certNumberSecret) {
        int certYear = 0;
        if (Strings.isNullOrEmpty(certNumber) || Strings.isNullOrEmpty(certNumberSecret)) {
            return certYear;
        }
        try {
            // 身份证解密
            String decryptNumber = NumEncryptUtils.decrypt(certNumber, certNumberSecret);
            int length = decryptNumber.length();
            if (length < 18 && length == 15) {
                certYear = Integer.parseInt("19".concat(decryptNumber.substring(6, 8)));
            } else if (length == 18) {
                certYear = Integer.parseInt(decryptNumber.substring(6, 10));
            } else {
                return certYear;
            }
        } catch (Exception e) {
            log.error("身份证解密失败：{}", e.getMessage(), e);
            return certYear;
        }
        return Calendar.getInstance().getWeekYear() - certYear;
    }

    /**
     * 根据身份证截取出生年月日
     *
     * @param certNumber       身份证密文
     * @param certNumberSecret 身份证脱敏文
     * @return
     */
    public static String getBirthday(String certNumber, String certNumberSecret) {
        String birthday = "";
        if (Strings.isNullOrEmpty(certNumber) || Strings.isNullOrEmpty(certNumberSecret)) {
            return birthday;
        }
        try {
            // 身份证解密
            String decryptNumber = NumEncryptUtils.decrypt(certNumber, certNumberSecret);
            int length = decryptNumber.length();
            if (length < 18 && length == 15) {
                birthday = "19".concat(decryptNumber.substring(6, 8))
                        .concat("-")
                        .concat(decryptNumber.substring(8, 10))
                        .concat("-")
                        .concat(decryptNumber.substring(10, 12));
            } else if (length == 18) {
                birthday = decryptNumber.substring(6, 10)
                        .concat("-")
                        .concat(decryptNumber.substring(10, 12))
                        .concat("-")
                        .concat(decryptNumber.substring(12, 14));
            }
        } catch (Exception e) {
            log.error("身份证解密失败：{}", e.getMessage(), e);
        }
        return birthday;
    }

    /**
     * 根据身份证计算性别
     *
     * @param certNumber 身份证明文
     * @return
     */
    public static int getGender(String certNumber) {
        if (Strings.isNullOrEmpty(certNumber) || (certNumber.length() != 18 && certNumber.length() != 15)) {
            return 3;
        }
        int gender = 3;
        if (certNumber.length() == 18) {
            //第17位，奇数为男，偶数为女
            int g = Integer.parseInt(certNumber.substring(16, 17)) % 2;
            gender = g == 0 ? 2 : 1;
        }
        if (certNumber.length() == 15) {
            //第15位，奇数为男，偶数为女
            int g = Integer.parseInt(certNumber.substring(14, 15)) % 2;
            gender = g == 0 ? 2 : 1;
        }
        return gender;
    }

    /**
     * 获取客户端IP地址
     *
     * @param request
     * @return
     */
    public static String getRemoteHost(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip.equals("0:0:0:0:0:0:0:1") ? "127.0.0.1" : ip;
    }

    /**
     * 校验证件号码格式
     *
     * @param type
     * @param number
     * @return
     */
    public static boolean checkCertNumber(Integer type, String number) {
        if (type == null || Strings.isNullOrEmpty(number)) {
            return false;
        }
        // 校验身份证
        if (Constants.CERT_NUMBER_IDENTITY == type) {
            Pattern regex = Pattern.compile("^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$|^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{2}[0-9Xx]$");
            Matcher m = regex.matcher(number);
            return m.matches();
        } else {
            return false;
        }
    }

    /**
     * 校验电话号码格式
     *
     * @param phone
     * @return
     */
    public static boolean checkPhone(String phone) {
        if (Strings.isNullOrEmpty(phone)) {
            return false;
        }
        // 校验电话号码格式
        Pattern regex = Pattern.compile("^1\\d{10}$");
        Matcher m = regex.matcher(phone);
        return m.matches();
    }


    /**
     * 返回一个定长的随机数字串(只包含数字)
     *
     * @param length 随机数字长度
     */
    public static String generateNumberChar(int length) throws NoSuchAlgorithmException {
        String numberChar = "23456789ABCDEFGHKLMNPQRSTUVWXYZ";
        StringBuffer sb = new StringBuffer(length);
        Random random = SecureRandom.getInstanceStrong();
        for (int i = 0; i < length; i++) {
            sb.append(numberChar.charAt(random.nextInt(numberChar.length())));
        }
        return sb.toString();
    }


    /**
     * 下載文件
     *
     * @param fileId 文件的id
     * @return 返回文件存放的是临时目录
     * @throws IOException
     */
    public static String downloadFile(String fileId, String serverFileCenter, RestTemplate restTemplate, HttpServletRequest request) throws IOException {
        ResponseEntity<byte[]> resultEntity = HttpUtils.downloadFile(
                restTemplate,
                String.format("http://%s/file/download/%s",
                        serverFileCenter,
                        fileId)
        );
        String path = getPath(request);
        File file = new File(path);
        if (!file.exists()) {
            file.mkdirs();
        }
        Files.write(Paths.get(path + fileId), resultEntity.getBody());
        return path + fileId;
    }

    /**
     * 下載文件
     *
     * @param fileId 文件的id
     * @return 返回文件存放的是临时目录
     * @throws IOException
     */
    public static String downloadFile(String fileId, String serverFileCenter, RestTemplate restTemplate, String path) throws IOException {
        ResponseEntity<byte[]> resultEntity = HttpUtils.downloadFile(
                restTemplate,
                String.format("http://%s/file/download/%s",
                        serverFileCenter,
                        fileId)
        );
        File file = new File(path);
        if (!file.exists()) {
            file.mkdirs();
        }
        Files.write(Paths.get(path + fileId), resultEntity.getBody());
        return path + fileId;
    }

    /**
     * Date to String
     *
     * @param date
     * @param format
     * @return
     */
    public static String dateToString(Date date, String format) {
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(date);
    }

    /**
     * 获取临时存放目录
     *
     * @return
     */
    private static String getPath(HttpServletRequest request) {
        return request.getSession().getServletContext().getRealPath("/") + "tempFile/";
    }

    /**
     * 校验昵称长度4-16个字符
     *
     * @param nickName
     * @return
     */
    public static int checkNickName(String nickName) {
        if (Strings.isNullOrEmpty(nickName)) {
            return 0;
        }
        String getChinese = "([\u4e00-\u9fa5]+)";
        String chinese = "";
        Matcher matcher1 = Pattern.compile(getChinese).matcher(nickName);
        while (matcher1.find()) {
            chinese += matcher1.group(0);
        }
        String getEnglish = "([A-Za-z]+)";
        String english = "";
        Matcher matcher2 = Pattern.compile(getEnglish).matcher(nickName);
        while (matcher2.find()) {
            english += matcher2.group(0);
        }

        String getNumber = "([0-9]+)";
        String number = "";

        Matcher matcher3 = Pattern.compile(getNumber).matcher(nickName);
        while (matcher3.find()) {
            number += matcher3.group(0);
        }
        return chinese.length() * 2 + english.length() * 1 + number.length() * 1;
    }

    /**
     * 获取随机数字字母
     *
     * @return
     */
    public static String getRandomCode(int length) throws NoSuchAlgorithmException {
        String val = "";
         Random random = SecureRandom.getInstanceStrong();
        //length为几位密码
        for (int i = 0; i < length; i++) {
            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            //输出字母还是数字
            if ("char".equalsIgnoreCase(charOrNum)) {
                //输出是大写字母还是小写字母
                int temp = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val += (char) (random.nextInt(26) + temp);
            } else if ("num".equalsIgnoreCase(charOrNum)) {
                val += String.valueOf(random.nextInt(10));
            }
        }
        return val.toUpperCase();
    }

    /**
     * 计算两个日期相差多少天
     *
     * @param fromDate
     * @param toDate
     * @return
     */
    public static int dayCompare(String fromDate, String toDate) {
        Date fd = stringToDate(fromDate);
        Date td = stringToDate(toDate);
        Calendar from = Calendar.getInstance();
        from.setTime(fd);
        Calendar to = Calendar.getInstance();
        to.setTime(td);
        return (int) ((to.getTimeInMillis() - from.getTimeInMillis()) / (24 * 3600 * 1000));
    }

    /**
     * String to Date
     *
     * @param dateStr
     * @return
     */
    public static Date stringToDate(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            log.error("字符串类型转换日期类型失败" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 从header获取用户所在组织(uoid为空取oid)
     *
     * @param header
     * @return
     */
    public static Long getUoid(HeaderHelper.SysHeader header) {
        Long uoid = header.getUoid();
        if (null == uoid) {
            return header.getOid();
        }
        return uoid;
    }

    /**
     * Date类型日期转String
     *
     * @param date
     * @return
     */
    public static String dateToString(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

    /**
     * 分段List
     *
     * @param list
     * @param pageSize
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> splitList(List<T> list, int pageSize) {
        List<List<T>> listArray = new ArrayList<>();
        List<T> subList = null;
        for (int i = 0; i < list.size(); i++) {
            if (i % pageSize == 0) {
                subList = new ArrayList<>();
                listArray.add(subList);
            }
            subList.add(list.get(i));
        }
        return listArray;
    }

    /**
     * 日期格式转换 yyyy-MM-dd 为 yyyy年MM月dd日
     *
     * @param dateStr
     * @return
     */
    public static String convertDate(String dateStr) {
        if (Strings.isNullOrEmpty(dateStr) || dateStr.length() < 10) {
            return "";
        }
        return dateStr.substring(0, 4).concat("年")
                .concat(dateStr.substring(5, 7)).concat("月")
                .concat(dateStr.substring(8, 10)).concat("日");
    }

}
