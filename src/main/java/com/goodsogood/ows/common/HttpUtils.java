package com.goodsogood.ows.common;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import lombok.extern.log4j.Log4j2;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.http.*;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.URI;
import java.util.Arrays;
import java.util.Map;

/**
 *
 * http发送工具
 *
 * <AUTHOR>
 * @create 2018-04-08 16:10
 **/
@Log4j2
public class HttpUtils {

     /**
      * GET
      * @param restTemplate   RestTemplate
      * @param headers   HttpHeaders
      * @param url  url 用string.format()进行格式化后的数据
      * @return
      */
     public static ResponseEntity<Result> doGet(RestTemplate restTemplate, HttpHeaders headers, String url){
          //构建头信息
          HttpEntity<String> entity = null;
          try {
               if(headers != null) {
                    HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);

                    headers = HeaderHelper.setMyHttpHeader(headers, sysHeader);

                    entity = new HttpEntity<>(null, headers);
               }
          } catch (NullPointerException e){
          } catch (Exception e){
               log.error("构建头信息出错", e);
          }
          try {
               ResponseEntity<Result> resultResponseEntity = restTemplate.exchange(
                       url, HttpMethod.GET, entity, Result.class);

               if (resultResponseEntity.getStatusCode() != HttpStatus.OK || (resultResponseEntity.getBody() != null && resultResponseEntity.getBody().getCode() != 0)) {
                    log.error("httputils error , error code : " + resultResponseEntity.getStatusCode());
               }
               return resultResponseEntity;
          } catch (Exception e) {
               log.error("httputils doGet error", e);
          }
          return null;
     }

     /**
      * 下载资源
      * @param restTemplate
      * @param url
      * @return
      */
     public static ResponseEntity<byte[]> downloadFile(RestTemplate restTemplate, String url){
          try {
               restTemplate.getMessageConverters().add(
                       new ByteArrayHttpMessageConverter());

               HttpHeaders _headers = new HttpHeaders();
               _headers.setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM));

               HttpEntity<String> entity = new HttpEntity<String>(_headers);

               ResponseEntity<byte[]> resultResponseEntity = restTemplate.exchange(
                       url, HttpMethod.GET, entity, byte[].class);

               return resultResponseEntity;
          } catch (Exception e) {
               log.error("httputils downloadFile error", e);
          }
          return null;
     }

     /**
      * doGet带参数
      *
      * @param url
      * @param param
      * @return
      */
     public static String doGet(String url, Map<String, String> param) {
          // 创建Httpclient对象
          CloseableHttpClient httpclient = HttpClients.createDefault();

          String resultString = "";
          CloseableHttpResponse response = null;
          try {
               // 创建uri
               URIBuilder builder = new URIBuilder(url);
               if (param != null) {
                    for (String key : param.keySet()) {
                         builder.addParameter(key, param.get(key));
                    }
               }
               URI uri = builder.build();

               // 创建http GET请求
               HttpGet httpGet = new HttpGet(uri);

               // 执行请求
               response = httpclient.execute(httpGet);
               // 判断返回状态是否为200
               if (response.getStatusLine().getStatusCode() == 200) {
                    resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
               }
          } catch (Exception e) {
               log.error("调用HttpUtils.doGet异常",e.getMessage(),e);
          } finally {
               try {
                    if (response != null) {
                         response.close();
                    }
                    httpclient.close();
               } catch (IOException e) {
                    log.error("调用HttpUtils.doGet异常",e.getMessage(),e);
               }
          }
          return resultString;
     }

}
