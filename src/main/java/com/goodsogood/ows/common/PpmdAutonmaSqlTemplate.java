package com.goodsogood.ows.common;

/**
 * 党费中心自动打分SQL模板
 *
 * <AUTHOR> tc
 * @date 2020/11/24
 */
public class PpmdAutonmaSqlTemplate {

    /**
     * 组织党费未交人数
     */
    public static String unfinishNum = "select '${dateMonth}' dateMonth,${orgId} orgId,sum(case when isPay=0 then 1 else 0 end) rt from ( " +
            " SELECT IF(tp2.payDate is null,0,1) isPay from ( " +
            " SELECT user_id userId FROM (  " +
            " select org_id,user_id,type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo, " +
            " @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b " +
            " where region_id=${regionId} and DATE_FORMAT(start_time,'%Y-%m') <= '${dateMonth}' and" +
            " IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') > '${dateMonth}' " +
            " ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4) and user_id in (" +
            " SELECT us.user_id FROM t_user_snapshot us WHERE us.date_month = '${dateMonth}' AND us.region_id = ${regionId} " +
            " AND ( us.position_code NOT IN(49,50,51,52,53) OR us.position_code IS NULL OR us.position_code = '' )" +
            " AND us.political_type IN( 1,5,17,18 ) AND us.status = 1)" +
            " and org_id in (${orgIds})) tp1 LEFT JOIN ( " +
            " SELECT user_id userId,pay_date payDate from t_ppmd_pay_log where region_id=${regionId} and" +
            " pay_for ='${dateMonth}' and pay_log_type =1) tp2 on tp1.userId=tp2.userId) tab";


    /**
     * 组织党费未设置数量
     */
    public static String unsetNum = "select '${dateMonth}' dateMonth,${orgId} orgId,count(1) rt from ( " +
            " select org_id,user_id,type,ratio_type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b" +
            " where region_id=${regionId} and DATE_FORMAT(start_time,'%Y-%m') <= '${dateMonth}'" +
            " and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') > '${dateMonth}' " +
            " ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type =4 and t1.ratio_type!=2 and t1.org_id in (${orgIds})" +
            " and user_id in (SELECT us.user_id FROM t_user_snapshot us WHERE us.date_month = '${dateMonth}' AND us.region_id = ${regionId} " +
            " AND ( us.position_code NOT IN(49,50,51,52,53) OR us.position_code IS NULL OR us.position_code = '' )" +
            " AND us.political_type IN( 1,5,17,18 ) AND us.status = 1)";


    /**
     * 组织党费交齐日期
     * 补交不算交齐，返回0表示未交齐
     */

    public static String finishDay ="select '${dateMonth}' dateMonth,${orgId} orgId,IF(unfinish > 0,0,maxPayDay) rt from (" +
            " select sum(case when isPay=0 then 1 else 0 end) unfinish,max(payDay) maxPayDay from (" +
            " SELECT tp1.orgId,tp1.userId,IF(tp2.payDate is null,0,1) isPay,DATE_FORMAT(payDate,'%d') payDay from (" +
            " SELECT org_id orgId,user_id userId FROM ( " +
            " select org_id,user_id,type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b" +
            " where region_id=${regionId} and DATE_FORMAT(start_time,'%Y-%m') <= '${dateMonth}' and" +
            " IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') > '${dateMonth}'" +
            " ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4) and org_id in (${orgIds})" +
            " and user_id in (SELECT us.user_id FROM t_user_snapshot us WHERE us.date_month = '${dateMonth}' AND us.region_id = ${regionId} " +
            " AND ( us.position_code NOT IN(49,50,51,52,53) OR us.position_code IS NULL OR us.position_code = '' )" +
            " AND us.political_type IN( 1,5,17,18 ) AND us.status = 1)" +
            " ) tp1 LEFT JOIN (SELECT user_id userId,pay_date payDate from t_ppmd_pay_log where region_id=${regionId} and" +
            " pay_for ='${dateMonth}' and pay_log_type =1) tp2 on tp1.userId=tp2.userId) retab) tab";


    /**
     * 党员当月交纳党费日期
     * 党员未交，返回空
     */
    public static String userPayDay ="SELECT user_id userId,DATE_FORMAT(pay_date,'%d') payDay from t_ppmd_pay_log where region_id=${regionId} and" +
            " pay_for ='${dateMonth}' and pay_log_type =1 and user_id in (${userIds})";
}
