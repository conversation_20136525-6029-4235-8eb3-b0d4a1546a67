package com.goodsogood.ows.common.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import org.joda.time.DateTime;

import java.util.HashSet;
import java.util.Set;

/**
 * <p>Description: 时间类型</p>
 *
 * <AUTHOR>
 * @version 2019/7/24 9:44
 */
@Getter
public class TimeBean {

    /**
     * 时间查询类型：1-按年
     */
    public final static int TIME_TYPE_YEAR = 1;
    /**
     * 时间查询类型：2-按半年
     */
    public final static int TIME_TYPE_HALF_YEAR = 2;

    /**
     * 上半年
     */
    public final static int TIME_FIRST_HALF_YEAR = 1;
    /**
     * 小半年
     */
    public final static int TIME_SECOND_HALF_YEAR = 2;


    /**
     * 时间查询类型：3-按季度
     */
    public final static int TIME_TYPE_QUARTER = 3;
    /**
     * 1 季度
     */
    public final static int TIME_FIRST_QUARTER = 1;
    /**
     * 2 季度
     */
    public final static int TIME_SECOND_QUARTER = 2;
    /**
     * 3 季度
     */
    public final static int TIME_THIRD_QUARTER = 3;
    /**
     * 4 季度
     */
    public final static int TIME_FOURTH_QUARTER = 4;


    /**
     * 时间查询类型：4-按月份
     */
    public final static int TIME_TYPE_MONTH = 4;
    /**
     * 1 月
     */
    public final static int TIME_JANUARY = 1;
    /**
     * 12 月
     */
    public final static int TIME_DECEMBER = 12;

    /**
     * 年
     */
    @JsonProperty(value = "year")
    private Integer year;


    /**
     * 时间查询类型：1-按年，2-按半年，3-按季度，4-按月份
     */
    @JsonProperty(value = "time_type")
    private Integer timeType;

    /**
     * 查询时间:
     * time_type=2时 ，代表上半年或下半年：1-上半年，2-下半年，
     * time_type=3时，代表季度 ：1-第一季度，2-第二季度，3-第三季度，4-第四季度
     * time_type=4时，代表月份 ： 1-12
     */
    @JsonProperty(value = "time")
    private Integer time;

    /**
     * 月份
     */
    @JsonProperty(value = "months")
    private Set<Integer> months = new HashSet<>();


    /**
     * 开始月份
     */
    @JsonProperty(value = "start_month")
    private Integer startMonth;

    /**
     * 结束月份
     */
    @JsonProperty(value = "end_month")
    private Integer endMonth;

    /**
     * @param year     年
     * @param timeType 时间查询类型：1-按年，2-按半年，3-按季度，4-按月份
     * @param time     查询时间:
     *                 time_type=2时 ，代表上半年或下半年：1-上半年，2-下半年，
     *                 time_type=3时，代表季度 ：1-第一季度，2-第二季度，3-第三季度，4-第四季度
     *                 time_type=4时，代表月份 ： 1-12
     */
    public TimeBean(Integer year, Integer timeType, Integer time) {
        this.init(year, timeType, time);
    }

    public Set<Integer> getMonths() {
        for (int i = this.startMonth; i <= this.endMonth; i++) {
            this.months.add(i);
        }
        return this.months;
    }
    /**
     * 初始化
     *
     * @param year     年
     * @param timeType 时间查询类型：1-按年，2-按半年，3-按季度，4-按月份
     * @param time     查询时间:
     *                 time_type=2时 ，代表上半年或下半年：1-上半年，2-下半年，
     *                 time_type=3时，代表季度 ：1-第一季度，2-第二季度，3-第三季度，4-第四季度
     *                 time_type=4时，代表月份 ： 1-12
     */
    private void init(Integer year, Integer timeType, Integer time) {
        DateTime now = DateTime.now();
        if (year == null) {
            year = now.getYear();
        }
        if (timeType == null) {
            timeType = TIME_TYPE_MONTH;
        }
        if (time == null) {
            time = now.getMonthOfYear();
        }
        if (time < TIME_JANUARY) {
            time = TIME_JANUARY;
        } else if (time > TIME_DECEMBER) {
            time = TIME_DECEMBER;
        }
        this.year = year;
        this.timeType = timeType;
        this.time = time;
        this.startMonth = time;
        this.endMonth = time;
        this.setStartMonthAndEndMonth();
    }


    /**
     * 初始化开始月和结束月
     */
    private void setStartMonthAndEndMonth() {
        // 按年
        if (this.timeType == TIME_TYPE_YEAR) {
            startMonth = 1;
            endMonth = 12;
        }
        // 按半年 time大于0，小于等于2
        else if (this.timeType == TIME_TYPE_HALF_YEAR) {
            if (this.time < TIME_FIRST_HALF_YEAR) {
                this.time = TIME_FIRST_HALF_YEAR;
            } else if (this.time > TIME_SECOND_HALF_YEAR) {
                this.time = TIME_SECOND_HALF_YEAR;
            }
            if (this.time == TIME_FIRST_HALF_YEAR) {
                startMonth = 1;
                endMonth = 6;
            } else {
                startMonth = 7;
                endMonth = 12;
            }
        }
        // 按季度 time大于0，小于等于4
        else if (this.timeType == TIME_TYPE_QUARTER) {
            if (this.time < TIME_FIRST_QUARTER) {
                this.time = TIME_FIRST_QUARTER;
            } else if (time > TIME_FOURTH_QUARTER) {
                this.time = TIME_FOURTH_QUARTER;
            }
            if (this.time == TIME_FIRST_QUARTER) {
                this.startMonth = 1;
                this.endMonth = 3;
            } else if (time == TIME_SECOND_QUARTER) {
                this.startMonth = 4;
                this.endMonth = 6;
            } else if (time == TIME_TYPE_QUARTER) {
                this.startMonth = 7;
                this.endMonth = 9;
            } else {
                this.startMonth = 10;
                this.endMonth = 12;
            }
        }
    }
}
