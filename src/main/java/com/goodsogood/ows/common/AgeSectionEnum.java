package com.goodsogood.ows.common;

/**
 * 年龄区间
 * <AUTHOR>
 *
 */

public enum AgeSectionEnum {

    AGE_1(new Integer[]{1, 30}),
    AGE_2(new Integer[]{31, 35}),
    AGE_3(new Integer[]{36, 40}),
    AGE_4(new Integer[]{41, 45}),
    AGE_5(new Integer[]{46, 50}),
    AGE_6(new Integer[]{51, 55}),
    AGE_7(new Integer[]{56, 60}),
    AGE_8(new Integer[]{61, 999});

    private final Integer[] section;

    AgeSectionEnum(Integer[] section) {
        this.section = section;
    }

    public Integer[] getSection() {
        return section;
    }
}
