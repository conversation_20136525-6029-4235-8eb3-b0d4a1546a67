package com.goodsogood.ows.common;

import lombok.extern.log4j.Log4j2;
import lombok.val;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.*;

/**
 * 外部排序工具类
 * 使用说明：
 * 第一步: 在使用该工具类时，请先调用creatMainFile()
 * 第二步: 引入ExternalSort，调用addData(File, value)添加value(转换为Long型)到文件中
 * 第三步: 调用mSort(mainFile, fileCount)/sort(mainFile, fileCount)进行排序, 返回排序好的文件
 * 第四步: 从排好的文件中获取数据可以调用getDataIndex、getDataSize....方法
 * 第五步: 调用deleteFile删除主要文件和排序好的文件
 * <AUTHOR>
 * @createTime 2022年06月23日 11:26:00
 */
@Component
@Log4j2
public class ExternalSort {
    /** 一次缓冲读取 */
    public static int BUFFER_SIZE = 1024 * 4 * 1000;
    /** 临时目录 */
    public static String path;
    /** 要排序的文件 */
    public static String MAIN_FILE = "mainset-";
    public static String MERGED_FILE = "multipleMerged-";
    /** 转存多路文件的开头名 */
    public static String TEMP_FILE_START_WITH = "set-";

    @Value("${dss.path}")
    public void setPath(String pathStr) {
        ExternalSort.path = pathStr;
    }

    /**
     * 创建主文件
     * @return
     * @throws IOException
     */
    public static File creatMainFile() throws IOException {
        final File file = new File(path + File.separator +  MAIN_FILE + UUID.randomUUID());
        if (!file.exists()) {
            file.createNewFile();
        }
        return file;
    }

    /**
     * 二路归并
     * @param file  文件
     */
    public static File sort(File file, int fileCount) throws IOException {
        ArrayList<File> files = split(file, fileCount);
        final File process = process(files);
        delFilesByPath(TEMP_FILE_START_WITH);
        return process;
    }

    /**
     * 多路归并
     * @param file  文件
     * @param fileCount 每条路的大小
     * @return  排好序的文件
     */
    public static File mSort(File file, int fileCount) throws IOException {
        ArrayList<File> files = split(file, fileCount);
        final File mergeFile = multipleMerge(files);
        delFilesByPath(TEMP_FILE_START_WITH);
        return mergeFile;
    }

    /**
     * 递归方法来合并列表，直到我们剩下一个
     * 单个合并列表
     */
    private static File process(ArrayList<File> list) throws IOException {
        if (list.size() == 1) {
            return list.get(0);
        }
        ArrayList<File> inter = new ArrayList<>();
        for (Iterator<File> itr = list.iterator(); itr.hasNext();) {
            File one = itr.next();
            if (itr.hasNext()) {
                File two = itr.next();
                inter.add(merge(one, two));
            } else {
                inter.add(one);
            }
        }
        return process(inter);
    }
    /**
     * 将原始文件拆分为多个子文件。
     */
    private static ArrayList<File> split(File file, int fileCount) throws IOException {
        ArrayList<File> files = new ArrayList<File>();
        long[] buffer = new long[fileCount];
        FileInputStream fr = new FileInputStream(file);
        BufferedInputStream bin = new BufferedInputStream(fr,BUFFER_SIZE);
        DataInputStream din=new DataInputStream(bin);
        boolean fileComplete = false;

        while (!fileComplete) {
            int index = buffer.length;
            for (int i = 0; i < buffer.length && !fileComplete; i++) {
                try {
                    buffer[i] = din.readLong();
                } catch (Exception e) {
                    fileComplete = true;
                    index = i;
                }
            }
            if (index != 0 && buffer[0] > -1) {
                Arrays.sort(buffer, 0, index);
                File f = new File(path + File.separator + TEMP_FILE_START_WITH + new Random().nextInt());
                //       File temp = File.createTempFile("josp", ".tmp", f);
                FileOutputStream writer = new FileOutputStream(f);
                BufferedOutputStream bOutputStream = new BufferedOutputStream(writer);

                DataOutputStream dout=new DataOutputStream(bOutputStream);
                for (int j = 0; j < index; j++) {
                    dout.writeLong(buffer[j]);
                }
                dout.close();
                bOutputStream.close();
                writer.close();
                files.add(f);

            }

        }
        din.close();
        bin.close();
        fr.close();
        return files;
    }
    /**
     * 多路归并
     * @param list  多路文件
     */
    private static File multipleMerge(ArrayList<File> list) throws IOException {
        int fileSize = list.size();
        if(fileSize == 1){
            return null;
        }
        ArrayList<DataInputStream> dinlist = new ArrayList<>();
        //比较数组
        long[] ext = new long[fileSize];
        final File file = new File(path + File.separator + MERGED_FILE + UUID.randomUUID());
        FileOutputStream os = new FileOutputStream(file);
        BufferedOutputStream bout = new BufferedOutputStream(os);
        DataOutputStream dout = new DataOutputStream(bout);
        for (int i = 0; i < fileSize; i++) {
            try {
                dinlist.add(i, new DataInputStream(new BufferedInputStream(
                        new FileInputStream(list.get(i)), BUFFER_SIZE)));
            } catch (Exception e) {
               throw e;
            }
        }
        int index = 0;
        for (int i = 0; i < fileSize; i++) {
            try {
                ext[i] = dinlist.get(i).readLong();
            } catch (Exception e) {
                ext[i] = -1;
            }
        }
        int count = fileSize;
        int[] sum = new int[fileSize];
        while (count > 1) {
            index = getMinIndex(ext);
            dout.writeLong(ext[index]);
            sum[index]++;
            try {
                ext[index] = dinlist.get(index).readLong();
            } catch (Exception e) {
                ext[index] = -1;
                count--;
                dinlist.get(index).close();
            }
        }
        int sIndex = getLastIndex(ext);
        dout.writeLong(ext[sIndex]);
        while (true) {
            try {
                dout.writeLong(dinlist.get(sIndex).readLong());
            } catch (Exception e) {
                dinlist.get(sIndex).close();
                break;
            }
        }
        dout.close();
        bout.close();
        os.close();
        return file;
    }

    /**
     * 找到剩下的最后一个文件输入流
     * @param ext  比较数组
     */
    public static int getLastIndex(long[] ext){
        int result = 0;
        for (int i = 0; i < ext.length; i++) {
            if(ext[i]!= -1){
                result = i;
                break;
            }
        }
        return result;
    }

    /**
     * 找到数据中最小的一个
     * @param ext   比较数组
     */
    public static int getMinIndex(long[] ext){
        long min = Long.MAX_VALUE;
        int index = -1;
        for (int i = 0; i < ext.length; i++) {
            if(ext[i] != -1 && ext[i] < min){
                min = ext[i];
                index = i;
            }
        }
        return index;
    }

    /**
     * 二路归并
     */
    private static File merge(File one, File two) throws IOException {
        FileInputStream fis1 = new FileInputStream(one);
        FileInputStream fis2 = new FileInputStream(two);
        BufferedInputStream bin1 = new BufferedInputStream(fis1,BUFFER_SIZE);
        BufferedInputStream bin2 = new BufferedInputStream(fis2,BUFFER_SIZE);

        DataInputStream din1=new DataInputStream(bin1);
        DataInputStream din2=new DataInputStream(bin2);

        File output = new File(path + File.separator + MERGED_FILE + UUID.randomUUID());
        FileOutputStream os = new FileOutputStream(output);
        BufferedOutputStream bout = new BufferedOutputStream(os);
        DataOutputStream dout=new DataOutputStream(bout);

        long a = din1.readLong();
        long b = din2.readLong();

        boolean finished = false;
        boolean emptyA = false;
        int flag = 0;
        while (!finished) {

            if (flag != 1) {
                try {
                    a = din1.readLong();
                } catch (Exception e) {
                    emptyA = true;
                    break;
                }
            }
            if (flag != 2) {
                try {
                    b = din2.readLong();
                } catch (Exception e) {
                    emptyA = false;
                    break;
                }
            }
            if(a > b){
                dout.writeLong(b);
                flag = 1;
            }else if( a < b){
                dout.writeLong(a);
                flag = 2;
            }else if(a == b){
                dout.writeLong(a);
                dout.writeLong(b);
                flag = 0;
            }
        }
        finished = false;
        if(emptyA){
            dout.writeLong(b);
            while(!finished){
                try {
                    b = din2.readLong();
                } catch (Exception e) {
                    break;
                }
                dout.writeLong(b);
            }
        }else{
            dout.writeLong(a);
            while(!finished){
                try {
                    a = din1.readLong();
                } catch (Exception e) {
                    break;
                }
                dout.writeLong(a);
            }
        }
        fis1.close();
        fis2.close();
        bin1.close();
        bin2.close();
        din1.close();
        din2.close();
        os.close();
        bout.close();
        dout.close();
        return output;
    }

    /**
      * 用以模糊删除头部为str的文件
      */
    public static void delFilesByPath(String str){
        File file = new File(path);
        File[] tempFile = file.listFiles();
        for(int i = 0; i < Objects.requireNonNull(tempFile).length; i++){
            if(tempFile[i].getName().startsWith(str)||tempFile[i].getName().endsWith(str)){
                deleteFile(path+tempFile[i].getName());
            }
        }
    }

    private static void deleteFile(String path){
        log.debug("删除文件" + path);
        File file=new File(path);
        if(file.isFile()){
            file.delete();
        }
    }

    public static void deleteFile(File file){
        log.debug("删除文件" + file.getPath());
        if(file.isFile()){
            file.delete();
        }
    }

    public static void addData(File file, long value) throws IOException {
        try {
            val fw = new FileOutputStream(file, true);
            val bout = new BufferedOutputStream(fw);
            val doubt = new DataOutputStream(bout);
            doubt.writeLong(value);
            // 保存文件成功后
            doubt.close();
            bout.close();
            fw.close();
        } catch (Exception e) {
            log.error("添加数据失败 value: ${}", value, e);
            throw e;
        }
    }

    /** 获取得分的排名 取重复排名的第一个下标 从1开始*/
    public static Long getDataIndex(File file, Long score) {
        long line = -1;
        try {
            FileInputStream fr = new FileInputStream(file);
            BufferedInputStream bin = new BufferedInputStream(fr, 1024 * 4 * 1000);
            DataInputStream din = new DataInputStream(bin);
            while (true) {
                try {
                    final long readLong = din.readLong();
                    line = line + 1;
                    if (readLong == score) {
                        break;
                    }
                } catch (Exception e) {
                    break;
                }
            }
            fr.close();
            bin.close();
            din.close();
        } catch (Exception e) {
            log.error("获取排名失败 line: {}", line, e);
        }
        return line;
    }

    /**
     * 值去重后的长度
     * @return
     */
    public static Long getDataSize(File file) {
        long line = 0;
        try {
            FileInputStream fr = new FileInputStream(file);
            BufferedInputStream bin = new BufferedInputStream(fr, 1024 * 4 * 1000);
            DataInputStream din = new DataInputStream(bin);
            long min = -1;
            while (true) {
                try {
                    final long readLong = din.readLong();
                    if (readLong != min) {
                        line = line + 1;
                        min = readLong;
                    }
                } catch (Exception e) {
                    break;
                }
            }
            fr.close();
            bin.close();
            din.close();
        } catch (Exception e) {
            log.error("获取排名失败 line: {}", line, e);
        }
        return line;
    }

    /** 获取得分的排名  取重复排名的最后一个下标 从1开始*/
    public static Long getDataLastIndex(File file, long score) {
        long line = 0;
        try {
            FileInputStream fr = new FileInputStream(file);
            BufferedInputStream bin = new BufferedInputStream(fr, 1024 * 4 * 1000);
            DataInputStream din = new DataInputStream(bin);
            boolean f = false;
            while (true) {
                try {
                    final long readLong = din.readLong();
                    if (readLong == score) {
                        f = true;
                    }
                    line = line + 1;
                    if (f && readLong != score) {
                        break;
                    }
                } catch (Exception e) {
                    break;
                }
            }
            fr.close();
            bin.close();
            din.close();
        } catch (Exception e) {
            log.error("获取排名失败 line: {}", line, e);
        }
        return line;

    }

    /** 获取得分的排名  取去重排名的的下标 从1开始*/
    public static Long getDeduplicateRankIndex(File file, long score) {
        long line = -1;
        try {
            FileInputStream fr = new FileInputStream(file);
            BufferedInputStream bin = new BufferedInputStream(fr, 1024 * 4 * 1000);
            DataInputStream din = new DataInputStream(bin);
            long min = -1;
            while (true) {
                try {
                    final long readLong = din.readLong();
                    if (readLong != min) {
                        line = line + 1;
                        min = readLong;
                    }
                    if (readLong == score) {
                        break;
                    }
                } catch (Exception e) {
                    break;
                }
            }
            fr.close();
            bin.close();
            din.close();
        } catch (Exception e) {
            log.error("获取排名失败 line: {}", line, e);
        }
        return line;

    }


    /**
     * @throws IOException
     */
    @Test
    public  void test() throws IOException {

        Random random = new Random(70000);
        FileOutputStream fw = new FileOutputStream(path + File.separator + MAIN_FILE);
        BufferedOutputStream bout = new BufferedOutputStream(fw);
        DataOutputStream dout=new DataOutputStream(bout);

        for (int i = 0; i < 70000; i++) {
            int ger = random.nextInt(70000);
            //ger = ger < 0 ? -ger : ger;
            dout.writeLong(ger);
        }
        dout.close();
        bout.close();
        fw.close();
        System.out.println("Original:");
        long start = System.currentTimeMillis();
        //mSort(MAIN_FILE, 100);
        long end = System.currentTimeMillis();
        System.out.println((end - start)/1000 + "s");
        //recordFile((end - start)/1000 ,true);
    }

    private void recordFile(long time,boolean isBuffer)
            throws FileNotFoundException, IOException {
        BufferedWriter bw = new BufferedWriter(new FileWriter("log",true));
        bw.write("FILE_COUNT = "+1000+";对"+ 3000 + "条数据 "+ 3000*4/(1024*1204) +"MB排序耗时:" + time + "s ");
        if(isBuffer){
            bw.write("  使用缓冲:"+BUFFER_SIZE*4/(1024*1204) +"MB");
        }
        bw.newLine();
        bw.close();
    }


}
