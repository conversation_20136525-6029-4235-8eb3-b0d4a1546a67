package com.goodsogood.ows.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/* 
 * 利用HttpClient进行post请求的工具类 
 */
public class HttpClientUtil {

    static Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);
	private HttpClientUtil() {
	}

	private static final Charset DEFAULT_CHARSET = Consts.UTF_8;

	private static final CloseableHttpClient httpClient;
	private static final ObjectMapper OBJECTMAPPER = new ObjectMapper();

	private static final int MAX_TOTAL = 200;
	private static final int MAX_PERROUTE = 1000;
	private static final int defaultSocketTimeout = 6000;
	private static final int defaultConnectTimeout = 3000;

	static {
		PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
		cm.setMaxTotal(MAX_TOTAL);
		cm.setDefaultMaxPerRoute(MAX_PERROUTE);
		httpClient = HttpClients.custom().setConnectionManager(cm).build();
		OBJECTMAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		OBJECTMAPPER.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
	}

	/**
	 * 发送POST请求
	 *
	 * @param url
	 * @param map
	 * @param headerMap
	 * @param charset
	 * @return
	 */
	public static String doPost(String url, Map<String, String> map, Map<String, String> headerMap, String charset) {
		HttpPost httpPost = null;
		String body = null;
		try {
			httpPost = new HttpPost(url);
			// 设置请求和传输超时时间
			RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(defaultSocketTimeout).setConnectTimeout(defaultConnectTimeout).build();
			httpPost.setConfig(requestConfig);
			
			// 设置参数
			List<NameValuePair> list = new ArrayList<NameValuePair>();
			Iterator iterator = map.entrySet().iterator();
			while (iterator.hasNext()) {
				Entry<String, String> elem = (Entry<String, String>) iterator.next();
				list.add(new BasicNameValuePair(elem.getKey(), elem.getValue()));
			}
			Iterator headerIt = headerMap.entrySet().iterator();
			while (headerIt.hasNext()) {
				Entry<String, String> elem = (Entry<String, String>) headerIt.next();
				httpPost.addHeader(elem.getKey(), elem.getValue());
			}
			if (list.size() > 0) {
				UrlEncodedFormEntity entity = new UrlEncodedFormEntity(list, charset);
				httpPost.setEntity(entity);
			}
			HttpResponse response = httpClient.execute(httpPost);
			if (response != null) {
				HttpEntity resEntity = response.getEntity();
				if (resEntity != null) {
					body = EntityUtils.toString(resEntity, charset);
				}
			}
			httpPost.completed();
		} catch (Exception ex) {
		    logger.error("doPost happen error ", ex);
		}finally {
		    logger.info("httpPost.reset start");
            httpPost.reset();
            logger.info("httpPost.result end");
		}
		return body;
	}

	/**
	 *
	 * @param url
	 * @param headerMap
	 * @param localContext
	 * @return
	 */
	public static String doGet(String url, Map<String, String> headerMap, HttpContext localContext) {
		return doGet(url, null, null, headerMap, localContext);
	}

	/**
	 *
	 * @param url
	 * @param charset
	 * @param requestConfig
	 * @param headerMap
	 * @param localContext
	 * @return
	 */
	public static String doGet(String url, Charset charset, RequestConfig requestConfig, Map<String, String> headerMap,
                               HttpContext localContext){
		String postString = null;
		CloseableHttpResponse response = null;
		HttpGet request = null;
		try {
			if (charset == null) {
				charset = DEFAULT_CHARSET;
			}

			if (requestConfig == null) {
				requestConfig = RequestConfig.custom().setSocketTimeout(defaultSocketTimeout).setConnectTimeout(defaultConnectTimeout).build();
			}

			request = new HttpGet(url);

			request.setConfig(requestConfig);

			if (null != headerMap) {
				Header[] headerArr = new BasicHeader[headerMap.size()];
				int counter = 0;
				for (Entry<String, String> entry : headerMap.entrySet()) {
					headerArr[counter++] = new BasicHeader(entry.getKey(), entry.getValue());
				}
				request.setHeaders(headerArr);
			}

			response = httpClient.execute(request, localContext);
			HttpEntity resEntity = response.getEntity();
			postString = (resEntity == null) ?
					null :
					EntityUtils.toString(resEntity, charset);

		}catch (Exception e){
			logger.error("doGet happen error ", e);
		}finally {
			if (response != null) {
				try {
					response.close();
				}catch (IOException ioe){
				}
			}
			if(request!=null){
				logger.info("httpGet.reset start");
				request.reset();
				logger.info("httpGet.result end");
			}
		}
		return postString;
	}

	/**
	 * 发送post请求
	 * @param url 请求路径
	 * @return
	 */
	public static String doPost(String url, Map<String, String> headerMap,Map<String, String> param){
		HttpPost httpPost = null;
		String result = null;
		try{
			HttpClient client =  HttpClients.createDefault();
			httpPost = new HttpPost(url);
			// 设置post超时时间
			RequestConfig.Builder requestConfig = RequestConfig.custom().setConnectTimeout(5000).setConnectionRequestTimeout(1000);
			httpPost.setConfig(requestConfig.build());
			Iterator headerIt = headerMap.entrySet().iterator();
			while (headerIt.hasNext()) {
				Entry<String, String> elem = (Entry<String, String>) headerIt.next();
				httpPost.addHeader(elem.getKey(), elem.getValue());
			}
			if(param != null){
				String body = OBJECTMAPPER.writeValueAsString(param);
				StringEntity se = new StringEntity(body,"utf-8");
				httpPost.setEntity(se); //post方法中，加入json数据
				httpPost.setHeader("Content-Type","application/json");
			}

			HttpResponse response = client.execute(httpPost);
			if(response != null){
				HttpEntity resEntity = response.getEntity();
				if(resEntity != null){
					result = EntityUtils.toString(resEntity,"utf-8");
				}
			}

		}catch(Exception ex){
			ex.printStackTrace();
		}
		logger.debug("返回结果:"+result);
		return result;
	}

}