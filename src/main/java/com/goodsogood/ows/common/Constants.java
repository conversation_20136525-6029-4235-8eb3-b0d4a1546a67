package com.goodsogood.ows.common;

/**
 * 常量
 *
 * @param
 * <AUTHOR>
 * @return
 * @date 2019/4/19 11:18
 */

public class Constants {

    // 性别
    public static final String GENDER_CODE = "1002";
    public static final String GENDER_NAME = "性别";
    // 户籍类型
    public static final String CENSUS_TYPE_CODE = "1011";
    public static final String CENSUS_TYPE_NAME = "户籍类型";
    // 文化程度
    public static final String EDUCATION_CODE = "1003";
    public static final String EDUCATION_NAME = "文化程度";
    // 民族
    public static final String ETHNIC_CODE = "1004";
    public static final String ETHNIC_NAME = "民族";
    // 政治面貌
    public static final String POLITICAL_CODE = "1013";
    public static final String POLITICAL_NAME = "政治面貌";
    // 中共党员
    public static final String COMMUNIST_CODE = "1022";
    public static final String COMMUNIST_NAME = "党组织";
    // 共青团
    public static final String YOUTH_LEAGUE_CODE = "1023";
    public static final String YOUTH_LEAGUE_NAME = "团组织";
    // 工会会员
    public static final String UNION_MEMBER_CODE = "1024";
    public static final String UNION_MEMBER_NAME = "工会组织";
    // 妇联
    public static final String WOMEN_LEAGUE_CODE = "1025";
    public static final String WOMEN_LEAGUE_NAME = "妇女组织";
    // 技术等级
    public static final String JOB_GRADE_CODE = "1009";
    public static final String JOB_GRADE_NAME = "技术等级";

    // 证件类型
    public static final String CERT_TYPE_CODE = "1010";
    public static final String CERT_TYPE_NAME = "证件类型";

    /**
     * 组织类型 党组织
     */
    public static final Long ORG_TYPE_COMMUNIST = 102803L;
    public static final Integer ORG_TYPE_COMMUNIST_INT = 102803;

    /**
     * 党费缴纳数据类型
     */
    public static final int PARTY_PAY_TYPE = 3;

    /**
     * 领导干部组织双重组织生活数据类型
     */
    public static final int LEADER_ORG_MEETING_TYPE = 2;

    /**
     * 组织生活类型
     */
    public static final int ORG_LIFE_TYPE = 1;

    /**
     * 有效状态
     */
    public static final Integer STATUS_YES = 1;

    /**
     * 无效状态
     */
    public static final Integer STATUS_NO = 0;

    /**
     * 包含
     */
    public static final Integer INCLUDE = 1;

    /**
     * 身份证code
     */
    public static final int CERT_NUMBER_IDENTITY = 1;

    /**
     * 重庆市级机关党组织ID
     */
    public static final Long ROOT_ORG_ID = 3L;
    public static final Long DEFAULT_ORG_ID = 1L;
    public static final Long EX_ORG_ID = -999L;

    /**
     * 实名认证 1-已认证 2-未认证
     */
    public static final int IS_VERIFY_YES = 1;
    public static final int IS_VERIFY_NO = 2;

    // 组织激活状态 1-已激活 2-未激活
    public static final int ACTIVATE_STATUS_YES = 1;
    public static final int ACTIVATE_STATUS_NO = 2;

    /**
     * 组织层级关系初始化
     */
    public static final String INIT_ORG_LEVEL = "-0-1-";

    /**
     * 时间查询类型,1-按年，2-按半年，3-按季度，4-按月份
     */
    public static final Integer SELECT_TIME_TYPE_YEAR = 1;
    public static final Integer SELECT_TIME_TYPE_HALF_YEAR = 2;
    public static final Integer SELECT_TIME_TYPE_QUARTER = 3;
    public static final Integer SELECT_TIME_TYPE_MONTH = 4;

    /**
     * 查询时间,1-上半年，2-下半年，3-第一季度，4-第二季度，5-第三季度，6-第四季度
     */
    public static final Integer SELECT_TIME_FIRST_HALF_YEAR = 1;
    public static final Integer SELECT_TIME_SECOND_HALF_YEAR = 2;
    public static final Integer SELECT_TIME_FIRST_QUARTER = 3;
    public static final Integer SELECT_TIME_SECOND_QUARTER = 4;
    public static final Integer SELECT_TIME_THIRD_QUARTER = 5;
    public static final Integer SELECT_TIME_FORTH_QUARTER = 6;

    /**
     * 党员组织生活情况缓存 PARTY_MEMBER_ORG_LIFE_{orgId}_{year}_{paramsHash}
     */
    public static final String PARTY_MEMBER_ORG_LIFE = "SAS_PARTY_MEMBER_ORG_LIFE_";

    /**
     * 每月底获取积极缴纳的组织和人员 保存时间1天
     */
    public static final String ACTIVE_PAYMENT_INFO = "REDIS_KEY_ACTIVE_PAYMENT_INFO";

    /**
     * 高级管理员
     */
    public static final int ROLE_TYPE_DEFINED_USER = 3;

    /**
     * 一般管理员
     */
    public static final int ROLE_TYPE_DEFINED_ROOT = 9;

    /**
     * 工委管理员
     */
    public static final int ROLE_TYPE_Work_Committee = 8;


    /**
     * 组织的所有下级组织缓存key
     */
    public static final String CACHE_ORG_CHILD_PREFIX = "CACHE_ORG_CHILD_";

    /**
     * 1号工作总结RedisKey - 高级
     */
    public static final String REDIS_KEY_PUSH_WORK_REPORT_SENIOR = "REDIS_KEY_PUSH_WORK_REPORT_SENIOR";

    /**
     * 1号工作总结RedisKey - 一般
     */
    public static final String REDIS_KEY_PUSH_WORK_REPORT_GENERA = "REDIS_KEY_PUSH_WORK_REPORT_GENERA";

    /**
     * 1号工作总结RedisKey - 党员
     */
    public static final String REDIS_KEY_PUSH_WORK_REPORT_ALL = "REDIS_KEY_PUSH_WORK_REPORT_ALL";

    /**
     * 1号工作报告给工委管理员看
     */
    public static final String REDIS_KEY_PUSH_WORK_REPORT_WORK_COMMIT = "REDIS_KEY_PUSH_WORK_REPORT_WORK_COMMIT";

    /**
     * 20号工作提醒RedisKey - 高级
     */
    public static final String REDIS_KEY_PUSH_WORK_REMIND_SENIOR = "REDIS_KEY_PUSH_WORK_REMIND_SENIOR";

    /**
     * 20号工作提醒RedisKey - 一般
     */
    public static final String REDIS_KEY_PUSH_WORK_REMIND_GENERA = "REDIS_KEY_PUSH_WORK_REMIND_GENERA";

    /**
     * 积极排行榜的缓存
     */
    public static final String REDIS_KEY_ACTIVE_PAY_FROM_ORG = "ACTIVE_PAY_FROM_ORG";


    /**
     * 用于时间拼接 获取每月最开始
     */
    public static final String START_MONTH_TIME = "-01 00:00:00";


    /**
     * 用于时间平拼接 获取每月默认最后一天
     */
    public static final String END_MONTH_TIME = "-31 23:59:59";
    /**
     * 电子党务报告-党费统计最先交纳人员和党支部缓存RedisKey  key_orgId_time
     */
    public static final String REDIS_KEY_REPORT_PPMD_ACTIVE_ORG_PAY = "ACTIVE_PAYMENT_ORG_";

    public static final String REDIS_KEY_REPORT_PPMD_ACTIVE_USER_PAY = "ACTIVE_PAYMENT_USER_";

    public static final String REDIS_KEY_REPORT_PPMD_ACTIVE_PARTY_PAY = "ACTIVE_PAYMENT_PARTY_";

    /**
     * 党组织层级信息缓存KEY前缀
     */
    public static final String ORG_LEVEL_INFO_KEY = "PPMD_ORG_LEVEL_INFO_KEY_";

    /**
     * 党组织层级信息缓存KEY,过期时间为32天
     */
    public static final int ORG_LEVEL_INFO_KEY_TIMEOUT_DAY = 32;

    /**
     * 党费交纳统计机关单位维度KEY前缀
     */
    public static final String STATISTICS_REDIS_OFFICE_KEY = "PPMD_STATISTICS_REDIS_OFFICE_KEY_";

    /**
     * 党费交纳统计缓存KEY, 过期时间为30天
     */
    public static final int STATISTICS_REDIS_KEY_TIMEOUT_DAY = 70;

    /**
     * 党费交纳统计党支部维度KEY前缀
     */
    public static final String STATISTICS_REDIS_ORG_KEY = "PPMD_STATISTICS_REDIS_ORG_KEY_";

    public static final Integer EVAL_TAG_TYPE = 6;

    public static final Integer MANAGER_TAG_TYPE = 7;


    /**
     * 本月工作提醒（工委管理员）
     */
    public static final String REDIS_KEY_PUSH_WORK_REMIND_WORK_COMMIT = "REDIS_KEY_PUSH_WORK_REMIND_WORK_COMMIT";

    /**
     * 监督预警缓存key EVAL_SUPERVISE_EARLY_WARNING_{org_id}_{year}_{quarter}
     */
    public static final String EVAL_SUPERVISE_EARLY_WARNING = "EVAL_SUPERVISE_EARLY_WARNING_";

    /**
     * 用户上月积分获取和消耗数量统计的KEY
     */
    public static final String USER_SCORE_INFO_KEY = "SCORE_USER_SCORE_INFO_KEY_";

    /**
     * 组织渠道缓存
     */
    public static final String ORG_APP_CACHE = "SCORE_ORG_APP_CACHE_";

    public static final String REGION_LIST_KEY = "REGION_LIST";
    public static final String REGION_LOCK_KEY = "REGION_LOCK";

    /**
     * 前端路由表缓存Key
     */
    public static final String MENU_ROUTE_KEY = "MENU_ROUTE_CACHE";

    /**
     * 人员的登录模块缓存Key
     */
    public static final String DSS_USER_INFO_KEY = "DSS_USER_INFO_";

    /**
     * 用户自动打分缓存key
     */
    public static final String USER_SCORE_KEY = "USER_SCORE_";

    /**
     * 组织自动打分缓存key
     */
    public static final String ORG_SCORE_KEY = "ORG_SCORE_";

    /**
     * 辅助决策文件结尾
     */
    public static final String DSS_FILE_ENDS_NAME = ".ows";

    /**
     * 是否包含下级组织 是
     */
    public static final Integer HAS_CHILD_YES = 1;

    /**
     * 是否包含下级组织 否
     */
    public static final Integer HAS_CHILD_NO = 2;

    /**
     * 组织生活相关判断是否完成  已完成
     */
    public static final Integer MEETING_TASK_DONE_YES = 1;

    /**
     * 组织生活相关判断是否完成 未完成
     */
    public static final Integer MEETING_TASK_DONE_NO = 2;

    /**
     * Tbc 组织生活缓存key
     */
    public static final String TBC_MEETING_LIFE_ = "TBC_MEETING_LIFE_";

    /**
     * Tbc 党组织情况缓存key
     */
    public static final String TBC_PARTY_ORG_ = "TBC_PARTY_ORG_";

    /**
     * Tbc 党员情况缓存key
     */
    public static final String TBC_PARTY_USER_ = "TBC_PARTY_USER_";

    /**
     * Tbc 党员人员详情缓存key
     */
//    public static final String TBC_PARTY_Details_ = "TBC_PARTY_NATION_";

    /**
     * Tbc 烟草大屏党费缓存
     */
    public static final String TBC_DUES_FORM_ = "TBC_DUES_FORM_";

    /**
     * TBC 缓存时间  分钟
     */
    public static final Integer TBC_CACHE_TIME = 60;

    /**
     * 3:通过终审
     */
    public static final int CHECK_THREE = 3;

    /**
     * 党支部数
     */
    public static final String BASICS = "TBC-FUSION-BASICS-%s";

    /**
     * 党支部基础信息统计
     */
    public static final String ORG_BASICS_COUNT = "TBC-FUSION-ORG-BASICS-COUNT";

    /**
     * 支部堡垒指数和党员先锋指数
     */
    public static final String TBC_FORTRESS = "TBC-FUSION-TBC-FORTRESS-%s";

    /**
     * 支部堡垒指数和党员先锋指数前三统计
     */
    public static final String PARTY_FORTRESS = "TBC-FUSION-PARTY-FORTRESS-%s";

    /**
     * 支部工作情况(散点图)
     */
    public static final String ORG_SPLASHES = "TBC-FUSION-ORG-SPLASHES";

    /**
     * 党员”党建+业务”工作情况(柱状图)
     * 所属序列：卷烟销售、烟叶生产、专卖管理、综合管理
     * 学历：高中及以下、专科、本科、本科以上
     * 年龄：30岁以下、30岁～45岁、45岁～55岁、55岁以上
     */
    public static final String ORG_PARTY_MEMBER = "TBC-FUSION-ORG-PARTY-MEMBER-%s";

    /**
     * 党员先锋指数(饼状图)
     */
    public static final String ORG_PIONEER_INDEX = "TBC-FUSION-ORG-PIONEER-INDEX";

    /**
     * 创新情况统计(词云)
     */
    public static final String TBC_INNOVATE = "TBC-FUSION-TBC-INNOVATE";

    /**
     * 词云统计标题
     */
    public static final String ORG_PIONEER_TITLE = "TBC-FUSION-ORG-PIONEER-TITLE";

    /**
     * 党页拟合度(条形图)
     * %s: sort_type
     * %s: type
     */
    public static final String TBC_MATCHING = "TBC-FUSION-TBC-MATCHING";

    /**
     * 党页地图
     * %s : type
     */
    public static final String TBC_MAP = "TBC-FUSION-TBC_MAP-%s";


    /**
     * 来源
     */
    public static final String SOURCE = "ows-sas";


    /**
     * 发给组织的消息
     */
    public static final Integer SEND_MSG_ORG = 1;


    /**
     * 发给用户的消息
     */
    public static final Integer SEND_MSG_USER = 2;

    /**
     * 监督预警 一键督办 缓存
     * %s  region_id
     * %s  oid
     */
    public static final String ONE_KEY_SUPERVISE = "ONE_KEY_SUPERVISE:%s:%s";

    /**
     * 支部堡垒指数和党员先锋指数前十统计
     */
    public static final String PARTY_FORTRESS_TOP_TEN = "TBC-FUSION-PARTY-FORTRESS_TOP_TEN-%s";

    /**
     * 渠道类型  2:微信 3:主题推送 4:钉钉
     */
    public static Byte DING_DING_TYPE = 4;

    /**
     * 7-本月登录次数类型 8-本月最晚工作时间类型 1-完成
     */
    public static Byte PARAM_TYPE_SEVEN = 7;
    public static Byte PARAM_TYPE_EIGHT = 8;
    public static Byte CAL_STATUS_DONE = 1;

    /**
     * 登录前缀
     */
    public static final String LOGIN_TOKEN = "LT";

    public static final Integer IS_EMPLOYEE = 1;

    /**
     * 考核周期 1-周
     */
    public static final Integer CYCLE_WEEK = 1;

    /**
     * 单位拟合度列表 1-拟合度 2-党建工作 3-业务工作
     */
    public static final Integer TYPE_K = 1;
    public static final Integer TYPE_P = 2;
    public static final Integer TYPE_B = 3;

    /**
     * 登录排序的ZSet KEY
     */
    public static final String REDIS_KEY_LOGIN_SORT_CACHE = "LOGIN_SORT_HASH";

    // 单位经纬度信息的缓存 （目前大屏在使用）
    public static final String DATAV_UNIT_LNG_LAT_CACHE = "DV_U_LL";

    // 考核2.0的任务信息缓存key
    public static final String METRIC_TASK_CACHE_KEY = "METRIC_TASK_CACHE_KEY_";

    // 考核2.0的任务状态的缓存key
    public static final String METRIC_TASK_STATUS_CACHE_KEY = "METRIC_TASK_STATUS_CACHE_KEY_";

    // 考核2.0的轻流考核指标获取缓存key
    public static final String METRIC_TASK_QL_CACHE_KEY = "METRIC_TASK_QL_CACHE_KEY_";
}
