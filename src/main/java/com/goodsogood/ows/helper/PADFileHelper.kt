package com.goodsogood.ows.helper

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.*
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.goodsogood.ows.helper.entity.*
import org.apache.commons.io.FileUtils
import org.apache.logging.log4j.LogManager
import org.joda.time.DateTime
import java.io.File
import java.nio.charset.Charset
import java.security.MessageDigest
import java.util.*
import java.util.stream.Collectors
import kotlin.math.abs
import kotlin.text.Charsets.UTF_8


/**
 * <AUTHOR>
 * @date 2020/11/4
 * @param path 需要生成到文件的绝对路径（不包含最后一个/）
 * @param digit 校验位
 * @description class PADFileHelper
 */
class PADFileHelper(// 生成文件的 root path
    private val path: String, private val digit: String, private val version: String = "v1.0.0"
) {
    private val log = LogManager.getLogger(PADFileHelper::class.java)

    private val root = "${File.separator}ows" // 根路径
    private val datas = "${File.separator}datas" // 根路径

    private val mapper = ObjectMapper().registerKotlinModule().apply {
        // 自动驼峰转换
        this.propertyNamingStrategy = PropertyNamingStrategies.SNAKE_CASE
        // Don't throw an exception when json has extra fields you are
        // not serializing on. This is useful when you want to use a pojo
        // for deserialization and only care about a portion of the json
        // Don't throw an exception when json has extra fields you are
        // not serializing on. This is useful when you want to use a pojo
        // for deserialization and only care about a portion of the json
        this.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        // Ignore null values when writing json.
        this.setSerializationInclusion(JsonInclude.Include.NON_NULL)
        // Write times as a String instead of a Long so its human readable.
        this.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
        this.registerModule(JavaTimeModule())
    }

    // map <year,File>
    private val years: MutableList<String> = mutableListOf()
    private val files: MutableMap<String, MutableList<File>> = mutableMapOf()

    // 用户数据 users.json
    private val userArray: MutableList<User> = mutableListOf()

    // 组织数据 orgs.json
    private val orgArray: MutableList<Org> = mutableListOf()

    // key.json
    private val key = Key()

    // files.json
    private val structures: MutableSet<Structure> = mutableSetOf()

    /**
     * md5 姓名
     * @param source 明文
     */
    private fun md5(source: String?): ByteArray = MessageDigest.getInstance("MD5").digest(source?.toByteArray(UTF_8))
    private fun ByteArray.toHex() = joinToString("") { "%02x".format(it) }

    var debug: Boolean = false
        private set

    /**
     * 设置开启调试（调试时不加密，不清理目录）
     * @param debug 默认为 false 非调试默认
     */
    fun setDebug(debug: Boolean = false): PADFileHelper {
        this.debug = debug
        return this
    }

    /**
     * 加密文件
     * @param source 需要加密的文件内容
     * @param hash 秘钥（key+file sdbm hash）
     */
    private fun decrypt(source: String, hash: String): String {
        if (debug) return source
        val has = HashAlgorithms.FNVHash1(hash + digit)
        // 目前使用的是AES 16位 因此需要生成16位key
        val so = abs(has).toString()
        var tm: StringBuilder = StringBuilder(so)
        if (so.length < 16) {
            for (i in 0 until 16 - so.length) {
                tm.append("0")
            }
        } else if (so.length > 16) {
            tm = StringBuilder(tm.substring(0, 16))
        }

        val password = "$tm"

        return AESUtils(password).encryptStr(source)
    }

    /**
     * 通过明文生成 SDBM hash
     */
    private fun hash(source: String): String {
        return abs(HashAlgorithms.SDBMHash(source)).toString()
    }

    /**
     * clear 目录
     */
    fun clear(): PADFileHelper {
        if (!debug) {
            FileUtils.deleteDirectory(File("$path$root"))
        }
        return this
    }

    /**
     * 检查文件生成情况
     */
    private fun check(): Boolean {
        val rootFile = File("$path$root")
        val allFiles = FileUtils.listFiles(rootFile, arrayOf("json"), true)
        var fileSize = 0
        val trueFiles = mutableListOf<File>()
        files.values.forEach {
            fileSize += it.size
            trueFiles.addAll(it)
        }
        // 去重
        val unique: List<File> = trueFiles.stream().distinct().collect(Collectors.toList())
        if (allFiles.size != unique.size + 4) {
            log.debug("check失败，物理文件个数[${allFiles.size}]和实际文件个数[${unique.size + 4}] 不相同")
            val newTransfer: List<File> = trueFiles.stream().filter { m ->
                !allFiles.stream().map { d -> d.absolutePath }.collect(Collectors.toList()).contains(m.absolutePath)
            }.collect(Collectors.toList())
            log.debug("check失败，相差文件 -> {}", newTransfer)
            return false
        }
        // 其他校验暂不实现了
        return true
    }

    /**
     * 编译数据
     *@param phone 导出人的电话号码（脱敏）
     * @param user 导出人的用户（脱敏）
     * @param clean 是否清理临时目录
     * @return 最后的文件路径
     */
    @JvmOverloads
    fun build(phone: String, user: String, clean: Boolean? = false): File {
        key.key = digit
        key.version = version
        key.time = DateTime.now().toString("yyyy-MM-dd HH:mm:ss", Locale.CHINESE)
        // users.json
        var users = mapper.writeValueAsString(userArray)
        key.usersHs = hash(users)
        users = decrypt(users, key.usersHs)
        if (!makeFile(type = 6, fileString = users)) throw PADFileException("users.json创建失败")
        // orgs.json
        var orgs = mapper.writeValueAsString(orgArray)
        key.orgsHs = hash(orgs)
        orgs = decrypt(orgs, key.orgsHs)
        if (!makeFile(type = 7, fileString = orgs)) throw PADFileException("orgs.json创建失败")
        // files.json
        var structurestr = mapper.writeValueAsString(structures)
        key.filesHs = hash(structurestr)
        structurestr = decrypt(structurestr, key.filesHs)
        if (!makeFile(type = 8, fileString = structurestr)) throw PADFileException("files.json创建失败")
        // key.json
        if (!makeFile(type = 9, fileString = mapper.writeValueAsString(key))) throw PADFileException("key.json创建失败")
        if (!check()) throw PADFileException("生成的打包文件校验失败")
        // 生成打包文件
        val sourceFolder = "$path$root"
        val targetFolder = "$path${File.separator}"
        val fileName = "$phone-$user-${
            DateTime.now().toString("yyyyMMdd", Locale.CHINESE)
        }-${abs(UUID.randomUUID().leastSignificantBits)}.ows"
        log.debug("生成打包文件->from:{} to {}", sourceFolder, "$targetFolder$fileName")
        ZipHelper.zipFolderEncrypt(sourceFolder, ZipHelper.createPassword(fileName), "$targetFolder$fileName")
        if (clean!!) {
            clear()
        }
        return File("$targetFolder$fileName")
    }

    /**
     * 添加用户
     * @param user 用户对象
     */
    fun addUser(vararg user: User): PADFileHelper {
        for (u in user) {
            val temp = u.copy(name = md5(u.name).toHex())
            userArray.add(temp)
        }
        return this
    }

    /**
     * 重新设置用户数组
     * @param
     */
    fun setUser(users: List<User>): PADFileHelper {
        userArray.clear()
        for (user in users) {
            val temp = user.copy(name = md5(user.name).toHex())
            userArray.add(temp)
        }
        return this
    }

    /**
     * 添加用户
     * @param org 组织对象
     */
    fun addOrg(vararg org: Org): PADFileHelper {
        orgArray.addAll(org)
        return this
    }

    /**
     * 重新设置用户数组
     * @param orgs 组织数组
     * @return this
     */
    fun setOrg(orgs: List<Org>): PADFileHelper {
        orgArray.clear()
        orgArray.addAll(orgs)
        return this
    }


    /**
     * 设置年份
     * @param year 年份
     * @return this
     */
    fun setYears(vararg year: String): PADFileHelper {
        years.clear()
        files.clear()
        for (y in year) {
            years.add(y)
            files[y] = mutableListOf()
        }
        return this
    }

    /**
     * 创建实际文件
     * @param type 1.工委，2.党委，3.支部，4.个人，5.地图，6.users.json，7.orgs.json，8.files.json ,9 key.json
     * @param fileString 文件string
     * @param id 各自的id，1和5 默认为0
     */
    private fun makeFile(type: Int, year: String = "", fileString: String, id: Long = 0): Boolean {
        val filePath = when (type) {
            1 -> {
                "$year${File.separator}org.json"
            }

            5 -> {
                "$year${File.separator}map.json"
            }

            2 -> { //党委
                "$year${File.separator}orgs${File.separator}ommittee_$id.json"
            }

            3 -> { // 支部
                "$year${File.separator}orgs${File.separator}branch_$id.json"
            }

            4 -> {// 用户
                "$year${File.separator}users${File.separator}user_$id.json"
            }

            6 -> "users.json"
            7 -> "orgs.json"
            8 -> "files.json"
            9 -> "key.json"
            else -> return false
        }
        // 生成文件
        val hash = if (type < 6) {
            hash(fileString)
        } else {
            ""
        }
        val source = if (type < 6) {
            decrypt(fileString, hash) // key.key = digit
        } else {
            fileString
        }
        val file = if (type < 6) {
            File("$path$root$datas${File.separator}$filePath")
        } else {
            File("$path$root${File.separator}$filePath")
        }
        FileUtils.forceMkdirParent(file)
        FileUtils.writeStringToFile(file, source, Charset.forName("UTF-8"))
        if (file.exists()) {
            if (type < 6) { // 6以上的类型不需要要加载files.json中
                val structure = Structure(type = type, ownerId = id, date = year, filePath = "/$filePath", hash)
                this.structures.add(structure)
                this.files[year]?.add(file)
            }
            return true
        }
        return false
    }

    /**
     * 添加map文件（第一屏）
     * @param year 年份
     * @param mapJson map.json
     * @return this
     */
    fun addMapFile(year: String, mapJson: String): PADFileHelper {
        if (!years.contains(year)) {
            years.add(year)
        }
        if (files[year] == null) {
            files[year] = mutableListOf()
        }
        if (!this.makeFile(5, year, mapJson)) {
            throw PADFileException("map文件创建失败->>$year\r\n$mapJson")
        }
        return this
    }

    /**
     * 添加工委报表文件
     * @param year 年份
     * @param orgJson org.json
     * @return this
     */
    fun addOrgFile(year: String, orgJson: String): PADFileHelper {
        if (!years.contains(year)) {
            years.add(year)
        }
        if (files[year] == null) {
            files[year] = mutableListOf()
        }
        if (!this.makeFile(1, year, orgJson)) {
            throw PADFileException("工委级报表创建失败->$year\r\n$orgJson")
        }
        return this
    }

    /**
     * 添加其他报表文件
     * @param  2.党委，3.支部，4.个人
     * @param year 年份
     * @param data 数据
     * @param id 组织id或者个人id
     * @return this
     */
    private fun addOtherFile(type: Int, year: String, data: String, id: Long): PADFileHelper {
        if (type < 2 || type > 4) {
            throw PADFileException("type只能是2、3、4")
        }
        if (!years.contains(year)) {
            years.add(year)
        }
        if (files[year] == null) {
            files[year] = mutableListOf()
        }
        if (!this.makeFile(type, year, data, id)) {
            throw PADFileException("党委、支部或者个人报表创建失败->$type-$year-$id\r\n$data")
        }
        return this
    }

    /**
     * 添加报表文件
     * @param  1.工委，2.党委，3.支部，4.个人，5.地图
     * @param year 年份
     * @param data 数据
     * @param id 组织id或者个人id（1，5的时候不用传）
     */
    @JvmOverloads
    fun addFile(type: Int, year: String, data: String, id: Long = 0): PADFileHelper {
        when (type) {
            1 -> {
                addOrgFile(year, data)
            }

            5 -> {
                addMapFile(year, data)
            }

            2, 3, 4 -> {
                addOtherFile(type, year, data, id)
            }

            else -> throw PADFileException("type只能是1、2、3、4")
        }
        return this
    }

    /**
     * 添加报表文件
     * @param jsonFile vararg JsonFile对象
     * @return this
     */
    fun addFiles(vararg jsonFile: JsonFile): PADFileHelper {
        for (file in jsonFile) {
            addFile(file.type, file.year, file.data, file.id ?: 0)
        }
        return this
    }

}

class PADFileException(message: String?) : Exception(message)