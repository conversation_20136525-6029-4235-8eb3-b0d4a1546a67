package com.goodsogood.ows.helper;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.pagehelper.Page;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-24 10:44
 */
public class PageBeanHelper {

    /**
     * 初始化分页信息
     *
     * @param pageNo   Integer 页码 默认1
     * @param pageSize Integer 每页行数 默认10
     *
     * @return PageBean
     */
    public static PageBean page(Integer pageNo, Integer pageSize) {
        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        PageBean page = new PageBean();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);

        return page;
    }

    /**
     * 空分页数据
     */
    public static <T> Page<T> emptyPage(PageBean pageBean) {
        if (pageBean == null) {
            pageBean = page(null, null);
        } else {
            pageBean = page(pageBean.getPageNo(), pageBean.getPageSize());
        }
        // 初始化page信息
        Page<T> page = new Page<>();
        page.setPageNum(pageBean.getPageNo());
        page.setPageSize(pageBean.getPageSize());
        page.setTotal(0);
        page.setPages(0);
        return page;
    }

    /**
     * 初始化分页信息
     *
     * @param data     数据
     * @param pageBean 分页信息
     *
     * @return Page<T>
     */
    public static <T> Page<T> pageData(List<T> data, PageBean pageBean) {
        if (data == null) {
            data = new ArrayList<>();
        }

        if (pageBean == null) {
            pageBean = page(null, null);
        }

        int pageNum = pageBean.getPageNo();
        int pageSize = pageBean.getPageSize();
        int size = data.size();

        // 初始化page信息
        Page<T> page = new Page<>();
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setTotal(size);
        page.setPages((int) Math.ceil((double) size / pageBean.getPageSize()));
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = pageNum * pageSize;
        if (fromIndex >= size) {
            return page;
        }
        if (toIndex > size) {
            toIndex = size;
        }
        page.addAll(data.subList(fromIndex, toIndex));
        return page;
    }

    /**
     * 分页实体
     *
     * <AUTHOR>
     * @create 2018-10-24 10:40
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
//    @Builder
    public static class PageBean {

        /**
         * 当前页码 默认第一页
         */
        @JsonProperty(value = "page_no")
        private Integer pageNo = 1;

        /**
         * 每页大小 默认10条
         */
        @JsonProperty(value = "page_size")
        private Integer pageSize = 10;
    }
}
