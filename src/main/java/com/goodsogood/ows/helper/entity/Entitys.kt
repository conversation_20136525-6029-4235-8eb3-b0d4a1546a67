package com.goodsogood.ows.helper.entity

data class User(
        val id: Long,
        val date: String,// 年月份
        val name: String?, // 姓名 （加密）
        val phone: String?,// 手机号码（脱敏）
        val branch: String?,//党支部
        val committee: String?, //党委
        val org: String?//单位、组织
)

data class Org(
        val id: Long,
        val date: String,// 年月份
        val name: String?,//名称
        val shortName: String?,//别名
        val type: Int?,//  2.党委（二级党委、党总支）、3.支部
        val typeName: String?
)

data class Structure(
        val type: Int,// 1.工委，2.党委，3.支部，4.个人，5.地图
        val ownerId: Long,// 各自的id
        val date: String,//年月
        val filePath: String,
        val hs: String //  明文校验位
)

data class Key(
        var key: String?,//校验位
        var version: String?,//当前数据包版本号
        var usersHs: String,// users.json的校验位
        var orgsHs: String,//orgs.json的校验位
        var filesHs: String,//files.json的校验位
        var time: String? // 生成时间 yyyy-MM-dd HH:mm:ss
) {
    constructor() : this(null, null, "", "", "", null)
}

data class JsonFile(
        var type: Int, // 1.工委，2.党委，3.支部，4.个人，5.地图
        var year: String, // 年份
        var data: String, // 数据
        var id: Long? = 0 // 组织id或者个人id（1，5的时候为0）
) {
    constructor() : this(-1, "", "", -1)

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as JsonFile

        if (type != other.type) return false
        if (year != other.year) return false
        if (id != other.id) return false

        return true
    }

    override fun hashCode(): Int {
        var result = type
        result = 31 * result + year.hashCode()
        result = 31 * result + (id?.hashCode() ?: 0)
        return result
    }

}