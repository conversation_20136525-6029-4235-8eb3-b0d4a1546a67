package com.goodsogood.ows.helper;

import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.configuration.AspectConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * <p>为定时任务生成tracker_id</p>
 *
 * <p>由于定时任务没有走网关，所以无法传递tracker_id，在定时任务出问题后，无法定位到问题</p>
 * <p>在定时任务里需要重置下上下文</p>
 *
 * <AUTHOR>
 * @date 2018-11-22 11:29
 * @since 1.0.3
 **/
@Component
public class LogHelper {

    /**
     * 日志配置
     */
    private final AspectConfiguration aspectConfiguration;

    @Autowired
    public LogHelper(AspectConfiguration aspectConfiguration) {
        this.aspectConfiguration = aspectConfiguration;
    }

    /**
     * 获取日志输出对象
     *
     * @param helper 日志切面帮助类
     * @return
     * @since 1.0.3
     */
    public LogAspectHelper.SSLog getSSlog(LogAspectHelper helper) {
        return helper.ssLogBulder(
                System.currentTimeMillis(),
                UUID.randomUUID().toString(),
                aspectConfiguration.monitorProps().getSpanId(),
                "定时任务pSpanId",
                aspectConfiguration.monitorProps().getNodeName(),
                "定时任务type",
                aspectConfiguration.monitorProps().getAppName(),
                aspectConfiguration.monitorProps().getPort() + "");
    }

    /**
     * 获取日志输出对象
     * @param helper    日志切面帮助类
     * @param modelName 定时任务名称
     * @return
     * @since 1.0.3
     */
    public LogAspectHelper.SSLog getSSlog(LogAspectHelper helper, String modelName) {
        return helper.ssLogBulder(
                System.currentTimeMillis(),
                UUID.randomUUID().toString(),
                aspectConfiguration.monitorProps().getSpanId(),
                modelName + " pSpanId",
                aspectConfiguration.monitorProps().getNodeName(),
                modelName + " type",
                aspectConfiguration.monitorProps().getAppName(),
                aspectConfiguration.monitorProps().getPort()+"");
    }

    /**
     * 重新设置下上下文
     *
     * @param ssLog LogAspectHelper.SSLog 日志信息类
     * @since 1.0.4
     */
    public LogAspectHelper.SSLog reSetContext(LogAspectHelper.SSLog ssLog) {
        //重新设置下上下文
        LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
        if (null == ssLog) {
            ssLog = this.getSSlog(helper);
        }
        helper.reSetContext(ssLog);
        return ssLog;
    }
}
