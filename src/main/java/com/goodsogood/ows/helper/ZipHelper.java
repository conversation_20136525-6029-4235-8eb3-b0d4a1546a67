package com.goodsogood.ows.helper;

import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.AesKeyStrength;
import net.lingala.zip4j.model.enums.EncryptionMethod;

import java.io.File;
import java.nio.file.FileAlreadyExistsException;

/**
 * <AUTHOR>
 * @date 2020/10/29
 * @description class ZipHelper
 */
public class ZipHelper {
    /**
     * zip目录。并加密
     *
     * @param folderPath 需要zip的目录
     * @param password   密码
     * @param target     目标文件(注意！会删除原始目录)
     * @return 是否生成文件
     * @throws ZipException
     */
    public static boolean zipFolderEncrypt(String folderPath, String password, String target) throws ZipException, FileAlreadyExistsException {
        File file = new File(target);
        if (file.isFile() && file.exists()) {
            if (!file.delete()) {
                throw new FileAlreadyExistsException("文件已存在，并且不能被删除");
            }
        }
        ZipParameters zipParameters = new ZipParameters();
        zipParameters.setEncryptFiles(true);
        zipParameters.setEncryptionMethod(EncryptionMethod.AES);
// Below line is optional. AES 256 is used by default. You can override it to use AES 128. AES 192 is supported only for extracting.
        zipParameters.setAesKeyStrength(AesKeyStrength.KEY_STRENGTH_256);
        ZipFile zipFile = new ZipFile(target, password.toCharArray());
        file = new File(folderPath);
        zipFile.addFolder(file, zipParameters);
        return file.isFile() && file.exists();
    }

    /**
     * 通过文件名产生密码
     *
     * @param name 文件名
     * @return 密码
     */
    public static String createPassword(String name) {
        return String.valueOf(Math.abs(HashAlgorithms.FNVHash1(name)));
    }

    /**
     * 通过密码解压
     *
     * @param file     压缩文件
     * @param password 密码。可以为空
     * @param target   目标目录
     */
    public static void unZipEncrypt(String file, String password, String target) throws ZipException {
        if (null == password || password.isEmpty()) {
            new ZipFile(file).extractAll(target);
        } else {
            new ZipFile(file, password.toCharArray()).extractAll(target);
        }
    }
}
