package com.goodsogood.ows.mapper.learn;

import com.goodsogood.ows.model.vo.learn.LearnScoreVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * @description: 理论武装
 * @author: zhangtao
 * @create: 2022-03-18 10:01
 */
@Repository
@Mapper
public interface TheoryArmsMapper {

    /*
     * 学习时长排名
     */
    @Select("select count(1) + 1 from zy_user_study where count_day > #{learnDay}")
    Integer getRow(@Param(value = "learnDay") Integer learnDay);

    /**
     * 个人学习时长
     */
    @Select("select count_day from zy_user_study where user_id=#{userId} order by update_time desc limit 1")
    Integer getLearnDay(@Param(value = "userId") Long userId);

    /**
     * 累计学习时长排行为10%的值
     */
    @Select("SELECT\n" +
            "\tb.count_day \n" +
            "FROM\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\tuser_id," +
            "count_day,\n" +
            "\t\t@rownum := @rownum + 1 AS rownum \n" +
            "\tFROM\n" +
            "\t( SELECT * FROM zy_user_study ORDER BY count_day DESC ) AS a,\n" +
            "\t( SELECT @rownum := 0 ) r \n" +
            "\t) AS b \n" +
            "WHERE\n" +
            "\tb.rownum <= (#{sum}*0.1) and count_day > 0 order by count_day limit 1")
    Integer getTenDay(@Param(value = "sum") Integer sum);

    /**
     * 连续学习天数
     */
    @Select("select continuation_day from zy_user_study where user_id=#{userId} order by update_time desc limit 1")
    Long continuationDay(@Param(value = "userId") Long userId);

    /**
     * 参加考试次数
     */
    @Select("select count(1) from zy_examination_count where user_id=#{userId}")
    Integer getExamCount(@Param(value = "userId") Long userId);

    /**
     * 个人考试次数排名
     */
    @Select(" select count(1)+1 from (select user_id,count(1) as count_num from zy_examination_count group by user_id)a where a.count_num > #{num}")
    Integer getExam(@Param(value = "num") Integer num);

    /**
     * 用户本年考试平均成绩
     */
    @Select("select avg(score) from zy_examination_count where user_id=#{userId} and date_format(create_time,'%Y') = #{year}")
    Double getAvgScore(@Param(value = "userId") Long userId,
                       @Param(value = "year") String year);

    @Select("select distinct a.user_id from(" +
            "select zae.user_id, zec.id,sum(if(is_true,1,0)) istrue,count(zae.id) as total\n" +
            "from zy_answer_examination as zae\n" +
            "left join zy_examination as ze on zae.examination_id = ze.id\n" +
            "join zy_examination_count AS zec on zae.examination_count_id = zec.id\n" +
            "where zae.examination_id = #{trainingId} and date_format(zec.create_time,'%Y-%m-%d') = #{todayStr}\n" +
            "group by zae.user_id,zec.id \n" +
            ") a where a.istrue=a.total"
    )
    Set<Long> queryTraining(@Param("todayStr") String todayStr,
                            @Param("trainingId") Long trainingId);

    @Select("select zae.user_id as userId,zec.id as examId,sum(zae.is_true) AS num\n" +
            "from zy_answer_examination as zae\n" +
            "left join zy_examination as ze on zae.examination_id = ze.id\n" +
            "join zy_examination_count AS zec on zae.examination_count_id = zec.id\n" +
            "where zae.examination_id = #{examId} and date_format(zec.create_time,'%Y-%m')=#{thisMonth}\n" +
            "group by  zae.user_id, zec.id\n" +
            "order by  zae.user_id,zec.id asc \n")
    List<LearnScoreVO> queryExamineMonth(@Param("thisMonth") String thisMonth, @Param("examId") Long examId);


    @Select("select zae.user_id as userId,zec.id as examId,sum(zae.is_true) AS num\n" +
            "from zy_answer_examination as zae\n" +
            "left join zy_examination as ze on zae.examination_id = ze.id\n" +
            "join zy_examination_count AS zec on zae.examination_count_id = zec.id\n" +
            "where zae.examination_id = #{examId} and QUARTER(zec.create_time)=#{quartz} and year(zec.create_time)=#{year}\n" +
            "group by  zae.user_id, zec.id\n" +
            "order by  zae.user_id, zec.id asc\n")
    List<LearnScoreVO> queryExamineQuart(@Param("quartz") Integer quartz, @Param("year") Integer year, @Param("examId") Long examId);


    @Select("select distinct a.user_id from(" +
            "select zae.user_id, zec.id,sum(if(is_true,1,0)) istrue,count(zae.id) as total\n" +
            "from zy_answer_examination as zae\n" +
            "left join zy_examination as ze on zae.examination_id = ze.id\n" +
            "join zy_examination_count AS zec on zae.examination_count_id = zec.id\n" +
            "where zae.examination_id = #{trainingId} and date_format(zec.create_time,'%Y-%m-%d') = #{todayStr}\n" +
            "group by zae.user_id,zec.id \n" +
            ") a "
    )
    Set<String> queryTrainingUser(@Param("todayStr") String todayStr,
                                  @Param("trainingId") Long trainingId);

    @Select("select distinct zae.user_id \n" +
            "from zy_answer_examination as zae\n" +
            "left join zy_examination as ze on zae.examination_id = ze.id\n" +
            "join zy_examination_count AS zec on zae.examination_count_id = zec.id\n" +
            "where zae.examination_id = #{examId} and date_format(zec.create_time,'%Y-%m')=#{thisMonth}\n")
    Set<String> queryExamineMonthUser(@Param("thisMonth") String thisMonth, @Param("examId") Long examId);

    @Select("select distinct zae.user_id \n" +
            "from zy_answer_examination as zae\n" +
            "left join zy_examination as ze on zae.examination_id = ze.id\n" +
            "join zy_examination_count AS zec on zae.examination_count_id = zec.id\n" +
            "where zae.examination_id = #{examId} and QUARTER(zec.create_time)=#{quartz} and year(zec.create_time)=#{year}\n"
    )
    Set<String> queryExamineQuartUser(@Param("quartz") Integer quartz, @Param("year") Integer year, @Param("examId") Long examId);

    /**
     * 每日一练参与人数
     *
     * @param queryDay yyyy-MM-dd
     * @param examId   每日一测ID
     * @return int
     */
    @Select("select COUNT(DISTINCT zae.user_id) \n" +
            "from zy_answer_examination as zae \n" +
            "left join zy_examination as ze on zae.examination_id = ze.id \n" +
            "join zy_examination_count AS zec on zae.examination_count_id = zec.id\n" +
            "where zae.examination_id = #{examId} and date_format(zec.create_time,'%Y-%m-%d') = #{queryDay} ")
    Integer findDayExam(@Param("queryDay") String queryDay, @Param("examId") Long examId);

    /**
     * 每月一测参与人数
     *
     * @param queryMonth yyyy-MM
     * @param examId     每月一测
     * @return int
     */
    @Select("SELECT count(DISTINCT zae.user_id)\n" +
            "from zy_answer_examination as zae\n" +
            "left join zy_examination as ze on zae.examination_id = ze.id\n" +
            "join zy_examination_count AS zec on zae.examination_count_id = zec.id\n" +
            "where zae.examination_id = #{examId} and date_format(zec.create_time,'%Y-%m') = #{queryMonth} ")
    Integer findMonthExam(@Param("queryMonth") String queryMonth, @Param("examId") Long examId);

    /**
     * 每季一考试参与人数
     *
     * @param queryQuart 季度
     * @param examId     queryQuart
     * @param year       查询年度
     * @return int
     */
    @Select("\tselect count(DISTINCT zae.user_id) \n" +
            "\tfrom zy_answer_examination as zae \n" +
            "\tleft join zy_examination as ze on zae.examination_id = ze.id\n" +
            "\tjoin zy_examination_count AS zec on zae.examination_count_id = zec.id\n" +
            "\twhere zae.examination_id = #{examId} and QUARTER(zec.create_time) = #{queryQuart} and year(zec.create_time) = #{year} \n")
    Integer findQuartExam(@Param("queryQuart") Integer queryQuart, @Param("examId") Long examId, @Param("year") Integer year);
}


