package com.goodsogood.ows.mapper.finereport;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.finereport.FinereportOrgInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @program: downloadfile
 * @description: 统计组织信息
 * @author: Mr.<PERSON>
 * @create: 2020-08-06 11:31
 **/
@Mapper
@Repository
public interface FineReportOrgInfoMapper extends MyMapper<FinereportOrgInfo> {



    @Select(" select sum(party_org_number) as partyOrgNumber,\n" +
            " sum(career_org_number)  as careerOrgNumber,\n" +
            " sum(company_org_number) as companyOrgNumber,\n" +
            " sum(society_org_number) as societyOrgNumber,\n" +
            " sum(other_org_number) as otherOrgNumber,\n" +
            " sum(period_num) as periodNum,\n" +
            " sum(party_branch_total) as partyBranchTotal,\n" +
            " sum(party_branch_number) as partyBranchNumber\n" +
            " from \n" +
            " (\n" +
            "\t\t SELECT * FROM t_finereport_org_info\n" +
            "\t\t UNION \n" +
            "\t\t SELECT * FROM t_finereport_org_info_other\n" +
            " ) as L\n" +
            " where  L.sta_flag=1 " +
            " and    L.org_id NOT IN (4685 , 3261) AND L.sta_year=#{staYear}")
    FinereportOrgInfo getOrgInfoIndex(@Param(value = "orgId")Long orgId,
                                      @Param(value = "staYear")Integer staYear);


    @Select("select party_org_number as partyOrgNumber, career_org_number as careerOrgNumber," +
            " company_org_number as companyOrgNumber," +
            " society_org_number as societyOrgNumber ,other_org_number as otherOrgNumber," +
            " period_num as periodNum, party_branch_total as partyBranchTotal ," +
            " party_branch_number as partyBranchNumber " +
            " from  t_finereport_org_info  where  1=1  " +
            " and org_id=#{orgId} AND sta_year=#{staYear}")
    FinereportOrgInfo getOrgInfoByOrgId(@Param(value = "orgId")Long orgId,
                                      @Param(value = "staYear")Integer staYear);



    @Select("select party_org_number as partyOrgNumber, career_org_number as careerOrgNumber," +
            " company_org_number as companyOrgNumber," +
            " society_org_number as societyOrgNumber ,other_org_number as otherOrgNumber," +
            " period_num as periodNum, party_branch_total as partyBranchTotal ," +
            " party_branch_number as partyBranchNumber " +
            " from  t_finereport_org_info  where  1=1  " +
            " and org_id=#{orgId} AND sta_year=#{staYear}")
    FinereportOrgInfo getOrgInfoByOrgIdList(@Param(value = "orgIds") List<Long> orgIds,
                                        @Param(value = "staYear")Integer staYear);

}
