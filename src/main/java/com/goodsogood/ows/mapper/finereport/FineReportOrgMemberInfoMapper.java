package com.goodsogood.ows.mapper.finereport;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.finereport.FineReportOrgMemberInfo;
import com.goodsogood.ows.model.db.finereport.FinereportOrgInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @program: downloadfile
 * @description: 统计组织下党员的信息按照不同维度
 * @author: Mr.<PERSON>
 * @create: 2020-07-30 15:41
 **/
@Mapper
@Repository
public interface FineReportOrgMemberInfoMapper extends MyMapper<FineReportOrgMemberInfo> {


    @Select("select sum(formal_party_member)as formalPartyMember," +
            " sum(pre_party_member) as prePartyMember," +
            " sum(other_party_member) as otherPartyMember," +
            " sum(male_member) as maleMember," +
            " sum(female_member) as femaleMember," +
            " sum(other_sex_member) as otherSexMember," +
            " sum(nationality_party_member) as nationalityPartyMember," +
            " sum(minority_party_member) as minorityPartyMember," +
            " sum(other_nationality_party_member) as otherNationalityPartyMember," +
            " sum(education1) as education1, sum(education2) as education2," +
            " sum(education3) as education3, sum(education4) as education4," +
            " sum(education5) as education5,sum(education6) as education6,sum(education7) as education7," +
            " sum(time1) as time1,sum(time2) as time2,sum(time3) as time3," +
            " sum(time4) as time4,sum(time5) as time5," +
            " sum(age1) as age1,sum(age2) as age2,sum(age3) as age3,sum(age4) as age4,"+
            " sum(age5) as age5,sum(age6) as age6,sum(age7) as age7,sum(age8) as age8,"+
            " sum(profession1) as profession1,sum(profession2) as profession2,sum(profession3) as profession3,"+
            " sum(profession4) as profession4,sum(profession5) as profession5,sum(profession6) as profession6,"+
            " sum(profession7) as profession7,sum(profession8) as profession8,sum(profession9) as profession9"+
            " from  (" +
            "   SELECT * FROM t_finereport_org_member_info\n" +
            "   UNION \n" +
            "   SELECT * FROM t_finereport_org_member_info_other" +
            ") as L " +
            " where L.sta_flag=1 and org_id not in(3261) " +
            " and   L.sta_year=${staYear} ")
    FineReportOrgMemberInfo getOrgMemberInfoIndex(@Param(value = "orgId")Long orgId,
                                                  @Param(value = "staYear")Integer staYear);




    @Select("select formal_party_member as formalPartyMember ,pre_party_member as prePartyMember," +
            " other_party_member as otherPartyMember," +
            " male_member as maleMember ,female_member as femaleMember ,other_sex_member as otherSexMember," +
            " nationality_party_member as nationalityPartyMember," +
            " minority_party_member as minorityPartyMember," +
            " other_nationality_party_member as otherNationalityPartyMember," +
            " education1,education2, education3,education4,education5,education6,education7," +
            " time1,time2,time3,time4,time5 ," +
            " age1,age2,age3,age4,age5,age6,age7,age8," +
            " profession1,profession2,profession3,profession4,profession5," +
            " profession6,profession7,profession8,profession9 " +
            " from  t_finereport_org_member_info " +
            " where org_id=#{orgId} and sta_year=${staYear} ")
    FineReportOrgMemberInfo getOrgMemberInfo(@Param(value = "orgId") Long orgId,
                                             @Param(value = "staYear")Integer staYear);



    @Select(" <script> select formal_party_member as formalPartyMember ,pre_party_member as prePartyMember," +
            " other_party_member as otherPartyMember," +
            " male_member as maleMember ,female_member as femaleMember ,other_sex_member as otherSexMember," +
            " nationality_party_member as nationalityPartyMember," +
            " minority_party_member as minorityPartyMember," +
            " other_nationality_party_member as otherNationalityPartyMember," +
            " education1,education2, education3,education4,education5,education6,education7," +
            " time1,time2,time3,time4,time5 ," +
            " age1,age2,age3,age4,age5,age6,age7,age8," +
            " profession1,profession2,profession3,profession4,profession5," +
            " profession6,profession7,profession8,profession9 ,org_id as orgId" +
            " from  t_finereport_org_member_info " +
            " where  org_id in "+
            " <foreach collection=\"orgIds\" index=\"index\" item=\"oid\" open=\"(\" separator=\",\" close=\")\">\n"+
            "  #{oid} "+
            " </foreach>"+
            "  and sta_year=${staYear} </script>")
    List<FineReportOrgMemberInfo> getOrgMemberInfoList(@Param(value = "orgIds") List<Long> orgIds,
                                                       @Param(value = "staYear") Integer staYear);

}
