package com.goodsogood.ows.mapper.activity;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.activity.RevisitEntity;
import com.goodsogood.ows.model.vo.overview.KeyValVo;
import com.goodsogood.ows.model.vo.overview.RevisitRankVo;
import com.goodsogood.ows.model.vo.tbc.TbcBaseVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-09-15 16:41
 **/
@Repository
@Mapper
public interface RevisitMapper extends MyMapper<RevisitEntity> {


    @Select("<script>" +
            "SELECT A.alias as `name`,IF(b.number IS NULL,0,b.number) as `value` FROM (\n" +
            "\tSELECT * FROM t_overview_option WHERE type=11 and project_name='overview'\n" +
            ") as A\n" +
            "LEFT JOIN\n" +
            "(\n" +
            "\tSELECT count as number,'sta_all_person' as `name`\n" +
            "\tFROM\n" +
            "\t(\n" +
            "\t\t\tSELECT COUNT(\tDISTINCT user_id ) AS count" +
            " FROM t_revisit_result WHERE type=#{type} and  ${sqlPart} "+
            "\t) as L\n" +
            "\tUNION\n" +
            "\tSELECT count as number,'today_all' as `name`\n" +
            "\tFROM\n" +
            "\t(\n" +
            "\t\tSELECT COUNT(DISTINCT user_id)  AS count,user_id FROM t_revisit_result WHERE type=#{type} and  ${sqlPart} and  " +
            " to_days(create_time) = to_days(now())\n" +
            "\t) as L\n" +
            "\tUNION\n" +
            "\tSELECT count as number,'month_all' as `name`\n" +
            "\tFROM\n" +
            "\t(\n" +
            "\t\tSELECT COUNT(1) AS count,user_id FROM t_revisit_result WHERE type=#{type} and  ${sqlPart} and " +
            "  DATE_FORMAT(create_time,'%Y%m') = DATE_FORMAT(NOW() ,'%Y%m')\n" +
            "\t) as L\n" +
            "\tUNION\n" +
            "\tSELECT count(1) as number,'online_person' as `name`\n" +
            "\tFROM\n" +
            "\t(\n" +
            "\t\t\tSELECT COUNT(DISTINCT user_id) AS count  FROM t_revisit_result " +
            " WHERE type=#{type} and  ${sqlPart}  AND create_time <![CDATA[ >= ]]> DATE_SUB(NOW(), INTERVAL 3 MINUTE)\n" +
            "\t\t\tGROUP BY user_id\n" +
            "\t) as L"+
            "\tUNION\n" +
            "\tSELECT count as number,'last_month_all' as `name`\n" +
            "\tFROM\n" +
            "\t(\n" +
            "\t\tSELECT COUNT(1) AS count,user_id FROM t_revisit_result WHERE type=#{type} and  ${sqlPart} " +
            " and  DATE_FORMAT(create_time,'%Y%m') =  DATE_FORMAT(date_sub(now(),interval 1 month),'%Y%m')\n" +
            "\t) as L\n" +
            "\tUNION\n" +
            "\tSELECT totalCount as number,'sta_all_count' as `name`\n" +
            "\tFROM\n" +
            "\t(\n" +
            "\t\t\tSELECT count(1) as totalCount " +
            " FROM t_revisit_result WHERE type=#{type} and  ${sqlPart} \n" +
            "\t) as L\n" +
            ") as B ON A.`name`=b.`name` ORDER BY A.order_num"+
            "</script>")
    List<KeyValVo>  orgOverviewRevisit(@Param(value = "type") Integer type,
                                       @Param(value = "sqlPart") String sqlPart);


//    @Select("\tSELECT * FROM (\n" +
//            "\t\t\tSELECT days as `name`, SUM(if(countNum>0,1,0)) as `value` FROM\n" +
//            "\t\t\t(\n" +
//            "\t\t\t\tSELECT COUNT(1) AS countNum,user_id, DATE_FORMAT(create_time,'%e') AS days \n" +
//            "\t\t\t\tFROM t_revisit_result WHERE type=#{type} and ${sqlPart}\n" +
//            "\t\t\t\tand DATE_FORMAT(create_time,'%Y%m') = DATE_FORMAT(NOW() ,'%Y%m')  \n" +
//            "\t\t\t\tGROUP BY days,user_id desc\t\t\n" +
//            "\t\t\t) as L GROUP BY days ORDER BY days\n" +
//            "\t)as L1 ORDER BY  cast(`name` as UNSIGNED INTEGER)")
//    List<KeyValVo> getCurrentMonthRevisit(@Param(value = "type")Integer type,@Param(value = "sqlPart")String sqlPart);

    @Select(" SELECT days as `name`,countNum as `value` FROM\n" +
            " (\n" +
            "\t SELECT\n" +
            "\t\t\tCOUNT(1) AS countNum,\n" +
            "\t\t\tDATE_FORMAT(create_time,'%e') AS days\n" +
            "\tFROM\n" +
            "\t\t\tt_revisit_result \n" +
            "\tWHERE\n" +
            "\t\t\ttype=#{type} \n" +
            "\t\t\tand  ${sqlPart} \n" +
            "\t\t\tand DATE_FORMAT(create_time,'%Y%m') = DATE_FORMAT(NOW() ,'%Y%m') \n" +
            "\t\t\tGROUP BY days\n" +
            ") as L ORDER BY  cast(`name` as UNSIGNED INTEGER)")
    List<KeyValVo> getCurrentMonthRevisit(@Param(value = "type")Integer type,@Param(value = "sqlPart")String sqlPart);


//    @Select("\tSELECT * FROM (\n" +
//            "\t\t\tSELECT days as `name`, SUM(if(countNum>0,1,0)) as `value` FROM\n" +
//            "\t\t\t(\n" +
//            "\t\t\t\tSELECT COUNT(1) AS countNum,user_id, DATE_FORMAT(create_time,'%e') AS days \n" +
//            "\t\t\t\tFROM t_revisit_result WHERE type=#{type} and ${sqlPart} \n" +
//            "\t\t\t\tand DATE_FORMAT(create_time,'%Y%m') =  DATE_FORMAT(date_sub(now(),interval 1 month),'%Y%m')\n" +
//            "\t\t\t\tGROUP BY days,user_id desc\t\t\n" +
//            "\t\t\t) as L GROUP BY days ORDER BY days\n" +
//            "\t)as L1 ORDER BY  cast(`name` as UNSIGNED INTEGER)")
//    List<KeyValVo> getLastMonthRevisit(@Param(value = "type")Integer type,@Param(value = "sqlPart")String sqlPart);


    @Select(" SELECT days as `name`,countNum as `value` FROM\n" +
            " (\n" +
            "\t SELECT\n" +
            "\t\t\tCOUNT(1) AS countNum,\n" +
            "\t\t\tDATE_FORMAT(create_time,'%e') AS days\n" +
            "\tFROM\n" +
            "\t\t\tt_revisit_result \n" +
            "\tWHERE\n" +
            "\t\t\ttype=#{type} \n" +
            "\t\t\tand ${sqlPart} \n" +
            "\t\t\tand DATE_FORMAT(create_time,'%Y%m') = DATE_FORMAT(date_sub(now(),interval 1 month),'%Y%m') \n" +
            "\t\t\tGROUP BY days\n" +
            ") as L \n" +
            "ORDER BY  cast(`name` as UNSIGNED INTEGER)")
    List<KeyValVo> getLastMonthRevisit(@Param(value = "type")Integer type,@Param(value = "sqlPart")String sqlPart);


    @Select("<script> "+
            "\t\t\t\tSELECT    \n" +
            "\t\t\t\t\t\tDATE_FORMAT(create_time, '%c') as `name` ,\n" +
            "            COUNT(1) AS `value`\n" +
            "        FROM\n" +
            "            t_revisit_result \n" +
            "        WHERE\n" +
            "            type=#{type} \n" +
            "            and  ${sqlPart}"+
            "            and DATE_FORMAT(create_time,'%Y') = DATE_FORMAT(NOW() ,'%Y') \n" +
            "        GROUP BY `name` "+
            "</script>")
    List<KeyValVo> getCurrentYearRevisit(@Param(value = "type")Integer type,@Param(value = "sqlPart")String sqlPart);


    @Select("\t\t\t\tSELECT    \n" +
            "\t\t\t\t\t\tDATE_FORMAT(create_time, '%c') as `name` ,\n" +
            "            COUNT(1) AS `value`\n" +
            "        FROM\n" +
            "            t_revisit_result \n" +
            "        WHERE\n" +
            "            type=2 \n" +
            "            and ${sqlPart}"+
            "            and DATE_FORMAT(create_time,'%Y') =DATE_FORMAT(date_sub(now(),interval 1 YEAR),'%Y') \n" +
            "        GROUP BY `name`")
    List<KeyValVo> getLastYearRevisit(@Param(value = "type")Integer type,@Param(value = "sqlPart")String sqlPart);

    @Select("SELECT user_id FROM t_revisit_result WHERE user_name is null\n" +
            "GROUP BY user_id")
    List<Long> supplementaryUserInfo();

    @Update("UPDATE t_revisit_result SET org_name=#{orgName},own_Id=#{ownerId}, " +
            "own_name=#{ownerName},org_type_child=#{orgTypeChild},user_name=#{userName} " +
            "WHERE user_id=#{userId} and user_name is null ")
    int updateSupplementaryRecord(TbcBaseVo tbcBaseVo);



    @Select("SELECT t.`rank`,t.userName,t.orgName,t.countNum FROM \n" +
            "  (\n" +
            "\t\tSELECT u.countNum,u.orgName, u.userName,\n" +
            "\t\tCASE \n" +
            "\t\t WHEN @last_score = u.countNum\n" +
            "\t\t\t THEN @rank \n" +
            "\t\t WHEN @last_score := u.countNum \n" +
            "\t\t\t THEN @rank := @rank + 1    \n" +
            "\t\tEND AS `rank` \n" +
            "  FROM \n" +
            "    (\n" +
            "\t\t\tSELECT COUNT(1) AS countNum,user_name as userName,org_name as orgName\n" +
            "\t\t\tFROM t_revisit_result WHERE type=#{type}" +
            " and org_id=#{orgId}"+
            " and org_type_child in(${orgTypeChild})  and user_name is not null\n" +
            "\t\t\tand ${sqlPart}"+
            "\t\t\tGROUP BY user_id,org_id\n" +
            "\t\t\tORDER BY countNum DESC\n" +
            "\t\t\tLIMIT 100\n" +
            "\t ) u, \n" +
            "   (SELECT @rank := 0, @last_score := NULL) r\n" +
            ") t;")
    List<RevisitRankVo> partyMemberRankByOrg(@Param(value = "type") Integer type,
                                             @Param(value = "orgId") Long  orgId,
                                             @Param(value = "sqlPart") String sqlPart,
                                             @Param(value = "orgTypeChild") String orgTypeChild);


    @Select("SELECT t.`rank`,t.userName,t.orgName ,t.countNum  FROM \n" +
            "  (\n" +
            "\t\tSELECT u.countNum,u.orgName, u.userName,\n" +
            "\t\tCASE \n" +
            "\t\t WHEN @last_score = u.countNum\n" +
            "\t\t\t THEN @rank \n" +
            "\t\t WHEN @last_score := u.countNum \n" +
            "\t\t\t THEN @rank := @rank + 1    \n" +
            "\t\tEND AS `rank` \n" +
            "  FROM \n" +
            "    (\n" +
            "\t\t\tSELECT COUNT(1) AS countNum,user_name as userName,org_name as orgName\n" +
            "\t\t\tFROM t_revisit_result WHERE type=#{type} and own_id=#{ownerId}" +
            "  and user_name is not null\n" +
            "\t\t\tand ${sqlPart}"+
            "\t\t\tGROUP BY user_id\n" +
            "\t\t\tORDER BY countNum DESC\n" +
            "\t\t\tLIMIT 100\n" +
            "\t ) u, \n" +
            "   (SELECT @rank := 0, @last_score := NULL) r\n" +
            ") t;")
    List<RevisitRankVo> partyMemberRankByOwnName(@Param(value = "type") Integer type,
                                                 @Param(value = "ownerId") Long ownerId,
                                                 @Param(value = "sqlPart") String sqlPart);

    @Select("SELECT t.`rank`,t.userName ,t.countNum ,t.orgName FROM \n" +
            "  (\n" +
            "\t\tSELECT u.countNum, u.userName, u.orgName, \n" +
            "\t\tCASE \n" +
            "\t\t WHEN @last_score = u.countNum\n" +
            "\t\t\t THEN @rank \n" +
            "\t\t WHEN @last_score := u.countNum \n" +
            "\t\t\t THEN @rank := @rank + 1    \n" +
            "\t\tEND AS `rank` \n" +
            "  FROM \n" +
            "    (\n" +
            "\t\t\tSELECT COUNT(1) AS countNum,user_name as userName,org_name as orgName\n" +
            "\t\t\tFROM t_revisit_result WHERE type=#{type} and user_name is not null\n" +
            "\t\t\tand ${sqlPart}" +
            "\t\t\tGROUP BY user_id \n" +
            "\t\t\tORDER BY countNum DESC\n" +
            "\t\t\tLIMIT 100\n" +
            "\t ) u, \n" +
            "   (SELECT @rank := 0, @last_score := NULL) r\n" +
            ") t;")
    List<RevisitRankVo> partyMemberRankByUserId(@Param(value = "type") Integer type,
                                                @Param(value = "sqlPart") String sqlPart);


    @Select("SELECT t.`rank`,t.org_name as orgName,countNum as rate FROM \n" +
            "  (\n" +
            "\t\tSELECT u.countNum,u.org_name, \n" +
            "\t\tCASE \n" +
            "\t\t WHEN @last_score = u.countNum\n" +
            "\t\t\t THEN @rank \n" +
            "\t\t WHEN @last_score := u.countNum \n" +
            "\t\t\t THEN @rank := @rank + 1    \n" +
            "\t\tEND AS `rank` \n" +
            "  FROM \n" +
            "    (\n" +
            "\t\t\tSELECT (COUNT(DISTINCT user_id)/\n" +
            "\t\t\t( SELECT COUNT(1) FROM t_user_snapshot us \n" +
            "\t\t\tWHERE org_id = org_id AND `status` = 1 and org_type_child in(${orgTypeChild}) \n" +
            "\t\t\tAND ${sqlSnapshotDatePart}))\n" +
            "\t\t\tAS countNum,org_name  \n" +
            "\t\t\tFROM t_revisit_result WHERE type=#{type} and user_name is not NULL and org_id is not null\n" +
            "\t\t\tand  ${sqlPart}\n" +
            "\t\t\tGROUP BY org_id\n" +
            "\t\t\tORDER BY countNum DESC\n" +
            "\t\t\tLIMIT 100\n" +
            "\t ) u, \n" +
            "   (SELECT @rank := 0, @last_score := NULL) r\n" +
            ") t;")
    List<RevisitRankVo> revisitOrgRankByOrg(@Param(value = "type") Integer type,
                                            @Param(value = "sqlSnapshotDatePart")String sqlSnapshotDatePart,
                                            @Param(value = "sqlPart")String sqlPart,
                                            @Param(value = "orgTypeChild")String orgTypeChild);



    @Select("\t\t\tSELECT COUNT(DISTINCT user_id)AS countNum,org_name as orgName ,org_id as orgId\n" +
            "\t\t\tFROM t_revisit_result WHERE type=#{type} and user_name is not NULL and org_id is not null\n" +
            "\t\t\tand  ${sqlPart}\n" +
            "\t\t\tGROUP BY org_id\n" +
            "\t\t\tORDER BY countNum DESC\n" +
            "\t\t\tLIMIT 100")
    List<RevisitRankVo> revisitOrgRankByOrgNew(@Param(value = "type") Integer type,
                                               @Param(value = "sqlPart")String sqlPart,
                                               @Param(value = "orgTypeChild")String orgTypeChild);


    @Select("SELECT t.`rank`,t.own_name as ownName,countNum as rate FROM \n" +
            "  (\n" +
            "\t\tSELECT u.countNum,u.own_name, \n" +
            "\t\tCASE \n" +
            "\t\t WHEN @last_score = u.countNum\n" +
            "\t\t\t THEN @rank \n" +
            "\t\t WHEN @last_score := u.countNum \n" +
            "\t\t\t THEN @rank := @rank + 1    \n" +
            "\t\tEND AS `rank` \n" +
            "  FROM \n" +
            "    (\n" +
            "\t\t\tSELECT \n" +
            "\t\t\tCOUNT(DISTINCT user_id)/\n" +
            "\t\t\t( SELECT  COUNT(1) FROM\n" +
            "\t\t\t\tt_user_snapshot us\n" +
            "\t\t\t\tLEFT JOIN t_org_snapshot os ON os.org_id = us.org_id AND os.date_month = us.date_month\n" +
            "\t\t\t\tWHERE os.owner_id = own_id\n" +
            "\t\t\t\t\tAND us.`status` = 1\n" +
            "\t\t\t\t\tAND os.`status` = 1  AND ${sqlSnapshotDatePart} \n" +
            "\t\t\t)AS countNum,own_name  \n" +
            "\t\t\tFROM t_revisit_result WHERE type=#{type} and user_name is not NULL and org_id is not null\n" +
            "\t\t\tand ${sqlPart}\n" +
            "\t\t\tGROUP BY own_id\n" +
            "\t\t\tORDER BY countNum DESC\n" +
            "\t\t\tLIMIT 100\n" +
            "\t ) u, \n" +
            "   (SELECT @rank := 0, @last_score := NULL) r\n" +
            ") t;")
    List<RevisitRankVo> revisitOrgRankByOwnName(@Param(value = "type") Integer type,
                                                @Param(value = "sqlSnapshotDatePart")String sqlSnapshotDatePart,
                                                @Param(value = "sqlPart")String sqlPart);





    @Select("SELECT COUNT(1) FROM t_revisit_result  WHERE user_id=#{userId} and type=#{type} and ${sqlPart}")
    Integer singleUserSta(@Param(value = "userId") Long userId,
                          @Param(value = "type") Integer type,
                          @Param(value = "sqlPart")String sqlPart);


    @Select("SELECT COUNT(1) FROM\n" +
            "(\n" +
            "\t\t\tSELECT COUNT(1) AS count,user_id FROM t_revisit_result WHERE type=#{type} and  org_id=#{orgId} and \n" +
            "\t\t\t to_days(create_time) = to_days(now())\n" +
            "\t\t\tGROUP BY user_id\n" +
            ") as L")
    Integer todayCount(@Param(value = "orgId") Long orgId, @Param(value = "type") Integer type);


    @Select("\tSELECT COUNT(1) AS count FROM t_revisit_result WHERE type=#{type} and  org_id=#{orgId} and \n" +
            "\tDATE_FORMAT(create_time,'%Y') = DATE_FORMAT(now(),'%Y')")
    Integer thisYearCount(@Param(value = "orgId") Long orgId, @Param(value = "type") Integer type);

    /**
     * 入党誓词与志愿书统计 得到参加数据
     * @param type
     * @return
     */
    @Select("SELECT COALESCE(SUM(countNum),0) as countNum FROM\n" +
            "(\n" +
            "\tSELECT COUNT( DISTINCT user_id) as countNum, DATE_FORMAT(create_time,'%Y-%m') as addDate FROM t_revisit_result \n" +
            "\tWHERE  type=#{type} and user_name is not NULL and org_id is not null and org_id=#{orgId} \n" +
            "\tand DATE_FORMAT(create_time,'%Y-%m')IN( ${sqlDateParty} ) \n" +
            "\tGROUP BY addDate\n" +
            ") as L\t\n")
    Integer getAddUserCountByOrgId(@Param(value = "type") Integer type,
                                   @Param(value = "orgId") Long orgId,
                                   @Param(value = "sqlDateParty") String sqlDateParty);


    /**
     * 入党誓词与志愿书统计 得到参加数据
     * @param type
     * @return
     */
    @Select("SELECT COALESCE(SUM(countNum),0) as countNum FROM\n" +
            "(\n" +
            "\tSELECT COUNT( DISTINCT user_id) as countNum, DATE_FORMAT(create_time,'%Y-%m') as addDate FROM t_revisit_result \n" +
            "\tWHERE  type=#{type} and user_name is not NULL and org_id is not null and own_id=#{ownId} \n" +
            "\tand DATE_FORMAT(create_time,'%Y-%m')IN( ${sqlDateParty} ) \n" +
            "\tGROUP BY addDate\n" +
            ") as L\t\n")
    Integer getAddUserCountByOwnerId(@Param(value = "type") Integer type,
                                     @Param(value = "ownId") Long ownId,
                                     @Param(value = "sqlDateParty") String sqlDateParty);




    @Select("<script>" +
            "\n" +
            "SELECT\n" +
            "CASE \n" +
            " WHEN @last_score = u.gg\n" +
            " THEN @rank \n" +
            " WHEN @last_score := u.gg \n" +
            " THEN @rank := @rank + 1    \n" +
            "END AS `rank` ,\n" +
            "u.owner_name as ownName,u.org_name as orgName,u.gg as rate,u.org_id as orgId\n" +
            "FROM \n" +
            "(\t\n" +
            "\t\tselect * from (\n" +
            "\t\tselect org_id,org_name,owner_id,owner_name,COUNT(t3.user_id) sum_u,sum(if(t4.user_id is null,0,1)) c_u1," +
            "convert((sum(if(t4.user_id is null,0,1))/COUNT(t3.user_id))*100,decimal(10,2)) gg\n" +
            "\t\tfrom (\n" +
            "\t\t\t\tselect user_id,user_name,date_month,org_id,org_name,owner_id,owner_name from \n" +
            "\t\t\t\t(\n" +
            "\t\t\t\t\tselect user_id,user_name,tus.date_month,tus.org_id,tos.org_name," +
            "   tos.owner_id,tos1.org_name owner_name from t_user_snapshot tus\n" +
            "\t\t\t\t\tleft join t_org_snapshot tos on tus.org_id=tos.org_id and tos.date_month=tus.date_month \n" +
            "\t\t\t\t\tleft join t_org_snapshot tos1 on tos.owner_id=tos1.org_id and tos.date_month=tos1.date_month \n" +
            "\t\t\t\t\twhere tus.`year`=#{year} " +
            "   and tus.`month`IN " +
            "<foreach item=\"item\" collection=\"listMonth\" open=\"(\" separator=\",\" close=\")\">" +
            "   #{item} " +
            "</foreach> " +
            "   and political_type in (1,5) " +
            "   and tos.org_id not in(1294,5,2409,2417,1222,1255)\n" +
            "\t\t\t\t)t1\n" +
            "\t\t\t\tunion\n" +
            "\t\t\t\tselect * from (\n" +
            "\t\t\t\t\tselect u.user_id,u.`name` user_name,'${currentMonth}' as date_month,o.organization_id org_id," +
            " o.`name` org_name,o.owner_id,o1.`name` owner_name  from t_user u \n" +
            "\t\t\t\t\tinner join t_user_org_corp uoc on u.user_id=uoc.user_id and uoc.is_employee=1 and u.`status`=1 and u.political_type in (1,5) and uoc.region_id=19 \n" +
            "\t\t\t\t\tinner join t_organization o on o.organization_id=uoc.organization_id and  o.`status`=1 and o.region_id=19 and  o.organization_id not in(1294,5,2409,2417,1222,1255)\n" +
            "\t\t\t\t\tinner join t_organization o1 on o.owner_id=o1.organization_id \n" +
            "\t\t\t\t)t2\n" +
            "\t\t)t3 \n" +
            "\t\tLEFT JOIN\n" +
            "\t\t(\n" +
            "\t\t\tselect user_id,DATE_FORMAT(create_time,'%Y-%m') as date from \n" +
            "\t\t\tt_revisit_result where type=#{type}  " +
            "  GROUP BY user_id,DATE_FORMAT(create_time,'%Y-%m')\n" +
            "\t\t)t4 on t3.user_id =t4.user_id and t3.date_month=t4.date GROUP BY org_id\n" +
            "\t\t)t5 where org_id is not null  order by gg desc\n" +
            ") u, (SELECT @rank := 0, @last_score := NULL) r"+
            "</script>")
    List<RevisitRankVo> revisitOrgRankByOrgName(@Param(value = "type") Integer type,
                                                @Param(value = "currentMonth")String currentMonth,
                                                @Param(value = "year")Integer year,
                                                @Param(value = "listMonth") List<Integer> listMonth);


    @Select("<script>" +
            "\n" +
            "SELECT\n" +
            "CASE \n" +
            " WHEN @last_score = u.gg\n" +
            " THEN @rank \n" +
            " WHEN @last_score := u.gg \n" +
            " THEN @rank := @rank + 1    \n" +
            "END AS `rank` ,\n" +
            "u.owner_name as ownName,u.org_name as orgName,u.gg as rate,u.org_id as orgId\n" +
            "FROM \n" +
            "(\t\n" +
            "\t\tselect * from (\n" +
            "\t\tselect org_id,org_name,owner_id,owner_name,COUNT(t3.user_id) sum_u,sum(if(t4.user_id is null,0,1)) c_u1," +
            "convert((sum(if(t4.user_id is null,0,1))/COUNT(t3.user_id))*100,decimal(10,2)) gg\n" +
            "\t\tfrom (\n" +
            "\t\t\t\tselect user_id,user_name,date_month,org_id,org_name,owner_id,owner_name from \n" +
            "\t\t\t\t(\n" +
            "\t\t\t\t\tselect user_id,user_name,tus.date_month,tus.org_id,tos.org_name," +
            "   tos.owner_id,tos1.org_name owner_name from t_user_snapshot tus\n" +
            "\t\t\t\t\tleft join t_org_snapshot tos on tus.org_id=tos.org_id and tos.date_month=tus.date_month \n" +
            "\t\t\t\t\tleft join t_org_snapshot tos1 on tos.owner_id=tos1.org_id and tos.date_month=tos1.date_month \n" +
            "\t\t\t\t\twhere tus.`year`=#{year} " +
            "   and tus.`month`IN " +
            "<foreach item=\"item\" collection=\"listMonth\" open=\"(\" separator=\",\" close=\")\">" +
            "   #{item} " +
            "</foreach> " +
            "   and political_type in (1,5) " +
            "   and tos.org_id not in(1294,5,2409,2417,1222,1255)\n" +
            "\t\t\t\t)t1\n" +
            "\t\t\t\tunion\n" +
            "\t\t\t\tselect * from (\n" +
            "\t\t\t\t\tselect u.user_id,u.`name` user_name,'${currentMonth}' as date_month,o.organization_id org_id," +
            " o.`name` org_name,o.owner_id,o1.`name` owner_name  from t_user u \n" +
            "\t\t\t\t\tinner join t_user_org_corp uoc on u.user_id=uoc.user_id and uoc.is_employee=1 and u.`status`=1 and u.political_type in (1,5) and uoc.region_id=19 \n" +
            "\t\t\t\t\tinner join t_organization o on o.organization_id=uoc.organization_id and  o.`status`=1 and o.region_id=19 and  o.organization_id not in(1294,5,2409,2417,1222,1255)\n" +
            "\t\t\t\t\tinner join t_organization o1 on o.owner_id=o1.organization_id \n" +
            "\t\t\t\t)t2\n" +
            "\t\t)t3 \n" +
            "\t\tLEFT JOIN\n" +
            "\t\t(\n" +
            "\t\t\tselect user_id,DATE_FORMAT(create_time,'%Y-%m') as date from \n" +
            "\t\t\tt_revisit_result where type=#{type}  " +
            " GROUP BY user_id,DATE_FORMAT(create_time,'%Y-%m')\n" +
            "\t\t)t4 on t3.user_id =t4.user_id and t3.date_month=t4.date GROUP BY owner_id\n" +
            "\t\t)t5 where owner_id is not null  order by gg desc\n" +
            ") u, (SELECT @rank := 0, @last_score := NULL) r"+
            "</script>")
    List<RevisitRankVo> revisitOrgRankByOwnerId(@Param(value = "type") Integer type,
                                                @Param(value = "currentMonth")String currentMonth,
                                                @Param(value = "year")Integer year,
                                                @Param(value = "listMonth") List<Integer> listMonth);


    @Select("<script>" +
            "SELECT COUNT(1) FROM\n" +
            "(\n" +
            "\tselect DATE_FORMAT(create_time,'%Y-%m-%d')  from t_revisit_result WHERE user_id =#{userId} AND type=#{type}\n" +
            "<if test=\"startTime != null and startTime != ''\"> and DATE_FORMAT(create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startTime}\n" +
            "</if>"+
            "<if test=\"endTime != null and endTime != ''\"> and DATE_FORMAT(create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}\n" +
            "</if>"+
            "and region_id = #{regionId}"+
            "\tGROUP BY DATE_FORMAT(create_time,'%Y-%m-%d')\n" +
            ") as L"+
            "</script>")
    Integer staRevisitDays(@Param(value = "type") Integer type,
                           @Param(value = "userId") Long userId,
                           @Param("startTime") String startTime,
                           @Param("endTime") String endTime,
                           @Param("regionId") Long regionId);


    /**
     *
     * @param type 1.入党誓词浏览 2.入党志愿书浏览
     * @param quarterly 20231  20232 20233 20234
     * @param orgId 组织Id
     * @param regionId 区域Id
     * @return
     */
    @Select("""
              SELECT  DISTINCT user_id  FROM t_revisit_result\s
              WHERE region_id=#{regionId} and quarterly=#{quarterly} and org_id=#{orgId} and type=#{type}
            """)
    List<Long> getRevisitByQuarterlyAndOrgId(@Param(value = "type") Integer type,
                                          @Param("quarterly") Integer quarterly,
                                          @Param("orgId") Long orgId,
                                          @Param("regionId") Long regionId);



}