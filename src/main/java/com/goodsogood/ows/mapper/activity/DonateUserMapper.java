package com.goodsogood.ows.mapper.activity;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.activity.DonateUserEntity;
import com.goodsogood.ows.model.vo.activity.Donate;
import com.goodsogood.ows.model.vo.rank.OrgJoinTotalVO;
import com.goodsogood.ows.model.vo.rank.UserJoinTotalVO;
import com.goodsogood.ows.model.vo.score.JoinVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/8
 */
@Repository
@Mapper
public interface DonateUserMapper extends MyMapper<DonateUserEntity> {

    String JOIN_DONATE_USER_TOTAL_SQL_TEMPLATE = "SELECT\n" +
            " count(0) joinTotal,\n" +
            " ${orgId} AS orgId,\n" +
            " DATE_FORMAT(c.donate_time,'%Y-%m') dateMonth \n" +
            "FROM (\n" +
            " SELECT\n" +
            "  a.user_id,\n" +
            "  a.donate_time\n" +
            " FROM\n" +
            "  t_donate_user a\n" +
            " WHERE\n" +
            "  a.activity_id = 82\n" +
            "  AND a.donate_time >= '${startTime} 00:00:00' \n" +
            "  AND a.donate_time <= '${endTime} 23:59:59'\n" +
            "  AND a.user_id IN (${userId}) \n" +
            " GROUP BY a.user_id\n" +
            ") c";

    @Select(" select tdu.user_id tempUserId,tdu.donate_user_id donateUserId,tdu.donate_num donateNum,tdu.donate_time donateTime,ta.activity_id activityId,ta.title activityName,ta.region_id regionId\n" +
            "from t_donate_user as tdu force index(`donate_time`) \n" +
            "LEFT JOIN t_activity as ta on tdu.activity_id=ta.activity_id\n" +
            "where tdu.donate_time between #{startDate} and #{endDate} " +
            " order by tdu.user_id limit ${skip},${limitSize} ")
    List<Donate> findDonateUsers(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("limitSize") Integer limitSize, @Param("skip") Integer skip);

    /**
     * 本年捐赠积分（一元捐） - 工委
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param activityId 活动id
     * @param userIds    用户id
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            "sum(donate_num) total\n" +
            "FROM\n" +
            "t_donate_user \n" +
            "WHERE\n" +
            "activity_id = #{activityId}\n" +
            "AND donate_time <![CDATA[ >= ]]> '${startTime} 00:00:00'\n" +
            "AND donate_time <![CDATA[ <= ]]> '${endTime} 23:59:59'\n" +
            "AND user_id in (${userIds})" +
            "</script>")
    Integer donateScore(@Param("startTime") String startTime,
                        @Param("endTime") String endTime,
                        @Param("activityId") Long activityId,
                        @Param("userIds") String userIds);

    /**
     * 累计捐赠积分（一元捐） - 党支部
     *
     * @param userId     用户id
     * @param endTime    结束时间
     * @param activityId 活动id
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " sum( u.donate_num ) score, \n" +
            " u.user_id userId \n" +
            "FROM\n" +
            " t_donate_user u\n" +
            "WHERE\n" +
            " u.activity_id = #{activityId} \n" +
            " AND u.donate_time <![CDATA[ <= ]]> '${endTime} 00:00:00' \n" +
            " AND u.user_id IN (${userId}) " +
            " GROUP BY u.user_id " +
            "</script>")
    List<JoinVO> donateScoreUser(@Param("userId") String userId,
                                 @Param("endTime") String endTime,
                                 @Param("activityId") Long activityId);

    /**
     * 获取某个组织及其下级组织在某月的捐赠用户总数
     *
     * @param sql
     * @return
     */
    @Select("<script>" +
            "${sql}" +
            "</script>")
    List<OrgJoinTotalVO> joinDonateUserTotal(@Param("sql") String sql);

    /**
     * 用户在某月参加过积分捐赠次数
     *
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " count( 0 ) joinTotal, \n" +
            " user_id userId \n" +
            "FROM\n" +
            " t_donate_user \n" +
            "WHERE\n" +
            " activity_id = 82 \n" +
            " AND user_id IN (${userId}) \n" +
            " AND donate_time <![CDATA[ >= ]]> '${startTime} 00:00:00' \n" +
            " AND donate_time <![CDATA[ <= ]]> '${endTime} 23:59:59'" +
            " GROUP BY user_id " +
            "</script>")
    List<UserJoinTotalVO> joinDonateTotal(@Param("userId") String userId,
                                          @Param("startTime") String startTime,
                                          @Param("endTime") String endTime);
}
