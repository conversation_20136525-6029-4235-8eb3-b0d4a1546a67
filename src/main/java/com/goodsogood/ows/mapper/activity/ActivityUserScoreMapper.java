package com.goodsogood.ows.mapper.activity;

import com.goodsogood.ows.model.vo.ScoreResult;
import lombok.Builder;
import lombok.Data;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 计算评分
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface ActivityUserScoreMapper {

  /**
   * 用户参加互动得分
   *
   * @param form 查询添加
   * @return 获取分值
   */
  @Select(
      "<script> "
          + "<if test =\"form != null and form.size() > 0 \">"
          + "  <foreach collection=\"form\" index=\"index\" item=\"item\" open=\"\" separator=\" UNION ALL \" close=\"\">\n"
          // + "-- 参与过即得分"
          + "    SELECT  concat(b.user_id,'-',#{item.currentMonth}) as k,if(count(*)>0,#{item.score},#{item.deductScore}) as score FROM t_activity a,t_activity_user b"
          + "    WHERE a.activity_id = b.activity_id"
          + "    AND `status`=1 AND region_id=#{item.regionId}"
          + "    AND type IN (${item.types})"
          + "    AND DATE_FORMAT(b.create_time,'%Y-%m') = #{item.currentMonth} "
          + "    <if test =\"item.userIdSet != null and item.userIdSet.size() > 0 \">"
          + "     and b.user_id in "
          + "      <foreach collection=\"item.userIdSet\" index=\"iindex\" item=\"iitem\" open=\"(\" separator=\",\" close=\")\">\n"
          + "                 #{iitem}\n"
          + "      </foreach>"
          + "    </if> "
          + "   GROUP BY b.user_id "
          + "  </foreach>"
          + "</if> "
          + " </script>")
  List<ScoreResult> userActivityScore(@Param("form") List<UserActivityScoreForm> form);

  /** 用户参加互动得分查询条件 */
  @Data
  @Builder
  class UserActivityScoreForm {
    /** 分值 */
    private Double score;
    /** 扣分值 */
    private Double deductScore;

    private Set<Long> userIdSet;
    private Long regionId;
    // 统计的互动类型 逗号分割
    private String types;

    // 当前月份 YYYY-MM
    private String currentMonth;
  }
}
