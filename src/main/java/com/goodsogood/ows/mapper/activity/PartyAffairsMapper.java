package com.goodsogood.ows.mapper.activity;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.activity.PartyAffairsEntity;
import com.goodsogood.ows.model.vo.activity.PartyAffairs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-11-19 16:47
 **/
@Repository
@Mapper
public interface PartyAffairsMapper extends MyMapper<PartyAffairsEntity> {
//
//    /**
//     * 公示区列表 包括全市发布都可以看到
//     * @return
//     */
//    @Select(" <script> " +
//            "SELECT CASE type\n" +
//            " WHEN 1 THEN '公示公告'\n" +
//            " WHEN 2 THEN  '单位动态'\n" +
//            " WHEN 3 THEN  '计划总结'\n" +
//            " WHEN 4 THEN  '领导分工'\n" +
//            " WHEN 5 THEN  '党费交纳'\n" +
//            " ELSE '其它'\n" +
//            " END `type`,title FROM t_party_affairs as a " +
//            " WHERE 1=1  and status =1 " +
//            " and ( a.org_level LIKE CONCAT( '%-', ${orgId} , '-%' ) or a.org_id=#{orgId} or scope=2 )" +
//            " order by create_time desc limit 5"+
//            " </script>")
//    List<PartyAffairs> showList11(@Param(value = "orgId") Long orgId);


    /**
     * 公示区列表 包括全市发布都可以看到
     * @return
     */
    @Select(" <script> " +
            "SELECT CASE type\n" +
            " WHEN 1 THEN '公示公告'\n" +
            " WHEN 2 THEN  '单位动态'\n" +
            " WHEN 3 THEN  '计划总结'\n" +
            " WHEN 4 THEN  '领导分工'\n" +
            " WHEN 5 THEN  '党费交纳'\n" +
            " ELSE '其它'\n" +
            " END `type`,title"+
            " FROM t_party_affairs as a " +
            " WHERE 1=1  and status =1 and start_time <![CDATA[ <= ]]> now() and end_time <![CDATA[ >= ]]> now() " +
            " and a.org_Id not in (${excludeOrgIds})"+
            " and (scope=2 or  ( a.org_level LIKE CONCAT( '%-', #{orgId} , '-%' ) or a.org_id=#{orgId} and scope=1 )) "+
            " order by a.create_time desc limit 5"+
            " </script>")
    List<PartyAffairs> showList( @Param(value = "orgId") Long orgId,
                                       @Param(value = "excludeOrgIds") String excludeOrgIds);


    /**
     * 公示区列表 包括全市发布都可以看到
     * @return
     */
    @Select(" <script> " +
            "SELECT CASE type\n" +
            " WHEN 1 THEN '公示公告'\n" +
            " WHEN 2 THEN  '单位动态'\n" +
            " WHEN 3 THEN  '计划总结'\n" +
            " WHEN 4 THEN  '领导分工'\n" +
            " WHEN 5 THEN  '党费交纳'\n" +
            " ELSE '其它'\n" +
            " END `type`,title"+
            " from t_party_affairs as a " +
            " WHERE 1=1  and status =1 and start_time <![CDATA[ <= ]]> now() and end_time <![CDATA[ >= ]]> now() " +
            " and (scope=2 or  ( a.org_id=#{orgId} and scope=1 )) "+
            " and a.org_Id not in (${excludeOrgIds})"+
            " order by a.create_time desc limit 5"+
            " </script>")
    List<PartyAffairs> showListSpecial(@Param(value = "orgId") Long orgId,
                                             @Param(value = "excludeOrgIds") String excludeOrgIds);


}