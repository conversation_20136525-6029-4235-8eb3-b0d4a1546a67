package com.goodsogood.ows.mapper.activity

import com.goodsogood.ows.mapper.MyMapper
import com.goodsogood.ows.model.db.activity.ActivityEntity
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * @date 2024/2/23
 * @description class DataVActivityMapper
 */
@Mapper
interface DataVActivityMapper : MyMapper<ActivityEntity> {
    /**
     * 获取党务公开分类汇总数据
     * 由于党务公开的数据量太少了，不排除测试组织了
     *
     * -- 1.公示公告 2.单位动态 3.计划总结 4.领导分工 5、党费交纳 6.其它 7.发展党员 8.党费公开
     */
    @Select(
        """
        SELECT '支委分工' AS `key`, COUNT(CASE WHEN type = 4 THEN 1 END) AS `value`
        FROM t_party_affairs 
        WHERE `status` = 1
        UNION ALL
        SELECT '党费收交' AS `key`, COUNT(CASE WHEN type IN (5, 8) THEN 1 END) AS `value`
        FROM t_party_affairs 
        WHERE `status` = 1
        UNION ALL
        SELECT '发展党员' AS `key`, COUNT(CASE WHEN type = 7 THEN 1 END) AS `value`
        FROM t_party_affairs 
        WHERE `status` = 1
        UNION ALL
        SELECT '计划和总结' AS `key`, COUNT(CASE WHEN type = 3 THEN 1 END) AS `value`
        FROM t_party_affairs 
        WHERE `status` = 1;
        """
    )
    fun getPartyAffairsOpenData(): List<Pair<String, Int>>
}