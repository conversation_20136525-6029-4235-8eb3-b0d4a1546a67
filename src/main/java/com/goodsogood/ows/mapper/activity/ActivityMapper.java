package com.goodsogood.ows.mapper.activity;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.activity.ActivityEntity;
import com.goodsogood.ows.model.mongodb.ActivityInfo;
import com.goodsogood.ows.model.mongodb.dss.ActivityJoinList;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-11-19 14:00
 **/
@Repository
@Mapper
public interface ActivityMapper extends MyMapper<ActivityEntity> {

    /**
     * 查询所有投票活动 包括已经结束和正在参与活动
     * 根据查询天数进行过滤
     * 比如今天20191121过滤20191111以后这一段时间已经结束和正在参与的活动
//     * @param days
     * @return
     */
    @Select("SELECT a.activity_id as activityId,b.region_id as regionId,max(a.activity_user_id) as activityUserId FROM t_activity_user as a\n" +
            "INNER JOIN t_activity as b \n" +
            "ON a.activity_id=b.activity_id \n" +
//             "WHERE b.isdel=0 AND TIMESTAMPDIFF(DAY,DATE_FORMAT(b.end_time,'%Y-%m-%d'), DATE_FORMAT(now(), '%Y-%m-%d'))<#{days}\n" +
            "WHERE b.isdel=0 AND (b.end_time >= #{startTime} or b.end_time is NULL ) and (b.start_time <= #{endTime} or b.start_time is NULL ) \n" +
            "GROUP BY a.activity_id,b.region_id")
    List<ActivityInfo> getParticipantActivity(@Param("startTime") String startTime,@Param("endTime") String endTime);
    //     List<ActivityInfo> getParticipantActivity(Integer days);

    /**
     *  查询每个活动下面参与的人数
      * @param activityId
     * @return
     */
    @Select("SELECT \n" +
            "a.activity_id ,a.organization_id ,a.title,a.create_time,\n" +
            "a.organization_name,a.type,\n" +
            "CASE\n" +
            "\t\tWHEN type = 1 THEN\n" +
            "\t\t'投票' \n" +
            "\t\tWHEN type = 2 THEN\n" +
            "\t\t'问卷调查' \n" +
            "\t\tWHEN type = 3 THEN\n" +
            "\t\t'有奖竞答' \n" +
            "\t\tWHEN type = 4 THEN\n" +
            "\t\t'线下活动' \n" +
            "\t\tWHEN type = 5 THEN\n" +
            "\t\t'积分捐赠' \n" +
            "\t\tWHEN type = 6 THEN\n" +
            "\t\t'调查评价' \n" +
            "\tEND AS typeName\n" +
            "FROM t_activity as a\n" +
            "WHERE a.activity_id=#{activityId}")
    @Results({
            @Result(property = "activityId", column = "activity_id"),
            @Result(property = "orgId", column = "organization_id"),
            @Result(property = "activityName", column = "title"),
            @Result(property = "activityType", column = "type"),
            @Result(property = "activityTypeName", column = "typeName"),
            @Result(property = "activityCreatTime", column = "create_time"),
            @Result(property = "participantUsers", column = "activity_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.activity.ActivityMapper.getParticipantUsersByActivityId")),
    })
    ActivityInfo getActivityInfo(@Param(value = "activityId") Long activityId);



    @Select( "\tSELECT a.user_id,a.create_time ,\n" +
            "\tDATE_FORMAT(a.create_time, '%Y%m') as participation_date \n" +
            "\tFROM t_activity_user as a  \n" +
            "\tWHERE a.activity_id=#{activityId} \n"
            )
    @Results({
            @Result(property = "userId", column = "user_id"),
            @Result(property = "participationDate", column = "participation_date"),
            @Result(property = "participationTime", column = "create_time")
    })
    List<ActivityInfo.ParticipantUsers> getParticipantUsersByActivityId(@Param(value = "activityId") Long activityId);


    /**
     * 根据活动统计该活动的中奖用户
     * @param activityId
     * @return
     */
    @Select("SELECT (SELECT if(sum(num) IS NULL,0,SUM(num)) FROM t_winning_log WHERE activity_id=#{activityId} and winner=userId AND type=1) as score ,\n" +
            " (SELECT if(sum(num) IS NULL,0,SUM(num)) FROM t_winning_log WHERE activity_id=#{activityId} and winner=userId and type=2) as real_things,\n" +
            " (SELECT GROUP_CONCAT(DISTINCT(prize_name)) FROM t_winning_log WHERE activity_id=#{activityId} and winner=userId and type=2) as real_things_detail,\n" +
            " (SELECT if(sum(num) IS NULL,0,SUM(num)) FROM t_winning_log WHERE activity_id=#{activityId} and winner=userId and type=3) as cash,\n" +
            " userId\n" +
            " FROM\n" +
            "\t( \n" +
            "\t\tSELECT winner as userId   FROM t_winning_log \n" +
            "\t\tWHERE activity_id=#{activityId}\n" +
            "\t\tGROUP BY winner\n" +
            " ) as L")
    @Results({
            @Result(property = "userId", column = "userId"),
            @Result(property = "score", column = "score"),
            @Result(property = "realThing", column = "real_things"),
            @Result(property = "realThingsDetail", column = "real_things_detail"),
            @Result(property = "cash", column = "cash"),
    })
    List<ActivityInfo.WinUsers> getGetWinUserByActivityId(@Param(value = "activityId") Long activityId);

  @Select(
      "SELECT DATE_FORMAT(b.create_time,'%Y-%m-%d') as joinDate,a.title as activityName, \n"
          + "CASE a.type\n"
          + "   WHEN 1 THEN '投票'\n"
          + "   WHEN 2 THEN '问卷调查'\n"
          + "   WHEN 3 THEN '有奖竞答'\n"
          + "   WHEN 4 THEN '线下活动'\n"
          + "   WHEN 5 THEN '积分捐赠'\n"
          + "   WHEN 6 THEN '上评下'\n"
          + "  ELSE '' END AS typeName FROM t_activity a,t_activity_user b\n"
          + "WHERE a.activity_id=b.activity_id AND b.user_id=#{userId} and  a.region_id=#{regionId} and a.type in (${types})\n"
          + "AND YEAR(b.create_time)=#{year} AND a.isdel=0 ORDER BY b.create_time DESC\n")
  List<ActivityJoinList> activityJoinListByUserId(
      @Param(value = "regionId") Long regionId,
      @Param(value = "userId") Long userId,
      @Param(value = "year") Integer year,@Param(value = "types") String types);
}