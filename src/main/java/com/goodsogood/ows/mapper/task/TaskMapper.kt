package com.goodsogood.ows.mapper.task

import com.goodsogood.ows.mapper.MyMapper
import com.goodsogood.ows.model.db.task.*
import com.goodsogood.ows.model.vo.EcpRecordVo
import com.goodsogood.ows.model.vo.EcpTaskListForm
import com.goodsogood.ows.model.vo.TaskFrom
import com.goodsogood.ows.model.vo.eval.v2.ExamineTaskFrom
import com.goodsogood.ows.model.vo.user.DevelopForm
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
@Mapper
interface TaskMapper : MyMapper<TaskEntity> {

    @Select(
        "select t.taskClass as item, t.num as total from \n" +
                "(select task_class taskClass, count(*) num \n" +
                "from t_task where is_del = 0 and task_class is not null and is_third_type is null \n" +
                "and create_time like concat(year(now()),'%') \n" +
                "group by task_class) t "
    )
    fun taskTotal(): MutableList<DevelopForm>?

    @Select(
        "select t.type as item, t.num as total from (\n" +
                "select 1 as type,count(*) num from t_task where \n" +
                " is_del = 0 and task_class is not null and is_third_type is null and \n" +
                "begin_time > date_format(now(),'%Y-01-01 00:00:00') and end_time < now() \n" +
                "union\n" +
                "select 2 as type, count(*) num from t_task where\n" +
                "is_del = 0 and task_class is not null and is_third_type is null and \n" +
                "begin_time > date_format(now(),'%Y-01-01 00:00:00') and end_time > now()\n" +
                ") t"
    )
    fun taskDone(): MutableList<DevelopForm>?


    /**
     * 获取accessOrgId的执行情况（operate_status）
     * @param taskId 任务id，可以为空
     * @param examine 是否是考核任务，可以为空(未空就不验证考核任务)
     * @param createOrgId 任务发布组织，可以为空
     * @param accessOrgId 目标接收组织，可以为空
     * @param beginTime 任务开始时间，可以为空
     * @param endTime 任务结束时间，可以为空
     */
    @Select(
        """
        <script>
    SELECT
        t.task_id as taskId,
        t.task_title as taskTitle,
        t.create_org as createOrg,
        ta.access_org_id as accessOrgId,
        ta.operate_status as operateStatus
    FROM
        t_task AS t
        LEFT JOIN t_task_sub AS ts ON t.task_id = ts.task_id
        LEFT JOIN t_task_access ta ON t.task_id = ta.task_id 
    WHERE
        t.is_del = 0 
        AND t.task_class IS NOT NULL 
        AND t.is_draft = 0 
        AND t.is_third_type IS NULL 
        <if test="taskId != null">
        AND t.task_id = #{taskId}
        </if>
        <if test="createOrgId != null">
        AND t.create_org = #{createOrgId}
        </if>
        <if test="examine != null">
        AND ts.is_examine = #{examine}
        </if>
        <if test="beginTime != null and beginTime != ''">
        AND t.begin_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
        AND t.end_time &lt;= #{endTime} 
        </if>
        <if test="accessOrgId != null">
        AND ta.access_org_id = #{accessOrgId}
        </if>
        <if test="accessOrgId == null">
        AND ta.access_org_id IS NOT NULL
        </if>
    GROUP BY
        t.task_id,
        t.task_title,
        ta.access_org_id,
        ta.operate_status
        </script>
    """
    )
    fun getExamineOperateStatus(
        @Param("taskId") taskId: Long?,
        @Param("checkExamine") examine: Int?,
        @Param("createOrgId") createOrgId: Long?,
        @Param("accessOrgId") accessOrgId: Long?,
        @Param("beginTime") beginTime: String?,
        @Param("endTime") endTime: String?,
    ): List<ExamineTaskFrom>
}

@Repository
@Mapper
interface TaskAccessMapper : MyMapper<TaskAccessEntity> {

    /**
     * taskClass: 13-党建任务、14-创新任务、15-业务任务
     * 查询党员的排名
     */
    @Select(
        "SELECT\n" +
                "\tcommentNum,\n" +
                "\tuserId \n" +
                "FROM\n" +
                "\t(\n" +
                "\tSELECT CONVERT\n" +
                "\t\t( SUM( t2.assess_score / 20 ), SIGNED ) commentNum,\n" +
                "\t\tt2.access_user_id userId \n" +
                "\tFROM\n" +
                "\t\tt_task t1\n" +
                "\t\tLEFT JOIN t_task_access t2 ON t1.task_id = t2.task_id \n" +
                "\tWHERE\n" +
                "\t\tt2.access_range = 2 \n" +
                "\t\tAND t1.is_del != 1 \n" +
                "\t\tAND t2.operate_status = 8 \n" +
                "\t\tAND t2.is_del != 1 \n" +
                "\t\tAND t1.task_class = #{taskClass} \n" +
                "\t\tAND t2.create_time LIKE CONCAT( '%',\${dateMonth}, '%' ) \n" +
                "\tGROUP BY\n" +
                "\t\tt2.access_user_id \n" +
                "\tORDER BY\n" +
                "\t\tcommentNum \n" +
                "\t) t HAVING 1 = 1 \n" +
                "\tAND t.commentNum > (\n" +
                "\tSELECT CONVERT\n" +
                "\t\t( SUM( t2.assess_score / 20 ), SIGNED ) commentNum \n" +
                "\tFROM\n" +
                "\t\tt_task t1\n" +
                "\t\tLEFT JOIN t_task_access t2 ON t1.task_id = t2.task_id \n" +
                "\tWHERE\n" +
                "\t\tt2.access_range = 2 \n" +
                "\t\tAND t1.is_del != 1 \n" +
                "\t\tAND t2.operate_status = 8 \n" +
                "\t\tAND t2.is_del != 1 \n" +
                "\t\tAND t1.task_class = #{taskClass} \n" +
                "\t\tAND t2.access_user_id = #{userId} \n" +
                "\t\tAND t2.create_time LIKE CONCAT( '%',\${dateMonth}, '%' ) \n" +
                "\tGROUP BY\n" +
                "\t\tt2.access_user_id \n" +
                "ORDER BY\n" +
                "\tcommentNum)"
    )
    fun partyConstructionTask(
        @Param("taskClass") taskClass: Int,
        @Param("userId") userId: Long,
        @Param("dateMonth") dateMonth: String,
    ): MutableList<TaskFrom>

    /**
     * 确认党员是否在排名中
     */
    @Select(
        "SELECT\n" +
                "\tcount(1)\n" +
                "FROM\n" +
                "\t(\n" +
                "\tSELECT CONVERT\n" +
                "\t\t( SUM( t2.assess_score / 20 ), SIGNED ) commentNum,\n" +
                "\t\tt2.access_user_id userId \n" +
                "\tFROM\n" +
                "\t\tt_task t1\n" +
                "\t\tLEFT JOIN t_task_access t2 ON t1.task_id = t2.task_id \n" +
                "\tWHERE\n" +
                "\t\tt2.access_range = 2 \n" +
                "\t\tAND t1.is_del != 1 \n" +
                "\t\tAND t2.operate_status = 8 \n" +
                "\t\tAND t2.is_del != 1 \n" +
                "\t\tAND t1.task_class = #{taskClass}\n" +
                "\tAND t2.access_user_id = #{userId}\t\n" +
                "\tAND t2.create_time LIKE CONCAT( '%',\${dateMonth}, '%' ) \n" +
                "\tGROUP BY\n" +
                "\t\tt2.access_user_id HAVING commentNum is not null \n" +
                "\tORDER BY\n" +
                "\t\tcommentNum \n" +
                "\t) t "
    )
    fun findUserExist(
        @Param("taskClass") taskClass: Int?,
        @Param("userId") userId: Long?,
        @Param("dateMonth") dateMonth: String?,
    ): Int


    /**
     * 获取用户获取的五星次数
     */
    @Select(
        "SELECT\n" +
                "\tcount( 1 ) \n" +
                "FROM\n" +
                "\tt_task t1\n" +
                "\tLEFT JOIN t_task_access t2 ON t1.task_id = t2.task_id \n" +
                "WHERE\n" +
                "\tt2.access_range = 2 \n" +
                "\tAND t1.is_del != 1 \n" +
                "\tAND t2.operate_status = 8 \n" +
                "\tAND t2.is_del != 1 \n" +
                "\tAND t1.task_class = #{taskClass} \n" +
                "\tAND t2.access_user_id = #{userId} \n" +
                "\tAND t2.create_time LIKE CONCAT( '%',\${dateMonth}, '%' ) \n" +
                "\tAND t2.assess_score = 100"
    )
    fun getNumberOfStars(
        @Param("taskClass") taskClass: Int?,
        @Param("userId") userId: Long?,
        @Param("dateMonth") dateMonth: String?,
    ): Int?

    /**
     * 查询主动报名参与云上党支部任务的党员排名
     */
    @Select(
        "select * from (select \n" +
                "t2.access_user_id userId,\n" +
                "count(t2.access_user_id) `rank`\n" +
                "from t_task t1 \n" +
                "left join t_task_access t2 on t1.task_id = t2.task_id \n" +
                "left join t_task_third t3 on t2.task_id = t3.task_id \n" +
                "where t1.task_class = 26 \n" +
                "and t2.operate_status = 8\n" +
                "and t3.source_id like \"ecp-%\" \n" +
                "and t2.access_range = 2\n" +
                "and t1.is_del != 1\n" +
                "and t2.is_del != 1\n" +
                "and t2.create_time like CONCAT( '%',\${dateMonth}, '%' ) " +
                "GROUP BY t2.access_user_id ) r where r.`rank` > (select \n" +
                "IF(count(t2.access_user_id) = 0 ,null,count(t2.access_user_id)) `rank`\n" +
                "from t_task t1 \n" +
                "left join t_task_access t2 on t1.task_id = t2.task_id \n" +
                "left join t_task_third t3 on t2.task_id = t3.task_id \n" +
                "where t1.task_class = 26 \n" +
                "and t2.operate_status = 8\n" +
                "and t3.source_id like \"ecp-%\" \n" +
                "and t2.access_range = 2\n" +
                "and t1.is_del != 1\n" +
                "and t2.is_del != 1\n" +
                "and t2.create_time like CONCAT( '%',\${dateMonth}, '%' )\n" +
                "and t2.access_user_id = #{userId})"
    )
    fun ecpUserTask(
        @Param("dateMonth") dateMonth: String,
        @Param("userId") userId: Long,
    ): MutableList<TaskFrom>

    /**
     * 查询该用户是否有主动报名参与云上党支部任务排名
     */
    @Select(
        "select `rank` from (select \n" +
                "t2.access_user_id userId,\n" +
                "count(t2.access_user_id) `rank`\n" +
                "from t_task t1 \n" +
                "left join t_task_access t2 on t1.task_id = t2.task_id \n" +
                "left join t_task_third t3 on t2.task_id = t3.task_id \n" +
                "where t1.task_class = 26 \n" +
                "and t2.operate_status = 8\n" +
                "and t3.source_id like \"ecp-%\" \n" +
                "and t2.access_range = 2\n" +
                "and t1.is_del != 1\n" +
                "and t2.is_del != 1\n" +
                "and t2.create_time like CONCAT( '%',\${dateMonth}, '%' )\n" +
                "group by t2.access_user_id ) r \n" +
                "where r.userId = #{userId}"
    )
    fun findEcpTaskExist(
        @Param("dateMonth") dateMonth: String,
        @Param("userId") userId: Long,
    ): Int?


    /**
     * 用户参与统计
     */
    @Select(
        "<script>" +
                "SELECT COUNT(1) FROM t_task as a\n" +
                " LEFT JOIN t_task_access as b\n" +
                " on a.task_id=b.task_id  WHERE task_class=27  and operate_status IN(3,7,8) and a.is_del=0 and a.is_draft=0" +
                " and access_user_id in " +
                " <foreach collection=\"userIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
                " #{item} " +
                " </foreach> " +
                "</script>"
    )
    fun ecpUserTaskSta(
        @Param("userIds") userIds: MutableList<Long>,
    ): Int?


//    /**
//     * 我的云上任务列表
//     */
//    @Select("<script>" +
//            "SELECT a.task_id as taskId, task_title as taskName,\n" +
//            "(\n" +
//            "\tSELECT COUNT(1) FROM t_task as c\n" +
//            "\tLEFT JOIN t_task_access as d\n" +
//            "\ton c.task_id=d.task_id WHERE task_class=27 \n" +
//            "\tand c.task_id=a.task_id and operate_status IN(3,7,8)\n" +
//            ") as number\n" +
//            "FROM t_task as a\n" +
//            "LEFT JOIN t_task_access as b\n" +
//            "on a.task_id=b.task_id WHERE task_class=27 " +
//            " and access_user_id =#{userId} and operate_status IN " +
//            " <foreach collection=\"operateStatus\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
//            " #{item} " +
//            " </foreach> " +
//            "</script>")
//    fun ecpTaskRecordList(
//            @Param("userId") userId: Long,
//            @Param("operateStatus") operateStatus: MutableList<Int>,
//    ): MutableList<EcpTaskListForm>?

    /**
     * 我的云上任务列表
     */
    @Select(
        "<script>" +
                "SELECT DISTINCT if(f.user_id is not null and d.operateStatus = 3, 1,0) verify,d.*  FROM\n" +
                "(\n" +
                "\tSELECT\n" +
                "\t\t\tb.access_user_id,\n" +
                "\t\t\ta.task_id as taskId,\n" +
                " DATE_FORMAT(a.begin_time,'%Y.%m.%d') as beginTime," +
                " DATE_FORMAT(a.end_time,'%Y.%m.%d') as endTime," +
                "\t\t\ttask_title as taskName,\n" +
                "\t\t\tb.operate_status as operateStatus,\n" +
                "\t\t\tb.access_range as accessRange,\n" +
                " b.task_access_id as taskAccessId," +
                "if(b.access_range = 2 or b.access_range = 4,b.access_user_id,b.access_org_id) accessId ," +
                "\t\t\ta.`status`,\n" +
                "\t\t\t( SELECT\n" +
                "\t\t\t\t\tCOUNT(1) \n" +
                "\t\t\tFROM\n" +
                "\t\t\t\t\tt_task as c \n" +
                "\t\t\tLEFT JOIN\n" +
                "\t\t\t\t\tt_task_access as d \n" +
                "\t\t\t\t\t\t\ton c.task_id=d.task_id \n" +
                "\t\t\tWHERE\n" +
                "\t\t\t\t\ttask_class=27 \n" +
                "\t\t\t\t\tand c.task_id=a.task_id \n" +
                "\t\t\t\t\tand operate_status IN(\n" +
                "\t\t\t\t\t\t\t3,7,8\n" +
                "\t\t\t\t\t) ) as number \n" +
                "\tFROM\n" +
                "\t\t\tt_task as a \n" +
                "\tLEFT JOIN\n" +
                "\t\t\tt_task_access as b \n" +
                "\t\t\t\t\ton a.task_id=b.task_id \n" +
                "\tWHERE\n" +
                "\t\t\ttask_class=27 \n" +
                " and a.is_del=0 and a.is_draft=0" +
                "\t\t\tand access_user_id =#{userId} \n" +
                "\t\t\tand operate_status IN " +
                " <foreach collection=\"operateStatus\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
                " #{item} " +
                " </foreach> " +
                " order by a.create_time desc" +
                ")as d LEFT JOIN t_task_verify as f\n" +
                "on d.taskId=f.task_id  " +
                "</script>"
    )
    fun ecpTaskRecordList(
        @Param("userId") userId: Long,
        @Param("operateStatus") operateStatus: MutableList<Int>,
    ): MutableList<EcpTaskListForm>?


//
//    @Select("SELECT department ,user_name as `name`,type,content,content_detail as " +
//            "contentDetail FROM t_ecp_record where owner_id=#{userOwnerId} order by create_time desc limit 20")
//    fun unitRecord(@Param("userOwnerId") userOwnerId: Long) : MutableList<EcpRecordVo>?

//    @Select("SELECT DISTINCT  a.department ,user_name as `name`,type,content,content_detail as contentDetail," +
//            " a.ecp_org_id as ecpOrgId,a.task_id as taskId " +
//            " FROM t_ecp_record as a\n" +
//            " LEFT JOIN t_organization as b\n" +
//            " on a.org_id=b.organization_id\n" +
//            " where a.owner_id=#{userOwnerId} and b.`status`=1 order by a.create_time desc ")
//    fun unitRecord(@Param("userOwnerId") userOwnerId: Long) : MutableList<EcpRecordVo>?


    @Select(
        "SELECT IF(taskTitle is NULL ,ecpOrgName,taskTitle) as content,L.* FROM\n" +
                "(\n" +
                "\tSELECT DISTINCT  a.department ,user_name as `name`,type,\n" +
                "\tcontent_detail as contentDetail, \n" +
                "\ta.ecp_org_id as ecpOrgId,a.task_id as taskId ,\n" +
                "\t(SELECT task_title FROM t_task WHERE task_id=a.task_id AND is_del=0 LIMIT 1) as taskTitle,\n" +
                "\t(SELECT ecp_org_name  FROM t_ecp_org WHERE ecp_org_id=a.ecp_org_id and  is_del=0 LIMIT 1) as ecpOrgName\n" +
                "\t FROM t_ecp_record as a\n" +
                "\t LEFT JOIN t_organization as b\n" +
                "\t on a.org_id=b.organization_id\n" +
                "\t where a.owner_id=#{userOwnerId} and b.`status`=1 order by a.create_time desc\n" +
                ") as L \n" +
                "WHERE ecpOrgName is not null or taskTitle is not NULL LIMIT 20 "
    )
    fun unitRecord(@Param("userOwnerId") userOwnerId: Long): MutableList<EcpRecordVo>?

    @Select("SELECT task_title FROM t_task WHERE task_id=#{taskId} and  is_del=0")
    fun getTaskTitle(@Param("taskId") taskId: Long): String?


    @Select("SELECT ecp_org_name  FROM t_ecp_org WHERE ecp_org_id=#{ecpOrgId} and  is_del=0 ")
    fun getEcpOrgName(@Param("ecpOrgId") ecpOrgId: Long): String?

//    @Select("SELECT a.department,b.`name` as unitName ,a.user_name as `name`,a.type," +
//            "a.content,a.content_detail as contentDetail,a.ecp_org_id as ecpOrgId,a.task_id as taskId\n" +
//            " FROM t_ecp_record as a\n" +
//            " INNER JOIN t_organization as b \n" +
//            " on a.owner_id=b.organization_id " +
//            " where b.`status`=1" +
//            " order by a.create_time desc ")
//    fun allRecord(): MutableList<EcpRecordVo> ?

    @Select(
        "SELECT IF(taskTitle is NULL ,ecpOrgName,taskTitle) as content,L.* FROM\n" +
                "(\n" +
                "\tSELECT DISTINCT  a.department ,user_name as `name`,type,b.`name` as unitName,\n" +
                "\tcontent_detail as contentDetail, \n" +
                "\ta.ecp_org_id as ecpOrgId,a.task_id as taskId ,\n" +
                "\t(SELECT task_title FROM t_task WHERE task_id=a.task_id AND is_del=0 LIMIT 1) as taskTitle,\n" +
                "\t(SELECT ecp_org_name  FROM t_ecp_org WHERE ecp_org_id=a.ecp_org_id and  is_del=0 LIMIT 1) as ecpOrgName\n" +
                "\t FROM t_ecp_record as a\n" +
                "\t LEFT JOIN t_organization as b\n" +
                "\t on a.owner_id=b.organization_id\n" +
                "\t where  b.`status`=1 order by a.create_time desc\n" +
                ") as L \n" +
                "WHERE ecpOrgName is not null or taskTitle is not NULL LIMIT 20"
    )
    fun allRecord(): MutableList<EcpRecordVo>?

}

@Repository
@Mapper
interface TaskFileMapper : MyMapper<TaskFileEntity> {
}

@Repository
@Mapper
interface TaskLogMapper : MyMapper<TaskLogEntity> {

    @Select(
        "<script>" +
                "SELECT\n" +
                "\tcount(1) \n" +
                "FROM\n" +
                "\tt_task t\n" +
                "\tJOIN t_task_access ta ON t.task_id = ta.task_id \n" +
                "\tAND ta.is_del = 0\n" +
                "\tJOIN t_task_log tl ON ta.task_access_id = tl.task_access_id \n" +
                "\tAND ta.is_del = 0\n" +
                "\tLEFT JOIN t_task_third tt ON t.task_id = tt.task_id \n" +
                "WHERE\n" +
                "\tt.is_del = 0 \n" +
                "\tAND ta.is_del = 0 \n" +
                "\tAND YEAR ( t.create_time ) = YEAR ( NOW( ) ) \n" +
                "\tAND t.task_class IS NOT NULL \n" +
                "\tAND tt.task_id IS NULL \n" +
                "\tAND t.is_third_type IS NULL \n" +
                "<if test=\"userList != null and userList.size > 0\"> " +
                "\tAND tl.user_id IN \n" +
                " <foreach collection=\"userList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
                " #{item}" +
                " </foreach> " +
                "</if>" +
                "</script>"
    )
    fun attendTotal(@Param("userList") userList: List<Long>): Int?
}

@Repository
@Mapper
interface TaskSubMapper : MyMapper<TaskSubEntity> {
}

@Repository
@Mapper
interface TaskThirdMapper : MyMapper<TaskThirdEntity> {
}

@Repository
@Mapper
interface TaskVerifyMapper : MyMapper<TaskVerifyEntity> {
}