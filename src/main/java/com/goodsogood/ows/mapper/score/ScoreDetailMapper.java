package com.goodsogood.ows.mapper.score;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.score.ScoreDetailEntity;
import com.goodsogood.ows.model.mongodb.ScoreInfo;
import com.goodsogood.ows.model.vo.score.Study;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-12-26
 **/
@Repository
@Mapper
public interface ScoreDetailMapper extends MyMapper<ScoreDetailEntity> {

    @Select(" select tsu.user_id tempUserId,tsd.score,tsd.score_detail_id scoreDetailId,tsd.consume_time consumeTime,tsd.remark,to1.region_id regionId\n" +
            "from (select score,score_detail_id,consume_time,remark,score_user_id,oper_type,score_type,org_id from t_score_detail FORCE INDEX(`consume_time`)  where consume_time between  #{startTime} and #{endTime}) tsd \n" +
            "LEFT JOIN t_score_user tsu on tsd.score_user_id=tsu.score_user_id\n" +
            " left join t_organization to1 on tsd.org_id=to1.organization_id " +
            "where \n" +
//            "tsd.consume_time BETWEEN #{startTime} and #{endTime} and " +
            "tsd.oper_type=0 " +
//            "and tsd.score_type=4 \n" +
            "and tsu.user_id is not null\n" +
//            "ORDER BY tsu.user_id " +
            "limit ${skip},${limitSize}; ")
    List<Study> findStudyInfo(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("limitSize") Integer limitSize, @Param("skip") Integer skip);

    @Select(" select tsu.user_id tempUserId,tsd.score,tsd.score_detail_id scoreDetailId,tsd.consume_time consumeTime,tsd.remark\n" +
            "from (select score,score_detail_id,consume_time,remark,score_user_id,oper_type,score_type from t_score_detail FORCE INDEX(`consume_time`)  where consume_time like concat(#{queryDay},'%')) tsd \n" +
            "LEFT JOIN t_score_user tsu on tsd.score_user_id=tsu.score_user_id\n" +
            "where \n" +
//            "tsd.consume_time BETWEEN #{startTime} and #{endTime} and " +
            "tsd.oper_type=0 " +
//            "and tsd.score_type=4 \n" +
            "and tsu.user_id is not null\n" +
//            "ORDER BY tsu.user_id " +
            "limit ${skip},${limitSize}; ")
    List<Study> findStudyInfo2(@Param("queryDay") String queryDay, @Param("limitSize") Integer limitSize, @Param("skip") Integer skip);

}
