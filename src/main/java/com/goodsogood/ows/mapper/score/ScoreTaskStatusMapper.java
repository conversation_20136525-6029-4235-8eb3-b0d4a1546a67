package com.goodsogood.ows.mapper.score;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.score.ScoreTaskStatusEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Mapper
@Repository
public interface ScoreTaskStatusMapper extends MyMapper<ScoreTaskStatusEntity> {
    @Insert("<script>"+
            "insert ignore into t_score_task_status(task_status_id,task_info_id,object_id,create_time ) values "+
            " <foreach collection=\"list\" index=\"index\" item=\"item\" open=\"\" separator=\",\" close=\"\">\n" +
            " (null,#{item.taskInfoId},#{item.objectId},now())"+
            " </foreach>"+
            "</script>")
    void insertData(@Param("list") List<ScoreTaskStatusEntity> list);
}
