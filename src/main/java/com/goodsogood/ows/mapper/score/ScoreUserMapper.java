package com.goodsogood.ows.mapper.score;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.score.ScoreUserEntity;
import com.goodsogood.ows.model.vo.rank.OrgJoinTotalVO;
import com.goodsogood.ows.model.vo.rank.UserJoinTotalVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-06-11 9:22
 **/
@Repository
@Mapper
public interface ScoreUserMapper extends MyMapper<ScoreUserEntity> {

    String JOIN_ONLINE_STUDY_USER_TOTAL_SQL_TEMPLATE = "SELECT \n" +
            " count(0) joinTotal,\n" +
            " ${orgId} AS orgId,\n" +
            " DATE_FORMAT(b.consume_time,'%Y-%m') dateMonth \n" +
            " FROM (\n" +
            "  SELECT\n" +
            "   a.score_user_id,\n" +
            "  a.consume_time\n" +
            "  FROM\n" +
            "   t_score_detail a\n" +
            "  WHERE\n" +
            "   a.consume_time >= '${startTime} 00:00:00' \n" +
            "   AND a.consume_time <= '${endTime} 23:59:59'\n" +
            "   AND a.app_id = 7\n" +
            "   AND a.org_id = 3\n" +
            "   AND a.score_user_id IN (" +
            " ${scoreUserId} "+
            ")\n" +
            "  GROUP BY a.score_user_id\n" +
            " ) b ";

    /**
     * 获取某个组织及其下级组织在某月的在线学习人数
     *
     * @param sql
     * @return
     */
    @Select("<script>" +
            "${sql}"
            + "</script>")
    List<OrgJoinTotalVO> joinOnlineStudyUserTotal(@Param("sql") String sql);

    /**
     * 用户在某月参加过在线学习次数
     *
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " count(0) joinTotal,\n" +
            " su.user_id userId \n" +
            "FROM\n" +
            " t_score_user su\n" +
            " LEFT JOIN t_score_detail sd ON su.score_user_id = sd.score_user_id \n" +
            "WHERE\n" +
            " su.user_id IN (${userId}) \n" +
            " AND sd.consume_time <![CDATA[ >= ]]> '${startTime} 00:00:00' \n" +
            " AND sd.consume_time <![CDATA[ <= ]]> '${endTime} 23:59:59'" +
            " AND sd.app_id = 7 \n" +
            " AND sd.org_id = 3 " +
            " GROUP BY  su.user_id " +
            "</script>")
    List<UserJoinTotalVO> joinOnlineStudyTotal(@Param("userId") String userId,
                                               @Param("startTime") String startTime,
                                               @Param("endTime") String endTime);

    /**
     * 获取所有积分用户id
     *
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            "\tscore_user_id scoreUserId,\n" +
            "\tuser_id userId\n" +
            "FROM\n" +
            "\tt_score_user \n" +
            "\tWHERE user_id IS NOT NULL" +
            "</script>")
    List<ScoreUserEntity> getAllScoreUserIds();


    /**
     * 统计用户 那个月的积分
     * 1.党建指标 2.业务指标 3.创新指标
     * @return
     */
    @Select("select IF( MAX(score) is NULL,0,score) as score from t_score_user sc \n" +
            "left join (\n" +
            "select score_user_id,ifnull(date_format(consume_time,'%Y%m'),date_format(create_time,'%Y%m'))time," +
            "    sum(score)score from t_score_detail_other\n" +
            "    where parent_score_type=#{type} \n" +
            "    group by score_user_id,ifnull(date_format(consume_time,'%Y%m'),date_format(create_time,'%Y%m'))  \n" +
            ")sd on sc.score_user_id=sd.score_user_id\n" +
            "WHERE user_id=#{userId} and time=#{staMonth}  ")
    Integer getUserByMonth(@Param("type") Integer type,
                           @Param("userId") Long userId,
                           @Param("staMonth") String staMonth);


    /**
     * 统计组织 那个月的积分
     * 1.党建指标 2.业务指标 3.创新指标
     * @return
     */
    @Select(" SELECT IF( MAX(score) is NULL,0,score) as score FROM\n" +
            "(\n" +
            "\tselect score_org_id,ifnull(date_format(consume_time,'%Y%m'),date_format(create_time,'%Y%m'))time,\n" +
            "\tsum(score)score from t_score_org_detail\n" +
            "\twhere parent_score_type=#{type} \n" +
            "\tand \n" +
            "\t(\n" +
            "\t\t\tdate_format(consume_time,'%Y%m') \n" +
            "\t\t\tbetween date_format(date_sub(curdate(),interval 5 month),'%Y%m')\n" +
            "\t\t\tand date_format(date_sub(curdate(),interval 0 month),'%Y%m') or\n" +
            "\t\t\tdate_format(create_time,'%Y%m') between date_format(date_sub(curdate(),interval 6 month),'%Y%m') \n" +
            "\t\t\tand date_format(date_sub(curdate(),interval 0 month),'%Y%m')\n" +
            "\t )\n" +
            "\tgroup by score_org_id,ifnull(date_format(consume_time,'%Y%m'),date_format(create_time,'%Y%m'))\n" +
            ") as L\n" +
            "WHERE score_org_id=#{orgId} and time=#{staMonth} ")
    Integer getOrgScoreByMonth(@Param("type") Integer type,
                               @Param("orgId") Long orgId,
                               @Param("staMonth") String staMonth);



}
