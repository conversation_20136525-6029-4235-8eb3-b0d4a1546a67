package com.goodsogood.ows.mapper.score;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.score.ScoreRuleExplainEntity;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ClassName : ScoreRuleExplainMapper
 * <AUTHOR> tc
 * @Description : 积分规则说明
 */
@Repository
@Mapper
public interface ScoreRuleExplainMapper extends MyMapper<ScoreRuleExplainEntity> {

}
