package com.goodsogood.ows.mapper.score;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.score.PovertyCommodityEntity;
import com.goodsogood.ows.model.vo.score.PovertyCommodity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/** @Author: tc
 * @Description 扶贫商城据库查询
 * @Date 14:39 2019/10/21
 **/
@Repository
@Mapper
public interface PovertyCommodityMapper extends MyMapper<PovertyCommodityEntity> {

    @Select(" select poverty_commodity_id povertyCommodityId,commodity_id commodityId,commodity_name commodityName,count,type from t_score_poverty_commodity where order_id=#{orderId} ")
    List<PovertyCommodity> getPovertyCommodityByOrderId(@Param("orderId") String orderId);

    @Select("<script> " +
            " select order_id orderId, poverty_commodity_id povertyCommodityId,commodity_id commodityId,commodity_name commodityName,count,type,price,actual_price actualPrice,carriage from t_score_poverty_commodity where order_id in " +
            " <foreach collection=\"orderIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            " #{item} " +
            " </foreach> " +
            " </script>  ")
    List<PovertyCommodity> getPovertyCommodityByOrderIds(@Param("orderIds") List<String> orderIds);

}
