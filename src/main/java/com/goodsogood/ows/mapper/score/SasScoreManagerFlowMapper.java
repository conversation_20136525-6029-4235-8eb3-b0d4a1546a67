package com.goodsogood.ows.mapper.score;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.score.SasScoreManagerFlowEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/12/13
 */
@Repository
@Mapper
public interface SasScoreManagerFlowMapper extends MyMapper<SasScoreManagerFlowEntity> {

    @Select("select count(f1.flow_id)\n" +
            "from t_sas_score_manager_flow f1\n" +
            "where f1.region_id=#{regionId} and f1.data_type=#{dataType} and data_id=#{dataId} and f1.status=1\n" +
            "and date_format(f1.handle_time,'%Y-%m')=#{executeTime} and f1.type=#{typeId}")
    Long countExistData(@Param("regionId") Long regionId, @Param("dataType") Integer dataType,
                        @Param("dataId") Long dataId, @Param("executeTime") String executeTime, @Param("typeId") Integer typeId);

    @Select("select f1.source_data_id\n" +
            "from t_sas_score_manager_flow f1\n" +
            "where f1.region_id=#{regionId} and f1.data_type=#{dataType} and data_id=#{dataId} and f1.status=1\n" +
            "and date_format(f1.handle_time,'%Y-%m')=#{executeTime} and f1.type=#{typeId}")
    List<Long> findSourceDataIdExistData(@Param("regionId") Long regionId, @Param("dataType") Integer dataType,
                                         @Param("dataId") Long dataId, @Param("executeTime") String executeTime, @Param("typeId") Integer typeId);

    @Select("select f1.source_data_id_str\n" +
            "from t_sas_score_manager_flow f1\n" +
            "where f1.region_id=#{regionId} and f1.data_type=#{dataType} and data_id=#{dataId} and f1.status=1\n" +
            "and date_format(f1.handle_time,'%Y-%m')=#{executeTime} and f1.type=#{typeId}")
    List<String> findSourceDataStrIdExistData(@Param("regionId") Long regionId, @Param("dataType") Integer dataType,
                                              @Param("dataId") Long dataId, @Param("executeTime") String executeTime, @Param("typeId") Integer typeId);

    /**
     * 如果重复插入则忽略 与 唯一索引关联
     * 在业务上可以不考虑重复插入的问题
     */
    @Insert("INSERT ignore INTO t_sas_score_manager_flow(flow_id, region_id, data_id, data_type, source_data_id, source_data_id_str, type, type_str, status, is_del, handle_time, token, score, send_type, send_time, create_user, create_time, update_user, change_time, var1, var2, var3, var4, var5,score_type) VALUES " +
            "(null, #{regionId}, #{dataId}, #{dataType}, #{sourceDataId}, #{sourceDataIdStr}, #{type}, #{typeStr}, #{status}, #{isDel}, #{handleTime}, #{token}, #{score}, #{sendType}, #{sendTime}, #{createUser}, #{createTime}, #{updateUser}, #{changeTime}, #{var1}, #{var2}, #{var3}, #{var4}, #{var5},#{scoreType});")
    Integer insertIgnore(SasScoreManagerFlowEntity flowEntity);

    /**
     * 查询需要上报到积分中心的积分数据
     */
    @Select("select type,flow_id flowId,region_id regionId,data_id dataId,data_type dataType,type_str typeStr,token,score,score_type scoreType,send_type sendType,handle_time as handleTime " +
            " from t_sas_score_manager_flow where is_del =1 and status in (1,3) and region_id = #{regionId}")
    List<SasScoreManagerFlowEntity> findAddSourceInfo(@Param("regionId") Long regionId);

}
