package com.goodsogood.ows.mapper.score;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.score.ScoreOrderCommodityEntity;
import com.goodsogood.ows.model.vo.score.BookCommodity;
import com.goodsogood.ows.model.vo.score.Books;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: tc
 * @Description 积分订单商品数据表
 * @Date 20:39 2018/9/20
 */
@Repository
@Mapper
public interface ScoreOrderCommodityMapper extends MyMapper<ScoreOrderCommodityEntity> {

    @Select(" select order_commodity_id orderCommodityId,commodity_id commodityId,commodity_name commodityName,count from t_score_order_commodity where order_id=#{orderId} ")
    List<BookCommodity> getCommodityByOrderId(@Param("orderId") Long orderId);

    @Select("<script> " +
            "select order_id orderId,order_commodity_id orderCommodityId,commodity_id commodityId,commodity_name commodityName,count from t_score_order_commodity where order_id in " +
            " <foreach collection=\"orderIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            " #{item} " +
            " </foreach> " +
            "</script>")
    List<BookCommodity> getCommodityByOrderIds(@Param("orderIds") List<Long> orderIds);

}
