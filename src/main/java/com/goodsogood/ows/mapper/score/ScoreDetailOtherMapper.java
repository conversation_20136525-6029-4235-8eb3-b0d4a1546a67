package com.goodsogood.ows.mapper.score;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.doris.IndexOrgScoreEntity;
import com.goodsogood.ows.model.db.doris.IndexUserScoreEntity;
import com.goodsogood.ows.model.db.score.ScoreDetailOtherEntity;
import com.goodsogood.ows.model.vo.score.Books;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-03-14 09:22
 **/
@Repository
@Mapper
public interface ScoreDetailOtherMapper extends MyMapper<ScoreDetailOtherEntity> {


    @Select(" select tsu.user_id tempUserId,orders.order_id orderId,commodity.commodity_count commodityCount,other.out_trade_no outTradeNo,other.consume_time consumeTime,other.score_detail_id scoreDetailOtherId,other.remark,to1.region_id regionId\n" +
            "from (select score_detail_id,score_user_id,out_trade_no,IFNULL(consume_time,create_time)  consume_time,remark,org_id from t_score_detail_other where IFNULL(consume_time,create_time) between #{startTime} and #{endTime} and out_trade_no is not null) as other\n" +
            "LEFT JOIN (select order_id,out_trade_no from t_score_order where `status`=1) orders on other.out_trade_no=orders.out_trade_no\n" +
            "LEFT JOIN (select order_id,sum(count) commodity_count from t_score_order_commodity GROUP BY order_id) commodity on orders.order_id=commodity.order_id\n" +
            "LEFT JOIN t_score_user tsu on other.score_user_id=tsu.score_user_id\n" +
            " left join t_organization to1 on other.org_id=to1.organization_id " +
            "where tsu.user_id is not null and commodity.commodity_count  is not null " +
//            " order by tsu.user_id " +
            "limit ${skip},${limitSize} ")
    @Results({
            @Result(column = "orderId", property = "orderId")
//            @Result(column = "orderId", property = "commoditys", many = @Many(select = "com.goodsogood.ows.mapper.score.ScoreOrderCommodityMapper.getCommodityByOrderId"))
    })
    List<Books> findScoreBuyBooks(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("limitSize") Integer limitSize, @Param("skip") Integer skip);


    @Select("select 1000 as ruleId,t2.user_id userId,t2.user_name userName,t1.score_type as scoreType," +
            "t3.organization_id as orgId,#{dataMonth} as dataMonth,now() as createTime," +
            "t1.parent_score_type parentScoreType,sum(t1.score) score from t_score_detail_other t1 "+
            "join t_score_user t2 on t1.score_user_id=t2.score_user_id "+
            "join t_user_org_corp t3 on t2.user_id=t3.user_id and t3.is_employee=1 "+
            "where t1.create_time like concat(#{dataMonthStr},'%') "+
            "group by t2.user_id,t1.score_type"
    )
    List<IndexUserScoreEntity> sumUserScore(@Param("dataMonthStr") String dataMonthStr,@Param("dataMonth") Integer dataMonth);


    @Select("select 1000 as ruleId,t1.score_org_id orgId,t3.name orgName,t1.score_type as scoreType," +
            "t1.score_org_type as scoreOrgType,#{dataMonth} as dataMonth,now() as createTime," +
            "t1.parent_score_type parentScoreType,sum(t1.score) score from t_score_org_detail t1 "+
            "join t_organization t3 on t1.score_org_id=t3.organization_id and t3.status=1 "+
            "where t1.score_org_type=1 and t1.create_time like concat(#{dataMonthStr},'%') "+
            "group by t1.score_org_id,t1.score_type"
    )
    List<IndexOrgScoreEntity> sumOrgScore(@Param("dataMonthStr") String dataMonthStr, @Param("dataMonth") Integer dataMonth);

    @Select("select 1000 as ruleId,t1.score_org_id orgId,t3.name orgName,t1.score_type as scoreType," +
            "t1.score_org_type as scoreOrgType,#{dataMonth} as dataMonth,now() as createTime," +
            "t1.parent_score_type parentScoreType,sum(t1.score) score from t_score_org_detail t1 "+
            "join t_party_group t3 on t1.score_org_id=t3.group_id and t3.status=1 "+
            "where t1.score_org_type=2 and t1.create_time like concat(#{dataMonthStr},'%') "+
            "group by t1.score_org_id,t1.score_type")
    List<IndexOrgScoreEntity> sumGroupScore(@Param("dataMonthStr") String dataMonthStr, @Param("dataMonth") Integer dataMonth);
}