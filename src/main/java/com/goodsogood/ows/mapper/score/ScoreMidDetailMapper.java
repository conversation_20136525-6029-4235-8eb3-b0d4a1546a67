package com.goodsogood.ows.mapper.score;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.score.ScoreMidDetailEntity;
import com.goodsogood.ows.model.mongodb.fusion.FusionBaseData;
import com.goodsogood.ows.model.mongodb.fusion.SystemUsageDetail;
import com.goodsogood.ows.model.vo.score.ReportVo;
import com.goodsogood.ows.model.vo.score.UnitScoreVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface ScoreMidDetailMapper extends MyMapper<ScoreMidDetailEntity> {
    @Select("select unitId,ifnull(score,0) as score from(\n" +
            "SELECT t1.organization_id as unitId,avg(usual_score) as score FROM t_organization t1\n" +
            "left join t_score_mid_detail t2 on t1.organization_id=t2.unit_id and t2.type=2 and t2.data_month=#{year} \n" +
            "WHERE t1.`status` = 1 AND t1.org_type = 102807 AND t1.parent_id = 2 \n" +
            "group by t1.organization_id\n" +
            ") a")
    List<UnitScoreVo> queryUnitYearScore(@Param("regionId") Long regionId, @Param("year") Integer year);


    @Select(" select max(usual_score) as maxScore,avg(usual_score) as avgScore" +
            " from t_score_mid_detail   " +
            " where type={type} and data_month=#{date}"
    )
    ReportVo querySystemMax(@Param("type") Integer type, @Param("date") String date);

    @Select("<script>" +
            " select avg(usual_score) as avgScore" +
            " from t_score_mid_detail   " +
            " where type=1 and data_month in " +
            " <foreach item=\"item\" collection=\"sdate\" open=\"(\" separator=\",\" close=\")\">#{item}</foreach> " +
            " group by data_month  order by data_month desc" +
            "</script>"
    )
    List<Double> queryLast12Month(@Param("sdate") List<String> sdate);

    @Select("select user_id userId,user_name userName,org_id orgId,org_name orgName,usual_score score from t_score_mid_detail" +
            " where data_month=#{dateMonth} and unit_id=#{unitId} order by usual_score desc\n")
    List<SystemUsageDetail> queryUsualMonthScore(@Param("unitId") Long unitId, @Param("dateMonth") String dateMonth);
}
