package com.goodsogood.ows.mapper.score;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.score.PovertyOrderEntity;
import com.goodsogood.ows.model.vo.score.Poverty;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: tc
 * @Description 扶贫商城据库查询
 * @Date 14:39 2019/10/21
 **/
@Repository
@Mapper
public interface PovertyOrderMapper extends MyMapper<PovertyOrderEntity> {

    @Select(" select tspo.user_id tempUserId,tspo.order_id orderId,tspo.trading_time tradingTime, tspo.prverty_order_id povertyOrderId,tspo.total_price totalPrice,tspo.donation,tspc.count_commodity countCommodity,tspc.count_common countCommon,tspc.size_common sizeCommon,tspc.count_donate countDonate,tspc.size_donate sizeDonate,tspo.logistics,to1.region_id regionId\n" +
            "from t_score_poverty_order tspo\n" +
            "LEFT JOIN (select order_id,count(order_id) count_commodity,sum(if(type=1,1,0)) count_common,sum(if(type=1,count,0)) size_common,sum(if(type=2,1,0)) count_donate,sum(if(type=2,count,0)) size_donate  from t_score_poverty_commodity  GROUP BY order_id) tspc on tspo.order_id=tspc.order_id\n" +
            " left join t_organization to1 on tspo.oid=to1.organization_id " +
            "where tspo.trading_time between #{startDate} and #{endDate} " +
            " limit ${skip},${limitSize} ")
    @Results({
            @Result(column = "orderId", property = "orderId")
//            @Result(column = "orderId", property = "commoditys", many = @Many(select = "com.goodsogood.ows.mapper.score.PovertyCommodityMapper.getPovertyCommodityByOrderId"))
    })
    List<Poverty> findPovertyInfo(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("limitSize") Integer limitSize, @Param("skip") Integer skip);



}
