package com.goodsogood.ows.mapper.score;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.score.SasScoreManagerFlowEntity;
import com.goodsogood.ows.model.db.score.SasScoreManagerRatioEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/12/13
 */
@Repository
@Mapper
public interface SasScoreManagerRatioMapper extends MyMapper<SasScoreManagerRatioEntity> {

    @Insert("insert ignore into t_sas_score_manager_ratio (ratio_id,region_id,org_id,type,total,find,date,ratio,create_user,create_time) \n" +
            "select null ratio_id,#{regionId},#{orgId},1,count(distinct u1.user_id) total,\tcount(distinct ull.login_id) find,#{time}, " +
            "ifnull(CONCAT( ROUND( \t\tcount(distinct ull.login_id) / count(distinct u1.user_id) * 100, 2 ), '', '%'),-1) ratio,#{changeUser},now()\n" +
            "from t_organization o1 \n" +
            "inner join t_user_org_corp oc1 on o1.organization_id =oc1.organization_id and oc1.is_employee=1\n" +
            "inner join t_user u1 on oc1.user_id=u1.user_id and u1.`status`=1 and u1.political_type in (1,5,17,18) \n" +
            "left join  t_user_login_log ull on u1.user_id=ull.login_id and DATE_FORMAT(ull.login_date,'%Y-%m-%d') = #{time}\t\n" +
            "where o1.`status` = 1  \n" +
            "and o1.organization_id = #{orgId}\n" +
            "and o1.region_id=#{regionId}")
    Integer addBaseOrgPeopleLoginRatio(@Param("regionId") Long regionId, @Param("orgId") Long orgId,
                                       @Param("time") String time, @Param("changeUser") Long changeUser);


    /**
     * 指定组织 指定月 指定类型 的平均数据
     * @param type  1:登录 2:新闻浏览
     */
    @Select("select ifnull(ROUND(avg(r1.ratio),2),0)\n" +
            "from t_sas_score_manager_ratio r1\n" +
            "where r1.org_id=#{orgId} and r1.type=#{type} and r1.ratio != -1 and date_format(r1.date,'%Y-%m')=#{month}")
    Double findAvgRatio(@Param("orgId") Long orgId,@Param("type") Integer type,@Param("month") String month);

    @Insert("insert ignore into t_sas_score_manager_ratio (region_id,org_id,type,total,find,date,ratio,create_user,create_time) values \n" +
            "(#{regionId},#{orgId},#{type},#{total},#{find},#{date},#{ratio},#{createUser},#{createTime})")
    Integer insertIgnore(SasScoreManagerRatioEntity ratioEntity);
}
