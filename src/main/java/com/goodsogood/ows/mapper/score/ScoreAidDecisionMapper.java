package com.goodsogood.ows.mapper.score;

import com.goodsogood.ows.model.mongodb.dss.*;
import com.goodsogood.ows.model.vo.score.JoinVO;
import com.goodsogood.ows.model.vo.score.ScoreVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 辅助决策积分板块
 *
 * <AUTHOR>
 * @create 2020-11-09
 **/
@Repository
@Mapper
public interface ScoreAidDecisionMapper {
    @Select("<script> select IFNULL(sum(count),0) totalBook," +
            " IFNULL(sum(saleScore*count),0) totalExchangeScore from (" +
            " select ttcmp1.orgId,ttcmp1.userId,ttcmp3.saleScore,ttcmp3.count from (" +
            " SELECT user_id userId,org_id orgId FROM t_user_snapshot WHERE status=1 and region_id=#{regionId} and date_month ='${year}-${month}'" +
            " and org_type_child!=10280329 and org_type_child!=10280330" +
            " and org_id !=3261 and org_level not like CONCAT('%-',3261,'-%')" +
            " AND (org_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%'))" +
            " ) ttcmp1 INNER JOIN (select buyer_id userId,sale_score saleScore,count from t_score_order_commodity t1" +
            " inner JOIN t_score_order t2 on t1.order_id = t2.order_id and DATE_FORMAT(deal_date,'%Y') ='${year}'" +
            " ) ttcmp3 on ttcmp1.userId = ttcmp3.userId) ttcmp4 </script>")
    /**
     * 组织累计兑换图书，累计兑换积分，累计的需要循环查询
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param year
     * @param month
     * @param orgId
     */
    StudyInfo orgBookOrder(@Param("regionId") Long regionId, @Param("year") String year, @Param("month") String month, @Param("orgId") Long orgId);


    @Select("<script> select count(1) userTotal," +
            " IFNULL(sum(totalOrder),0) buyTotal," +
            " Round(IFNULL(sum(totalPrice),0)/100,2) payTotal," +
            " Round(IFNULL(sum(totalDonation),0)/100,2) donateTotal" +
            " from (select ttcmp1.orgId,ttcmp1.userId," +
            " ttcmp3.totalOrder,ttcmp3.totalPrice,ttcmp3.totalDonation from (" +
            " SELECT user_id userId,org_id orgId FROM t_user_snapshot WHERE status=1 and region_id=#{regionId} and date_month ='${year}-${month}' and" +
            " org_type_child!=10280329 and org_type_child!=10280330" +
            " AND (org_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%'))" +
            " and org_id !=3261 and org_level not like CONCAT('%-',3261,'-%')) ttcmp1 INNER JOIN (" +
            " SELECT user_id userId,count(1) totalOrder,sum(total_price) totalPrice,sum(donation) totalDonation" +
            " FROM t_score_poverty_order where DATE_FORMAT(trading_time,'%Y') = '${year}' GROUP BY user_id" +
            " ) ttcmp3 on ttcmp1.userId = ttcmp3.userId) ttcmp4</script>")
    /**
     * 组织扶贫消费累计和年度统计，累计的需要循环查询
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param year
     * @param month
     * @param orgId
     */
    ConsumeInfo orgPovertyOrder(@Param("regionId") Long regionId, @Param("year") String year, @Param("month") String month, @Param("orgId") Long orgId);

    @Select("<script> select subClass name,count(1) value from (" +
            " select ttcmp1.orgId,ttcmp1.userId,ttcmp3.subClass from (" +
            " SELECT user_id userId,org_id orgId FROM t_user_snapshot WHERE status=1 and region_id=#{regionId}" +
            " and date_month ='${year}-${month}' and org_type_child!=10280329 and org_type_child!=10280330" +
            " and org_id !=3261 and org_level not like CONCAT('%-',3261,'-%')" +
            " AND (org_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%'))" +
            " ) ttcmp1 INNER JOIN (select user_id userId,sub_class subClass FROM t_score_poverty_commodity t1" +
            " inner JOIN t_score_poverty_order t2 on t1.order_id = t2.order_id and DATE_FORMAT(trading_time,'%Y') ='${year}'" +
            " ) ttcmp3 on ttcmp1.userId = ttcmp3.userId) ttcmp4 GROUP BY subClass</script>")
    /**
     * 组织消费扶贫产品类别饼图
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param year
     * @param month
     * @param orgId
     */
    List<PieObject> orgPovertyPie(@Param("regionId") Long regionId, @Param("year") String year, @Param("month") String month, @Param("orgId") Long orgId);


    @Select("<script> select IFNULL(sum(count),0) book," +
            " IFNULL(sum(saleScore*count),0) studyOutScore" +
            " from (select buyer_id userId,sale_score saleScore,count from t_score_order_commodity t1 inner JOIN" +
            " t_score_order t2 on t1.order_id = t2.order_id and DATE_FORMAT(deal_date,'%Y') &lt;=#{year} and buyer_id = #{userId}" +
            ") ttcmp4 </script>")
    /**
     * 党员累计兑换图书，累计兑换积分
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param year
     * @param userId
     */
    UserStudyInfo userBookOrder(@Param("year") String year, @Param("userId") Long userId);


    @Select("<script> select userId,IFNULL(sum(count),0) book," +
            " IFNULL(sum(saleScore*count),0) useScore" +
            " from (select buyer_id userId,sale_score saleScore,count from t_score_order_commodity t1 inner JOIN" +
            " t_score_order t2 on t1.order_id = t2.order_id and DATE_FORMAT(deal_date,'%Y') &lt;=#{year}" +
            " and buyer_id in (${userIds})) ttcmp4 GROUP BY userId </script>")
    /**
     * 党员累计兑换图书，累计兑换积分(批量查询)
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param year
     * @param userIds 逗号间隔
     */
    List<ScoreVo> userBookOrderBatch(@Param("year") String year, @Param("userIds") String userIds);


    @Select("<script> SELECT count(1) orderNum," +
            " Round(IFNULL(sum(total_price),0)/100,2) amount," +
            " Round(IFNULL(sum(donation),0)/100,2) totalDonation " +
            " FROM t_score_poverty_order where DATE_FORMAT(trading_time,'%Y') =#{year}" +
            " and user_id = #{userId} </script>")
    /**
     * 党员扶贫消费年度统计
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param year
     * @param userId
     */
    UserDonateInfo userPovertyOrder(@Param("year") String year, @Param("userId") Long userId);

    @Select("<script> SELECT user_id userId,count(1) orderNum," +
            " Round(IFNULL(sum(total_price),0)/100,2) amount," +
            " Round(IFNULL(sum(donation),0)/100,2) totalDonation " +
            " FROM t_score_poverty_order where DATE_FORMAT(trading_time,'%Y') =#{year}" +
            " and user_id in (${userIds}) GROUP BY user_id </script>")
    /**
     * 党员扶贫消费年度统计(批量)
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param year
     * @param userIds
     */
    List<ScoreVo> userPovertyOrderBatch(@Param("year") String year, @Param("userIds") String userIds);


    @Select("<script> select subClass name,count(1) value" +
            " from (select user_id userId,sub_class subClass FROM t_score_poverty_commodity t1 inner JOIN" +
            " t_score_poverty_order t2 on t1.order_id = t2.order_id and DATE_FORMAT(trading_time,'%Y') =#{year}" +
            " and user_id =#{userId}) ttcmp4 GROUP BY subClass </script>")
    /**
     * 党员消费扶贫产品类别饼图
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param year
     * @param userId
     */
    List<PieObject> userPovertyPie(@Param("year") String year, @Param("userId") Long userId);



    @Select("<script> select userId,subClass name,count(1) value" +
            " from (select user_id userId,sub_class subClass FROM t_score_poverty_commodity t1 inner JOIN" +
            " t_score_poverty_order t2 on t1.order_id = t2.order_id and DATE_FORMAT(trading_time,'%Y') =#{year}" +
            " and user_id in (${userIds})) ttcmp4 GROUP BY userId,subClass order by userId</script>")
    /**
     * 党员消费扶贫产品类别饼图(批量)
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param year
     * @param userIds
     */
    List<ScoreVo> userPovertyPieBatch(@Param("year") String year, @Param("userIds") String userIds);

    // ----------------------------------------- 辅助决策-zch start  -----------------------------------------

    /**
     * 年度学习情况走势 - 工委
     *
     * @param sql 子sql
     */
    @Select("<script>" +
            "SELECT \n" +
            " a.time, \n" +
            " sum( a.total ) total \n" +
            "FROM \n" +
            " ( " +
            " <foreach collection=\"list\" item=\"item\" separator=\" UNION ALL \" >\n" +
            "    ${item} \n" +
            " </foreach>" +
            " ) a GROUP BY a.time" +
            "</script>")
    List<StudySituationInfo> studySituationInfo(@Param("list") List<String> sql);

    String SQL_TEMPLATE = "SELECT \n" +
            "HOUR( d.consume_time ) time,\n" +
            "count( 0 ) total \n" +
            "FROM t_score_detail d \n" +
            "WHERE \n" +
            "d.consume_time >= '${startTime} 00:00:00' \n" +
            "AND d.consume_time <= '${endTime} 23:59:59' \n" +
            "AND d.score_user_id IN (${scoreUserIds})\n" +
            "GROUP BY\n" +
            "HOUR ( d.consume_time )";

    /**
     * 拼接sql
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param scoreUserIds 积分用户id
     * @return
     */
    default String joinSql(String startTime,
                           String endTime,
                           String scoreUserIds) {
        String replace = SQL_TEMPLATE.replace("${startTime}", startTime)
                .replace("${endTime}", endTime)
                .replace("${scoreUserIds}", scoreUserIds);
        return replace;
    }

    /**
     * 从快照表中获取积分用户id
     *
     * @param regionId
     * @param orgId
     * @param include
     * @param dateMonth
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " su.score_user_id  \n" +
            "FROM\n" +
            "t_user_snapshot us\n" +
            "LEFT JOIN t_score_user su ON su.user_id = us.user_id \n" +
            "WHERE\n" +
            "us.date_month = '${dateMonth}' \n" +
            "AND us.region_id = #{regionId} \n" +
            "<if test=\"orgId != null and include == true\">" +
            "   AND (us.org_level LIKE CONCAT( '%-', #{orgId}, '-%' ) OR us.org_id = #{orgId}) \n" +
            "</if>" +
            "<if test=\"orgId != null and include != true\">" +
            "   AND us.org_id = #{orgId} \n" +
            "</if>" +
            "AND su.score_user_id IS NOT NULL\n" +
            "AND us.org_id != 3261 \n" +
            "AND us.org_level NOT LIKE CONCAT('%-',3261,'-%')\n" +
            "AND us.org_type_child NOT IN (10280329,10280330)" +
            "AND us.status = 1 " +
            "</script>")
    List<Long> getScoreUserIdsByUserSnapshot(@Param("regionId") Long regionId,
                                         @Param("orgId") Long orgId,
                                         @Param("include") Boolean include,
                                         @Param("dateMonth") String dateMonth);

    /**
     * 从快照表中获取用户id
     *
     * @param regionId
     * @param orgId
     * @param include
     * @param dateMonth
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " us.user_id  \n" +
            "FROM\n" +
            "t_user_snapshot us\n" +
            "WHERE\n" +
            "us.date_month = '${dateMonth}' \n" +
            "AND us.region_id = #{regionId} \n" +
            "<if test=\"orgId != null and include == true\">" +
            "   AND (us.org_level LIKE CONCAT( '%-', #{orgId}, '-%' ) OR us.org_id = #{orgId}) \n" +
            "</if>" +
            "<if test=\"orgId != null and include != true\">" +
            "   AND us.org_id = #{orgId} \n" +
            "</if>" +
            "AND us.org_id != 3261 \n" +
            "AND us.org_level NOT LIKE CONCAT('%-',3261,'-%')\n" +
            "AND us.org_type_child NOT IN (10280329,10280330)" +
            "AND us.status = 1 " +
            "</script>")
    List<Long> getUserIdsByUserSnapshot(@Param("regionId") Long regionId,
                                        @Param("orgId") Long orgId,
                                        @Param("include") Boolean include,
                                        @Param("dateMonth") String dateMonth);

    /**
     * 本年学习人次 - 工委
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @Select("<script>" +
            "SELECT\n" +
            " count(0) total\n" +
            "FROM\n" +
            " t_score_detail\n" +
            " WHERE consume_time <![CDATA[ >= ]]> '${startTime} 00:00:00'\n" +
            " AND consume_time <![CDATA[ <= ]]> '${endTime} 23:59:59'" +
            " AND score_user_id IN (${scoreUserIds})" +
//            " AND app_id = #{appId} " +
//            " AND org_id = #{orgId} " +
            "</script>")
    Integer curStudyTimes(
//            @Param("appId") Integer appId,
//                          @Param("orgId") Integer orgId,
            @Param("scoreUserIds") String scoreUserIds,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime);

    /**
     * 本年获得学习积分 - 工委
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @Select("<script>" +
            "SELECT\n" +
            " sum(score) total\n" +
            "FROM\n" +
            " t_score_detail\n" +
            " WHERE consume_time <![CDATA[ >= ]]> '${startTime} 00:00:00'\n" +
            " AND consume_time <![CDATA[ <= ]]> '${endTime} 23:59:59'" +
            " AND score_user_id IN (${scoreUserIds})" +
//            " AND app_id = #{appId} " +
//            " AND org_id = #{orgId} " +
            "</script>")
    Integer curStudyScore(
//            @Param("appId") Integer appId,
//            @Param("orgId") Integer orgId,
            @Param("scoreUserIds") String scoreUserIds,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime);

    /**
     * 累计在71书院学习次数 - 用户
     *
     * @param scoreUserId 积分用户id
     * @param endTime     结束时间
     */
    @Select("<script>" +
            "SELECT\n" +
            " count( 0 ) joinTotal,\n" +
            " a.score_user_id scoreUserId\n" +
            "FROM\n" +
            " t_score_detail a\n" +
            "WHERE\n" +
            "  a.consume_time <![CDATA[ <= ]]> '${endTime} 23:59:59'\n" +
            " AND a.score_user_id IN (${scoreUserId}) \n" +
            " GROUP BY a.score_user_id \n"+
            "</script>")
    List<JoinVO> studySituationInfoUser(@Param("scoreUserId") String scoreUserId,
                                        @Param("endTime") String endTime);

    /**
     * 累计获得积分 - 用户
     *
     * @param scoreUserId 积分用户id
     * @param endTime     结束时间
     */
    @Select("<script>" +
            "SELECT\n" +
            " sum( a.score ) score,\n" +
            " a.score_user_id scoreUserId \n" +
            "FROM\n" +
            " t_score_detail a\n" +
            "WHERE\n" +
            "  a.consume_time <![CDATA[ <= ]]> '${endTime} 23:59:59'\n" +
            " AND a.score_user_id IN (${scoreUserId}) \n" +
            " GROUP BY a.score_user_id \n" +
            "</script>")
    List<JoinVO> totalScoreUser(@Param("scoreUserId") String scoreUserId,
                           @Param("endTime") String endTime);
    // ----------------------------------------- 辅助决策-zch end -----------------------------------------
}
