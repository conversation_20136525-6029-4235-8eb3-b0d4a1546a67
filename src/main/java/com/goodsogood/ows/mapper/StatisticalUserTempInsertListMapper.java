package com.goodsogood.ows.mapper;

import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Options;
import tk.mybatis.mapper.provider.SpecialProvider;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-07-26 17:42
 **/
public interface StatisticalUserTempInsertListMapper<T> {
    @Options(useGeneratedKeys = true, keyProperty = "statisticalUserTempId")
    @InsertProvider(type = SpecialProvider.class, method = "dynamicSQL")
    int insertList(List<T> recordList);
}