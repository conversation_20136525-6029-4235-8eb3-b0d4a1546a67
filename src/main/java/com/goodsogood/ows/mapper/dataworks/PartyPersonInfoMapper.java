package com.goodsogood.ows.mapper.dataworks;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.tbcFusion.PartyPersonInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: mengting
 * @Date: 2022/5/25 16:11
 */
@Mapper
@Repository
public interface PartyPersonInfoMapper extends MyMapper<PartyPersonInfoEntity> {
    @Update("<script>"
            + " <foreach collection=\"list\" item=\"entity\" open=\"\" separator=\";\" close=\" \">"
            + " update t_party_person_info set user_id=#{entity.userId},phone_secret=#{entity.phoneSecret},update_time=now() where person_uuid=#{entity.personUuid}"
            + " </foreach>"
            +"</script>")
    void updateUserId(@Param("list") List<PartyPersonInfoEntity>list);
}
