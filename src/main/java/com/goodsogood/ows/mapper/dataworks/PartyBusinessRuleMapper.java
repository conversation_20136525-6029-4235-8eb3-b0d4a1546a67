package com.goodsogood.ows.mapper.dataworks;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.tbcFusion.PartyBusinessRuleEntity;
import com.goodsogood.ows.model.mongodb.pbm.BusinessForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * @Author: mengting
 * @Date: 2022/4/6 14:03
 */
@Repository
@Mapper
public interface PartyBusinessRuleMapper extends MyMapper<PartyBusinessRuleEntity> {

    /**
     * 查询组织业务详情
     *
     * @param ruleId   指标ruleId
     * @param orgId    组织id
     * @param calDate  查询年月
     * @param yearDate year
     * @param week     周
     * @return BusinessForm
     */
    @Select("<script>" +
            "SELECT DISTINCT ROUND(pbcr.cal_result,1) AS calResult,pbcr.explain_str AS explainStr,pbcr.rank_num AS rankNum \n" +
            "FROM t_party_business_cal_result AS pbcr \n" +
            "\tLEFT JOIN t_party_business_rule AS pbr ON pbcr.rule_id = pbr.rule_id \n" +
            "\tLEFT JOIN t_party_county_info AS pci ON pbcr.odps_object = pci.org_uuid \n" +
            "\tWHERE pbcr.rule_id = #{ruleId} AND pci.org_id = #{orgId} \n" +
            "<if test=\"yearDate != null and week != null\">AND pbcr.`year` = #{yearDate} AND pbcr.cal_date = #{week} limit 1\n</if>" +
            "<if test=\"calDate != null\">AND pbcr.cal_date = #{calDate} limit 1\n</if>" +
            "</script>")
    BusinessForm findOrgBusiness(@Param("ruleId") Long ruleId,
                                 @Param("orgId") Long orgId,
                                 @Param("calDate") Integer calDate,
                                 @Param("yearDate") Integer yearDate,
                                 @Param("week") Integer week);

    /**
     * 查询用户业务详情
     *
     * @param userId  用户id
     * @param ruleId  指标ruleId
     * @param calDate 查询年月
     * @return BusinessForm
     */
    @Select("<script>" +
            "SELECT DISTINCT ROUND(pbcr.cal_result,1) AS calResult, pbcr.explain_str AS explainStr,pbcr.rank_num AS rankNum\n" +
            "FROM t_party_person_info AS ppi \n" +
            "\tLEFT JOIN t_party_business_cal_result AS pbcr ON ppi.master_mobile = pbcr.cal_object \n" +
            "\tLEFT JOIN t_party_business_rule AS pwi ON pwi.rule_id = pbcr.rule_id \n" +
            "WHERE ppi.user_id = #{userId} \n" +
            "\tAND pbcr.rule_id = #{ruleId} \n" +
            "\tAND pbcr.cal_date = #{calDate} limit 1 " +
            "</script>")
    BusinessForm findUserBusiness(@Param("userId") Long userId,
                                  @Param("ruleId") Long ruleId,
                                  @Param("calDate") Integer calDate);

    /**
     * 查询根据排名计算百分比
     *
     * @param userId  用户id
     * @param ruleId  指标ruleId
     * @param calDate 查询年月
     * @return string
     */
    @Select("<script>" +
            "SELECT DISTINCT\n" +
            "IFNULL( ( ( 1- ROUND( pbcr.rank_num / b.den, 3 ) ) * 100 ) + '%', '0%' ) AS per \n" +
            "FROM t_party_business_cal_result AS pbcr \n" +
            "\tINNER JOIN t_party_person_info AS ppi ON pbcr.cal_object = ppi.master_mobile \n" +
            "\tINNER JOIN ( \n" +
            "SELECT a.rule_id, count( DISTINCT a.cal_object ) AS den FROM t_party_business_cal_result AS a \n" +
            "WHERE a.rule_id = #{ruleId} AND a.cal_date = #{calDate} ) AS b ON pbcr.rule_id = b.rule_id \n" +
            "WHERE pbcr.rule_id = #{ruleId} \n" +
            "\tAND pbcr.cal_date = #{calDate} \n" +
            "\tAND ppi.user_id = #{userId} limit 1\n" +
            "</script>")
    String findUserPer(@Param("userId") Long userId,
                       @Param("ruleId") Long ruleId,
                       @Param("calDate") Integer calDate);
}
