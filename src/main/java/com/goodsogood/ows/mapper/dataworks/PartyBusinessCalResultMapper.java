package com.goodsogood.ows.mapper.dataworks;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.tbcFusion.PartyBusinessCalResultEntity;
import com.goodsogood.ows.model.vo.score.ScoreUserBusinessVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: mengting
 * @Date: 2022/4/6 14:04
 */
@Repository
@Mapper
public interface PartyBusinessCalResultMapper extends MyMapper<PartyBusinessCalResultEntity> {
    @Select("select round(avg(cal_result),6) from t_party_business_cal_result where rule_id=4 and year=#{year} and cal_date=#{weekNum} and region_id=#{regionId} \n")
    Double queryAvgProfile(Long regionId, Integer weekNum,Integer year);

    @Select("select round(avg(cal_result),6) from t_party_business_cal_result where rule_id=4 and year in(#{year},#{lastYear}) and cal_date=#{weekNum} and region_id=#{regionId} \n")
    Double queryAvgProfileSpecial(Long regionId, Integer weekNum,Integer year, Integer lastYear);

    @Update("<script>"
            + " <foreach collection=\"list\" item=\"entity\" open=\"\" separator=\";\" close=\" \">"
            + " update t_party_business_cal_result set score=#{entity.score},rank_num=#{entity.rankNum},explain_str=#{entity.explainStr} where result_id=#{entity.resultId}"
            + " </foreach>"
            +"</script>")
    void updateScore(@Param("list") List<PartyBusinessCalResultEntity> list);

//    @Update("<script>"
//            + " <foreach collection=\"list\" item=\"entity\" open=\"\" separator=\";\" close=\" \">"
//            + " update t_party_business_cal_result set score=#{entity.score},rank_num=#{entity.rank},update_time=now() where rule_id=#{entity.ruleId} and cal_object=#{entity.calObject}"
//            + " and cal_date=#{entity.calDate} "
//            + " </foreach>"
//            +"</script>")
//    /**
//     * 计算完人员的业务积分后回写数据
//     */
//    void updateScoreAndRank(@Param("list") List<ScoreUserBusinessVo> list);

    @Update("<script>"
            + " update t_party_business_cal_result set score=#{entity.score},rank_num=#{entity.rank},update_time=now() where rule_id=#{entity.ruleId} and cal_object=#{entity.calObject}"
            + " and cal_date=#{entity.calDate} "
            +"</script>")
    /**
     * 计算完人员的业务积分后回写数据
     */
    void updateScoreAndRank(@Param("entity") ScoreUserBusinessVo entity);
}