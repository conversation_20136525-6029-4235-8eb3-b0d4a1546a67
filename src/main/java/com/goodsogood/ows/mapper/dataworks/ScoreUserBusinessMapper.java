package com.goodsogood.ows.mapper.dataworks;

import com.goodsogood.ows.model.vo.score.ScoreUserBusinessVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 烟草每月用户业务积分
 * <AUTHOR>
 **/
@Repository
@Mapper
public interface ScoreUserBusinessMapper {

    /**
     * 获取所有积分用户id
     *
     * @return
     */
    @Select(" SELECT DISTINCT t3.user_id userId,t1.rule_id ruleId,t1.cal_result calResult,t1.cal_object calObject," +
            " t1.cal_date calDate FROM t_party_business_cal_result t1 INNER JOIN t_party_business_rule t2" +
            " on t1.rule_id = t2.rule_id and t2.object_type=1 and t1.region_id =#{regionId} and t1.cal_date=#{calDate}" +
            " INNER JOIN t_party_person_info t3 on t1.cal_object = t3.master_mobile ORDER BY t1.rule_id,cal_result desc")
    List<ScoreUserBusinessVo> findUserBusiness(@Param("regionId") Long regionId, @Param("calDate") Integer calDate);

    /**
     * 获取所有机构积分
     * @param regionId
     * @param calDate
     * @return
     */
    @Select(" SELECT DISTINCT t3.org_id userId,t1.rule_id ruleId,t1.cal_result calResult,t1.cal_object calObject," +
            " t1.cal_date calDate FROM t_party_business_cal_result t1 INNER JOIN t_party_business_rule t2" +
            " on t1.rule_id = t2.rule_id and t2.object_type=2 and t1.region_id =#{regionId} and t1.cal_date=#{calDate}" +
            " INNER JOIN t_party_county_info t3 on t1.odps_object = t3.org_uuid ORDER BY t1.rule_id,cal_result desc")
    List<ScoreUserBusinessVo> findOrgBusiness(@Param("regionId") Long regionId, @Param("calDate") Integer calDate);
}
