package com.goodsogood.ows.mapper.supervise;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseExtendEntity;
import com.goodsogood.ows.model.vo.supervise.OptionForm;
import com.goodsogood.ows.model.vo.supervise.SuperviseUserForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-10-13 17:24
 **/
@Repository
@Mapper
public interface SuperviseExtendMapper extends MyMapper<SuperviseExtendEntity> {

    //监督党委-按党员统计
    @Select("<script>"+
            "select * from ("+
            "SELECT user_id as userId,user_name as userName,user_phone as userPhone,count(option_key) as count\n" +
            "FROM t_supervise_extend \n" +
            "WHERE org_id=#{orgId} AND user_id is not NULL \n" +
            " <if test=\"userName !='' and userName !=null \"> and  user_name like concat('%',#{userName},'%') </if> "+
            " <if test=\"mobile !='' and mobile !=null \"> and  user_phone like concat('%',#{mobile},'%') </if> "+
            "GROUP BY user_id,user_name,user_phone ) as L order by count desc,userId  "+
            "</script>"
            )
    List<SuperviseUserForm> listByUserSta(@Param(value = "orgId") Long orgId,
                                          @Param(value = "userName") String userName,
                                          @Param(value = "mobile") String mobile);

    //监督党委-按党员统计-详情
    @Select("SELECT option_key as optionKey, option_name as optionName,detail_content as detailContent," +
            " org_name as orgName, org_secretary  as orgSecretary \n" +
            "FROM t_supervise_extend \n" +
            "WHERE user_id=#{userId} and org_id=#{orgId}")
    List<OptionForm> listByUserStaDetail(@Param(value = "userId")Long userId,@Param(value = "orgId")Long orgId);

    @Select("SELECT sub_org_id as subOrgId, org_id as orgId FROM t_supervise_extend  " +
            " WHERE org_id=#{orgId} and option_key= #{optionKey} " +
            " and ( FIND_IN_SET(#{userId} ,org_secretary_id) OR user_id=#{userId} ) limit 1")
    SuperviseExtendEntity getExtendInfo(@Param(value = "optionKey")String optionKey,
                                        @Param(value = "orgId") Long singleOrgId,
                                        @Param(value = "userId") Long userId);

}