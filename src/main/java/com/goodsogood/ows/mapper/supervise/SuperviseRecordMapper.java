package com.goodsogood.ows.mapper.supervise;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @create 2021-12-24 16:16
 **/
@Repository
@Mapper
public interface SuperviseRecordMapper extends MyMapper<SuperviseRecordEntity> {

    @Select("SELECT COUNT(1) AS count FROM t_supervise_record WHERE 1=1 " +
            "and org_id=#{orgId} and  to_days(create_time) = to_days(now())")
    Integer valTodayIsSend(Long orgId);
}