package com.goodsogood.ows.mapper.supervise;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.supervise.SuperviseEntity;
import com.goodsogood.ows.model.vo.supervise.SuperviseOrgForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2021-10-13 17:24
 **/
@Repository
@Mapper
public interface SuperviseMapper extends MyMapper<SuperviseEntity> {


    @Select(" <script> SELECT C.org_id FROM (\n" +
            "\tSELECT org_id FROM (\n" +
            "\t\t\tSELECT SUM(option_5+option_6+option_7+option_8+option_9) as countNum,org_id \n" +
            "\t\t\tFROM t_supervise WHERE org_id IN \n" +
            " <foreach collection=\"orgIds\" index=\"index\" item=\"oid\" open=\"(\" separator=\",\" close=\")\">\n"+
            " #{oid} " +
            " </foreach> " +
            "\t  GROUP BY org_id ) as A WHERE countNum>0\n" +
            "\n" +
            ") as B\n" +
            "INNER JOIN t_supervise as C\n" +
            "on B.org_id=c.org_id  </script>")
    Set<Long> getRevisitMeetingCount(@Param(value = "orgIds") Collection<Long> listOrgIds);


    @Select(" <script> " +
            " SELECT detail_content FROM t_supervise_extend " +
            " WHERE org_id=#{orgId} AND option_key IN('option5','option6','option7','option8','option9');\n"+
            "  </script>")
    List<String> getOption22SummaryInfo(Long orgId);


//    @Select(" <script>"+
//            "\n" +
//            " SELECT  count(1)\n" +
//            " FROM t_supervise\n" +
//            " WHERE (  org_level like concat('%-',#{orgId},'-%') and is_supervise>0 )"+
//            "</script>")
//    Integer listByOrgStaByIsSupervise(@Param(value = "orgId") Long orgId,@Param(value = "sqlPart") String sqlPart);




    @Select(" <script>"+
            "select  orgId from ("+
            "\n" +
            " SELECT org_id as orgId,org_name as orgName,org_secretary as orgSecretary," +
            " <foreach collection=\"listOptionKeys\" index=\"index\" item=\"key\" open=\" \" separator=\",\" close=\" \">\n"+
            " SUM(if(${key}>0,1,0)) as count " +
            " </foreach> " +
            " FROM t_supervise\n" +
            " WHERE 1=1 and  (  org_level like concat('%-',#{orgId},'-%') )"+
            " and org_type_child in"+
            " <foreach collection=\"committeeType\" index=\"index\" item=\"it\" open=\"( \" separator=\",\" close=\") \">\n"+
            "   #{it} " +
            " </foreach> " +
            " group by orgId,orgName, orgSecretary"+
            "\n union \n"+
            " select  L.orgId,L.orgName,L.orgSecretary, ${partSqlBranch} from ("+
            "\n" +
            " SELECT org_id as orgId,org_name as orgName,org_secretary as orgSecretary," +
            " <foreach collection=\"listOptionKeyBranch\" index=\"index\" item=\"key\" open=\" \" separator=\",\" close=\" \">\n"+
            " SUM(if(${key}>0,1,0)) as ${key} " +
            " </foreach> " +
            " FROM t_supervise\n" +
            " WHERE 1=1 and  (  org_level like concat('%-',#{orgId},'-%') )"+
            " and org_type_child in"+
            " <foreach collection=\"branchType\" index=\"index\" item=\"it\" open=\"( \" separator=\",\" close=\") \">\n"+
            "   #{it} " +
            " </foreach> " +
            " group by orgId,orgName, orgSecretary"+
            ") as L group by orgId,orgName "+
            ") as L2 where count>0 " +
            " order by count desc"+
            "</script>")
    Set<Long> listByOrgStaByIsSupervise(@Param(value = "orgId") Long orgId,
                                        @Param(value = "sqlPart") String partSqlCommittee,
                                        @Param(value = "listOptionKeys") List<String> listOptionKeyCommittee,
                                        @Param(value = "partSqlBranch") String partSqlBranch,
                                        @Param(value = "listOptionKeyBranch") List<String> listOptionKeyBranch,
                                        @Param(value = "committeeType") List<Integer> committeeType,
                                        @Param(value = "branchType") List<Integer> branchType);

    @Select(" <script>"+
            "select * from ("+
            "select * from ("+
            " select  L.orgId,L.orgName,L.orgSecretary, ${sqlPart} , parent_id from ("+
            "\n" +
            " SELECT org_id as orgId,org_name as orgName,org_secretary as orgSecretary, parent_id," +
            " <foreach collection=\"listOptionKeys\" index=\"index\" item=\"key\" open=\" \" separator=\",\" close=\" \">\n"+
            " SUM(if(${key}>0,1,0)) as ${key} " +
            " </foreach> " +
            " FROM t_supervise\n" +
            " WHERE 1=1 and  ( org_id=#{orgId}  or   org_level like concat('%-',#{orgId},'-%') )"+
            " and org_type_child in"+
            " <foreach collection=\"committeeType\" index=\"index\" item=\"it\" open=\"( \" separator=\",\" close=\") \">\n"+
            "   #{it} " +
            " </foreach> " +
            " group by orgId,orgName, orgSecretary"+
            ") as L group by orgId,orgName"+
            "\n union \n"+
            " select  L.orgId,L.orgName,L.orgSecretary, ${partSqlBranch},parent_id from ("+
            "\n" +
            " SELECT org_id as orgId,org_name as orgName,org_secretary as orgSecretary, parent_id," +
            " <foreach collection=\"listOptionKeyBranch\" index=\"index\" item=\"key\" open=\" \" separator=\",\" close=\" \">\n"+
            " SUM(if(${key}>0,1,0)) as ${key} " +
            " </foreach> " +
            " FROM t_supervise\n" +
            " WHERE 1=1 and  (  org_level like concat('%-',#{orgId},'-%') )"+
            " and org_type_child in"+
            " <foreach collection=\"branchType\" index=\"index\" item=\"it\" open=\"( \" separator=\",\" close=\") \">\n"+
            "   #{it} " +
            " </foreach> " +
            " group by orgId,orgName, orgSecretary"+
            ") as L group by orgId,orgName "+
            ") as L2 where count>0 " +
            " order by count desc,orgId"+
            ") as L where parent_id=#{orgId} " +
            " <if test=\"orgName !='' and orgName !=null \"> and  orgName like concat('%',#{orgName},'%') </if>"+
            " order by count desc , orgId "+
            "</script>")
    List<SuperviseOrgForm> listByOrgSta(@Param(value = "orgId") Long orgId,
                                        @Param(value = "orgName") String orgName,
                                        @Param(value = "sqlPart") String partSqlCommittee,
                                        @Param(value = "listOptionKeys") List<String> listOptionKeyCommittee,
                                        @Param(value = "partSqlBranch") String partSqlBranch,
                                        @Param(value = "listOptionKeyBranch") List<String> listOptionKeyBranch,
                                        @Param(value = "committeeType") List<Integer> committeeType,
                                        @Param(value = "branchType") List<Integer> branchType);

    /**
     * 得到本组织异常项
     * @param orgId
     * @return
     */
    @Select(" <script>"+
            " select ${sqlPart}\n" +
            " FROM t_supervise\n" +
            " WHERE org_Id= #{orgId}"+
            "</script>")
    Integer superviseOrgSelfOrgAbnormal(@Param(value = "orgId")Long orgId,@Param(value = "sqlPart") String sqlPart);


    /**
     * 得到本组织异常项
     * @param orgId
     * @return
     */
    @Select(" <script>"+
            " select  ${sqlPart} from ("+
            " select  \n" +
            " <foreach collection=\"listOptionKeys\" index=\"index\" item=\"key\" open=\" \" separator=\",\" close=\" \">\n"+
            " SUM(if(${key}>0,1,0)) as ${key} " +
            " </foreach> " +
            " FROM t_supervise\n" +
            " WHERE org_Id= #{orgId}"+
            ") as L"+
            "</script>")
    Integer superviseOrgSelfOrgAbnormalByListKey(@Param(value = "orgId")Long orgId,
                                                 @Param(value = "listOptionKeys") List<String> listOptionKey,
                                                 @Param(value = "sqlPart") String sqlPart);
}