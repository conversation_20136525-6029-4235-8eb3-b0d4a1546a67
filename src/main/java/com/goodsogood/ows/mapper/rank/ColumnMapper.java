package com.goodsogood.ows.mapper.rank;


import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.rank.ColumnEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * Create by FuXiao on 2020/10/19
 */
@Repository
@Mapper
public interface ColumnMapper extends MyMapper<ColumnEntity> {

    @Update("UPDATE t_rank_column SET `status` = 2 WHERE column_id = #{columnId}")
    void deleteColumn(@Param(value = "columnId") Long columnId);

    @Update("UPDATE t_rank_column SET `status` = 2 WHERE top_name = #{topName} AND type = #{type}")
    void deleteByTop(@Param(value = "topName") String topName,
                     @Param(value = "type") Integer type);
}
