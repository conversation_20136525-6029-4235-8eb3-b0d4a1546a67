package com.goodsogood.ows.mapper.rank;

import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Create by FuXiao on 2020/11/16
 */
@Repository
@Mapper
public interface SnapshotMapper {
    @Select("SELECT org_id,org_name FROM t_org_snapshot WHERE org_id = #{orgId} AND date_month=#{monthDate} AND org_type_child in (10280304, 10280309, 10280314, 10280315, 10280319, 10280301, 10280310, 10280322, 10280303, 10280308, 10280311, 10280318 ) AND region_id=3 AND is_retire!=1  AND `status`=1 LIMIT 1")
    @Results({
            @Result(column = "org_id", property = "orgId"),
            @Result(column = "org_name", property = "orgName")
    })
    /**
     * 根据月份查询自动打分需要的快照表组织信息
     */
    OrgSnapshotEntity findOrgSnapshot(@Param(value = "orgId") Long orgId,
                                      @Param(value = "monthDate") String monthDate);

    @Select("SELECT DISTINCT org_id, org_name FROM t_org_snapshot WHERE org_type_child IN ( 10280304, 10280309, 10280314, 10280315, 10280319, 10280301, 10280310, 10280322, 10280303, 10280308, 10280311, 10280318 ) AND region_id = 3 AND date_month LIKE \"${year}%\" AND is_retire != 1 AND org_type=102803 AND `status`=1")
    @Results({
            @Result(column = "org_id", property = "orgId"),
            @Result(column = "org_name", property = "orgName")
    })
    /**
     * 根据年份查询快照表中的所有orgId
     */
    List<OrgSnapshotEntity> findAllOrgSnapshotByYear(@Param(value = "year") Integer year);

    @Select("SELECT user_id, user_name, org_id FROM t_user_snapshot WHERE user_id = #{userId} AND date_month = #{monthDate} AND region_id = 3 AND political_type IN (1, 5, 17, 18) AND `status`=1 LIMIT 1")
    @Results({
            @Result(column = "user_id", property = "userId"),
            @Result(column = "user_name", property = "userName"),
            @Result(column = "org_id", property = "orgId")
    })
    /**
     * 根据月份查询自动打分需要的快照表人员信息
     */
    UserSnapshotEntity findUserSnapshot(@Param(value = "userId") Long userId,
                                              @Param(value = "monthDate") String monthDate);

    @Select("SELECT DISTINCT user_id FROM t_user_snapshot WHERE date_month LIKE \"${year}%\" AND region_id = 3 AND political_type IN (1, 5, 17, 18) AND `status`=1")
    /**
     * 根据年份查询快照表中的所有userId
     */
    List<Long> findAllUserSnapshotByYear(@Param(value = "year") Integer year);

    @Select("SELECT DISTINCT user_id FROM `t_ppmd_pay_log` WHERE pay_date = #{payDate} AND user_name = #{userName} LIMIT 1")
    /**
     * 根据党费缴纳时间和用户名查询用户id
     */
    Long findUserId(@Param(value = "payDate") String payDate,
                      @Param(value = "userName") String userName);
}
