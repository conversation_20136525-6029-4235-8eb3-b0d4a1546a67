package com.goodsogood.ows.mapper.rank;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.rank.RateRuleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * Create by FuXiao on 2020/10/19
 */
@Repository
@Mapper
public interface RateRuleMapper extends MyMapper<RateRuleEntity> {
    @Select("SELECT MAX(rate_rule_id) FROM `t_rank_rate_rule`")
    Integer maxId();
}
