package com.goodsogood.ows.mapper.rank;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.rank.OrgScoreEntity;
import com.goodsogood.ows.model.vo.rank.TotalScoreVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * Create by FuXiao on 2020/10/23
 */
@Repository
@Mapper
public interface OrgScoreMapper extends MyMapper<OrgScoreEntity> {

    @Select("SELECT DISTINCT `year` FROM `t_rank_org_score`")
    /**
     * 查询表中所有的年度
     */
    List<Long> findYear();

    @Select("<script>SELECT org_id AS id,SUM(score) AS score FROM t_rank_org_score WHERE org_id IN <foreach item=\"orgIdList\" collection=\"orgIdList\" open=\"(\" separator=\",\" close=\")\">#{orgIdList}</foreach> AND time LIKE \"${year}%\" GROUP BY org_id ORDER BY score DESC</script>")
    /**
     * 根据orgIdList和year查询每个orgId对应的总分
     */
    List<TotalScoreVo> findOrgTotalScore(@Param(value = "orgIdList") Set<Long> orgIdList,
                                         @Param(value = "year") Integer year);

    @Select("SELECT org_id AS id, SUM(score) AS score FROM t_rank_org_score WHERE org_id = #{orgId} AND time LIKE \"${year}%\"")
    /**
     * 根据orgId和year查询某个组织的总分
     */
    TotalScoreVo findSingleOrgTotal(@Param(value = "orgId") Long orgId,
                                    @Param(value = "year") Integer year);

    @Select("SELECT SUM(score) AS score FROM t_rank_org_score WHERE org_id = #{orgId} AND time = #{time}")
    /**
     * 根据orgId和time查询某个月的得分
     */
    Double findOrgMonthScore(@Param(value = "orgId") Long orgId,
                             @Param(value = "time") String time);

    @Select("SELECT SUM(score) AS score FROM t_rank_org_score WHERE time = #{time} GROUP BY org_id ORDER BY score DESC")
    /**
     * 用于中位数
     */
    List<Double> forMedian(@Param(value = "time") String time);

    @Select("<script>SELECT org_id AS id,SUM(score) AS score FROM t_rank_org_score WHERE org_id IN <foreach item=\"orgIdList\" collection=\"orgIdList\" open=\"(\" separator=\",\" close=\")\">#{orgIdList}</foreach> AND time = #{time} GROUP BY org_id ORDER BY score DESC</script>")
    /**
     * 根据orgIdList和time查询每个orgId对应的总分并排序
     */
    List<TotalScoreVo> findOrgRanking(@Param(value = "orgIdList") Set<Long> orgIdList,
                                         @Param(value = "time") String time);
}
