package com.goodsogood.ows.mapper.rank;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.rank.UserScoreEntity;
import com.goodsogood.ows.model.vo.rank.TotalScoreVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * Create by FuXiao on 2020/10/19
 */
@Repository
@Mapper
public interface UserScoreMapper extends MyMapper<UserScoreEntity> {

    @Select("SELECT DISTINCT `year` FROM `t_rank_user_score`")
    /**
     * 查询表中所有的年度
     */
    List<Long> findYear();

    @Select("<script>SELECT user_id AS id,SUM(score) AS score FROM t_rank_user_score WHERE user_id IN <foreach item=\"userIdList\" collection=\"userIdList\" open=\"(\" separator=\",\" close=\")\">#{userIdList}</foreach> AND time LIKE \"${year}%\" GROUP BY user_id ORDER BY score DESC</script>")
    /**
     * 根据userIdList和year查询每个userId对应的总分
     */
    List<TotalScoreVo> findUserTotalScore(@Param(value = "userIdList") List<Long> userIdList,
                                          @Param(value = "year") Integer year);

    @Select("SELECT user_id AS id, SUM(score) AS score FROM t_rank_user_score WHERE user_id = #{userId} AND time LIKE \"${year}%\"")
    /**
     * 根据orgId和year查询某个组织的总分
     */
    TotalScoreVo findSingleUserTotal(@Param(value = "userId") Long userId,
                                    @Param(value = "year") Integer year);

    @Select("SELECT SUM(score) AS score FROM t_rank_user_score WHERE user_id = #{userId} AND time = #{time}")
    /**
     * 根据userId和time查询某个月的得分
     */
    Double findUserMonthScore(@Param(value = "userId") Long userId,
                             @Param(value = "time") String time);

    @Select("SELECT SUM(score) AS score FROM t_rank_user_score WHERE time = #{time} GROUP BY user_id ORDER BY score DESC")
    /**
     * 用于中位数
     */
    List<Double> forMedian(@Param(value = "time") String time);

    @Select("<script>SELECT user_id AS id,SUM(score) AS score FROM t_rank_user_score WHERE user_id IN <foreach item=\"userIdList\" collection=\"userIdList\" open=\"(\" separator=\",\" close=\")\">#{userIdList}</foreach> AND time = #{time} GROUP BY user_id ORDER BY score DESC</script>")
    /**
     * 根据userIdList和time查询每个userId对应的总分并排序
     */
    List<TotalScoreVo> findUserRanking(@Param(value = "userIdList") Set<Long> userIdList,
                                      @Param(value = "time") String time);
}
