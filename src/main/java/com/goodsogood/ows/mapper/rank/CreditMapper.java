package com.goodsogood.ows.mapper.rank;

import com.goodsogood.ows.model.vo.rank.OrgJoinTotalVO;
import com.goodsogood.ows.model.vo.rank.UserJoinTotalVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 自动打分积分中心部分
 * <AUTHOR>
 * @create 2020-11-05 15:58
 */
@Repository
@Mapper
public interface CreditMapper {


    /**
     * 组织当月换书订单率，换书人数/总人数 向下取整保留整数位
     * @param regionId
     * @param orgId
     * @param queryDate
     * @return
     */
    @Select("<script>" +
            " select floor(buyCount/totalCount*100) r from (select count(1) buyCount from" +
            " (select ttcmp3.userId from (SELECT user_id userId" +
            " from t_user_snapshot where status=1 and region_id=#{regionId} and political_type in(1,5,17,18)" +
            " and (position_code not in(49,50,51,52,53) or position_code is null or position_code='') and date_month=#{queryDate}" +
            " and (org_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%')) " +
            " ) ttcmp1 INNER JOIN (select buyer_id userId from t_score_order where status=1" +
            " and DATE_FORMAT(deal_date,'%Y-%m') = #{queryDate} GROUP BY buyer_id) ttcmp3 on ttcmp1.userId = ttcmp3.userId ) t" +
            " ) t1 LEFT JOIN (SELECT count(1) totalCount from t_user_snapshot where status=1 and region_id=#{regionId}" +
            " and political_type in(1,5,17,18) and (position_code not in(49,50,51,52,53) or position_code is null or position_code='') and date_month=#{queryDate}" +
            " and (org_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%'))  " +
            " ) t2 on 1=1 </script>")
    Integer getOrgOrderRate(@Param("regionId") Long regionId,@Param("orgId") Long orgId, @Param("queryDate") String queryDate);

    /**
     * 组织当月扶贫消费订单率，扶贫人数/总人数  向下取整保留整数位
     * @param regionId
     * @param orgId
     * @param queryDate
     * @return
     */
    @Select("<script>" +
            "select floor(buyCount/totalCount*100) r from (select count(1) buyCount from (select ttcmp3.userId from ( " +
            " SELECT user_id userId from t_user_snapshot where status=1 and region_id=#{regionId} and political_type in(1,5,17,18)" +
            " and (position_code not in(49,50,51,52,53) or position_code is null or position_code='') and date_month=#{queryDate}" +
            " and (org_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%')) " +
            " ) ttcmp1 INNER JOIN (select user_id userId from t_score_poverty_order where oid=3 and" +
            " DATE_FORMAT(trading_time,'%Y-%m') = #{queryDate} GROUP BY user_id) ttcmp3 on ttcmp1.userId = ttcmp3.userId) t" +
            " ) t1 LEFT JOIN (SELECT count(1) totalCount from t_user_snapshot where status=1 and region_id=#{regionId}" +
            " and political_type in(1,5,17,18) and (position_code not in(49,50,51,52,53) or position_code is null or position_code='') and date_month=#{queryDate}" +
            " and (org_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%'))  " +
            " ) t2 on 1=1 </script>")
    Integer getOrgPovertyOrderRate(@Param("regionId") Long regionId,@Param("orgId") Long orgId, @Param("queryDate") String queryDate);


    /**
     * 党员当月换书订单数
     * @param regionId
     * @param userIds
     * @param dateMonth
     * @return
     */
    @Select("<script>" +
            " select buyer_id userId,count(1) joinTotal " +
            " from t_score_order where status=1 and DATE_FORMAT(deal_date,'%Y-%m')" +
            " ='${dateMonth}' and buyer_id in (${userIds}) group by buyer_id" +
            "</script>")
    List<UserJoinTotalVO> getOrderCount(@Param("regionId") Long regionId, @Param("userIds") String userIds, @Param("dateMonth") String dateMonth);

    /**
     * 党员当月消费扶贫订单数
     * @param regionId
     * @param userIds
     * @param dateMonth
     * @return
     */
    @Select("<script>" +
            " select user_id userId,count(1) joinTotal" +
            " from t_score_poverty_order where oid=3 and DATE_FORMAT(trading_time,'%Y-%m') = '${dateMonth}'" +
            "  and user_id in (${userIds}) GROUP BY user_id" +
            "</script>")
    List<UserJoinTotalVO> getPovertyOrderCount(@Param("regionId") Long regionId, @Param("userIds") String userIds, @Param("dateMonth") String dateMonth);

    /**
     * 获取某个组织及其下级组织在某年每个月的扶贫人数和积分换书人数
     *
     * @param sql
     * @return
     */
    @Select("<script>" +
            "${sql}" +
            "</script>")
    List<OrgJoinTotalVO> joinUserTotal(@Param("sql") String sql);
}
