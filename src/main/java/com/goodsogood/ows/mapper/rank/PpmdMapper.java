package com.goodsogood.ows.mapper.rank;

import com.goodsogood.ows.model.vo.rank.PpmdOrgVO;
import com.goodsogood.ows.model.vo.rank.PpmdUserVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 自动打分党费平台部分
 *
 * <AUTHOR>
 * @create 2020-11-05 15:58
 */
@Repository
@Mapper
public interface PpmdMapper {
    /**
     * 组织党费未交人数
     *
     * @param regionId
     * @param orgId
     * @param queryDate
     *
     * @return
     */
    @Select("<script>" +
            " select sum(case when isPay=0 then 1 else 0 end) unfinish from ( " +
            " select ttcmp1.orgId,ttcmp3.userId,ttcmp3.isPay from (SELECT org_id orgId  " +
            " from t_org_snapshot where status=1 and region_id=#{regionId} and date_month=#{queryDate}" +
            " and org_type_child in (10280304,10280309,10280314,10280315,10280319,10280301,10280310,10280322,10280303,10280308,10280311,10280318)" +
            " and (org_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%')) " +
            " ) ttcmp1 INNER JOIN ( " +
            " SELECT tp1.orgId,tp1.userId,IF(tp2.payDate is null,0,1) isPay from ( " +
            " select tt1.* from (SELECT org_id orgId,user_id userId FROM (  " +
            " select org_id,user_id,type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo, " +
            " @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b " +
            " where region_id=#{regionId} and DATE_FORMAT(start_time,'%Y-%m') &lt;= #{queryDate} and" +
            " IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{queryDate} " +
            " ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4) " +
            " ) tt1 INNER JOIN (SELECT user_id userId from t_user_snapshot where status=1 and region_id=#{regionId} and political_type in(1,5,17,18) and (position_code not in(49,50,51,52,53) or position_code is null or position_code='')" +
            " and date_month=#{queryDate}) tt2 on tt1.userId = tt2.userId) tp1 LEFT JOIN ( " +
            " SELECT user_id userId,pay_date payDate from t_ppmd_pay_log where region_id=#{regionId} and" +
            " pay_for =#{queryDate} and pay_log_type =1) tp2 on tp1.userId=tp2.userId " +
            " ) ttcmp3 on ttcmp1.orgId = ttcmp3.orgId) tab" +
            "</script>")
    Integer getUnfinishNum(@Param("regionId") Long regionId, @Param("orgId") Long orgId, @Param("queryDate") String queryDate);


    /**
     * 组织党费未设置数量
     *
     * @param regionId
     * @param orgId
     * @param queryDate
     *
     * @return
     */
    @Select("<script>" +
            " select count(1) cn from (" +
            " SELECT org_id orgId from t_org_snapshot where status=1 and region_id=#{regionId} and date_month=#{queryDate} " +
            " and org_type_child in (10280304,10280309,10280314,10280315,10280319,10280301,10280310,10280322,10280303,10280308,10280311,10280318)" +
            " and (org_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%'))" +
            " ) ttcmp1 INNER JOIN (select tt1.* from (SELECT org_id orgId,user_id userId,type FROM ( " +
            " select org_id,user_id,type,ratio_type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b" +
            " where region_id=#{regionId} and DATE_FORMAT(start_time,'%Y-%m') &lt;= #{queryDate} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{queryDate}" +
            " ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type =4 and t1.ratio_type!=2" +
            " ) tt1 INNER JOIN (SELECT user_id userId from t_user_snapshot where status=1 and region_id=#{regionId} and political_type in(1,5,17,18) and (position_code not in(49,50,51,52,53) or position_code is null or position_code='')" +
            " and date_month=#{queryDate}) tt2 on tt1.userId = tt2.userId) ttcmp3 on ttcmp1.orgId = ttcmp3.orgId" +
            "</script>")
    Integer getUnsetNum(@Param("regionId") Long regionId, @Param("orgId") Long orgId, @Param("queryDate") String queryDate);


    /**
     * 组织党费交齐日期
     * 补交不算交齐，返回0表示未交齐
     *
     * @param regionId
     * @param orgId
     * @param queryDate
     *
     * @return
     */
    @Select("<script>" +
            " select IF(unfinish &gt; 0,0,maxPayDay) finishDay from (" +
            " select sum(case when isPay=0 then 1 else 0 end) unfinish,max(payDay) maxPayDay from (" +
            " select ttcmp1.orgId,ttcmp3.userId,ttcmp3.isPay,payDay from (" +
            " SELECT org_id orgId from t_org_snapshot where status=1 and region_id=#{regionId} and date_month=#{queryDate}" +
            " and org_type_child in (10280304,10280309,10280314,10280315,10280319,10280301,10280310,10280322,10280303,10280308,10280311,10280318)" +
            " and (org_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%'))" +
            " ) ttcmp1 INNER JOIN (" +
            " SELECT tp1.orgId,tp1.userId,IF(tp2.payDate is null,0,1) isPay,DATE_FORMAT(payDate,'%d') payDay from (" +
            " select tt1.* from (SELECT org_id orgId,user_id userId FROM ( " +
            " select org_id,user_id,type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b" +
            " where region_id=#{regionId} and DATE_FORMAT(start_time,'%Y-%m') &lt;= #{queryDate} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{queryDate}" +
            " ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4)" +
            " ) tt1 INNER JOIN (SELECT user_id userId from t_user_snapshot where status=1 and region_id=#{regionId} and political_type in (1,5,17,18) and (position_code not in(49,50,51,52,53) or position_code is null or position_code='')" +
            " and date_month=#{queryDate}) tt2 on tt1.userId = tt2.userId" +
            " ) tp1 LEFT JOIN (SELECT user_id userId,pay_date payDate from t_ppmd_pay_log where region_id=#{regionId} and" +
            " pay_for =#{queryDate} and pay_log_type =1) tp2 on tp1.userId=tp2.userId) ttcmp3" +
            " on ttcmp1.orgId = ttcmp3.orgId) retab ) tab" +
            "</script>")
    Integer getFinishDay(@Param("regionId") Long regionId, @Param("orgId") Long orgId, @Param("queryDate") String queryDate);


    /**
     * 党员当月交纳党费日期
     * 党员未交，返回空
     *
     * @param regionId
     * @param userIds
     * @param queryDate
     *
     * @return
     */
    @Select("<script>" +
            " SELECT user_id userId,DATE_FORMAT(pay_date,'%d') payDay from t_ppmd_pay_log where region_id=${regionId} and" +
            " pay_for ='${queryDate}' and pay_log_type =1 and user_id in (${userIds}) </script>")
    List<PpmdUserVO> getUserPayDay(@Param("regionId") Long regionId, @Param("userIds") String userIds, @Param("queryDate") String queryDate);


    /**
     * 组织党费执行SQL
     *
     * @param sql
     *
     * @return
     */
    @Select("<script>" +
            "${sql}" +
            "</script>")
    List<PpmdOrgVO> executePpmdOrgSql(@Param("sql") String sql);

}
