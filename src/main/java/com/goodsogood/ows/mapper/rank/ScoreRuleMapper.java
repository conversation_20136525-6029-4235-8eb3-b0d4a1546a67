package com.goodsogood.ows.mapper.rank;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.rank.ScoreRuleEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Create by FuXiao on 2020/10/19
 */
@Repository
@Mapper
public interface ScoreRuleMapper extends MyMapper<ScoreRuleEntity> {

    @Select("SELECT percentage FROM t_rank_score_rule WHERE score_rule_id = #{scoreRuleId}")
    /**
     * 查询系数
     */
    Double findPerCent(@Param(value = "scoreRuleId") Long scoreRuleId);

    @Update("UPDATE t_rank_top t, t_rank_user_score us, t_rank_org_score os SET t.percentage = #{percentage}, us.percentage = #{percentage}, os.percentage = #{percentage} WHERE t.top_id = #{topId} AND us.top_id = #{topId} AND os.top_id = #{topId}")
    /**
     * 修改系数
     */
    void updatePercent(@Param(value = "topId") Long topId,
                       @Param(value = "percentage") Double percentage);

    @Select("SELECT score_rule_id,rule_description FROM t_rank_score_rule WHERE column_id IN (SELECT column_id FROM t_rank_column WHERE top_id = (SELECT top_id FROM t_rank_top WHERE top_name = \"组织自动打分\"))")
    @Results({
            @Result(column = "score_rule_id", property = "scoreRuleId"),
            @Result(column = "rule_description", property = "ruleDescription")
    })
    /**
     * 查询所有需要组织自动打分的规则
     */
    List<ScoreRuleEntity> orgAutoRule();

    @Select("SELECT score_rule_id,rule_description FROM t_rank_score_rule WHERE column_id IN (SELECT column_id FROM t_rank_column WHERE top_id = (SELECT top_id FROM t_rank_top WHERE top_name = \"党员自动打分\"))")
    @Results({
            @Result(column = "score_rule_id", property = "scoreRuleId"),
            @Result(column = "rule_description", property = "ruleDescription")
    })
    /**
     * 查询所有需要党员自动打分的规则
     */
    List<ScoreRuleEntity> UserAutoRule();


    @Select("SELECT rule_description FROM t_rank_score_rule WHERE score_rule_id = #{scoreRuleId}")
    /**
     * 根据规则id查询规则描述
     */
    String findRuleDescription(@Param(value = "scoreRuleId") Long scoreRuleId);

}
