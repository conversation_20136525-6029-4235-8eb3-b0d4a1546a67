package com.goodsogood.ows.mapper.rank;

import com.goodsogood.ows.model.vo.rank.CompensateVo;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Create by FuXiao on 2020/12/9
 */
@Repository
@Mapper
public interface CompensateMapper {
    @Select("SELECT a.org_id AS id, a.score_rule_id AS score_rule_id FROM ( SELECT s.org_id, r.score_rule_id FROM ( SELECT org_id FROM t_rank_org_score WHERE time LIKE \"${year}%\" GROUP BY org_id ) s, ( SELECT score_rule_id FROM t_rank_score_rule WHERE column_id IN ( SELECT column_id FROM t_rank_column WHERE top_id = 1 )) r ) a LEFT JOIN ( SELECT org_id, score_rule_id FROM t_rank_org_score WHERE time LIKE \"${year}%\" GROUP BY org_id, score_rule_id ORDER BY org_id, score_rule_id ) b ON a.org_id = b.org_id AND a.score_rule_id = b.score_rule_id WHERE b.score_rule_id IS NULL")
    @Results({
            @Result(column = "id", property = "id"),
            @Result(column = "score_rule_id", property = "scoreRuleId")
    })
    List<CompensateVo> orgCompensateList(@Param(value = "year") Integer year);

    @Select("SELECT a.user_id AS id, a.score_rule_id AS score_rule_id FROM ( SELECT s.user_id, r.score_rule_id FROM ( SELECT user_id FROM t_rank_user_score WHERE time LIKE \"${year}%\" GROUP BY user_id ) s, ( SELECT score_rule_id FROM t_rank_score_rule WHERE column_id IN ( SELECT column_id FROM t_rank_column WHERE top_id = 2 )) r ) a LEFT JOIN ( SELECT user_id, score_rule_id FROM t_rank_user_score WHERE time LIKE \"${year}%\" GROUP BY user_id, score_rule_id ORDER BY user_id, score_rule_id ) b ON a.user_id = b.user_id AND a.score_rule_id = b.score_rule_id WHERE b.score_rule_id IS NULL")
    @Results({
            @Result(column = "id", property = "id"),
            @Result(column = "score_rule_id", property = "scoreRuleId")
    })
    List<CompensateVo> userCompensateList(@Param(value = "year") Integer year);
}
