package com.goodsogood.ows.mapper.constitution

import com.goodsogood.ows.mapper.MyMapper
import com.goodsogood.ows.model.vo.constitution.ConstitutionCountVO
import com.goodsogood.ows.model.vo.count.ReadClassCountVO
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select

@Mapper
interface ConstitutionMapper :MyMapper<ConstitutionCountVO>{

    @Select("""
        <script>
            SELECT
                tm.org_id AS orgId,
                tm.org_name AS orgName,
                COUNT(DISTINCT tm.meeting_id) AS times
            FROM
                t_meeting tm
            LEFT JOIN t_meeting_tag tmt ON tm.meeting_id = tmt.meeting_id
            WHERE
                tm.`status` IN (7, 12, 13, 14)
                AND tm.is_del = 0
                <if test="tag!=null">
                    AND tmt.tag_id = #{tag}
                </if>
                 <if test="inOrNot and orgIds != null and !orgIds.isEmpty()">
                    AND tm.org_id IN
                    <foreach item="orgId" collection="orgIds" open="(" separator="," close=")">
                        #{orgId}
                    </foreach>
                </if>
                <if test="!inOrNot and orgIds != null and !orgIds.isEmpty()">
                    AND tm.org_id NOT IN
                    <foreach item="orgId" collection="orgIds" open="(" separator="," close=")">
                        #{orgId}
                    </foreach>
                </if>
                <if test="name != null">
                    AND tm.org_name LIKE CONCAT('%', #{name}, '%')
                </if>
                <if test="year != null">
                    AND YEAR(tm.start_time) = #{year}
                </if>
            GROUP BY
                tm.org_id;
        </script>
    """)
    fun countElseOrgByList(orgIds:List<Long>,name: String?, year: Int?,tag:Long?,inOrNot:Boolean):List<ConstitutionCountVO>


    @Select("""
        <script>
            SELECT
                tm.org_id AS orgId,
                tm.org_name AS orgName,
                COUNT(DISTINCT tm.meeting_id) AS times
            FROM
                t_meeting tm
            LEFT JOIN t_meeting_tag tmt ON tm.meeting_id = tmt.meeting_id
            WHERE
                tm.`status` IN (7, 12, 13, 14)
                <if test="tag!=null">
                    AND tmt.tag_id = #{tag}
                </if>
                AND tm.is_del = 0
                AND tm.types = '党小组会议'
                <if test="year != null">
                    AND YEAR(tm.start_time) = #{year}
                </if>
            GROUP BY
                tm.org_id;
        </script>
    """)
    fun countALLGroup(year:Int?,tag:Long?):List<ConstitutionCountVO>

    @Select("""
        <script>
            SELECT
                tm.org_id AS orgId,
                tm.org_name AS orgName,
                COUNT(DISTINCT tm.meeting_id) AS times
            FROM
                t_meeting tm
            LEFT JOIN t_meeting_tag tmt ON tm.meeting_id = tmt.meeting_id
            WHERE
                tm.`status` IN (7, 12, 13, 14)
                <if test="tag!=null">
                    AND tmt.tag_id = #{tag}
                </if>
                AND tm.is_del = 0
                AND tm.types <![CDATA[ <> ]]> '党小组会议'
                <if test="year != null">
                    AND YEAR(tm.start_time) = #{year}
                </if>
            GROUP BY
                tm.org_id;
        </script>
    """)
    fun countExceptGroup(year:Int?,tag:Long?):List<ConstitutionCountVO>
}