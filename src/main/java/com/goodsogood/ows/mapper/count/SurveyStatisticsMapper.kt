package com.goodsogood.ows.mapper.count

import com.goodsogood.ows.mapper.MyMapper
import com.goodsogood.ows.model.vo.count.SurveyStatisticVO
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

@Mapper
interface SurveyStatisticsMapper : MyMapper<SurveyStatisticVO> {

    @Select(
        """
    <script>
        SELECT  
                CASE WHEN `subject`=1 THEN target_id_split END AS leaderId,
                CASE WHEN `subject`=1 THEN target_split END AS leaderName,
                CASE WHEN `subject`=2 THEN target_id_split END AS departmentId,
                CASE WHEN `subject`=2 THEN target_split END AS department,
            GROUP_CONCAT(interview SEPARATOR ',') AS interviewConcat,
                sum(num_customer) AS total,`subject`,count(1) AS times,`year`
        FROM (
            SELECT DISTINCT 
            REPLACE(REPLACE(SUBSTRING_INDEX(SUBSTRING_INDEX(t.target, ',', numbers.n), ',', - 1), '\"', ''), ' ', '') AS target_split,
            REPLACE(REPLACE(SUBSTRING_INDEX(SUBSTRING_INDEX(t.target_id, ',', numbers.n), ',', - 1), '\"', ''), ' ', '') AS target_id_split,
            t.leader_survey_id,t.interview,t.num_customer,t.`subject`,t.`year`
        FROM
            t_meeting_leader_survey t
        CROSS JOIN
            (
                SELECT 1 AS n UNION ALL
                SELECT 2 UNION ALL
                SELECT 3 UNION ALL
                SELECT 4 UNION ALL
                SELECT 5 UNION ALL
                SELECT 6 UNION ALL
                SELECT 7 UNION ALL
                SELECT 8 UNION ALL
                SELECT 9 UNION ALL
                SELECT 10 
            ) AS numbers 
        ) AS temp
        WHERE 1=1
        <if test="leader_name != null and leader_name != ''">
          AND target_split LIKE CONCAT('%', #{leader_name}, '%') AND `subject`=1
        </if>
        <if test="department != null and department != ''">
            AND target_split LIKE CONCAT('%', #{department}, '%') AND `subject`=2
        </if>
        <if test="interview != null">
            AND interview like CONCAT('%', #{interview}, '%')
        </if>
        <if test="year != null">
            AND `year` = #{year}
        </if>
        GROUP BY target_id_split, `subject`
        ORDER BY target_id_split;
    </script>
    """
    )
    fun getSurveyStatistics(
        @Param("leader_name") leaderName: String?,
        @Param("department") department: String?,
        @Param("interview") interview: Int?,
        @Param("year") year: Int?
    ): List<SurveyStatisticVO>
}