package com.goodsogood.ows.mapper.count

import com.goodsogood.ows.mapper.MyMapper
import com.goodsogood.ows.model.vo.count.ReadClassCountVO
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import java.util.LinkedList

@Mapper
interface ReadClassCountMapper :MyMapper<ReadClassCountVO>{

    @Select("""
        <script>
         SELECT
                    subquery.orgId,subquery.orgName,
                COUNT(*) AS times, 
                SUM(total_hours) AS duration
            FROM
            (
                SELECT
                    DISTINCT tm.meeting_id, tm.org_id AS orgId, tm.org_name AS orgName, tm.total_hours
                FROM
                    t_meeting tm
                LEFT JOIN t_meeting_tag tmt ON tm.meeting_id = tmt.meeting_id
                WHERE
                    tm.`status` IN (7, 12, 13, 14)
                <if test = "tag!= null">
                    AND tmt.tag_id = #{tag}
                </if>
                    AND tm.is_del = 0
                 <if test="inOrNot and orgIds != null and !orgIds.isEmpty()">
                    AND tm.org_id IN
                    <foreach item="orgId" collection="orgIds" open="(" separator="," close=")">
                        #{orgId}
                    </foreach>
                </if>
                <if test="!inOrNot and orgIds != null and !orgIds.isEmpty()">
                    AND tm.org_id NOT IN
                    <foreach item="orgId" collection="orgIds" open="(" separator="," close=")">
                        #{orgId}
                    </foreach>
                </if>
                <if test="orgName!= null">
                    AND tm.org_name LIKE CONCAT('%', #{orgName}, '%')
                </if>
                <if test="year!= null">
                    AND YEAR(tm.start_time) = #{year}
                </if>
            )AS subquery
            GROUP BY subquery.orgId;
        </script>
    """)
    fun countElseOrgByList(orgIds:List<Long>, @Param("orgName")name: String?, year: Int?, tag:Long?,inOrNot:Boolean):List<ReadClassCountVO>


    @Select("""
        <script>
         SELECT
                    subquery.orgId,subquery.orgName,
                COUNT(*) AS times, 
                SUM(total_hours) AS duration
            FROM
            (
                SELECT
                    DISTINCT tm.meeting_id, tm.org_id AS orgId, tm.org_name AS orgName, tm.total_hours
                FROM
                    t_meeting tm
                LEFT JOIN t_meeting_tag tmt ON tm.meeting_id = tmt.meeting_id
                WHERE
                    tm.`status` IN (7, 12, 13, 14)
                <if test = "tag!= null">
                    AND tmt.tag_id = #{tag}
                </if>
                    AND tm.is_del = 0
                    AND tm.types = '党小组会'
                    <if test="year!= null">
                        AND YEAR(tm.start_time) = #{year}
                    </if>
            ) AS subquery
            GROUP BY subquery.orgId;
        </script>
    """)
    fun countALLGroup(year:Int?,tag:Long?):List<ReadClassCountVO>



    //                    SELECT
    //                        tm.org_id AS orgId,
    //                        tm.org_name AS orgName,
    //                        COUNT(1) AS times,
    //                        SUM(total_hours) AS duration
    //                    FROM
    //                        t_meeting tm
    //                    LEFT JOIN t_meeting_tag tmt ON tm.meeting_id = tmt.meeting_id
    //                    WHERE
    //                        tm.`status` IN (7, 12, 13, 14)
    //                        AND tm.is_del = 0
    //                        AND tm.types <![CDATA[ <> ]]>  '党小组会议'
    //                        <if test = "tag!= null">
    //                            AND tmt.tag_id = #{tag}
    //                        </if>
    //                        <if test="year!= null">
    //                            AND YEAR(tm.start_time) = #{year}
    //                        </if>
    //                    GROUP BY
    //                        tm.org_id;
    @Select("""
        <script>
            SELECT
                    subquery.orgId,subquery.orgName,
                COUNT(*) AS times, 
                SUM(total_hours) AS duration
            FROM
                (
                    SELECT
                        DISTINCT tm.meeting_id, tm.org_id AS orgId, tm.org_name AS orgName, tm.total_hours
                    FROM
                        t_meeting tm
                    LEFT JOIN
                        t_meeting_tag tmt ON tm.meeting_id = tmt.meeting_id
                    WHERE
                        tm.`status` IN (7, 12, 13, 14)
                        AND tm.is_del = 0
                        AND tm.types <![CDATA[ <> ]]> '党小组会'
                        <if test = "tag!= null">
                            AND tmt.tag_id = #{tag}
                        </if>
                        <if test="year!= null">
                            AND YEAR(tm.start_time) = #{year}
                        </if>
                ) AS subquery
            GROUP BY subquery.orgId;
        </script>
    """)
    fun countExceptGroup(year:Int?,tag:Long?):List<ReadClassCountVO>
}