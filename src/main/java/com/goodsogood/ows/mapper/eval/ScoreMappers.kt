package com.goodsogood.ows.mapper.eval

import com.goodsogood.ows.mapper.MyMapper
import com.goodsogood.ows.model.db.eval.v2.OrgScoreEntity
import com.goodsogood.ows.model.db.eval.v2.UnitScoreEntity
import org.apache.ibatis.annotations.*

// OrgScoreEntity mapper
@Mapper
interface EvalOrgScoreMapper : MyMapper<OrgScoreEntity> {
    // 通过taskId获取积分结果，结果通过metric_class_id,metric_id,unit_id汇总汇总
    @Select(
        """
        SELECT
            metric_class_id as metricClassId,
            metric_id as metricId,
            unit_id as unitId,
            sum(score) as score
        FROM
            t_eval_v2_org_score
        WHERE
            task_id = #{taskId}
        GROUP BY
            metric_class_id,metric_id,unit_id
    """
    )
    fun selectByTaskIdGroup(taskId: String): List<OrgScoreEntity>
}


// UnitScoreEntity mapper
@Mapper
interface EvalUnitScoreMapper : MyMapper<UnitScoreEntity> {
    @Delete(
        """
        DELETE FROM t_eval_v2_unit_score WHERE 1 = 1
    """
    )
    fun deleteAll(): Long

    // 批量插入 // UnitScoreEntity 对应表 t_eval_v2_unit_score
    @Insert(
        """
    <script>
        INSERT INTO t_eval_v2_unit_score(
            year, 
            metric_class_id, 
            metric_id, 
            org_id, 
            org_name, 
            org_level, 
            unit_id, 
            unit_name, 
            sys_score, 
            assmt_score, 
            party_score, 
            final_score, 
            modified_flag,
            remark, 
            create_time, 
            last_change_user, 
            update_time
        )
        VALUES
        <foreach collection="unitScores" item="item" index="index" separator=",">
            (#{item.year}, #{item.metricClassId}, #{item.metricId}, #{item.orgId}, 
            #{item.orgName}, #{item.orgLevel}, #{item.unitId}, #{item.unitName}, 
            #{item.sysScore}, #{item.assmtScore}, #{item.partyScore}, 
            #{item.finalScore}, #{item.modifiedFlag}, #{item.remark}, #{item.createTime}, #{item.lastChangeUser}, #{item.updateTime})
        </foreach>
    </script>
    """
    )
    fun insertBatch(@Param("unitScores") unitScores: List<UnitScoreEntity>): Long

}