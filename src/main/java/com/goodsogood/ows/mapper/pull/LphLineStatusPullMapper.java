package com.goodsogood.ows.mapper.pull;


import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.history.LphLineStatusEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @create 2021-05-08 13:47
 **/
@Repository
@Mapper
public interface LphLineStatusPullMapper extends MyMapper<LphLineStatusEntity> {

    @Update("TRUNCATE TABLE #{tableName}")
    int truncateTable(@Param(value = "tableName") String tableName);

}