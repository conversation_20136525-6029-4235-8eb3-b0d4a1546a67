package com.goodsogood.ows.mapper.utilsProxy;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.utilsProxy.BlockchainAmountEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * 区块链上链次数统计
 * <AUTHOR>
 * @date 2022-04-19
 * @return
 */
@Mapper
@Repository
public interface BlockchainAmountMapper extends MyMapper<BlockchainAmountEntity> {
    @Select("<script>SELECT sum(amount) amount FROM t_utils_proxy_statistics WHERE region_id = #{regionId} </script>")
    /**
     * 查询缴纳人数和最后缴纳时间
     * @param regionId
     * @return
     */
    Integer findBlockchainAmount(@Param(value = "regionId") Long regionId);

}
