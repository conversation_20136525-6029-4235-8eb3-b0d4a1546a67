package com.goodsogood.ows.mapper.experience;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.experience.UserPartyEvalEntity;
import com.goodsogood.ows.model.vo.experience.OrgUserReportVO;
import com.goodsogood.ows.model.vo.experience.ReportChooseForm;
import com.goodsogood.ows.model.vo.experience.UserPartyReportVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-03-11 10:52
 **/
@Repository
@Mapper
public interface UserPartyEvalMapper extends MyMapper<UserPartyEvalEntity> {

    @Select(" select eval_id as evalId,date_month as dateMonth,type,user_id as userId from t_user_party_eval \n" +
            " where region_id=#{regionId} and (org_id = #{orgId} OR org_level like CONCAT('%-', #{orgId}, '-%')) " +
            "and left(date_month,4)=#{year} and date_month<(#{dateMonth}+1)\n" +
            " and cal_status=1")
    List<UserPartyEvalEntity> queryOrgTimes(@Param("regionId") Long regionId,@Param("orgId") Long orgId,
                                            @Param("year")String year, @Param("dateMonth")Integer dateMonth);

    @Select("SELECT user_name as userName,position ,tag_add as tagAdd,avatar,\n" +
            "tag_reduce as tagReduce,total_star as totalStart,org_name as orgName,eval_date as evalDate\n" +
            "FROM t_user_party_eval WHERE user_id=#{userId} and cal_status=1 and " +
            " create_time> #{startTime} and create_time<#{endTime}")
    List<UserPartyEvalEntity> report(@Param(value = "userId") Long userId,
                                     @Param(value = "startTime") String startTime,
                                     @Param(value = "endTime") String endTime);

    @Select(" select task_id as taskId from t_user_party_eval where user_id=#{userId} and type=1 and date_month=\n" +
            " (select max(date_month) \n" +
            "  from t_user_party_eval where user_id=#{userId} and type=1)  limit 1")
    String queryLastMonthEval(Long userId);

    @Select("select task_id as taskId,date_format(eval_date,'%Y-%m') as evalDate,total_star as totalStar" +
            " from t_user_party_eval \n" +
            " where type=1 and user_id=#{userId} and cal_status=1 order by eval_date desc limit 5")
    List<UserPartyEvalEntity> queryLastFiveMonth(Long userId);


    @Select("select task_id as taskId,date_format(eval_date,'%Y-%m') as evalDate,total_star as totalStar" +
            " from t_user_party_eval \n" +
            " where type=1 and user_id=#{userId} and type=1 and cal_status=1 and date_month>=#{startMonth} and date_month<=#{endMonth} " +
            " order by eval_date desc ")
    List<UserPartyEvalEntity> queryMonthReport(@Param("userId") Long userId,@Param("startMonth") Integer startMonth,@Param("endMonth") Integer endMonth);

    @Select("SELECT eval_id as evalId, user_name as userName,position ,tag_add as tagAdd,avatar,\n" +
            "tag_reduce as tagReduce,total_star as totalStar,org_name as orgName,eval_date as evalDate\n" +
            "FROM t_user_party_eval WHERE user_id=#{userId} and cal_status=1 and type=1 and " +
            "  eval_date   like CONCAT('',#{year},'%')")
    List<UserPartyEvalEntity> reportByYear(@Param(value = "year") Integer year, @Param(value = "userId") Long userId);


    @Select(" select total_star as star,count(0) as num from t_user_party_eval \n" +
            " where region_id=#{regionId} and (org_id = #{orgId} OR org_level like CONCAT('%-', #{orgId}, '-%'))\n" +
            " and date_month=#{dateMonth} and cal_status=1 and type=1\n" +
            " group by total_star order by total_star")
    List<UserPartyReportVO.TypeStarVO> queryStarPieChart(Long regionId, Long orgId, Integer dateMonth);


    @Select("select t2.rule_id as ruleId,t2.star as star,count(0) as num from t_user_party_eval t1\n" +
            "join t_user_party_eval_result t2 on t1.eval_id=t2.eval_id " +
            " where region_id=#{regionId} and (org_id = #{orgId} OR org_level like CONCAT('%-', #{orgId}, '-%'))\n" +
            " and date_month=#{dateMonth} and cal_status=1 and type=1\n" +
            " group by t2.rule_id,t2.star order by t2.rule_id,t2.star")
    List<UserPartyReportVO.TypeStarVO> queryPillarChart(Long regionId, Long orgId, Integer dateMonth);

    @Select(" select count(0)  from t_user_party_eval \n" +
            " where region_id=#{regionId} and (org_id = #{orgId} OR org_level like CONCAT('%-', #{orgId}, '-%'))\n" +
            " and date_month=#{dateMonth} and cal_status=1 and type=1 and FIND_IN_SET(#{tag},tag_add)>0\n")
    Long queryTag(Long regionId, Long orgId, Integer dateMonth, String tag);

    @Select("<script>"+
            " select count(0)  from t_user_party_eval \n" +
            " where region_id=#{regionId} and (org_id = #{orgId} OR org_level like CONCAT('%-', #{orgId}, '-%'))\n" +
            " and date_month=#{dateMonth} and cal_status=1 and type=1 \n"+
            " <choose> "+
            "<when test=\" type==1\">" +
            " and (  tag_add  is null or (find_in_set('学习达人',tag_add)=0 and find_in_set('学习标兵',tag_add)=0 and find_in_set('学习先锋',tag_add)=0 )) </when>" +
            "<when test=\" type==2\">" +
            " and (  tag_add  is null or (find_in_set('担当之星',tag_add)=0 and find_in_set('担当模范',tag_add)=0 and find_in_set('担当先锋',tag_add)=0 )) </when>" +
            "<when test=\" type==3\">" +
            " and (  tag_add  is null or (find_in_set('以身作则',tag_add)=0 and find_in_set('恪尽职守',tag_add)=0 and find_in_set('责任先锋',tag_add)=0 )) </when>" +
            "<when test=\" type==4\">" +
            " and (  tag_add  is null or (find_in_set('业务先锋',tag_add)=0 and find_in_set('业务模范',tag_add)=0 and find_in_set('业务之星',tag_add)=0 )) </when>" +
            "<when test=\" type==5\">" +
            " and (  tag_add  is null or (find_in_set('数智新手',tag_add)=0 and find_in_set('数智达人',tag_add)=0 and find_in_set('数智专家',tag_add)=0 )) </when>" +
            "<otherwise></otherwise></choose>"+
            " </script>")
    Integer queryNoTag(Long regionId, Long orgId, Integer dateMonth, Integer type);

    @Select("<script>"+
            " select eval_id as evalId,tag_add as tag,user_id as userId,user_name as userName, total_star as star " +
            " from t_user_party_eval \n" +
            " where region_id=#{regionId} and (org_id = #{orgId} OR org_level like CONCAT('%-', #{orgId}, '-%'))\n" +
            " and date_month=#{dateMonth} and cal_status=1 and type=1  order by total_star desc,user_id desc "+
            "<if test=\"num != null \">" +
            " limit #{num}\n"+
            " </if>" +
            "</script>")
    List<OrgUserReportVO> queryUserReport(@Param("regionId")Long regionId, @Param("orgId")Long orgId,
                                          @Param("dateMonth")Integer dateMonth, @Param("num")Integer num);

    @Select("<script>"+
            " select org_id as orgId,org_name as orgName,eval_id as evalId,#{dateMonthStr} as dateMonth,tag_add as tagAdd," +
            "tag_reduce as tagReduce,tag_add as tag,user_id as userId,user_name as userName, total_star as star, " +
            " '综合测评' as item,'综合' as type "+
            " from t_user_party_eval \n" +
            " where region_id=#{regionId} and (org_id = #{orgId} OR org_level like CONCAT('%-', #{orgId}, '-%'))\n" +
            " and date_month=#{dateMonth} and cal_status=1 and type=1\n"+
            "<if test=\"tag != null  and tag.size()>0 \">" +
            " and (" +
            "   <foreach collection=\"tag\" item=\"tagvo\" open=\"\" separator=\"or \" close=\"\"> " +
            " find_in_set(#{tagvo},tag_add)" +
            "   </foreach>" +
            ")" +
            " </if>" +
            "</script>")
    List<OrgUserReportVO> queryUserReportMore(@Param("regionId")Long regionId, @Param("orgId")Long orgId,
                                              @Param("dateMonth")Integer dateMonthm,
                                              @Param("dateMonthStr")String dateMonthStr,
                                              @Param("tag")List<String> tag);

    @Select(
            " select org_id as orgId,org_name as orgName,eval_id as evalId,tag_add as tag,user_id as userId,user_name as userName, total_star as star " +
            " from t_user_party_eval \n" +
            " where region_id=#{regionId} \n" +
            " and date_month=#{dateMonth} and cal_status=1 and type=1 order by org_id"
//            "and org_id in \n"+
//            " <foreach collection=\"orgIds\" item=\"orgId\" open=\"(\" separator=\",\" close=\")\"> " +
//            " #{orgId}" +
//            " </foreach>" +
           )
    List<OrgUserReportVO> queryUserReportByOrgIds(@Param("regionId")Long regionId,
                                                  @Param("dateMonth")Integer dateMonth);

    @Select("<script>"+
            "select eval_id as evalId,user_id as userId,user_name as userName, total_star as star, #{dateMonthStr} as dateMonth," +
            " '综合测评' as item,'综合' as type "+
            " from t_user_party_eval \n" +
            " where region_id=#{regionId}  and (org_id = #{orgId} OR org_level like CONCAT('%-', #{orgId}, '-%')) "+
            "<if test=\"userName != null \">" +
            "and user_name like concat('%',#{userName},'%')  \n" +
            " </if>" +
            " and date_month=#{dateMonth} and cal_status=1 and type=1 \n"+
            "<if test=\"tag != null and tag.size()>0 \">" +
            " and (" +
            "  <foreach collection=\"tag\" item=\"tagvo\" open=\"\" separator=\"or \" close=\"\"> " +
            " find_in_set(#{tagvo},tag_add)" +
            "   </foreach>" +
             ")" +
             " </if>" +
             "</script>")
    List<OrgUserReportVO> queryUserReportByName(ReportChooseForm chooseForm);


    @Select("<script>" +
            " select t1.eval_id as evalId,#{dateMonthStr} as dateMonth,tag_add as tagAdd,tag_reduce as tagReduce,"+
            " t1.tag_add as tag,t1.user_id as userId,t1.user_name as userName,t1.total_star as totalStar,"+
            " if(#{type}=0,t1.total_star,t2.star) as star,"+
            " '综合测评' as item,if(#{type}=0,'综合',if(#{type}=1,'理论武装',if(#{type}=2,'担当作为',if(#{type}=3,'义务履行',if(#{type}=4,'业务实绩','活跃程度'))))) as type from t_user_party_eval t1 \n" +
            " join t_user_party_eval_result t2 on t1.eval_id=t2.eval_id" +
            " where t1.region_id=#{regionId} and (t1.org_id = #{orgId} OR t1.org_level like CONCAT('%-', #{orgId}, '-%'))\n" +
            " and t1.date_month=#{dateMonth} and t1.cal_status=1 and t1.type=1  " +
            "<if test=\"type != null and type!=0\">" +
            " and t2.rule_id=#{type}"+
            "</if>"+
            "<if test=\"evalId != null \">" +
            " and t1.eval_id in" +
            "   <foreach collection=\"evalIds\" item=\"evalId\" open=\"(\" separator=\",\" close=\")\"> " +
            "   #{evalId}" +
            "   </foreach>" +
            " </if>" +
            "<if test=\"userName != null \">" +
            "and t1.user_name like concat('%',#{userName},'%')  \n" +
            " </if>" +
            "<if test=\"star != null and star.size()>0 and type!=0\">" +
            " and t2.star in" +
            "   <foreach collection=\"star\" item=\"starvo\" open=\"(\" separator=\",\" close=\")\"> " +
            "   #{starvo}" +
            "   </foreach>" +
            " </if>" +
            "<if test=\"star != null and star.size()>0 and type==0\">" +
            " and t1.total_star in" +
            "   <foreach collection=\"star\" item=\"starvo\" open=\"(\" separator=\",\" close=\")\"> " +
            "   #{starvo}" +
            "   </foreach>" +
            " </if>" +
            "<if test=\"tag != null and tag.size()>0  \">" +
            " and (" +
            " <foreach collection=\"tag\" item=\"tagvo\" open=\"\" separator=\"or \" close=\"\"> " +
            " <choose> "+
            "<when test=\" tagvo == '暂无标签' and type==0\">" +
            "  (t1.tag_add  is null) </when>" +
            "<when test=\" tagvo == '暂无标签' and type==1\">" +
            " ( (t1.tag_add is null) or (find_in_set('学习达人',t1.tag_add)=0 and find_in_set('学习标兵',t1.tag_add)=0 and find_in_set('学习先锋',t1.tag_add)=0 )) </when>" +
            "<when test=\" tagvo == '暂无标签' and type==2\">" +
            " ( (t1.tag_add is null) or (find_in_set('担当之星',t1.tag_add)=0 and find_in_set('担当模范',t1.tag_add)=0 and find_in_set('担当先锋',t1.tag_add)=0 )) </when>" +
            "<when test=\" tagvo == '暂无标签' and type==3\">" +
            " ((t1.tag_add is null) or (find_in_set('以身作则',t1.tag_add)=0 and find_in_set('恪尽职守',t1.tag_add)=0 and find_in_set('责任先锋',t1.tag_add)=0) ) </when>" +
            "<when test=\" tagvo == '暂无标签' and type==4\">" +
            " ((t1.tag_add is null) or (find_in_set('业务先锋',t1.tag_add)=0 and find_in_set('业务模范',t1.tag_add)=0 and find_in_set('业务之星',t1.tag_add)=0) ) </when>" +
            "<when test=\" tagvo == '暂无标签' and type==5\">" +
            " ((t1.tag_add is null) or (find_in_set('数智新手',t1.tag_add)=0 and find_in_set('数智达人',t1.tag_add)=0 and find_in_set('数智专家',t1.tag_add)=0) ) </when>" +
            "  <otherwise>" +
            " find_in_set(#{tagvo},t1.tag_add)" +
            "</otherwise></choose>"+
            " </foreach>" +
            ")" +
            " </if>" +
            " group by t1.eval_id\n"+
            "</script>")
    List<OrgUserReportVO> queryReportByCondition(ReportChooseForm chooseForm);


    @Select("select min(date_month) from t_user_party_eval where region_id=#{regionId} and org_id=#{orgId} and cal_status=1\n")
    Integer queryMinMonth(@Param("regionId") Long regionId, @Param("orgId") Long orgId);

    @Select("SELECT eval_id as evalId, date_month as dateMonth,user_name as userName,tag_add as tagAdd,\n" +
            "tag_reduce as tagReduce,total_star as totalStar,org_name as orgName,eval_date as evalDate\n" +
            "FROM t_user_party_eval WHERE user_id=#{userId} and cal_status=1 and type=1 and " +
            "  date_month>=#{startMonth} and date_month<=#{endMonth}")
    List<UserPartyEvalEntity> reportByMonth(@Param(value = "startMonth") Integer startMonth,
                                            @Param(value = "endMonth") Integer endMonth,@Param(value = "userId") Long userId);


    @Select(" select t1.eval_id as evalId,t1.date_month as dateMonth,t1.type,t1.user_id as userId from t_user_party_eval t1\n" +
            " join t_user_party_eval_result t2 on t1.eval_id=t2.eval_id"+
            " where t1.region_id=#{regionId} and (t1.org_id = #{orgId} OR org_level like CONCAT('%-', #{orgId}, '-%')) " +
            "and left(date_month,4)=#{year} and date_month<(#{dateMonth}+1)\n" +
            " and cal_status=1 and t2.star!=5 group by t1.eval_id")
    List<UserPartyEvalEntity>  queryPlanTimes(Long regionId, Long orgId, String year, Integer dateMonth);
}