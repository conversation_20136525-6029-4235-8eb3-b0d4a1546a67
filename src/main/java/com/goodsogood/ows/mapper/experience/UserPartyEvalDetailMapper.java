package com.goodsogood.ows.mapper.experience;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.experience.UserPartyEvalDetailEntity;
import com.goodsogood.ows.model.db.experience.UserPartyEvalResultEntity;
import com.goodsogood.ows.model.vo.experience.UserPartyListVO;
import com.goodsogood.ows.model.vo.experience.UserCalResult;
import com.goodsogood.ows.model.vo.experience.UserCalStarResult;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2022-03-11 10:52
 **/
@Repository
@Mapper
public interface UserPartyEvalDetailMapper extends MyMapper<UserPartyEvalDetailEntity> {


    /**
     * 得到待计算的结果
     * @param batchNumber
     * @return
     */
    @Select("SELECT detail_id as detailId,rule_id as ruleId ,b.user_id as userId ,b.region_id as regionId,\n" +
            "b.date_month as dateMonth, b.task_id as taskId ,star,plan_id as planId,param_type as paramType,\n" +
            "param_value as paramValue,b.cal_status as calStatus,b.create_time as createTime,b.update_time as updateTime\n" +
            "FROM t_user_party_eval as a\n" +
            "LEFT JOIN t_user_party_eval_detail as b \n" +
            "ON a.task_id=b.task_id\n" +
            "WHERE a.batch_number=#{batchNumber} order by a.user_id asc")
    List<UserPartyEvalDetailEntity> getWaitCalcData(String batchNumber);





    /**
     * 得到计算结果
     * @param batchNumber
     * @param userId
     * @return
     */
    @Select("SELECT c.rule_id as ruleId,c.rule_name as ruleName,b.cal_status as calStatus,b.star,\n" +
            "c.parent_id as topRuleId,\n" +
            "( SELECT rule_name  FROM t_user_party_eval_rule WHERE rule_id=c.parent_id) as topRuleName\n" +
            "FROM t_user_party_eval as a\n" +
            "INNER  JOIN t_user_party_eval_detail as b \n" +
            "ON a.task_id=b.task_id\n" +
            "INNER JOIN t_user_party_eval_rule as c \n" +
            "ON b.rule_id=c.rule_id\n" +
            "WHERE a.batch_number=#{batchNumber} and a.user_id=#{userId}")
    List<UserCalResult> getCalcData(@Param(value = "batchNumber")String batchNumber,@Param(value = "userId") Long userId);




    /**
     * 得到计算结果生成星期
     * @param
     * @return
     */
    @Select("\t\t\tSELECT a.date_month as dateMonth,b.rule_id as ruleId,b.star ," +
            " user_id as userId FROM t_user_party_eval as a \n" +
            "\t\t\tLEFT JOIN t_user_party_eval_result as b \n" +
            "\t\t\ton a.eval_id=b.eval_id\n" +
            "\t\t\tWHERE a.user_id=#{userId} and rule_id=#{ruleId}")
    List<UserCalStarResult> getCalcResult(@Param(value = "userId") Long userId, @Param(value = "ruleId") Integer ruleId);



    @Select("select t2.advice_id as adviceId,t2.rule_pid as ruleId,t1.param_value as paramValue from t_user_party_eval_detail t1 \n" +
            "join t_user_party_eval_advice t2 on t1.rule_id=t2.rule_id\n" +
            "where t1.task_id=#{taskId} and t1.star=5 ")
    List<UserPartyEvalResultEntity> queryFiveStarAdvice(String taskId);


    @Select("select t3.rule_name as type,t2.plan as planStr,t1.param_value as paramValue from t_user_party_eval_detail t1\n" +
            "join t_user_party_eval_plan t2 on t1.plan_id=t2.plan_id\n" +
            "join t_user_party_eval_rule t3 on t2.rule_parent_id=t3.rule_id\n" +
            "JOIN t_user_party_eval_rule t4 ON t2.rule_id = t4.rule_id \n"+
            "where t1.task_id=#{taskId} AND t4.is_show =1  and t1.star<5\n" )
    List<UserPartyListVO> queryPlanList(String taskId);

    @Select("<script> " +
            "SELECT tag_name FROM t_user_party_eval_tag WHERE  rule_id=#{ruleId} "+
            "and  tag_name in " +
            " <foreach collection=\"tagNames\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            " #{item} " +
            " </foreach> " +
            " order by tag_order desc  limit 1"+
            " </script> ")
    String getTagName(@Param(value = "tagNames") List<String> tagName,@Param(value = "ruleId")Integer ruleId);


    @Select("<script> " +
            "SELECT GROUP_CONCAT( tag_name ) FROM t_user_party_eval_tag WHERE  1=1 "+
            "and  tag_name in " +
            " <foreach collection=\"tagNames\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            " #{item} " +
            " </foreach> " +
            " order by rule_order "+
            " </script> ")
    String getLastTagName(@Param(value = "tagNames") Set<String> tagName);



}