package com.goodsogood.ows.mapper.experience;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.experience.UserPartyEvalEntity;
import com.goodsogood.ows.model.db.experience.UserPartyEvalResultEntity;
import com.goodsogood.ows.model.vo.experience.UserPartyListVO;
import com.goodsogood.ows.model.vo.experience.UserPartyReportVO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-03-11 10:52
 **/
@Repository
@Mapper
public interface UserPartyEvalResultMapper extends MyMapper<UserPartyEvalResultEntity> {

    @Update("<script>"+
            "update t_user_party_eval_result t1 \n" +
            "join t_user_party_eval_advice t2 on t1.rule_id=t2.rule_pid and t2.default_status=1\n" +
            "set t1.advice_id=t2.advice_id\n" +
            "where t1.task_id=#{taskId}"+
            "<if test=\" null!=ruleId\">"+
            " and t1.rule_id=#{ruleId}\n"+
            "</if>"+
            "</script>"
    )
    void updateAdviceId(String taskId, Integer ruleId);



    @Update("update t_user_party_eval_result set advice_id=#{adviceId},param_value=#{paramValue} where task_id=#{taskId} and rule_id=#{ruleId}")
    void updateAdviceIdFiveStar(@Param("taskId") String taskId, @Param("ruleId") Integer ruleId,
                                @Param("adviceId") Integer adviceId, @Param("paramValue") String paramValue);


    @Select("<script>"+
            "select date_format(t2.eval_date,'%Y-%m') as evalDate,t1.rule_name as ruleName,t1.star  as totalStar from t_user_party_eval_result t1\n" +
            "join t_user_party_eval t2 on t1.task_id=t2.task_id\n" +
            "JOIN t_user_party_eval_rule t3 ON t1.rule_id = t3.rule_id\n" +
            "where t1.task_id in\n"+
            "<foreach collection=\"taskIds\" item=\"taskId\" open=\"(\" separator=\",\" close=\")\">" +
            "#{taskId}" +
            "</foreach>" +
            " and t3.is_show=1\n"+
            " order by t1.rule_id,t2.eval_date desc"+
            "</script>")
    List<UserPartyEvalEntity> queryLastFiveMonthStar(@Param("taskIds") List<String> taskIds);


    @Select("select t1.rule_name as type,t2.advice_name as planStr,t1.param_value as paramValue from t_user_party_eval_result t1\n" +
            "join t_user_party_eval_advice t2 on t1.advice_id=t2.advice_id\n" +
            "where t1.eval_id=#{evalId}")
    List<UserPartyListVO> queryAdvice(Long evalId);


    @Select(
            "select a1.rule_name as type,a1.star,count(0) as num from  t_user_party_eval_result a1\n" +
            "join t_user_party_eval_rule a3 on a1.rule_id=a3.parent_id\n"+
            "join t_user_party_eval_detail a2 on a1.task_id=a2.task_id and a2.rule_id=a3.rule_id\n" +
            "where a1.eval_id=#{evalId}\n" + " \tAND a3.is_show =1 "+
            "group by a1.rule_name,a1.star\n" +
            "order by a1.rule_id")
    List<UserPartyReportVO.TypeStarVO> queryRedaChart(Long evalId);

    @Select("select a1.rule_name as type,a1.star,a3.rule_name as ruleName,a2.star as ruleStar from  t_user_party_eval_result a1\n" +
            "join t_user_party_eval_rule a4 on a1.rule_id=a4.parent_id\n"+
            "join t_user_party_eval_detail a2 on a1.task_id=a2.task_id and a2.rule_id=a4.rule_id\n" +
            "join t_user_party_eval_rule a3 on a2.rule_id=a3.rule_id\n" +
            "where a1.eval_id=#{evalId}\n" +
            " \tAND a3.is_show =1 "+
            "order by a1.rule_id,a3.rule_id")
    List<UserPartyReportVO.RuleVO> queryRuleList(Long evalId);

    @Select("select * from  t_user_party_eval_result a1\n"+
            "join t_user_party_eval_rule a3 on a1.rule_id=a3.rule_id\n" +
            "where a1.eval_id=#{evalId}\n" +
            " AND a3.is_show =1 ")
    List<UserPartyEvalResultEntity> selectIdsByEvalId(Long evalId);

}