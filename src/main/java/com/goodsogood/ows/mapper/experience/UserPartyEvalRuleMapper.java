package com.goodsogood.ows.mapper.experience;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.experience.UserPartyEvalRuleEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-03-11 10:52
 **/
@Repository
@Mapper
public interface UserPartyEvalRuleMapper extends MyMapper<UserPartyEvalRuleEntity> {

    @Select("select rule_id from t_user_party_eval_rule where parent_id=0 and region_id=#{regionId}")
    List<Integer> queryBigRuleId(Long regionId);

}