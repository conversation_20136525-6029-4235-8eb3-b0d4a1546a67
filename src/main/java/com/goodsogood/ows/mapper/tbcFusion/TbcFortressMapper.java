package com.goodsogood.ows.mapper.tbcFusion;

import com.goodsogood.ows.model.vo.tbcFusion.TbcPartyIndexForm;
import com.goodsogood.ows.service.tbcFusion.TbcFortressService;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description: 支部平均堡垒和党员先锋指数
 * @author: zhangtao
 * @create: 2021-12-14 17:38
 */
@Mapper
@Repository
public interface TbcFortressMapper {

    /**
     * 获取党组经纬度
     *
     * @param orgIds
     * @return
     */
    @Select("SELECT \n" +
            "organization_id as orgId," +
            "longitude as longitude," +
            "latitude as latitude \n" +
            "FROM \n" +
            "t_organization \n" +
            "WHERE organization_id in (${orgIds})")
    List<TbcFortressService.LongitudeAndLatitude> getOrgLongitudeAndLatitude(@Param("orgIds") String orgIds);

//
//    @Select("select \n" +
//            "avg(score1) partyIndex, \n" +
//            "avg(score2) businessIndex, \n" +
//            "avg(score3) innovationIndex \n" +
//            "from ( \n" +
//            "select tmp1.org_id orgId,  \n" +
//            "round(IFNULL(tmp1.score1/tmp2.scoreMedian1*100,0),4) score1,  \n" +
//            "round(IFNULL(tmp1.score2/tmp2.scoreMedian2*100,0),4) score2,  \n" +
//            "round(IFNULL(tmp1.score3/tmp2.scoreMedian3*100,0),4) score3\n" +
//            "from (select t1.organization_id org_id,IFNULL(t2.score1,0) score1,IFNULL(t2.score2,0) score2,\n" +
//            "IFNULL(t2.score3,0) score3 from (  \n" +
//            "select organization_id  from t_organization  where  organization_id not in(${outOrgId}) and   \n" +
//            "  org_type_child in (10280304,10280309,10280314,10280315,10280319) and `status`=1  \n" +
//            ") t1 LEFT JOIN (  \n" +
//            "select tt1.score_org_id,  \n" +
//            "sum(case when tt1.parent_score_type=1 then tt1.total else 0 end) score1,   \n" +
//            "sum(case when tt1.parent_score_type=2 then tt1.total else 0 end) score2,   \n" +
//            "sum(case when tt1.parent_score_type=3 then tt1.total else 0 end) score3   \n" +
//            "from t_score_org_type_count tt1 INNER JOIN t_organization tt2 on tt1.score_org_id = tt2.organization_id   \n" +
//            " and tt1.org_id =3 and tt1.score_org_type =1 and tt1.parent_score_type in (1,2,3) and tt2.org_type_child   \n" +
//            "in (10280304,10280309,10280314,10280315,10280319) and tt2.organization_id not in(${outOrgId}) and tt2.`status`=1 GROUP BY tt1.score_org_id  \n" +
//            ") t2 on t1.organization_id = t2.score_org_id  \n" +
//            ") tmp1,(  \n" +
//            " select scoreMedian1,scoreMedian2,scoreMedian3 from (  \n" +
//            " SELECT AVG(g.score1) scoreMedian1 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score1 AS score1   \n" +
//            " FROM (select score1 from (  \n" +
//            " select sum(IFNULL(tt1.total,0)) score1 from (select organization_id from t_organization where organization_id not in(${outOrgId}) and   \n" +
//            "  org_type_child in (10280304,10280309,10280314,10280315,10280319) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =1 GROUP BY tt3.organization_id  \n" +
//            ") tmmp where score1 !=0  \n" +
//            "union all  \n" +
//            "select score1 from (  \n" +
//            "select DISTINCT sum(IFNULL(tt1.total,0)) score1 from (select organization_id from t_organization where organization_id not in(${outOrgId}) and   \n" +
//            "org_type_child in (10280304,10280309,10280314,10280315,10280319) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =1 GROUP BY tt3.organization_id  \n" +
//            ") tmmp2 where score1 =0  \n" +
//            "ORDER BY score1) grades,(Select @rowindex:=-1) b  \n" +
//            "ORDER BY grades.score1) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))  \n" +
//            ") t1,(  \n" +
//            "SELECT AVG(g.score2) scoreMedian2 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score2 AS score2   \n" +
//            "FROM (select score2 from (  \n" +
//            "select sum(IFNULL(tt1.total,0)) score2 from (select organization_id from t_organization where organization_id not in(${outOrgId}) and   \n" +
//            "org_type_child in (10280304,10280309,10280314,10280315,10280319) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =2 GROUP BY tt3.organization_id  \n" +
//            ") tmmp where score2 !=0  \n" +
//            "union all  \n" +
//            "select score2 from (  \n" +
//            "select DISTINCT sum(IFNULL(tt1.total,0)) score2 from (select organization_id from t_organization where organization_id not in(${outOrgId}) and   \n" +
//            "org_type_child in (10280304,10280309,10280314,10280315,10280319) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =2 GROUP BY tt3.organization_id  \n" +
//            ") tmmp2 where score2 =0  \n" +
//            "ORDER BY score2) grades,(Select @rowindex:=-1) b  \n" +
//            "ORDER BY grades.score2) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))  \n" +
//            ") t2,(  \n" +
//            "SELECT AVG(g.score3) scoreMedian3 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score3 AS score3   \n" +
//            "FROM (select score3 from (  \n" +
//            "select sum(IFNULL(tt1.total,0)) score3 from (select organization_id from t_organization where organization_id not in(${outOrgId}) and   \n" +
//            "org_type_child in (10280304,10280309,10280314,10280315,10280319) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =3 GROUP BY tt3.organization_id  \n" +
//            ") tmmp where score3 !=0  \n" +
//            "union all  \n" +
//            "select score3 from (  \n" +
//            "select DISTINCT sum(IFNULL(tt1.total,0)) score3 from (select organization_id from t_organization where organization_id not in(${outOrgId}) and   \n" +
//            "org_type_child in (10280304,10280309,10280314,10280315,10280319) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =3 GROUP BY tt3.organization_id  \n" +
//            ") tmmp2 where score3 =0  \n" +
//            " ORDER BY score3) grades,(Select @rowindex:=-1) b  \n" +
//            " ORDER BY grades.score3) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))  \n" +
//            " ) t3 ) tmp2 ORDER BY org_id) L where 1=1")
//    List<TbcPartyIndexForm> getAvgPartyOrgIndex(@Param("outOrgId") String orgId);

    @Select("SELECT\n" +
            "\tround(avg( party_index ),1) partyIndex,\n" +
            "\tround(avg( business_index ),1) businessIndex,\n" +
            "\tround(avg( innovation_index ),1) innovationIndex,\n" +
            "\tround(avg( fortress_index ),1) fortressIndex \n" +
            "FROM\n" +
            "\tt_tbc_org_info \n" +
            "WHERE\n" +
            "\torg_type_child IN ( 10280304, 10280309, 10280314, 10280315, 10280319 ) \n" +
            "\tAND org_id NOT IN ( ${outOrgId} ) \n" +
            "\tAND sta_month = #{staMonth}")
    TbcPartyIndexForm getAvgPartyOrgIndex(
            @Param("outOrgId") String orgId,
            @Param("staMonth") Integer staMonth);


//    @Select("select \n" +
//            "avg(score1) partyIndex, \n" +
//            "avg(score2) businessIndex, \n" +
//            "avg(score3) innovationIndex, \n" +
//            "\tround(\n" +
//            "\t(log(score1 * 0.8 + score2 * 0.1 + score3 * 0.1) - min(log(score1 * 0.8 + score2 * 0.1 + score3 * 0.1))\n" +
//            "\t/\n" +
//            "\tmax(log(score1 * 0.8 + score2 * 0.1 + score3 * 0.1)) - min(log(score1 * 0.8 + score2 * 0.1 + score3 * 0.1))) * 100\n" +
//            "\t,1) partyMembersNumber\n" +
//            "from ( \n" +
//            "select tmp1.user_id,tmp1.org_id,  \n" +
//            "round(IFNULL(tmp1.score1/tmp2.scoreMedian1*100,0),4) score1,  \n" +
//            "round(IFNULL(tmp1.score2/tmp2.scoreMedian2*100,0),4) score2,  \n" +
//            "round(IFNULL(tmp1.score3/tmp2.scoreMedian3*100,0),4) score3 from (  \n" +
//            "select t1.user_id user_id,t1.org_id,IFNULL(t2.score1,0) score1,IFNULL(t2.score2,0) score2,IFNULL(t2.score3,0) score3 from (  \n" +
//            "select u.user_id,uoc.organization_id org_id from t_user u INNER JOIN  t_user_org_corp uoc on u.user_id=uoc.user_id and u.`status`=1 and u.political_type in (1,5) and uoc.is_employee=1 and uoc.organization_id not in (outOrgId)  \n" +
//            ") t1 LEFT JOIN (  \n" +
//            "select tt2.user_id,tt4.organization_id org_id,  \n" +
//            "sum(case when tt1.parent_score_type=1 then tt1.total else 0 end) score1,   \n" +
//            "sum(case when tt1.parent_score_type=2 then tt1.total else 0 end) score2,   \n" +
//            "sum(case when tt1.parent_score_type=3 then tt1.total else 0 end) score3  \n" +
//            "from t_score_type_count tt1 INNER JOIN t_score_user tt2 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type in (1,2,3) INNER JOIN t_user tt3 on tt2.user_id = tt3.user_id  and tt3.`status` =1 and tt3.political_type in (1,5) INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and  tt4.is_employee=1 and tt4.organization_id not in (outOrgId) GROUP BY tt1.score_user_id  \n" +
//            ") t2 on t1.user_id=t2.user_id and t1.org_id =t2.org_id ) tmp1,(  \n" +
//            "select scoreMedian1,scoreMedian2,scoreMedian3 from (  \n" +
//            "SELECT AVG(g.score1) scoreMedian1 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score1 AS score1   \n" +
//            "FROM (select score1 from (  \n" +
//            "select sum(IFNULL(tt1.total,0)) score1 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (outOrgId)) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =1 GROUP BY tt3.user_id  \n" +
//            ") tmmp where score1 !=0 union all select score1 from (  \n" +
//            "select DISTINCT sum(IFNULL(tt1.total,0)) score1 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (outOrgId)) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =1 GROUP BY tt3.user_id  \n" +
//            ") tmmp2 where score1 =0 ORDER BY score1) grades,(Select @rowindex:=-1) b ORDER BY grades.score1) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))  \n" +
//            ") t1,( SELECT AVG(g.score2) scoreMedian2 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score2 AS score2   \n" +
//            "FROM (select score2 from (select sum(IFNULL(tt1.total,0)) score2 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (outOrgId)) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =2 GROUP BY tt3.user_id  \n" +
//            ") tmmp where score2 !=0 union all select score2 from (  \n" +
//            "select DISTINCT sum(IFNULL(tt1.total,0)) score2 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (outOrgId)) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =2 GROUP BY tt3.user_id  \n" +
//            ") tmmp2 where score2 =0 ORDER BY score2) grades,(Select @rowindex:=-1) b ORDER BY grades.score2) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))  \n" +
//            ") t2,( SELECT AVG(g.score3) scoreMedian3 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score3 AS score3 FROM (select score3 from (select sum(IFNULL(tt1.total,0)) score3 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (outOrgId)) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =3 GROUP BY tt3.user_id  \n" +
//            ") tmmp where score3 !=0 union all select score3 from (select DISTINCT sum(IFNULL(tt1.total,0)) score3 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (outOrgId)) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =3 GROUP BY tt3.user_id  \n" +
//            ") tmmp2 where score3 =0 ORDER BY score3) grades,(Select @rowindex:=-1) b ORDER BY grades.score3) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))  \n" +
//            ") t3  \n" +
//            ") tmp2 ORDER BY tmp1.user_id   \n" +
//            ") L where 1=1 ")
//    TbcPartyIndexForm getAvgPartyUserIndex(@Param("outOrgId") String orgId);

    @Select("SELECT\n" +
            "\tround(avg( party_index ),1) partyIndex,\n" +
            "\tround(avg( business_index ),1) businessIndex,\n" +
            "\tround(avg( innovation_index ),1) innovationIndex,\n" +
            "\tround(avg( xianfeng_index ),1) partyMembersNumber \n" +
            "FROM\n" +
            "\tt_tbc_user_info \n" +
            "WHERE\n" +
            "\torg_id NOT IN ( ${outOrgId} ) \n" +
            "\tAND sta_month = #{staMonth}")
    TbcPartyIndexForm getAvgPartyUserIndex(@Param("outOrgId") String orgId,
                                           @Param("staMonth") Integer staMonth);
}
