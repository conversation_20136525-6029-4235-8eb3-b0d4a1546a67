package com.goodsogood.ows.mapper.tbcFusion;

import com.goodsogood.ows.model.vo.tbcFusion.TbcPartyAndBusinessScoreVo;
import com.goodsogood.ows.model.vo.tbcFusion.TbcScoreUserRankVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 党业融合积分相关
 * <AUTHOR>
 * @date 2022-06-21
 */
@Mapper
@Repository
public interface TbcScoreMapper {

    @Select("<script> select  userId, userName, orgId, orgName,score,(@rownum :=@rownum + 1) rank from (" +
            " select t0.user_id userId,t0.name userName,t4.organization_id orgId,t4.name orgName,ifnull(t2.score,0) score from (" +
            " select user_id,name from t_user where status =1 and user_id !=1 ) t0 LEFT JOIN t_score_user t1 on t0.user_id = t1.user_id" +
            " LEFT JOIN (select sum(score) score,score_user_id from t_score_detail_other" +
            " where org_id=#{orgId} and parent_score_type = #{parentScoreType} and DATE_FORMAT(IF(consume_time is null,create_time,consume_time),'%Y-%m') = #{yearMonth}" +
            " GROUP BY score_user_id) t2 on t1.score_user_id = t2.score_user_id LEFT JOIN t_user_org_corp t3 on t0.user_id = t3.user_id and t3.is_employee =1 and t3.region_id=#{regionId}" +
            " LEFT JOIN t_organization t4 on t3.organization_id = t4.organization_id and t4.region_id =#{regionId}" +
            " <if test=\" excludeUserIds != null and excludeUserIds !='' \"> where t1.user_id not in (${excludeUserIds}) </if>" +
            " ORDER BY t2.score desc,t0.user_id) tmp1,(SELECT @rownum := 0) tmp2 </script>")

//    select t0.user_id userId,t0.name userName,t4.organization_id orgId,t4.name orgName,ifnull(t2.score,0) score from (
//            select user_id,name from t_user where status =1 and user_id !=1 ) t0 LEFT JOIN t_score_user t1 on t0.user_id = t1.user_id LEFT JOIN (select sum(score) score,score_user_id from t_score_detail_other
//    where org_id=3 and parent_score_type = 2 and DATE_FORMAT(IF(consume_time is null,create_time,consume_time),'%Y-%m') = '2022-05'
//    GROUP BY score_user_id) t2 on t1.score_user_id = t2.score_user_id LEFT JOIN t_user_org_corp t3 on t0.user_id = t3.user_id
//    and t3.is_employee =1 and t3.region_id=19 LEFT JOIN t_organization t4 on t3.organization_id = t4.organization_id and t4.region_id =19 ORDER BY t2.score desc,t0.user_id) tmp1,(SELECT @rownum := 0) tmp2

    List<TbcScoreUserRankVo> findUserRank(@Param(value = "regionId") Long regionId,
                                           @Param(value = "orgId") Long orgId,
                                           @Param(value = "parentScoreType") Integer parentScoreType,
                                           @Param(value = "yearMonth") String yearMonth,
                                           @Param(value = "excludeUserIds") String excludeUserIds);

    @Select("<script> select score_org_id orgId,sum(case when parent_score_type =1 then score else 0 end) partyScore," +
            " sum(case when parent_score_type =2 then score else 0 end) businessScore from (select score_org_id,parent_score_type,sum(score) score" +
            " from t_score_org_detail where org_id =3 and score_org_type=#{scoreOrgType} and parent_score_type in (1,2) " +
            " <if test=\" startTime != null and startTime !='' \">and DATE_FORMAT(IF(consume_time is null,create_time,consume_time),'%Y-%m-%d') &gt;= #{startTime}</if>" +
            " <if test=\" endTime != null and endTime !='' \">and DATE_FORMAT(IF(consume_time is null,create_time,consume_time),'%Y-%m-%d') &lt;= #{endTime}</if>" +
            " <if test=\" scoreOrgIds != null and scoreOrgIds.size !=0 \"> and score_org_id in" +
            " <foreach collection=\"scoreOrgIds\" index=\"index\" item=\"soid\" open=\"(\" separator=\",\" close=\")\">#{soid} </foreach> </if>" +
            " <if test=\" testBranch != null and testBranch !='' \"> and score_org_id not in (${testBranch}) </if>" +
            " GROUP BY parent_score_type,score_org_id) tmp GROUP BY score_org_id  </script>")
    /**
     * 查询单位党建/业务积分
     * 党建积分=单位对应的党组党建积分+与单位关联的最高级党组织党建积分；
     * 业务积分=与单位关联的最高级党组织业务积分；
     */
    List<TbcPartyAndBusinessScoreVo> findOrgPartyAndBusinessScore(@Param(value = "scoreOrgType") Integer scoreOrgType,
                                                               @Param(value = "scoreOrgIds") List<Long> scoreOrgIds,
                                                               @Param(value = "startTime") String startTime,
                                                               @Param(value = "endTime") String endTime,
                                                               @Param(value = "testBranch") String testBranch);

    @Select("<script> select user_id userId,sum(case when parent_score_type =1 then score else 0 end) partyScore," +
            " sum(case when parent_score_type =2 then score else 0 end) businessScore from (select t2.user_id,parent_score_type,sum(score) score" +
            " from t_score_detail_other t1 INNER JOIN t_score_user t2 on t1.score_user_id = t2.score_user_id where t1.org_id=3 and t1.parent_score_type in (1,2)" +
            " <if test=\" startTime != null and startTime !='' \"> and DATE_FORMAT(IF(t1.consume_time is null,t1.create_time,t1.consume_time),'%Y-%m-%d') &gt;= #{startTime}</if>" +
            " <if test=\" endTime != null and endTime !='' \"> and DATE_FORMAT(IF(t1.consume_time is null,t1.create_time,t1.consume_time),'%Y-%m-%d') &lt;= #{endTime}</if>" +
            " <if test=\" userIds != null and userIds.size !=0 \"> and t2.user_id in" +
            " <foreach collection=\"userIds\" index=\"index\" item=\"uid\" open=\"(\" separator=\",\" close=\")\">#{uid} </foreach> </if>" +
            " GROUP BY t1.parent_score_type,t2.user_id) tmp GROUP BY user_id  </script>")
    /**
     * 查询用户党建/业务积分
     */
    List<TbcPartyAndBusinessScoreVo> findUserPartyAndBusinessScore(@Param(value = "userIds") List<Long> userIds,
                                                                  @Param(value = "startTime") String startTime,
                                                                  @Param(value = "endTime") String endTime);
}
