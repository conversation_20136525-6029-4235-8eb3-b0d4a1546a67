package com.goodsogood.ows.mapper.tbcFusion;

import com.goodsogood.ows.model.vo.tbcFusion.TbcPartyIndexForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description: 党员“党建+业务”工作情况（散点图）
 * @author: zhangtao
 * @create: 2021-12-14 17:46
 */
@Mapper
@Repository
public interface TbcPartyMemberWorkMapper {
    /**
     * PC端查询 散点图
     */
    @Select("select " +
            "org_id as orgId," +
            "org_name as orgName," +
            "party_index as partyIndex," +
            "business_index as businessIndex " +
            "from " +
            "`t_tbc_org_info` " +
            "where " +
            "sta_month = #{staMonth} ")
    List<TbcPartyIndexForm> getPcPartyMember(@Param("staMonth") String staMonth);
}
