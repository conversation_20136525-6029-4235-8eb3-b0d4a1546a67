package com.goodsogood.ows.mapper.tbcFusion;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.user.UserOrgAndCorpEntity;
import com.goodsogood.ows.model.vo.fusion.IndexVO;
import com.goodsogood.ows.model.vo.tbc.TbcBaseIndexForm;
import com.goodsogood.ows.model.vo.tbc.TbcScoreForm;
import com.goodsogood.ows.model.vo.tbcFusion.TbcMapForm;
import com.goodsogood.ows.model.vo.tbcFusion.TbcPartyIndexForm;
import com.goodsogood.ows.model.vo.tbcFusion.TbcUserIndexForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-12-14
 */
@Mapper
@Repository
public interface TbcBasicsMapper extends MyMapper<UserOrgAndCorpEntity> {

    /**
     * -- 全市职工
     *
     * @return
     */
    @Select("select\n" +
            "count( distinct u.user_id ) \n" +
            "from\n" +
            "t_user u\n" +
            "left join t_user_org_corp uoc on u.user_id = uoc.user_id\n" +
            "left join t_organization o on o.organization_id = uoc.organization_id \n" +
            "where\n" +
            "u.`status` = 1 \n" +
            "and o.org_type = 102807 \n" +
            "and uoc.organization_id not in ( 1294, 5, 2409, 2417, 1222, 1255 ) \n" +
            "and o.org_level NOT LIKE '%-1294-%' \n" +
            "and o.org_level NOT LIKE '%-5-%' \t\n" +
            "and o.org_level NOT LIKE '%-2409-%' \n" +
            "and o.org_level NOT LIKE '%-2417-%' \n" +
            "and o.org_level NOT LIKE '%-1222-%' \n" +
            "and o.org_level NOT LIKE '%-1255-%'\n")
    Integer getEmployeeNumber();

    /**
     * 党员
     */
    @Select("select \n" +
            "count( 0 )  \n" +
            "from \n" +
            "t_user u  \n" +
            "left join t_user_org_corp uoc on uoc.user_id = u.user_id  \n" +
            "left join t_organization o on o.organization_id = uoc.organization_id \n" +
            "where \n" +
            "u.`status` = 1  \n" +
            "and o.`status` = 1 \n" +
            "and uoc.is_employee = 1 \n" +
            "and u.political_type in ( 1, 5, 17, 18 )\n" +
            "and o.organization_id not in (${outOrgId})")
    Integer getPartyMember(@Param("outOrgId") String orgId);

    /**
     * 获取当月支部堡垒指数
     * @return
     */
    @Select("select longitude as lng,latitude as lat,fortress_index `value` " +
            "from `t_tbc_org_info` where sta_month = #{staMonth} and longitude is not null order by `value` desc")
    List<TbcMapForm> getOrgMap(@Param("staMonth")String staMonth);

    /**
     * 获取当月党员先锋指数
     * @return
     */
    @Select("SELECT \n" +
            "longitude as lng,latitude as lat,\n" +
            "avg(party_index * 0.4 + business_index * 0.4 + innovation_index * 0.2) `value` \n" +
            "FROM `t_tbc_user_info` where sta_month = #{staMonth} and longitude is not null GROUP BY org_id ORDER BY `value` desc")
    List<TbcMapForm> getUserMap(@Param("staMonth")String staMonth);

    @Select("<script>" +
            "select " +
            "L.orgId," +
            "score1 partyIndex,\n" +
            "score2 businessIndex,\n" +
            "score3 InnovationIndex " +
            "from (" +
            "select tmp1.org_id orgId,\n" +
            "round(IFNULL(tmp1.score1/tmp2.scoreMedian1*100,0),4) score1,\n" +
            "round(IFNULL(tmp1.score2/tmp2.scoreMedian2*100,0),4) score2,\n" +
            "round(IFNULL(tmp1.score3/tmp2.scoreMedian3*100,0),4) score3 \n" +
            "from (select t1.organization_id org_id,IFNULL(t2.score1,0) score1,IFNULL(t2.score2,0) score2," +
            "IFNULL(t2.score3,0) score3 from (\n" +
            "select organization_id  from t_organization  where  organization_id not in(${excludeOrgIds}) and " +
            "  org_type_child in (${orgTypeChild}) and `status`=1\n" +
            ") t1 LEFT JOIN (\n" +
            "select tt1.score_org_id,\n" +
            "sum(case when tt1.parent_score_type=1 then tt1.total else 0 end) score1, \n" +
            "sum(case when tt1.parent_score_type=2 then tt1.total else 0 end) score2, \n" +
            "sum(case when tt1.parent_score_type=3 then tt1.total else 0 end) score3 \n" +
            "from t_score_org_type_count tt1 INNER JOIN t_organization tt2 on tt1.score_org_id = tt2.organization_id " +
            " and tt1.org_id =3 and tt1.score_org_type =1 and tt1.parent_score_type in (1,2,3) and tt2.org_type_child " +
            "in (${orgTypeChild}) and tt2.organization_id not in(${excludeOrgIds}) and tt2.`status`=1 GROUP BY tt1.score_org_id\n" +
            ") t2 on t1.organization_id = t2.score_org_id\n" +
            ") tmp1,(" +
            " select scoreMedian1,scoreMedian2,scoreMedian3 from (\n" +
            " SELECT AVG(g.score1) scoreMedian1 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score1 AS score1 \n" +
            " FROM (select score1 from (\n" +
            " select sum(IFNULL(tt1.total,0)) score1 from (select organization_id from t_organization where organization_id not in(${excludeOrgIds}) and \n" +
            "  org_type_child in (${orgTypeChild}) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =1 GROUP BY tt3.organization_id\n" +
            " ) tmmp where score1 !=0\n" +
            " union all\n" +
            " select score1 from (\n" +
            " select DISTINCT sum(IFNULL(tt1.total,0)) score1 from (select organization_id from t_organization where organization_id not in(${excludeOrgIds}) and \n" +
            " org_type_child in (${orgTypeChild}) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =1 GROUP BY tt3.organization_id\n" +
            " ) tmmp2 where score1 =0\n" +
            " ORDER BY score1) grades,(Select @rowindex:=-1) b\n" +
            " ORDER BY grades.score1) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))\n" +
            " ) t1,(\n" +
            " SELECT AVG(g.score2) scoreMedian2 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score2 AS score2 \n" +
            " FROM (select score2 from (\n" +
            " select sum(IFNULL(tt1.total,0)) score2 from (select organization_id from t_organization where organization_id not in(${excludeOrgIds}) and \n" +
            " org_type_child in (${orgTypeChild}) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =2 GROUP BY tt3.organization_id\n" +
            " ) tmmp where score2 !=0\n" +
            " union all\n" +
            " select score2 from (\n" +
            " select DISTINCT sum(IFNULL(tt1.total,0)) score2 from (select organization_id from t_organization where organization_id not in(${excludeOrgIds}) and \n" +
            " org_type_child in (${orgTypeChild}) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =2 GROUP BY tt3.organization_id\n" +
            " ) tmmp2 where score2 =0\n" +
            " ORDER BY score2) grades,(Select @rowindex:=-1) b\n" +
            " ORDER BY grades.score2) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))\n" +
            " ) t2,(\n" +
            " SELECT AVG(g.score3) scoreMedian3 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score3 AS score3 \n" +
            " FROM (select score3 from (\n" +
            " select sum(IFNULL(tt1.total,0)) score3 from (select organization_id from t_organization where organization_id not in(${excludeOrgIds}) and \n" +
            "  org_type_child in (${orgTypeChild}) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =3 GROUP BY tt3.organization_id\n" +
            " ) tmmp where score3 !=0\n" +
            " union all\n" +
            " select score3 from (\n" +
            " select DISTINCT sum(IFNULL(tt1.total,0)) score3 from (select organization_id from t_organization where organization_id not in(${excludeOrgIds}) and \n" +
            " org_type_child in (${orgTypeChild}) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =3 GROUP BY tt3.organization_id\n" +
            " ) tmmp2 where score3 =0\n" +
            " ORDER BY score3) grades,(Select @rowindex:=-1) b\n" +
            " ORDER BY grades.score3) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))\n" +
            " ) t3" +
            ") tmp2 ORDER BY org_id \n" +
            ")as L " +
            "WHERE 1=1 " +
            "<if test =\"orgIds != null  and orgIds != ''\"> and orgId IN  ( ${orgIds}  ) </if>" +
            "</script>")
    List<TbcPartyIndexForm> getTbcPartyIndex(@Param(value = "orgTypeChild") String orgTypeChild,
                                             @Param(value = "orgIds") String orgIds,
                                             @Param(value = "excludeOrgIds") String excludeOrgIds);


    @Select("<script>" +
            "SELECT user_id as userId,org_id as orgId,score1 as partyIndex,\n" +
            "score2 as businessIndex,score3 as InnovationIndex FROM\n" +
            "(select tmp1.user_id,tmp1.org_id,\n" +
            "round(IFNULL(tmp1.score1/tmp2.scoreMedian1*100,0),4) score1,\n" +
            "round(IFNULL(tmp1.score2/tmp2.scoreMedian2*100,0),4) score2,\n" +
            "round(IFNULL(tmp1.score3/tmp2.scoreMedian3*100,0),4) score3 from (\n" +
            "select t1.user_id user_id,t1.org_id,IFNULL(t2.score1,0) score1,IFNULL(t2.score2,0) score2,IFNULL(t2.score3,0) score3 from (\n" +
            "select u.user_id,uoc.organization_id org_id from t_user u INNER JOIN  t_user_org_corp uoc on u.user_id=uoc.user_id and u.`status`=1 and u.political_type in (1,5) and uoc.is_employee=1 and uoc.organization_id not in (${excludeOrgIds})\n" +
            ") t1 LEFT JOIN (\n" +
            "select tt2.user_id,tt4.organization_id org_id,\n" +
            "sum(case when tt1.parent_score_type=1 then tt1.total else 0 end) score1, \n" +
            "sum(case when tt1.parent_score_type=2 then tt1.total else 0 end) score2, \n" +
            "sum(case when tt1.parent_score_type=3 then tt1.total else 0 end) score3\n" +
            "from t_score_type_count tt1 INNER JOIN t_score_user tt2 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type in (1,2,3) INNER JOIN t_user tt3 on tt2.user_id = tt3.user_id  and tt3.`status` =1 and tt3.political_type in (1,5) INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and  tt4.is_employee=1 and tt4.organization_id not in (${excludeOrgIds}) GROUP BY tt1.score_user_id\n" +
            ") t2 on t1.user_id=t2.user_id and t1.org_id =t2.org_id ) tmp1,(" +
            " select scoreMedian1,scoreMedian2,scoreMedian3 from (" +
            " SELECT AVG(g.score1) scoreMedian1 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score1 AS score1 " +
            " FROM (select score1 from (" +
            " select sum(IFNULL(tt1.total,0)) score1 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${excludeOrgIds})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =1 GROUP BY tt3.user_id" +
            " ) tmmp where score1 !=0 union all select score1 from (" +
            " select DISTINCT sum(IFNULL(tt1.total,0)) score1 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${excludeOrgIds})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =1 GROUP BY tt3.user_id" +
            " ) tmmp2 where score1 =0 ORDER BY score1) grades,(Select @rowindex:=-1) b ORDER BY grades.score1) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))" +
            " ) t1,( SELECT AVG(g.score2) scoreMedian2 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score2 AS score2 " +
            " FROM (select score2 from (select sum(IFNULL(tt1.total,0)) score2 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${excludeOrgIds})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =2 GROUP BY tt3.user_id" +
            " ) tmmp where score2 !=0 union all select score2 from (" +
            " select DISTINCT sum(IFNULL(tt1.total,0)) score2 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${excludeOrgIds})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =2 GROUP BY tt3.user_id" +
            " ) tmmp2 where score2 =0 ORDER BY score2) grades,(Select @rowindex:=-1) b ORDER BY grades.score2) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))" +
            " ) t2,( SELECT AVG(g.score3) scoreMedian3 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score3 AS score3 FROM (select score3 from (select sum(IFNULL(tt1.total,0)) score3 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${excludeOrgIds})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =3 GROUP BY tt3.user_id" +
            " ) tmmp where score3 !=0 union all select score3 from (select DISTINCT sum(IFNULL(tt1.total,0)) score3 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${excludeOrgIds})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =3 GROUP BY tt3.user_id" +
            " ) tmmp2 where score3 =0 ORDER BY score3) grades,(Select @rowindex:=-1) b ORDER BY grades.score3) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))" +
            " ) t3" +
            ") tmp2 ORDER BY tmp1.user_id " +
            ") as L\n" +
            "WHERE 1=1 " +
            "<if test =\"orgIds != null and orgIds != '' \"> and L.org_id IN  ( ${orgIds}  ) </if>" +
            "<if test =\"userIds != null\"> and L.user_id IN ( ${userIds} ) </if>" +
            "</script>")
    List<TbcUserIndexForm> getUserIndex(@Param(value = "orgIds") String orgIds,
                                        @Param(value = "userIds") String userIds,
                                        @Param(value = "excludeOrgIds") String excludeOrgIds);


    @Select( "SELECT user_id as userId,org_id as orgId,score1 as partyIndex, score2 as businessIndex,score3 as InnovationIndex FROM (select tmp1.user_id,tmp1.org_id, round(IFNULL(tmp1.score1/tmp2.scoreMedian1*100,0),4) score1, round(IFNULL(tmp1.score2/tmp2.scoreMedian2*100,0),4) score2, round(IFNULL(tmp1.score3/tmp2.scoreMedian3*100,0),4) score3 from ( select t1.user_id user_id,t1.org_id,IFNULL(t2.score1,0) score1,IFNULL(t2.score2,0) score2,IFNULL(t2.score3,0) score3 from ( select u.user_id,uoc.organization_id org_id from t_user u INNER JOIN t_user_org_corp uoc on u.user_id=uoc.user_id and u.`status`=1 and u.political_type in (1,5) and uoc.is_employee=1 and uoc.organization_id not in (${excludeOrgIds}) ) t1 LEFT JOIN ( select tt2.user_id,tt4.organization_id org_id, sum(case when tt1.parent_score_type=1 then tt1.score else 0 end) score1, sum(case when tt1.parent_score_type=2 then tt1.score else 0 end) score2, sum(case when tt1.parent_score_type=3 then tt1.score else 0 end) score3 from t_score_detail_other tt1 INNER JOIN t_score_user tt2 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 AND date_format(IFNULL(tt1.consume_time,tt1.create_time),'%Y-%m')<=#{staMonth} and tt1.parent_score_type in (1,2,3) INNER JOIN t_user tt3 on tt2.user_id = tt3.user_id and tt3.`status` =1 and tt3.political_type in (1,5) INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt4.is_employee=1 and tt4.organization_id not in (${excludeOrgIds}) GROUP BY tt1.score_user_id ) t2 on t1.user_id=t2.user_id and t1.org_id =t2.org_id) tmp1,( select scoreMedian1,scoreMedian2,scoreMedian3 from ( SELECT AVG(g.score1) scoreMedian1 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score1 AS score1 FROM (select score1 from ( select sum(IFNULL(tt1.score,0)) score1 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${excludeOrgIds})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_detail_other tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and date_format(IFNULL(tt1.consume_time,tt1.create_time),'%Y-%m')<=#{staMonth} and tt1.parent_score_type =1 GROUP BY tt3.user_id ) tmmp where score1 !=0 union all select score1 from ( select DISTINCT sum(IFNULL(tt1.score,0)) score1 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${excludeOrgIds})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_detail_other tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and date_format(IFNULL(tt1.consume_time,tt1.create_time),'%Y-%m')<=#{staMonth} and tt1.parent_score_type =1 GROUP BY tt3.user_id ) tmmp2 where score1 =0 ORDER BY score1) grades,(Select @rowindex:=-1) b ORDER BY grades.score1) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2)) ) t1,( SELECT AVG(g.score2) scoreMedian2 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score2 AS score2 FROM (select score2 from (select sum(IFNULL(tt1.score,0)) score2 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${excludeOrgIds})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_detail_other tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 AND date_format(IFNULL(tt1.consume_time,tt1.create_time),'%Y-%m')<=#{staMonth} and tt1.parent_score_type =2 GROUP BY tt3.user_id ) tmmp where score2 !=0 union all select score2 from ( select DISTINCT sum(IFNULL(tt1.score,0)) score2 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${excludeOrgIds})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_detail_other tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 AND date_format(IFNULL(tt1.consume_time,tt1.create_time),'%Y-%m')<=#{staMonth} and tt1.parent_score_type =2 GROUP BY tt3.user_id ) tmmp2 where score2 =0 ORDER BY score2) grades,(Select @rowindex:=-1) b ORDER BY grades.score2) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))) t2,( SELECT AVG(g.score3) scoreMedian3 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score3 AS score3 FROM (select score3 from (select sum(IFNULL(tt1.score,0)) score3 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${excludeOrgIds})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_detail_other tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 AND date_format(IFNULL(tt1.consume_time,tt1.create_time),'%Y-%m')<=#{staMonth} and tt1.parent_score_type =3 GROUP BY tt3.user_id ) tmmp where score3 !=0 union all select score3 from (select DISTINCT sum(IFNULL(tt1.score,0)) score3 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${excludeOrgIds})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_detail_other tt1 on tt1.score_user_id=tt2.score_user_id AND date_format(IFNULL(tt1.consume_time,tt1.create_time),'%Y-%m')<=#{staMonth} and tt1.org_id=3 and tt1.parent_score_type =3 GROUP BY tt3.user_id ) tmmp2 where score3 =0 ORDER BY score3) grades,(Select @rowindex:=-1) b ORDER BY grades.score3) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2)) \n" +
            "\n" +
            ") t3) tmp2 ORDER BY tmp1.user_id ) as L WHERE 1=1 and L.user_id in (${userIds})")
    List<TbcUserIndexForm> getClearUserIndex(@Param(value = "staMonth") String staMonth,
                                       @Param(value = "userIds") String userIds,
                                       @Param(value = "excludeOrgIds") String excludeOrgIds);


    /**
     * 获取党支部数量
     */
    @Select("select \n" +
            "count( 1 )  \n" +
            "from \n" +
            "t_organization  \n" +
            "where \n" +
            "org_type_child in ( 10280304, 10280309, 10280314, 10280315, 10280319 )  \n" +
            "and `status` = 1 \n" +
            "and organization_id not in (${outOrgId})")
    Integer getPartyBranch(@Param("outOrgId") String orgId);

    /**
     * 获取云上党支部数量
     */
    @Select("select \n" +
            "count( * )  \n" +
            "from \n" +
            "`t_organization`  \n" +
            "where \n" +
            "parent_id = 2550  \n" +
            "and org_type = 102813  \n" +
            "and `status` = 1\n" +
            "and organization_id not in (${outOrgId})")
    Integer getCloudOrgNumber(@Param("outOrgId") String orgId);

    /**
     * 云区任务党员参与人数
     *
     * @return
     */
    @Select("SELECT\n" +
            "\tcount( DISTINCT u.user_id )\n" +
            "FROM\n" +
            "\tt_user u\n" +
            "\tLEFT JOIN t_task_access tta ON u.user_id = tta.access_user_id \n" +
            "\tLEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "\tLEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tu.`status` = 1 \n" +
            "\tAND o.parent_id = 2550 \n" +
            "\tAND o.org_type = 102813 \n" +
            "\tAND u.political_type IN ( 1, 5 )\n" +
            "\tAND tta.operate_status = 8 " +
            "\tAND o.organization_id not in (${outOrgId})")
    Integer getPartyMemberNumber(@Param("outOrgId")String orgId);


    /**
     * 云区任务非党员参与人数
     */
    @Select("SELECT\n" +
            "\tcount( DISTINCT u.user_id ) AS cnt \n" +
            "FROM\n" +
            "\tt_user u\n" +
            "\tLEFT JOIN t_task_access tta ON u.user_id = tta.access_user_id \n" +
            "\tLEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "\tLEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tu.`status` = 1 \n" +
            "\tAND o.parent_id = 2550 \n" +
            "\tAND o.org_type = 102813 \n" +
            "\tAND u.political_type NOT IN ( 1, 5 )\n" +
            "\tAND tta.operate_status = 8")
    Integer getNotPartyMemberNumber();

    /**
     * 云区任务完成总数
     */
    @Select("select  \n" +
            "count(*) num\n" +
            "from  \n" +
            "t_task t  \n" +
            "left join t_organization tor  \n" +
            "on t.create_org = tor.organization_id  \n" +
            "where tor.parent_id = 2550  \n" +
            "and tor.org_type = 102813  \n" +
            "and tor.`status` = 1  \n" +
            "and tor.organization_id not in (${outOrgId}) \n" +
            "and t.end_time <= NOW()")
    Integer getCloudTaskNumber(@Param("outOrgId") String orgId);

    /**
     * 获取任务词云
     */
    @Select("select \n" +
            "t.source_mark as sourceMark\n" +
            "from \n" +
            "t_task t \n" +
            "left join t_organization tor \n" +
            "on t.create_org = tor.organization_id \n" +
            "where tor.parent_id = 2550 \n" +
            "and tor.org_type = 102813 \n" +
            "and tor.`status` = 1 \n" +
            "and t.is_del != 1\n" +
            "and tor.organization_id not in (${outOrgId})")
    List<String> getCloudWord(@Param("outOrgId") String orgId);


    /**
     * 获取所有“党支部”下的党员先锋指数
     *
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            "\tuser_id AS userId,\n" +
            "\torg_id AS orgId,\n" +
            "\tavg( score1 * 0.4 + score2 * 0.4 + score3 * 0.2) AS partyMembersNumber\n" +
            "FROM\n" +
            "\t(\n" +
            "select tmp1.user_id,tmp1.org_id, \n" +
            "round(IFNULL(tmp1.score1/tmp2.scoreMedian1*100,0),4) score1, \n" +
            "round(IFNULL(tmp1.score2/tmp2.scoreMedian2*100,0),4) score2, \n" +
            "round(IFNULL(tmp1.score3/tmp2.scoreMedian3*100,0),4) score3 from ( \n" +
            "select t1.user_id user_id,t1.org_id,IFNULL(t2.score1,0) score1,IFNULL(t2.score2,0) score2,IFNULL(t2.score3,0) score3 from ( \n" +
            "select u.user_id,uoc.organization_id org_id from t_user u INNER JOIN  t_user_org_corp uoc on u.user_id=uoc.user_id and u.`status`=1 and u.political_type in (1,5) and uoc.is_employee=1 and uoc.organization_id not in (${outOrgId}) \n" +
            ") t1 LEFT JOIN ( \n" +
            "select tt2.user_id,tt4.organization_id org_id, \n" +
            "sum(case when tt1.parent_score_type=1 then tt1.total else 0 end) score1,  \n" +
            "sum(case when tt1.parent_score_type=2 then tt1.total else 0 end) score2,  \n" +
            "sum(case when tt1.parent_score_type=3 then tt1.total else 0 end) score3 \n" +
            "from t_score_type_count tt1 INNER JOIN t_score_user tt2 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type in (1,2,3) INNER JOIN t_user tt3 on tt2.user_id = tt3.user_id  and tt3.`status` =1 and tt3.political_type in (1,5) INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and  tt4.is_employee=1 and tt4.organization_id not in (${outOrgId}) GROUP BY tt1.score_user_id \n" +
            ") t2 on t1.user_id=t2.user_id and t1.org_id =t2.org_id ) tmp1,( \n" +
            "select scoreMedian1,scoreMedian2,scoreMedian3 from ( \n" +
            "SELECT AVG(g.score1) scoreMedian1 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score1 AS score1  \n" +
            "FROM (select score1 from ( \n" +
            "select sum(IFNULL(tt1.total,0)) score1 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =1 GROUP BY tt3.user_id \n" +
            ") tmmp where score1 !=0 union all select score1 from ( \n" +
            "select DISTINCT sum(IFNULL(tt1.total,0)) score1 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =1 GROUP BY tt3.user_id \n" +
            ") tmmp2 where score1 =0 ORDER BY score1) grades,(Select @rowindex:=-1) b ORDER BY grades.score1) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2)) \n" +
            ") t1,( SELECT AVG(g.score2) scoreMedian2 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score2 AS score2  \n" +
            "FROM (select score2 from (select sum(IFNULL(tt1.total,0)) score2 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =2 GROUP BY tt3.user_id \n" +
            ") tmmp where score2 !=0 union all select score2 from ( \n" +
            "select DISTINCT sum(IFNULL(tt1.total,0)) score2 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =2 GROUP BY tt3.user_id \n" +
            ") tmmp2 where score2 =0 ORDER BY score2) grades,(Select @rowindex:=-1) b ORDER BY grades.score2) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2)) \n" +
            ") t2,( SELECT AVG(g.score3) scoreMedian3 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score3 AS score3 FROM (select score3 from (select sum(IFNULL(tt1.total,0)) score3 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =3 GROUP BY tt3.user_id \n" +
            ") tmmp where score3 !=0 union all select score3 from (select DISTINCT sum(IFNULL(tt1.total,0)) score3 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =3 GROUP BY tt3.user_id \n" +
            ") tmmp2 where score3 =0 ORDER BY score3) grades,(Select @rowindex:=-1) b ORDER BY grades.score3) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2)) \n" +
            ") t3 \n" +
            ") tmp2 ORDER BY tmp1.user_id  \n" +
            ") AS L \n" +
            "WHERE 1 = 1 \n" +
            "<if test =\"orgIds != null and orgIds != '' \"> and L.org_id IN  ( ${orgIds}  ) </if> " +
            "GROUP BY L.org_id " +
            "</script>")
    List<TbcUserIndexForm> getOrgUserIndex(@Param("orgIds") String orgIds,
                                           @Param("outOrgId")String outOrgId);


    /**
     * 查询所有党员
     * 如果不传limit 就获取全部
     *
     * @return
     */
    @Select("<script>" +
            "select \n" +
            "user_id UserId,\n" +
            "org_id OrgId,\n" +
            "(score1 * 0.4 + score2 * 0.4 + score3 * 0.2) partyMembersNumber\n" +
            "from (" +
            "select tmp1.user_id,tmp1.org_id, \n" +
            "            round(IFNULL(tmp1.score1/tmp2.scoreMedian1*100,0),4) score1, \n" +
            "            round(IFNULL(tmp1.score2/tmp2.scoreMedian2*100,0),4) score2, \n" +
            "            round(IFNULL(tmp1.score3/tmp2.scoreMedian3*100,0),4) score3 from ( \n" +
            "            select t1.user_id user_id,t1.org_id,IFNULL(t2.score1,0) score1,IFNULL(t2.score2,0) score2,IFNULL(t2.score3,0) score3 from ( \n" +
            "            select u.user_id,uoc.organization_id org_id from t_user u INNER JOIN  t_user_org_corp uoc on u.user_id=uoc.user_id and u.`status`=1 and u.political_type in (1,5) and uoc.is_employee=1 and uoc.organization_id not in (${outOrgId}) \n" +
            "            ) t1 LEFT JOIN ( \n" +
            "            select tt2.user_id,tt4.organization_id org_id, \n" +
            "            sum(case when tt1.parent_score_type=1 then tt1.total else 0 end) score1,  \n" +
            "            sum(case when tt1.parent_score_type=2 then tt1.total else 0 end) score2,  \n" +
            "            sum(case when tt1.parent_score_type=3 then tt1.total else 0 end) score3 \n" +
            "            from t_score_type_count tt1 INNER JOIN t_score_user tt2 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type in (1,2,3) INNER JOIN t_user tt3 on tt2.user_id = tt3.user_id  and tt3.`status` =1 and tt3.political_type in (1,5) INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and  tt4.is_employee=1 and tt4.organization_id not in (${outOrgId}) GROUP BY tt1.score_user_id \n" +
            "            ) t2 on t1.user_id=t2.user_id and t1.org_id =t2.org_id ) tmp1,( \n" +
            "             select scoreMedian1,scoreMedian2,scoreMedian3 from ( \n" +
            "             SELECT AVG(g.score1) scoreMedian1 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score1 AS score1  \n" +
            "             FROM (select score1 from ( \n" +
            "             select sum(IFNULL(tt1.total,0)) score1 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =1 GROUP BY tt3.user_id \n" +
            "             ) tmmp where score1 !=0 union all select score1 from ( \n" +
            "             select DISTINCT sum(IFNULL(tt1.total,0)) score1 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =1 GROUP BY tt3.user_id \n" +
            "             ) tmmp2 where score1 =0 ORDER BY score1) grades,(Select @rowindex:=-1) b ORDER BY grades.score1) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2)) \n" +
            "             ) t1,( SELECT AVG(g.score2) scoreMedian2 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score2 AS score2  \n" +
            "             FROM (select score2 from (select sum(IFNULL(tt1.total,0)) score2 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =2 GROUP BY tt3.user_id \n" +
            "             ) tmmp where score2 !=0 union all select score2 from ( \n" +
            "             select DISTINCT sum(IFNULL(tt1.total,0)) score2 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =2 GROUP BY tt3.user_id \n" +
            "             ) tmmp2 where score2 =0 ORDER BY score2) grades,(Select @rowindex:=-1) b ORDER BY grades.score2) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2)) \n" +
            "             ) t2,( SELECT AVG(g.score3) scoreMedian3 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score3 AS score3 FROM (select score3 from (select sum(IFNULL(tt1.total,0)) score3 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =3 GROUP BY tt3.user_id \n" +
            "             ) tmmp where score3 !=0 union all select score3 from (select DISTINCT sum(IFNULL(tt1.total,0)) score3 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =3 GROUP BY tt3.user_id \n" +
            "             ) tmmp2 where score3 =0 ORDER BY score3) grades,(Select @rowindex:=-1) b ORDER BY grades.score3) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2)) \n" +
            "             ) t3 \n" +
            "            ) tmp2 ORDER BY tmp1.user_id  \n" +
            "            ) as L where 1=1 \n" +
            "order by partyMembersNumber desc " +
            "<if test =\" num != null and num != ''\"> limit #{num} </if>" +
            "</script>")
    List<TbcUserIndexForm> getOrderByIndex(@Param("num") Integer limit,
                                           @Param("outOrgId") String outOrgId);


//    /**
//     * 获取支部堡垒指数排名前三的组织
//     */
//    @Select("select \n" +
//            "log(a.score1 * 0.8 + a.score2 * 0.1 + a.score3 * 0.1) fortressIndex,\n" +
//            "a.orgId orgId,\n" +
//            "`name` orgName\n" +
//            "from (select tmp1.org_id orgId, \n" +
//            "round(IFNULL(tmp1.score1/tmp2.scoreMedian1*100,0),4) score1, \n" +
//            "round(IFNULL(tmp1.score2/tmp2.scoreMedian2*100,0),4) score2, \n" +
//            "round(IFNULL(tmp1.score3/tmp2.scoreMedian3*100,0),4) score3  \n" +
//            "from (select t1.organization_id org_id,IFNULL(t2.score1,0) score1,IFNULL(t2.score2,0) score2, \n" +
//            "IFNULL(t2.score3,0) score3 from ( \n" +
//            "select organization_id  from t_organization  where  organization_id not in(${outOrgId}) and  \n" +
//            "  org_type_child in (10280304,10280309,10280314,10280315,10280319) and `status`=1 \n" +
//            ") t1 LEFT JOIN ( \n" +
//            "select tt1.score_org_id, \n" +
//            "sum(case when tt1.parent_score_type=1 then tt1.total else 0 end) score1,  \n" +
//            "sum(case when tt1.parent_score_type=2 then tt1.total else 0 end) score2,  \n" +
//            "sum(case when tt1.parent_score_type=3 then tt1.total else 0 end) score3  \n" +
//            "from t_score_org_type_count tt1 INNER JOIN t_organization tt2 on tt1.score_org_id = tt2.organization_id  \n" +
//            " and tt1.org_id =3 and tt1.score_org_type =1 and tt1.parent_score_type in (1,2,3) and tt2.org_type_child  \n" +
//            "in (10280304,10280309,10280314,10280315,10280319) and tt2.organization_id not in(${outOrgId}) and tt2.`status`=1 GROUP BY tt1.score_org_id \n" +
//            ") t2 on t1.organization_id = t2.score_org_id \n" +
//            ") tmp1,( \n" +
//            " select scoreMedian1,scoreMedian2,scoreMedian3 from ( \n" +
//            " SELECT AVG(g.score1) scoreMedian1 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score1 AS score1  \n" +
//            " FROM (select score1 from ( \n" +
//            " select sum(IFNULL(tt1.total,0)) score1 from (select organization_id from t_organization where organization_id not in(${outOrgId}) and  \n" +
//            "  org_type_child in (10280304,10280309,10280314,10280315,10280319) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =1 GROUP BY tt3.organization_id \n" +
//            ") tmmp where score1 !=0 \n" +
//            "union all \n" +
//            "select score1 from ( \n" +
//            "select DISTINCT sum(IFNULL(tt1.total,0)) score1 from (select organization_id from t_organization where organization_id not in(${outOrgId}) and  \n" +
//            "org_type_child in (10280304,10280309,10280314,10280315,10280319) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =1 GROUP BY tt3.organization_id \n" +
//            ") tmmp2 where score1 =0 \n" +
//            "ORDER BY score1) grades,(Select @rowindex:=-1) b \n" +
//            "ORDER BY grades.score1) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2)) \n" +
//            ") t1,( \n" +
//            "SELECT AVG(g.score2) scoreMedian2 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score2 AS score2  \n" +
//            "FROM (select score2 from ( \n" +
//            "select sum(IFNULL(tt1.total,0)) score2 from (select organization_id from t_organization where organization_id not in(${outOrgId}) and  \n" +
//            "org_type_child in (10280304,10280309,10280314,10280315,10280319) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =2 GROUP BY tt3.organization_id \n" +
//            ") tmmp where score2 !=0 \n" +
//            "union all \n" +
//            "select score2 from ( \n" +
//            "select DISTINCT sum(IFNULL(tt1.total,0)) score2 from (select organization_id from t_organization where organization_id not in(${outOrgId}) and  \n" +
//            "org_type_child in (10280304,10280309,10280314,10280315,10280319) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =2 GROUP BY tt3.organization_id \n" +
//            ") tmmp2 where score2 =0 \n" +
//            "ORDER BY score2) grades,(Select @rowindex:=-1) b \n" +
//            "ORDER BY grades.score2) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2)) \n" +
//            ") t2,( \n" +
//            "SELECT AVG(g.score3) scoreMedian3 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score3 AS score3  \n" +
//            "FROM (select score3 from ( \n" +
//            "select sum(IFNULL(tt1.total,0)) score3 from (select organization_id from t_organization where organization_id not in(${outOrgId}) and  \n" +
//            "org_type_child in (10280304,10280309,10280314,10280315,10280319) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =3 GROUP BY tt3.organization_id \n" +
//            ") tmmp where score3 !=0 \n" +
//            "union all \n" +
//            "select score3 from ( \n" +
//            "select DISTINCT sum(IFNULL(tt1.total,0)) score3 from (select organization_id from t_organization where organization_id not in(${outOrgId}) and  \n" +
//            "org_type_child in (10280304,10280309,10280314,10280315,10280319) and `status`=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =3 GROUP BY tt3.organization_id \n" +
//            ") tmmp2 where score3 =0 \n" +
//            " ORDER BY score3) grades,(Select @rowindex:=-1) b \n" +
//            " ORDER BY grades.score3) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2)) \n" +
//            " ) t3 ) tmp2 ORDER BY org_id)as a  " +
//            "left join t_organization tor on a.orgId = tor.organization_id where 1=1 \n" +
//            "order by fortressIndex")
//    List<TbcUserIndexForm> getTopThreeMsg(@Param("outOrgId") String outOrgId);
    /**
     * 获取支部堡垒指数排名前几的组织
     */
    @Select("select " +
            "org_id orgId," +
            "org_name orgName," +
            "fortress_index fortressIndex" +
            " from t_tbc_org_info \n" +
            "\tWHERE\n" +
            "\torg_type_child IN ( 10280304, 10280309, 10280314, 10280315, 10280319 ) \n" +
            "\tAND org_id NOT IN ( ${outOrgId} ) \n" +
            "\tAND sta_month = #{staMonth} order by fortress_index desc limit #{num}")
    List<TbcUserIndexForm> getTopThreeMsg(@Param("outOrgId") String outOrgId,
                                          @Param("num") Integer num,
                                          @Param("staMonth") Integer staMonth);




    //用户积分选项
    @Select("\n" +
            "SELECT A2.number,A2.score,A1.party_index as `index`,A2.`name` FROM\n" +
            "(\n" +
            "\tSELECT party_index ,1 as scoreType FROM t_tbc_user_info WHERE user_id = #{userId}" +
            " and sta_month=DATE_FORMAT(now(), '%Y%m')\n" +
            "\tUNION\n" +
            "\tSELECT business_index,2 as scoreType FROM t_tbc_user_info WHERE user_id = #{userId}" +
            " and sta_month=DATE_FORMAT(now(), '%Y%m')\n" +
            "\tUNION\n" +
            "\tSELECT innovation_index,3 as scoreType FROM t_tbc_user_info WHERE user_id = #{userId} " +
            "and sta_month=DATE_FORMAT(now(), '%Y%m')\n" +
            ") as A1\n" +
            "LEFT JOIN (\n" +
            "\tSELECT A.`name`,IF(B.total IS NULL,0,B.total) as number,IF(B.score IS NULL,0,B.score) as score,\n" +
            "\tIF(B.parent_score_type IS NULL,A.option_type,B.parent_score_type) as scoreType FROM \n" +
            "\t(\n" +
            "\t\t\tSELECT * FROM t_overview_option WHERE type=10 and project_name='tbc'\n" +
            "\t) as A\n" +
            "\t\tLEFT JOIN (\n" +
            "\t\t\tSELECT COUNT(1) as total,SUM(score) as score,parent_score_type FROM (\n" +
            "\t\t\t\tSELECT\n" +
            "\t\t\t\t\tscore ,\n" +
            "\t\t\t\t\tscore_type_name,\n" +
            "\t\t\t\t\tcreate_time,\n" +
            "\t\t\t\t\tparent_score_type,\n" +
            "\t\t\t\t\tparent_score_type_name,\n" +
            "\t\t\t\t\texplain_txt \n" +
            "\t\t\t\tFROM\n" +
            "\t\t\t\t\t(\n" +
            "\t\t\t\t\tSELECT\n" +
            "score_user_id,\n" +
            "score,\n" +
            "score_type,\n" +
            "oper_type,\n" +
            "score_type_name,\n" +
            "create_time,\n" +
            "parent_score_type,\n" +
            "parent_score_type_name,\n" +
            "explain_txt \n" +
            "\t\t\t\t\tFROM\n" +
            "t_score_detail_other \n" +
            "\t\t\t\t\tWHERE\n" +
            "org_id = 3 \n" +
            "and score>0\n" +
            "\t\t\t\t\t) t1\n" +
            "\t\t\t\t\tINNER JOIN ( SELECT user_id, score_user_id FROM t_score_user WHERE user_id = #{userId} ) t2 \n" +
            "\t\t\t\t\tON t1.score_user_id = t2.score_user_id \n" +
            "\t\t\t\tORDER BY\n" +
            "\t\t\t\t\tcreate_time DESC\n" +
            "\t\t\t) as L\n" +
            "\t\t\tGROUP BY L.parent_score_type\n" +
            "\t\t) as B\n" +
            "\t\tON A.option_type=B.parent_score_type\n" +
            ") as A2\n" +
            "ON A1.scoreType =A2.scoreType\n" +
            "\n")
    List<TbcBaseIndexForm> getTbcBaseUserScore(@Param("userId") Long userId);

    //组织积分选项
    @Select("SELECT A2.number,A2.score,A1.party_index as `index`,A2.`name` FROM\n" +
            "(\n" +
            "\tSELECT party_index ,1 as scoreType FROM t_tbc_org_info WHERE org_id = #{orgId} and sta_month=#{staMonth}\n" +
            "\tUNION\n" +
            "\tSELECT business_index,2 as scoreType FROM t_tbc_org_info WHERE org_id = #{orgId} and sta_month=#{staMonth} \n" +
            "\tUNION\n" +
            "\tSELECT innovation_index,3 as scoreType FROM t_tbc_org_info WHERE org_id = #{orgId} and sta_month=#{staMonth}\n" +
            ") as A1\n" +
            "LEFT JOIN (\n" +
            "\tSELECT A.`name`,IF(B.total IS NULL,0,b.total) as number,IF(B.score IS NULL,0,b.score) as score,\n" +
            "\tIF(B.parentScoreType IS NULL,A.option_type,B.parentScoreType) as scoreType FROM \n" +
            "\t(\n" +
            "\t\t\tSELECT * FROM t_overview_option WHERE type=10 and project_name='tbc'\n" +
            "\t) as A\n" +
            "\t\tLEFT JOIN (\n" +
            "\t\t\tSELECT COUNT(1) as total,SUM(score) as score,parentScoreType FROM(\n" +
            "\t\t\t\t\tSELECT abs(score) score, \n" +
            "\t\t score_type type, \n" +
            "\t\t oper_type operType, \n" +
            "\t\t score_type_name typeName, \n" +
            "\t\t create_time time, \n" +
            "\t\t parent_score_type parentScoreType,\n" +
            "\t\t parent_score_type_name parentScoreTypeName, \n" +
            "\t\t explain_txt explainTxt \n" +
            "\t\t FROM  t_score_org_detail\n" +
            "\t\t WHERE org_id = 3 \n" +
            "\t\t and score_org_id =#{orgId} \n" +
            "\t\t AND score > 0\n" +
            "\t\t and parent_score_type is not NULL\n" +
            "\t\t order by create_time desc\n" +
            "\t\t\t\t) as L\t\t\t \n" +
            "\t\t\t\tGROUP BY parentScoreType\t\t\t \n" +
            "\t\t) as B\n" +
            "\t\tON A.option_type=B.parentScoreType\n" +
            ") as A2\n" +
            "ON A1.scoreType =A2.scoreType\n" +
            "\n")
    List<TbcBaseIndexForm> getTbcBaseUserOrg(@Param("orgId") Long orgId,@Param("staMonth")String staMonth);


    //得到用户积分记录
    @Select("" +
            "SELECT\n" +
            "  score,\n" +
            "\tscore_type_name as title,\n" +
            "\tscore_type as scoreType,\n" +
            "\tparent_score_type as parentScoreType,\n" +
            "\tcreate_time as createTime,\n" +
            "\tparent_score_type_name as tips,\n" +
            "\texplain_txt as content \n" +
            "FROM\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\tscore_user_id,\n" +
            "\t\tscore,\n" +
            "\t\tscore_type,\n" +
            "\t\toper_type,\n" +
            "\t\tscore_type_name,\n" +
            "\t\tcreate_time,\n" +
            "\t\tparent_score_type,\n" +
            "\t\tparent_score_type_name,\n" +
            "\t\texplain_txt \n" +
            "\tFROM\n" +
            "\t\tt_score_detail_other \n" +
            "\tWHERE\n" +
            "\t\torg_id = 3  \n" +
            "\t\tand score>0\n" +
            "\t) t1\n" +
            "\tINNER JOIN ( SELECT user_id, score_user_id FROM t_score_user WHERE user_id = #{userId} ) t2 ON" +
            "  t1.score_user_id = t2.score_user_id \n" +
            "ORDER BY\n" +
            "\tcreate_time DESC" +
            "")
    List<TbcScoreForm> getTbcUserScoreRecord(@Param("userId") Long userId);


    //得到组织记分记录
    @Select("\t\n" +
            "SELECT\n" +
            "  score,\n" +
            "\tscore_type_name as title,\n" +
            "\tscore_type as scoreType,\n" +
            "\tparent_score_type as parentScoreType,\n" +
            "\tcreate_time as createTime,\n" +
            "\tparent_score_type_name as tips,\n" +
            "\texplain_txt as content \n" +
            "FROM\n" +
            "\tt_score_org_detail \n" +
            "WHERE\n" +
            "\torg_id = 3 \n" +
            "\tAND score_org_id = #{orgId} and score>0 \n" +
            "\tAND parent_score_type in (1,2,3) \n" +
            "ORDER BY\n" +
            "\tcreate_time DESC")
    List<TbcScoreForm> getTbcOrgScoreRecord(@Param("orgId") Long orgId);

//    /**
//     * 获取党员先锋指数排名前十的组织
//     *
//     * @return
//     */
//    @Select("select \n" +
//            "a.org_id as orgId,\n" +
//            "log(a.score1 * 0.4 + a.score2 * 0.4 + a.score3 * 0.2) as partyMembersNumber,\n" +
//            "tor.`name` as orgName\n" +
//            "\n" +
//            "from (\n" +
//            "select tmp1.user_id,tmp1.org_id, \n" +
//            "            round(IFNULL(tmp1.score1/tmp2.scoreMedian1*100,0),4) score1, \n" +
//            "            round(IFNULL(tmp1.score2/tmp2.scoreMedian2*100,0),4) score2, \n" +
//            "            round(IFNULL(tmp1.score3/tmp2.scoreMedian3*100,0),4) score3 from ( \n" +
//            "            select t1.user_id user_id,t1.org_id,IFNULL(t2.score1,0) score1,IFNULL(t2.score2,0) score2,IFNULL(t2.score3,0) score3 from ( \n" +
//            "            select u.user_id,uoc.organization_id org_id from t_user u INNER JOIN  t_user_org_corp uoc on u.user_id=uoc.user_id and u.`status`=1 and u.political_type in (1,5) and uoc.is_employee=1 and uoc.organization_id not in (${outOrgId}) \n" +
//            "            ) t1 LEFT JOIN ( \n" +
//            "            select tt2.user_id,tt4.organization_id org_id, \n" +
//            "            sum(case when tt1.parent_score_type=1 then tt1.total else 0 end) score1,  \n" +
//            "            sum(case when tt1.parent_score_type=2 then tt1.total else 0 end) score2,  \n" +
//            "            sum(case when tt1.parent_score_type=3 then tt1.total else 0 end) score3 \n" +
//            "            from t_score_type_count tt1 INNER JOIN t_score_user tt2 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type in (1,2,3) INNER JOIN t_user tt3 on tt2.user_id = tt3.user_id  and tt3.`status` =1 and tt3.political_type in (1,5) INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and  tt4.is_employee=1 and tt4.organization_id not in (${outOrgId}) GROUP BY tt1.score_user_id \n" +
//            "            ) t2 on t1.user_id=t2.user_id and t1.org_id =t2.org_id ) tmp1,( \n" +
//            "             select scoreMedian1,scoreMedian2,scoreMedian3 from ( \n" +
//            "             SELECT AVG(g.score1) scoreMedian1 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score1 AS score1  \n" +
//            "             FROM (select score1 from ( \n" +
//            "             select sum(IFNULL(tt1.total,0)) score1 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =1 GROUP BY tt3.user_id \n" +
//            "             ) tmmp where score1 !=0 union all select score1 from ( \n" +
//            "             select DISTINCT sum(IFNULL(tt1.total,0)) score1 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =1 GROUP BY tt3.user_id \n" +
//            "             ) tmmp2 where score1 =0 ORDER BY score1) grades,(Select @rowindex:=-1) b ORDER BY grades.score1) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2)) \n" +
//            "             ) t1,( SELECT AVG(g.score2) scoreMedian2 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score2 AS score2  \n" +
//            "             FROM (select score2 from (select sum(IFNULL(tt1.total,0)) score2 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =2 GROUP BY tt3.user_id \n" +
//            "             ) tmmp where score2 !=0 union all select score2 from ( \n" +
//            "             select DISTINCT sum(IFNULL(tt1.total,0)) score2 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =2 GROUP BY tt3.user_id \n" +
//            "             ) tmmp2 where score2 =0 ORDER BY score2) grades,(Select @rowindex:=-1) b ORDER BY grades.score2) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2)) \n" +
//            "             ) t2,( SELECT AVG(g.score3) scoreMedian3 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score3 AS score3 FROM (select score3 from (select sum(IFNULL(tt1.total,0)) score3 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =3 GROUP BY tt3.user_id \n" +
//            "             ) tmmp where score3 !=0 union all select score3 from (select DISTINCT sum(IFNULL(tt1.total,0)) score3 from (select tt3.user_id from t_user tt3 INNER JOIN t_user_org_corp tt4 on tt3.user_id=tt4.user_id and tt3.`status` =1 and tt3.political_type in (1,5) and tt4.is_employee=1 and tt4.organization_id not in (${outOrgId})) tt3 LEFT JOIN t_score_user tt2 on tt3.user_id = tt2.user_id LEFT JOIN t_score_type_count tt1 on tt1.score_user_id=tt2.score_user_id and tt1.org_id=3 and tt1.parent_score_type =3 GROUP BY tt3.user_id \n" +
//            "             ) tmmp2 where score3 =0 ORDER BY score3) grades,(Select @rowindex:=-1) b ORDER BY grades.score3) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2)) \n" +
//            "             ) t3 \n" +
//            "            ) tmp2 ORDER BY tmp1.user_id  \n" +
//            "            ) a \n" +
//            "left join t_organization tor on a.org_id = tor.organization_id where 1=1 group by orgId order by partyMembersNumber desc")
//    List<TbcUserIndexForm> getTopThreeUserOrgMsg(@Param("outOrgId") String outOrgId);

    /**
     * 获取党员先锋指数排名前十的组织
     *
     * @return
     */
    @Select("SELECT\n" +
            "\torg_id AS orgId,\n" +
            "\torg_name AS orgName,\n" +
            "\txianfeng_index AS partyMembersNumber\n" +
            "FROM\n" +
            "\tt_tbc_user_info \n" +
            "WHERE\n" +
            "\torg_id NOT IN ( ${outOrgId} ) \n" +
            "\tAND sta_month = #{staMonth} \n" +
            "ORDER BY\n" +
            "\txianfeng_index DESC \n" +
            "\tLIMIT #{num}")
    List<TbcUserIndexForm> getTopThreeUserOrgMsg(@Param("outOrgId") String outOrgId,
                                                 @Param("num") Integer num,
                                                 @Param("staMonth") Integer staMonth);

    @Select("SELECT AVG(xianfeng_index) as avg, MAX(xianfeng_index) as max,  MIN(xianfeng_index) as min FROM t_tbc_user_info" +
            " WHERE sta_month = #{staMonth} " +
            "   AND org_id NOT IN ( ${outOrgId} )")
    IndexVO queryIndexStat(@Param("staMonth") String staMonth, @Param("outOrgId") String orgId);

    @Select("SELECT AVG(party_index) as avg, MAX(party_index) as max,  MIN(party_index) as min FROM t_tbc_user_info" +
            " WHERE sta_month = #{staMonth}")
    IndexVO queryBuildIndexStat(@Param("staMonth") String staMonth);

    @Select("SELECT AVG(business_index) as avg, MAX(business_index) as max,  MIN(business_index) as min FROM t_tbc_user_info" +
            " WHERE sta_month = #{staMonth}")
    IndexVO queryBusinessIndexStat(@Param("staMonth") String staMonth);

    @Select("SELECT AVG(innovation_index) as avg, MAX(innovation_index) as max,  MIN(innovation_index) as min FROM t_tbc_user_info" +
            " WHERE sta_month = #{staMonth}")
    IndexVO queryInnovationIndexStat(@Param("staMonth") String staMonth);

    @Select("<script>" +
            "SELECT AVG(party_index) as avg, MAX(party_index) as max,  MIN(party_index) as min FROM t_tbc_org_info" +
            " WHERE sta_month = #{staMonth} " +
            "   AND org_type_child in " +
            "<foreach collection=\"orgTypeList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    IndexVO queryBuildFortressStat(@Param("staMonth") String staMonth,
                                   @Param("orgTypeList") List<Integer> orgTypeList);

    @Select("<script>" +
            "SELECT AVG(business_index) as avg, MAX(business_index) as max,  MIN(business_index) as min FROM t_tbc_org_info" +
            " WHERE sta_month = #{staMonth}" +
            "   AND org_type_child in " +
            "<foreach collection=\"orgTypeList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    IndexVO queryBusinessFortressStat(@Param("staMonth") String staMonth,
                                      @Param("orgTypeList") List<Integer> orgTypeList);

    @Select("<script>" +
            "SELECT AVG(innovation_index) as avg, MAX(innovation_index) as max,  MIN(innovation_index) as min FROM t_tbc_org_info" +
            " WHERE sta_month = #{staMonth}" +
            "   AND org_type_child in " +
            "<foreach collection=\"orgTypeList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    IndexVO queryInnovationFortressStat(@Param("staMonth") String staMonth,
                                        @Param("orgTypeList") List<Integer> orgTypeList);

    @Select("<script>" +
            "SELECT AVG(fortress_index) as avg, MAX(fortress_index) as max,  MIN(fortress_index) as min FROM t_tbc_org_info" +
            " WHERE sta_month = #{staMonth}" +
            "   AND org_id NOT IN ( ${outOrgId} ) \n" +
            "   AND org_type_child in " +
            "<foreach collection=\"orgTypeList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    IndexVO queryFortressStat(@Param("staMonth") String staMonth,
                              @Param("outOrgId") String orgId,
                              @Param("orgTypeList") List<Integer> orgTypeList);
}
