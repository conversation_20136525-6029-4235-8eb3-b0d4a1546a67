package com.goodsogood.ows.mapper;

import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Options;
import tk.mybatis.mapper.provider.SpecialProvider;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-07-23 17:05
 **/

public interface StatisticalUserOrgLifeInsertListMapper<T> {
    @Options(useGeneratedKeys = true, keyProperty = "userOrgLifeId")
    @InsertProvider(type = SpecialProvider.class, method = "dynamicSQL")
    int insertList(List<T> recordList);
}
