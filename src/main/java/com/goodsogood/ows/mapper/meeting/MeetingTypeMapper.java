package com.goodsogood.ows.mapper.meeting;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.meeting.MeetingTypeEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-26 11:40
 **/
@Repository
@Mapper
public interface MeetingTypeMapper extends MyMapper<MeetingTypeEntity> {

    @Select("<script>select meeting_type_id,meeting_id,meeting_task_id,type_id,category_id,type,category " +
            " from t_meeting_type where meeting_id = #{meetingId}</script>")
    @Results({
            @Result(column = "meeting_type_id", property = "meetingTypeId"),
            @Result(column = "meeting_id", property = "meetingId"),
            @Result(column = "meeting_task_id", property = "meetingTaskId"),
            @Result(column = "type_id", property = "typeId"),
            @Result(column = "category_id", property = "categoryId"),
            @Result(column = "type", property = "type"),
            @Result(column = "category", property = "category")
    })
    List<MeetingTypeEntity> selectMeetingTypeByMeetingId(@Param("meetingId") Long meetingId);
}