package com.goodsogood.ows.mapper.meeting;

import com.goodsogood.ows.model.db.sas.TypeEntity;
import com.goodsogood.ows.model.vo.meeting.TypeListForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface TypeMapper {

    @Select(" <script> " +
            "SELECT type_id,type,category_id,category,code FROM t_type " +
            "WHERE 1=1 " +
            " <if test =\"typeId != null\"> and type_id = #{typeId} </if> " +
            " <if test =\"categoryId != null\"> and category_id = #{categoryId} </if> " +
            " <if test =\"type != null and type !='' \"> and type like  \"%\"#{type}\"%\"</if> " +
            " </script>")
    @Results({
            @Result(property = "typeId", column = "type_id"),
            @Result(property = "type", column = "type"),
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "category", column = "category")
    })
    List<TypeEntity> listAll(TypeListForm typeListFrom);
}
