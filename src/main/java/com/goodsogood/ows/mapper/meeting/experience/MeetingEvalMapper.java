package com.goodsogood.ows.mapper.meeting.experience;

import com.goodsogood.ows.model.vo.meeting.experience.PrelectionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @ClassName : MeetingEvalMapper
 * <AUTHOR> tc
 * @Date: 2022/3/17 12:39
 * @Description : 党性体检功能，相关组织生活星级评定
 */
@Repository
@Mapper
public interface MeetingEvalMapper {

    /**
     * 根据条件，所有党员讲党课次数
     * @param regionId
     * @param year 查询年份
     * @return
     */
    @Select("<script>" +
            "select #{year} As year,t4.user_id userId,count(1) prelectionNum from t_meeting t1 " +
            " INNER JOIN t_meeting_type t2 on t1.meeting_id = t2.meeting_id and t1.region_id =#{regionId} and t1.status in(7,12,13,14)" +
            " INNER JOIN t_meeting_task t3 on t2.meeting_task_id = t3.meeting_task_id and t3.type_id=#{typeId}" +
            " and DATE_FORMAT(t3.start_time,'%Y') = #{year} and DATE_FORMAT(t3.end_time,'%Y') = #{year}" +
            " INNER JOIN t_meeting_user t4 on t1.meeting_id = t4.meeting_id and t4.tag = 5 and t4.user_id>0" +
            " GROUP BY userId ORDER BY prelectionNum desc" +
            "</script>")
    List<PrelectionVo> selectMeetingUserPrelectionNum(@Param("regionId") Long regionId, @Param("typeId") Integer typeId, @Param("year") Integer year);


    /**
     * 党员参加组织生活，获得星数
     *
     * 5星：按时参加
     * 4星：在规定时间内补学
     * 3星：后超期补学
     * 2星：请假后未补学
     * 1星：未请假
     *
     * @param regionId  区县编号
     * @param userId  用户编号
     * @param typeId   会议类型
     * @param start  会议开始时间
     * @param end   会议结束时间
     * @return
     */
    @Select("<script>" +
            "select IFNULL(max(case when signStatus=1 then 5 when signStatus=6 and type=2 then 4 when signStatus=6 and type=3 then 3" +
            " when signStatus in (3,4) then 2 else 1 end),1) AS score from (" +
            " select t1.meeting_id,t4.user_id,t4.sign_status signStatus,t4.tag,t5.type from t_meeting t1 " +
            " INNER JOIN t_meeting_type t2 on t1.meeting_id = t2.meeting_id and t1.region_id =#{regionId} and t1.status in(7,12,13,14)" +
            " INNER JOIN t_meeting_task t3 on t2.meeting_task_id = t3.meeting_task_id and t3.type_id=#{typeId}" +
            " and DATE_FORMAT(t3.start_time,'%Y%m') &gt;= #{start} and DATE_FORMAT(t3.end_time,'%Y%m') &lt;= #{end}" +
            " INNER JOIN t_meeting_user t4 on t1.meeting_id = t4.meeting_id and t4.tag = 3 and t4.user_id=#{userId}" +
            " LEFT JOIN t_meeting_wait_sign t5 on t1.meeting_id = t5.meeting_id and t4.meeting_user_id = t5.meeting_user_id and t5.type in (2,3)" +
            " and t5.is_now=1) tmp" +
            "</script>")

    Integer selectMeetingUserScore(@Param("regionId") Long regionId,@Param("userId") Long userId,@Param("typeId") Integer typeId, @Param("start") Integer start, @Param("end") Integer end);


    /**
     * 根据条件，查询最近一次参加组织生活日期
     * @param regionId
     * @param userId
     * @param typeId
     * @param start
     * @param end
     * @return
     */
    @Select("<script>" +
            "select max(t1.end_time) joinDate from t_meeting t1 " +
            " INNER JOIN t_meeting_type t2 on t1.meeting_id = t2.meeting_id and t1.region_id =#{regionId} and t1.status in(7,12,13,14)" +
            " INNER JOIN t_meeting_task t3 on t2.meeting_task_id = t3.meeting_task_id and t3.type_id=#{typeId}" +
            " and DATE_FORMAT(t3.start_time,'%Y-%m') &gt;= #{start} and DATE_FORMAT(t3.end_time,'%Y-%m') &lt;= #{end}" +
            " INNER JOIN t_meeting_user t4 on t1.meeting_id = t4.meeting_id and t4.tag = 3 and t4.sign_status=1 and t4.user_id=#{userId}" +
            "</script>")
    Date selectMeetingUserLastJoinDate(@Param("regionId") Long regionId, @Param("userId") Long userId, @Param("typeId") Integer typeId, @Param("start") Integer start, @Param("end") Integer end);

    /**
     * 查询参与排行的总人数，烟草的有效党员人数
     * @param regionId
     * @return
     */
    @Select(" SELECT count(u.user_id) FROM t_user AS u INNER JOIN t_user_org_corp AS uoc ON " +
            " u.user_id = uoc.user_id and uoc.region_id =#{regionId} and u.`status` =1 AND uoc.is_employee = 1" +
            " INNER JOIN t_organization AS o ON uoc.organization_id = o.organization_id and o.region_id =#{regionId}" +
            " AND o.`status` = 1 AND u.political_type in (1,5)")
    Integer selectRankUserCount(@Param("regionId") Long regionId);


}
