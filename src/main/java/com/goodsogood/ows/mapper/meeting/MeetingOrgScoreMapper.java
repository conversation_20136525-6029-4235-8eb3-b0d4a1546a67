package com.goodsogood.ows.mapper.meeting;

import com.goodsogood.ows.model.vo.ScoreResult;
import lombok.Builder;
import lombok.Data;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 计算评分
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface MeetingOrgScoreMapper {

  /**
   * 组织会议相关的基本得分
   *
   * @param form 查询添加
   * @return 获取分值
   */
  @Select(
      "<script> "
          + "<if test =\"form != null and form.size() > 0 \">"
          + "  <foreach collection=\"form\" index=\"index\" item=\"item\" open=\"\" separator=\" UNION ALL \" close=\"\">\n"
          // + "-- 任务总数大于0 且全部完成 得分"
          + " SELECT concat(a.org_id,'-',#{item.currentMonth}) as k,"
          + " if(count(*) &gt; 0 AND SUM(if(`status`=1,1,0)) &lt;= 0 , #{item.score} ,#{item.deductScore}) as score"
          + " FROM t_meeting_task a,t_meeting_plan b,t_type c "
          + " WHERE a.meeting_plan_id = b.meeting_plan_id and a.type_id=c.type_id "
          + "       <if test =\"item.orgIdSet != null and item.orgIdSet.size() > 0 \">"
          + "        and a.org_id in "
          + "         <foreach collection=\"item.orgIdSet\" index=\"iindex\" item=\"iitem\" open=\"(\" separator=\",\" close=\")\">\n"
          + "                    #{iitem}\n"
          + "         </foreach>"
          + "       </if> "
          //  + "-- b.execute_type=2  月 b.execute_type=3  季度"
          + " AND ("
          + "       ( b.execute_type=2 AND c.type_id IN (${item.monthTypeIds})"
          + "         AND DATE_FORMAT(a.start_time,'%Y-%m')  &lt;=  #{item.currentMonth}"
          + "         AND DATE_FORMAT(a.end_time,'%Y-%m')  &gt;=   #{item.currentMonth}"
          + "        ) "
          + "       <if test=\"item.lastMonthOfQuarter\">"
          + "        OR  "
          + "       ( b.execute_type=3 AND c.type_id IN (${item.quarterTypeIds})"
          + "         AND DATE_FORMAT(a.start_time,'%Y-%m')   &lt;=  #{item.currentMonth}"
          + "         AND DATE_FORMAT(a.end_time,'%Y-%m')   &gt;=   #{item.startMonthOfQuarter}"
          + "        ) "
          + "       </if>"
          + "   )"
          + "   GROUP BY a.org_id "
          + "  </foreach>"
          + "</if> "
          + " </script>")
  List<ScoreResult> orgMeetingScore(
      @Param("form") List<OrgMeetingScoreForm> form);

  /**
   * 奖惩得分
   *
   * @param form 条件
   * @return 分值
   */
  @Select(
      "<script> "
          + "<if test =\"form != null and form.size() > 0 \">"
          + "  <foreach collection=\"form\" index=\"index\" item=\"item\" open=\"\" separator=\" UNION ALL \" close=\"\">\n"
          + "    SELECT concat(org_id,'-',#{item.currentMonth}) as k,IF(score IS NULL,0,score) as score FROM"
          + "    ("
          + "       SELECT org_id, SUM(CASE type"
          + "         WHEN 1 THEN #{item.commendScore}"
          + "         WHEN 2 THEN #{item.penalizeScore}"
          + "         ELSE 0"
          + "         END "
          + "       ) AS score FROM t_meeting_org_commend_penalize"
          + "       WHERE `status`=1 "
          + "       <if test =\"item.orgIdSet != null and item.orgIdSet.size() > 0 \">"
          + "        and org_id in "
          + "         <foreach collection=\"item.orgIdSet\" index=\"iindex\" item=\"iitem\" open=\"(\" separator=\",\" close=\")\">\n"
          + "                    #{iitem}\n"
          + "         </foreach>"
          + "       </if> "
          + "       AND DATE_FORMAT(ratify_time,'%Y-%m')=#{item.currentMonth}"
          + "       GROUP BY org_id "
          + "    )t"
          + "  </foreach>"
          + "</if> "
          + " </script>")
  List<ScoreResult> orgCommendPenalizeScore(@Param("form") List<OrgCommendPenalizeScoreForm> form);

  /**
   * 述职评议得分
   *
   * @param form 条件
   * @return 分值
   */
  @Select(
      "<script> "
          + "<if test =\"form != null and form.size() > 0 \">"
          + "  <foreach collection=\"form\" index=\"index\" item=\"item\" open=\"\" separator=\" UNION ALL \" close=\"\">\n"
          + "SELECT concat(org_id,'-',#{item.currentMonth}) as k,IF(score IS NULL,0,score) as score  FROM("
          + "  SELECT"
          //   + "--   `rating` int(4) DEFAULT NULL COMMENT '评议等级 1:好 2:较好 3:一般 4:差',"
          + "   org_id, CASE"
          + "     rating "
          + "       WHEN 1 THEN #{item.rating1Score} "
          + "       WHEN 2 THEN #{item.rating2Score} "
          + "       WHEN 3 THEN #{item.rating3Score} "
          + "       WHEN 4 THEN #{item.rating4Score} "
          + "       ELSE #{item.ratingOtherScore} "
          + "    END as score "
          + "   FROM"
          + "    t_meeting_org_debrief_review "
          + "   WHERE"
          + "    `status` = 1 "
          + "    AND review_year = #{item.year}"
          + "       <if test =\"item.orgIdSet != null and item.orgIdSet.size() > 0 \">"
          + "        and org_id in "
          + "         <foreach collection=\"item.orgIdSet\" index=\"iindex\" item=\"iitem\" open=\"(\" separator=\",\" close=\")\">\n"
          + "                    #{iitem}\n"
          + "         </foreach>"
          + "       </if> "
          + "       GROUP BY org_id "
          + ") t"
          + "  </foreach>"
          + "</if> "
          + " </script>")
  List<ScoreResult> orgDebriefReviewScore(@Param("form") List<OrgDebriefReviewScoreForm> form);


  /** 奖惩得分查询条件 */
  @Data
  @Builder
  class OrgCommendPenalizeScoreForm {
    /** 奖励分值 */
    private Double commendScore;
    /** 惩罚扣分值 */
    private Double penalizeScore;

    private Set<Long> orgIdSet;
    // 当前月份 YYYY-MM
    private String currentMonth;
  }
  /** 述职评议得分查询条件 */
  @Data
  @Builder
  class OrgDebriefReviewScoreForm {
    /** 分值 */
    private Double rating1Score;

    private Double rating2Score;
    private Double rating3Score;
    private Double rating4Score;
    private Double ratingOtherScore;

    private Set<Long> orgIdSet;
    // 当前月份 YYYY-MM
    private String currentMonth;
    private Integer year;
  }
  /** 组织基本得分查询条件 */
  @Data
  @Builder
  class OrgMeetingScoreForm {
    /** 分值 */
    private Double score;
    /** 扣分值 */
    private Double deductScore;

    private Set<Long> orgIdSet;
    // 当前月份 YYYY-MM
    private String currentMonth;
    // 是否是季度最后一月
    private Boolean lastMonthOfQuarter;
    // 季度开始月份 YYYY-MM
    private String startMonthOfQuarter;
    /** 月度类型的typeId 逗号分割 */
    private String monthTypeIds;
    /** 季度类型的typeId 逗号分割 */
    private String quarterTypeIds;
  }
}
