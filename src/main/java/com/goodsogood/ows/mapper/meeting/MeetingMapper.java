package com.goodsogood.ows.mapper.meeting;

import com.goodsogood.ows.configuration.MeetingConfig;
import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.meeting.MeetingEntity;
import com.goodsogood.ows.model.db.sas.StatisticalUserOrgLifeEntity;
import com.goodsogood.ows.model.mongodb.dss.MeetingDevelopInfo;
import com.goodsogood.ows.model.mongodb.fusion.DualLifeDetail;
import com.goodsogood.ows.model.mongodb.fusion.LeaderDutyDetail;
import com.goodsogood.ows.model.mongodb.fusion.LeadersContactDetail;
import com.goodsogood.ows.model.mongodb.fusion.MeetingDetail;
import com.goodsogood.ows.model.vo.meeting.MeetingForm;
import com.goodsogood.ows.model.vo.meeting.MeetingUserForm;
import com.goodsogood.ows.model.vo.meeting.MonthMeetingForm;
import com.goodsogood.ows.model.vo.meeting.YearMeetingForm;
import com.goodsogood.ows.model.vo.overview.KeyValVo;
import com.goodsogood.ows.model.vo.sas.SasUserOrgLifeForm;
import com.goodsogood.ows.service.rank.RemoteRuleWryService;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.goodsogood.ows.configuration.MeetingConfig.*;

/**
 * 会议Mapper
 *
 * <AUTHOR>
 * @date 2019/11/21
 * @return
 */
@Repository
@Mapper
public interface MeetingMapper extends MyMapper<MeetingEntity> {

    /**
     * 根绝组织和时间查询会议记录
     *
     * @param orgId
     * @param statsTime
     * @return java.util.List<com.goodsogood.ows.model.db.meeting.MeetingEntity>
     * <AUTHOR>
     * @date 2019/11/21
     */
    @Select("<script>" +
            "SELECT a.org_id, a.org_name, a.meeting_id, a.`name`, b.type_id, b.type, a.start_time " +
            "FROM t_meeting AS a LEFT JOIN t_meeting_type AS b ON a.meeting_id = b.meeting_id  " +
            "WHERE a.org_id = #{orgId} AND a.`status` IN ( 7, 8, 10, 11, 12, 13, 14 ) AND DATE_FORMAT(start_time,'%Y-%m') = #{statsTime};" +
            "</script>")
    @Results({
            @Result(column = "org_id", property = "orgId"),
            @Result(column = "org_name", property = "orgName"),
            @Result(column = "meeting_id", property = "meetingId"),
            @Result(column = "name", property = "name"),
            @Result(column = "type_id", property = "typeId"),
            @Result(column = "type", property = "typeName"),
            @Result(column = "start_time", property = "startTime")
    })
    List<MeetingForm> selectMeetingByOrgId(@Param("orgId") Long orgId, @Param("statsTime") String statsTime);

    /**
     * 根绝时间查询会议记录
     *
     * @param statsTime
     * @return java.util.List<com.goodsogood.ows.model.db.meeting.MeetingEntity>
     * <AUTHOR>
     * @date 2019/11/21
     */
    @Select("<script>" +
            "SELECT a.org_id, a.org_name, a.meeting_id, a.`name`, a.start_time " +
            "FROM t_meeting AS a " +
            "WHERE a.`status` IN ( 7, 8, 10, 11, 12, 13, 14 ) AND DATE_FORMAT(start_time,'%Y-%m') = #{statsTime};" +
            "</script>")
    @Results({
            @Result(column = "org_id", property = "orgId"),
            @Result(column = "org_name", property = "orgName"),
            @Result(column = "meeting_id", property = "meetingId"),
            @Result(column = "name", property = "name"),
            @Result(column = "start_time", property = "startTime")
    })
    List<MeetingForm> selectMeetingByDate(@Param("statsTime") String statsTime);


    /**
     * 根绝时间查询会议记录
     *
     * @param meetingIds
     * <AUTHOR>
     * @date 2020/11/30
     */
    @Select("<script>" +
            "SELECT GROUP_CONCAT (a.start_time) as meetingTimes" +
            " FROM t_meeting AS a " +
            "WHERE a.`meeting_id` IN (${meetingIds})" +
            "</script>")
    String getMeetingTime(@Param("meetingIds") String meetingIds);

    /**
     * 根据会员ID查询会议人员记录
     *
     * @param meetingId
     * @return java.util.List<com.goodsogood.ows.model.vo.meeting.MeetingUserForm>
     * <AUTHOR>
     * @date 2019/11/21
     */
    @Select("<script>" +
            "select user_id, user_name, sign_status, GROUP_CONCAT(tag) tag from t_meeting_user where meeting_id = #{meetingId} GROUP BY user_id" +
            "</script>")
    @Results({
            @Result(column = "user_id", property = "userId"),
            @Result(column = "user_name", property = "userName"),
            @Result(column = "sign_status", property = "signStatus"),
            @Result(column = "tag", property = "tag")
    })
    List<MeetingUserForm> selectUserByMeetingId(@Param("meetingId") Long meetingId);

    /**
     * 统计用户每月参加各类型的组织生活次数
     *
     * @param sasUserOrgLifeForm 统计条件
     * @return List<StatisticsMeetingForm>
     */
    @Select("<script>"
            + " SELECT "
            + "     YEAR(t2.start_time) AS statisticalYear,"
            + "     MONTH(t2.start_time) AS statisticalMonth,"
            + "     t1.user_id as userId,"
            + "     t2.region_id as regionId,"
            + "     t3.type_id as typeId,"
            + "     t3.type as typeName,"
            + "     count(1) as participateNum  "
            + " FROM (\n"
            + "      SELECT user_id,meeting_id,SUM("
            + "        <choose>"
            + "              <when test=\"signStatus != null and signStatus.size() > 0 \">"
            + "                     if(sign_status in"
            + "                       <foreach collection=\"signStatus\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                                  #{item}\n"
            + "                       </foreach>"
            + "                     ,0,-1) "
            + "              </when>"
            + "              <otherwise>"
            + "                   0 "
            + "              </otherwise>"
            + "        </choose>)  AS ctag "
            + "    FROM t_meeting_user\n"
            + "      WHERE 1=1 \n"
            + "         <if test =\"uids != null and uids.size() != 0 \">"
            + "                  and user_id in "
            + "            <foreach collection=\"uids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                #{item}"
            + "            </foreach>"
            + "         </if> "
            + "         <if test =\"tags != null and tags.size() != 0 \">"
            + "                  and tag in "
            + "            <foreach collection=\"tags\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                #{item}"
            + "            </foreach>"
            + "         </if> "
            + "      GROUP BY user_id,meeting_id\n"
            + "      ) t1,"
            + "         t_meeting t2,"
            + "         (SELECT meeting_id,type_id,type from t_meeting_type GROUP BY meeting_id,type_id ) t3 "
            + " WHERE "
            + "   t1.meeting_id = t2.meeting_id "
            + "   AND t1.meeting_id = t3.meeting_id\n"
            + "   AND t1.ctag = 0 \n"
            + "   AND t2.is_del=0"
            + "   AND t2.region_id=#{regionId}"
            + "   AND t2.`status` IN ( 7, 8, 10, 11, 12, 13, 14 ) \n"
            + "   <if test=\"orgId !=null\"> AND t2.org_id=#{orgId} </if>"
            + " GROUP BY "
            + "    t1.user_id,t3.type_id,YEAR(t2.start_time),MONTH(t2.start_time)"
            + "</script>")
    List<StatisticalUserOrgLifeEntity> sasUserOrgLife(SasUserOrgLifeForm sasUserOrgLifeForm);

    /**
     * 获取组织一年中每个月参加组织生活的类型及次数
     *
     * @param year
     * @param orgId
     * @param typeIds
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            "  month(m.start_time) as `month`,\n" +
            "  mt.type_id as typeId,\n" +
            "  mt.type,\n" +
            "  count(*) total\n" +
            "FROM\n" +
            "  t_meeting m,\n" +
            "  t_meeting_type mt \n" +
            "WHERE\n" +
            "  m.is_del = 0\n" +
            "  AND m.meeting_id = mt.meeting_id \n" +
            "  AND m.`status` IN ( 7, 8, 10, 11, 12, 13, 14 ) \n" +
            "  AND mt.type_id IN (${typeIds} ) \n" +
            "  AND year(m.start_time) = #{year}\n" +
            "\tAND m.org_id = #{orgId}\n" +
            " GROUP BY\n" +
            "  mt.type_id,month(m.start_time)\n" +
            "</script>")
    List<MonthMeetingForm> getMonthOrgMeeting(@Param("year") Integer year,
                                              @Param("orgId") Long orgId,
                                              @Param("typeIds") String typeIds);

    /**
     * 获取用户每月开展的组织生活信息
     *
     * @param year    年
     * @param month   月
     * @param typeIds 会议类型
     * @param userId  用户id
     * @return List<MeetingDevelopInfo>
     */
    @Select("<script>" +
            "  SELECT\n" +
            "    month(m.start_time) as `month`,\n" +
            "    DATE_FORMAT(m.start_time,'%Y-%m-%d') as `date`,\n" +
            "    m.org_name as orgName,\n" +
            "    mt.type_id as typeId,\n" +
            "    mt.type\n" +
            "  FROM\n" +
            "    t_meeting m,\n" +
            "    t_meeting_type mt,\n" +
            "    t_meeting_user mu\t\n" +
            "  WHERE\n" +
            "    m.is_del = 0\n" +
            "    AND m.meeting_id = mt.meeting_id \n" +
            "  \tAND m.meeting_id=mu.meeting_id\n" +
            "    AND m.`status` IN ( 7, 8, 10, 11, 12, 13, 14 ) \n" +
            "    AND mt.type_id IN ( ${typeIds} ) \n" +
            "    AND year(m.start_time) = #{year}\n" +
            "    AND month(m.start_time) = #{month}\n" +
            "  \tAND mu.user_id = #{userId}\n" +
//          "  GROUP BY\n" +
//          "    mt.type_id,month(m.start_time) \n" +
            "</script>")
    List<MeetingDevelopInfo> getMonthUserMeeting(@Param("year") Integer year,
                                                 @Param("month") Integer month,
                                                 @Param("typeIds") String typeIds,
                                                 @Param("userId") Long userId);

    /**
     * 查询月份和党委组织下级所有组织的完成的数量
     *
     * @param orgIds
     * @param queryTime
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            "  COUNT(DISTINCT mt.org_id)\n" +
            "FROM\n" +
            "  t_meeting_task mt,\n" +
            "  t_meeting_plan mp \n" +
            "WHERE\n" +
            "  mt.meeting_plan_id = mp.meeting_plan_id \n" +
            "  AND mp.is_del = 0 \n" +
            "  AND mp.is_execute = 1 \n" +
            "  AND mt.`status` = 1\n" +
            "  AND mt.type_id IN ( 60, 61, 62, 63, 64 ) \n" +
            "  AND DATE_FORMAT(mt.start_time,'%Y-%m') <![CDATA[<=]]>  #{queryTime}\n" +
            "  AND DATE_FORMAT(mt.end_time,'%Y-%m') <![CDATA[>=]]> #{queryTime}\n" +
            "  AND mt.org_id IN " +
            " <foreach collection=\"orgIds\" index=\"index\" item=\"orgId\" open=\"(\" separator=\",\" close=\")\">\n" +
            " #{orgId}" +
            " </foreach>" +
            "</script>")
    Integer getMeetingComplete(@Param("orgIds") List<Long> orgIds,
                               @Param("queryTime") String queryTime);

    /**
     * 工委级 - 查询组织生活统计情况
     *
     * @param year                  年份
     * @param type                  会议类型
     * @param noRetireBranchOrgIds  非离退休 & 管理组织的组织id
     * @param hasPeriodBranchOrgIds 非离退休 & 管理组织 & 设置了支委会的组织id
     * @return YearMeetingForm
     */
    @Select(
            "<script>"
                    + "SELECT \n"
                    + " IFNULL(SUM(total),0) total,\n"
                    + " <foreach collection=\"type\" index=\"index\" item=\"item\" open=\"\" separator=\",\" close=\"\">\n"
                    + "    <choose>"
                    + "         <when test=\"item.mappingType == " + PARTY_MEMBER + "\">"
                    + "             IFNULL(MAX(if(type_id= #{item.typeId},total,0)),0) as partyMember \n"
                    + "         </when>"
                    + "         <when test=\"item.mappingType == " + PARTY_COMMITTEE + "\">"
                    + "             IFNULL(MAX(if(type_id= #{item.typeId},total,0)),0) as partyCommittee \n"
                    + "         </when>"
                    + "         <when test=\"item.mappingType == " + PARTY_GROUP + "\">"
                    + "             IFNULL(MAX(if(type_id= #{item.typeId},total,0)),0) as partyGroup \n"
                    + "         </when>"
                    + "         <when test=\"item.mappingType == " + PARTY_LECTURE + "\">"
                    + "            IFNULL(MAX(if(type_id= #{item.typeId},total,0)),0) as partyLecture \n"
                    + "         </when>"
                    + "         <when test=\"item.mappingType == " + PARTY_THEME_DAY + "\">"
                    + "            IFNULL(MAX(if(type_id= #{item.typeId},total,0)),0) as partyThemeDay \n"
                    + "         </when>"
                    + "    </choose>"
                    + " </foreach>"
                    + "FROM (\n"
                    + "SELECT\n"
                    + "  mt.type_id,\n"
                    + "  count(*) total\n"
                    + "FROM\n"
                    + "  t_meeting m,\n"
                    + "  t_meeting_type mt \n"
                    + "WHERE\n"
                    + "  m.is_del = 0\n"
                    + "  AND m.meeting_id = mt.meeting_id \n"
                    + "  AND m.`status` IN ( 7, 8, 10, 11, 12, 13, 14 ) \n"
                    + "  AND mt.type_id IN  \n"
                    + "    <foreach collection=\"type\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "               <if test = \"item.mappingType != " + PARTY_COMMITTEE + "\">#{item.typeId}</if>\n"
                    + "    </foreach>"
                    + "     "
                    + "  AND year(m.start_time) = #{year}\n"
                    + "  AND m.org_id IN (${noRetireBranchOrgIds}) \n"
                    + "GROUP BY\n"
                    + "  mt.type_id\n"
                    + " UNION "
                    + "SELECT\n"
                    + "  mt.type_id,\n"
                    + "  count(*) total\n"
                    + "FROM\n"
                    + "  t_meeting m,\n"
                    + "  t_meeting_type mt \n"
                    + "WHERE\n"
                    + "  m.is_del = 0\n"
                    + "  AND m.meeting_id = mt.meeting_id \n"
                    + "  AND m.`status` IN ( 7, 8, 10, 11, 12, 13, 14 ) \n"
                    + "  AND mt.type_id IN  \n"
                    + "    <foreach collection=\"type\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "               <if test = \"item.mappingType == " + PARTY_COMMITTEE + "\">#{item.typeId}</if>\n"
                    + "    </foreach>"
                    + "     "
                    + "  AND year(m.start_time) = #{year}\n"
                    + "  AND m.org_id IN (${hasPeriodBranchOrgIds}) \n"
                    + "GROUP BY\n"
                    + "  mt.type_id\n"
                    + ") t"
                    + "</script>")
    YearMeetingForm getYearMeeting(@Param("year") Integer year,
                                   @Param("type") List<MeetingConfig.MeetingType> type,
                                   @Param("noRetireBranchOrgIds") String noRetireBranchOrgIds,
                                   @Param("hasPeriodBranchOrgIds") String hasPeriodBranchOrgIds);

    /**
     * 工委级 - 查询组织生活统计情况
     *
     * @param currentMonth         月 yyyy-mm
     * @param isLastMonthOfQuarter 是否是季度最后一月
     * @param startMonthOfQuarter  季度开始月份  yyyy-mm
     * @param orgIds               组织id
     * @param monthTypeIds         月度会议类型
     * @param quarterTypeIds       季度会议类型
     * @return 未完成的支部数量
     */
    @Select(
            "<script>SELECT\n"
                    + "  COUNT(DISTINCT mt.org_id)\n"
                    + "FROM\n"
                    + "  t_meeting_task mt,\n"
                    + "  t_meeting_plan mp \n"
                    + "WHERE\n"
                    + "  mt.meeting_plan_id = mp.meeting_plan_id \n"
                    + "  AND mp.is_del = 0 \n"
                    + "  AND mp.is_execute = 1 \n"
                    // -- 完成状态 1完成 2 未完成
                    + "  AND mt.`status` = 1 \n"
                    + "  AND ( (mt.type_id IN ( ${monthTypeIds} ) \n"
                    + "        AND DATE_FORMAT(mt.start_time,'%Y-%m') &lt;=  #{currentMonth}\n"
                    + "        AND DATE_FORMAT(mt.end_time,'%Y-%m') &gt;= #{currentMonth})\n"
                    + "      <if test=\"isLastMonthOfQuarter\">\n"
                    + "          OR (mt.type_id IN ( ${quarterTypeIds} ) \n"
                    + "             AND DATE_FORMAT(mt.start_time,'%Y-%m') &lt;=  #{startMonthOfQuarter}\n"
                    + "             AND DATE_FORMAT(mt.end_time,'%Y-%m') &gt;= #{currentMonth})\n"
                    + "      </if>\n"
                    + "      )\n"
                    + "\tAND mt.org_id IN (${orgIds})"
                    + "</script>")
    int unfinishedBranchNum(
            @Param("currentMonth") String currentMonth,
            @Param("isLastMonthOfQuarter") Boolean isLastMonthOfQuarter,
            @Param("startMonthOfQuarter") String startMonthOfQuarter,
            @Param("orgIds") String orgIds,
            @Param("monthTypeIds") String monthTypeIds,
            @Param("quarterTypeIds") String quarterTypeIds);

    @Select("SELECT count(m.meeting_id) >=1\n" +
            "FROM t_meeting_user mu \n" +
            "inner join t_meeting m on mu.meeting_id = m.meeting_id AND m.is_del = 0 AND m.`status` IN ( 7, 8, 10, 11, 12, 13, 14 ) \n" +
            "AND mu.tag = 5 and mu.user_id=#{userId} and year(m.start_time)=${year} and m.region_id=3 " +
            "inner join (select distinct user_id from t_user_org_leader where is_delete=2 and region_id=3 and user_id=#{userId} and year(create_time)<= ${year}) leader on mu.user_id=leader.user_id\n" +
            "GROUP BY mu.user_id ")
    Boolean leaderLectures(@Param("userId") Long userId, @Param("year") String year);

    @Select("SELECT mu.user_id id,count( m.meeting_id ) >= 1 aBoolean\n" +
            "FROM t_meeting_user mu \n" +
            "inner join t_meeting m on mu.meeting_id = m.meeting_id AND m.is_del = 0 AND m.`status` IN ( 7, 8, 10, 11, 12, 13, 14 ) \n" +
            "AND mu.tag = 5 and mu.user_id in (${ids}) and year(m.start_time)=${year} and m.region_id=3 " +
            "inner join (select distinct user_id from t_user_org_leader where is_delete=2 and region_id=3 and user_id in (${ids}) and year(create_time)<= ${year}) leader on mu.user_id=leader.user_id\n" +
            "GROUP BY mu.user_id ")
    List<RemoteRuleWryService.QueryResult> leaderLectures2(@Param("ids") String ids, @Param("year") String year);

    @Select("select rating\n" +
            "from t_meeting_user_comment  uc\n" +
            "inner join t_user_snapshot tu1 on tu1.status=1 and uc.user_id= tu1.user_id and uc.`status`=1 " +
            " AND (tu1.position_code NOT IN(49,50,51,52,53) or tu1.position_code is null or tu1.position_code ='') " +
            "AND tu1.political_type IN(1,5,17,18) " +
            " and tu1.region_id=3 and (tu1.is_retire!=1 or tu1.is_retire is null)" +
            "and uc.review_year=#{year} and tu1.date_month=CONCAT(#{year},'-',#{month}) and uc.user_id=#{userId}\n" +
            "order by uc.user_comment_id desc")
    List<Integer> getYearUserCommentResult(@Param("userId") Long userId, @Param("year") Integer year, @Param("month") String month);

    @Select("select uc.user_id id,rating type\n" +
            "from t_meeting_user_comment  uc\n" +
            "inner join t_user_snapshot tu1 on tu1.status=1 and uc.user_id= tu1.user_id and uc.`status`=1 " +
            " AND (tu1.position_code NOT IN(49,50,51,52,53) or tu1.position_code is null or tu1.position_code ='') " +
            "AND tu1.political_type IN(1,5,17,18) " +
            " and tu1.region_id=3 and (tu1.is_retire!=1 or tu1.is_retire is null)" +
            "and uc.review_year=${year} and tu1.date_month=CONCAT(#{year},'-',#{month}) and uc.user_id in (${ids})\n" +
            "order by uc.user_comment_id desc")
    List<RemoteRuleWryService.QueryResult> getYearUserCommentResult2(@Param("ids") String ids, @Param("year") String year, @Param("month") String month);

    @Select("select  count(us.user_snapshot_id) !=0 and (count(us.user_snapshot_id) =count(uc.user_comment_id))\n" +
            "from t_user_snapshot us \n" +
            "left join t_meeting_user_comment uc on us.status=1 and us.user_id=uc.user_id and (uc.org_id=#{orgId} or uc.org_level like concat('%-',#{orgId},'-%'))  and uc.review_year=#{year}\n" +
            "where us.date_month=concat(#{year},'-',#{month}) and us.org_id=#{orgId} " +
            " AND (us.position_code NOT IN(49,50,51,52,53) or us.position_code is null or us.position_code ='') AND us.political_type IN(1,5,17,18) and us.region_id=3 " +
            "and us.org_type_child in (${orgType})\n" +
            "group by us.org_id")
    Boolean lastYearOrgCommentResult(@Param("orgId") Long orgId, @Param("year") int year, @Param("month") String month, @Param("orgType") String orgType);

    @Select("select  #{orgId} id,count(us.user_snapshot_id) !=0 and (count(us.user_snapshot_id) =count(uc.user_comment_id)) aBoolean\n" +
            "from t_user_snapshot us \n" +
            "left join t_meeting_user_comment uc on us.status=1 and us.user_id=uc.user_id and (uc.org_id=#{orgId} or uc.org_level like concat('%-',#{orgId},'-%'))  and uc.review_year=${year}\n" +
            "where us.date_month=concat(#{year},'-',#{month}) and us.org_id=#{orgId} " +
            " AND (us.position_code NOT IN(49,50,51,52,53) or us.position_code is null or us.position_code ='') AND us.political_type IN(1,5,17,18) and us.region_id=3 " +
            "and us.org_type_child in (${orgType})\n" +
            "group by us.org_id")
    List<RemoteRuleWryService.QueryResult> lastYearOrgCommentResult2(@Param("orgId") Long orgId, @Param("year") String year, @Param("month") String month, @Param("orgType") String orgType);


    @Select("SELECT   COUNT(1) FROM t_meeting_task" +
            " WHERE type_id=#{activityId} and org_id=#{orgId}  " +
            " and start_time>=#{startTime} and end_time<#{endTime}")
    Integer getMeetTaskCount(@Param("orgId") Long orgId, @Param("activityId") Integer activityId,
                             @Param("startTime") String startTime, @Param("endTime") String endTime);

    @Select("<script> " +
            " SELECT   COUNT(1) FROM t_meeting_task" +
            " WHERE type_id=#{activityId} and org_id in (${orgIds}) " +
            " and start_time <![CDATA[ >= ]]> #{startTime} and end_time <![CDATA[ < ]]> #{endTime}" +
            " <if test=\" meetingNumber != null \"> and meeting_num <![CDATA[ >= ]]> #{meetingNumber}</if> " +
            " </script>")
    Integer getMeetTaskCountByOrgIds(@Param("orgIds") String orgIds, @Param("activityId") Integer activityId,
                                     @Param("startTime") String startTime, @Param("endTime") String endTime,
                                     @Param("meetingNumber") Integer meetingNumber);

    /**
     * 针对option_6特殊处理党小组
     */
    @Select("SELECT org_id FROM\n" +
            "(\n" +
            "SELECT\n" +
            "   org_id,COUNT(1) as number\n" +
            "FROM\n" +
            "    t_meeting_task \n" +
            "WHERE\n" +
            "    type_id=#{activityId} \n" +
            "    and org_id in (${orgIds}) \n" +
            "    and start_time >= #{startTime} \n" +
            "    and end_time < #{endTime} \n" +
            "\t\tGROUP BY org_id\n" +
            ") as L\n" +
            "WHERE L.number>=3")
    List<Integer> getMeetTaskCountBySpecial(@Param("orgIds") String orgIds, @Param("activityId") Integer activityId,
                                            @Param("startTime") String startTime, @Param("endTime") String endTime);


    @Select("SELECT A.alias as `name`,IF(b.value IS NULL,0,b.value) as `value` FROM (\n" +
            "            SELECT * FROM t_overview_option WHERE type=6 and project_name='overview' and overview_option_id!=29 \n" +
            ") as A\n" +
            "LEFT JOIN (\n" +
            " SELECT\n" +
            "( CASE rating WHEN 1 THEN '优秀' WHEN 2 THEN '合格' WHEN 3 THEN '基本合格' " +
            " WHEN 4 THEN '不合格' ELSE '不确定等次' END ) AS `name`,\n" +
            " rating_number  as value\n" +
            "FROM(\n" +
            "SELECT t1.rating,sum(t1.rating_number) AS rating_number \n" +
            "FROM  t_meeting_comment_statistics t1\n" +
            "JOIN t_meeting_comment t2 ON t1.comment_id = t2.comment_id \n" +
            "WHERE t1.region_id = 19 AND  (t1.org_id=#{orgId} or t1.org_level like concat('%-',#{orgId},'-%')) " +
            " AND t1.`year` = #{year} AND t2.STATUS = 6 \n" +
            "GROUP BY t1.rating\n" +
//            "UNION ALL\n" +
//            "(SELECT '5' AS rating,sum( party_number - join_number ) FROM\n" +
//            "(SELECT t1.comment_id,t1.party_number,t1.join_number \n" +
//            "FROM t_meeting_comment_statistics t1\n" +
//            "JOIN t_meeting_comment t2 ON t1.comment_id = t2.comment_id \n" +
//            "WHERE t1.region_id = 19 AND  (t1.org_id=#{orgId} or t1.org_level like concat('%-',#{orgId},'-%')) " +
//            " AND t1.`year` = #{year}  AND t2.STATUS = 6 \n" +
//            "GROUP BY t1.comment_id ) a ) \n" +
            ") b" +
            ") as B ON A.name=B.name\n" +
            "ORDER BY A.order_num")
    List<KeyValVo> orgLifeOverviewDemocraticReview(@Param("orgId") Long orgId, @Param("year") Integer year);


    @Select("SELECT A.alias as `name`,IF(b.value IS NULL,0,b.value) as `value` FROM (\n" +
            "            SELECT * FROM t_overview_option WHERE type=7 and project_name='overview' and overview_option_id not in(36,38)  \n" +
            ") as A\n" +
            "LEFT JOIN (\n" +
            "\tSELECT name as `name`,sum( L.meeting_num) as `value` FROM (\n" +
            "\t\tSELECT type_id,`name`,meeting_num\n" +
            "\t\tfrom t_meeting_task\n" +
            "\t\twhere DATE_FORMAT(start_time,'%Y') = DATE_FORMAT(now(),'%Y') and `name` <>'民主生活会'  and `name` <>'组织生活会' " +
            " and meeting_num>0 and org_id IN \n" +
            "\t\t(select organization_id from t_organization WHERE (org_level LIKE CONCAT('%-',#{orgId},'-%') " +
            " or organization_id=#{orgId}))\n" +
            "\t) as L\n" +
            "\tGROUP BY type_id\n" +
            " UNION \n" +
            " select '组织生活会',count(1) as `value` from t_meeting_org_life where region_id=19 \n" +
            " and (org_id=#{orgId} or org_level like concat('%-',#{orgId},'-%')) " +
            " and status>=3 and is_del=0 and `years`=date_format(now(),'%Y')" +
//            " UNION \n"+
//            "select '民主生活会' as `name`,count(1) as value from t_meeting_life where status>=3 and is_del=0" +
//            " and `years`=date_format(now(),'%Y') and (org_level LIKE CONCAT('%-',#{orgId},'-%') or org_id=#{orgId})"+
//            " UNION \n"+
//            "SELECT '谈心谈话' as `name`, COUNT(1) as `value`\n" +
//            "  FROM t_meeting_talk\n" +
//            " WHERE `status` = 1\n" +
//            " AND copy_id = 0\n" +
//            " AND YEAR(begin_time) = YEAR(CURRENT_DATE())\n" +
//            " AND YEAR(end_time) = YEAR(CURRENT_DATE())\n" +
//            " AND (org_id = #{orgId} or org_level LIKE CONCAT('%-',#{orgId},'-%'))"+
            ") as B ON A.name=B.name\n" +
            "ORDER BY A.order_num")
    List<KeyValVo> orgLifeOverviewOrgLife(@Param("orgId") Long orgId);


    @Select("SELECT overview_option_id as typeId,A.alias as `name`,IF(b.value IS NULL,0,b.value) as `value` FROM (\n" +
            "            SELECT * FROM t_overview_option WHERE type=7 and project_name='overview' \n" +
            ") as A\n" +
            "LEFT JOIN (\n" +
            "\tSELECT name as `name`,count(1) as `value` FROM (\n" +
            "\t\tSELECT type_id,`name`,org_id as num\n" +
            "\t\tfrom t_meeting_task\n" +
            "\t\twhere DATE_FORMAT(start_time,'%Y') = DATE_FORMAT(now(),'%Y') and and QUARTER(start_time)= QUARTER(now()) " +
            " and type_id in (1,4) " +
            "and `name` <>'民主生活会'  and `name` <>'组织生活会' " +
            " and meeting_num>0 and org_id IN \n" +
            "\t\t(select organization_id from t_organization WHERE (org_level LIKE CONCAT('%-',#{orgId},'-%') " +
            " or organization_id=#{orgId}))\n" +
            "\t) as L\n" +
            "\tGROUP BY type_id,org_id\n" +
            " UNION \n" +
            "\tSELECT name as `name`,count(1) as `value` FROM (\n" +
            "\t\tSELECT type_id,`name`,org_id as num\n" +
            "\t\tfrom t_meeting_task\n" +
            "\t\twhere DATE_FORMAT(start_time,'%Y-%m') = DATE_FORMAT(now(),'%Y-%m') " +
            " and type_id in (2,5,3) " +
            "and `name` <>'民主生活会'  and `name` <>'组织生活会' " +
            " and meeting_num>0 and org_id IN \n" +
            "\t\t(select organization_id from t_organization WHERE (org_level LIKE CONCAT('%-',#{orgId},'-%') " +
            " or organization_id=#{orgId}))\n" +
            "\t) as L\n" +
            "\tGROUP BY type_id,org_id\n" +
            " UNION \n" +
            " select '组织生活会',count(distinct org_id) as `value` from t_meeting_org_life where region_id=19 \n" +
            " and (org_id=#{orgId} or org_level like concat('%-',#{orgId},'-%')) " +
            " and status>=3 and is_del=0 and `years`=date_format(now(),'%Y')" +
            " UNION \n" +
            "select '民主生活会' as `name`,count(distinct org_id) as value from t_meeting_life where status>=3 and is_del=0" +
            " and `years`=date_format(now(),'%Y') and (org_level LIKE CONCAT('%-',#{orgId},'-%') or org_id=#{orgId})" +
            ") as B ON A.name=B.name\n" +
            "ORDER BY A.order_num")
    List<KeyValVo> orgLifeOverviewOrgLifeNew1(@Param("orgId") Long orgId);


    @Select("<script>" +
            "select t1.org_name as name,t2.user_name as valueStr, t2.user_id as id from t_meeting_comment t1\n" +
            "join t_meeting_comment_member t2 on t1.comment_id=t2.comment_id\n" +
            "left join t_meeting_comment_member_complex t3 on t2.comment_member_id=t3.comment_member_id\n" +
            "where t1.year=#{year} and t1.region_id=#{regionId} and (t1.org_id=#{orgId} or t1.org_level like concat('%-',#{orgId},'-%')) " +
            "and t1.status=6  " +
            " and t3.complex_rating in "
            + "<foreach collection=\"types\" item=\"type\" open=\"(\" separator=\",\" close=\")\">\n"
            + " #{type}\n"
            + " </foreach>"
            + "</script>")
    List<KeyValVo> orgLifeOverviewDemocraticReviewType(@Param("regionId") Long regionId, @Param("types") List<Integer> types,
                                                       @Param("year") Integer year, @Param("orgId") Long orgId);

    @Select(" <script> select IFNULL(sum(case when sign_status=1 then 1 else 0 end)/count(1)*100,0) rate from (" +
            " select t2.user_id,t2.user_name,t2.sign_status,t1.types from t_meeting t1 INNER JOIN" +
            " t_meeting_user t2 on t1.meeting_id=t2.meeting_id and t2.user_id = #{userId}" +
            " where t1.is_del=0  and t1.status in(7,12,13,14)" +
            " and t1.types like CONCAT('%',#{typeName},'%')" +
            " <if test=\"startTime != null and startTime!='' \">and date_format(t1.start_time,'%Y-%m-%d') &gt;=#{startTime}</if>" +
            " <if test=\"endTime != null and endTime!='' \">and date_format(t1.start_time,'%Y-%m-%d') &lt;=#{endTime}</if> " +
            " and t2.tag=3" +
            ") tmp </script>")
    /**
     * 查询会议出勤率
     */
    Double meetingJoinRate(@Param("userId") Long userId, @Param("typeName") String typeName,
                           @Param("startTime") String startTime, @Param("endTime") String endTime);


    @Select("<script> select count(1) cn from (" +
            " select t2.user_id,t2.user_name,t2.sign_status,t1.types from t_meeting t1 INNER JOIN" +
            " t_meeting_user t2 on t1.meeting_id=t2.meeting_id and t2.user_id = #{userId}" +
            " where t1.is_del=0  and t1.status in(7,12,13,14)" +
            " and t1.types like CONCAT('%','党课','%')" +
            " <if test=\"startTime != null and startTime!='' \">and date_format(t1.start_time,'%Y-%m-%d') &gt;=#{startTime}</if>" +
            " <if test=\"endTime != null and endTime!='' \">and date_format(t1.start_time,'%Y-%m-%d') &lt;=#{endTime}</if> " +
            " and t2.tag=5" +
            ") tmp </script>")
    /**
     * 查询讲党课次数
     */
    Integer partyClassCount(@Param("userId") Long userId,
                            @Param("startTime") String startTime, @Param("endTime") String endTime);

    @Select("<script> select IFNULL(sum(case when STATUS=1 then 1 else 0 END),0) unfinished" +
            " from t_meeting_task where type_id =#{typeId} " +
            " <if test=\"startTime != null and startTime!='' \">and DATE_FORMAT(start_time,'%Y-%m-%d') &gt;= #{startTime}</if>" +
            " <if test=\"endTime != null and endTime!='' \">and DATE_FORMAT(start_time,'%Y-%m-%d') &lt;= #{endTime}</if>  " +
            " and org_id in (SELECT organization_id FROM t_organization WHERE owner_id = #{unitId} AND `status` = 1) </script>")
    /**
     * 查询单位下级组织组织生活未完成数量(除了党小组会)
     */
    Integer meetingUnfinishedCountByUnitId(@Param("typeId") Integer typeId, @Param("unitId") Long unitId,
                                           @Param("startTime") String startTime, @Param("endTime") String endTime);

    @Select("<script> select IFNULL(sum(case when STATUS=1 then 1 else 0 END),0) unfinished" +
            " from t_meeting_task where type_id =3" +
            " <if test=\"startTime != null and startTime!='' \">and DATE_FORMAT(start_time,'%Y-%m-%d') &gt;= #{startTime}</if>" +
            " <if test=\"endTime != null and endTime!='' \">and DATE_FORMAT(start_time,'%Y-%m-%d') &lt;= #{endTime}</if> " +
            " and org_id in (SELECT organization_id FROM t_organization WHERE" +
            " `status` = 1 and org_type_child IN (10280306)" +
            " and parent_id in (select organization_id from t_organization where owner_id = #{unitId} and `status` = 1)) </script>")
    /**
     * 查询单位下级组织党小组会未完成数量
     */
    Integer meetingUnfinishedCountPartyGroup(@Param("unitId") Long unitId,
                                             @Param("startTime") String startTime, @Param("endTime") String endTime);


    @Select("select t1.start_time as startTime,t1.name as name from t_meeting t1 join t_meeting_type t2 on t1.meeting_id=t2.meeting_id\n" +
            "where t1.org_id=#{orgId} and year(t1.start_time)=year(now()) and t2.type_id in (${typeIds}) and t1.status in (${status}) " +
            " order by t1.start_time desc limit #{num}")
    List<MeetingEntity> queryOrgMeetingList(@Param("orgId") Long orgId, @Param("typeIds") String typeIds, @Param("status") String status, @Param("num") Integer num);

    @Select("select count(distinct t1.meeting_id) from t_meeting t1 join t_meeting_type t2 on t1.meeting_id=t2.meeting_id\n" +
            "where t1.org_id=#{orgId} and year(t1.start_time)=#{year} and t2.type_id in (${typeIds}) and t1.status in (7,8,10,11,12,13,14,15)")
    Integer queryOrgMeetingNum(@Param("orgId") Long orgId, @Param("typeIds") String typeIds, @Param("status") String status, @Param("year") int year);

    /**
     * 获取年份获取评议和应评议的数据
     *
     * @param year          查询年费，yyyy
     * @param regionId      region id
     * @param orgId         查询的组织id
     * @param excludeOrgIds 排除组织id
     * @return map -> 列 : 值
     */
    @Select("<script>" +
            "SELECT" +
            " count( IF ( t1.`status` = 6, 1, NULL ) ) AS '已评议'," +
            " count( 1 ) AS '应评议'" +
            " FROM " +
            " t_meeting_comment t1 " +
            " LEFT JOIN t_meeting_comment_member t2 ON t1.comment_id = t2.comment_id" +
            " WHERE" +
            " t1.year = #{year}" +
            " AND t1.region_id = #{regionId}" +
            " AND ( t1.org_id = #{orgId} OR t1.org_level LIKE concat( '%-', #{orgId}, '-%' ) )" +
            "<if test=\"excludeOrgIds != null and excludeOrgIds.size != 0 \"> " +
            "<foreach collection=\"excludeOrgIds\" index=\"index\" item=\"item\">\n" +
            "   AND t1.org_id <![CDATA[ <> ]]> #{item} AND t1.org_level not like concat('%-', #{item}, '-%') " +
            "</foreach>" +
            "</if>" +
            "</script>")
    Map<String, Object> countOrgMeetingComment(
            @Param("year") Integer year,
            @Param("regionId") Long regionId,
            @Param("orgId") Long orgId,
            @Param("excludeOrgIds") List<Long> excludeOrgIds
    );

    @Select("<script>" +
            "SELECT count( t2.user_id ) \n" +
            "FROM t_meeting t1 \n" +
            "\tJOIN t_meeting_user t2 ON t1.meeting_id = t2.meeting_id \n" +
            "\tAND t2.sign_status IN ( 1, 6 ) \n" +
            "\tAND t2.tag = 3 \n" +
            "WHERE t1.start_time LIKE concat( YEAR ( CURRENT_DATE ), '%' ) \n" +
            "\tAND t1.theory_learn IS NOT NULL \n" +
            "\tAND t1.STATUS IN ( 7, 12, 13, 14 ) \n" +
            "\tAND t1.is_del = 0 \n" +
            "<if test=\"userList != null and userList.size != 0\">" +
            "AND t2.user_id IN " +
            "<foreach collection=\"userList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            " #{item} " +
            "</foreach>" +
            "</if>" +
            "</script>")
    Integer learnTime(@Param("userList") List<Long> userList);

    @Select("SELECT ROUND(sum(theory_learn)) \n" +
            "FROM t_meeting \n" +
            "WHERE start_time LIKE concat(YEAR(CURRENT_DATE), '%' ) \n" +
            "\tAND STATUS IN ( 7, 12, 13, 14 ) \n" +
            "\tAND is_del = 0")
    Integer trainingTime();

    @Select("<script>" +
            "select t1.meeting_id meetingId,date_format(t1.start_time,'%Y-%m-%d %H:%i') meetingDate,t1.name meetingName from t_meeting t1\n" +
            "join t_meeting_type t2 on t1.meeting_id=t2.meeting_id and t2.type_id in (${types})\n" +
            "where\n" +
            "t1.org_id in \n" +
            "      <foreach collection=\"orgIds\" index=\"index\" item=\"org\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                 #{org}\n"
            + "      </foreach>" +
            "and t1.start_time like concat(#{dateMonth},'%')\n" +
            "and t1.status in (${status}) and t1.is_del=0 " +
            "order by t1.start_time " +
            "</script>"
    )
    List<MeetingDetail> partyGroup(@Param("types") String types, @Param("status") String status, @Param("dateMonth") String dateMonth, @Param("orgIds") List<Long> orgIds);


    @Select("<script>" +
            "select t1.meeting_id meetingId,date_format(t1.start_time,'%Y-%m-%d %H:%i') meetingDate," +
            "t1.name meetingName,group_concat(t3.tag_id) as tag from t_meeting t1\n" +
            "join t_meeting_type t2 on t1.meeting_id=t2.meeting_id and t2.type_id in (${types})\n" +
            "left join t_meeting_tag t3 on t1.meeting_id=t3.meeting_id \n" +
            "where\n" +
            "t1.org_id in \n" +
            "      <foreach collection=\"orgIds\" index=\"index\" item=\"org\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                 #{org}\n"
            + "      </foreach>" +
            "and t1.start_time like concat(#{dateMonth},'%')\n" +
            "and t1.status in(${status}) and t1.is_del=0 " +
            "group by t1.meeting_id " +
            "order by t1.start_time " +
            "</script>"
    )
    List<LeaderDutyDetail> leaderGroup(@Param("orgIds") List<Long> orgIds, @Param("types") String types, @Param("status") String status, @Param("dateMonth") String dateMonth);


    @Select("<script>" +
            "select t2.user_id userId,t2.user_name userName,t1.org_id as orgId from t_meeting t1\n" +
            "join t_meeting_type t3 on t1.meeting_id=t3.meeting_id\n" +
            "join t_meeting_user t2 on t1.meeting_id=t2.meeting_id and t2.sign_status in(1) \n" +
            "where t3.type_id in(${types}) and t1.start_time like concat(#{dateMonth},'%') and t1.status in(${status}) and t1.is_del=0 \n" +
            " and t2.user_id in " +
            "      <foreach collection=\"userList\" index=\"index\" item=\"user\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                 #{user.userId}\n"
            + "      </foreach>" +
            "group by t2.user_id,t1.meeting_id\n" +
            "</script>"
    )
    List<DualLifeDetail> leaderLife(@Param("unitId") Long unitId, @Param("types") String types, @Param("status") String status, @Param("dateMonth") String dateMonth, @Param("userList") List<DualLifeDetail> userList);

    @Select("<script>" +
            "select t2.user_id userId,t2.user_name userName,t1.org_id as orgId from t_meeting t1\n" +
            "join t_meeting_type t3 on t1.meeting_id=t3.meeting_id\n" +
            "join t_meeting_user t2 on t1.meeting_id=t2.meeting_id and t2.tag=5 \n" +
            "where t3.type_id in(${types}) and  t1.start_time like concat(#{dateMonth},'%') and t1.status in(${status}) and t1.is_del=0 \n" +
            " and t2.user_id in " +
            "      <foreach collection=\"userList\" index=\"index\" item=\"user\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                 #{user.userId}\n"
            + "      </foreach>" +
            "group by t2.user_id,t1.meeting_id \n" +
            "</script>"
    )
    List<LeadersContactDetail> lectureList(@Param("unitId") Long unitId, @Param("types") String types, @Param("status") String status, @Param("dateMonth") String dateMonth, @Param("userList") List<LeadersContactDetail> userList);

    @Select("<script>" +
            "select t1.meeting_id meetingId,t1.types meetingType,date_format(t1.start_time,'%Y-%m-%d %H:%i') meetingDate," +
            "t1.name meetingName,\n" +
            "group_concat(t3.tag_id) tag from t_meeting t1\n" +
            "join t_meeting_type t2 on t1.meeting_id=t2.meeting_id \n" +
            "left join t_meeting_tag t3 on t1.meeting_id=t3.meeting_id \n" +
            "where\n" +
            "t1.org_id in \n" +
            "<foreach collection=\"orgIds\" item=\"org\" open=\"(\" separator=\",\" close=\")\">" +
            "#{org}" +
            "</foreach>" +
            "and t1.start_time like concat(#{dateMonth},'%') \n" +
            "and t2.type_id in(${types})\n" +
            "and t1.status in(${status}) and t1.is_del=0\n" +
            "group by t1.meeting_id order by t1.start_time " +
            "</script>")
    List<MeetingDetail> studyList(@Param("orgIds") List<Long> orgIds, @Param("types") String types, @Param("status") String status, @Param("dateMonth") String dateMonth);


    /**
     * 查询用户某年参加组织生活次数-不包含党小组会
     *
     * @param year     year
     * @param userId   userId
     * @param typeList typeList
     * @return 参加总次数
     */
    @Select("""
            <script>
            SELECT
            	COUNT( 1 )
            FROM
            	t_meeting m,
            	t_meeting_type mt,
            	t_meeting_user mu
            WHERE
            	m.is_del = 0
            	AND m.meeting_id = mt.meeting_id
            	AND m.meeting_id = mu.meeting_id
            	AND m.`status` IN ( 7, 12, 13, 14 )
            	<if test="typeList != null and typeList.size != 0">
            	AND mt.type_id IN
            	    <foreach item="typeId" collection="typeList" open="(" separator="," close=")">
            	        #{typeId}
            	    </foreach>
            	</if>
            	AND m.start_time LIKE CONCAT( #{year}, '%' )
            	AND QUARTER ( m.start_time ) = #{quarter}
            	AND mu.user_id = #{userId}
            	AND mu.tag = 3
                AND mu.sign_status = 1
            </script>
            """)
    Integer findMeetingCount(@Param("year") Integer year, @Param("userId") Long userId,
                             @Param("typeList") List<Integer> typeList, @Param("quarter") Integer quarter);

    @Select("""
                SELECT
                	COUNT(DISTINCT m.meeting_id)
                FROM
                	t_meeting AS m
                	LEFT JOIN t_meeting_type AS mt ON m.meeting_id = mt.meeting_id
                	LEFT JOIN t_meeting_user AS mu ON mt.meeting_id = mu.meeting_id\s
                WHERE
                	mt.type_id = #{typeId}
                	AND mu.user_id = #{userId}
                	AND m.start_time LIKE CONCAT(#{year}, '%')
                	AND m.`status` in (7,12,13,14)
                	AND mu.tag = 3
                	AND m.is_del = 0
                	AND mu.sign_status = 1
            """)
    Integer findMeetingTypeCount(@Param("typeId") Integer typeId, @Param("userId") Long userId, @Param("year") Integer year);

    /**
     * 通过会议标签和会议类型获取会议列表
     *
     * @param orgId     orgId
     * @param startTime startTime
     * @param endTime   endTime
     * @param tagIds    tagIds
     * @param types     types
     * @return 会议列表
     */
    @Select("""
            <script>
            SELECT
            	DISTINCT m.meeting_id meetingId,
            	m.name name,
            	m.start_time startTime,
            	m.end_time endTime,
                CASE
            	    WHEN m.theory_learn IS NOT NULL THEN
            		m.theory_learn ELSE 0.0
            	END theoryLearn
            FROM
                t_meeting_task mt
                LEFT JOIN t_meeting_type AS mType ON mt.meeting_task_id = mType.meeting_task_id
                LEFT JOIN t_meeting AS m ON mType.meeting_id = m.meeting_id
                LEFT JOIN t_meeting_tag AS mTag ON m.meeting_id = mTag.meeting_id
                WHERE
                	m.is_del = 0
                    AND m.start_time &gt;= #{startTime}
                 	AND m.start_time &lt;= #{endTime}
                	AND m.`status` IN ( 7, 12, 13, 14 )
                	<if test="orgId != null and orgId !=''">
                	AND mt.org_id = #{orgId}
                	</if>
                	<if test="tagIds != null and tagIds.size != 0">
                	AND  mTag.tag_id IN
                    <foreach item="tagId" collection="tagIds" open="(" separator="," close=")">
                        #{tagId}
                    </foreach>
                	</if>
                	<if test="types != null and types.size != 0">
                	AND mType.type IN
                	    <foreach item="type" collection="types" open="(" separator="," close=")">
                	        #{type}
                	    </foreach>
                	</if>
             </script>
            """)
    List<MeetingEntity> getMeetingListByTagAndType(
            @Param("orgId") Long orgId,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("tagIds") List<Long> tagIds,
            @Param("types") List<String> types);

    @Select("""
            select COUNT(1) from t_meeting m
            	left join t_meeting_type mt on m.meeting_id=mt.meeting_id
            	left join t_meeting_tag tag on m.meeting_id=tag.meeting_id
            	where m.status in(7,12,13,14) and tag.tag_id=#{tagId}
            	and mt.type_id=#{typeId}
            	and m.org_id=#{orgId}
            	and year(m.start_time)=#{year}
            """)
    Integer getStudyClassCount(@Param("typeId") Integer typeId,
                               @Param("tagId") Long tagId,
                               @Param("year") Integer year,
                               @Param("orgId") Long orgId);


    @Select("""
            <script>
             SELECT user_id FROM
             (
             	select mu.user_id,sum(if(mu.sign_status in (1,6), ifnull(m.theory_learn,0),0 )) theory_learn, sum(if(mu.sign_status in (1,6), TIMESTAMPDIFF(HOUR, m.start_time, m.end_time),0)) h from t_meeting m
             	left join (
             	select meeting_id,user_id,user_name,tag,min(if(tag=5,1,sign_status))sign_status from t_meeting_user
             	where tag in (3,5)
             	and user_id <![CDATA[ > ]]>  0
             	group by meeting_id,user_id
             	)mu on m.meeting_id=mu.meeting_id
             	where m.status in(7,12,13,14)
             	and year(m.start_time)= #{year}
             	and mu.user_id in
             	<foreach collection="userIds" item="userId" open="(" separator="," close=")" >
                       #{userId}
                </foreach>
             	group by mu.user_id
             ) as t
             WHERE t.theory_learn <![CDATA[ >= ]]> 32 or t.h <![CDATA[ >= ]]> 32
             </script>
            """)
    List<Long> getTheoryStudyUser(@Param("userIds") List<Long> userIds, @Param("year") Integer year);

    /**
     * 验证当前组织是否有对应的会议任务
     *
     * @param orgId     组织id
     * @param startTime 开始时间
     * @param types     会议类型
     * @return 会议任务数量
     */
    @Select("""
            <script>
            select count(1) from t_meeting_task mt where
                 mt.org_id = #{orgId}
                 and mt.start_time = #{startTime}
                 <if test="types != null and types.size != 0">
                 AND mt.type IN
                     <foreach item="type" collection="types" open="(" separator="," close=")">
                         #{type}
                     </foreach>
                 </if>
             </script>
             """)
    Long getTaskUserCount(@Param("orgId") Long orgId,
                          @Param("startTime") String startTime,
                          @Param("types") List<String> types
    );

    /**
     * 获取会议提交时间和开展时间超过5天的数据
     * 需要处理的会议包含 党员大会、支委会、党小组会、党课、主题党日、中心组学习、党组会学习
     *
     * @param orgId     组织id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param types     会议类型
     * @return 数量
     */
    @Select("""
     <script>
            SELECT
            	count(DISTINCT m.meeting_id)
            FROM
            	t_meeting m
            	LEFT JOIN t_meeting_history mh ON m.meeting_id = mh.meeting_id
            	LEFT JOIN t_meeting_type mt ON m.meeting_id = mt.meeting_id
            WHERE
            	mh.`status` = 7
            	AND m.`status` IN ( 7, 12, 13, 14 )
                <if test="types != null and types.size != 0">
                AND mt.type_id IN
                   <foreach item="type" collection="types" open="(" separator="," close=")">
                     #{type}
                   </foreach>
                </if>
            	AND m.start_time <![CDATA[ >= ]]> #{startTime}
            	AND m.start_time <![CDATA[ <= ]]> #{endTime} AND ( TIMESTAMPDIFF( DAY, m.start_time, mh.create_time ) <![CDATA[ > ]]> 5
            	 OR TIMESTAMPDIFF( HOUR, m.start_time, mh.create_time ) <![CDATA[ > ]]> 120
            	)
            	and m.org_id = #{orgId}
     </script>
            """)
    Long getMeetingCount(@Param("orgId") Long orgId,
                         @Param("startTime") LocalDateTime startTime,
                         @Param("endTime") LocalDateTime endTime,
                         @Param("types") List<Integer> types);
}
