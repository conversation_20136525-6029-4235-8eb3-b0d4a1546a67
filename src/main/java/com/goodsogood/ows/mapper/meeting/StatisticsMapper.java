package com.goodsogood.ows.mapper.meeting;

import com.goodsogood.ows.model.vo.dataCockpit.DataCockpitOverviewActivityVo;
import com.goodsogood.ows.model.vo.meeting.MeetingForm;
import com.goodsogood.ows.model.vo.overview.OverviewActivityFinishVo;
import com.goodsogood.ows.model.vo.overview.OverviewActivityVo;
import com.goodsogood.ows.model.vo.sas.StatisticsMeetingForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import javax.websocket.server.ServerEndpoint;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
@Mapper
public interface StatisticsMapper {


    //SELECT count(1) as countNum, DATE_FORMAT(start_time,'%Y-%m') as staTime , typeId, typeName
    // FROM (
    // SELECT  DISTINCT a.org_name , b.type_id as typeId,b.type as typeName ,a.start_time,a.meeting_id
    // FROM t_meeting as a
    // LEFT JOIN t_meeting_type as b
    // ON a.meeting_id=b.meeting_id
    // WHERE a.org_id=388 AND a.`status` in (7,8,10,11,12,13,14)
    // AND DATE_SUB(CURDATE(), INTERVAL 24 MONTH)  <=  date(a.start_time)
    // )as L
    // WHERE typeId=63
    // GROUP BY typeId,staTime

//    @Select("<script>SELECT count(1) as countNum, DATE_FORMAT(start_time,'%Y-%m') as staTime , typeId, typeName \n" +
//            " FROM (\n" +
//            " SELECT  DISTINCT a.org_name , b.type_id as typeId,b.type as typeName ,a.start_time,a.meeting_id \n" +
//            " FROM t_meeting as a\n" +
//            " LEFT JOIN t_meeting_type as b\n" +
//            " ON a.meeting_id=b.meeting_id\n" +
//            " WHERE a.org_id=#{orgId} AND a.`status` in (${queryStatus}) \n" +
//            " AND DATE_SUB(CURDATE(), ${queryCode}) <![CDATA[ <= ]]> date(a.start_time)\n" +
//            " )as L\n" +
//            " GROUP BY typeId,staTime</script>")
//    List<StatisticsMeetingForm> statisticsOrganizeInfo(@Param("orgId")Long orgId, @Param("queryCode") String queryCode, @Param("queryStatus") String queryStatus);


    @Select("<script>" +
            "SELECT  typeId,typeName, staTime ,SUM(countNum) as countNum," +
            " GROUP_CONCAT(startTime) as meetingStartTime," +
            "GROUP_CONCAT(meetingIds) as meetingIds FROM\n" +
            "(\n" +
            "\t\tSELECT DISTINCT  activity_id as typeId, " +
            "type_name as  typeName, 0 as countNum,date as staTime, " +
            " NULL as  startTime ,NULL as meetingIds FROM \n" +
            "\t\t\t(\n" +
            "\t\t\tSELECT a.*  FROM t_statistical_temp_activity  as a\n" +
            "\t\t\tLEFT JOIN t_eval_option as b\n" +
            "\t\t\ton a.activity_id=b.meeting_type_ids\n" +
            "\t\t\tWHERE b.region_id=#{regionId}\n" +
            "\t\t\t) as T1,\n" +
            "\t\t\t(SELECT  LEFT( adddate( DATE_SUB( CURDATE( ), ${queryCode}), INTERVAL numlist.id+1 MONTH ), 7 ) AS 'date' \n" +
            "\t\t\t\tFROM\t( \n" +
            "\t\t\t\t\t\tSELECT *  FROM ( SELECT n1.i + n10.i * 10 AS id FROM t_num n1 CROSS JOIN t_num AS n10 ) a  \n" +
            "\t\t\t\t\t\tWHERE a.id <![CDATA[ < ]]>(SELECT TIMESTAMPDIFF(MONTH,DATE_SUB( CURDATE(), ${queryCode}),CURDATE()))\n" +
            "\t\t\t\t) AS numlist \n" +
            "\t\t\t\t\tWHERE  adddate( DATE_SUB( CURDATE(),${queryCode}), INTERVAL numlist.id MONTH ) <![CDATA[ <=]]> CURDATE()\n" +
            "\t\t\t) as T2 WHERE date>'2019-01' \t\t\n" +
            "\tUNION    ALL\n" +
            "\t\tSELECT  typeId, typeName ,count(1) as countNum," +
            " DATE_FORMAT(start_time,'%Y-%m') as staTime ," +
            " GROUP_CONCAT(start_time)  as startTime," +
            " GROUP_CONCAT(meeting_id) as meetingIds\n" +
            "\t\tFROM (\n" +
            "\tSELECT  DISTINCT a.org_name , b.type_id as typeId,b.type as typeName ," +
            "a.start_time,a.meeting_id  \n" +
            "\tFROM t_meeting as a\n" +
            "\t LEFT JOIN t_meeting_type as b\n" +
            "\t ON a.meeting_id=b.meeting_id\n" +
            "\t WHERE a.org_id=#{orgId} AND a.`status` in (${queryStatus}) \n" +
            "\t AND DATE_FORMAT(DATE_SUB(CURDATE(), ${queryCode}),\" %Y-%m\")" +
            " <![CDATA[ <= ]]> DATE_FORMAT(DATE_SUB(date(a.start_time),INTERVAL  1 MONTH),\" %Y-%m\") \n" +
            "\t )as L\n" +
            "\t GROUP BY typeId,staTime\n" +
            " ) as Last_T\n" +
            " GROUP BY typeId,typeName,staTime" +
            "</script>")
    List<StatisticsMeetingForm> statisticsOrganizeInfo(@Param("orgId") Long orgId,
                                                       @Param("queryCode") String queryCode,
                                                       @Param("queryStatus") String queryStatus,
                                                       @Param("regionId") Long regionId);


    @Select("<script>" +
            "SELECT  LEFT( adddate( DATE_SUB( CURDATE( ), INTERVAL 6 MONTH ), INTERVAL numlist.id+1 MONTH ), 7 ) AS 'date' \n" +
            "\t\t\tFROM\t( \n" +
            "\t\t\t\t\tSELECT *  FROM ( SELECT n1.i + n10.i * 10 AS id FROM t_num n1 CROSS JOIN t_num AS n10 ) a  \n" +
            "\t\t\t\t\tWHERE a.id <![CDATA[ < ]]>(SELECT TIMESTAMPDIFF(MONTH,DATE_SUB( CURDATE(), INTERVAL  6 MONTH ),CURDATE()))\n" +
            "\t\t\t) AS numlist \n" +
            "\t\t\t\tWHERE  adddate( DATE_SUB( CURDATE(), INTERVAL  6 MONTH ), INTERVAL numlist.id MONTH ) <![CDATA[ <= ]]> CURDATE()" +
            "</script>")
    List<String> statisticsMonth(@Param("queryCode") String queryCode);

    @Select("<script>"
            + "select \n"
            + " c_table.date as staTime,\n"
            + " c_table.activity_id as typeId,\n"
            + " c_table.type_name as typeName,\n"
            + " IFNULL(c.total,0) as countNum\n"
            + "FROM\n"
            + "(SELECT\n"
            + " temp.date,\n"
            + " ta.type_id as activity_id,\n"
            + " ta.type as type_name\n"
            + "FROM\n"
            + "(\n"
            + " SELECT left(adddate( DATE_SUB( CURDATE( ), ${queryCode} ), interval numlist.id month),7) \n"
            + "AS 'date' FROM \n"
            + " (\n"
            + " SELECT * from \n"
            + " (SELECT n1.i + n10.i * 10 AS id FROM t_num n1 CROSS JOIN t_num AS n10) a \n"
            + " where a.id <![CDATA[ <= ]]> "
            + "(SELECT TIMESTAMPDIFF(MONTH,DATE_SUB( CURDATE(), INTERVAL  4 YEAR ),CURDATE()))\n"
            + "  ) AS numlist\n"
            + "  WHERE adddate( DATE_SUB( CURDATE( ), ${queryCode} ), interval numlist.id month) "
            + "<![CDATA[ <= ]]> CURDATE( )) temp INNER JOIN\n"
            + "  (SELECT a.type_id,a.type FROM t_type as a INNER JOIN \n"
            + " t_category as b on a.category_id=b.category_id "
            + " \tWHERE b.region_id=#{regionId}) ta) c_table LEFT JOIN \n"
            + "  (\n"
            + " SELECT DISTINCT\n"
            + "  c.type_id,\n"
            + "  DATE_FORMAT( a.start_time, '%Y-%m' ) as start_time,\n"
            + "  count(DISTINCT a.meeting_id) as total\n"
            + " FROM\n"
            + "  t_meeting AS a\n"
            + "  INNER JOIN ("
            + "      SELECT user_id,meeting_id,SUM("
            + "        <choose>"
            + "              <when test=\"signStatus != null and signStatus.size() > 0 \">"
            + "                     if(sign_status in"
            + "                       <foreach collection=\"signStatus\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                                  #{item}\n"
            + "                       </foreach>"
            + "                     ,0,-1) "
            + "              </when>"
            + "              <otherwise>"
            + "                   0 "
            + "              </otherwise>"
            + "        </choose>)  AS ctag "
            + "    FROM t_meeting_user\n"
            + "      WHERE user_id = #{userId} \n"
            + "      GROUP BY user_id,meeting_id\n"
            + "      )"
            + " AS b ON a.meeting_id = b.meeting_id\n"
            + "  INNER JOIN t_meeting_type AS c ON a.meeting_id = c.meeting_id \n"
            + " WHERE\n"
            + "  a.`status` IN ( 7, 8, 10, 11, 12, 13, 14 ) \n"
            + "  AND a.is_del = 0   "
            + "   AND b.ctag = 0 \n"
            + "<if test=\"orgIds != null and orgIds.size != 0\"> and a.org_id in"
            + " <foreach item=\"orgId\" collection=\"orgIds\" open=\"(\" separator=\",\" close=\")\">#{orgId}</foreach> "
            + "</if>"
            + " GROUP BY\n"
            + "  DATE_FORMAT( a.start_time, '%Y-%m' ), c.type_id\n"
            + " ) c on c_table.date = c.start_time AND c_table.activity_id = c.type_id"
            + "</script>")
    List<StatisticsMeetingForm> statisticsOrganizeUserInfo(
            @Param("regionId") Long regionId,
            @Param("orgIds") Set<Long> orgIdList,
            @Param("userId") Long userId,
            @Param("queryCode") String queryCode,
            @Param("signStatus") List<Integer> signStatus);


    @Select("<script>" +
            "SELECT A.alias as `name`,IF(b.value IS NULL,0,b.value) as `number`,A.option_type as `type` FROM (\n" +
            "            SELECT * FROM t_overview_option WHERE type=8 and project_name='overview' \n" +
            ") as A\n" +
            "LEFT JOIN (\n" +
            "\tSELECT `name`, COUNT(1) as value FROM\n" +
            "\t t_meeting_task where DATE_FORMAT(start_time,'%Y-%m') <![CDATA[  >= ]]> #{startTime} \n" +
            "\tand DATE_FORMAT(start_time,'%Y-%m') <![CDATA[ <= ]]> #{endTime} and type_id in (1,4) and status=2 \n" +
            "\tand org_id in (select organization_id from t_organization " +
            " where (org_level LIKE CONCAT('%-',#{orgId},'-%') or organization_id=#{orgId}) \n" +
            "\tand org_type_child in(10280304,10280309,10280314,10280315,10280319))\n" +
            "\tGROUP BY type_id\n" +
            "\tUNION\n" +
            "\tSELECT `name`, COUNT(1) as value from t_meeting_task " +
            " where DATE_FORMAT(start_time,'%Y-%m') = DATE_FORMAT(now(),'%Y-%m') \n" +
            "\tand type_id in (2,5) and status=2 \n" +
            "\tand org_id in (select organization_id from t_organization " +
            " where (org_level LIKE CONCAT('%-',#{orgId},'-%') or organization_id=#{orgId}) \n" +
            "\tand org_type_child in(10280304,10280309,10280314,10280315,10280319))\n" +
            "\tGROUP BY type_id\n" +
            "\tUNION\n" +
            "select '民主生活会' as name,sum(num) as value  from(\n" +
            "select count(distinct t1.owner_id) num\n" +
            "from t_meeting_life a1\n" +
            "join t_organization t1 on a1.org_id=t1.organization_id\n" +
            "join t_party_group t2 on t1.owner_id=t2.org_id\n" +
            "where  a1.status>=3 and t1.status=1 and t2.`status`=1 and \n" +
            "(t1.organization_id=#{orgId} or t1.org_level like concat('%-',#{orgId},'-%')) " +
            "and a1.`years`=date_format(now(),'%Y')\n" +
            "group by  t1.owner_id\n" +
            ")AA" +
            " UNION " +
            "select '组织生活会' as `name` , count(distinct(t1.org_id)) as `value` from t_meeting_org_life t1\n" +
            "join t_organization t2 on t1.org_id=t2.organization_id\n" +
            "where  t1.region_id=19 and (t1.org_id=#{orgId} or t1.org_level like concat('%-',#{orgId},'-%'))" +
            " and t2.org_type_child in(\n" +
            "10280303,\n" +
            "10280304,\n" +
            "10280308,\n" +
            "10280309,\n" +
            "10280311,\n" +
            "10280314,\n" +
            "10280315,\n" +
            "10280318,\n" +
            "10280319) and t2.status=1 \n" +
            "and t1.status>=3 and t1.is_del=0 and t1.`years`=date_format(now(),'%Y')" +
            "\tUNION\n" +
            "SELECT '谈心谈话' as `name`, COUNT(1) as `value`\n" +
            "  FROM t_meeting_talk\n" +
            " WHERE `status` = 1\n" +
            " AND copy_id = 0\n" +
            " AND YEAR(begin_time) = YEAR(CURRENT_DATE())\n" +
            " AND YEAR(end_time) = YEAR(CURRENT_DATE())\n" +
            " AND (org_id = #{orgId} or org_level LIKE CONCAT('%-',#{orgId},'-%'))" +
            "\tUNION\n" +
            "SELECT '党小组会' AS name,COUNT( 1 ) AS value \n" +
            "  FROM\n" +
            "    (\n" +
            "    SELECT\n" +
            "      STATUS,\n" +
            "      t1.org_id \n" +
            "    FROM\n" +
            "      ( SELECT org_id, STATUS FROM t_meeting_task WHERE DATE_FORMAT( start_time, '%Y-%m' ) \n" +
            "\t\t\t= DATE_FORMAT( now(), '%Y-%m' ) AND type_id = 3 ) t1\n" +
            "      INNER JOIN (\n" +
            "      SELECT\n" +
            "          organization_id \n" +
            "        FROM\n" +
            "          t_organization \n" +
            "        WHERE\n" +
            "          ( org_level LIKE CONCAT( '%-', #{orgId}, '-%' ) OR organization_id = #{orgId} ) \n" +
            "          AND org_type_child IN (10280306) \n" +
            "         \n" +
            "      ) t2 ON t1.org_id = t2.organization_id \n" +
            "    ) t3" +
            ") as B ON A.name=B.name\n" +
            "ORDER BY A.order_num" +
            "</script>")
    List<OverviewActivityVo.Series> staOverviewMeetingByCommittee(@Param(value = "startTime") String startTime,
                                                                  @Param(value = "endTime") String endTime,
                                                                  @Param(value = "orgId") Long orgId);


    @Select("<script>" +
            "SELECT concat(A.alias_name,'(',A.period_str,')') as `name`,A.period_str as periodStr,IF(b.`value` IS NULL,0,b.`value`) as `number`,IF(b.total IS NULL,0,b.total) as total, " +
            "b.type as `type` FROM (\n" +
            " SELECT * FROM t_overview_option WHERE type=8 and project_name='overview' and option_type not in(7,8)  \n" +
            ") as A\n" +
            "LEFT JOIN (\n" +
            "\tSELECT type_id as type,`name`, sum(if(status=2,1,0)) as value,COUNT(1) as total FROM\n" +
            "\t t_meeting_task where DATE_FORMAT(start_time,'%Y-%m') <![CDATA[  >= ]]> #{startTime} \n" +
            "\tand DATE_FORMAT(start_time,'%Y-%m') <![CDATA[ <= ]]> #{endTime} and type_id in (1,4) \n" +
            "\tand org_id in (select organization_id from t_organization " +
            " where (org_level LIKE CONCAT('%-',#{orgId},'-%') or organization_id=#{orgId}) \n" +
            "\tand org_type_child in(10280304,10280309,10280314,10280315,10280319))\n" +
            "\tGROUP BY type_id\n" +
            "\tUNION\n" +
            "\tSELECT type_id as type,`name`,  sum(if(status=2,1,0)) as value,COUNT(1) as total from t_meeting_task " +
            " where DATE_FORMAT(start_time,'%Y-%m') = DATE_FORMAT(now(),'%Y-%m') \n" +
            "\tand type_id in (2,5)  \n" +
            "\tand org_id in (select organization_id from t_organization " +
            " where (org_level LIKE CONCAT('%-',#{orgId},'-%') or organization_id=#{orgId}) \n" +
            "\tand org_type_child in(10280304,10280309,10280314,10280315,10280319))\n" +
            "\tGROUP BY type_id\n" +
//            "\tUNION\n" +
//            "select '民主生活会' as name,sum(num) as value  from(\n" +
//            "select count(distinct t1.owner_id) num\n" +
//            "from t_meeting_life a1\n" +
//            "join t_organization t1 on a1.org_id=t1.organization_id\n" +
//            "join t_party_group t2 on t1.owner_id=t2.org_id\n" +
//            "where  a1.status>=3 and t1.status=1 and t2.`status`=1 and \n" +
//            "(t1.organization_id=#{orgId} or t1.org_level like concat('%-',#{orgId},'-%')) " +
//            "and a1.`years`=date_format(now(),'%Y')\n" +
//            "group by  t1.owner_id\n" +
//            ")AA"+
            " UNION " +
            "select 6 as type,'组织生活会' as `name` , count(distinct(t1.org_id)) as `value`,0 as total from t_meeting_org_life t1\n" +
            "join t_organization t2 on t1.org_id=t2.organization_id\n" +
            "where  t1.region_id=19 and (t1.org_id=#{orgId} or t1.org_level like concat('%-',#{orgId},'-%'))" +
            " and t2.org_type_child in(\n" +
            "10280303,\n" +
            "10280304,\n" +
            "10280308,\n" +
            "10280309,\n" +
            "10280311,\n" +
            "10280314,\n" +
            "10280315,\n" +
            "10280318,\n" +
            "10280319) and t2.status=1 \n" +
            "and t1.status>=3 and t1.is_del=0 and t1.`years`=date_format(now(),'%Y')" +
//            "\tUNION\n" +
//            "SELECT '谈心谈话' as `name`, COUNT(1) as `value`\n" +
//            "  FROM t_meeting_talk\n" +
//            " WHERE `status` = 1\n" +
//            " AND copy_id = 0\n" +
//            " AND YEAR(begin_time) = YEAR(CURRENT_DATE())\n" +
//            " AND YEAR(end_time) = YEAR(CURRENT_DATE())\n" +
//            " AND (org_id = #{orgId} or org_level LIKE CONCAT('%-',#{orgId},'-%'))"+
            "\tUNION\n" +
            "SELECT 3 as type,'党小组会' AS name,sum(if(status=2,1,0)) as value,COUNT(1) as total  \n" +
            "  FROM\n" +
            "    (\n" +
            "    SELECT\n" +
            "      STATUS,\n" +
            "      t1.org_id \n" +
            "    FROM\n" +
            "      ( SELECT org_id, STATUS FROM t_meeting_task WHERE DATE_FORMAT( start_time, '%Y-%m' ) \n" +
            "\t\t\t= DATE_FORMAT( now(), '%Y-%m' ) AND type_id = 3 ) t1\n" +
            "      INNER JOIN (\n" +
            "      SELECT\n" +
            "          organization_id \n" +
            "        FROM\n" +
            "          t_organization \n" +
            "        WHERE\n" +
            "          ( org_level LIKE CONCAT( '%-', #{orgId}, '-%' ) OR organization_id = #{orgId} ) \n" +
            "          AND org_type_child IN (10280306) \n" +
            "         \n" +
            "      ) t2 ON t1.org_id = t2.organization_id  \n" +
            "    ) t3" +
            ") as B ON A.name=B.name\n" +
            "ORDER BY A.order_num" +
            "</script>")
    List<OverviewActivityVo.Series> staOverviewMeetingByCommitteeNew1(@Param(value = "startTime") String startTime,
                                                                      @Param(value = "endTime") String endTime,
                                                                      @Param(value = "orgId") Long orgId);

    @Select("SELECT COUNT( 1 ) AS value \n" +
            "  FROM\n" +
            "    (\n" +
            "    SELECT\n" +
            "      STATUS,\n" +
            "      t1.org_id \n" +
            "    FROM\n" +
            "      ( SELECT org_id, STATUS FROM t_meeting_task WHERE DATE_FORMAT( start_time, '%Y-%m' ) \n" +
            "\t\t\t= DATE_FORMAT( now(), '%Y-%m' ) AND type_id = 3 ) t1\n" +
            "      INNER JOIN (\n" +
            "      SELECT\n" +
            "          organization_id \n" +
            "        FROM\n" +
            "          t_organization \n" +
            "        WHERE\n" +
            "          ( org_level LIKE CONCAT( '%-', #{orgId}, '-%' ) OR organization_id = #{orgId} ) \n" +
            "          AND org_type_child IN (10280306) \n" +
            "         \n" +
            "      ) t2 ON t1.org_id = t2.organization_id \n" +
            "    ) a")
    Integer countPartyOrgNum(@Param(value = "startTime") String startTime,
                             @Param(value = "endTime") String endTime,
                             @Param(value = "orgId") Long orgId);

    @Select("<script>" +
            "select IFNULL(sum(case when type_id=1 AND STATUS=2 then 1 else 0 END),'-') finished1\n" +
            "from t_meeting_task where DATE_FORMAT(start_time,'%Y-%m') <![CDATA[ >= ]]> #{startTime}" +
            " and DATE_FORMAT(start_time,'%Y-%m')<![CDATA[ <= ]]> #{endTime} and type_id =1 AND org_id= #{orgId} " +
            "</script>")
    OverviewActivityFinishVo staOverviewMeetingBranch1(@Param(value = "startTime") String startTime,
                                                       @Param(value = "endTime") String endTime,
                                                       @Param(value = "orgId") Long orgId);


    @Select("<script>" +
            "select IFNULL(sum(case when type_id=4 AND STATUS=2 then 1 else 0 END),'-') finished2\n" +
            "from t_meeting_task where DATE_FORMAT(start_time,'%Y-%m') <![CDATA[ >= ]]> #{startTime}" +
            " and DATE_FORMAT(start_time,'%Y-%m')<![CDATA[ <= ]]> #{endTime} and type_id =4 AND org_id= #{orgId}" +
            "</script>")
    OverviewActivityFinishVo staOverviewMeetingBranch2(@Param(value = "startTime") String startTime,
                                                       @Param(value = "endTime") String endTime,
                                                       @Param(value = "orgId") Long orgId);

    @Select("<script>" +
            "select IFNULL(sum(case when type_id=2 AND STATUS=2 then 1 else 0 end),'-') finished3\n" +
            "from t_meeting_task where DATE_FORMAT(start_time,'%Y-%m') = DATE_FORMAT(now(),'%Y-%m') " +
            "and type_id =2 and org_id= #{orgId};" +
            "</script>")
    OverviewActivityFinishVo staOverviewMeetingBranch3(@Param(value = "startTime") String startTime,
                                                       @Param(value = "endTime") String endTime,
                                                       @Param(value = "orgId") Long orgId);

    //党小组
    @Select("<script>" +
            "SELECT IFNULL(sum(case when un>0 then 0 ELSE 1 end),'-') finished4 FROM (\n" +
            "SELECT branchOrgId,SUM(case when STATUS=1 then 1 ELSE 0 END) un FROM (\n" +
            "\tSELECT t2.org_id branchOrgId,STATUS,t1.org_id  FROM (\n" +
            "\tselect org_id,status from t_meeting_task where " +
            " DATE_FORMAT(start_time,'%Y-%m') = DATE_FORMAT(now(),'%Y-%m') and type_id = 3\n" +
            "\t) t1 INNER JOIN (\n" +
            "\tSELECT org_id,link_org_id FROM t_user_org_group where org_id \n" +
            "\tin (select organization_id from t_organization where region_id=19 \n" +
            "\tand (org_level LIKE CONCAT('%-',#{orgId},'-%') or organization_id=#{orgId}) " +
            "  and org_type_child in(10280304,10280309,10280314,10280315,10280319))\n" +
            "\t) t2 ON t1.org_id = t2.link_org_id\n" +
            ") t3 GROUP BY branchOrgId\n" +
            ") temp" +
            "</script>")
    OverviewActivityFinishVo staOverviewMeetingBranch4(@Param(value = "startTime") String startTime,
                                                       @Param(value = "endTime") String endTime,
                                                       @Param(value = "orgId") Long orgId);


    //党小组
    @Select("<script>" +
            "SELECT  round(if(sum(total)=0,0.00,sum(un)/sum(total)),4)*100 as finished4 FROM (\n" +
            "SELECT branchOrgId,SUM(case when STATUS=2 then 1 ELSE 0 END) un,count(0) as total FROM (\n" +
            "\tSELECT t2.org_id branchOrgId,STATUS,t1.org_id  FROM (\n" +
            "\tselect org_id,status from t_meeting_task where " +
            " DATE_FORMAT(start_time,'%Y-%m') = DATE_FORMAT(now(),'%Y-%m') and type_id = 3\n" +
            "\t) t1 INNER JOIN (\n" +
            "\tSELECT org_id,link_org_id FROM t_user_org_group where org_id \n" +
            "\tin (select organization_id from t_organization where region_id=19 \n" +
            "\tand (org_level LIKE CONCAT('%-',#{orgId},'-%') or organization_id=#{orgId}) " +
            "  and org_type_child in(10280304,10280309,10280314,10280315,10280319))\n" +
            "\t) t2 ON t1.org_id = t2.link_org_id\n" +
            ") t3 GROUP BY branchOrgId\n" +
            ") temp" +
            "</script>")
    OverviewActivityFinishVo staOverviewMeetingBranch4New(@Param(value = "startTime") String startTime,
                                                          @Param(value = "endTime") String endTime,
                                                          @Param(value = "orgId") Long orgId);


    @Select("<script>" +
            "select IFNULL(sum(case when type_id=5 AND STATUS=2 then 1 else 0 end),'-') finished5\n" +
            "from t_meeting_task where DATE_FORMAT(start_time,'%Y-%m') = DATE_FORMAT(now(),'%Y-%m') " +
            "and type_id =5 and org_id= #{orgId};" +
            "</script>")
    OverviewActivityFinishVo staOverviewMeetingBranch5(@Param(value = "startTime") String startTime,
                                                       @Param(value = "endTime") String endTime,
                                                       @Param(value = "orgId") Long orgId);


    //民主生活会
    @Select("<script>" +
            "select count(1) from t_meeting_life where status>=3 and is_del=0 " +
            "and `years`=date_format(now(),'%Y') and org_id=#{orgId}" +
            "</script>")
    Integer staOverviewMeetingLifeBranch(@Param(value = "orgId") Long orgId);


    //谈心谈话
    @Select("<script>" +
            "SELECT COUNT(1) as `value`\n" +
            "  FROM t_meeting_talk\n" +
            " WHERE `status` = 1\n" +
            " AND copy_id = 0\n" +
            " AND is_submit = 1\n" +
            " AND YEAR(begin_time) = YEAR(CURRENT_DATE())\n" +
            " AND YEAR(end_time) = YEAR(CURRENT_DATE())\n" +
            " AND (org_id = #{orgId} or org_level LIKE CONCAT('%-',#{orgId},'-%'))" +
            "</script>")
    Integer staOverviewMeetingTalk(@Param(value = "orgId") Long orgId);

    /**
     * 谈心谈话统计，排除了测试组织
     *
     * @param orgId         组织id
     * @param excludeOrgIds 排除组织列表
     *
     * @return 谈心谈话数量
     */
    @Select("<script>" +
            "SELECT COUNT(1) as `value`\n" +
            "  FROM t_meeting_talk\n" +
            " WHERE `status` = 1\n" +
            " AND copy_id = 0\n" +
            " AND is_submit = 1\n" +
            " AND YEAR(begin_time) = YEAR(CURRENT_DATE())\n" +
            " AND YEAR(end_time) = YEAR(CURRENT_DATE())\n" +
            " AND (org_id = #{orgId} or org_level LIKE CONCAT('%-',#{orgId},'-%'))" +
            "<if test=\"excludeOrgIds != null and excludeOrgIds.size != 0 \"> " +
            "<foreach collection=\"excludeOrgIds\" index=\"index\" item=\"item\">\n" +
            "   AND org_id <![CDATA[ <> ]]> #{item} AND org_level not like concat('%-', #{item}, '-%') " +
            "</foreach>" +
            "</if>" +
            "</script>")
    Integer staOverviewMeetingTalkAndExclude(@Param(value = "orgId") Long orgId, @Param("excludeOrgIds") List<Long> excludeOrgIds);

    @Select("select count(1) from t_meeting_org_life where region_id=19 " +
            "and (org_id=#{org_id} or org_level like concat('%-',#{org_id},'-%')) " +
            "and status>=3 and is_del=0 and `years`=date_format(now(),'%Y');")
    Integer staOverviewOrgLifeBranch(@Param(value = "org_id") Long orgId);


    @Select(" SELECT t2.name FROM t_meeting_task t1\n" +
            " join t_organization t2 on t1.org_id=t2.organization_id and t2.status=1\n" +
            "\twhere DATE_FORMAT(t1.start_time,'%Y-%m') >= #{startTime} and DATE_FORMAT(t1.start_time,'%Y-%m') <= #{endTime}" +
            " and t1.type_id=#{type} and t1.status=2 \n" +
            "\tand  (t2.org_level LIKE CONCAT('%-',#{orgId},'-%') or t2.organization_id=#{orgId}) \n" +
            "\tand t2.org_type_child in(10280304,10280309,10280314,10280315,10280319)\n" +
            "\torder by t1.create_time,t1.org_id")
    List<String> queryOrgActivityDetailQuart(@Param("orgId") Long orgId, @Param("type") Integer type,
                                             @Param("startTime") String startTime, @Param("endTime") String endTime);


    @Select(" SELECT t2.name FROM t_meeting_task t1\n" +
            " join t_organization t2 on t1.org_id=t2.organization_id and t2.status=1\n" +
            "\twhere DATE_FORMAT(t1.start_time,'%Y-%m') =#{dateMonth}" +
            " and t1.type_id=#{type} and t1.status=2 \n" +
            "\tand  (t2.org_level LIKE CONCAT('%-',#{orgId},'-%') or t2.organization_id=#{orgId}) \n" +
            "\tand t2.org_type_child in(10280304,10280309,10280314,10280315,10280319)\n" +
            "\torder by t1.create_time,t1.org_id")
    List<String> queryOrgActivityDetailMonth(@Param("orgId") Long oid, @Param("type") Integer type, @Param("dateMonth") String dateMonth);


    @Select(" SELECT t2.name FROM\n" +
            " ( SELECT org_id, STATUS FROM t_meeting_task WHERE DATE_FORMAT( start_time, '%Y-%m' )=#{dateMonth} \n" +
            " AND type_id = #{type} and status=2) t1\n" +
            "                  INNER JOIN (\n" +
            "                  SELECT\n" +
            "                      organization_id,name \n" +
            "                    FROM\n" +
            "                      t_organization \n" +
            "                    WHERE\n" +
            "                      ( org_level LIKE CONCAT( '%-', #{orgId}, '-%' ) OR organization_id =  #{orgId} ) \n" +
            "                      AND org_type_child IN (10280306) \n" +
            "                  ) t2 ON t1.org_id = t2.organization_id  ")
    List<String> queryOrgActivityDetailMonth3(@Param("orgId") Long finalOrgId, @Param("type") Integer type, @Param("dateMonth") String dateMonth);

    @Select("    select distinct(t2.name) as `value` from t_meeting_org_life t1\n" +
            "            join t_organization t2 on t1.org_id=t2.organization_id\n" +
            "            where  t1.region_id=19 and (t1.org_id=#{orgId} or t1.org_level like concat('%-',#{orgId},'-%')) \n" +
            "             and t2.org_type_child in(\n" +
            "            10280303,\n" +
            "            10280304,\n" +
            "            10280308,\n" +
            "            10280309,\n" +
            "            10280311,\n" +
            "            10280314,\n" +
            "            10280315,\n" +
            "            10280318,\n" +
            "            10280319) and t2.status=1 \n" +
            "            and t1.status>=3 and t1.is_del=0 and t1.`years`=#{year}\n" +
            "  order by t1.create_time,t1.org_id")
    List<String> queryOrgLifeYear(@Param("orgId") Long finalOrgId, @Param("type") Integer type, @Param("year") Integer year);

    @Select(" SELECT t2.name as orgName,if(t1.status=1,'未完成','完成') as typeName FROM t_meeting_task t1\n" +
            " join t_organization t2 on t1.org_id=t2.organization_id and t2.status=1\n" +
            "\twhere DATE_FORMAT(t1.start_time,'%Y-%m') >= #{startTime} and DATE_FORMAT(t1.start_time,'%Y-%m') <= #{endTime}" +
            " and t1.type_id=#{type} \n" +
            "\tand  (t2.org_level LIKE CONCAT('%-',#{orgId},'-%') or t2.organization_id=#{orgId}) \n" +
            "\tand t2.org_type_child in(10280304,10280309,10280314,10280315,10280319)\n" +
            "\torder by t1.create_time,t1.org_id")
    List<MeetingForm> queryOrgActivityDetailQuartAll(@Param("orgId") Long orgId, @Param("type") Integer type,
                                                     @Param("startTime") String startTime, @Param("endTime") String endTime);


    @Select(" SELECT t2.name as orgName,if(t1.status=1,'未完成','完成') as typeName FROM t_meeting_task t1\n" +
            " join t_organization t2 on t1.org_id=t2.organization_id and t2.status=1\n" +
            "\twhere DATE_FORMAT(t1.start_time,'%Y-%m') =#{dateMonth}" +
            " and t1.type_id=#{type}  \n" +
            "\tand  (t2.org_level LIKE CONCAT('%-',#{orgId},'-%') or t2.organization_id=#{orgId}) \n" +
            "\tand t2.org_type_child in(10280304,10280309,10280314,10280315,10280319)\n" +
            "\torder by t1.create_time,t1.org_id")
    List<MeetingForm> queryOrgActivityDetailMonthAll(@Param("orgId") Long oid, @Param("type") Integer type, @Param("dateMonth") String dateMonth);


    @Select(" SELECT t2.name as orgName,if(t1.status=1,'未完成','完成') as typeName FROM\n" +
            " ( SELECT org_id, STATUS FROM t_meeting_task WHERE DATE_FORMAT( start_time, '%Y-%m' )=#{dateMonth} \n" +
            " AND type_id = #{type} ) t1\n" +
            "                  INNER JOIN (\n" +
            "                  SELECT\n" +
            "                      organization_id,name \n" +
            "                    FROM\n" +
            "                      t_organization \n" +
            "                    WHERE\n" +
            "                      ( org_level LIKE CONCAT( '%-', #{orgId}, '-%' ) OR organization_id =  #{orgId} ) \n" +
            "                      AND org_type_child IN (10280306) \n" +
            "                  ) t2 ON t1.org_id = t2.organization_id  ")
    List<MeetingForm> queryOrgActivityDetailMonth3All(@Param("orgId") Long finalOrgId, @Param("type") Integer type, @Param("dateMonth") String dateMonth);

    @Select("    select t2.name as orgName,if(t1.status>=3,'完成','未完成') as typeName from t_meeting_org_life t1\n" +
            "            join t_organization t2 on t1.org_id=t2.organization_id\n" +
            "            where  t1.region_id=#{regionId} and (t1.org_id=#{orgId} or t1.org_level like concat('%-',#{orgId},'-%')) \n" +
            "             and t2.org_type_child in(\n" +
            "            10280303,\n" +
            "            10280304,\n" +
            "            10280308,\n" +
            "            10280309,\n" +
            "            10280311,\n" +
            "            10280314,\n" +
            "            10280315,\n" +
            "            10280318,\n" +
            "            10280319) and t2.status=1 \n" +
            "            and t1.is_del=0 and t1.`years`=#{year}\n" +
            "  order by t1.create_time,t1.org_id")
    List<MeetingForm> queryOrgLifeYearAll(@Param("orgId") Long finalOrgId, @Param("type") Integer type,
                                          @Param("year") Integer year, @Param("regionId") Long regionId);


    @Select("<script>SELECT\n" +
            "            type_id AS type,\n" +
            "            `name`,\n" +
            "            sum(\n" +
            "            IF\n" +
            "            ( STATUS = 2, 1, 0 )) AS `value`,\n" +
            "            COUNT( 1 ) AS counts ,\n" +
            "\t\t\tSUM(meeting_num) total\n" +
            "            FROM\n" +
            "            t_meeting_task\n" +
            "            WHERE\n" +
            "            DATE_FORMAT( start_time, '%Y-%m' ) <![CDATA[  >= ]]> #{startTime} \n" +
            "            AND DATE_FORMAT( start_time, '%Y-%m' ) <![CDATA[  <= ]]> #{endTime} \n" +
            "            AND type_id IN ( 1, 4, 25) \n" +
            "            AND org_id IN (\n" +
            "            SELECT\n" +
            "            organization_id \n" +
            "            FROM\n" +
            "            t_organization \n" +
            "            WHERE\n" +
            "            ( org_level LIKE CONCAT( '%-', #{orgId}, '-%' ) OR organization_id = #{orgId} ) \n" +
            "            AND org_type_child IN ( 10280304, 10280309, 10280314, 10280315, 10280319 )) \n" +
            "            GROUP BY\n" +
            "            type_id UNION\n" +
            "            SELECT\n" +
            "           type_id AS type,\n" +
            "            `name`,\n" +
            "            sum(\n" +
            "            IF\n" +
            "            ( STATUS = 2, 1, 0 )) AS `value`,\n" +
            "            COUNT( 1 ) AS counts ,\n" +
            "\t\t\tSUM(meeting_num) total\n" +
            "            FROM\n" +
            "            t_meeting_task \n" +
            "            WHERE\n" +
            "            DATE_FORMAT( start_time, '%Y-%m' )= DATE_FORMAT( now(), '%Y-%m' ) \n" +
            "            AND type_id IN ( 2, 5 ) \n" +
            "            AND org_id IN (\n" +
            "            SELECT\n" +
            "            organization_id \n" +
            "            FROM\n" +
            "            t_organization \n" +
            "            WHERE\n" +
            "            ( org_level LIKE CONCAT( '%-', #{orgId}, '-%' ) OR organization_id = #{orgId} ) \n" +
            "            AND org_type_child IN ( 10280304, 10280309, 10280314, 10280315, 10280319 )) \n" +
            "            GROUP BY\n" +
            "            type_id UNION\n" +
            "            SELECT\n" +
            "            3 AS type,\n" +
            "            '党小组会' AS NAME,\n" +
            "            sum(\n" +
            "            IF\n" +
            "            ( STATUS = 2, 1, 0 )) AS `value`,\n" +
            "            COUNT( 1 ) AS counts ,\n" +
            "\t\t\tSUM(meeting_num) total\n" +
            "            FROM\n" +
            "            (\n" +
            "            SELECT STATUS\n" +
            "            ,\n" +
            "            t1.org_id ,\n" +
            "\t\t\tmeeting_num\n" +
            "            FROM\n" +
            "            (\n" +
            "            SELECT\n" +
            "            org_id,\n" +
            "            STATUS ,\n" +
            "\t\t\tmeeting_num\n" +
            "            FROM\n" +
            "            t_meeting_task \n" +
            "            WHERE\n" +
            "            DATE_FORMAT( start_time, '%Y-%m' )= DATE_FORMAT( now(), '%Y-%m' ) \n" +
            "            AND type_id = 3 \n" +
            "            ) t1\n" +
            "            INNER JOIN (\n" +
            "            SELECT\n" +
            "            organization_id \n" +
            "            FROM\n" +
            "            t_organization \n" +
            "            WHERE\n" +
            "            ( org_level LIKE CONCAT( '%-', #{orgId}, '-%' ) OR organization_id = #{orgId} ) \n" +
            "            AND org_type_child IN ( 10280306 ) \n" +
            "            ) t2 ON t1.org_id = t2.organization_id \n" +
            "            ) t3</script>")
    List<DataCockpitOverviewActivityVo> getMeetingNumByOrgId(@Param(value = "startTime") String startTime,
                                                             @Param(value = "endTime") String endTime,
                                                             @Param(value = "orgId") Long orgId);

    @Select("<script>SELECT\n" +
            "\ttype_id AS type,\n" +
            "\t`name`,\n" +
            "\tsum(\n" +
            "\tIF\n" +
            "\t( STATUS = 2, 1, 0 )) AS `value`,\n" +
            "\tCOUNT( 1 ) AS counts,\n" +
            "\tSUM(meeting_num)  total\n" +
            "FROM\n" +
            "\tt_meeting_task \n" +
            "WHERE\n" +
            "\tDATE_FORMAT( start_time, '%Y' ) = #{year} \n" +
            "\tAND type_id IN ( 1, 2, 4, 5, 25 ) \n" +
            "\tAND org_id IN (\n" +
            "\tSELECT\n" +
            "\t\torganization_id \n" +
            "\tFROM\n" +
            "\t\tt_organization \n" +
            "\tWHERE\n" +
            "\t\t( org_level LIKE CONCAT( '%-', #{orgId}, '-%' ) OR organization_id = #{orgId} ) \n" +
            "\tAND org_type_child IN ( 10280304, 10280309, 10280314, 10280315, 10280319 )) \n" +
            "GROUP BY\n" +
            "\ttype_id UNION\n" +
            "SELECT\n" +
            "\t3 AS type,\n" +
            "\t'党小组会' AS NAME,\n" +
            "\tsum(\n" +
            "\tIF\n" +
            "\t( STATUS = 2, 1, 0 )) AS `value`,\n" +
            "\tCOUNT( 1 ) AS counts ,\n" +
            "\tSUM(meeting_num) total\n" +
            "FROM\n" +
            "\t(\n" +
            "\tSELECT STATUS\n" +
            "\t\t,\n" +
            "\t\tt1.org_id ,\n" +
            "\t\tmeeting_num\n" +
            "\tFROM\n" +
            "\t\t( SELECT org_id, STATUS,meeting_num FROM t_meeting_task WHERE DATE_FORMAT( start_time, '%Y' )= #{year} AND type_id = 3 ) t1\n" +
            "\t\tINNER JOIN (\n" +
            "\t\tSELECT\n" +
            "\t\t\torganization_id \n" +
            "\t\tFROM\n" +
            "\t\t\tt_organization \n" +
            "\t\tWHERE\n" +
            "\t\t\t( org_level LIKE CONCAT( '%-', #{orgId}, '-%' ) OR organization_id = #{orgId} ) \n" +
            "\t\t\tAND org_type_child IN ( 10280306 ) \n" +
            "\t\t) t2 ON t1.org_id = t2.organization_id \n" +
            "\t) t3</script>")
    List<DataCockpitOverviewActivityVo> getMeetingYearTotalNumByOrgId(@Param(value = "year") String year,
                                                             @Param(value = "orgId") Long orgId);

    @Select("<script>" +
            "SELECT\n" +
            "\tSUM( CASE WHEN STATUS = 2 THEN 1 ELSE 0 END ) finished4 \n" +
            "FROM\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\tt2.org_id branchOrgId,\n" +
            "\t\tSTATUS,\n" +
            "\t\tt1.org_id \n" +
            "\tFROM\n" +
            "\t\t( SELECT org_id, STATUS FROM t_meeting_task WHERE DATE_FORMAT( start_time, '%Y-%m' ) = DATE_FORMAT( now(), '%Y-%m' ) AND type_id = 3 ) t1\n" +
            "\t\tINNER JOIN ( SELECT org_id, link_org_id FROM t_user_org_group WHERE org_id = #{orgId} ) t2 ON t1.org_id = t2.link_org_id \n" +
            "\t) t3 \n" +
            "GROUP BY\n" +
            "\tbranchOrgId" +
            "</script>")
    OverviewActivityFinishVo staDataCockpitFinished4(@Param(value = "startTime") String startTime,
                                                       @Param(value = "endTime") String endTime,
                                                       @Param(value = "orgId") Long orgId);

}
