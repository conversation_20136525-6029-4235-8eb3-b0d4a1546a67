package com.goodsogood.ows.mapper.meeting;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.meeting.MeetingOrgCommendPenalizeEntity;
import com.goodsogood.ows.model.db.meeting.UserCommendPenalizeEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @describe 组织奖惩sql
 * @date 2019-12-30
 */
@Repository
@Mapper
public interface MeetingOrgCommendPenalizeMapper extends MyMapper<MeetingOrgCommendPenalizeEntity> {

    /**
     * 查询人员的奖惩信息
     * @param orgIds
     * @param year
     * @param month
     * @return
     */
    @Select("<script>" +
            "SELECT meeting_org_commend_penalize_id meetingOrgCommendPenalizeId, org_id orgId, org_name orgName, `level`, `name`\n" +
            "FROM t_meeting_org_commend_penalize\n" +
            "WHERE type = 1 \n" +
            "  AND org_id in " +
            "<foreach item=\"orgId\" collection=\"orgIds\" open=\"(\" separator=\",\" close=\")\"> " +
            "#{orgId} " +
            "</foreach>" +
            "  AND approval_status = 2 \n" +
            "  AND `status` = 1 \n" +
            "  AND YEAR (update_time) = #{year}\n" +
            "  AND MONTH(update_time) = #{month}\n" +
            "</script>")
    List<MeetingOrgCommendPenalizeEntity> getCommendPenalizeByOrg(@Param("orgIds") List<Long> orgIds,
                                                                 @Param("year") Integer year,
                                                                 @Param("month") Integer month);
}
