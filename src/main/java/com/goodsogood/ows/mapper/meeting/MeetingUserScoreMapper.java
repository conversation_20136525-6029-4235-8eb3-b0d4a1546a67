package com.goodsogood.ows.mapper.meeting;

import com.goodsogood.ows.model.vo.ScoreResult;
import lombok.Builder;
import lombok.Data;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 计算评分
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface MeetingUserScoreMapper {

    /**
     * 奖惩得分
     *
     * @param form 条件
     * @return 分值
     */
    @Select(
            "<script> "
                    + "<if test =\"form != null and form.size() > 0 \">"
                    + "  <foreach collection=\"form\" index=\"index\" item=\"item\" open=\"\" separator=\" UNION ALL \" close=\"\">\n"
                    + " SELECT concat(user_id,'-',#{item.currentMonth}) as k,IF(score IS NULL,0,score) as score FROM"
                    + " ("
                    + "    SELECT user_id, SUM(CASE type"
                    + "      WHEN 1 THEN #{item.commendScore}"
                    + "      WHEN 2 THEN #{item.penalizeScore}"
                    + "      ELSE 0"
                    + "      END "
                    + "    ) AS score FROM t_meeting_user_commend_penalize"
                    + "    WHERE `status`=1 "
                    + "       <if test =\"item.userIdSet != null and item.userIdSet.size() > 0 \">"
                    + "        and user_id in "
                    + "         <foreach collection=\"item.userIdSet\" index=\"iindex\" item=\"iitem\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "                    #{iitem}\n"
                    + "         </foreach>"
                    + "       </if> "
                    + "    AND region_id=#{item.regionId}"
                    + "    AND DATE_FORMAT(effective_time,'%Y-%m')=#{item.currentMonth}"
                    + "       GROUP BY user_id "
                    + " )t"
                    + "  </foreach>"
                    + "</if> "
                    + " </script>")
    List<ScoreResult> userCommendPenalizeScore(@Param("form") List<UserCommendPenalizeScoreForm> form);

    /**
     * 用户参与会议得分
     *
     * @param form 查询添加
     * @return 获取分值
     */
    @Select(
            "<script> "
//                    "SELECT if(SUM(if(total>=#{min},1,0))&gt;=#{typeNum},#{score},#{deductScore}) AS score FROM"
//                    + "("
                    + "  SELECT "
                    + "     b.type_id typeId,c.sign_status signStatus,c.meeting_id meetingId "
                    + "  FROM t_meeting a,t_meeting_type b,t_meeting_user c"
                    + "  WHERE"
                    + "  a.meeting_id = b.meeting_id AND a.meeting_id = c.meeting_id"
                    + "  AND a.is_del = 0 AND a.`status` IN (${status})"
//          + "  AND c.sign_status IN (${signStatus})"
                    + "  AND c.user_id=#{userId}"
                    + "  AND a.region_id=#{regionId}"
                    + "  AND ("
                    + "       (  b.type_id IN (${monthTypeIds})"
                    + "          AND DATE_FORMAT(a.start_time,'%Y-%m') = #{currentMonth}"
                    + "        ) "
                    + "       <if test=\"lastMonthOfQuarter\">"
                    + "        OR  "
                    + "       ( b.type_id IN (${quarterTypeIds})"
                    + "         AND DATE_FORMAT(a.start_time,'%Y-%m')   &gt;=   #{startMonthOfQuarter}"
                    + "         AND DATE_FORMAT(a.start_time,'%Y-%m')   &lt;=  #{currentMonth}"
                    + "        ) "
                    + "       </if>"
                    + "   )"
//                    + "  GROUP BY b.type_id"
//                    + ")t"
                    + " </script>")
    List<UserJoinMeetingRecord> userMeetingScore(UserMeetingScoreForm form);

    /**
     * 双重组织生活 机关单位领导干部
     *
     * @param form 查询添加
     * @return 获取分值
     */
    @Select(
            "<script>SELECT if(SUM(if(ctag=0,1,0))&gt;=#{min},#{score},#{deductScore}) AS score FROM t_meeting a,"
                    + "  (SELECT user_id,meeting_id,SUM(IF( sign_status IN ( ${signStatus} ), 0,-1 ) )AS ctag "
                    + "     FROM"
                    + "     t_meeting_user "
                    + "     WHERE"
                    + "     user_id = #{userId}"
                    + "     GROUP BY"
                    + "     user_id,"
                    + "     meeting_id) c"
                    + "  WHERE"
                    + "  a.meeting_id = c.meeting_id"
                    + "  AND a.is_del=0 AND a.`status` IN (${status})"
                    + "  AND a.region_id=#{regionId}"
                    + "  AND DATE_FORMAT(a.start_time,'%Y-%m')   &gt;=   #{startMonthOfQuarter}"
                    + "  AND DATE_FORMAT(a.start_time,'%Y-%m')   &lt;=  #{currentMonth}"
                    + " </script>")
    Double userDualLifeScore(UserMeetingScoreForm form);

    /**
     * 奖惩得分查询条件
     */
    @Data
    @Builder
    class UserCommendPenalizeScoreForm {
        /**
         * 奖励分值
         */
        private Double commendScore;
        /**
         * 惩罚扣分值
         */
        private Double penalizeScore;

        private Long regionId;
        private Set<Long> userIdSet;
        // 当前月份 YYYY-MM
        private String currentMonth;
    }

    /**
     * 用户基本得分查询条件
     */
    @Data
    @Builder
    class UserMeetingScoreForm {
        /**
         * 最少次数
         */
        private int min;
        /**
         * 分值
         */
        private Double score;
        /**
         * 扣分值
         */
        private Double deductScore;

        private Long userId;
        private Long regionId;
        // 年份
        private Integer year;
        private String currentMonth;
        // 会议状态 7,8,10,11,12,13,14
        private String status;
        // 签到状态
        private String signStatus;
        // 是否是季度最后一月
        private Boolean lastMonthOfQuarter;
        // 季度开始月份 YYYY-MM
        private String startMonthOfQuarter;
        /**
         * 类型的typeId 逗号分割
         */
        private String typeIds;
        /**
         * 需要完成的会议类型数量
         */
        private Integer typeNum;
        /**
         * 月度类型的typeId 逗号分割
         */
        private String monthTypeIds;
        /**
         * 季度类型的typeId 逗号分割
         */
        private String quarterTypeIds;
    }

    @Data
     class UserJoinMeetingRecord{

        private Long typeId;

        private Integer signStatus;

        private Long meetingId;

    }
}
