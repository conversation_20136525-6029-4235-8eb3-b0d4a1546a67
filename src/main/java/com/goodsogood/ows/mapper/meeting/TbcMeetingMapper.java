package com.goodsogood.ows.mapper.meeting;

import com.goodsogood.ows.model.vo.tbc.OrganLifeUserCommentForm;
import com.goodsogood.ows.model.vo.tbc.OrganizationLifeForm;
import com.goodsogood.ows.model.vo.tbc.TbcCommendPenalizeDbForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ruoyu
 * @date : 2020/12/28
 */
@Repository
@Mapper
public interface TbcMeetingMapper {

//    /**
//     * 联系领导参与次数
//     *
//     * @param regionId
//     * @param userIds
//     * @return
//     */
//    @Select("SELECT mc.user_id userId,\n" +
//            "       m.meeting_id meetingId,\n" +
//            "       count(0)                 num\n" +
//            "FROM t_meeting m,\n" +
//            "     t_meeting_type mt,\n" +
//            "     t_meeting_contact_leader mc,\n" +
//            "     (SELECT MAKEDATE(YEAR(CURDATE()), 1) + INTERVAL QUARTER(CURDATE()) QUARTER - INTERVAL 1 QUARTER AS QUARTER_START,\n" +
//            "             LAST_DAY(MAKEDATE(EXTRACT(YEAR FROM CURDATE()), 1) +\n" +
//            "                      INTERVAL QUARTER(CURDATE()) * 3 - 1 MONTH)                                     AS QUARTER_END\n" +
//            "     ) q\n" +
//            "WHERE m.meeting_id = mt.meeting_id\n" +
//            "  AND m.meeting_id = mc.meeting_id\n" +
//            "  AND m.is_del = 0\n" +
//            "  AND m.sel_contact_leaders = 1\n" +
//            "  AND mc.is_del = 0\n" +
//            "  AND mc.user_id IN (${userIds})\n" +
//            "  AND m.start_time <= q.QUARTER_END\n" +
//            "  AND m.start_time >= q.QUARTER_START\n" +
//            "  AND m.`status` IN (7, 8, 10, 11, 12, 13, 14)\n" +
//            "  AND mt.type_id IN (60, 61, 62, 63, 64)\n" +
//            "  AND m.region_id = #{regionId}\n" +
//            "GROUP BY mc.user_id, m.meeting_id")
//    List<OrganizationLifeForm.OrganizationLifeLeaderMeetingActivityForm> meetingContactLeaderJoinNum(
//            @Param("regionId") Long regionId,
//            @Param("userIds") String userIds
//    );

    /**
     * 领导参与次数
     *
     * @param regionId
     * @param userIds
     * @return
     */
    @Select("<script> " +
            "SELECT a.user_id userId, sum(a.num) num\n" +
            "FROM (" +
            "SELECT a.user_id,\n" +
            "       a.meeting_id,\n" +
            "       SUM(a.total) num\n" +
            "FROM (\n" +
            "         SELECT a.user_id,\n" +
            "                a.meeting_id,\n" +
            "                IF(a.sign_status = 1, b.total, 0) total\n" +
            "         FROM (\n" +
            "                  SELECT a.user_id,\n" +
            "                         a.meeting_id,\n" +
            "                         IF(mc.meeting_contact_leader_id IS NOT NULL, 1,\n" +
            "                            IF((a.sign_status LIKE '%2%' OR a.sign_status LIKE '%3%' OR a.sign_status LIKE '%4%' OR\n" +
            "                                a.sign_status LIKE '%5%'), 0, 1)\n" +
            "                             ) sign_status\n" +
            "                  FROM (\n" +
            "                           SELECT mu.user_id,\n" +
            "                                  m.meeting_id,\n" +
            "                                  m.sel_contact_leaders,\n" +
            "                                  GROUP_CONCAT(mu.sign_status) sign_status\n" +
            "                           FROM t_meeting m,\n" +
            "                                t_meeting_type ty,\n" +
            "                                t_meeting_user mu,\n" +
            "                                (\n" +
            "                                    SELECT MAKEDATE(YEAR(CURDATE()), 1) +\n" +
            "                                           INTERVAL QUARTER(CURDATE()) - 1 QUARTER             AS QUARTER_START,\n" +
            "                                           LAST_DAY(MAKEDATE(YEAR(CURDATE()), 1) +\n" +
            "                                                    INTERVAL QUARTER(CURDATE()) * 3 - 1 MONTH) AS QUARTER_END\n" +
            "                                ) q\n" +
            "                           WHERE m.meeting_id = mu.meeting_id\n" +
            "                             AND m.meeting_id = ty.meeting_id\n" +
            "                             AND m.is_del = 0\n" +
            "                             AND m.p_org_id = #{pOrgId} \n" +
            "                             AND m.`status` IN (7, 8, 10, 11, 12, 13, 14)\n" +
            "                             AND ty.type_id in (60, 61, 62, 63, 64)\n" +
            "                    AND DATE_FORMAT(m.start_time, '%Y-%m-%d') <![CDATA[ >= ]]> q.QUARTER_START\n" +
            "                    AND DATE_FORMAT(m.start_time, '%Y-%m-%d') <![CDATA[ <= ]]> q.QUARTER_END\n" +
            "                             AND m.region_id = #{regionId}\n" +
            "                             AND mu.user_id IN (${userIds})\n" +
            "                           GROUP BY mu.user_id, m.meeting_id\n" +
            "                       ) a\n" +
            "                           LEFT JOIN t_meeting_contact_leader mc ON a.meeting_id = mc.meeting_id\n" +
            "                      AND a.user_id = mc.user_id\n" +
            "                      AND mc.is_del = 0\n" +
            "                      AND a.sel_contact_leaders = 1\n" +
            "              ) a\n" +
            "                  LEFT JOIN (\n" +
            "             SELECT m.meeting_id,\n" +
            "                    COUNT(0) total\n" +
            "             FROM t_meeting m,\n" +
            "                  t_meeting_type ty,\n" +
            "                  (\n" +
            "                      SELECT MAKEDATE(YEAR(CURDATE()), 1) +\n" +
            "                             INTERVAL QUARTER(CURDATE()) - 1 QUARTER             AS QUARTER_START,\n" +
            "                             LAST_DAY(MAKEDATE(YEAR(CURDATE()), 1) +\n" +
            "                                      INTERVAL QUARTER(CURDATE()) * 3 - 1 MONTH) AS QUARTER_END\n" +
            "                  ) q\n" +
            "             WHERE m.meeting_id = ty.meeting_id\n" +
            "               AND m.is_del = 0\n" +
            "               AND m.`status` IN (7, 8, 10, 11, 12, 13, 14)\n" +
            "               AND m.p_org_id = #{pOrgId} \n" +
            "               AND ty.type_id in (60, 61, 62, 63, 64)\n" +
            "                    AND DATE_FORMAT(m.start_time, '%Y-%m-%d') <![CDATA[ >= ]]> q.QUARTER_START\n" +
            "                    AND DATE_FORMAT(m.start_time, '%Y-%m-%d') <![CDATA[ <= ]]> q.QUARTER_END\n" +
            "               AND m.region_id = 3\n" +
            "             GROUP BY m.meeting_id\n" +
            "         ) b ON a.meeting_id = b.meeting_id\n" +
            "     ) a\n" +
            "GROUP BY a.user_id" +
            " UNION " +
            " SELECT mc.user_id,\n" +
            "       m.meeting_id,\n" +
            "       count(0) num\n" +
            "FROM t_meeting m,\n" +
            "     t_meeting_type mt,\n" +
            "     t_meeting_contact_leader mc,\n" +
            "     (SELECT MAKEDATE(YEAR(CURDATE()), 1) + INTERVAL QUARTER(CURDATE()) QUARTER - INTERVAL 1 QUARTER AS QUARTER_START,\n" +
            "             LAST_DAY(MAKEDATE(EXTRACT(YEAR FROM CURDATE()), 1) +\n" +
            "                      INTERVAL QUARTER(CURDATE()) * 3 - 1 MONTH)                                     AS QUARTER_END\n" +
            "     ) q\n" +
            "WHERE m.meeting_id = mt.meeting_id\n" +
            "  AND m.meeting_id = mc.meeting_id\n" +
            "  AND m.p_org_id = #{pOrgId} \n" +
            "  AND m.is_del = 0\n" +
            "  AND m.sel_contact_leaders = 1\n" +
            "  AND mc.is_del = 0\n" +
            "  AND mc.user_id IN (81254, 65332, 65101, 65867, 81431, 152877)\n" +
            "                    AND DATE_FORMAT(m.start_time, '%Y-%m-%d') <![CDATA[ >= ]]> q.QUARTER_START\n" +
            "                    AND DATE_FORMAT(m.start_time, '%Y-%m-%d') <![CDATA[ <= ]]> q.QUARTER_END\n" +
            "  AND m.`status` IN (7, 8, 10, 11, 12, 13, 14)\n" +
            "  AND mt.type_id IN (60, 61, 62, 63, 64)\n" +
            "  AND m.region_id = #{regionId} \n" +
            "GROUP BY mc.user_id, m.meeting_id " +
            "     ) a\n" +
            "GROUP BY a.user_id" +
            " </script> ")
    List<OrganizationLifeForm.OrganizationLifeLeaderMeetingActivityForm> meetingJoinNum(
            @Param("pOrgId") Long pOrgId,
            @Param("regionId") Long regionId,
            @Param("userIds") String userIds
    );

    @Select("select case uc.rating \n" +
            "when 1 then '优秀'\n" +
            "when 2 then '合格'\n" +
            "when 3 then '基本合格'\n" +
            "when 4 then '不合格'\n" +
            "end as name,count(1) nums\n" +
            "from t_meeting_user_comment uc\n" +
            "where (uc.org_id=#{orgId} or uc.org_level like concat('%-',#{orgId},'-%')) and uc.review_year=#{lastYear} and uc.rating in (1,2,3,4)" +
            " and uc.status=1 " +
            "group by uc.rating")
    List<OrganizationLifeForm.OrganizationLifeCommentForm> getOrgUserComment(@Param("orgId") Long orgId, @Param("lastYear") Integer lastYear);

    @Select("select case dr.rating \n" +
            "            when 1 then '好'\n" +
            "            when 2 then '较好'\n" +
            "            when 3 then '一般'\n" +
            "            when 4 then '差'\n" +
            "            end as name,sum(1) nums\n" +
            "from t_meeting_org_debrief_review dr\n" +
            "where dr.review_year=#{lastYear} and dr.status=1 and (dr.org_id=#{orgId} or dr.org_level like '%-${orgId}-%')\n" +
            "group by dr.rating")
    List<OrganizationLifeForm.OrganizationLifeCommentForm> getOrgComment(@Param("orgId") Long orgId, @Param("lastYear") Integer lastYear);

    @Select("SELECT count(*) total\n" +
            "FROM t_meeting m,\n" +
            "     t_meeting_type mt\n" +
            "WHERE m.is_del = 0\n" +
            "  AND YEAR(m.start_time) = YEAR(NOW())\n" +
            "  AND m.meeting_id = mt.meeting_id\n" +
            "  AND m.`status` IN (7, 8, 10, 11, 12, 13, 14)\n" +
            "  AND mt.type_id IN (60, 61, 62, 63, 64)\n" +
            "  AND m.p_org_id = #{pOrgId} \n" +
            "  AND m.org_id IN (\n" +
            "    SELECT organization_id\n" +
            "    FROM t_organization\n" +
            "    WHERE status = 1\n" +
            "      AND (org_level LIKE '%-${orgId}-%' OR organization_id = #{orgId})\n" +
            "      AND region_id = #{regionId}\n" +
            "  )\n" +
            "  AND m.region_id = #{regionId}")
    Integer getMeetingActivityTotal(@Param("pOrgId") Long pOrgId, @Param("orgId") Long orgId, @Param("regionId") Long regionId);

    @Select("SELECT a.organization_id orgId,\n" +
            "       a.name orgName,\n" +
            "       if(a.status not like '%1%', 1, 2) isDone\n" +
            "FROM (\n" +
            "         SELECT a.organization_id,\n" +
            "                a.`name`,\n" +
            "                GROUP_CONCAT(a.status) status\n" +
            "         FROM (\n" +
            "                  SELECT ot.organization_id,\n" +
            "                         ot.short_name name,\n" +
            "                         IF(mt.type_id IN (60, 63) AND MONTH(NOW()) NOT IN (3, 6, 9, 12), 2, mt.status) status\n" +
            "                  FROM t_meeting_task mt\n" +
            "                           LEFT JOIN t_organization ot ON mt.org_id = ot.organization_id,\n" +
            "                       t_meeting_plan mp,\n" +
            "                       (SELECT MAKEDATE(YEAR(CURDATE()), 1) + INTERVAL QUARTER(CURDATE()) - 1 QUARTER AS QUARTER_START,\n" +
            "                               LAST_DAY(MAKEDATE(YEAR(CURDATE()), 1) +\n" +
            "                                        INTERVAL QUARTER(CURDATE()) * 3 - 1 MONTH)                    AS QUARTER_END\n" +
            "                       ) q\n" +
            "                  WHERE mt.meeting_plan_id = mp.meeting_plan_id\n" +
            "                    AND mp.is_del = 0\n" +
            "                    AND mp.is_execute = 1\n" +
            "                    AND mt.p_org_id = #{pOrgId} \n" +
            "                    AND (\n" +
            "                          (\n" +
            "                                  mt.type_id IN (60, 63)\n" +
            "                                  AND DATE_FORMAT(mt.start_time, '%Y-%m-%d') <= q.QUARTER_END\n" +
            "                                  AND DATE_FORMAT(mt.end_time, '%Y-%m-%d') >= q.QUARTER_START\n" +
            "                              )\n" +
            "                          OR (\n" +
            "                                  mt.type_id IN (61, 62)\n" +
            "                                  AND DATE_FORMAT(mt.start_time, '%Y-%m') <= DATE_FORMAT(NOW(), '%Y-%m') AND\n" +
            "                                  DATE_FORMAT(mt.end_time, '%Y-%m') >= DATE_FORMAT(NOW(), '%Y-%m')\n" +
            "                              )\n" +
            "                      )\n" +
            "                    AND ot.status = 1\n" +
            "                    AND (ot.org_level LIKE '%-${orgId}-%' OR ot.organization_id = #{orgId})\n" +
            "                    AND ot.region_id = #{regionId}\n" +
            "                    AND mp.region_id = #{regionId}\n" +
            "              ) a\n" +
            "         GROUP BY a.organization_id\n" +
            "     ) a order by a.organization_id ")
    List<OrganizationLifeForm.OrganizationLifeCenterMeetingActivityForm> getCenterMeetingActivityIsDone(@Param("pOrgId") Long pOrgId, @Param("orgId") Long orgId, @Param("regionId") Long regionId);

    @Select(" SELECT ot.organization_id organizationId,\n" +
            "       mt.name taskName,\n" +
            "       mt.start_time startTime,\n" +
            "       mt.end_time endTime,\n" +
            "        if(IF(mt.type_id IN (60, 63) AND MONTH(NOW()) NOT IN (3, 6, 9, 12), 2, mt.status)=2,1,2) status\n" +
            "FROM t_meeting_task mt\n" +
            "         LEFT JOIN t_organization ot ON mt.org_id = ot.organization_id,\n" +
            "     t_meeting_plan mp,\n" +
            "     (SELECT MAKEDATE(YEAR(CURDATE()), 1) + INTERVAL QUARTER(CURDATE()) - 1 QUARTER             AS QUARTER_START,\n" +
            "             LAST_DAY(MAKEDATE(YEAR(CURDATE()), 1) + INTERVAL QUARTER(CURDATE()) * 3 - 1 MONTH) AS QUARTER_END\n" +
            "     ) q\n" +
            "WHERE mt.meeting_plan_id = mp.meeting_plan_id\n" +
            "  AND mp.is_del = 0\n" +
            "  AND mp.is_execute = 1\n" +
            "  AND mt.p_org_id = #{pOrgId}\n" +
            "  AND (\n" +
            "        (\n" +
            "                mt.type_id IN (60, 63)\n" +
            "                AND DATE_FORMAT(mt.start_time, '%Y-%m-%d') <= q.QUARTER_END\n" +
            "                AND DATE_FORMAT(mt.end_time, '%Y-%m-%d') >= q.QUARTER_START\n" +
            "            )\n" +
            "        OR (\n" +
            "                mt.type_id IN (61, 62)\n" +
            "                AND DATE_FORMAT(mt.start_time, '%Y-%m') <= DATE_FORMAT(NOW(), '%Y-%m') AND\n" +
            "                DATE_FORMAT(mt.end_time, '%Y-%m') >= DATE_FORMAT(NOW(), '%Y-%m')\n" +
            "            )\n" +
            "    )\n" +
            "  AND ot.status = 1\n" +
            "  AND ot.organization_id in(${orgIds})\n" +
            "  AND ot.region_id = #{regionId}\n" +
            "  AND mp.region_id = #{regionId}\n" +
            "  order by ot.organization_id " +
            "\t\t ")
    List<OrganizationLifeForm.MeetingDoneInfo> getCenterMeetingActivityIsDoneDetail(@Param("pOrgId") Long pOrgId, @Param("orgIds") String orgIds, @Param("regionId") Long regionId);

    @Select("SELECT ot.organization_id orgId,\n" +
            "       ot.short_name orgName,\n" +
            "       SUM(if(mt.`status` = 2, 1, 2)) as isDone\n" +
            "FROM t_meeting_task mt\n" +
            "         LEFT JOIN t_organization ot ON mt.org_id = ot.organization_id,\n" +
            "     t_meeting_plan mp\n" +
            "WHERE mt.meeting_plan_id = mp.meeting_plan_id\n" +
            "  AND mp.is_del = 0\n" +
            "  AND mp.is_execute = 1\n" +
            "  AND mt.p_org_id = #{pOrgId} \n" +
            "  AND (\n" +
            "        mt.type_id = 64\n" +
            "        AND DATE_FORMAT(mt.start_time, '%Y-%m') <= DATE_FORMAT(NOW(), '%Y-%m') AND\n" +
            "        DATE_FORMAT(mt.end_time, '%Y-%m') >= DATE_FORMAT(NOW(), '%Y-%m')\n" +
            "    )\n" +
            "  AND ot.status = 1\n" +
            "  AND (ot.org_level LIKE '%-${orgId}-%' OR ot.organization_id = #{orgId})\n" +
            "  AND ot.region_id = #{regionId}\n" +
            "  AND mp.region_id = #{regionId}\n" +
            "GROUP BY ot.organization_id")
    List<OrganizationLifeForm.OrganizationLifeCenterPartyActivityForm> getCenterPartyActivity(@Param("pOrgId") Long pOrgId, @Param("orgId") Long orgId, @Param("regionId") Long regionId);

    @Select("SELECT a.type_id typeId,\n" +
            "       a.type typeName,\n" +
            "       IFNULL(b.total, 0)             alreadyMeetingNum,\n" +
            "       IFNULL(c.ok_hold_total, 0)     alreadyDoneNum,\n" +
            "       IFNULL(c.no_hold_total, 0)     undoneNum,\n" +
            "       IFNULL(c.should_hold_total, 0) shouldDoneNum\n" +
            "FROM (\n" +
            "         SELECT type_id,\n" +
            "                type\n" +
            "         FROM t_type\n" +
            "         WHERE type_id IN (\n" +
            "                           60,\n" +
            "                           61,\n" +
            "                           62,\n" +
            "                           63,\n" +
            "                           64\n" +
            "             )\n" +
            "     ) a\n" +
            "         LEFT JOIN(\n" +
            "    SELECT a.*\n" +
            "    FROM (\n" +
            "             SELECT mt.type_id,\n" +
            "                    count(*) total\n" +
            "             FROM t_meeting m,\n" +
            "                  t_meeting_type mt\n" +
            "             WHERE m.is_del = 0\n" +
            "               AND DATE_FORMAT(m.start_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')\n" +
            "               AND m.meeting_id = mt.meeting_id\n" +
            "               AND m.`status` IN (7, 8, 10, 11, 12, 13, 14)\n" +
            "               AND mt.type_id IN (61, 62, 64)\n" +
            "               AND m.p_org_id = #{pOrgId} \n" +
            "               AND m.org_id IN (\n" +
            "                 SELECT organization_id\n" +
            "                 FROM t_organization\n" +
            "                 WHERE status = 1\n" +
            "                   AND (org_level LIKE '%-${orgId}-%' OR organization_id = #{orgId})\n" +
            "                   AND region_id = #{regionId}\n" +
            "             )\n" +
            "               AND m.region_id = #{regionId}\n" +
            "             GROUP BY mt.type_id\n" +
            "             UNION\n" +
            "             SELECT mt.type_id,\n" +
            "                    count(*) total\n" +
            "             FROM t_meeting m,\n" +
            "                  t_meeting_type mt,\n" +
            "                  (SELECT MAKEDATE(YEAR(CURDATE()), 1) + INTERVAL QUARTER(CURDATE()) QUARTER -\n" +
            "                          INTERVAL 1 QUARTER                                  AS QUARTER_START,\n" +
            "                          LAST_DAY(MAKEDATE(EXTRACT(YEAR FROM CURDATE()), 1) +\n" +
            "                                   INTERVAL QUARTER(CURDATE()) * 3 - 1 MONTH) AS QUARTER_END\n" +
            "                  ) q\n" +
            "             WHERE m.is_del = 0\n" +
            "               AND m.start_time <= q.QUARTER_END\n" +
            "               AND m.start_time >= q.QUARTER_START\n" +
            "               AND m.meeting_id = mt.meeting_id\n" +
            "               AND m.`status` IN (7, 8, 10, 11, 12, 13, 14)\n" +
            "               AND mt.type_id IN (60, 63)\n" +
            "               AND m.p_org_id = #{pOrgId} \n" +
            "               AND m.org_id IN (\n" +
            "                 SELECT organization_id\n" +
            "                 FROM t_organization\n" +
            "                 WHERE status = 1\n" +
            "                   AND (org_level LIKE '%-${orgId}-%' OR organization_id = #{orgId})\n" +
            "                   AND region_id = #{regionId}\n" +
            "             )\n" +
            "               AND m.region_id = #{regionId}\n" +
            "             GROUP BY mt.type_id\n" +
            "         ) a\n" +
            ") b ON a.type_id = b.type_id\n" +
            "         LEFT JOIN(\n" +
            "    SELECT *\n" +
            "    FROM (\n" +
            "             SELECT t.type_id,\n" +
            "                    SUM(if(`status` = 2, 1, 0)) ok_hold_total,\n" +
            "                    SUM(if(`status` = 1, 1, 0)) no_hold_total,\n" +
            "                    count(*)                    should_hold_total\n" +
            "             FROM (\n" +
            "                      SELECT mt.org_id,\n" +
            "                             mt.type_id,\n" +
            "                             mt.type,\n" +
            "                             mt.`status`\n" +
            "                      FROM t_meeting_task mt,\n" +
            "                           t_meeting_plan mp\n" +
            "                      WHERE mt.meeting_plan_id = mp.meeting_plan_id\n" +
            "                        AND mp.is_del = 0\n" +
            "                        AND mp.is_execute = 1\n" +
            "                        AND mt.type_id in (61, 62, 64)\n" +
            "                        AND mt.p_org_id = #{pOrgId} \n" +
            "                        AND DATE_FORMAT(mt.start_time, '%Y-%m') <= DATE_FORMAT(NOW(), '%Y-%m')\n" +
            "                        AND DATE_FORMAT(mt.end_time, '%Y-%m') >= DATE_FORMAT(NOW(), '%Y-%m')\n" +
            "                        AND mt.org_id IN (\n" +
            "                          SELECT organization_id\n" +
            "                          FROM t_organization\n" +
            "                          WHERE status = 1\n" +
            "                            AND (org_level LIKE '%-${orgId}-%' OR organization_id = #{orgId})\n" +
            "                            AND region_id = #{regionId}\n" +
            "                      )\n" +
            "                        AND mp.region_id = #{regionId}\n" +
            "                      GROUP BY mt.org_id, mt.type_id\n" +
            "                  ) t\n" +
            "             GROUP BY t.type_id\n" +
            "             UNION ALL\n" +
            "             SELECT t.type_id,\n" +
            "                    SUM(if(`status` = 2, 1, 0)) AS ok_total,\n" +
            "                    SUM(if(`status` = 1, 1, 0)) AS no_total,\n" +
            "                    count(*)                       total\n" +
            "             FROM (\n" +
            "                      SELECT mt.org_id,\n" +
            "                             mt.type_id,\n" +
            "                             mt.type,\n" +
            "                             mt.`status`\n" +
            "                      FROM t_meeting_task mt,\n" +
            "                           t_meeting_plan mp,\n" +
            "                           (SELECT MAKEDATE(YEAR(CURDATE()), 1) +\n" +
            "                                   INTERVAL QUARTER(CURDATE()) - 1 QUARTER             AS QUARTER_START,\n" +
            "                                   LAST_DAY(MAKEDATE(YEAR(CURDATE()), 1) +\n" +
            "                                            INTERVAL QUARTER(CURDATE()) * 3 - 1 MONTH) AS QUARTER_END\n" +
            "                           ) q\n" +
            "                      WHERE mt.meeting_plan_id = mp.meeting_plan_id\n" +
            "                        AND mp.is_del = 0\n" +
            "                        AND mp.is_execute = 1\n" +
            "                        AND mt.type_id in (60, 63)\n" +
            "                        AND mt.p_org_id = #{pOrgId} \n" +
            "                        AND DATE_FORMAT(mt.start_time, '%Y-%m-%d') <= q.QUARTER_END\n" +
            "                        AND DATE_FORMAT(mt.end_time, '%Y-%m-%d') >= q.QUARTER_START\n" +
            "                        AND mt.org_id IN (\n" +
            "                          SELECT organization_id\n" +
            "                          FROM t_organization\n" +
            "                          WHERE status = 1\n" +
            "                            AND (org_level LIKE '%-${orgId}-%' OR organization_id = #{orgId})\n" +
            "                            AND region_id = #{regionId}\n" +
            "                      )\n" +
            "                        AND mp.region_id = #{regionId}\n" +
            "                      GROUP BY mt.org_id, mt.type_id\n" +
            "                  ) t\n" +
            "             GROUP BY t.type_id\n" +
            "         ) a\n" +
            ") c ON a.type_id = c.type_id")
    List<OrganizationLifeForm.OrganizationLifeInnerMeetingActivityForm> getMeetingActivityDetail(@Param("pOrgId") Long pOrgId, @Param("orgId") Long orgId, @Param("regionId") Long regionId);

    @Select("SELECT mt.org_id orgId,\n" +
            "       o.name orgName,\n" +
            "       IF(GROUP_CONCAT(mt.status) LIKE '%1%', 1, 2) completeStatus\n" +
            "FROM t_meeting_task mt,\n" +
            "     t_meeting_plan mp,\n" +
            "     (SELECT MAKEDATE(YEAR(CURDATE()), 1) + INTERVAL QUARTER(CURDATE()) - 1 QUARTER             AS QUARTER_START,\n" +
            "             LAST_DAY(MAKEDATE(YEAR(CURDATE()), 1) + INTERVAL QUARTER(CURDATE()) * 3 - 1 MONTH) AS QUARTER_END\n" +
            "     ) q,\n" +
            "     t_organization o\n" +
            "WHERE mt.meeting_plan_id = mp.meeting_plan_id\n" +
            "  AND o.organization_id = mt.org_id\n" +
            "  AND mp.is_del = 0\n" +
            "  AND mt.p_org_id = #{pOrgId}\n" +
            "  AND mp.is_execute = 1\n" +
            "  AND mt.type_id =#{typeId}\n" +
            "  AND DATE_FORMAT(mt.start_time, '%Y-%m-%d') <= q.QUARTER_END\n" +
            "  AND DATE_FORMAT(mt.end_time, '%Y-%m-%d') >= q.QUARTER_START\n" +
            "  AND mt.org_id IN (\n" +
            "    SELECT organization_id\n" +
            "    FROM t_organization\n" +
            "    WHERE status = 1\n" +
            "      AND (org_level LIKE '%-${orgId}-%' OR organization_id = #{orgId})\n" +
            "      AND region_id =#{regionId}\n" +
            "  )\n" +
            "  AND mp.region_id =#{regionId}\n" +
//            "  AND mt.status = 1\n" +
            "GROUP BY mt.org_id")
    List<OrganizationLifeForm.OrganizationLifeInnerUndoenMeetingActivityForm> getQuarterMeetingUnDoneInfo(@Param("pOrgId") Long pOrgId, @Param("typeId") Integer typeId, @Param("orgId") Long orgId, @Param("regionId") Long regionId);

    @Select("SELECT mt.org_id orgId,\n" +
            "       o.name orgName,\n" +
            "       IF(GROUP_CONCAT(mt.status) LIKE '%1%', 1, 2) completeStatus\n" +
            "FROM t_meeting_task mt,\n" +
            "     t_meeting_plan mp,\n" +
            "     t_organization o\n" +
            "WHERE mt.meeting_plan_id = mp.meeting_plan_id\n" +
            "  AND o.organization_id = mt.org_id\n" +
            "  AND mp.is_del = 0\n" +
            "  AND mp.is_execute = 1\n" +
            "  AND mt.p_org_id = #{pOrgId} \n" +
            "  AND mt.type_id =#{typeId}\n" +
            "  AND DATE_FORMAT(mt.start_time, '%Y-%m') <= DATE_FORMAT(NOW(), '%Y-%m')\n" +
            "  AND DATE_FORMAT(mt.end_time, '%Y-%m') >= DATE_FORMAT(NOW(), '%Y-%m')\n" +
            "  AND mt.org_id IN (\n" +
            "    SELECT organization_id\n" +
            "    FROM t_organization\n" +
            "    WHERE status = 1\n" +
            "      AND (org_level LIKE '%-${orgId}-%' OR organization_id = #{orgId})\n" +
            "      AND region_id =#{regionId}\n" +
            "  )\n" +
            "  AND mp.region_id =#{regionId}\n" +
//            "  AND mt.status = 1\n" +
            "GROUP BY mt.org_id")
    List<OrganizationLifeForm.OrganizationLifeInnerUndoenMeetingActivityForm> getMonthMeetingUnDoneInfo(@Param("pOrgId") Long pOrgId, @Param("typeId") Integer typeId, @Param("orgId") Long orgId, @Param("regionId") Long regionId);

    @Select("select case uc.rating \n" +
            "when 1 then '优秀'\n" +
            "when 2 then '合格'\n" +
            "when 3 then '基本合格'\n" +
            "when 4 then '不合格'\n" +
            "end as commentResult,uc.org_id orgId,uc.org_name orgName,uc.user_id userId,uc.user_name userName\n" +
            "from t_meeting_user_comment uc\n" +
            "where (uc.org_id=#{orgId} or uc.org_level like concat('%-',#{orgId},'-%')) and uc.review_year=#{lastYear} and uc.rating =1 and uc.status=1")
    List<OrganLifeUserCommentForm> meetingLifeUserComment(@Param("orgId") Long orgId, @Param("lastYear") Integer lastYear);

    @Select("select case dr.rating \n" +
            "when 1 then '好'\n" +
            "when 2 then '较好'\n" +
            "when 3 then '一般'\n" +
            "when 4 then '差'\n" +
            "end as commentResult,dr.org_id orgId,dr.org_name orgName,to1.party_leader partyLeader\n" +
            "from t_meeting_org_debrief_review dr\n" +
            " left join t_organization to1 on dr.org_id=to1.organization_id " +
            "where dr.review_year=#{lastYear} and dr.status=1 and (dr.org_id=#{orgId} or dr.org_level like '%-${orgId}-%') and dr.rating =1")
    List<OrganLifeUserCommentForm> meetingLifeOrgComment(@Param("orgId") Long orgId, @Param("lastYear") Integer lastYear);


    @Select("<script>" +
            "SELECT user_id id, 1 type, `level`, `name` FROM t_meeting_user_commend_penalize \n" +
            "WHERE type = 1 AND approval_status = 2 AND `level` IN " +
            "<foreach collection=\"levelList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "#{item}" +
            "</foreach>" +
            "\nUNION all\n" +
            "SELECT org_id id, 2 type, `level`, `name` FROM t_meeting_org_commend_penalize \n" +
            "WHERE type = 1 AND approval_status = 2 AND `level` IN " +
            "<foreach collection=\"levelList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    List<TbcCommendPenalizeDbForm> meetingCommendPenalize(@Param("levelList") List<Integer> levelList);
}
