package com.goodsogood.ows.mapper.meeting

import com.goodsogood.ows.mapper.MyMapper
import com.goodsogood.ows.model.db.meeting.MeetingEntity
import com.goodsogood.ows.model.vo.meeting.MeetingOrgCommendPenalizeQueryVO
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Repository
import javax.validation.constraints.NotNull

/**
 * <AUTHOR>
 * @date 2024/2/22
 * @description class DataVMeetingMapper
 */
@Repository
@Mapper
interface DataVMeetingMapper : MyMapper<MeetingEntity> {

    /**
     * 获取领导调研相关统计数据
     *
     */
    @Select(
        """
        SELECT '调研总计' AS `key`, (SELECT COUNT(*) FROM t_meeting_leader_survey) AS `value`
        UNION ALL
        SELECT '年度调研' AS `key`, (SELECT COUNT(*) FROM t_meeting_leader_survey WHERE survery_type = 2) AS `value`
        UNION ALL
        SELECT '日常调研' AS `key`, (SELECT COUNT(*) FROM t_meeting_leader_survey WHERE survery_type = 1) AS `value`
        UNION ALL
        SELECT '确定课题数' AS `key`, (SELECT COUNT(*) FROM t_meeting_leader_survey WHERE survery_type IN (1, 2)) AS `value`
        UNION ALL
        SELECT '参与调研人数' AS `key`, (SELECT SUM(num_people) FROM t_meeting_leader_survey WHERE survery_type IN (1, 2)) AS `value`;
    """
    )
    fun getLeaderResearchStatistics(): List<Pair<String, Long>>

    /**
     * 获取领导调研分类统计数据
     */
    @Select(
        """
        SELECT '座谈访谈' AS `key`, COUNT(CASE WHEN FIND_IN_SET('1', interview) > 0 THEN 1 END) AS `value` FROM t_meeting_leader_survey
        UNION ALL
        SELECT '随机走访' AS `key`, COUNT(CASE WHEN FIND_IN_SET('2', interview) > 0 THEN 1 END) AS `value` FROM t_meeting_leader_survey
        UNION ALL
        SELECT '问卷调查（轻流）' AS `key`, COUNT(CASE WHEN FIND_IN_SET('3', interview) > 0 THEN 1 END) AS `value` FROM t_meeting_leader_survey
        UNION ALL
        SELECT '问卷调查' AS `key`, COUNT(CASE WHEN FIND_IN_SET('4', interview) > 0 THEN 1 END) AS `value` FROM t_meeting_leader_survey
        UNION ALL
        SELECT '专家调查' AS `key`, COUNT(CASE WHEN FIND_IN_SET('5', interview) > 0 THEN 1 END) AS `value` FROM t_meeting_leader_survey
        UNION ALL
        SELECT '抽样调查' AS `key`, COUNT(CASE WHEN FIND_IN_SET('6', interview) > 0 THEN 1 END) AS `value` FROM t_meeting_leader_survey
        UNION ALL
        SELECT '统计分析' AS `key`, COUNT(CASE WHEN FIND_IN_SET('7', interview) > 0 THEN 1 END) AS `value` FROM t_meeting_leader_survey
        UNION ALL
        SELECT '其他' AS `key`, COUNT(CASE WHEN FIND_IN_SET('8', interview) > 0 THEN 1 END) AS `value` FROM t_meeting_leader_survey;
    """
    )
    fun getLeaderResearchTypeStatistics(): List<Pair<String, Int>>

    /**
     * 获取奖惩登记列表
     */
    @Select(
        """
        <script>
        SELECT
            t.org_id,
            t.org_name as orgName,
            t.category,
            ( SELECT op_value FROM t_option WHERE op_key = t.category ) AS categoryValue,
            t.level,
            ( SELECT op_value FROM t_option WHERE op_key = t.`level` ) AS levelValue,
            ( SELECT op_value FROM t_option WHERE op_key = t.`name` ) AS nameValue,
            t.ratify_time AS ratifyTime
        FROM
            t_meeting_org_commend_penalize t
            LEFT JOIN t_organization o ON t.org_id = o.organization_id 
        WHERE
            t.region_id = 19 
            AND t.STATUS = 1 
            AND t.approval_status = 2 
            AND t.type = 1
            and o.`status` = 1
            <if test="excludeOrgIds != null and excludeOrgIds.size > 0">
                AND t.org_id NOT IN
                <foreach collection="excludeOrgIds" item="excludeOrgId" open="(" separator="," close=")">
                    #{excludeOrgId}
                </foreach>
                <foreach item="item" collection="excludeOrgIds" open="" separator=" " close=" ">
                AND o.org_level NOT LIKE CONCAT('%-',#{item},'-%') AND o.organization_id != #{item}
                </foreach>
            </if>
            <if test="unitId != null">
                AND o.owner_id = #{unitId}
            </if>
            ORDER BY t.ratify_time
        </script>
    """
    )
    fun getRewardPunishStatistics(
        @Param("unitId") unitId: Long?,
        @Param("excludeOrgIds") excludeOrgIds: List<String>?,
        @Param("regionId") regionId: @NotNull Long,
    ): List<MeetingOrgCommendPenalizeQueryVO>
}