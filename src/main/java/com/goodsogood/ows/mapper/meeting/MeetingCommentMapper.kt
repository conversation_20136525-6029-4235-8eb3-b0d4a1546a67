package com.goodsogood.ows.mapper.meeting

import com.goodsogood.ows.mapper.MyMapper
import com.goodsogood.ows.model.db.*
import com.goodsogood.ows.model.db.meeting.*
import com.goodsogood.ows.model.vo.*
import com.goodsogood.ows.model.vo.dataCockpit.StaticsForm
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Repository

@Repository
@Mapper
interface MeetingCommentMapper : MyMapper<MeetingCommentEntity> {

}

@Repository
@Mapper
interface MeetingCommentApproveMapper : MyMapper<MeetingCommentApproveEntity> {

}

@Repository
@Mapper
interface MeetingCommentMemberMapper : MyMapper<MeetingCommentMemberEntity> {

}

@Repository
@Mapper
interface MeetingCommentMemberAppraisalMapper : MyMapper<MeetingCommentMemberAppraisalEntity> {

}

@Repository
@Mapper
interface MeetingCommentMemberComplexMapper : MyMapper<MeetingCommentMemberComplexEntity> {

}

@Repository
@Mapper
interface MeetingCommentStatisticsMapper : MyMapper<MeetingCommentStatisticsEntity> {
    @Select(
        """<script> select comment_id as commentId,org_id as orgId,org_name as orgName,year,party_number as partyNumber,join_number as joinNumber,sum(A)  as level1,sum(B)  as level2,sum(C)  as level3,sum(D)  as level4 from(
select t1.comment_id, t1.org_id,t1.org_level,t1.year as year,t1.org_name,t1.party_number,t1.join_number,
case when t1.rating=1  then t1.rating_number else 0 end as 'A',
case when t1.rating=2 then rating_number else 0 end as 'B',
case when t1.rating=3 then rating_number else 0 end as 'C',
case when t1.rating=4 then rating_number else 0 end as 'D'
 from t_meeting_comment_statistics t1 where t1.region_id=86
 ) a 
 where 1=1 
 <if test ="orgId != null and orgId != ''"> and (a.org_id=#{orgId} or a.org_level like concat('%-',#{orgId},'-%')) </if> 
 <if test ="year != null and year != ''"> and a.year = #{year} </if> 
 group by a.comment_id,a.org_id,a.year,a.org_name,a.party_number,a.join_number
order by a.org_id,a.year desc</script> """
    )
    fun queryStatics(@Param("year")year: Int?, @Param("orgId")orgId:Long?) : MutableList<StaticsForm>
}

@Repository
@Mapper
interface MeetingCommentMemberComplexHistoryMapper : MyMapper<MeetingCommentMemberComplexHistoryEntity> {

}
