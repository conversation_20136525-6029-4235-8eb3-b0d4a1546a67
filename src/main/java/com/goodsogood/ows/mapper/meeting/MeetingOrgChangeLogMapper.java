package com.goodsogood.ows.mapper.meeting;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.meeting.MeetingOrgChangeLogEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;


/**
 * 查询组织信息变更
 * <AUTHOR>
 * @date 2019/12/11
 * @return
 */
@Repository
@Mapper
public interface MeetingOrgChangeLogMapper extends MyMapper<MeetingOrgChangeLogEntity> {

    /**
     * 查询组织当前时间最新的一条数据
     * <AUTHOR>
     * @date 2019/12/11
     * @param orgId
     * @param time
     * @return com.goodsogood.ows.model.db.meeting.MeetingOrgChangeLogEntity
     */
    @Select("<script>select meeting_org_change_log_id,org_id,org_name,type,org_type_child,is_retire,period,create_time,process_tag,org_level " +
            " from t_meeting_org_change_log where org_id = #{orgId} and create_time &lt;= #{time} ORDER BY create_time desc limit 1 </script>")
    @Results({
            @Result(column = "meeting_org_change_log_id", property = "meetingOrgChangeLogId"),
            @Result(column = "org_id", property = "orgId"),
            @Result(column = "org_name", property = "orgName"),
            @Result(column = "type", property = "type"),
            @Result(column = "org_type_child", property = "orgTypeChild"),
            @Result(column = "is_retire", property = "type"),
            @Result(column = "period", property = "period"),
            @Result(column = "create_time", property = "createTime"),
            @Result(column = "process_tag", property = "processTag"),
            @Result(column = "org_level", property = "orgLevel")
    })
    MeetingOrgChangeLogEntity selectChangeLogByDate(@Param("orgId")Long orgId, @Param("time") String time);
}
