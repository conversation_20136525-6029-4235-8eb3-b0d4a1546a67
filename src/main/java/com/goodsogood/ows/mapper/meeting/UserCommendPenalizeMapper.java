package com.goodsogood.ows.mapper.meeting;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.meeting.UserCommendPenalizeEntity;
import com.goodsogood.ows.service.scoreManager.impl.BaseOrgPeoplePunishService;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface UserCommendPenalizeMapper extends MyMapper<UserCommendPenalizeEntity> {

    @Select("<script>" +
            " select o.op_value " +
            "   from t_meeting_user_commend_penalize mu, t_option o " +
            "  where mu.name = o.op_key " +
            "    and user_id = #{userId} " +
            "    and `status` = 1" +
            "    and Year(effective_time) = #{year}" +
            "</script>")
    List<String> getUserCommendPenalize(@Param("userId") Long userId,
                                        @Param("year") int year);


    @Select("SELECT\n" +
            "\tmeeting_user_commend_penalize_id meetingUserCommendPenalizeId,\n" +
            "\tuser_id as userId, \n" +
            "\tuser_name as userName,\n" +
            "\teffective_time as effectiveTime,\n" +
            "\t(select op_value from t_option where code = '104901' and op_key = p1.`category`) as type,\n" +
            "\t(select op_value from t_option where code like '104901%' and op_key = p1.`name`) as typeName,\n" +
            "\tbasis_description basisDescription,\n" +
            "\torg_id orgId\n" +
            "FROM\n" +
            "\tt_meeting_user_commend_penalize p1\n" +
            "WHERE\n" +
            "\t`status` = 1 \n" +
            "\tAND approval_status = 2 \n" +
            "\tAND type = 2\n" +
            "\tAND org_id= #{orgId}\n"+
            "\tAND DATE_FORMAT(effective_time,'%Y-%m') = #{queryTime}"
    )
    List<BaseOrgPeoplePunishService.PeoplePunishVo> getOrgPeoplePunishInfo(@Param("orgId") Long orgId, @Param("queryTime") String queryTime);

    @Select("SELECT\n" +
            "\tmeeting_user_commend_penalize_id meetingUserCommendPenalizeId,\n" +
            "\tuser_id as userId, \n" +
            "\tuser_name as userName,\n" +
            "\teffective_time as effectiveTime,\n" +
            "\t(select op_value from t_option where code = '104901' and op_key = p1.`category`) as type,\n" +
            "\t(select op_value from t_option where code like '104901%' and op_key = p1.`name`) as typeName,\n" +
            "\tbasis_description basisDescription,\n" +
            "\torg_id orgId\n" +
            "FROM\n" +
            "\tt_meeting_user_commend_penalize p1\n" +
            "WHERE\n" +
            "\t`status` = 1 \n" +
            "\tAND approval_status = 2 \n" +
            "\tAND type = 2\n" +
            "\tAND org_id in ( select o1.organization_id \n" +
            "from t_organization o1\n" +
            "where o1.status=1 and o1.region_id=#{regionId} and o1.org_level like '%-${orgId}-%' and o1.org_type_child in (${orgTypeChild}) ) \n"+
            "\tAND DATE_FORMAT(effective_time,'%Y-%m') = #{queryTime}"
    )
    List<BaseOrgPeoplePunishService.PeoplePunishVo> getOrgPeoplePunishInfoByLowerOrg(@Param("orgId") Long id, @Param("queryTime") String queryTime,
                                                                                     @Param("orgTypeChild") String orgTypeChild, @Param("regionId") Long regionId);

    /**
     * 查询人员的奖惩信息
     * @param userIds
     * @param year
     * @param month
     * @return
     */
    @Select("<script>" +
            "SELECT meeting_user_commend_penalize_id meetingUserCommendPenalizeId, user_id userId, user_name userName, `level`, `name`\n" +
            "FROM t_meeting_user_commend_penalize\n" +
            "WHERE type = 1 \n" +
            "  AND user_id in " +
            "<foreach item=\"userId\" collection=\"userIds\" open=\"(\" separator=\",\" close=\")\"> " +
            "#{userId} " +
            "</foreach>" +
            "  AND approval_status = 2 \n" +
            "  AND `status` = 1 \n" +
            "  AND YEAR (update_time) = #{year}\n" +
            "  AND MONTH(update_time) = #{month}\n" +
            "</script>")
    List<UserCommendPenalizeEntity> getCommendPenalizeByUser(@Param("userIds") List<Long> userIds,
                                                             @Param("year") Integer year,
                                                             @Param("month") Integer month);

}
