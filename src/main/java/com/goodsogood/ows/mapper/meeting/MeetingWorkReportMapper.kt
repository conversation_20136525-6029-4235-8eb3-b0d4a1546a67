package com.goodsogood.ows.mapper.meeting

import com.goodsogood.ows.mapper.MyMapper
import com.goodsogood.ows.model.db.meeting.ReportEntity
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * @date 2023/12/4
 * @description class MeetingWorkReportMapper
 */
@Mapper
interface MeetingWorkReportMapper : MyMapper<ReportEntity> {
    /**
     * 通过领导id，查询时间段内是否听取过汇报 / 通过单位id，查询时间段内是否进行过意识形态工作
     * （t_meeting_work_report 表中的 leader_ids 中的 领导id是逗号分割的字符串,需要用类似find_in_set方式查询）
     * （t_meeting_work_report 表中的 unit_id 中的 单位id是逗号分割的字符串,需要用类似find_in_set方式查询）
     * @param year 年份
     * @param reportType 汇报类型 1.工作汇报，2.意识形态
     * @param ids 领导id数组 reportType = 1 时使用
     * @param unitIds 单位id数组 reportType = 2 时使用
     * @return 汇报列表
     */
    @Select(
        """
<script>
    SELECT
      work_report_id as workReportId,
      report_type as reportType,
      leader_names as leaderNames,
      leader_ids as leaderIds,
      leader as leader,
      department as department,
      reporter as reporter,
      report_time as reportTime,
      year as year,
      half_year_interval as halfYearInterval,
      report_record as reportRecord,
      unit_id as unitId,
      unit_name as unitName,
      attachment as attachment,
      create_user as createUser,
      create_time as createTime,
      update_user as updateUser,
      update_time as updateTime
     FROM t_meeting_work_report
    WHERE report_type = #{reportType}
    <if test="year != null">
        AND year = #{year}
    </if>
    <if test="ids != null and ids.size() != 0">
        AND
        <foreach collection="ids" item="id" open="(" separator="OR" close=")">
            find_in_set(#{id}, leader_ids)
        </foreach>
    </if>
    <if test="unitIds != null and unitIds.size() != 0">
        AND
        <foreach collection="unitIds" item="unitId" open="(" separator="OR" close=")">
            find_in_set(#{unitId}, unit_id)
        </foreach>
    </if>
</script>
"""
    )
    fun getReportListByLeaderIds(year: Int, reportType: Int, ids: List<Long>? = null, unitIds: List<Long>? = null): List<ReportEntity>
}