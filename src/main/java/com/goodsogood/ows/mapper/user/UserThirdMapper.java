package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.user.UserThirdEntity;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * userThirdMapper
 *
 * <AUTHOR>
 * @data 2018-05-23
 */
@Repository
@Mapper
public interface UserThirdMapper extends MyMapper<UserThirdEntity> {

     @Insert("insert into t_user_third (user_id,th_token,th_type,create_time,update_time)  values " +
             "(#{userId},#{thToken},#{thType},SYSDATE(),SYSDATE())")
     @Options(useGeneratedKeys = true, useCache = false, keyProperty = "userId", keyColumn = "user_id")
     int importUser(UserThirdEntity userThirdEntity);

     /**
      * 根据th_token查询用户信息
      *
      * @param thToken
      * @return
      */
     @Select("SELECT third_id thirdId, user_id userId, th_type thType, th_token thToken, head, nick_name nickName, create_time createTime, update_time updateTime " +
             " FROM `t_user_third` where th_token = #{thToken} and oid = #{oId}")
     UserThirdEntity findByToken(@Param("thToken") String thToken, @Param("oId") Long oId);


     /**
      * 通过用户id以及openId查询主键
      *
      * @param userId 用户id
      * @param token  openId
      * @return 主键
      */
     @Select("SELECT third_id FROM t_user_third WHERE user_id = #{userId} AND th_token = #{token}")
     long getThirdId(@Param("userId") long userId, @Param("token") String token);

     /**
      * @Author: tc
      * @Description 通过用户编号、组织编号、第三方类型修改用户昵称
      * @Date 15:02 2018/7/19
      */
     @Update("update t_user_third set nick_name = #{nickname},update_time = now() " +
             "where user_id = #{userId} and th_type = #{thType} and oid = #{oid}")
     void updNickname(@Param("userId") Long userId, @Param("thType") Integer thType,
                      @Param("nickname") String nickname, @Param("oid") Long oid);

     @Select("select `third_id` thirdId, `user_id` userId, `th_type` thType, `th_token` thToken, `head`, `nick_name` nickName, `create_time` createTime, `update_time` updateTime, `oid` oId " +
             "from t_user_third limit #{pageNo},#{pageSize}")
     List<UserThirdEntity> getAllOpenIdByLimit(@Param("pageNo") int pageNo, @Param("pageSize") int pageSize);

     @Select("<script>SELECT ut.th_token openId,u.user_id userId,u.`name`," +
             "<if test=\"isVolunteer == null || isVolunteer == 0\">" +
             "u.phone phone,u.phone_secret phoneSecret," +
             "</if>" +
             "<if test=\"isVolunteer != null and isVolunteer == 1\">" +
             "IF(u.contact_number is null,u.phone,u.contact_number) phone, IF(u.contact_number_secret is null,u.phone_secret,u.contact_number_secret) phoneSecret," +
             "</if>" +
             "u.cert_number certNumber," +
             "u.cert_number_secret certNumberSecret," +
             "u.age age,u.education education,u.ethnic ethnic,u.gender gender " +
             "FROM t_user_third ut " +
             "RIGHT JOIN t_user u ON ut.user_id = u.user_id  AND ut.oid = #{orgId} " +
             "<if test=\"userOrgId != null\">RIGHT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id " +
             "AND uoc.organization_id = #{userOrgId} AND uoc.is_employee = #{isEmployee} </if>" +
             "<if test=\"userOrgId == null\">RIGHT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
             "AND uoc.is_employee = 1\n</if>" +
             "<if test=\"regionId != null\">AND uoc.`region_id` = #{regionId}\n</if>" +
             "WHERE u.`status` = #{status} and u.user_id > 1\n" +
             "</script>")
     List<UserInfoBase> findAllUserByOrgId(@Param("orgId") Long orgId, @Param("userOrgId") Long userOrgId,
                                           @Param("status") Integer status, @Param("isEmployee") Integer isEmployee,
                                           @Param("isVolunteer") Integer isVolunteer, @Param("regionId") Long regionId);

}


