package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.tbcFusion.PartyPersonInfoEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.mongodb.ActivityInfo;
import com.goodsogood.ows.model.mongodb.ScoreInfo;
import com.goodsogood.ows.model.mongodb.fusion.DualLifeDetail;
import com.goodsogood.ows.model.vo.ItemVO;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.experience.ExperienceUser;
import com.goodsogood.ows.model.vo.overview.KeyValVo;
import com.goodsogood.ows.model.vo.overview.OverviewUserVo;
import com.goodsogood.ows.model.vo.overview.PeriodInfoVo;
import com.goodsogood.ows.model.vo.overview.PeriodPartyBranchVo;
import com.goodsogood.ows.model.vo.sas.UserOrgInfo;
import com.goodsogood.ows.model.vo.sas.UserScoreVo;
import com.goodsogood.ows.model.vo.supervise.SendMsgVo;
import com.goodsogood.ows.model.vo.tbc.TbcBaseVo;
import com.goodsogood.ows.model.vo.tbc.UserCorpInfo;
import com.goodsogood.ows.model.vo.user.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/11/19
 * Description: t_user
 */
@Repository
@Mapper
public interface UserMapper extends MyMapper<UserEntity> {

    @Select("<script> " +
            " select tu.user_id userId, tu.`name` userName,to1.organization_id orgId,to1.`name` orgName," +
            "to1.parent_id parentId,to1.org_type orgType,to1.org_type_child orgTypeChild,to1.org_level orgLevel,to1.org_create_time orgCreateTime\n" +
            "from t_user tu\n" +
            "LEFT JOIN t_user_org_corp tuoc on tu.user_id=tuoc.user_id and tuoc.is_employee=1\n" +
            "LEFT JOIN t_organization to1 on tuoc.organization_id=to1.organization_id " +
            "and to1.`status`=1 and to1.org_type=102803\n" +
            "where to1.org_type=102803 and tu.user_id in " +
            " <foreach collection=\"userIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            " #{item} " +
            " </foreach> " +
            " </script> ")
    List<ScoreInfo> getScoreMongoUser(@Param("userIds") List<Long> userIds);

    @Select("<script> " +
            " select tu.user_id userId,to1.organization_id orgId,to1.org_level orgLevel\n" +
            "from t_user tu\n" +
            "LEFT JOIN t_user_org_corp tuoc on tu.user_id=tuoc.user_id and tuoc.is_employee=1\n" +
            "LEFT JOIN t_organization to1 on tuoc.organization_id=to1.organization_id " +
            "and to1.`status`=1\n" +
            "where tu.user_id in " +
            " <foreach collection=\"userIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            " #{item} " +
            " </foreach> " +
            " </script> ")
    List<ActivityInfo.ParticipantUsers> getActivityMongoUsers(@Param("userIds") List<Long> userIds);

    @Select("SELECT DISTINCT\n" +
            "  u.user_id as userId,\n" +
            "  u.`name` as userName,o.`name` as orgName,o.organization_id orgId,o.region_id as regionId \n" +
            "FROM\n" +
            "  t_user u\n" +
            "  LEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "  LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND uoc.is_employee = 1 \n" +
            "  AND o.`status` = 1 \n" +
            "  AND o.`region_id` = #{regionId}" +
//            "--  AND o.org_id IS NOT NULL \n" +
//            "--  AND o.org_id != '' \n" +
            "  AND o.org_type = 102803 \n" +
//            "--  AND u.id IS NOT NULL \n" +
//            "--  AND u.id != ''" +
            "")
    List<UserInfoBase> getAllUserInfo(@Param("regionId") Long regionId);


    @Select("SELECT DISTINCT\n" +
            "  u.user_id as userId,\n" +
            "  u.`name` as userName,o.`name` as orgName ,o.organization_id as orgId," +
            "  o.region_id as regionId\n" +
            "FROM\n" +
            "  t_user u\n" +
            "  LEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "  LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "  u.`status` = 1 and u.user_id=#{userId} and o.region_id=#{regionId} \n" +
            "  AND uoc.is_employee = 1 \n" +
            "  AND o.`status` = 1 \n" +
//            "  --AND o.organization_id IS NOT NULL \n" +
//            "  --AND o.organization_id != '' \n" +
            "  AND o.org_type = 102803 \n" +
//            " -- AND u.id IS NOT NULL \n" +
//            " -- AND u.id != ''" +
            "")
    UserInfoBase getUserInfoByUserId(@Param("userId") Long userId, @Param("regionId") Long regionId);

    @Select("<script>" +
            "SELECT DISTINCT u.user_id userId,u.`name`,u.phone,u.phone_secret phoneSecret\n" +
            "FROM t_user u\n" +
            "INNER JOIN t_user_role ur ON u.user_id = ur.user_id\n" +
            "INNER JOIN t_role r ON ur.role_id = r.role_id\n" +
            "INNER JOIN t_organization o ON ur.organization_id = o.organization_id\n" +
            "WHERE r.`status` = #{status}\n" +
            "AND o.`status` = #{status}\n" +
            "AND u.`status` = #{status}\n" +
            "AND r.role_type = #{roleType}\n" +
            "AND ur.organization_id = #{orgId}\n" +
            "AND u.id IS NOT NULL\n" +
            "AND u.id <![CDATA[<>]]> ''" +
            "</script>")
    List<UserInfoBase> findManagerByWhere(@Param("status") Integer status,
                                          @Param("roleType") Integer roleType,
                                          @Param("orgId") Long orgId);

    /**
     * 根据条件查询政治，自然生日党员(组织以及下级组织)
     *
     * @param curDate       查询日期
     * @param status        状态
     * @param politicalType 政治面貌
     * @param flag          flag
     * @param isEmployee    isEmployee
     * @param orgId         orgId
     */
    @Select("<script>" +
            "SELECT u.user_id userId,u.`name`,u.phone,u.phone_secret phoneSecret\n" +
            "FROM t_user u\n" +
            "INNER JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            "INNER JOIN t_organization o ON uoc.organization_id = o.organization_id\n" +
            "WHERE u.`status` = #{status} AND FIND_IN_SET(u.political_type,#{politicalType})\n" +
            "<if test = \"flag == 1\"> AND u.joining_time LIKE CONCAT('%', #{curDate}, '%')\n</if>" +
            "<if test = \"flag == 2\"> AND u.birthday LIKE CONCAT('%', #{curDate}, '%')\n</if>" +
            "AND o.`status` = #{status}\n" +
            "AND (uoc.organization_id = #{orgId} OR o.org_level LIKE CONCAT('%-', #{orgId}, '-%'))\n" +
            "AND uoc.is_employee = #{isEmployee}\n" +
            "AND u.phone IS NOT NULL\n" +
            "AND u.phone <![CDATA[<>]]> ''\n" +
            "AND u.id IS NOT NULL\n" +
            "AND u.id <![CDATA[<>]]> ''" +
            "</script>")
    List<UserInfoBase> findMemberByDate(@Param("curDate") String curDate,
                                        @Param("status") Integer status,
                                        @Param("politicalType") String politicalType,
                                        @Param("flag") Integer flag,
                                        @Param("isEmployee") Integer isEmployee,
                                        @Param("orgId") Long orgId);

    @Select("SELECT\n" +
            "  COUNT(IF(LENGTH(trim(u.user_name))>0 ,TRUE,NULL)) as `name`,\n" +
            "  COUNT(IF(LENGTH(trim(u.phone))>0 ,TRUE,NULL)) as phone,\n" +
            "  COUNT(IF(LENGTH(trim(u.cert_number))>0 ,TRUE,NULL)) as certNumber,\n" +
            "  COUNT(IF(LENGTH(trim(u.joining_time))>0 ,TRUE,NULL)) as joiningTime,\n" +
            "  COUNT(IF(LENGTH(trim(u.education))>0 ,TRUE,NULL)) as education,\n" +
            "  COUNT(IF(LENGTH(trim(u.gender))>0 ,TRUE,NULL)) as gender,\n" +
            "  COUNT(IF(LENGTH(trim(u.political_type))>0 ,TRUE,NULL)) as politicalType,\n" +
            "  COUNT(IF(LENGTH(trim(u.position_code))>0 ,TRUE,NULL)) as positionCode,\n" +
            "  COUNT(1) as totalCount\n" +
            "  FROM\n" +
            "  t_user_snapshot u\n" +
            "WHERE 1=1\n" +
            "  AND u.date_month =#{staDate}  \n" +
            " AND (u.org_id = #{orgId} OR u.org_level LIKE CONCAT('%-', #{orgId}, '-%'))\n" +
            "  AND ( u.position_code NOT IN(49,50,51,52,53)" +
            " or u.position_code is null or u.position_code='' ) " +
            "and political_type IN(1,5,17,18)\n" +
            "and region_id=86  and u.status=1 \n"
    )
    Map<String, Object> getScoreUser(@Param("orgId") Long orgId, @Param("staDate") String staDate);

    //<foreach collection="months" index="index" item="item" open="( separator="," close=")">
    //</foreach>"

    @Select("<script>" +
            "<foreach collection=\"orgIds\" index=\"index\" item=\"item\" separator=\" UNION ALL \" >" +
            " SELECT\n" +
            "  SUM(LENGTH(u.user_name)>0 ) as `name`,\n" +
            "  SUM(LENGTH(u.phone)>0 ) as phone,\n" +
            "  SUM(LENGTH(u.cert_number)>0 ) as certNumber,\n" +
            "  SUM(LENGTH(u.joining_time)>0) as joiningTime,\n" +
            "  SUM(LENGTH(u.education)>0 ) as education,\n" +
            "  SUM(LENGTH(u.gender)>0 ) as gender,\n" +
            "  SUM(LENGTH(u.political_type)>0 ) as politicalType,\n" +
            "  SUM(LENGTH(u.position_code)>0 ) as positionCode,\n" +
            "  COUNT(1) as totalCount ," +
            "  #{item} as orgId ,\n" +
            " u.`month` as dateMonth" +
            "  FROM\n" +
            "  t_user_snapshot u\n" +
            "WHERE 1=1\n" +
            "  AND u.year=#{staDate} \n" +
            " AND (u.org_id = #{item} OR u.org_level LIKE CONCAT('%-', #{item}, '-%'))\n" +
            "  AND ( u.position_code NOT IN(49,50,51,52,53)" +
            " or u.position_code is null or u.position_code='' ) " +
            "and political_type IN(1,5,17,18)\n" +
            "and region_id=86  and u.status=1 \n" +
            " group by `month`" +
            "</foreach>" +
            "</script>"
    )
    List<UserScoreVo> getScoreUserList(@Param("orgIds") List<Long> orgIds, @Param("staDate") int staDate);

    /**
     * 根据组织ID查询下级人员列表
     *
     * @param orgId orgId
     * @return list
     */
    @Select("<script>" +
            "SELECT\n" +
            "  DISTINCT u.user_id as userId,\n" +
            "  u.`name` as `name`,\n" +
            "  u.phone_secret as phoneSecret,\n" +
            "  o.organization_id as branchId, \n" +
            "  o.`name` as branchName,\n" +
            "  corp.partyId as partyId,\n" +
            "  ( SELECT `name` FROM t_organization WHERE organization_id = corp.partyId ) as partyName\n" +
            "FROM\n" +
            "  t_user u\n" +
            "  LEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id \n" +
            "  LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id\n" +
            "  LEFT JOIN (\n" +
            "  SELECT\n" +
            "    organization_id orgId,\n" +
            "    `name` orgName,\n" +
            "    ( SELECT op_value FROM t_option WHERE op_key = org_type_child AND CODE = 102803 ) orgType,\n" +
            "    owner_id ownerId,(\n" +
            "    CASE\n" +
            "        is_flow \n" +
            "        WHEN 1 THEN\n" +
            "        '是' ELSE '否' \n" +
            "      END \n" +
            "      ) isFlow,\n" +
            "      ( CASE is_retire WHEN 1 THEN '是' ELSE '否' END ) isRetire,(\n" +
            "      SUBSTRING_INDEX( SUBSTRING_INDEX( org_level, '-', 5 ), '-',- 1 )) partyId \n" +
            "    FROM\n" +
            "      t_organization \n" +
            "    WHERE\n" +
            "      `status` = 1 \n" +
            "      AND organization_id > 2 \n" +
            "    ) corp ON o.organization_id = corp.orgId \n" +
            "  WHERE\n" +
            "    u.`status` = 1 \n" +
            "    AND o.`status` = 1 \n" +
            "    AND uoc.is_employee = 1 \n" +
            "    AND u.is_visitor = 2\n" +
            "    AND u.political_type in (1, 5, 17 ,18)" +
            "   AND  (o.organization_id = #{orgId} or o.org_level like CONCAT(\"%-\",#{orgId},\"-%\"))" +
            "<if test=\"userIds != null and userIds.size != 0\">" +
            "   AND  u.user_id in " +
            "<foreach collection=\"userIds\" index=\"index\" item=\"item\" " +
            " open=\"(\" separator=\",\" close=\")\">\n" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "</script>")
    List<UserDssVo> getUserOrgByOrgId(@Param("orgId") Long orgId,
                                      @Param("userIds") List<Long> userIds);

    /**
     * 根据组织ID查询下级人员列表
     *
     * @return list
     */
    @Select("<script>" +
            "select u.user_id as userId, u.`name`, u.political_type as politicalType, u.gender, " +
            "  u.education, u.age, u.position, u.birthplace, u.joining_time as joiningTime,\n" +
            " uoc.organization_id as orgId, o.name as orgName, o.short_name as shortName \n" +
            "  from t_user u\n" +
            "  LEFT JOIN t_user_org_corp uoc on u.user_id = uoc.user_id and is_employee = 1\n" +
            "  LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id\n" +
            " WHERE o.`status` = 1\n" +
            "   AND u.`status` = 1\n" +
            "   AND u.is_visitor <![CDATA[ <> ]]> 1\n" +
            "<if test=\"isFilter != null and isFilter == 1 \"> " +
            "   AND u.political_type in (1, 5, 17 ,18)" +
            "</if>" +
            "<if test=\"isEmployee != null and isEmployee == 1 \"> " +
            "   AND uoc.is_employee = #{isEmployee} \n" +
            "</if>" +
            "   AND  (o.organization_id = #{orgId} or o.org_level like CONCAT(\"%-\",#{orgId},\"-%\"))" +
            "<if test=\"noStaOrgTypeChild != null and noStaOrgTypeChild.size != 0 \"> " +
            "   AND o.org_type_child not in " +
            "<foreach collection=\"noStaOrgTypeChild\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "<if test=\"excludeOrgIds != null and excludeOrgIds.size != 0 \"> " +
            "<foreach collection=\"excludeOrgIds\" index=\"index\" item=\"item\">\n" +
            "   AND o.organization_id != #{item} AND o.org_level not like concat('%-', #{item}, '-%') " +
            "</foreach>" +
            "</if>" +
            "</script>")
    List<UserEntity> findUserList(@Param("orgId") Long orgId,
                                  @Param("isFilter") Integer isFilter,
                                  @Param("excludeOrgIds") List<Long> excludeOrgIds,
                                  @Param("noStaOrgTypeChild") List<Integer> noStaOrgTypeChild,
                                  @Param("isEmployee") Integer isEmployee);

    /**
     * 根据组织ID查询下级人员列表
     *
     * @param orgIds orgIds
     * @return list
     */
    @Select("<script>" +
            "select u.user_id as userId, u.`name`, u.political_type as politicalType, u.gender, " +
            "  u.education, u.age, u.position, u.birthplace, u.joining_time as joiningTime,\n" +
            " uoc.organization_id as orgId, o.name as orgName, o.short_name as shortName \n" +
            "  from t_user u\n" +
            "  LEFT JOIN t_user_org_corp uoc on u.user_id = uoc.user_id and is_employee = 1\n" +
            "  LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id\n" +
            " WHERE o.`status` = 1\n" +
            "   AND u.`status` = 1\n" +
            "   AND u.is_visitor <![CDATA[ <> ]]> 1 \n" +
            "<if test=\"isFilter != null and isFilter == 1 \"> " +
            "   AND u.political_type in (1, 5, 17 ,18) \n" +
            "</if>" +
            "<if test=\"isEmployee != null and isEmployee == 1 \"> " +
            "   AND uoc.is_employee = #{isEmployee} \n" +
            "</if>" +
            "<if test=\"orgIds != null and orgIds.size != 0 \"> " +
            "   AND " +
            "<foreach collection=\"orgIds\" index=\"index\" item=\"item\" open=\"(\" separator=\" or \" close=\")\">\n" +
            "     o.organization_id = #{item} " +
            "     <if test=\"subOrg != 0 \"> " +
            "       or o.org_level like CONCAT(\"%-\",#{item},\"-%\") " +
            "     </if>" +
            "</foreach>" +
            "</if>" +
            "<if test=\"noStaOrgTypeChild != null and noStaOrgTypeChild.size != 0 \"> " +
            "   AND o.org_type_child not in " +
            "<foreach collection=\"noStaOrgTypeChild\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "<if test=\"excludeOrgIds != null and excludeOrgIds.size != 0 \"> " +
            "<foreach collection=\"excludeOrgIds\" index=\"index\" item=\"item\">\n" +
            "   AND o.organization_id <![CDATA[ <> ]]> #{item} AND o.org_level not like concat('%-', #{item}, '-%') " +
            "</foreach>" +
            "</if>" +
            " group by u.user_id " +
            "</script>")
    List<UserEntity> getUserByOrgIds(@Param("orgIds") List<Long> orgIds,
                                     @Param("subOrg") Integer subOrg,
                                     @Param("isFilter") Integer isFilter,
                                     @Param("excludeOrgIds") List<Long> excludeOrgIds,
                                     @Param("noStaOrgTypeChild") List<Integer> noStaOrgTypeChild,
                                     @Param("isEmployee") Integer isEmployee);


    /**
     * 查询快照表的用户信息
     *
     * @param orgId  orgId
     * @param years  years
     * @param months months
     * @return set
     */
    @Select("<script>" +
            "SELECT DISTINCT user_id FROM t_user_snapshot " +
            "WHERE `year` in( #{years} ) AND `month` in (#{months})  AND org_id = #{orgId} AND `status` = 1 and is_retire=2 " +
            "</script>")
    Set<Long> getUserInfoByOrgId(@Param("orgId") Long orgId, @Param("years") String years, @Param("months") String months);


    /**
     * 查询当月党支部下面的用户
     *
     * @param orgId orgId
     * @return Set
     */
    @Select("<script>" +
            "SELECT DISTINCT u.user_id FROM t_user u LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            " WHERE uoc.is_employee = 1 AND u.`status` = 1   AND uoc.organization_id = #{orgId}" +
            " and political_type in (1,5) " +
            " and (uoc.position_code not in(49,50,51,52,53) or position_code is null or position_code='')  " +
            "\n" +
            "</script>")
    Set<Long> getCurrentMonthUserByOrgId(@Param("orgId") Long orgId);


    /**
     * 查询当月党小组下面的用户
     *
     * @param orgId orgId
     * @return set
     */
    @Select("<script>" +
            "SELECT DISTINCT u.user_id FROM t_user u LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            " WHERE  u.`status` = 1   AND uoc.organization_id = #{orgId}" +
            " and political_type in (1,5)" +
            " and (uoc.position_code not in(49,50,51,52,53) or position_code is null or position_code='')  " +
            "\n" +
            "</script>")
    Set<Long> getOrgCurrentMonthGroupUserIds(Long orgId);


    /**
     * 查询当月党小组下面的用户
     *
     * @param orgIds orgIds
     * @return set
     */
    @Select("<script>" +
            "SELECT uogm.user_id FROM t_user_org_group g \n" +
            "INNER JOIN t_user_org_group_member uogm\n" +
            "  ON g.org_group_id = uogm.org_group_id\n" +
            "  AND uogm.is_delete = 1   \n" +
            "INNER JOIN t_user u    \n" +
            "  ON uogm.user_id = u.user_id\n" +
            "  AND u.`status` = 1\n" +
            "  AND u.political_type in (1,5,17,18)\n" +
            "INNER JOIN t_user_org_corp uoc  \n" +
            "  ON u.user_id = uoc.user_id\n" +
            "  AND uoc.is_employee = 1\n" +
            "  AND (position_code not in(49,50,51,52,53) or position_code is null or position_code='') \n" +
            "WHERE g.link_org_id in( ${orgIds}) " +
            "</script>")
    Set<Long> getOrgGroupUserIds(String orgIds);


    /**
     * 根据组织id 得到支委会 成员
     *
     * @param orgId orgId
     * @return set
     */
    @Select("<script>" +
            "select distinct u1.user_id\n" +
            "from t_user_org_period p1 \n" +
            "inner join t_user_org_period_member m1 on p1.period_id=m1.period_id and m1.is_delete=2 and m1.type=1\n" +
            "inner join t_user u1 on m1.user_id =u1.user_id and u1.`status`=1\n" +
            "where p1.is_delete=2 and p1.is_valid=1 and p1.org_id=#{orgId}" +
            "</script>")
    Set<Long> getPeriodUserInfo(@Param("orgId") Long orgId);


    /**
     * 得到支部或者党小组届次信息
     *
     * @param orgId orgId
     * @return string
     */
    @Select("<script>" +
            "select concat(date_format(start_time,'%Y.%m.%d'),'——',date_format(end_time,'%Y.%m.%d')) as result \n" +
            "from t_user_org_period where period_id =\n" +
            "ifnull((select period_id from t_user_org_period where region_id=#{regionId} " +
            "and org_id=#{orgId} and is_valid=1 and is_delete=2),\n" +
            "(select period_id from t_user_org_period where region_id=#{regionId} and org_id=#{orgId} and is_delete=2 " +
            "order by start_time desc limit 1 )) and org_id not in(${excludeOrgIds})" +
            "</script>")
    String getPeriodInfo(@Param("orgId") Long orgId, @Param("regionId") Long regionId,
                         @Param("excludeOrgIds") String excludeOrgIds);

    @Select("<script>" +
            "SELECT\n" +
            "    o1.`name` as orgName\n" +
            "FROM\n" +
            "    t_organization o1\n" +
            "    LEFT JOIN t_user_org_period p1 ON o1.organization_id = p1.org_id\n" +
            "    AND p1.is_valid = 1\n" +
            "    AND p1.is_delete = 2\n" +
            "    LEFT JOIN t_user_org_period_result pr ON pr.region_id = 86\n" +
            "    AND pr.create_date = date_format( now(), '%Y-%m-%d' )\n" +
            "    AND pr.org_id = o1.organization_id\n" +
            "    AND pr.batch IN ( 1, 2, 3, 4, 5, 6 )\n" +
            "WHERE\n" +
            "    o1.`status` = 1\n" +
            "    and o1.org_type_child IN (${sqlPart})\n" +
            "    and o1.organization_id not in(${excludeOrgIds})" +
            "    AND (o1.organization_id = #{orgId} OR o1.org_level LIKE CONCAT('%-',#{orgId},'-%'))\n" +
            "<foreach item=\"item\" collection=\"listExcludeOrgIds\" open=\"\" separator=\" \" close=\" \">" +
            "    AND o1.org_level NOT LIKE CONCAT('%-',#{item},'-%') AND o1.organization_id != #{item}" +
            "</foreach> " +
            "    AND pr.expire_date IS NOT NULL" +
            "</script>")
    List<OverviewUserVo> getSixMonthSetPeriodDetail(@Param("orgId") Long orgId,
                                                    @Param("regionId") Long regionId,
                                                    @Param("sqlPart") String sqlPart,
                                                    @Param("excludeOrgIds") String excludeOrgIds,
                                                    @Param("listExcludeOrgIds") List<Long> listExcludeOrgIds);

    /**
     * 本月未设置换届信息组织数量
     *
     * @param orgId orgId
     * @return PeriodInfoVo
     */
    @Select("<script>" +
            "SELECT\n" +
            "   ifnull( sum(IF(o1.org_type_child in (10280301, 10280310, 10280322, 10280344), 1, 0)) ,0)  as party,\n" +
            "   ifnull( sum(IF(o1.org_type_child in (10280303, 10280308, 10280311, 10280318, 10280342), 1, 0)),0) as total,\n" +
            "   ifnull(sum(IF(o1.org_type_child in (10280304, 10280309, 10280314, 10280315, 10280319), 1, 0)),0) as branch\n" +
            "FROM\n" +
            "    t_organization o1\n" +
            "    LEFT JOIN t_user_org_period p1 ON o1.organization_id = p1.org_id\n" +
            "    AND p1.is_valid = 1\n" +
            "    AND p1.is_delete = 2\n" +
            "    LEFT JOIN t_user_org_period_result pr ON pr.region_id = #{regionId}\n" +
            "    AND pr.create_date = date_format( now(), '%Y-%m-%d' )\n" +
            "    AND pr.org_id = o1.organization_id\n" +
            "    AND pr.batch = 0\n" +
            "WHERE\n" +
            "    o1.`status` = 1\n" +
            "    AND o1.organization_id not in( ${excludeOrgIds} )\n " +
            "    AND (o1.organization_id = #{orgId} OR o1.org_level LIKE CONCAT('%-',#{orgId},'-%'))\n" +
            "    AND pr.expire_date IS NOT NULL;" +
            "</script>")
    PeriodInfoVo getCurrentMonthSetPeriodInfo(@Param("orgId") Long orgId, @Param("regionId") Long regionId, @Param("excludeOrgIds") String excludeOrgIds);


    /**
     * 未设置换届信息组织数量
     * 本月到期还未换届 不包含标签 has_tag=1
     *
     * @param orgId orgId
     * @return List
     */
    @Select("<script>" +
            " SELECT\n" +
            "  o1.`name` as orgName \n" +
            "FROM\n" +
            "    t_organization o1\n" +
            "    LEFT JOIN t_user_org_period p1 ON o1.organization_id = p1.org_id\n" +
            "    AND p1.is_valid = 1\n" +
            "    AND p1.is_delete = 2\n" +
            "    LEFT JOIN t_user_org_period_result pr ON pr.region_id = 86\n" +
            "    AND pr.create_date = date_format( now(), '%Y-%m-%d' )\n" +
            "    AND pr.org_id = o1.organization_id\n" +
            "    AND pr.batch = 0\n" +
            "WHERE\n" +
            "    o1.`status` = 1\n" +
            "    and o1.org_type_child IN(${sqlPart})\n" +
            "    and o1.organization_id not in(${excludeOrgIds})" +
            "    AND (o1.organization_id = #{orgId} OR o1.org_level LIKE CONCAT('%-', #{orgId} ,'-%'))\n" +
            "    AND pr.expire_date IS NOT NULL" +
            "<foreach item=\"item\" collection=\"listExcludeOrgIds\" open=\"\" separator=\" \" close=\" \">" +
            "    AND o1.org_level NOT LIKE CONCAT('%-',#{item},'-%') AND o1.organization_id != #{item}" +
            "</foreach> " +
            "</script>"
    )
    List<OverviewUserVo> getCurrentMonthSetPeriodDetail(@Param("orgId") Long orgId,
                                                        @Param("regionId") Long regionId,
                                                        @Param("sqlPart") String sqlPart,
                                                        @Param("excludeOrgIds") String excludeOrgIds,
                                                        @Param("listExcludeOrgIds") List<Long> listExcludeOrgIds);

    /**
     * 未设置换届信息组织数量
     * 已到期未换届
     *
     * @param orgId orgId
     * @return List
     */
    @Select("<script>" +
            "SELECT\n" +
            "    o1.`name` as orgName\n" +
            "FROM\n" +
            "    t_organization o1\n" +
            "    LEFT JOIN t_user_org_period p1 ON o1.organization_id = p1.org_id\n" +
            "    AND p1.is_valid = 1\n" +
            "    AND p1.is_delete = 2\n" +
            "WHERE\n" +
            "    o1.`status` = 1\n" +
            "    and o1.org_type_child in(${sqlPart})\n" +
            "    and o1.organization_id not in(${excludeOrgIds})" +
            "    AND (o1.organization_id = #{orgId} OR o1.org_level LIKE CONCAT('%-',#{orgId},'-%'))\n" +
            "    AND (date_format(( p1.end_time ), '%Y-%m-%d' ) &lt;=  date_format( now(), '%Y-%m-%d' ) " +
            "        OR p1.period_id IS NULL)" +
            "<foreach item=\"item\" collection=\"listExcludeOrgIds\" open=\"\" separator=\" \" close=\" \">" +
            "    AND o1.org_level NOT LIKE CONCAT('%-',#{item},'-%') AND o1.organization_id != #{item}  " +
            "</foreach> " +
            "</script>")
    List<OverviewUserVo> getOverdueSetPeriodDetail(@Param("orgId") Long orgId,
                                                   @Param("regionId") Long regionId,
                                                   @Param("sqlPart") String sqlPart,
                                                   @Param("excludeOrgIds") String excludeOrgIds,
                                                   @Param("listExcludeOrgIds") List<Long> listExcludeOrgIds);

    /**
     * 组织关系转接-转入
     *
     * @param orgId orgId
     * @return orgId
     */
    @Select("<script>" +
            "SELECT\n" +
            "  utm.master_id,\n" +
            "  utm.user_id as userId,\n" +
            "  o.name as orgName ," +
            "  utm.user_name as name\n" +
            "FROM\n" +
            "  `t_organization` o LEFT JOIN\n" +
            "  `t_user_transfer_master` utm ON o.organization_id = utm.to_org_id\n" +
            " WHERE utm.`status` = 1\n" +
            "   AND utm.approve_type in (3,5)\n" +
            "   AND o.`status` = 1\n" +
            "   AND  ${sqlDataPart} \n" +
            "   AND o.organization_id not in(${excludeOrgIds}) " +
            "   AND (o.organization_id = #{orgId} OR o.org_level LIKE CONCAT('%-',#{orgId},'-%'))" +
            "<foreach item=\"item\" collection=\"listExcludeOrgIds\" open=\"\" separator=\" \" close=\"\">" +
            "    AND o.org_level NOT LIKE CONCAT('%-',#{item},'-%')" +
            "</foreach> " +
            "</script>")
    List<OverviewUserVo> transferInCountDetail(@Param("orgId") Long orgId,
                                               @Param("regionId") Long regionId,
                                               @Param("sqlDataPart") String sqlDataPart,
                                               @Param("sqlTypePart") String sqlTypePart,
                                               @Param("excludeOrgIds") String excludeOrgIds,
                                               @Param("listExcludeOrgIds") List<Long> listExcludeOrgIds);

    /**
     * 组织关系转接转出
     *
     * @param orgId orgId
     * @return list
     */
    @Select("<script>" +
            "SELECT\n" +
            "  utm.master_id,\n" +
            "  utm.user_id as userId,\n" +
            "  o.name as orgName ," +
            "  utm.user_name as name\n" +
            "FROM\n" +
            "  `t_organization` o LEFT JOIN\n" +
            "  `t_user_transfer_master` utm ON o.organization_id = utm.from_org_id\n" +
            " WHERE utm.`status` = 1\n" +
            "   AND utm.approve_type in (3,5)\n" +
            "   AND o.`status` = 1\n" +
            "   AND ${sqlDataPart}\n" +
            "   and  o.organization_id not in(${excludeOrgIds})" +
            "   AND (o.organization_id = #{orgId} OR o.org_level LIKE CONCAT('%-',#{orgId},'-%'))" +
            "<foreach item=\"item\" collection=\"listExcludeOrgIds\" open=\"\" separator=\" \" close=\" \">" +
            "    AND o.org_level NOT LIKE CONCAT('%-',#{item},'-%')" +
            "</foreach> " +
            "</script>")
    List<OverviewUserVo> transferOutCountDetail(@Param("orgId") Long orgId,
                                                @Param("regionId") Long regionId,
                                                @Param("sqlDataPart") String sqlDataPart,
                                                @Param("sqlTypePart") String sqlTypePart,
                                                @Param("excludeOrgIds") String excludeOrgIds,
                                                @Param("listExcludeOrgIds") List<Long> listExcludeOrgIds);


    @Select("<script>" +
            "SELECT SUM(value) as value,`name` FROM ( " +
            "SELECT CASE\n" +
            "  org_type_child\n" +
            "  WHEN 10280304 THEN '党支部'\n" +
            "  WHEN 10280306 THEN '党小组'\n" +
            "  ELSE '其他'" +
            " END AS name,\n" +
            "  count( 0 ) AS value,\n" +
            " org_type_child " +
            "FROM\n" +
            "  t_organization \n" +
            "WHERE\n" +
            "  `status` = 1 \n" +
            "  AND org_type = 102803 and organization_id not in(${excludeOrgIds})\n" +
            "  AND (organization_id = #{orgId} OR org_level LIKE '%-${orgId}-%')\n" +
            "GROUP BY\n" +
            "  org_type_child " +
            " ) as L   " +
            " GROUP BY `name` ORDER BY org_type_child" +
            "</script>")
    List<KeyValVo> orgOverviewOrgCommittee(@Param("orgId") Long orgId, @Param("excludeOrgIds") String excludeOrgIds);


    @Select("""
            <script>
            SELECT
            CASE
                u.political_type
                WHEN 1 THEN '正式党员'
                WHEN 5 THEN '预备党员'
                ELSE '其他' END AS name,
              count( DISTINCT u.user_id ) AS value
            FROM
              t_user u
              LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id
              LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id
            WHERE
              u.`status` = 1
              AND o.`status` = 1
              AND uoc.is_employee = 1
              AND u.political_type IN ( 1, 5, 17, 18 )
             and o.organization_id not in(${excludeOrgIds})
              AND (o.organization_id = #{orgId} OR o.org_level LIKE '%-${orgId}-%')
            GROUP BY
              u.political_type;
            </script>
            """)
    List<KeyValVo> staPartyMember(@Param("orgId") Long orgId, @Param("excludeOrgIds") String excludeOrgIds);

    @Select("<script>" +
            "SELECT * FROM \n" +
            "(\n" +
            "  SELECT\n" +
            "   u.name ,u.user_id as userId,u.political_type,o.`name` as orgName\n" +
            "  FROM\n" +
            "      t_user u \n" +
            "  LEFT JOIN\n" +
            "      t_user_org_corp uoc \n" +
            "          ON uoc.user_id = u.user_id \n" +
            "  LEFT JOIN\n" +
            "      t_organization o \n" +
            "          ON o.organization_id = uoc.organization_id \n" +
            "  WHERE\n" +
            "      u.`status` = 1 \n" +
            "      AND o.`status` = 1 \n" +
            "      AND uoc.is_employee = 1 \n" +
            "      AND u.political_type IN (\n" +
            "         1, 5, 17, 18  \n" +
            "      ) \n" +
            "      and o.organization_id not in(${excludeOrgIds})" +
            "      AND (o.organization_id = #{orgId} OR o.org_level LIKE '%-${orgId}-%') \n" +
            ") as L \n" +
            "WHERE political_type=#{politicalType}" +
            "</script>")
    List<OverviewUserVo> staPartyMemberDetail(@Param("orgId") Long orgId,
                                              @Param("excludeOrgIds") String excludeOrgIds,
                                              @Param("politicalType") Integer politicalType);


    @Select("<script>" +
            "SELECT distinct u.user_id \n" +
            "FROM\n" +
            "  t_user u \n" +
            "  LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id \n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1\n" +
            "  AND uoc.is_employee = 1\n" +
            "  AND u.political_type IN ( 1, 5, 17, 18 )\n" +
            " and o.organization_id not in(${excludeOrgIds}) " +
            "  AND (o.organization_id =  #{orgId} OR o.org_level LIKE '%-${orgId}-%')" +
            "</script>")
    List<Long> staPartyMemberTotal(@Param("orgId") Long orgId, @Param("excludeOrgIds") String excludeOrgIds);


    @Select("<script>" +
            "SELECT DISTINCT u.user_id \n" +
            "FROM\n" +
            "  t_user u\n" +
            "  LEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id \n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1  " +
            "  AND u.user_id != 1  \n" +
            "  AND (u.political_type not in (1,5,17,18) or u.political_type is null or u.political_type = '') \n" +
            "<if test=\"orgList != null and orgList.size > 0\">" +
            "<foreach collection=\"orgList\" item=\"orgId\" open=\"\" separator=\"\" close=\"\">" +
            "  AND o.org_level NOT LIKE CONCAT('%-', #{orgId}, '-%') AND uoc.organization_id <![CDATA[ <> ]]> #{orgId} \n" +
            "</foreach>" +
            "</if>" +
            "<if test=\"includeOrgList != null and includeOrgList.size > 0 \">AND " +
            "<foreach collection=\"includeOrgList\" item=\"orgId\" open=\"(\" separator=\"OR\" close=\")\">" +
            "o.org_level LIKE CONCAT('%-', #{orgId}, '-%') OR o.organization_id = #{orgId} \n" +
            "</foreach>" +
            "</if>" +
            "</script>")
    List<Long> staNoPartyMemberTotal(@Param("includeOrgList") List<Long> includeOrgList, @Param("orgList") List<Long> orgList);


    @Select("<script>" +
            "SELECT\n" +
            "   DISTINCT u.user_id as userId ,u.`name`,o.name as orgName \n" +
            "FROM\n" +
            "  t_user u\n" +
            "  LEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id \n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1\n" +
            "  AND u.user_id != 1    \n" +
            "  AND (u.political_type not in (1,5,17,18) or u.political_type is null or u.political_type = '')\n" +
            "  AND uoc.organization_id NOT IN ( ${excludeOrgIds} ) \n" +
            "  AND o.org_level NOT LIKE '%-1294-%' \n" +
            "  AND o.org_level NOT LIKE '%-5-%' \n" +
            "  AND o.org_level NOT LIKE '%-2409-%' \n" +
            "  AND o.org_level NOT LIKE '%-2417-%' \n" +
            "  AND o.org_level NOT LIKE '%-1222-%' \n" +
            "  AND o.org_level NOT LIKE '%-1255-%'" +
            "</script>")
    List<OverviewUserVo> staNoPartyMemberTotalDetail(@Param("orgId") Long orgId, @Param("excludeOrgIds") String excludeOrgIds);

    @Select("<script>" +
            "SELECT\n" +
            " u.`name`,o.name as orgName,u.user_id as userId " +
            " FROM \n" +
            "  t_user u \n" +
            "  LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id \n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1\n" +
            "  AND uoc.is_employee = 1\n" +
            "  AND u.political_type IN ( 1, 5, 17, 18 )  \n" +
            " and o.organization_id not in(${excludeOrgIds}) " +
            "  AND (o.organization_id =  #{orgId} OR o.org_level LIKE '%-${orgId}-%')" +
            "</script>")
    List<OverviewUserVo> staPartyMemberTotalDetail(@Param("orgId") Long orgId, @Param("excludeOrgIds") String excludeOrgIds);


    @Select("<script>" +
            "SELECT\n" +
            "CASE\n" +
            "    gender \n" +
            "    WHEN 1 THEN '男' \n" +
            "    WHEN 2 THEN '女' \n" +
            "    ELSE '未知' END AS name,\n" +
            "  count( DISTINCT u.user_id ) AS value\n" +
            "FROM\n" +
            "  t_user u \n" +
            "  LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1\n" +
            "  AND uoc.is_employee = 1\n" +
            "  AND u.political_type IN ( 1, 5, 17, 18 )\n" +
            " and o.organization_id not in(${excludeOrgIds})" +
            "  AND (o.organization_id =  #{orgId} OR o.org_level LIKE '%-${orgId}-%')\n" +
            "GROUP BY\n" +
            "  u.gender" +
            "</script>")
    List<KeyValVo> staPartyMemberGender(@Param("orgId") Long orgId, @Param("excludeOrgIds") String excludeOrgIds);


    @Select("<script>" +
            "SELECT  u.name , o.name as orgName ,u.user_id as userId" +
            " FROM \n" +
            "  t_user u \n" +
            "  LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1\n" +
            "  AND uoc.is_employee = 1\n" +
            "  AND u.political_type IN ( 1, 5, 17, 18 )\n" +
            " and  u.gender=#{gender}" +
            " and o.organization_id not in(${excludeOrgIds})" +
            "  AND (o.organization_id =  #{orgId} OR o.org_level LIKE '%-${orgId}-%')\n" +
            "</script>")
    List<OverviewUserVo> staPartyMemberGenderDetail(@Param("orgId") Long orgId,
                                                    @Param("excludeOrgIds") String excludeOrgIds,
                                                    @Param("gender") Integer gender);


    @Select("<script>" +
            "SELECT  u.name , o.name as orgName ,u.user_id as userId" +
            " FROM \n" +
            "  t_user u \n" +
            "  LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1\n" +
            "  AND uoc.is_employee = 1\n" +
            "  AND u.political_type IN ( 1, 5, 17, 18 )\n" +
            " and ( u.gender is null or u.gender ='' )" +
            " and o.organization_id not in(${excludeOrgIds})" +
            "  AND (o.organization_id =  #{orgId} OR o.org_level LIKE '%-${orgId}-%')\n" +
            "</script>")
    List<OverviewUserVo> staPartyMemberNoGenderDetail(@Param("orgId") Long orgId,
                                                      @Param("excludeOrgIds") String excludeOrgIds);

    @Select("<script>" +
            "\n" +
            "SELECT A.alias as `name`,IF(b.total IS NULL,0,b.total) as `value` FROM (\n" +
            "  SELECT * FROM t_overview_option WHERE type=1 and project_name='overview'\n" +
            ") as A\n" +
            "LEFT JOIN (\n" +
            "select `name`,sum(total) as total from (\n" +
            "SELECT\n" +
            "u.education,\n" +
            "  CASE\n" +
            "    WHEN (u.education = 1005 OR u.education = 103909 OR u.education = 103906 OR u.education = 103907 \n" +
            "    OR u.education = 103908 OR u.education = 103909 OR u.education = 103910 ) THEN '高中生及以下' \n" +
            "    WHEN (u.education = 103904 OR u.education = 103905 OR u.education = 1004 ) THEN '专科' \n" +
            "    WHEN (u.education = 1003 OR u.education = 103903) THEN '本科' \n" +
            "    WHEN (u.education = 103901 OR u.education = 103902) THEN '研究生及以上' \n" +
            "    ELSE '未知' END AS `name`,\n" +
            "  count(distinct u.user_id) AS total\n" +
            "FROM\n" +
            "  t_user u \n" +
            "  LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1\n" +
            "  AND uoc.is_employee = 1\n" +
            "  AND u.political_type IN ( 1, 5, 17, 18 )\n" +
            "  and o.organization_id not in(${excludeOrgIds})" +
            "  AND (o.organization_id = #{orgId} OR o.org_level LIKE '%-${orgId}-%') \n" +
            "GROUP BY\n" +
            "  u.education) temp GROUP BY `name`\n" +
            ")as B ON A.`name`=B.`name`\n" +
            "ORDER BY A.order_num" +
            "</script>")
    List<KeyValVo> staOverviewEducation(@Param("orgId") Long orgId, @Param("excludeOrgIds") String excludeOrgIds);


    @Select("  SELECT\n" +
            "  u.`name`,o.name as orgName ,o.organization_id as orgId,u.user_id as userId\n" +
            "  FROM\n" +
            "      t_user u \n" +
            "  LEFT JOIN\n" +
            "      t_user_org_corp uoc \n" +
            "          ON uoc.user_id = u.user_id \n" +
            "  LEFT JOIN\n" +
            "      t_organization o \n" +
            "          ON o.organization_id = uoc.organization_id \n" +
            "  WHERE\n" +
            "      u.`status` = 1 \n" +
            "      AND o.`status` = 1 \n" +
            "      AND uoc.is_employee = 1 \n" +
            "      AND u.political_type IN (1, 5, 17, 18 ) \n" +
            "      and o.organization_id not in(${excludeOrgIds}) \n" +
            "      AND (o.organization_id = #{orgId} OR o.org_level LIKE '%-${orgId}-%') \n" +
            "      and u.education IN(${education})")
    List<OverviewUserVo> staOverviewEducationDetail(@Param("orgId") Long orgId,
                                                    @Param("excludeOrgIds") String excludeOrgIds,
                                                    @Param("education") String education);

    @Select("  SELECT\n" +
            "  u.`name`,o.name as orgName,o.organization_id as orgId,u.user_id as userId\n" +
            "  FROM\n" +
            "      t_user u \n" +
            "  LEFT JOIN\n" +
            "      t_user_org_corp uoc \n" +
            "          ON uoc.user_id = u.user_id \n" +
            "  LEFT JOIN\n" +
            "      t_organization o \n" +
            "          ON o.organization_id = uoc.organization_id \n" +
            "  WHERE\n" +
            "      u.`status` = 1 \n" +
            "      AND o.`status` = 1 \n" +
            "      AND uoc.is_employee = 1 \n" +
            "      AND u.political_type IN (1, 5, 17, 18 ) \n" +
            "      and o.organization_id not in(${excludeOrgIds}) \n" +
            "      AND (o.organization_id = #{orgId} OR o.org_level LIKE '%-${orgId}-%') \n" +
            "      and ( u.education not  IN(${education}) or u.education is NULL )")
    List<OverviewUserVo> staOverviewEducationOtherDetail(@Param("orgId") Long orgId,
                                                         @Param("excludeOrgIds") String excludeOrgIds,
                                                         @Param("education") String education);

    @Select("<script>" +
            "SELECT A.alias as `name`, IF(b.total IS NULL,0,b.total) as `value` FROM (\n" +
            "  SELECT * FROM t_overview_option WHERE type=2 and project_name='overview' \n" +
            ") as A\n" +
            "LEFT JOIN (\n" +
            "select `name`,count(DISTINCT userId) as total from (\n" +
            "SELECT\n" +
            "(CASE\n" +
            "    WHEN age BETWEEN 1 AND 30 THEN '30岁及以下' \n" +
            "    WHEN age BETWEEN 31 and 45 THEN '31岁~45岁' \n" +
            "    WHEN age BETWEEN 46 and 55 THEN '46岁~55岁' \n" +
            "    WHEN age > 55 THEN '55岁以上'\n" +
            "    ELSE '未知' END) AS `name`,\n" +
            "    u.user_id as userId \n" +
            "FROM\n" +
            "  t_user u \n" +
            "  LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1\n" +
            "  AND uoc.is_employee = 1\n" +
            "  AND u.political_type IN ( 1, 5, 17, 18 )\n" +
            " and o.organization_id not in(${excludeOrgIds}) " +
            "  AND (o.organization_id = #{orgId} OR o.org_level LIKE '%-${orgId}-%')) tmp \n" +
            "  group by `name`\n" +
            ")as B ON A.`name`=B.`name`\n" +
            "ORDER BY A.order_num\n" +
            "\n" +
            "</script>")
    List<KeyValVo> orgOverviewAgeDistributed(@Param("orgId") Long orgId, @Param("excludeOrgIds") String excludeOrgIds);


    @Select("SELECT u.`name`,o.name as orgName ,u.user_id as userId,o.organization_id as orgId FROM\n" +
            "    t_user u \n" +
            "LEFT JOIN\n" +
            "    t_user_org_corp uoc \n" +
            "        ON uoc.user_id = u.user_id \n" +
            "LEFT JOIN\n" +
            "    t_organization o \n" +
            "        ON o.organization_id = uoc.organization_id \n" +
            "WHERE\n" +
            "    u.`status` = 1 \n" +
            "    AND o.`status` = 1 \n" +
            "    AND uoc.is_employee = 1 \n" +
            "    AND u.political_type IN (1, 5, 17, 18 ) \n" +
            "    and o.organization_id not in(${excludeOrgIds}) \n" +
            "    AND (o.organization_id = #{orgId}  OR o.org_level LIKE '%-${orgId}-%')\n" +
            "    and u.age>=#{minAge} and u.age<=#{maxAge}")
    List<OverviewUserVo> orgOverviewAgeDistributedDetail(@Param("orgId") Long orgId,
                                                         @Param("excludeOrgIds") String excludeOrgIds,
                                                         @Param("minAge") Integer minAge,
                                                         @Param("maxAge") Integer maxAge);


    @Select("SELECT u.`name`,o.name as orgName ,u.user_id as userId,o.organization_id as orgId FROM\n" +
            "    t_user u \n" +
            "LEFT JOIN\n" +
            "    t_user_org_corp uoc \n" +
            "        ON uoc.user_id = u.user_id \n" +
            "LEFT JOIN\n" +
            "    t_organization o \n" +
            "        ON o.organization_id = uoc.organization_id \n" +
            "WHERE\n" +
            "    u.`status` = 1 \n" +
            "    AND o.`status` = 1 \n" +
            "    AND uoc.is_employee = 1 \n" +
            "    AND u.political_type IN (1, 5, 17, 18 ) \n" +
            "    and o.organization_id not in(${excludeOrgIds}) \n" +
            "    AND (o.organization_id = #{orgId}  OR o.org_level LIKE '%-${orgId}-%')\n" +
            "    and ( u.age is null or u.age<=0 )")
    List<OverviewUserVo> orgOverviewAgeDistributedOtherDetail(@Param("orgId") Long orgId,
                                                              @Param("excludeOrgIds") String excludeOrgIds,
                                                              @Param("minAge") Integer minAge,
                                                              @Param("maxAge") Integer maxAge);

    @Select("<script>" +
            "SELECT A.alias as `name`,IF(b.total IS NULL,0,b.total) as `value` FROM (\n" +
            "  SELECT * FROM t_overview_option WHERE type=3 and project_name='overview' \n" +
            ") as A\n" +
            "LEFT JOIN (\n" +
            "select `name`,count(distinct userId) as total from (\n" +
            "SELECT\n" +
            "(CASE\n" +
            "    WHEN TIMESTAMPDIFF(YEAR, u.joining_time, CURDATE()) BETWEEN 0 AND 10 THEN '10年及以下' \n" +
            "    WHEN TIMESTAMPDIFF(YEAR, u.joining_time, CURDATE()) BETWEEN 11 and 20 THEN '11年~20年' \n" +
            "    WHEN TIMESTAMPDIFF(YEAR, u.joining_time, CURDATE()) BETWEEN 21 and 30 THEN '21年~30年' \n" +
            "    WHEN TIMESTAMPDIFF(YEAR, u.joining_time, CURDATE()) > 30 THEN '30年以上'\n" +
            "    ELSE '未知' END) AS `name`,\n" +
            "    u.user_id AS userId \n" +
            "FROM\n" +
            "  t_user u \n" +
            "  LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1\n" +
            "  AND uoc.is_employee = 1\n" +
            "  AND u.political_type IN ( 1, 5, 17, 18 )\n" +
            " and o.organization_id not in(${excludeOrgIds}) " +
            "  AND (o.organization_id = #{orgId} OR o.org_level LIKE '%-${orgId}-%')) tmp \n" +
            "  group by `name`\n" +
            ")as B ON A.`name`=B.`name`\n" +
            "ORDER BY A.order_num" +
            "</script>")
    List<KeyValVo> orgOverviewPartyAgeDistributed(@Param("orgId") Long orgId, @Param("excludeOrgIds") String excludeOrgIds);


    @Select("SELECT u.`name`,o.name as orgName,u.user_id as userId,o.organization_id as orgId  FROM t_user u \n" +
            "  LEFT JOIN\n" +
            "      t_user_org_corp uoc \n" +
            "          ON uoc.user_id = u.user_id \n" +
            "  LEFT JOIN\n" +
            "      t_organization o \n" +
            "          ON o.organization_id = uoc.organization_id \n" +
            "  WHERE\n" +
            "      u.`status` = 1 \n" +
            "      AND o.`status` = 1 \n" +
            "      AND uoc.is_employee = 1 \n" +
            "      AND u.political_type IN (1, 5, 17, 18 ) \n" +
            "      and o.organization_id not in(${excludeOrgIds}) \n" +
            "      AND (  o.organization_id = #{orgId} OR o.org_level LIKE '%-${orgId}-%')    \n" +
            "      and TIMESTAMPDIFF(YEAR,u.joining_time,CURDATE()) BETWEEN #{minAge} AND #{maxAge}")
    List<OverviewUserVo> orgOverviewPartyAgeDistributedDetail(@Param("orgId") Long orgId,
                                                              @Param("excludeOrgIds") String excludeOrgIds,
                                                              @Param("minAge") Integer minAge,
                                                              @Param("maxAge") Integer maxAge);


    @Select("SELECT u.`name`,o.name as orgName,u.user_id as userId,o.organization_id as orgId  FROM t_user u \n" +
            "  LEFT JOIN\n" +
            "      t_user_org_corp uoc \n" +
            "          ON uoc.user_id = u.user_id \n" +
            "  LEFT JOIN\n" +
            "      t_organization o \n" +
            "          ON o.organization_id = uoc.organization_id \n" +
            "  WHERE\n" +
            "      u.`status` = 1 \n" +
            "      AND o.`status` = 1 \n" +
            "      AND uoc.is_employee = 1 \n" +
            "      AND u.political_type IN (1, 5, 17, 18 ) \n" +
            "      and o.organization_id not in(${excludeOrgIds}) \n" +
            "      AND (  o.organization_id = #{orgId} OR o.org_level LIKE '%-${orgId}-%')    \n" +
            "      and ( u.joining_time is null  OR u.joining_time='' )")
    List<OverviewUserVo> orgOverviewPartyAgeDistributedOtherDetail(@Param("orgId") Long orgId,
                                                                   @Param("excludeOrgIds") String excludeOrgIds,
                                                                   @Param("minAge") Integer minAge,
                                                                   @Param("maxAge") Integer maxAge);

    @Select("<script>" +
            "SELECT A.`name`, IF(b.total IS NULL,0,b.total) as `value` FROM (\n" +
            "\tSELECT `name`,default_value as `value` FROM t_overview_option WHERE type=4 and project_name='overview'\n" +
            ") as A\n" +
            " LEFT JOIN (\n" +
            "\tselect `name`,count(*) as total from\n" +
            "\t\t(\t\n" +
            "\t\t\tSELECT CASE\n" +
            "\t\t\t\t\t\tWHEN stage=1 THEN '入党申请人'\n" +
            "\t\t\t\t\t\tWHEN stage=2 THEN '入党积极分子' \n" +
            "\t\t\t\t\t\tWHEN stage=3 THEN '发展对象' \n" +
            "\t\t\t\t\t\tWHEN stage=4 THEN '预备党员' \n" +
            "\t\t\t\t\t\tELSE '未知' END\n" +
            "\t\t\t\t AS `name`\n" +
            "\t\t\t\tFROM\n" +
            "\t\t\t\t\tt_user_develop  WHERE  `status` IS null \n" +
            " and   org_id in" +
            "<foreach collection=\"orgIdList\" item=\"orgId\" index=\"index\" open=\"(\" close=\")\" separator=\",\">#{orgId}</foreach>\n" +
            "\t\t) as L\t\t\t\n" +
            "\t\tGROUP BY `name`\t\t\t\t\n" +
            ")as B\n" +
            " ON A.`name`=B.`name`" +
            "</script>")
    List<KeyValVo> orgOverviewDevelopPartyMembers(@Param(value = "orgIdList") List<Long> collect);


    @Select("<script>" +
            "SELECT A.alias as `name`,IF(B.value IS NULL,0,B.value) as `value` FROM (\n" +
            "            SELECT * FROM t_overview_option WHERE type=5 and project_name='overview' \n" +
            ") as A\n" +
            "LEFT JOIN (\n" +
            " select  \"行政单位-家\" as name,\n" +
            " count(0) as value from t_organization \n" +
            " where `status` = 1 and org_type = 102807 and parent_id = 2 and organization_id not in(${excludeOrgIds})\n" +
            " UNION\n" +
            " SELECT L.`name`,SUM(IF(L.org_id>0,1,0)) as `value` FROM\n" +
            " (\n" +
            " SELECT\n" +
            "    g1.org_id ,\n" +
            "    '党组' as name \n" +
            "FROM\n" +
            "    t_organization o1 \n" +
            "INNER JOIN\n" +
            "    t_party_group g1 \n" +
            "        ON o1.owner_id=g1.org_id \n" +
            "        AND g1.`status`=1 \n" +
            "WHERE\n" +
            "    o1.`status`=1 \n" +
            "    and o1.organization_id not in(${excludeOrgIds}) \n" +
            "    AND (\n" +
            "        o1.organization_id=#{orgId} \n" +
            "        OR o1.org_level LIKE '%-${orgId}-%'\n" +
            "    ) \n" +
            "group by\n" +
            "    g1.org_id \n" +
            ") as L  " +
            " UNION \n" +
            "select org_type_temp as name,count(*) as value from (\n" +
            "SELECT\n" +
            "CASE\n" +
            "  WHEN (org_type_child = 10280301 or org_type_child = 10280310 or org_type_child = 10280322) THEN '党委'\n" +
            "  WHEN (org_type_child = 10280304 or org_type_child = 10280309 or org_type_child = 10280314 or \n" +
            "  org_type_child = 10280315 or org_type_child = 10280319) THEN '党支部'\n" +
            "  WHEN (org_type_child = 10280306) THEN '党小组'\n" +
            "  WHEN (org_type_child = 10280303 or org_type_child = 10280308 or " +
            " org_type_child = 10280311 or org_type_child = 10280318) THEN '党总支'\n" +
            "  ELSE '其他' END AS org_type_temp\n" +
            "FROM\n" +
            "  t_organization \n" +
            "WHERE\n" +
            "  `status` = 1 \n" +
            "  AND org_type = 102803 \n" +
            " and organization_id not in(${excludeOrgIds})" +
            "  AND (organization_id = #{orgId} OR org_level LIKE '%-${orgId}-%')) tmp group by org_type_temp" +
            ") as B ON A.name=B.name\n" +
            "ORDER BY A.order_num" +
            "</script>")
    List<KeyValVo> orgCityWide(@Param("orgId") Long orgId, @Param("excludeOrgIds") String excludeOrgIds);

    @Select("select distinct u1.user_id\n" +
            "from t_organization o1 \n" +
            "inner join t_user_org_corp oc1 on o1.organization_id =oc1.organization_id and oc1.is_employee=1\n" +
            "inner join t_user u1 on oc1.user_id=u1.user_id and u1.`status`=1 and u1.political_type in (1,5,17,18)\n" +
            "where o1.organization_id=#{orgId}")
    List<Long> getCqycPartyUserByOrgId(@Param("orgId") Long id);


    @Select("<script>" +
            "SELECT\n" +
            "(CASE\n" +
            "    WHEN age BETWEEN 1 AND 30 THEN '30岁及以下' \n" +
            "    WHEN age BETWEEN 31 and 45 THEN '31岁~45岁' \n" +
            "    WHEN age BETWEEN 46 and 55 THEN '46岁~55岁' \n" +
            "    WHEN age > 55 THEN '55岁以上'\n" +
            "    ELSE '未知' END) AS item,\n" +
            "    u.user_id AS userId\n" +
            "FROM\n" +
            "  t_user u \n" +
            "  LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1\n" +
            "  AND uoc.is_employee = 1\n" +
            "  AND u.political_type IN ( 1, 5, 17, 18 )\n" +
            "  AND (o.organization_id = #{orgId} OR o.org_level LIKE CONCAT('%-', #{orgId},'-%'))" +
            "<if test = \"excludeOrgIds !=null and excludeOrgIds !=''\"> " +
            " AND o.organization_id not in (${excludeOrgIds}) \n</if>" +
            "</script>")
    List<ItemVO> selectUserAgeDistributed(@Param("orgId") Long orgId, @Param("excludeOrgIds") String excludeOrgIds);

    @Select("<script>" +
            "SELECT\n" +
            "  CASE\n" +
            "    WHEN (u.education = 1005 OR u.education = 103909 OR u.education = " +
            "    103906 OR u.education = 103907 OR u.education = 103908 OR u.education = 103909 OR u.education = 103910 ) " +
            "   THEN '高中生及以下' \n" +
            "    WHEN (u.education = 103904 OR u.education = 103905 OR u.education = 1004 ) THEN '专科' \n" +
            "    WHEN (u.education = 1003 OR u.education = 103903) THEN '本科' \n" +
            "    WHEN (u.education = 103901 OR u.education = 103902) THEN '研究生及以上' \n" +
            "    ELSE '未知' END AS `item`, u.user_id as userId\n" +
            "FROM\n" +
            "  t_user u \n" +
            "  LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1\n" +
            "  AND uoc.is_employee = 1\n" +
            "  AND u.political_type IN ( 1, 5, 17, 18 )\n" +
            "  AND (o.organization_id = #{orgId} OR o.org_level LIKE CONCAT('%-',#{orgId},'-%')) " +
            "<if test = \"excludeOrgIds !=null and excludeOrgIds !=''\"> " +
            " AND o.organization_id not in (${excludeOrgIds}) \n</if>" +
            "</script>")
    List<ItemVO> selectUserEducationDistributed(@Param("orgId") Long orgId, @Param("excludeOrgIds") String excludeOrgIds);

    @Select("select distinct u1.user_id\n" +
            "from t_organization to1 \n" +
            "inner join t_user_org_corp c1 on to1.organization_id = c1.organization_id and c1.is_employee=1\n" +
            "inner join t_user u1 on c1.user_id=u1.user_id and u1.`status`=1 and u1.political_type in (1,5,17,18)\n" +
            "where to1.`status`=1 and to1.region_id=#{regionId} and (to1.organization_id=#{topOrgId} or to1.org_level like '%-${topOrgId}-%') and to1.org_type_child in (${typeChild})")
    List<Long> getPartyUserByOrgId(@Param("topOrgId") Long topOrgId, @Param("regionId") Long regionId, @Param("typeChild") String collect);


    @Select("SELECT DISTINCT\n" +
            "  u.user_id userId " +
            "FROM\n" +
            "  t_user u\n" +
            "  LEFT JOIN t_user_role ur ON u.user_id = ur.user_id\n" +
            "  LEFT JOIN t_role r ON ur.role_id = r.role_id\n" +
            "  LEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "  LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "  AND ur.organization_id = o.organization_id \n" +
            " WHERE u.`status` = 1 AND o.`status` = 1 AND r.`status` = 1\n" +
            "  and  r.role_type IN (3,9)\n" +
            "  AND o.organization_id = #{orgId}")
    Set<Long> getAdministratorByOrgId(@Param("orgId") Long orgId);

    @Select("<script>" +
            " SELECT  user_id as userId,`name` as userName  FROM t_user where user_id in" +
            "<foreach collection=\"userIds\" index=\"index\" item=\"item\" separator=\",\"  open=\"(\"  close=\")\">\n" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    Set<SendMsgVo.UserInfo> getUserNameByUserIds(@Param("userIds") List<Long> userIds);

    /**
     * 得到所有组织
     *
     * @return set
     */
    @Select("<script>" +
            "SELECT\n" +
            "o.`name` orgName, o.organization_id as orgId \n" +
            ",o.org_level as orgLevel,o.owner_id as ownerId,o.org_type_child as orgTypeChild,o.parent_id as parentId," +
            " o.longitude,o.latitude \n" +
            "FROM\n" +
            "  t_organization as o\n" +
            "WHERE\n" +
            "  `status` = 1 \n" +
            "  AND org_type = 102803 \n" +
            "  AND (organization_id = 3 OR org_level LIKE '%-3-%');" +
            "</script>")
    Set<TbcBaseVo> getAllOrgInfo();


    /**
     * 得到所有用户信息
     *
     * @return set
     */
    @Select("<script>" +
            "SELECT\n" +
            "u.user_id as userId ,u.`name` as userName, o.`name` orgName, o.organization_id as orgId ,u.head_url as headUrl," +
            "o.parent_id as parentId,o.longitude,o.latitude,o.org_level as orgLevel,\n" +
            "o.org_level as orgLevel,o.owner_id as ownerId,o.org_type_child as orgTypeChild," +
            " (SELECT op_value FROM t_option WHERE `op_key`=uoc.position_code limit 1) as partyPosition, \n" +
            " (SELECT `name` FROM t_organization WHERE organization_id=o.owner_id) as ownerName \n" +
            "FROM\n" +
            "  t_user u \n" +
            "  LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id \n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE\n" +
            "\tu.`status` = 1 \n" +
            "\tAND o.`status` = 1\n" +
            "\tAND uoc.is_employee = 1\n" +
            "\tAND u.political_type IN ( 1, 5, 17, 18 )\n" +
            "\tAND (o.organization_id = 3 OR o.org_level LIKE '%-3-%')" +
            " AND u.id IS NOT NULL \n" +
            " AND u.id  <![CDATA[ <> ]]>  '' \n" +
            " AND u.id NOT LIKE 'gsg%' order by u.user_id asc " +
            "</script>")
    Set<TbcBaseVo> getAllUserInfoByTbc();


    /**
     * 根据用户id 获取用户所在组织 以及所在单位
     *
     * @return TbcBaseVo
     */
    @Select("<script>" +
            "SELECT\n" +
            "u.user_id as userId ,u.`name` as userName, o.`name` orgName, o.organization_id as orgId ,u.head_url as headUrl," +
            "o.parent_id as parentId,o.longitude,o.latitude,o.org_level as orgLevel,\n" +
            "o.org_level as orgLevel,o.owner_id as ownerId,o.org_type_child as orgTypeChild," +
            " (SELECT op_value FROM t_option WHERE `op_key`=uoc.position_code limit 1) as partyPosition, \n" +
            " (SELECT `name` FROM t_organization WHERE organization_id=o.owner_id) as ownerName \n" +
            "FROM\n" +
            "  t_user u \n" +
            "  LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id \n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "   AND u.`user_id` = #{userId} \n" +
            "  AND o.`status` = 1\n" +
            "  AND uoc.is_employee = 1\n" +
            "  AND (o.organization_id = 3 OR o.org_level LIKE '%-3-%')  limit 1" +
            "</script>")
    TbcBaseVo getAllUserInfoByUserId(Long userId);

    /**
     * 得到体验用户信息
     */
    @Select("<script>" +
            "select a.name  name,a.user_id userId," +
            "(SELECT head FROM t_user_third WHERE user_id =a.user_id LIMIT 1) as avatar,a.head_url as avatarUser,\n" +
            "b.organization_id organizationId,b.`name` orgName ,b.org_level as orgLevel,c.position\n" +
            " from t_user a \n" +
            "left join t_user_org_corp c on a.user_id = c.user_id \n" +
            "left join t_organization b on c.organization_id = b.organization_id \n" +
            "where 1=1 <if test=\"userId !=null\"> AND a.user_id = #{userId} </if>" +
            " and c.is_employee = 1 and a.`status` = 1 and b.`status`=1 AND c.region_id=86" +
            "</script>")
    List<ExperienceUser> getExperienceUserInfo(Long userId);


    /**
     * 根据用户id查询他是否是党委、党总支、党支部管理员
     */
    @Select("SELECT DISTINCT\n" +
            "  u.user_id \n" +
            "FROM\n" +
            "  t_user AS u\n" +
            "  LEFT JOIN t_user_role AS ur ON u.user_id = ur.user_id\n" +
            "  LEFT JOIN t_role AS r ON ur.role_id = r.role_id \n" +
            "  LEFT JOIN t_organization AS o ON ur.organization_id = o.organization_id\n" +
            "WHERE\n" +
            "  r.role_type IN ( 3, 9 ) \n" +
            "  AND u.user_id = #{userId} \n" +
            "  AND r.`status` = 1\n" +
            "  AND o.`status` = 1 \n" +
            "  AND o.org_type_child IN (10280301,10280310,10280322,10280303,10280308,10280311,10280318,10280304,10280309,10280314,10280315,10280319);")
    Integer isAdmin(@Param("userId") Long userId);


    /**
     * 根据用户id查询他是否是党小组管理员
     */
    @Select("SELECT DISTINCT\n" +
            "  u.user_id \n" +
            "FROM\n" +
            "  t_user AS u\n" +
            "  LEFT JOIN t_user_role AS ur ON u.user_id = ur.user_id\n" +
            "  LEFT JOIN t_role AS r ON ur.role_id = r.role_id \n" +
            "  WHERE u.user_id =#{userId} \n" +
            "  AND r.role_type = 16")
    Integer isPartyGroupAdmin(@Param("userId") Long userId);


    /**
     * 根据用户id查询他是否是党小组管理员
     */
    @Select("SELECT detail_id FROM t_overview_option WHERE type=#{type} and alias=#{alias};")
    Integer getOverviewOptionType(@Param("type") Integer type, @Param("alias") String alias);


    /**
     * 根据用户id查询他是否是党小组管理员
     */
    @Select("${sql}")
    List<OverviewUserVo> getOrgNameBySql(@Param("sql") String sql);

    @Select("<script>" +
            " select phone as phone,user_id as userId from t_user where phone in" +
            "  <foreach collection=\"list\" item=\"user\" open=\"(\" separator=\",\" close=\")\"> " +
            " #{user.phoneSecret}" +
            " </foreach>" +
            "</script>"
    )
    List<UserEntity> queryUserByPhone(@Param("list") List<PartyPersonInfoEntity> list);

    @Select("SELECT\n" +
            "  ifnull(  sum(IF(o1.org_type_child in (10280301, 10280310, 10280322, 10280344), 1, 0)) ,0) as party,\n" +
            "  ifnull(  sum(IF(o1.org_type_child in (10280303, 10280308, 10280311, 10280318, 10280342), 1, 0)),0) as total,\n" +
            "  ifnull(  sum(IF(o1.org_type_child in (10280304, 10280309, 10280314, 10280315, 10280319), 1, 0)),0) as branch\n" +
            "FROM\n" +
            "    t_organization o1\n" +
            "    LEFT JOIN t_user_org_period p1 ON o1.organization_id = p1.org_id\n" +
            "    AND p1.is_valid = 1\n" +
            "    AND p1.is_delete = 2\n" +
            "    LEFT JOIN t_user_org_period_result pr ON pr.region_id = #{regionId}\n" +
            "    AND pr.create_date = date_format( now(), '%Y-%m-%d' )\n" +
            "    AND pr.org_id = o1.organization_id\n" +
            "    AND pr.batch in (1,2,3,4,5,6)\n" +
            "WHERE\n" +
            "    o1.`status` = 1\n" +
            "    AND o1.organization_id not in( ${excludeOrgIds} )\n " +
            "    AND (o1.organization_id = #{oid} OR o1.org_level LIKE CONCAT('%-',#{oid},'-%'))\n" +
            "    AND pr.expire_date IS NOT NULL;")
    PeriodInfoVo getSixMonthSetPeriodInfo(@Param("oid") Long oid, @Param("regionId") Long regionId, @Param("excludeOrgIds") String excludeOrgIds);

    @Select("SELECT\n" +
            "  ifnull(  sum(IF(o1.org_type_child in (10280301, 10280310, 10280322, 10280344), 1, 0)),0) as party,\n" +
            "  ifnull( sum(IF(o1.org_type_child in (10280303, 10280308, 10280311, 10280318, 10280342), 1, 0)),0) as total,\n" +
            "  ifnull(  sum(IF(o1.org_type_child in (10280304, 10280309, 10280314, 10280315, 10280319), 1, 0)),0) as branch\n" +
            "FROM\n" +
            "    t_organization o1\n" +
            "    LEFT JOIN t_user_org_period p1 ON o1.organization_id = p1.org_id\n" +
            "    AND p1.is_valid = 1\n" +
            "    AND p1.is_delete = 2\n" +
            "WHERE\n" +
            "    o1.`status` = 1\n" +
            "    AND o1.organization_id not in( ${excludeOrgIds} )\n " +
            "    AND (o1.organization_id = #{oid} OR o1.org_level LIKE CONCAT('%-',#{oid},'-%'))\n" +
            "    AND (date_format(( p1.end_time ), '%Y-%m-%d' ) <= date_format( now(), '%Y-%m-%d' ) OR p1.period_id IS NULL)")
    PeriodInfoVo getNoPeriodInfo(@Param("oid") Long oid, @Param("regionId") Long regionId, @Param("excludeOrgIds") String excludeOrgIds);


    @Select("SELECT\n" +
            "  DISTINCT\n" +
            "  pm.user_name as `name`,\n" +
            "  SUBSTRING_INDEX( SUBSTRING_INDEX(pm.position_name,',',b.i+1),',',-1) as position\n" +
            "FROM\n" +
            "  t_organization o1\n" +
            "  LEFT JOIN t_user_org_period p1 ON o1.organization_id = p1.org_id\n" +
            "  AND p1.is_valid = 1\n" +
            "  AND p1.is_delete = 2\n" +
            "  INNER JOIN t_num b\n" +
            "  LEFT JOIN t_user_org_period_member pm ON p1.period_id = pm.period_id\n" +
            "  AND pm.is_delete = 2\n" +
            "  WHERE o1.`status` = 1\n" +
            "  AND o1.organization_id = #{oid}\n" +
            "  AND o1.org_type_child IN (10280304, 10280309, 10280314, 10280315, 10280319)\n" +
            "  AND p1.period_id IS NOT NULL")
    List<PeriodPartyBranchVo.LeaderInfo> getLeaderInfo(Long oid);

    /**
     * 根据用户id 查询用户的所在耽误的owner_id
     *
     * @return long
     */
    @Select("    SELECT o.owner_id\n" +
            "    FROM\n" +
            "      t_user u\n" +
            "      LEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "      LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "    WHERE\n" +
            "      u.`status` = 1 \n" +
            "      AND o.`status` = 1 \n" +
            "      AND uoc.is_employee = 1 \n" +
            "      AND o.region_id = #{regionId}\n" +
            "      AND u.user_id = #{userId}\n"
    )
    Long getUserOwnerId(@Param("regionId") Long regionId, @Param("userId") Long userId);

    /**
     * 查询单位的党员的数量
     *
     * @param unitId        单位ID
     * @param regionId      区域ID
     * @param isParty       是否查询党员 1-党员，2-全体
     * @param excludeOrgIds 排除组织
     * @return list
     */
    @Select("<script>" +
            "(SELECT DISTINCT u.user_id AS userId, u.NAME,\n" +
            "  u.political_type AS politicalType,\n" +
            "  u.phone_secret AS phoneSecret,\n" +
            "  u.sequence, u.title,\n" +
            "  o.organization_id AS orgId,\n" +
            "  o.`name` AS orgName,\n" +
            "  1 AS isEmployee\n" +
            "FROM t_user u\n" +
            "  LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id AND uoc.is_employee = 1\n" +
            "  LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id\n" +
            "WHERE u.`status` = 1 \n" +
            "  AND o.`status` = 1 \n" +
            "  AND u.id IS NOT NULL \n" +
            "  AND u.id <![CDATA[ <> ]]> '' \n" +
            "  AND u.id NOT LIKE 'gsg%' \n" +
            "  AND o.region_id = #{regionId} \n" +
            "  AND uoc.region_id = #{regionId} \n" +
            "  AND u.political_type in (1,5,17,18)\n" +
            "<if test=\"unitId != null \">  AND o.owner_id = #{unitId}\n</if>" +
            "<if test=\"sequence != 0 and sequence != null\"> AND u.sequence = #{sequence}</if>" +
            "<foreach collection=\"excludeOrgIds\" item=\"orgId\" open=\" AND o.organization_id not in (\" separator=\",\" close=\")\"> #{orgId} </foreach>" +
            "<foreach collection=\"excludeOrgIds\" item=\"orgId\" open=\" \" separator=\" \" close=\" \">" +
            "  AND o.org_level NOT LIKE CONCAT('%-', #{orgId}, '-%') \n" +
            "</foreach>" +
            "GROUP BY u.user_id\n" +
            "ORDER BY u.user_id)\n" +
            "<if test=\"isParty == 2 \">" +
            " union all\n" +
            "(SELECT u.user_id AS userId,\n" +
            "  u.NAME,\n" +
            "  u.political_type AS politicalType,\n" +
            "  u.phone_secret AS phoneSecret,\n" +
            "  u.sequence,\n" +
            "  u.title,\n" +
            "  o.organization_id AS orgId,\n" +
            "  o.`name` AS orgName,\n" +
            "  0 AS isEmployee\n" +
            "FROM\n" +
            "  t_user u\n" +
            "  LEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id \n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1 \n" +
            "  AND u.id IS NOT NULL \n" +
            "  AND u.id <![CDATA[ <> ]]> '' \n" +
            "  AND u.id NOT LIKE 'gsg%' \n" +
            "<if test=\"sequence != 0 and sequence != null\"> AND u.sequence = #{sequence}\n</if>" +
            "  AND (u.political_type NOT IN ( 1, 5, 17, 18 ) OR u.political_type IS NULL OR u.political_type = '' ) \n" +
            "<if test=\"unitId != null \">AND (o.organization_id = #{unitId} OR o.org_level LIKE CONCAT('%-',#{unitId},'-%'))\n</if>" +
            "<foreach collection=\"excludeOrgIds\" item=\"orgId\" open=\" AND o.organization_id not in (\" separator=\",\" close=\")\"> #{orgId} </foreach>" +
            "<foreach collection=\"excludeOrgIds\" item=\"orgId\" open=\" \" separator=\" \" close=\" \">" +
            "  AND o.org_level NOT LIKE CONCAT('%-', #{orgId}, '-%') \n" +
            "</foreach>" +
            "GROUP BY u.user_id\n" +
            "ORDER BY u.user_id)" +
            "</if>" +
            "</script>")
    List<PbmUserInfo> findPbmUserList(@Param("unitId") Long unitId,
                                      @Param("regionId") Long regionId,
                                      @Param("isParty") Integer isParty,
                                      @Param("sequence") Integer sequence,
                                      @Param("excludeOrgIds") List<Long> excludeOrgIds);

    @Select("SELECT COUNT(1) FROM t_user_highlight WHERE user_id=#{userId} and `status`=1 " +
            " and create_time<#{endTime}")
    Integer userHighlight(@Param("userId") long userId,
                          @Param("startTime") @Nullable String startTime,
                          @Param("endTime") @Nullable String endTime);


    @Select("<script>" +
            "SELECT\n" +
            "\tSUM(IF(u.political_type in (1,17),1,0)) as officialMember,\n" +
            "\tSUM(IF(u.political_type in (5,18),1,0)) as prepareMember,\n" +
            "\tSUM(IF(u.gender = 1, 1, 0)) man,\n" +
            "\tSUM(IF(u.gender = 2, 1, 0)) woman\n" +
            "FROM\n" +
            "\tt_user u\n" +
            "\tLEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            "\tLEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE\n" +
            "\tu.`status` = 1\n" +
            "\tAND o.`status` = 1\n" +
            "\tAND uoc.is_employee = 1\n" +
            "\tAND o.activate = 1 \n" +
            "\tAND o.parent_id <![CDATA[<>]]> - 999 \n" +
            "\tAND o.org_type_child IN ( 10280301, 10280310, 10280322, 10280303, 10280308, 10280311, 10280318, 10280304, 10280309, 10280314, 10280315, 10280319 ) \n" +
            "\tAND (o.organization_id = #{orgId} OR o.org_level LIKE CONCAT('%-',#{orgId},'-%'))\n" +
            "\tAND FIND_IN_SET(u.political_type,'1,5,17,18')" +
            "</script>")
    OrgUserNumberInfo getUserNumberInfo(@Param("orgId") Long orgId);


    @Select("<script>" +
            "SELECT\n" +
            "\tu.user_id as userId,\n" +
            "\tu.`name`,\n" +
            "\to.`name` as orgName,\n" +
            "\tDATE_FORMAT(u.joining_time,'%Y-%m-%d') as politicalBirthday\n" +
            "FROM\n" +
            "\tt_user u\n" +
            "\tLEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id \n" +
            "\tAND uoc.is_employee = 1\n" +
            "\tLEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE o.`status` = 1\n" +
            "  AND u.`status` = 1\n" +
            "\tAND MONTH(u.joining_time) = MONTH(SYSDATE())\n" +
            "\tAND DAY(u.joining_time) = DAY(SYSDATE())\n" +
            "  and o.org_type_child in(10280301,10280310,10280322,10280303,10280308,10280311,10280318,10280304,10280309,10280314,10280315,10280319)\n" +
            "\tAND (o.organization_id = #{orgId} or o.org_level LIKE CONCAT('%-',#{orgId},'-%'))" +
            "</script>")
    List<UserInfo> getUserPoliticalBirthday(@Param("orgId") Long orgId);

    @Select("<script>" +
            "select count( DISTINCT u.user_id ) \n" +
            "  from t_user u\n" +
            "  LEFT JOIN t_user_org_corp uoc on u.user_id = uoc.user_id \n" +
            "  LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            " WHERE o.`status` = 1 AND u.`status` = 1 \n" +
            "   AND u.is_visitor <![CDATA[ <> ]]> 1 \n" +
            "   AND uoc.is_employee = 1 \n" +
            "   AND o.org_type = 102803 \n" +
            "   AND o.org_level like CONCAT('%-',#{orgId},'-%') \n" +
            "   AND o.org_type_child <![CDATA[ <> ]]> 10280306 \n" +
            "<if test=\"orgList != null and orgList.size > 0\">" +
            "<foreach collection=\"orgList\" item=\"id\" open=\" \" separator=\" \" close=\" \">" +
            " AND o.org_level NOT LIKE CONCAT( '%-', #{id}, '-%' ) \n" +
            " AND o.organization_id <![CDATA[<>]]> #{id} \n" +
            "</foreach>" +
            "</if>" +
            "</script>")
    Integer findUserTotal(@Param("orgId") Long orgId, @Param("orgList") List<Long> orgList);

    @Select("<script>" +
            "select DISTINCT u.user_id \n" +
            "  from t_user u\n" +
            "  LEFT JOIN t_user_org_corp uoc on u.user_id = uoc.user_id \n" +
            "  LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            " WHERE o.`status` = 1 AND u.`status` = 1 \n" +
            "   AND u.is_visitor <![CDATA[ <> ]]> 1 \n" +
            "   AND uoc.is_employee = 1 \n" +
            "   AND o.org_type = 102803 \n" +
            "<if test=\"orgList != null and orgList.size > 0\">" +
            "<foreach collection=\"orgList\" item=\"id\" open=\" \" separator=\" \" close=\" \">" +
            " AND (o.org_level LIKE CONCAT( '%-', #{id}, '-%' ) OR o.organization_id = #{id}) \n" +
            "</foreach>" +
            "</if>" +
            "</script>")
    List<Long> findTestOrgUsers(@Param("orgList") List<Long> orgList);

    @Select("<script>" +
            "select u.user_id as userId, u.`name`, u.political_type as politicalType, u.gender, " +
            "  u.education, u.age, u.position, u.birthplace, u.joining_time as joiningTime,\n" +
            " uoc.organization_id as orgId, o.name as orgName, o.short_name as shortName \n" +
            "  from t_user u\n" +
            "  LEFT JOIN t_user_org_corp uoc on u.user_id = uoc.user_id \n" +
            "  LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id\n" +
            " WHERE o.`status` = 1\n" +
            "   AND u.`status` = 1\n" +
            "   AND u.is_visitor <![CDATA[ <> ]]> 1\n" +
            "   AND uoc.is_employee = 1 \n" +
            "   AND o.org_type = 102803 \n" +
            "   AND o.org_level like CONCAT(\"%-\",#{orgId},\"-%\") \n" +
            "<if test=\"noStaOrgTypeChild != null and noStaOrgTypeChild.size != 0 \"> " +
            "   AND o.org_type_child not in " +
            "<foreach collection=\"noStaOrgTypeChild\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "<if test=\"excludeOrgIds != null and excludeOrgIds.size != 0 \"> " +
            "<foreach collection=\"excludeOrgIds\" index=\"index\" item=\"item\">\n" +
            "   AND o.organization_id != #{item} AND o.org_level not like concat('%-', #{item}, '-%') " +
            "</foreach>" +
            "</if>" +
            "</script>")
    List<UserEntity> findNewUserList(@Param("orgId") Long orgId,
                                     @Param("excludeOrgIds") List<Long> excludeOrgIds,
                                     @Param("noStaOrgTypeChild") List<Integer> noStaOrgTypeChild);

    @Select("<script>" +
            "SELECT\n" +
            "\tDATE_FORMAT( uoc.joining_time, '%Y' ) AS item,\n" +
            "\tCOUNT( DISTINCT u.user_id ) AS total \n" +
            "FROM\n" +
            "\tt_user u\n" +
            "\tLEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            "\tLEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tu.`status` = 1 \n" +
            "\tAND o.`status` = 1 \n" +
            "\tAND uoc.is_employee = 1 \n" +
            "\tAND u.political_type IN ( 1, 5, 17, 18 ) \n" +
            "\tAND o.region_id = #{regionId} \n" +
            "\tAND o.org_level like CONCAT(\"%-\",#{orgId},\"-%\") \n" +
            "<if test=\"yearList != null and yearList.size != 0 \"> AND \n" +
            " <foreach collection=\"yearList\" index=\"index\" item=\"item\" open=\"(\" separator=\"or\" close=\")\"> " +
            " uoc.joining_time LIKE CONCAT(#{item},'%') \n" +
            " </foreach> " +
            "</if>" +
            "GROUP BY item \n" +
            "ORDER BY DATE_FORMAT( uoc.joining_time, '%Y' ) ASC" +
            "</script>")
    List<DevelopForm> develop(@Param("orgId") Long orgId, @Param("regionId") Long regionId,
                              @Param("yearList") List<Integer> yearList);

    @Select("<script>" +
            "SELECT count( DISTINCT uopm.user_id ) \n" +
            "FROM t_user_org_period AS uop \n" +
            "\tLEFT JOIN t_user_org_period_member AS uopm ON uop.period_id = uopm.period_id \n" +
            "WHERE uop.is_delete = 2 \n" +
            "\tAND uop.is_valid = 1 \n" +
            "\tAND uopm.is_delete = 2 \n" +
            "<if test=\"type != null and type == 1\">" +
            "AND uopm.position_name LIKE CONCAT('%','书记','%') AND uopm.position_name NOT LIKE CONCAT('%','副书记','%') \n" +
            "</if>" +
            "<if test=\"type != null and type == 2\">" +
            "AND uopm.position_name LIKE CONCAT('%','副书记','%') \n" +
            "</if>" +
            "<if test=\"type != null and type == 3\">" +
            "AND uopm.position_name NOT LIKE CONCAT('%','书记','%') AND uopm.position_name NOT LIKE CONCAT('%','副书记','%') \n" +
            "</if>" +
            "</script>")
    Integer findPartyLeaderTotal(@Param("regionId") Long regionId, @Param("type") Integer type);

    @Select("<script>" +
            "SELECT COUNT( DISTINCT u.user_id ) \n" +
            "FROM t_user u\n" +
            "\tLEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id \n" +
            "WHERE u.`status` = 1 \n" +
            "<if test=\"tagList != null and tagList.size != 0 \"> AND \n" +
            " <foreach collection=\"tagList\" index=\"index\" item=\"item\" open=\"(\" separator=\"or\" close=\")\"> " +
            " FIND_IN_SET(#{item}, uoc.tag_id ) \n" +
            " </foreach> " +
            "</if>" +
            "</script>")
    Integer fullPartyTimeTotal(@Param("regionId") Long regionId, @Param("tagList") List<String> tagList);

    @Select("<script>" +
            "<if test=\"paramList != null and paramList.size != 0 \"> \n" +
            "<foreach collection=\"paramList\" index=\"index\" item=\"form\" open=\"\" separator=\"UNION\" close=\"\"> " +
            "select sta_year as `year`, #{form.month} as `month`, ROUND(sum(month_#{form.month})/count(1)) AS score \n" +
            "from t_study_score where sta_year = #{form.year}" +
            " </foreach> \n" +
            "ORDER BY `year`, `month`" +
            "</if>" +
            "</script>")
    List<StudyForm.StudyMap> trendList(@Param("paramList") List<StudyForm.StudyMap> paramList);

    @Select("SELECT max(total_score) AS maxTotal,\n" +
            "\tROUND(sum(total_score)/count(1)) AS averTotal,\n" +
            "\tMAX(year_score) AS yearMaxTotal,\n" +
            "\tROUND(sum(year_score)/count(1)) AS yearAverTotal \n" +
            "FROM t_study_score \n" +
            "WHERE `sta_year` = #{year}")
    StudyForm.Study studyScore(@Param("year") Integer year);

    @Select("SELECT MAX( CAST( total_time AS SIGNED ) ) AS maxTotal,\n" +
            "\tROUND(sum(total_time)/count(1)) AS averTotal,\n" +
            "\tMAX( CAST( year_time AS SIGNED ) ) AS yearMaxTotal,\n" +
            "\tROUND(sum(year_time)/count(1)) AS yearAverTotal \n" +
            "FROM t_study_time \n" +
            "WHERE `sta_year` = #{year}")
    StudyForm.Study studyTime(@Param("year") Integer year);


    @Select("<script>" +
            "SELECT u.user_id as userId, u.name as username, c.`name` corpName\n" +
            "FROM t_user u\n" +
            "LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id \n" +
            "AND uoc.is_employee = 1\n" +
            "LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id \n" +
            "LEFT JOIN t_organization c ON c.organization_id = o.owner_id\n" +
            "where u.user_id in " +
            "<foreach collection=\"userIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "   #{item} " +
            "</foreach>" +
            "GROUP BY u.user_id" +
            "</script>")
    List<UserCorpInfo> getUserCorp(@Param("userIds") List<Long> userIds);

    @Select("<script>" +
            "SELECT ( CASE u.sequence WHEN 1 THEN '卷烟营销' WHEN 2 THEN '烟叶生产' WHEN 3 THEN '专卖管理' WHEN 4 THEN '综合管理' ELSE '未知' END ) AS `name`,\n" +
            "\tcount(DISTINCT u.user_id) AS `value` \n" +
            "FROM t_user u \n" +
            "  LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id \n" +
            "  LEFT JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1\n" +
            "  AND uoc.is_employee = 1\n" +
            "  AND u.political_type IN ( 1, 5, 17, 18 )\n" +
            " and o.organization_id not in(${excludeOrgIds}) " +
            "  AND (o.organization_id =  #{orgId} OR o.org_level LIKE '%-${orgId}-%') \n" +
            " GROUP BY u.sequence " +
            "</script>")
    List<KeyValVo> orgOverviewSequenceDistributed(@Param("orgId") Long orgId, @Param("excludeOrgIds") String excludeOrgIds);

    @Select("<script>SELECT u.`name` as userName,o.name as orgName, u.phone, u.phone_secret phoneSecret, u.user_id as userId,o.organization_id as orgId \n" +
            "FROM t_user u \n" +
            "LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id \n" +
            "LEFT JOIN t_organization o  ON o.organization_id = uoc.organization_id \n" +
            "WHERE u.`status` = 1 AND o.`status` = 1 AND uoc.is_employee = 1 \n" +
            "AND u.political_type IN (1, 5, 17, 18 ) and o.region_id = #{regionId} \n" +
            "<if test=\"excludeOrgIds != null and excludeOrgIds.size > 0\"> " +
            "and o.organization_id NOT IN \n" +
            " <foreach collection=\"excludeOrgIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            " #{item} " +
            " </foreach> " +
            "</if>" +
            "<if test=\"orgList != null and orgList.size > 0\"> AND " +
            " <foreach collection=\"orgList\" index=\"index\" item=\"item\" open=\"(\" separator=\"OR\" close=\")\"> " +
            " o.organization_id = #{item} OR o.org_level LIKE '%-${item}-%' \n" +
            " </foreach> " +
            "</if>" +
            "</script>")
    List<UserInfoBase> unitUserList(@Param("orgList") List<Long> orgList,
                                    @Param("excludeOrgIds") List<Long> excludeOrgIds,
                                    @Param("regionId") Long regionId);


    /**
     * 查询单位的经纬度
     *
     * @param excludeOrgIds 需要排除的组织
     * @return list of map
     */
    @Select("<script>" +
            "SELECT\n" +
            "\ta.owner_id AS unit_id,\n" +
            "\ta.organization_id AS uid,\n" +
            "\ta.longitude AS lng,\n" +
            "\ta.latitude AS lat \n" +
            "FROM\n" +
            "\t( SELECT * FROM t_organization WHERE `status` = 1 \n" +
            "<if test=\"excludeOrgIds != null and excludeOrgIds.size != 0 \"> " +
            "<foreach collection=\"excludeOrgIds\" index=\"index\" item=\"item\">\n" +
            "   AND organization_id != #{item} AND org_level not like concat('%-', #{item}, '-%') " +
            "</foreach>" +
            "</if>" +
            "\tAND owner_id IS NOT NULL ORDER BY LENGTH( org_level ) ASC LIMIT 1000000 ) a \n" +
            "GROUP BY\n" +
            "\ta.owner_id \n" +
            "ORDER BY\n" +
            "\ta.owner_id" +
            "</script>")
    List<Map<String, Object>> unitLngAndLat(@Param("excludeOrgIds") List<Long> excludeOrgIds);

    @Select("select user_id from t_user where name = #{userName} and phone = #{phone}")
    Long getUserIdByPhone(@Param("userName") String userName, @Param("phone") String phone);

    @Select("select IF(political_type in (1,5),1,2) from t_user where user_id = #{userId}")
    Integer getIsPartyMember(@Param("userId") Long userId);

    @Select("SELECT\n" +
            "\to.`name`\n" +
            "FROM\n" +
            "\tt_user_org_corp uop\n" +
            "\tLEFT JOIN t_organization o ON uop.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tuop.is_employee = 1 \n" +
            "\tAND user_id = #{userId}\n")
    String getBranch(@Param("userId") Long userId);

    @Select("SELECT\n" +
            "\to.organization_id\n" +
            "FROM\n" +
            "\tt_user_org_corp uop\n" +
            "\tLEFT JOIN t_organization o ON uop.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tuop.is_employee = 1 \n" +
            "\tAND user_id = #{userId}\n")
    Long getBranchId(@Param("userId") Long userId);

    @Select("SELECT\n" +
            "\to.`name`\n" +
            "FROM\n" +
            "\tt_user_org_corp uop\n" +
            "\tLEFT JOIN t_organization o ON uop.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\to.org_type = 102807 and o.parent_id != 2 \n" +
            "\tAND user_id = #{userId}\n")
    List<String> getDepartment(@Param("userId") Long userId);

    @Select("SELECT\n" +
            "\to.organization_id\n" +
            "FROM\n" +
            "\tt_user_org_corp uop\n" +
            "\tLEFT JOIN t_organization o ON uop.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\to.org_type = 102807 and o.parent_id != 2 \n" +
            "\tAND user_id = #{userId}\n")
    List<Long> getDepartmentId(@Param("userId") Long userId);

    @Select("SELECT o.`name` FROM\t\n" +
            "(SELECT\n" +
            "\tuop.organization_id,\n" +
            "\to.owner_id\n" +
            "FROM\n" +
            "\tt_user_org_corp uop\n" +
            "\tLEFT JOIN t_organization o ON uop.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tuop.is_employee = 1\n" +
            "\tAND user_id = #{userId}) AS L\n" +
            "\tLEFT JOIN t_organization o ON o.organization_id = L.owner_id")
    List<String> getUnit(@Param("userId") Long userId);

    @Select("SELECT o.organization_id FROM\t\n" +
            "(SELECT\n" +
            "\tuop.organization_id,\n" +
            "\to.owner_id\n" +
            "FROM\n" +
            "\tt_user_org_corp uop\n" +
            "\tLEFT JOIN t_organization o ON uop.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tuop.is_employee = 1\n" +
            "\tAND user_id = #{userId}) AS L\n" +
            "\tLEFT JOIN t_organization o ON o.organization_id = L.owner_id")
    List<Long> getUnitId(@Param("userId") Long userId);

    @Select("SELECT o.organization_id FROM\t\n" +
            "(SELECT\n" +
            "\tuop.organization_id,\n" +
            "\to.owner_id\n" +
            "FROM\n" +
            "\tt_user_org_corp uop\n" +
            "\tLEFT JOIN t_organization o ON uop.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tuop.is_employee = 1\n" +
            "\tAND user_id = #{userId}) AS L\n" +
            "\tLEFT JOIN t_organization o ON o.organization_id = L.owner_id")
    Long getOrgId(@Param("userId") Long userId);

    @Select("SELECT u.user_id AS userId,u.`name`,u.phone,u.phone_secret AS phoneSecret,\n" +
            "\tu.cert_number AS certNumber,u.cert_number_secret AS certNumberSecret \n" +
            "FROM t_user AS u\n" +
            "\tLEFT JOIN t_user_org_corp AS uoc ON u.user_id = uoc.user_id \n" +
            "\tLEFT JOIN t_organization AS o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tu.`status` = 1 \n")
    List<UserInfoBase> findUserPlaintext();

    @Select("<script>" +
            "select user_id userId,organization_id orgId from t_user_org_corp where user_id in" +
            " <foreach collection=\"userList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            " #{item.userId}" +
            " </foreach> " +
            " and is_employee=1\n" +
            "union all\n" +
            "select a1.userId,o.organization_id as orgId from (select user_id userId,organization_id orgId from t_user_org_corp where user_id in" +
            " <foreach collection=\"userList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            " #{item.userId}" +
            " </foreach> " +
            " and is_employee=1) a1 \n" +
            "join  t_organization o on a1.orgId=o.parent_id  and o.status=1 and o.org_type=102803 and o.org_type_child=10280306 " +
            //之前在某个党小组，后来又在该党小组下把他删了，所以关系不存在，就不能去t_user_org_group_member进行查询
//           "select m1.user_id userId,group1.link_org_id orgId from t_user_org_group_member m1\n" +
//           "join t_user_org_group group1 on m1.org_group_id=group1.org_group_id\n" +
            "</script>")
    List<DualLifeDetail> queryOrgAndGroup(@Param("userList") List<DualLifeDetail> userList);

    @Select("SELECT\n" +
            "\to.organization_id\n" +
            "FROM\n" +
            "\tt_user_org_corp uop\n" +
            "\tLEFT JOIN t_organization o ON uop.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tuop.is_employee = 1 \n" +
            "\tAND user_id = #{userId}\n")
    List<Long> getBranchIdList(@Param("userId") Long userId);

    @Select("<script>" +
            " select uoc.user_id as userId, o.organization_id as orgId, o.`name`\n" +
            " from t_user_org_corp uoc JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            " where o.org_type = 102807\n" +
            "  and uoc.user_id = #{userId}\n" +
            "  and uoc.region_id = #{regionId}" +
            "</script>")
    List<UserOrgInfo> getByUserAndOrg(@Param("userId") Long userId,
                                      @Param("regionId") Long regionId);

    /**
     * 根据组织id 查询当前组织所有党员（包括党员）
     * 统计之前进入系统人的信息 比如2023年只统计2022年进入系统的人员信息
     *
     * @param regionId
     * @return
     */
    @Select("""
             SELECT DISTINCT
              u.user_id as userId,
              u.`name` as userName,o.`name` as orgName,o.organization_id orgId,o.region_id as regionId 
            FROM
              t_user u
              LEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id
              LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id 
            WHERE
              u.`status` = 1 
              AND uoc.is_employee = 1 
              AND o.`status` = 1 
              AND o.`region_id` = #{regionId}
              AND o.organization_id=#{orgId}
              AND year(u.create_time) < #{year}
              AND o.org_type = 102803 
              AND u.political_type IN (1, 5, 17, 18 )
             """
    )
    List<UserInfoBase> getAllUserInfoByOrgId(@Param("regionId") Long regionId,
                                             @Param("orgId") Long orgId,
                                             @Param("year") Integer year);


    /**
     * 根据组织id 查询当前组织所有党员(不包括发展党员)
     *
     * @param regionId
     * @return
     */
    @Select("""
             SELECT DISTINCT
              u.user_id as userId,
              u.`name` as userName,o.`name` as orgName,o.organization_id orgId,o.region_id as regionId 
            FROM
              t_user u
              LEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id
              LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id 
            WHERE
              u.`status` = 1 
              AND uoc.is_employee = 1 
              AND o.`status` = 1 
              AND o.`region_id` = #{regionId}
              AND o.organization_id=#{orgId}
              AND ( year(uoc.join_party_time) < #{year} or join_party_time is  null )
              AND o.org_type = 102803 
              AND u.political_type IN (1,17 )
             """
    )
    List<UserInfoBase> getAllUserInfoByOrgIdExcludeDeveloper(@Param("regionId") Long regionId,
                                                             @Param("orgId") Long orgId,
                                                             @Param("year") Integer year);

    @Select("SELECT\n" +
            "\tcount( u.user_id ) \n" +
            "FROM\n" +
            "\tt_user u\n" +
            "\tLEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            "\tLEFT JOIN t_organization o ON o.organization_id = uoc.organization_id \n" +
            "WHERE\n" +
            "\tu.`status` = 1 \n" +
            "\tAND o.`status` = 1 \n" +
            "\tAND uoc.is_employee = 1 \n" +
            "\tAND u.political_type = 1 \n" +
            "\tAND (\n" +
            "\to.organization_id = #{orgId} \n" +
            "\tOR o.org_level LIKE '%-${orgId}-%')")
    Integer getOrgIdAllMembers(@Param("orgId") Long orgId);


    @Select("<script>SELECT\n" +
            "\ttu.political_type politicalType,\n" +
            "\ttu.gender gender,\n" +
            "\ttu.education education,\n" +
            "\ttu.joining_time joiningTime,\n" +
            "\ttu.age age \n" +
            "FROM\n" +
            "\tt_user tu\n" +
            "\tLEFT JOIN t_user_org_corp tuoc ON tu.user_id = tuoc.user_id\n" +
            "\tLEFT JOIN t_organization tor ON tuoc.organization_id = tor.organization_id \n" +
            "WHERE\n" +
            "\ttuoc.is_employee = 1 \n" +
            "\tAND (\n" +
            "\ttor.org_level LIKE '%-${orgId}-%' \n" +
            "\tOR tor.organization_id = #{orgId}) GROUP BY tu.user_id </script>")
    List<UserEntity> getAllUserByOrgId(@Param("orgId") Long orgId);

}
