package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import com.goodsogood.ows.model.vo.user.UserDssVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 人员快照表
 * <AUTHOR>
 */
@Mapper
@Repository
public interface UserSnapshotMapper extends MyMapper<UserSnapshotEntity> {

    @Select("<script>" +
            "SELECT\n" +
            "  DISTINCT us.user_id as userId,\n" +
            "  us.user_name as `name`,\n" +
            "  us.phone_secret as phoneSecret,\n" +
            "  us.org_id as branchId,\n" +
            "  ( SELECT o.`name` FROM t_organization o WHERE o.organization_id = us.org_id ) as branchName,\n" +
            "  ( SUBSTRING_INDEX( SUBSTRING_INDEX( us.org_level, '-', 5 ), '-',- 1 )) as partyId,\n" +
            "  ( SELECT o1.`name` FROM t_organization o1 WHERE o1.organization_id = partyId ) as partyName \n" +
            "FROM\n" +
            "  t_user_snapshot us \n" +
            "WHERE\n" +
            "   us.date_month = #{dateMonth} \n" +
            "  AND us.political_type IN ( 1, 5, 17, 18 ) \n" +
            "  AND us.`status` = 1\n" +
            "  AND (us.org_id = #{orgId} or us.org_level like CONCAT( '%-', #{orgId}, '-%' ))" +
            "<if test=\"userIds != null and userIds.size != 0\">" +
            "   AND  us.user_id in " +
            "   <foreach collection=\"userIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "   #{item}" +
            "   </foreach>" +
            "</if>" +
            "</script>")
    List<UserDssVo> getHistoryUserList(@Param("orgId") Long orgId,
                                       @Param("dateMonth") String dateMonth,
                                       @Param("userIds") List<Long> userIds);

    /**
     * 查询某个月份下所有非离退休党员
     *
     * @param regionId
     * @param dateMonth
     * @param retireCode
     * @param politicalCode
     * @return
     */
    @Select("  SELECT\n" +
            "    user_id \n" +
            "  FROM\n" +
            "    t_user_snapshot \n" +
            "  WHERE\n" +
            "    STATUS = 1 \n" +
            "    AND region_id = #{regionId} \n" +
            "     AND ( position_code NOT IN(${retireCode}) OR position_code IS NULL OR position_code = '' ) \n" +
            "     AND political_type IN( ${politicalCode} ) \n" +
            "    AND date_month = '${dateMonth}' ")
    Set<Long> getNotRetiredUserIds(@Param("regionId") Long regionId,
                                   @Param("dateMonth") String dateMonth,
                                   @Param("retireCode") String retireCode,
                                   @Param("politicalCode") String politicalCode);

    /**
     * 获取组织下的所有非离退休人员（包含下级）
     *
     * @param orgId
     * @param dateMonth
     * @param regionId
     * @param retireCode
     * @param politicalCode
     * @return
     */
    @Select("<script> SELECT " +
            " us.user_id " +
            " FROM " +
            " t_user_snapshot us WHERE us.date_month = '${dateMonth}' " +
            " <if test=\"orgId != null\"> AND (us.org_id = #{orgId} OR us.org_level LIKE CONCAT( '%-', #{orgId}, '-%' )) </if>" +
            " <if test=\"orgId == 999\"> AND us.org_level not LIKE CONCAT( '%--', #{orgId}, '-%' ) </if>" +
            " AND us.region_id = #{regionId} " +
            " AND ( us.position_code NOT IN(${retireCode}) OR us.position_code IS NULL OR us.position_code = '' ) " +
            " AND us.political_type IN( ${politicalCode} ) " +
            " AND us.status = 1 </script>")
    Set<Long> getNotRetiredUserIdsByOrg(@Param("orgId") Long orgId,
                                        @Param("dateMonth") String dateMonth,
                                        @Param("regionId") Long regionId,
                                        @Param("retireCode") String retireCode,
                                        @Param("politicalCode") String politicalCode);
}
