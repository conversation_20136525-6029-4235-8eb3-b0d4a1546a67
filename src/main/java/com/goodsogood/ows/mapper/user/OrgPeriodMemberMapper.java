package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.user.OrgPeriodMemberEntity;
import com.goodsogood.ows.model.vo.dataCockpit.UserOrgPeriodVO;
import com.goodsogood.ows.model.vo.user.OrgUserPeriodForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface OrgPeriodMemberMapper extends MyMapper<OrgPeriodMemberEntity> {

    @Select("<script>" +
            "select distinct pm.user_id as userId,pm.position_name as positionName,pm.user_name as userName\n" +
            "\tfrom t_user_org_period_member pm \n" +
            "\tleft join (select period1.period_id,period1.org_id from(\n" +
            "\tselect period_id,org_id,start_time,end_time,create_time,update_time from t_user_org_period where is_delete = 2 \n" +
            "\tand start_time <![CDATA[ <= ]]> now() and end_time <![CDATA[ >= ]]> now() \n" +
            "\tORDER BY start_time desc,end_time desc,period_id desc)\n" +
            "\tas period1 group by period1.org_id order by org_id) as period on  pm.period_id = period.period_id\n" +
            "\tleft join t_user u on u.user_id = pm.user_id\n" +
            "\twhere pm.is_delete = 2\n" +
            "\tand period.org_id = #{orgId}\n" +
            "\tand \n" +
            "\t<foreach collection=\"positionList\" item=\"position\" open=\"(\" separator=\"or\" close=\")\">" +
            "\tpm.position_name like CONCAT('%',#{position},'%') \n" +
            "</foreach>" +
            "\tand period.period_id is not null\n" +
            "\tand u.`status`= 1" +
            "</script>")
    List<OrgUserPeriodForm> findOrgPeriodUser(@Param("orgId") Long orgId,
                                              @Param("positionList") List<String> positionList);


    /**
     * 工委管理员 查看应换届的党委
     * @param orgId
     * @param today
     * @param thisMonth
     * @return
     */
//    @Select("SELECT SUM(num)\n" +
//            "FROM (\n" +
//            "\tselect o1.organization_id orgId,o1.`name` orgName,count(period.org_id) num,period.parent_id \n" +
//            "\tparentId,period.short_name shortName from t_organization o1 left join (select distinct a.org_id,a.expire_date \n" +
//            "\texpireDate,org_level,parent_id,short_name from (select org_id,expire_date,org_level,parent_id,short_name \n" +
//            "\tfrom t_user_org_period_result where create_date= #{today} and (org_id=#{orgId} or \n" +
//            "\torg_level like concat('%-', #{orgId}, '-%')) and expire_date is not null and expire_date like concat(#{thisMonth},'%') \n" +
//            "\t) as a GROUP BY a.org_id) as period on period.org_level like concat('%-',o1.organization_id,'-%') \n" +
//            "\tor period.org_id=o1.organization_id where o1.parent_id=#{orgId} and o1.`status`=1 group by o1.organization_id \n" +
//            "\torder by num desc\n" +
//            ")as L")
    @Select("SELECT count(1) FROM\n" +
            "(\n" +
            "\tselect to1.organization_id,to1.`name`\n" +
            "\tfrom t_organization to1\n" +
            "\twhere to1.parent_id=#{orgId} and to1.`status`=1 and to1.org_type=102803 \n" +
            "\tand to1.organization_id in (select org_id from t_user_org_period_result \n" +
            "\twhere create_date=#{today} and batch=0 and expire_date is not null)\n" +
            ") as L")
    Integer lastMonthPartyChange(@Param("orgId") Long orgId,@Param("today") String today,
                                 @Param("thisMonth")  String thisMonth);

    /**
     * 工委管理员 查看应换届的支部
     * @param orgId
     * @param today
     * @param thisMonth
     * @return
     */
    @Select("SELECT\n" +
            "\tcount( c.org_id ) \n" +
            "FROM\n" +
            "\t(SELECT\n" +
            "\t\tb.org_id \n" +
            "\tFROM\n" +
            "\t\t(SELECT\n" +
            "\t\t\ta.org_id,\n" +
            "\t\t\ta.org_name orgName,\n" +
            "\t\t\ta.expire_date expireDate \n" +
            "\t\tFROM\n" +
            "\t\t\t(\n" +
            "\t\t\tSELECT\n" +
            "\t\t\t\torg_id,\n" +
            "\t\t\t\texpire_date,\n" +
            "\t\t\t\torg_name \n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tt_user_org_period_result \n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\thas_tag = 1 \n" +
            "\t\t\t\tAND create_date = #{today}\n" +
            "\t\t\t\tAND expire_date IS NOT NULL \n" +
            "\t\t\t\tAND ( org_id = #{orgId}\n" +
            "\t\t\t\tOR org_level LIKE concat( '%-', #{orgId}, '-%' )) \n" +
            "\t\t\tORDER BY\n" +
            "\t\t\t\tresult_id DESC \n" +
            "\t\t\t) AS a \n" +
            "\t\tGROUP BY\n" +
            "\t\t\ta.org_id \n" +
            "\t\t) AS b \n" +
            "\tWHERE\n" +
            "\tb.expireDate LIKE concat( #{thisMonth},'%')\n" +
            "\t) AS c")
    Integer thisMonthBranchChange(@Param("orgId") Long orgId,@Param("today") String today,
                                  @Param("thisMonth")  String thisMonth);

    /**
     * 工委管理员查看单位组织届次到期未换届 截止当天
     * @param orgId
     * @return
     */
    @Select("SELECT count(1)\n" +
            "FROM (\n" +
            "\t\tselect period2.period_id,period2.org_id\n" +
            "\t\tfrom (select uop.period_id,uop.org_id\n" +
            "\t\t from t_user_org_period uop\n" +
            "\t\t where uop.end_time <= date_sub(date_sub(date_format(now(),'%y-%m-%d'),interval extract(day from now()) day),interval 0 month)\n" +
            "\t\t and uop.is_delete=2\n" +
            "\t\t order by uop .end_time desc,uop.start_time desc,uop.period_id desc) as period2\n" +
            "\t\t left join t_organization to1 on to1.`status`=1 and to1.org_type=102803 " +
            " and to1.parent_id=#{orgId} and to1.organization_id=period2.org_id\n" +
            "\t\twhere period2.org_id not in (select distinct org_id from t_user_org_period " +
            " where is_delete=2 and end_time >= CONCAT(DATE_FORMAT(now(),'%Y-%m-'),'01'))\n" +
            "\t\tand to1.org_id is not null\n" +
            "\t\tgroup by period2.org_id\n" +
            ") as L\n")
    Integer getNotPeriodOrg(Long orgId);

    @Select("select REPLACE(GROUP_CONCAT(CONCAT(to1.`short_name`,' ',pm.position_name)),',','|')\n" +
            "from t_user tu1 \n" +
            "inner join t_user_org_period_member pm on tu1.user_id=pm.user_id and tu1.`status`=1 and pm.is_delete=2\n" +
            "inner join (select period1.* from (select period_id,org_id,start_time,end_time,discipline_inspection_committee from t_user_org_period\n" +
            "where is_delete=2 and (year(start_time)<=#{year} and year(end_time)>=#{year})\n" +
            "order by start_time DESC,period_id DESC) as period1 group by period1.org_id) period on pm.period_id=period.period_id\n" +
            "inner join t_organization to1 on period.org_id=to1.organization_id and to1.`status`=1 and to1.region_id=#{regionId}\n" +
            "where tu1.user_id=#{userId}\n" +
            "group by tu1.user_id")
    String getPartyPosition(@Param("year") Integer year,@Param("regionId") Long regionId,@Param("userId") Long userId);

    @Select("<script>SELECT\n" +
            "\tcount(period_member_id) \n" +
            "FROM\n" +
            "\tt_user_org_period_member tuopm\n" +
            "\tLEFT JOIN t_user_org_period tuop ON tuopm.period_id = tuop.period_id \n" +
            "WHERE\n" +
            "\ttuop.is_valid = 1 \n" +
            "\tAND tuop.is_delete = 2 \n" +
            "\tAND tuop.org_id IN " +
            "<foreach collection='orgIds' item='orgId' open='(' separator=',' close=')'>#{orgId}</foreach> \n" +
            "\tAND tuopm.position_name LIKE '%书记%' \n" +
            "\tAND tuopm.position_name NOT LIKE '%副书记%'</script>")
    Integer getOrgPeriodMemberBoos1(@Param("orgIds") List<Long> orgIds);

    @Select("<script>SELECT\n" +
            "\tuser_name AS userName,tuop.start_time AS startTime,tuop.end_time AS endTime \n" +
            "FROM\n" +
            "\tt_user_org_period_member tuopm\n" +
            "\tLEFT JOIN t_user_org_period tuop ON tuopm.period_id = tuop.period_id \n" +
            "WHERE\n" +
            "\ttuop.is_valid = 1 \n" +
            "\tAND tuop.is_delete = 2 \n" +
            "\tAND tuop.org_id = #{orgId} \n" +
            "\tAND tuopm.position_name LIKE '%书记%' \n" +
            "\tAND tuopm.position_name NOT LIKE '%副书记%'</script>")
    UserOrgPeriodVO getMyOrgPeriodMemberBoos1(@Param("orgId") Long orgId);

    @Select("<script>SELECT\n" +
            "\ttuopm.user_name AS userName\n" +
            "FROM\n" +
            "\tt_user_org_period_member tuopm\n" +
            "\tLEFT JOIN t_user_org_period tuop ON tuopm.period_id = tuop.period_id \n" +
            "WHERE\n" +
            "\ttuop.is_valid = 1 \n" +
            "\tAND tuop.is_delete = 2 \n" +
            "\tAND tuop.org_id = #{orgId} \n" +
            "\tAND (tuopm.position_code Like '%43%' OR tuopm.position_code Like '%44%' OR " +
            "tuopm.position_code Like '%45%' OR tuopm.position_code Like '%46%' OR " +
            "tuopm.position_code Like '%47%' OR tuopm.position_code Like '%48%' OR " +
            "tuopm.position_code Like '%49%' ) </script>")
    List<OrgPeriodMemberEntity> getMyOrgPeriodOtherMember(@Param("orgId") Long orgId);
}
