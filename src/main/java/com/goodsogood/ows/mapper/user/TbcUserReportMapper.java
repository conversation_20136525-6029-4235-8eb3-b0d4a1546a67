package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.vo.tbc.OrganizationLifeForm;
import com.goodsogood.ows.model.vo.tbc.TbcOrganBranchDetailForm;
import com.goodsogood.ows.model.vo.tbc.PartyOrganizationForm;
import com.goodsogood.ows.model.vo.tbc.TbcOrganNumDetailForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ruoyu
 * @date : 2020/12/28
 */
@Repository
@Mapper
public interface TbcUserReportMapper {

    @Select("select to1.organization_id organizationId,to1.`name`,to1.parent_id parentId,to1.seq,to1.org_type_child orgTypeChild\n" +
            "from t_organization to1  \n" +
            "where to1.status=1 and to1.org_level like concat('%-',#{orgId},'-%')\n" +
            "order by to1.seq desc")
    List<OrganizationEntity> getOrganizationListBySeq(@Param("orgId") Long orgId);


    /**
     * 一次查询本月到期 6月内到期  已到期未换届数据  届次状态 1:本月到期  2:6个月内到期  3:已到期未换届 4:已设置
     *
     * @return
     */
    @Select("select pr.org_id orgId,pr.org_name orgName,to1.user_num nums,concat(date_format(p1.start_time,'%Y年%m月%d日'),' 至 ',date_format(p1.end_time,'%Y年%m月%d日')) nowPeriod,\n" +
            "pr.is_create_period isCreatePeriod,if(pr.batch=0,1,2) type\n" +
            "from t_user_org_period_result pr \n" +
            "inner join t_organization to1 on pr.org_id=to1.organization_id and to1.status=1 \n" +
            "inner join t_user_org_period p1 on pr.period_id=p1.period_id and p1.is_delete=2\n" +
            "where pr.expire_date is not null and pr.create_date=#{nowDate}\n" +
            "and pr.org_type_child in(${orgType}) and (pr.org_id=#{orgId} or pr.org_level like concat('%-',#{orgId},'-%')) \n" +
            "union all \n" +
            "select p1.org_id orgId,to1.name orgName,to1.user_num nums,period.nowPeriod,2 isCreatePeriod,3 type\n" +
            "from t_user_org_period p1\n" +
            "inner join t_organization to1 on p1.org_id=to1.organization_id and to1.status=1\n" +
            "inner join (select period.org_id,concat(date_format(period.start_time,'%Y年%m月%d日'),' 至 ',date_format(period.end_time,'%Y年%m月%d日')) nowPeriod from (\n" +
            "select period.period_id,period.org_id,period.start_time,period.end_time,\n" +
            "case @org_id \n" +
            "when period.org_id \n" +
            "then @sort:=@sort+1\n" +
            "else @sort:=1\n" +
            "end as sort_id,\n" +
            "@org_id:=period.org_id \n" +
            "from t_user_org_period period,(select @org_id:=-1,@sort:=0) a\n" +
            "where period.is_delete=2 order by period.org_id,period.end_time desc,period.start_time desc) as period where period.sort_id=1) as period on period.org_id=p1.org_id\n" +
            "where p1.is_delete=2 and p1.end_time <= CONCAT(last_day(date_sub(now(),interval 1 MONTH)),' 23:59:59')\n" +
            "and (to1.organization_id=#{orgId} or to1.org_level like concat('%-',#{orgId},'-%')) and to1.org_type_child in(${orgType})\n" +
            "and p1.org_id not in (select distinct p2.org_id from t_user_org_period p2 where p2.is_delete=2 and DATE_FORMAT(p2.start_time,'%Y-%m-01 00:00:00') <= now() and p2.end_time >= DATE_FORMAT(now(),'%Y-%m-01 00:00:00'))\n" +
            "group by to1.organization_id" +
            " union all \n" +
            "select to1.organization_id orgId,to1.`name` orgName,to1.user_num nums,concat(date_format(p1.start_time,'%Y年%m月%d日'),' 至 ',date_format(p1.end_time,'%Y年%m月%d日')) nowPeriod,1 isCreatePeriod,4 type\n" +
            "from t_organization to1 \n" +
            "inner join t_user_org_period p1 on to1.organization_id=p1.org_id and p1.is_valid=1 and p1.is_delete=2\n" +
            "where to1.`status`=1 and to1.org_type_child in (${orgType}) and (to1.org_id=#{orgId} or to1.org_level like concat('%-',#{orgId},'-%'))")
    List<PartyOrganizationForm.TbcTbcOrganInnerPeriodForm> getExpireOrganPeriod(@Param("orgId") Long orgId, @Param("nowDate") String nowDate, @Param("orgType") String orgType);

    /**
     * 获取领导成员
     *
     * @param orgId
     * @return
     */
    @Select("select u1.user_id userId, u1.`name` name, to3.organization_id orgId, to3.short_name orgName \n" +
            "from t_organization to1\n" +
            "         inner join t_organization to2 on to1.owner_id = to2.organization_id and to2.status = 1\n" +
            "         inner join t_user_org_leader l1 on to2.organization_id = l1.org_id and l1.is_delete = 2\n" +
            "         inner join t_user u1 on l1.user_id = u1.user_id and u1.status = 1\n" +
            "         left join t_user_org_corp oc on u1.user_id = oc.user_id and oc.region_id = 3 and oc.is_employee = 1\n" +
            "         left join t_organization to3 on oc.organization_id = to3.organization_id and to3.`status` = 1\n" +
            "where to1.status = 1\n" +
            "  and to1.organization_id = #{orgId}"+
    " ORDER BY l1.is_head,l1.enter_time,convert(l1.`user_name` using gbk)")
    List<OrganizationLifeForm.OrganizationLifeLeaderMeetingActivityForm> getLeader(@Param("orgId") Long orgId);

    @Select("select \n" +
            "(select count(organization_id) from t_organization where status=1 and (organization_id=#{orgId} or org_level like CONCAT('%-',#{orgId},'-%')) and org_type_child in (${generalBranchChild})) as party1,\n" +
            "(select count(organization_id) from t_organization where status=1 and (organization_id=#{orgId} or org_level like CONCAT('%-',#{orgId},'-%')) and org_type_child in (${branchChild})) as party2,\n" +
            "(select count(to1.organization_id)\n" +
            "from t_organization to1 \n" +
            "inner join t_user_org_group og on to1.organization_id=og.org_id and og.is_delete=1 and og.create_date < now()\n" +
            "where to1.status=1 and (to1.organization_id=#{orgId} or to1.org_level like CONCAT('%-',#{orgId},'-%'))) as party3")
    PartyOrganizationForm.TbcPartyOrganizationPartyNumsForm getPartyNums(@Param("orgId") Long orgId, @Param("generalBranchChild") String generalBranchChild,
                                                                         @Param("branchChild") String branchChild);


    @Select("select count(distinct result.organization_id) from (\n" +
            "select to1.organization_id\n" +
            "from t_organization to1\n" +
            "inner join t_user_org_corp oc1 on to1.organization_id=oc1.organization_id and oc1.is_employee=1\n" +
            "inner join t_user tu1 on oc1.user_id=tu1.user_id and tu1.status=1 and tu1.political_type in (1,17)\n" +
            "where to1.status=1 and to1.region_id=3 and to1.org_type_child in (${orgTypeChild}) " +
            "and (to1.organization_id=#{orgId} or to1.org_level like '%-${orgId}-%') " +
            "group by to1.organization_id  having count(to1.organization_id)>=7) as result;")
    Integer getshouldPeriodTagNum(@Param("orgId") Long orgId, @Param("orgTypeChild") String orgTypeChild);

    @Select("select count(distinct result.organization_id) from (select to1.organization_id\n" +
            "from t_organization to1\n" +
            "inner join t_user_org_corp oc1 on to1.organization_id=oc1.organization_id and oc1.is_employee=1\n" +
            "inner join t_user tu1 on oc1.user_id=tu1.user_id and tu1.status=1 and tu1.political_type in (1,17)\n" +
            "inner join t_org_tag ot1 on to1.organization_id=ot1.org_id and ot1.tag_id=(select tag_id from t_tag where region_id=3 and tag_type=9)\n" +
            "where to1.status=1 and to1.region_id=3 and to1.org_type_child in (${orgTypeChild}) " +
            "and (to1.organization_id=#{orgId} or to1.org_level like '%-${orgId}-%') " +
            "group by to1.organization_id  having count(to1.organization_id)>=7) as result;")
    Integer getPeriodTagNum(@Param("orgId") Long orgId, @Param("orgTypeChild") String join);

    @Select("select result.* from (\n" +
            "select to1.organization_id orgId,to1.name orgName,count(to1.organization_id) nums,2 isCreatePeriodTag\n" +
            "from t_organization to1\n" +
            "inner join t_user_org_corp oc1 on to1.organization_id=oc1.organization_id and oc1.is_employee=1\n" +
            "inner join t_user tu1 on oc1.user_id=tu1.user_id and tu1.status=1 and tu1.political_type in (1,17)\n" +
            "where to1.status=1 and to1.region_id=3 and to1.org_type_child in (${orgTypeChild}) and (to1.organization_id=#{orgId} or to1.org_level like '%-${orgId}-%') \n" +
            "and to1.organization_id not in (select to1.organization_id\n" +
            "from t_organization to1\n" +
            "inner join t_user_org_corp oc1 on to1.organization_id=oc1.organization_id and oc1.is_employee=1\n" +
            "inner join t_user tu1 on oc1.user_id=tu1.user_id and tu1.status=1 and tu1.political_type in (1,17)\n" +
            "inner join t_org_tag ot1 on to1.organization_id=ot1.org_id and ot1.tag_id=(select tag_id from t_tag where region_id=3 and tag_type=9)\n" +
            "where to1.status=1 and to1.region_id=3 and to1.org_type_child in (${orgTypeChild}) and (to1.organization_id=#{orgId} or to1.org_level like '%-${orgId}-%') group by to1.organization_id  having count(to1.organization_id)>=7)\n" +
            "group by to1.organization_id  having count(to1.organization_id)>=7) as result;")
    List<PartyOrganizationForm.TbcTbcOrganInnerPeriodForm> getNotTagNum(@Param("orgId") Long orgId, @Param("orgTypeChild") String join);

    @Select("select to1.organization_id orgId,to1.name orgName,to1.user_num nums\n" +
            "from t_organization to1 \n" +
            "where to1.status=1 and to1.org_type_child in (${join}) \n" +
            "and (to1.organization_id=#{orgId} or to1.org_level like '%-${orgId}-%') \n" +
            "order by to1.user_num desc")
    List<PartyOrganizationForm.TbcPartyOrganizationTbcBranchForm> getPartyBranch(@Param("orgId") Long orgId, @Param("join") String join);

    @Select("select organization_id orgId,name orgName from t_organization where status=1 and (organization_id=#{orgId} or org_level like CONCAT('%-',#{orgId},'-%')) and org_type_child in (${orgTypeChild})")
    List<TbcOrganNumDetailForm> getOrgChildInfoByType(@Param("orgId") Long orgId, @Param("orgTypeChild") String orgTypeChild);

    @Select("select to1.organization_id orgId,to1.`name` orgName,og.org_group_name groupName \n" +
            "from t_organization to1 \n" +
            "inner join t_user_org_group og on to1.organization_id=og.org_id and og.is_delete=1 and og.create_date < now()\n" +
            "where to1.status=1 and (to1.organization_id=#{orgId} or to1.org_level like CONCAT('%-',#{orgId},'-%'))")
    List<TbcOrganNumDetailForm> getOrgChildInfoByTypeGroup(@Param("orgId") Long orgId);

    @Select("select to1.organization_id orgId,to1.name orgName,tu1.user_id userId,tu1.name userName,p1.op_value politicalName\n" +
            "from t_organization to1 \n" +
            "inner join t_user_org_corp oc1 on to1.organization_id=oc1.organization_id and oc1.is_employee=1\n" +
            "inner join t_user tu1 on oc1.user_id=tu1.user_id and tu1.status=1\n" +
            "left join t_option p1 on p1.code=1013 and tu1.political_type=p1.op_key\n" +
            "where to1.status=1 and to1.organization_id=#{orgId}\n" +
            "order by to1.user_num desc")
    List<TbcOrganBranchDetailForm> partyBranchDetail(@Param("orgId") Long orgId);
}
