package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.dss.LeaderInfo;
import com.goodsogood.ows.model.mongodb.dss.OrgBaseLeaderInfo;
import com.goodsogood.ows.model.mongodb.dss.OrgDssUser;
import com.goodsogood.ows.model.mongodb.dss.OrgPeriodInfo;
import com.goodsogood.ows.model.vo.meeting.MeetingPlanCountVO;
import com.goodsogood.ows.model.vo.sas.OrgPeriodWarningDetailForm;
import com.goodsogood.ows.model.vo.sas.PeriodFindOrgsResultForm;
import com.goodsogood.ows.model.vo.supervise.SupervisePeriodForm;
import com.goodsogood.ows.model.vo.user.RankPeriodIsEquleFrom;
import com.goodsogood.ows.service.rank.RemoteRuleWryService;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
@Mapper
public interface OrgPeriodMapper {

    @Select("  select o.organization_id orgId,p2.create_time periodCreateTime,p.cot periodSize,g.create_org_groups createOrgGroups" +
            ",if(p2.org_id is not null,1,2) hasPeriodTag " +
            "from t_organization o \n" +
            "LEFT JOIN (select org_id,start_time,count(org_id) cot from t_user_org_period where period_id in \n" +
            "( select period_id  from t_user_org_period where   is_delete=2 and start_time <= #{startTime} and end_time>= #{endTime} order by create_time)  group by org_id ) p on o.organization_id=p.org_id \n" +
            "LEFT JOIN (select count(*) create_org_groups,org_id   from t_user_org_group where is_delete=1  and create_date < #{currentTime} group by org_id) g  on o. organization_id=g.org_id\n" +
            " left join (select t1.org_id,t1.create_time\n" +
            "from t_org_tag t1\n" +
            "inner join t_tag t2 on t2.tag_type=9 and belong=2 and t2.`status`=1 and t1.tag_id=t2.tag_id group by t1.org_id) p2 on o.organization_id=p2.org_id " +
            "where \n" +
            "o.organization_id =#{orgId} ")
    PeriodFindOrgsResultForm findOrgs(@Param("orgId") Long orgId, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("currentTime") String currentTime);

    @Select(" <script> " +
            " select b.orgName,DATE_FORMAT(b.expireDate,'%Y-%m-%d') expireDate from (select   a.order_num,a.org_name orgName,a.expire_date expireDate from (select  order_num,org_id,expire_date,org_name from t_user_org_period_result where has_tag=1 and  create_date=#{createDate} and region_id=#{regionId} and expire_date is not null and (org_id=#{oid} or org_level like concat('%-',#{oid},'-%'))  ORDER BY order_num desc) as a GROUP BY a.org_id) as b " +
            "<if test=\"expireDate != null\"> where  b.expireDate like #{expireDate} </if>  " +
            " ORDER BY b.order_num  " +
            " </script> ")
    List<OrgPeriodWarningDetailForm> findExpireApp(@Param("createDate") String createDate, @Param("oid") Long oid, @Param("expireDate") String expireDate, @Param("regionId") Long regionId);

    /**
     * pageHelper 会吃掉order by 所以需要自己count　查询
     */
    @Select(" <script> " +
            "select  count(c.org_id) from (select b.org_id from (select   a.org_id,a.org_name orgName,a.expire_date expireDate from (select  org_id,expire_date,org_name from t_user_org_period_result where create_date=#{createDate} and expire_date is not null and (org_id=#{oid} or org_level like concat('%-',#{oid},'-%'))  ORDER BY result_id desc) as a GROUP BY a.org_id) as b  " +
            "<if test=\"expireDate != null\"> where  b.expireDate like #{expireDate} </if>  ) as c" +
            " </script> ")
    Long countFindExpireApp(@Param("createDate") String createDate, @Param("oid") Long oid, @Param("expireDate") String expireDate);

    /**
     * 查询上个月的换届支部数量
     *
     * @param orgIdList
     * @return
     */
    @Select("SELECT count(DISTINCT a.org_id) FROM t_user_org_period a, t_user_org_period b WHERE b.end_time LIKE '${time}%' AND a.start_time <= b.end_time\n" +
            "AND a.org_id = b.org_id AND a.period_id != b.period_id AND a.org_id IN (${orgIdList})")
    Long getChangeLastMonth(@Param(value = "time") String time, @Param("orgIdList") String orgIdList);


//    @Select("<script>" +
//            "select GROUP_CONCAT(if(m1.position_name like '%书记%' and m1.position_name not like '%副书记%',m1.user_name,null)) secretary,\n" +
//            "       GROUP_CONCAT(if(m1.position_name like '%副书记%',m1.user_name,null)) deputySecretary\n" +
//            "  from t_user_org_period_member m1 \n" +
//            " inner join (select period1.period_id from (select period_id,org_id,start_time,end_time,discipline_inspection_committee " +
//            "                                              from t_user_org_period\n" +
//            "                                             where is_delete = 2 " +
//            "                                               and org_id = #{orgId}\n" +
//            "                                               and year(start_time) <![CDATA[<=]]> #{year} and year(end_time) <![CDATA[>=]]> #{year}\n" +
//            "                                order by start_time DESC,period_id DESC) as period1 " +
//            " group by period1.org_id) as period on m1.period_id=period.period_id and m1.is_delete = 2\n" +
//            " inner join t_user u1 on u1.user_id = m1.user_id and u1.`status`=1\n" +
//            " group by m1.period_id" +
//            "</script>")

    /**
     * 获取组织的书记和副书记
     *
     * @param orgId
     * @param year
     * @return
     */
    @Select("select concat(DATE_FORMAT(period.start_time,'%Y.%m.%d'),'-',DATE_FORMAT(period.end_time,'%Y.%m.%d')) validTime,\n" +
            "GROUP_CONCAT(if(m1.position_name like '%书记%' and m1.position_name not like '%副书记%',m1.user_name,null)) partyLeader,\n" +
            "GROUP_CONCAT(if(m1.position_name like '%副书记%',m1.user_name,null)) partyLeaderSec,\n" +
            "GROUP_CONCAT(if(m1.position_name not like '%书记%',m1.user_name,null)) leaderOtherGroups\n" +
            "from t_user_org_period_member m1 \n" +
            "inner join (select period_id,start_time,end_time from (select period_id,org_id,start_time,end_time,discipline_inspection_committee \n" +
            "\t\tfrom t_user_org_period\n" +
            "\t\twhere is_delete = 2 \n" +
            "\t\tand org_id = #{orgId}\n" +
            "\t\tand year(start_time) <= #{year} and year(end_time) >= #{year}\n" +
            "\t\torder by start_time DESC,period_id DESC) as period1 \n" +
            "group by period1.org_id) as period on m1.period_id=period.period_id and m1.is_delete = 2\n" +
            "inner join t_user u1 on u1.user_id = m1.user_id and u1.`status`=1\n" +
            "group by m1.period_id")
    OrgPeriodInfo getSecretaryInfo(@Param("orgId") Long orgId,
                                   @Param("year") Integer year);

    /**
     * 获取组织的书记和副书记
     *
     * @param orgId
     * @param year
     * @return
     */
    @Select("select\n" +
            " period.org_id orgId,m1.user_id userId,m1.user_name `name`,m1.position_name position,\n" +
            " concat(DATE_FORMAT(period.start_time,'%Y.%m.%d'),'-',DATE_FORMAT(period.end_time,'%Y.%m.%d')) validDate,\n" +
            " case \n" +
            " when m1.position_name like '%书记%' and m1.position_name not like '%副书记%' then 1\n" +
            " when m1.position_name like '%副书记%' then 2\n" +
            " else 3 end as type\n" +
            "from t_user_org_period_member m1 \n" +
            "inner join (select period.period_id,period.org_id,period.start_time,period.end_time from (\n" +
            "select period.period_id,period.org_id,period.start_time,period.end_time,\n" +
            "case @org_id \n" +
            "when period.org_id -- 是否等于上一个组织id与当前行组织id相等\n" +
            "then @sort:=@sort+1\n" +
            "else @sort:=1\n" +
            "end as sort_id,\n" +
            "@org_id:=period.org_id -- 赋值当前组织id 用于比较\n" +
            "from t_user_org_period period,(select @org_id:=-1,@sort:=0) a\n" +
            "where period.is_delete=2 and year(period.start_time) <= #{year} and year(period.end_time)>=#{year} and period.org_id in (${orgId})\n" +
            "order by period.org_id,period.start_time desc) as period where period.sort_id=1) as period on m1.period_id=period.period_id and m1.is_delete = 2 and m1.type=1\n" +
            "inner join t_user u1 on u1.user_id = m1.user_id and u1.`status`=1")
    List<OrgDssUser> getNowSecretaryInfo(@Param("orgId") String orgId,
                                         @Param("year") Integer year);


    /**
     * 获取组织的领导班子
     *
     * @param orgId
     * @param year
     * @return
     */
    @Select("<script>" +
            "select GROUP_CONCAT(distinct u1.`name`)\n" +
            "  from t_organization to1 \n" +
            " inner join t_organization to2 " +
            "    on to1.`status` = 1 " +
            "   and to2.`status` = 1 " +
            "   and to1.owner_id = to2.organization_id " +
            "   and to1.organization_id = #{orgId}\n" +
            " inner join t_user_org_leader l1 " +
            "    on to2.organization_id = l1.org_id " +
            "   and l1.is_delete = 2 " +
            "   and year(l1.create_time) <![CDATA[<=]]> #{year}\n" +
            " inner join t_user u1 " +
            "    on l1.user_id = u1.user_id " +
            "   and u1.`status`= 1\n" +
            " group by to1.organization_id" +
            "</script>")
    String getLeaderInfo(@Param("orgId") Long orgId,
                         @Param("year") Integer year);

    /**
     * 获取领导班子信息 只有顶层党委才有可能有信息
     *
     * @param orgId
     * @param year
     * @return
     */
    @Select("select u1.`name`,l1.position,if(l1.is_head=1,1,0) isHead,u1.user_id userId,u1.name\n" +
            "from t_organization to1 \n" +
            "inner join t_organization to2 on to1.parent_id=3 and to1.region_id=#{regionId} and to1.`status`=1 and to2.`status`=1 and to1.owner_id=to2.organization_id and to1.organization_id=#{orgId}\n" +
            "inner join t_user_org_leader l1 on to2.organization_id=l1.org_id and l1.is_delete=2 and year(l1.create_time)<=#{year}\n" +
            "inner join t_user u1 on l1.user_id=u1.user_id and u1.`status`=1\n" +
            "group by u1.user_id order by l1.is_head")
    List<LeaderInfo> getLeaderInfoNew(@Param("orgId") Long orgId,
                                      @Param("year") Integer year,
                                      @Param("regionId") Long regionId);

    /**
     * 查询有支委的支部数量
     *
     * @param regionId    区县id
     * @param branchChild 党支部
     * @return 数量
     */
    @Select(
            "<script>"
                    + "    SELECT DISTINCT to1.org_id\n" +
                    "    FROM (\n" +
                    "             SELECT ot.org_id\n" +
                    "             FROM t_org_tag ot\n" +
                    "                      LEFT JOIN t_tag t ON ot.tag_id = t.tag_id\n" +
                    "             WHERE t.tag_type = 9\n" +
                    "               AND t.`status` = 1\n" +
                    "               AND t.region_id = #{regionId}\n" +
                    "         ) period\n" +
                    "             INNER JOIN t_org_snapshot to1\n" +
                    "                        ON period.org_id = to1.org_id\n" +
                    "                            AND to1.`status` = 1\n" +
                    "                            AND to1.region_id = 3\n" +
                    "                            AND to1.org_type_child in (${branchChild}) AND\n" +
                    "                           to1.org_id != 3261\n" +
                    "                            AND to1.org_level not like concat('%-', 3261, '-%') and to1.org_level not like '%-999-%'" +
                    "                            AND to1.date_month = '${dateMonth}' " +
                    "                            AND  to1.org_pid != -999 "
                    + "</script>")
    List<Long> hasPeriodBranchNum(@Param("regionId") Long regionId, @Param("dateMonth") String dateMonth, @Param("branchChild") String branchChild);

    @Select("select to1.org_id is not null\n" +
            "from t_org_snapshot to1 \n" +
            "inner join (select period1.* from (select period_id,org_id,start_time,end_time,discipline_inspection_committee from t_user_org_period\n" +
            "where is_delete=2 and UNIX_TIMESTAMP(start_time) <= (UNIX_TIMESTAMP(LAST_DAY(concat(#{queryDate},'-01')))+86399) and (unix_timestamp(end_time)+86399)  >= unix_timestamp(concat(#{queryDate},'-01')) \n" +
            "order by start_time DESC,period_id DESC) as period1 group by period1.org_id) period on to1.status=1 and to1.org_id=period.org_id \n" +
            "where to1.date_month=#{queryDate} and to1.org_id=#{orgId} and to1.org_type_child in (${orgType}) ")
    Boolean isCreatePeriod(@Param("orgId") Long orgId, @Param("queryDate") String queryDate, @Param("orgType") String orgType);

    @Select("select to1.org_id id,to1.org_id is not null as aBoolean \n" +
            "from t_org_snapshot to1 \n" +
            "inner join (select period.period_id,period.org_id,period.start_time,period.end_time,period.discipline_inspection_committee from (\n" +
            "select period.period_id,period.org_id,period.start_time,period.end_time,period.discipline_inspection_committee,\n" +
            "case @org_id \n" +
            "when period.org_id\n" +
            "then @sort:=@sort+1\n" +
            "else @sort:=1\n" +
            "end as sort_id,\n" +
            "@org_id:=period.org_id\n" +
            "from t_user_org_period period,(select @org_id:=-1,@sort:=0) a\n" +
            "where is_delete=2 and UNIX_TIMESTAMP(start_time) <= (UNIX_TIMESTAMP(LAST_DAY(concat(#{queryDate},'-01')))+86399) and (unix_timestamp(end_time)+86399)  >= unix_timestamp(concat(#{queryDate},'-01')) \n" +
            "order by period.org_id,period.start_time desc) as period where period.sort_id=1) period on to1.status=1 and to1.org_id=period.org_id \n" +
            "where to1.date_month=#{queryDate} and to1.org_id in (${ids}) and to1.org_type_child in (${orgType}) ")
    List<RemoteRuleWryService.QueryResult> isCreatePeriod2(@Param("ids") String ids, @Param("queryDate") String queryDate, @Param("orgType") String orgType);

    //    @Select("select org.num>=3 and org.num <=50\n" +
//            "from (select count(distinct user_id) num,org_id\n" +
//            "from  t_user_snapshot\n" +
//            "where date_month=#{queryDate} and org_id=#{orgId} and org_type_child in (${join}) group by org_id) org\n" +
//            "inner join (select period1.* from (select period_id,org_id,start_time,end_time,discipline_inspection_committee from t_user_org_period\n" +
//            "where is_delete=2 and UNIX_TIMESTAMP(start_time) <= (UNIX_TIMESTAMP(LAST_DAY(concat(#{queryDate},'-01')))+86399) and (unix_timestamp(end_time)+86399)  >= unix_timestamp(concat(#{queryDate},'-01')) \n" +
//            "order by start_time DESC,period_id DESC) as period1 group by period1.org_id) period on org.org_id=period.org_id")
    @Select("select org.num>=3 and org.num <=50\n" +
            "from (select count(distinct user_id) num,org_id\n" +
            "from  t_user_snapshot\n" +
            "where status=1 and date_month=#{queryDate} and org_id=#{orgId} and org_type_child in (${join}) group by org_id) org\n")
    Boolean checkPeriodPerson1(@Param("orgId") Long orgId, @Param("queryDate") String queryDate, @Param("join") String join);

    @Select("select org.org_id id,org.org.num>=3 and org.org.num<=50 aBoolean\n" +
            "from (select count(distinct user_id) num,org_id\n" +
            "from  t_user_snapshot\n" +
            "where status=1 and date_month=#{queryDate} and org_id in (${ids}) and org_type_child in (${join}) group by org_id) org\n")
    List<RemoteRuleWryService.QueryResult> newCheckPeriodPerson1(@Param("ids") String ids, @Param("queryDate") String queryDate, @Param("join") String join);

    @Select("select (org.num>=7 and period.period_id is not null) or (org.num<7 and period.period_id is null)\n" +
            "from (select count(distinct user_id) num,org_id\n" +
            "from  t_user_snapshot\n" +
            "where status=1 and date_month=#{queryDate} and org_id=#{orgId} and political_type IN(1,17) and org_type_child in (${join}) group by org_id) org\n" +
            "left join (select period1.* from (select period_id,org_id,start_time,end_time,discipline_inspection_committee from t_user_org_period\n" +
            "where is_delete=2 and UNIX_TIMESTAMP(start_time) <= (UNIX_TIMESTAMP(LAST_DAY(concat(#{queryDate},'-01')))+86399) and (unix_timestamp(end_time)+86399)  >= unix_timestamp(concat(#{queryDate},'-01')) \n" +
            "order by start_time DESC,period_id DESC) as period1 group by period1.org_id) period on org.org_id=period.org_id")
    Boolean checkPeriodPerson2(@Param("orgId") Long orgId, @Param("queryDate") String queryDate, @Param("join") String join);

    @Select("select org.org_id id,(org.num>=7 and period.period_id is not null) or (org.num<7 and period.period_id is null) aBoolean\n" +
            "from (select count(distinct user_id) num,org_id\n" +
            "from  t_user_snapshot\n" +
            "where status=1 and date_month=#{queryDate} and org_id in (${ids}) and political_type IN(1,17) and org_type_child in (${join}) group by org_id) org\n" +
            "left join (select period.period_id,period.org_id,period.start_time,period.end_time,period.discipline_inspection_committee from (\n" +
            "select period.period_id,period.org_id,period.start_time,period.end_time,period.discipline_inspection_committee,\n" +
            "case @org_id \n" +
            "when period.org_id\n" +
            "then @sort:=@sort+1\n" +
            "else @sort:=1\n" +
            "end as sort_id,\n" +
            "@org_id:=period.org_id\n" +
            "from t_user_org_period period,(select @org_id:=-1,@sort:=0) a\n" +
            "where is_delete=2 and UNIX_TIMESTAMP(start_time) <= (UNIX_TIMESTAMP(LAST_DAY(concat(#{queryDate},'-01')))+86399) and (unix_timestamp(end_time)+86399)  >= unix_timestamp(concat(#{queryDate},'-01')) \n" +
            "order by period.org_id,period.start_time desc) as period where period.sort_id=1) period on org.org_id=period.org_id")
    List<RemoteRuleWryService.QueryResult> newCheckPeriodPerson2(@Param("ids") String orgId, @Param("queryDate") String queryDate, @Param("join") String join);

    @Select("select sum(if(pm.position_name='党委书记',1,0)) >0 and sum(if(pm.position_name='党委专职副书记',1,0))>0\n" +
            "from t_org_snapshot to1 \n" +
            "inner join (select period1.* from (select period_id,org_id,start_time,end_time,discipline_inspection_committee from t_user_org_period\n" +
            "where is_delete=2 and UNIX_TIMESTAMP(start_time) <= (UNIX_TIMESTAMP(LAST_DAY(concat(#{queryDate},'-01')))+86399) and (unix_timestamp(end_time)+86399)  >= unix_timestamp(concat(#{queryDate},'-01')) \n" +
            "order by start_time DESC,period_id DESC) as period1 group by period1.org_id) period on to1.status=1 and period.org_id=to1.org_id\n" +
            "inner join t_user_org_period_member pm on pm.period_id=period.period_id and pm.is_delete=2 and pm.type=1\n" +
            "where to1.date_month=#{queryDate} and to1.org_id=#{orgId} \n" +
            "and to1.org_pid=3\n" +
            "group by to1.org_id")
    Boolean checkTopOrgLackPosition(@Param("orgId") Long orgId, @Param("queryDate") String queryDate);

    @Select("select to1.org_id id,sum(if(pm.position_name='党委书记',1,0)) >0 and sum(if(pm.position_name='党委专职副书记',1,0))>0 aBoolean\n" +
            "from t_org_snapshot to1 \n" +
            "inner join (select period.period_id,period.org_id,period.start_time,period.end_time,period.discipline_inspection_committee from (\n" +
            "select period.period_id,period.org_id,period.start_time,period.end_time,period.discipline_inspection_committee,\n" +
            "case @org_id \n" +
            "when period.org_id\n" +
            "then @sort:=@sort+1\n" +
            "else @sort:=1\n" +
            "end as sort_id,\n" +
            "@org_id:=period.org_id\n" +
            "from t_user_org_period period,(select @org_id:=-1,@sort:=0) a\n" +
            "where is_delete=2 and UNIX_TIMESTAMP(start_time) <= (UNIX_TIMESTAMP(LAST_DAY(concat(#{queryDate},'-01')))+86399) and (unix_timestamp(end_time)+86399)  >= unix_timestamp(concat(#{queryDate},'-01')) \n" +
            "order by period.org_id,period.start_time desc) as period where period.sort_id=1) period on to1.status=1 and period.org_id=to1.org_id\n" +
            "inner join t_user_org_period_member pm on pm.period_id=period.period_id and pm.is_delete=2 and pm.type=1\n" +
            "where to1.date_month=#{queryDate} and to1.org_id in (${ids}) \n" +
            "and to1.org_pid=3\n" +
            "group by to1.org_id")
    List<RemoteRuleWryService.QueryResult> checkTopOrgLackPosition2(@Param("ids") String ids, @Param("queryDate") String queryDate);

    @Select("select p1.period_id periodId,p1.start_time startTime,p1.end_time endTime," +
            " UNIX_TIMESTAMP(DATE_FORMAT(p1.start_time,'%Y-%m-01 00:00:00')) unixStartTime, UNIX_TIMESTAMP(DATE_FORMAT(LAST_DAY(p1.end_time),'%Y-%m-%d 23:59:59')) unixEndTime " +
            "from t_org_snapshot to1\n" +
            "inner join t_user_org_period p1 on to1.status=1 and to1.org_id=p1.org_id and p1.is_delete=2\n" +
            "where to1.date_month=#{queryDate} and to1.org_pid=3 and to1.org_id=#{orgId}\n" +
            "order by p1.start_time DESC")
    List<RankPeriodIsEquleFrom> getPeriodForTime(@Param("orgId") Long orgId, @Param("queryDate") String queryDate);

    @Select("select count(*) from \n" +
            "(select period_id,user_name,user_id,position_name\n" +
            "from t_user_org_period_member where period_id=#{newPeriodId} and is_delete=2 and position_name in ('党委书记','党委专职副书记') and type=1 group by user_id) period1\n" +
            "inner join (select period_id,user_name,user_id,position_name\n" +
            "from t_user_org_period_member where period_id=#{beforePeriodId} and is_delete=2 and position_name in ('党委书记','党委专职副书记') and type=1 group by user_id) period2\n" +
            "on period1.user_id=period2.user_id and period1.position_name=period2.position_name")
    Long checkPeriodMemberIsRepeat(@Param("newPeriodId") Long newPeriodId, @Param("beforePeriodId") Long beforePeriodId);

    /**
     * 查询用户所在支委会的数量
     *
     * @param userId    组织id
     * @param queryDate 查询月份
     * @return int 党小组数量
     */
    @Select("<script> "
            + " SELECT count(*) FROM t_user_org_period_member"
            + " tupg  where DATE_FORMAT(create_time,'%Y-%m') &lt;= #{queryDate} and is_delete=2 and type=1 and user_id=#{userId}  "
            + " and tupg.period_id IN (\n" +
            "       SELECT\n" +
            "           period_id \n" +
            "       FROM\n" +
            "           t_user_org_period \n" +
            "       WHERE\n" +
            "           is_delete = 2 \n" +
            ") "
            + " </script>")
    Integer periodNumByUserId(@Param("userId") long userId, @Param("queryDate") String queryDate);

    /**
     * 获取领导干部与联系本支部领导的信息
     *
     * @param year 查询年
     * @param join 有效快照表月
     * @param join 组织Id
     * @return 领导班子信息
     */
    @Select("select result.*,leader.position,if(leader.is_head=1,1,0) isHead from (\n" +
            "select us.user_id userId,us.user_name name,1 type,us.org_id fromOrg\n" +
            "from t_user_snapshot us \n" +
            "inner join t_org_snapshot os on us.org_id=os.org_id and os.status=1 and os.year=#{year} and os.month=#{month}\n" +
            "where us.year=#{year} and us.month=#{month} and us.status=1 and os.org_id in (${join})\n" +
            "union all\n" +
            "select ol.user_id userId,ol.user_name name,2 type,lc.org_id fromOrg\n" +
            "from t_user_org_leader ol \n" +
            "inner join t_user_org_leader_contact lc on ol.leader_id=lc.leader_id and lc.select_type=1 \n" +
            "where ol.is_delete=2 and year(ol.create_time)<=#{year} and lc.org_id in (${join})) as result\n" +
            "inner join \n" +
            "(select ol.user_id,ol.user_name,ol.position,ol.is_head,us.org_id from t_org_snapshot os\n" +
            "inner join t_user_org_leader ol on os.owner_id=ol.org_id and ol.is_delete=2 and year(ol.create_time)<=#{year}\n" +
            "inner join t_user_snapshot us on ol.user_id=us.user_id and us.year=#{year} and us.month=#{month} and us.status=1\n" +
            "where os.year=#{year} and os.month=#{month} and os.org_pid=3 and os.region_id=3 and os.status=1) leader on result.userId=leader.user_id group by result.fromOrg,result.userId;")
    List<OrgBaseLeaderInfo> getBaseOrgLeaderInfo(@Param("year") Integer year, @Param("month") Integer month, @Param("join") String join);


//    @Select("select distinct org_id as organizationId\n" +
//            "from t_user_org_period p1\n" +
//            "where p1.is_delete=2 and region_id=#{regionId}\n" +
//            "and DATE_FORMAT(p1.end_time,'%Y-%m-01')  < DATE_FORMAT(now(),'%Y-%m-01') \n" +
//            "and p1.org_id not in (select org_id from t_user_org_period where is_valid=1)\n" +
//            "and p1.org_id in (${orgIds})")
//    List<OrganizationEntity> findExpireOrg(@Param("regionId") Long regionId, @Param("orgIds") String orgIds);


    @Select("<script>" +
            "SELECT organizationId FROM \n" +
            "(\n" +
            "\tSELECT\n" +
            "\t o.organization_id AS organizationId,\n" +
            "\t o.`name` AS orgName,\n" +
            "\t o.short_name AS orgShortName,\n" +
            "\t p1.start_time, \n" +
            "\t p1.end_time\n" +
            "\tFROM\n" +
            "\t\tt_organization o\n" +
            "\t\tLEFT JOIN t_user_org_period p1 ON o.organization_id = p1.org_id\n" +
            "\tWHERE\n" +
            "<if test =\"type == 1\"> " +
            "(o.organization_id = #{orgId} or o.org_level like CONCAT('%-',#{orgId},'-%'))\n" +
            "</if>" +
            "<if test =\"type == 2\"> " +
            "\t\t( o.organization_id = #{orgId} )\n" +
            "</if>" +
            "\t\tAND p1.period_id = ifnull((\n" +
            "\t\t\tSELECT\n" +
            "\t\t\t\tp2.period_id \n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tt_user_org_period p2\n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\tp2.org_id = p1.org_id \n" +
            "\t\t\t\tAND p2.is_valid = 1 \n" +
            "\t\t\t\tAND p2.is_delete = 2 \n" +
            "\t\t\t),\n" +
            "\t\t( SELECT\n" +
            "\t\t\tp3.period_id \n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tt_user_org_period p3 \n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\tp3.org_id = p1.org_id \n" +
            "\t\t\t\tAND p3.is_delete = 2 \n" +
            "\t\t\tORDER BY\n" +
            "\t\t\t\tp3.start_time DESC \n" +
            "\t\t\tLIMIT 1 \n" +
            "\t\t))\n" +
            "\t) as L\n" +
            "\tWHERE L.start_time <![CDATA[ <  ]]>  NOW() and L.end_time <![CDATA[ < ]]> NOW()" +
            "</script>")
    List<OrganizationEntity> findExpireOrg(@Param("regionId") Long regionId, @Param("orgId") Long orgId,@Param("type") Integer type);



    @Select("select distinct o1.organization_id\n" +
            "from t_organization o1\n" +
            "left join t_org_tag t1 on o1.organization_id=t1.org_id and  t1.tag_id in (select tag_id from t_tag where region_id=#{regionId} and tag_type=9)\n" +
            "where o1.region_id=#{regionId} and o1.status=1 and o1.party_member_num>=7 and t1.org_tag_id is null\n" +
            "and o1.organization_id in (${join})")
    Set<Long> findNotExistPeriodTagByOrgs(@Param("regionId") Long regionId, @Param("join") String join);

    @Select("select GROUP_CONCAT(m1.user_name) as org_secretary , GROUP_CONCAT(m1.user_id) as org_secretary_id \n" +
            "from t_user_org_period p1\n" +
            "inner join t_user_org_period_member m1 on p1.period_id=m1.period_id and m1.is_delete=2 and m1.type=1\n" +
            "where p1.is_delete=2 and p1.is_valid=1 and p1.org_id=#{orgId} and m1.position_name like '%书记%' and m1.position_name not like '%副书记%'")
    Map<String, String> findPeriodLeaderByOrg(@Param("orgId") Long orgId);

    /**
     * 党支部 未按期开展换届工作 组织届次到期未换届，积分-5
     */
    @Select("select p1.period_id\n" +
            "from t_user_org_period p1\n" +
            "where p1.is_delete=2 and date_format(p1.end_time,'%Y-%m')=#{queryMonth} and p1.region_id=#{regionId} and p1.org_id=#{orgId}\n" +
            "and p1.org_id not in (\n" +
            "select distinct org_id\n" +
            "from t_user_org_period where is_delete=2 and date_format(start_time,'%Y-%m-01') <= #{nextMonth} and date_format(end_time,'%Y-%m-01') >= #{nextMonth})\n" +
            "group by p1.org_id")
    Long dueThisMonthNextNotExist(@Param("regionId") Long regionId, @Param("orgId") Long id,
                                  @Param("queryMonth") String executeTime, @Param("nextMonth") String format);

    /**
     * 党委党总支
     */
    @Select("select p1.org_id\n" +
            "from t_user_org_period p1\n" +
            "where p1.is_delete=2 and date_format(p1.end_time,'%Y-%m')=#{queryMonth} and p1.region_id=#{regionId} " +
            "and p1.org_id in (select o1.organization_id \n" +
            "from t_organization o1\n" +
            "where o1.status=1 and o1.region_id=#{regionId} and o1.org_level like '%-${orgId}-%' and o1.org_type_child in (${orgTypeChild})) \n" +
            "and p1.org_id not in (\n" +
            "select distinct org_id\n" +
            "from t_user_org_period where is_delete=2 and date_format(start_time,'%Y-%m-01') <= #{nextMonth} and date_format(end_time,'%Y-%m-01') >= #{nextMonth})\n" +
            "group by p1.org_id")
    List<Long> dueThisMonthNextNotExistByLowerLevel(@Param("regionId") Long regionId, @Param("orgId") Long orgId,
                                                    @Param("queryMonth") String executeTime, @Param("nextMonth") String format,
                                                    @Param("orgTypeChild") String orgTypeChild);

    @Select("select m1.period_member_id\n" +
            "from t_user_org_period_member  m1 \n" +
            "where m1.type=1 and m1.is_delete=2 and m1.position_name like '%书记%'  and m1.position_name not like '%副书记%'\n" +
            "and m1.period_id in (\n" +
            "select period_id from t_user_org_period where region_id=#{regionId} and is_delete=2 and date_format(start_time,'%Y-%m-01') <= #{queryTime} and date_format(end_time,'%Y-%m-01') >= #{queryTime} and org_id=#{orgId}\n" +
            ") limit 1")
    Long periodExistLeader(@Param("regionId") Long regionId, @Param("queryTime") String queryTime, @Param("orgId") Long orgId);

    @Select("select p1.period_id\n" +
            "from t_organization to1\n" +
            "inner join t_user_org_period p1 on to1.organization_id=p1.org_id and p1.is_delete=2 \n" +
            "and date_format(p1.start_time,'%Y-%m-01')<=#{time} and date_format(p1.end_time,'%Y-%m-01')>=#{time}\n" +
            "inner join t_user_org_period_member m1 on p1.period_id=m1.period_id and m1.is_delete=2 and m1.type=1 and m1.position_name like '%书记%'\n" +
            "where to1.`status`=1 and to1.organization_id=#{orgId} limit 1")
    Long isExistPeriodLeaderByTimeAndOrg(@Param("orgId") Long orgId, @Param("time") String time);


    /**
     * 根据组织id查询组织组织逾期未换届
     * @param orgId
     * @return
     */
    @Select("SELECT\n" +
            " o.organization_id AS orgId,\n" +
            " o.`name` AS orgName,\n" +
            " o.short_name AS orgShortName,\n" +
            "date_format( p1.start_time, '%Y.%m.%d' ) as startTime,\n" +
            "date_format( p1.end_time, '%Y.%m.%d' ) as endTime\n" +
            "FROM\n" +
            "\tt_organization o\n" +
            "  LEFT JOIN t_user_org_period p1 ON o.organization_id = p1.org_id\n" +
            "WHERE\n" +
            "\to.organization_id = #{orgId}\n" +
            "\tAND p1.period_id = ifnull((\n" +
            "\t\tSELECT\n" +
            "\t\t\tp2.period_id \n" +
            "\t\tFROM\n" +
            "\t\t\tt_user_org_period p2\n" +
            "\t\tWHERE\n" +
            "\t\t\tp2.org_id = p1.org_id \n" +
            "\t\t\tAND p2.is_valid = 1 \n" +
            "\t\t\tAND p2.is_delete = 2 \n" +
            "\t\t),\n" +
            "\t( SELECT\n" +
            "\t\tp3.period_id \n" +
            "\t\tFROM\n" +
            "\t\t\tt_user_org_period p3 \n" +
            "\t\tWHERE\n" +
            "\t\t\tp3.org_id = p1.org_id \n" +
            "\t\t\tAND p3.is_delete = 2 \n" +
            "\t\tORDER BY\n" +
            "\t\t\tp3.start_time DESC \n" +
            "\t\tLIMIT 1 \n" +
            "\t));")
    SupervisePeriodForm getOverdueInfoByOrgId(@Param("orgId") Long orgId);





    /**
     * 查询组织是否设立支委会
     * @param orgId
     * @return
     */
    @Select("SELECT\n" +
            "    o.organization_id AS orgId,\n" +
            "    o.`name` AS orgName,\n" +
            "    o.short_name AS orgShortName,\n" +
            "\tIF(ot.tag_id IS NULL,0 , 1) as createPeriod, -- 是否设立支委会 1-是，0-否\n" +
            "    count(DISTINCT u.user_id) total -- 党员数量\n" +
            "FROM\n" +
            "    t_organization o\n" +
            "\t\tLEFT JOIN t_user_org_corp uoc ON uoc.organization_id = o.organization_id\n" +
            "\t\tAND uoc.is_employee = 1\n" +
            "\t\tLEFT JOIN t_user u ON u.user_id = uoc.user_id\n" +
            "\t\tAND u.`status` = 1\n" +
            "\t\tAND u.political_type in (1)\n" +
            "    LEFT JOIN t_org_tag ot ON o.organization_id = ot.org_id \n" +
            "    AND ot.tag_id = ( SELECT tag_id FROM t_tag WHERE region_id = 19 AND tag_type = 9 AND STATUS = 1 )\n" +
            "WHERE\n" +
            "    o.`status` = 1\n" +
            "\t\tAND o.organization_id = #{orgId}\n" +
            "GROUP BY\n" +
            "    o.organization_id;")
    SupervisePeriodForm getIsCreatePeriod(@Param("orgId") Long orgId);


    @Select("select count(m1.period_member_id) \n" +
            "from t_user_org_period_member  m1 \n" +
            "where m1.type=1 and m1.is_delete=2 and m1.position_name like '%书记%'  and m1.position_name not like '%副书记%'\n" +
            "and m1.period_id in (\n" +
            "select period_id from t_user_org_period where region_id=#{regionId} and is_delete=2 and date_format(start_time,'%Y-%m-01') <= #{queryTime} and date_format(end_time,'%Y-%m-01') >= #{queryTime} and org_id=#{orgId}\n" +
            ") ")
    Integer periodExistLeaderCount(@Param("regionId") Long regionId, @Param("queryTime") String queryTime, @Param("orgId") Long orgId);


    @Select(" select  \n" +
            "            tm.type_id typeId, \n" +
            "            tm.type type, \n" +
            "            if(t.num is null,1,2) status \n" +
            "            from t_meeting_task tm left join \n" +
            "            ( \n" +
            "            SELECT \n" +
            "              t2.type type, \n" +
            "              t2.type_id typeId, \n" +
            "              count(0) num \n" +
            "            FROM \n" +
            "              t_meeting t1 \n" +
            "              LEFT JOIN t_meeting_type t2 ON t1.meeting_id = t2.meeting_id  \n" +
            "              where t1.org_id = #{orgId} and t1.status in(7,8,10,11,12,13,14,15)\n" +
            "              AND t2.type_id in (1,3,4) \n" +
            "              AND year(t1.start_time) = year(now())  \n" +
            "              AND month(t1.start_time) = month(now())  \n" +
            "              group by t2.type_id \n" +
            "              \n" +
            "              UNION ALL\n" +
            "              \n" +
            "              SELECT \n" +
            "              t2.type type, \n" +
            "              t2.type_id typeId, \n" +
            "              count(0) num \n" +
            "            FROM \n" +
            "              t_meeting t1 \n" +
            "              LEFT JOIN t_meeting_type t2 ON t1.meeting_id = t2.meeting_id  \n" +
            "              where t1.org_id = #{orgId} \n" +
            "              AND t2.type_id in (2,5) and t1.status in(7,8,10,11,12,13,14,15)\n" +
            "              AND year(t1.start_time) = year(now())  \n" +
            "              AND quarter(t1.start_time) = quarter(now())  \n" +
            "              group by t2.type_id  \n" +
            "              \n" +
            "            ) t on tm.type_id = t.typeId  \n" +
            "            where tm.org_id = #{orgId} \n" +
            "            and tm.start_time <= date_format(SUBDATE(now(),INTERVAL 0 month), \"%Y-%m-%d\")\n" +
            "            and tm.end_time>= date_format(SUBDATE(now(),INTERVAL 0 month) , \"%Y-%m-%d\")")
    List<MeetingPlanCountVO> getMeetingPlanList(@Param("orgId") Long orgId, @Param("flag") Integer flag);


    @Select("""
            SELECT COUNT(DISTINCT b.org_Id) FROM
            (
            SELECT * FROM
            (
            SELECT
            o1.org_id ,
            o1.period_id,
            o1.start_time start_time1,
            o1.end_time end_time1,
            o2.start_time start_time2,
            o2.end_time end_time2
            FROM t_user_org_period o1
            LEFT JOIN t_user_org_period o2 ON o1.org_id = o2.org_id  -- 每个届次去关联他同组织的其他届次
            AND o1.period_id != o2.period_id
            AND o2.is_delete =2
            AND o2.start_time >= o1.start_time   -- 只关联时间在他之后的届次
            AND o2.end_time >= o1.end_time
            JOIN t_organization o3 ON o1.org_id = o3.organization_id
            AND o3.organization_id = #{orgId}
            WHERE o1.is_delete =2
            AND year(o1.end_time)=year(now())     -- 届次本年度到期
            ORDER BY o1.org_id,o1.period_id, o2.start_time LIMIT 100000 )a
            GROUP BY a.org_Id,a.period_id  -- 用order by 和group by 每个届次只关联在他之后 且离他最近的届次
            )b
            WHERE
            TIMESTAMPDIFF(month,b.end_time1,b.start_time2 ) >= 6  -- 该届次结束时间 距下一个届次开始时间 超过6个月
            OR
            (b.start_time2 is NULL AND TIMESTAMPDIFF(month,b.end_time1,now())>=6)  -- 或者没有下一个届次,且上一个届次结束距现在已经超过6个月
            """)
    Integer getSettingOrgInfoPeriodSCount(@Param("orgId") Long orgId, @Param("year") Integer year);


    @Select("""            
            SELECT
            if((count(DISTINCT u.user_id) >7
            AND (period_id IS NULL OR ot.tag_id is NULL)),1,0 ) as count
            FROM t_user_org_corp uoc
            INNER JOIN t_user u
            ON uoc.user_id = u.user_id AND u.status =1
            LEFT JOIN t_user_org_period uop
            ON uoc.organization_id = uop.org_id AND uop.is_delete = 2 AND uop.start_time <= now() AND uop.end_time>= now()
            LEFT JOIN t_org_tag ot ON uoc.organization_id = ot.org_id AND ot.tag_id = 7
            WHERE  u.political_type IN (1,17)
            AND uoc.organization_id =#{orgId}
            """)
    Integer getNoSettingBranchCommittee(@Param("orgId") Long orgId,
                                        @Param("year") Integer year,
                                        @Param("regionId") Long regionId);


}
