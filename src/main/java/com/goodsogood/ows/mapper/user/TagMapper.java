package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.user.TagEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface TagMapper extends MyMapper<TagEntity> {

    @Select("<script>" +
            "select GROUP_CONCAT(name) from t_tag where tag_id in " +
            "<foreach collection=\"tagIds\" item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    String getTagName(@Param("tagIds") List<Long> tagIds);
}
