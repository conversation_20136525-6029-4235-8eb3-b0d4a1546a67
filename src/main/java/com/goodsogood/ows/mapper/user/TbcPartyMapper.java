package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.model.vo.tbc.ExamineForm;
import com.goodsogood.ows.model.vo.tbc.PartyCaucusForm;
import com.goodsogood.ows.model.vo.tbc.RankForm;
import com.goodsogood.ows.model.vo.tbc.VisitMonthForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface TbcPartyMapper {
    /**
     * 获取党员人数详情
     * @param orgId 组织id
     * @param staYear 年份
     * @return
     */
    @Select("SELECT\n" +
            "\tifnull(formal_party_member,0) officialNumber,ifnull(pre_party_member,0) prepareNumber\n" +
            "FROM\n" +
            "\tt_finereport_org_member_info \n" +
            "WHERE\n" +
            "\torg_id = ${orgId} \n" +
//            "\tAND region_id = 3 \n" +
            "\tAND sta_year = ${staYear}")
    PartyCaucusForm.TbcPrepareNumberForm getCaucus(@Param(value = "orgId")Long orgId,
                                      @Param(value = "staYear") String staYear);

    /**
     * 查询预备党员信息
     * @param orgId
     *
     * @return
     */
    @Select(" select  DISTINCT u.`name` memberName,o.`name` memberBranch\n" +
            " from t_user u \n" +
            " LEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            " LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            " where u.`status` = 1\n" +
            " and o.`status` = 1 AND o.activate=1  and u.political_type in(1,5,17,18)\n" +
            " and uoc.is_employee = 1 and o.parent_id  <>  -999 \n" +
            " and o.org_type_child in(10280301,10280310,10280322,10280303,10280308,\n" +
            "10280311,10280318,10280304,10280309,10280314,10280315,10280319)\n" +
            " and (o.organization_id = ${orgId} or o.org_level LIKE CONCAT(\"%-\",${orgId},\"-%\"))\n" +
            " AND  u.political_type in(5,18)\n" +
            " ORDER BY o.seq DESC")
    List<PartyCaucusForm.TbcDetailsOfProbationaryPartyMembershipForm> getPrepareMember(@Param(value = "orgId")Long orgId);

    /**
     * 获取民族类型
     * @param orgId 组织ID
     * @return
     */
    @Select("SELECT temp.id nationId,temp.nation nationName,count(temp.id) nationNumber FROM (\n" +
            "SELECT \n" +
            "  ( SELECT op_key AS ed FROM t_option WHERE op_key IN ( u.ethnic ) AND `code` = 1004 ) id,\n" +
            "  ( SELECT op_value AS ed FROM t_option WHERE op_key IN ( u.ethnic ) AND `code` = 1004 ) nation\n" +
            "FROM\n" +
            "  t_user u\n" +
            "  LEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "  LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1 \n" +
            "  AND o.activate = 1 \n" +
            "  AND u.political_type IN ( 1, 5, 17, 18 ) \n" +
            "  AND uoc.is_employee = 1 \n" +
            "  AND o.parent_id <> - 999 \n" +
            "  AND o.org_type_child IN ( 10280301, 10280310, 10280322, 10280303, 10280308, 10280311, 10280318, 10280304, 10280309, 10280314, 10280315, 10280319 ) \n" +
            "  AND ( o.organization_id = ${orgId} OR o.org_level LIKE CONCAT( \"%-\", ${orgId}, \"-%\" ) ) \n" +
            ") temp \n" +
            "GROUP BY temp.id \n")
    List<PartyCaucusForm.TbcEthnicDistributionOfPartyMembers> getMemberNation(@Param(value = "orgId")Long orgId);
    /**
     * 获取各民族人数详情
     * @param orgId 组织ID
     * @return
     */
    @Select(" select  DISTINCT u.`name` memberName,o.`name` orgName,2 as seq,\n" +
            " ( SELECT op_value as ed FROM t_option WHERE op_key IN(u.ethnic) AND `code`=1004) memberNation\n" +
            " from t_user u \n" +
            " LEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            " LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            " where u.`status` = 1\n" +
            " and o.`status` = 1 AND o.activate=1  and u.political_type in(1,5,17,18)\n" +
            " and uoc.is_employee = 1 and o.parent_id  <>  -999 \n" +
            " and o.org_type_child in(10280301,10280310,10280322,10280303,10280308,\n" +
            "  10280311,10280318,10280304,10280309,10280314,10280315,10280319)\n" +
            " and (o.organization_id = ${orgId} or o.org_level LIKE CONCAT(\"%-\",${orgId},\"-%\"))\n" +
            " AND u.ethnic=${ethnicId}\n" +
            " ORDER BY o.seq DESC")
    List<PartyCaucusForm.TbcDetailsOfEthnicPersonsForm> getNationNumber(@Param(value = "orgId")Long orgId,
                                                                       @Param(value = "ethnicId")Integer ethnicId);

    /**
     * 获取视频图片详情
     * @param orgId 图片ID
     * @return
     */
    @Select("")
    List<PartyCaucusForm.TbcVideoAndImgViewForm> getImgAndVideo(@Param(value = "orgId")Long orgId);

    /**
     * 获取领导成员详情
     * @param orgId 组织ID
     * @return
     */
    @Select("")
    List<PartyCaucusForm.TbcDetailsOfLeadingMembersForm> getLeads(@Param(value = "orgId")Long orgId);

    /**
     * 获取男女人数
     * @param orgId 组织ID
     * @return
     */
    @Select("SELECT\n" +
            "\tmale_member manNum,\n" +
            "\tfemale_member womanNum \n" +
            "FROM\n" +
            "\tt_finereport_org_member_info \n" +
            "WHERE\n" +
            "\torg_id = #{orgId} \n" +
//            "\tAND region_id = 3 \n" +
            "\tAND sta_year = ${staYear};")
    PartyCaucusForm.TbcGenderNumberForm getGenderNumber(@Param(value = "orgId")Long orgId,
                                                        @Param(value = "staYear") String staYear);

    /**
     * 获取人数
     * @param orgId 组织ID
     * @param age 各个阶段年龄参数
     * @return
     */
 @Select("SELECT DISTINCT\n" +
         "\tCOUNT(u.age) ageNumber \n" +
         "FROM\n" +
         "\tt_user u\n" +
         "\tLEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
         "\tLEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
         "WHERE\n" +
         "\tu.`status` = 1 \n" +
         "\tAND o.`status` = 1 \n" +
         "\tAND o.activate = 1 \n" +
         "\tAND u.political_type IN ( 1, 5, 17, 18 ) \n" +
         "\tAND uoc.is_employee = 1 \n" +
         "\tAND o.parent_id <> - 999 \n" +
         "\tAND o.org_type_child IN ( 10280301, 10280310, 10280322, 10280303, 10280308, 10280311, 10280318, 10280304, 10280309, 10280314, 10280315, 10280319 ) \n" +
         "\tAND ( o.organization_id = ${orgId} OR o.org_level LIKE CONCAT( \"%-\", ${orgId}, \"-%\" ) ) \n" +
         "\tAND ${age} \n" +
         "ORDER BY\n" +
         "\tage DESC")
    Long getAgeNumber(@Param(value = "orgId")Long orgId,
                                                        @Param(value = "age")String age);

    /**
     * 获取党员各年龄人员详情信息
     * @param orgId
     * @return
     */
    @Select("SELECT DISTINCT\n" +
            "\tu.`name` memberName,\n" +
            "\to.`name` orgName,\n" +
            "\tu.age partyAge\n" +
            "FROM\n" +
            "\tt_user u\n" +
            "\tLEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "\tLEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tu.`status` = 1 \n" +
            "\tAND o.`status` = 1 \n" +
            "\tAND o.activate = 1 \n" +
            "\tAND u.political_type IN ( 1, 5, 17, 18 ) \n" +
            "\tAND uoc.is_employee = 1 \n" +
            "\tAND o.parent_id <> - 999 \n" +
            "\tAND o.org_type_child IN ( 10280301, 10280310, 10280322, 10280303, 10280308, 10280311, 10280318, 10280304, 10280309, 10280314, 10280315, 10280319 ) \n" +
            "\tAND ( o.organization_id = ${orgId} OR o.org_level LIKE CONCAT( \"%-\",${orgId}, \"-%\" ) ) \n" +
            "\tAND u.age\n" +
            "ORDER BY\n" +
            "\tage DESC")
    List<PartyCaucusForm.TbcAgeGradesForm> getAgeDetails(@Param(value = "orgId")Long orgId);


    /**
     * 获取学历人数
     * @param orgId 组织ID
     * @return
     */
    @Select("SELECT DISTINCT\n" +
//            "\t( SELECT op_value AS ed FROM t_option WHERE op_key IN ( u.education ) AND `code` = 1003 ) backdrop, \n"+
            "\tCOUNT(( SELECT op_value AS ed FROM t_option WHERE op_key IN ( u.education ) AND `code` = 1003 )) peopleNumber \n" +
            "FROM\n" +
            "\tt_user u\n" +
            "\tLEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "\tLEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tu.`status` = 1 \n" +
            "\tAND o.`status` = 1 \n" +
            "\tAND o.activate = 1 \n" +
            "\tAND u.political_type IN ( 1, 5, 17, 18 ) \n" +
            "\tAND uoc.is_employee = 1 \n" +
            "\tAND o.parent_id <> - 999 \n" +
            "\tAND o.org_type_child IN ( 10280301, 10280310, 10280322, 10280303, 10280308, 10280311, 10280318, 10280304, 10280309, 10280314, 10280315, 10280319 ) \n" +
            "\tAND ( o.organization_id = ${orgId} OR o.org_level LIKE CONCAT( \"%-\", ${orgId}, \"-%\" ) ) \n" +
            "\tAND ${student} \n" +
            "ORDER BY\n" +
            "\to.seq DESC")
    Long getBackdropPages(@Param(value = "orgId")Long orgId,@Param(value = "student")String student);


    /**
     * 获取学历人员详情
     * @param orgId 组织ID
     * @return
     */
    @Select("")
    List<PartyCaucusForm.TbcEducationBackgroundForm> getBackDropDetails(@Param(value = "orgId")Long orgId);

    /**
     * 查询领导班子
     * @param orgId
     * @return
     */
    @Select("select tu1.user_id userId,tu1.`name` userName,group_concat(distinct l1.position) positionName,to2.`name` fromOrgName,lc.org_name contactOrgName\n" +
            "from t_organization to1 \n" +
            "inner join t_user_org_leader l1 on to1.owner_id=l1.org_id and l1.is_delete=2\n" +
            "inner join t_user tu1 on l1.user_id=tu1.user_id and tu1.status=1\n" +
            "inner join t_user_org_corp oc1 on tu1.user_id=oc1.user_id and oc1.is_employee=1\n" +
            "inner join t_organization to2 on oc1.organization_id=to2.organization_id and to2.status=1\n" +
            "left join t_user_org_leader_contact lc on l1.leader_id=lc.leader_id\n" +
            "where to1.status=1 and (to1.organization_id=#{orgId} or to1.org_level like concat('%-',#{orgId},'-%'))\n" +
            "group by tu1.user_id ORDER BY l1.is_head,l1.enter_time,convert(l1.`user_name` using gbk) ")
    List<PartyCaucusForm.TbcDetailsOfLeadingMembersForm> getPartyLead(@Param("orgId") Long orgId);

    /**
     * 党龄
     * @param orgId
     * @param age
     * @return
     */
    @Select("SELECT DISTINCT\n" +
            "\tCOUNT(u.joining_time) ageNumber \n" +
            "FROM\n" +
            "\tt_user u\n" +
            "\tLEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "\tLEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tu.`status` = 1 \n" +
            "\tAND o.`status` = 1 \n" +
            "\tAND o.activate = 1 \n" +
            "\tAND u.political_type IN ( 1, 5, 17, 18 ) \n" +
            "\tAND uoc.is_employee = 1 \n" +
            "\tAND o.parent_id <> - 999 \n" +
            "\tAND o.org_type_child IN ( 10280301, 10280310, 10280322, 10280303, 10280308, 10280311, 10280318, 10280304, 10280309, 10280314, 10280315, 10280319 ) \n" +
            "\tAND ( o.organization_id = ${orgId} OR o.org_level LIKE CONCAT( \"%-\", ${orgId}, \"-%\" ) ) \n" +
            "\tAND ${age} \n" +
            "ORDER BY\n" +
            "\to.seq DESC")
    Long getPartyAgeNumber(@Param(value = "orgId")Long orgId,
                           @Param(value = "age")String age);


    @Select("SELECT DISTINCT\n" +
//            "\t( SELECT op_value AS ed FROM t_option WHERE op_key IN ( u.education ) AND `code` = 1003 ) backdrop, \n"+
            "\tCOUNT(u.user_id) \n" +
            "FROM\n" +
            "\tt_user u\n" +
            "\tLEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "\tLEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tu.`status` = 1 \n" +
            "\tAND o.`status` = 1 \n" +
            "\tAND o.activate = 1 \n" +
            "\tAND u.political_type IN ( 1, 5, 17, 18 ) \n" +
            "\tAND uoc.is_employee = 1 \n" +
            "\tAND o.parent_id <> - 999 \n" +
            "\tAND o.org_type_child IN ( 10280301, 10280310, 10280322, 10280303, 10280308, 10280311, 10280318, 10280304, 10280309, 10280314, 10280315, 10280319 ) \n" +
            "\tAND ( o.organization_id = ${orgId} OR o.org_level LIKE CONCAT( \"%-\", ${orgId}, \"-%\" ) ) \n" +
            "\tAND ${eth} \n" +
            "ORDER BY\n" +
            "\to.seq DESC")
    Long getEthnicNum(@Param(value = "orgId")Long orgId,@Param(value = "eth")String ethnic);

    @Select("SELECT\n" +
            "\tunit_id,\n" +
            "\tunit_name,\n" +
            "\tnumber,\n" +
            "\tSUM( a + b + c ) AS score \n" +
            "FROM\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\tunit_id,\n" +
            "\t\tunit_name,\n" +
            "\t\tcount(*) number,\n" +
            "\t\tIFNULL( SUM( sys_score ), 0 ) a,\n" +
            "\t\tIFNULL( SUM( assmt_score ), 0 ) b,\n" +
            "\t\tIFNULL( SUM( party_score ), 0 ) c \n" +
            "\tFROM\n" +
            "\t\t`t_eval_v2_unit_score` \n" +
            "\tWHERE\n" +
            "\t\tsys_score != 0.000000 \n" +
            "\t\tOR assmt_score != 0.000000 \n" +
            "\t\tOR party_score != 0.000000 \n" +
            "\t\tAND `year` = ${year} \n" +
            "\tGROUP BY\n" +
            "\t\tunit_id \n" +
            "\t) AS L \n" +
            "GROUP BY\n" +
            "\tunit_id")
    ExamineForm getExamine(@Param(value = "regionId") Long regionId,@Param(value = "year") Integer year);

    @Select("SELECT\n" +
            "\tunit_id,\n" +
            "\tunit_name,\n" +
            "\tsum( final_score ) score \n" +
            "FROM\n" +
            "\tt_eval_v2_unit_score \n" +
            "WHERE\n" +
            "\t`year` = ${year} \n" +
            "GROUP BY\n" +
            "\tunit_id")
    RankForm getRank(@Param(value = "regionId") Long regionId,@Param(value = "year") Integer year);

    @Select("SELECT\n" +
            "\tcount( 0 ) \n" +
            "FROM\n" +
            "\tt_user_login_log \n" +
            "WHERE\n" +
            "\tlogin_status = 1 \n" +
            "\tAND login_day = ${today}")
    Double getVisitToDay(@Param(value = "today") String today);

    @Select("SELECT\n" +
            "\tcount( 0 )/(DAY(LAST_DAY(login_day))*(SELECT count(u.user_id) FROM t_user AS u INNER JOIN t_user_org_corp AS uoc ON  \n" +
            "             u.user_id = uoc.user_id  and u.`status` =1 AND uoc.is_employee = 1 \n" +
            "             INNER JOIN t_organization AS o ON uoc.organization_id = o.organization_id  \n" +
            "             AND o.`status` = 1 AND u.political_type in (1,5))) number,\n" +
            "\tDATE_FORMAT( login_day, '%Y-%m' ) AS month\n" +
            "FROM\n" +
            "\tt_user_login_log \n" +
            "WHERE\n" +
            "\tlogin_status = 1 \n" +
            "GROUP BY\n" +
            "\tMONTH (login_day)")
    List<VisitMonthForm> getVisitMonth();
}
