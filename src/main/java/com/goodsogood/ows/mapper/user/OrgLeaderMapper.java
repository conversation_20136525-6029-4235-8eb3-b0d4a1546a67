package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.user.OrgLeaderEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.mongodb.fusion.DualLifeDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface OrgLeaderMapper extends MyMapper<OrgLeaderEntity> {
    @Select("select a1.user_id as userId,a1.user_name as userName from t_user_org_leader a1\n" +
            "where a1.org_id=#{unitId} and a1.is_delete=2 ")
    List<DualLifeDetail> queryLeaderMember(@Param("unitId") Long unitId);
}
