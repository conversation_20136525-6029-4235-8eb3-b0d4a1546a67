package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020-11-04 10:57
 * @since 3.0.1
 **/
@Repository
@Mapper
public interface OrgSnapshotMapper extends MyMapper<OrgSnapshotEntity> {

    /**
     * 根据月份获取组织的总人数（不包含下级组织）
     *
     * @param orgId
     * @param dateMonth
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            "  count(0) total\n" +
            "FROM\n" +
            "  t_user_snapshot us\n" +
            "WHERE\n" +
            "  us.date_month = '${dateMonth}' \n" +
            "  AND us.region_id = #{regionId} \n" +
            " AND (us.org_id = #{orgId} OR us.org_level LIKE CONCAT( '%-', #{orgId}, '-%' )) " +
            " <if test=\"orgId == 999\"> AND us.org_level not LIKE CONCAT( '%--', #{orgId}, '-%' ) </if>" +
            " AND ( us.position_code NOT IN(${retireCode}) OR us.position_code IS NULL OR us.position_code = '' ) " +
            " AND us.political_type IN(${politicalCode}) " +
            " AND us.status = 1 " +
            "</script>")
    Long getOrgUserTotal(@Param("regionId") Long regionId,
                         @Param("orgId") Long orgId,
                         @Param("dateMonth") String dateMonth,
                         @Param("retireCode") String retireCode,
                         @Param("politicalCode") String politicalCode);

    /**
     * 获取某个月的所有基层党组织
     *
     * @param regionId
     * @param dateMonth
     * @param orgTypeChild
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " org_id \n" +
            "FROM\n" +
            "t_org_snapshot \n" +
            "WHERE\n" +
            "date_month = '${dateMonth}' \n" +
            "AND region_id = #{regionId} \n" +
            "AND `status` = 1 \n" +
            "AND org_type_child IN (${orgTypeChild})" +
            "</script>")
    Set<Long> getBaseOrgIds(@Param("regionId") Long regionId,
                            @Param("dateMonth") String dateMonth,
                            @Param("orgTypeChild") String orgTypeChild);

    /**
     * 某个组织，某个月的所有基层党组织（包含下级）
     *
     * @param regionId
     * @param dateMonth
     * @param orgId
     * @return
     */
    @Select("SELECT org_id orgId from t_org_snapshot where status =1 and region_id=#{regionId}  and date_month=#{queryDate} \n" +
            "       and org_type_child in (10280304,10280309,10280314,10280315,10280319,10280301,10280310,10280322,10280303,10280308,10280311,10280318)\n" +
            "       and (org_id = #{orgId} or org_level like CONCAT('%-', #{orgId},'-%'))")
    Set<Long> getBaseOrgIncludeChildOrgIds(@Param("regionId") Long regionId,
                                           @Param("queryDate") String dateMonth,
                                           @Param("orgId") Long orgId);
}
