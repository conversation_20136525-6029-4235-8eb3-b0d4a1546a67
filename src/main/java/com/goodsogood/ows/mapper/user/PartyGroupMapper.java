package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.user.PartyGroupEntity;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.mongodb.fusion.DualLifeDetail;
import com.goodsogood.ows.model.mongodb.fusion.LeadersContactDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/12/12
 */
@Repository
@Mapper
public interface PartyGroupMapper extends MyMapper<PartyGroupEntity> {

    @Select("select g1.group_id\n" +
            "from t_party_group g1\n" +
            "where g1.status=1 and g1.region_id=#{regionId}")
    List<Long> findGroupIdByRegion(@Param("regionId") Long regionId);

    @Select("select pwlg.party_work_leader_group_id\n" +
            "from t_party_group pg\n" +
            "LEFT JOIN t_party_work_leader_group pwlg ON pwlg.org_id = pg.org_id\n" +
            "WHERE pg.`status` = 1\n" +
            "AND pwlg.`status` = 1\n" +
            "and pg.group_id =  #{partyGroupId} and pwlg.create_time like concat(#{executeTime},'%')")
    Long existsPartyWorkLeaderGroup(@Param("partyGroupId") Long id, @Param("executeTime") String executeTime);

    @Select("select mucp.meeting_user_commend_penalize_id\n" +
            "from t_party_group pg\n" +
            "LEFT JOIN t_organization o ON o.owner_id = pg.org_id\n" +
            "LEFT JOIN t_user_org_corp uoc ON uoc.organization_id = o.organization_id AND uoc.is_employee = 1\n" +
            "LEFT JOIN t_user u ON u.user_id = uoc.user_id\n" +
            "LEFT JOIN t_meeting_user_commend_penalize mucp ON mucp.user_id = u.user_id\n" +
            "where u.`status` = 1\n" +
            "AND pg.`status` = 1\n" +
            "AND mucp.type = 2\n" +
            "AND mucp.`status` = 1\n" +
            "AND mucp.approval_status = 2\n" +
            "AND pg.group_id = #{groupId}\n" +
            "AND DATE_FORMAT(mucp.effective_time,'%Y-%m') = #{executeTime}"
    )
    List<Long> partyGroupExistPunish(@Param("groupId") Long id, @Param("executeTime") String executeTime);

    @Select("select  if(count(distinct u1.user_id)=count(distinct uo1.user_id) and count(distinct u1.user_id) is not null and count(distinct u1.user_id) >0,1,0)\n" +
            "from t_party_group p1\n" +
            "inner join t_party_group_user u1 on p1.group_id=u1.group_id and u1.`status`=1\n" +
            "left join t_party_group_user_org uo1 on u1.group_user_id=uo1.group_user_id and uo1.type=#{userOrgType}\n" +
            "where p1.group_id=#{partyGroupId} ")
    Boolean partyGroupExistWorkContact(@Param("partyGroupId") Long id, @Param("userOrgType") Integer userOrgType);

    /**
     * 是否建立党建工作小组
     *
     * @param orgId 单位ID
     * @return long
     */
    @Select("SELECT pg.group_id\n" +
            "FROM t_party_group pg\n" +
            "\tLEFT JOIN t_party_work_leader_group pwlg ON pwlg.org_id = pg.org_id \n" +
            "WHERE pg.`status` = 1 \n" +
            "\tAND pwlg.`status` = 1 \n" +
            "\tAND pg.org_id = #{orgId}")
    Long existLeaderGroup(@Param("orgId") Long orgId);

    /**
     * @param orgId 单位ID
     * @param type  2-建立党支部工作联系点制度 3-建立干部基层联系点制度
     * @return true or false
     */
    @Select("select  if(count(distinct u1.user_id)=count(distinct uo1.user_id) and count(distinct u1.user_id) is not null and count(distinct u1.user_id) >0,1,0)\n" +
            "from t_party_group p1\n" +
            "inner join t_party_group_user u1 on p1.group_id=u1.group_id and u1.`status`=1\n" +
            "left join t_party_group_user_org uo1 on u1.group_user_id=uo1.group_user_id and uo1.type = #{type} \n" +
            "where p1.org_id = #{orgId}")
    Boolean existWorkContact(@Param("orgId") Long orgId, @Param("type") Integer type);

    @Select("select t2.user_id as userId,t2.user_name as userName from t_party_group t1\n" +
            "join t_party_group_user t2 on t1.group_id=t2.group_id and t2.status=1\n" +
            "where t1.org_id=#{unitId} and t1.status=1 ")
    List<DualLifeDetail> queryPartyMember(@Param("unitId") Long unitId);

    @Select("select t2.user_id userId,t2.user_name userName,t3.org_id orgId,t3.org_name contactOrg from t_party_group t1\n" +
            "join t_party_group_user t2 on t1.group_id=t2.group_id and t2.status=1\n" +
            "left join t_party_group_user_org t3 on t2.group_user_id=t3.group_user_id and t3.type=2\n" +
            "where t1.org_id=#{unitId} and t1.status=1  group by  t2.user_id,t3.org_id ")
    List<LeadersContactDetail> queryPartyMemberOrg(@Param("unitId") Long unitId);

    @Select("""
               SELECT DISTINCT pgu.user_id
               FROM t_party_group AS pg
               	LEFT JOIN t_party_group_user AS pgu ON pg.group_id = pgu.group_id
               	LEFT JOIN t_user AS u ON pgu.user_id = u.user_id
               WHERE
               	pg.`status` = 1
               	AND u.`status` = 1
               	AND pgu.`status` = 1
               	AND pg.org_id = #{unitId}
            """)
    List<Long> findPartyGroupUser(@Param("unitId") Long unitId);
}
