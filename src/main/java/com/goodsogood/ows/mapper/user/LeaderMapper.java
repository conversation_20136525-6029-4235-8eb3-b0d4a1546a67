package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.user.OrgLeaderEntity;
import com.goodsogood.ows.model.vo.sas.SasLeaderOrgForm;
import com.goodsogood.ows.model.vo.user.LeaderContactForm;
import com.goodsogood.ows.service.rank.RemoteRuleWryService;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface LeaderMapper extends MyMapper<OrgLeaderEntity> {

    @Select("select uol.user_id userId , u.`name` userName, o.organization_id orgId, o.`name` orgName," +
            " o.parent_id parentOrgId,o.region_id as regionId, o.org_type_child orgType, o.org_level orgLevel, " +
            "CASE WHEN o.is_retire != 1 THEN 2 WHEN o.is_retire IS NULL THEN 2 WHEN o.is_retire = 0" +
            " THEN 2 ELSE o.is_retire end isRetire " +
            " from t_user_org_leader uol,  " +
            "    t_user u,  " +
            "    t_user_org_corp uoc,  " +
            "    t_organization o " +
            " where u.user_id = uol.user_id " +
            "  and uoc.user_id = uol.user_id " +
            "  and uoc.region_id = uol.region_id" +
            "  and uoc.organization_id = o.organization_id " +
            "  and o.org_type = 102803" +
            "  and uol.is_delete = 2 " +
            "  and DATE_FORMAT(uol.enter_time,'%Y-%m-%d') <= DATE_FORMAT(SYSDATE(),'%Y-%m-%d') " +
            "  and uoc.is_employee = 1 " +
            "  and u.political_type in (1, 5, 17 ,18)")
    List<SasLeaderOrgForm> findAllLeaderList();

    @Select("select ol.leader_id,tu1.user_id as userId," +
            "       tu1.`name`," +
            "       to1.organization_id as organizationId," +
            "       to1.`name` as orgName," +
            "       olc.org_id as orgId," +
            "       olc.org_name as contactName\n" +
            "from t_user_org_leader ol\n" +
            "inner join t_user_org_leader_contact olc on ol.leader_id=olc.leader_id and olc.select_type=1\n" +
            "inner join t_organization to2 on to2.organization_id=olc.org_id and to2.`status`=1\n" +
            "inner join t_user tu1 on tu1.user_id=ol.user_id and tu1.`status`=1\n" +
            "inner join t_user_org_corp uoc on ol.user_id=uoc.user_id and uoc.region_id=#{regionId} and uoc.is_employee=1\n" +
            "inner join t_organization to1 on uoc.organization_id=to1.organization_id and to1.`status`=1 \n" +
            "where ol.region_id=#{regionId} and ol.is_delete=2 and ol.user_id=#{userId}")
    List<LeaderContactForm> getLeaderContact(Long regionId, Long userId);

    @Select("select count(ul.leader_id) != 0 and count(ul.leader_id)=count(lc.leader_contact_id)\n" +
            "from t_user_org_leader ul \n" +
            "inner join (select distinct to2.org_id\n" +
            "from t_org_snapshot to1 \n" +
            "inner join t_org_snapshot to2 on to1.status=1 and to2.status=1 and to2.date_month=#{queryDate} and to1.owner_id=to2.org_id and to1.org_pid=3\n" +
            "where to1.date_month=#{queryDate} and to1.org_id=#{orgId}) unit on ul.org_id=unit.org_id\n" +
            "inner join t_user_snapshot tu1 on tu1.status=1 and tu1.date_month=#{queryDate} and ul.user_id=tu1.user_id\n" +
            "left join t_user_org_leader_contact lc on ul.leader_id=lc.leader_id\n" +
            "where ul.is_delete=2 and UNIX_TIMESTAMP(ul.create_time)<= UNIX_TIMESTAMP(last_day(CONCAT(#{queryDate},'-01')))+86399 ")
    Boolean isCreateLeaderContact(@Param("orgId") Long orgId, @Param("queryDate") String queryDate);

    @Select("select unit.org_id id,count(ul.leader_id) != 0 and count(ul.leader_id)=count(lc.leader_contact_id) aBoolean\n" +
            "from t_user_org_leader ul \n" +
            "inner join (select to1.org_id,to2.org_id unit_id\n" +
            "from t_org_snapshot to1 \n" +
            "inner join t_org_snapshot to2 on to1.status=1 and to2.status=1 and to2.date_month=#{queryDate} and to1.owner_id=to2.org_id and to1.org_pid=3\n" +
            "where to1.date_month=#{queryDate} and to1.org_id in (${ids})) unit on ul.org_id=unit.unit_id\n" +
            "inner join t_user_snapshot tu1 on tu1.status=1 and tu1.date_month=#{queryDate} and ul.user_id=tu1.user_id\n" +
            "left join t_user_org_leader_contact lc on ul.leader_id=lc.leader_id\n" +
            "where ul.is_delete=2 and UNIX_TIMESTAMP(ul.create_time)<= UNIX_TIMESTAMP(last_day(CONCAT(#{queryDate},'-01')))+86399 group by  unit.org_id")
    List<RemoteRuleWryService.QueryResult> isCreateLeaderContact2(@Param("ids") String ids, @Param("queryDate") String queryDate);

    @Select("select  count(ul.leader_id) is not null and count(ul.leader_id)!=0 and count(ul.leader_id)=sum(if(lc.org_id is not null and tu1.org_id!=lc.org_id,1,0))\n" +
            "from t_user_org_leader ul \n" +
            "inner join (select distinct to2.org_id\n" +
            "from t_org_snapshot to1 \n" +
            "inner join t_org_snapshot to2 on to1.status=1 and to2.status=1 and to2.date_month=#{queryDate} and to1.owner_id=to2.org_id and to1.org_pid=3\n" +
            "where to1.date_month=#{queryDate} and to1.org_id=#{orgId}) unit on ul.org_id=unit.org_id\n" +
            "inner join t_user_snapshot tu1 on tu1.status=1 and tu1.date_month=#{queryDate} and ul.user_id=tu1.user_id\n" +
            "left join t_user_org_leader_contact lc on ul.leader_id=lc.leader_id\n" +
            "where ul.is_delete=2 and UNIX_TIMESTAMP(ul.create_time)<= UNIX_TIMESTAMP(last_day(CONCAT(#{queryDate},'-01')))+86399 ")
    Boolean checkLeaderContactEqual(@Param("orgId") Long orgId, @Param("queryDate") String queryDate);

    @Select("select unit.org_id id,count(ul.leader_id) is not null and count(ul.leader_id)!=0 and count(ul.leader_id)=sum(if(lc.org_id is not null and tu1.org_id!=lc.org_id,1,0)) aBoolean \n" +
            "from t_user_org_leader ul \n" +
            "inner join (select  to1.org_id, to2.org_id unit_id\n" +
            "from t_org_snapshot to1 \n" +
            "inner join t_org_snapshot to2 on to1.status=1 and to2.status=1 and to2.date_month=#{queryDate} and to1.owner_id=to2.org_id and to1.org_pid=3\n" +
            "where to1.date_month=#{queryDate} and to1.org_id in (${ids}) ) unit on ul.org_id=unit.unit_id\n" +
            "inner join t_user_snapshot tu1 on tu1.status=1 and tu1.date_month=#{queryDate} and ul.user_id=tu1.user_id\n" +
            "left join t_user_org_leader_contact lc on ul.leader_id=lc.leader_id\n" +
            "where ul.is_delete=2 and UNIX_TIMESTAMP(ul.create_time)<= UNIX_TIMESTAMP(last_day(CONCAT(#{queryDate},'-01')))+86399 group by unit.org_id")
    List<RemoteRuleWryService.QueryResult> checkLeaderContactEqual2(@Param("ids") String ids, @Param("queryDate") String queryDate);

    /**
     * 查询领导班子信息
     *
     * @param userId    用户id
     * @param queryDate 查询日志
     * @return OrgLeaderEntity
     */
    @Select(
            "<script> "
                    + "select distinct user_id from t_user_org_leader "
                    + "where DATE_FORMAT(create_time,'%Y-%m') &lt;= #{queryDate}"
                    + " and is_delete=2 and user_id=#{userId} "
                    + " </script>")
    List<OrgLeaderEntity> findLeaderInfoByUserId(
            @Param("userId") Long userId, @Param("queryDate") String queryDate);


    @Select("SELECT\n" +
            "\tcount(leader_id)\n" +
            "FROM\n" +
            "\t`t_user_org_leader` \n" +
            "WHERE\n" +
            "\torg_id IN (\n" +
            "\tSELECT\n" +
            "\t\torganization_id \n" +
            "\tFROM\n" +
            "\t\tt_organization \n" +
            "\tWHERE\n" +
            "\t( org_level LIKE '%-${ownerId}-%' OR organization_id = ${ownerId} ) \n" +
            "\tAND `status` = 1)")
    Integer getAllOrgChildLeaderCount(@Param("ownerId") Long ownerId);
}
