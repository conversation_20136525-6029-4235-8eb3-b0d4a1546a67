package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.model.vo.tbc.PartyCaucusUserForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface TbcPartyUserMapper {
    /**
     * 获取民族类型
     *
     * @param orgId 组织ID
     * @return
     */
    @Select("SELECT temp.id nationId,temp.nation nationName FROM (\n" +
            "SELECT \n" +
            "  ( SELECT op_key AS ed FROM t_option WHERE op_key IN ( u.ethnic ) AND `code` = 1004 ) id,\n" +
            "  ( SELECT op_value AS ed FROM t_option WHERE op_key IN ( u.ethnic ) AND `code` = 1004 ) nation\n" +
            "FROM\n" +
            "  t_user u\n" +
            "  LEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "  LEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "  u.`status` = 1 \n" +
            "  AND o.`status` = 1 \n" +
            "  AND o.activate = 1 \n" +
            "  AND u.political_type IN ( 1, 5, 17, 18 ) \n" +
            "  AND uoc.is_employee = 1 \n" +
            "  AND o.parent_id <> - 999 \n" +
            "  AND o.org_type_child IN ( 10280301, 10280310, 10280322, 10280303, 10280308, 10280311, 10280318, 10280304, 10280309, 10280314, 10280315, 10280319 ) \n" +
            "  AND ( o.organization_id = ${orgId} OR o.org_level LIKE CONCAT( \"%-\", ${orgId}, \"-%\" ) ) \n" +
            ") temp \n" +
            "GROUP BY temp.id \n" +
            "ORDER BY nation  DESC")
    List<PartyCaucusUserForm.TbcEthnicDistributionOfPartyMembers> getNationDetails(@Param(value = "orgId") Long orgId);

    /**
     * 民族详情
     *
     * @param orgId 组织id
     * @param age 年龄字符串
     * @param ethnicId 民族id
     * @return
     */
    @Select("SELECT DISTINCT\n" +
            "\tu.`user_id` memberId,\n" +
            "\tu.`name` memberName,\n" +
            "\to.`name` orgName,\n" +
            "\t5 AS seq,\n" +
            "\t( SELECT op_value AS ed FROM t_option WHERE op_key IN ( u.education ) AND `code` = 1003 ) AS educationBackground,\n" +
            "\t( SELECT op_value as ed FROM t_option WHERE op_key IN(u.ethnic) AND `code`=1004) memberNation,\n" +
            "\tu.age age\n" +
            "FROM\n" +
            "\tt_user u\n" +
            "\tLEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "\tLEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tu.`status` = 1 \n" +
            "\tAND o.`status` = 1 \n" +
            "\tAND o.activate = 1 \n" +
            "\tAND u.political_type IN ( 1, 5, 17, 18 ) \n" +
            "\tAND uoc.is_employee = 1 \n" +
            "\tAND o.parent_id <> - 999 \n" +
            "\tAND o.org_type_child IN ( 10280301, 10280310, 10280322, 10280303, 10280308, 10280311, 10280318, 10280304, 10280309, 10280314, 10280315, 10280319 ) \n" +
            "\tAND ( o.organization_id = ${orgId} OR o.org_level LIKE CONCAT( \"%-\", ${orgId}, \"-%\" ) ) \n" +
            "\tAND u.ethnic =${ethnicId}\n" +
            "ORDER BY\n" +
            "\to.seq DESC")
    List<PartyCaucusUserForm.TbcPNationalDetailsFrom>getNationalDetails(@Param(value = "orgId") Long orgId,
                                                                                   @Param(value = "ethnicId")Long ethnicId);

    /**
     * 年龄详情
     * <p>
     * * @param orgId 组织id
     * * @param student 学历字符串
     * * @param age 年龄字符串
     * * @param ethnic 民族字符串
     *
     * @return
     */
    @Select("SELECT DISTINCT\n" +
            "\tu.`user_id` memberId,\n" +
            "\tu.`name` memberName,\n" +
            "\to.`name` orgName,\n" +
            "\t5 AS seq,\n" +
            "\t( SELECT op_value AS ed FROM t_option WHERE op_key IN ( u.education ) AND `code` = 1003 ) AS educationBackground,\n" +
            "\t( SELECT op_value as ed FROM t_option WHERE op_key IN(u.ethnic) AND `code`=1004) memberNation,\n" +
            "\tu.age age\n" +
            "FROM\n" +
            "\tt_user u\n" +
            "\tLEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "\tLEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tu.`status` = 1 \n" +
            "\tAND o.`status` = 1 \n" +
            "\tAND o.activate = 1 \n" +
            "\tAND u.political_type IN ( 1, 5, 17, 18 ) \n" +
            "\tAND uoc.is_employee = 1 \n" +
            "\tAND o.parent_id <> - 999 \n" +
            "\tAND o.org_type_child IN ( 10280301, 10280310, 10280322, 10280303, 10280308, 10280311, 10280318, 10280304, 10280309, 10280314, 10280315, 10280319 ) \n" +
            "\tAND ( o.organization_id = ${orgId} OR o.org_level LIKE CONCAT( \"%-\", ${orgId}, \"-%\" ) ) \n" +
            "\tAND ${age}\n" +
            "ORDER BY\n" +
            "\tu.age ASC")
    List<PartyCaucusUserForm.TbcAgeForDetailsFrom> getAgeDetail(@Param(value = "orgId") Long orgId,
                                                                @Param(value = "age") String age);

    /**
     * 学历详情
     *
     * @param orgId   组织id
     * @param student 学历字符串
     * @param age     年龄字符串
     * @param ethnic  民族字符串
     * @return
     */
    @Select("SELECT DISTINCT\n" +
            "\tu.`user_id` memberId,\n" +
            "\tu.`name` memberName,\n" +
            "\to.`name` orgName,\n" +
            "\t5 AS seq,\n" +
            "\t( SELECT op_value AS ed FROM t_option WHERE op_key IN ( u.education ) AND `code` = 1003 ) AS educationBackground,\n" +
            "\t( SELECT op_value as ed FROM t_option WHERE op_key IN(u.ethnic) AND `code`=1004) memberNation,\n" +
            "\tu.age age\n" +
            "FROM\n" +
            "\tt_user u\n" +
            "\tLEFT JOIN t_user_org_corp uoc ON u.user_id = uoc.user_id\n" +
            "\tLEFT JOIN t_organization o ON uoc.organization_id = o.organization_id \n" +
            "WHERE\n" +
            "\tu.`status` = 1 \n" +
            "\tAND o.`status` = 1 \n" +
            "\tAND o.activate = 1 \n" +
            "\tAND u.political_type IN ( 1, 5, 17, 18 ) \n" +
            "\tAND uoc.is_employee = 1 \n" +
            "\tAND o.parent_id <> - 999 \n" +
            "\tAND o.org_type_child IN ( 10280301, 10280310, 10280322, 10280303, 10280308, 10280311, 10280318, 10280304, 10280309, 10280314, 10280315, 10280319 ) \n" +
            "\tAND ( o.organization_id = ${orgId} OR o.org_level LIKE CONCAT( \"%-\", ${orgId}, \"-%\" ) ) \n" +
            "\tAND ${student}\n" +
            "\tAND ${age}\n" +
            "\tAND ${ethnic}\n" +
            "ORDER BY\n" +
            "\to.seq DESC")
    List<PartyCaucusUserForm.TbcDegreeInDetailsFrom> getDegreeDetails(@Param(value = "orgId") Long orgId,
                                                                      @Param(value = "student") String student,
                                                                      @Param(value = "age") String age,
                                                                      @Param(value = "ethnic") String ethnic);
}
