package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.sas.OrgGroupEntity;
import com.goodsogood.ows.model.vo.sas.OrgGroupForm;
import com.goodsogood.ows.model.vo.user.OrgGroupVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description 党小组Mapper
 * @date 2019/12/19
 */
@Repository
@Mapper
public interface OrgGroupMapper extends MyMapper<OrgGroupEntity> {

    /**
     * 查询党小组信息
     *
     * @param orgId
     * @return
     */
    @Select("select tupg.org_group_id orgGroupId,tupg.org_id orgId" +
            ",tupg.org_group_name orgGroupName,tupg.create_date createDate from " +
            " t_user_org_group tupg  where tupg.org_id=#{orgId} and tupg.is_delete=1 order by tupg.org_group_id ")
    List<OrgGroupForm> list(@Param("orgId") long orgId);


    /**
     * 查询支部下面党小组信息
     *
     * @param orgId
     * @return
     */
    @Select("SELECT uog.link_org_id FROM t_organization o INNER JOIN t_user_org_group uog \n" +
            "ON o.organization_id = uog.org_id \n" +
            "AND uog.is_delete =1   \n" +
            "AND o.`status` = 1\n" +
            "WHERE o.organization_id = #{orgId}")
    List<Long> getPartyGroupInfo(@Param("orgId") long orgId);

    /**
     * 查询党小组数量
     *
     * @param orgId     组织id
     * @param queryDate 查询月份
     * @return int 党小组数量
     */
    @Select(
            "<script> "
            + "select count(*) from "
            + " t_user_org_group tupg  where DATE_FORMAT(create_time,'%Y-%m') &lt;= #{queryDate} " +
            " and tupg.is_delete=1 and  tupg.org_id=#{orgId}  "
            + " </script>")
    Integer partyGroupNumByOrgId(@Param("orgId") long orgId, @Param("queryDate") String queryDate);

    @Select("select to1.organization_id orgId,og.org_group_name orgGroupName\n" +
            "from t_organization to1  \n" +
            "inner join t_user_org_group og on to1.organization_id=og.org_id and og.is_delete=1 and og.create_date < now()\n" +
            "where to1.status=1 and (to1.org_level like concat('%-',#{orgId},'-%') or to1.organization_id=#{orgId})\n" +
            "order by to1.seq desc")
    List<OrgGroupEntity> selectGroupByOrg(@Param("orgId") Long orgId);

    @Select("select o1.organization_id \n" +
            " from t_organization o1\n" +
            " left join (select count(distinct c1.user_id) numbers,c1.organization_id from t_user_org_corp c1 " +
            " where c1.is_employee=1 " +
            " and c1.region_id=#{regionId} group by c1.organization_id) c2 on o1.organization_id=c2.organization_id\n" +
            " left join (select distinct org_id from t_user_org_group where region_id=#{regionId} and is_delete=1)  " +
            " g1 on o1.organization_id=g1.org_id\n" +
            " where o1.region_id=#{regionId}" +
            " and c2.numbers >20 and g1.org_id is null \n" +
            " and o1.organization_id in (${join})")
    List<Long> findNotExisGroupNumByOrg(@Param("regionId") Long regionId, @Param("join") String join);


    @Select("""
            select COUNT(numbers)
             from t_organization o1
             left join (select count(distinct c1.user_id) numbers,c1.organization_id from t_user_org_corp c1
             where c1.is_employee=1
             and c1.region_id=#{regionId} group by c1.organization_id) c2 on o1.organization_id=c2.organization_id
             left join (select distinct org_id from t_user_org_group where region_id=#{regionId} and is_delete=1)
             g1 on o1.organization_id=g1.org_id
             where o1.region_id=#{regionId}
             and c2.numbers >20 and g1.org_id is null
             and o1.organization_id =#{orgId}
            """)
    Integer findExistGroupNumber(@Param("regionId") Long regionId, @Param("orgId") Long orgId);

    @Select("SELECT \n" +
            "uog.org_group_name as orgGroupName,u.`name`,u.head_url as headUrl, uopm.position_name as orgPeriodName \n" +
            "FROM t_organization o \n" +
            "LEFT JOIN t_user_org_group uog ON o.organization_id = uog.org_id AND uog.is_delete =1\n" +
            "INNER JOIN t_user_org_group_member uogm on uog.org_group_id = uogm.org_group_id AND uogm.is_delete = 1\n" +
            "INNER JOIN t_user u ON uogm.user_id = u.user_id AND u.`status` =1\n" +
            "LEFT JOIN t_user_org_period uop ON o.organization_id = uop.org_id AND uop.is_delete = 2 AND uop.is_valid =1\n" +
            "LEFT JOIN t_user_org_period_member uopm on uop.period_id = uopm.period_id AND uopm.user_id = u.user_id AND uopm.is_delete = 2\n" +
            "WHERE \n" +
            "u.political_type in (1,5,17,18)\n" +
            "AND o.organization_id = #{orgId}\n" +
            " ORDER BY uog.org_group_id,if(uopm.position_code is  NULL,1,0) , uopm.position_code ")
    List<OrgGroupVo> queryGroupuser(@Param("orgId") Long orgId);

    @Select("""
                <script>
                    SELECT
                    	tmu.user_id AS userId,
                    	count( tm.meeting_id ) AS number
                    FROM
                    	t_meeting tm
                    	LEFT JOIN t_meeting_user tmu ON tm.meeting_id = tmu.meeting_id
                    	AND tmu.tag = 5
                    	LEFT JOIN t_meeting_type tmt ON tm.meeting_id = tmt.meeting_id
                    	AND tm.is_del != 1
                    	LEFT JOIN t_type t ON tmt.type_id = t.type_id
                    WHERE
                    	t.type_id = #{typeId}
                    	AND tm.`status` IN ( 7, 12, 13, 14 )
                    	AND tmu.user_id IS NOT NULL
                    	AND tmu.user_id > 0
                     	AND tm.start_time LIKE CONCAT(#{year},'%')
                     	<if test="userList != null and userList.size > 0">
                        AND tmu.user_id IN
                            <foreach item="userId" collection="userList" open="(" separator="," close=")">#{userId}</foreach>
                     	</if>
                    GROUP BY  tmu.user_id
                </script>
            """)
    List<UserInfoBase> findLessionsByUser(@Param("year") Integer year, @Param("typeId") Integer typeId,
                                          @Param("userList") List<Long> userList);
}






























