package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.user.UserLoginLogEntity;
import com.goodsogood.ows.model.vo.LoginValue;
import com.goodsogood.ows.model.vo.VisitLoginForm;
import com.goodsogood.ows.model.vo.user.UserLoginRankForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface UserLoginLogMapper extends MyMapper<UserLoginLogEntity> {

    /**
     * 首页获取登录次数
     *
     * @param year
     * @return
     */
    @Select("<script>" +
            "select count(0) \n" +
            "  from t_user_login_log \n" +
            "where year(login_date) = #{year}" +
            "  AND region_id = #{regionId} \n" +
            "</script>")
    Integer getUserLoginTotal(@Param("regionId") Long regionId,
                              @Param("year") Integer year);

    /**
     * 首页查询一年中登录最高的一天
     *
     * @param regionId
     * @param year
     * @return
     */
    @Select("<script>" +
            "SELECT MAX( a.total ) as num, a.date as `date`\n" +
            "FROM (\n" +
            "   SELECT count( 0 ) total, DATE_FORMAT( login_date, '%Y-%m-%d' ) `date` \n" +
            "     FROM t_user_login_log \n" +
            "    WHERE YEAR ( login_date ) = #{year} \n" +
            "      AND region_id = #{regionId}\n" +
            "    GROUP BY date \n" +
            "    ORDER BY total DESC) a" +
            "</script>")
    UserLoginRankForm getMaxDayLogin(@Param("regionId") Long regionId,
                                     @Param("year") Integer year);

    /**
     * 首页查询一年中登录次数最多的一月
     *
     * @param regionId
     * @param year
     * @return
     */
    @Select("<script>" +
            "SELECT MAX( a.total ) num, a.`month`\n" +
            "FROM (\n" +
            "   SELECT count( 0 ) total, MONTH(login_date) `month` \n" +
            "     FROM t_user_login_log \n" +
            "    WHERE YEAR ( login_date ) = #{year} \n" +
            "      AND region_id = #{regionId}\n" +
            "    GROUP BY `month` \n" +
            "    ORDER BY total DESC) a" +
            "</script>")
    UserLoginRankForm getMaxMonthLogin(@Param("regionId") Long regionId,
                                       @Param("year") Integer year);

    /**
     * 获取登录次数
     *
     * @param orgId
     * @param year
     * @return
     */
    @Select("<script>" +
            "select count(0) \n" +
            "  from t_user_login_log \n" +
            "where year(login_date) = #{year} \n" +
            "  AND org_id in (\n" +
            "select organization_id \n" +
            "  from t_organization \n" +
            " where `status` = 1 \n" +
            "   AND (organization_id = #{orgId} " +
            "    OR org_level LIKE CONCAT('%-', #{orgId}, '-%')))" +
            "</script>")
    Integer getUserLoginTotalByOrg(@Param("orgId") Long orgId,
                                   @Param("year") Integer year);

    /**
     * 获取登录次数
     *
     * @param orgIds
     * @param year
     * @return
     */
    @Select("<script>" +
            "<foreach item=\"orgId\" collection=\"orgIds\" separator=\"union all\" >" +
            "select #{orgId} as id, count(0) total\n" +
            "  from t_user_login_log \n" +
            "where year(login_date) = #{year} \n" +
            "  AND org_id in (\n" +
            "select organization_id \n" +
            "  from t_organization \n" +
            " where `status` = 1 \n" +
            "   AND (organization_id = #{orgId} " +
            "    OR org_level LIKE CONCAT('%-', #{orgId}, '-%')))" +
            "</foreach>" +
            "</script>")
    List<LoginValue> getUserLoginTotalByOrgList(@Param("orgIds") List<Long> orgIds,
                                                @Param("year") Integer year);


    /**
     * 获取历史登录次数
     *
     * @param orgId
     * @param year
     * @return
     */
    @Select("<script>" +
            "select count(0) \n" +
            "  from t_user_login_log \n" +
            "where year(login_date) = #{year} \n" +
            "  AND org_id in (\n" +
            "select org_id \n" +
            "  from t_org_snapshot \n" +
            " where `status` = 1 \n" +
            "   AND date_month = #{dateMonth} \n" +
            "   AND (org_id = #{orgId} \n" +
            "    OR org_level LIKE CONCAT('%-', #{orgId}, '-%'))) \n" +
            "</script>")
    Integer getHistoryUserLoginTotalByOrg(@Param("orgId") Long orgId,
                                          @Param("year") Integer year,
                                          @Param("dateMonth") String dateMonth);

    /**
     * 获取历史登录次数
     *
     * @param orgIds
     * @param year
     * @return
     */
    @Select("<script>" +
            "<foreach item=\"orgId\" collection=\"orgIds\" separator=\"union all\"> " +
            "select #{orgId} as id, count(0) total\n" +
            "  from t_user_login_log \n" +
            "where year(login_date) = #{year} \n" +
            "  AND org_id in (\n" +
            "select org_id \n" +
            "  from t_org_snapshot \n" +
            " where `status` = 1 \n" +
            "   AND date_month = #{dateMonth} \n" +
            "   AND (org_id = #{orgId} \n" +
            "    OR org_level LIKE CONCAT('%-', #{orgId}, '-%'))) \n" +
            "</foreach>" +
            "</script>")
    List<LoginValue> getHistoryUserLoginTotalByOrgList(@Param("orgIds") List<Long> orgIds,
                                                       @Param("year") Integer year,
                                                       @Param("dateMonth") String dateMonth);

    /**
     * 查询一年中登录最高的一天
     *
     * @param orgId
     * @param year
     * @return
     */
    @Select("<script>" +
            "SELECT MAX( a.total ) as num, a.date as `date`\n" +
            "FROM (\n" +
            "   SELECT count( 0 ) total, DATE_FORMAT( login_date, '%Y-%m-%d' ) `date` \n" +
            "     FROM t_user_login_log \n" +
            "    WHERE YEAR ( login_date ) = #{year} \n" +
            "      AND org_id IN (\n" +
            "        SELECT organization_id \n" +
            "          FROM t_organization \n" +
            "         WHERE `status` = 1 \n" +
            "           AND (organization_id = #{orgId} OR org_level LIKE CONCAT( '%-', #{orgId}, '-%' ))) \n" +
            "    GROUP BY date \n" +
            "    ORDER BY total DESC) a" +
            "</script>")
    UserLoginRankForm getMaxDayLoginByOrg(@Param("orgId") Long orgId,
                                          @Param("year") Integer year);

    /**
     * 查询一年中登录最高的一天
     *
     * @param orgIds
     * @param year
     * @return
     */
    @Select("<script>" +
            "<foreach item=\"orgId\" collection=\"orgIds\" separator=\"union all\"> " +
            "SELECT #{orgId} orgId, MAX( a.total ) as num, a.date as `date`\n" +
            "FROM (\n" +
            "   SELECT count( 0 ) total, DATE_FORMAT( login_date, '%Y-%m-%d' ) `date` \n" +
            "     FROM t_user_login_log \n" +
            "    WHERE YEAR ( login_date ) = #{year} \n" +
            "      AND org_id IN (\n" +
            "        SELECT organization_id \n" +
            "          FROM t_organization \n" +
            "         WHERE `status` = 1 \n" +
            "           AND (organization_id = #{orgId} OR org_level LIKE CONCAT( '%-', #{orgId}, '-%' ))) \n" +
            "    GROUP BY date \n" +
            "    ORDER BY total DESC) a" +
            "</foreach>" +
            "</script>")
    List<UserLoginRankForm> getMaxDayLoginByOrgList(@Param("orgIds") List<Long> orgIds,
                                                    @Param("year") Integer year);

    /**
     * 查询历史一年中登录最高的一天
     *
     * @param orgId
     * @param year
     * @return
     */
    @Select("<script>" +
            "SELECT MAX( a.total ) as num, a.date as `date`\n" +
            "FROM (\n" +
            "   SELECT count( 0 ) total, DATE_FORMAT( login_date, '%Y-%m-%d' ) `date` \n" +
            "     FROM t_user_login_log \n" +
            "    WHERE YEAR ( login_date ) = #{year} \n" +
            "      AND org_id IN (\n" +
            "        SELECT org_id \n" +
            "          FROM t_org_snapshot \n" +
            "         WHERE `status` = 1 \n" +
            "           AND date_month = #{dateMonth} \n" +
            "           AND (org_id = #{orgId} OR org_level LIKE CONCAT( '%-', #{orgId}, '-%' ))) \n" +
            "    GROUP BY date \n" +
            "    ORDER BY total DESC) a" +
            "</script>")
    UserLoginRankForm getHistoryMaxDayLoginByOrg(@Param("orgId") Long orgId,
                                                 @Param("year") Integer year,
                                                 @Param("dateMonth") String dateMonth);

    /**
     * 查询历史一年中登录最高的一天 - 批量
     *
     * @param orgIds
     * @param year
     * @return
     */
    @Select("<script>" +
            "<foreach item=\"orgId\" collection=\"orgIds\" separator=\"union all\"> " +
            "SELECT #{orgId} orgId, MAX( a.total ) as num, a.date as `date`\n" +
            "FROM (\n" +
            "   SELECT count( 0 ) total, DATE_FORMAT( login_date, '%Y-%m-%d' ) `date` \n" +
            "     FROM t_user_login_log \n" +
            "    WHERE YEAR ( login_date ) = #{year} \n" +
            "      AND org_id IN (\n" +
            "        SELECT org_id \n" +
            "          FROM t_org_snapshot \n" +
            "         WHERE `status` = 1 \n" +
            "           AND date_month = #{dateMonth} \n" +
            "           AND (org_id = #{orgId} OR org_level LIKE CONCAT( '%-', #{orgId}, '-%' ))) \n" +
            "    GROUP BY date \n" +
            "    ORDER BY total DESC) a" +
            "</foreach>" +
            "</script>")
    List<UserLoginRankForm> getHistoryMaxDayLoginByOrgList(@Param("orgIds") List<Long> orgIds,
                                                           @Param("year") Integer year,
                                                           @Param("dateMonth") String dateMonth);

    /**
     * 查询一年中登录次数最多的一月
     *
     * @param orgId
     * @param year
     * @return
     */
    @Select("<script>" +
            "SELECT MAX( a.total ) num, a.`month`\n" +
            "FROM (\n" +
            "   SELECT count( 0 ) total, MONTH(login_date) `month` \n" +
            "     FROM t_user_login_log \n" +
            "    WHERE YEAR ( login_date ) = #{year} \n" +
            "      AND org_id IN (\n" +
            "        SELECT org_id \n" +
            "          FROM t_org_snapshot \n" +
            "         WHERE `status` = 1 \n" +
            "           AND date_month = #{dateMonth} \n" +
            "           AND (org_id = #{orgId} OR org_level LIKE CONCAT( '%-', #{orgId}, '-%' ))) \n" +
            "    GROUP BY `month` \n" +
            "    ORDER BY total DESC) a" +
            "</script>")
    UserLoginRankForm getHistoryMaxMonthLoginByOrg(@Param("orgId") Long orgId,
                                                   @Param("year") Integer year,
                                                   @Param("dateMonth") String dateMonth);

    /**
     * 查询一年中登录次数最多的一月 - 批量
     *
     * @param orgIds
     * @param year
     * @return
     */
    @Select("<script>" +
            "<foreach item=\"orgId\" collection=\"orgIds\" separator=\"union all\"> " +
            "SELECT #{orgId} orgId, MAX( a.total ) num, a.`month`\n" +
            "FROM (\n" +
            "   SELECT count( 0 ) total, MONTH(login_date) `month` \n" +
            "     FROM t_user_login_log \n" +
            "    WHERE YEAR ( login_date ) = #{year} \n" +
            "      AND org_id IN (\n" +
            "        SELECT org_id \n" +
            "          FROM t_org_snapshot \n" +
            "         WHERE `status` = 1 \n" +
            "           AND date_month = #{dateMonth} \n" +
            "           AND (org_id = #{orgId} OR org_level LIKE CONCAT( '%-', #{orgId}, '-%' ))) \n" +
            "    GROUP BY `month` \n" +
            "    ORDER BY total DESC) a" +
            "</foreach>" +
            "</script>")
    List<UserLoginRankForm> getHistoryMaxMonthLoginByOrgList(@Param("orgIds") List<Long> orgIds,
                                                             @Param("year") Integer year,
                                                             @Param("dateMonth") String dateMonth);

    /**
     * 查询一年中登录次数最多的一月
     *
     * @param orgId
     * @param year
     * @return
     */
    @Select("<script>" +
            "SELECT MAX( a.total ) num, a.`month`\n" +
            "FROM (\n" +
            "   SELECT count( 0 ) total, MONTH(login_date) `month` \n" +
            "     FROM t_user_login_log \n" +
            "    WHERE YEAR ( login_date ) = #{year} \n" +
            "      AND org_id IN (\n" +
            "        SELECT organization_id \n" +
            "          FROM t_organization \n" +
            "         WHERE `status` = 1 \n" +
            "           AND (organization_id = #{orgId} OR org_level LIKE CONCAT( '%-', #{orgId}, '-%' ))) \n" +
            "    GROUP BY `month` \n" +
            "    ORDER BY total DESC) a" +
            "</script>")
    UserLoginRankForm getMaxMonthLoginByOrg(@Param("orgId") Long orgId,
                                            @Param("year") Integer year);

    /**
     * 查询一年中登录次数最多的一月
     *
     * @param orgIds
     * @param year
     * @return
     */
    @Select(
            "<script>"
                    + "<foreach item=\"orgId\" collection=\"orgIds\" separator=\"union all\"> "
                    + "SELECT #{orgId} orgId, MAX( a.total ) num, a.`month`\n"
                    + "FROM (\n"
                    + "   SELECT count( 0 ) total, MONTH(login_date) `month` \n"
                    + "     FROM t_user_login_log \n"
                    + "    WHERE YEAR ( login_date ) = #{year} \n"
                    + "      AND org_id IN (\n"
                    + "        SELECT organization_id \n"
                    + "          FROM t_organization \n"
                    + "         WHERE `status` = 1 \n"
                    + "           AND (organization_id = #{orgId} OR org_level LIKE CONCAT( '%-', #{orgId}, '-%' ))) \n"
                    + "    GROUP BY `month` \n"
                    + "    ORDER BY total DESC) a\n"
                    + "</foreach>"
                    + "</script>")
    List<UserLoginRankForm> getMaxMonthLoginByOrgList(@Param("orgIds") List<Long> orgIds,
                                                      @Param("year") Integer year);

    /**
     * 获取用户在一年中登录的次数
     *
     * @param userId
     * @param year
     * @return
     */
    @Select("<script>" +
            "select count(0) from t_user_login_log WHERE login_id = #{userId} and YEAR ( login_date ) = #{year}" +
            "</script>")
    Integer getUserLoginCountByUser(@Param("userId") Long userId,
                                    @Param("year") Integer year);

    /**
     * 获取用户在一年中登录的次数 - 批量
     *
     * @param userIds
     * @param year
     * @return
     */
    @Select("<script>" +
            "select login_id id, count(0) total from t_user_login_log WHERE login_id in " +
            "<foreach item=\"userId\" collection=\"userIds\" open=\"(\" separator=\",\" close=\")\"> " +
            "#{userId} " +
            "</foreach>" +
            "and YEAR ( login_date ) = #{year} group by login_id" +
            "</script>")
    List<LoginValue> getUserLoginCountByUserList(@Param("userIds") List<Long> userIds,
                                                 @Param("year") Integer year);

    @Select("select distinct date_format(l1.login_date,'%Y%m%d')+0\n" +
            "from t_user_login_log l1\n" +
            "where l1.login_id=#{userId} and date_format(l1.login_date,'%Y-%m-%d')=#{monthDay}")
    Long checkIsLoginBy(@Param("userId") Long id, @Param("monthDay") String day);

    /**
     * 查询某月连续登录次数
     *
     * @param userId
     * @param yearMonth
     * @return
     */
    @Select("<script>SELECT max(days) days \n" +
            " FROM (SELECT login_id,@cont_day:= \n" +
            " (CASE WHEN (@last_uid = login_id AND DATEDIFF(login_dt, @last_dt)=1) THEN \n" +
            " (@cont_day + 1) WHEN (@last_uid = login_id AND DATEDIFF(login_dt, @last_dt) <![CDATA[ < ]]> 1) THEN \n" +
            " (@cont_day + 0) ELSE 1 END) AS days,(@cont_ix:= (@cont_ix + IF(@cont_day = 1, 1, 0))) AS countOrder,\n" +
            " @last_uid:= login_id,@last_dt:= login_dt as loginDay \n" +
            " FROM (SELECT login_id, DATE(login_date) AS login_dt FROM t_user_login_log ORDER BY login_id, login_date) AS t,\n" +
            " (SELECT @last_uid:='',@last_dt:='',@cont_ix:=0,@cont_day:=0) AS t1) AS t2 \n" +
            " WHERE login_id = #{userId} AND loginDay LIKE CONCAT(#{yearMonth},'%') \n" +
            " GROUP BY login_id, countOrder \n" +
            " ORDER BY days desc limit 1 " +
            "</script>")
    Integer findMaxByLoginId(@Param("userId") Long userId, @Param("yearMonth") String yearMonth);

    /**
     * 查询某月登录天数
     *
     * @param userId
     * @param yearMonth
     * @return
     */
    @Select("SELECT count(DISTINCT DATE_FORMAT(login_date,'%Y-%m-%d')) FROM t_user_login_log WHERE login_id = #{userId} AND login_date LIKE CONCAT(#{yearMonth},'%')")
    Integer findTotalByLoginId(@Param("userId") Long userId, @Param("yearMonth") String yearMonth);

    /**
     * @param userId
     * @param yearMonth
     * @return
     */
    @Select("SELECT CONCAT(DATE_FORMAT( login_date, '%Y年%m月%d日' ),',',DATE_FORMAT(login_date,'%H:%i'))\n" +
            "FROM t_user_login_log \n" +
            "WHERE login_id = #{userId} \n" +
            "AND DATE_FORMAT( login_date, '%Y-%m-%d' ) like CONCAT(#{yearMonth},'%') \n" +
            "AND( DATE_FORMAT(login_date,'%H:%i') BETWEEN '00:00' \n" +
            "AND #{endTime} OR DATE_FORMAT(login_date,'%H:%i') BETWEEN #{startTime} AND '23:59') limit 1")
    String findParamValue(@Param("userId") Long userId, @Param("yearMonth") String yearMonth,
                          @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询季度/年度登录天数
     *
     * @param userId 登录用户ID
     * @param year   年份
     * @param months 季度月份，如果为空则查询年度登录天数
     * @return int 登录天数
     */
    @Select("<script>SELECT count( DISTINCT DATE_FORMAT( login_date, '%Y-%m-%d' ) ) AS cnt \n" +
            "FROM t_user_login_log \n" +
            "WHERE login_id = #{userId} \n" +
            "AND DATE_FORMAT( login_date, '%Y' ) = #{year} \n" +
            "<if test=\"months != null and months.size > 0 \">" +
            "AND ABS(DATE_FORMAT( login_date, '%m' )) IN " +
            "<foreach item=\"month\" collection=\"months\" open=\"(\" separator=\",\" close=\")\">#{month}</foreach>" +
            "</if>" +
            "</script>")
    Integer findQuarterAndYearTotalByLoginId(@Param("userId") Long userId, @Param("year") Integer year,
                                             @Param("months") List<Integer> months);

    /**
     * @return List<VisitLoginForm>
     */
    @Select("<script>SELECT login_id AS loginId,MIN( login_date ) AS visitTime \n" +
            "FROM t_user_login_log \n" +
            "WHERE login_date like CONCAT(#{loginDate},'%') \n" +
            "<if test=\"userIdList != null and userIdList.size > 0\">AND login_id IN \n" +
            "<foreach item=\"userId\" collection=\"userIdList\" open=\"(\" separator=\",\" close=\")\">#{userId}</foreach>" +
            "</if> \n" +
            "<if test=\"testUserList != null and testUserList.size > 0\">AND login_id NOT IN \n" +
            "<foreach item=\"userId\" collection=\"testUserList\" open=\"(\" separator=\",\" close=\")\">#{userId}</foreach>" +
            "</if> \n" +
            "GROUP BY login_id \n" +
            "ORDER BY visitTime ASC" +
            "</script>")
    List<VisitLoginForm> findLoginCountByDate(@Param("loginDate") String loginDate,
                                              @Param("type") Integer type, @Param("userIdList") List<Long> userIdList,
                                              @Param("testUserList") List<Long> testUserList);

    /**
     * 查询用户累计登录天数，若用户为空，则查询全市所有用户登录天数
     *
     * @param testUserList testUserList
     * @return list
     */
    @Select("<script>" +
            "SELECT login_id AS loginId,count( DISTINCT DATE_FORMAT( login_date, '%Y-%m-%d' ) ) AS visitDays,\n" +
            "MAX( login_date ) AS visitTime \n" +
            "FROM t_user_login_log force index(login_log_idx_login_day, login_date_index) where 1=1  \n" +
            "<if test=\"testUserList != null and testUserList.size > 0\">AND login_id NOT IN \n" +
            "<foreach item=\"userId\" collection=\"testUserList\" open=\"(\" separator=\",\" close=\")\">#{userId}</foreach> \n" +
            "</if> \n" +
            "<if test=\"orgIdList != null and orgIdList.size > 0\">" +
            "<foreach collection=\"orgIdList\" item=\"id\" open=\" \" separator=\" \" close=\" \">" +
            " AND org_id <![CDATA[<>]]> #{id} \n" +
            "</foreach>" +
            "</if>" +
            "<if test=\"statsDate != null and statsDate != ''\">" +
            "AND DATE_FORMAT( login_date, '%Y-%m-%d' ) <![CDATA[ <= ]]> #{statsDate} \n" +
            "</if>" +
            "GROUP BY login_id \n" +
            "ORDER BY visitDays DESC,visitTime ASC" +
            "</script>")
    List<VisitLoginForm> findLoginCountByUser(@Param("testUserList") List<Long> testUserList, @Param("statsDate") String statsDate,
                                              @Param("orgIdList") List<Long> orgIdList);

    /**
     * 查询
     *
     * @param userList userList
     * @return list
     */
    @Select("<script>" +
            "SELECT DISTINCT login_id AS loginId,count( DISTINCT DATE_FORMAT( login_date, '%Y-%m-%d' ) ) AS visitDays " +
            "FROM t_user_login_log " +
            "WHERE 1 = 1 " +
            "<if test=\"userList != null and userList.size > 0\">AND login_id IN " +
            "<foreach item=\"userId\" collection=\"userList\" open=\"(\" separator=\",\" close=\")\">#{userId}</foreach> " +
            "</if> \n" +
            "AND DATE_FORMAT(login_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{statsDate} \n" +
            "GROUP BY login_id " +
            "</script>")
    List<VisitLoginForm> findLoginCountByOrgAndUser(@Param("userList") List<Long> userList, @Param("statsDate") String statsDate);

    /**
     * 查询累计登录次数
     *
     * @param first  开始时间
     * @param second 结束时间
     * @return int
     */
    @Select("<script>" +
            "SELECT count(login_id) AS cnt\n" +
            "FROM t_user_login_log \n" +
            "WHERE login_date <![CDATA[ >= ]]> #{first} \n" +
            "AND login_date <![CDATA[ < ]]> #{second} \n" +
            "AND region_id = #{regionId} \n" +
            "<if test=\"userList != null and userList.size > 0\"> \n" +
            " AND login_id NOT IN " +
            "<foreach collection=\"userList\" item=\"id\" open=\"(\" separator=\",\" close=\")\">#{id}</foreach>" +
            "</if>" +
            "</script>")
    Integer findTotalByTimes(@Param("first") String first, @Param("second") String second,
                             @Param("regionId") Long regionId, @Param("userList") List<Long> userList);

    /**
     * 查询当天组织用户登录次数，若用户为空，则查询全市当天所有用户登录天数
     *
     * @param userList userList
     * @return int
     */
    @Select("<script>" +
            "SELECT count(DISTINCT login_id)  FROM t_user_login_log \n" +
            "WHERE login_day = #{queryDay}" +
            "<if test=\"userList != null and userList.size > 0\"> AND login_id IN \n" +
            "<foreach item=\"userId\" collection=\"userList\" open=\"(\" separator=\",\" close=\")\">#{userId}</foreach> \n" +
            "</if>" +
            "</script>")
    int findNowLoginCountByUser(@Param("userList") List<Long> userList, String queryDay);

    /**
     * 获取用户最后登录日期那天的最早登录时间
     *
     * @param regionId
     * @param userId
     * @return
     */
    @Select("<script>" +
            " select visitTime from (SELECT " +
            " DATE_FORMAT( login_date, '%Y-%m-%d' ) as visitDate," +
            " DATE_FORMAT(login_date,'%H:%i:%s') as visitSec," +
            " DATE_FORMAT(login_date,'%Y-%m-%d %H:%i:%s') as visitTime" +
            " FROM t_user_login_log WHERE DATE_FORMAT(login_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{statsDate} and login_id = #{userId} and region_id = #{regionId}" +
            " GROUP BY visitDate ORDER BY visitDate desc,visitSec asc LIMIT 1) tmp" +
            "</script>")
    String findUserLastLoginDate(@Param("regionId") Long regionId, @Param("userId") Long userId, @Param("statsDate") String statsDate);

    /**
     * 获取用户当月的登录次数
     *
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    @Select("<script>" +
            "select count(DISTINCT DATE_FORMAT(login_date,'%Y-%m-%d')) total from t_user_login_log WHERE login_id = #{userId} \n" +
            "<if test=\"startTime != null and startTime != ''\"> and DATE_FORMAT(login_date,'%Y-%m-%d') <![CDATA[ >= ]]> #{startTime}</if>\n" +
            "<if test=\"endTime != null and endTime != ''\"> and DATE_FORMAT(login_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}</if>\n" +
            "and region_id = #{regionId}" +
            "</script>")
    int getNumByUserAndMonth(@Param("userId") Long userId,
                             @Param("startTime") String startTime,
                             @Param("endTime") String endTime,
                             @Param("regionId") Long regionId);
}