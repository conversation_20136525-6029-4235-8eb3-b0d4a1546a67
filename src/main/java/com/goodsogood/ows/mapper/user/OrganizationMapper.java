package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.user.OptionEntity;
import com.goodsogood.ows.model.db.user.OrgGroupVo;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.experience.OrgUserReportVO;
import com.goodsogood.ows.model.vo.fusion.TopOrgDetail;
import com.goodsogood.ows.model.vo.sas.OrgScoreVo;
import com.goodsogood.ows.model.vo.sas.OrganizationForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 组织Mapper
 *
 * <AUTHOR>
 * @date 2019/11/19
 */
@Repository
@Mapper
public interface OrganizationMapper extends MyMapper<OrganizationEntity> {

    /**
     * 查询组织直接下级
     */
    @Select("<script>select distinct organization_id orgId,`name` ,region_id as regionId from t_organization " +
            "WHERE `status` = 1 and parent_id = #{orgId} order by seq desc" +
            " </script>")
    List<OrganizationBase> findChildByOrgId(@Param("orgId") Long orgId);

    /**
     * 查询党树组织里组织的直接下级，排除党小组
     */
    @Select("<script>select distinct organization_id orgId,`name` ,region_id as regionId from t_organization " +
            "WHERE `status` = 1 and region_id = #{regionId} and parent_id = #{orgId} <if test=\"orgType != null\"> AND org_type = #{orgType}</if>" +
            " <if test=\"excludeList != null and excludeList.size!=0 \"> and org_type_child not in" +
            " <foreach item=\"typeChild\" collection=\"excludeList\" open=\"(\" separator=\",\" close=\")\">#{typeChild}</foreach> </if>" +
            " <if test=\"testBranch != null and testBranch!='' \">  and organization_id not in (${testBranch})</if></script>")
    List<OrganizationBase> findChildByOrgIdExclude(@Param("regionId") Long regionId, @Param("orgId") Long orgId,
                                                   @Param("orgType") Integer orgType, @Param("excludeList") List<Integer> excludeList,
                                                   @Param("testBranch") String testBranch);

    @Select("<script>" +
            " SELECT organization_id organizationId, name, org_type orgType, org_type_child orgTypeChild, parent_id parentId, org_level orgLevel, region_id regionId, IFNULL(org_create_time,create_time) orgCreateTime, " +
            " (select count(0) from t_user_org_group uog where uog.org_id = organization_id) groupSum," +
            " (SELECT count(0) FROM t_user_org_period uop WHERE uop.org_id = organization_id and uop.is_delete = 2 AND SYSDATE() <![CDATA[ >= ]]> uop.start_time and SYSDATE() <![CDATA[ <= ]]> uop.end_time) periodSum, " +
            " CASE WHEN is_retire != 1 THEN 2 WHEN is_retire IS NULL THEN 2 WHEN is_retire = 0 THEN 2 ELSE is_retire end isRetire ,region_id regionId " +
            " FROM t_organization " +
            " WHERE `status` = 1 " +
            " <if test=\"orgType != null\"> AND org_type = #{orgType}</if>" +
            " <if test=\"orgTypeChild != null\"> AND org_type_child = #{orgTypeChild}</if>" +
            " <if test=\"regionId != null\"> AND region_id = #{regionId}</if>" +
            "</script>")
    List<OrganizationForm> findOrgByType(@Param("orgType") Long orgType,
                                         @Param("orgTypeChild") Long orgTypeChild,
                                         @Param("regionId") Long regionId);

    @Select("<script>" +
            "SELECT DISTINCT o.organization_id orgId,o.parent_id parentId,o.region_id regionId, o.`name` `orgName`, o.short_name shortName, o.org_type orgType,o.org_type_child orgTypeChild " +
            "FROM t_tag t " +
            "INNER JOIN t_org_tag ot ON t.tag_id = ot.tag_id\n" +
            "INNER JOIN t_organization o ON ot.org_id = o.organization_id " +
            "WHERE o.org_level like CONCAT('%-', #{orgId},'-%')" +
            "AND o.`status` = #{status} " +
            "AND t.`status` = #{status} " +
            "AND t.tag_type in " +
            "<foreach collection=\"tagType\" item=\"type\" open=\"(\" separator=\",\" close=\")\">" +
            "#{type}" +
            "</foreach>" +
            "</script>")
    List<OrganizationBase> findEvalOrgById(@Param("orgId") Long orgId,
                                           @Param("status") Integer status,
                                           @Param("tagType") List<Integer> tagType);


    /**
     * 得到组织信息 进行打分计算
     *
     * @param orgId   orgId
     * @param staDate staDate
     */
    @Select("SELECT\n" +
            " o.org_pid as parentId,\n" +
            " o.`org_name` as name,\n" +
            " o.org_type_child as orgTypeChild,\n" +
            " o.org_short_name as shortName,\n" +
            " o.is_retire as isRetire,\n" +
            " o.contacts_phone as orgPhone,\n" +
            " o.contacts as orgContacts,\n" +
            " o.owner_id as ownerId\n" +
            "  FROM\n" +
            " t_org_snapshot o\n" +
            "  WHERE  o.date_month =#{staDate}\n" +
            "  AND    o.org_id = #{orgId}" +
            "  AND    region_id=3 and `status`=1 "
    )
    Map<String, Object> getOrgScoreInfo(@Param("orgId") Long orgId,
                                        @Param("staDate") String staDate);


    /**
     * 得到组织信息 进行打分计算
     */
    @Select("<script>" +
            "<foreach collection=\"orgIds\" index=\"index\" item=\"item\" separator=\" UNION ALL \" >" +
            " SELECT\n" +
            " o.org_pid as parentId,\n" +
            " o.`org_name` as name,\n" +
            " o.org_type_child as orgTypeChild,\n" +
            " o.org_short_name as shortName,\n" +
            " o.is_retire as isRetire,\n" +
            " o.contacts_phone as orgPhone,\n" +
            " o.contacts as orgContacts,\n" +
            " o.owner_id as ownerId ,\n" +
            " o.month as  dateMonth ," +
            " o.org_id as orgId" +
            "  FROM\n" +
            " t_org_snapshot o\n" +
            "  WHERE  1=1\n" +
            " AND o.year = #{staDate}  \n" +
            "  AND o.org_id = #{item}" +
            "  AND o.region_id=3 and o.`status`=1  " +
            "</foreach>" +
            "</script>"
    )
    List<OrgScoreVo> getOrgScoreInfoList(@Param("orgIds") List<Long> orgId,
                                         @Param("staDate") int year);


    @Select("select organization_id from t_organization where status=1 and region_id=3")
    List<Long> getAllOrgan();

    /**
     * 纪实获取党支部数量（非离退休组织&管理组织）
     *
     * @param orgId     顶级组织id
     * @param dateMonth 快照年月
     */
    @Select("SELECT \n" +
            "    o.org_id\n" +
            "FROM t_org_snapshot o\n" +
            "    left join t_org_tag ot on o.org_id = ot.org_id\n" +
            "    left join t_tag t ON ot.tag_id = t.tag_id\n" +
            "WHERE ((o.org_level like '%-${orgId}-%' and o.org_pid <> -999 and o.date_month = '${dateMonth}'\n" +
            "    and o.status = 1 and o.org_type_child in (${orgTypeChild}\n" +
            "        ) and o.org_pid is not null and (o.is_retire IS NULL OR o.is_retire != 1)))\n" +
            "and t.tag_type = 7 and o.org_pid != -999 ")
    List<Long> meetingGetHistoryPartyBranch(@Param("orgId") Long orgId, @Param("dateMonth") String dateMonth, @Param("orgTypeChild") String orgTypeChild);


    /**
     * 纪实获取党支部数量（非离退休组织&管理组织）从当前最新数据获取
     *
     * @param orgId        orgId
     * @param orgTypeChild orgTypeChild
     */
    @Select("SELECT ot.org_id\n" +
            "FROM t_org_tag ot\n" +
            "         LEFT JOIN t_organization o on ot.org_id = o.organization_id\n" +
            "         LEFT JOIN t_tag t ON ot.tag_id = t.tag_id\n" +
            "WHERE t.tag_type = 7\n" +
            "  AND t.`status` = 1\n" +
            "  AND o.`status` = 1\n" +
            "  and o.org_type_child in (${orgTypeChild})\n" +
            "  and (o.is_retire IS NULL OR o.is_retire != 1)\n" +
            "  and (o.org_level like '%-${orgId}-%') and o.parent_id != -999 ")
    List<Long> meetingGetPartyBranch(@Param("orgId") Long orgId, @Param("orgTypeChild") String orgTypeChild);

    @Select("select f1.field_name fieldName,f1.alias \n" +
            "from t_organization_tree ot\n" +
            "inner join t_field f1 on ot.tree_id=f1.org_tree_id\n" +
            "where ot.region_id=#{regionId} \n" +
            "and ot.organization_id=#{orgId}\n" +
            "and f1.alias in (${fields})\n" +
            "group by f1.alias")
    List<Map> getCheckField(@Param("regionId") Long regionId, @Param("orgId") Long orgId, @Param("fields") String fields);

    @Select("SELECT `name` FROM\n" +
            "  t_organization \n" +
            "WHERE\n" +
            "  `status` = 1 \n" +
            "  AND org_type = 102803 \n" +
            "  and organization_id not in(${excludeOrgIds})" +
            "  AND ( organization_id = #{orgId} OR org_level LIKE '%-${orgId}-%' ) \n" +
            "  AND org_type_child = 10280306;")
    List<String> getPartyGroupName(@Param("orgId") Long orgId, @Param("excludeOrgIds") String excludeOrgIds);

    @Select("select distinct to1.organization_id\n" +
            "from t_organization to1 \n" +
            "where to1.status=1 and to1.region_id=#{regionId} \n" +
            "and (to1.organization_id=#{orgId} or to1.org_level like '%-${orgId}-%')\n" +
            "and to1.org_type_child in (${join})")
    List<Long> getOrgIdsByRegionAndTypeAndOrgId(@Param("regionId") Long regionId,
                                                @Param("orgId") Long orgId,
                                                @Param("join") String collect);


    @Select("SELECT org_id as orgId FROM t_user_org_group WHERE link_org_id=#{orgId} and is_delete=1")
    OrgGroupVo getOrgBranch(@Param("orgId") Long orgId);


    @Select("SELECT organization_id as organizationId ,name" +
            " FROM t_organization tor2 WHERE org_type=102807 and parent_id=2 and  status=1 " +
            " and organization_id not IN(#{excludeOrgIds})")
    List<OrganizationForm> getBranchOfficeInfo(@Param("excludeOrgIds") String excludeOrgIds);


    @Select(" SELECT organization_id as organizationId " +
            " FROM t_organization WHERE org_type_child in (10280304,10280309,10280314,10280315,10280319) " +
            " and  status=1  and owner_id = #{orgId}")
    List<Long> getSubOrgInfo(@Param("orgId") Long orgId);


    /**
     * 得到他所有下级的组织id
     */
    @Select(" SELECT organization_id as organizationId " +
            " FROM t_organization WHERE status=1  and owner_id = #{orgId}")
    List<Long> getSubAllOrgId(@Param("orgId") Long orgId);

    @Select("<script>" +
            "SELECT\n" +
            " DISTINCT o2.org_level\n" +
            "FROM\n" +
            " t_organization o1\n" +
            " LEFT JOIN t_organization o2 on o2.org_level like CONCAT('%-', o1.organization_id,'-%')\n" +
            "WHERE\n" +
            " o1.`status` = 1\n" +
            " AND o2.`status` = 1\n" +
            " AND o1.owner_id = #{unitId}" +
            "</script>")
    List<String> getOrgIdsByUnit(@Param("unitId") Long unit);

    /**
     * 获取顶级单位下以及党组织
     */
    @Select("<script> select organization_id from (" +
            " SELECT a.organization_id,a.region_id FROM (SELECT * FROM t_organization WHERE `status` = 1 AND owner_id IS NOT NULL " +
            " <if test=\"virtualOrg != null and virtualOrg!='' \"> AND owner_id not in (${virtualOrg})</if>" +
            " ORDER BY LENGTH( org_level ) ASC LIMIT 1000000 ) a GROUP BY a.owner_id " +
            " UNION " +
            " SELECT organization_id, region_id from t_organization " +
            " WHERE `status` = 1 AND owner_id IS NOT NULL AND parent_id = 3 " +
            " <if test=\"virtualOrg != null and virtualOrg!='' \"> AND owner_id not in (${virtualOrg})</if>" +
            ") tmp where region_id = #{regionId} <if test=\"testBranch != null and testBranch!='' \">  and organization_id not in (${testBranch})</if> </script>")
    List<Long> findUnitTopOrgIds(@Param("regionId") Long regionId, @Param("testBranch") String testBranch, @Param("virtualOrg") String virtualOrg);

    /**
     * 查询组织同父组织的同级组织编号
     */
    @Select("<script>select organization_id from t_organization " +
            " WHERE `status` = 1  and region_id = #{regionId} and parent_id = (select parent_id from t_organization \n" +
            "WHERE `status` = 1 and region_id = #{regionId} and organization_id = #{orgId}) \n" +
            "<if test=\"orgType != null\"> AND org_type = #{orgType}</if>" +
            "<if test=\"isGroupType != null and isGroupType == 1\"> AND org_type_child <![CDATA[<>]]> 10280306 </if>" +
            "<if test=\"testBranch != null and testBranch!='' \">  and organization_id not in (${testBranch})</if></script>")
    List<Long> findBrotherOrgId(@Param("regionId") Long regionId, @Param("orgId") Long orgId, @Param("orgType") Integer orgType,
                                @Param("testBranch") String testBranch, @Param("isGroupType") Integer isGroupType);

    /**
     * 查询组织类型信息
     */
    @Select("<script>select parent_id parentId,org_type_child orgTypeChild from t_organization " +
            " WHERE `status` = 1  and region_id = #{regionId} and organization_id = #{orgId} <if test=\"orgType != null\"> AND org_type = #{orgType}</if>" +
            " <if test=\"testBranch != null and testBranch!='' \">  and organization_id not in (${testBranch})</if></script>")
    OrganizationBase findOrgTypeChildByOrgId(@Param("regionId") Long regionId, @Param("orgId") Long orgId, @Param("orgType") Integer orgType, @Param("testBranch") String testBranch);

    /**
     * 得到用户所在组织信息
     */
    @Select("<script>" +
            "select o.organization_id organizationId,o.`name`" +
            " from t_user_org_corp uoc " +
            " INNER JOIN t_organization o ON o.organization_id = uoc.organization_id and uoc.region_id = o.region_id" +
            " INNER JOIN t_user u on uoc.user_id = u.user_id and uoc.is_employee = 1 " +
            " where u.`status`=1 AND o.`status` = 1 " +
            " and o.org_type = #{orgType}" +
            " and u.user_id = #{userId}  " +
            " and uoc.region_id = #{regionId}" +
            "</script>")
    OrganizationBase findUserOrgInfo(@Param("regionId") Long regionId, @Param("orgType") Integer orgType, @Param("userId") Long userId);

    /**
     * 得到用户所在单位编号
     */
    @Select("<script>" +
            "SELECT o.owner_id FROM t_user u" +
            " INNER JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id" +
            " AND uoc.is_employee = 1" +
            " INNER JOIN t_organization o ON uoc.organization_id = o.organization_id and uoc.region_id = o.region_id" +
            " WHERE u.`status` = 1" +
            " AND o.`status` = 1" +
            " and o.org_type = #{orgType}" +
            " and u.user_id = #{userId}  " +
            " and uoc.region_id = #{regionId}" +
            "</script>")
    Long findUserUnitId(@Param("regionId") Long regionId, @Param("orgType") Integer orgType, @Param("userId") Long userId);


    /**
     * 查询单位下所有人员编号
     */
    @Select("<script>" +
            " SELECT u.user_id FROM t_user u" +
            " LEFT JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id" +
            " AND uoc.is_employee = 1" +
            " LEFT JOIN t_organization o1 ON uoc.organization_id = o1.organization_id" +
            " LEFT JOIN t_organization o2 ON (o1.org_level LIKE CONCAT('%-',o2.organization_id,'-%') OR o1.organization_id = o2.organization_id)" +
            " WHERE u.`status` = 1 AND o1.`status` = 1 AND o2.`status` = 1" +
            " AND o2.owner_id = #{ownerId} and o1.region_id=#{regionId} and o2.region_id=#{regionId} and uoc.region_id=#{regionId}" +
            "</script>")
    List<Long> findUnitUser(@Param("regionId") Long regionId, @Param("ownerId") Long ownerId);


    /**
     * 根据顶级组织id查询出所有单位，以及单位下所有的机构id
     */

    @Select("select t1.organization_id as unitId,t1.name as unitName,t2.organization_id as orgId,t2.name as orgName from t_organization  t1 \n" +
            "join t_organization t2 on t1.organization_id=t2.owner_id\n" +
            "where t1.status=1 and t1.region_id=#{regionId} and t1.org_type=102807 and t1.parent_id=2 and t1.organization_id\n" +
            "not in(5,1222,1255,1294,2409,2417)\n" +
            "and  t2.status=1 and t2.region_id=19")
    List<OrgUserReportVO> findUnitOrg(@Param("regionId") Long regionId);


    /**
     * 查询机构下的党支部数量
     */
    @Select("select count(0) from t_organization WHERE ((org_level LIKE CONCAT('%-',#{orgId},'-%') " +
            " or organization_id=#{orgId})) and  status=1 and org_type_child in" +
            "(10280303,10280304,10280308,10280309,10280311,10280314,10280315,10280318,10280319)"
    )
    Integer countBranchNum(Long orgId);

    @Select("<script>" +
            " SELECT organization_id as unitId, `name` as unitName" +
            "   FROM t_organization " +
            "  where `status` = 1 " +
            "    AND org_type = 102807 " +
            "    AND parent_id = 2 " +
            "    AND region_id = #{regionId} " +
            "<if test=\"unitId != null\">    AND organization_id = #{unitId} </if>" +
            "    AND organization_id not in " +
            "<foreach collection=\"excludeOrgIds\" item=\"orgId\" open=\"(\" separator=\",\" close=\")\"> #{orgId} </foreach>" +
            "</script>")
    List<OrgUserReportVO> findUnits(@Param("regionId") Long regionId,
                                    @Param("unitId") Long unitId,
                                    @Param("excludeOrgIds") List<Long> excludeOrgIds);


    /**
     * 融合概况V4.0
     */
    @Select("SELECT organization_id as organizationId,REPLACE(`name`,'（分公司）','') as `name` FROM t_organization WHERE parent_id=2")
    List<OrganizationBase> getCompanyInfo();

    @Select("<script>" +
            "SELECT organization_id \n" +
            "FROM t_organization \n" +
            "WHERE owner_id = #{unitId} \n" +
            "AND org_type_child <![CDATA[<>]]> 10280306 \n" +
            "AND `status` = 1 \n" +
            "<foreach collection=\"orgList\" item=\"id\" open=\" \" separator=\" \" close=\" \">" +
            " AND org_level NOT LIKE CONCAT( '%-', #{id}, '-%' ) \n" +
            " AND organization_id <![CDATA[<>]]> #{id} \n" +
            "</foreach>" +
            "</script>")
    List<Long> findAllOrgByUnit(@Param("unitId") Long unitId, @Param("orgList") List<Long> orgList);


    @Select("<script>" +
            "SELECT organization_id \n" +
            "FROM t_organization  \n" +
            "where parent_id in \n" +
            "<foreach collection=\"orgList\" item=\"id\" open=\" (\" separator=\",\" close=\" )\">" +
            " #{id} \n" +
            "</foreach>" +
            " and status=1 and org_type=102803 and org_type_child=10280306 " +
            "</script>")
    List<Long> findGroup(@Param("orgList") List<Long> orgList);

    @Select("<script> select organization_id organizationId,owner_id ownerId from (" +
            " SELECT a.organization_id,a.region_id,a.owner_id FROM ( " +
            " SELECT * FROM t_organization WHERE  `status` = 1 AND owner_id IS NOT NULL " +
            " ORDER BY LENGTH( org_level ) ASC LIMIT 1000000 ) a " +
            " GROUP BY a.owner_id" +
            " UNION " +
            " SELECT organization_id, region_id,owner_id  from t_organization " +
            " WHERE `status` = 1  AND owner_id IS NOT NULL  AND parent_id = #{topOrgId} " +
            " ) tmp where region_id = #{regionId} and owner_id in " +
            " <foreach collection=\"unitIds\" item=\"unitId\" open=\"(\" separator=\",\" close=\")\"> #{unitId} </foreach>" +
            "</script>")
    List<OrganizationEntity> findTopOrgIdByUnitIdsUseSql(@Param("regionId") Long regionId, @Param("topOrgId") Long topOrgId, @Param("unitIds") List<Long> unitIds);

    @Select("<script>" +
            "select organization_id AS organizationId,parent_id AS parentId,`name`,short_name AS shortName from t_organization \n" +
            "where status = 1 and region_id = #{regionId} \n" +
            "  AND ( organization_id = #{orgId} OR org_level LIKE CONCAT('%-', #{orgId},'-%') ) \n" +
            "<if test=\"orgList != null and orgList.size > 0\">" +
            "  and organization_id not in \n" +
            " <foreach collection=\"orgList\" item=\"orgId\" open=\"(\" separator=\",\" close=\")\"> #{orgId} </foreach>" +
            "</if>" +
            "</script>")
    List<OrganizationEntity> findAllChildOrg(@Param("regionId") Long regionId, @Param("orgId") Long orgId,
                                             @Param("orgList") List<Long> orgList);

    @Select(" \n" +
            "SELECT op.op_key as opKey,op.op_value as opValue \n" +
            "FROM\n" +
            " t_organization AS o\n" +
            " LEFT JOIN t_option AS op ON o.unit_category = op.op_key \n" +
            "WHERE\n" +
            " op.`code` = 1051 \n" +
            " AND o.`status` = 1 \n" +
            " AND o.organization_id = #{orgId}")
    OptionEntity getCategoryById(@Param("orgId") Long orgId);

    @Select("<script> " +
            "SELECT\n" +
            " a.owner_id unitId,\n" +
            " a.organization_id as organizationId,\n" +
            " a.longitude,\n" +
            " a.latitude,\n" +
            "  a.adcode,\n" +
            " count( DISTINCT o.organization_id ) branch,\n" +
            " count( DISTINCT u.user_id ) member \n" +
            "FROM (\n" +
            " SELECT a.* \n" +
            " FROM ( SELECT * FROM t_organization WHERE `status` = 1 AND owner_id IS NOT NULL ORDER BY LENGTH( org_level ) ASC LIMIT 1000000 ) a \n" +
            " WHERE a.owner_id NOT IN \n" +
            "<foreach collection=\"excludeOrgIds\" item=\"orgId\" open=\"(\" separator=\",\" close=\")\"> #{orgId} </foreach>" +
            " GROUP BY\n" +
            "  a.owner_id \n" +
            " ORDER BY\n" +
            "  a.owner_id) a\n" +
            " LEFT JOIN t_organization o ON o.org_type_child IN " +
            "<foreach collection=\"branchOrgType\" item=\"type\" open=\"(\" separator=\",\" close=\")\"> #{type} </foreach>" +
            " AND ( o.org_level LIKE CONCAT( '%-', a.organization_id, '-%' ) OR o.organization_id = a.organization_id )\n" +
            " LEFT JOIN t_user_org_corp uoc ON uoc.organization_id = o.organization_id \n" +
            " AND uoc.is_employee = 1\n" +
            " LEFT JOIN t_user u ON u.user_id = uoc.user_id AND u.political_type in (1,5,17,18)\n" +
            " AND u.`status` = 1 \n" +
            "WHERE o.`status` = 1 \n" +
            "<foreach collection=\"excludeOrgIds\" index=\"index\" item=\"item\">\n" +
            "   AND o.organization_id <![CDATA[ <> ]]> #{item} AND o.org_level not like concat('%-', #{item}, '-%') " +
            "</foreach>" +
            "GROUP BY\n" +
            " a.owner_id" +
            "</script>")
    List<TopOrgDetail> findTopOrgId(@Param("branchOrgType") List<Integer> branchOrgType,
                                    @Param("excludeOrgIds") List<Long> excludeOrgIds);


    @Select("""
                    SELECT organization_id From t_organization where org_type_child = 10280306
            """)
    List<Long> selectAllGroup();

    @Select("""
            SELECT organization_id as organizationId,`name`,
            short_name as shortName ,region_id as regionId,region_id as parentId,
            org_type as orgType,org_type_child as orgTypeChild,org_level as orgLevel,
            org_create_time as orgCreateTime,owner_id as ownerId
            FROM t_organization o
            WHERE o.owner_id = #{orgId} AND `status` = 1
            ORDER BY o.parent_id ASC
            LIMIT 1
            """)
    OrganizationBase getLevelOneOrg(@Param("orgId") Long orgId);


    @Select("""
             SELECT COUNT(1) FROM
             (
                 select count(distinct c1.user_id) numbers from t_user_org_corp c1\s
                 where c1.is_employee=1 and c1.organization_id=#{orgId}
                 and c1.region_id=#{regionId} group by c1.organization_id
             ) as t WHERE t.numbers>50
            """)
    Integer getOrgUserExceedFifty(@Param("orgId") Long orgId,
                                  @Param("regionId") Long regionId);

    @Select("""
                <script>
            	SELECT organization_id AS organizationId,owner_id AS ownerId FROM t_organization WHERE status = 1
            	 <if test="orgIds != null and orgIds.size > 0">
            	 AND organization_id IN
            	 <foreach item="orgId" collection="orgIds" open="(" separator="," close=")">
            	 #{orgId}
            	 </foreach>
            	 </if>
            	 </script>
            """)
    List<OrganizationEntity> getOwnerId(@Param("orgIds") List<Long> orgIds);


    @Select("""
            SELECT
            	o1.organization_id AS organizationId,
            	o1.NAME,
            	o1.org_type_child AS orgTypeChild,
            	o1.org_level AS orgLevel
            FROM
            	t_organization o1
            	LEFT JOIN t_organization o2 ON o1.owner_id = o2.organization_id
            WHERE
            	o1.`status` = 1
            	AND o2.`status` = 1
            	AND o2.organization_id = #{unitId}
            	AND o1.region_id = #{regionId}
            	AND o2.region_id = #{regionId}
            ORDER BY
            	o1.org_level
            	LIMIT 1
            """)
    OrganizationBase findLevelOneOrgByOwner(@Param("unitId") Long unitId, @Param("regionId") Long regionId);

    /**
     * 通过regionId和adCode获取对应的单位id
     * ！！！！ 该方法为烟草定制方法，请其他系统不要使用 ！！！！
     *
     * @param regionId region id 这里应该一直都是19
     * @param adCode   区域编码（datav 的地图区域国标）
     * @return list of OrganizationBase
     */
    @Select("""
            SELECT
            	o1.organization_id AS organizationId,
            	o1.NAME,
            	o1.org_type_child AS orgTypeChild,
            	o1.org_level AS orgLevel ,
            	o1.adcode
            FROM
            	`t_organization` as o1
            WHERE
            	o1.`org_type_child` = '10280730'
            	AND o1.`seq` > 0
            	AND o1.`parent_id` = 2
            	AND o1.`status` = 1
            	AND o1.`region_id` = #{regionId}
            	AND o1.adcode = #{adCode}
            """)
    List<OrganizationBase> findOrgByUnitADCode(@Param("adCode") String adCode, @Param("regionId") Long regionId);
}
