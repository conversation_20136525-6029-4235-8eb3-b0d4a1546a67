package com.goodsogood.ows.mapper.user;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.sas.StatisticalUserTempEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.db.user.UserOrgAndCorpEntity;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.sas.UserOrgInfo;
import com.goodsogood.ows.model.vo.user.OrgUserCountForm;
import com.goodsogood.ows.model.vo.user.UserPartyQueryForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface UserOrgAndCorpMapper extends MyMapper<UserOrgAndCorpEntity> {

    @Select("<script>" +
            "SELECT o.organization_id orgId,o.`name` `orgName`," +
            "(CASE WHEN ISNULL(o.organization_id) THEN null ELSE count(DISTINCT uoc.user_id) END ) userCount\n" +
            "FROM t_user_org_corp uoc\n" +
            "INNER JOIN t_user u ON uoc.user_id = u.user_id\n" +
            "AND u.`status` = #{status}\n" +
            "INNER JOIN t_organization o ON uoc.organization_id = o.organization_id\n" +
            "AND o.`status` = #{status}\n" +
            "WHERE u.political_type IN " +
            "<foreach collection=\"list\" item=\"item\" open=\"(\" close=\")\" separator=\",\">#{item}</foreach>\n" +
            "AND uoc.is_employee = #{isEmployee}\n" +
            "AND uoc.organization_id = #{orgId}" +
            "</script>")
    OrgUserCountForm findOrgUserCountByOrgId(@Param("status") Integer status, @Param("isEmployee") Integer isEmployee,
                                             @Param("list") String[] politicalArray, @Param("orgId") Long orgId);

    @Select("<script>" +
            "SELECT u.user_id userId,u.`name` userName,\n" +
            "u.gender,u.education,u.age,u.phone,u.phone_secret phoneSecret,u.cert_number certNumber,\n" +
            "u.cert_number_secret certNumberSecret,o.organization_id orgId,o.`name` orgName,\n" +
            "o.org_type orgType,o.org_type_child orgTypeChild\n" +
            "FROM t_user u\n" +
            "INNER JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            "INNER JOIN t_organization o ON uoc.organization_id = o.organization_id\n" +
            "WHERE u.`status` = #{status}\n" +
            "AND o.`status` = #{status}\n" +
            "AND uoc.organization_id IN \n" +
            "<foreach collection=\"list\" item=\"item\" open=\"(\" close=\")\" separator=\",\">#{item}</foreach>\n" +
            "<if test=\"isEmployee != null\">AND uoc.is_employee = #{isEmployee}\n</if>" +
            "<if test=\"isFilter == 1 \">AND FIND_IN_SET(u.political_type,#{politicalType})\n</if>" +
            "AND u.phone IS NOT NULL\n" +
            "AND u.phone <![CDATA[<>]]> ''\n" +
            "<if test=\"isInclude == 2\">AND u.id IS NOT NULL AND u.id <![CDATA[<>]]> ''\n</if>" +
            "</script>")
    List<UserInfoBase> findUserByOrgId(@Param("status") Integer status, @Param("isEmployee") Integer isEmployee,
                                       @Param("list") List<Long> orgList, @Param("isFilter") Integer isFilter,
                                       @Param("politicalType") String politicalType,
                                       @Param("isInclude") Integer isInclude);

    /**
     * includeLevel 是否包含下级 1:包含 2：不包含 3：仅包含 默认1
     * includeRetire 是否包含离退休组织 1:包含 2：不包含 3：仅包含 默认1
     * includeAll 是否包含所有人 1-是 2-否，默认2
     * filterStatus 是否包含删除状态信息 1-是 2-否，默认1
     *
     * @return List<UserPartyResultForm>
     */
    @Select("<script>" +
            "SELECT u.user_id userId,u.`name` userName, o.region_id as regionId, o.organization_id orgId,o.parent_id parentId,"+
            "o.`name` orgName,o.org_type orgType,o.org_type_child orgTypeChild,o.org_level orgLevel,\n" +
            "o.`status` orgStatus,u.`status` userStatus,(CASE o.is_retire WHEN 1 THEN 1 ELSE 2 END) isRetire,\n" +
            "(CASE u.political_type WHEN 1 THEN 1 WHEN 5 THEN 1 WHEN 17 THEN 1 WHEN 18 THEN 1 ELSE 2 END) isPartyMember,\n" +
            "u.create_time userCreateTime,u.update_time userUpdateTime,o.create_time orgCreateTime,o.update_time orgUpdateTime\n" +
            "FROM t_user_org_corp uoc\n" +
            "INNER JOIN t_organization o ON uoc.organization_id = o.organization_id\n" +
            "<if test=\"form.includeLevel == 1 \">AND (o.org_level LIKE CONCAT('%-',#{form.orgId},'-%') OR o.organization_id = #{form.orgId})\n</if>" +
            "<if test=\"form.includeLevel == 2 \">AND o.organization_id = #{form.orgId}\n</if>" +
            "<if test=\"form.includeLevel == 3 \">AND o.org_level LIKE CONCAT('%-',#{form.orgId},'-%')\n</if>" +
            "<if test=\"form.includeRetire == 2 \">AND o.is_retire != #{isRetire}\n</if>" +
            "<if test=\"form.includeRetire == 3 \">AND o.is_retire = #{isRetire}\n</if>" +
            "<if test=\"form.filterStatus == 2 \">AND o.`status` = #{status}\n</if>" +
            "INNER JOIN t_user u ON uoc.user_id = u.user_id\n" +
            "<if test=\"form.filterStatus == 2 \">AND o.`status` = #{status}\n</if>" +
            "<if test=\"form.includeAll == 2 \">AND FIND_IN_SET(u.political_type,#{politicalType})\n</if>" +
            "WHERE uoc.is_employee = #{isEmployee} ORDER BY uoc.organization_id DESC,uoc.user_id DESC \n" +
            "</script>")
    // 这里获取组织 所所属区县id
    List<StatisticalUserTempEntity> findPartyUserByWhere(@Param("form") UserPartyQueryForm form, @Param("isRetire") Integer isRetire,
                                                         @Param("status") Integer status, @Param("isEmployee") Integer isEmployee,
                                                         @Param("politicalType") String politicalType);

    /**
     * 根据角色 查询那些是工委管理员
     *
     * @param roleType
     * @return
     */
    @Select("<script>" +
            "SELECT   user_id  FROM t_user_role WHERE role_id IN (\n" +
            "\t\t\tSELECT role_id  FROM `t_role` WHERE `organization_id` = #{orgId}" +
            " AND `status` = '1' AND `role_type` <![CDATA[ <> ]]> '2'  and role_type=#{roleType} \n" +
            ")" +
            "</script>")
    List<Long> findWorkCommitManagerByWhere(@Param("orgId") Long orgId,
                                            @Param("roleType") int roleType);

    @Select("<script>" +
            "select tag_id from t_user_org_corp where user_id = #{userId} and organization_id = -999" +
            "</script>")
    String getPublicTag(@Param("userId") Long userId);

    @Select("<script>SELECT a.organization_id organizationId,a.`name` name,a.parent_id parentId ,a.org_level orgLevel " +
            "from t_organization a,t_user_org_corp b " +
            "where a.organization_id = b.organization_id \n" +
            " and b.user_id = #{userId} and b.region_id = #{regionId}\n" +
            "<if test=\"activate != 0\"> and a.activate = #{activate}</if>" +
            "<if test=\"status != 0\"> and a.`status` = #{status}</if> order by a.organization_id </script>")
    List<OrganizationEntity> getOrgList(@Param("userId") long userId, @Param("status") int status,
                                        @Param("activate") int activate, @Param("regionId") Long regionId);

    @Select("<script>" +
            "SELECT u.user_id userId,u.`name` userName,\n" +
            "u.gender,u.education,u.age,u.phone,u.phone_secret phoneSecret,u.cert_number certNumber,\n" +
            "u.cert_number_secret certNumberSecret,o.organization_id orgId,o.`name` orgName,\n" +
            "o.org_type orgType,o.org_type_child orgTypeChild,o.region_id regionId\n" +
            "FROM t_user u\n" +
            "INNER JOIN t_user_org_corp uoc ON uoc.user_id = u.user_id\n" +
            "INNER JOIN t_organization o ON uoc.organization_id = o.organization_id\n" +
            "WHERE u.`status` = #{status}\n" +
            "<if test=\"regionId != null\">AND o.`region_id` = #{regionId}\n</if>" +
            "AND o.`status` = #{status}\n" +
            "AND uoc.user_id = #{userId}\n" +
            "<if test=\"isEmployee != null\">AND uoc.is_employee = #{isEmployee}\n</if>" +
            "</script>")
    List<UserInfoBase> findUserByKey(@Param("status") Integer status, @Param("isEmployee") Integer isEmployee,
                                     @Param("userId") Long userId, @Param("regionId") Long regionId);

    @Select("SELECT DISTINCT u.user_id \n" +
            "FROM t_user AS u\n" +
            "\tLEFT JOIN t_user_org_group_member AS uogm ON u.user_id = uogm.user_id \n" +
            "\tLEFT JOIN t_user_org_group AS uog ON uogm.org_group_id = uog.org_group_id \n" +
            "WHERE u.user_id = #{userId} \n" +
            "\tAND u.`status` = 1 \n" +
            "\tAND uog.is_delete = 2 \n" +
            "\tAND DATE_FORMAT( uogm.create_time, '%Y-%m' ) <= #{calDate}")
    Long findOrgGroupMember(@Param("userId") Long userId, @Param("calDate") String calDate);


    @Select("<script>" +
            " select uoc.user_id as userId, o.organization_id as orgId, o.`name`\n" +
            " from t_user_org_corp uoc JOIN t_organization o ON o.organization_id = uoc.organization_id\n" +
            " where o.org_type = 102807\n" +
            "  and uoc.user_id = #{userId}\n" +
            "  and uoc.region_id = #{regionId}" +
            "</script>")
    List<UserOrgInfo> selectByUserAndOrg(@Param("userId") Long userId,
                                         @Param("regionId") Long regionId);
}
