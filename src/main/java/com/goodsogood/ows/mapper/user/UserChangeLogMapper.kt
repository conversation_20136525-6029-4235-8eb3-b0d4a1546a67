package com.goodsogood.ows.mapper.user

import com.goodsogood.ows.mapper.MyMapper
import com.goodsogood.ows.model.db.user.UserChangeLogEntity
import com.goodsogood.ows.model.mongodb.fusion.MemberChangeDetail
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Repository

/**
 *
 * <AUTHOR>
 * @Date 2023-03-22 15:42:29
 * @Description UserChangeLogMapper
 *
 */
@Repository
@Mapper
interface UserChangeLogMapper : MyMapper<UserChangeLogEntity> {

    /**
     * 移除 专职党务工作者 标签
     */
    @Select(
        "SELECT COUNT(DISTINCT user_id) FROM `t_user_change_log` \n " +
                "WHERE LOCATE( #{tagId}, tags_before ) > 0 AND LOCATE( #{tagId}, tags_after ) = 0 \n" +
                "AND user_id = #{userId} AND create_time LIKE CONCAT(#{year},'%') \n"
    )
    fun removeTag(
        tagId: String,
        userId: Long,
        year: Int
    ): Int?

    /**
     * 添加 专职党务工作者 标签
     */
    @Select(
        "SELECT COUNT(DISTINCT user_id) \n" +
                "FROM `t_user_change_log` \n" +
                "WHERE LOCATE( #{tagId}, tags_before ) = 0 AND LOCATE( #{tagId}, tags_after ) > 0 \n" +
                "AND user_id = #{userId} AND create_time LIKE CONCAT(#{year},'%') \n"
    )
    fun addTag(
        tagId: String,
        userId: Long,
        year: Int
    ): Int?


    @Select(
        "SELECT DISTINCT '业务到党务' AS changeDirection,\tuser_id AS userId, \t\n" +
                "user_name AS userName,\tphone, CONCAT(user_name,'(',phone,')') AS userView,\n" +
                "'原: 专职党务干部，现：\"专卖管理\"业务序列' AS detail \n" +
                "FROM `t_user_change_log` \n" +
                "WHERE LOCATE(#{seq4},sequence_before) > 0 AND LOCATE(#{seq4},sequence_after) = 0 \n" +
                "AND user_id = #{userId} AND create_time LIKE CONCAT(#{year},'%') \n"
    )
    fun partyToBusiness(
        userId: Long,
        seq4: Int,
        year: Int
    ): MemberChangeDetail?

    @Select(
        "SELECT DISTINCT '党务到业务' AS changeDirection,\tuser_id AS userId, \tuser_name AS userName,\tphone,\n" +
                "CONCAT(user_name,'(',phone,')') AS userView,'原: \"专卖管理\"业务序列，现：专职党务干部' AS detail \n" +
                "FROM `t_user_change_log`\n" +
                "WHERE (LOCATE(#{seq1},sequence_before) > 0 " +
                "OR LOCATE(#{seq2},sequence_before) > 0 " +
                "OR LOCATE(#{seq3},sequence_before) > 0 ) AND LOCATE(#{seq4},sequence_after) > 0 \n" +
                "AND user_id = #{userId} AND create_time LIKE CONCAT(#{year},'%') \n"
    )
    fun businessToParty(
        userId: Long,
        year: Int,
        seq1: Int,
        seq2: Int,
        seq3: Int,
        seq4: Int
    ): MemberChangeDetail?
}