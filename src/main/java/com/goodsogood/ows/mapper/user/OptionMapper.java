package com.goodsogood.ows.mapper.user;


import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.user.OptionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 * 数据字典mapper
 *
 * <AUTHOR>
 * @data 2018-03-29
 */
@Repository
@Mapper
public interface OptionMapper extends MyMapper<OptionEntity> {

    @Select("select max(op_key)+1 opKey from t_option where code = #{code}")
    OptionEntity getNextOpKey(@Param("code") String code);

//    @Select("select op_key,op_value from t_option where code=${code}")
//    List<HashMap<String,Object>> getOption(@Param("code") Integer code);

    /**
     * 获取父节点职务或者职级
     * @param code
     * @return
     *  type=${code}
     */
    @Select(" select option_id id,value,have_node haveNode from t_user_leader_option " +
            "where parent_id =0  and   FIND_IN_SET(type,#{code}) order by order_number asc ")
    List<HashMap<String,Object>> getParentOptions(@Param("code") String code);

    /**
     * 获取子节点职务或者职级
     * @param parentId
     * @return
     */
    @Select("select value from t_user_leader_option where parent_id =${parentId} order by order_number asc ")
    List<String> getSonOptions(@Param("parentId") Long parentId);

    /**
     * 迷糊匹配code
     * @param code
     * @return
     */
    @Select("select code, op_key opKey, op_value opValue from t_option where code like concat(#{code}, '%')")
    List<OptionEntity> likeCode(@Param("code") String code);
}
