package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.vo.CenterGroupVo;
import com.rabbitmq.client.LongString;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface CenterGroupMapper extends MyMapper<CenterGroupVo>{
@Select("""

select m.org_id AS orgId,m.org_name AS orgName,count(1) AS learn,sum(ifnull(tag.ztjy,0)) AS educations,sum(ifnull(tag.dyyt,0)) AS topics,sum(ifnull(tag.dyrh,0)) AS fusions
from t_meeting m
left join t_meeting_type mt on m.meeting_id=mt.meeting_id
left join (select meeting_id,max(if(tag_id=#{educations},1,0))ztjy,max(if(tag_id=#{topics},1,0))dyyt,max(if(tag_id=#{fusions},1,0))dyrh
	from t_meeting_tag
	group by meeting_id
) tag on m.meeting_id=tag.meeting_id
	where m.status in(7,12,13,14)
	and mt.type='党组理论学习中心组学习会议'
	and year(m.start_time)=#{year}
	and m.is_del = 0
	group by m.org_id
""")
    List<CenterGroupVo> statistics(@Param("topics")Long topics,@Param("fusions") Long fusions,@Param("educations") Long educations, @Param("year")Integer year);


//    @Select("""
//
//select m.org_id AS orgId,m.org_name AS orgName,count(1) AS learn,sum(ifnull(tag.ztjy,0)) AS educations,sum(ifnull(tag.dyyt,0)) AS topics,sum(ifnull(tag.dyrh,0)) AS fusions
//from t_meeting m
//left join t_meeting_type mt on m.meeting_id=mt.meeting_id
//left join (select meeting_id,max(if(tag_id=#{educations},1,0))ztjy,max(if(tag_id=#{topics},1,0))dyyt,max(if(tag_id=#{fusions},1,0))dyrh
//	from t_meeting_tag
//	group by meeting_id
//) tag on m.meeting_id=tag.meeting_id
//	where m.status in(7,12,13,14)
//	and mt.type='党组理论学习中心组学习会议'
//	and year(m.start_time)=#{year}
//	and m.is_del = 0
//	and m.org_id in (#{orgIds})
//	group by m.org_id
//""")
@Select("""
    <script>
select m.org_id AS orgId,m.org_name AS orgName,count(1) AS learn,sum(ifnull(tag.ztjy,0)) AS educations,sum(ifnull(tag.dyyt,0)) AS topics,sum(ifnull(tag.dyrh,0)) AS fusions
from t_meeting m
left join t_meeting_type mt on m.meeting_id=mt.meeting_id
left join (select meeting_id,max(if(tag_id=#{educations},1,0))ztjy,max(if(tag_id=#{topics},1,0))dyyt,max(if(tag_id=#{fusions},1,0))dyrh
	from t_meeting_tag
	group by meeting_id
) tag on m.meeting_id=tag.meeting_id
	where m.status in(7,12,13,14)
	and mt.type='党组理论学习中心组学习会议'
	and year(m.start_time)=#{year}
	and m.is_del = 0
	 <if test=\"orgIds != null and orgIds.size > 0\">
	 AND m.org_id IN 
	 <foreach item=\"orgId\" collection=\"orgIds\" open=\"(\" separator=\",\" close=\")\">
	 #{orgId}
	 </foreach>
	 </if>
	group by m.org_id
	</script>
""")
    List<CenterGroupVo> statisticsOrgIds(@Param("orgIds")List<Long>orgIds, @Param("topics")Long topics,@Param("fusions") Long fusions,@Param("educations") Long educations, @Param("year")Integer year);
}
