package com.goodsogood.ows.mapper.sas;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.sas.PbmOrgTargetEntity;
import com.goodsogood.ows.model.db.sas.PbmTargetEntity;
import com.goodsogood.ows.model.mongodb.fusion.*;
import com.goodsogood.ows.model.vo.pbm.PbmTargetExportVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface PbmTargetMapper extends MyMapper<PbmTargetEntity> {


    @Select("<script>" +
            "SELECT\n" +
            "\tpbm_target_id pbmTargetId,\n" +
            "\torg_id orgId,\n" +
            "\tuser_name userName,\n" +
            "\tphone,\n" +
            "\tunit,\n" +
            "\tdepartment,\n" +
            "\tis_party_member isPartyMember,\n" +
            "\tbranch,\n" +
            "\tscore,\n" +
            "\tremark,\n" +
            "\ttime,\n" +
            "\tcreate_time createTime,\n" +
            "\tupdate_time updateTime,\n" +
            "\tlast_change_user lastChangeUser \n" +
            "FROM\n" +
            "\tt_pbm_target\n" +
            "WHERE\n" +
            "\t org_id = #{orgId} \n" +
            "\tAND time = #{time}\n " +
            "<if test = \"name != null\">  AND user_name like CONCAT('%',#{name},'%')\n </if> " +
            "<if test = \"isPartyMember != null\">  AND is_party_member = #{isPartyMember}\n </if>" +
            "</script>")
    List<PbmTargetEntity> pbmTargetList(@Param("orgId") Long orgId,
                                        @Param("time") String time,
                                        @Param("name") String name,
                                        @Param("isPartyMember") Integer isPartyMember);



    @Select("  SELECT org_id as unitId, examine_score as itemScore FROM t_pbm_org_target\n" +
            "  WHERE time=#{time} and org_id=#{unitId} and examine_score is not NULL\n" +
            "  ORDER BY examine_score DESC")
    List<PartyBuildTarget> getPartyBuildingEvalScore(@Param("time") Integer time,
                                                     @Param("unitId") Long unitId);



    @Select("  SELECT org_id as unitId, target_score as itemScore FROM t_pbm_org_target\n" +
            "  WHERE time=#{time} and org_id=#{unitId} and target_score is not NULL\n" +
            "  ORDER BY target_score DESC")
    List<BusinessTarget> getBusinessEvalScore(@Param("time") Integer time,
                                              @Param("unitId") Long unitId);



    @Select("  SELECT org_id as unitId, satisfied_score as itemScore FROM t_pbm_org_target\n" +
            "  WHERE time=#{time} and org_id=#{unitId} and satisfied_score is not NULL\n" +
            "  ORDER BY satisfied_score DESC")
    List<SatisfiedTarget> getSatisfiedEvalScore(@Param("time") Integer time,
                                                @Param("unitId") Long unitId);


    @Select("  SELECT  \n" +
            "SUM(case is_party_member when 1 then avg_score else 0 end) 'partyMemberAvgScore',\n" +
            "SUM(case is_party_member when 2 then avg_score else 0 end) 'notPartyMemberAvgScore'\n" +
            "FROM\n" +
            "( \n" +
            "\tSELECT avg(score) as avg_score,is_party_member,unit\n" +
            "\tFROM t_pbm_target \n" +
            "\tWHERE org_id=#{unitId} and time=#{time}\n" +
            "\tGROUP BY org_id,is_party_member\n" +
            ") as l")
    PartyGroupCompareTarget getPartyGroupCompareCalc(@Param("time") String time,
                                                     @Param("unitId") Long unitId);

    /**
     * 党群绩效对比 详情
     * @param time
     * @param unitId
     * @return
     */
    @Select(" \tSELECT (@row_number:=@row_number + 1) AS num,\n" +
            "\tuser_name as userName,phone,unit as unitName,branch,is_party_member as isPartyMember,\n" +
            "\tscore as itemScore,department\n" +
            "\tFROM t_pbm_target,(SELECT @row_number:=0) AS t \n" +
            "\tWHERE org_id=#{unitId} and time=#{time}\n" +
            "\tORDER BY score " )
    List<PartyGroupCompareTargetDetail> getPartyGroupCompareInfo(@Param("time") String time,
                                                                 @Param("unitId") Long unitId);
    @Select("SELECT\n" +
            "\tpbm_target_org_id pbmTargetOrgId,\n" +
            "\torg_id orgId,\n" +
            "\t`name`,\n" +
            "\ttarget_score targetScore,\n" +
            "\ttarget_remark targetRemark,\n" +
            "\tsatisfied_score satisfiedScore,\n" +
            "\tsatisfied_remark satisfiedRemark,\n" +
            "\texamine_score examineScore,\n" +
            "\texamine_remark examineRemark,\n" +
            "\ttime,\n" +
            "\tcreate_time createTime,\n" +
            "\tupdate_time updateTime,\n" +
            "\tlast_change_user lastChangeUser\n" +
            "WHERE\n" +
            "\ttime = #{time} \n" +
            "<if test=\"#{orgName} != null\"> AND name like CONCAT('%',#{orgName},'%')</if>\n" +
            "FROM\n" +
            "\tt_pbm_org_target")
    List<PbmOrgTargetEntity> pbmOrgTargetList(@Param("orgName") String orgName, @Param("time") String time);

    @Select("<script>" +
            "SELECT\n" +
            "\tuser_name userName,\n" +
            "\tphone,\n" +
            "\tunit,\n" +
            "\tdepartment,\n" +
            "\tIF(is_party_member = 1,'是','否') isPartyMember,\n" +
            "\tbranch,\n" +
            "\tscore,\n" +
            "\tremark\n" +
            "FROM\n" +
            "\tt_pbm_target\n" +
            "WHERE\n" +
            "\t org_id = #{orgId} \n" +
            "\tAND time = #{time}\n " +
            "<if test = \"name != null\">  AND user_name like CONCAT('%',#{name},'%')\n </if> " +
            "<if test = \"isPartyMember != null\">  AND is_party_member = #{isPartyMember}\n </if>" +
            "</script>")
    List<PbmTargetExportVO> getList(@Param("orgId") Long orgId,
                                    @Param("time") String time,
                                    @Param("name") String name,
                                    @Param("isPartyMember") Integer isPartyMember);
}
