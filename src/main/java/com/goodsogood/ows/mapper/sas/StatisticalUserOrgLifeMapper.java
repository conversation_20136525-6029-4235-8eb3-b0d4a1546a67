package com.goodsogood.ows.mapper.sas;

import com.goodsogood.ows.mapper.StatisticalUserOrgLifeInsertListMapper;
import com.goodsogood.ows.model.db.sas.StatisticalUserOrgLifeEntity;
import com.goodsogood.ows.model.db.sas.StatisticalUserTempEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.goodsogood.ows.mapper.sas.StatisticalUserTempMapper.SELECT_BY_WHERE;

/**
 * <AUTHOR>
 * @create 2019-07-23 17:05
 */
@Repository
@Mapper
public interface StatisticalUserOrgLifeMapper
        extends tk.mybatis.mapper.common.Mapper<StatisticalUserOrgLifeEntity>,
        StatisticalUserOrgLifeInsertListMapper<StatisticalUserOrgLifeEntity> {
    /**
     * 历史统计数据更新为上传状态
     */
    @Update("delete from t_statistical_user_org_life where  region_id=#{regionId}")
    void deleteAll(long regionId);


    /**
     * 查询双重组织生活情况
     *
     * @param pageUser        分页用户
     * @param year            年
     * @param months          月
     * @param activityTypeIds 显示的活动类型
     * @return
     */
    @Select(
            " <script> "
                    + "  SELECT \n"
                    + "  user_id,user_name,org_name\n"
                    + "  <if test =\"months != null and months.size() > 0 and  activityTypeIds != null and activityTypeIds != ''  \">"
                    + "     , "
                    + "     <foreach collection=\"months\" index=\"m_index\" item=\"m_item\" open=\" \" separator=\",\" close=\" \">\n"
                    + "          <foreach collection=\"activityTypeIds.split(',')\" index=\"t_index\" item=\"t_item\" open=\" \" separator=\",\" close=\" \">\n"
                    + "             MAX( CASE WHEN participate_num IS NOT NULL AND type_id = #{t_item} AND statistical_month = #{m_item} THEN participate_num ELSE 0 END ) AS `${t_item}_${m_item}`"
                    + "          </foreach>"
                    + "     </foreach>"
                    + "  </if> "
                    + "  FROM\n"
                    + "  (\n"
                    + "  SELECT a.user_id,user_name,org_name,type_id,type_name,participate_num,statistical_year,statistical_month from\n"
                    + "  (  SELECT user_id,user_name,org_name FROM t_statistical_user_temp WHERE 1=1 "
                    + "     <if test=\"regionId !=null\"> AND region_id = #{regionId} </if>"
                    + "     <if test=\"pageUser !=null and pageUser.size>0\">"
                    + "        and user_id in "
                    + "          <foreach collection=\"pageUser\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "           #{item.userId} "
                    + "          </foreach>"
                    + "     </if>"
                    + "     GROUP BY user_id "
                    + "      ) a\n"
                    + "      LEFT JOIN  "
                    + "    ( SELECT user_id,type_id,type_name,participate_num,statistical_year,statistical_month "
                    + "          FROM t_statistical_user_org_life WHERE 1=1 "
                    + "           <if test=\"regionId !=null\"> AND region_id = #{regionId} </if>"
                    + "           <if test =\"activityTypeIds != null and activityTypeIds != '' \">  and type_id in (${activityTypeIds}) </if> "
                    + "           <if test=\"pageUser !=null and pageUser.size>0\">"
                    + "              and user_id in "
                    + "                <foreach collection=\"pageUser\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "                 #{item.userId} "
                    + "                </foreach>"
                    + "           </if>"
                    + "           <if test =\"year != null \">  AND statistical_year = #{year} </if> "
                    + "            <if test =\"months != null and months.size() > 0 \">"
                    + "             and statistical_month in  "
                    + "               <foreach collection=\"months\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "                #{item} "
                    + "               </foreach>"
                    + "            </if> "
                    + "      ) b ON a.user_id = b.user_id\n"
                    + "  ) t\n"
                    + "  GROUP BY user_id"
                    + "   </script> ")
    List<Map<String, Object>> findListByPageUser(
            @Param(value = "pageUser") List<StatisticalUserTempEntity> pageUser,
            @Param("year") Integer year,
            @Param("months") List<String> months,
            @Param(value = "activityTypeIds") String activityTypeIds,
            @Param(value = "regionId") Long regionId);
    /**
     * 查询双重组织生活情况
     *
     * @param orgId           组织id
     * @param showOrgType     显示的组织类型
     * @param includeRetire   是都包含离退休 1 包含，2 不包含
     * @param includeAll      是否只查询党员 1 全部，2 只查询党员
     * @param year            年
     * @param months          月
     * @param activityTypeIds 显示的活动类型
     */
    @Select(
            " <script> "
                    + "  SELECT \n"
                    + "  user_id,user_name,org_name,org_type_child,is_retire\n"
                    + "  <if test =\"months != null and months.size() > 0 and  activityTypeIds != null and activityTypeIds != ''  \">"
                    + "     , "
                    + "     <foreach collection=\"months\" index=\"m_index\" item=\"m_item\" open=\" \" separator=\",\" close=\" \">\n"
                    + "          <foreach collection=\"activityTypeIds.split(',')\" index=\"t_index\" item=\"t_item\" open=\" \" separator=\",\" close=\" \">\n"
                    + "             MAX( CASE WHEN participate_num IS NOT NULL AND type_id = #{t_item} AND statistical_month = #{m_item} THEN participate_num ELSE 0 END ) AS `${t_item}_${m_item}`"
                    + "          </foreach>"
                    + "     </foreach>"
                    + "  </if> "
                    + "  FROM\n"
                    + "  (\n"
                    + "  SELECT a.user_id,user_name,org_name,type_id,type_name,participate_num,statistical_year,statistical_month,org_type_child,is_retire from\n"
                    + "  (  SELECT user_id,user_name,org_name,org_type_child,is_retire FROM t_statistical_user_temp "
                    + SELECT_BY_WHERE
                    + "     GROUP BY user_id "
                    + "      ) a\n"
                    + "      LEFT JOIN  "
                    + "    ( SELECT user_id,type_id,type_name,participate_num,statistical_year,statistical_month "
                    + "          FROM t_statistical_user_org_life WHERE 1=1 "
                    + "           <if test =\"activityTypeIds != null and activityTypeIds != '' \">  and type_id in (${activityTypeIds}) </if> "
                    + "           <if test =\"year != null \">  AND statistical_year = #{year} </if> "
                    + "            <if test =\"months != null and months.size() > 0 \">"
                    + "             and statistical_month in  "
                    + "               <foreach collection=\"months\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "                #{item} "
                    + "               </foreach>"
                    + "            </if> "
                    + "      ) b ON a.user_id = b.user_id\n"
                    + "  ) t\n"
                    + "  GROUP BY user_id"
                    + "   </script> ")
    List<Map<String, Object>> findListAll(
            @Param(value = "orgId") Long orgId,
            @Param(value = "showOrgType") String showOrgType,
            @Param(value = "includeRetire") Integer includeRetire,
            @Param(value = "includeAll") Integer includeAll,
            @Param(value = "startTime") Date startTime,
            @Param(value = "endTime") Date endTime,
            @Param("year") Integer year,
            @Param("months") List<String> months,
            @Param(value = "activityTypeIds") String activityTypeIds,
            @Param(value = "regionId") Long regionId);
}
