package com.goodsogood.ows.mapper.sas;


import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.tbc.TbcUserInfoEntity;
import com.goodsogood.ows.model.vo.tbc.PartyMemberXFIndexVo;
import com.goodsogood.ows.model.vo.tbc.TbcPbmVo;
import com.goodsogood.ows.model.vo.tbc.UserRankVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-12-27 17:19
 **/
@Repository
@Mapper
public interface TbcUserInfoMapper extends MyMapper<TbcUserInfoEntity> {

    /**
     * 全市排名
     * @param userId
     * @param currentMonth
     * @return
     */
    @Select("SELECT * FROM\n" +
            "    ( SELECT\n" +
            "        @row := @row + 1 as `row`,\n" +
            "\t\t\t\tt.*\n" +
            "    FROM\n" +
            "        (\n" +
            "\t\t\t\t\tSELECT * FROM\n" +
            "\t\t\t\t\t(\n" +
            "\t\t\t\t\t\tSELECT\n" +
            "\t\t\t\t\t\t\t\tGROUP_CONCAT(user_id) as userIds,xianfeng_index\n" +
            "\t\t\t\t\t\tFROM\n" +
            "\t\t\t\t\t\t\t\tt_tbc_user_info \n" +
            "\t\t\t\t\t\tWHERE\n" +
            "\t\t\t\t\t\t\t\tsta_month=#{currentMonth} \n" +
            "\t\t\t\t\t\t\t\tand org_id not in (${excludeOrgIds}) \n" +
            "\t\t\t\t\t\t\t\tGROUP BY xianfeng_index\n" +
            "\t\t\t\t\t\t)as L ORDER BY xianfeng_index DESC\n" +
            "\t\t\t\t) t ,\n" +
            "        (SELECT\n" +
            "            @row := 0) r )as L2  ")
    List<UserRankVo> allCityRank(@Param(value = "userId") Long userId,
                                 @Param(value = "currentMonth") String currentMonth,
                                 @Param(value = "excludeOrgIds")String excludeOrgIds);




    /**
     * 全市排名
     * @param currentMonth
     * @param excludeOrgIds
     * @return
     */
    @Select(" SELECT\n" +
            "\t CAST(AVG(xianfeng_index) AS DECIMAL(8))\n" +
            "FROM\n" +
            "\tt_tbc_user_info \n" +
            "WHERE\n" +
            "\tsta_month=#{currentMonth} \n" +
            "\tand org_id not in (${excludeOrgIds})")
   Integer allCityAvg(@Param(value = "currentMonth") String currentMonth,
                      @Param(value = "excludeOrgIds")String excludeOrgIds);


    /**
     * 全市党员人数
     * @param userId
     * @param currentMonth
     * @return
     */
    @Select("\t\t\t\t\t\tSELECT\n" +
            "\t\t\t\t\t\t\t\tcount(1)\n" +
            "\t\t\t\t\t\tFROM\n" +
            "\t\t\t\t\t\t\t\tt_tbc_user_info \n" +
            "\t\t\t\t\t\tWHERE\n" +
            "\t\t\t\t\t\t\t\tsta_month=#{currentMonth} \n" +
            "\t\t\t\t\t\t\t\tand org_id not in (${excludeOrgIds}) ")
    int allCityRankCount(@Param(value = "userId") Long userId,
                                 @Param(value = "currentMonth") String currentMonth,
                                 @Param(value = "excludeOrgIds")String excludeOrgIds);

    /**
     * 单位排名  这里是用户id 合在一起统计
     * @param userId
     * @param currentMonth
     * @return
     */
   @Select("\t\tSELECT * FROM\n" +
            "    ( \n" +
            "\t\t\tSELECT @row := @row + 1 as `row`, t.*\n" +
            "\t\t\tFROM\n" +
            "        (\t\t\t\t\n" +
            "\t\t\t\t\tSELECT * FROM \t\t\t\t\n" +
            "\t\t\t\t\t(\t\t\t\t\n" +
            "\t\t\t\t\t\t\tSELECT\n" +
            "\t\t\t\t\t\t\t\t\tGROUP_CONCAT(user_id) as userIds,xianfeng_index \n" +
            "\t\t\t\t\t\t\tFROM\n" +
            "\t\t\t\t\t\t\t\t\tt_tbc_user_info \n" +
            "\t\t\t\t\t\t\tWHERE\n" +
            "\t\t\t\t\t\t\tsta_month=#{currentMonth} \n" +
            "\t\t\t\t\t\t\tand owner_id=(\n" +
            "\t\t\t\t\t\t\t\t\tSELECT\n" +
            "\t\t\t\t\t\t\t\t\t\t\towner_id \n" +
            "\t\t\t\t\t\t\t\t\tFROM\n" +
            "\t\t\t\t\t\t\t\t\t\t\tt_tbc_user_info \n" +
            "\t\t\t\t\t\t\t\t\tWHERE\n" +
            "\t\t\t\t\t\t\t\t\t\t\tuser_id=#{userId} \n" +
            "\t\t\t\t\t\t\t\t\t\t\tand sta_month=#{currentMonth} \n" +
            "\t\t\t\t\t\t\t\t\t\t\tand org_id not in (${excludeOrgIds}) \n" +
            "\t\t\t\t\t\t\t) \n" +
            "\t\t\t\t\t\t\tGROUP BY xianfeng_index\n" +
            "\t\t\t\t\t)as L ORDER BY xianfeng_index DESC\t\t\n" +
            "\t\t\t\t) t ,\n" +
            "        (SELECT @row := 0) r )as L2")
   List<UserRankVo> unitRank(@Param(value = "userId") Long userId,
                             @Param(value = "currentMonth") String currentMonth,
                             @Param(value = "excludeOrgIds")String excludeOrgIds);


    /**
     * 单位排名  这里是用户id 合在一起统计
     * @return
     */
    @Select("SELECT rank,xainFengIndex,org_name as orgName,`name`, party_score as partyIndex,\n" +
            "business_score as businessIndex,innovation_score as innovationIndex FROM\n" +
            "( \n" +
            "\tSELECT CASE \n" +
            "\t\tWHEN t.xainFengIndex = 0 THEN @rank\n" +
            "\t WHEN @last_score = t.xainFengIndex\n" +
            "\t\t\tTHEN @rank \n" +
            "\t WHEN @last_score := t.xainFengIndex\n" +
            "\t\t\tTHEN @rank := @rank + 1    \n" +
            "\t END AS rank , t.* FROM   \n" +
            "\t\t( \n" +
            "\t\t\tSELECT  * FROM\n" +
            "\t\t\t\t( SELECT\n" +
            "\t\t\t\t\t\txianfeng_index as xainFengIndex , user_name as `name`,\n" +
            "\t\t\t\t\t\torg_name, party_score,business_score,innovation_score\n" +
            "\t\t\t\tFROM\n" +
            "\t\t\t\t\t\tt_tbc_user_info \n" +
            "\t\t\t\tWHERE\n" +
            "\t\t\t\t\t\torg_id not in ( ${excludeOrgIds} ) \n" +
            "\t\t\t\t\t\tand sta_month= #{staMonth} \n" +
            "\t\t\t\t\t\tAND new_owner_id=#{unitId}\n" +
            "\t\t\t) as L ORDER BY xainFengIndex  DESC\n" +
            "\t\t) t ,(SELECT @rank := 0, @last_score := NULL) r\n" +
            ")as L2 ")
    List<PartyMemberXFIndexVo.XianFengIndex> unitRankDetail(@Param(value = "userId") Long userId,
                              @Param(value = "staMonth") String staMonth,
                              @Param(value = "excludeOrgIds")String excludeOrgIds,@Param(value = "unitId") Long unitId);

    /**
     * 单位人数
     * @param userId
     * @param currentMonth
     * @return
     */
    @Select("\t\t\t\tSELECT count(1)  FROM  t_tbc_user_info \n" +
            "        WHERE\n" +
            "            sta_month=#{currentMonth} \n" +
            "            and owner_id=(\n" +
            "                SELECT\n" +
            "                    owner_id \n" +
            "                FROM\n" +
            "                    t_tbc_user_info \n" +
            "                WHERE\n" +
            "                    user_id=#{userId} \n" +
            "\t\t\t\t\t\t\t\tand sta_month=#{currentMonth} \n" +
            "\t\t\t\t\t\t)\n" +
            "\t\t\t\t\t\tand org_id not in (${excludeOrgIds})")
    int unitRankCount(@Param(value = "userId") Long userId,
                              @Param(value = "currentMonth") String currentMonth,
                              @Param(value = "excludeOrgIds")String excludeOrgIds);

     /**
     * 支部排名
     * @param userId
     * @param currentMonth
     * @return
     */
    @Select("\t\tSELECT * FROM\n" +
            "    ( \n" +
            "\t\t\tSELECT @row := @row + 1 as `row`, t.*\n" +
            "\t\t\tFROM\n" +
            "        (\t\t\t\t\n" +
            "\t\t\t\t\tSELECT * FROM \t\t\t\t\n" +
            "\t\t\t\t\t(\t\t\t\t\n" +
            "\t\t\t\t\t\t\tSELECT\n" +
            "\t\t\t\t\t\t\t\t\tGROUP_CONCAT(user_id) as userIds,xianfeng_index \n" +
            "\t\t\t\t\t\t\tFROM\n" +
            "\t\t\t\t\t\t\t\t\tt_tbc_user_info \n" +
            "\t\t\t\t\t\t\tWHERE\n" +
            "\t\t\t\t\t\t\tsta_month=#{currentMonth} \n" +
            "\t\t\t\t\t\t\tand org_id=(\n" +
            "\t\t\t\t\t\t\t\t\tSELECT\n" +
            "\t\t\t\t\t\t\t\t\t\t\torg_id \n" +
            "\t\t\t\t\t\t\t\t\tFROM\n" +
            "\t\t\t\t\t\t\t\t\t\t\tt_tbc_user_info \n" +
            "\t\t\t\t\t\t\t\t\tWHERE\n" +
            "\t\t\t\t\t\t\t\t\t\t\tuser_id=#{userId} \n" +
            "\t\t\t\t\t\t\t\t\t\t\tand sta_month=#{currentMonth} \n" +
            "\t\t\t\t\t\t\t\t\t\t\tand org_id not in (${excludeOrgIds}) \n" +
            "\t\t\t\t\t\t\t) \n" +
            "\t\t\t\t\t\t\tGROUP BY xianfeng_index\n" +
            "\t\t\t\t\t)as L ORDER BY xianfeng_index DESC\t\t\n" +
            "\t\t\t\t) t ,\n" +
            "        (SELECT @row := 0) r )as L2")
    List<UserRankVo> branchRank(@Param(value = "userId") Long userId,
                                @Param(value = "currentMonth") String currentMonth,
                                @Param(value = "excludeOrgIds")String excludeOrgIds);

    /**
     * 支部排名人数
     * @param userId
     * @param currentMonth
     * @return
     */
    @Select("\t\t\t\tSELECT count(1)  FROM  t_tbc_user_info \n" +
            "        WHERE\n" +
            "            sta_month=#{currentMonth} \n" +
            "            and org_id=(\n" +
            "                SELECT\n" +
            "                    org_id \n" +
            "                FROM\n" +
            "                    t_tbc_user_info \n" +
            "                WHERE\n" +
            "                    user_id=#{userId} \n" +
            "\t\t\t\t\t\t\t\tand sta_month=#{currentMonth} \n" +
            "\t\t\t\t\t\t)\n" +
            "\t\t\t\t\t\tand org_id not in (${excludeOrgIds})")
    int branchRankCount(@Param(value = "userId") Long userId,
                                @Param(value = "currentMonth") String currentMonth,
                                @Param(value = "excludeOrgIds")String excludeOrgIds);


    /**
     * 全市排名平均
     * @return
     */
    @Select("\t\t\tSELECT AVG(xianfeng_index)  as xianFengIndex, sta_month as staMonth  \n" +
            "\t\t\t\t\t\tFROM t_tbc_user_info  where  sta_month=#{currentMonth} \n" +
            "\t\t\t\t\t\tand org_id not IN(${excludeOrgIds})")
    UserRankVo  allCityRankAvg(@Param(value = "userId") Long userId,
                               @Param(value = "currentMonth") String currentMonth,
                               @Param(value = "excludeOrgIds")  String excludeOrgIds);


    /**
     * 支部排名平均
     * @param userId
     * @param currentMonth
     * @return
     */
    @Select("\t\t\tSELECT\n" +
            "            AVG(xianfeng_index)  as xianFengIndex, sta_month as staMonth \n" +
            "        FROM\n" +
            "            t_tbc_user_info \n" +
            "        WHERE\n" +
            "           sta_month=#{currentMonth}"+
            "           and  org_id=(\n" +
            "                SELECT\n" +
            "                    org_id \n" +
            "                FROM\n" +
            "                    t_tbc_user_info \n" +
            "                WHERE\n" +
            "                    user_id=#{userId} \n" +
            "                    and sta_month=#{currentMonth}\n" +
            "            ) \n" +
            "        and org_id not in (${excludeOrgIds}) ")
    UserRankVo branchRankAvg(@Param(value = "userId") Long userId,
                             @Param(value = "currentMonth") String currentMonth,
                             @Param(value = "excludeOrgIds")  String excludeOrgIds);


    /**
     * 支部排人员的平均指数
     * @param currentMonth
     * @return
     */
    @Select("<script>"+
            "\t\t\tSELECT\n" +
            "            xianfeng_index as xianFengIndex, sta_month as staMonth," +
            "            user_id as userId,user_name as userName,org_name as orgName \n" +
            "        FROM\n" +
            "            t_tbc_user_info \n" +
            "        WHERE\n" +
            "   <if test=\"overviewType !=null and overviewType==2 \">"+
            "           org_id=#{orgId} "+
            "   </if>"+
            "   <if test=\"overviewType !=null and overviewType==1 \">"+
            "           ( org_id=#{orgId} or org_level like concat('%-',#{orgId},'-%') ) "+
            "   </if>"+
            "         AND  sta_month=#{currentMonth}"+
            "         and org_id not in (${excludeOrgIds}) order by  xianfeng_index desc "+
            "</script>")
    List<TbcUserInfoEntity> branchPersonRankAvg(
                            @Param(value = "overviewType") Integer overviewType,
                            @Param(value = "orgId") Long orgId,
                            @Param(value = "currentMonth") String currentMonth,
                            @Param(value = "excludeOrgIds")  String excludeOrgIds);


    /**
     * 人员平均
     * @param userId
     * @return
     */
    @Select("SELECT xianfeng_index as xianFengIndex, sta_month as staMonth  " +
            "FROM t_tbc_user_info WHERE user_id=#{userId} and sta_month IN(${staMonth})")
    List<UserRankVo> userRankAvg(@Param(value = "userId") Long userId,
                           @Param(value = "staMonth") String staMonth,
                           @Param(value = "excludeOrgIds")  String excludeOrgIds);

   @Select("SELECT MIN(log_xianfeng_index) FROM t_tbc_user_info where DATE_FORMAT(create_time,'%Y%m') = DATE_FORMAT(NOW() ,'%Y%m')" )
   Double selectUserLogMin();

    @Select("SELECT Max(log_xianfeng_index) FROM t_tbc_user_info where DATE_FORMAT(create_time,'%Y%m') = DATE_FORMAT(NOW() ,'%Y%m')" )
    Double selectUserLogMax();


    @Select("SELECT MIN(log_xianfeng_index) FROM t_tbc_user_info where sta_month = #{currentMonth}" )
    Double selectUserLogMinNew(String currentMonth);

    @Select("SELECT Max(log_xianfeng_index) FROM t_tbc_user_info where sta_month = #{currentMonth}" )
    Double selectUserLogMaxNew(String currentMonth );


   @Update("update t_tbc_user_info set log_xianfeng_index=#{logXianfengIndex},update_time=now(),party_score=#{partyScore}," +
           "business_score=#{businessScore},innovation_score=#{innovationScore}"+
           " where tbc_user_info_id=#{tbcUserInfoId}")
    Integer updateLongXianFengIndex(TbcUserInfoEntity item);



    /**
     * 所属序列排名
     * @param userId
     * @param currentMonth
     * @return
     */
   @Select("SELECT * FROM\n" +
            "    ( SELECT\n" +
            "        @row := @row + 1 as `row`,\n" +
            "\t\t\t\tt.*\n" +
            "    FROM\n" +
            "        (\n" +
            "\t\t\t\t\tSELECT * FROM\n" +
            "\t\t\t\t\t(\n" +
            "\t\t\t\t\t\tSELECT\n" +
            "\t\t\t\t\t\t\t\tGROUP_CONCAT(user_id) as userIds,xianfeng_index\n" +
            "\t\t\t\t\t\tFROM\n" +
            "\t\t\t\t\t\t\t\tt_tbc_user_info \n" +
            "\t\t\t\t\t\tWHERE\n" +
            "\t\t\t\t\t\t\t\tsta_month=#{currentMonth}  and  seq_number=#{seqNumber} \n" +
            "\t\t\t\t\t\t\t\tand org_id not in (${excludeOrgIds}) \n" +
            "\t\t\t\t\t\t\t\tGROUP BY xianfeng_index\n" +
            "\t\t\t\t\t\t)as L ORDER BY xianfeng_index DESC\n" +
            "\t\t\t\t) t ,\n" +
            "        (SELECT\n" +
            "            @row := 0) r )as L2  ")
   List<UserRankVo> rankBySeqNumber(@Param(value = "userId") Long userId,
                                    @Param(value = "currentMonth") String currentMonth,
                                    @Param(value = "excludeOrgIds")String excludeOrgIds,
                                    @Param(value = "seqNumber")Integer seqNumber );


    /**
     * 得到时间序列
     * @return
     */
   @Select("select DISTINCT  DATE_FORMAT(create_time,'%c') from t_tbc_user_info \n" +
            "where  create_time between date_sub(now(),interval 6 month) and now()\n" +
            "ORDER BY create_time ASC")
   List<String> getDateList();



   @Select("select ${colName}  from t_tbc_user_info \n" +
           "where user_id=#{userId} AND create_time > #{startTime} and create_time < #{endTime}\n" +
           "ORDER BY create_time ASC\n")
   List<String> getUserIndex(@Param(value = "colName")String colName,
                             @Param(value = "userId") Long userId,
                             @Param(value = "startTime")String startTime,
                             @Param(value = "endTime")String endTime);


   @Select("select round(AVG(${colName}),1)  from t_tbc_user_info \n" +
           "where   create_time > #{startTime} and create_time < #{endTime}\n" +
           "GROUP BY DATE_FORMAT(create_time,'%Y-%m')")
   List<String> getSystemIndex(@Param(value = "colName")String colName,
                               @Param(value = "startTime")String startTime,
                               @Param(value = "endTime")String endTime);

   @Select("select round(AVG(${colName}),1)  from t_tbc_user_info \n" +
           "where seq_number=#{seqNumber} AND create_time > #{startTime} and create_time < #{endTime} \n" +
           "GROUP BY DATE_FORMAT(create_time,'%Y-%m');")
   List<String> getSeqIndex(@Param(value = "colName")String colName,
                            @Param(value = "seqNumber")Integer seqNumber,
                            @Param(value = "startTime")String startTime,
                            @Param(value = "endTime")String endTime);

   @Select("select round(AVG(${colName}),1)  from t_tbc_user_info \n" +
           "where new_owner_id=#{ownerId} AND create_time > #{startTime} and create_time < #{endTime}\n" +
           "GROUP BY DATE_FORMAT(create_time,'%Y-%m');")
   List<String> getUnitIndex(@Param(value = "colName")String colName,
                             @Param(value = "ownerId")Long ownerId,
                             @Param(value = "startTime")String startTime,
                             @Param(value = "endTime")String endTime);


    /**
     * 单位排名极限 最高与最小值
     * @param staMonth
     * @return
     */
   @Select("SELECT round(AVG(xianfeng_index),1) as unitAvg ,round(MAX(xianfeng_index),1) as unitMax from t_tbc_user_info\n" +
           " WHERE new_owner_id= #{newOwnerId}\n" +
           " and sta_month=#{staMonth} and org_id not in (${excludeOrgIds})")
   PartyMemberXFIndexVo unitRankLimitInfo(@Param(value = "newOwnerId") Long newOwnerId,
                                           @Param(value = "staMonth") String staMonth,
                                           @Param(value = "excludeOrgIds")String excludeOrgIds);


    /**
     * 系统排名极限 最高与最小值
     * @param userId
     * @param staMonth
     * @return
     */
    @Select(" SELECT round(AVG(xianfeng_index),1) as systemAverage ,round(MAX(xianfeng_index),1) as systemMax from t_tbc_user_info\n" +
            " WHERE  sta_month=#{staMonth} and org_id not in (${excludeOrgIds})")
   PartyMemberXFIndexVo systemRankLimitInfo(@Param(value = "userId") Long userId,
                                            @Param(value = "staMonth") String staMonth,
                                            @Param(value = "excludeOrgIds")String excludeOrgIds);
   @Select("<script>" +
           "SELECT rank,xainFengIndex,org_name as orgName,`name`, party_score as partyIndex,\n" +
            "business_score as businessIndex,innovation_score as innovationIndex FROM\n" +
            "( \n" +
            "\tSELECT CASE \n" +
            " WHEN t.xainFengIndex = 0 THEN @rank "+
            "\t WHEN @last_score = t.xainFengIndex\n" +
            "\t\t\tTHEN @rank \n" +
            "\t WHEN @last_score := t.xainFengIndex\n" +
            "\t\t\tTHEN @rank := @rank + 1    \n" +
            "\t END AS rank , t.* FROM   \n" +
            "\t\t( \n" +
            "\t\t\tSELECT  * FROM\n" +
            "\t\t\t\t( SELECT\n" +
            "\t\t\t\t\t\txianfeng_index as xainFengIndex , user_name as `name`,\n" +
            "\t\t\t\t\t\torg_name, party_score,business_score,innovation_score\n" +
            "\t\t\t\tFROM\n" +
            "\t\t\t\t\t\tt_tbc_user_info \n" +
            "\t\t\t\tWHERE\n" +
            "\t\t\t\t\t\torg_id not in ( ${excludeOrgIds}) \n" +
            "\t\t\t\t\t\tand sta_month= #{staMonth}  \n" +
           "   <if test=\" unitId !=null \">"+
           "           and  new_owner_id=#{unitId} "+
           "   </if>"+
            "\t\t\t) as L ORDER BY xainFengIndex  DESC LIMIT ${limitSize}\n" +
            "\t\t) t ,(SELECT @rank := 0, @last_score := NULL) r\n" +
            ")as L2 "+
            "</script>")
   List<PartyMemberXFIndexVo.XianFengIndex> rankTop(@Param(value = "staMonth")String staMonth,
                                                       @Param(value = "excludeOrgIds")String excludeOrgIds,
                                                       @Param(value = "unitId")Long unitId,
                                                       @Param(value = "limitSize")Integer limitSize);


   @Select("SELECT DISTINCT user_id FROM t_tbc_user_info  WHERE user_id is not NULL order by user_id ")
   List<Long> getDistinctUserInfo();

    @Update("UPDATE t_tbc_user_info set seq_number=#{seqNumber},new_owner_id=#{newOwnerId} WHERE user_id=#{userId}")
    Integer cleaningTbcBaseInfo(@Param(value = "seqNumber") Integer seqNumber,
                                @Param(value = "newOwnerId") Long newOwnerId,
                                @Param(value = "userId") Long userId);


    @Select("select Max(${colName})  from t_tbc_user_info \n" +
            "where new_owner_id=#{unitId} AND create_time > #{startTime} and create_time < #{endTime}\n" +
            "GROUP BY DATE_FORMAT(create_time,'%Y-%m');")
    List<String> getUnitMaxIndex(@Param(value = "colName")String colName,
                                 @Param(value = "startTime")String startTime,
                                 @Param(value = "endTime")String endTime,
                                 @Param(value = "unitId")Long unitId);

    /**
     * 党业融合统计用户先峰指数信息
     * @return
     */
    @Select("SELECT AVG(xianfeng_index) as avgIndex,if(MIN(xianfeng_index)<0,0,MIN(xianfeng_index)) as minIndex," +
            "MAX(xianfeng_index) as maxIndex FROM t_tbc_user_info;")
    TbcPbmVo  getTbcUserPbmInfo();



    /**
     * 党业融合统计用户的党建指标信息
     * @return
     */
    @Select("SELECT AVG(party_index) as avgIndex,if(MIN(party_index)<0,0,MIN(party_index)) as minIndex," +
            "MAX(party_index) as maxIndex FROM t_tbc_user_info;")
    TbcPbmVo  getTbcUserPartyIndexPbmInfo();


    /**
     * 党业融合统计用户的业务指标信息
     * @return
     */
    @Select("SELECT AVG(business_index) as avgIndex,if(MIN(business_index)<0,0,MIN(business_index)) as minIndex," +
            "MAX(business_index) as maxIndex FROM t_tbc_user_info;")
    TbcPbmVo  getTbcUserBusinessIndexPbmInfo();


    /**
     * 党业融合统计用户的创新指标信息
     * @return
     */
    @Select("SELECT AVG(innovation_index) as avgIndex,if(MIN(innovation_index)<0,0,MIN(innovation_index)) as minIndex," +
            "MAX(innovation_index) as maxIndex FROM t_tbc_user_info;")
    TbcPbmVo  getTbcUserInnovationIndexPbmInfo();
}
