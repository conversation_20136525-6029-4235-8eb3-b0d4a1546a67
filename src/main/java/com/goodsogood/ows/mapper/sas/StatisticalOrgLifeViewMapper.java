package com.goodsogood.ows.mapper.sas;

import com.goodsogood.ows.mapper.StatisticalOrgLifeViewInsertListMapper;
import com.goodsogood.ows.model.db.sas.StatisticalOrgLifeViewEntity;
import com.goodsogood.ows.model.vo.sas.MeetingTypeForm;
import com.goodsogood.ows.model.vo.sas.StatisticalOrgLifeViewForm;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2019-07-23 15:15
 */
@Repository
@Mapper
public interface StatisticalOrgLifeViewMapper
        extends tk.mybatis.mapper.common.Mapper<StatisticalOrgLifeViewEntity>,
        StatisticalOrgLifeViewInsertListMapper<StatisticalOrgLifeViewEntity> {
    @Select(" <script> "
            + " SELECT org_id,org_name"
            + " <if test =\"orgLifeTypes != null and orgLifeTypes.size() >0\"> "
            + "      , "
            + "      <foreach collection=\"orgLifeTypes\" index=\"index\" item=\"item\" open=\"\" separator=\",\" close=\"\">\n"
            + "         SUM(IF (participate_num IS NOT NULL AND type_id=#{item.typeId},participate_num, 0 ) ) AS `${item.typeId}`"
            + "      </foreach>"
            + " </if> "
            + " FROM "
            + " t_statistical_org_life_view WHERE statistical_year = #{year} and region_id=#{regionId}"
            + " <if test =\"months != null and months.size() >0\"> "
            + "    and  statistical_month in  "
            + "      <foreach collection=\"months\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "        #{item}"
            + "      </foreach>"
            + " </if> "
            + " GROUP BY org_id "
            + " order by org_seq"
            + " </script> ")
    List<Map<String, String>> findList(
            @Param("year") Integer year, @Param("months") Set<Integer> months, @Param("orgLifeTypes") List<MeetingTypeForm> orgLifeTypes,@Param("regionId") Long regionId);
    /**
     * 查询党组织生活统计列表
     *
     * @param orgLifeTypes          组织生活统计类型
     * @param dualOrgLifeTypes      双重组织生活统计类型
     * @param orgTypesOfOrgLife     组织生活统计的组织类型
     * @param orgTypesOfDualOrgLife 双重组织生活统计的组织类型
     * @param year                  年份
     * @param month                 月份
     */
    @Insert(
            " <script> "
                    + " Insert into t_statistical_org_life_view "
                    + "      (org_id,"
                    +"        region_id,"
                    + "       org_name,"
                    + "       org_seq,"
                    + "       statistical_year,"
                    + "       statistical_month,"
                    + "       create_time,"
                    + "       type_id,"
                    + "       type_name,"
                    + "       participate_num)"
                    + " SELECT\n"
                    + "      #{orgId} as orgId,\n"
                    + "      (SELECT region_id FROM t_organization WHERE organization_id=#{orgId} LIMIT 1) AS regionId,\n"
                    + "      #{orgName} as orgName,\n"
                    + "      #{orgSeq} as orgSeq,\n"
                    + "      #{year} as statisticalYear,\n"
                    + "      #{month} as statistical_month,\n"
                    + "      NOW() as create_time,\n"
                    + "      b.activity_id as typeId,\n"
                    + "      b.type_name as typeName,\n"
                    + "      SUM(IF (participate_num IS NOT NULL,participate_num, 0 ) ) AS participateNum \n"
                    + " FROM\n"
                    + "     (    SELECT participate_num,activity_type_id,region_id "
                    + "            FROM t_statistical_org_life "
                    + "            WHERE  ( org_level LIKE concat( '%-', #{orgId}, '-%' ) OR org_id = #{orgId} ) AND is_retire != 1  \n"
                    + "                  <if test =\"year != null \"> AND statistical_year = #{year} </if> "
                    + "                  <if test =\"orgTypesOfOrgLife != null and orgTypesOfOrgLife != ''\"> AND org_type_id in (${orgTypesOfOrgLife}) </if> "
                    + "                  <if test =\"month != null \"> and statistical_month = #{month} </if> "
                    + "    ) a\n"
                    + " RIGHT JOIN t_statistical_temp_activity b ON a.activity_type_id = b.activity_id \n"
                    + " WHERE 1=1 \n"
                    + "     <if test =\"orgLifeTypes != null and orgLifeTypes != ''\"> AND b.activity_id in (${orgLifeTypes}) </if> "
                    + " GROUP BY b.activity_id "
                    + " UNION ALL "
                    + " SELECT  "
                    + "      #{orgId} as orgId,\n"
                    + "      (SELECT region_id FROM t_organization WHERE organization_id=#{orgId} LIMIT 1) as regionId,\n"
                    + "      #{orgName} as orgName,\n"
                    + "      #{orgSeq} as orgSeq,\n"
                    + "      #{year} as statisticalYear,\n"
                    + "      #{month} as statisticalNonth,\n"
                    + "      NOW() as createTime,\n"
                    + "    "
                    + StatisticalOrgLifeViewForm.TYPE_DUAL_ORG_LIFE
                    + "   as typeId,"
                    + "    '"
                    + StatisticalOrgLifeViewForm.TYPE_DUAL_ORG_LIFE_NAME
                    + "'  as typeName,"
                    + "    IF(sum( participate_num ) IS NOT NULL, sum( participate_num ), 0 ) AS participate_num "
                    + " FROM t_statistical_leader_org_life "
                    + " WHERE  ( org_level LIKE concat( '%-', #{orgId}, '-%' ) OR org_id = #{orgId} ) AND is_retire != 1  \n"
                    + "                  <if test =\"year != null \"> AND statistical_year = #{year} </if> "
                    + "                  <if test =\"orgTypesOfDualOrgLife != null and orgTypesOfDualOrgLife != ''\"> AND org_type_id in (${orgTypesOfDualOrgLife}) </if> "
                    + "                  <if test =\"dualOrgLifeTypes != null and dualOrgLifeTypes != ''\"> AND activity_type_id in (${dualOrgLifeTypes}) </if> "
                    + "                  <if test =\"month != null \"> and statistical_month = #{month} </if> "
                    + " </script> ")
    int count(
            @Param("orgId") Long orgId,
            @Param("orgName") String orgName,
            @Param("orgSeq") Integer orgSeq,
            @Param("orgLifeTypes") String orgLifeTypes,
            @Param("orgTypesOfOrgLife") String orgTypesOfOrgLife,
            @Param("dualOrgLifeTypes") String dualOrgLifeTypes,
            @Param("orgTypesOfDualOrgLife") String orgTypesOfDualOrgLife,
            @Param("year") Integer year,
            @Param("month") Integer month);

    /**
     * 历史统计数据更新为上传状态
     */
    @Delete("delete from t_statistical_org_life_view ")
    void deleteAll();
}
