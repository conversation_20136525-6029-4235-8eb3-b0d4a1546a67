package com.goodsogood.ows.mapper.sas;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.sas.StatisticalOrgLifeEntity;
import com.goodsogood.ows.model.vo.sas.*;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.annotations.Result;
import org.springframework.stereotype.Repository;

import javax.print.DocFlavor;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2019-04-22 15:57
 **/
@Repository
//@Mapper
public interface StatisticalOrgLifeMapper extends MyMapper<StatisticalOrgLifeEntity> {

    @Update("UPDATE t_statistical_org_life set participate_num=#{participateNum} ," +
            " update_time=now(), " +
            " meeting_start_time=#{meetingStartTime} ," +
            " meeting_ids =#{meetingIds}" +
            " WHERE org_life_id=#{orgLifeId}")
    int updateMeetingStaInfo(@Param(value = "orgLifeId") Long orgLifeId,
                             @Param(value = "participateNum") Long participateNum,
                             @Param(value = "meetingStartTime") String meetingStartTime,
                             @Param(value = "meetingIds") String meetingIds);


    @Update("UPDATE t_statistical_org_life set participate_num=#{participateNum} ,update_time=now() ,`status`=#{status} WHERE org_life_id=#{orgLifeId}")
    int updateMeetingStaInfoByStatus(@Param(value = "orgLifeId") Long orgLifeId, @Param(value = "participateNum") Long participateNum,@Param(value = "status") Long status);

    @Select("SET @sql = NULL;\n" +
        "SELECT\n" +
        "  GROUP_CONCAT(DISTINCT\n" +
        "    CONCAT(\n" +
        "      'MAX(IF(L.statistical_month = ''',\n" +
        "      c.statistical_month,\n" +
        "      ''', L.participate_num, 0)) AS ''',\n" +
        "       CONCAT('month',c.statistical_month), ''''\n" +
        "    )\n" +
        "  ) INTO @sql\n" +
        "FROM t_statistical_org_life c\n" +
        "WHERE c.org_id=3048 AND c.activity_type_id=1;\n" +
        "SET @sql = CONCAT('SELECT org_name ,' , @sql ,' ,sum(participate_num) totalNum  FROM',\n" +
        "\t'(\n" +
        "\t\tSELECT org_name, activity_type_id,participate_num, statistical_year ,statistical_month  FROM t_statistical_org_life \n" +
        "\t\tWHERE org_id=3048 AND activity_type_id=1\n" +
        "\t\tGROUP BY statistical_year,statistical_month,activity_type_id\n" +
        "\t) as L'\n" +
        ");\n" +
        "PREPARE stmt FROM @sql;\n" +
        "EXECUTE stmt;\n" +
        "DEALLOCATE PREPARE stmt;")
    List<StasticReport> getStaReportInfo();



    /**
     * 统计组织生活list
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/4/24 18:52
     */
    //partBranchType=61
    //partGroupType=62
    @Select("<script> " +
        "SELECT\n" +
        "\tL.*,\n" +
        "\t( ${totalParam} ) AS total \n" +
        "FROM\n" +
        "\t(\n" +
        "\tSELECT\n" +
        "\t\torg_id,\t\t\tactivity_type_id,\n" +
        "\t\t\tactivity_type_name,\n" +
        "\t\torg_name,\n" +
        "\t\t${rowParam}\n" +
        "\tFROM\n" +
        "\t\t( SELECT s.org_name, s.activity_type_id,s.activity_type_name,s.statistical_month, sum( s.participate_num ) participate_num, s.org_id " +
        "FROM t_statistical_org_life as s " +
        "<if test=\"participateFlag and activityTypeId== #{partGroupType}\">LEFT JOIN t_statistical_storage_variable v ON s.org_id = v.org_id AND s.statistical_date = v.statistical_date </if> " +
        "WHERE s.statistical_year = #{statisticalYear} " +
        "and s.activity_type_id=#{activityTypeId} and (s.org_id=#{orgId} or s.org_level LIKE concat('%-', #{orgId}, '-%'))" +
        "<if test =\"isRetire != null\"> and s.is_retire=#{isRetire} </if>" +
        "<if test=\"showOrgType !='' and showOrgType !=null \"> " +
        "and  s.org_type_id in (${showOrgType}) and region_id=#{regionId} </if> " +
        "<if test=\"participateFlag and activityTypeId== #{partGroupType}\">AND v.storage_val > 0 </if> " +
        "<if test=\"participateFlag and activityTypeId== #{partBranchType}\">AND s.org_create_time is NOT NULL </if> " +
        "GROUP BY s.statistical_month, s.org_id ) AS a\n" +
        "\t\tRIGHT JOIN t_statistical_date_temp AS b ON a.statistical_month = b.`month` \n" +
        "\tWHERE\n" +
        "\t\ta.`statistical_month` IN (${dateStr}) \n" +
        "\tGROUP BY\n" +
        "\torg_id \n" +
        "\t) AS L"+
        "</script>")
    @Results({
        @Result(property = "orgName", column = "org_name"),
        @Result(property = "orgId", column = "org_id"),
        @Result(property = "activityTypeId", column = "activity_type_id"),
        @Result(property = "activityTypeName", column = "activity_type_name")
    })
    List<StatisticalOrgLifeEntity> findPageOrgLifepage(StatisticalOrgLifeEntity entity);

    /**
     * 统计每个月份的总计
     *
     * <AUTHOR>
     * @date 2019/4/24 9:17
     */
    @Select(" <script> " +
        "SELECT\n" +
        "\tL.*,\n" +
        "\t( ${totalParam} ) AS total \n" +
        "FROM\n" +
        "\t(\n" +
        "\tSELECT\n" +
        "${rowParam} " +
        "\tFROM\n" +
        "\t\t( SELECT org_id,org_name,statistical_month, sum( participate_num ) participate_num FROM t_statistical_org_life " +
        "WHERE statistical_year = #{statisticalYear} and region_id=#{regionId} " +
        "and activity_type_id=#{activityTypeId} " +
        "and (org_id=#{orgId} or `org_level` LIKE concat('%-', #{orgId}, '-%'))\n " +
        "<if test =\"isRetire != null\"> and is_retire=#{isRetire} </if> " +
        "<if test=\"showOrgType !='' and showOrgType !=null \"> and  org_type_id in (${showOrgType}) </if> " +
        "GROUP BY statistical_month ) AS a\n" +
        "\t\tRIGHT JOIN t_statistical_date_temp AS b ON a.statistical_month = b.`month` \n" +
        "\tWHERE\n" +
        "\t\ta.`statistical_month` IN ( ${dateStr} ) \n" +
        "\n" +
        "\t) AS L"+
        "</script>")
    StaMonth orgLifeCount(StatisticalOrgLifeEntity entity);

    /**
     * 获取组织名称
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/4/29 16:08
     */
    @Select("select org_name from ${tableName}  where org_id=#{orgId} limit 1")
    String getOrgName(@Param(value = "orgId")Long orgId,@Param(value = "tableName")String tableName);

    @Select("select org_life_id as orgLifeId , participate_num as participateNum from t_statistical_org_life  where org_id=#{orgId} and activity_type_id=#{activityTypeId} and statistical_date=#{statisticalDate} ")
    StatisticalOrgLifeEntity
    selectIsExistStaRecord(@Param(value = "orgId") Long orgId, @Param(value = "activityTypeId") Integer activityTypeId,  @Param(value = "statisticalDate") String statisticalDate);


    /**
     * 验证只有一条数据的时候
     * @return
     */
    @Select("<script>select org_life_id as orgLifeId , participate_num as participateNum ,org_id as orgId ,statistical_date as statisticalDate,activity_type_id as activityTypeId" +
            " from t_statistical_org_life " +
            " where 1=1 and participate_num <![CDATA[ <= ]]>  2  " +
            //"AND DATE_SUB(CURDATE(), ${queryCode}) <![CDATA[ <= ]]> CURDATE()" +
            "And DATE_SUB(DATE(CONCAT(statistical_date,\"-01\")),INTERVAL -1 MONTH) <![CDATA[ >= ]]> DATE_SUB( CURDATE(),${queryCode})</script>")
    List<StatisticalOrgLifeEntity> valEqualOneParticipateNum(@Param(value = "queryCode") String queryCode);

    /**
     * 得到支委会的信息
     * @return
     */
    @Select("<script>select org_life_id as orgLifeId , org_id as orgId ,statistical_date as statisticalDate" +
            " from t_statistical_org_life " +
            " where 1=1 and activity_type_id in ( SELECT GROUP_CONCAT(meeting_type_ids) FROM t_eval_option WHERE type_id=3 ) </script>")
    List<StatisticalOrgLifeEntity> getPeriodInfo(@Param(value = "queryCode") String queryCode);

    /**
     * 得到支委会的信息 创建时间,这里委会数量不做更改
     * @return
     */
    @Update("<script> UPDATE t_statistical_org_life set org_create_time=#{orgCreateTime} ,update_time=now(),has_period_tag=#{hasPeriodTag} WHERE org_life_id=#{orgLifeId} </script>" )
    int updatePeriodInfoInfo(@Param(value = "orgCreateTime")  String createTime,@Param(value = "orgLifeId") Long orgLifeId ,@Param("hasPeriodTag")Integer hasPeriodTag);


    /**
     * 获取活动名称
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/4/29 16:07
     */

    @Select("SELECT activity_type_name FROM t_statistical_org_life WHERE activity_type_id=#{activityId} LIMIT 1")
    String getActivityName(@Param(value = "activityId")Integer activityId);


    @Insert("INSERT INTO `t_statistical_org_life`(`org_id`,`region_id`, `org_name`, `org_type_id`, `parent_org_id`, `org_level`, `is_retire`, `activity_type_id`, `activity_type_name`, `participate_num`, `statistical_year`, `statistical_month`, `statistical_date`, `status`, `create_time`)" +
            " VALUES ( #{orgId},#{regionId}, #{orgName}, #{orgTypeId}, #{parentOrgId}, #{orgLevel}, #{isRetire}, #{activityTypeId},#{activityTypeName}, #{participateNum}, #{statisticalYear}, #{statisticalMonth}, #{statisticalDate}, #{status}, now())")
    Integer insertOrgLife(StatisticalOrgLifeEntity statisticalOrgLifeEntity);


    /**
     * 查询当月存在组织信息
     * @param orgId
     * @return
     */
    @Select("select org_life_id as orgLifeId , participate_num as participateNum from t_statistical_org_life  where org_id=#{orgId} and DATE_FORMAT(statistical_date,'%Y%m') = DATE_FORMAT(NOW() ,'%Y%m') ")
    List<StatisticalOrgLifeEntity> selectIsExistStaRecordTheMonth(@Param(value = "orgId") Long orgId);


    @Update("UPDATE t_statistical_org_life set org_name=#{orgName} ,org_type_id=#{orgTypeId},parent_org_id=#{parentOrgId},org_level=#{orgLevel},is_retire=#{isRetire},update_time=now() WHERE org_id=#{orgId} and DATE_FORMAT(statistical_date,'%Y%m') = DATE_FORMAT(NOW() ,'%Y%m')")
    int updateOrgRelationShip(StatisticalOrgLifeEntity statisticalOrgLifeEntity);

    @Update("<script> UPDATE t_statistical_org_life set org_create_time=#{orgCreateTime} ,period_sum=#{periodSum} " +
            " WHERE org_id=#{orgId} and region_id=#{regionId}" +
            " and activity_type_id <![CDATA[ <> ]]>" +
            " (SELECT meeting_type_ids FROM t_eval_option WHERE type_id=3 and region_id=#{regionId}) </script>" )
    int supplementOrgInfo(@Param(value = "orgCreateTime")  Date createTime,@Param(value = "orgId") Long orgId ,
                          @Param(value = "periodSum") Integer periodSum,@Param(value = "regionId") Long regionId );

    /**
     * 电子报表统计
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/6/12 17:56
     */
    //partBranchType=61
    //partGroupType=62
    @Select("<script>" +
        "SELECT\n" +
        "\ts.activity_type_id AS activityTypeId,\n" +
        "\ts.activity_type_name AS activityTypeName,\n" +
        "\t(\n" +
        "\t\tCASE\n" +
        "\t\t\t\n" +
        "\t\t\tWHEN s.activity_type_id = #{partBranchType} THEN\n" +
        "\t\t\tSUM(IF ( s.org_create_time IS NOT NULL, s.participate_num, 0 ) )\n" +
        "\t\t\tWHEN s.activity_type_id = #{partGroupType} THEN\n" +
        "\t\t\tSUM(IF ( v.storage_val > 0, s.participate_num, 0 ) )\n" +
        "\t\t\tELSE\n" +
        "\t\t\tSUM( s.participate_num )\n" +
        "\t\t\tEND\n" +
        "\t) AS participateNum," +
        "\tSUM(s.participate_num and IF(v.storage_val >0,true,null)) AS participatePartyNum,\n" +
        "\tSUM(s.participate_num  AND IF (s.org_create_time IS NOT NULL,TRUE,NULL)) AS participateNum,\n" +
        "\tCOUNT( s.org_id ) AS allOrgCount, \n" +
        "  COUNT(IF(s.participate_num>0,true,null)) AS participateOrgCount,\n" +
        "  COUNT(IF(s.participate_num>0,true,null) AND IF(v.storage_val >0,true,null)) AS hasParty,\n" +
        "  COUNT(IF(v.storage_val = 0,true,null)) AS notHasParty,\n" +
        "\tCOUNT(IF (s.participate_num > 0,TRUE,NULL) AND IF (s.org_create_time IS NOT NULL,TRUE,NULL)) AS participateHasPeriod,\n" +
        "\tCOUNT(IF (s.org_create_time IS NULL,TRUE,NULL)) as notHasPeriod, "+
        "\t(\n" +
        "  CASE\n" +
        "\tWHEN s.activity_type_id=#{partBranchType} THEN \n" +
        "\tCONVERT (COUNT(IF (s.participate_num > 0,TRUE,NULL) AND IF (s.org_create_time IS NOT NULL,TRUE,NULL)) / (COUNT(s.org_id)-COUNT(IF (s.org_create_time IS NULL,TRUE,NULL))) * 100,DECIMAL (15, 2)) \n" +
        "\tWHEN s.activity_type_id=#{partGroupType} THEN \n" +
        "\tconvert(COUNT(IF(s.participate_num>0,true,null) AND IF(v.storage_val >0,true,null))/(COUNT( s.org_id )-COUNT(IF(v.storage_val = 0,true,null)))*100,decimal(15,2))\n" +
        "\tELSE\n" +
        "\tconvert(COUNT(IF(s.participate_num>0,true,null))/COUNT( s.org_id )*100,decimal(15,2)) \n" +
        "\tEND ) AS proportion\n" +
        "FROM\n" +
        "\tt_statistical_org_life s\n" +
        "\tLEFT JOIN t_statistical_storage_variable v ON s.org_id = v.org_id AND s.statistical_date = v.statistical_date "+
        "WHERE\n" +
        "\ts.statistical_month  in (#{dateStr}) \n" +
        "\tAND s.statistical_year =  #{statisticalYear} \n" +
        "\tAND ( s.org_id = #{orgId} OR s.org_level LIKE concat( '%-', #{orgId}, '-%' ) ) \n" +
        "<if test=\"showOrgType !='' and showOrgType !=null \"> and  s.org_type_id in (${showOrgType}) </if> " +
        "GROUP BY\n" +
        "\ts.activity_type_id" +
        "</script>")
    List<ElectronicOrgLifeData> selectOrgLifeData(StatisticalOrgLifeEntity statisticalOrgLifeEntity);

    /**
     * 电子报表统计基本信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/6/13 8:52
     */
    @Select("<script>" +
        "SELECT\n" +
        "\tL.subOrgCount,\n" +
        "\t( SELECT org_name FROM t_statistical_org_life WHERE org_id = #{orgId} LIMIT 1 ) AS orgName\n" +
        "FROM\n" +
        "\t(\n" +
        "\tSELECT\n" +
        "\t\tCOUNT( DISTINCT org_id ) AS subOrgCount\n" +
        "\tFROM\n" +
        "\t\tt_statistical_org_life s \n" +
        "\tWHERE\n" +
        "\tDATE_FORMAT( STR_TO_DATE(statistical_date, '%Y-%m'), '%Y-%m' ) = DATE_FORMAT(NOW(),'%Y-%m') " +
        "\t\tAND ( s.org_level LIKE concat( '%-', #{orgId}, '-%' ) ) \n" +
        "\t) AS L;" +
        "</script>")
    ElectronicOrgLifeData selectOrgLifeBasicData(StatisticalOrgLifeEntity statisticalOrgLifeEntity);


    /**
     * 查询党组织生活统计列表
     *
     * @param orgLifeTypes          组织生活统计类型
     * @param dualOrgLifeTypes      双重组织生活统计类型
     * @param orgTypesOfOrgLife     组织生活统计的组织类型
     * @param orgTypesOfDualOrgLife 双重组织生活统计的组织类型
     * @param year                  年份
     * @param months                月份
     * @return List<StatisticalOrgLifeViewForm>
     */
    @Select(
            " <script> "
                    + " SELECT\n"
                    + "      b.activity_id as typeId,\n"
                    + "      SUM(IF (participate_num IS NOT NULL,participate_num, 0 ) ) AS participateNum \n"
                    + " FROM\n"
                    + "     (    SELECT participate_num,activity_type_id " +
                    "            FROM t_statistical_org_life " +
                    "            WHERE  ( org_level LIKE concat( '%-', #{orgId}, '-%' ) OR org_id = #{orgId} ) AND is_retire != 1  \n"
                    + "                  <if test =\"year != null \"> AND statistical_year = #{year} </if> "
                    + "                  <if test =\"orgTypesOfOrgLife != null and orgTypesOfOrgLife != ''\"> AND org_type_id in (${orgTypesOfOrgLife}) </if> "
                    + "                  <if test =\"months != null and months.size() > 0 \">"
                    + "                       and statistical_month in "
                    + "                      <foreach collection=\"months\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "                        #{item} "
                    + "                      </foreach>"
                    + "                   </if> "
                    + "    ) a\n"
                    + " RIGHT JOIN t_statistical_temp_activity b ON a.activity_type_id = b.activity_id \n"
                    + " WHERE 1=1 \n"
                    + "     <if test =\"orgLifeTypes != null and orgLifeTypes != ''\"> AND b.activity_id in (${orgLifeTypes}) </if> "
                    + " GROUP BY b.activity_id "
                    + " UNION ALL "
                    + " SELECT  "
                    + "    " + StatisticalOrgLifeViewForm.TYPE_DUAL_ORG_LIFE + "   as typeId,"
                    + "      SUM(IF (participate_num IS NOT NULL,participate_num, 0 ) ) AS participateNum \n"
                    + " FROM t_statistical_leader_org_life "
                    + " WHERE  ( org_level LIKE concat( '%-', #{orgId}, '-%' ) OR org_id = #{orgId} ) AND is_retire != 1  \n"
                    + "                  <if test =\"year != null \"> AND statistical_year = #{year} </if> "
                    + "                  <if test =\"orgTypesOfDualOrgLife != null and orgTypesOfDualOrgLife != ''\"> AND org_type_id in (${orgTypesOfOrgLife}) </if> "
                    + "                  <if test =\"dualOrgLifeTypes != null and dualOrgLifeTypes != ''\"> AND activity_type_id in (${dualOrgLifeTypes}) </if> "
                    + "                  <if test =\"months != null and months.size() > 0 \">"
                    + "                       and statistical_month in "
                    + "                      <foreach collection=\"months\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "                        #{item} "
                    + "                      </foreach>"
                    + "                   </if> "
                    + " </script> ")
    List<StatisticalOrgLifeViewForm> findList(
            @Param("orgId") Long orgId,
            @Param("orgName") String orgName,
            @Param("orgLifeTypes") String orgLifeTypes,
            @Param("orgTypesOfOrgLife") String orgTypesOfOrgLife,
            @Param("dualOrgLifeTypes") String dualOrgLifeTypes,
            @Param("orgTypesOfDualOrgLife") String orgTypesOfDualOrgLife,
            @Param("year") Integer year,
            @Param("months") Set<Integer> months);


    /**
     * 查询组织
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/6/13 10:01
     */
    @Select("<script>" +
            "SELECT s.org_id as orgId,s.org_name as orgName " +
            "FROM\n" +
            "\tt_statistical_org_life s \n" +
            "WHERE status = 1 \n" +
            "\tAND s.statistical_month  = #{time} \n"
            + "<if test=\"isRetire !=null\">"
            + "\t AND s.is_retire =  #{isRetire} \n"
            + "</if>" +
            "\tAND s.statistical_year =  #{year} \n" +
            "  <if test =\"name != null and name != '' \"> and s.org_name like concat( '%', #{name}, '%' ) </if> " +
            "\tAND ( s.org_id = #{orgId} OR s.org_level LIKE concat( '%-', #{orgId}, '-%' ) ) \n" +
            " GROUP BY org_id " +
            "</script>")
    List<EvalIdForm> findOrgList(EvalIdRequestForm form);


    @Select("SELECT meeting_type_ids FROM t_eval_option WHERE type_id=#{typeId} and region_id=#{regionId}")
    Integer getMeetingTypeId(@Param("typeId") Integer typeId, @Param("regionId") Long regionId);


    /**
     * 得到统计那些活动
     * @return
     */
    @Select("SELECT region_id as regionId,meeting_type_ids as meetingTypeIds  FROM t_eval_option WHERE search_type=1")
    List<com.goodsogood.ows.model.vo.activity.StaMeetingType> getStaMeetingType();


}
