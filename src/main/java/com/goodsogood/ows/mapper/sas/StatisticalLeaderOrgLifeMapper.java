package com.goodsogood.ows.mapper.sas;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.sas.StatisticalLeaderOrgLifeEntity;
import com.goodsogood.ows.model.vo.sas.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-04-22 16:01
 **/
@Repository
@Mapper
public interface StatisticalLeaderOrgLifeMapper extends MyMapper<StatisticalLeaderOrgLifeEntity> {

    @Select("<script> SELECT org_name as orgName,leader_name as leaderName,leader_user_id as leaderUserId,GROUP_CONCAT(leader_org_life_id) as ids \n" +
            "FROM t_statistical_leader_org_life as a\n" +
            "WHERE 1=1 and region_id=#{regionId} and  ( org_id=#{orgId} or `org_level` LIKE concat('%-', #{orgId}, '-%') ) and statistical_year=#{year} <if test=\"showOrgType !='' and showOrgType !=null \"> and  org_type_id in (${showOrgType})</if>\n" +
            "<if test=\"month !='' and month !=null \">\n" +
            "      and a.statistical_month in (${month})\n" +
            "</if>\n" +
            "<if test=\"isRetire !='' and isRetire !=null \">\n" +
            "      and a.is_retire = #{isRetire}\n" +
            "</if>\n" +
            "<if test=\"queryLimit == 1 \">\n" +
            "      and a.leader_user_id in ( SELECT DISTINCT\n" +
            "                               ol.user_id\n" +
            "                               FROM t_org_snapshot os\n" +
            "                               INNER JOIN t_user_org_leader ol ON os.owner_id = ol.org_id \n" +
            "                               AND ( ol.is_delete = 2  " +
            "                                           OR date_format(ol.update_time,'%Y-%m')" +
            "                                            <![CDATA[ >= ]]> #{startTime}) \n" +
            "                               AND date_format(ol.create_time,'%Y-%m')" +
            "                                            <![CDATA[ <= ]]> #{endTime}  \n" +
            "                               INNER JOIN t_user_snapshot us ON ol.user_id = us.user_id \n" +
            "                               AND us.YEAR = #{year} \n" +
            "                               <if test=\"month !='' and month != null \">\n" +
            "                               AND us.MONTH in (${month})\n" +
            "                               </if>\n" +
            "                               AND us.STATUS = 1 \n" +
            "                               WHERE\n" +
            "                               os.YEAR = #{year} \n" +
            "                               AND os.MONTH in (${month})\n" +
            "                               AND os.org_pid = #{rootId} \n" +
            "                               AND os.region_id = #{regionId} \n" +
            "                               AND os.STATUS =1 ) \n" +
            "</if>\n" +
            "GROUP BY org_id,leader_user_id </script>")
    List<StaLeaderOrgLife> getLeadOrgLife(@Param(value = "orgId") Long orgId,
                                          @Param(value = "rootId") Long rootId,
                                          @Param(value = "year") Integer year,
                                          @Param(value = "month") String month ,
                                          @Param(value = "isRetire") Integer isRetire,
                                          @Param(value = "showOrgType") String showOrgType,
                                          @Param(value = "regionId") Long regionId,
                                          @Param(value = "queryLimit") Integer queryLimit,
                                          @Param(value = "startTime") String startTime,
                                          @Param(value = "endTime") String endTime);


    @Select("<script>SELECT L3.*,(${totalParam}) as total FROM\n" +
            "(\n" +
            "\tSELECT \t\n" +
            "${rowParam} ,"+
            "\tc.activity_id as activityTypeId,c.type_name as activityTypeName FROM\n" +
            "\t(\n" +
            "\t\tSELECT * FROM\n" +
            "\t\t(\n" +
            "\t\t\tSELECT statistical_month ,activity_type_id,  sum(participate_num) participate_num ,org_id \n" +
            "\t\t\tFROM \t t_statistical_leader_org_life \n" +
            "\t\t\tWHERE leader_org_life_id in (${ids})   AND leader_user_id=#{leaderUserId}  " +
            "<if test=\"showOrgType !='' and showOrgType !=null \"> and  org_type_id in (${showOrgType}) </if>" +
            " <if test=\"isRetire !='' and isRetire !=null \"> and  is_retire =#{isRetire}</if> \n" +
            "\t\t\tGROUP BY statistical_month,org_id,activity_type_id\n" +
            "\t\t) as a\n" +
            "\t\tRIGHT JOIN  t_statistical_date_temp as b\n" +
            "\t\ton a.statistical_month=b.`month`\n" +
            "\t\tWHERE 1=1\n" +
            "\t\t<if test=\"month !='' and month !=null \"> and a.statistical_month in (${month}) </if>" +
            "\t\tGROUP BY org_id,activity_type_id,statistical_month\n" +
            ") as L2 \n" +
            "RIGHT JOIN t_statistical_temp_activity as c \n" +
            "on L2.activity_type_id=c.activity_id\n" +
            " WHERE 1=1 <if test=\"activityTypeIds !='' and activityTypeIds !=null \"> and  c.activity_id IN (${activityTypeIds})</if>\n" +
            "GROUP BY c.activity_id\n" +
            ") as L3</script>")
    List<StaMonth> getLeadOrgLifeDetailInfo(@Param(value = "leaderUserId") Long leaderUserId,@Param(value = "orgId")Long orgId,
                                            @Param(value = "year") Integer year,@Param(value = "month") String  month,
                                            @Param(value = "totalParam") String totalParam, @Param(value = "rowParam") String rowParam,
                                            @Param(value = "activityTypeIds") String activityTypeIds,@Param(value = "isRetire") Integer isRetire,
                                            @Param(value = "showOrgType") String showOrgType,@Param(value = "ids")String ids);

    @Select("SELECT activity_type_id as activityId ,activity_type_name as typeName  FROM t_statistical_leader_org_life\n" +
            "GROUP BY activity_type_id,activity_type_name")
    List<StaMeetingType> staMeetingType();


    @Select("<script>SELECT org_id as orgId,org_name as orgName,activity_type_id as activityTypeId ,activity_type_name as activityTypeName,status,\n" +
            "SUM(participate_num) as participateNum,org_create_time as orgCreateTime,has_period_tag hasPeriodTag, " +
            " party_group_num as partyGroupNum , \n" +
            "statistical_date AS statisticalDate,statistical_year as statisticalYear," +
            "org_type_id as orgTypeId,period_sum as periodSum , \n" +
            "statistical_month as statisticalMonth,is_retire as isRetire ,\n" +
            " meeting_start_time as meetingStartTime ,meeting_ids as meetingIds "+
            "FROM t_statistical_org_life\n" +
            "where 1=1 and region_id=#{regionId} <if test=\"orgId !='' and orgId !=null \"> and org_id=#{orgId}</if> \n" +
            "and  DATE_FORMAT(DATE_SUB(CURDATE(), ${queryCode}),'%Y-%m') <![CDATA[ < ]]> statistical_date\n" +
            "GROUP BY org_id,activity_type_id,statisticalDate </script>")
    List<EvalPullDataForm> evalPullData(@Param(value = "orgId") Long orgId,
                                        @Param(value = "queryCode") String queryCode,
                                        @Param(value = "regionId") Long regionId);



    @Select("<script>SELECT org_id as orgId,org_name as orgName,activity_type_id as activityTypeId ,activity_type_name as activityTypeName,status,\n" +
            "SUM(participate_num) as participateNum,org_create_time as orgCreateTime,has_period_tag hasPeriodTag, " +
            " party_group_num as partyGroupNum , \n" +
            "statistical_date AS statisticalDate,statistical_year as statisticalYear," +
            "org_type_id as orgTypeId,period_sum as periodSum , \n" +
            "statistical_month as statisticalMonth,is_retire as isRetire ,\n" +
            " meeting_start_time as meetingStartTime ,meeting_ids as meetingIds "+
            "FROM t_statistical_org_life\n" +
            "where 1=1 and region_id=#{regionId} <if test=\"orgId !='' and orgId !=null \"> and org_id=#{orgId}</if> \n" +
            "and  DATE_FORMAT(DATE_SUB(CURDATE(), ${queryCode}),'%Y-%m') <![CDATA[ < ]]> statistical_date\n" +
            "GROUP BY org_id,activity_type_id,statisticalDate </script>")
    List<EvalPullDataForm> evalPullDataNew(@Param(value = "orgId") Long orgId,
                                        @Param(value = "queryCode") String queryCode,
                                        @Param(value = "regionId") Long regionId);

    @Select("<script>" +
        "SELECT leader_user_id leaderUserId, " +
        "       leader_name leaderName, " +
        "       org_id orgId, " +
        "       org_name orgName, " +
        "       org_type_id orgTypeId, " +
        "       parent_org_id parentOrgId, " +
        "       org_level orgLevel, " +
        "       is_retire isRetire, " +
        "       SUM(participate_num) participateNum, " +
        "       statistical_date statisticalDate " +
        "  FROM t_statistical_leader_org_life where 1=1 and region_id=#{regionId}" +
        "  <if test=\"queryDate !='' and queryDate !=null \"> and statistical_date = #{queryDate} </if> " +
        " GROUP BY leader_user_id, statistical_date " +
        "</script>")
    List<EvalLeaderMeeting> getLeaderMeeting(@Param("queryDate") String queryDate,@Param("regionId") Long regionId);

    @Select("SELECT statistical_date ,storage_val as val  FROM t_statistical_storage_variable WHERE org_id=#{orgId} and storage_type=#{code}")
    List<Map<String, Object>> getStorageVariable(@Param("orgId")Long orgId, @Param("code") Long code);


    /**
     * 查询领导班子信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/6/13 10:01
     */
    @Select("<script>" +
            "SELECT s.leader_user_id as userId,s.leader_name as userName,s.org_id as orgId,s.org_name as orgName " +
            "FROM\n" +
            "\tt_statistical_leader_org_life s \n" +
            "WHERE status = 1 \n" +
            "\tAND s.statistical_month  = #{time} \n"
            + "<if test=\"isRetire !=null\">"
            + "\t AND s.is_retire =  #{isRetire} \n"
            + "</if>" +
            "\tAND s.statistical_year =  #{year} \n" +
            "  <if test =\"name != null and name != '' \"> and s.leader_name like concat( '%', #{name}, '%' ) </if> " +
            "\tAND ( s.org_id = #{orgId} OR s.org_level LIKE concat( '%-', #{orgId}, '-%' ) ) \n" +
            " GROUP BY org_id,leader_user_id " +
            "</script>")
    List<EvalIdForm> findLeaderList(EvalIdRequestForm form);

    @Select("<script>" +
            "SELECT left(adddate( DATE_SUB( CURDATE(), ${queryCode} ), interval numlist.id month),7) AS date FROM\n" +
            "(SELECT * from\n" +
            "(SELECT n1.i + n10.i * 10 AS id FROM t_num n1 CROSS JOIN t_num AS n10) a\n" +
            "where a.id  <![CDATA[ <= ]]> " +
            " (SELECT TIMESTAMPDIFF(MONTH,DATE_SUB( CURDATE(), INTERVAL  4 YEAR ),CURDATE()))) numlist\n" +
            "WHERE adddate( DATE_SUB( CURDATE(), ${queryCode}), interval numlist.id month)  <![CDATA[ <= ]]>  CURDATE()" +
            "</script>")
    List<String> findDateByCode(@Param("queryCode") String queryCode);
}
