package com.goodsogood.ows.mapper.sas;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Create by FuXiao on 2020/11/18
 */
@Repository
@Mapper
public interface AutoScoreMapper {

    @Select("SELECT score FROM t_rank_score_rule WHERE column_id IN (SELECT column_id FROM t_rank_column WHERE top_id = #{topId})")
    /**
     * 查询自动打分的总分 1组织 2人员
     */
    List<String> findScoreList(@Param(value = "topId") Long topId);

    @Select("SELECT SUM(score) FROM t_rank_org_score WHERE score_rule_id IN (SELECT score_rule_id FROM t_rank_score_rule WHERE column_id IN (SELECT column_id FROM t_rank_column WHERE column_name = #{ruleName} AND top_id = 1)) AND time LIKE \"${year}%\" AND org_id = #{orgId}")
    /**
     * 根据规则名，年份，组织id查询该规则总分
     */
    Integer findOrgRuleScore(@Param(value = "ruleName") String ruleName,
                          @Param(value = "year") Integer year,
                          @Param(value = "orgId") Long orgId);

    @Select("SELECT SUM(score) FROM t_rank_user_score WHERE score_rule_id IN (SELECT score_rule_id FROM t_rank_score_rule WHERE column_id IN (SELECT column_id FROM t_rank_column WHERE column_name = #{ruleName} AND top_id = 2)) AND time LIKE \"${year}%\" AND user_id = #{userId}")
    /**
     * 根据规则名，年份，人员id查询该规则总分
     */
    Integer findUserRuleScore(@Param(value = "ruleName") String ruleName,
                             @Param(value = "year") Integer year,
                             @Param(value = "userId") Long userId);

    @Select("SELECT SUM(score) FROM t_rank_user_score WHERE score_rule_id IN (SELECT score_rule_id FROM t_rank_score_rule WHERE column_id IN (SELECT column_id FROM t_rank_column WHERE column_name = \"活跃程度\" AND top_id = 2)) AND time LIKE \"${year}%\" AND score_rule_name != \"志愿者\" AND score_rule_name != \"学习\" AND user_id = #{userId}")
    /**
     * 人员活跃程度
     */
    Integer activeLevel(@Param(value = "year") Integer year,
                          @Param(value = "userId") Long userId);

    @Select("SELECT SUM(score) FROM t_rank_user_score WHERE score_rule_name = #{ruleName} AND time LIKE \"${year}%\" AND user_id = #{userId}")
    /**
     * 志愿者和学习
     */
    Integer volunteerLearn(@Param(value = "ruleName") String ruleName,
                          @Param(value = "year") Integer year,
                          @Param(value = "userId") Long userId);
}
