package com.goodsogood.ows.mapper.sas;

import com.goodsogood.ows.mapper.StatisticalUserTempInsertListMapper;
import com.goodsogood.ows.model.db.sas.StatisticalUserTempEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-07-26 17:42
 **/
@Repository
@Mapper
public interface StatisticalUserTempMapper extends tk.mybatis.mapper.common.Mapper<StatisticalUserTempEntity>, StatisticalUserTempInsertListMapper<StatisticalUserTempEntity> {
    /**
     * 历史统计数据更新为上传状态
     */
    @Update("delete from t_statistical_user_temp where  region_id=#{regionId} ")
    void deleteAll(long regionId);

    /**
     * 查询用户
     *
     * @param orgId         组织id
     * @param showOrgType   显示的组织类型
     * @param includeRetire 是否包含离退休 1:包含（默认）；2：不包含；
     * @param includeAll    1-是 2-否，是否包含所有人,默认所有.2只查询党员
     * @return List<StaLeaderOrgLife>
     */

    /**
     * 是否包含所有人, 2 只查询党员
     */
    int INCLUDE_ALL_NO = 2;

    String SELECT_BY_WHERE = "   WHERE 1=1 "
            + "   <if test=\"orgId !=null \"> and  ( org_id= #{orgId} or `org_level` LIKE concat('%-',#{orgId}, '-%') )</if>"
            + "   <if test=\"showOrgType !='' and showOrgType !=null \"> and  org_type_child in (${showOrgType})</if>"
            + "   <if test=\"includeRetire !=null and includeRetire==2 \">"
            + "           and is_retire = 2 "
            + "   </if>"
            + "   <if test=\"includeAll !=null and includeAll==" + INCLUDE_ALL_NO + " \">"
            + "           and  is_party_member = 1 "
            + "   </if>"
            + "   <if test=\"startTime !=null\">"
            + "           AND (org_status = 1  or (org_status = 2 AND org_update_time &gt;= #{startTime} ) ) "
            + "           AND (user_status = 1  or (user_status = 2 AND user_update_time &gt;= #{startTime} ) ) "
            + "   </if>"
            + "   <if test=\"endTime !=null\">"
            + "          AND org_create_time &lt;= #{endTime} "
            + "          AND user_create_time &lt;= #{endTime} "
            + "   </if>"
            + "   <if test=\"regionId !=null\">"
            + "          AND region_id = #{regionId} "
            + "   </if>";

    @Select("<script> "
            + " SELECT org_name as orgName,user_name as leaderName,user_id as leaderUserId,user_id as userId FROM t_statistical_user_temp "
            + SELECT_BY_WHERE
                    + " GROUP BY user_id "
                    + " ORDER BY user_id "
                    + " </script>")
    List<StatisticalUserTempEntity> findUser(
            @Param(value = "orgId") Long orgId,
            @Param(value = "showOrgType") String showOrgType,
            @Param(value = "includeRetire") Integer includeRetire,
            @Param(value = "includeAll") Integer includeAll,
            @Param(value = "startTime") Date startTime,
            @Param(value = "endTime") Date endTime,
            @Param(value = "regionId") Long regionId);
}