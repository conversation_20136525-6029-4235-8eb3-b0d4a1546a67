package com.goodsogood.ows.mapper.sas;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.sas.PbmOrgTargetEntity;
import com.goodsogood.ows.model.vo.pbm.PbmOrgTargetBuildVo;
import com.goodsogood.ows.model.vo.pbm.PbmOrgTargetPersonVo;
import com.goodsogood.ows.model.vo.pbm.PbmOrgTargetUnitVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface PbmOrgTargetMapper extends MyMapper<PbmOrgTargetEntity> {

    @Select("<script>" +
            "SELECT\n" +
            "\tL.organization_id orgId,\n" +
            "  L.`name` orgName,\n" +
            "\ttarget_score targetScore, \n" +
            "target_remark targetRemark, \n" +
            "satisfied_score satisfiedScore, \n" +
            "satisfied_remark satisfiedRemark, \n" +
            "examine_score examineScore, \n" +
            "examine_remark examineRemark, \n" +
            "time,pbm_target_org_id pbmTargetOrgId \n" +
            "FROM\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\torganization_id,\n" +
            "\t\t`name` \n" +
            "\tFROM\n" +
            "\t\tt_organization \n" +
            "\tWHERE\n" +
            "\t\tSTATUS = 1 \n" +
            "\t\tAND org_type = 102807 \n" +
            "\t\tAND parent_id = 2 \n" +
            "<if test=\"orgName != null\"> AND name like CONCAT('%',#{orgName},'%')</if>\n" +
            "\tAND organization_id NOT IN ( 5, 1222, 1255, 1294, 2409, 2417, 377, 3404 ) ) AS L\n" +
            "\tLEFT JOIN t_pbm_org_target pot ON L.organization_id = pot.org_id \n" +
            "\tAND pot.time = #{time} \n" +
            "</script>")
    List<PbmOrgTargetEntity> pbmOrgTargetList(@Param("orgName") String orgName, @Param("time") String time);

    @Update("UPDATE t_pbm_org_target\n" +
            "\t\t\t\t\t\tSET examine_remark = NULL\n" +
            "\t\t\t\t\t\tWHERE org_id = #{orgId}\n" +
            "\t\t\t\t\t\tAND time = #{time}")
    void updateExamineRemark(@Param("orgId") Long orgId,@Param("time") String time);

    @Update("UPDATE t_pbm_org_target\n" +
            "\t\t\t\t\t\tSET examine_score = NULL\n" +
            "\t\t\t\t\t\tWHERE org_id = #{orgId}\n" +
            "\t\t\t\t\t\tAND time = #{time}")
    void updateExamineScore(@Param("orgId") Long orgId,@Param("time") String time);

    @Update("UPDATE t_pbm_org_target\n" +
            "\t\t\t\t\t\tSET satisfied_remark = NULL\n" +
            "\t\t\t\t\t\tWHERE org_id = #{orgId}\n" +
            "\t\t\t\t\t\tAND time = #{time}")
    void updateSatisfiedRemark(@Param("orgId") Long orgId,@Param("time") String time);

    @Update("UPDATE t_pbm_org_target\n" +
            "\t\t\t\t\t\tSET satisfied_score = NULL\n" +
            "\t\t\t\t\t\tWHERE org_id = #{orgId}\n" +
            "\t\t\t\t\t\tAND time = #{time}")
    void updateSatisfiedScore(@Param("orgId") Long orgId,@Param("time") String time);

    @Update("UPDATE t_pbm_org_target\n" +
            "\t\t\t\t\t\tSET target_remark = NULL\n" +
            "\t\t\t\t\t\tWHERE org_id = #{orgId}\n" +
            "\t\t\t\t\t\tAND time = #{time}")
    void updateTargetRemark(@Param("orgId") Long orgId,@Param("time") String time);

    @Update("UPDATE t_pbm_org_target\n" +
            "\t\t\t\t\t\tSET target_score = NULL\n" +
            "\t\t\t\t\t\tWHERE org_id = #{orgId}\n" +
            "\t\t\t\t\t\tAND time = #{time}")
    void updateTargetScore(@Param("orgId") Long orgId,@Param("time") String time);


    @Select("<script>" +
            "SELECT\n" +
            "  L.`name` orgName,\n" +
            "\ttarget_score score, \n" +
            "target_remark remark \n" +
            "FROM\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\torganization_id,\n" +
            "\t\t`name` \n" +
            "\tFROM\n" +
            "\t\tt_organization \n" +
            "\tWHERE\n" +
            "\t\tSTATUS = 1 \n" +
            "\t\tAND org_type = 102807 \n" +
            "\t\tAND parent_id = 2 \n" +
            "<if test=\"orgName != null\"> AND name like CONCAT('%',#{orgName},'%')</if>\n" +
            "\tAND organization_id NOT IN ( 5, 1222, 1255, 1294, 2409, 2417, 377, 3404 ) ) AS L\n" +
            "\tLEFT JOIN t_pbm_org_target pot ON L.organization_id = pot.org_id \n" +
            "\tAND pot.time = #{time} \n" +
            "</script>")
    List<PbmOrgTargetUnitVo> getUnitList(@Param("orgName") String orgName, @Param("time") String time);

    @Select("<script>" +
            "SELECT\n" +
            "  L.`name` orgName,\n" +
            "satisfied_score score, \n" +
            "satisfied_remark remark \n" +
            "FROM\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\torganization_id,\n" +
            "\t\t`name` \n" +
            "\tFROM\n" +
            "\t\tt_organization \n" +
            "\tWHERE\n" +
            "\t\tSTATUS = 1 \n" +
            "\t\tAND org_type = 102807 \n" +
            "\t\tAND parent_id = 2 \n" +
            "<if test=\"orgName != null\"> AND name like CONCAT('%',#{orgName},'%')</if>\n" +
            "\tAND organization_id NOT IN ( 5, 1222, 1255, 1294, 2409, 2417, 377, 3404 ) ) AS L\n" +
            "\tLEFT JOIN t_pbm_org_target pot ON L.organization_id = pot.org_id \n" +
            "\tAND pot.time = #{time} \n" +
            "</script>")
    List<PbmOrgTargetPersonVo> getPersonList(@Param("orgName") String orgName, @Param("time") String time);

    @Select("<script>" +
            "SELECT\n" +
            "  L.`name` orgName,\n" +
            "examine_score score, \n" +
            "examine_remark remark \n" +
            "FROM\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\torganization_id,\n" +
            "\t\t`name` \n" +
            "\tFROM\n" +
            "\t\tt_organization \n" +
            "\tWHERE\n" +
            "\t\tSTATUS = 1 \n" +
            "\t\tAND org_type = 102807 \n" +
            "\t\tAND parent_id = 2 \n" +
            "<if test=\"orgName != null\"> AND name like CONCAT('%',#{orgName},'%')</if>\n" +
            "\tAND organization_id NOT IN ( 5, 1222, 1255, 1294, 2409, 2417, 377, 3404 ) ) AS L\n" +
            "\tLEFT JOIN t_pbm_org_target pot ON L.organization_id = pot.org_id \n" +
            "\tAND pot.time = #{time} \n" +
            "</script>")
    List<PbmOrgTargetBuildVo> getBuildList(@Param("orgName") String orgName, @Param("time") String time);
}
