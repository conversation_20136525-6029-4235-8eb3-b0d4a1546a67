package com.goodsogood.ows.mapper.sas;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.tbc.TbcOrgInfoEntity;
import com.goodsogood.ows.model.vo.tbc.BranchFortressIndexVo;
import com.goodsogood.ows.model.vo.tbc.TbcPbmVo;
import com.goodsogood.ows.model.vo.tbc.UserRankVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-12-27 17:19
 **/
@Repository
@Mapper
public interface TbcOrgInfoMapper extends MyMapper<TbcOrgInfoEntity> {


    /**
     * 得支部排名
     * @return
     */
    @Select("SELECT *  FROM\n" +
            "(\n" +
            "SELECT  @row := @row + 1 as row, t.* FROM\n" +
            "( \n" +
            "\t\tSELECT * FROM\n" +
            "\t\t(\n" +
            "\t\t\t\tSELECT GROUP_CONCAT(org_id) as orgIds ,fortress_index as  fortressIndex\n" +
            "\t\t\t\tFROM\n" +
            "\t\t\t\t\t\tt_tbc_org_info \n" +
            "\t\t\t\tWHERE\n" +
            "\t\t\t\t\t\torg_id not in (${excludeOrgIds}) \n" +
            "\t\t\t\t\t\tand org_type_child in(\n" +
            "\t\t\t\t\t\t\t\t10280304,10280309,10280314,10280315,10280319\n" +
            "\t\t\t\t\t\t) \n" +
            "\t\t\t\t\t\tand sta_month= #{staMonth} \n" +
            "\t\t\t\t\tGROUP BY \tfortress_index\n" +
            "\t\t) as L\tORDER BY fortressIndex DESC\n" +
            "\n" +
            ") t ,\n" +
            "(SELECT @row := 0) r )as L2 ")
    List<UserRankVo> allBranchRank(@Param(value = "staMonth") String staMonth,
                                   @Param(value = "excludeOrgIds") String excludeOrgIds);


    /**
     * 得支部排名
     * @return
     */
    @Select(" SELECT rank,fortressIndex,org_name as orgName, party_score as partyIndex,\n" +
            "business_score as businessIndex,innovation_score as innovationIndex FROM\n" +
            "( \n" +
            "\tSELECT CASE \n" +
            " WHEN t.fortressIndex = 0 THEN @rank  "+
            "\t WHEN @last_score = t.fortressIndex\n" +
            "\t\t\tTHEN @rank \n" +
            "\t WHEN @last_score := t.fortressIndex\n" +
            "\t\t\tTHEN @rank := @rank + 1    \n" +
            "\t END AS rank , t.* FROM   \n" +
            "\t\t( \n" +
            "\t\t\tSELECT  * FROM\n" +
            "\t\t\t\t( SELECT\n" +
            "\t\t\t\t\t\tfortress_index as fortressIndex , \n" +
            "\t\t\t\t\t\torg_name, party_score,business_score,innovation_score,fortress_index\n" +
            "\t\t\t\tFROM\n" +
            "\t\t\t\t\t\tt_tbc_org_info \n" +
            "\t\t\t\tWHERE\n" +
            "\t\t\t\t\t\torg_id not in ( ${excludeOrgIds}) \n" +
            "\t\t\t\t\t\tand org_type_child in(10280304,10280309,10280314,10280315,10280319 ) \n" +
            "\t\t\t\t\t\tand sta_month= #{staMonth}  \n" +
            "\t\t\t) as L ORDER BY fortressIndex DESC \n" +
            "\t\t) t ,(SELECT @rank := 0, @last_score := NULL) r\n" +
            ")as L2 where 1=1 limit ${limitSize} ")
    List<BranchFortressIndexVo.BranchFortress> branchRank(@Param(value = "staMonth") String staMonth,
                                                          @Param(value = "excludeOrgIds") String excludeOrgIds,
                                                          @Param(value = "limitSize") Integer limitSize);

    /**
     * 得支部平均值
     * @return
     */
    @Select(" SELECT CAST(AVG(fortress_index) AS DECIMAL(8))  as  fortressIndex\n" +
            "  FROM\n" +
            "  t_tbc_org_info \n" +
            "  WHERE\n" +
            "  org_id not in (${excludeOrgIds}) \n" +
            "  and org_type_child in(\n" +
            "  10280304,10280309,10280314,10280315,10280319\n" +
            "  ) \n" +
            "  and sta_month=  #{staMonth}")
    Integer allBranchAvg(@Param(value = "staMonth") String staMonth,
                                @Param(value = "excludeOrgIds") String excludeOrgIds);


    /**
     * 得到系统平均
     * @return
     */
    @Select(" SELECT  round(AVG(fortress_index),1)  as  fortressIndex\n" +
            "  FROM\n" +
            "  t_tbc_org_info \n" +
            "  WHERE\n" +
            "  org_id not in (${excludeOrgIds}) \n" +
            "  and sta_month=  #{staMonth}")
    Double systemAvg(@Param(value = "staMonth") String staMonth,
                     @Param(value = "excludeOrgIds") String excludeOrgIds);



    /**
     * 得到系统最高
     * @return
     */
    @Select(" SELECT  round(Max(fortress_index),1)  as  fortressIndex\n" +
            "  FROM\n" +
            "  t_tbc_org_info \n" +
            "  WHERE\n" +
            "  org_id not in (${excludeOrgIds}) \n" +
            "  and sta_month=  #{staMonth}")
    Double systemMax(@Param(value = "staMonth") String staMonth,
                     @Param(value = "excludeOrgIds") String excludeOrgIds);

    /**
     * 得组织排名
     * @return
     */
    @Select("SELECT count(1)\n" +
            "FROM\n" +
            "\t\tt_tbc_org_info \n" +
            "WHERE\n" +
            "\t\torg_id not in (${excludeOrgIds}) \n" +
            "\t\tand org_type_child in(\n" +
            "\t\t\t\t10280304,10280309,10280314,10280315,10280319\n" +
            "\t\t) \n" +
            "\t\tand sta_month= #{staMonth} ")
    Integer allBranchRankCount(@Param(value = "staMonth") String staMonth,
                               @Param(value = "excludeOrgIds") String excludeOrgIds);

    @Select("SELECT MIN(log_fortress_index) FROM t_tbc_org_info where " +
            " org_type_child in (${orgTypeChild}) and  DATE_FORMAT(create_time,'%Y%m') = DATE_FORMAT(NOW() ,'%Y%m')" )
    Double selectUserLogMin(String orgTypeChild);

    @Select("SELECT max(log_fortress_index) FROM t_tbc_org_info where org_type_child in (${orgTypeChild}) " +
            " and DATE_FORMAT(create_time,'%Y%m') = DATE_FORMAT(NOW() ,'%Y%m')" )
    Double selectUserLogMax(String orgTypeChild);

    @Update("update t_tbc_org_info set log_fortress_index=#{logFortressIndex}," +
            "party_score=#{partyScore},business_score=#{businessScore},innovation_score=#{innovationScore},update_time=now()" +
            " where tbc_org_info_id=#{tbcOrgInfoId}")
    Integer updateLogFortressIndex(TbcOrgInfoEntity item);


    /**
     * 党业融合统计用户堡垒指数信息
     * @return
     */
    @Select("SELECT AVG(fortress_index) as avgIndex,if(MIN(fortress_index)<0,0,MIN(fortress_index))as minIndex," +
            "MAX(fortress_index) as maxIndex FROM t_tbc_org_info;")
    TbcPbmVo getTbcOrgPbmInfo();


    /**
     * 党业融合统计组织党建指标信息
     * @return
     */
    @Select("SELECT AVG(party_index) as avgIndex,if(MIN(party_index)<0,0,MIN(party_index)) as minIndex," +
            "MAX(party_index) as maxIndex FROM t_tbc_org_info;")
    TbcPbmVo  getTbcOrgPartyIndexPbmInfo();


    /**
     * 党业融合统计组织业务指标信息
     * @return
     */
    @Select("SELECT AVG(business_index) as avgIndex,if(MIN(business_index)<0,0,MIN(business_index)) as minIndex," +
            "MAX(business_index) as maxIndex FROM t_tbc_org_info;")
    TbcPbmVo  getTbcOrgBusinessIndexPbmInfo();


    /**
     * 党业融合统计组织创新指标信息
     * @return
     */
    @Select("SELECT AVG(innovation_index) as avgIndex,if(MIN(innovation_index)<0,0,MIN(innovation_index)) as minIndex," +
            "MAX(innovation_index) as maxIndex FROM t_tbc_org_info;")
    TbcPbmVo  getTbcOrgInnovationIndexPbmInfo();
}