package com.goodsogood.ows.mapper.sas;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.sas.StatisticalPartyFeeEntity;
import com.goodsogood.ows.model.vo.sas.*;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.annotations.Result;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-04-22 16:04
 **/
@Repository
@Mapper
public interface StatisticalPartyFeeMapper extends MyMapper<StatisticalPartyFeeEntity> {

    @Select("<script> " +
            "SELECT " +
            "   L.* " +
            "FROM " +
            "   ( " +
            "   SELECT " +
            "   org_id, " +
            "   org_name, " +
            "   ${payFeeParam} " +
            "   FROM " +
            "   (SELECT org_name, statistical_month, payable_person_num, unpaid_person_num, org_id " +
            "      FROM t_statistical_party_fee " +
            "     WHERE statistical_year = #{statisticalYear} " +
            "       and region_id=#{regionId} and  (org_id = #{orgId} or org_level like CONCAT('%-',#{orgId},'-%'))" +
            "       and org_type_id in (${showOrgType})" +
            "       <if test=\"isRetire !='' and isRetire !=null \"> and  is_retire = #{isRetire}</if>" +
            "  GROUP BY statistical_month, org_id ) AS a " +
            "   RIGHT JOIN t_statistical_date_temp AS b ON a.statistical_month = b.`month`  " +
            "   WHERE " +
            "   a.`statistical_month` IN (${dateStr})  " +
            "   GROUP BY " +
            "   org_id  " +
            "   ) AS L"+
            "</script>")
    @Results({
            @Result(property = "orgName", column = "org_name"),
            @Result(property = "orgId", column = "org_id"),
    })
    List<StatisticalPartyFeeForm> findPayFeeList(SasOrgPayFeeForm entity);




    /**
     * 考核系统拉取统计党费缴纳情况数据
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/5/8 14:17
     */
    @Select("<script> " +
        "SELECT\n" +
        "\torg_id,\n" +
        "\torg_name ,\n" +
        "\tpayable_person_num,\n" +
        "\tunpaid_person_num,\n" +
        "\torg_create_time,\n" +
        "\teval_unpaid_person_num,\n" +
        "\torg_type_id,\n" +
        "\tstatistical_date,\n" +
        "\tstatistical_month,\n" +
        "\tstatistical_year,\n" +
        "\t`status` \n" +
        "FROM\n" +
        "\tt_statistical_party_fee \n" +
        "where 1=1 and region_id=#{regionId} <if test=\"orgId !='' and orgId !=null \"> and org_id=#{orgId}</if> \n" +
        "AND DATE_FORMAT(DATE_SUB(CURDATE(), ${queryCode}),'%Y-%m') <![CDATA[ < ]]> statistical_date \n \n" +
        " </script>")
    @Results({
        @Result(property = "orgId", column = "org_id"),
        @Result(property = "orgName", column = "org_name"),
        @Result(property = "payablePersonNum", column = "payable_person_num"),
        @Result(property = "unpaidPersonNum", column = "unpaid_person_num"),
        @Result(property = "orgCreateTime", column = "org_create_time"),
        @Result(property = "evalUnpaidPersonNum", column = "eval_unpaid_person_num"),
        @Result(property = "statisticalDate", column = "statistical_date"),
        @Result(property = "statisticalYear", column = "statistical_year"),
        @Result(property = "orgTypeId", column = "org_type_id"),
        @Result(property = "statisticalMonth", column = "statistical_month"),
    })
    List<StatisticalPartyFeeForm> findForCondition(@Param(value = "orgId")Long orgId, @Param(value = "queryCode")String queryCode,@Param(value = "regionId")Long regionId);


    /**
     * 电子报表统计
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/6/13 10:01
     */
    @Select("<script>" +
        "SELECT\n" +
        "SUM( s.payable_person_num ) AS payablePersonNum,\n" +
        "SUM( s.unpaid_person_num ) AS unpaidPersonNum ,\n" +
        "convert((SUM( s.payable_person_num )-SUM( s.unpaid_person_num ))/(SUM( s.payable_person_num ))*100,decimal(20,2)) as proportion\n" +
        "\n" +
        "FROM\n" +
        "\tt_statistical_party_fee s \n" +
        "WHERE\n" +
        "\ts.statistical_month  in (#{dateStr}) \n" +
        "\tAND s.statistical_year =  #{statisticalYear} \n" +
        "\tAND ( s.org_id = #{orgId} OR s.org_level LIKE concat( '%-', #{orgId}, '-%' ) ) \n" +
        "<if test=\"showOrgType !='' and showOrgType !=null \"> and  s.org_type_id in (${showOrgType}) </if> " +
        "</script>")
    ElectronicPartyFeeData stasticPartyFee(SasOrgPayFeeForm entity);


    /**
     * 查询组织
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/6/13 10:01
     */
    @Select("<script>" +
            "SELECT s.org_id as orgId,s.org_name as orgName " +
            "FROM\n" +
            "\tt_statistical_party_fee s \n" +
            "WHERE status = 1 \n" +
            "\tAND s.statistical_month  = #{time} \n"
            + "<if test=\"isRetire !=null\">"
            + "\t AND s.is_retire =  #{isRetire} \n"
            + "</if>" +
            "\tAND s.statistical_year =  #{year} \n" +
            "  <if test =\"name != null and name != '' \"> and s.org_name like concat( '%', #{name}, '%' ) </if> " +
            "\tAND ( s.org_id = #{orgId} OR s.org_level LIKE concat( '%-', #{orgId}, '-%' ) ) \n" +
            " GROUP BY org_id " +
            "</script>")
    List<EvalIdForm> findOrgList(EvalIdRequestForm form);

}
