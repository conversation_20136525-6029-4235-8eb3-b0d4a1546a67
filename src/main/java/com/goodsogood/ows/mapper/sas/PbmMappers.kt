package com.goodsogood.ows.mapper.sas

import com.goodsogood.ows.mapper.MyMapper
import com.goodsogood.ows.model.db.sas.PbmFusionItemEntity
import com.goodsogood.ows.model.mongodb.pbm.UserWorkDetailInfo
import com.goodsogood.ows.model.db.sas.WorkItemEntity
import org.apache.ibatis.annotations.Mapper

/**
 *
 * <AUTHOR>
 * @Date 2022-06-21 19:11:54
 * @Description WorkItemMapper
 *
 */
@Mapper
interface WorkItemMapper : MyMapper<WorkItemEntity> {

}

@Mapper
interface UserWorkDetailMapper : MyMapper<UserWorkDetailInfo> {

}

@Mapper
interface FusionItemMapper : MyMapper<PbmFusionItemEntity> {

}