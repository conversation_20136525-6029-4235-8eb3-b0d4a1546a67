package com.goodsogood.ows.mapper;

import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Options;
import tk.mybatis.mapper.provider.SpecialProvider;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-07-23 15:15
 */
public interface StatisticalOrgLifeViewInsertListMapper<T> {
    @Options(useGeneratedKeys = true, keyProperty = "orgLifeViewId")
    @InsertProvider(type = SpecialProvider.class, method = "dynamicSQL")
    int insertList(List<T> recordList);
}
