package com.goodsogood.ows.mapper.ecp

import com.goodsogood.ows.mapper.MyMapper
import com.goodsogood.ows.model.db.ecp.EcpOrgEntity
import com.goodsogood.ows.model.db.ecp.EcpTaskUserInfoEntity
import com.goodsogood.ows.model.mongodb.fusion.EcpActivityDetail
import com.goodsogood.ows.model.mongodb.fusion.EcpJoinDetail
import com.goodsogood.ows.model.vo.ecp.*
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select

@Mapper
interface EcpTaskMapper : MyMapper<EcpTaskUserInfoEntity> {

    /**
     * 烟草党业融合 全市级别:云上任务参与情况
     */
    @Select(
        "SELECT \n" +
                "ifnull(sum(finish_num),0) as peopleNum,\n" +
                "ifnull((select count(1) from t_ecp_task_user_info where is_party = 1 and org_id not in (7,8,2063,2091,2418,2575,2576,2650)),0) as partyNum,\n" +
                "ifnull((select sum(finish_num) from t_ecp_task_user_info where is_party = 1 and org_id not in (7,8,2063,2091,2418,2575,2576,2650)),0) as partyFinishNum,\n" +
                "ifnull((select count(1) from t_ecp_task_user_info where is_party = 0 and org_id not in (7,8,2063,2091,2418,2575,2576,2650)),0) as notPartyNum,\n" +
                "ifnull((select sum(finish_num) from t_ecp_task_user_info where is_party = 0 and org_id not in (7,8,2063,2091,2418,2575,2576,2650)),0) as notPartyFinish\n" +
                "\n" +
                "FROM `t_ecp_task_user_info`\n" +
                "where region_id = #{regionId} and org_id not in (7,8,2063,2091,2418,2575,2576,2650)"
    )
    fun findEcpStatisticsListVO(regionId: Long): EcpStatisticsListVO

    /**
     * 烟草党业融合 本单位级别:云上任务参与情况
     */
    @Select(
        "select \n" +
                "concat(ROUND(count(1) / #{count1} * 100,1),'%')  as staffRate,\n" +
                "concat(ROUND((select count(1) from t_ecp_task_user_info where region_id = #{regionId} and owner_id = #{unitId} and is_party = 1  and org_id not in (7,8,2063,2091,2418,2575,2576,2650))/ #{count2} * 100,1),'%') as partyRate,\n" +
                "ifnull(sum(finish_num),0) as peopleNum,\n" +
                "ifnull((select sum(finish_num) from t_ecp_task_user_info where region_id = #{regionId} and owner_id = #{unitId} and is_party = 1 and org_id not in (7,8,2063,2091,2418,2575,2576,2650)),0) as partyPeopleNum,\n" +
                "ifnull((select sum(finish_num) from t_ecp_task_user_info where region_id = #{regionId} and owner_id = #{unitId} and is_party = 0 and org_id not in (7,8,2063,2091,2418,2575,2576,2650)),0) as notPartyPeopleNum\n" +
                "from t_ecp_task_user_info\n" +
                "where region_id = #{regionId} and owner_id = #{unitId} and org_id not in (7,8,2063,2091,2418,2575,2576,2650) GROUP BY owner_id"
    )
    fun findEcpStatisticsUserVO(regionId: Long, unitId: Long, count1: Int, count2: Int): EcpStatisticsUserVO

    /**
     * 全市系统: 云上任务参与情况列表
     */
    @Select(
        "<script>" +
                "select \n" +
                "t1.owner_id as ownerId,\n" +
                "t1.owner_name as ownerName,\n" +
                "count(1) as staff,\n" +
                "ifnull(t2.num,0) as party,\n" +
                "ifnull(sum(t1.publish_task_num),0) as publishTaskNum,\n" +
                "ifnull( sum(t1.finish_num), 0 ) as peopleNum\n" +
                "from t_ecp_task_user_info t1 left join (\n" +
                "   select " +
                "   owner_id,count(1) num " +
                "   from " +
                "   t_ecp_task_user_info " +
                "   where region_id = #{regionId} " +
                "   and is_party = 1 " +
                "   group by owner_id" +
                ") t2 on t1.owner_id = t2.owner_id\n" +
                "where t1.region_id = #{regionId}  and t1.org_id not in (7,8,2063,2091,2418,2575,2576,2650) group by t1.owner_id \n" +
                "<if test =\"type != null and type == 1 \"> order by count(1) </if>" +
                "<if test =\"type != null and type == 2 \"> order by t2.num </if>" +
                "<if test =\"type != null and type == 3 \"> order by sum(t1.publish_task_num) </if>" +
                "<if test =\"type != null and type == 4 \"> order by sum(t1.finish_num) </if>" +
                "<if test =\"sort != null and sort == -1 \"> desc </if>" +
                "</script>"
    )
    fun findEcpStatisticsCityList(regionId: Long, type: Int?, sort: Int?): MutableList<EcpStatisticsCityVO>

    /**
     * 本单位: 云上任务参与情况人员列表
     */
    @Select(
        "<script>" +
                "select \n" +
                "user_id userId,\n" +
                "user_name userName,\n" +
                "finish_num partInNum,\n" +
                "if(is_party = 0,'否','是') isParty\n" +
                "from t_ecp_task_user_info \n" +
                "where region_id = #{regionId} " +
                "and owner_id = #{unitId} and org_id not in (7,8,2063,2091,2418,2575,2576,2650)" +
                "<if test = \"type != null and type == 0\"> and is_party = 1 </if>" +
                "<if test = \"type != null and type == 1\"> and is_party = 0 </if>" +
                "group by user_id order by finish_num desc" +
                "</script>"
    )
    fun findEcpStatisticsUserList(regionId: Long, unitId: Long, type: Int?): MutableList<EcpStatisticsUserListVO>

    @Select(
        "<script>" +
                "SELECT ta.access_user_id AS userId,count( ta.access_user_id ) AS joinNum \n" +
                "\tFROM `t_task` AS t \n" +
                "\t\tLEFT JOIN t_task_third tt ON t.task_id = tt.task_id \n" +
                "\t\tLEFT JOIN t_task_access ta ON t.task_id = ta.task_id \n" +
                "\t\tLEFT JOIN t_task_log tl ON ta.task_access_id = tl.task_access_id \n" +
                "\tWHERE t.task_class IN ( 16, 27, 26 ) \n" +
                "\t\tAND t.is_del = 0 \n" +
                "\t\tAND ta.operate_status = 8 \n" +
                "\t\tAND ta.access_user_id IS NOT NULL \n" +
                "\t\tAND tl.create_time LIKE concat(#{yearMonth}, '%' ) \n" +
                "<if test=\"userList != null and userList.size > 0\"> " +
                "AND ta.access_user_id IN \n" +
                " <foreach collection=\"userList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
                " #{item}" +
                " </foreach> " +
                "</if>" +
                "GROUP BY ta.access_user_id " +
                "</script>"
    )
    fun findDoneTotal(yearMonth: String, userList: List<Long>?): MutableList<EcpJoinDetail>

    @Select(
        "SELECT\n" +
                "\tcount( 0 ) \n" +
                "FROM\n" +
                "\tt_task \n" +
                "WHERE\n" +
                "\ttask_class = 27  \n" +
                "\tAND is_draft = 0 \n" +
                "\tAND is_del <> 1 \n" +
                "\tAND is_third_type  is null"
    )
    fun totalEcpTask(): Long?
}

@Mapper
interface EcpOrgMapper : MyMapper<EcpOrgEntity> {

    @Select(
        "<script>" +
                "SELECT eot.tag_id AS tagId,\n" +
                "\t COUNT(eo.ecp_org_id) AS total,\n" +
                "\t GROUP_CONCAT(eo.ecp_org_name) as separateUnitName\n" +
                "FROM t_ecp_org AS eo\n" +
                "\tLEFT JOIN t_ecp_org_tag AS eot ON eo.ecp_org_id = eot.ecp_org_id\n" +
                "\tWHERE eo.is_del = 0 \n" +
                "<if test=\"userList != null and userList.size > 0\"> " +
                "AND eo.create_user_id IN \n" +
                " <foreach collection=\"userList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
                " #{item}" +
                " </foreach> " +
                "</if>" +
                "\tAND eo.create_time LIKE CONCAT(#{year},'%') \n" +
                "<if test=\"tagList != null and tagList.size > 0\"> " +
                "\tAND eot.tag_id IN \n" +
                " <foreach collection=\"tagList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
                " #{item}" +
                " </foreach> " +
                "</if>" +
                "\tGROUP BY eot.tag_id" +
                "</script>"
    )
    fun ecpOrgDetail(
        year: Int,
        tagList: List<Long>,
        userList: List<Long>
    ): MutableList<EcpOrgDetailForm>

    @Select(
        "<script>" +
                "SELECT eo.ecp_org_id AS orgId, eo.ecp_org_name AS orgName, COUNT( DISTINCT eot.ecp_task_id ) AS taskNum \n" +
                "FROM t_ecp_org AS eo\n" +
                "LEFT JOIN t_ecp_org_task AS eot ON eo.ecp_org_id = eot.ecp_org_id AND eot.create_time LIKE CONCAT(#{yearMonth},'%') \n" +
                "LEFT JOIN t_ecp_org_tag AS tag ON eo.ecp_org_id = tag.ecp_org_id \n" +
                "WHERE eo.is_del = 0 AND eo.`status` IS NULL  \n" +
                "<if test=\"userList != null and userList.size > 0\"> " +
                "\tAND eo.create_user_id IN \n" +
                " <foreach collection=\"userList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
                " #{item}" +
                " </foreach> \n" +
                "</if>" +
                "<if test=\"ecpTagList != null and ecpTagList.size > 0\"> " +
                "AND tag.tag_id IN \n" +
                " <foreach collection=\"ecpTagList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
                " #{item}" +
                " </foreach> \n" +
                "</if>" +
                "GROUP BY eo.ecp_org_id \n" +
                "</script>"
    )
    fun ecpActivityDetail(
        yearMonth: String,
        userList: List<Long>?,
        ecpTagList: List<Long>?
    ): MutableList<EcpActivityDetail>

    @Select("SELECT  count(ecp_org_id) FROM t_ecp_org WHERE is_del != 1 AND `status` IS NULL")
    fun totalEcpOrg(): Long
}