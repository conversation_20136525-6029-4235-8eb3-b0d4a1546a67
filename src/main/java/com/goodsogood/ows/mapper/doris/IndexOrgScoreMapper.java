package com.goodsogood.ows.mapper.doris;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.doris.IndexOrgScoreEntity;
import com.goodsogood.ows.model.db.doris.IndexUserScoreEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface IndexOrgScoreMapper extends MyMapper<IndexOrgScoreEntity> {
    /**
     * 获取组织当月三指标积分
     * 计算方式：
     * 通过data_month 查询 org_id,parent_score_type 和 sum(score)
     * 分组为 org_id,parent_score_type
     *
     * @param dataMonth 日期-yyyyMM
     * @return list of IndexOrgScoreEntity
     */
    @Select("""
            <script>
            select 
            org_id orgId,
            parent_score_type parentScoreType,
            sum(score) score from 
            <choose>            
                <when test="table == 't_index_org_score_gray'">t_index_org_score_gray</when>
                <otherwise>t_index_org_score</otherwise>
            </choose> 
            where data_month = #{dataMonth} GROUP BY org_id,parent_score_type 
            </script>
            """)
    List<IndexOrgScoreEntity> findOrgScoreByMonth(@Param(value = "table") String table,@Param("dataMonth") String dataMonth);
}
