package com.goodsogood.ows.mapper.doris;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.doris.IndexUserScoreEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface IndexUserScoreMapper extends MyMapper<IndexUserScoreEntity> {
    /**
     * 获取用户当月三指标积分
     * 计算方式：
     * 通过data_month 查询 user_id,parent_score_type 和 sum(score)
     * 分组为 user_id,parent_score_type
     */
    @Select("""
            <script>
            select 
            user_id userId,
            parent_score_type parentScoreType,
            sum(score) score from
            <choose>            
                <when test="table == 't_index_user_score_gray'">t_index_user_score_gray</when>
                <otherwise>t_index_user_score</otherwise>
            </choose>
            where data_month = #{dataMonth} GROUP BY user_id,parent_score_type 
            </script>
            """)
    List<IndexUserScoreEntity> findUsersScoreByMonth(@Param(value = "table") String table, @Param(value = "dataMonth") String dataMonth);

    /**
     * 通过用户id和月份 获取用户当月三指标积分
     */
    @Select("""
            <script>
            select 
            user_id userId,
            parent_score_type parentScoreType,
            sum(score) score from
            <choose>            
                <when test="table == 't_index_user_score_gray'">t_index_user_score_gray</when>
                <otherwise>t_index_user_score</otherwise>
            </choose>
            where data_month = #{dataMonth} and user_id = #{userId} GROUP BY user_id,parent_score_type 
            </script>
            """)
    List<IndexUserScoreEntity> findUserScoreByMonthAndUserId(@Param(value = "table") String table, @Param(value = "dataMonth") String dataMonth, @Param(value = "userId") Long userId);
}
