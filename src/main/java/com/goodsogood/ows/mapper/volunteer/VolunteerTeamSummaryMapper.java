package com.goodsogood.ows.mapper.volunteer;

import com.goodsogood.ows.model.vo.volunteer.TrendVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface VolunteerTeamSummaryMapper {

    /**
     * 志愿团体数
     *
     * @param regionId
     * @param year
     * @param orgId    为null时，查询工委
     * @return
     */
    @Select("<script>" +
            "select count(distinct vt.volunteer_team_id)\n" +
            "from t_volunteer_team vt \n" +
            "inner join t_organization to1 on to1.organization_id = vt.organization_id \n" +
            "and vt.`status`= 1 \n" +
            "and to1.`status`= 1 \n" +
            "and vt.region_id = #{regionId} and to1.region_id = #{regionId} \n" +
            "and year(vt.create_time)<![CDATA[<=]]>#{year}\n" +
            "<if test =\"orgId != null \">" +
            "and (vt.party_org_id=#{orgId} or vt.team_level like concat('-%',(select volunteer_team_id from t_volunteer_team where `status`=1 and party_org_id=#{orgId}),'-%'))\n" +
            "</if>" +
            "</script>")
    Integer getTeamNumber(@Param("regionId") Long regionId,
                          @Param("year") Integer year,
                          @Param("orgId") Long orgId);

    /**
     * 志愿者数量 - 工委
     *
     * @param regionId
     * @param year
     * @return
     */
    @Select("<script>" +
            "select count(distinct vu1.user_id)\n" +
            "from t_volunteer_team vt1 \n" +
            "inner join t_organization to1 on vt1.organization_id=to1.organization_id and vt1.region_id = #{regionId} and vt1.`status`=1 and to1.`status`=1 and year(vt1.create_time) <![CDATA[<=]]> #{year}\n" +
            "inner join t_user_org_corp uoc on to1.organization_id = uoc.organization_id and uoc.region_id = #{regionId}\n" +
            "inner join t_user tu1 on uoc.user_id=tu1.user_id and tu1.`status`=1\n" +
            "inner join t_volunteer_user vu1 on tu1.user_id=vu1.user_id and vu1.`status`=1 and year(vu1.create_time) <![CDATA[<=]]> #{year} " +
            "</script>")
    Integer getVolunteerNumber(@Param("regionId") Long regionId,
                               @Param("year") Integer year);

    /**
     * 志愿者数量 - 党委
     *
     * @param year
     * @param orgId
     * @return
     */
    @Select("<script>" +
            "select count(distinct tu1.user_id)\n" +
            "from t_volunteer_team vt \n" +
            "inner join t_organization to1 on vt.`status`=1 and to1.`status`= 1 and vt.organization_id=to1.organization_id and year(vt.create_time) <![CDATA[<=]]> #{year}\n" +
            "and (vt.party_org_id = #{orgId} or vt.team_level like concat('-%',(select volunteer_team_id from t_volunteer_team where `status`=1 and party_org_id = #{orgId}),'-%'))\n" +
            "inner join t_volunteer_user_team vut on vt.volunteer_team_id = vut.volunteer_team_id\n" +
            "inner join t_volunteer_user vu on vut.volunteer_user_id = vu.volunteer_user_id and vu.`status`= 1 and year(vu.create_time) <![CDATA[<=]]> #{year}\n" +
            "inner join t_user tu1 on vu.user_id = tu1.user_id and tu1.`status`= 1" +
            "</script>")
    Integer getVolunteerNumberByOrg(@Param("year") Integer year,
                                    @Param("orgId") Long orgId);

    /**
     * 开展志愿服务次数 - 工委
     *
     * @param regionId
     * @param year
     * @return
     */
    @Select("<script>" +
            "select count(distinct vp.project_id)\n" +
            "from t_volunteer_project vp \n" +
            "where vp.`status` = 1 " +
            "  and vp.state = 3 " +
            "  and vp.region_id = #{regionId} " +
            "  and YEAR(vp.create_time) = #{year}" +
            "</script>")
    Integer getServiceNumber(@Param("regionId") Long regionId,
                             @Param("year") Integer year);

    /**
     * 开展志愿服务次数 - 党委
     *
     * @param regionId
     * @param year
     * @param orgId
     * @return
     */
    @Select("<script>" +
            "select count(distinct vp.project_id)\n" +
            "  from t_volunteer_project vp \n" +
            " inner join t_volunteer_team vt \n" +
            "    on vp.`status` = 1 \n" +
            "   and vt.`status`= 1 \n" +
            "   and vp.state = 3 \n" +
            "   and vp.region_id = #{regionId} \n" +
            "   and vp.volunteer_team_id = vt.volunteer_team_id \n" +
            "   and year(vp.create_time) = #{year}\n" +
            "   and (vt.party_org_id = #{orgId} or vt.team_level like concat('-%',(select volunteer_team_id from t_volunteer_team where `status` = 1 and party_org_id = #{orgId}),'-%'));\n" +
            "</script>")
    Integer getServiceNumberByOrg(@Param("regionId") Long regionId,
                                  @Param("year") Integer year,
                                  @Param("orgId") Long orgId);


    /**
     * 求助接单 - 工委
     *
     * @param regionId
     * @param year
     * @return
     */
    @Select("<script>" +
            "select count(*)\n" +
            "  from t_volunteer_help vh\n" +
            " where vh.region_id = #{regionId} " +
            "   and vh.state = 3 " +
            "   and YEAR(vh.create_time) = #{year}" +
            "</script>")
    Integer getHelpNumber(@Param("regionId") Long regionId,
                          @Param("year") Integer year);

    /**
     * 求助接单 - 党委
     *
     * @param regionId
     * @param year
     * @param orgId
     * @return
     */
    @Select("<script>" +
            "select count(*)\n" +
            "from t_volunteer_help vh\n" +
            "inner join t_volunteer_team vt \n" +
            "   on vh.region_id = #{regionId} " +
            "  and vh.state = 3 " +
            "  and vt.`status` = 1 " +
            "  and vh.volunteer_team_id = vt.volunteer_team_id " +
            "  and year(vh.create_time) = #{year}\n" +
            "  and (vt.party_org_id = #{orgId} " +
            "       or vt.team_level like concat('-%',(select volunteer_team_id from t_volunteer_team where `status`= 1 and party_org_id = #{orgId}),'-%'))\n" +
            "</script>")
    Integer getHelpNumberByOrg(@Param("regionId") Long regionId,
                               @Param("year") Integer year,
                               @Param("orgId") Long orgId);


    /**
     * 开展志愿服务的团体 - 工委
     *
     * @param regionId
     * @param year
     * @return
     */
    @Select("<script>" +
            "select count(distinct vp.volunteer_team_id)\n" +
            "  from t_volunteer_project vp \n" +
            " where vp.`status` = 1 \n" +
            "   and vp.state = 3 \n" +
            "   and vp.region_id = #{regionId} \n" +
            "   and YEAR(vp.create_time) = #{year}" +
            "</script>")
    Integer getServiceTeamNumber(@Param("regionId") Long regionId,
                                 @Param("year") Integer year);

    /**
     * 开展志愿服务的团体 - 党委
     *
     * @param regionId
     * @param year
     * @param orgId
     * @return
     */
    @Select("<script>" +
            "select count(distinct vp.volunteer_team_id)\n" +
            "from t_volunteer_project vp \n" +
            "inner join t_volunteer_team vt " +
            "        on vp.`status` = 1 " +
            "       and vp.state = 3 " +
            "       and vp.region_id = #{regionId} " +
            "       and vt.`status` = 1 " +
            "       and vp.volunteer_team_id=vt.volunteer_team_id and year(vp.create_time) = #{year}\n" +
            "and (vt.party_org_id = #{orgId} or vt.team_level like concat('-%',(select volunteer_team_id from t_volunteer_team where `status`=1 and party_org_id=#{orgId}),'-%'));\n" +
            "</script>")
    Integer getServiceTeamNumberByOrg(@Param("regionId") Long regionId,
                                      @Param("year") Integer year,
                                      @Param("orgId") Long orgId);


    /**
     * 求助接单的团体 - 工委
     *
     * @param regionId
     * @param year
     * @return
     */
    @Select("<script>" +
            "select count(distinct vh.volunteer_team_id)\n" +
            "from t_volunteer_help vh \n" +
            "where vh.region_id = #{regionId} " +
            "  and vh.state = 3 " +
            "  and YEAR(vh.create_time) = #{year}" +
            "</script>")
    Integer getHelpTeamNumber(@Param("regionId") Long regionId,
                              @Param("year") Integer year);

    /**
     * 求助接单的团体 - 党委
     *
     * @param regionId
     * @param year
     * @param orgId
     * @return
     */
    @Select("<script>" +
            "select count(distinct vh.volunteer_team_id)\n" +
            "  from t_volunteer_help vh \n" +
            " inner join t_volunteer_team vt " +
            "    on vh.region_id = #{regionId} " +
            "   and vh.state = 3 " +
            "   and vt.`status` = 1 " +
            "   and vh.volunteer_team_id = vt.volunteer_team_id " +
            "   and year(vh.create_time) = #{year}\n" +
            "   and (vt.party_org_id = #{orgId} " +
            "        or vt.team_level like concat('-%',(select volunteer_team_id from t_volunteer_team where `status` = 1 and party_org_id = #{orgId}),'-%'));\n" +
            "</script>")
    Integer getHelpTeamNumberByOrg(@Param("regionId") Long regionId,
                                   @Param("year") Integer year,
                                   @Param("orgId") Long orgId);


    /**
     * 志愿服务开展趋势 - 志愿项目 - 工委
     *
     * @param regionId
     * @param year
     * @return
     */
//    @Select("<script>" +
//            "select count(*) num, month(vp.create_time) `month`\n" +
//            "  from t_volunteer_project vp \n" +
//            " where vp.`status` = 1 \n" +
//            "   and vp.state = 3 \n" +
//            "   and vp.region_id = #{regionId}  \n" +
//            "   and YEAR(vp.create_time) = #{year}\n" +
//            "group by month(vp.create_time) order by month(vp.create_time)" +
//            "</script>")

    @Select("select count(*) num,month(vp.create_time) `month`\n" +
            "from t_volunteer_project vp \n" +
            "inner join t_volunteer_user_recruit ur on vp.project_id=ur.project_id and ur.status=1\n" +
            "where  vp.state=3 and vp.region_id=#{regionId}  and YEAR(vp.create_time)=#{year}\n" +
            "group by month(vp.create_time) order by month(vp.create_time);")
    List<TrendVO> getProjectNum(@Param("regionId") Long regionId,
                                @Param("year") Integer year);

    /**
     * 志愿服务开展趋势 - 志愿项目 - 党委
     *
     * @param regionId
     * @param year
     * @return
     */
    @Select("<script>" +
            "select count(*) num, month(vp.create_time) `month`\n" +
            "from t_volunteer_project vp\n" +
            "inner join t_volunteer_team vt \n" +
            "   on vp.`status` = 1 \n" +
            "  and vp.state = 3 \n" +
            "  and vp.region_id = #{regionId}  \n" +
            "  and vt.`status` = 1 \n" +
            "  and vp.volunteer_team_id=vt.volunteer_team_id \n" +
            "  and year(vp.create_time) = #{year}\n" +
            "  and (vt.party_org_id = #{orgId} or vt.team_level like concat('-%',(select volunteer_team_id from t_volunteer_team where `status`=1 and party_org_id = #{orgId}),'-%'))\n" +
            "group by month(vp.create_time) order by month(vp.create_time)" +
            "</script>")
    List<TrendVO> getProjectNumByOrg(@Param("regionId") Long regionId,
                                     @Param("year") Integer year,
                                     @Param("orgId") Long orgId);

    /**
     * 求助接单趋势 - 工委
     *
     * @param regionId
     * @param year
     * @return
     */
//    @Select("<script>" +
//            "select count(distinct vh.volunteer_team_id) num ,month(vh.create_time) `month`\n" +
//            "  from t_volunteer_help vh \n" +
//            " where vh.state = 3 \n" +
//            "   and vh.region_id = #{regionId} \n" +
//            "   and YEAR(vh.create_time) = #{year}\n" +
//            " group by month(vh.create_time) " +
//            " order by (vh.create_time)" +
//            "</script>")
    @Select("select count(ur.recruit_id) num ,month(vh.create_time) `month`\n" +
            "from t_volunteer_help vh \n" +
            "inner join t_volunteer_project_help ph on vh.help_id=ph.help_id\n" +
            "inner join t_volunteer_project vp on ph.project_id=vp.project_id and vp.status=3\n" +
            "inner join t_volunteer_user_recruit ur on vp.project_id=ur.project_id and ur.status=1\n" +
            "where vh.region_id=#{regionId} and vh.state=3  and YEAR(vh.create_time)=#{year}\n" +
            "group by month(vh.create_time) order by (vh.create_time);")
    List<TrendVO> getHelpNum(@Param("regionId") Long regionId,
                             @Param("year") Integer year);

    /**
     * 求助接单趋势 - 党委
     *
     * @param regionId
     * @param year
     * @return
     */
    @Select("<script>" +
            "select count(distinct vh.volunteer_team_id) num ,month(vh.create_time) `month`\n" +
            "  from t_volunteer_help vh \n" +
            " inner join t_volunteer_team vt " +
            "    on vh.state = 3 " +
            "   and vh.region_id = #{regionId} " +
            "   and vt.`status` = 1 " +
            "   and vh.volunteer_team_id=vt.volunteer_team_id " +
            "   and year(vh.create_time) = #{year}\n" +
            "   and (vt.party_org_id = #{orgId} or vt.team_level like concat('-%',(select volunteer_team_id from t_volunteer_team where `status` = 1 and party_org_id = #{orgId}),'-%'))\n" +
            " group by month(vh.create_time) " +
            " order by (vh.create_time)" +
            "</script>")
    List<TrendVO> getHelpNumByOrg(@Param("regionId") Long regionId,
                                  @Param("year") Integer year,
                                  @Param("orgId") Long orgId);

    // ----------------------------------------- 辅助决策-zch start  -----------------------------------------

    /**
     * 开展送服务 - 工委
     *
     * @param regionId  区域id
     * @param startTime 开始时间 yyyy-MM-dd
     * @param endTime   结束时间 yyyy-MM-dd
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " count( 0 ) total \n" +
            "FROM\n" +
            " t_volunteer_reservation \n" +
            "WHERE\n" +
            " is_del = 1 \n" +
            " AND `status` = 2 \n" +
            " AND region_id = #{regionId} \n" +
            " AND exec_time <![CDATA[ >= ]]> '${startTime} 00:00:00' AND exec_time <![CDATA[ <= ]]> '${endTime} 23:59:59' \n" +
            "</script>")
    Integer getReservationNumber(@Param("regionId") Long regionId,
                                 @Param("startTime") String startTime,
                                 @Param("endTime") String endTime);

    /**
     * 开展了送服务的团队 - 工委
     *
     * @param regionId  区域id
     * @param startTime 开始时间 yyyy-MM-dd
     * @param endTime   结束时间 yyyy-MM-dd
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " count( 0 ) total\n" +
            "FROM\n" +
            " (\n" +
            "  SELECT\n" +
            "   b.volunteer_team_id \n" +
            "  FROM\n" +
            "   t_volunteer_reservation a\n" +
            "   LEFT JOIN t_volunteer_service_info b ON a.service_info_id = b.service_info_id \n" +
            "  WHERE\n" +
            "   a.is_del = 1 \n" +
            "   AND a.`status` = 2 \n" +
            "   AND a.region_id = #{regionId} \n" +
            "   AND a.exec_time <![CDATA[ >= ]]> '${startTime} 00:00:00' AND a.exec_time <![CDATA[ <= ]]> '${endTime} 23:59:59' \n" +
            "  GROUP BY\n" +
            "   b.volunteer_team_id \n" +
            " ) c " +
            "</script>")
    Integer getReservationTeamNumber(@Param("regionId") Long regionId,
                                     @Param("startTime") String startTime,
                                     @Param("endTime") String endTime);

    /**
     * 志愿者参与 - 工委
     *
     * @param regionId  区域id
     * @param startTime 开始时间 yyyy-MM-dd
     * @param endTime   结束时间 yyyy-MM-dd
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " count( 0 ) total \n" +
            "FROM\n" +
            " t_volunteer_user_service_flow \n" +
            "WHERE\n" +
            " region_id = #{regionId}\n" +
            " AND create_time <![CDATA[ >= ]]> '${startTime} 00:00:00' AND create_time <![CDATA[ <= ]]> '${endTime} 23:59:59' \n" +
            "</script>")
    Integer getJoinNumber(@Param("regionId") Long regionId,
                          @Param("startTime") String startTime,
                          @Param("endTime") String endTime);

    /**
     * 参与了志愿项目的志愿者 - 工委
     *
     * @param regionId  区域id
     * @param startTime 开始时间 yyyy-MM-dd
     * @param endTime   结束时间 yyyy-MM-dd
     * @return
     */
    @Select("<script>" +
            "SELECT \n" +
            " count(0) total\n" +
            "FROM \n" +
            " (\n" +
            "  SELECT\n" +
            "   volunteer_user_id\n" +
            "  FROM\n" +
            "   t_volunteer_user_service_flow \n" +
            "  WHERE\n" +
            "   region_id = #{regionId}\n" +
            "   AND create_time <![CDATA[ >= ]]> '${startTime} 00:00:00' AND create_time <![CDATA[ <= ]]> '${endTime} 23:59:59' \n" +
            "   GROUP BY volunteer_user_id\n" +
            " ) a " +
            "</script>")
    Integer getJoinUserTeam(@Param("regionId") Long regionId,
                            @Param("startTime") String startTime,
                            @Param("endTime") String endTime);

    /**
     * 志愿服务开展趋势（送服务）- 工委
     *
     * @param regionId  区域id
     * @param startTime 开始时间 yyyy-MM-dd
     * @param endTime   结束时间 yyyy-MM-dd
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " * \n" +
            "FROM\n" +
            " (\n" +
            "  SELECT \n" +
            "   MONTH ( exec_time ) month,\n" +
            "   count( user_id ) num \n" +
            "  FROM\n" +
            "   t_volunteer_reservation \n" +
            "  WHERE\n" +
            "    exec_time <![CDATA[ >= ]]> '${startTime} 00:00:00' AND exec_time <![CDATA[ <= ]]> '${endTime} 23:59:59' \n" +
            "   AND is_del = 1 \n" +
            "   AND `status` = 2 \n" +
            "   AND region_id = #{regionId} \n" +
            "  GROUP BY\n" +
            "   MONTH ( exec_time ),\n" +
            "   user_id \n" +
            " ) a \n" +
            "ORDER BY \n" +
            " a.month " +
            "</script>")
    List<TrendVO> getReservationNum(@Param("regionId") Long regionId,
                                    @Param("startTime") String startTime,
                                    @Param("endTime") String endTime);

    /**
     * 开展送服务 - 党委
     *
     * @param volunteerTeamId 组织对应的志愿团队id
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " count( 0 ) total \n" +
            "FROM\n" +
            " t_volunteer_reservation a\n" +
            " LEFT JOIN t_volunteer_service_info b ON a.service_info_id = b.service_info_id \n" +
            "WHERE\n" +
            " a.is_del = 1 \n" +
            " AND a.`status` = 2 \n" +
            " AND a.exec_time <![CDATA[ >= ]]> '${startTime} 00:00:00' AND a.exec_time <![CDATA[ <= ]]> '${endTime} 23:59:59' \n" +
            " AND b.volunteer_team_id IN (\n" +
            "  SELECT\n" +
            "   volunteer_team_id \n" +
            "  FROM\n" +
            "   t_volunteer_team \n" +
            "  WHERE\n" +
            "   volunteer_team_id = #{volunteerTeamId} \n" +
            "   OR team_level LIKE CONCAT( '%-', #{volunteerTeamId}, '-%' )\n" +
            ") " +
            "</script>")
    Integer getReservationNumberByOrg(@Param("volunteerTeamId") Long volunteerTeamId,
                                      @Param("startTime") String startTime,
                                      @Param("endTime") String endTime);

    /**
     * 开展了送服务的团队 - 党委
     *
     * @param volunteerTeamId 组织对应的志愿团队id
     * @param startTime       开始时间 yyyy-MM-dd
     * @param endTime         结束时间 yyyy-MM-dd
     * @return
     */
    @Select("<script>" +
            "SELECT \n" +
            " count( 0 ) total\n" +
            "FROM \n" +
            " (\n" +
            "  SELECT\n" +
            "   b.volunteer_team_id\n" +
            "  FROM\n" +
            "   t_volunteer_reservation a\n" +
            "   LEFT JOIN t_volunteer_service_info b ON a.service_info_id = b.service_info_id \n" +
            "  WHERE\n" +
            "   a.is_del = 1 \n" +
            "   AND a.`status` = 2 \n" +
            "   AND b.volunteer_team_id IN (\n" +
            "    SELECT\n" +
            "     volunteer_team_id \n" +
            "    FROM\n" +
            "     t_volunteer_team \n" +
            "    WHERE\n" +
            "    volunteer_team_id = #{volunteerTeamId} \n" +
            "    OR team_level LIKE CONCAT( '%-', #{volunteerTeamId}, '-%' )\n" +
            "   )\n" +
            "   AND a.exec_time <![CDATA[ >= ]]> '${startTime} 00:00:00' AND a.exec_time <![CDATA[ <= ]]> '${endTime} 23:59:59' \n" +
            "  GROUP BY\n" +
            "   b.volunteer_team_id\n" +
            " ) c" +
            "</script>")
    Integer getReservationTeamNumberByOrg(@Param("volunteerTeamId") Long volunteerTeamId,
                                          @Param("startTime") String startTime,
                                          @Param("endTime") String endTime);

    /**
     * 志愿者参与 - 党委
     *
     * @param volunteerTeamId 组织对应的志愿团队id
     * @param startTime       开始时间 yyyy-MM-dd
     * @param endTime         结束时间 yyyy-MM-dd
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " count( 0 ) total \n" +
            "FROM\n" +
            " t_volunteer_user_service_flow \n" +
            "WHERE\n" +
            "  volunteer_team_id in(\n" +
            "  SELECT\n" +
            "   volunteer_team_id \n" +
            "  FROM\n" +
            "   t_volunteer_team \n" +
            "  WHERE\n" +
            "   volunteer_team_id = #{volunteerTeamId} \n" +
            "   OR team_level LIKE CONCAT( '%-', #{volunteerTeamId}, '-%' )\n" +
            " )\n" +
            " AND create_time <![CDATA[ >= ]]> '${startTime} 00:00:00' AND create_time <![CDATA[ <= ]]> '${endTime} 23:59:59' \n" +
            "</script>")
    Integer getJoinNumberByOrg(@Param("volunteerTeamId") Long volunteerTeamId,
                               @Param("startTime") String startTime,
                               @Param("endTime") String endTime);

    /**
     * 参与了志愿项目的志愿者 - 党委
     *
     * @param volunteerTeamId 组织对应的志愿团队id
     * @param startTime       开始时间 yyyy-MM-dd
     * @param endTime         结束时间 yyyy-MM-dd
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " count( 0 ) total \n" +
            "FROM\n" +
            " (\n" +
            "  SELECT\n" +
            "   volunteer_user_id \n" +
            "  FROM\n" +
            "   t_volunteer_user_service_flow \n" +
            "  WHERE\n" +
            "    volunteer_team_id in(\n" +
            "   SELECT\n" +
            "    volunteer_team_id \n" +
            "   FROM\n" +
            "    t_volunteer_team \n" +
            "   WHERE\n" +
            "   volunteer_team_id = #{volunteerTeamId} \n" +
            "   OR team_level LIKE CONCAT( '%-', #{volunteerTeamId}, '-%' )\n" +
            "  )\n" +
            " AND create_time <![CDATA[ >= ]]> '${startTime} 00:00:00' AND create_time <![CDATA[ <= ]]> '${endTime} 23:59:59' \n" +
            "  GROUP BY\n" +
            "   volunteer_user_id \n" +
            " ) a" +
            "</script>")
    Integer getJoinUserTeamByOrg(@Param("volunteerTeamId") Long volunteerTeamId,
                                 @Param("startTime") String startTime,
                                 @Param("endTime") String endTime);

    /**
     * 志愿服务开展趋势（送服务）- 党委
     *
     * @param volunteerTeamId
     * @param startTime
     * @param endTime
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " * \n" +
            "FROM\n" +
            " (\n" +
            "  SELECT\n" +
            "   MONTH ( a.exec_time ) month,\n" +
            "   count( a.user_id ) num \n" +
            "  FROM\n" +
            "   t_volunteer_reservation a\n" +
            "   LEFT JOIN t_volunteer_service_info b ON a.service_info_id = b.service_info_id \n" +
            "  WHERE\n" +
            "   a.exec_time <![CDATA[ >= ]]> '${startTime} 00:00:00' AND a.exec_time <![CDATA[ <= ]]> '${endTime} 23:59:59' \n" +
            "   AND a.is_del = 1 \n" +
            "   AND a.`status` = 2 \n" +
            "   AND b.volunteer_team_id in (\n" +
            "    SELECT\n" +
            "     volunteer_team_id \n" +
            "    FROM\n" +
            "     t_volunteer_team \n" +
            "    WHERE\n" +
            "    volunteer_team_id = #{volunteerTeamId} \n" +
            "    OR team_level LIKE CONCAT( '%-', #{volunteerTeamId}, '-%' )\n" +
            "   )\n" +
            "  GROUP BY\n" +
            "   MONTH ( a.exec_time ),\n" +
            "   a.user_id\n" +
            " ) a \n" +
            "ORDER BY \n" +
            " a.month " +
            "</script>")
    List<TrendVO> getReservationNumByOrg(@Param("volunteerTeamId") Long volunteerTeamId,
                                         @Param("startTime") String startTime,
                                         @Param("endTime") String endTime);

    /**
     * 根据党委组织id获取团队id
     *
     * @param orgId
     * @return
     */
    @Select("SELECT volunteer_team_id FROM t_volunteer_team WHERE party_org_id = #{orgId}")
    Long getVolunteerTeamId(Long orgId);

    // ----------------------------------------- 辅助决策-zch end -----------------------------------------
}
