package com.goodsogood.ows.mapper.volunteer;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.volunteer.VolunteerProjectEntity;
import com.goodsogood.ows.model.vo.rank.OrgJoinTotalVO;
import com.goodsogood.ows.model.vo.rank.UserJoinTotalVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/17
 * Description:
 */
@Repository
@Mapper
public interface VolunteerProjectMapper extends MyMapper<VolunteerProjectEntity> {

    /**
     * 获取某个组织及其下级组织在某月的参加志愿活动人数
     */
    String JOIN_VOLUNTEER_PROJECT_USER_TOTAL_SQL_TEMPLATE = "SELECT \n" +
            "count(0) joinTotal, \n" +
            "${orgId} AS orgId, \n" +
            "DATE_FORMAT(a.create_time,'%Y-%m') dateMonth \n" +
            "FROM(\n" +
            "SELECT\n" +
            "f.volunteer_user_id,\n" +
            "f.create_time\n" +
            "FROM\n" +
            "t_volunteer_user_service_flow f\n" +
            "LEFT JOIN t_volunteer_user_recruit u ON u.volunteer_user_id = f.volunteer_user_id AND u.project_id = f.project_id\n" +
            "WHERE\n" +
            " u.user_id IN (${userId})" +
            " AND f.create_time  >=  '${startTime} 00:00:00' \n" +
            " AND f.create_time  <= '${endTime} 23:59:59' \n" +
            "AND u.`status` = 1\n" +
            "GROUP BY\n" +
            "f.volunteer_user_id\n" +
            ") a";

    /**
     * 获取某个组织及其下级组织在某年每个月的参加志愿活动人数
     *
     * @param sql
     * @return
     */
    @Select("<script>" +
            "${sql}" +
            "</script>")
    List<OrgJoinTotalVO> joinVolunteerProjectUserTotal(@Param("sql") String sql);

    /**
     * 用户在某月参加过志愿活动次数
     *
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " count( 0 ) joinTotal, \n" +
            " u.user_id userId \n" +
            "FROM\n" +
            " t_volunteer_user_service_flow f\n" +
            " LEFT JOIN t_volunteer_user_recruit u ON u.volunteer_user_id = f.volunteer_user_id AND u.project_id = f.project_id \n" +
            "WHERE\n" +
            " u.user_id IN (${userId}) \n" +
            " AND f.create_time <![CDATA[ >= ]]> '${startTime} 00:00:00' \n" +
            " AND f.create_time <![CDATA[ <= ]]> '${endTime} 23:59:59'" +
            " AND f.region_id = #{regionId} " +
            " GROUP BY u.user_id " +
            "</script>")
    List<UserJoinTotalVO> joinVolunteerProjectTotal(@Param("userId") String userId,
                                                    @Param("startTime") String startTime,
                                                    @Param("endTime") String endTime,
                                                    @Param("regionId") Long regionId);
}
