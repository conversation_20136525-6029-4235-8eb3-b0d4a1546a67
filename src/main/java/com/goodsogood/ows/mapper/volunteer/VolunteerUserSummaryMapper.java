package com.goodsogood.ows.mapper.volunteer;

import com.goodsogood.ows.model.db.volunteer.AppraiseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface VolunteerUserSummaryMapper {

    // ----------------------------------------- 辅助决策-zch start -----------------------------------------

    /**
     * 根据人员查询参与次数
     *
     * @param volunteerUserId 志愿者id
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            "  count(0) serviceNumber, \n" +
            " volunteer_user_id volunteerUserId \n" +
            "FROM\n" +
            "  t_volunteer_user_service_flow \n" +
            "WHERE\n" +
            "  volunteer_user_id IN (${volunteerUserId}) \n" +
            "  AND create_time <![CDATA[ >= ]]> '${startTime} 00:00:00' AND create_time <![CDATA[ <= ]]> '${endTime} 23:59:59'\n" +
            " GROUP BY volunteer_user_id "+
            "</script>")
    List<AppraiseInfo> getTotalByUser(@Param("volunteerUserId") String volunteerUserId,
                           @Param("startTime") String startTime,
                           @Param("endTime") String endTime);

    /**
     * 根据人员和时间查询服务时长
     *
     * @param userId    用户id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " a.user_id userId," +
            " ( a.`hour` * 60 + a.`minute` ) time \n" +
            "FROM\n" +
            " (\n" +
            " SELECT\n" +
            "  r.user_id,\n" +
            "  sum( t.`hour` ) `hour`,\n" +
            "  sum( t.`minute` ) `minute` \n" +
            " FROM\n" +
            "  t_volunteer_user_time t\n" +
            "  LEFT JOIN t_volunteer_user_recruit r ON t.recruit_id = r.recruit_id\n" +
            " WHERE\n" +
            "  t.create_time <![CDATA[ >= ]]> '${startTime} 00:00:00' \n" +
            "  AND t.create_time <![CDATA[ <= ]]> '${endTime} 23:59:59' \n" +
            "  AND r.user_id IN (${userId})\n" +
            " ) a" +
            "</script>")
    List<AppraiseInfo> getTimeByUser(@Param("userId") String userId,
                                     @Param("startTime") String startTime,
                                     @Param("endTime") String endTime);

    /**
     * 根据人员和时间查询志愿者积分
     *
     * @param userId    用户id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " user_id userId, \n" +
            " sum( score ) score \n" +
            "FROM\n" +
            " t_volunteer_user_score \n" +
            "WHERE\n" +
            " settlement_time <![CDATA[ >= ]]> '${startTime} 00:00:00' AND settlement_time <![CDATA[ <= ]]> '${endTime} 23:59:59' \n" +
            " AND user_id IN (${userId}) \n" +
            " AND `status` = 1 " +
            " GROUP BY user_id " +
            "</script>")
    List<AppraiseInfo> getScoreByUserId(@Param("userId") String userId,
                                        @Param("startTime") String startTime,
                                        @Param("endTime") String endTime);

    /**
     * 根据人员和时间查询评分评价
     * 守时程度、服务态度、专业水平
     *
     * @param userId    用户id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    @Select("<script>" +
            "SELECT\n" +
            " ru.user_id userId, " +
            " FORMAT( avg( au.punctuality_index ), 1 ) punctuality,\n" +
            " FORMAT( avg( attitude_index ), 1 ) attitude,\n" +
            " FORMAT( avg( professional_index ), 1 ) professional " +
            "FROM\n" +
            " t_volunteer_appraised_user au\n" +
            " LEFT JOIN t_volunteer_user_recruit ru ON au.recruit_id = ru.recruit_id \n" +
            "WHERE\n" +
            " ru.user_id IN (${userId})\n" +
            " AND au.create_time <![CDATA[ >= ]]> '${startTime} 00:00:00' AND au.create_time <![CDATA[ <= ]]> '${endTime} 23:59:59' " +
            " GROUP BY ru.user_id " +
            "</script>")
    List<AppraiseInfo> getAppraiseByUserId(@Param("userId") String userId,
                                           @Param("startTime") String startTime,
                                           @Param("endTime") String endTime);

    // ----------------------------------------- 辅助决策-zch end -----------------------------------------
}
