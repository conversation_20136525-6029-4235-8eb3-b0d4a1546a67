package com.goodsogood.ows.mapper.ppmd;

import com.goodsogood.ows.model.db.ppmd.PayLogEntity;
import com.goodsogood.ows.model.vo.ppmd.PpmdUserInfo;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 党费查询Mapper
 * <AUTHOR>
 * @date 2019/11/19
 * @return
 */
@Repository
@Mapper
public interface OrgPayMapper {

    /**
     * 组织党费应交人数
     * @param orgId
     * @param startTime
     * @return
     */
    @Select("<script>" +
            "SELECT " +
            "x " +
            "FROM ( " +
            "select *,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo, " +
            "@user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b " +
            "where DATE_FORMAT(start_time,'%Y-%m') &lt;= #{startTime} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{startTime} " +
            "and org_id = #{orgId} " +
            "ORDER BY user_id,party_member_id desc " +
            ") t1 where type != 3 and t1.rowNo = 1" +
            "</script>")
    List<Long> getOrgPayAbleUser(@Param("orgId") Long orgId, @Param("startTime") String startTime);

    /**
     * 查询已缴纳党费人员列表
     * <AUTHOR>
     * @date 2019/11/19
     * @param userList
     * @param startTime
     * @return
     */
    @Select("<script>select pay_log_id, pay_info_id, org_id, org_name, office_id, office_name, user_id," +
            " user_name, pay_method, pay_cardinal_number, pay_ratio, ratio_type, pay_log_type, pay_already," +
            " pay_type, pay_for, pay_date, pay_user_id, pay_user_name, reason_id, reason, org_level, create_time" +
            " from t_ppmd_pay_log where user_id in " +
            "<foreach collection=\"userList\" item=\"userId\" open=\"(\" separator=\",\" close=\")\">" +
            "#{userId}" +
            "</foreach>" +
            " and (DATE_FORMAT(pay_date,'%Y-%m') = #{startTime}" +
            " or (pay_date is null and pay_for = #{startTime}))" +
            "</script>")
    @Results({
            @Result(column = "pay_log_id", property = "payLogId"),
            @Result(column = "pay_info_id", property = "payInfoId"),
            @Result(column = "org_id", property = "orgId"),
            @Result(column = "org_name", property = "orgName"),
            @Result(column = "office_id", property = "officeId"),
            @Result(column = "office_name", property = "officeName"),
            @Result(column = "user_id", property = "userId"),
            @Result(column = "user_name", property = "userName"),
            @Result(column = "pay_method", property = "payMethod"),
            @Result(column = "pay_cardinal_number", property = "payCardinalNumber"),
            @Result(column = "pay_ratio", property = "payRatio"),
            @Result(column = "ratio_type", property = "ratioType"),
            @Result(column = "pay_log_type", property = "payLogType"),
            @Result(column = "pay_already", property = "payAlready"),
            @Result(column = "pay_type", property = "payType"),
            @Result(column = "pay_for", property = "payFor"),
            @Result(column = "pay_date", property = "payDate"),
            @Result(column = "pay_user_id", property = "payUserId"),
            @Result(column = "pay_user_name", property = "payUserName"),
            @Result(column = "reason", property = "reason"),
            @Result(column = "reason_id", property = "reasonId"),
            @Result(column = "create_time", property = "createTime"),
            @Result(column = "org_level", property = "orgLevel"),
            @Result(column = "create_time", property = "createTime"),

    })
    List<PayLogEntity> getOrgPay(@Param("userList")List<Long> userList, @Param("startTime") String startTime);

    /**
     * 查询党费人员列表
     * <AUTHOR>
     * @date 2019/11/19
     * @param orgId
     * @param startTime
     * @return
     */
    @Select("<script>select pay_log_id, pay_info_id, org_id, org_name, office_id, office_name, user_id," +
            " user_name, pay_method, pay_cardinal_number, pay_ratio, ratio_type, pay_log_type, pay_already," +
            " pay_type, pay_for, pay_date, pay_user_id, pay_user_name, reason_id, reason, org_level, create_time" +
            " from t_ppmd_pay_log where org_id = #{orgId} " +
            " and (DATE_FORMAT(pay_date,'%Y-%m') = #{startTime}" +
            " or (pay_date is null and pay_for = #{startTime}))" +
            "</script>")
    @Results({
            @Result(column = "pay_log_id", property = "payLogId"),
            @Result(column = "pay_info_id", property = "payInfoId"),
            @Result(column = "org_id", property = "orgId"),
            @Result(column = "org_name", property = "orgName"),
            @Result(column = "office_id", property = "officeId"),
            @Result(column = "office_name", property = "officeName"),
            @Result(column = "user_id", property = "userId"),
            @Result(column = "user_name", property = "userName"),
            @Result(column = "pay_method", property = "payMethod"),
            @Result(column = "pay_cardinal_number", property = "payCardinalNumber"),
            @Result(column = "pay_ratio", property = "payRatio"),
            @Result(column = "ratio_type", property = "ratioType"),
            @Result(column = "pay_log_type", property = "payLogType"),
            @Result(column = "pay_already", property = "payAlready"),
            @Result(column = "pay_type", property = "payType"),
            @Result(column = "pay_for", property = "payFor"),
            @Result(column = "pay_date", property = "payDate"),
            @Result(column = "pay_user_id", property = "payUserId"),
            @Result(column = "pay_user_name", property = "payUserName"),
            @Result(column = "reason", property = "reason"),
            @Result(column = "reason_id", property = "reasonId"),
            @Result(column = "create_time", property = "createTime"),
            @Result(column = "org_level", property = "orgLevel"),
            @Result(column = "create_time", property = "createTime"),

    })
    List<PayLogEntity> getOrgPayByOrgId(@Param("orgId")Long orgId, @Param("startTime") String startTime);

    /**
     * 根据时间查询党费人员列表
     * <AUTHOR>
     * @date 2019/11/19
     * @param startTime
     * @return
     */
    @Select("<script>select pay_log_id, pay_info_id, org_id, org_name, office_id, office_name, user_id," +
            " user_name, pay_method, pay_cardinal_number, pay_ratio, ratio_type, pay_log_type, pay_already," +
            " pay_type, pay_for, pay_date, pay_user_id, pay_user_name, reason_id, reason, org_level, create_time" +
            " from t_ppmd_pay_log where (DATE_FORMAT(pay_date,'%Y-%m') = #{startTime}" +
            " or (pay_date is null and pay_for = #{startTime}))" +
            "</script>")
    @Results({
            @Result(column = "pay_log_id", property = "payLogId"),
            @Result(column = "pay_info_id", property = "payInfoId"),
            @Result(column = "org_id", property = "orgId"),
            @Result(column = "org_name", property = "orgName"),
            @Result(column = "office_id", property = "officeId"),
            @Result(column = "office_name", property = "officeName"),
            @Result(column = "user_id", property = "userId"),
            @Result(column = "user_name", property = "userName"),
            @Result(column = "pay_method", property = "payMethod"),
            @Result(column = "pay_cardinal_number", property = "payCardinalNumber"),
            @Result(column = "pay_ratio", property = "payRatio"),
            @Result(column = "ratio_type", property = "ratioType"),
            @Result(column = "pay_log_type", property = "payLogType"),
            @Result(column = "pay_already", property = "payAlready"),
            @Result(column = "pay_type", property = "payType"),
            @Result(column = "pay_for", property = "payFor"),
            @Result(column = "pay_date", property = "payDate"),
            @Result(column = "pay_user_id", property = "payUserId"),
            @Result(column = "pay_user_name", property = "payUserName"),
            @Result(column = "reason", property = "reason"),
            @Result(column = "reason_id", property = "reasonId"),
            @Result(column = "create_time", property = "createTime"),
            @Result(column = "org_level", property = "orgLevel"),
            @Result(column = "create_time", property = "createTime"),

    })
    List<PayLogEntity> getOrgPayByDate(@Param("startTime") String startTime);

    @Select("<script>select DISTINCT org_id from t_ppmd_pay_log where 1 = 1 " +
            "<if test =\"orgId != 3\"> " +
            " AND (org_id = #{orgId} or office_id = #{orgId} or org_level like concat( '%-', #{orgId}, '-%' )) " +
            "</if>" +
            " AND DATE_FORMAT(pay_date,'%Y-%m') = #{time}</script>")
    /**
     * 根据查询时间查询所有需要交纳的组织ID
     */
    List<Long> findOrgIdByPartyId(@Param(value = "time") String time, @Param(value = "orgId") Long orgId);

    @Select("<script>" +
            "select DISTINCT user_id from t_ppmd_pay_log where pay_log_type in (1,3) and DATE_FORMAT(pay_date,'%Y-%m') = #{time}" +
            "</script>")
    List<Long> selectUserByDate(@Param("time")String time);

    @Select("<script>select count(0) from t_ppmd_pay_log where user_id in " +
            "<foreach collection=\"userList\" item=\"userId\" open=\"(\" separator=\",\" close=\")\">" +
            "#{userId}" +
            "</foreach>" +
            " and pay_for = #{startTime} and pay_log_type in (1,3) " +
            "</script>")
    Integer getOrgPayNum(@Param("userList")List<Long> userList, @Param("startTime") String startTime);

    @Select("<script>" +
            "SELECT " +
            "   count(0) " +
            "FROM " +
            "   ( " +
            "   SELECT " +
            "   tab1.orgId, " +
            "   tab1.orgName, " +
            "   tab1.userId, " +
            "   tab1.userName, " +
            "   tab1.type, " +
            "   CASE " +
            "   WHEN tab2.payLogType = 4  " +
            "   OR tab2.payLogType = 5  " +
            "   OR tab2.payLogType = 6 THEN " +
            "   4  " +
            "   WHEN tab2.payLogType = 3 THEN " +
            "   3  " +
            "   WHEN tab2.payLogType = 1 THEN " +
            "   1 ELSE 2  " +
            "   END STATUS, " +
            "   tab4.inMonth, " +
            "   CASE " +
            "   WHEN tab4.inMonth = #{startTime} " +
            "   THEN 1 ELSE 0  " +
            "   END isCurrentMonth " +
            "   FROM " +
            "   ( " +
            "   SELECT " +
            "   org_id orgId, " +
            "   org_name orgName, " +
            "   user_id userId, " +
            "   user_name userName, " +
            "   type " +
            "   FROM " +
            "   ( " +
            "   SELECT " +
            "   *, " +
            "   IF " +
            "   ( @user_id = user_id, @rowNum := @rowNum + 1, @rowNum := 1 ) AS rowNo, " +
            "   @user_id := user_id x  " +
            "   FROM " +
            "   t_ppmd_party_member, " +
            "   ( SELECT @rowNum := 0, @user_id := NULL ) b  " +
            "   WHERE " +
            "   DATE_FORMAT( start_time, '%Y-%m' ) <![CDATA[ <= ]]>  #{startTime} " +
            "   AND IFNULL( DATE_FORMAT( end_time, '%Y-%m' ), '9999-99' ) <![CDATA[ > ]]> #{startTime}  " +
            "   AND org_id IN (#{orgId})  " +
            "   ORDER BY " +
            "   user_id, " +
            "   create_time DESC, " +
            "   is_import ASC  " +
            "   ) t1  " +
            "   WHERE " +
            "   t1.rowNo = 1  " +
            "   AND t1.type != 3  " +
            "   ) tab1 " +
            "   LEFT JOIN ( " +
            "   SELECT " +
            "   orgId, " +
            "   orgName, " +
            "   userId, " +
            "   userName, " +
            "   payLogType  " +
            "   FROM " +
            "   ( " +
            "   SELECT " +
            "   org_id orgId, " +
            "   org_name orgName, " +
            "   user_id userId, " +
            "   user_name userName, " +
            "   pay_log_type payLogType, " +
            "IF " +
            "   ( @user_id_two = user_id, @rowNum_two := @rowNum_two + 1, @rowNum_two := 1 ) AS rowNo, " +
            "   @user_id_two := user_id x  " +
            "FROM " +
            "   t_ppmd_pay_log, " +
            "   ( SELECT @rowNum_two := 0, @user_id_two := NULL ) b  " +
            "WHERE " +
            "   ( pay_type != 3 OR pay_type IS NULL )  " +
            "   AND pay_for = #{startTime}  " +
            "ORDER BY " +
            "   user_id, " +
            "   pay_log_type  " +
            "   ) t2  " +
            "WHERE " +
            "   t2.rowNo = 1  " +
            "   ) tab2 ON tab1.userId = tab2.userId " +
            "   LEFT JOIN ( " +
            "   SELECT " +
            "   user_id userId, " +
            "   DATE_FORMAT( min( create_time ), '%Y-%m' ) inMonth  " +
            "   FROM " +
            "   t_ppmd_party_member  " +
            "   WHERE " +
            "   org_id IN (#{orgId})  " +
            "   GROUP BY " +
            "   user_id " +
            "   ) tab4 ON tab1.userId = tab4.userId " +
            "   ) tmp  " +
            "WHERE " +
            "   STATUS IN (2,4) " +
            "AND isCurrentMonth = 0" +
            "</script>")
    Integer getEvalUnNum(@Param("orgId") Long orgId, @Param("startTime") String startTime);

    @Select("<script>"+
            "select sum(case when cn2 <![CDATA[ < ]]> cn then 1 else 0 end) '未交齐党委数量' from (\n" +
            "SELECT officeId,officeName,count(1) cn,sum(case when payDate is not null then 1 else 0 end) cn2 from (\n" +
            "SELECT tp1.userId,tp1.userName,tp3.officeId,tp3.officeName,tp1.orgId,tp1.orgName,tp2.payDate from (\n" +
            "SELECT user_id userId,user_name userName,office_id officeId,office_name officeName,org_id orgId,org_name orgName\n" +
            "             FROM ( \n" +
            "             select *,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo,\n" +
            "             @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b\n" +
            "             where region_id = #{regionId} and DATE_FORMAT(start_time,'%Y-%m') <![CDATA[ <= ]]> #{queryDate} \n" +
            "\t\t\t\t\t\t and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') <![CDATA[ > ]]> #{queryDate} \n" +
            "             ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4)\n" +
            ") tp1 LEFT JOIN (\n" +
            "SELECT user_id userId,user_name userName,office_id officeId,office_name officeName,\n" +
            "        org_id orgId,org_name orgName,pay_date payDate\n" +
            " from t_ppmd_pay_log where  region_id = #{regionId} and pay_for =#{queryDate} and pay_log_type =1\n" +
            ") tp2 on tp1.userId=tp2.userId LEFT JOIN (\n" +
            "select t1.orgId,t1.orgName,t1.officeId,t2.name officeName from (\n" +
            "select organization_id orgId,name orgName,\n" +
            "IF(SUBSTRING_INDEX(SUBSTRING_INDEX(org_level,'-',5),'-',-1) <![CDATA[ <> ]]>''," +
            "SUBSTRING_INDEX(SUBSTRING_INDEX(org_level,'-',5),'-',-1),organization_id) \n" +
            "officeId from t_organization where region_id = #{regionId} and  `status`=1 and parent_id <![CDATA[<>]]> 2\n" +
            ") t1 LEFT JOIN t_organization t2 on t1.officeId=t2.organization_id    \n" +
            ") tp3 on tp1.orgId = tp3.orgId\n" +
            ") tmp GROUP BY officeId\n" +
            ") ttt"+
            "</script>")
    Integer getOwnOrgCount(@Param("queryDate")  String queryDate,@Param("regionId")  Long regionId);
}
