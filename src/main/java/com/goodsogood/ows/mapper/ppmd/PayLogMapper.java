package com.goodsogood.ows.mapper.ppmd;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.ppmd.PayLogEntity;
import com.goodsogood.ows.model.vo.ppmd.OrgNumVo;
import com.goodsogood.ows.model.vo.ppmd.OrgPayVo;
import com.goodsogood.ows.model.vo.sas.ActiveUserVo;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 党费日志记录
 * <AUTHOR>
 * @date 2019/11/20
 * @return
 */
@Mapper
@Repository
public interface PayLogMapper extends MyMapper<PayLogEntity> {

    @Select("<script>SELECT org_id, org_name, COUNT(DISTINCT user_id) AS num FROM " +
            " ( SELECT * FROM ( SELECT DISTINCT * FROM t_ppmd_party_member ORDER BY create_time DESC ) A " +
            " WHERE start_time &lt;= #{time} AND #{time} &lt; IFNULL( end_time, \"2030-01-01 00:00:00\" ) " +
            " GROUP BY user_id ORDER BY create_time DESC ) B " +
            " WHERE 1=1" +
            "<if test=\"orgIdList != null and orgIdList.size != 0\"> " +
            " AND org_id IN " +
            "<foreach item=\"orgIdList\" collection=\"orgIdList\" open=\"(\" separator=\",\" close=\")\">#{orgIdList}</foreach> " +
            "</if>" +
            " GROUP BY org_id</script>")
    @Results({
            @Result(column = "org_id", property = "orgId"),
            @Result(column = "org_name", property = "orgName"),
            @Result(column = "num", property = "num")
    })
    /**
     * 根据orgIdList查询组织的人数
     * @param time
     * @param orgIdList
     * @return
     */
    List<OrgNumVo> findOrgNumByOrg(@Param(value = "time") String time, @Param(value = "orgIdList") List<Long> orgIdList);

    @Select("<script>SELECT pay_date,(SELECT COUNT(DISTINCT user_id) FROM t_ppmd_pay_log WHERE pay_log_type = 1 AND pay_for = #{time} AND user_id in <foreach item=\"userId\" collection=\"userIds\" open=\"(\" separator=\",\" close=\")\">#{userId}</foreach>) AS num FROM t_ppmd_pay_log WHERE pay_log_type = 1 AND pay_for = #{time} AND user_id in <foreach item=\"userId\" collection=\"userIds\" open=\"(\" separator=\",\" close=\")\">#{userId}</foreach> ORDER BY pay_date DESC LIMIT 1</script>")
    @Results({
            @Result(column = "pay_date", property = "payDate"),
            @Result(column = "num", property = "num")
    })
    /**
     * 查询缴纳人数和最后缴纳时间
     * @param time
     * @param userIdList
     * @return
     */
    OrgPayVo findFinalTimeByUserList(@Param(value = "time") String time,
                                     @Param(value = "userIds") List<Long> userIdList);

    @Select("<script>SELECT user_id, user_name, org_name, pay_date FROM `t_ppmd_pay_log` WHERE pay_already &gt; 0 AND pay_for = #{time} AND pay_log_type = 1 AND pay_type = 1 AND user_id IN <foreach item=\"users\" collection=\"users\" open=\"(\" separator=\",\" close=\")\">#{users}</foreach> ORDER BY pay_date LIMIT 1</script>")
    @Results({
            @Result(column = "user_id", property = "userId"),
            @Result(column = "user_name", property = "userName"),
            @Result(column = "org_name", property = "orgName"),
            @Result(column = "pay_date", property = "payDate")
    })
    /**
     * 根据userIds查询最积极缴费的用户
     */
    ActiveUserVo findActiveUserByOrg(@Param(value = "time") String time, @Param(value = "users") Set<Long> users);

    @Select("select pay_date from t_ppmd_pay_log where pay_log_type = 1 and region_id=#{regionId} and user_id = #{userId} and pay_for = #{payFor} ")
    /**
     * 根据userId和交纳月份查询交费日期
     */
    Date findPayDate(@Param(value = "regionId") Long regionId,@Param(value = "userId") Long userId,@Param(value = "payFor") String payFor);

    @Select("<script> SELECT IFNULL(sum(case when payDate is not null then 1 else 0 end)/count(1)*100, 0) rate from (" +
            " SELECT tp1.userId,tp2.payDate from (SELECT user_id userId FROM ( " +
            " select user_id,type,org_id,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b" +
            " where region_id =#{regionId} and DATE_FORMAT(start_time,'%Y-%m') &lt;= #{payFor} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{payFor}" +
            " ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4) and org_id in" +
            " (SELECT organization_id FROM t_organization WHERE owner_id = #{unitId} <if test=\"testBranch != null and testBranch != ''\"> and organization_id not in (${testBranch}) </if> AND `status` = 1)" +
            " ) tp1 LEFT JOIN (SELECT user_id userId,pay_date payDate from t_ppmd_pay_log where region_id =#{regionId}" +
            " and pay_log_type =1 and pay_for =#{payFor} and org_id in (SELECT organization_id FROM t_organization WHERE owner_id = #{unitId}" +
            " <if test=\"testBranch != null and testBranch != ''\"> and organization_id not in (${testBranch}) </if> AND `status` = 1)" +
            " ) tp2 on tp1.userId=tp2.userId) tmp </script>")
    /**
     * 单位组织党费交纳率
     */
    Double findOrgPayRate(@Param(value = "regionId") Long regionId,@Param(value = "unitId") Long unitId,
                         @Param(value = "payFor") String payFor,@Param(value = "testBranch") String testBranch);


    @Select("""
            <script>
            select count(*)nPay
            from (
                select * from ( select user_id,type,org_id orgId,create_time,
                                if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo,@user_id:=user_id
                                x from t_ppmd_party_member,(select @rowNum :=0,@user_id:=null) b
                                where region_id=19 and DATE_FORMAT(start_time,'%Y-%m') <![CDATA[ <= ]]>  #{payFor}
                                and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') <![CDATA[ > ]]> #{payFor}
                                and create_time <![CDATA[ >= ]]> start_time -- 这个条件
                                order by user_id,create_time desc
                                ) t1
             where t1.rowNo = 1 and DATE_FORMAT(create_time,'%Y-%m') <![CDATA[ <= ]]>  #{payFor} )pp
             left join t_ppmd_pay_log l on pp.user_id=l.user_id and l.pay_for=#{payFor}  where pay_log_type in (4,5,6)
             and orgId=#{orgId}
             GROUP BY pp.orgId
            </script>
            """)
    Integer getOwePayNum(@Param(value = "orgId") Long orgId,
                         @Param(value = "payFor") String payFor);

}
