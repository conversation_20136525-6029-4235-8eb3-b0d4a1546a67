package com.goodsogood.ows.mapper.ppmd;

import com.goodsogood.ows.model.vo.tbc.OfficeDuesForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 烟草大屏党费情况mapper
 * <AUTHOR> huangbinyang
 * @date : 2020.12.29
 */
@Repository
@Mapper
public interface TbcPpmdMapper {

    @Select("select IFNULL(Round(sum(pay_already)/100,2),0) paidYear from t_ppmd_pay_log where office_id =#{oid} and pay_log_type in (1,3) and SUBSTR(pay_for,1,4) = #{year};")
    /**
     * 获取年度交纳金额
     * @param oid
     * @param year
     */
    Double getPayYear(@Param("oid")Long oid, @Param("year")String year);

    @Select("select IFNULL(Round(sum(pay_already)/100,2),0) paidMonth,count(1) numberMonth from t_ppmd_pay_log where office_id =#{oid} and pay_log_type in (1,3) and pay_for= #{month};")
    /**
     * 获取本月已交纳党费金额和人数
     * @param oid
     * @param month
     * @return
     */
    Map<String, Double> getMonthPayAndNumber(@Param("oid")Long oid, @Param("month")String month);

    @Select("select sum(payshould) unpaidMonth from (\n" +
            "SELECT tp1.userId,tp1.officeId,tp1.orgId branchId,IF(type=2\n" +
            "or type=5,ceil(tp1.revisedPartyFee/100*1*10.0)/10.0,ceil(tp1.cardinalNumber/100*convert(tp3.proportion,decimal(10,2))/100*10.0)/10.0) \n" +
            "payShould,IFNULL(Round(tp2.payAlready/100,2),0) payAlready from (SELECT user_id userId,office_id officeId,org_id orgId,\n" +
            "type,cardinal_number cardinalNumber,revised_party_fee revisedPartyFee,ratio_type ratioType\n" +
            "             FROM ( \n" +
            "             select *,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo,\n" +
            "             @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b\n" +
            "             where DATE_FORMAT(start_time,'%Y-%m') <= #{month} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') >  #{month}\n" +
            "             ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4) and office_id = #{oid} \n" +
            ") tp1 LEFT JOIN (\n" +
            "SELECT user_id userId,office_id officeId,org_id orgId,pay_already payAlready\n" +
            " from t_ppmd_pay_log where pay_for = #{month} and pay_log_type =1\n" +
            ") tp2 on tp1.userId=tp2.userId LEFT JOIN (select IF(upper_limit is null,t1.lower_limit+0.001,t1.lower_limit) \n" +
            "lowerLimit, IFNULL(upper_limit,9999999999) upperLimit,proportion,ratio_type ratioType from \n" +
            "t_ppmd_pay_ratio t1 INNER JOIN ((SELECT uuid from t_ppmd_pay_ratio where DATE_FORMAT(effective_time,'%Y-%m') \n" +
            "<= DATE_FORMAT(now(),'%Y-%m') and ratio_type=1 GROUP BY uuid ORDER BY create_time desc limit 1) union all (SELECT \n" +
            "uuid from t_ppmd_pay_ratio where DATE_FORMAT(effective_time,'%Y-%m') <= DATE_FORMAT(now(),'%Y-%m') and ratio_type=2 \n" +
            "GROUP BY uuid ORDER BY create_time desc limit 1) ) t2 on t1.uuid = t2.uuid ORDER BY ratioType,lowerLimit) \n" +
            "tp3 on tp1.cardinalNumber >= tp3.lowerLimit and tp1.cardinalNumber <=tp3.upperLimit and \n" +
            "tp1.ratioType = tp3.ratioType\n" +
            ") r where payAlready =0")
    /**
     * 本月未交纳金额
     * @param oid
     * @param month
     * @return
     */
    Double getMonthUnpaid(@Param("oid")Long oid, @Param("month")String month);

    @Select("select IFNULL(TRUNCATE(sum(isPay)/count(1)*100,2),0) r from (\n" +
            "SELECT tp1.officeId,tp1.userId,IF(tp2.payDate is null,0,1) isPay from (\n" +
            "SELECT office_id officeId,user_id userId FROM (\n" +
            "select office_id,user_id,type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo,\n" +
            "@user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b\n" +
            "where region_id=#{rid} and DATE_FORMAT(start_time,'%Y-%m') <= #{month} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') > #{month}\n" +
            "ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4) and office_id = #{oid}\n" +
            ") tp1 LEFT JOIN (\n" +
            "SELECT user_id userId,pay_date payDate from t_ppmd_pay_log where region_id=#{rid} and pay_for =#{month} and pay_log_type in (1,3)\n" +
            ") tp2 on tp1.userId=tp2.userId) temp;")
    /**
     * 获取本月已党费交纳百分比
     * @param oid
     * @param month
     * @return
     */
    Double getPercent(@Param("oid")Long oid,@Param("month")String month,@Param("rid")Long rid);

    @Select("select IFNULL(Round(sum(pay_already)/100,2),0) paidDay,\n" +
            "IFNULL(sum(case when pay_type = 1 then 1 else 0 end),0) self,\n" +
            "IFNULL(sum(case when pay_type = 2 then 1 else 0 end),0) other\n" +
            "from t_ppmd_pay_log where office_id =#{oid} and pay_log_type in (1,3)\n" +
            "and pay_for= #{month} and DATE_FORMAT(pay_date,'%d') = DATE_FORMAT(now(),'%d');")
    /**
     * 获取今日交费情况（今日已交党费，自己交纳人数，他人代缴人数）
     * @param oid
     * @param month
     * @return
     */
    OfficeDuesForm.StatusForm getDayStatus(@Param("oid")Long oid, @Param("month")String month);

    @Select("select user_name name,org_name of from t_ppmd_pay_log\n" +
            "where office_id =#{oid} and pay_log_type in (1,3) and pay_for= #{month} and pay_type= #{type} and DATE_FORMAT(pay_date,'%d') = DATE_FORMAT(now(),'%d')")
    /**
     * 今日交费交互
     * @param oid
     * @param month
     * @param type
     * @return
     */
    List<OfficeDuesForm.Details> getDetails(@Param("oid")Long oid, @Param("month")String month, @Param("type")Integer type);

    @Select("select DATE_FORMAT(pay_date,'%e') day,IFNULL(Round(sum(pay_already)/100,2),0) money\n" +
            "from t_ppmd_pay_log where office_id =#{oid} and pay_for= #{month} GROUP BY DATE_FORMAT(pay_date,'%e');")
    /**
     * 查询月份党费交纳趋势
     * @param oid
     * @param month
     * @return
     */
    List<OfficeDuesForm.Node> getTrend(@Param("oid")Long oid, @Param("month")String month);

    @Select("select * from  (\n" +
            "select ttmp1.orgId branchId,ttmp2.short_name shortName,payAlready paid,payOwing unpaid,IF(cn<=cn2,1,0) isFinish,IF(cn<=cn2,DATE_FORMAT(ttmp1.lastPayDate,'%m月%d号交齐'),null) date from (\n" +
            "SELECT orgId,orgName,sum(payAlready) payAlready,sum(case when payAlready !=0 then 0 else payShould end) payOwing,max(payDate) lastPayDate,count(1) cn,sum(case when payDate is not null then 1 else 0 end) cn2 from (\n" +
            "SELECT tp1.userId,tp1.userName,tp1.officeId,tp1.officeName,tp1.orgId,tp1.orgName, IF(type=2 \n" +
            "or type=5,ceil(tp1.revisedPartyFee/100*1*10.0)/10.0,ceil(tp1.cardinalNumber/100*convert(tp3.proportion,decimal(10,2))/100*10.0)/10.0) \n" +
            "payShould,IFNULL(Round(tp2.payAlready/100,2),0) payAlready,tp2.payDate from (SELECT user_id userId,user_name userName,office_id officeId,office_name officeName,org_id orgId,org_name orgName,\n" +
            "type,cardinal_number cardinalNumber,revised_party_fee revisedPartyFee,ratio_type ratioType\n" +
            "             FROM ( \n" +
            "             select *,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo,\n" +
            "             @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b\n" +
            "             where DATE_FORMAT(start_time,'%Y-%m') <= #{month} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') > #{month}\n" +
            "             ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4) and office_id = #{oid} \n" +
            ") tp1 LEFT JOIN (\n" +
            "SELECT user_id userId,user_name userName,office_id officeId,office_name officeName,\n" +
            "        org_id orgId,org_name orgName,pay_already payAlready,pay_date payDate\n" +
            " from t_ppmd_pay_log where pay_for =#{month} and pay_log_type =1\n" +
            ") tp2 on tp1.userId=tp2.userId LEFT JOIN (select IF(upper_limit is null,t1.lower_limit+0.001,t1.lower_limit) \n" +
            "lowerLimit, IFNULL(upper_limit,9999999999) upperLimit,proportion,ratio_type ratioType from \n" +
            "t_ppmd_pay_ratio t1 INNER JOIN ((SELECT uuid from t_ppmd_pay_ratio where DATE_FORMAT(effective_time,'%Y-%m') \n" +
            "<= DATE_FORMAT(now(),'%Y-%m') and ratio_type=1 GROUP BY uuid ORDER BY create_time desc limit 1) union all (SELECT \n" +
            "uuid from t_ppmd_pay_ratio where DATE_FORMAT(effective_time,'%Y-%m') <= DATE_FORMAT(now(),'%Y-%m') and ratio_type=2 \n" +
            "GROUP BY uuid ORDER BY create_time desc limit 1) ) t2 on t1.uuid = t2.uuid ORDER BY ratioType,lowerLimit) \n" +
            "tp3 on tp1.cardinalNumber >= tp3.lowerLimit and tp1.cardinalNumber <=tp3.upperLimit and \n" +
            "tp1.ratioType = tp3.ratioType\n" +
            ") tmp GROUP BY orgId ORDER BY lastPayDate\n" +
            ") ttmp1 LEFT JOIN t_organization ttmp2 on ttmp1.orgId = ttmp2.organization_id\n" +
            ") r ORDER BY branchId;")
    /**
     * 支部交费情况
     * @param oid
     * @param month
     * @return
     */
    List<OfficeDuesForm.Paid> getBranchList(@Param("oid")Long oid,@Param("month")String month);

    @Select("select username name,orgName of from (\n" +
            "SELECT tp1.userId,tp1.userName,tp1.officeId,tp1.officeName,tp1.orgId branchId,tp1.orgName, IF(type=2\n" +
            "or type=5,ceil(tp1.revisedPartyFee/100*1*10.0)/10.0,ceil(tp1.cardinalNumber/100*convert(tp3.proportion,decimal(10,2))/100*10.0)/10.0) \n" +
            "payShould,IFNULL(Round(tp2.payAlready/100,2),0) payAlready,tp2.payDate from (SELECT user_id userId,user_name userName,office_id officeId,office_name officeName,org_id orgId,org_name orgName,\n" +
            "type,cardinal_number cardinalNumber,revised_party_fee revisedPartyFee,ratio_type ratioType\n" +
            "             FROM ( \n" +
            "             select *,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo,\n" +
            "             @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b\n" +
            "             where DATE_FORMAT(start_time,'%Y-%m') <= #{month} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') > #{month}\n" +
            "             ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4) and office_id = #{oid} \n" +
            ") tp1 LEFT JOIN (\n" +
            "SELECT user_id userId,user_name userName,office_id officeId,office_name officeName,\n" +
            "        org_id orgId,org_name orgName,pay_already payAlready,pay_date payDate\n" +
            " from t_ppmd_pay_log where pay_for =#{month} and pay_log_type =1\n" +
            ") tp2 on tp1.userId=tp2.userId LEFT JOIN (select IF(upper_limit is null,t1.lower_limit+0.001,t1.lower_limit) \n" +
            "lowerLimit, IFNULL(upper_limit,9999999999) upperLimit,proportion,ratio_type ratioType from \n" +
            "t_ppmd_pay_ratio t1 INNER JOIN ((SELECT uuid from t_ppmd_pay_ratio where DATE_FORMAT(effective_time,'%Y-%m') \n" +
            "<= DATE_FORMAT(now(),'%Y-%m') and ratio_type=1 GROUP BY uuid ORDER BY create_time desc limit 1) union all (SELECT \n" +
            "uuid from t_ppmd_pay_ratio where DATE_FORMAT(effective_time,'%Y-%m') <= DATE_FORMAT(now(),'%Y-%m') and ratio_type=2 \n" +
            "GROUP BY uuid ORDER BY create_time desc limit 1) ) t2 on t1.uuid = t2.uuid ORDER BY ratioType,lowerLimit) \n" +
            "tp3 on tp1.cardinalNumber >= tp3.lowerLimit and tp1.cardinalNumber <=tp3.upperLimit and \n" +
            "tp1.ratioType = tp3.ratioType\n" +
            ") r where payAlready =0 and branchId=#{bid}")
    /**
     * 支部下未交费党员列表
     * @param bid
     * @param month
     * @return
     */
    List<OfficeDuesForm.Details> getUnpaidMember(@Param("oid")Long oid,@Param("bid")Long bid, @Param("month")String month);

    @Select("select ttmp1.user_name name,ttmp3.short_name of,ttmp2.pay_date time from (\n" +
            "SELECT user_id,user_name,org_id,org_name\n" +
            "FROM ( \n" +
            "select user_id,user_name,office_id,org_id,org_name,type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo,\n" +
            "@user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b\n" +
            "where DATE_FORMAT(start_time,'%Y-%m') <= #{month} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') > #{month}\n" +
            "ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4) and office_id = #{oid}\n" +
            ") ttmp1 LEFT JOIN (\n" +
            "select user_id,DATE_FORMAT(pay_date,'%m月%d号 %H:%i:%s') pay_date from t_ppmd_pay_log where pay_for =#{month}\n" +
            ") ttmp2 on ttmp1.user_id = ttmp2.user_id LEFT JOIN t_organization ttmp3 on ttmp1.org_id = ttmp3.organization_id where pay_date is not null ORDER BY pay_date;")
    /**
     * 党员交费排名
     * @param oid
     * @param month
     * @return
     */
    List<OfficeDuesForm.Details> getRankList(@Param("oid")Long oid, @Param("month")String month);
}
