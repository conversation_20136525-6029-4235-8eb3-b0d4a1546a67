package com.goodsogood.ows.mapper.ppmd;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.ppmd.StatisticsInfoEntity;
import com.goodsogood.ows.model.vo.dataCockpit.PpmdStsVo;
import com.goodsogood.ows.model.vo.overview.PpmdStaDetailsVo;
import com.goodsogood.ows.model.vo.overview.PpmdStaResultVo;
import com.goodsogood.ows.model.vo.overview.PpmdStaVo;
import com.goodsogood.ows.model.vo.ppmd.*;
import com.goodsogood.ows.model.vo.sas.PayDetailsAppVo;
import com.goodsogood.ows.model.vo.sas.PayDetailsForm;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
@Repository
public interface PpmdStatisticsMapper extends MyMapper<StatisticsInfoEntity> {

    @Select("<script> SELECT " +
            " parent_org_id parentOrgId,org_id id,org_name name,examine,stats_date statsDate," +
            " num_total numTotal,num_should numShould,pay_should payShould,num_already numAlready,pay_already payAlready," +
            " num_owing numOwing,pay_owing payOwing,org_type orgType FROM t_ppmd_statistics_info WHERE 1=1 " +
            " <if test=\" redisKey != null and redisKey !='' \"> and redis_key = #{redisKey}</if> " +
            " <if test=\" orgId != null \"> and org_id = #{orgId}</if> " +
            " <if test=\" statsDate != null and statsDate !='' \"> and stats_date = #{statsDate}</if> </script>")
    /**
     * 根据条件获取历史统计数据
     * @param redisKey     缓存Key
     * @param orgId       组织编号
     * @param statsDate  统计月份
     * @return
     */
    List<StatsRedisVo> findStatisticsInfo(@Param(value = "redisKey") String redisKey, @Param(value = "orgId") Long orgId, @Param(value = "statsDate") String statsDate);

    @Select("<script> SELECT " +
            " user_id userId,user_name userName,office_id officeId,office_name officeName,org_id orgId,org_name orgName," +
            " cert_number_secret certNumberSecret,cardinal_number cardinalNumber,type,revised_party_fee revisedPartyFee,reason_id reasonId,reason,ratio_type ratioType" +
            " FROM ( " +
            " select *,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b" +
            " where DATE_FORMAT(start_time,'%Y-%m') &lt;= #{statsDate} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{statsDate} " +
            " ORDER BY user_id,create_time desc " +
            " ) t1 where t1.rowNo = 1 </script>")
    /**
     * 根据统计日期，获取当时生效的交纳标准
     */
    List<StatsPartyMemberVo> findStatsPartyMember(@Param(value = "statsDate") String statsDate);

    @Select("<script> SELECT " +
            " user_id userId,user_name userName,office_id officeId,office_name officeName,org_id orgId,org_name orgName," +
            " cert_number_secret certNumberSecret,cardinal_number cardinalNumber,type,revised_party_fee revisedPartyFee,reason_id reasonId,reason,ratio_type ratioType" +
            " FROM ( " +
            " select *,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b" +
            " where DATE_FORMAT(start_time,'%Y-%m') &lt;= #{statsDate} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{statsDate} " +
            " ORDER BY user_id,create_time desc " +
            " ) t1 where t1.rowNo = 1 " +
            "  <choose> <when test=\" mode == 'office' \">" +
            "  and t1.office_id in <foreach item=\"rid\" collection=\"refreshIds\" open=\"(\" separator=\",\" close=\")\">#{rid}</foreach></when>" +
            "  <when test=\" mode == 'org' \">" +
            "  and t1.org_id in <foreach item=\"rid\" collection=\"refreshIds\" open=\"(\" separator=\",\" close=\")\">#{rid}</foreach></when>" +
            "  <otherwise></otherwise></choose></script>")
    /**
     * 根据指定的2级维度组织编号和3级维度组织编号，获取这些党员生效的交纳标准
     * tc 2019.01.24 v1.0.3.1
     */
    List<StatsPartyMemberVo> findStatsPartyMemberByIdList(@Param(value = "statsDate") String statsDate,
                                                          @Param(value = "mode") String mode,
                                                          @Param(value = "refreshIds") List<Long> refreshIds);

    @Select("<script> SELECT " +
            " t1.lower_limit lowerLimit,upper_limit upperLimit,proportion,ratio_type ratioType " +
            " FROM " +
            " t_ppmd_pay_ratio t1 INNER JOIN " +
            " ((SELECT uuid from t_ppmd_pay_ratio where DATE_FORMAT(effective_time,'%Y-%m') &lt;= #{statsDate} and ratio_type=1 " +
            " GROUP BY uuid ORDER BY create_time desc limit 1)" +
            " union all (SELECT uuid from t_ppmd_pay_ratio where DATE_FORMAT(effective_time,'%Y-%m') &lt;= #{statsDate} and ratio_type=2 " +
            "  GROUP BY uuid ORDER BY create_time desc limit 1)" +
            " ) t2 on t1.uuid = t2.uuid ORDER BY ratio_type,lowerLimit </script>")
    /**
     * 根据统计日期，获取生效的交纳比例
     */
    List<StatsPayRatioVo> findStatsPayRatio(@Param(value = "statsDate") String statsDate);

    @Select("<script> SELECT " +
            " orgId,orgName,officeId,officeName,userId,userName,payMethod,ratioType,payAlready,payLogType,payType " +
            " FROM (SELECT org_id orgId,org_name orgName,office_id officeId,office_name officeName,user_id userId,user_name userName," +
            " pay_method payMethod,ratio_type ratioType,CASE WHEN pay_already IS NOT NULL AND ( pay_log_type = 1 OR pay_log_type = 3 )  " +
            " THEN pay_already WHEN ( pay_log_type = 4 OR pay_log_type = 5 OR pay_log_type = 6 ) THEN ABS( pay_already ) ELSE NULL " +
            " END payAlready,pay_log_type payLogType,pay_type payType,IF( @user_id_two = user_id, @rowNum_two := @rowNum_two + 1, @rowNum_two := 1 ) AS rowNo, " +
            " @user_id_two := user_id x FROM t_ppmd_pay_log, ( SELECT @rowNum_two := 0, @user_id_two := NULL ) b WHERE ( pay_type != 3 OR pay_type IS NULL ) " +
            " AND pay_for = #{statsDate}  AND pay_log_type !=2 ORDER BY user_id, pay_log_type) t2 WHERE t2.rowNo = 1 </script>")
    /**
     * 根据统计日期，获取已经交纳记录
     *
     *   修改 tc 2019.01.14
     *  如果获取的是历史月份的记录，如果有欠交的记录，应交金额使用记录的欠交金额，避免中途修改了金额进位规则后带来的差异
     */
    List<StatsPayLogVo> findStatsPayLog(@Param(value = "statsDate") String statsDate);

    @Delete(
            "<script> DELETE " +
                    " from t_ppmd_statistics_info where redis_key = #{redisKey} and org_id in " +
                    " <foreach item=\"oid\" collection=\"refreshIds\" open=\"(\" separator=\",\" close=\")\">#{oid}</foreach>" +
                    " </script>")
    /**
     * 根据redisKey,统计类型和编号删除统计记录
     * @param redisKey
     */
    void delStatisticsInfoByRefreshIds(@Param(value = "redisKey") String redisKey, @Param(value = "refreshIds") List<Long> refreshIds);

    @Delete(
            "DELETE from t_ppmd_statistics_info where redis_key = #{redisKey}"
    )
    /**
     * 根据redisKey删除统计记录
     * @param redisKey
     */
    void delStatisticsInfoByRedisKey(@Param(value = "redisKey") String redisKey);

    @Select("<script> select * from (select tab1.orgId,tab1.orgName,tab1.userId,tab1.userName,tab3.lastPayDate,type from " +
            " (select org_id orgId,org_name orgName,user_id userId,user_name userName,type from (" +
            " select *,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo,@user_id:=user_id x" +
            " from t_ppmd_party_member,(select @rowNum :=0,@user_id:=null) b where " +
            " DATE_FORMAT(start_time,'%Y-%m') &lt;= #{form.queryDate} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{form.queryDate}" +
            " and org_id in <foreach item=\"oid\" collection=\"orgIdList\" open=\"(\" separator=\",\" close=\")\">#{oid}</foreach> " +
            " order by user_id,create_time desc,is_import asc) t1 where t1.rowNo = 1 and t1.type!=3) tab1 " +
            " LEFT JOIN (SELECT org_id orgId,user_id userId,max( pay_for ) lastPayDate FROM t_ppmd_pay_log WHERE" +
            " (pay_type != 3 OR pay_type IS NULL) AND (pay_log_type = 1 OR pay_log_type = 3) GROUP BY user_id" +
            " ) tab3 on tab1.userId = tab3.userId) tmp ORDER BY orgId,userId </script>")
    /**
     * 根据查询条件，获取党支部个人党费交纳情况(移动端)
     *  修改 非当月查询记录表获取历史记录 2019.01.07
     *  @param form
     *  @param orgIdList
     * @return
     */
    List<PayDetailsAppVo> findBranchPayDetailsAppHistory(@Param(value = "form") PayDetailsForm form,
                                                         @Param(value = "orgIdList") List<Long> orgIdList);

    @Select("<script> select userId,payAlready,payLogType from (select user_id userId,case " +
            " when pay_already is not null and (pay_log_type=1 or pay_log_type=3) then pay_already/100 when  " +
            " (pay_log_type=4 or pay_log_type=5 or pay_log_type=6) then ABS(pay_already)/100 else null end " +
            " payAlready,pay_log_type payLogType, if(@user_id_two=user_id,@rowNum_two:=@rowNum_two+1,@rowNum_two:=1) " +
            " as rowNo,@user_id_two:=user_id x from t_ppmd_pay_log,(select @rowNum_two :=0,@user_id_two:=null) " +
            " b where (pay_type!=3 or pay_type is null) and pay_for = #{form.queryDate}" +
            " <if test=\" userIdList != null and userIdList.size() !=0 \"> and user_id in " +
            " <foreach item=\"uid\" collection=\"userIdList\" open=\"(\" separator=\",\" close=\")\">#{uid}</foreach> </if>" +
            " order by user_id,pay_log_type) t2 where t2.rowNo = 1 </script>")
    /**
     * 根据查询条件，获取党支部个人党费交纳记录(移动端)
     * @param userIdList  用户编号集合
     * @return
     */
    List<PayDetailsLogAppVo> findBranchPayDetailsAppPayLog(@Param(value = "form") PayDetailsForm form, @Param(value = "userIdList") List<Long> userIdList);


    @Select("""
            select
                sum(case when payLogType=1 then isPay else 0 end) payNum,
                sum(case when payLogType=1 then 1 else 0 end) totalNum,
                IFNULL(Round(sum(payAlready)/100,2),0) totalAmount
            from
                ( SELECT
                    tp1.userId,
                    IF(tp2.payDate is null,
                    0,
                    1) isPay,
                    payAlready,
                    IFNULL(payLogType,1) payLogType
                from
                    ( SELECT
                        user_id userId
                    FROM
                        ( select
                            org_id,
                            user_id,
                            type,
                            if(@user_id=user_id,
                            @rowNum:=@rowNum+1,
                            @rowNum:=1) as rowNo,
                            @user_id:=user_id x
                        from
                            t_ppmd_party_member,
                            (Select
                                @rowNum :=0,
                                @user_id:=null) b
                        where
                            DATE_FORMAT(start_time,'%Y-%m') <= DATE_FORMAT(#{yearMonth},'%Y-%m')
                            and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') > DATE_FORMAT(#{yearMonth},'%Y-%m')
                        ORDER BY
                            user_id,
                            create_time desc ) t1
                        where
                            t1.rowNo = 1
                            and t1.type not in (
                                3,4
                            )
                            and (
                                org_id=#{orgId}
                                or org_id in (
                                    select
                                        organization_id
                                    from
                                        t_organization
                                    where
                                        org_level LIKE CONCAT('%-',#{orgId} ,'-%')
                                )
                            )
                        ) tp1
                    LEFT JOIN
                        (
                            SELECT
                                user_id userId,
                                pay_date payDate,
                                pay_already payAlready,
                                pay_log_type payLogType
                            from
                                t_ppmd_pay_log
                            where
                                DATE_FORMAT(pay_date,'%Y-%m') = DATE_FORMAT(#{yearMonth},'%Y-%m')
                                and pay_log_type in (
                                    1,3
                                )
                        ) tp2
                            on tp1.userId=tp2.userId
                        ) temp
            """)
    PpmdStaVo getOverviewPpmdSta(@Param(value = "orgId") Long orgId,@Param(value = "yearMonth") String yearMonth);

    @Select(" <script> select * FROM (SELECT tp1.userId,tp1.userName,tp1.orgId,tp1.orgName,IF(tp2.payDate is null,0,1) isPay from" +
            " (SELECT user_id userId,user_name userName,org_id orgId,org_name orgName FROM ( select org_id,org_name,user_id,user_name,type," +
            " if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo,@user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b" +
            " where DATE_FORMAT(start_time,'%Y-%m') &lt;= DATE_FORMAT(now(),'%Y-%m') and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; DATE_FORMAT(now(),'%Y-%m')" +
            " /*keep orderby*/ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4) and (org_id=#{orgId} or org_id in (select organization_id from" +
            " t_organization where org_level LIKE CONCAT('%-',#{orgId},'-%')))) tp1 LEFT JOIN (SELECT user_id userId,pay_date payDate,pay_already payAlready,pay_log_type payLogType" +
            " from t_ppmd_pay_log where DATE_FORMAT(pay_date,'%Y-%m') = DATE_FORMAT(now(),'%Y-%m') and pay_log_type in (1,3)) tp2 on tp1.userId=tp2.userId" +
            " ) tmp <if test=\" isPay != null \"> where isPay = #{isPay} </if> </script>")
    List<PpmdStaDetailsVo> getOverviewPpmdStaDetails(@Param(value = "orgId") Long orgId, @Param(value = "isPay") Integer isPay);


    @Select("select DATE_FORMAT(pay_date,'%e') payDate,IFNULL(Round(sum(pay_already/100),2),0) payAlready\n" +
            "from (\n" +
            "SELECT tp1.userId,tp2.pay_date,pay_already from (\n" +
            "SELECT user_id userId FROM ( \n" +
            "             select org_id,user_id,type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo,\n" +
            "             @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b\n" +
            "             where region_id =19 and DATE_FORMAT(start_time,'%Y-%m') <= DATE_FORMAT((${time}),'%Y-%m') \n" +
            "\t\t\t\t\t\t and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') > DATE_FORMAT((${time}),'%Y-%m')\n" +
            "             ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4) \n" +
            "\t\t\t\t\t\t and (org_id=#{orgId} or org_id in (select organization_id from t_organization " +
            " where org_level LIKE CONCAT('%-',#{orgId},'-%')))\n" +
            ") tp1 INNER JOIN (\n" +
            "SELECT user_id userId,pay_date,pay_already from t_ppmd_pay_log where region_id =19" +
            " and DATE_FORMAT(pay_date,'%Y-%m') = DATE_FORMAT((${time}),'%Y-%m') and pay_log_type in (1,3)\n" +
            ") tp2 on tp1.userId=tp2.userId\n" +
            ") temp GROUP BY DATE_FORMAT(pay_date,'%Y-%m-%d')")
    List<PpmdStaResultVo> getOverviewCurrentMonth(@Param(value = "orgId") Long orgId,
                                                  @Param(value = "time") String time);

    /**
     * 通过时间段查询交纳党费费用 左开右闭
     *
     * @param date1 开始时间
     * @param date2 结束时间
     *
     * @return total 总数(分)
     */
    @Select("SELECT\n" +
            "\t IFNULL(Round(sum(pay_already/100),2),0) AS total \n" +
            "FROM\n" +
            "\tt_ppmd_pay_log t \n" +
            "WHERE\n" +
            "\tt.pay_date > #{date1} \n" +
            "\tAND t.pay_date <= #{date2} \n" +
            "\tAND pay_log_type IN ( 1, 3 )")
    Double totalPayByDate(@Param("date1") LocalDateTime date1, @Param("date2") LocalDateTime date2);

    /**
     * 通过时间（年月）获取时间对应年每月的党费交纳数据
     * @param orgId 组织id
     * @param time 时间 yyyy-mm
     * @return 党费交纳数据
     */
    @Select("""
            SELECT
            	DATE_FORMAT( pay_date, '%e' ) payDate,
            	count( DISTINCT temp.userId ) payAlready
            FROM
            	(
            	SELECT
            		tp1.userId,
            		tp2.pay_date
            	FROM
            		(
            		SELECT
            			user_id userId
            		FROM
            			(
            			SELECT
            				org_id,
            				user_id,
            				type,
            			IF
            				( @user_id = user_id, @rowNum := @rowNum + 1, @rowNum := 1 ) AS rowNo,
            				@user_id := user_id x
            			FROM
            				t_ppmd_party_member,(
            				SELECT
            					@rowNum := 0,
            					@user_id := NULL
            				) b
            			WHERE
            				region_id = 19
            				AND DATE_FORMAT( start_time, '%Y-%m' ) <= DATE_FORMAT(( #{time} ), '%Y-%m' )
            				AND IFNULL( DATE_FORMAT( end_time, '%Y-%m' ), '9999-99' ) > DATE_FORMAT(( #{time} ), '%Y-%m' )
            			ORDER BY
            				user_id,
            				create_time DESC
            			) t1
            		WHERE
            			t1.rowNo = 1
            			AND t1.type NOT IN ( 3, 4 )
            			AND (
            				org_id = 3
            				OR org_id IN (
            				SELECT
            					organization_id
            				FROM
            					t_organization
            				WHERE
            				org_level LIKE CONCAT( '%-', #{orgId}, '-%' )))
            		) tp1
            		INNER JOIN (
            		SELECT
            			user_id userId,
            			pay_date
            		FROM
            			t_ppmd_pay_log
            		WHERE
            			region_id = 19
            			AND DATE_FORMAT( pay_date, '%Y-%m' ) = DATE_FORMAT(( #{time} ), '%Y-%m' )
            			AND pay_log_type IN ( 1, 3 )
            		) tp2 ON tp1.userId = tp2.userId
            	) temp
            GROUP BY
            	DATE_FORMAT(
            	pay_date,
            	'%Y-%m-%d')
            """)
    List<PpmdStaResultVo> getPayNumByDate(@Param("orgId") Long orgId, @Param("time") String time);

    @Select("SELECT\n" +
            "\tcount(0) partyMemberNum,\n" +
            "\tSUM(b.payAlready ) partyMemberAmount\n" +
            "FROM\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\tuser_id,\n" +
            "\t\torg_id AS orgId,\n" +
            "\t\torg_level AS orgLevel \n" +
            "\tFROM\n" +
            "\t\t(\n" +
            "\t\tSELECT\n" +
            "\t\t\ttppm.*,\n" +
            "\t\t\ttor.org_level org_level,\n" +
            "\t\t\tROW_NUMBER() OVER ( PARTITION BY tppm.user_id ORDER BY tppm.create_time DESC ) AS rowNo \n" +
            "\t\tFROM\n" +
            "\t\t\tt_ppmd_party_member tppm\n" +
            "\t\t\tLEFT JOIN t_organization tor ON tppm.org_id = tor.organization_id\n" +
            "\t\tWHERE\n" +
            "\t\t\ttppm.start_time <= CONCAT(#{payFor},'-01 00:00:00')\n" +
            "\t\t\t\tAND tppm.end_time > CONCAT(#{payFor},'-01 00:00:00')\n" +
            "\t\t\t\tAND tor.org_level LIKE '%-${orgId}-%'\n" +
            "\t\t\t\t) AS cte \n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\trowNo = 1 \n" +
            "\t\t\t\tAND type NOT IN ( 3, 4 ) \n" +
            "\t\t\t\tAND region_id = 86\n" +
            "\t\t\t\t\n" +
            "\t\t\t) AS a\n" +
            "\t\t\tLEFT JOIN ( SELECT pay_already AS payAlready, user_id FROM t_ppmd_pay_log WHERE pay_log_type IN ( 1, 3 ) AND pay_for = #{payFor}\n" +
            "\t\t\t) AS b ON a.user_id = b.user_id \n" +
            "\tWHERE\n" +
            "\tb.payAlready IS NOT NULL\n" +
            "\tGROUP BY a.orgId")
    List<PpmdStsVo> getSubmitted(@Param("orgId") Long orgId, @Param("payFor") String payFor);

    @Select("SELECT\n" +
            "\tt.org_id AS orgId,\n" +
            "\torg_level AS orgLevel,\n" +
            "\tcount( DISTINCT t.user_id ) AS partyMemberNum,\n" +
            "\tSUM( CASE WHEN pay_already IS NOT NULL THEN pay_already WHEN revised_party_fee IS NOT NULL AND revised_party_fee <> 0 THEN revised_party_fee ELSE payAmount END ) AS payAmount \n" +
            "FROM\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\tuser_id,\n" +
            "\t\torg_id,\n" +
            "\t\torg_level,\n" +
            "\t\tparty_member_id,(\n" +
            "\t\t\tcardinal_number * 100 \n" +
            "\t\t) AS payAmount,\n" +
            "\t\trevised_party_fee,\n" +
            "\t\t(\n" +
            "\t\tSELECT\n" +
            "\t\t\tpay_already \n" +
            "\t\tFROM\n" +
            "\t\t\tt_ppmd_pay_log \n" +
            "\t\tWHERE\n" +
            "\t\t\tuser_id = cte.user_id \n" +
            "\t\t\tAND pay_for = #{payFor} \n" +
            "\t\t\tAND pay_log_type IN ( 1, 3 ) \n" +
            "\t\t\tAND pay_already >= 0 \n" +
            "\t\t\tAND region_id = 86 \n" +
            "\t\t) AS pay_already \n" +
            "\tFROM\n" +
            "\t\t(\n" +
            "\t\tSELECT\n" +
            "\t\t\ttppm.*,\n" +
            "\t\t\ttor.org_level org_level,\n" +
            "\t\t\tROW_NUMBER() OVER ( PARTITION BY tppm.user_id ORDER BY tppm.create_time DESC ) AS rowNo \n" +
            "\t\tFROM\n" +
            "\t\t\tt_ppmd_party_member tppm\n" +
            "\t\t\tLEFT JOIN t_organization tor ON tppm.org_id = tor.organization_id \n" +
            "\t\tWHERE\n" +
            "\t\t\ttppm.start_time <= CONCAT(#{payFor},'-01 00:00:00') AND tppm.end_time > CONCAT(#{payFor},'-01 00:00:00') \n" +
            "\t\t\tAND tppm.region_id = 86 \n" +
            "\t\t) AS cte \n" +
            "\tWHERE\n" +
            "\t\trowNo = 1 \n" +
            "\t\tAND type NOT IN ( 3, 4 ) \n" +
            "\t\tAND org_level LIKE '%-${orgId}-%'\n" +
            "\t) AS t \n" +
            "GROUP BY\n" +
            "\tt.org_id,\n" +
            "\torg_level")
    List<PpmdStsVo> getNotSubmitted(@Param("orgId") Long orgId, @Param("payFor") String payFor);

    @Select("SELECT\n" +
            "\tcount(tppl.pay_log_id)\n" +
            "FROM\n" +
            "\tt_ppmd_pay_log tppl\n" +
            "\tLEFT JOIN t_organization tor ON tppl.org_id = tor.organization_id \n" +
            "WHERE\n" +
            "\ttppl.pay_for = #{payFor} \n" +
            "\tAND tppl.pay_date <= CONCAT(#{payForDay},' 23:59:59')\n" +
            "\tAND tor.org_level LIKE '%-${orgId}-%'\n" +
            "\tGROUP BY tppl.pay_log_id")
    Integer getSubmittedMemberNum(@Param("orgId") Long orgId, @Param("payFor") String payFor, @Param("payForDay") String payForDay);
    }
