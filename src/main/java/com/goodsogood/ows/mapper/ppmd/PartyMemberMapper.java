package com.goodsogood.ows.mapper.ppmd;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.ppmd.PartyMemberEntity;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.sas.ActiveOrgVo;
import com.goodsogood.ows.model.vo.sas.ActiveUserVo;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 党费人员信息
 * <AUTHOR>
 * @date 2019/11/20
 * @return
 */
@Repository
@Mapper
public interface PartyMemberMapper extends MyMapper<PartyMemberEntity> {

    @Select("SELECT DISTINCT user_id FROM t_ppmd_party_member WHERE org_id = #{orgId} AND start_time <= #{time} AND IFNULL(end_time,\"2099-01-01 00:00:00\") > #{time}")
    /**
     * 查询组织中的所有的userId
     */
    List<Long> findUserIdByOrg(@Param(value = "time") String time, @Param(value = "orgId") Long orgId);


    @Select("select DISTINCT org_id from t_ppmd_party_member where (org_id = #{orgId} or office_id = #{orgId}) AND start_time < #{time} AND IFNULL(end_time,\"2099-01-01 00:00:00\") > #{time}")
    /**
     * 根据查询时间查询所有需要交纳的组织ID
     */
    List<Long> findOrgIdByDate(@Param(value = "time") String time, @Param(value = "orgId") Long orgId);

    @Select("<script>" +
            " SELECT GG.org_id, GG.org_name, GG.pay_date from (SELECT * FROM ( " +
            " SELECT FF.user_id,FF.user_name,FF.org_id,FF.org_name,FF.type,pay_log_id,pay_log_type,pay_date from ( " +
            " SELECT * from (SELECT * FROM ( " +
            " SELECT * FROM t_ppmd_party_member WHERE DATE_FORMAT(start_time, '%Y-%m') <![CDATA[ <= ]]> #{queryTime} AND (DATE_FORMAT(end_time, '%Y-%m') <![CDATA[ > ]]> #{queryTime} OR end_time IS NULL) " +
            " ORDER BY create_time DESC) AS Q GROUP BY user_id)as W where W.office_id = #{partyId} and W.type != 3 )FF LEFT JOIN " +
            " t_ppmd_pay_log tppl on FF.user_id = tppl.user_id and tppl.pay_for = #{queryTime} and tppl.pay_log_type in (1,3))KK WHERE KK.org_id NOT in (SELECT org_id from ( " +
            " SELECT AA.user_id,AA.org_id,l.pay_log_id from (SELECT * from (SELECT * FROM( SELECT * FROM t_ppmd_party_member " +
            " WHERE DATE_FORMAT(start_time, '%Y-%m') <![CDATA[ <= ]]> #{queryTime} AND (DATE_FORMAT(end_time, '%Y-%m') <![CDATA[ > ]]> #{queryTime} OR end_time IS NULL) " +
            " ORDER BY create_time DESC) AS A GROUP BY user_id )B where B.office_id = #{partyId} and B.type != 3)AA LEFT JOIN t_ppmd_pay_log l on AA.user_id = l.user_id " +
            " and l.pay_for = #{queryTime} and l.pay_log_type in (1,3))BB where pay_log_id is NULL)ORDER BY pay_date DESC)GG GROUP BY GG.org_id ORDER BY GG.pay_date LIMIT 1 " +
            "</script>")
    @Results({
            @Result(column = "org_id", property = "orgId"),
            @Result(column = "org_name", property = "orgName"),
            @Result(column = "pay_date", property = "finishTime")
    })
    /**
     * 根据月份查询当前党委下最先交齐党支部
     */
    ActiveOrgVo findActiveOrg(@Param(value = "queryTime") String queryTime, @Param(value = "partyId") Long partyId);

    @Select("<script>" +
            " SELECT user_id, user_name, org_name, pay_date from t_ppmd_pay_log where pay_for = #{queryTime} and pay_log_type in (1,3) and user_id in ( " +
            " SELECT user_id from (SELECT * FROM( SELECT * FROM t_ppmd_party_member WHERE DATE_FORMAT(start_time, '%Y-%m') <![CDATA[ <= ]]> #{queryTime} AND (DATE_FORMAT(end_time, '%Y-%m') <![CDATA[ > ]]> #{queryTime} OR end_time IS NULL) " +
            " ORDER BY create_time DESC) AS A GROUP BY user_id)as B where B.office_id = #{partyId}) ORDER BY pay_date LIMIT 1 " +
            "</script>")
    @Results({
            @Result(column = "user_id", property = "userId"),
            @Result(column = "user_name", property = "userName"),
            @Result(column = "org_name", property = "orgName"),
            @Result(column = "pay_date", property = "payDate")
    })
    /**
     * 根据月份查询当前党委下最先交齐党员
     */
    ActiveUserVo findActiveUser(@Param(value = "queryTime") String queryTime, @Param(value = "partyId") Long partyId);
}
