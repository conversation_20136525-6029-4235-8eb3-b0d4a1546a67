package com.goodsogood.ows.mapper.ppmd;

import com.goodsogood.ows.model.mongodb.dss.UserPayInfo;
import com.goodsogood.ows.model.vo.ppmd.PpmdAidDecisionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * 辅助决策党费板块
 * <AUTHOR>
 * @create 2020-11-09
 **/
@Repository
@Mapper
public interface PpmdAidDecisionMapper {
    @Select("<script> select IFNULL(Round(sum(pay_already)/100,2),0) curYear from t_ppmd_pay_log" +
            " where region_id=#{regionId} and pay_log_type in (1,3) and SUBSTR(pay_for,1,4) = '${year}'</script>")
    /**
     * 首页本年度党费交纳
    * @Author: tc
    * @Description
    * @Date 15:15 2020/11/9
    * @param year
    */
    PpmdAidDecisionVo allOrgPayAlready(@Param("regionId") Long regionId,@Param("year") String year);

    @Select("<script> select IFNULL(Round(sum(pay_already)/100,2),0) curYear" +
            " from t_ppmd_pay_log where region_id=#{regionId} and pay_log_type in (1,3) and SUBSTR(pay_for,1,4) = '${year}'" +
            " and (org_id =#{orgId} or office_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%'))</script>")
    /**
     * 党委、党支部本年度党费交纳
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param year
     * @param month
     * @param orgId
     */
    PpmdAidDecisionVo orgPayAlready(@Param("regionId") Long regionId,@Param("year") String year, @Param("orgId") Long orgId);


    @Select("<script> select IFNULL(sum(case when numAlready &gt;= numShould then 1 else 0 end),0) finish," +
            " IFNULL(sum(case when numAlready &lt; numShould then 1 else 0 end),0) unfinish from (" +
            " select org_id orgId,numShould,numAlready,org_level orgLevel" +
            " from (select org.*,sum(numShould) numShould,sum(numAlready) numAlready" +
            " from (SELECT org_id,org_type_child,org_level from t_org_snapshot where status=1 and region_id=#{regionId} and date_month=#{queryMonth}" +
            " and org_type_child!=10280329 and org_type_child!=10280330 and (org_id =#{orgId} or org_level like" +
            " CONCAT('%-',#{orgId},'-%')) and org_id !=3261 and org_level not like CONCAT('%-',3261,'-%')) org LEFT JOIN(" +
            " select  orgLevelList,count(1) numShould,sum(isPay) numAlready" +
            " from (select ttcmp1.orgLevelList,ttcmp1.orgId,ttcmp3.userId,ttcmp3.isPay from (" +
            " SELECT org_id orgId,SUBSTR(REPLACE(concat(org_level,org_id),'-',','),2) orgLevelList from" +
            " t_org_snapshot where status=1 and region_id=#{regionId} and date_month=#{queryMonth} and org_type_child!=10280329 and org_type_child!=10280330" +
            " and (org_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%'))" +
            " and org_id !=3261 and org_level not like CONCAT('%-',3261,'-%')) ttcmp1" +
            " INNER JOIN (SELECT tp1.orgId,tp1.userId,IF(tp2.payDate is null,0,1) isPay from (" +
            " SELECT org_id orgId,user_id userId FROM ( " +
            " select org_id,user_id,type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b" +
            " where region_id=#{regionId} and DATE_FORMAT(start_time,'%Y-%m') &lt;= #{queryMonth} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{queryMonth}" +
            " ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4)" +
            " ) tp1 LEFT JOIN (SELECT user_id userId,pay_date payDate from t_ppmd_pay_log where region_id=#{regionId} and" +
            " pay_for =#{queryMonth} and pay_log_type in (1,3)) tp2 on tp1.userId=tp2.userId" +
            " ) ttcmp3 on ttcmp1.orgId = ttcmp3.orgId) ttcmp4 GROUP BY orgId) PPMD ON" +
            " FIND_IN_SET(org.org_id,PPMD.orgLevelList) GROUP BY org.org_id) retab where 1=1 AND (org_level ='-0-1-3-')) tab </script>")
    /**
     * 组织单月党费交纳进度,首页报表
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param queryMonth
     * @param orgId
     * 首页特殊语句 where 1=1 AND (org_level ='-0-1-3-')  -- 只获取党委级
     */
    PpmdAidDecisionVo orgPayFinish(@Param("regionId") Long regionId,@Param("queryMonth") String queryMonth, @Param("orgId") Long orgId);


    @Select("<script> select IFNULL(sum(case when numAlready &gt;= numShould then 1 else 0 end),0) finish," +
            " IFNULL(sum(case when numAlready &lt; numShould then 1 else 0 end),0) unfinish from (" +
            " select  orgId,count(1) numShould,sum(isPay) numAlready from (" +
            " select ttcmp1.orgLevelList,ttcmp1.orgId,ttcmp3.userId,ttcmp3.isPay from (" +
            " SELECT org_id orgId,SUBSTR(REPLACE(concat(org_level,org_id),'-',','),2) orgLevelList from t_org_snapshot" +
            " where status =1 and region_id=#{regionId} and date_month=#{queryMonth} and org_type_child!=10280329" +
            " and org_type_child!=10280330 and (org_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%'))) ttcmp1" +
            " INNER JOIN (SELECT tp1.orgId,tp1.userId,IF(tp2.payDate is null,0,1) isPay from (" +
            " SELECT org_id orgId,user_id userId FROM (select org_id,user_id,type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b" +
            " where region_id=#{regionId} and DATE_FORMAT(start_time,'%Y-%m') &lt;= #{queryMonth} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{queryMonth}" +
            " ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4)) tp1 LEFT JOIN (" +
            " SELECT user_id userId,pay_date payDate from t_ppmd_pay_log where region_id=#{regionId} and pay_for =#{queryMonth} and pay_log_type in (1,3)" +
            " ) tp2 on tp1.userId=tp2.userId) ttcmp3 on ttcmp1.orgId = ttcmp3.orgId) ttcmp4 GROUP BY orgId) tab </script>")
    /**
     * 组织单月党费交纳进度,党委报表
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param queryMonth
     * @param orgId
     */
    PpmdAidDecisionVo orgPartyPayFinish(@Param("regionId") Long regionId,@Param("queryMonth") String queryMonth, @Param("orgId") Long orgId);


    @Select("<script> select IFNULL(sum(isPay),0) finish,IFNULL(sum(case when isPay=0 then 1 else 0 end),0) unfinish from (" +
            " select ttcmp1.orgId,ttcmp3.userId,ttcmp3.isPay from (" +
            " SELECT org_id orgId,SUBSTR(REPLACE(concat(org_level,org_id),'-',','),2) orgLevelList" +
            " from t_org_snapshot where status=1 and region_id=#{regionId} and date_month=#{queryMonth}" +
            " and org_type_child!=10280329 and org_type_child!=10280330" +
            " and (org_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%'))) ttcmp1" +
            " INNER JOIN (SELECT tp1.orgId,tp1.userId,IF(tp2.payDate is null,0,1) isPay from (" +
            " SELECT org_id orgId,user_id userId FROM ( " +
            " select org_id,user_id,type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b" +
            " where region_id=#{regionId} and DATE_FORMAT(start_time,'%Y-%m') &lt;= #{queryMonth} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{queryMonth}" +
            " ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4)" +
            " ) tp1 LEFT JOIN (SELECT user_id userId,pay_date payDate from t_ppmd_pay_log where region_id=#{regionId} and" +
            " pay_for =#{queryMonth} and pay_log_type in (1,3)) tp2 on tp1.userId=tp2.userId" +
            " ) ttcmp3 on ttcmp1.orgId = ttcmp3.orgId) ttcmp4 </script>")
    /**
     * 组织单月党费交纳进度,党支部报表
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param queryMonth
     * @param orgId
     */
    PpmdAidDecisionVo orgMemberPayFinish(@Param("regionId") Long regionId,@Param("queryMonth") String queryMonth, @Param("orgId") Long orgId);


    @Select("<script> select count(1) numShould,IFNULL(sum(case when payAlready!=0 then 1 else 0 end),0) numAlready, " +
            " IFNULL(sum(payShould),0) payShould,IFNULL(sum(payAlready),0) payAlready from ( " +
            " select ttmp1.*,IFNULL(ttmp2.payAlready,0) payAlready from (select tmp1.orgId,tmp1.userId,tmp1.type, " +
            " tmp1.payShould from (select tab1.orgId,tab1.userId,tab1.type, IF(type=2  " +
            " or type=5,floor(tab1.revisedPartyFee/100*1*10.0)/10.0, " +
            " floor(tab1.cardinalNumber/100*convert(tab4.proportion,decimal(10,2))/100*10.0)/10.0) " +
            " payShould from ( select org_id orgId,user_id userId,type,cardinal_number cardinalNumber," +
            " revised_party_fee revisedPartyFee,ratio_type ratioType from ( select org_id,user_id,type,cardinal_number," +
            " revised_party_fee,ratio_type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @user_id:=user_id x from t_ppmd_party_member,(select @rowNum :=0,@user_id:=null) b where region_id=#{regionId} and" +
            " DATE_FORMAT(start_time,'%Y-%m') &lt;= #{queryMonth} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') " +
            " &gt; #{queryMonth} order by user_id,create_time desc) t1 where t1.rowNo = 1 and t1.type!=3 and t1.type!=4) " +
            " tab1 LEFT JOIN (select IF(upper_limit is null,t1.lower_limit+0.001,t1.lower_limit) " +
            " lowerLimit, IFNULL(upper_limit,9999999999) upperLimit,proportion,ratio_type ratioType from " +
            " t_ppmd_pay_ratio t1 INNER JOIN ((SELECT uuid from t_ppmd_pay_ratio where region_id=#{regionId} and" +
            " DATE_FORMAT(effective_time,'%Y-%m') &lt;= #{queryMonth} and ratio_type=1 GROUP BY uuid ORDER BY " +
            " create_time desc limit 1) union all (SELECT uuid from t_ppmd_pay_ratio where region_id=#{regionId} and" +
            " DATE_FORMAT(effective_time,'%Y-%m') &lt;= #{queryMonth} and ratio_type=2 " +
            " GROUP BY uuid ORDER BY create_time desc limit 1) ) t2 on t1.uuid = t2.uuid ORDER BY ratioType,lowerLimit) " +
            " tab4 on tab1.cardinalNumber &gt;= tab4.lowerLimit and tab1.cardinalNumber &lt;=tab4.upperLimit and " +
            " tab1.ratioType = tab4.ratioType) tmp1 INNER JOIN (SELECT org_id orgId from t_org_snapshot where status=1 and region_id=#{regionId} and" +
            " date_month =#{queryMonth} and org_type_child!=10280329 and org_type_child!=10280330 and " +
            " org_id !=3261 and org_level not like CONCAT('%-',3261,'-%')) tmp2 on tmp1.orgId= tmp2.orgId) ttmp1 " +
            " LEFT JOIN (select user_id userId,pay_already/100 payAlready from t_ppmd_pay_log where region_id=#{regionId} and" +
            " pay_log_type = 1 and pay_for = #{queryMonth} and DATE_FORMAT(pay_date,'%d') ='01' " +
            " ) ttmp2 on ttmp1.userId = ttmp2.userId) result </script>")
    /**
     * 组织单月一号党费交纳完成度，首页报表
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param queryMonth
     */
    PpmdAidDecisionVo allOrgPayMonthFirst(@Param("regionId") Long regionId,@Param("queryMonth") String queryMonth);


    @Select("<script> select count(1) numShould,IFNULL(sum(case when payAlready!=0 then 1 else 0 end),0) numAlready," +
            " IFNULL(sum(payShould),0) payShould,IFNULL(sum(payAlready),0) payAlready from (" +
            " select ttmp1.*,IFNULL(ttmp2.payAlready,0) payAlready from (select tmp1.orgId,tmp1.userId,tmp1.type," +
            " tmp1.payShould from (select tab1.orgId,tab1.userId,tab1.type, IF(type=2 " +
            " or type=5,floor(tab1.revisedPartyFee/100*1*10.0)/10.0," +
            " floor(tab1.cardinalNumber/100*convert(tab4.proportion,decimal(10,2))/100*10.0)/10.0) " +
            " payShould from ( select org_id orgId,user_id userId,type,cardinal_number cardinalNumber," +
            " revised_party_fee revisedPartyFee,ratio_type ratioType from ( select org_id,user_id,type,cardinal_number," +
            " revised_party_fee,ratio_type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @user_id:=user_id x from t_ppmd_party_member,(select @rowNum :=0,@user_id:=null) b where region_id=#{regionId} and" +
            " DATE_FORMAT(start_time,'%Y-%m') &lt;= #{queryMonth} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') " +
            " &gt; #{queryMonth} order by user_id,create_time desc) t1 where t1.rowNo = 1 and t1.type!=3 and t1.type!=4) " +
            " tab1 LEFT JOIN (select IF(upper_limit is null,t1.lower_limit+0.001,t1.lower_limit) " +
            " lowerLimit, IFNULL(upper_limit,9999999999) upperLimit,proportion,ratio_type ratioType from " +
            " t_ppmd_pay_ratio t1 INNER JOIN ((SELECT uuid from t_ppmd_pay_ratio where region_id=#{regionId} and" +
            " DATE_FORMAT(effective_time,'%Y-%m') &lt;= #{queryMonth} and ratio_type=1 GROUP BY uuid ORDER BY" +
            " create_time desc limit 1) union all (SELECT uuid from t_ppmd_pay_ratio where region_id=#{regionId} and" +
            " DATE_FORMAT(effective_time,'%Y-%m') &lt;= #{queryMonth} and ratio_type=2 " +
            " GROUP BY uuid ORDER BY create_time desc limit 1) ) t2 on t1.uuid = t2.uuid ORDER BY ratioType,lowerLimit) " +
            " tab4 on tab1.cardinalNumber &gt;= tab4.lowerLimit and tab1.cardinalNumber &lt;=tab4.upperLimit and " +
            " tab1.ratioType = tab4.ratioType) tmp1 INNER JOIN (SELECT org_id orgId from t_org_snapshot where status=1 and region_id=#{regionId} and" +
            " date_month =#{queryMonth} and org_type_child!=10280329 and org_type_child!=10280330 and" +
            " (org_id =#{orgId} or org_level like CONCAT('%-',#{orgId},'-%'))) tmp2 on tmp1.orgId= tmp2.orgId) ttmp1" +
            " LEFT JOIN (select user_id userId,pay_already/100 payAlready from t_ppmd_pay_log where region_id=#{regionId} and" +
            " pay_log_type = 1 and pay_for = #{queryMonth} and DATE_FORMAT(pay_date,'%d') ='01'" +
            " ) ttmp2 on ttmp1.userId = ttmp2.userId) result </script>")
    /**
     * 组织单月一号党费交纳完成度，党委报表、支部报表
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param year
     * @param month
     * @param orgId
     */
    PpmdAidDecisionVo orgPayMonthFirst(@Param("regionId") Long regionId,@Param("queryMonth") String queryMonth, @Param("orgId") Long orgId);




    @Select("<script> select IFNULL(payShould,0) standard from (" +
            " select tab1.orgId,tab1.orgName,tab1.userId,tab1.userName,tab1.type, IF(type=2 " +
            " or type=5,floor(tab1.revisedPartyFee/100*1*10.0)/10.0,floor(tab1.cardinalNumber/100*convert(tab4.proportion,decimal(10,2))/100*10.0)/10.0) " +
            " payShould from ( select org_id orgId,org_name orgName,user_id userId,user_name " +
            " userName,type,cardinal_number cardinalNumber, revised_party_fee revisedPartyFee,ratio_type " +
            " ratioType from (select org_id,org_name,user_id,user_name,cardinal_number,type,revised_party_fee,ratio_type,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo,@user_id:=user_id " +
            " x from t_ppmd_party_member,(select @rowNum :=0,@user_id:=null) b where region_id=#{regionId} and DATE_FORMAT(start_time,'%Y-%m') " +
            " &lt;= #{queryMonth} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{queryMonth}" +
            " and user_id=#{userId} order by create_time desc) t1 where t1.rowNo = 1 and t1.type not in (3,4)) " +
            " tab1 LEFT JOIN (select IF(upper_limit is null,t1.lower_limit+0.001,t1.lower_limit) " +
            " lowerLimit, IFNULL(upper_limit,9999999999) upperLimit,proportion,ratio_type ratioType from " +
            " t_ppmd_pay_ratio t1 INNER JOIN ((SELECT uuid from t_ppmd_pay_ratio where region_id=#{regionId} and DATE_FORMAT(effective_time,'%Y-%m') " +
            " &lt;= #{queryMonth} and ratio_type=1 GROUP BY uuid ORDER BY create_time desc limit 1) union all (SELECT " +
            " uuid from t_ppmd_pay_ratio where region_id=#{regionId} and DATE_FORMAT(effective_time,'%Y-%m') &lt;= #{queryMonth} and ratio_type=2 " +
            " GROUP BY uuid ORDER BY create_time desc limit 1) ) t2 on t1.uuid = t2.uuid ORDER BY ratioType,lowerLimit) " +
            " tab4 on tab1.cardinalNumber &gt;= tab4.lowerLimit and tab1.cardinalNumber &lt;=tab4.upperLimit and " +
            " tab1.ratioType = tab4.ratioType) tab </script>")
    /**
     * 党员党费标准
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param queryMonth
     * @param userId
     */
    UserPayInfo memberCardinalNumber(@Param("regionId") Long regionId,@Param("queryMonth") String queryMonth, @Param("userId") Long userId);



    @Select("<script> select r1.cn numShould,r2.payDay,IFNULL(r2.rank,0) rank from (" +
            " select count(1) cn from (SELECT user_id,office_id FROM (" +
            " select user_id,type,office_id,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b" +
            " where region_id=#{regionId} and DATE_FORMAT(start_time,'%Y-%m') &lt;= #{queryMonth} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{queryMonth}" +
            " ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4)) tab1  INNER JOIN (" +
            " SELECT office_id FROM (select user_id,type,office_id,if(@user_id=user_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @user_id:=user_id x from t_ppmd_party_member,(Select @rowNum :=0,@user_id:=null) b" +
            " where region_id=#{regionId} and DATE_FORMAT(start_time,'%Y-%m') &lt;= #{queryMonth} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{queryMonth}" +
            " ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4) and user_id = #{userId} " +
            " ) tab2 on tab1.office_id = tab2.office_id) r1 LEFT JOIN (select payDay,rank from (" +
            " SELECT user_id userId,DATE_FORMAT(pay_date,'%d') payDay from t_ppmd_pay_log where region_id=#{regionId} and pay_for =#{queryMonth} and pay_log_type =1 and user_id = #{userId}" +
            " ) ttpm1 LEFT JOIN (select @rank:=@rank+1 as rank,userId from (select tmp1.userId,pay_date from " +
            " (SELECT tt1.user_id userId,tt2.office_id,tt1.pay_date from (select user_id,pay_date from t_ppmd_pay_log where region_id=#{regionId} and pay_for =#{queryMonth} and pay_log_type =1 ) tt1 INNER JOIN (SELECT user_id,office_id FROM (" +
            "  select user_id,type,office_id,if(@user_id2=user_id,@rowNum2:=@rowNum2+1,@rowNum2:=1) as rowNo," +
            "  @user_id2:=user_id x from t_ppmd_party_member,(Select @rowNum2 :=0,@user_id2:=null) b" +
            "  where region_id=#{regionId} and DATE_FORMAT(start_time,'%Y-%m') &lt;= #{queryMonth} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{queryMonth}" +
            "  ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4)) tt2 on tt1.user_id = tt2.user_id" +
            ") tmp1 INNER JOIN (" +
            " SELECT office_id FROM (select user_id,type,office_id,if(@user_id3=user_id,@rowNum3:=@rowNum3+1,@rowNum3:=1) as rowNo," +
            " @user_id3:=user_id x from t_ppmd_party_member,(Select @rowNum3 :=0,@user_id3:=null) b" +
            " where region_id=#{regionId} and DATE_FORMAT(start_time,'%Y-%m') &lt;= #{queryMonth} and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),'9999-99') &gt; #{queryMonth}" +
            " ORDER BY user_id,create_time desc ) t1 where t1.rowNo = 1 and t1.type not in (3,4) and user_id = #{userId} " +
            " ) tmp2 on tmp1.office_id=tmp2.office_id) t,(Select @rank :=0) b ORDER BY pay_date,userId" +
            " ) ttpm2 on ttpm1.userId = ttpm2.userId) r2 on 1=1 </script>")
    /**
     * 党员交费排名
     * @Author: tc
     * @Description
     * @Date 15:15 2020/11/9
     * @param queryMonth
     * @param userId
     */
    PpmdAidDecisionVo memberRank(@Param("regionId") Long regionId,@Param("queryMonth") String queryMonth, @Param("userId") Long userId);

}
