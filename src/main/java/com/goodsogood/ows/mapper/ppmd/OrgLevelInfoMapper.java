package com.goodsogood.ows.mapper.ppmd;

import com.goodsogood.ows.mapper.MyMapper;
import com.goodsogood.ows.model.db.ppmd.OrgLevelInfoEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: tc
 * @Description
 */
@Repository
@Mapper
public interface OrgLevelInfoMapper extends MyMapper<OrgLevelInfoEntity> {

    @Select("<script> select org_level_info_id orgLevelInfoId,org_id orgId,parent_list parentList," +
            " child_list childList,create_time createTime" +
            " from t_ppmd_org_level_info where org_id = #{orgId} and create_time &lt;= #{queryMonth} order by create_time desc limit 1" +
            "</script>")
    /**
     * 根据查询月份和组织编号查询组织当时的层级关系
     * @param orgId
     * @param queryMonth
     * @return
     */
    OrgLevelInfoEntity findOrgLevelInfo(@Param("orgId") Long orgId, @Param("queryMonth") String queryMonth);

    @Select("<script> select org_level_info_id orgLevelInfoId,org_id orgId,parent_list parentList," +
            " child_list childList,create_time createTime" +
            " from (select *,if(@org_id=org_id,@rowNum:=@rowNum+1,@rowNum:=1) as rowNo," +
            " @org_id:=org_id x from t_ppmd_org_level_info,(Select @rowNum :=0,@org_id:=null) b" +
            " where find_in_set(#{orgId},parent_list) or find_in_set(#{orgId},child_list) or org_id =#{orgId} and create_time &lt;= #{queryMonth}" +
            " ORDER BY org_id,create_time desc ) t1 where t1.rowNo = 1 </script>")
    /**
     * 根据查询月份和组织编号查询所有相关组织的层级信息集合(不包含所属党委(工委直接下级)、工委及以上的组织编号)
     * @param orgId
     * @param queryMonth
     * @return
     */
    List<OrgLevelInfoEntity> findFullOrgLevelInfo(@Param("orgId") Long orgId, @Param("queryMonth") String queryMonth);


    @Delete("<script> delete from t_ppmd_org_level_info where org_level_info_id in(select org_level_info_id from (" +
            " select *,if(@ct=CONCAT(org_id,'_',IFNULL(parent_list,'-1'),'_',IFNULL(child_list,'-1'))," +
            " @rowNum:=@rowNum+1,@rowNum:=1) as rowNo,@ct:=CONCAT(org_id,'_',IFNULL(parent_list,'-1'),'_',IFNULL(child_list,'-1'))" +
            " x from t_ppmd_org_level_info,(Select @rowNum :=0,@ct:=null) b " +
            " where create_time &gt;= #{startDate} and  create_time &lt;= #{endDate} " +
            " order by org_id,create_time desc) tmp where tmp.rowNo !=1) </script>")
    /**
     * 根据月份删除当月重复的数据
     * @param queryMonth
     */
    void deleteRepeatOrgLevelInfoByMonth(@Param("startDate") String startDate, @Param("endDate") String endDate);

}
