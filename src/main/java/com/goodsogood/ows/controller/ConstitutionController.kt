package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.constitution.ConstitutionService
import com.goodsogood.ows.service.readClass.ReadClassService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*


@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "党章统计")
@RequestMapping("/constitution")
class ConstitutionController(@Autowired val errors: Errors, @Autowired val constitutionService: ConstitutionService) {
    @HttpMonitorLogger
    @GetMapping("/count")
    @ApiOperation("党章统计")
    fun count(
        @RequestHeader headers: HttpHeaders,
        @RequestParam(value = "name", required = false) name: String?,
        @RequestParam(value = "year", required = false) year: Int?
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                constitutionService.count(headers, name, year), errors
            ), HttpStatus.OK
        )
    }
}