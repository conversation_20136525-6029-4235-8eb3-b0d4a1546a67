package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.service.dss.DssTestService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 辅助决策测试控制层
 * <AUTHOR>
 */
@Controller
@Log4j2
@RequestMapping("/dss/test")
@CrossOrigin(origins = "*")
public class DssTestController {

	private final Errors errors;
	private final DssTestService dssTestService;

	@Autowired
	public DssTestController(Errors errors, DssTestService dssTestService) {
		this.errors = errors;
		this.dssTestService = dssTestService;
	}

	@HttpMonitorLogger
	@GetMapping("/report/get/{type}/{id}/{date}")
	@ApiOperation("构建辅助决策数据")
	public ResponseEntity<String> getReport(@RequestHeader HttpHeaders headers,
											   @PathVariable("type") int type,
											   @PathVariable("id") long id,
											   @PathVariable("date") String date) {
		HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
		Long regionId = header.getRegionId();
		String report = this.dssTestService.getReport(type, id, Integer.parseInt(date), null == regionId ? 3L : regionId);
		return new ResponseEntity<>(report, HttpStatus.OK);
	}

	@HttpMonitorLogger
	@GetMapping("/user/find/{name}/{year}")
	@ApiOperation("构建辅助决策数据")
	public ResponseEntity<String> getUser(@RequestHeader HttpHeaders headers,
											   @PathVariable("name") String name,
											   @PathVariable("year") String year) {
		HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
		Long regionId = header.getRegionId();
		String json = this.dssTestService.getUsers(name, Integer.parseInt(year), null == regionId ? 3L : regionId);
		return new ResponseEntity<>(json, HttpStatus.OK);
	}

	@HttpMonitorLogger
	@GetMapping("/org/find/{name}/{year}/{last_id}")
	@ApiOperation("构建辅助决策数据")
	public ResponseEntity<String> getOrg(@RequestHeader HttpHeaders headers,
											@PathVariable("name") String name,
											@PathVariable("year") String year,
											@PathVariable("last_id") String last_id) {
		HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
		Long regionId = header.getRegionId();
		String json = this.dssTestService.getOrgs(name, Integer.parseInt(year), null == regionId ? 3L : regionId);
		return new ResponseEntity<>(json, HttpStatus.OK);
	}
}
