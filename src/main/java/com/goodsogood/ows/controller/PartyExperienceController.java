package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.experience.UserPartyEvalEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.experience.*;
import com.goodsogood.ows.service.experience.UserPartyEvalAdviceService;
import com.goodsogood.ows.service.experience.UserPartyEvalPlanService;
import com.goodsogood.ows.service.experience.UserPartyEvalResultService;
import com.goodsogood.ows.service.experience.UserPartyEvalService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 党性体验
 *
 * <AUTHOR> liguoyong
 * @date : 2022/03/11
 */
@RestController
@RequestMapping("/party_eval")
@Log4j2
@CrossOrigin(origins = "*")
public class PartyExperienceController {

    private final UserPartyEvalService userPartyEvalService;
    private final UserPartyEvalPlanService userPartyEvalPlanService;
    private final UserPartyEvalAdviceService userPartyEvalAdviceService;
    private final UserPartyEvalResultService userPartyEvalResultService;



    private final Errors errors;

    public PartyExperienceController(UserPartyEvalService userPartyEvalService,
                                     UserPartyEvalPlanService userPartyEvalPlanService,
                                     UserPartyEvalAdviceService userPartyEvalAdviceService,
                                     UserPartyEvalResultService userPartyEvalResultService,
                                     Errors errors) {
        this.userPartyEvalService = userPartyEvalService;
        this.userPartyEvalPlanService = userPartyEvalPlanService;
        this.userPartyEvalAdviceService = userPartyEvalAdviceService;
        this.userPartyEvalResultService = userPartyEvalResultService;
        this.errors = errors;
    }

    @HttpMonitorLogger
    @PostMapping("/click_experience")
    @ApiOperation("手动调用接口体验")
    public ResponseEntity<Result<?>> clickExperience(@RequestHeader HttpHeaders headers,
                                                     @RequestParam(name = "type",defaultValue = "2") Integer type )
            throws InterruptedException {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return   userPartyEvalService.clickExperience(sysHeader,type);
    }

    @HttpMonitorLogger
    @PostMapping("/val_click_experience")
    @ApiOperation("校验今天是不是点击")
    public ResponseEntity<Result<?>> valClickExperience(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return   userPartyEvalService.valClickExperience(sysHeader,2);
    }

    @HttpMonitorLogger
    @PostMapping("/auto_experience")
    @ApiOperation("自动调用接口体验")
    public ResponseEntity<Result<?>> autoExperience(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        String batchNumber= UUID.randomUUID().toString().replace("-", "");
        userPartyEvalService.autoExperience(sysHeader.getRegionId(),null,(byte) 1,batchNumber);
        return null;
    }


    @HttpMonitorLogger
    @GetMapping("/result")
    @ApiOperation("体检结果")
    public ResponseEntity<Result<?>> experienceResult(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return userPartyEvalService.experienceResult(sysHeader);
    }

    @HttpMonitorLogger
    @GetMapping("/cal_star")
    @ApiOperation("计算汇总星级")
    public ResponseEntity<Result<?>> calStar(@RequestHeader HttpHeaders headers) {
        userPartyEvalService.calStar(1);
        return new ResponseEntity<>(new Result<>("suc", errors), HttpStatus.OK);
    }


//    @HttpMonitorLogger
//    @GetMapping("/report_list")
//    @ApiOperation("历史体检报告列表展示")
//    public ResponseEntity<Result<?>> report(@RequestHeader HttpHeaders headers,
//                                            @RequestParam(name = "start_time",defaultValue = "") String startTime,
//                                            @RequestParam(name = "end_time",defaultValue = "") String endTime,
//                                            @RequestParam(required = false, defaultValue = "1") Integer page,
//                                            @RequestParam(required = false,
//                                                    name = "page_size", defaultValue = "10") Integer pageSize) {
//        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
//        if(StringUtils.isEmpty(startTime)){
//            Integer year = DateUtils.getYear(new Date());
//            Integer month = DateUtils.getMonth(new Date());
//            startTime = DateUtils.dateFormat(year,month);
//        }
//        if(StringUtils.isEmpty(endTime)){
//            endTime =DateUtils.getNextMonth( DateUtils.toFormatDate(new Date(),"yyyy-MM") );
//        }
//        return new ResponseEntity<>(new Result<>( userPartyEvalService.report(
//                new PageNumber(page, pageSize), sysHeader,startTime,endTime), errors), HttpStatus.OK);
//    }

    @HttpMonitorLogger
    @GetMapping("/report_list")
    @ApiOperation("历史体检报告列表展示")
    public ResponseEntity<Result<?>> report(@RequestHeader HttpHeaders headers,
                                            @RequestParam(name = "year",defaultValue = "") Integer year ) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>( userPartyEvalService.
                reportByYear(year,sysHeader.getUserId()), errors), HttpStatus.OK);
    }


    @GetMapping("/plan")
    @ApiOperation("锤炼计划列表展示")
    @HttpMonitorLogger
    public ResponseEntity<Result<List<UserPartyListVO>>> queryPlanList(@RequestHeader HttpHeaders headers,
                                                                       @RequestParam(value = "eval_id",required = false) Long evalId){
        List<UserPartyListVO> list = userPartyEvalPlanService.queryPlanList(headers,evalId);
        return new ResponseEntity<Result<List<UserPartyListVO>>>(new Result<>(list,errors), HttpStatus.OK);

    }

    @GetMapping("/advice")
    @ApiOperation("意见建议")
    @HttpMonitorLogger
    public ResponseEntity<Result<List<UserPartyListVO>>> queryAdviceList(@RequestHeader HttpHeaders headers,
                                                                         @RequestParam("eval_id") Long evalId){
        List<UserPartyListVO> list = userPartyEvalAdviceService.queryAdviceList(headers,evalId);
        return new ResponseEntity<Result<List<UserPartyListVO>>>(new Result<>(list,errors), HttpStatus.OK);
    }



    @GetMapping("/report")
    @ApiOperation("体检报告")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> queryReport(@RequestHeader HttpHeaders headers, @RequestParam(value = "eval_id",required = false) Long evalId){
        return userPartyEvalResultService.queryReport(headers,evalId);
    }


    @GetMapping("/t1")
    @ApiOperation("柱状图测试")
    public ResponseEntity<Result<List<UserPartyEvalEntity.TypeMonthStarForm>>> test1(@RequestParam("user_id") Long userId){
        List<UserPartyEvalEntity.TypeMonthStarForm> list = userPartyEvalService.createHistogramImg(userId);
        return new ResponseEntity<Result<List<UserPartyEvalEntity.TypeMonthStarForm>>>(new Result<>(list,errors), HttpStatus.OK);
    }


    @GetMapping("/t2")
    @ApiOperation("给result设置adviceid测试")
    public String test2(@RequestParam("task_id") String taskId){
        userPartyEvalService.updateResultAdviceId(86L,taskId);
        return "ssss";
    }



    @HttpMonitorLogger
    @GetMapping("/org-complex/times")
    @ApiOperation("统计体检次数")
    public ResponseEntity<Result<OrgTimesVO>> sumExperienceTimes(@RequestParam("org_id") Long orgId,
                                                                 @RequestParam("date_month") Integer dateMonth,
                                                                 @RequestHeader HttpHeaders headers){
        OrgTimesVO times = userPartyEvalService.sumExperienceTimes(orgId,dateMonth,headers);
        return new ResponseEntity<>(new Result<>(times, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org-complex/report-chart")
    @ApiOperation("党员体检结果：饼图+柱状图+标签统计")
    public ResponseEntity<Result<OrgReportChartVO>> reportChart(@RequestParam("org_id") Long orgId,
                                                                @RequestParam("date_month") Integer dateMonth,
                                                                @RequestHeader HttpHeaders headers){
        OrgReportChartVO list = userPartyEvalService.reportChart(orgId,dateMonth,headers);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org-complex/report-detail")
    @ApiOperation("党员体检详情10条")
    public ResponseEntity<Result<List<OrgUserReportVO>>> reportDetail(@RequestParam("org_id") Long orgId,
                                                                      @RequestParam("date_month") Integer dateMonth,
                                                                      @RequestHeader HttpHeaders headers,
                                                                      @RequestParam(value="num",required = false) Integer num){
        List<OrgUserReportVO> list = userPartyEvalService.reportDetail(orgId,dateMonth,headers,num);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }



    @HttpMonitorLogger
    @PostMapping("/org-complex/report-detail-condition")
    @ApiOperation("党员体检详情-筛选条件")
    public ResponseEntity<Result<List<OrgUserReportVO>>> reportDetailCondition(@RequestBody ReportChooseForm reportChooseForm,
                                                                               @RequestHeader HttpHeaders headers){
        List<OrgUserReportVO> list = userPartyEvalService.reportDetailCondition(headers,reportChooseForm);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @PostMapping("/org-complex/report-detail-condition-pc")
    @ApiOperation("党员体检详情-pc端筛选条件")
    public ResponseEntity<Result<Page<OrgUserReportVO>>> reportPcDetailListCondition(@RequestBody ReportChooseForm reportChooseForm,
                                                                                     @RequestHeader HttpHeaders headers){
        Optional<Page<OrgUserReportVO>> list = userPartyEvalService.reportPcDetailPageConditionNullPoint(headers,reportChooseForm);
        return new ResponseEntity<>(new Result<>(list.orElse(null), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org-complex/date-month")
    @ApiOperation("最小月份")
    public ResponseEntity<Result<Integer>> queryMinMonth(@RequestHeader HttpHeaders headers,@RequestParam("org_id") Long orgId){
        Integer yearMonth = userPartyEvalService.queryMinMonth(headers,orgId);
        return new ResponseEntity<>(new Result<>(yearMonth,errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org-complex/leader-board")
    @ApiOperation("五星党员占比排行榜")
    public ResponseEntity<Result<LeaderBoardVO>> queryLeaderBoard(@RequestHeader HttpHeaders headers,
                                                                  @RequestParam("org_id") Long orgId,
                                                                  @RequestParam("date_month") Integer dateMonth){
        LeaderBoardVO yearMonth = userPartyEvalService.queryLeaderBoard(headers,orgId,dateMonth);
        return new ResponseEntity<>(new Result<>(yearMonth,errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org-complex/report-item-pc")
    @ApiOperation("党员体检详情-细项详情")
    public ResponseEntity<Result<List<UserPartyReportVO.TypeStarVO>>> reportPcItemDetail(@RequestParam("eval_id") Long evalId,
                                                                                  @RequestHeader HttpHeaders headers){
        List<UserPartyReportVO.TypeStarVO> list = userPartyEvalService.reportPcItemDetail(headers,evalId);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/org-complex/report-download")
    @ApiOperation("下载查询结果")
    public void reportDownLoad(@RequestBody ReportChooseForm reportChooseForm,
                                                          @RequestHeader HttpHeaders headers,
                                                          HttpServletResponse httpServletResponse){
        userPartyEvalService.reportDownLoad(headers,reportChooseForm,httpServletResponse);
//        return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/report_list-pc")
    @ApiOperation("历史体检报告列表展示+柱状图")
    public ResponseEntity<Result<HistoryReportVO>> reportPc(@RequestHeader HttpHeaders headers,
                                              @RequestParam(name = "start_month") Integer startMonth,
                                              @RequestParam(name = "end_month") Integer endMonth,
                                              @RequestParam(name = "user_id") Long userId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>( userPartyEvalService.
                reportByMonth(startMonth,endMonth,userId), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org-complex/tag")
    @ApiOperation("标签查询")
    public ResponseEntity<Result<Map<Integer,List<String>>>> queryTag(){
        Map<Integer,List<String>> tag = userPartyEvalService.queryTag();
        return new ResponseEntity<>(new Result<>(tag, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org-complex/head")
    @ApiOperation("头部信息查询")
    public ResponseEntity<Result<UserPartyEvalEntity>> queryHead(@RequestParam("user_id") Long userId){
        UserPartyEvalEntity head = userPartyEvalService.queryHead(userId);
        return new ResponseEntity<>(new Result<>(head, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/last_user/eval")
    @ApiOperation("指定月份系统测评体检结果查询")
    public ResponseEntity<Result<UserPartyEvalEntity>> queryLastMonthEval(@RequestParam("user_id") Long userId,
                                                                          @RequestParam(value = "date_month",required = false) Integer dateMonth){
        UserPartyEvalEntity head = userPartyEvalService.queryLastMonthEval(userId,dateMonth);
        return new ResponseEntity<>(new Result<>(head, errors), HttpStatus.OK);
    }

}
