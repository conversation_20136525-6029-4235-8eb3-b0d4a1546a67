package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.sas.WorkItemEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.pbm.impl.PbmNewsStudyServiceImpl;
import com.goodsogood.ows.service.pbm.impl.PbmUserPartyOathServiceImpl;
import com.goodsogood.ows.service.task.TaskServices;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 云区任务统计接口
 */
@Controller
@Log4j2
@RequestMapping("/ecp")
@CrossOrigin(origins = "*")
public class TaskController {

    private final Errors errors;
    private final TaskServices taskServices;

    private final PbmUserPartyOathServiceImpl pbmUserPartyOathService;
    private final PbmNewsStudyServiceImpl pbmNewsStudyServiceImpl;

    public TaskController(Errors errors,
                          TaskServices taskServices,
                          PbmUserPartyOathServiceImpl pbmUserPartyOathService, PbmNewsStudyServiceImpl pbmNewsStudyServiceImpl) {
        this.errors = errors;
        this.taskServices = taskServices;
        this.pbmUserPartyOathService = pbmUserPartyOathService;
        this.pbmNewsStudyServiceImpl = pbmNewsStudyServiceImpl;
    }

    @HttpMonitorLogger
    @GetMapping("/task/task-sta")
    @ApiOperation("我的云上任务汇总统计")
    public ResponseEntity<Result<?>> taskSta(@RequestHeader HttpHeaders headers,
                                             @RequestParam(value = "unit_id",required = false,defaultValue = "869") Long unitId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(taskServices.taskSta(sysHeader,unitId), errors), HttpStatus.OK);
    }

    /**
     * 1.已完成 2.待完成
     * @param headers
     * @param type
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/task/task-record-list")
    @ApiOperation("我的云上任务列表")
    public ResponseEntity<Result<?>> taskRecordList(@RequestHeader HttpHeaders headers,
                                             @RequestParam(value = "type",defaultValue = "1") Integer type) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(taskServices.taskRecordList(sysHeader,type), errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/unit-record")
    @ApiOperation("融合云区动态按单位统计")
    public ResponseEntity<Result<?>> unitRecord(@RequestHeader HttpHeaders headers,
                                                @RequestParam(value = "unit_id",required = false) Long unitId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(taskServices.unitRecord(sysHeader,unitId), errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/all-record")
    @ApiOperation("融合云区动态按全市系统统计")
    public ResponseEntity<Result<?>> allRecord(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(taskServices.allRecord(sysHeader), errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/revisit_sta")
    @ApiOperation("入党誓词与志愿书统计天数计算")
    public ResponseEntity<Result<?>> revisitSta(@RequestHeader HttpHeaders headers,
                                                @RequestParam(value = "startTime") String startTime,
                                                @RequestParam(value = "endTime") String endTime) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Object userWorkData = pbmUserPartyOathService.getUserWorkData(sysHeader.getUserId(),
                startTime, endTime, null, sysHeader.getRegionId(), new WorkItemEntity());
        return new ResponseEntity<>(new Result<>(userWorkData, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/news-sta")
    @ApiOperation("看新闻次数计算")
    public ResponseEntity<Result<?>> newsSta(@RequestHeader HttpHeaders headers,
                                                @RequestParam(value = "startTime") String startTime,
                                                @RequestParam(value = "endTime") String endTime) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Object userWorkData = pbmNewsStudyServiceImpl.getUserWorkData(sysHeader.getUserId(),
                startTime, endTime, null, sysHeader.getRegionId(), new WorkItemEntity());
        return new ResponseEntity<>(new Result<>(userWorkData, errors), HttpStatus.OK);
    }
}
