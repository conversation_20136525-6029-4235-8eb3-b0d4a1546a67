package com.goodsogood.ows.controller;


import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.bean.TimeBean;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.sas.SasOrgLifeForm;
import com.goodsogood.ows.service.sas.StatisticalOrgLifeViewService;
import com.goodsogood.ows.utils.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 组织生活一览表统计
 *
 * <AUTHOR>
 * @create 2019年7月23日 15:14:05
 **/
@RestController
@RequestMapping("/sas/org-life-view")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "/sas/org-life-view", tags = {"组织生活一览表统计"})
@Validated
public class StatisticalOrgLifeViewController {

    private final StatisticalOrgLifeViewService statisticalOrgLifeViewService;
    private final Errors errors;

    @Autowired
    public StatisticalOrgLifeViewController(StatisticalOrgLifeViewService statisticalOrgLifeViewService, Errors errors) {
        this.statisticalOrgLifeViewService = statisticalOrgLifeViewService;
        this.errors = errors;
    }


    /**
     * 分页查询党组织生活统计列表
     *
     * @param timeType       时间查询类型
     * @param year       年份
     * @param time 查询时间
     * @param pageNo   页码
     * @param pageSize   每页条数
     */
    @HttpMonitorLogger
    @GetMapping("/list")
    @ApiOperation("分页查询党组织生活统计列表")
    public ResponseEntity<Result<SasOrgLifeForm>> list(@RequestHeader HttpHeaders headers,
                                                       @ApiParam(value = "时间查询类型：1-按年，2-按半年，3-按季度，4-按月份")
                                                       @Range(min = 1, max = 4, message = "时间查询类型：1-按年，2-按半年，3-按季度，4-按月份")
                                                       @RequestParam(value = "time_type") Integer timeType,
                                                       @ApiParam(value = "查询年份")
                                                       @RequestParam(value = "year") Integer year,
                                                       @ApiParam(value = "查询时间:\n" +
                                                               "time_type=2时 ，代表上半年或下半年：1-上半年，2-下半年，\n" +
                                                               "time_type=3时，代表季度 ：1-第一季度，2-第二季度，3-第三季度，4-第四季度\n" +
                                                               "time_type=4时，代表月份 ： 1-12")
                                                       @RequestParam(value = "time", required = false) Integer time,
                                                       @ApiParam(value = "页码")
                                                       @RequestParam(value = "page_no", required = false) Integer pageNo,
                                                       @ApiParam(value = "每页条数")
                                                       @RequestParam(value = "page_size", required = false) Integer pageSize) {
        log.info("分页查询党组织生活统计列表. StatisticalOrgLifeViewController.list timeType:{}, year:{},time:{}", timeType, year, time);
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Long regionId=sysHeader.getRegionId();
        // 查询统计信息
        SasOrgLifeForm sasOrgLifeViewForm = statisticalOrgLifeViewService
                .sasOrgLifeView(new TimeBean(year, timeType, time), PageUtils.page(pageNo, pageSize),regionId);
        Result<SasOrgLifeForm> result = new Result<>(sasOrgLifeViewForm, errors);
        // 设置分页信息
        result.setPageNum(sasOrgLifeViewForm.getListDetail().getPageNum());
        result.setPages(sasOrgLifeViewForm.getListDetail().getPages());
        result.setPageSize(sasOrgLifeViewForm.getListDetail().getPageSize());
        result.setTotal(sasOrgLifeViewForm.getListDetail().getTotal());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 党组织生活统计一览导出
     *
     * @param timeType 时间查询类型
     * @param year     年份
     * @param time     查询时间
     */
    @HttpMonitorLogger
    @GetMapping("/export")
    @ApiOperation("党组织生活统计一览导出")
    public void export(HttpServletResponse response, HttpServletRequest request, @RequestHeader HttpHeaders headers,
                                            @ApiParam(value = "时间查询类型：1-按年，2-按半年，3-按季度，4-按月份")
                                            @Range(min = 1, max = 4, message = "时间查询类型：1-按年，2-按半年，3-按季度，4-按月份")
                                            @RequestParam(value = "time_type") Integer timeType,
                                            @ApiParam(value = "查询年份")
                                            @RequestParam(value = "year") Integer year,
                                            @ApiParam(value = "查询时间:\n" +
                                                    "time_type=2时 ，代表上半年或下半年：1-上半年，2-下半年，\n" +
                                                    "time_type=3时，代表季度 ：1-第一季度，2-第二季度，3-第三季度，4-第四季度\n" +
                                                    "time_type=4时，代表月份 ： 1-12")
                                            @RequestParam(value = "time", required = false) Integer time) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Long regionId=sysHeader.getRegionId();
        log.info("党组织生活统计一览导出.StatisticalOrgLifeViewController.export timeType{}, year:{},time:{}", timeType, year, time);
        statisticalOrgLifeViewService.export(response, request, new TimeBean(year, timeType, time),regionId);
//        Result<String> result = new Result<>("success", errors);
//        return new ResponseEntity<>(result, HttpStatus.OK);
    }

}
