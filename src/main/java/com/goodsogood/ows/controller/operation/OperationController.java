package com.goodsogood.ows.controller.operation;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.redisUtil.RedisLockUtil;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.operation.DataDisposeService;
import com.goodsogood.ows.service.operation.OperationService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;

/**
 * @ClassName : OperationController
 * <AUTHOR> tc
 * @Date: 2022/4/14 18:01
 * @Description : 系统运行报表功能
 */

@Controller
@Log4j2
@RequestMapping("/operation/system")
public class OperationController {
    private final Errors errors;
    private final OperationService operationService;
    private final RedisTemplate redisTemplate;
    private final DataDisposeService dataDisposeService;

    @Autowired
    public OperationController(Errors errors, OperationService operationService, RedisTemplate redisTemplate, DataDisposeService dataDisposeService) {
        this.errors = errors;
        this.operationService = operationService;
        this.redisTemplate = redisTemplate;
        this.dataDisposeService = dataDisposeService;
    }


    @HttpMonitorLogger
    @GetMapping("/overview")
    @ApiOperation("数据概览")
    public ResponseEntity<Result<?>> overview(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(operationService.overview(header), errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/visitRate/date")
    @ApiOperation("按日期统计访问率")
    public ResponseEntity<Result<?>> visitRateDate(@RequestHeader HttpHeaders headers,
                                                   @RequestParam(value = "start_time") String startTime,
                                                   @RequestParam(value = "end_time") String endTime) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(operationService.visitRateDate(header, startTime, endTime), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/visitRate/org")
    @ApiOperation("按组织统计访问率")
    public ResponseEntity<Result<?>> visitRateOrg(@RequestHeader HttpHeaders headers,
                                                  @RequestParam(value = "stats_date") String statsDate,
                                                  @RequestParam(value = "org_id", required = false) Long orgId) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(operationService.visitRateOrg(header, statsDate, orgId), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/visitDays/org")
    @ApiOperation("按组织统计人均访问天数")
    public ResponseEntity<Result<?>> visitDaysOrg(@RequestHeader HttpHeaders headers,
                                                  @RequestParam(value = "org_id", required = false) Long orgId) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(operationService.visitDaysOrg(header, orgId), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/visitDays/user")
    @ApiOperation("按用户统计累计访问天数")
    public ResponseEntity<Result<?>> visitDaysUser(@RequestHeader HttpHeaders headers,
                                                   @RequestParam(value = "type", required = false, defaultValue = "1") Integer type) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(operationService.visitDaysUser(header, type), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/visitNum")
    @ApiOperation("按时间段同级全市访问量")
    public ResponseEntity<Result<?>> visitNum(@RequestHeader HttpHeaders headers,
                                              @RequestParam(value = "start_time") String startTime,
                                              @RequestParam(value = "end_time") String endTime) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(operationService.visitNum(header, startTime, endTime), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/clickMenu/top")
    @ApiOperation("统计访问量最高的功能")
    public ResponseEntity<Result<?>> clickMenuTop(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(operationService.clickMenuTop(header), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/clickMenu")
    @ApiOperation("功能访问情况")
    public ResponseEntity<Result<?>> clickMenu(@RequestHeader HttpHeaders headers,
                                               @RequestParam(value = "start_time") String startTime,
                                               @RequestParam(value = "end_time") String endTime) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(operationService.clickMenu(header, startTime, endTime), errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/test/dataDispose")
    @ApiOperation("系统运行报表定时记录数据任务手动调用")
    public ResponseEntity<Result<?>> testDataDispose(@RequestHeader HttpHeaders headers,
                                                     @RequestParam(value = "stats_date") String statsDate,
                                                     @RequestParam(value = "method_name") String methodName) {
        String logTxt = "<系统运行报表定时记录数据任务>手动执行 ";
        String requestId = UUID.randomUUID().toString();
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long regionId = header.getRegionId();
        try {
            log.debug(logTxt + "开始执行...  regionId={} statsDate={} methodName={}", regionId, statsDate, methodName);
            //获取分布式锁
            boolean lock = RedisLockUtil.tryGetDistributedLock(redisTemplate, "SAS_OPERATION_SYSTEM_TASK_LOCK_TEST_KEY", requestId, 120 * 1000);
            if (lock) {
                long l = System.currentTimeMillis();
                log.debug(logTxt + "获得锁，开始执行");
                if (methodName.equals("all")) {
                    CompletableFuture<String> cf1 = CompletableFuture.supplyAsync(() -> dataDisposeService.findVisitRate(regionId, statsDate));
                    CompletableFuture<String> cf2 = CompletableFuture.supplyAsync(() -> dataDisposeService.findVisitDays(regionId, statsDate));
                    CompletableFuture<String> cf3 = CompletableFuture.supplyAsync(() -> dataDisposeService.findClickMenu(regionId, statsDate));
                    CompletableFuture<String> cf4 = CompletableFuture.supplyAsync(() -> dataDisposeService.findVisitNumByInterval(regionId, statsDate));
                    // 并行执行，阻塞main线程直到所有任务执行完成
                    Stream.of(cf1, cf2, cf3, cf4).parallel().map(CompletableFuture::join);
                } else {
                    switch (methodName) {
                        case "VisitRate":
                            dataDisposeService.findVisitRate(regionId, statsDate);
                            break;
                        case "VisitDays":
                            dataDisposeService.findVisitDays(regionId, statsDate);
                            break;
                        case "ClickMenu":
                            dataDisposeService.findClickMenu(regionId, statsDate);
                            break;
                        case "VisitNumByInterval":
                            dataDisposeService.findVisitNumByInterval(regionId, statsDate);
                            break;
                        default:
                            log.error("统计方法名不匹配！ regionId={} statsDate={} methodName={}", regionId, statsDate, methodName);
                            break;
                    }
                }
                long le = System.currentTimeMillis();
                log.debug(logTxt + "执行完成... 消耗时间(ms): {}", (le - l));
            } else {
                log.debug(logTxt + "未获得锁，无法执行！...");
            }
        } catch (Exception e) {
            log.error(logTxt + "报错! ", e);
        } finally {
            //解锁
            RedisLockUtil.releaseDistributedLock(redisTemplate, "SAS_OPERATION_SYSTEM_TASK_LOCK_TEST_KEY", requestId);
        }
        return new ResponseEntity<>(new Result<>("区县编号:" + regionId + " 统计日期:" + statsDate + " 统计数据:" + methodName, errors), HttpStatus.OK);
    }

}
