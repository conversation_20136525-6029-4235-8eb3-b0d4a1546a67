package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.sas.LeaderOrgMeetingFeeService;
import com.goodsogood.ows.service.sas.PartyFeeService;
import com.goodsogood.ows.service.sas.StatisticalUserOrgLifeService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.time.StopWatch;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.Date;

/**
 * 定制化统计接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sas/init")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class InitDataController {

    private final Errors errors;
    private final PartyFeeService partyFeeService;
    private final LeaderOrgMeetingFeeService meetingFeeService;
    private final StatisticalUserOrgLifeService statisticalUserOrgLifeService;

    @Autowired
    public InitDataController(Errors errors, PartyFeeService partyFeeService, LeaderOrgMeetingFeeService meetingFeeService, StatisticalUserOrgLifeService statisticalUserOrgLifeService) {
        this.errors = errors;
        this.partyFeeService = partyFeeService;
        this.meetingFeeService = meetingFeeService;
        this.statisticalUserOrgLifeService = statisticalUserOrgLifeService;
    }

    @HttpMonitorLogger
    @GetMapping("/partyPayInfo")
    @ApiOperation("初始化党费统计接口")
    public ResponseEntity<Result<?>> testOrgPay(@RequestParam("query_date") String queryDate,
                                                @RequestParam(value = "page_size", required = false, defaultValue = "0") int size,
                                                @RequestParam(value = "is_one_month", required = false, defaultValue = "0") int isOneMonth,
                                                @RequestParam(value = "region_id", required = false) Long regionId) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            // 获取当前月份
            Date date = new Date();
            boolean flag = (isOneMonth != 0);
            this.partyFeeService.OrgPayMain(queryDate, date, flag, stopWatch, size, regionId);
        } catch (ParseException e) {
            log.error(e.getLocalizedMessage(), e);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/supplyPartyPayInfo")
    @ApiOperation("补录党费统计接口")
    public ResponseEntity<Result<?>> supplyPartyPayInfo(@RequestParam("query_date") String queryDate,
                                                        @RequestParam("org_id") Long orgId,
                                                        @RequestParam(value = "page_size", required = false, defaultValue = "0") int size,
                                                        @RequestParam(value = "is_one_month", required = false, defaultValue = "0") int isOneMonth,
                                                        @RequestParam(value = "region_id", required = false) Long regionId) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            // 获取当前月份
            Date date = new Date();
            boolean flag = (isOneMonth != 0);
            this.partyFeeService.OrgPayMain(queryDate, date, flag, stopWatch, size, regionId, orgId);
        } catch (ParseException e) {
            log.error(e.getLocalizedMessage(), e);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 初始化调用用户中心组织接口
     *
     * @param queryDate 1-全部时间（不包括当月）， 2-1年， 3-3个月， 4-1个月，null-6个月
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/leaderOrgMeeting")
    @ApiOperation("初始化调用用户中心组织接口")
    public ResponseEntity<Result<?>> leaderOrgMeeting(@RequestParam(value = "query_date", required = false) Long queryDate) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            // 获取当前月份
            Date date = new Date();
            this.meetingFeeService.leaderOrgMeetingFeeMain(date, stopWatch, queryDate);
        } catch (ParseException e) {
            log.debug("初始化调用用户中心组织接口" + e.getMessage(), e);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/sasUserOrgLifeCount")
    @ApiOperation("统计用户双重组织生活情况")
    public void sasUserOrgLifeCount(@RequestParam(value = "region_id") Long regionId) {
        statisticalUserOrgLifeService.sasUserOrgLifeCount(regionId, DateTime.now().toDate());
    }

}
