package com.goodsogood.ows.controller;


import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.tbcFusion.*;
import com.goodsogood.ows.service.tbcFusion.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-06-20
 **/
@RestController
@RequestMapping("/tbc/score")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class TbcScoreController {
    private final Errors errors;
    @Autowired
    public TbcScoreController(Errors errors, TbcScoreService tbcScoreService) {
        this.errors = errors;
        this.tbcScoreService = tbcScoreService;
    }

    private final TbcScoreService tbcScoreService;

    @HttpMonitorLogger
    @GetMapping("/rank/party")
    @ApiOperation("上月党建积分")
    public ResponseEntity<Result<?>> rankPartyLastMonth(@RequestHeader HttpHeaders headers,@RequestParam(value = "org_id", required = false) Long orgId,
                                                        @RequestParam(value = "user_id", required = false) Long userId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        if(orgId==null){
            orgId = sysHeader.getOid();
        }
        if(userId==null){
            userId = sysHeader.getUserId();
        }
        Long regionId = sysHeader.getRegionId();
        TbcScoreRankPartyVo re = tbcScoreService.findScoreRankParty(regionId,orgId,userId);

        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/rank/business")
    @ApiOperation("上月业务积分")
    public ResponseEntity<Result<?>> rankBusinessLastMonth(@RequestHeader HttpHeaders headers,@RequestParam(value = "org_id", required = false) Long orgId,
                                                           @RequestParam(value = "user_id", required = false) Long userId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        if(orgId==null){
            orgId = sysHeader.getOid();
        }
        if(userId==null){
            userId = sysHeader.getUserId();
        }
        Long regionId = sysHeader.getRegionId();
        TbcScoreRankBusinessVo re = tbcScoreService.findScoreRankBusiness(regionId,orgId,userId);

        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("test/rank/party")
    @ApiOperation("党建积分手动调用类")
    public ResponseEntity<Result<?>> testRankParty(@RequestHeader HttpHeaders headers,@RequestParam(value = "region_id") Long regionId,@RequestParam(value = "org_id") Long orgId,@RequestParam(value = "year_month") String yearMonth) {
        log.debug("测试日志:进入党建积分手动调用类");
        Map<Long,TbcScoreRankPartyVo> re = tbcScoreService.createPartyRank(regionId,orgId,yearMonth);
        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("test/rank/business")
    @ApiOperation("业务积分手动调用类")
    public ResponseEntity<Result<?>> testRankBusiness(@RequestHeader HttpHeaders headers,@RequestParam(value = "region_id") Long regionId,@RequestParam(value = "org_id") Long orgId,@RequestParam(value = "year_month") String yearMonth) {
        Map<Long,TbcScoreRankBusinessVo> re = tbcScoreService.createBusinessRank(regionId,orgId,yearMonth);
        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }

}