package com.goodsogood.ows.controller

import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.tbcFusion.FusionScreenService
import io.swagger.annotations.Api
import org.joda.time.DateTime
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 *
 * <AUTHOR>
 * @createTime 2023年04月06日 10:56:00
 */
@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "党业融合大屏", tags = ["党业融合大屏"])
@RequestMapping("/fusion-screen")
class FusionScreenController @Autowired constructor(
    private val errors: Errors,
    private val fusionScreenService: FusionScreenService
) {

    @GetMapping("/two-index")
    fun getScreenTwoIndex(
        @RequestParam(value = "year", required = false) year: Int? = null,
        @RequestParam(value = "month", required = false) month: Int? = null
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.fusionScreenService.getScreenTwoIndex(year, month), errors
            ), HttpStatus.OK
        )
    }

    @GetMapping("/three-indicators")
    fun getScreenThreeIndicators(
        @RequestParam(value = "year", required = false) year: Int? = null,
        @RequestParam(value = "month", required = false) month: Int? = null
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.fusionScreenService.getScreenThreeIndicators(year, month), errors
            ), HttpStatus.OK
        )
    }

    @GetMapping("/four-fusion")
    fun getFourFusion(
        @RequestParam(value = "year", required = false) year: Int? = null,
        @RequestParam(value = "month", required = false) month: Int? = null
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.fusionScreenService.getFourFusion(year, month), errors
            ), HttpStatus.OK
        )
    }
}