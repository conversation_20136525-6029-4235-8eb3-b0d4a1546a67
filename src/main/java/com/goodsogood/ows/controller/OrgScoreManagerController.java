package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.ScoreAddScheduler;
import com.goodsogood.ows.component.ScoreManagerScheduler;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.scoreManager.ScoreManagerAddVo;
import com.goodsogood.ows.service.scoreManager.OrgScoreManagerService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 组织积分管理
 *
 * <AUTHOR> ruoyu
 * @date : 2021/12/11
 */
@RestController
@RequestMapping("/ows-score")
@Log4j2
public class OrgScoreManagerController {

    private final Errors errors;
    private final OrgScoreManagerService orgScoreManagerService;
    private final ScoreManagerScheduler scoreManagerScheduler;
    private final ScoreAddScheduler scoreAddScheduler;

    @Autowired
    public OrgScoreManagerController(Errors errors,
                                     OrgScoreManagerService orgScoreManagerService,
                                     ScoreManagerScheduler scoreManagerScheduler, ScoreAddScheduler scoreAddScheduler) {
        this.errors = errors;
        this.orgScoreManagerService = orgScoreManagerService;
        this.scoreManagerScheduler = scoreManagerScheduler;
        this.scoreAddScheduler = scoreAddScheduler;
    }

    @HttpMonitorLogger
    @PostMapping("/execute")
    @ApiOperation("手动调用接口")
    public ResponseEntity<Result<?>> execute(@RequestParam("time") String executeTime,
                                             @RequestParam("regionId") Long regionId,
                                             @RequestBody Map<Integer, List<Long>> map,
                                             @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        orgScoreManagerService.execute(executeTime, regionId, map, header);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/execute-score")
    @ApiOperation("手动调用接口-积分发放")
    public ResponseEntity<Result<?>> executeScore() {
        scoreAddScheduler.execute();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/executeAll")
    @ApiOperation("直接调用定时任务")
    public ResponseEntity<Result<?>> executeAll() {
        scoreManagerScheduler.execute();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/add-score")
    @ApiOperation("其他业务并入到积分发放业务时调用")
    public ResponseEntity<Result<?>> addScore(@RequestBody ScoreManagerAddVo scoreManagerAddVo) {
        orgScoreManagerService.addScore(scoreManagerAddVo);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }
}
