package com.goodsogood.ows.controller;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.dataworks.TbcBusinessService;
import com.goodsogood.ows.service.score.ScoreService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description 党业融合业务指标处理
 * @Author: mengting
 * @Date: 2022/4/13 11:24
 */
@RestController
@RequestMapping("/business-index")
@Log4j2
public class PartyBusinessController {
    private final TbcBusinessService tbcBusinessService;
    private final ScoreService scoreService;
    private final Errors errors;

    @Autowired
    public PartyBusinessController(TbcBusinessService tbcBusinessService, ScoreService scoreService, Errors errors) {
        this.tbcBusinessService = tbcBusinessService;
        this.scoreService = scoreService;
        this.errors = errors;
    }


    @GetMapping("/month-score")
    @ApiOperation("手动计算得分接口-月指标")
    public ResponseEntity<Result<Boolean>> calMonthScore(@RequestParam("region_id") Long regionId, @RequestParam("date_month") Integer dateMonth) {
        tbcBusinessService.calMonthScore(regionId, dateMonth);
        return new ResponseEntity<Result<Boolean>>(new Result<>(true,errors),HttpStatus.OK);

    }

    @GetMapping("/week-score")
    @ApiOperation("手动计算得分接口-周指标")
    public ResponseEntity<Result<Boolean>> calWeekScore(@RequestParam("region_id") Long regionId,
                                                        @RequestParam("week_year") Integer weekYear,
                                                        @RequestParam("year") Integer year) {
        tbcBusinessService.calWeekScore(regionId, weekYear,year);
        return new ResponseEntity<Result<Boolean>>(new Result<>(true,errors),HttpStatus.OK);
    }


    @GetMapping("/add-score-by-id")
    @ApiOperation("手动发送至积分中心-测试：可根据result_id跑分")
    public ResponseEntity<Result<Boolean>> sendToScoreCenterByIds(@RequestParam("result_ids") List<String> resultIds) {
        tbcBusinessService.sendScoreCenterByIds(resultIds);
        return new ResponseEntity<Result<Boolean>>(new Result<>(true,errors),HttpStatus.OK);
    }


    @GetMapping("/add-score-by-month")
    @ApiOperation("手动发送至积分中心-补数据")
    public ResponseEntity<Result<Boolean>> sendToScoreCenterByDate(@RequestParam("date_month") Integer dateMonth,
                                        @RequestParam(value = "year", required = false) Integer year,
                                        @RequestParam(value = "rule_ids", required = false) List<Integer> ruleIds) {
        tbcBusinessService.sendScoreCenterByDate(dateMonth, year, ruleIds);
        return new ResponseEntity<Result<Boolean>>(new Result<>(true,errors),HttpStatus.OK);
    }



    @GetMapping("/test-week")
    @ApiOperation("算分+发积分到积分中心全量指标手动跑数测试")
    public ResponseEntity<Result<Boolean>> testWeek() {
        tbcBusinessService.calBusinessWeekScore();
        return new ResponseEntity<Result<Boolean>>(new Result<>(true,errors),HttpStatus.OK);
    }

    @GetMapping("/test-month")
    @ApiOperation("算分+发积分到积分中心全量指标手动跑数测试")
    public ResponseEntity<Result<Boolean>> testMonth() {
        tbcBusinessService.calBusinessMonthScore();
        return new ResponseEntity<Result<Boolean>>(new Result<>(true,errors),HttpStatus.OK);
    }


    @GetMapping("/phone-secret-user")
    @ApiOperation("加密手机号-人员基本信息")
    public ResponseEntity<Result<Boolean>> phoneSecretPerson() throws Exception {
        log.error("mt更新营销人员基本信息11");
        tbcBusinessService.phoneSecretPerson();
        log.error("mt更新营销人员基本信息12");
        return new ResponseEntity<Result<Boolean>>(new Result<>(true,errors),HttpStatus.OK);
    }

    @GetMapping("/test/cal_user_business")
    @ApiOperation("手动计算人员业务得分接口")
    /**
     * dateMonth   格式 整数  YYYYmm
     */
    public ResponseEntity<Result<Boolean>> calUserBusinessMonthScore(@RequestParam("region_id") Long regionId, @RequestParam("date_month") Integer dateMonth) {
        scoreService.calUserBusinessScore(regionId, dateMonth);
        return new ResponseEntity<Result<Boolean>>(new Result<>(true,errors),HttpStatus.OK);

    }

    @GetMapping("/test/cal_org_business")
    @ApiOperation("手动计算机构业务得分接口")
    /**
     * dateMonth   格式 整数  YYYYmm
     */
    public ResponseEntity<Result<Boolean>> calOrgBusinessMonthScore(@RequestParam("region_id") Long regionId, @RequestParam("date_month") Integer dateMonth) {
        scoreService.calOrgBusinessScore(regionId, dateMonth);
        return new ResponseEntity<Result<Boolean>>(new Result<>(true,errors),HttpStatus.OK);

    }
}
