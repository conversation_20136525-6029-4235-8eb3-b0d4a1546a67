package com.goodsogood.ows.controller.datav

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.datav.base.*
import com.goodsogood.ows.service.DataVService
import com.goodsogood.ows.service.overview.OverviewService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.slf4j.LoggerFactory
import org.springframework.cache.annotation.Cacheable
import org.springframework.http.HttpHeaders
import org.springframework.web.bind.annotation.*
import javax.servlet.http.HttpServletRequest

/**
 * <AUTHOR>
 * @date 2024/2/1
 * @description class PartyMemberController
 */
@Api(value = "数智党建可视化大屏-党员管理", tags = ["数智党建可视化大屏"])
@RestController
@RequestMapping("/datav/party-member")
@CrossOrigin(originPatterns = ["*"], maxAge = 3600)
class PartyMemberController(
    val dataVService: DataVService,
    val overviewService: OverviewService,
) {
    private val log = LoggerFactory.getLogger(PartyMemberController::class.java)

    /**
     * 获取大屏数据
     * @param unitId 单位id 大屏传入 可以为空，不传入默认使用顶级组织
     * @param adcode 行政区划 大屏传入 可以为空，不传入默认使用全重庆数据
     * @return 从上到下，从左到右
     */
    @HttpMonitorLogger
    @GetMapping("v1/show")
    @ApiOperation("党员管理")
    @Cacheable(
        value = ["datav-cache"],
        key = "#root.method.name + '_' + #unitId + '_' + #adcode + '_' + #request.getRequestURI()"
    )
    fun show(
        @RequestParam("unit_id", required = false) unitId: Long?,
        @RequestParam("adcode", required = false) adcode: String?,
        @RequestHeader headers: HttpHeaders,
        request: HttpServletRequest,
    ): List<List<Any>> {
        val myHeaders = HeaderHelper.buildMyHeader(headers)
        val pair = dataVService.getOrgIdAndUnitId(unitId, adcode, myHeaders)
        val unid = pair.first
        val orgId = pair.second
        log.debug(
            "获取党员管理大屏数据->unitId:{},unid:{},adcode:{},orgIds:{},orgId:{}",
            unitId,
            unid,
            adcode,
            null,
            orgId
        )
        // 如果没有组织id，返回空
        if (orgId == null || unid == null) {
            return emptyList()
        }
        headers[HeaderHelper.OPERATOR_OID] = listOf(orgId.toString())
        val list = mutableListOf<List<Any>>()
        // ------------左边-----------------
        val partyMember = dataVService.findPartyMember(unid, orgId)
        val total = (partyMember["总人数"] as Long?) ?: 1
        val party = (partyMember["党员"] as Long?) ?: 0
        val other = (partyMember["非党员"] as Long?) ?: 0
        var index = 0
        // 0.水滴图 - 党员人数 / 党员 + 非党员 人数
        list.add(
            listOf(
                WaterWave(
                    party / (
                            if (total <= 0) {
                                1.0
                            } else {
                                total.toDouble()
                            })
                ), Type(
                    typeName = "党员水滴图",
                    typeIndex = index++
                )
            )
        )
        // 1.党员翻牌器
        list.add(
            listOf(
                CardFlop("党员", party), Type(
                    typeName = "党员翻牌器",
                    typeIndex = index++
                )
            )
        )
        // 2.非党员翻牌器
        list.add(
            listOf(
                CardFlop("非党员", other), Type(
                    typeName = "非党员翻牌器",
                    typeIndex = index++
                )
            )
        )
        // --------------------------------
        val typeData = this.overviewService.orgOverviewDevelopPartyMembersVo(headers)
        // 3.入党申请人
        list.add(
            listOf(
                CardFlop(
                    "入党申请人",
                    typeData.listKeyValue.find { it.name == "入党申请人" }?.value?.toLong() ?: 0L
                ), Type(
                    typeName = "入党申请人翻牌器",
                    typeIndex = index++
                )
            )
        )
        // 4.入党积极分子
        list.add(
            listOf(
                CardFlop(
                    "入党积极分子",
                    typeData.listKeyValue.find { it.name == "入党积极分子" }?.value?.toLong() ?: 0L
                ), Type(
                    typeName = "入党积极分子翻牌器",
                    typeIndex = index++
                )
            )
        )
        // 5.发展对象
        list.add(
            listOf(
                CardFlop(
                    "发展对象",
                    typeData.listKeyValue.find { it.name == "发展对象" }?.value?.toLong() ?: 0L
                ), Type(
                    typeName = "发展对象翻牌器",
                    typeIndex = index++
                )
            )
        )
        // 6.预备党员
        list.add(
            listOf(
                CardFlop(
                    "预备党员",
                    typeData.listKeyValue.find { it.name == "预备党员" }?.value?.toLong() ?: 0L
                ), Type(
                    typeName = "预备党员翻牌器",
                    typeIndex = index++
                )
            )
        )
        // --------------------------------
        val genderData = this.dataVService.partyMemberGender(orgId)
        // 7.男性 - 翻牌器
        list.add(
            listOf(
                CardFlop("男性", genderData.find { it.name == "男" }?.value?.toLong() ?: 0L), Type(
                    typeName = "男性翻牌器",
                    typeIndex = index++
                )
            )
        )
        // 8.女性 - 翻牌器
        list.add(
            listOf(
                CardFlop("女性", genderData.find { it.name == "女" }?.value?.toLong() ?: 0L), Type(
                    typeName = "女性翻牌器",
                    typeIndex = index++
                )
            )
        )
        // 9.男性-女性 - 进度条 // 进度是男性，剩下的是女性
        list.add(
            listOf(
                ProgressBar(
                    genderData.find { it.name == "男" }?.value?.toLong() ?: 0L,
                    genderData.sumOf {
                        if (it.name == "女" || it.name == "男") {
                            it.value
                        } else {
                            0
                        }
                    }.toLong()
                ), Type(
                    typeName = "男性-女性进度条",
                    typeIndex = index++
                )
            )
        )
        // --------------------------------
        // 10.年龄分布 - 柱状图 - 党员 / 党务干部
        val ageData =
            this.overviewService.orgOverviewAgeDistributedVo(headers).listKeyValue.filter { it.name != "未知" }
        val leaderAgeData =
            this.dataVService.userOrgPeriodMemberAgeDistributed(orgId).filter { it.first != "未知" }
        list.add(
            listOf(
                ageData.map {
                    Line(colorField = "党员", x = it.name, y = it.value.toDouble())
                }.toMutableList().also {
                    it.addAll(
                        leaderAgeData.map { kv ->
                            Line(colorField = "党务干部", x = kv.first, y = kv.second.toDouble())
                        })
                }, Type(
                    typeName = "年龄分布柱状图",
                    typeIndex = index++
                )
            )
        )
        // 11.党龄 - 柱状图 - 党员
        val partyAgeData = this.overviewService.orgOverviewPartyAgeDistributedVo(headers).listKeyValue
        list.add(
            listOf(
                partyAgeData.map {
                    Line(colorField = "党龄", x = it.name, y = it.value.toDouble())
                }, Type(
                    typeName = "党龄分布柱状图",
                    typeIndex = index++
                )
            )
        )
        // --------------------------------
        // 12.学历 - 雷达图
        val educationData = this.dataVService.education(orgId, null)
        addRaDar(list, educationData, Type(typeName = "学历雷达图", typeIndex = index++))
        // 13.序列 - 雷达图
        val sequenceData = this.dataVService.sequence(orgId, null)
        addRaDar(list, sequenceData, Type(typeName = "序列雷达图", typeIndex = index++))
        // --------------------------------
        // ------------中间-----------------
        // 14.先锋指数左侧数字 - 翻牌器
        list.add(
            listOf(
                CardFlop("先锋指数堡垒指数平均值", 0),
                Type(typeName = "先锋指数（左）翻牌器", typeIndex = index++)
            )
        )
        // 15.先锋指数最高 - 翻牌器
        list.add(listOf(CardFlop("最高", 0), Type(typeName = "先锋指数最高翻牌器", typeIndex = index++)))
        // 16.先锋指数最低 - 翻牌器
        list.add(listOf(CardFlop("最低", 0), Type(typeName = "先锋指数最低翻牌器", typeIndex = index++)))
        // 17.党建-平均 - 翻牌器
        list.add(listOf(CardFlop("平均", 0), Type(typeName = "党建-平均翻牌器", typeIndex = index++)))
        // 18.党建-最高 - 翻牌器
        list.add(listOf(CardFlop("最高", 0), Type(typeName = "党建-最高翻牌器", typeIndex = index++)))
        // 19.党建-最低 - 翻牌器
        list.add(listOf(CardFlop("最低", 0), Type(typeName = "党建-最低翻牌器", typeIndex = index++)))
        // 20.业务-平均 - 翻牌器
        list.add(listOf(CardFlop("平均", 0), Type(typeName = "业务-平均翻牌器", typeIndex = index++)))
        // 21.业务-最高 - 翻牌器
        list.add(listOf(CardFlop("最高", 0), Type(typeName = "业务-最高翻牌器", typeIndex = index++)))
        // 22.业务-最低 - 翻牌器
        list.add(listOf(CardFlop("最低", 0), Type(typeName = "业务-最低翻牌器", typeIndex = index++)))
        // 23.创新-平均 - 翻牌器
        list.add(listOf(CardFlop("平均", 0), Type(typeName = "创新-平均翻牌器", typeIndex = index++)))
        // 24.创新-最高 - 翻牌器
        list.add(listOf(CardFlop("最高", 0), Type(typeName = "创新-最高翻牌器", typeIndex = index++)))
        // 25.创新-最低 - 翻牌器
        list.add(listOf(CardFlop("最低", 0), Type(typeName = "创新-最低翻牌器", typeIndex = index++)))
        // --------------------------------
        // ------------右边-----------------
        val ppmdStdVo = this.overviewService.getOrgLifeOverviewPpmd(headers)
        val psv = this.dataVService.getPpmdPayRate(orgId)
        // 26.党费本月 - 翻牌器
        list.add(
            listOf(
                CardFlop("本月已交纳党费（元）", (ppmdStdVo.totalNum ?: 0).toLong()),
                Type(typeName = "党费本月翻牌器", typeIndex = index++)
            )
        )
        // 27.完成率 - 环图
        list.add(
            listOf(
                Pie(type = "已完成", value = ppmdStdVo.payNum),
                Pie(type = "未完成", value = ppmdStdVo.noPayNum),
                Type(typeName = "党费完成率环图", typeIndex = index++)
            )
        )
        // 28.走势图 - 本月 / 上月
        list.add(
            listOf(
                mutableListOf<Line>().also {
                    if (psv.listSeries?.size == 2) {
                        it.addAll(
                            psv.listSeries[0].data.mapIndexed { index, d ->
                                Line(colorField = "本月", x = index.toString(), y = d)
                            }
                        )
                        it.addAll(
                            psv.listSeries[1].data.mapIndexed { index, d ->
                                Line(colorField = "上月", x = index.toString(), y = d)
                            }
                        )
                    }
                }, Type(typeName = "党费走势图", typeIndex = index++)
            )
        )
        // --------------------------------
        val partyMemberDevelop = this.dataVService.partyMemberDevelop(unid, null, myHeaders.regionId ?: 19)
        // 29.发展党员总数 - 翻牌器
        list.add(
            listOf(
                CardFlop("发展党员总数", partyMemberDevelop["total"]?.toString()?.toLong() ?: 0L),
                Type(typeName = "发展党员总数翻牌器", typeIndex = index++)
            )
        )
        // 30.连续2年未发展党员单位 - 翻牌器
        list.add(
            listOf(
                CardFlop("连续2年未发展党员单位", partyMemberDevelop["noDevelop"]?.toString()?.toLong() ?: 0L),
                Type(typeName = "连续2年未发展党员单位翻牌器", typeIndex = index++)
            )
        )
        // 31.发展阶段 - 序列 - 堆积柱状图
        list.add(
            listOf(
                mutableListOf<HoriBar>().also {
                    it.addAll(
                        (partyMemberDevelop["data"] as List<*>).map { pair ->
                            val data = pair as Pair<*, *>
                            val (colorField, y) = (data.first as String).split("-")
                            val x = data.second as Double
                            HoriBar(colorField = colorField, x = x, y = y)
                        }
                    )
                }, Type(typeName = "发展阶段堆积柱状图", typeIndex = index++)
            )
        )
        // --------------------------------
        val promiseData = this.dataVService.partyMemberPromise(unid)
        // 32.承诺践诺 - 翻牌器
        promiseData.find { it.first == "承诺践诺" }?.let {
            list.add(
                listOf(
                    CardFlop("承诺践诺", it.second),
                    Type(typeName = "承诺践诺翻牌器", typeIndex = index++)
                )
            )
        }
        // 33.责任区 - 翻牌器
        promiseData.find { it.first == "责任区" }?.let {
            list.add(listOf(CardFlop("责任区", it.second), Type(typeName = "责任区翻牌器", typeIndex = index++)))
        }
        // 34.志愿服务 - 翻牌器
        promiseData.find { it.first == "志愿服务" }?.let {
            list.add(
                listOf(
                    CardFlop("志愿服务", it.second),
                    Type(typeName = "志愿服务翻牌器", typeIndex = index++)
                )
            )
        }
        // 35.示范岗 - 翻牌器
        promiseData.find { it.first == "示范岗" }?.let {
            list.add(listOf(CardFlop("示范岗", it.second), Type(typeName = "示范岗翻牌器", typeIndex = index++)))
        }
        // 36.参与人次 - 翻牌器
        list.add(listOf(CardFlop("参与人次", 0), Type(typeName = "参与人次翻牌器", typeIndex = index++)))
        // 37.服务次数 - 翻牌器
        list.add(listOf(CardFlop("服务次数", 0), Type(typeName = "服务次数翻牌器", typeIndex = index++)))
        // 38.党员比例 - 翻牌器
        list.add(listOf(CardFlop("党员比例", 0), Type(typeName = "党员比例翻牌器", typeIndex = index++)))
        log.debug("index:{},size:{}", index - 1, list.size)
        // --------------------------------
        return list
    }

    private fun addRaDar(list: MutableList<List<Any>>, data: Map<String, List<Pair<String, Any>>>, type: Type) {
        list.add(
            listOf(
                mutableListOf<RaDar>().also { d ->
                    d.addAll(
                        data["partyMember"]?.map {
                            RaDar(colorField = "党员", t = it.first, r = it.second.toString().toDouble())
                        }?.filter { it.t != "未知" } ?: emptyList()
                    )
                    d.addAll(
                        data["partyMemberNew"]?.map {
                            RaDar(colorField = "新发展党员", t = it.first, r = it.second.toString().toDouble())
                        }?.filter { it.t != "未知" } ?: emptyList()
                    )
                    d.addAll(
                        data["partyLeader"]?.map {
                            RaDar(colorField = "党务干部", t = it.first, r = it.second.toString().toDouble())
                        }?.filter { it.t != "未知" } ?: emptyList()
                    )
                }, type
            )
        )
    }
}