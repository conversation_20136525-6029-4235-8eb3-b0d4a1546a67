package com.goodsogood.ows.controller.datav

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.datav.*
import com.goodsogood.ows.model.vo.datav.base.*
import com.goodsogood.ows.model.vo.fusion.FusionMapDetail
import com.goodsogood.ows.model.vo.tbcFusion.TbcSplashesForm
import com.goodsogood.ows.service.ecp.EcpStatisticsTaskService
import com.goodsogood.ows.service.overview.OverviewService
import com.goodsogood.ows.service.pbm.fusion.FusionDataService
import com.goodsogood.ows.service.tbcFusion.FusionScreenService
import com.goodsogood.ows.service.user.PbmService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.joda.time.DateTime
import org.slf4j.LoggerFactory
import org.springframework.cache.annotation.Cacheable
import org.springframework.http.HttpHeaders
import org.springframework.web.bind.annotation.*
import kotlin.math.roundToInt
import kotlin.math.roundToLong


/**
 * <AUTHOR>
 * @date 2023/3/29
 * @description class PbmDatavController
 */
@Api(value = "数智党建可视化大屏-党业融合", tags = ["数智党建可视化大屏"])
@RestController
@RequestMapping("/datav/pbm")
@CrossOrigin(originPatterns = ["*"], maxAge = 3600)
class PbmDatavController(
    val ecpStatisticsTaskService: EcpStatisticsTaskService,
    val overviewService: OverviewService,
    val pdmService: PbmService,
    val fusionScreenService: FusionScreenService,
    val fusionDataService: FusionDataService
) {
    private val log = LoggerFactory.getLogger(PbmDatavController::class.java)

    @HttpMonitorLogger
    @GetMapping("/v1/one")
    @ApiOperation("党业融合-一云区")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") //  接口缓存，需要配置manger和在定时器ClearCacheScheduler配置清理
    fun one(@RequestHeader headers: HttpHeaders): OneVO {
        val header = HeaderHelper.buildMyHeader(headers)
        val oneVO = OneVO()
        // 一云区相关数据
        val taskRage = this.ecpStatisticsTaskService.findCityTaskRate(header)
        // 云区任务数量
        val taskCount = this.ecpStatisticsTaskService.tbcAllEcpTaskCount()
        // 云区组织数量
        val cloudOrg = overviewService.totalEcpOrg()
        with(oneVO) {
            this.cardFlopTask.add(CardFlop(name = "云上任务", value = taskCount))
            this.cardFlopOrg.add(CardFlop(name = "云上组织", value = cloudOrg))
            this.cardFlopPeople.add(
                CardFlop(
                    name = "参与人数",
                    value = ((taskRage.partyNum ?: 0) + (taskRage.notPartyNum ?: 0)).toLong()
                )
            )
            this.cardFlopNum.add(CardFlop(name = "总人次", value = taskRage.peopleNum?.toLong() ?: 0L))
            this.pie = mutableListOf(
                Pie(type = "党员", value = taskRage.partyFinishNum ?: 0, x = "党员", y = taskRage.partyFinishNum ?: 0),
                Pie(
                    type = "非党员",
                    value = taskRage.notPartyFinish ?: 0,
                    x = "非党员",
                    y = taskRage.notPartyFinish ?: 0
                ),
            )
            this.cardFlopParty.add(
                CardFlop(
                    name = "党员",
                    value = taskRage.partyNum?.toLong() ?: 0L,
                    suffix = "人"
                )
            )
            this.cardFlopNotParty.add(
                CardFlop(
                    name = "非党员",
                    value = taskRage.notPartyNum?.toLong() ?: 0L,
                    suffix = "人"
                )
            )
        }
        return oneVO
    }

    @HttpMonitorLogger
    @GetMapping("/v1/two")
    @ApiOperation("党业融合-两指数")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") //  接口缓存，需要配置manger和在定时器ClearCacheScheduler配置清理
    fun two(@RequestHeader headers: HttpHeaders): TwoVO {
        val now = DateTime.now() // 上个月
        val two = this.fusionScreenService.getScreenTwoIndex(now.year, now.monthOfYear)
//        log.debug("党业融合-两指数->${now.year)},${now.monthOfYear}->$two")
        val twoVO = TwoVO()
        with(twoVO) {
            this.cardFlopAvg0.add(
                CardFlop(
                    name = "支部堡垒指数平均值",
                    value = two.orgFortress.avg.roundToLong()
                )
            )
            this.avg0 = two.orgFortress.avg.roundToInt()
            this.cardFlopMax0.add(CardFlop(name = "最高", value = two.orgFortress.max.roundToLong()))
            this.cardFlopMin0.add(CardFlop(name = "最低", value = two.orgFortress.min.roundToLong()))

            this.cardFlopAvg1.add(
                CardFlop(
                    name = "党员先锋指数平均值",
                    value = two.memberPioneer.avg.roundToLong()
                )
            )
            this.avg1 = two.memberPioneer.avg.roundToInt()
            this.cardFlopMax1.add(CardFlop(name = "最高", value = two.memberPioneer.max.roundToLong()))
            this.cardFlopMin1.add(CardFlop(name = "最低", value = two.memberPioneer.min.roundToLong()))
        }
        return twoVO
    }

    @HttpMonitorLogger
    @GetMapping("/v1/three")
    @ApiOperation("党业融合-三指标")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") //  接口缓存，需要配置manger和在定时器ClearCacheScheduler配置清理
    fun three(@RequestHeader headers: HttpHeaders): Array<ThreeVO> {
        val now = DateTime.now().minusMonths(1) // 上个月
        val three = this.fusionScreenService.getScreenThreeIndicators(now.year, now.monthOfYear)
        // 0 党支部  1 党员
        val threeVO = Array(2) { ThreeVO() }
        with(threeVO[0]) {
            // 党建
            this.cardFlopAvg0.add(CardFlop("平均", value = three.org.build.avg.roundToLong()))
            this.cardFlopMax0.add(CardFlop("最高", value = three.org.build.max.roundToLong()))
            this.cardFlopMin0.add(CardFlop("最低", value = three.org.build.min.roundToLong()))
            // 业务
            this.cardFlopAvg1.add(CardFlop("平均", value = three.org.business.avg.roundToLong()))
            this.cardFlopMax1.add(CardFlop("最高", value = three.org.business.max.roundToLong()))
            this.cardFlopMin1.add(CardFlop("最低", value = three.org.business.min.roundToLong()))
            // 创新
            this.cardFlopAvg2.add(CardFlop("平均", value = three.org.innovate.avg.roundToLong()))
            this.cardFlopMax2.add(CardFlop("最高", value = three.org.innovate.max.roundToLong()))
            this.cardFlopMin2.add(CardFlop("最低", value = three.org.innovate.min.roundToLong()))
        }
        with(threeVO[1]) {
            // 党建
            this.cardFlopAvg0.add(CardFlop("平均", value = three.user.build.avg.roundToLong()))
            this.cardFlopMax0.add(CardFlop("最高", value = three.user.build.max.roundToLong()))
            this.cardFlopMin0.add(CardFlop("最低", value = three.user.build.min.roundToLong()))
            // 业务
            this.cardFlopAvg1.add(CardFlop("平均", value = three.user.business.avg.roundToLong()))
            this.cardFlopMax1.add(CardFlop("最高", value = three.user.business.max.roundToLong()))
            this.cardFlopMin1.add(CardFlop("最低", value = three.user.business.min.roundToLong()))
            // 创新
            this.cardFlopAvg2.add(CardFlop("平均", value = three.user.innovate.avg.roundToLong()))
            this.cardFlopMax2.add(CardFlop("最高", value = three.user.innovate.max.roundToLong()))
            this.cardFlopMin2.add(CardFlop("最低", value = three.user.innovate.min.roundToLong()))
        }
        return threeVO
    }

    @HttpMonitorLogger
    @GetMapping("/v1/four")
    @ApiOperation("党业融合-四融合")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") //  接口缓存，需要配置manger和在定时器ClearCacheScheduler配置清理
    fun four(@RequestHeader headers: HttpHeaders): Array<FourVO> {
        val now = DateTime.now() // 本月
        val four = this.fusionScreenService.getFourFusion(now.year, now.monthOfYear)
        //0 目标融合、1 组织融合、2 工作融合、3 数据融合
        val fourVO = Array(4) { FourVO() }
        (0..3).forEach {
            val value = when (it) {
                0 -> "目标融合" to four.target
                1 -> "组织融合" to four.org
                2 -> "工作融合" to four.work
                3 -> "数据融合" to four.data
                else -> null
            }
            with(fourVO[it]) {
                this.genericTitle = listOf(GenericTitle(value = value?.first))
                this.cardFlopHeight = listOf(CardFlop(name = "最高得分", value = value?.second?.max?.toLong()))
                this.cardFlopAvg = listOf(CardFlop(name = "平均得分", value = value?.second?.avg?.roundToLong()))
            }
        }
        return fourVO
    }

    @HttpMonitorLogger
    @GetMapping("/v1/map-card-flop")
    @ApiOperation("党业融合-地图翻牌器")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") //  接口缓存，需要配置manger和在定时器ClearCacheScheduler配置清理
    fun mapCardFlop(@RequestHeader headers: HttpHeaders): Array<List<OtherCardFlop>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val now = DateTime.now().minusMonths(1) // 上个月
        val data = this.getMapData(now.year, now.monthOfYear, header).map { it.fusion }
        // 0 全市系统党业融合度、1 最高、2 最低
        return arrayOf(
            listOf(OtherCardFlop(name = "全市系统党业融合度", value = data.average(), suffix = "%")),
            listOf(OtherCardFlop(name = "最高", value = data.maxOrNull() ?: 0.0, suffix = "%")),
            listOf(OtherCardFlop(name = "最低", value = data.minOrNull() ?: 0.0, suffix = "%"))
        )
    }

    @HttpMonitorLogger
    @GetMapping("/v1/region-heat-map")
    @ApiOperation("党业融合-热力图")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") //  接口缓存，需要配置manger和在定时器ClearCacheScheduler配置清理
    fun heatMap(@RequestHeader headers: HttpHeaders): List<MapChart> {
        val header = HeaderHelper.buildMyHeader(headers)
        val now = DateTime.now().minusMonths(1) // 上个月
        val data = this.getMapData(now.year, now.monthOfYear, header)
        return data.map {
            MapChart(
                lat = it.latitude ?: 0.0,
                lng = it.longitude ?: 0.0,
                adcode = it.adcode,
                colorField = it.fusion.toString(),
                textureColorField = it.fusion.toString(),
                radiusField = it.fusion,
                textureShapeField = "A"
            )
        }
    }

    @HttpMonitorLogger
    @GetMapping("/v1/capsule")
    @ApiOperation("党业融合-党业融合度")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") //
    fun capsule(@RequestHeader headers: HttpHeaders): CapsuleVO {
        val header = HeaderHelper.buildMyHeader(headers)
        val now = DateTime.now().minusMonths(1) // 上个月
        val data = this.getMapData(now.year, now.monthOfYear, header)
        val (xAxisData, yAxisData) = data.map {
            (it.unitShortName ?: "无简称${(0..10).random()}") to it.fusion
        }.unzip()
//        log.debug("党业融合-党业融合度->${xAxisData},${yAxisData},${yAxisData.average()}")
        return CapsuleVO(
            yAxisData = yAxisData.map { String.format("%.2f", it) },
            xAxisData = xAxisData,
            threshold = if (yAxisData.isEmpty()) {
                "0.00"
            } else {
                String.format("%.2f", yAxisData.average())
            }
        )
    }

    @HttpMonitorLogger
    @GetMapping("/v1/org-splashes")
    @ApiOperation("党业融合-'党建+业务'融合质量（单位）")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") //
    fun unitFusionMass(
        @RequestHeader headers: HttpHeaders,
        // 月份 格式 "2222-12
        @RequestParam(required = false) calDate: String?
    ): TbcSplashesForm {
        val header = HeaderHelper.buildMyHeader(headers)
        // 由于历史遗留问题，这里找一个最后计算的融合度值
        val month = calDate ?: "2022-12"
        // 获取单位融合度
        var points = pdmService.findUnitScatterPlot(month, header)?.map {
            // x -> 党建 , y -> 业务
            listOf((it.chartData?.get(0) ?: 0).toDouble(), (it.chartData?.get(1) ?: 0).toDouble())
        } ?: listOf()
        log.debug("党业融合-'党建+业务'融合质量（单位）-> points -> {}", points)
        // 转换坐标体系
        val px = convertToRange(points.map { it[0] })
        val py = convertToRange(points.map { it[1] })
        // 重新组装成 List<List<Double>>
        points = px.zip(py).map { (px, py) -> listOf(px, py) }
        //拆分融合度为4个象限
        // 计算党建积分二哈业务积分的中位数
        val medianX = med(points.map { it[0] })
        val medianY = med(points.map { it[1] })

        // 划分四个象限
        val quadrants = Array(4) { mutableListOf<List<Double>>() }
        for (point in points) {
            val x = point[0]
            val y = point[1]
            when {
                x > medianX && y > medianY -> quadrants[0].add(point) // 第一象限
                x <= medianX && y > medianY -> quadrants[1].add(point) // 第二象限
                x <= medianX && y <= medianY -> quadrants[2].add(point) // 第三象限
                x > medianX && y < medianY -> quadrants[3].add(point) // 第四象限
            }
        }

        return TbcSplashesForm().also {
            it.color1 = quadrants[0]
            it.color2 = quadrants[1]
            it.color3 = quadrants[2]
            it.color4 = quadrants[3]
            it.medianX = medianX
            it.medianY = medianY
        }
    }

    /**
     *  计算中位数的函数
     *  @param list 数据点
     *  @return  中位数
     */
    private fun med(list: List<Double>) = list.sorted().let {
        if (it.size % 2 == 0)
            (it[it.size / 2] + it[(it.size - 1) / 2]) / 2
        else
            it[it.size / 2]
    }

    /**
     * 数组转换到区间 0 ~ 100
     * @param data 数组
     * @return 通过公式  zi = (xi − min (x)) / (max (x) − min (x)) * 100计算出来的分布
     */
    private fun convertToRange(data: List<Double>): List<Double> {
        val min = data.minOrNull() ?: return listOf() // 空数组就返回空
        val max = data.maxOrNull() ?: 0.0
        val normalized = mutableListOf<Double>()
        // 遍历原始数据normalized，应用公式 zi = (xi − min (x)) / (max (x) − min (x)) * 100，并把数据存入新数组
        for (i in data.indices) {
            normalized.add(i, (data[i] - min) / (max - min) * 100)
        }
        return normalized
    }

    /**
     * 获取地图数据的方法
     * @param year 年
     * @param month 月
     * @param header
     * @return map data
     */
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") //  接口缓存，需要配置manger和在定时器ClearCacheScheduler配置清理
    fun getMapData(year: Int, month: Int, header: HeaderHelper.SysHeader): List<FusionMapDetail> {
        return fusionDataService.getFusionMapDetail(year, month, header.regionId)
    }
}