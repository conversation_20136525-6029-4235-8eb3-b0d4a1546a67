package com.goodsogood.ows.controller.datav

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.datav.base.CardFlop
import com.goodsogood.ows.model.vo.datav.base.Line
import com.goodsogood.ows.model.vo.datav.base.Pie
import com.goodsogood.ows.model.vo.datav.base.Type
import com.goodsogood.ows.model.vo.meeting.MeetingOrgCommendPenalizeQueryVO
import com.goodsogood.ows.model.vo.overview.*
import com.goodsogood.ows.service.DataVService
import com.goodsogood.ows.service.overview.OverviewService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.slf4j.LoggerFactory
import org.springframework.cache.annotation.Cacheable
import org.springframework.http.HttpHeaders
import org.springframework.web.bind.annotation.*
import javax.servlet.http.HttpServletRequest

/**
 * <AUTHOR>
 * @date 2024/2/21
 * @description class OrgController
 */
@Api(value = "数智党建可视化大屏-组织管理", tags = ["数智党建可视化大屏"])
@RestController
@RequestMapping("/datav/org")
@CrossOrigin(originPatterns = ["*"], maxAge = 3600)
class OrgController(
    val dataVService: DataVService,
    val overviewService: OverviewService,
) {
    private val log = LoggerFactory.getLogger(OrgController::class.java)

    /**
     * 获取大屏数据
     * @param unitId 单位id 大屏传入 可以为空，不传入默认使用顶级组织
     * @param adcode 行政区划 大屏传入 可以为空，不传入默认使用全重庆数据
     * @return 从上到下，从左到右
     */
    @HttpMonitorLogger
    @GetMapping("v1/show")
    @ApiOperation("组织管理")
    @Cacheable(
        value = ["datav-cache"],
        key = "#root.method.name + '_' + #unitId + '_' + #adcode + '_' + #request.getRequestURI()"
    )
    fun show(
        @RequestParam("unit_id", required = false) unitId: Long?,
        @RequestParam("adcode", required = false) adcode: String?,
        @RequestHeader headers: HttpHeaders,
        request: HttpServletRequest,
    ): List<List<Any>> {
        val myHeaders = HeaderHelper.buildMyHeader(headers)
        val pair = dataVService.getOrgIdAndUnitId(unitId, adcode, myHeaders)
        val unid = pair.first // 实际单位id
        val orgId = pair.second
        log.debug(
            "获取组织管理大屏数据->unitId:{},unid:{},adcode:{},orgIds:{},orgId:{}",
            unitId,
            unid,
            adcode,
            null,
            orgId
        )
        // 如果没有组织id，返回空
        if (orgId == null || unid == null) {
            return emptyList()
        }
        headers[HeaderHelper.OPERATOR_OID] = listOf(orgId.toString())
        val list = mutableListOf<List<Any>>()
        var index = 0
        //--------- 左边 -----------
        //--------- 组织概况 -------------
        val vo = overviewService.orgOverviewOrg(headers)?.body?.data as OverviewBaseVo?
        // 全市返回的是： type = 1 ，overviewStandardVo，
        // 党委党支部返回的是： type = 2， orgPartyCommitteeVo，
        // 党支部和党小组返回的是： type = 3,：orgPartyBranchVo
        val value = mutableMapOf<String, Long>()
        val orgStandards = when (vo?.type) {
            1 -> {
                val temp = vo as OverviewStandardVo
                // 排除原接口中的行政单位数据
                value["基层党组织"] =
                    (temp.total ?: 0).toLong() - (temp.listKeyValue.find { it.name == "行政单位" }?.value ?: 0)
                temp.listKeyValue
            }

            2 -> {
                val temp = vo as OrgPartyCommitteeVo
                value["基层党组织"] = (temp.list?.size ?: 0).toLong()
                temp.list
            }

            3 -> {
                val temp = vo as OrgPartyBranchVo
                value["基层党组织"] = (temp.partyGroupName?.size ?: 0).toLong()
                listOf(
                    KeyValVo().also { kv ->
                        kv.name = "党小组"
                        kv.value = temp.partyGroupName?.size ?: 0
                    }
                )
            }

            else -> listOf()
        }
        orgStandards.forEach {
            value[it.name] = (it.value ?: 0).toLong()
        }
        // 0.基层党组织-翻牌器
        list.add(
            listOf(
                CardFlop("", value["基层党组织"] ?: 0L),
                Type(typeName = "基层党组织翻牌器", typeIndex = index++)
            )
        )
        // 1.党委-翻牌器
        list.add(
            listOf(
                CardFlop("", value["党委"] ?: 0L),
                Type(typeName = "党委翻牌器", typeIndex = index++)
            )
        )
        // 2.党总支-翻牌器
        list.add(
            listOf(
                CardFlop("", value["党总支"] ?: 0L),
                Type(typeName = "党总支翻牌器", typeIndex = index++)
            )
        )
        // 3.党支部-翻牌器
        list.add(
            listOf(
                CardFlop("", value["党支部"] ?: 0L),
                Type(typeName = "党支部翻牌器", typeIndex = index++)
            )
        )
        // 4.党组-翻牌器
        list.add(
            listOf(
                CardFlop("", value["党组"] ?: 0L),
                Type(typeName = "党组翻牌器", typeIndex = index++)
            )
        )
        // 5.党小组-翻牌器
        list.add(
            listOf(
                CardFlop("", value["党小组"] ?: 0L),
                Type(typeName = "党小组翻牌器", typeIndex = index++)
            )
        )
        // 6.云上组织-翻牌器
        list.add(
            listOf(
                CardFlop("", value["云上组织"] ?: 0L),
                Type(typeName = "云上组织翻牌器", typeIndex = index++)
            )
        )
        // -------- 组织换届 -------------
        // 7.近6月组织换届-柱状图(使用线图数据)
        list.add(
            listOf(
                // 转换数据为Line数据，并用x排序
                dataVService.getOrgLeaderChange(
                    if (orgId == 3.toLong()) {
                        null
                    } else {
                        unid
                    }, 6, myHeaders.regionId
                ).map {
                    // 获取第一条map数据，封装成Line
                    Line(it.first, it.second.toDouble(), "换届数")
                }.sortedBy { it.x },
                Type(typeName = "近6月组织换届柱状图", typeIndex = index++)
            )
        )
        // -------- 调查研究 -------------
        val surveyResearchData = dataVService.getLeaderResearchData()
        // 8.调研总计-翻牌器
        list.add(
            listOf(
                CardFlop("", surveyResearchData.find { it.first == "调研总计" }?.second ?: 0L),
                Type(typeName = "调研总计翻牌器", typeIndex = index++)
            )
        )
        // 9.年度调研-翻牌器
        list.add(
            listOf(
                CardFlop("", surveyResearchData.find { it.first == "年度调研" }?.second ?: 0L),
                Type(typeName = "年度调研翻牌器", typeIndex = index++)
            )
        )
        // 10.日常调研-翻牌器
        list.add(
            listOf(
                CardFlop("", surveyResearchData.find { it.first == "日常调研" }?.second ?: 0L),
                Type(typeName = "日常调研翻牌器", typeIndex = index++)
            )
        )
        // 11.确定课题数-翻牌器
        list.add(
            listOf(
                CardFlop("", surveyResearchData.find { it.first == "确定课题数" }?.second ?: 0L),
                Type(typeName = "确定课题数翻牌器", typeIndex = index++)
            )
        )
        // 12.参与调研人数-翻牌器
        list.add(
            listOf(
                CardFlop("", surveyResearchData.find { it.first == "参与调研人数" }?.second ?: 0L),
                Type(typeName = "参与调研人数翻牌器", typeIndex = index++)
            )
        )
        val surveyTypeResearchData = dataVService.getLeaderResearchTypeData()
        // 13.调研分布-饼图
        list.add(
            listOf(
                surveyTypeResearchData.map {
                    Pie(type = it.first, value = it.second)
                },
                Type(typeName = "调研分布饼图", typeIndex = index++)
            )
        )
        // --------------------------------
        // ------------中间-----------------
        // 14.先锋指数左侧数字 - 翻牌器
        list.add(
            listOf(
                CardFlop("先锋指数堡垒指数平均值", 0),
                Type(typeName = "先锋指数（左）翻牌器", typeIndex = index++)
            )
        )
        // 15.先锋指数最高 - 翻牌器
        list.add(listOf(CardFlop("最高", 0), Type(typeName = "先锋指数最高翻牌器", typeIndex = index++)))
        // 16.先锋指数最低 - 翻牌器
        list.add(listOf(CardFlop("最低", 0), Type(typeName = "先锋指数最低翻牌器", typeIndex = index++)))
        // 17.党建-平均 - 翻牌器
        list.add(listOf(CardFlop("平均", 0), Type(typeName = "党建-平均翻牌器", typeIndex = index++)))
        // 18.党建-最高 - 翻牌器
        list.add(listOf(CardFlop("最高", 0), Type(typeName = "党建-最高翻牌器", typeIndex = index++)))
        // 19.党建-最低 - 翻牌器
        list.add(listOf(CardFlop("最低", 0), Type(typeName = "党建-最低翻牌器", typeIndex = index++)))
        // 20.业务-平均 - 翻牌器
        list.add(listOf(CardFlop("平均", 0), Type(typeName = "业务-平均翻牌器", typeIndex = index++)))
        // 21.业务-最高 - 翻牌器
        list.add(listOf(CardFlop("最高", 0), Type(typeName = "业务-最高翻牌器", typeIndex = index++)))
        // 22.业务-最低 - 翻牌器
        list.add(listOf(CardFlop("最低", 0), Type(typeName = "业务-最低翻牌器", typeIndex = index++)))
        // 23.创新-平均 - 翻牌器
        list.add(listOf(CardFlop("平均", 0), Type(typeName = "创新-平均翻牌器", typeIndex = index++)))
        // 24.创新-最高 - 翻牌器
        list.add(listOf(CardFlop("最高", 0), Type(typeName = "创新-最高翻牌器", typeIndex = index++)))
        // 25.创新-最低 - 翻牌器
        list.add(listOf(CardFlop("最低", 0), Type(typeName = "创新-最低翻牌器", typeIndex = index++)))
        // --------------------------------
        // ------------右边-----------------
        // 争先创优
        val honorMap = dataVService.getRewardPunishStatistics(
            if (orgId == 3.toLong()) {
                null
            } else {
                unid
            }, myHeaders.regionId
        )
        // 26.国家级荣誉 - 翻牌器 // 10480201 10480202
        list.add(
            listOf(
                CardFlop("", (honorMap["国家级荣誉-total"] as Int? ?: 0).toLong()),
                Type(typeName = "国家级荣誉翻牌器", typeIndex = index++)
            )
        )
        // 27.省部级荣誉 - 翻牌器 // 10480203 10480204
        list.add(
            listOf(
                CardFlop("", (honorMap["省部级荣誉-total"] as Int? ?: 0).toLong()),
                Type(typeName = "省部级荣誉翻牌器", typeIndex = index++)
            )
        )
        // 28.地市级荣誉 - 翻牌器 // 10480205 10480206
        list.add(
            listOf(
                CardFlop("", (honorMap["地市级荣誉-total"] as Int? ?: 0).toLong()),
                Type(typeName = "地市级荣誉翻牌器", typeIndex = index++)
            )
        )
        // xxxxx（组织名称），获得xxx级（级别）xxxxx（奖惩名称）
        // 字符串模板
        val str = "%s，获得%s：“%s”"
        // 29.国家级荣誉 - 轮播
        list.add(
            listOf(
                (honorMap["国家级荣誉-list"] as List<*>).map {
                    val temp = it as MeetingOrgCommendPenalizeQueryVO
                    MeetingOrgCommendPenalizeQueryVO().also { vo ->
                        vo.datavMessage = str.format(
                            temp.orgName,
                            temp.levelValue,
                            temp.nameValue
                        )
                        vo.orgName = temp.orgName
                        vo.ratifyTime = temp.ratifyTime
                    }
                }.sortedByDescending { it.ratifyTime },
                Type(typeName = "国家级荣誉轮播", typeIndex = index++)
            )
        )
        // 30.省部级荣誉 - 轮播
        list.add(
            listOf(
                (honorMap["省部级荣誉-list"] as List<*>).map {
                    val temp = it as MeetingOrgCommendPenalizeQueryVO
                    MeetingOrgCommendPenalizeQueryVO().also { vo ->
                        vo.datavMessage = str.format(
                            temp.orgName,
                            temp.levelValue,
                            temp.nameValue
                        )
                        vo.orgName = temp.orgName
                        vo.ratifyTime = temp.ratifyTime
                    }
                }.sortedByDescending { it.ratifyTime },
                Type(typeName = "省部级荣誉轮播", typeIndex = index++)
            )
        )
        // 31.地市级荣誉 - 轮播
        list.add(
            listOf(
                (honorMap["地市级荣誉-list"] as List<*>).map {
                    val temp = it as MeetingOrgCommendPenalizeQueryVO
                    MeetingOrgCommendPenalizeQueryVO().also { vo ->
                        vo.datavMessage = str.format(
                            temp.orgName,
                            temp.levelValue,
                            temp.nameValue
                        )
                        vo.orgName = temp.orgName
                        vo.ratifyTime = temp.ratifyTime
                    }
                }.sortedByDescending { it.ratifyTime },
                Type(typeName = "地市级荣誉轮播", typeIndex = index++)
            )
        )
        // 党务公开
        // 32.党务公开-柱状图(使用线图数据)
        list.add(
            listOf(
                // 转换数据为Line数据，并用x排序
                dataVService.getPartyAffairsOpenData().map {
                    // 获取第一条map数据，封装成Line
                    Line(it.first, it.second.toDouble(), "数量")
                }.sortedBy { it.x },
                Type(typeName = "党务公开柱状图", typeIndex = index++)
            )
        )
        // 品牌阵地
        val brandData = dataVService.getPartyBrandBaseData(orgId, unid, headers)
        // 33.党建品牌 - 翻牌器
        list.add(
            listOf(
                CardFlop("", brandData.second),
                Type(typeName = "党建品牌翻牌器", typeIndex = index++)
            )
        )
        val positions = dataVService.getPartyPositionsData(orgId, headers)
        // 34.党建文化长廊 - 翻牌器
        list.add(
            listOf(
                CardFlop("", positions.find { it.first == "党建文化长廊" }?.second ?: 0L),
                Type(typeName = "党建文化长廊翻牌器", typeIndex = index++)
            )
        )
        // 35.党员活动室 - 翻牌器
        list.add(
            listOf(
                CardFlop("", positions.find { it.first == "党员活动室" }?.second ?: 0L),
                Type(typeName = "党员活动室翻牌器", typeIndex = index++)
            )
        )
        // 36.支部园地 - 翻牌器
        list.add(
            listOf(
                CardFlop("", positions.find { it.first == "支部园地" }?.second ?: 0L),
                Type(typeName = "支部园地翻牌器", typeIndex = index++)
            )
        )
        // 37.党建VR - 翻牌器
        list.add(
            listOf(
                CardFlop("", positions.find { it.first == "党建VR" }?.second ?: 0L),
                Type(typeName = "党建VR翻牌器", typeIndex = index++)
            )
        )
        // -------------------------
        log.debug("index:{},size:{}", index - 1, list.size)
        return list
    }
}