package com.goodsogood.ows.controller.datav

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.configuration.ExcludeOrgConfig
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.helper.RemoteApiHelper
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.model.vo.datav.DataVO
import com.goodsogood.ows.model.vo.datav.FullOrPartyVO
import com.goodsogood.ows.model.vo.datav.LeaderVO
import com.goodsogood.ows.model.vo.datav.UserVO
import com.goodsogood.ows.model.vo.datav.base.*
import com.goodsogood.ows.model.vo.datav.others.HomeResultVo
import com.goodsogood.ows.model.vo.datav.others.PartyPositionsForm
import com.goodsogood.ows.model.vo.overview.KeyValVo
import com.goodsogood.ows.model.vo.overview.OverviewStandardVo
import com.goodsogood.ows.model.vo.overview.PartyMemberStaVo
import com.goodsogood.ows.model.vo.overview.PeriodPartyCommitteeVo
import com.goodsogood.ows.model.vo.tbc.TbcCommendPenalizeVo
import com.goodsogood.ows.service.overview.OverviewService
import com.goodsogood.ows.service.tbc.TbcMeetingLifeService
import com.goodsogood.ows.service.tbc.TbcPartyUsersService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.swagger.annotations.ApiOperation
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.cache.annotation.Cacheable
import org.springframework.http.HttpHeaders
import org.springframework.web.bind.annotation.*
import org.springframework.web.client.RestTemplate
import java.io.IOException
import java.time.LocalDate
import kotlin.math.roundToLong

/**
 * <AUTHOR>
 * @date 2023/3/9
 * @description 数智党建可视化大屏-党建概况
 */
@Api(value = "数智党建可视化大屏-党建概况", tags = ["数智党建可视化大屏"])
@RestController
@RequestMapping("/datav/overview")
@CrossOrigin(originPatterns = ["*"], maxAge = 3600)
data class OverviewDatavController(
    val overviewService: OverviewService,
    val meetingLifeService: TbcMeetingLifeService,
    val partyUsersService: TbcPartyUsersService,
    val excludeOrgConfig: ExcludeOrgConfig,
    val restTemplate: RestTemplate
) {
    @Value("\${tog-services.user-center}")
    private val userCenter: String? = null

    private val log = LoggerFactory.getLogger(OverviewDatavController::class.java)

    /**
     * 组织生活部分数据
     */
    @HttpMonitorLogger
    @GetMapping("/v1/org")
    @ApiOperation("党建概况-组织概况")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要配置manger和在定时器ClearCacheScheduler配置清理
    fun org(@RequestHeader headers: HttpHeaders): Array<DataVO?> {
//        val sysHeader = HeaderHelper.buildMyHeader(headers)
        val entity = overviewService.orgOverviewOrg(headers)
        if (entity.body == null || entity.body!!.code != 0 || entity.body.data == null) {
            log.error("党建概况-组织概况->获取数据失败->${entity}")
            return arrayOf()
        }
        // 组装数据
        if (entity.body.data !is OverviewStandardVo) {
            log.error("党建概况-组织概况->数据格式判断失败->${entity.body}")
            return arrayOf()
        }
        val data = entity.body.data as OverviewStandardVo
        val result = arrayOfNulls<DataVO>(8)
        var baseNumber = 0L // 基层党组织数量
        // 基层党组织 0
        // 党委 1
        // 党总支 2
        // 党支部 3
        // 行政单位 4
        // 党组  5
        // 党小组 6
        // 云上组织 7
        data.listKeyValue.forEach {
            when (it.name) {
                "党委" -> {
                    result[1] =
                        DataVO().addCardFlop(CardFlop(name = "党委", value = it.value.toLong()))
                            .addTitle(GenericTitle(value = "党委"))
                    baseNumber += it.value
                }

                "党总支" -> {
                    result[2] = DataVO().addCardFlop(CardFlop(name = "党总支", value = it.value.toLong()))
                        .addTitle(GenericTitle(value = "党总支"))
                    baseNumber += it.value
                }

                "党支部" -> {
                    result[3] = DataVO().addCardFlop(CardFlop(name = "党支部", value = it.value.toLong()))
                        .addTitle(GenericTitle(value = "党支部"))
                    baseNumber += it.value
                }

                "行政单位" -> result[4] = DataVO().addCardFlop(CardFlop(name = "行政单位", value = it.value.toLong()))
                    .addTitle(GenericTitle(value = "行政单位"))

                "党组" -> result[5] = DataVO().addCardFlop(CardFlop(name = "党组", value = it.value.toLong()))
                    .addTitle(GenericTitle(value = "党组"))

                "党小组" -> result[6] = DataVO().addCardFlop(CardFlop(name = "党小组", value = it.value.toLong()))
                    .addTitle(GenericTitle(value = "党小组"))
            }
        }
        result[0] = DataVO().addCardFlop(CardFlop(name = "基层党组织", value = baseNumber))
            .addTitle(GenericTitle(value = "基层党组织"))
        // 云区组织数量
        val cloudOrg = overviewService.totalEcpOrg()
        result[7] = DataVO().addCardFlop(CardFlop(name = "云上组织", value = cloudOrg))
            .addTitle(GenericTitle(value = "云上组织"))
        return result
    }

    @HttpMonitorLogger
    @GetMapping("/v1/period")
    @ApiOperation("党建概况-组织换届")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun period(@RequestHeader headers: HttpHeaders): List<MutableList<Pie>> {
        val entity = overviewService.orgOverviewPeriod(headers)
        if (entity.body == null || entity.body!!.code != 0 || entity.body.data == null) {
            log.error("党建概况-组织换届->获取数据失败->${entity}")
            return listOf()
        }
        // 组装数据
        if (entity.body.data !is List<*>) {
            log.error("党建概况-组织换届->数据格式判断失败->${entity.body}")
            return listOf()
        }
        val data = entity.body.data as List<*>
        // 本月应换届 0
        val pie0: MutableList<Pie> = mutableListOf()
        // 6月内应换届 1
        val pie1: MutableList<Pie> = mutableListOf()
        // 超期未换届 2
        val pie2: MutableList<Pie> = mutableListOf()
        data.forEach {
            if (it is PeriodPartyCommitteeVo) {
                when (it.staType) {
                    // 本月
                    1 -> pie0.addAll(
                        listOf(
                            Pie(type = "党委", value = it.committee),
                            Pie(type = "党支部", value = it.branch),
                            Pie(type = "党总支", value = it.totalBranch)
                        )
                    )
                    // 6月
                    2 -> pie1.addAll(
                        listOf(
                            Pie(type = "党委", value = it.committee),
                            Pie(type = "党支部", value = it.branch),
                            Pie(type = "党总支", value = it.totalBranch)
                        )
                    )
                    // 超期未换届
                    3 -> pie2.addAll(
                        listOf(
                            Pie(type = "党委", value = it.committee),
                            Pie(type = "党支部", value = it.branch),
                            Pie(type = "党总支", value = it.totalBranch)
                        )
                    )
                }
            }
        }
        return listOf(pie0, pie1, pie2)
    }

    @HttpMonitorLogger
    @GetMapping("/v1/party-positions")
    @ApiOperation("党建概况-品牌阵地")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun partyPositions(@RequestHeader headers: HttpHeaders): Array<List<CardFlop>?> {
        val sysHeader = HeaderHelper.buildMyHeader(headers)
        val result = arrayOfNulls<List<CardFlop>?>(6)
        //党建阵地 https://dangjian.cq.tobacco.gov.cn/zuul/owsz/user/partyPositions/selectExcellent?org_id=3&type=1&page=1&page_size=6
        val urlPosition =
            "http://${userCenter}/partyPositions/selectExcellent?org_id=${sysHeader.oid}&type=1&page=1&page_size=1"
        try {
            val partyPositionsForm =
                RemoteApiHelper.get(
                    restTemplate,
                    urlPosition,
                    headers,
                    object : TypeReference<Result<PartyPositionsForm>>() {})
            (0..5).forEach {
                var name = ""
                var value = 0L
                var suffix: String? = null
                when (it) {
                    // 党建文化长廊 0
                    0 -> {
                        name = "党建文化长廊"
                        value = (partyPositionsForm?.numberAddForm?.corridorNumber ?: 0.0).roundToLong()
                        suffix = "㎡"
                    }
                    // 党建文化中心 1
                    1 -> {
                        name = "党建文化中心"
                        value = (partyPositionsForm?.numberAddForm?.centerNumber ?: 0).toLong()
                    }
                    // 党建品牌 2
                    2 -> {
                        name = "党建品牌"
                        value = 0L
                    }
                    //  党建活动室 3
                    3 -> {
                        name = "党建活动室"
                        value = (partyPositionsForm?.numberAddForm?.liveNumber ?: 0).toLong()
                    }
                    // 支部园地 4
                    4 -> {
                        name = "支部园地"
                        value = (partyPositionsForm?.numberAddForm?.gardenNumber ?: 0).toLong()
                    }
                    // 党建VR 5
                    5 -> {
                        name = "党建VR"
                        value = (partyPositionsForm?.numberAddForm?.vrNumber ?: 0).toLong()
                    }
                }
                result[it] = listOf(CardFlop(name = name, value = value, suffix = suffix))
            }
        } catch (e: ApiException) {
            log.error(
                "党建概况-品牌阵地->获取阵地发生异常: ${e.localizedMessage}->${e.result?.code} : ${e.result?.message}",
                e
            )
        } catch (e: IOException) {
            log.error("党建概况-品牌阵地->获取阵地发生网络异常: ${e.localizedMessage}", e)
        }
        // 获取党建品牌 https://dangjian.cq.tobacco.gov.cn/zuul/owsz/user/partyBrand/selectSub?org_id=3
        val urlBrand = "http://${userCenter}/partyBrand/selectSub?org_id=${sysHeader.oid}"
        try {
            val list =
                RemoteApiHelper.get(
                    restTemplate,
                    urlBrand,
                    headers,
                    object : TypeReference<Result<List<Map<String, *>>>>() {})
            // 党建品牌 2
            result[2]?.get(0)?.value = (list?.size ?: 0).toLong()
        } catch (e: ApiException) {
            log.error(
                "党建概况-品牌阵地->获取品牌发生异常: ${e.localizedMessage}->${e.result?.code} : ${e.result?.message}",
                e
            )
        } catch (e: IOException) {
            log.error("党建概况-品牌阵地->获取品牌发生网络异常: ${e.localizedMessage}", e)
        }
        return result
    }

    @HttpMonitorLogger
    @GetMapping("/v1/penalize")
    @ApiOperation("党建概况-争先创优")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun penalize(@RequestHeader headers: HttpHeaders): Array<DataVO?> {
        val penalizeList: List<TbcCommendPenalizeVo> = meetingLifeService.commendPenalizeList
        val result = arrayOfNulls<DataVO>(3)
        penalizeList.forEach {
            val value: MutableList<Pair<String, *>> = it.list.map { value -> Pair("value", value) }.toMutableList()
            when (it.type) {
                // 国家级 0
                1 -> result[0] = DataVO(
                    cardFlop = mutableListOf(CardFlop(value = it.num.toLong(), name = "国家级荣誉", suffix = "个")),
                    keyValue = value
                )
                // 省部级 1
                2 -> result[1] = DataVO(
                    cardFlop = mutableListOf(CardFlop(value = it.num.toLong(), name = "省部级荣誉", suffix = "个")),
                    keyValue = value
                )
                // 地市级 2
                3 -> result[2] = DataVO(
                    cardFlop = mutableListOf(CardFlop(value = it.num.toLong(), name = "地市级荣誉", suffix = "个")),
                    keyValue = value
                )
            }
        }
        return result
    }

    /**
     * 党员概况数据
     */
    @HttpMonitorLogger
    @GetMapping("/v1/user")
    @ApiOperation("党建概况-党员概况")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun user(@RequestHeader headers: HttpHeaders): UserVO {
        val userVO = UserVO()
        // 人员概况
        val entity = overviewService.orgOverviewUserSta(headers)
        if (entity.body != null && entity.body!!.code == 0 && entity.body.data != null || entity.body.data is PartyMemberStaVo) {
            val partyMemberStaVo = entity.body.data as PartyMemberStaVo
            userVO.rate = DataVO()
            // 党员人数占比
            with(userVO.rate) {
                // 党员总数翻牌器
                this!!.cardFlop = mutableListOf(CardFlop(value = partyMemberStaVo.total.toLong(), suffix = "人"))
                // 仪表盘
                this.dashboard = mutableListOf(
                    Dashboard(
                        // 四舍五入保留整数
                        percent = (partyMemberStaVo.total * 100.0 / partyMemberStaVo.cityWideStaff).roundToLong()
                            .toDouble()
                    )
                )
            }
//            log.debug("党建概况-党员概况-人员概况->${userVO.rate}")
            // 党员性别
            userVO.gender = arrayOfNulls(3)
            with(userVO.gender) {
                // 男
                this!![0] = listOf(
                    CardFlop(
                        value = (partyMemberStaVo.maleMember * 100.0 / partyMemberStaVo.total).roundToLong(),
                        suffix = "%"
                    )
                )
                // 女
                this[1] = listOf(
                    CardFlop(
                        value = (partyMemberStaVo.femaleMember * 100.0 / partyMemberStaVo.total).roundToLong(),
                        suffix = "%"
                    )
                )
                // 未知
                this[2] = listOf(
                    CardFlop(
                        value = (partyMemberStaVo.otherGenderMember * 100.0 / partyMemberStaVo.total).roundToLong(),
                        suffix = "%"
                    )
                )
            }
        } else {
            log.error("党建概况-党员概况->获取数据失败->${entity}-${entity?.body}")
            log.warn("党建概况-党员概况-人员基础数据获取失败，跳过")
        }
        // 学历 - 0
        userVO.tab = arrayOfNulls(4)
        val education = overviewService.orgOverviewEducationVo(headers)
//        log.debug("党建概况-党员概况-学历->$education")
        userVO.tab!![0] = createUserVO(education.listKeyValue, education.total, education.listKeyValue.size)
        // 党龄 - 1
        val partyAgeDistributed = overviewService.orgOverviewPartyAgeDistributedVo(headers)
//        log.debug("党建概况-党员概况-党龄->$partyAgeDistributed")
        userVO.tab!![1] =
            createUserVO(
                partyAgeDistributed.listKeyValue,
                partyAgeDistributed.total,
                partyAgeDistributed.listKeyValue.size
            )
        // 年龄 - 2
        val ageDistributed = overviewService.orgOverviewAgeDistributedVo(headers)
//        log.debug("党建概况-党员概况-年龄->$ageDistributed")
        userVO.tab!![2] =
            createUserVO(ageDistributed.listKeyValue, ageDistributed.total, ageDistributed.listKeyValue.size)
        // 序列 - 3
        val sequence = overviewService.sequenceDistributed(headers)
//        log.debug("党建概况-党员概况-序列->$sequence")
        val data = createUserVO(sequence.listKeyValue, sequence.total, sequence.listKeyValue.size)
        // 序列需要特殊排序
        userVO.tab!![3] = arrayOfNulls(data.size)
        data.forEach {
            when (it?.title?.get(0)?.value) {
                "综合管理" -> userVO.tab!![3]!![0] = it
                "专卖管理" -> userVO.tab!![3]!![1] = it
                "卷烟营销" -> userVO.tab!![3]!![2] = it
                "烟叶生产" -> userVO.tab!![3]!![3] = it
                "未知" -> userVO.tab!![3]!![4] = it
                else -> log.debug(" 序列排序时，发生数据错误->${it?.title?.get(0)?.value}")
            }
        }
        return userVO
    }

    private fun createUserVO(listKeyValue: List<KeyValVo>, total: Int, size: Int): Array<DataVO?> {
        val array = arrayOfNulls<DataVO?>(size)
        with(array) {
            listKeyValue.withIndex().forEach {
                this[it.index] = DataVO(
                    title = mutableListOf(GenericTitle(value = it.value.name)),
                    cardFlop = mutableListOf(
                        CardFlop(
                            name = it.value.value.toString(),
                            value = (it.value.value * 100.0 / total).roundToLong(),
                            suffix = "%"
                        ),
                    ),
                    multiPie = mutableListOf(
                        MultidimensionalPie(y = it.value.value.toDouble(), s = "1"),
                        MultidimensionalPie(y = (total - it.value.value).toDouble(), s = "2")
                    )
                )
            }
        }
        return array
    }

    /**
     * 党员概况数据
     */
    @HttpMonitorLogger
    @GetMapping("/v1/develop")
    @ApiOperation("党建概况-党员发展")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun develop(@RequestHeader headers: HttpHeaders): UserVO {
        val sysHeader = HeaderHelper.buildMyHeader(headers)
        val userVO = UserVO()
        // 本年党员发展
        val overviewStandardVo = overviewService.orgOverviewDevelopPartyMembersVo(headers)
        userVO.develop = arrayOfNulls(overviewStandardVo.listKeyValue.size)
        overviewStandardVo.listKeyValue.withIndex().forEach {
            userVO.develop!![it.index] = listOf(
                CardFlop(name = it.value.name, value = it.value.value.toLong())
            )
        }
        // 转正党员趋势-折线图
        userVO.becameNum = mutableListOf()
        val result = partyUsersService.develop(sysHeader)
        // 封装前5年
        val currentYear = LocalDate.now().year + 1
        // 循环前5年的年份（包含今年）
        for (year in currentYear - 5 until currentYear) {
            val form = result.firstOrNull { it.item.trim() == year.toString() }
            userVO.becameNum!!.add(
                Line(
                    x = "${year}年",
                    y = (form?.total ?: 0).toDouble(),
                    s = "转正党员数量",
                    colorField = "转正党员数量"
                )
            )
        }
        return userVO
    }

    @HttpMonitorLogger
    @GetMapping("/v1/party-leader")
    @ApiOperation("党建概况-党务干部")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun partyLeader(@RequestHeader headers: HttpHeaders): LeaderVO {
        val sysHeader = HeaderHelper.buildMyHeader(headers)
        val result = partyUsersService.partyLeader(sysHeader)
//        log.debug("党建概况-党务干部->result->$result")
        val leaderVO = LeaderVO()
        // 专职和兼职
        leaderVO.fullParty = listOf(
            FullOrPartyVO(
                key = "专职",
                value = (result.fullTimeTotal * 100.00 / (result.fullTimeTotal + result.partyTimeTotal)).roundToLong()
                    .toDouble()
            ),
            FullOrPartyVO(
                key = "兼职",
                value = (result.partyTimeTotal * 100.00 / (result.fullTimeTotal + result.partyTimeTotal)).roundToLong()
                    .toDouble()
            ),
        )
        leaderVO.position = arrayOfNulls(3)
        with(leaderVO.position) {
            // 书记 - 0
            this!![0] = listOf(CardFlop(name = "书记", value = result.partyTotal.toLong()))
            // 副书记 - 1
            this[1] = listOf(CardFlop(name = "副书记", value = result.deputyTotal.toLong()))
            // 委员 - 3
            this[2] = listOf(CardFlop(name = "委员", value = result.memberTotal.toLong()))
        }
        return leaderVO
    }

    @HttpMonitorLogger
    @GetMapping("/v1/point")
    @ApiOperation("党建概况-党建地图")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun pointMap(@RequestHeader headers: HttpHeaders): List<MapChart> {
//        val sysHeader = HeaderHelper.buildMyHeader(headers)
        val result = mutableListOf<MapChart>()
        // 党建地图 https://dangjian.cq.tobacco.gov.cn/zuul/owsz/user/home/<USER>
        val urlIndex = "http://${userCenter}/home/<USER>"
        try {
            RemoteApiHelper.get(
                restTemplate,
                urlIndex,
                headers,
                object : TypeReference<Result<HomeResultVo>>() {}).mapResult.forEach {
                if (it.longitude != null && it.latitude != null && it.orgType != null) {
                    result.add(
                        MapChart(
                            lng = it.longitude!!,
                            lat = it.latitude!!,
                            name = it.orgName ?: "",
                            iconField = it.orgType.toString()
                        )
                    )
                }
            }
        } catch (e: ApiException) {
            log.error(
                "党建概况-党建地图->获取党建地图数据发生异常: ${e.localizedMessage}->${e.result?.code} : ${e.result?.message}",
                e
            )
        } catch (e: IOException) {
            log.error("党建概况-党建地图->获取党建地图数据发生网络异常: ${e.localizedMessage}", e)
        }
        return result
    }
}
