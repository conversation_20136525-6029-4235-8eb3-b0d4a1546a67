package com.goodsogood.ows.controller.datav

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.ClearCacheScheduler
import com.goodsogood.ows.configuration.ExcludeOrgConfig
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.datav.*
import com.goodsogood.ows.model.vo.datav.base.*
import com.goodsogood.ows.model.vo.overview.OverviewActivityVo
import com.goodsogood.ows.model.vo.overview.OverviewStandardVo
import com.goodsogood.ows.model.vo.overview.PpmdStaVo
import com.goodsogood.ows.service.overview.OverviewService
import com.goodsogood.ows.service.score.ScoreService
import com.goodsogood.ows.service.tbc.TbcPartyUsersService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.joda.time.DateTime
import org.slf4j.LoggerFactory
import org.springframework.cache.annotation.Cacheable
import org.springframework.http.HttpHeaders
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import kotlin.math.roundToInt
import kotlin.math.roundToLong

/**
 * <AUTHOR>
 * @date 2023/3/7
 * @description 数智党建可视化大屏-党务工作大屏
 */
@Api(value = "数智党建可视化大屏-党务工作大屏", tags = ["数智党建可视化大屏"])
@RestController
@RequestMapping("/datav/job")
@CrossOrigin(originPatterns = ["*"], maxAge = 3600)
class JobController(
    val overviewService: OverviewService,
    val partyUsersService: TbcPartyUsersService,
    val excludeOrgConfig: ExcludeOrgConfig,
    val scoreService: ScoreService,
    val clearCacheScheduler: ClearCacheScheduler
) {
    private val log = LoggerFactory.getLogger(JobController::class.java)

    @HttpMonitorLogger
    @GetMapping("/v1/clean-cache")
    @ApiOperation("党建大屏-清理缓存")
    fun cleanCache() {
        clearCacheScheduler.cleanDatavCache()
    }

    /**
     * 组织生活部分数据
     */
    @HttpMonitorLogger
    @GetMapping("/v1/meeting")
    @ApiOperation("党务工作-组织生活")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun meeting(@RequestHeader headers: HttpHeaders): Array<DataVO?> {
        val sysHeader = HeaderHelper.buildMyHeader(headers)
        val entity = overviewService.orgLifeOverviewActivityNew1(headers, null)
        if (entity.body == null || entity.body!!.code != 0 || entity.body.data == null) {
            log.error("党务工作-组织生活->获取数据失败->${entity}")
            return arrayOf()
        }
        // 组装数据
        if (entity.body.data !is OverviewActivityVo) {
            log.error("党务工作-组织生活->数据格式判断失败->${entity.body}")
            return arrayOf()
        }
        val data = entity.body.data as OverviewActivityVo
        val result = arrayOfNulls<DataVO>(6)
        data.listSeries.forEach {
            when (it.type) {
                // 党员大会(本季度)
                1 -> result[0] = DataVO(
                    title = mutableListOf(GenericTitle(value = "党员大会（本季度）")),
                    subTitle = mutableListOf(
                        GenericTitle(
                            value = (it.number * 100 / it.total.toDouble()).roundToLong().toString() + "%"
                        )
                    ),
                    cardFlop = mutableListOf(CardFlop(value = it.number.toLong(), suffix = "个支部")),
                    multiPie = mutableListOf(
                        MultidimensionalPie(s = "1", y = it.number.toDouble()),
                        MultidimensionalPie(s = "2", y = (it.total - it.number).toDouble())
                    )
                    // progressBar = mutableListOf(ProgressBar(value = it.number.toLong(), sum = it.total.toLong()))

                )
                // 党课(本季度)
                4 -> result[1] = DataVO(
                    title = mutableListOf(GenericTitle(value = "党课（本季度）")),
                    subTitle = mutableListOf(
                        GenericTitle(
                            value = (it.number * 100 / it.total.toDouble()).roundToLong().toString() + "%"
                        )
                    ),
                    cardFlop = mutableListOf(CardFlop(value = it.number.toLong(), suffix = "个支部")),
                    multiPie = mutableListOf(
                        MultidimensionalPie(s = "1", y = it.number.toDouble()),
                        MultidimensionalPie(s = "2", y = (it.total - it.number).toDouble())
                    )
                )
                // 支委会(本月)
                2 -> result[2] = DataVO(
                    title = mutableListOf(GenericTitle(value = "支委会（本月）")),
                    subTitle = mutableListOf(
                        GenericTitle(
                            value = (it.number * 100 / it.total.toDouble()).roundToLong().toString() + "%"
                        )
                    ),
                    cardFlop = mutableListOf(CardFlop(value = it.number.toLong(), suffix = "个支部")),
                    multiPie = mutableListOf(
                        MultidimensionalPie(s = "1", y = it.number.toDouble()),
                        MultidimensionalPie(s = "2", y = (it.total - it.number).toDouble())
                    )
                )
                // 党小组会(本月)
                3 -> result[3] = DataVO(
                    title = mutableListOf(GenericTitle(value = "党小组会（本月）")),
                    subTitle = mutableListOf(
                        GenericTitle(
                            value = (it.number * 100 / it.total.toDouble()).roundToLong().toString() + "%"
                        )
                    ),
                    cardFlop = mutableListOf(CardFlop(value = it.number.toLong(), suffix = "个党小组")),
                    multiPie = mutableListOf(
                        MultidimensionalPie(s = "1", y = it.number.toDouble()),
                        MultidimensionalPie(s = "2", y = (it.total - it.number).toDouble())
                    )
                )
                //主题党日(本月)
                5 -> result[4] = DataVO(
                    title = mutableListOf(GenericTitle(value = "主题党日（本月）")),
                    subTitle = mutableListOf(
                        GenericTitle(
                            value = (it.number * 100 / it.total.toDouble()).roundToLong().toString() + "%"
                        )
                    ),
                    cardFlop = mutableListOf(CardFlop(value = it.number.toLong(), suffix = "个支部")),
                    multiPie = mutableListOf(
                        MultidimensionalPie(s = "1", y = it.number.toDouble()),
                        MultidimensionalPie(s = "2", y = (it.total - it.number).toDouble())
                    )
                )
            }
        }
        result[5] = DataVO(
            title = mutableListOf(GenericTitle(value = "谈心谈话")),
            cardFlop = mutableListOf(
                CardFlop(
                    value = overviewService.getStaOverviewMeetingTalk(sysHeader.oid, excludeOrgConfig.orgIds).toLong(),
                    suffix = "次"
                )
            ),
        )
        return result
    }

    @HttpMonitorLogger
    @GetMapping("/v1/democratic_review")
    @ApiOperation("党务工作-民主评议党员")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun democraticReview(@RequestHeader headers: HttpHeaders): DemocraticReviewVO {
        val sysHeader = HeaderHelper.buildMyHeader(headers)
        val entity = overviewService.orgLifeOverviewDemocraticReview(headers)
        if (entity.body == null || entity.body!!.code != 0 || entity.body.data == null) {
            log.error("党务工作-民主评议党员->获取数据失败->${entity}")
            return DemocraticReviewVO()
        }
        // 组装数据
        if (entity.body.data !is OverviewStandardVo) {
            log.error("党务工作-民主评议党员->数据格式判断失败->${entity.body}")
            return DemocraticReviewVO()
        }
        val vo = DemocraticReviewVO()
        (entity.body.data as OverviewStandardVo).listKeyValue.forEach {
            vo.pie.add(Pie(type = it.name, value = it.value, x = it.name, y = it.value))
        }
        val comment = overviewService.countOrgMeetingComment(
            DateTime.now().year - 1, sysHeader.regionId, sysHeader.oid, excludeOrgConfig.orgIds
        )

        // 已评审->未评审 仪表盘
        vo.dashboard.add(
            Dashboard(
                min = 0,
                max = 100,
//                content = "工作完成进度",
                percent = ((comment["已评议"] as Long) * 100.0 / (comment["应评议"] as Long)).roundToLong().toDouble(),
            )
        )
        return vo
    }


    @HttpMonitorLogger
    @GetMapping("/v1/ppmd")
    @ApiOperation("党务工作-党费交纳")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun ppmd(@RequestHeader headers: HttpHeaders): PpmdVO {
//        val sysHeader = HeaderHelper.buildMyHeader(headers)
        val entity = overviewService.orgLifeOverviewPpmd(headers)
        if (entity.body == null || entity.body!!.code != 0 || entity.body.data == null) {
            log.error("党务工作-党费交纳->获取数据失败->${entity}")
            return PpmdVO()
        }
        // 组装数据
        if (entity.body.data !is PpmdStaVo) {
            log.error("党务工作-党费交纳->数据格式判断失败->${entity.body}")
            return PpmdVO()
        }
        val ppmdStaVo = entity.body.data as PpmdStaVo
        val vo = PpmdVO()
        with(vo) {
            // 本月
            cardFlopMoon.add(
                OneCardFlop(name = "本月已交党费（元）", value = ppmdStaVo.totalAmount)
            )
            dashboard.add(
                Dashboard(percent = ppmdStaVo.payNum.toDouble() / ppmdStaVo.totalNum * 100)
            )
            // 本年
            genericTitle.add(
                GenericTitle(value = "本年度已交党费（元）")
            )
            cardFlopYear.add(
                OneCardFlop(name = "本年度已交党费（元）", value = overviewService.totalPayThisYear(LocalDate.now().year))
            )
        }
        return vo
    }

    @HttpMonitorLogger
    @GetMapping("/v1/integral")
    @ApiOperation("党务工作-党员积分")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun integral(@RequestHeader headers: HttpHeaders): IntegralVO {
        val integralVO = IntegralVO()
        // 积分翻牌器
        val report = scoreService.queryAvgAndMax()
        // 党员积分（累计最高积分、累计平均积分、年度最高积分、年度平均积分）
        integralVO.integral = arrayOfNulls(4)
        // 累计最高
        integralVO.integral!![0] = listOf(CardFlop(name = "累计最高积分", value = report.maxScore.toLong()))
        // 累计平均积分
        integralVO.integral!![1] = listOf(CardFlop(name = "累计平均积分", value = report.avgScore.toLong()))
        // 年度最高积分
        integralVO.integral!![2] = listOf(CardFlop(name = "年度最高积分", value = report.yearMax.toLong()))
        // 年度平均积分
        integralVO.integral!![3] = listOf(CardFlop(name = "年度平均积分", value = report.yearAvg.toLong()))
        // 走势图
        val scoreLine = scoreService.queryMonthScore(headers)
        integralVO.integralNum = mutableListOf()
        scoreLine.month.withIndex().forEach {
            integralVO.integralNum!!.add(
                Line(
                    x = it.value.replace("-", "."),
                    y = ((scoreLine.score[it.index] ?: 0.0).roundToLong()).toDouble(),
                    s = "党员积分",
                    colorField = "党员积分"
                )
            )
        }
        integralVO.integralNum?.reverse()
        return integralVO
    }

    @HttpMonitorLogger
    @GetMapping("/v1/study")
    @ApiOperation("党务工作-学习教育")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun study(@RequestHeader headers: HttpHeaders): StudyVO {
        val studyVO = StudyVO()
        val sysHeader = HeaderHelper.buildMyHeader(headers)
        val form = partyUsersService.study(sysHeader)
        // 学习强国
        with(studyVO.powerful) {
            // 4个翻牌器
            // 累计最高积分
            this.add(
                listOf(
                    CardFlop(name = "累计最高积分", value = (form.studyScore?.maxTotal ?: 0).toLong())
                )
            )
            // 累计平均积分
            this.add(
                listOf(
                    CardFlop(name = "累计平均积分", value = (form.studyScore?.averTotal ?: 0).toLong())
                )
            )
            // 年度最高积分
            this.add(
                listOf(
                    CardFlop(name = "年度最高积分", value = (form.studyScore?.yearMaxTotal ?: 0).toLong())
                )
            )
            // 年度平均积分
            this.add(
                listOf(
                    CardFlop(name = "年度平均积分", value = (form.studyScore?.yearAverTotal ?: 0).toLong())
                )
            )
            // 一个走势图
            this.add(
                form.trendList.map {
                    Line(
                        x = String.format("%d.%02d", it.year, it.month),
                        y = it.score.toDouble(),
                        s = "积分",
                        colorField = "积分"
                    )
                }
            )
        }
        // 网络学院
        with(studyVO.online) {
            // 累计最高学时
            this[0] = listOf(CardFlop(name = "累计最高学时", value = (form.studyTime?.maxTotal ?: 0).toLong()))
            //累计平均学时
            this[1] = listOf(CardFlop(name = "累计平均学时", value = (form.studyTime?.averTotal ?: 0).toLong()))
            // 年度最高学时
            this[2] = listOf(CardFlop(name = "年度最高学时", value = (form.studyTime?.yearMaxTotal ?: 0).toLong()))
            // 年度平均学时
            this[3] = listOf(CardFlop(name = "年度平均学时", value = (form.studyTime?.yearAverTotal ?: 0).toLong()))
        }
        // 数值党建
        with(studyVO.tobacco) {
            // 集中学习时长/h
            this[0] = listOf(CardFlop(name = "集中学习时长/h", value = (form.studyDuration?.learnTime ?: 0).toLong()))
            // 参与学习人次
            this[1] =
                listOf(CardFlop(name = "参与学习人次", value = (form.studyDuration?.trainingTime ?: 0).toLong()))
            // 每日一练
            this[2] = listOf(
                CardFlop(
                    name = "每日一练参与率",
                    value = ((form.studyDuration?.dayTotal ?: 0) * 100.0 / (form.studyDuration?.partyTotal
                        ?: 1)).roundToLong(),
                    suffix = "%"
                )
            )
            // 每月一测
            this[3] = listOf(
                CardFlop(
                    name = "每月一测参与率",
                    value = ((form.studyDuration?.monthTotal ?: 0) * 100.0 / (form.studyDuration?.partyTotal
                        ?: 1)).roundToLong(),
                    suffix = "%"
                )
            )
            // 每季一考
            this[4] = listOf(
                CardFlop(
                    name = "每季一考参与率",
                    value = ((form.studyDuration?.quarterTotal ?: 0) * 100.0 / (form.studyDuration?.partyTotal
                        ?: 1)).roundToLong(),
                    suffix = "%"
                )
            )
        }
        return studyVO
    }

    @HttpMonitorLogger
    @GetMapping("/v1/task")
    @ApiOperation("党务工作-系统任务")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun task(@RequestHeader headers: HttpHeaders): TaskVO {
        val taskVO = TaskVO()
        val sysHeader = HeaderHelper.buildMyHeader(headers)
        val form = partyUsersService.task(sysHeader)
        val map = linkedMapOf(
            "党建任务" to "partyTotal",
            "业务任务" to "businessTotal",
            "创新任务" to "innovationTotal",
            "云上任务" to "ecpTotal",
            "发布任务数" to "total",
            "已完成" to "doneTotal",
            "正在进行" to "doingTotal",
            "党员参与人次" to "partyAttendTotal",
            "非党员参与人次" to "otherAttendTotal",
        )
        with(taskVO.cardFlop) {
            // 党建任务、业务任务、创新任务、云上任务、发布任务数、已完成、正在进行、党员参与人次、非党员参与人次
            // 在kotlin中反射Person对象的属性
            // 获取Class实例
            val clazz = form::class.java
            map.onEachIndexed { index, entry ->
                // 获取name属性
                val field = clazz.getDeclaredField(entry.value)
                // 设置可访问性，因为name是私有的
                field.isAccessible = true
                // 获取name属性的值
                val value = field.get(form) as Int?
                // 加入翻牌器
                this[index] = listOf(CardFlop(name = entry.key, value = (value ?: 0).toLong()))
            }
        }
        // 进度条
        with(taskVO.progressBar) {
            // 已完成进度
            this[0] = listOf(
                ProgressBar(value = form.doneTotal.toLong(), sum = form.doneTotal.toLong() + form.doingTotal)
            )
            // 党员参与人次进度
            this[1] = listOf(
                ProgressBar(
                    value = form.partyAttendTotal.toLong(),
                    sum = form.partyAttendTotal.toLong() + form.otherAttendTotal
                )
            )
        }
        return taskVO
    }

    @HttpMonitorLogger
    @GetMapping("/v1/bubble")
    @ApiOperation("党务工作-呼吸气泡地图")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun bubbleMap(@RequestHeader headers: HttpHeaders): List<MapChart> {
        val list = mutableListOf<MapChart>()
        overviewService.unitLngAndLat(excludeOrgConfig.orgIds)?.forEach {
            if (it["lng"] != null && it["lat"] != null) {
                list.add(
                    MapChart(
                        lng = it["lng"] as Double,
                        lat = it["lat"] as Double,
                        name = it["unit_id"].toString(),
                    )
                )
            } else {
                log.warn("党务工作-呼吸气泡地图->单位[${it["unit_id"]}:${"uid"}]没有配置经纬度")
            }
        }
        return list
    }

    @HttpMonitorLogger
    @GetMapping("/v1/histogram")
    @ApiOperation("党务工作-柱状图地图")
    @Cacheable(value = ["datav-cache"], key = "#root.method.name") // 接口缓存，需要在定时器ClearCacheScheduler配置清理
    fun histogramMap(@RequestHeader headers: HttpHeaders): List<MapChart> {
        val list = mutableListOf<MapChart>()
        val lngLats = overviewService.unitLngAndLat(excludeOrgConfig.orgIds)
        val scores = scoreService.queryUnitYearScore(headers)
        // 转换成快速查询的map， key -> unit_id
        val lngLatsMap = lngLats.associateBy { it["unit_id"].toString().toLong() }
        val scoresMap = scores.associateBy { it.unitId }
        // 通过unit_id 取交集
        //  lngLats.map { it["unit_id"] as Long }.intersect(scores.map { it.unitId }.toSet()).
        (lngLatsMap.keys.intersect(scoresMap.keys)).forEach {
            if (lngLatsMap[it]?.get("lng") != null && lngLatsMap[it]?.get("lat") != null) {
                // 封装实际的数据
                list.add(
                    MapChart(
                        lng = lngLatsMap[it]!!["lng"] as Double,
                        lat = lngLatsMap[it]!!["lat"] as Double,
                        name = it.toString(),
                        sizeField = (scoresMap[it]?.score ?: 0).toDouble()
                    )
                )
            }
        }
        return list
    }
}