package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.PpmdPayInfo;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.activity.ActivityService;
import com.goodsogood.ows.service.electronicreport.ElectronicReportServices;
import com.goodsogood.ows.service.electronicreport.OrgReportService;
import com.goodsogood.ows.service.meeting.MeetingReportService;
import com.goodsogood.ows.service.ppmd.OrgPayService;
import com.goodsogood.ows.service.ppmd.PpmdReportService;
import com.goodsogood.ows.service.score.ScoreMasterService;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @description 初始化电子党务报告控制层
 * @date 2019/11/20
 */
@RestController
@RequestMapping("/sas/report")
@Log4j2
public class InitReportController {

    private final Errors errors;
    private final PpmdReportService ppmdReportService;
    private final OrgService orgService;
    private final MeetingReportService meetingReportService;
    private final OrgReportService orgReportService;
    private final OrgPayService orgPayService;
    private final ActivityService activityService;
    private final ScoreMasterService scoreMasterService;
    private final ElectronicReportServices createPersonElectronicReport;

    @Autowired
    public InitReportController(Errors errors, PpmdReportService ppmdReportService,
                                OrgService orgService, MeetingReportService meetingReportService,
                                OrgReportService orgReportService, OrgPayService orgPayService,
                                ActivityService activityService, ScoreMasterService scoreMasterService,
                                ElectronicReportServices createPersonElectronicReport) {
        this.errors = errors;
        this.ppmdReportService = ppmdReportService;
        this.orgService = orgService;
        this.meetingReportService = meetingReportService;
        this.orgReportService = orgReportService;
        this.orgPayService = orgPayService;
        this.activityService = activityService;
        this.scoreMasterService = scoreMasterService;
        this.createPersonElectronicReport = createPersonElectronicReport;
    }

    @HttpMonitorLogger
    @GetMapping("/getPpmdReport")
    public ResponseEntity<Result<?>> testPpmdReport(@RequestParam(value = "org_id") Long orgId,
                                                    @RequestParam(value = "date") String date) {
        OrganizationEntity org = this.orgService.getById(orgId);
        CountDownLatch latch = new CountDownLatch(1);
        PpmdPayInfo info = new PpmdPayInfo();
        try {
            info = this.ppmdReportService.getPpmdReportByOrgId(org, date, new Date(), latch).get();
            latch.await();
        } catch (InterruptedException e) {
            log.error("计数器发送线程错误", e);
            Thread.currentThread().interrupt();
        } catch (ExecutionException e) {
            log.error("计数器异常", e);
        }
        return new ResponseEntity<>(new Result<>(info, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/createPpmdReport")
    public ResponseEntity<Result<?>> testPpmdReport(@RequestParam(value = "date", required = false) String date) {
        if (StringUtils.isNotBlank(date)) {
            this.ppmdReportService.generatePpmdReport(date);
        } else {
            List<String> queryTimes = this.getQueryTime();
            queryTimes.forEach(this.ppmdReportService::generatePpmdReport);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/createMeetingReport")
    public ResponseEntity<Result<?>> meetingReport(@RequestParam(value = "date", required = false) String date) {
        if (StringUtils.isNotBlank(date)) {
            this.meetingReportService.meetingReport(date);
        } else {
            List<String> queryTimes = this.getQueryTime();
            queryTimes.forEach(this.meetingReportService::meetingReport);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/orgReport")
    public ResponseEntity<Result<?>> getOrgReport(@RequestParam(value = "date", required = false) String date) {
        if (StringUtils.isNotBlank(date)) {
            this.orgReportService.runOrgReport(date);
        } else {
            List<String> queryTimes = this.getQueryTime();
            queryTimes.forEach(queryTime -> {
                this.orgReportService.runOrgReport(queryTime);
            });
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/initActivePay")
    public ResponseEntity<Result<?>> initActivePay(@RequestParam(value = "org_id", required = false) Long orgId,
                                                   @RequestParam(value = "date", required = false) String date) {
        List<OrganizationEntity> orgList = new ArrayList<>();
        if (orgId == null) {
            orgList = this.orgService.getChildPartyOrg(Constants.ROOT_ORG_ID, Constants.INCLUDE);
        } else {
            orgList.add(this.orgService.getById(orgId));
        }
        PpmdPayInfo payInfo = new PpmdPayInfo();
        orgList.forEach(org -> {
            if (StringUtils.isNotBlank(date)) {
                this.orgPayService.getActivePayOrg(org.getOrganizationId(), org.getOrgLevel(), date, payInfo);
            } else {
                List<String> queryTimes = this.getQueryTime();
                queryTimes.forEach(queryTime -> {
                    this.orgPayService.getActivePayOrg(org.getOrganizationId(), org.getOrgLevel(), queryTime, payInfo);
                });
            }
        });

        return new ResponseEntity<>(new Result<>(payInfo, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/initModuleReport")
    public ResponseEntity<Result<?>> initModuleReport(@RequestParam(value = "date", required = false) String date) {
        log.debug("【电子党务报告】开始， 拉取时间 -> [{}]! 当前时间-> [{}]", date, new Date());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        try {
            if (StringUtils.isNotBlank(date)) {
                // 拉取党群活动数据
                this.activityService.buildActivityStaInfo(date);
                log.debug("【电子党务报告】，党群活动拉取完成，开始拉取党费 -> [{}]! 当前时间-> [{}]", date, new Date());
                // 拉取党费数据
                this.ppmdReportService.generatePpmdReport(date);
                log.debug("【电子党务报告】，党费数据拉取完成，开始拉取组织生活 -> [{}]! 当前时间-> [{}]", date, new Date());
                // 拉取组织生活数据
                this.meetingReportService.meetingReport(date);
                log.debug("【电子党务报告】，组织生活拉取完成，开始拉取积分 -> [{}]! 当前时间-> [{}]", date, new Date());
                // 拉取积分数据
                this.scoreMasterService.runOfScore(format.parse(date));
                log.debug("【电子党务报告】，积分数据拉取完成 -> [{}]! 当前时间-> [{}]", date, new Date());
            } else {
                List<String> queryTimes = this.getQueryTime();
                // 拉取党群活动数据
                this.activityService.buildActivityStaInfo(true);
                log.debug("【电子党务报告】，党群活动拉取完成，开始拉取党费 -> [{}]! 当前时间-> [{}]", date, new Date());
                queryTimes.forEach(queryTime -> {
                    // 拉取党费数据
                    log.debug("【电子党务报告】，开始拉取党费 -> [{}]! 当前时间-> [{}]", queryTime, new Date());
                    this.ppmdReportService.generatePpmdReport(queryTime);
                    log.debug("【电子党务报告】，党费拉取完成，开始拉取组织生活 -> [{}]! 当前时间-> [{}]", queryTime, new Date());
                    // 拉取组织生活数据
                    this.meetingReportService.meetingReport(queryTime);
                    log.debug("【电子党务报告】，组织生活拉取完成，开始拉取积分 -> [{}]! 当前时间-> [{}]", queryTime, new Date());
                    // 拉取积分数据
                    try {
                        this.scoreMasterService.runOfScore(format.parse(queryTime));
                    } catch (Exception e) {
                        log.error("①拉取数据错误...", e);
                    }
                    log.debug("【电子党务报告】，积分拉取完成 -> [{}]! 当前时间-> [{}]", queryTime, new Date());
                });
            }
        } catch (Exception e) {
            log.error("拉取数据错误...", e);
        } finally {
            log.debug("【电子党务报告】关闭! 当前时间-> [{}]", new Date());
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/initReport")
    public ResponseEntity<Result<?>> initReport(@RequestParam(value = "date", required = false) String date,
                                                @RequestParam(value = "region_id") Long regionId) {
        log.debug("【电子党务报告】开始， 拉取时间 -> [{}]! 当前时间-> [{}]", date, new Date());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        try {
            if (StringUtils.isNotBlank(date)) {
                // 生成组织党务报表
                this.orgReportService.runOrgReport(date);
                log.debug("【电子党务报告】，组织党务报告完成，开始生成人员报告 -> [{}]! 当前时间-> [{}]", date, new Date());
                // 生成人员党务报表
                this.createPersonElectronicReport.createPersonElectronicReportByMonth(regionId, date);
                log.debug("【电子党务报告】，生成人员报告完成 -> [{}]! 当前时间-> [{}]", date, new Date());
            } else {
                List<String> queryTimes = this.getQueryTime();
                queryTimes.forEach(queryTime -> {
                    // 生成组织党务报表
                    this.orgReportService.runOrgReport(queryTime);
                    log.debug("【电子党务报告】，[{}] 组织党务报告完成 ->! 当前时间-> [{}]", queryTime, new Date());
                });
                log.debug("【电子党务报告】，组织党务报告完成，开始生成人员报告 ! 当前时间-> [{}]", new Date());
                // 生成人员党务报表
                this.createPersonElectronicReport.createPersonElectronicReport(true);
                log.debug("【电子党务报告】，生成人员报告完成 -> [{}]! 当前时间-> [{}]", date, new Date());
            }
        } catch (Exception e) {
            log.error("拉取数据错误...", e);
        } finally {
            log.debug("【电子党务报告】关闭! 当前时间-> [{}]", new Date());
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    private List<String> getQueryTime() {
        String beginDate = "2019-01";
        List<String> rangeSet = null;
        SimpleDateFormat sdf = null;
        Date begin_date = null;
        Date end_date = new Date();
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTime(end_date);
        // 设置为上一个月
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
        end_date = calendar.getTime();
        rangeSet = new java.util.ArrayList<String>();
        sdf = new SimpleDateFormat("yyyy-MM");
        try {
            //定义起始日期
            begin_date = sdf.parse(beginDate);
        } catch (ParseException e) {
            System.out.println("时间转化异常，请检查你的时间格式是否为yyyy-MM或yyyy-MM-dd");
        }
        //定义日期实例
        Calendar dd = Calendar.getInstance();
        //设置日期起始时间
        dd.setTime(begin_date);
        //判断是否到结束日期
        while (!dd.getTime().after(end_date)) {
            rangeSet.add(sdf.format(dd.getTime()));
            //进行当前日期月份加1
            dd.add(Calendar.MONTH, 1);
        }
        return rangeSet;
    }
}
