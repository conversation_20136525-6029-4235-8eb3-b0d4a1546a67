package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.db.doris.IndexOrgScoreEntity;
import com.goodsogood.ows.model.db.doris.IndexUserScoreEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.dss.DssDorisScoreService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/doris")
public class DssDorisScoreController {
    private final DssDorisScoreService dssDorisScoreService;
    private final Errors errors;

    @Autowired
    public DssDorisScoreController(DssDorisScoreService dssDorisScoreService, Errors errors) {
        this.dssDorisScoreService = dssDorisScoreService;
        this.errors = errors;
    }

    @HttpMonitorLogger
    @PostMapping("/user-score")
    @ApiOperation("增加人员指标分值")
    public ResponseEntity<Result<Boolean>> insertDorisUserScore(@RequestHeader HttpHeaders headers, @RequestBody List<IndexUserScoreEntity> dataList) {
        dssDorisScoreService.insertOutUserScore(dataList);
        return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/org-score")
    @ApiOperation("增加人员指标分值")
    public ResponseEntity<Result<Boolean>> insertDorisOrgScore(@RequestHeader HttpHeaders headers, @RequestBody List<IndexOrgScoreEntity> dataList) {
        dssDorisScoreService.insertOuterOrgScore(dataList);
        return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/his-user-score")
    @ApiOperation("将人员积分历史数据写入doris")
    public ResponseEntity<Result<Boolean>> insertDorisUserScore(@RequestHeader HttpHeaders headers,
                                                                @RequestParam(value = "year", required = false) Integer year,
                                                                @RequestParam(value = "data_month", required = false) String dataMonth) {
        if (year == null) {
            dssDorisScoreService.insertHisUserMonth(dataMonth);
        } else {
            dssDorisScoreService.insertUserHis(year);
        }
        return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/his-org-score")
    @ApiOperation("将机构积分历史数据写入doris")
    public ResponseEntity<Result<Boolean>> insertDorisOrgScore(@RequestHeader HttpHeaders headers,
                                                               @RequestParam(value = "year", required = false) Integer year,
                                                               @RequestParam(value = "data_month", required = false) String dataMonth
    ) {
        if (year == null) {
            dssDorisScoreService.insertHisOrgMonth(dataMonth);
        } else {
            dssDorisScoreService.insertOrgHis(year);
        }
        return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/remove-score-record")
    @ApiOperation("将doris中的积分数据删除")
    public ResponseEntity<Result<Integer>> removeScoreRecord(@RequestHeader HttpHeaders headers,
                                                             @RequestParam(value = "flag", required = false,defaultValue = "1") Integer flag,
                                                             @RequestParam(value = "data_month", required = false) String dataMonth,
                                                             @RequestParam(value = "rule_id", required = false) Long ruleId,
                                                             @RequestParam(value = "id", required = false) Long id
    ) {
        Integer row = dssDorisScoreService.removeScoreRecord(flag, dataMonth, ruleId, id);
        return new ResponseEntity<>(new Result<>(row, errors), HttpStatus.OK);
    }
}
