package com.goodsogood.ows.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.StaDualLifeViewScheduler;
import com.goodsogood.ows.component.StaMeetingScheduler;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.LogHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.mapper.experience.UserPartyEvalDetailMapper;
import com.goodsogood.ows.mapper.sas.StatisticalOrgLifeMapper;
import com.goodsogood.ows.model.db.eval.v2.MetricEntity;
import com.goodsogood.ows.model.db.experience.UserPartyEvalDetailEntity;
import com.goodsogood.ows.model.db.sas.StatisticalDataRecordEntity;
import com.goodsogood.ows.model.db.sas.StatisticalOrgLifeEntity;
import com.goodsogood.ows.model.db.user.OptionEntity;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.report.UserScoreStudyReport;
import com.goodsogood.ows.model.vo.MessageVO;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.activity.ActivityOrgElectronicReport;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.mongodb.QueryMongoInfoForm;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.model.vo.report.PersonElectronicReport;
import com.goodsogood.ows.model.vo.sas.*;
import com.goodsogood.ows.model.vo.score.ScoreOrgElectronicReport;
import com.goodsogood.ows.service.activity.ActivityDetailDonateService;
import com.goodsogood.ows.service.activity.ActivityService;
import com.goodsogood.ows.service.adb.QLService;
import com.goodsogood.ows.service.electronicreport.ElectronicReportServices;
import com.goodsogood.ows.service.eval.v2.*;
import com.goodsogood.ows.service.experience.UserTaskService;
import com.goodsogood.ows.service.finereport.OrgInfoReportService;
import com.goodsogood.ows.service.meeting.StaMeetingService;
import com.goodsogood.ows.service.mongodb.QueryMongoInfoService;
import com.goodsogood.ows.service.mongodb.UserScoreReportService;
import com.goodsogood.ows.service.operation.DataSourceService;
import com.goodsogood.ows.service.ppmd.OrgPayService;
import com.goodsogood.ows.service.ppmd.PpmdReportService;
import com.goodsogood.ows.service.pull.PullDataService;
import com.goodsogood.ows.service.sas.*;
import com.goodsogood.ows.service.score.*;
import com.goodsogood.ows.service.supervise.SuperviseService;
import com.goodsogood.ows.service.tbcFusion.TbcBasicsService;
import com.goodsogood.ows.service.tbcFusion.TbcGetService;
import com.goodsogood.ows.service.tbcFusion.TbcMatchingService;
import com.goodsogood.ows.service.tbcFusion.TbcPartyBranchWordService;
import com.goodsogood.ows.service.user.*;
import com.goodsogood.ows.utils.JsonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.validation.Valid;
import java.lang.reflect.InvocationTargetException;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/sas/test")
@Log4j2
@Validated
public class TestController {

    private final Errors errors;
    private final OptionService optionService;
    private final LeaderService leaderService;
    private final StaMeetingService meetingService;
    private final StatisticalOrgLifeService statisticalOrgLifeService;
    private final StatisticalOrgLifeMapper statisticalOrgLifeMapper;
    private final PartyFeeService partyFeeService;
    private final LeaderOrgMeetingFeeService meetingFeeService;
    private final StaMeetingScheduler staMeetingScheduler;
    private final StatisticalOrgLifeViewCountService statisticalOrgLifeViewCountService;
    private final StatisticalUserOrgLifeService statisticalUserOrgLifeService;
    private final StaDualLifeViewScheduler staDualLifeViewScheduler;
    private final UserCenterService userCenterService;
    private final WorkReportService workReportService;
    private final MonthCollectService monthCollectService;
    private final StringRedisTemplate redisTemplate;
    private final RestTemplate restTemplate;
    private final TogServicesConfig togServicesConfig;
    private final WorkRemindService workRemindService;
    private final PushDataService pushDataService;
    private final ScoreDetailStudyService scoreDetailStudyService;
    private final ScoreDetailBookService scoreDetailBookService;
    private final ActivityDetailDonateService activityDetailDonateService;
    private final ScorePovertyService scorePovertyService;
    private final ScoreMasterService scoreMasterService;
    private final OrgPayService orgPayService;
    private final PpmdReportService ppmdReportService;
    private final OrgService orgService;
    private final ActivityService activityService;
    private final ElectronicReportServices electronicReportServices;
    private final UserScoreReportService userScoreReportService;
    private final ScoreService scoreService;
    private final QueryMongoInfoService queryMongoInfoService;
    private final AsyncWorkReportService asyncWorkReportService;
    private final OrgInfoReportService orgInfoReportService;
    private final UserService userService;
    private final PullDataService pullDataService;
    private final OrgHighlightService orgHighlightService;
    private final SuperviseService superviseService;
    private final TbcBasicsService tbcBasicsService;
    private final TbcGetService tbcGetService;
    private final TbcMatchingService tbcMatchingService;
    private final TbcPartyBranchWordService tbcPartyBranchWordService;
    private final UserTaskService userTaskService;
    private final UserPartyEvalDetailMapper userPartyEvalDetailMapper;
    private final DataSourceService dataSourceService;
    private final QLService qlService;

    private final RevisitStrategyImpl revisitStrategy;

    private final PPMDStrategyImpl ppmdStrategy;

    private final LogHelper logHelper;

    private final StudyClassStrategyImpl studyClassStrategy;

    private final TheoryStudyClassStrategyImpl theoryStudyClassStrategy;

    private final SettingOrgInfoStrategyImpl settingOrgInfoStrategy;

    private final SettingOrgInfoPeriodStrategyImpl settingOrgInfoPeriodStrategy;

    @Autowired
    public TestController(
            Errors errors,
            OptionService optionService, LeaderService leaderService,
            StaMeetingService meetingService, StatisticalOrgLifeService statisticalOrgLifeService,
            StatisticalOrgLifeMapper statisticalOrgLifeMapper,
            PartyFeeService partyFeeService,
            LeaderOrgMeetingFeeService meetingFeeService,
            StaMeetingScheduler staMeetingScheduler,
            StatisticalOrgLifeViewCountService statisticalOrgLifeViewCountService,
            StatisticalUserOrgLifeService statisticalUserOrgLifeService,
            StaDualLifeViewScheduler staDualLifeViewScheduler,
            UserCenterService userCenterService,
            WorkReportService workReportService,
            MonthCollectService monthCollectService,
            StringRedisTemplate redisTemplate,
            PushDataService pushDataService,
            WorkRemindService workRemindService,
            RestTemplate restTemplate,
            TogServicesConfig togServicesConfig,
            ActivityService activityService,
            ScoreDetailStudyService scoreDetailStudyService,
            ScoreDetailBookService scoreDetailBookService,
            ActivityDetailDonateService activityDetailDonateService,
            ScorePovertyService scorePovertyService,
            ScoreMasterService scoreMasterService,
            OrgPayService orgPayService,
            PpmdReportService ppmdReportService,
            OrgService orgService,
            UserScoreReportService userScoreReportService,
            ElectronicReportServices electronicReportServices,
            ScoreService scoreService,
            QueryMongoInfoService queryMongoInfoService,
            AsyncWorkReportService asyncWorkReportService,
            OrgInfoReportService orgInfoReportService,
            UserService userService,
            PullDataService pullDataService,
            OrgHighlightService orgHighlightService,
            SuperviseService superviseService,
            TbcBasicsService tbcBasicsService,
            TbcGetService tbcGetService,
            TbcMatchingService tbcMatchingService,
            TbcPartyBranchWordService tbcPartyBranchWordService,
            UserTaskService userTaskService,
            UserPartyEvalDetailMapper userPartyEvalDetailMapper,
            DataSourceService dataSourceService,
            QLService qlService, RevisitStrategyImpl revisitStrategy,
            PPMDStrategyImpl ppmdStrategy,
            LogHelper logHelper, StudyClassStrategyImpl studyClassStrategy,
            TheoryStudyClassStrategyImpl theoryStudyClassStrategy,
            SettingOrgInfoStrategyImpl settingOrgInfoStrategy, SettingOrgInfoPeriodStrategyImpl settingOrgInfoPeriodStrategy) {
        this.optionService = optionService;
        this.leaderService = leaderService;
        this.meetingService = meetingService;
        this.errors = errors;
        this.statisticalOrgLifeService = statisticalOrgLifeService;
        this.statisticalOrgLifeMapper = statisticalOrgLifeMapper;
        this.partyFeeService = partyFeeService;
        this.meetingFeeService = meetingFeeService;
        this.staMeetingScheduler = staMeetingScheduler;
        this.statisticalOrgLifeViewCountService = statisticalOrgLifeViewCountService;
        this.statisticalUserOrgLifeService = statisticalUserOrgLifeService;
        this.staDualLifeViewScheduler = staDualLifeViewScheduler;
        this.userCenterService = userCenterService;
        this.workReportService = workReportService;
        this.monthCollectService = monthCollectService;
        this.redisTemplate = redisTemplate;
        this.pushDataService = pushDataService;
        this.workRemindService = workRemindService;
        this.restTemplate = restTemplate;
        this.togServicesConfig = togServicesConfig;
        this.activityService = activityService;
        this.scoreDetailStudyService = scoreDetailStudyService;
        this.scoreDetailBookService = scoreDetailBookService;
        this.activityDetailDonateService = activityDetailDonateService;
        this.scorePovertyService = scorePovertyService;
        this.scoreMasterService = scoreMasterService;
        this.orgPayService = orgPayService;
        this.ppmdReportService = ppmdReportService;
        this.orgService = orgService;
        this.electronicReportServices = electronicReportServices;
        this.userScoreReportService = userScoreReportService;
        this.scoreService = scoreService;
        this.queryMongoInfoService = queryMongoInfoService;
        this.asyncWorkReportService = asyncWorkReportService;
        this.orgInfoReportService = orgInfoReportService;
        this.userService = userService;
        this.pullDataService = pullDataService;
        this.orgHighlightService = orgHighlightService;
        this.superviseService = superviseService;
        this.tbcBasicsService = tbcBasicsService;
        this.tbcGetService = tbcGetService;
        this.tbcMatchingService = tbcMatchingService;
        this.tbcPartyBranchWordService = tbcPartyBranchWordService;
        this.userTaskService = userTaskService;
        this.userPartyEvalDetailMapper = userPartyEvalDetailMapper;
        this.dataSourceService = dataSourceService;
        this.qlService = qlService;
        this.revisitStrategy = revisitStrategy;
        this.ppmdStrategy = ppmdStrategy;
        this.logHelper = logHelper;
        this.studyClassStrategy = studyClassStrategy;
        this.theoryStudyClassStrategy = theoryStudyClassStrategy;
        this.settingOrgInfoStrategy = settingOrgInfoStrategy;
        this.settingOrgInfoPeriodStrategy = settingOrgInfoPeriodStrategy;
    }

    @HttpMonitorLogger
    @GetMapping("/testOrg")
    @ApiOperation("测试调用用户中心组织接口")
    public ResponseEntity<Result<?>> testGetOrg(@RequestParam(value = "org_type", required = false) Long orgType,
                                                @RequestParam(value = "org_type_child", required = false) Long orgTypeChild,
                                                @RequestParam(value = "region_id", required = false) Long regionId) {
        List<OrganizationForm> orgList = this.orgService.findOrgByType(orgType, orgTypeChild, regionId);
        return new ResponseEntity<>(new Result<>(orgList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testOrgInfoIndex")
    @ApiOperation("决策首页")
    public ResponseEntity<Result<?>> testOrgInfoIndex() {
        IndexInfo indexInfo = new IndexInfo();
        indexInfo.setRootId(3L);
        indexInfo.setYear(2020);
        IndexInfo result = orgInfoReportService.buildIndex(indexInfo);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/testPartyBranch")
    @ApiOperation("决策党支部")
    public ResponseEntity<Result<?>> testPartyBranch() {
        PartyBranchInfo partyBranchInfo = new PartyBranchInfo();
        partyBranchInfo.setOrganizationId(1045666L);
        partyBranchInfo.setYear(2020);
        PartyBranchInfo result = orgInfoReportService.buildPartyBranch(partyBranchInfo);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/testPartyBranchList")
    @ApiOperation("决策党支部")
    public ResponseEntity<Result<?>> testPartyBranchList() {
        PartyBranchInfo partyBranchInfo = new PartyBranchInfo();
        partyBranchInfo.setOrganizationId(104104L);
        partyBranchInfo.setYear(2020);
        PartyBranchInfo partyBranchInfo2 = new PartyBranchInfo();
        partyBranchInfo2.setOrganizationId(105105L);
        partyBranchInfo2.setYear(2020);

        List<PartyBranchInfo> list = new ArrayList<>();
        list.add(partyBranchInfo);
        list.add(partyBranchInfo2);

        List<PartyBranchInfo> result = orgInfoReportService.buildPartyBranchList(list);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testPartyCommittee")
    @ApiOperation("决策党委")
    public ResponseEntity<Result<?>> testPartyCommittee() {
        PartyCommitteeInfo partyCommitteeInfo = new PartyCommitteeInfo();
        partyCommitteeInfo.setOrganizationId(1045666L);
        partyCommitteeInfo.setYear(2020);
        PartyCommitteeInfo result = orgInfoReportService.buildPartyCommittee(partyCommitteeInfo);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/testPartyCommitteeList")
    @ApiOperation("决策党委")
    public ResponseEntity<Result<?>> testPartyCommitteeList() {
        PartyCommitteeInfo partyCommitteeInfo = new PartyCommitteeInfo();
        partyCommitteeInfo.setOrganizationId(104L);
        partyCommitteeInfo.setYear(2020);

        PartyCommitteeInfo partyCommitteeInfo2 = new PartyCommitteeInfo();
        partyCommitteeInfo2.setOrganizationId(105L);
        partyCommitteeInfo2.setYear(2020);
        List<PartyCommitteeInfo> list = new ArrayList<>();
        list.add(partyCommitteeInfo);
        list.add(partyCommitteeInfo2);
        List<PartyCommitteeInfo> result = orgInfoReportService.buildPartyCommitteeList(list);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/testSql")
    @ApiOperation("测试调用用户中心组织接口")
    public ResponseEntity<Result<?>> testMeeting() {
        Page<StasticReport> staReportInfo = statisticalOrgLifeService.getStaReportInfoPage(1, 20);
        return new ResponseEntity<>(new Result<>(staReportInfo, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testMeetingScheduler")
    @ApiOperation("测试调用用户中心组织接口")
    public ResponseEntity<Result<?>> testMeetingScheduler() {
        List<StatisticalOrgLifeEntity> listOrgLife = new ArrayList<>();
        List<StatisticalDataRecordEntity> listDataRecord = new ArrayList<>();
        //获取所有组织信息
        List<OrganizationForm> list = orgService.findOrgByType(null, null, null);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> {
                List<StatisticsMeetingForm> organizeStaInfo = this.meetingService.getOrganizeStaInfo(item.getOrganizationId(),
                        0L, 0L, item.getRegionId());
                if (!CollectionUtils.isEmpty(organizeStaInfo)) {
                    for (int i = 0; i < organizeStaInfo.size(); i++) {
                        StatisticalOrgLifeEntity statisticalOrgLifeEntity = staMeetingScheduler.setStatisticalOrgLifeEntity(item, organizeStaInfo.get(i));
                        if (null == statisticalOrgLifeEntity) {
                            continue;
                        } else {
                            listOrgLife.add(statisticalOrgLifeEntity);
                        }
                    }
                }
            });
        }

        if (!CollectionUtils.isEmpty(listOrgLife)) {
            statisticalOrgLifeMapper.insertList(listOrgLife);
        }
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testPPMD")
    @ApiOperation("测试调用用户中心组织接口")
    public ResponseEntity<Result<?>> testPPMD(@RequestParam(value = "org_ids", required = false) List<Long> orgIds, @RequestParam(value = "query_date", required = false) String queryDate) throws ParseException {
        SasOrgPayForm form = new SasOrgPayForm();
        form.setDate(queryDate);
        form.setOrgIds(orgIds);
        List<SasOrgPaySituationForm> orgPaySituation = this.orgPayService.getOrgPaySituation(form);
        return new ResponseEntity<>(new Result<>(orgPaySituation, errors), HttpStatus.OK);
    }

    /**
     * query_date 1-全部时间， 2-1年， 3-3个月， 4-1个月，null-6个月
     *
     * @param queryDate
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/testLeaderOrgMeeting")
    @ApiOperation("测试调用用户中心组织接口")
    public ResponseEntity<Result<?>> testLeaderOrgMeeting(@RequestParam(value = "query_date", required = false) Long queryDate) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            // 获取当前月份
            Date date = new Date();
            this.meetingFeeService.leaderOrgMeetingFeeMain(date, stopWatch, queryDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/optionList")
    @ApiOperation("测试调用用户中心组织列表接口")
    public ResponseEntity<Result<?>> testGetOrgList() {
        List<OptionEntity> orgList = this.optionService.getOptionList(String.valueOf(Constants.ORG_TYPE_COMMUNIST));
        return new ResponseEntity<>(new Result<>(orgList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testGetOrg")
    @ApiOperation("测试调用活动中心活动列表接口")
    public ResponseEntity<Result<List<TypeAllResultForm>>> testGetOrg() {
        List<TypeAllResultForm> activityList = this.meetingService.getMeetingTypeInfo();
        Result<List<TypeAllResultForm>> result = new Result<>(activityList, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testGetLeader")
    @ApiOperation("测试调用领导干部列表接口")
    public ResponseEntity<Result<List<?>>> testGetLeader() {
        List<SasLeaderOrgForm> leader = this.leaderService.findAllLeader();
        return new ResponseEntity<>(new Result<>(leader, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testOrganizeInfo")
    @ApiOperation("测试统计组织生活")
    public String testOrganizeInfo(@RequestParam(value = "sta_code", required = false) Long staCode,
                                   @RequestParam Integer isSup) {
        //完成过后调用考核系统 让考核系统启动定时任务
        String url = String.format("http://%s/eval/init/pullData", togServicesConfig.getEval());
        HttpHeaders headers = new HttpHeaders();
        try {
            staMeetingScheduler.staMeeting(staCode, isSup == 1);
            String result = RemoteApiHelper.get(restTemplate, url, headers, new TypeReference<Result<String>>() {
            });
            log.error("调用远程考核服务接口地址-{}, 结果-{}", url, result);
        } catch (Exception e) {
            log.error("调用远程考核服务接口地址-{}, 错误内容-{}", url, e.getMessage());
        }
        return "success";
    }

    @HttpMonitorLogger
    @GetMapping("/testOrganizeInfoId")
    @ApiOperation("测试统计组织生活")
    public String testOrganizeInfo(@RequestParam(value = "sta_code", required = false) Long staCode,
                                   @RequestParam Integer isSup, @RequestParam Long orgId) {
        staMeetingScheduler.staMeeting(staCode, isSup == 1, orgId);
        return "success";
    }


    @HttpMonitorLogger
    @GetMapping("/supplementOrgInfo")
    @ApiOperation("补录组织信息")
    public String supplementOrgInfo(@RequestParam(value = "is_init", required = false) Long isInit) {
        staMeetingScheduler.supplementOrgInfo(isInit > 0);
        return "success";
    }

    @HttpMonitorLogger
    @GetMapping("/supplementOrgInfo2")
    @ApiOperation("补录具体那个组织")
    public String supplementOrgInfo2(@RequestParam(value = "is_init", required = false) Long isInit,
                                     @RequestParam(value = "org_id", required = false) Long orgId) {
        staMeetingScheduler.supplementOrgInfo(isInit > 0, orgId);
        return "success";
    }


//    @HttpMonitorLogger
//    @GetMapping("/resetPeriodCreateTime")
//    @ApiOperation("刷新组织的支委会创建时间")
//    public String resetPeriodCreateTime() {
//        staMeetingScheduler.resetPeriodCreateTime();
//        return "success";
//    }

    @HttpMonitorLogger
    @GetMapping("/testMeetingUserInfo")
    @ApiOperation("测试调用领导干部列表接口")
    public ResponseEntity<Result<List<?>>> testMeetingUserInfo(@RequestParam(value = "user_id") Long userId, @RequestParam(value = "sta_code", required = false) Long staCode, @RequestParam(name = "sign_status", required = false) String signStatus) {
        List<StatisticsMeetingForm> statisticsOrganizeUserInfo = this.meetingService.getStatisticsOrganizeUserInfo(3L, userId, staCode, signStatus);
        return new ResponseEntity<>(new Result<>(statisticsOrganizeUserInfo, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/orgInfoView")
    @ApiOperation("组织生活一览表")
    public String orgInfoView() {
        statisticalOrgLifeViewCountService.sasOrgLifeViewCount();
        return "success";
    }

    @HttpMonitorLogger
    @GetMapping("/sasUserOrgLife")
    @ApiOperation("双重组织生活统计")
    public String sasUserOrgLife() {
        staDualLifeViewScheduler.sasOrgLifeViewCount();
        return "success";
    }

    @HttpMonitorLogger
    @GetMapping("/testBirthday")
    @ApiOperation("测试生日推送")
    public ResponseEntity<Result<?>> testBirthday(@RequestParam(value = "org_id") Long orgId) {
        MemberResultForm memberResultForms = userCenterService.findMemberByDate(orgId);
        return new ResponseEntity<>(new Result<>(memberResultForms, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testManager")
    @ApiOperation("测试管理员推送")
    public ResponseEntity<Result<?>> testManager(@RequestParam(value = "org_id") Long orgId, @RequestParam(value = "role_type") Integer roleType) {
        List<UserInfoBase> userInfoBaseList = userCenterService.findManagerByWhere(orgId, roleType);
        return new ResponseEntity<>(new Result<>(userInfoBaseList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testChange")
    @ApiOperation("测试换届")
    public ResponseEntity<Result<?>> testChange(@RequestParam(value = "type") Integer type, @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        List<OrgPeriodWarningDetailForm> orgPeriodWarningDetailFormList = userCenterService.findExpireApp(sysHeader.getOid(), type, sysHeader.getRegionId());
        return new ResponseEntity<>(new Result<>(orgPeriodWarningDetailFormList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testLastChange")
    @ApiOperation("测试上个月支部换届数量")
    public ResponseEntity<Result<?>> testLastChange(@RequestParam(value = "org_id_list") String orgIdList) {
        Integer count = userCenterService.lastMonthChange(orgIdList);
        return new ResponseEntity<>(new Result<>(count, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testOrgWorkReport")
    @ApiOperation("测试组织工作报告")
    public ResponseEntity<Result<?>> testOrgWorkReport(@RequestParam(value = "org_id") Long orgId,
                                                       @RequestParam(value = "name") String name,
                                                       @RequestParam(value = "org_type") Integer orgType,
                                                       @RequestParam(value = "date") String date,
                                                       @RequestParam(value = "last_date") String lastDate) {
        PushWorkReportVo reportInfo = this.workReportService.getOrgWordReportInfo(orgId, name, orgType, date, lastDate);
        return new ResponseEntity<>(new Result<>(reportInfo, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testUserWorkReport")
    @ApiOperation("测试组织工作报告")
    public ResponseEntity<Result<?>> testUserWorkReport(@RequestParam(value = "org_id") Long orgId,
                                                        @RequestParam(value = "user_id") Long userId,
                                                        @RequestParam(value = "user_name") String userName,
                                                        @RequestParam(value = "name") String name,
                                                        @RequestParam(value = "date") String date,
                                                        @RequestParam(value = "last_date") String lastDate) {
        PushWorkReportVo reportInfo = this.workReportService.getUserReportInfo(orgId, userId, userName, name, date, lastDate);
        return new ResponseEntity<>(new Result<>(reportInfo, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testPush")
    @ApiOperation("测试发送数据")
    public ResponseEntity<Result<?>> testPush(@RequestBody PushWorkReportVo reportVo) {
        this.pushDataService.testPush(reportVo);
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/testHugePush")
    @ApiOperation("测试发送数据")
    public ResponseEntity<Result<?>> testHugePush(@RequestBody PushWorkReportVo reportVo) {
        List<UserOrgResultForm> userList = this.getUserByOrgId();
        userList.forEach(user -> {
            reportVo.setUserId(user.getUserId());
            reportVo.setUserName(user.getName());
            this.asyncWorkReportService.saveRedis(reportVo, 3);
        });
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testPushData")
    @ApiOperation("测试发送数据")
    public ResponseEntity<Result<?>> testPushData(@RequestParam(value = "user_ids") List<Long> userIds,
                                                  @RequestParam(value = "size") Integer size,
                                                  @RequestParam(value = "type") Integer type) throws NoSuchAlgorithmException {
        this.pushDataService.testPushReportData(userIds, size, type);
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testPushReData")
    @ApiOperation("测试发送数据")
    public ResponseEntity<Result<?>> testPushReData(@RequestParam(value = "user_ids") List<Long> userIds,
                                                    @RequestParam(value = "size") Integer size,
                                                    @RequestParam(value = "type") Integer type
    ) throws NoSuchAlgorithmException {
        this.pushDataService.testPushRemindData(userIds, size, type);
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/testOrgReport")
    @ApiOperation("测试某个组织的数据写入缓存")
    public ResponseEntity<Result<?>> testOrgReport(@RequestBody List<OrganizationBase> orgList) {
        this.workReportService.testOrgReport(orgList);
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testPullReportData")
    @ApiOperation("测试调用总方法")
    public ResponseEntity<Result<?>> testPullReportData() {
        this.workReportService.pullBeforeWorkReport();
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testPullReportDataByRegion")
    @ApiOperation("测试调用总方法")
    public ResponseEntity<Result<?>> testPullReportDataByRegion(@RequestParam("region_id") Long regionId) {
        this.workReportService.pullBeforeWorkReportByRegion(regionId);
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testPushReportData")
    @ApiOperation("测试调用总方法")
    public ResponseEntity<Result<?>> testPushReportData() {
        this.pushDataService.pushWorkReportData();
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testPushRemindData")
    @ApiOperation("测试调用总方法")
    public ResponseEntity<Result<?>> testPushRemindData() {
        this.pushDataService.pushWorkRemindData();
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testPullRemindData")
    @ApiOperation("测试调用总方法")
    public ResponseEntity<Result<?>> testPullRemindData() {
        this.workRemindService.pullBeforeWorkRemind();
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testPullRemindDataByRegion")
    @ApiOperation("测试调用总方法")
    public ResponseEntity<Result<?>> testPullRemindDataByRegion(@RequestParam("region_id") Long regionId) {
        this.workRemindService.pullBeforeWorkRemindByRegion(regionId);
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/testOrgRemind")
    @ApiOperation("测试某个组织的数据写入缓存")
    public ResponseEntity<Result<?>> testOrgRemind(@RequestBody List<OrganizationBase> orgList) {
        this.workRemindService.testBeforeWorkRemind(orgList);
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testScoreStudyTask")
    @ApiOperation("测试处理学习积分数据处理")
    public ResponseEntity<Result<?>> testScoreStudyTask(@RequestParam String requestParam) throws ParseException {
        Date parse = new SimpleDateFormat("yyyy-MM").parse(requestParam);
        this.scoreDetailStudyService.toStudyUserReport(parse);
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testScoreBooksTask")
    @ApiOperation("测试处理积分换书数据处理")
    public ResponseEntity<Result<?>> testScoreBooksTask(@RequestParam String requestParam) throws ParseException {
        Date parse = new SimpleDateFormat("yyyy-MM").parse(requestParam);
        this.scoreDetailBookService.toBooksUserReport(parse);
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testScoreDonateTask")
    @ApiOperation("测试处理积分捐赠数据处理")
    public ResponseEntity<Result<?>> testScoreDonateTask(@RequestParam String requestParam) throws ParseException {
        Date parse = new SimpleDateFormat("yyyy-MM").parse(requestParam);
        this.activityDetailDonateService.toDonateUserReport(parse);
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testScorePovertyTask")
    @ApiOperation("测试处理扶贫商城数据处理")
    public ResponseEntity<Result<?>> testScorePovertyTask(@RequestParam String requestParam) throws ParseException {
        Date parse = new SimpleDateFormat("yyyy-MM").parse(requestParam);
        this.scorePovertyService.toPovertyUserReport(parse);
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testScoreMasterTask")
    @ApiOperation("Score相关主线程节点")
    public ResponseEntity<Result<?>> testScoreMasterTask(@RequestParam String requestParam) throws ParseException, InterruptedException {
        Date parse = new SimpleDateFormat("yyyy-MM").parse(requestParam);
        this.scoreMasterService.runOfScore(parse);
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testUserScoreReportTask")
    @ApiOperation("测试个人党员学习与扶贫数据")
    public ResponseEntity<Result<?>> testUserScoreReportTask(@RequestParam Long userId, @RequestParam Long regionId,
                                                             @RequestParam String requestParam) throws ParseException, InterruptedException {
        Date parse = new SimpleDateFormat("yyyy-MM").parse(requestParam);
        UserScoreStudyReport userStudyByMonth = this.userScoreReportService.getUserStudyByMonth(regionId, userId, requestParam);
        log.debug("调用成功!");
        return new ResponseEntity<>(new Result<>(userStudyByMonth, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/initActivityInfo")
    @ApiOperation("初始化活动信息")
    public ResponseEntity<Result<?>> testGetActivityInfo() {
        activityService.buildActivityStaInfo(true);
        return new ResponseEntity<>(new Result<>("生成完成", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/initActivityInfo1")
    @ApiOperation("初始化活动信息1（模拟每个1号统计数据）")
    public ResponseEntity<Result<?>> testGetActivityInfo1() {
        activityService.buildActivityStaInfo(false);
        return new ResponseEntity<>(new Result<>("生成完成", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/createPersonElectronicReport3")
    @ApiOperation("手动初始化电子报告信息-必须等待其它所有信息生成完成后调用")
    public ResponseEntity<Result<?>> createPersonElectronicReport() {
        electronicReportServices.createPersonElectronicReport(true);
        return new ResponseEntity<>(new Result<>("生成完成", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/createPersonElectronicReport2")
    @ApiOperation("手动初始化电子报告信息-必须等待其它所有信息生成完成后调用")
    public ResponseEntity<Result<?>> createPersonElectronicReport2() {
        electronicReportServices.createPersonElectronicReport(false);
        return new ResponseEntity<>(new Result<>("生成完成", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/createPersonMonthEleReport")
    @ApiOperation("生成一个人某月的报告")
    public ResponseEntity<Result<?>> createPersonMonthEleReport(
            @RequestParam(value = "region_id") Long regionId,
            @RequestParam(value = "user_id") Long userId,
            @RequestParam(value = "query_time") String[] queryTime) {
        electronicReportServices.createPersonMonthEleReport(regionId, userId, queryTime);
        return new ResponseEntity<>(new Result<>("生成完成", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/deletePersonMonthEleReport")
    @ApiOperation("删除一个人某月的报告")
    public ResponseEntity<Result<?>> deletePersonMonthEleReport(@RequestParam(value = "user_id") Long userId,
                                                                @RequestParam(value = "query_time") String[] queryTime) {
        electronicReportServices.deletePersonMonthEleReport(userId, queryTime);
        return new ResponseEntity<>(new Result<>("删除完成", errors), HttpStatus.OK);

    }


    @HttpMonitorLogger
    @GetMapping("/getOrgStudyPovertyInfo")
    @ApiOperation("根据组织Id查询组织学习以及扶贫信息")
    public ResponseEntity<Result<?>> getOrgStudyPovertyInfo(@RequestParam(value = "org_id") Long userId,
                                                            @RequestParam(value = "query_time") String queryTime) throws Exception {
        ScoreOrgElectronicReport orgStudyPovertyInfo = scoreService.getOrgStudyPovertyInfo(userId, queryTime);
        return new ResponseEntity<>(new Result<>(orgStudyPovertyInfo, errors), HttpStatus.OK);

    }


    @HttpMonitorLogger
    @GetMapping("/getScoreTopStudyUser")
    @ApiOperation("手获取指定月 指定组织 及其下级最高学习积分的人员")
    public ResponseEntity<Result<?>> getScoreTopStudyUser(@RequestParam Long orgId, @RequestParam String queryTime) {
        String topStudyUser = scoreService.getTopStudyUser(orgId, queryTime);
        return new ResponseEntity<>(new Result<>(topStudyUser, errors), HttpStatus.OK);

    }

    @HttpMonitorLogger
    @PostMapping("/getMongoInfo")
    @ApiOperation("获得mongo信息")
    public ResponseEntity<Result<?>> getMongoInfo(@RequestBody @Valid QueryMongoInfoForm queryMongoInfoForm,
                                                  BindingResult bindingResult) throws Exception {
        Object result = queryMongoInfoService.queryMongoInfo(queryMongoInfoForm);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);

    }

    @HttpMonitorLogger
    @GetMapping("/getPpmdByUser")
    @ApiOperation("查询人员党费信息")
    public ResponseEntity<Result<?>> getPpmdByUser(@RequestParam(value = "region_id") Long regionId,
                                                   @RequestParam(value = "user_id") Long userId,
                                                   @RequestParam(value = "query_time") String queryTime) {
        PersonElectronicReport.PpmdPayInfo userPpmdReport = this.ppmdReportService.getUserPpmdReport(regionId, userId, queryTime);
        return new ResponseEntity<>(new Result<>(userPpmdReport, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/createPersonElectronicReportByMonth")
    @ApiOperation("创建指定月的个人数据")
    public ResponseEntity<Result<?>> createPersonElectronicReportByMonth(@RequestParam(value = "region_id") Long regionId,
                                                                         @RequestParam(value = "query_time") String queryTime) {
        electronicReportServices.createPersonElectronicReportByMonth(regionId, queryTime);
        return new ResponseEntity<>(new Result<>("ok", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/getActivityStaInfoByOrgId")
    @ApiOperation("查询指定月指定组织数据")
    public ResponseEntity<Result<?>> getActivityStaInfoByOrgId(@RequestParam(value = "org_id") Long orgId,
                                                               @RequestParam(value = "query_time") Integer queryTime) {
        List<ActivityOrgElectronicReport> activityStaInfoByOrgId = activityService.getActivityStaInfoByOrgId(orgId, queryTime);
        return new ResponseEntity<>(new Result<>(activityStaInfoByOrgId, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/initActivityBaseInfoByMonth")
    @ApiOperation("查询指定月指定组织数据")
    public ResponseEntity<Result<?>> initActivityBaseInfoByMonth(@RequestParam(value = "query_time") String queryTime) {
        this.activityService.buildActivityStaInfo(queryTime);
        return new ResponseEntity<>(new Result<>("ok", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/pullUserReport")
    @ApiOperation("拉取指定用户的1号的工作报告")
    public ResponseEntity<Result<?>> pullUserReport(@RequestParam(value = "org_ids", required = false) List<Long> orgIds) {
        this.workReportService.testUserReport(orgIds);
        return new ResponseEntity<>(new Result<>("ok", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testReport")
    @ApiOperation("查询指定月指定组织数据")
    public ResponseEntity<Result<?>> testReport(@RequestParam(value = "user_id") Long userId,
                                                @RequestParam(value = "type") Integer type) {
        Map<String, Object> resultMap = new HashMap<>();
        if (type == 1) {
            Long seniorSize = this.redisTemplate.opsForList().size(Constants.REDIS_KEY_PUSH_WORK_REPORT_SENIOR);
            List<String> seniorList = this.redisTemplate.opsForList().range(Constants.REDIS_KEY_PUSH_WORK_REPORT_SENIOR, 0L, seniorSize);
            resultMap.put("senior", this.getObject(seniorList, PushWorkReportVo.class, userId));
            Long generaSize = this.redisTemplate.opsForList().size(Constants.REDIS_KEY_PUSH_WORK_REPORT_GENERA);
            List<String> generaList = this.redisTemplate.opsForList().range(Constants.REDIS_KEY_PUSH_WORK_REPORT_GENERA, 0L, generaSize);
            resultMap.put("genera", this.getObject(generaList, PushWorkReportVo.class, userId));
            Long allSize = this.redisTemplate.opsForList().size(Constants.REDIS_KEY_PUSH_WORK_REPORT_GENERA);
            List<String> allList = this.redisTemplate.opsForList().range(Constants.REDIS_KEY_PUSH_WORK_REPORT_ALL, 0L, allSize);
            resultMap.put("all", this.getObject(allList, PushWorkReportVo.class, userId));
        } else {
            Long seniorSize = this.redisTemplate.opsForList().size(Constants.REDIS_KEY_PUSH_WORK_REMIND_SENIOR);
            List<String> seniorList = this.redisTemplate.opsForList().range(Constants.REDIS_KEY_PUSH_WORK_REMIND_SENIOR, 0L, seniorSize);
            resultMap.put("senior", this.getObject(seniorList, PushWorkReportVo.class, userId));
            Long generaSize = this.redisTemplate.opsForList().size(Constants.REDIS_KEY_PUSH_WORK_REMIND_GENERA);
            List<String> generaList = this.redisTemplate.opsForList().range(Constants.REDIS_KEY_PUSH_WORK_REMIND_GENERA, 0L, generaSize);
            resultMap.put("genera", this.getObject(generaList, PushWorkReportVo.class, userId));
        }

        return new ResponseEntity<>(new Result<>(resultMap, errors), HttpStatus.OK);
    }


    private <T> T getObject(List<String> list, Class<T> clazz, Long userId) {
        for (String str : list) {
            T t = JsonUtils.fromJson(str, clazz);
            String id = "";
            try {
                id = t.getClass().getMethod("getUserId").invoke(t).toString();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            }
            if (id.equals(userId.toString())) {
                return t;
            }
        }
        return null;
    }


    private List<UserOrgResultForm> getUserByOrgId() {
        OrgUserQueryForm form = new OrgUserQueryForm();
        form.setOrgId(3L);
        form.setPagesize(58000);
        Page<UserOrgResultForm> page = null;
        // 接口地址
        String url = String.format("http://%s/org/user/getUserList",
                this.togServicesConfig.getUserCenter());
        HttpHeaders headers = new HttpHeaders();
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        //调用远程接口
        try {
            page = RemoteApiHelper.post(restTemplate, url, form, headers, new TypeReference<Result<Page<UserOrgResultForm>>>() {
            });
            log.debug("远程调用User根据组织ID查询组织下的党员: [{}]", page);
        } catch (Exception e) {
            log.debug("调用远程服务失败, 错误内容:", e);
        }
        return page.getResult();
    }


    @HttpMonitorLogger
    @GetMapping("/testUserScore")
    public ResponseEntity<Result<?>> testUserScore() {
        Double score = this.userService.getUserScore(216L, "2020-01");
        return new ResponseEntity<>(new Result<>(score, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testUserScoreList")
    public ResponseEntity<Result<?>> testUserScoreList() {
        List<Long> orgIds = new ArrayList<>();
        orgIds.add(4L);
        orgIds.add(6L);
        orgIds.add(7L);
        orgIds.add(8L);
        orgIds.add(9L);
        orgIds.add(10L);
        orgIds.add(11L);
        orgIds.add(12L);
        int year = 2019;
        ScoreResultVo scoreResultVo = this.userService.getUserScoreList(orgIds, year);
        return new ResponseEntity<>(new Result<>(scoreResultVo, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/testOrgScore")
    public ResponseEntity<Result<?>> testOrgScore() {
        Double score = this.orgService.getOrgScore(216L, "2020-01");
        return new ResponseEntity<>(new Result<>(score, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testOrgScoreList")
    public ResponseEntity<Result<?>> testOrgScoreList() {
        List<Long> orgIds = new ArrayList<>();
        orgIds.add(125L);
        orgIds.add(132L);
        int year = 2019;
        ScoreResultVo scoreResultVo = this.orgService.getOrgScoreList(orgIds, year);
        return new ResponseEntity<>(new Result<>(scoreResultVo, errors), HttpStatus.OK);
    }

    /**
     * 测试摘取数据
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/testPullData")
    public ResponseEntity<Result<?>> testPullData(
            @RequestParam(value = "table_name") String tableName) throws Exception {
        pullDataService.handlerHistoryPullData(tableName);
        return new ResponseEntity<>(new Result<>("", errors), HttpStatus.OK);
    }

    /**
     * 测试摘取数据
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/testPullOrgLight")
    public ResponseEntity<Result<?>> testPullOrgLight() throws Exception {
        List<MessageVO> messageVOS = orgHighlightService.generateMessage(3L, 0, 1, 5);
        return new ResponseEntity<>(new Result<>(messageVOS, errors), HttpStatus.OK);
    }

    /**
     * 拉取党支部的党业融合的数据
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/getPartyIndex")
    public ResponseEntity<Result<?>> getPartyIndex() throws Exception {
        List<Long> listOrgId = new ArrayList<>();
        listOrgId.add(3L);
        return new ResponseEntity<>(new Result<>(tbcBasicsService.getPartyIndex(1, listOrgId), errors), HttpStatus.OK);
    }

    /**
     * 测试摘取数据
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/getUserIndex")
    public ResponseEntity<Result<?>> getUserIndex() throws Exception {
        return new ResponseEntity<>(new Result<>(tbcBasicsService.
                getUserIndex(null, null), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org-party-member")
    @ApiOperation("党员”党建+业务”工作情况柱状图")
    public ResponseEntity<Result<?>> getOrgPartyMember(@RequestParam(name = "org_id", required = false) Long orgId,
                                                       @RequestParam(name = "region_id", required = false) Long regionId,
                                                       @RequestParam(name = "type", required = false) Integer type) {
        return new ResponseEntity<>(new Result<>(Collections.
                singletonList(tbcPartyBranchWordService.getPartyMemberNew(type)), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/tbc-matching")
    @ApiOperation("党页拟合度(条形图)")
    public ResponseEntity<Result<?>> getTbcMatching(@RequestParam(name = "org_id", required = false) Long orgId,
                                                    @RequestParam(name = "region_id", required = false) Long regionId,
                                                    @RequestParam(name = "type") Integer type) {
        return new ResponseEntity<>(new Result<>(tbcMatchingService.getTbcMatching(type), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/tbc-base-info")
    @ApiOperation("拉取基础数据")
    public ResponseEntity<Result<?>> getTbcBaseInfo(@RequestParam(name = "type") Integer type) {
        return new ResponseEntity<>(new Result<>(tbcBasicsService.getTbcBaseInfo(type), errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/clear-tbc-base-info")
    @ApiOperation("清洗数据")
    public ResponseEntity<Result<?>> clearTbcBaseInfo(@RequestParam(name = "type") Integer type,
                                                      @RequestParam(name = "staMonth") String staMonth,
                                                      @RequestParam(name = "user_id", required = false) Long userId,
                                                      @RequestParam(name = "scoreStaMonth") String scoreStaMonth) {
        return new ResponseEntity<>(new Result<>(tbcBasicsService.clearTbcBaseInfo(type, staMonth, userId, scoreStaMonth),
                errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/calc-xiangfeng-index")
    @ApiOperation("清洗先峰指数")
    public ResponseEntity<Result<?>> calcXiangfengIndex(@RequestParam(name = "staMonth") String staMonth) {
        return new ResponseEntity<>(new Result<>(tbcBasicsService.calcXiangfengIndex(staMonth), errors), HttpStatus.OK);
    }

    /**
     * 重置基础信息
     *
     * @param type
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/rest-tbc-base-info")
    @ApiOperation("重置基础信息")
    public ResponseEntity<Result<?>> resetTbcBaseInfo(@RequestParam(name = "type") Integer type,
                                                      @RequestParam(name = "user_ids", required = false) List<Long> userIds) {
        return new ResponseEntity<>(new Result<>(tbcBasicsService.resetTbcBaseInfo(type, userIds), errors), HttpStatus.OK);
    }

    /**
     * 清洗 new_owner_id 和 seq_number 两个字段信息
     * 清洗数据
     *
     * @param type
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/cleaning-tbc-base-info")
    @ApiOperation("清洗数据")
    public ResponseEntity<Result<?>> cleaningTbcBaseInfo(@RequestParam(name = "type", required = false) Integer type) {
        tbcBasicsService.cleaningTbcBaseInfo();
        return new ResponseEntity<>(new Result<>(1, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/tbc-base-info-index")
    @ApiOperation("拉取基础跑指数")
    public ResponseEntity<Result<?>> getTbcBaseIndex(@RequestParam(name = "type") Integer type) {
        return new ResponseEntity<>(new Result<>(tbcBasicsService.getTbcBaseIndex(type), errors), HttpStatus.OK);
    }

//    @HttpMonitorLogger
//    @GetMapping("/org-splashes")
//    @ApiOperation("支部工作情况(散点图)")
//    public ResponseEntity<Result<?>> getSplashes(@RequestParam(name = "org_id", required = false) Long orgId,
//                                                 @RequestParam(name = "region_id", required = false) Long regionId,
//                                                 @RequestParam(name = "type", required = false) Integer type
//    ) {
//        List<List<Double>> result = (List<List<Double>>)JsonUtils.fromJson(tbcGetService.TbcPartyMemberWork(),List.class);
//        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
//    }

    @HttpMonitorLogger
    @GetMapping("/task-test")
    @ApiOperation("测试党性体检的任务方法")
    public ResponseEntity<Result<?>> testUserTask(@RequestParam(name = "type") Integer type,
                                                  @RequestParam("detail_id") Long detailId
    ) {
        UserPartyEvalDetailEntity w = userPartyEvalDetailMapper.selectByPrimaryKey(detailId);
        switch (type) {
            case 1:
                userTaskService.newsStudyDays(w);
                break;
            case 2://13
                userTaskService.partyConstructionTask(w);
                break;
            case 3://16
                userTaskService.cloudPartyBranchTasks(w);
                break;
            case 4://17
                userTaskService.bearTheTask(w);
                break;
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/task-find-overview")
    @ApiOperation("测试系统报告")
    public ResponseEntity<Result<?>> testFindOverview(@RequestParam(name = "region_id") Long regionId,
                                                      @RequestParam("user_id") Long userId,
                                                      @RequestParam("org_id") Long orgId
    ) {
        return new ResponseEntity<>(new Result<>(dataSourceService.findOverview(regionId, userId, orgId), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/revisit-strategy")
    @ApiOperation("测试系统报告")
    public ResponseEntity<Result<?>> revisitStrategyImplTest() {
        OrganizationBase org = new OrganizationBase();
        org.setOrganizationId(10L);
        org.setRegionId(19L);

        OrganizationBase unit = new OrganizationBase();
        unit.setOrganizationId(3L);
        unit.setRegionId(19L);

        MetricEntity metricEntity = new MetricEntity();
        metricEntity.setScore(0.3);

        LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
        LogAspectHelper.SSLog ssLog = logHelper.getSSlog(helper, "[异步刷新党费交纳信息统计队列任务]");
        helper.reSetContext(ssLog);

        Map<String, Object> params = new HashMap<>();
        // return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        //return new ResponseEntity<>(new Result<>(revisitStrategy.compute(org, unit, metricEntity,2023,ssLog,params), errors), HttpStatus.OK);
        return new ResponseEntity<>(new Result<>(ppmdStrategy.compute(org, unit, metricEntity,2023,"kjkljlk",params), errors), HttpStatus.OK);
       // return new ResponseEntity<>(new Result<>(studyClassStrategy.compute(org, unit, metricEntity,2023,ssLog,params), errors), HttpStatus.OK);
       // return new ResponseEntity<>(new Result<>(theoryStudyClassStrategy.compute(org, unit, metricEntity,2023,ssLog,params), errors), HttpStatus.OK);
      //  return new ResponseEntity<>(new Result<>(settingOrgInfoStrategy.compute(org, unit, metricEntity,2023,ssLog,params), errors), HttpStatus.OK);
       // return new ResponseEntity<>(new Result<>(settingOrgInfoPeriodStrategy.compute(org, unit, metricEntity,2023,ssLog,params), errors), HttpStatus.OK);
    }
}
