package com.goodsogood.ows.controller.rank;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.db.rank.OrgScoreDetailEntity;
import com.goodsogood.ows.model.db.rank.OrgScoreEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.rank.OrgWhereResultForm;
import com.goodsogood.ows.model.vo.rank.ScoreOverviewForm;
import com.goodsogood.ows.service.rank.ClientUserCenterService;
import com.goodsogood.ows.service.rank.OrgScoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Create by FuXiao on 2020/10/27
 */
@RestController
@Log4j2
@RequestMapping("/org")
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "组织分数", tags = {"组织分数"})
@Validated
public class OrgScoreController {
    private final Errors errors;
    private final StringRedisTemplate redisTemplate;
    private final ClientUserCenterService clientUserCenterService;
    private final OrgScoreService orgScoreService;

    public OrgScoreController(Errors errors, StringRedisTemplate redisTemplate, ClientUserCenterService clientUserCenterService, OrgScoreService orgScoreService) {
        this.errors = errors;
        this.redisTemplate = redisTemplate;
        this.clientUserCenterService = clientUserCenterService;
        this.orgScoreService = orgScoreService;
    }

    /**
     * 组织得分概览
     */
    @HttpMonitorLogger
    @GetMapping(value = "/score/overview")
    @ApiOperation(value = "组织得分概览")
    public ResponseEntity<Result<?>> scoreOverview(@RequestParam(value = "org_id") Long orgId,
                                                   @RequestParam(value = "year") Long year,
                                                   @RequestParam(value = "org_name", required = false) String orgName,
                                                   @RequestParam(value = "org_type", required = false) Integer orgType,
                                                   @RequestParam(value = "page_num", required = false, defaultValue = "1") Integer pageNum,
                                                   @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize) {
        //访问用户中心，查询组织以及下级列表
        Result<List<OrgWhereResultForm>> forms = clientUserCenterService.findOrgByWhere(orgId, pageNum, pageSize);
        Page<ScoreOverviewForm> res = new Page<>(pageNum, pageSize);
        res.setTotal(forms.getTotal());
        res.setPageNum(forms.getPageNum());
        res.setPageSize(forms.getPageSize());
        res.setPages(forms.getPages());
        for (OrgWhereResultForm form : forms.getData()) {
            ScoreOverviewForm scoreOverviewForm = new ScoreOverviewForm();
            scoreOverviewForm.setOrgId(form.getOrgId());
            if (null != orgName && !orgName.equals(form.getOrgName())) continue;
            scoreOverviewForm.setOrgName(form.getOrgName());
            if (null != orgType && !orgType.equals(form.getOrgType())) continue;
            scoreOverviewForm.setOrgType(form.getOrgType());
            scoreOverviewForm.setYear(year);
            if (null == redisTemplate.opsForHash().get("ORG_SCORE_" + year, form.getOrgId().toString())) {
                orgScoreService.calculateScore(form.getOrgId());
            }
            Double score = Double.valueOf(redisTemplate.opsForHash().get("ORG_SCORE_" + year, form.getOrgId().toString()).toString());
            scoreOverviewForm.setScore(score);
            res.add(scoreOverviewForm);
        }
        return new ResponseEntity<>(new Result<>(res, errors), HttpStatus.OK);
    }

    /**
     * 给组织打分
     */
    @HttpMonitorLogger
    @PostMapping(value = "/score")
    @ApiOperation(value = "给组织打分")
    public ResponseEntity<Result<?>> score(@RequestBody List<OrgScoreDetailEntity> entitys) {
        orgScoreService.score(entitys);
        orgScoreService.calculateScore(entitys.get(0).getOrgId());
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 查询组织每项得分
     */
    @HttpMonitorLogger
    @GetMapping(value = "/score/detail")
    @ApiOperation(value = "查询组织每项得分")
    public ResponseEntity<Result<?>> scoreDetail(@RequestParam(value = "org_id") Long orgId,
                                                 @RequestParam(value = "year") Long year) {
        Map<Long, List<OrgScoreEntity>> res = orgScoreService.findOrgScore(orgId, year);
        return new ResponseEntity<>(new Result<>(res, errors), HttpStatus.OK);
    }
}
