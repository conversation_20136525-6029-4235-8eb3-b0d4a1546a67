package com.goodsogood.ows.controller.rank;

import com.goodsogood.ows.common.DingDingMessage;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.mapper.rank.CompensateMapper;
import com.goodsogood.ows.mapper.rank.SnapshotMapper;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.rank.CompensateVo;
import com.goodsogood.ows.service.PushDingDingService;
import com.goodsogood.ows.service.rank.CompensateService;
import com.goodsogood.ows.service.rank.DecisionSupportByMonthService;
import com.goodsogood.ows.service.rank.DecisionSupportService;
import com.goodsogood.ows.service.user.OrgSnapshotService;
import com.goodsogood.ows.service.user.UserSnapshotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Create by FuXiao on 2020/11/7
 */
@RestController
@Log4j2
@RequestMapping("/initial")
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "初始化分数", tags = {"初始化分数"})
@Validated
public class InitialController {
    private final DecisionSupportService decisionSupportService;
    private final Errors errors;
    private final SnapshotMapper snapshotMapper;
    private final DecisionSupportByMonthService decisionSupportByMonthService;
    private final StringRedisTemplate redisTemplate;
    private final PushDingDingService pushDingDingService;
    private final CompensateMapper compensateMapper;
    private final CompensateService compensateService;

    public InitialController(DecisionSupportService decisionSupportService, Errors errors, SnapshotMapper snapshotMapper, DecisionSupportByMonthService decisionSupportByMonthService, StringRedisTemplate redisTemplate, PushDingDingService pushDingDingService, CompensateMapper compensateMapper, CompensateService compensateService) {
        this.decisionSupportService = decisionSupportService;
        this.errors = errors;
        this.snapshotMapper = snapshotMapper;
        this.decisionSupportByMonthService = decisionSupportByMonthService;
        this.redisTemplate = redisTemplate;
        this.pushDingDingService = pushDingDingService;
        this.compensateMapper = compensateMapper;
        this.compensateService = compensateService;
    }

    /**
     * 初始化某年的人员分数
     */
    @GetMapping(value = "/user-score")
    @ApiOperation(value = "初始化某年的人员分数")
    public ResponseEntity<Result<?>> userScore(@RequestParam(value = "year") Integer year) {
        int size = 500;
        List<Long> userIdList = snapshotMapper.findAllUserSnapshotByYear(year);
        log.debug("人员列表总量为-->{}", userIdList.size());
        int start = 0, end = size;
        redisTemplate.delete("AUTO_SCORE_USER_COUNT");
        while (start < userIdList.size()) {
            if (end > userIdList.size()) {
                end = userIdList.size();
            }
            List<Long> subList = Collections.synchronizedList(userIdList.subList(start, end));
            log.debug("user-score : subList->{}", subList);
            decisionSupportService.initialUserScore(subList, year);
            redisTemplate.opsForValue().increment("AUTO_SCORE_USER_COUNT", 1);
            start += size;
            end += size;
        }
        while (Integer.parseInt(Objects.requireNonNull(redisTemplate.opsForValue().get("AUTO_SCORE_USER_COUNT"))) > 0) {
        }

        DingDingMessage message = DingDingMessage.builder()
                .content("初始化" + year + "年的自动打分人员分数完成")
                .time(new SimpleDateFormat().format(new Date()))
                .trackId(UUID.randomUUID().toString())
                .build();
        this.pushDingDingService.push(message);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 初始化某年的组织分数
     */
    @GetMapping(value = "/org-score")
    @ApiOperation(value = "初始化某年的组织分数")
    public ResponseEntity<Result<?>> orgScore(@RequestParam(value = "year") Integer year) {
        int size = 50;
        List<OrgSnapshotEntity> orgSnapshotEntities = snapshotMapper.findAllOrgSnapshotByYear(year);
        List<Long> orgIdList = orgSnapshotEntities.stream().map(OrgSnapshotEntity::getOrgId).collect(Collectors.toList());
        log.debug("组织列表总量为-->{}", orgIdList.size());
        redisTemplate.delete("AUTO_SCORE_ORG_COUNT");
        int start = 0, end = size;
        while (start < orgIdList.size()) {
            if (end > orgIdList.size()) {
                end = orgIdList.size();
            }
            List<Long> subList = Collections.synchronizedList(orgIdList.subList(start, end));
            log.debug("org-score : subList->{}", subList);
            decisionSupportService.initialOrgScore(subList, year);
            redisTemplate.opsForValue().increment("AUTO_SCORE_ORG_COUNT", 1);
            start += size;
            end += size;
        }
        while (Integer.parseInt(Objects.requireNonNull(redisTemplate.opsForValue().get("AUTO_SCORE_ORG_COUNT"))) > 0) {
        }

        DingDingMessage message = DingDingMessage.builder()
                .content("初始化" + year + "年的自动打分组织分数完成")
                .time(new SimpleDateFormat().format(new Date()))
                .trackId(UUID.randomUUID().toString())
                .build();
        this.pushDingDingService.push(message);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 刷新指定人员的某年分数
     */
    @GetMapping(value = "/user-score-specify")
    @ApiOperation(value = "刷新指定人员的某年分数")
    public ResponseEntity<Result<?>> userScore(@RequestParam(value = "user_id_list") List<Long> userIdList,
                                               @RequestParam(value = "year") Integer year) {
        decisionSupportService.initialUserScore(userIdList, year);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 刷新指定组织的某年分数
     */
    @GetMapping(value = "/org-score-specify")
    @ApiOperation(value = "刷新指定组织的某年分数")
    public ResponseEntity<Result<?>> orgScore(@RequestParam(value = "org_id_list") List<Long> orgIdList,
                                              @RequestParam(value = "year") Integer year) {
        decisionSupportService.initialOrgScore(orgIdList, year);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 刷新指定月份的
     * 所有人员分数
     */
    @GetMapping(value = "/user-score-month")
    @ApiOperation(value = "刷新指定月份的人员分数")
    public ResponseEntity<Result<?>> userScore(@RequestParam(value = "year") Integer year,
                                               @RequestParam(value = "month") Integer month) {
        int uSize = 100;
        List<Long> userIdList = snapshotMapper.findAllUserSnapshotByYear(year);
        log.debug("人员列表总量为-->{}", userIdList.size());
        redisTemplate.delete("AUTO_SCORE_USER_MONTH_COUNT");
        int uStart = 0, uEnd = uSize;
        String uuid = UUID.randomUUID().toString();
        while (uStart < userIdList.size()) {
            if (uEnd > userIdList.size()) {
                uEnd = userIdList.size();
            }
            List<Long> subList = Collections.synchronizedList(userIdList.subList(uStart, uEnd));
            log.debug("user-score : subList->{}", subList);
            decisionSupportByMonthService.initialUserScore(subList, year, month, uuid);
            redisTemplate.opsForValue().increment("AUTO_SCORE_USER_MONTH_COUNT", 1);
            uStart += uSize;
            uEnd += uSize;
        }
        while (Integer.parseInt(Objects.requireNonNull(redisTemplate.opsForValue().get("AUTO_SCORE_USER_MONTH_COUNT"))) > 0) {
        }

        DingDingMessage message = DingDingMessage.builder()
                .content("初始化" + year + "年" + month + "月的自动打分人员分数完成")
                .time(new SimpleDateFormat().format(new Date()))
                .trackId(uuid)
                .build();
        this.pushDingDingService.push(message);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 刷新指定月份的组织分数
     */
    @GetMapping(value = "/org-score-month")
    @ApiOperation(value = "刷新指定月份的组织分数")
    public ResponseEntity<Result<?>> orgScore(@RequestParam(value = "year") Integer year,
                                              @RequestParam(value = "month") Integer month) {
        int oSize = 10;
        List<OrgSnapshotEntity> orgSnapshotEntities = snapshotMapper.findAllOrgSnapshotByYear(year);
        List<Long> orgIdList = orgSnapshotEntities.stream().map(OrgSnapshotEntity::getOrgId).collect(Collectors.toList());
        log.debug("组织列表总量为-->{}", orgIdList.size());
        redisTemplate.delete("AUTO_SCORE_ORG_MONTH_COUNT");
        int oStart = 0, oEnd = oSize;
        String uuid = UUID.randomUUID().toString();
        while (oStart < orgIdList.size()) {
            if (oEnd > orgIdList.size()) {
                oEnd = orgIdList.size();
            }
            List<Long> subList = Collections.synchronizedList(orgIdList.subList(oStart, oEnd));
            log.debug("org-score : subList->{}", subList);
            decisionSupportByMonthService.initialOrgScore(subList, year, month, uuid);
            redisTemplate.opsForValue().increment("AUTO_SCORE_ORG_MONTH_COUNT", 1);
            oStart += oSize;
            oEnd += oSize;
        }
        while (Integer.parseInt(Objects.requireNonNull(redisTemplate.opsForValue().get("AUTO_SCORE_ORG_MONTH_COUNT"))) > 0) {
        }

        DingDingMessage message = DingDingMessage.builder()
                .content("初始化" + year + "年" + month + "月的自动打分组织分数完成")
                .time(new SimpleDateFormat().format(new Date()))
                .trackId(uuid)
                .build();
        this.pushDingDingService.push(message);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 用户补偿机制
     */
    @GetMapping(value = "/user-compensate")
    @ApiOperation(value = "用户补偿机制")
    public ResponseEntity<Result<?>> userCompensate(@RequestParam(value = "year") Integer year) {
        List<CompensateVo> compensateVoList = compensateMapper.userCompensateList(year);
        log.debug("本次共需补偿" + compensateVoList.size() + "名用户");
        for (CompensateVo compensateVo : compensateVoList) {
            compensateService.userCompensate(compensateVo.getId(), compensateVo.getScoreRuleId(), year);
        }
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 组织补偿机制
     */
    @GetMapping(value = "/org-compensate")
    @ApiOperation(value = "组织补偿机制")
    public ResponseEntity<Result<?>> orgCompensate(@RequestParam(value = "year") Integer year) {
        List<CompensateVo> compensateVoList = compensateMapper.orgCompensateList(year);
        log.debug("本次共需补偿" + compensateVoList.size() + "个组织");
        for (CompensateVo compensateVo : compensateVoList) {
            compensateService.orgCompensate(compensateVo.getId(), compensateVo.getScoreRuleId(), year);
        }
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    static Set<String> caches = new HashSet<>();

    static {
        caches.add(replace(UserSnapshotService.SNAPSHOT_USER));
        caches.add(replace(UserSnapshotService.NOT_RETIRED_USER_IDS));
        caches.add(replace(UserSnapshotService.ORG_NOT_RETIRED_USER_IDS));

        caches.add(replace(OrgSnapshotService.BASE_ORG_IDS));
        caches.add(replace(OrgSnapshotService.BASE_ORG_INCLUDE_CHILD_ORG_IDS));
        caches.add(replace(OrgSnapshotService.SNAPSHOT_ORG));
        caches.add(replace(OrgSnapshotService.USER_TOTAL_CACHE));
    }

    static String replace(String key) {
        return key.replaceAll(":%s", "");
    }

    @GetMapping(value = "/delScoreCache")
    @ApiOperation(value = "删除积分相关缓存")
    public ResponseEntity<Result<?>> delScoreCache() {
        caches.forEach(k -> {
            Set<String> keys = this.redisTemplate.keys(k + "*");
            this.redisTemplate.delete(keys);

        });
        return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
    }
}
