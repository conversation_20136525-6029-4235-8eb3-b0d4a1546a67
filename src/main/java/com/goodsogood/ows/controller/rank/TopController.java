package com.goodsogood.ows.controller.rank;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.db.rank.TopEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.rank.TopService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Create by FuXiao on 2020/11/2
 */
@RestController
@Log4j2
@RequestMapping("/top")
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "顶部类型", tags = {"顶部类型"})
@Validated
public class TopController {
    private final Errors errors;
    private final TopService topService;

    public TopController(Errors errors, TopService topService) {
        this.errors = errors;
        this.topService = topService;
    }

    /**
     * 新增顶部类型
     */
    @HttpMonitorLogger
    @PostMapping(value = "/insert")
    @ApiOperation(value = "新增")
    public ResponseEntity<Result<?>> insert(@RequestBody TopEntity entity) {
        topService.insert(entity);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 删除顶部类型
     */
    @HttpMonitorLogger
    @GetMapping(value = "/delete")
    @ApiOperation(value = "删除顶部类型")
    public ResponseEntity<Result<?>> delete(@RequestParam(value = "top_id") Long topId) {
        topService.delete(topId);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }


    /**
     * 修改顶部类型
     */
    @HttpMonitorLogger
    @PostMapping(value = "/update")
    @ApiOperation(value = "修改")
    public ResponseEntity<Result<?>> update(@RequestBody TopEntity entity) {
        topService.update(entity);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 根据分类查询顶部类型
     */
    @HttpMonitorLogger
    @GetMapping(value = "/find")
    @ApiOperation(value = "查询")
    public ResponseEntity<Result<?>> find(@RequestParam(value = "type") Integer type) {
        List<TopEntity> res = topService.find(type);
        return new ResponseEntity<>(new Result<>(res, errors), HttpStatus.OK);
    }
}
