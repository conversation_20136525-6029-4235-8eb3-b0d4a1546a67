package com.goodsogood.ows.controller.rank;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.db.rank.ColumnEntity;
import com.goodsogood.ows.model.db.rank.ScoreRuleEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.rank.ColumnService;
import com.goodsogood.ows.service.rank.ScoreRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by FuXiao on 2020/10/19
 */
@RestController
@Log4j2
@RequestMapping("/rule")
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "评分规则", tags = {"评分规则"})
@Validated
public class ScoreRuleController {
    private final Errors errors;
    private final ScoreRuleService scoreRuleService;
    private final ColumnService columnService;

    public ScoreRuleController(Errors errors, ScoreRuleService scoreRuleService, ColumnService columnService) {
        this.errors = errors;
        this.scoreRuleService = scoreRuleService;
        this.columnService = columnService;
    }

    /**
     * 新增评分规则
     */
    @HttpMonitorLogger
    @PostMapping(value = "/insert")
    @ApiOperation(value = "新增评分规则")
    public ResponseEntity<Result<?>> insert(@RequestBody ScoreRuleEntity entity) {
        scoreRuleService.insert(entity);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 删除评分规则
     */
    @HttpMonitorLogger
    @GetMapping(value = "/delete")
    @ApiOperation(value = "删除评分规则")
    public ResponseEntity<Result<?>> delete(@RequestParam(value = "score_rule_id") Long scoreRuleId) {
        scoreRuleService.delete(scoreRuleId);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 修改评分规则
     */
    @HttpMonitorLogger
    @PostMapping(value = "/update")
    @ApiOperation(value = "修改评分规则")
    public ResponseEntity<Result<?>> update(@RequestBody ScoreRuleEntity entity) {
        scoreRuleService.update(entity);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 查询评分规则
     */
    @HttpMonitorLogger
    @GetMapping(value = "/find")
    @ApiOperation(value = "查询评分规则")
    public ResponseEntity<Result<?>> find(@RequestParam(value = "score_rule_id") Long scoreRuleId) {
        ScoreRuleEntity res = scoreRuleService.find(scoreRuleId);
        return new ResponseEntity<>(new Result<>(res, errors), HttpStatus.OK);
    }

    /**
     * 查询评分规则大表
     */
    @HttpMonitorLogger
    @GetMapping(value = "/find-all")
    @ApiOperation(value = "查询评分规则大表")
    public ResponseEntity<Result<?>> findAll(@RequestParam(value = "top_id") Long topId) {
        List<ColumnEntity> res = columnService.findAll(topId);
        res.forEach(columnEntity -> {
            List<ScoreRuleEntity> list = scoreRuleService.findByColumn(columnEntity.getColumnId());
            if (null != list && 0 != list.size()) {
                columnEntity.setChildRule(list);
            }
        });
        //根据parent_id做嵌套组合
        res.stream().filter(re -> null != re.getParentId()).forEach(re -> {
            res.stream().filter(parent -> parent.getColumnId().equals(re.getParentId())).forEach(parent -> {
                List<ColumnEntity> tmp = null == parent.getChildren() ? new ArrayList<>() : parent.getChildren();
                tmp.add(re);
                parent.setChildren(tmp);
            });
        });
        res.removeIf(entity -> null != entity.getParentId());
        return new ResponseEntity<>(new Result<>(res, errors), HttpStatus.OK);
    }

    /**
     * 修改系数
     */
    @HttpMonitorLogger
    @GetMapping(value = "/update-percent")
    @ApiOperation(value = "修改系数")
    public ResponseEntity<Result<?>> updatePercent(@RequestParam(value = "top_id") Long topId,
                                                   @RequestParam(value = "percentage") Double percentage) {
        scoreRuleService.updatePercent(topId, percentage);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }
}
