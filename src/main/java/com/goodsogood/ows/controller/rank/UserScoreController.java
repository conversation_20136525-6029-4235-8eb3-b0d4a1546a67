package com.goodsogood.ows.controller.rank;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.db.rank.UserScoreDetailEntity;
import com.goodsogood.ows.model.db.rank.UserScoreEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.rank.ScoreOverviewVo;
import com.goodsogood.ows.model.vo.rank.UserOrgResultChildForm;
import com.goodsogood.ows.service.rank.ClientUserCenterService;
import com.goodsogood.ows.service.rank.UserScoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Create by FuXiao on 2020/10/22
 */
@RestController
@Log4j2
@RequestMapping("/user")
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "人员分数", tags = {"人员分数"})
@Validated
public class UserScoreController {
    private final Errors errors;
    private final StringRedisTemplate redisTemplate;
    private final UserScoreService userScoreService;
    private final ClientUserCenterService clientUserCenterService;

    public UserScoreController(Errors errors, StringRedisTemplate redisTemplate, UserScoreService userScoreService, ClientUserCenterService clientUserCenterService) {
        this.errors = errors;
        this.redisTemplate = redisTemplate;
        this.userScoreService = userScoreService;
        this.clientUserCenterService = clientUserCenterService;
    }

    /**
     * 人员得分概览
     */
    @HttpMonitorLogger
    @GetMapping(value = "/score/overview")
    @ApiOperation(value = "人员得分概览")
    public ResponseEntity<Result<?>> scoreOverview(@RequestParam(value = "org_id") Long orgId,
                                                   @RequestParam(value = "year") Long year,
                                                   @RequestParam(value = "user_name", required = false) String userName,
                                                   @RequestParam(value = "page_num", required = false, defaultValue = "1") Integer pageNum,
                                                   @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize) {
        //调用用户中心，获取党员姓名和所在组织列表
        Result<Page<UserOrgResultChildForm>> forms = clientUserCenterService.getUserList(orgId, userName, pageNum, pageSize);
        Page<ScoreOverviewVo> res = new Page<>(pageNum, pageSize);
        res.setTotal(forms.getTotal());
        res.setPageNum(forms.getPageNum());
        res.setPageSize(forms.getPageSize());
        res.setPages(forms.getPages());
        for (UserOrgResultChildForm form : forms.getData().getResult()) {
            ScoreOverviewVo scoreOverviewVo = new ScoreOverviewVo();
            scoreOverviewVo.setUserId(form.getUserId());

            scoreOverviewVo.setUserName(form.getName());
            scoreOverviewVo.setOrgId(form.getOrgId());
            scoreOverviewVo.setOrgName(form.getOrgName());
            //从缓存获取得分
            if (null == redisTemplate.opsForHash().get("USER_SCORE_" + year, form.getUserId().toString())) {
                userScoreService.calculateScore(form.getUserId());
            }
            Double score = Double.valueOf(redisTemplate.opsForHash().get("USER_SCORE_" + year, form.getUserId().toString()).toString());
            scoreOverviewVo.setScore(score);
            res.add(scoreOverviewVo);
        }
        return new ResponseEntity<>(new Result<>(res, errors), HttpStatus.OK);
    }

    /**
     * 给人员打分
     */
    @HttpMonitorLogger
    @PostMapping(value = "/score")
    @ApiOperation(value = "给人员打分")
    public ResponseEntity<Result<?>> score(@RequestBody List<UserScoreDetailEntity> entitys) {
        userScoreService.score(entitys);
        userScoreService.calculateScore(entitys.get(0).getUserId());
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 人员得分详细
     */
    @HttpMonitorLogger
    @GetMapping(value = "/score/detail")
    @ApiOperation(value = "人员得分详细")
    public ResponseEntity<Result<?>> scoreDetail(@RequestParam(value = "user_id") Long userId,
                                                 @RequestParam(value = "year") Long year) {
        Map<Long, List<UserScoreEntity>> res = userScoreService.findUserScore(userId, year);
        return new ResponseEntity<>(new Result<>(res, errors), HttpStatus.OK);
    }
}
