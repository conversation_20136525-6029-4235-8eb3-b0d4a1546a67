package com.goodsogood.ows.controller.rank;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.db.rank.RateDetailsEntity;
import com.goodsogood.ows.model.db.rank.RateRuleEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.rank.RateVo;
import com.goodsogood.ows.model.vo.rank.StarOrgVo;
import com.goodsogood.ows.service.rank.RateDetailsService;
import com.goodsogood.ows.service.rank.RateRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * Create by FuXiao on 2020/10/22
 */
@RestController
@Log4j2
@RequestMapping("/rate")
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "评级规则", tags = {"评级规则"})
@Validated
public class RateRuleController {
    private final Errors errors;
    private final RateRuleService rateRuleService;
    private final RateDetailsService rateDetailsService;

    public RateRuleController(Errors errors, RateRuleService rateRuleService, RateDetailsService rateDetailsService) {
        this.errors = errors;
        this.rateRuleService = rateRuleService;
        this.rateDetailsService = rateDetailsService;
    }

    /**
     * 新增一整套评级规则
     */
    @HttpMonitorLogger
    @PostMapping(value = "/insert")
    @ApiOperation(value = "新增一整套评级规则")
    public ResponseEntity<Result<?>> insert(@RequestBody RateVo rateVo) {
        Long rateRuleId = rateRuleService.insert(rateVo.getRateRuleEntity()).longValue();
        Arrays.stream(rateVo.getRateDetailsEntities()).forEach(entity -> {
            entity.setRateRuleId(rateRuleId);
            rateDetailsService.insert(entity);
        });
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 删除一整套评级规则
     */
    @HttpMonitorLogger
    @GetMapping(value = "/delete")
    @ApiOperation(value = "删除一整套评级规则")
    public ResponseEntity<Result<?>> delete(@RequestParam(value = "rate_rule_id") Long rateRuleId) {
        rateRuleService.delete(rateRuleId);
        rateDetailsService.delete(rateRuleId);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 修改一整套评级规则
     */
    @HttpMonitorLogger
    @PostMapping(value = "/update")
    @ApiOperation(value = "修改一整套评级规则")
    public ResponseEntity<Result<?>> update(@RequestBody RateVo rateVo) {
        RateRuleEntity rateRuleEntity = rateVo.getRateRuleEntity();
        rateRuleService.update(rateRuleEntity);
        rateDetailsService.delete(rateRuleEntity.getRateRuleId());
        Arrays.stream(rateVo.getRateDetailsEntities()).forEach(entity -> {
            entity.setRateRuleId(rateRuleEntity.getRateRuleId());
            rateDetailsService.insert(entity);
        });
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 查询评级详细
     */
    @HttpMonitorLogger
    @GetMapping(value = "/find-details")
    @ApiOperation(value = "查询评级详细")
    public ResponseEntity<Result<?>> findDetails(@RequestParam(value = "rate_details_id") Long rateDetailsId) {
        RateDetailsEntity res = rateDetailsService.findDetails(rateDetailsId);
        return new ResponseEntity<>(new Result<>(res, errors), HttpStatus.OK);
    }

    /**
     * 查询评级详细大表
     */
    @HttpMonitorLogger
    @GetMapping(value = "/find")
    @ApiOperation(value = "查询评级详细大表")
    public ResponseEntity<Result<?>> find(@RequestParam(value = "rate_rule_id") Long rateRuleId) {
        List<RateDetailsEntity> res = rateDetailsService.findAll(rateRuleId);
        return new ResponseEntity<>(new Result<>(res, errors), HttpStatus.OK);
    }

    /**
     * 查询评级规则大表
     */
    @HttpMonitorLogger
    @GetMapping(value = "/find-all")
    @ApiOperation(value = "查询评级规则大表")
    public ResponseEntity<Result<?>> findAll(@RequestParam(value = "type") Integer type) {
        List<RateRuleEntity> res = rateRuleService.findAll(type);
        return new ResponseEntity<>(new Result<>(res, errors), HttpStatus.OK);
    }

    /**
     * 组织层面查看各星级人数
     */
    @HttpMonitorLogger
    @GetMapping(value = "/find-star-org")
    @ApiOperation(value = "组织层面查看各星级人数")
    public ResponseEntity<Result<?>> findStarOrg(@RequestParam(value = "orgId") Long orgId,
                                                 @RequestParam(value = "rate_rule_id") Long rateRuleId) throws IOException {
        List<StarOrgVo> res = rateRuleService.findStarOrg(orgId, rateRuleId);
        return new ResponseEntity<>(new Result<>(res, errors), HttpStatus.OK);
    }
}
