package com.goodsogood.ows.controller.rank;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.db.rank.ColumnEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.rank.ColumnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by FuXiao on 2020/10/19
 */
@RestController
@Log4j2
@RequestMapping("/column")
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "评分栏目", tags = {"评分栏目"})
@Validated
public class ColumnController {
    private final Errors errors;
    private final ColumnService columnService;

    public ColumnController(Errors errors, ColumnService columnService) {
        this.errors = errors;
        this.columnService = columnService;
    }

    /**
     * 新增评分栏目
     */
    @HttpMonitorLogger
    @PostMapping(value = "/insert")
    @ApiOperation(value = "新增评分栏目")
    public ResponseEntity<Result<?>> insert(@RequestBody ColumnEntity entity) {
        columnService.insert(entity);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 删除评分栏目
     */
    @HttpMonitorLogger
    @GetMapping(value = "/delete")
    @ApiOperation(value = "删除评分栏目")
    public ResponseEntity<Result<?>> delete(@RequestParam(value = "column_id") Long columnId) {
        columnService.delete(columnId);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 修改评分栏目
     */
    @HttpMonitorLogger
    @PostMapping(value = "/update")
    @ApiOperation(value = "修改评分栏目")
    public ResponseEntity<Result<?>> update(@RequestBody ColumnEntity entity) {
        columnService.update(entity);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 查询评分栏目
     */
    @HttpMonitorLogger
    @GetMapping(value = "/find")
    @ApiOperation(value = "查询评分栏目")
    public ResponseEntity<Result<?>> find(@RequestParam(value = "column_id") Long columnId) {
        ColumnEntity res = columnService.find(columnId);
        return new ResponseEntity<>(new Result<>(res, errors), HttpStatus.OK);
    }

    /**
     * 查询评分栏目大表
     */
    @HttpMonitorLogger
    @GetMapping(value = "/find-all")
    @ApiOperation(value = "查询评分栏目大表")
    public ResponseEntity<Result<?>> findAll(@RequestParam(value = "top_id") Long topId) {
        List<ColumnEntity> res = columnService.findAll(topId);
        //根据parent_id做嵌套组合
        res.stream().filter(re -> null != re.getParentId()).forEach(re -> {
            res.stream().filter(parent -> parent.getColumnId().equals(re.getParentId())).forEach(parent -> {
                List<ColumnEntity> tmp = null == parent.getChildren() ? new ArrayList<>() : parent.getChildren();
                tmp.add(re);
                parent.setChildren(tmp);
            });
        });
        res.removeIf(entity -> null != entity.getParentId());
        return new ResponseEntity<>(new Result<>(res, errors), HttpStatus.OK);
    }
}
