package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.meeting.MeetingEntity;
import com.goodsogood.ows.model.db.user.BranchHighlightEntity;
import com.goodsogood.ows.model.mongodb.PartyBrandBase;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.meeting.MeetingPlanCountVO;
import com.goodsogood.ows.model.vo.user.*;
import com.goodsogood.ows.service.WindowService;
import com.goodsogood.ows.service.WindowThirdService;
import com.goodsogood.ows.service.user.UserService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/window")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class WindowController {
    private final Errors errors;
    private final WindowThirdService windowThirdService;
    private final WindowService windowService;
    private final UserService userService;


    public WindowController(Errors errors, WindowThirdService windowThirdService, WindowService windowService, UserService userService) {
        this.errors = errors;

        this.windowThirdService = windowThirdService;
        this.windowService = windowService;
        this.userService = userService;
    }

    @GetMapping("/get-org-period-info")
    @ApiOperation("查询组织届次信息和人数")//无缓存
    @HttpMonitorLogger
    public ResponseEntity<Result<OrgExpandVO>> getOrgPeriodInfo(@RequestParam(value = "org_id", required = false) Long orgId,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = orgId == null ? sysHeader.getOid() : orgId;
        OrgExpandVO vo = windowService.getOrgPeriodInfo(headers,orgId,sysHeader.getRegionId());
        return new ResponseEntity<>(new Result<>(vo, errors), HttpStatus.OK);
    }


    @GetMapping("/get-org-profile")
    @ApiOperation("查询支部简介")//有缓存
    @HttpMonitorLogger
    public ResponseEntity<Result<Map<String,Object>>> getOrgProfile(@RequestParam(value = "org_id", required = false) Long orgId,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = orgId == null ? sysHeader.getOid() : orgId;
        Map<String,Object> vo = windowService.getOrgProfile(headers, orgId,sysHeader.getRegionId());
        return new ResponseEntity<>(new Result<>(vo, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/get-party-member-overview-info")
    @ApiOperation("党员队伍查询:党员/预备党员 男/女")//需加缓存
    public ResponseEntity<Result<?>> getPartyMemberOverviewInfo(
            @RequestParam(value = "org_id", required = false) Long orgId,
            @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = orgId == null ? sysHeader.getOid() : orgId;
        OrgUserOverviewInfo vo = userService.getUserOverviewInfo(orgId,sysHeader,sysHeader.getRegionId());
        return new ResponseEntity<>(new Result<>(vo, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/get-party-distributed")
    @ApiOperation("党员队伍查询:学历/年龄/党龄/")//需加缓存
    public ResponseEntity<Result<PartyDistributedForm>> getPartyDistributed(
            @RequestParam(value = "org_id", required = false) Long orgId,
            @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = orgId == null ? sysHeader.getOid() : orgId;
        PartyDistributedForm vo = userService.getPartyDistributed(orgId,sysHeader,sysHeader.getRegionId());
        return new ResponseEntity<>(new Result<>(vo, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/get-user-list")
    @ApiOperation("根据组织获取用户列表")//无缓存
    public ResponseEntity<Result<?>> getUserList(
            @RequestParam(value = "org_id", required = false) Long orgId,
            @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = orgId == null ? sysHeader.getOid() : orgId;
        List<?> vo = windowService.getUserList(orgId,headers,sysHeader.getRegionId());
        return new ResponseEntity<>(new Result<>(vo, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/get-meeting-plan")
    @ApiOperation("组织生活开展情况")//无缓存
    public ResponseEntity<Result<List<MeetingPlanCountVO>>> getMeetingPlanList(
            @RequestParam(value = "org_id", required = false) Long orgId,
            @RequestParam(value= "flag", required = false, defaultValue = "1") Integer flag,
            @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = orgId == null ? sysHeader.getOid() : orgId;
        List<MeetingPlanCountVO> vo = windowService.getMeetingPlanList(sysHeader.getRegionId(), orgId, flag, 1, 100);
        return new ResponseEntity<>(new Result<>(vo, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/get-meeting-list")
    @ApiOperation("组织生活动态")//无缓存
    public ResponseEntity<Result<List<MeetingEntity>>> getMeetingList(
            @RequestParam(value = "org_id", required = false) Long orgId,
            @ApiParam("查询数量") @RequestParam(value = "num", required = false,defaultValue = "20") Integer num,
            @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = orgId == null ? sysHeader.getOid() : orgId;
        List<MeetingEntity> list = windowService.getMeetingList(headers,sysHeader.getRegionId(), orgId,num);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/get-meeting-num")
    @ApiOperation("组织生活动态")//无缓存
    public ResponseEntity<Result<Integer>> getMeetingNum(
            @RequestParam(value = "org_id", required = false) Long orgId,
            @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = orgId == null ? sysHeader.getOid() : orgId;
        Integer num = windowService.getMeetingNum(headers,sysHeader.getRegionId(), orgId);
        return new ResponseEntity<>(new Result<>(num, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/get-brand")
    @ApiOperation("查询党建品牌")//无缓存
    public ResponseEntity<Result<PartyBrandBase>> getBrandList(
            @RequestParam(value = "org_id", required = false) Long orgId,
            @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = orgId == null ? sysHeader.getOid() : orgId;
        PartyBrandBase vo = windowService.getBrand(sysHeader.getRegionId(), orgId,headers);
        return new ResponseEntity<>(new Result<>(vo, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/get-org-highlight")
    @ApiOperation("查询组织风采/历史")//无缓存
    public ResponseEntity<Result<List<BranchHighlightEntity>>> getOrgHighlight(
            @RequestParam(value="org_id") Long orgId,
            @RequestParam(value="type")Integer type,
            @RequestParam(required = false,name = "page",defaultValue = "1")
            Integer page,
            @RequestParam(required = false, name = "page_size",defaultValue = "5")
            Integer pageSize,
            @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = orgId == null ? sysHeader.getOid() : orgId;
        List<BranchHighlightEntity> vo = windowService.getOrgHighligh(sysHeader.getRegionId(),orgId,type,page,pageSize,headers);
        return new ResponseEntity<>(new Result<>(vo, errors), HttpStatus.OK);
    }

    /**
     * 数据更新接口，管理员补录数据后，点击更新调用该接口清理缓存，再重新调用接口查询
     */
    @HttpMonitorLogger
    @GetMapping("/del-cache")
    @ApiOperation("删除指定缓存")
    public ResponseEntity<Result<String>> updateData( @RequestParam(value="org_id",required = false) Long orgId,
                                                      @RequestParam(value="type",required = false)String type,@RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = orgId == null ? sysHeader.getOid() : orgId;
        windowService.delCache(orgId,type);
        return new ResponseEntity<>(new Result<>("删除成功", errors), HttpStatus.OK);
    }

}
