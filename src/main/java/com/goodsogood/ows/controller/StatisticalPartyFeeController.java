package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.sas.SasOrgLifeConditionForm;
import com.goodsogood.ows.model.vo.sas.SasOrgPayFeeForm;
import com.goodsogood.ows.model.vo.sas.SqlParam;
import com.goodsogood.ows.model.vo.sas.StatisticalPartyFeeForm;
import com.goodsogood.ows.model.vo.sas.WrapperEvalPullDataForm;
import com.goodsogood.ows.service.sas.ExportOrgPartyPayFeeService;
import com.goodsogood.ows.service.sas.StatisticalOrgLifeService;
import com.goodsogood.ows.service.sas.StatisticalPartyFeeService;

import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 *
 * <AUTHOR>
 * @create 2019-04-22 16:04
 **/
@RestController
@RequestMapping("/sas/pay-fee")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "党费统计查询", tags = {"党务看板"})
@Validated
public class StatisticalPartyFeeController {

     private final Errors errors;
     private final StatisticalPartyFeeService service;
     private final StatisticalOrgLifeService statisticalOrgLifeService;
     private final ExportOrgPartyPayFeeService exportOrgPartyPayFeeService;

     @Autowired
     public StatisticalPartyFeeController(Errors errors, StatisticalPartyFeeService service,
                                          StatisticalOrgLifeService statisticalOrgLifeService,
                                          ExportOrgPartyPayFeeService exportOrgPartyPayFeeService) {
          this.errors = errors;
          this.service = service;
          this.statisticalOrgLifeService = statisticalOrgLifeService;
          this.exportOrgPartyPayFeeService = exportOrgPartyPayFeeService;
     }

    @HttpMonitorLogger
    @PostMapping("/list")
    @ApiOperation("查询党费统计列表")
    public ResponseEntity<Result<?>> list(@RequestHeader HttpHeaders headers,
                                                          @Valid @RequestBody SasOrgLifeConditionForm form,
                                                          BindingResult bindingResult) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //组装查询条件
        SasOrgLifeConditionForm conditionForm = this.statisticalOrgLifeService.buildParam(form);
        SasOrgPayFeeForm queryForm = new SasOrgPayFeeForm();
        queryForm.setOrgId(conditionForm.getOrgId());
        queryForm.setStatisticalYear(conditionForm.getYear());
        queryForm.setStartTime(conditionForm.getStartTime());
        queryForm.setEndTime(conditionForm.getEndTime());
        queryForm.setDateStr(conditionForm.getDateStr());
        queryForm.setIsRetire(conditionForm.getIsRetire());
        queryForm.setRegionId(sysHeader.getRegionId());
        //组装sql片段
        SqlParam sqlParam = this.statisticalOrgLifeService.buildSql(conditionForm);
        queryForm.setPayFeeParam(sqlParam.getPayFeeParam());
        // 查询党费统计信息 
        Page<StatisticalPartyFeeForm> list = this.service.findOrgPayFeeList(queryForm, conditionForm.getPageNo(), conditionForm.getPageSize());
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/downExcel")
    @ApiOperation("下载党费统计列表")
    public void downExcel(@RequestHeader HttpHeaders headers,
                          @Range(min = 1, max = 999999L, message = "orgId参数出错") @RequestParam(value = "org_id") Long orgId,
                          @NotNull(message = "组织信息不能为空") @RequestParam(value = "org_name") String orgName,
                          @Range(min = 1, max = 4, message = "时间类型输入出错") @RequestParam(value = "time_type") Integer timeType,
                          @Range(min = 2018, max = 2024, message = "年份输出参数错误") @RequestParam(value = "year") Integer year,
                          @Range(min = 0, max = 6, message = "查询时间参数错误") @RequestParam(value = "time", required = false) Integer time,
                          @RequestParam(value = "is_retire" , required = false) Integer isRetire,
                          @RequestParam(value = "start_month", required = false) Integer startMonth,
                          @RequestParam(value = "end_month", required = false) Integer endMonth,
                          HttpServletResponse response) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        SasOrgLifeConditionForm form = new SasOrgLifeConditionForm();
        form.setOrgId(orgId);
        form.setOrgName(orgName);
        form.setTimeType(timeType);
        form.setYear(year);
        form.setTime(time);
        form.setIsRetire(isRetire);
        form.setStartMonth(startMonth);
        form.setEndMonth(endMonth);
        //组装查询条件
        SasOrgLifeConditionForm conditionForm = this.statisticalOrgLifeService.buildParam(form);
        SasOrgPayFeeForm sasOrgPayFeeForm = new SasOrgPayFeeForm();
        //组装sql片段
        SqlParam sqlParam = this.statisticalOrgLifeService.buildSql(conditionForm);
        sasOrgPayFeeForm.setPayFeeParam(sqlParam.getPayFeeParam());
        sasOrgPayFeeForm.setOrgId(conditionForm.getOrgId());
        sasOrgPayFeeForm.setStatisticalYear(conditionForm.getYear());
        sasOrgPayFeeForm.setStartTime(conditionForm.getStartTime());
        sasOrgPayFeeForm.setEndTime(conditionForm.getEndTime());
        sasOrgPayFeeForm.setDateStr(conditionForm.getDateStr());
        sasOrgPayFeeForm.setIsRetire(conditionForm.getIsRetire());
        sasOrgPayFeeForm.setRegionId(sysHeader.getRegionId());
        sasOrgPayFeeForm.setRegionId(sysHeader.getRegionId());
        // 查询党费统计信息
        List<StatisticalPartyFeeForm> list = this.service.findAllOrgPayFeeList(sasOrgPayFeeForm);
        this.exportOrgPartyPayFeeService.exportPayFee(response, conditionForm, list);
     }


     /**
      *考核系统党费缴纳数据拉取
      *
      * @param
      * @return
      * <AUTHOR>
      * @date 2019/5/8 10:56
      */
     @HttpMonitorLogger
     @ApiOperation(value = "考核系统拉取数据")
     @GetMapping("/pullPayFeeList")
     public ResponseEntity<Result<?>> pullPayFeeList(@RequestHeader HttpHeaders headers,
                                                                         @NotNull(message = "page_no 不能为空") @RequestParam(name = "page_no") Long pageNo,
                                                                         @NotNull(message = "page_size 不能为空") @RequestParam(name = "page_size") Long pageSize,
                                                                         @RequestParam(name = "org_id",required = false) Long orgId,
                                                                         @RequestParam(name = "sta_code",required = false,defaultValue = "0") Long staTimeCode) {
         try {

             HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
             Long regionId=sysHeader.getRegionId();
             Page<StatisticalPartyFeeForm> pageDatas = service.findPayFeeList(pageNo, pageSize, orgId, staTimeCode,regionId);
             WrapperEvalPullDataForm wrapper = new WrapperEvalPullDataForm();
             wrapper.setPageSize(pageDatas.getPageSize());
             wrapper.setTotal(pageDatas.getTotal());
             wrapper.setPages(pageDatas.getPages());
             wrapper.setList(pageDatas.getResult());
             return new ResponseEntity<>(new Result<>(wrapper, errors), HttpStatus.OK);
         }catch (Exception ex){
             log.error("考核系统拉取党费缴纳数据出错-msg-{}",ex);
             throw new ApiException("考核系统拉取党费缴纳数据出错", new Result<>(errors, 4713, HttpStatus.OK.value()));
         }

     }
}
