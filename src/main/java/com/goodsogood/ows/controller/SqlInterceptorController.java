package com.goodsogood.ows.controller;

import com.goodsogood.ows.interceptor.DamengSqlInterceptor;
import com.goodsogood.ows.interceptor.MySQLToDamengSqlConverter;
import com.goodsogood.ows.interceptor.SqlInterceptorConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * SQL拦截器管理控制器
 * 提供运行时管理SQL拦截器的接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Slf4j
@RestController
@RequestMapping("/api/sql-interceptor")
@Api(tags = "SQL拦截器管理")
public class SqlInterceptorController {

    @Autowired
    private DamengSqlInterceptor sqlInterceptor;
    
    @Autowired
    private MySQLToDamengSqlConverter sqlConverter;
    
    @Autowired
    private SqlInterceptorConfig config;
    
    /**
     * 获取拦截器状态
     */
    @GetMapping("/status")
    @ApiOperation("获取拦截器状态")
    public Map<String, Object> getStatus() {
        Map<String, Object> status = new HashMap<>();
        
        DamengSqlInterceptor.InterceptorStats stats = sqlInterceptor.getStats();
        
        status.put("enabled", stats.isEnabled());
        status.put("convertAllDataSources", stats.isConvertAllDataSources());
        status.put("cacheSize", stats.getCacheSize());
        status.put("enabledDataSources", stats.getEnabledDataSources());
        status.put("config", config.getConfigSummary());
        
        return status;
    }
    
    /**
     * 启用拦截器
     */
    @PostMapping("/enable")
    @ApiOperation("启用拦截器")
    public Map<String, Object> enableInterceptor() {
        config.setEnabled(true);
        log.info("SQL interceptor enabled");
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "SQL interceptor enabled");
        result.put("enabled", true);
        
        return result;
    }
    
    /**
     * 禁用拦截器
     */
    @PostMapping("/disable")
    @ApiOperation("禁用拦截器")
    public Map<String, Object> disableInterceptor() {
        config.setEnabled(false);
        log.info("SQL interceptor disabled");
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "SQL interceptor disabled");
        result.put("enabled", false);
        
        return result;
    }
    
    /**
     * 清除SQL缓存
     */
    @PostMapping("/clear-cache")
    @ApiOperation("清除SQL缓存")
    public Map<String, Object> clearCache() {
        int oldSize = sqlInterceptor.getCacheSize();
        sqlInterceptor.clearSqlCache();
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "SQL cache cleared");
        result.put("oldCacheSize", oldSize);
        result.put("newCacheSize", 0);
        
        return result;
    }
    
    /**
     * 添加启用的数据源
     */
    @PostMapping("/datasource/{dataSourceName}/enable")
    @ApiOperation("启用指定数据源的SQL转换")
    public Map<String, Object> enableDataSource(
            @ApiParam("数据源名称") @PathVariable String dataSourceName) {
        
        sqlInterceptor.addEnabledDataSource(dataSourceName);
        config.addEnabledDataSource(dataSourceName);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "Data source enabled: " + dataSourceName);
        result.put("dataSourceName", dataSourceName);
        result.put("enabledDataSources", sqlInterceptor.getEnabledDataSources());
        
        return result;
    }
    
    /**
     * 移除启用的数据源
     */
    @PostMapping("/datasource/{dataSourceName}/disable")
    @ApiOperation("禁用指定数据源的SQL转换")
    public Map<String, Object> disableDataSource(
            @ApiParam("数据源名称") @PathVariable String dataSourceName) {
        
        sqlInterceptor.removeEnabledDataSource(dataSourceName);
        config.removeEnabledDataSource(dataSourceName);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "Data source disabled: " + dataSourceName);
        result.put("dataSourceName", dataSourceName);
        result.put("enabledDataSources", sqlInterceptor.getEnabledDataSources());
        
        return result;
    }
    
    /**
     * 获取启用的数据源列表
     */
    @GetMapping("/datasources")
    @ApiOperation("获取启用的数据源列表")
    public Map<String, Object> getEnabledDataSources() {
        Set<String> enabledDataSources = sqlInterceptor.getEnabledDataSources();
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("enabledDataSources", enabledDataSources);
        result.put("count", enabledDataSources.size());
        
        return result;
    }
    
    /**
     * 测试SQL转换
     */
    @PostMapping("/test-conversion")
    @ApiOperation("测试SQL转换")
    public Map<String, Object> testSqlConversion(
            @ApiParam("原始SQL") @RequestBody Map<String, String> request) {
        
        String originalSql = request.get("sql");
        if (originalSql == null || originalSql.trim().isEmpty()) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "SQL cannot be empty");
            return error;
        }
        
        try {
            String convertedSql = sqlConverter.convertSql(originalSql);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("originalSql", originalSql);
            result.put("convertedSql", convertedSql);
            result.put("changed", !originalSql.equals(convertedSql));
            
            return result;
            
        } catch (Exception e) {
            log.error("Error testing SQL conversion", e);
            
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "Error converting SQL: " + e.getMessage());
            error.put("originalSql", originalSql);
            
            return error;
        }
    }
    
    /**
     * 获取配置信息
     */
    @GetMapping("/config")
    @ApiOperation("获取配置信息")
    public Map<String, Object> getConfig() {
        Map<String, Object> configMap = new HashMap<>();
        
        configMap.put("enabled", config.isEnabled());
        configMap.put("convertAllDataSources", config.isConvertAllDataSources());
        configMap.put("enabledDataSources", config.getEnabledDataSources());
        configMap.put("cacheEnabled", config.isCacheEnabled());
        configMap.put("maxCacheSize", config.getMaxCacheSize());
        configMap.put("logEnabled", config.isLogEnabled());
        configMap.put("logLevel", config.getLogLevel());
        configMap.put("performanceMonitorEnabled", config.isPerformanceMonitorEnabled());
        configMap.put("performanceThreshold", config.getPerformanceThreshold());
        configMap.put("functionConversionEnabled", config.isFunctionConversionEnabled());
        configMap.put("keywordConversionEnabled", config.isKeywordConversionEnabled());
        configMap.put("syntaxConversionEnabled", config.isSyntaxConversionEnabled());
        configMap.put("limitConversionEnabled", config.isLimitConversionEnabled());
        configMap.put("dateFunctionConversionEnabled", config.isDateFunctionConversionEnabled());
        configMap.put("stringFunctionConversionEnabled", config.isStringFunctionConversionEnabled());
        configMap.put("mathFunctionConversionEnabled", config.isMathFunctionConversionEnabled());
        configMap.put("aggregateFunctionConversionEnabled", config.isAggregateFunctionConversionEnabled());
        configMap.put("conditionalFunctionConversionEnabled", config.isConditionalFunctionConversionEnabled());
        
        return configMap;
    }
    
    /**
     * 更新配置
     */
    @PostMapping("/config")
    @ApiOperation("更新配置")
    public Map<String, Object> updateConfig(@RequestBody Map<String, Object> configUpdates) {
        try {
            // 更新基本配置
            if (configUpdates.containsKey("enabled")) {
                config.setEnabled((Boolean) configUpdates.get("enabled"));
            }
            if (configUpdates.containsKey("convertAllDataSources")) {
                config.setConvertAllDataSources((Boolean) configUpdates.get("convertAllDataSources"));
            }
            if (configUpdates.containsKey("cacheEnabled")) {
                config.setCacheEnabled((Boolean) configUpdates.get("cacheEnabled"));
            }
            if (configUpdates.containsKey("maxCacheSize")) {
                config.setMaxCacheSize((Integer) configUpdates.get("maxCacheSize"));
            }
            if (configUpdates.containsKey("logEnabled")) {
                config.setLogEnabled((Boolean) configUpdates.get("logEnabled"));
            }
            if (configUpdates.containsKey("logLevel")) {
                config.setLogLevel((String) configUpdates.get("logLevel"));
            }
            if (configUpdates.containsKey("performanceMonitorEnabled")) {
                config.setPerformanceMonitorEnabled((Boolean) configUpdates.get("performanceMonitorEnabled"));
            }
            if (configUpdates.containsKey("performanceThreshold")) {
                config.setPerformanceThreshold(((Number) configUpdates.get("performanceThreshold")).longValue());
            }
            
            // 更新功能开关
            if (configUpdates.containsKey("functionConversionEnabled")) {
                config.setFunctionConversionEnabled((Boolean) configUpdates.get("functionConversionEnabled"));
            }
            if (configUpdates.containsKey("keywordConversionEnabled")) {
                config.setKeywordConversionEnabled((Boolean) configUpdates.get("keywordConversionEnabled"));
            }
            if (configUpdates.containsKey("syntaxConversionEnabled")) {
                config.setSyntaxConversionEnabled((Boolean) configUpdates.get("syntaxConversionEnabled"));
            }
            if (configUpdates.containsKey("limitConversionEnabled")) {
                config.setLimitConversionEnabled((Boolean) configUpdates.get("limitConversionEnabled"));
            }
            if (configUpdates.containsKey("dateFunctionConversionEnabled")) {
                config.setDateFunctionConversionEnabled((Boolean) configUpdates.get("dateFunctionConversionEnabled"));
            }
            if (configUpdates.containsKey("stringFunctionConversionEnabled")) {
                config.setStringFunctionConversionEnabled((Boolean) configUpdates.get("stringFunctionConversionEnabled"));
            }
            if (configUpdates.containsKey("mathFunctionConversionEnabled")) {
                config.setMathFunctionConversionEnabled((Boolean) configUpdates.get("mathFunctionConversionEnabled"));
            }
            if (configUpdates.containsKey("aggregateFunctionConversionEnabled")) {
                config.setAggregateFunctionConversionEnabled((Boolean) configUpdates.get("aggregateFunctionConversionEnabled"));
            }
            if (configUpdates.containsKey("conditionalFunctionConversionEnabled")) {
                config.setConditionalFunctionConversionEnabled((Boolean) configUpdates.get("conditionalFunctionConversionEnabled"));
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Configuration updated successfully");
            result.put("updatedFields", configUpdates.keySet());
            
            return result;
            
        } catch (Exception e) {
            log.error("Error updating configuration", e);
            
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "Error updating configuration: " + e.getMessage());
            
            return error;
        }
    }
}
