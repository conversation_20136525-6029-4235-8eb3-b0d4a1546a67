package com.goodsogood.ows.controller;


import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.TbcFusionScheduler;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.tbc.BranchFortressIndexVo;
import com.goodsogood.ows.model.vo.tbc.PartyMemberXFIndexVo;
import com.goodsogood.ows.model.vo.tbc.SwimLaneDiagramDetailVo;
import com.goodsogood.ows.model.vo.tbc.SwimLaneDiagramVo;
import com.goodsogood.ows.model.vo.tbcFusion.*;
import com.goodsogood.ows.service.tbcFusion.*;
import com.goodsogood.ows.service.tbcFusion.test.TbcFalseDataService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.ExclExportUtils;
import com.goodsogood.ows.utils.JsonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021-12-03
 **/
@RestController
@RequestMapping("/tbc")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class TbcMergeController {
    private final Errors errors;
    private final TbcBasicsService tbcBasicsService;
    private final TbcFortressService tbcFortressService;
    private final TbcInnovateService tbcInnovateService;
    private final TbcMatchingService tbcMatchingService;
    private final TbcPartyBranchWordService tbcPartyBranchWordService;
    private final TbcPartyMemberWorkService tbcPartyMemberWorkService;
    private final TbcPioneerIndexService tbcPioneerIndexService;
    private final TbcGetService tbcGetService;
    private final StringRedisTemplate redisTemplate;
    private final TbcFusionScheduler tbcFusionScheduler;
    private final TbcFalseDataService tbcFalseDataService;

    @Autowired
    public TbcMergeController(Errors errors,
                              TbcBasicsService tbcBasicsService,
                              TbcFortressService tbcFortressService,
                              TbcInnovateService tbcInnovateService,
                              TbcMatchingService tbcMatchingService,
                              TbcPartyBranchWordService tbcPartyBranchWordService,
                              TbcPartyMemberWorkService tbcPartyMemberWorkService,
                              TbcPioneerIndexService tbcPioneerIndexService,
                              TbcGetService tbcGetService,
                              StringRedisTemplate redisTemplate,
                              TbcFusionScheduler tbcFusionScheduler,
                              TbcFalseDataService tbcFalseDataService) {
        this.errors = errors;
        this.tbcBasicsService = tbcBasicsService;
        this.tbcFortressService = tbcFortressService;
        this.tbcInnovateService = tbcInnovateService;
        this.tbcMatchingService = tbcMatchingService;
        this.tbcPartyBranchWordService = tbcPartyBranchWordService;
        this.tbcPartyMemberWorkService = tbcPartyMemberWorkService;
        this.tbcPioneerIndexService = tbcPioneerIndexService;
        this.tbcGetService = tbcGetService;
        this.redisTemplate = redisTemplate;
        this.tbcFusionScheduler = tbcFusionScheduler;
        this.tbcFalseDataService = tbcFalseDataService;
    }

    @HttpMonitorLogger
    @GetMapping("/org/basics")
    @ApiOperation("党支部基础信息统计")
    public ResponseEntity<Result<?>> getTbcBasics() {
        String key = Constants.ORG_BASICS_COUNT;
        List<TbcBasicsForm.OrgUserDetails> result;
        if (redisTemplate.hasKey(key)) {
            String value = redisTemplate.opsForValue().get(key);
            result = (List<TbcBasicsForm.OrgUserDetails>)
                    JsonUtils.fromJson(value, List.class, TbcBasicsForm.OrgUserDetails.class);
        } else {
            result = tbcBasicsService.getTbcBasics();
        }
        // TODO
//        List<TbcBasicsForm.OrgUserDetails> form = tbcFalseDataService.getTbcBasics();
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);


    }

    @HttpMonitorLogger
    @GetMapping("/org-basics-count")
    @ApiOperation("党支部党树统计信息")
    public ResponseEntity<Result<?>> getOrgBasics(@RequestParam(name = "type", required = false) Integer type) {
        String key = String.format(Constants.BASICS, type);
        Integer result;
        if (redisTemplate.hasKey(key)) {
            String value = redisTemplate.opsForValue().get(key);
            result = Integer.valueOf(value);
        } else {
            result = tbcBasicsService.getOrgBasics(type);
        }
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/tbc-fortress")
    @ApiOperation("支部堡垒指数和党建先锋指数统计")
    public ResponseEntity<Result<?>> getTbcFortress(@RequestParam(name = "type", defaultValue = "1") Integer type) {
        TbcFortressForm result;
        String key = String.format(Constants.TBC_FORTRESS, type);
        if (redisTemplate.hasKey(key)) {
            String value = redisTemplate.opsForValue().get(key);
            result = JsonUtils.fromJson(value, TbcFortressForm.class);
        } else {
            result = tbcFortressService.getTbcFortress(type);
        }
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/tbc-threefortress")
    @ApiOperation("支部堡垒指数和党建先锋指数前三统计")
    public ResponseEntity<Result<?>> getTbcThreeFortress(@RequestParam(name = "type", required = false) Integer type) {
        String key = String.format(Constants.PARTY_FORTRESS, type);
        List<TbcUserIndexForm> list;
        List<String> list1;
        List<String> result = new ArrayList<>();
        if (redisTemplate.hasKey(key)) {
            String value = redisTemplate.opsForValue().get(key);
            list = (List<TbcUserIndexForm>) JsonUtils.fromJson(value, List.class, TbcUserIndexForm.class);
        } else {
            list = tbcFortressService.getSortOrgName(type, 3);
        }
        list1 = list.stream().map(TbcUserIndexForm::getOrgName).collect(Collectors.toList());
        String s1;
        s1 = type == 1 ? "堡垒指数NO.%s %s" : "党员先锋指数NO.%s %s";
        for (int i = 0, j = 1; i < list1.size(); i++, j++) {
            result.add(String.format(s1, j, list1.get(i)));
        }
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org-splashes")
    @ApiOperation("支部工作情况(散点图)")
    public ResponseEntity<Result<?>> getSplashes() {
        String key = Constants.ORG_SPLASHES;
        TbcSplashesForm result;
        if (redisTemplate.hasKey(key)) {
            String value = redisTemplate.opsForValue().get(key);
            result = JsonUtils.fromJson(value, TbcSplashesForm.class);
        }
        // TODO
        result = tbcPartyMemberWorkService.getSplashes();
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org-party-member")
    @ApiOperation("党员”党建+业务”工作情况柱状图")
    public ResponseEntity<Result<?>> getPartyMember(@RequestParam(name = "type", required = false) Integer type) {
        String key = String.format(Constants.ORG_PARTY_MEMBER, type);
        List<TbcPartyMemberWorkForm> result;
        if (redisTemplate.hasKey(key)) {
            String value = redisTemplate.opsForValue().get(key);
            result = (List<TbcPartyMemberWorkForm>) JsonUtils.fromJson(value, List.class, TbcPartyMemberWorkForm.class);
        } else {
            result = Collections.singletonList(tbcPartyBranchWordService.getPartyMemberNew(type));

        }
        // TODO
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org-pioneer-index")
    @ApiOperation("党员先锋指数前20%(饼状图)")
    public ResponseEntity<Result<?>> getPioneerIndex() {
        String key = Constants.ORG_PIONEER_INDEX;
        TbcPioneerIndexForm result;
        if (redisTemplate.hasKey(key)) {
            String value = redisTemplate.opsForValue().get(key);
            result = JsonUtils.fromJson(value, TbcPioneerIndexForm.class);
        } else {
            result = tbcPioneerIndexService.getPioneerIndex();
        }
        // TODO
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/tbc-innovate")
    @ApiOperation("创新情况统计(词云)")
    public ResponseEntity<Result<?>> getTbcInnovate() {
        String key = Constants.TBC_INNOVATE;
        List<TbcInnovateForm> result;
        if (redisTemplate.hasKey(key)) {
            String value = redisTemplate.opsForValue().get(key);
            result = (List<TbcInnovateForm>) JsonUtils.fromJson(value, List.class, TbcInnovateForm.class);
        } else {
            result = tbcInnovateService.getTbcInnovate();
        }
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org-pioneer-tilte")
    @ApiOperation("词云统计标题")
    public ResponseEntity<Result<?>> getOrgPioneerTilte() {
        String key = Constants.ORG_PIONEER_TITLE;
        String result;
        if (redisTemplate.hasKey(key)) {
            String value = redisTemplate.opsForValue().get(key);
            result = value;
        } else {
            result = tbcInnovateService.getOrgPioneerTitle();
        }
        // TODO
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/tbc-matching")
    @ApiOperation("党页拟合度(条形图)")
    public ResponseEntity<Result<?>> getTbcMatching(@RequestParam(name = "sort_type", required = false, defaultValue = "1")
                                                    Integer sortType,
                                                    @RequestParam(name = "type") Integer type) {
        String key = String.format(Constants.TBC_MATCHING, sortType, type);
        String result;
        if (redisTemplate.hasKey(key)) {
            result = redisTemplate.opsForValue().get(key);
        } else {
            result = JsonUtils.toJson(tbcMatchingService.getTbcMatchingNew(type));
        }
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/tbc-map")
    @ApiOperation("党页融合地图区)")
    public ResponseEntity<Result<?>> getMap(@RequestParam(name = "type") Integer type) {
//        String key = String.format(Constants.TBC_MAP, type);
//        List<TbcMapForm> result;
//        if (redisTemplate.hasKey(key)) {
//            String value = redisTemplate.opsForValue().get(key);
//            result = (List<TbcMapForm>) JsonUtils.fromJson(value, List.class, TbcMapForm.class);
//        } else {
//            result = tbcFortressService.getMap(type);
//        }
        // TODO
        return new ResponseEntity<>(new Result<>(tbcFortressService.getMap(type), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/tbc-top-ten")
    @ApiOperation("获取前十的支部信息")
    public ResponseEntity<Result<?>> getTop(@RequestParam(name = "type") Integer type) {
        String key = String.format(Constants.PARTY_FORTRESS_TOP_TEN, type);
        List<TbcUserIndexForm> result;
        if (redisTemplate.hasKey(key)) {
            String value = redisTemplate.opsForValue().get(key);
            result = (List<TbcUserIndexForm>) JsonUtils.fromJson(value, List.class, TbcUserIndexForm.class);
        } else {
            result = tbcFortressService.getSortOrgName(type, 10);
            ;
        }
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/redis")
    @ApiOperation("刷新redis缓存)")
    public ResponseEntity<Result<?>> refreshCache() {
        tbcFusionScheduler.RefreshCache();
        return new ResponseEntity<>(new Result<>("", errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/tbc-org")
    @ApiOperation("党组织视图")
    public ResponseEntity<Result<?>> tbcOrg(@RequestHeader HttpHeaders headers,
                                            @RequestParam(value = "org_id", required = false) Long orgId) {
        return tbcBasicsService.tbcOrg(headers, orgId);
    }


    @HttpMonitorLogger
    @GetMapping("/tbc-org-more")
    @ApiOperation("查看更多的支部信息")
    public ResponseEntity<Result<?>> tbcOrgMore(@RequestHeader HttpHeaders headers,
                                                @RequestParam(value = "org_id") Long orgId,
                                                @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                @RequestParam(value = "page_size", required = false, defaultValue = "10")
                                                Integer pageSize) {
        return tbcBasicsService.tbcOrgMore(headers, orgId, page, pageSize);
    }

    @HttpMonitorLogger
    @GetMapping("/tbc-user")
    @ApiOperation("用户视图")
    public ResponseEntity<Result<?>> tbcUser(@RequestHeader HttpHeaders headers,
                                             @RequestParam(value = "user_id") Long userId) {
        return tbcBasicsService.tbcUser(headers, userId);
    }


    @HttpMonitorLogger
    @GetMapping("/tbc-user-more")
    @ApiOperation("查看更多的支部信息")
    public ResponseEntity<Result<?>> tbcUserMore(@RequestHeader HttpHeaders headers,
                                                 @RequestParam(value = "org_id") Long orgId,
                                                 @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                 @RequestParam(value = "page_size", required = false, defaultValue = "10")
                                                 Integer pageSize) {
        return tbcBasicsService.tbcUserMore(headers, orgId, page, pageSize);
    }

    @HttpMonitorLogger
    @GetMapping("/tbc-score-log")
    @ApiOperation("根据组织id或者用户id获取积分流水详情")
    public ResponseEntity<Result<?>> getScoreLog(@RequestHeader HttpHeaders headers,
                                                 @RequestParam(value = "org_id", required = false) Long orgId,
                                                 @RequestParam(value = "user_id", required = false) Long userId,
                                                 @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                 @RequestParam(value = "page_size", required = false, defaultValue = "10")
                                                 Integer pageSize) {
        return new ResponseEntity<>(new Result<>(tbcBasicsService.getScoreLog(orgId, userId, page, pageSize), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/xf-rank-sta")
    @ApiOperation("融合概况先锋指数排名")
    public ResponseEntity<Result<?>> xfRankSta(@RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(tbcBasicsService.xfRankSta(headers), errors), HttpStatus.OK);
    }


    /**
     * @param headers
     * @param type    1.先锋指数 2.党建积分3.业务积分4.创新积分
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/xf-stitching")
    @ApiOperation("融合概况先锋指数拆线图")
    public ResponseEntity<Result<?>> xfStitching(@RequestHeader HttpHeaders headers,
                                                 @RequestParam(value = "unit_id") Long unitId,
                                                 @RequestParam(value = "type") Integer type) {
        return new ResponseEntity<>(new Result<>(tbcBasicsService.xfStitching(headers,unitId,type), errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/manager/fortress-index")
    @ApiOperation("管理员页面支部堡垒指数")
    public ResponseEntity<Result<?>> fortressIndex(@RequestHeader HttpHeaders headers,
                                                   @RequestParam(value = "org_id") Long orgId,
                                                   @RequestParam(value = "sta_month", required = false) String staMonth) {
        //如果没有传值 默认取当信息
        if (StringUtils.isEmpty(staMonth)) {
            staMonth = DateUtils.getCurrentMonth("yyyyMM");
        }
        return new ResponseEntity<>(new Result<>(tbcBasicsService.fortressIndex(headers, staMonth, orgId), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/manager/fortress-index/detail")
    @ApiOperation("管理员页面支部堡垒指数详情")
    public ResponseEntity<Result<?>> fortressIndexDetail(@RequestHeader HttpHeaders headers,
                                                         @RequestParam(value = "sta_month", required = false) String staMonth) {
        //如果没有传值 默认取当信息
        if (StringUtils.isEmpty(staMonth)) {
            staMonth = DateUtils.getCurrentMonth("yyyyMM");
        }
        return new ResponseEntity<>(new Result<>(tbcBasicsService.fortressIndexDetail(headers, staMonth), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/manager/fortress-index/detail/export")
    @ApiOperation("管理员页面支部堡垒指数详情-导出")
    public ResponseEntity<Result<?>> fortressIndexDetailExport(@RequestHeader HttpHeaders headers,
                                                               HttpServletResponse response,
                                                               HttpServletRequest request,
                                                               @RequestParam(value = "sta_month", required = false) String staMonth) {
        //如果没有传值 默认取当信息
        if (StringUtils.isEmpty(staMonth)) {
            staMonth = DateUtils.getCurrentMonth("yyyyMM");
        }
        try {
            List<BranchFortressIndexVo.BranchFortress> branchFortresses = tbcBasicsService.fortressIndexDetail(headers, staMonth);
            if (CollectionUtils.isEmpty(branchFortresses)) {
                throw new ApiException(
                        "没有查询到数据，无法展出！", new Result<>(errors, 4712, HttpStatus.OK.value()));
            }
            String excelName = "支部堡垒指数";
            String[] colName = {"排名", "支部名称", "堡垒指数", "党建积分", "业务积分", "创新积分"};
            HSSFWorkbook workbook = tbcBasicsService.exportFortressIndexExcel(branchFortresses, excelName, colName);
            boolean flag = ExclExportUtils.export03(request, response, workbook, excelName);
            log.info("管理员页面支部堡垒指数详情导出结果：" + flag);
            return null;
        } catch (Exception ex) {
            log.error("管理员页面支部堡垒指数详情导出异常", ex);
            throw new ApiException(
                    "管理员页面支部堡垒指数详情导出失败！", new Result<>(errors, 4712, HttpStatus.OK.value()));
        }

    }

    @HttpMonitorLogger
    @GetMapping("/manager/party-member-xfindex")
    @ApiOperation("管理员页面人员先峰指数")
    public ResponseEntity<Result<PartyMemberXFIndexVo>> partyMemberXfindex(@RequestHeader HttpHeaders headers,
                                                                           @RequestParam(value = "unit_id") Long unitId,
                                                                           @RequestParam(value = "sta_month", required = false) String staMonth) {
        //如果没有传值 默认取当信息
        if (StringUtils.isEmpty(staMonth)) {
            staMonth = DateUtils.getCurrentMonth("yyyyMM");
        }
        PartyMemberXFIndexVo vo;
        try {
            vo = tbcBasicsService.partyMemberXfindex(headers, staMonth, unitId);
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(), e);
            vo = new PartyMemberXFIndexVo();
        }
        return new ResponseEntity<>(new Result<>(vo, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/manager/party-member-xfindex/detail")
    @ApiOperation("管理员页面人员先峰指数详情")
    public ResponseEntity<Result<?>> partyMemberXfindexDetail(@RequestHeader HttpHeaders headers,
                                                              @RequestParam(value = "unit_id") Long unitId,
                                                              @RequestParam(value = "sta_month", required = false) String staMonth,
                                                              @RequestParam(value = "type") Integer type) {
        //如果没有传值 默认取当信息
        if (StringUtils.isEmpty(staMonth)) {
            staMonth = DateUtils.getCurrentMonth("yyyyMM");
        }
        return new ResponseEntity<>(new Result<>(tbcBasicsService.partyMemberXfindexDetail(headers, staMonth, type, unitId), errors),
                HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/manager/party-member-xfindex/detail/export")
    @ApiOperation("管理员页面人员先峰指数详情-导出")
    public ResponseEntity<Result<?>> partyMemberXfindexDetailExport(@RequestHeader HttpHeaders headers,
                                                                    HttpServletResponse response,
                                                                    HttpServletRequest request,
                                                                    @RequestParam(value = "unit_id") Long unitId,
                                                                    @RequestParam(value = "sta_month", required = false) String staMonth,
                                                                    @RequestParam(value = "type") Integer type) {
        //如果没有传值 默认取当信息
        if (StringUtils.isEmpty(staMonth)) {
            staMonth = DateUtils.getCurrentMonth("yyyyMM");
        }
        try {
            List<PartyMemberXFIndexVo.XianFengIndex> xianFengIndices = tbcBasicsService.partyMemberXfindexDetail(headers,
                    staMonth, type, unitId);
            if (CollectionUtils.isEmpty(xianFengIndices)) {
                throw new ApiException(
                        "没有查询到数据，无法展出！", new Result<>(errors, 4712, HttpStatus.OK.value()));
            }
            String excelName = "党员先锋指数";
            String[] colName = {"排名", "党员姓名", "所属支部", "先锋指数", "党建积分", "业务积分", "创新积分"};
            HSSFWorkbook workbook = tbcBasicsService.exportXianFengIndexExcel(xianFengIndices, excelName, colName);
            boolean flag = ExclExportUtils.export03(request, response, workbook, excelName);
            log.info("管理员页面人员先峰指数详情展出结果：" + flag);
            return null;
        } catch (Exception ex) {
            log.error("管理员页面人员先峰指数详情展出异常", ex);
            throw new ApiException(
                    "管理员页面人员先峰指数详情展出失败！", new Result<>(errors, 4712, HttpStatus.OK.value()));
        }
    }


    @HttpMonitorLogger
    @GetMapping("/swim-lane-diagram")
    @ApiOperation("泳道图")
    public ResponseEntity<Result<?>> swimLaneDiagram(@RequestHeader HttpHeaders headers,
                                                     @RequestParam(value = "unit_id") Long unitId,
                                                     @RequestParam(value = "type") Integer type,
                                                     @RequestParam(value = "sta_month") String staMonth) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        SwimLaneDiagramVo swimLaneDiagramVo = tbcBasicsService.swimLaneDiagram(unitId, type, staMonth);
        return new ResponseEntity<>(new Result<>(swimLaneDiagramVo, errors), HttpStatus.OK);
    }

    /**
     * 0 -> 全体，1 -> 卷烟营销，2 -> 烟叶生成，3 -> 专卖管理，4 -> 综合管理
     *
     * @param headers
     * @param unitId
     * @param type
     * @param staMonth
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/swim-lane-diagram/detail")
    @ApiOperation("泳道图详情")
    public ResponseEntity<Result<?>> swimLaneDiagramDetail(@RequestHeader HttpHeaders headers,
                                                           @RequestParam(value = "unit_id", required = false) Long unitId,
                                                           @RequestParam(value = "type") Integer type,
                                                           @RequestParam(value = "sta_month") String staMonth) {
        List<SwimLaneDiagramDetailVo> swimLaneDiagramDetailVos = tbcBasicsService.swimLaneDiagramDetail(unitId, type, staMonth);
        return new ResponseEntity<>(new Result<>(swimLaneDiagramDetailVos, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/get-seq")
    @ApiOperation("得到用户所属性序列信息")
    public ResponseEntity<Result<?>> getSeq(@RequestHeader HttpHeaders headers,
                                            @RequestParam(value = "user_id") Long userId) {
        return new ResponseEntity<>(new Result<>(tbcBasicsService.getSeq(userId), errors), HttpStatus.OK);
    }

}