package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.CenterGroupVo;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.CenterGroupService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/center-group")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class CenterGroupController {
    private final Errors errors;
    private final CenterGroupService centerGroupService;

    @Autowired
    public CenterGroupController(Errors errors, CenterGroupService centerGroupService) {
        this.errors = errors;
        this.centerGroupService = centerGroupService;
    }


    @HttpMonitorLogger
    @ApiOperation(value = "中心组统计")
    @GetMapping("/statistics")
    public ResponseEntity<Result<List<CenterGroupVo>>> statistics(@RequestHeader HttpHeaders headers,
                                                                  @RequestParam(name = "name", required = false) String name,
                                                                  @RequestParam(name = "year", required = true) Integer year) {
        Result<List<CenterGroupVo>> result = new Result<>(centerGroupService.statistics(headers,name,year), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
}
