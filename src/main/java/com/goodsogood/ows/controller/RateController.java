package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.utils.RateUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 进度查看
 * <AUTHOR>
 */
@Controller
@Log4j2
@RequestMapping("/rate")
public class RateController {

    private final Errors errors;

    @Autowired
    public RateController(Errors errors) {
        this.errors = errors;
    }

    @HttpMonitorLogger
    @GetMapping("/getRate")
    @ApiOperation("获取实际数据进度")
    public ResponseEntity<Result<?>> getRate(@RequestHeader HttpHeaders headers,
                                                @RequestParam(value = "uuid") String uuid) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        String s = RateUtils.getRate(uuid, 1);
        return new ResponseEntity<>(new Result<>(s, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/getRatePercent")
    @ApiOperation("获取进度百分比")
    public ResponseEntity<Result<?>> getRatePercent(@RequestHeader HttpHeaders headers,
                                             @RequestParam(value = "uuid") String uuid) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        String s = RateUtils.getRate(uuid, 2);
        return new ResponseEntity<>(new Result<>(s, errors), HttpStatus.OK);
    }
}
