package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.sas.StasticConfigInfoForm;
import com.goodsogood.ows.model.vo.sas.StasticConfigListForm;
import com.goodsogood.ows.service.sas.StasticConfigInfoService;
import com.goodsogood.ows.utils.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 党务看板统计
 *
 * <AUTHOR>
 * @date 2019/4/18 19:42
 */
@RestController
@RequestMapping("/sas/sas-config")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "党务看板统计设置信息", tags = {"党务看板统计设置信息"})
@Validated
public class StasticConfigInfoController {

    private final Errors errors;
    private final StasticConfigInfoService stasticConfigInfoService;

    public StasticConfigInfoController(Errors errors, StasticConfigInfoService stasticConfigInfoService) {
        this.errors = errors;
        this.stasticConfigInfoService = stasticConfigInfoService;
    }


    /**
     * 新增党务看板统计设置信息
     *
     * <AUTHOR>
     * @date 2019/4/22 10:54
     */

    @HttpMonitorLogger
    @PostMapping("/add")
    @ApiOperation("新增党务看板统计设置信息")
    public ResponseEntity<Result<?>> addConfigInfo(@RequestHeader HttpHeaders headers,
                                                   @Valid @RequestBody StasticConfigListForm form,
                                                   BindingResult bindingResult) throws IOException {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<StasticConfigInfoForm> infoFormList = (List<StasticConfigInfoForm>) JsonUtils.fromJson(form.getStasticConfigInfoFormList(), ArrayList.class, StasticConfigInfoForm.class);
        stasticConfigInfoService.addConfigInfo(infoFormList, header);
        log.debug("设置信息添加成功");
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }


    /**
     * 修改党务看板统计设置信息
     *
     * <AUTHOR>
     * @date 2019/4/22 15:36
     */
    @HttpMonitorLogger
    @PostMapping("/edit")
    @ApiOperation("修改党务看板统计设置信息")
    public ResponseEntity<Result<?>> editConfigInfo(@RequestHeader HttpHeaders headers,
                                                    @Valid @RequestBody StasticConfigListForm form,
                                                    BindingResult bindingResult) throws IOException {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<StasticConfigInfoForm> infoFormList = (List<StasticConfigInfoForm>) JsonUtils.fromJson(form.getStasticConfigInfoFormList(), ArrayList.class, StasticConfigInfoForm.class);
        stasticConfigInfoService.editConfigInfo(infoFormList, header);
        log.debug("修改信息添加成功");
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/detail")
    @ApiOperation("查询党务看板统计设置信息")
    public ResponseEntity<Result<List<StasticConfigInfoForm>>> detailConfigInfo(
            @RequestHeader HttpHeaders headers) throws IOException {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<StasticConfigInfoForm> stasticConfigInfoList = stasticConfigInfoService.
                findConfigDetail(header.getRegionId());
        log.debug("查询信息添加成功");
        return new ResponseEntity<>(new Result<>(stasticConfigInfoList, errors), HttpStatus.OK);
    }


}
