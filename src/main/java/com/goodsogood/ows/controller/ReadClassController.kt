package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.readClass.ReadClassService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "读书班")
@RequestMapping("/reading-class")
class ReadClassController(@Autowired val errors: Errors,@Autowired val readClassService: ReadClassService) {
    @HttpMonitorLogger
    @GetMapping("/count")
    @ApiOperation("读书班统计")
    fun count(
        @RequestHeader headers: HttpHeaders,
        @RequestParam(name = "name", required = false) name: String?,
        @RequestParam(name = "year", required = false) year: Int?
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                readClassService.count(headers,name,year), errors
            ), HttpStatus.OK
        )
    }
}