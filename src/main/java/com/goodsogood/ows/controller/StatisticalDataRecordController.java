package com.goodsogood.ows.controller;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.service.sas.StatisticalDataRecordService;
import io.swagger.annotations.Api;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 * @create 2019-04-22 16:06
 **/
@RestController
@RequestMapping("")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "", tags = {""})
@Validated
public class StatisticalDataRecordController {

     private final Errors errors;
     private final StatisticalDataRecordService service;

     @Autowired
     public StatisticalDataRecordController(Errors errors, StatisticalDataRecordService service) {
          this.errors = errors;
          this.service = service;
     }

}
