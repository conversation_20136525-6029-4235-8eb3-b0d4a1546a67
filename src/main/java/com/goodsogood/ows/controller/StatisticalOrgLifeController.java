package com.goodsogood.ows.controller;


import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonParser.Feature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.StorageVariableEnum;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.db.sas.StatisticalOrgLifeEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.sas.*;
import com.goodsogood.ows.service.sas.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * 党组织生活
 *
 * <AUTHOR>
 * @create 2019-04-22 15:57
 **/
@RestController
@RequestMapping("/sas/org-life")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "/sas/org-life", tags = {"党组织生活统计"})
@Validated
public class StatisticalOrgLifeController {

    private final Errors errors;
    private final StatisticalOrgLifeService service;
    private final StatisticalLeaderOrgLifeService statisticalLeaderOrgLifeService;
    private final StasticConfigInfoService stasticConfigInfoService;
    private final  RestTemplate restTemplate;
    private final StringRedisTemplate redisTemplate;
    private static final ObjectMapper objectMapper = new ObjectMapper();

    static
    {
        // 允许整数前导为0,eg:"01"形式
        objectMapper.configure(JsonParser.Feature.ALLOW_NUMERIC_LEADING_ZEROS, true);
        objectMapper.configure(Feature.ALLOW_COMMENTS, true);
        objectMapper.configure(Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        objectMapper.configure(Feature.ALLOW_SINGLE_QUOTES, true);
        objectMapper.configure(Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
    }


    @Autowired
    public StatisticalOrgLifeController(Errors errors, StatisticalOrgLifeService service,
                                        LeaderOrgMeetingFeeService leaderOrgMeetingFeeService,
                                        StatisticalLeaderOrgLifeService statisticalLeaderOrgLifeService,
                                        StasticConfigInfoService stasticConfigInfoService,
                                        RestTemplate restTemplate, StringRedisTemplate redisTemplate) {
        this.errors = errors;
        this.service = service;
        this.statisticalLeaderOrgLifeService = statisticalLeaderOrgLifeService;
        this.stasticConfigInfoService = stasticConfigInfoService;
        this.restTemplate=restTemplate;
        this.redisTemplate = redisTemplate;
    }


    /**
     * 查询党组织生活统计列表
     *
     * <AUTHOR>
     * @date 2019/4/23 14:43
     */
    @HttpMonitorLogger
    @PostMapping("/list")
    @ApiOperation("查询党组织生活统计列表")
    public ResponseEntity<Result<SasOrgLifeReponse>> list(@RequestHeader HttpHeaders headers,
                                                          @Valid @RequestBody SasOrgLifeConditionForm form,
                                                          BindingResult bindingResult) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        form.setRegionId(sysHeader.getRegionId());
        //条件组装
        StatisticalOrgLifeEntity entity = buildOrgLifeParam(form);
        //查询列表
        SasOrgLifeReponse sasOrgLifeReponse = new SasOrgLifeReponse();
        Page<StatisticalOrgLifeEntity> pages = service.pageOrgLifeList(entity,
                form.getPageNo(), form.getPageSize(), sysHeader);
        sasOrgLifeReponse.setResultPage(new Result<>(pages, errors));
        //查询统计信息
        StaMonth orgLifeCountList = service.orgLifeCount(entity);
        sasOrgLifeReponse.setStaMonth(orgLifeCountList);
        return new ResponseEntity<>(new Result<>(sasOrgLifeReponse, errors), HttpStatus.OK);
    }


    /**
     * 组装查询条件
     *
     * <AUTHOR>
     * @date 2019/4/25 17:00
     */

    public StatisticalOrgLifeEntity buildOrgLifeParam(SasOrgLifeConditionForm form) {
        //组装查询条件
        SasOrgLifeConditionForm conditionForm = service.buildParam(form);
        StatisticalOrgLifeEntity entity = new StatisticalOrgLifeEntity();
        entity.setOrgId(conditionForm.getOrgId());
        entity.setActivityTypeId(conditionForm.getActivityTypeId());
        entity.setStatisticalYear(conditionForm.getYear());
        //当前配置的组织类型,是否需要过滤，默认需要过滤，其他访问如果不需要可设置未false
        if (form.getOrgTypeIdFlag()) {
            entity.setShowOrgType(stasticConfigInfoService.
                    getConfig(Constants.ORG_LIFE_TYPE,form.getRegionId()).getShowOrganizationTypes());
        }
        entity.setStartTime(conditionForm.getStartTime());
        entity.setEndTime(conditionForm.getEndTime());
        entity.setDateStr(conditionForm.getDateStr());
        entity.setIsRetire(conditionForm.getIsRetire());
        entity.setActivityTypeIds(form.getActivityTypeIds());
        entity.setRegionId(form.getRegionId());
        //组装sql片段
        SqlParam sqlParam = service.buildSql(conditionForm);
        entity.setTotalParam(sqlParam.getTotalParam());
        entity.setRowParam(sqlParam.getRowParam());
        entity.setParticipateFlag(form.getParticipateFlag());
        return entity;
    }

    /**
     * 导出组织生活
     * <AUTHOR>
     * @date 2019/4/25 17:37
     */
    @HttpMonitorLogger
    @GetMapping(value = "/export/life")
    @ApiOperation(value = "导出组织生活表格")
    public ResponseEntity<Result<?>> exportOrgLife(@RequestHeader HttpHeaders headers,
                                                   @Range(min = 1, max = 999999L, message = "orgId参数出错")
                                                   @RequestParam(value = "org_id") Long orgId,
                                                   @NotNull(message = "组织信息不能为空")
                                                   @RequestParam(value = "org_name") String orgName,
                                                   @NotNull(message = "导出活动ID不能为空")
                                                   @RequestParam(value = "activity_type_ids") String activityTypeIds,
                                                   @Range(min = 1, max = 4, message = "时间类型输入出错")
                                                   @RequestParam(value = "time_type") Integer timeType,
                                                   @Range(min = 2018, max = 2023, message = "年份输出参数错误")
                                                   @RequestParam(value = "year") Integer year,
                                                   @Range(min = 0, max = 6, message = "查询时间参数错误")
                                                   @RequestParam(value = "time", required = false) Integer time,
                                                   @RequestParam(value = "is_retire" , required = false) Integer isRetire,
                                                   @RequestParam(value = "start_month", required = false) Integer startMonth,
                                                   @RequestParam(value = "end_month", required = false) Integer endMonth,
                                                   @RequestParam(value = "step", required = false,defaultValue = "1") Integer step,
                                                   @RequestParam(value = "result_key", required = false) String resultKey,
                                                   HttpServletResponse response) throws Exception {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //条件组装
        SasOrgLifeConditionForm form = new SasOrgLifeConditionForm();
        form.setOrgId(orgId);
        form.setActivityTypeIds(activityTypeIds);
        form.setTimeType(timeType);
        form.setYear(year);
        form.setTime(time);
        form.setIsRetire(isRetire);
        form.setOrgName(orgName);
        form.setStartMonth(startMonth);
        form.setEndMonth(endMonth);
        form.setRegionId(sysHeader.getRegionId());
        StatisticalOrgLifeEntity entity = buildOrgLifeParam(form);
        //第一步是生成redisKey，第二步是拿到redisKey 进行轮询
        if(step==1){
            resultKey=DigestUtils.md5DigestAsHex(objectMapper.writeValueAsString(form).getBytes());
            //异步处理计算规则
            service.synResult(entity,activityTypeIds,sysHeader.getRegionId(),
                    resultKey,objectMapper,redisTemplate);
            return new ResponseEntity<>(new Result<>(resultKey, errors), HttpStatus.OK);
        }
        //如果没有结果一直返回-1 叫前端一直轮询
        // 当等于1 并且 step 不等于2的情况下就是真正的开始下载
       if(!redisTemplate.hasKey(resultKey)){
           return new ResponseEntity<>(new Result<>("-1", errors), HttpStatus.OK);
       }else if(step==2 && redisTemplate.hasKey(resultKey) ) {
           return new ResponseEntity<>(new Result<>("1", errors), HttpStatus.OK);
       }
       try {
            //标题
           String title = statisticalLeaderOrgLifeService.bulidSheetName(2,form);
           ExportOrgLifeService exportOrgLifeService = new ExportOrgLifeService();
           List<Map<String, List<StatisticalOrgLifeEntity>>> allList = new ArrayList<>();
           while (true) {
               String result= redisTemplate.opsForList().rightPop(resultKey);
               if(StringUtils.isEmpty(result)){
                   break;
               }
               Map<String, List<StatisticalOrgLifeEntity>> item= objectMapper.readValue(result,
                       new TypeReference<Map<String,List<StatisticalOrgLifeEntity>>>() {});
               allList.add(item);
           }
           log.info("开始计算导入时间开始-{}",new Date().getTime());
           exportOrgLifeService.export03(response, allList,
                    entity.getDateStr(),title,getRows(entity.getDateStr()),title);
           log.info("开始计算导入时间结束-{}",new Date().getTime());
        } catch (Exception e) {
           e.printStackTrace();
           throw new ApiException(
                    "导出组织生活失败！", new Result<>(errors, 4710, HttpStatus.OK.value()));
        }
        Result<String> resultLast = new Result<>("success", errors);
        return new ResponseEntity<>(resultLast, HttpStatus.OK);
    }


    /**
     * 组装Excel的title
     *
     * <AUTHOR>
     * @date 2019/4/26 14:32
     */

    public static String[] getRows(String monthStr) {
        List<String> monthList = new ArrayList<>();
        monthList.add("序号");
        monthList.add("组织名称");
        List<String> month = Arrays.asList(monthStr.split(","));
        month.forEach(s -> {
            monthList.add(s + "月");
        });
        monthList.add("小计");

        String[] strings = new String[monthList.size()];
        monthList.toArray(strings);
        return monthList.toArray(strings);
    }



    @HttpMonitorLogger
    @ApiOperation(value = "考核系统拉取数据")
    @GetMapping("/evalPullData")
    public ResponseEntity<Result<WrapperEvalPullDataForm>> evalPullData(@RequestHeader HttpHeaders headers,
                                                                             @NotNull(message = "page_no 不能为空") @RequestParam(name = "page_no") Long pageNo,
                                                                             @NotNull(message = "page_size 不能为空") @RequestParam(name = "page_size") Long pageSize,
                                                                             @RequestParam(name = "org_id",required = false) Long orgId,
                                                                             @RequestParam(name = "sta_code",required = false,defaultValue = "0") Long staTimeCode) {
        try {
            HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
            Long regionId=sysHeader.getRegionId();
            Page<EvalPullDataForm> pageDatas = statisticalLeaderOrgLifeService.evalPullData(pageNo, pageSize, orgId, staTimeCode,regionId);
            pageDatas.forEach(item->{
                String storagePartyGroupNum = statisticalLeaderOrgLifeService.getStorageVariable(item.getOrgId(), StorageVariableEnum.PARTY_GROUP_NUM);
                item.setPartyGroupNum(storagePartyGroupNum);
            });
            WrapperEvalPullDataForm wrapper = new WrapperEvalPullDataForm();
            wrapper.setPages(pageDatas.getPages());
            wrapper.setPageSize(pageDatas.getPageSize());
            wrapper.setTotal(pageDatas.getTotal());
            wrapper.setList(pageDatas.getResult());
            return new ResponseEntity<>(new Result<>(wrapper, errors), HttpStatus.OK);
        }catch (Exception ex){
            log.error("考核系统拉取数据出错-msg-{}",ex);
            throw new ApiException("考核系统拉取数据出错", new Result<>(errors, 4713, HttpStatus.OK.value()));
        }

    }

    @HttpMonitorLogger
    @ApiOperation(value = "新增问题单")
    @GetMapping("/test")
    @RepeatedCheck
    public ResponseEntity<Result<Long>> addEvalExamine() {
        WrapperEvalPullDataForm organizeStaInfo = getOrganizeStaInfo(0L, 0L, 1L, 500L);
        handlerData(organizeStaInfo.getList());
        if(organizeStaInfo.getPages()>1){
            for(int i=2;i<=organizeStaInfo.getPages();i++){
                WrapperEvalPullDataForm organizeStaInfo1 = getOrganizeStaInfo(0L, 0L, Long.valueOf(i + ""), 500L);
                handlerData(organizeStaInfo1.getList());
            }
        }
        return  null;
    }

    /**
     * 处理数据
     */
    private void  handlerData(List<EvalPullDataForm> list){
        int size=list.size();
        System.out.println(size);
    }


    /**
     * 调用组织生活模块查询组织生活信息
     * 组织id
     * 不传staCode 默认查询6个月的数据
     * @return
     */
    public WrapperEvalPullDataForm getOrganizeStaInfo(Long orgId, Long staCode,Long pageNo,Long pageSize){
        WrapperEvalPullDataForm wrapper = new WrapperEvalPullDataForm();
        String formatUrl = String.format("http://%s/sas/org-life/evalPullData?org_id=%s&sta_code=%s&page_no=%s&page_size=%s","SAS-LEO",orgId,staCode==null?0:staCode,pageNo,pageSize);
        HttpHeaders headers = new HttpHeaders();
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        // 调用远程方法
        try {
            wrapper = RemoteApiHelper.get(restTemplate, formatUrl, headers, new TypeReference<Result<WrapperEvalPullDataForm>>(){});
            log.debug("拉取组织生活查询组织生活信息[{}]的统计列表: [{}]", orgId, wrapper);
        } catch (Exception e) {
            log.error("拉取组织生活查询组织生活信息失敗, 错误内容:", e);
        }
        return wrapper;
    }
}
