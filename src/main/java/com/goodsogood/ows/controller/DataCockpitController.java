package com.goodsogood.ows.controller;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.DataCockpitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 *  数据驾驶舱
 *
 *
 **/
@RestController
@RequestMapping("/dataCockpit")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "数据驾驶舱", tags = {"数据驾驶舱"})
@Validated
public class DataCockpitController {

    private final DataCockpitService dataCockpitService;

    private final Errors errors;

    @Autowired
    public DataCockpitController(DataCockpitService dataCockpitService, Errors errors) {
        this.dataCockpitService = dataCockpitService;
        this.errors = errors;
    }

    /**
     * 数据驾驶舱 - 根据oid区分组织类型(1-党委 2-党支部 3-其他)
     */
    @GetMapping("/orgType")
    @ApiOperation(value = "根据oid区分组织类型(1-党委 2-党支部 3-其他)")
    public ResponseEntity<Result<?>> orgType(@RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(dataCockpitService.orgTyp(headers), errors), HttpStatus.OK);
    }

    /**
     * 数据驾驶舱 - 组织概况
     */
    @GetMapping("/orgOverview")
    @ApiOperation(value = "组织概况")
    public ResponseEntity<Result<?>> orgOverview(@RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(dataCockpitService.orgOverview(headers), errors), HttpStatus.OK);
    }

    /**
     * 数据驾驶舱 - 组织生活
     */
    @GetMapping("/organizationalLife")
    @ApiOperation(value = "组织生活")
    public ResponseEntity<Result<?>> organizationalLife(@RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(dataCockpitService.organizationalLife(headers), errors), HttpStatus.OK);
    }

    /**
     * 数据驾驶舱 - 党员概况
     */
    @GetMapping("/partyMember")
    @ApiOperation(value = "党员概况")
    public ResponseEntity<Result<?>> partyMember(@RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(dataCockpitService.partyMember(headers), errors), HttpStatus.OK);
    }

    /**
     * 数据驾驶舱 - 民主评议
     */
    @GetMapping("/evaluation")
    @ApiOperation(value = "民主评议")
    public ResponseEntity<Result<?>> evaluation(@RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(dataCockpitService.evaluation(headers), errors), HttpStatus.OK);
    }



    /**
     * 数据驾驶舱 - 党建阵地
     */
    @GetMapping("/partyBuildingBrand")
    @ApiOperation(value = "党建阵地")
    public ResponseEntity<Result<?>> partyBuildingBrand(@RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(dataCockpitService.partyBuildingBrand(headers), errors), HttpStatus.OK);
    }

    /**
     * 数据驾驶舱 - 地图数据
     */
    @GetMapping("/mapData")
    @ApiOperation(value = "地图数据")
    public ResponseEntity<Result<?>> mapData(@RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(dataCockpitService.mapData(headers), errors), HttpStatus.OK);
    }

    /**
     * 数据驾驶舱 - 监督预警
     */
    @GetMapping("/supervise")
    @ApiOperation(value = "监督预警")
    public ResponseEntity<Result<?>> supervise(@RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(dataCockpitService.supervise(headers), errors), HttpStatus.OK);
    }

    /**
     * 数据驾驶舱 - 党费缴纳
     */
    @GetMapping("/partyFeePayment")
    @ApiOperation(value = "党费缴纳")
    public ResponseEntity<Result<?>> partyFeePayment(@RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(dataCockpitService.partyFeePayment(headers), errors), HttpStatus.OK);
    }

}
