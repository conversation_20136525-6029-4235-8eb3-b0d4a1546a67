package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.TbcReportScheduler;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.tbc.*;
import com.goodsogood.ows.service.tbc.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ruoyu
 * @date : 2020/12/28
 * 烟草大屏
 */
@RestController
@RequestMapping("/tbc/report")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class TbcReportController {

    private final TbcPartyUsersService partyUsersService;
    private final TbcPartyOrganService PartyOrganService;
    private final TbcPartyDetailUsersService partyDetailUsersService;
    private final TbcPpmdService ppmdService;
    private final TbcMeetingLifeService meetingLifeService;
    private final TbcReportScheduler tbcReportScheduler;
    private final Errors errors;

    @Autowired
    public TbcReportController(TbcPartyUsersService partyUsersService,
                               TbcPartyOrganService partyOrganService,
                               TbcPartyDetailUsersService partyDetailUsersService, TbcPpmdService ppmdService,
                               TbcMeetingLifeService meetingLifeService,
                               TbcReportScheduler tbcReportScheduler, Errors errors) {
        this.partyUsersService = partyUsersService;
        PartyOrganService = partyOrganService;
        this.partyDetailUsersService = partyDetailUsersService;
        this.ppmdService = ppmdService;
        this.meetingLifeService = meetingLifeService;
        this.tbcReportScheduler = tbcReportScheduler;
        this.errors = errors;
    }

    @HttpMonitorLogger
    @GetMapping("/user-info")
    @ApiOperation("党员信息")
    public ResponseEntity<Result<?>> userInfo(@RequestParam(name = "org_id") Long orgId) {
        PartyCaucusForm form = partyUsersService.partyUser(orgId);
        return new ResponseEntity<>(new Result<>(form, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/user-nation")
    @ApiOperation("党员民族人员信息")
    public ResponseEntity<Result<?>> userNation(@RequestParam(name = "org_id") Long orgId,
                                                @RequestParam(name = "nation_id") Long nationId,
                                                @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
                                                @RequestParam(name = "size", required = false, defaultValue = "6") Integer size) {
        PartyCaucusUserForm form = partyDetailUsersService.partyNationUser(orgId, nationId, page, size);
        return new ResponseEntity<>(new Result<>(form, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/user-age")
    @ApiOperation("党员年龄人员信息")
    public ResponseEntity<Result<?>> userAge(@RequestParam(name = "org_id") Long orgId,
                                             @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
                                             @RequestParam(name = "size", required = false, defaultValue = "6") Integer size,
                                             @RequestParam(name = "age_type") Integer ageType) {
        PartyCaucusUserForm form = partyDetailUsersService.partyAgeUser(orgId, ageType, page, size);
        return new ResponseEntity<>(new Result<>(form, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/user-back")
    @ApiOperation("党员学历人员信息")
    public ResponseEntity<Result<?>> userBack(@RequestParam(name = "org_id") Long orgId,
                                              @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
                                              @RequestParam(name = "size", required = false, defaultValue = "6") Integer size,
                                              @RequestParam(name = "back_type") Integer backType) {
        PartyCaucusUserForm form = partyDetailUsersService.partyBackUser(orgId, backType, page, size);
        return new ResponseEntity<>(new Result<>(form, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/office-dues")
    @ApiOperation("党费情况")
    public ResponseEntity<Result<?>> ppmd(@RequestParam(name = "org_id") Long oid, @RequestHeader HttpHeaders httpHeaders) {
        if (httpHeaders.containsKey(HeaderHelper.OPERATOR_REGION) && httpHeaders.get(HeaderHelper.OPERATOR_REGION) != null) {
            Long rid = Long.valueOf(httpHeaders.get(HeaderHelper.OPERATOR_REGION).get(0));
            return new ResponseEntity<>(new Result<>(ppmdService.getDuesForm(oid, rid), errors), HttpStatus.OK);
        }
        Result<?> result = new Result<>(Global.Errors.ERROR_HEADER.getCode(), "请求头中未发现有效的regionId", HttpStatus.BAD_REQUEST.value(), false);
        throw new ApiException(result.getMessage(), result);
    }

    @HttpMonitorLogger
    @GetMapping("/office-dues/details")
    @ApiOperation("党费情况-今日交费交互")
    public ResponseEntity<Result<?>> ppmdSelfDetails(@RequestParam(name = "org_id") Long oid,
                                                     @RequestParam(name = "flag") boolean flag,
                                                     @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
                                                     @RequestParam(name = "size", required = false, defaultValue = "6") Integer size) {
        Page<OfficeDuesForm.Details> list = ppmdService.getDetails(oid, flag, page, size);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/office-dues/unpaid")
    @ApiOperation("党费情况-支部下未交费党员")
    public ResponseEntity<Result<?>> ppmdUnpaidMember(@RequestParam(name = "org_id") Long oid,
                                                      @RequestParam(name = "branch_id") Long bid,
                                                      @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
                                                      @RequestParam(name = "size", required = false, defaultValue = "6") Integer size) {
        Page<OfficeDuesForm.Details> list = ppmdService.getUnpaidMember(oid, bid, page, size);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/party-org")
    @ApiOperation("党组织情况")
    public ResponseEntity<Result<?>> partyOrg(@RequestParam(name = "org_id") Long orgId) {
        PartyOrganizationForm form = PartyOrganService.partyOrg(orgId);
        return new ResponseEntity<>(new Result<>(form, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/party-org/party-detail")
    @ApiOperation("组织数量详情 (党总支、党支部、党小组)")
    public ResponseEntity<Result<?>> partyOrgDetail(@RequestParam(name = "org_id") Long orgId,
                                                    @RequestParam(name = "type") @Range(min = 1, max = 3, message = "超出选择范围") Integer type,
                                                    @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
                                                    @RequestParam(name = "size", required = false, defaultValue = "6") Integer size) {
        Page<TbcOrganNumDetailForm> list = PartyOrganService.partyOrgDetail(orgId, type, page, size);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/party-org/branch-detail")
    @ApiOperation("党支部设置(详情)")
    public ResponseEntity<Result<?>> partyBranchDetail(@RequestParam(name = "org_id") Long orgId,
                                                       @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
                                                       @RequestParam(name = "size", required = false, defaultValue = "6") Integer size) {
        Page<TbcOrganBranchDetailForm> list = PartyOrganService.partyBranchDetail(orgId, page, size);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/meeting-life")
    @ApiOperation("组织生活")
    public ResponseEntity<Result<?>> meetingLife(@RequestParam(name = "org_id") Long orgId,
                                                 @RequestHeader HttpHeaders headers) {
        long regionId;
        try {
            regionId = Long.parseLong(Objects.requireNonNull(headers.get("_region_id")).get(0));
        } catch (NullPointerException e) {
            throw new ApiException("_region_id找不到", new Result(errors, 9404, HttpStatus.OK.value(), "_region_id"));
        }
        OrganizationLifeForm form = meetingLifeService.meetingLife(orgId, regionId);
        return new ResponseEntity<>(new Result<>(form, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/meeting-life/user-comment")
    @ApiOperation("民主评议党员情况详情")
    public ResponseEntity<Result<?>> meetingLifeUserComment(@RequestParam(name = "org_id") Long orgId,
                                                            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
                                                            @RequestParam(name = "size", required = false, defaultValue = "6") Integer size) {
        Page<OrganLifeUserCommentForm> list = meetingLifeService.meetingLifeUserComment(orgId, page, size);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/meeting-life/org-comment")
    @ApiOperation("述职评议组织情况详情")
    public ResponseEntity<Result<?>> meetingLifeOrgComment(@RequestParam(name = "org_id") Long orgId,
                                                           @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
                                                           @RequestParam(name = "size", required = false, defaultValue = "6") Integer size) {
        Page<OrganLifeUserCommentForm> list = meetingLifeService.meetingLifeOrgComment(orgId, page, size);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    @GetMapping("/test01")
    public void test01() {
        tbcReportScheduler.runOfPartyMeeting();
        tbcReportScheduler.runOfPartyOrg();
        tbcReportScheduler.runOfPartyUser();
    }

    @HttpMonitorLogger
    @GetMapping("/develop")
    @ApiOperation("党业发展趋势图")
    public ResponseEntity<Result<?>> develop(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.partyUsersService.develop(sysHeader), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/party-leader")
    @ApiOperation("党务干部")
    public ResponseEntity<Result<?>> partyLeader(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.partyUsersService.partyLeader(sysHeader), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/study")
    @ApiOperation("学习教育")
    public ResponseEntity<Result<?>> study(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.partyUsersService.study(sysHeader), errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/penalize/info")
    @ApiOperation("获取争先创优列表")
    public ResponseEntity<Result<?>> getCommendPenalizeList() {
        List<TbcCommendPenalizeVo> penalizeList = meetingLifeService.getCommendPenalizeList();
        return new ResponseEntity<>(new Result<>(penalizeList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/task")
    @ApiOperation("系统任务")
    public ResponseEntity<Result<?>> task(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.partyUsersService.task(sysHeader), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("examine")
    @ApiOperation("考核扣分")
    public ResponseEntity<Result<?>> examine(@RequestHeader HttpHeaders headers,
                                             @RequestParam(name = "year") Integer year) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.partyUsersService.examine(sysHeader,year), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("rank")
    @ApiOperation("考核排名")
    public ResponseEntity<Result<?>> rank(@RequestHeader HttpHeaders headers,
                                             @RequestParam(name = "year") Integer year) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.partyUsersService.rank(sysHeader,year), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("active")
    @ApiOperation("系统活跃")
    public ResponseEntity<Result<?>> active(@RequestHeader HttpHeaders headers,
                                          @RequestParam(name = "year") Integer year,
                                          @RequestParam(name = "org_id") Long orgId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.partyUsersService.active(orgId,sysHeader.getRegionId(),year), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("clickMenuTop")
    @ApiOperation("每日点击最多的功能")
    public ResponseEntity<Result<?>> clickMenuTop(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.partyUsersService.clickMenuTop(sysHeader), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("visit")
    @ApiOperation("访问率")
    public ResponseEntity<Result<?>> visit(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.partyUsersService.visit(sysHeader), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("supervise")
    @ApiOperation("监督预警")
    public ResponseEntity<Result<?>> supervise(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.partyUsersService.supervise(sysHeader), errors), HttpStatus.OK);
    }
}
