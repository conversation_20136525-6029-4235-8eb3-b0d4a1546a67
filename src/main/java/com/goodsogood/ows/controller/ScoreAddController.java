package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.redisUtil.RedisLockUtil;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.tbcFusion.TbcPartyAndBusinessScoreVo;
import com.goodsogood.ows.service.learn.LearnService;
import com.goodsogood.ows.service.score.ScoreAddService;
import com.goodsogood.ows.service.tbcFusion.TbcScoreService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;


/**
 * 上报积分中心
 * <AUTHOR>
 */
@Controller
@Log4j2
@RequestMapping("/score-center")
public class ScoreAddController {

    @Value("${scheduler.score-add.run}")
    private boolean runStatus;

    private final Errors errors;
    private final ScoreAddService scoreAddService;
    private final StringRedisTemplate stringRedisTemplate;
    private final TbcScoreService tbcScoreService;
    private final LearnService learnService;

    private static final String lock = "ScoreAddScheduler-lock";

    @Autowired
    public ScoreAddController(Errors errors, ScoreAddService scoreAddService, StringRedisTemplate stringRedisTemplate, TbcScoreService tbcScoreService, LearnService learnService) {
        this.errors = errors;
        this.scoreAddService = scoreAddService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.tbcScoreService = tbcScoreService;
        this.learnService = learnService;
    }

    @HttpMonitorLogger
    @GetMapping("/find/unit/score/rank")
    @ApiOperation("查询单位党建积分和业务积分以及排名")
    public ResponseEntity<Result<?>> findUnitScoreAndRank(@RequestHeader HttpHeaders headers, @RequestParam(value = "unit_id") Long unitId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //从头消息里获取regionId
        Long regionId = sysHeader.getRegionId();
        TbcPartyAndBusinessScoreVo re = tbcScoreService.findUnitScoreAndRank(regionId,unitId);
        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/test/add/score")
    @ApiOperation("手动调用上报到积分中心任务")
    public ResponseEntity<Result<?>> getRate(@RequestParam(value = "region_id") Long regionId) {
        String re = "执行成功！";
        if (runStatus) {
            String uuid = UUID.randomUUID().toString();
            try {
                boolean b = RedisLockUtil.tryGetDistributedLock(stringRedisTemplate, lock, uuid, 60 * 1000);
                if (b) {
                    log.debug("<手动调用上报到积分中心任务> 开始！");
                    //手动执行任务
                    scoreAddService.addScoreByScheduler(regionId);
                }else {
                    log.debug("<手动调用上报到积分中心任务> 未获取到锁,结束执行！");
                    re = "未获取到锁！";
                }
            } catch (Exception e) {
                re = "执行报错！";
                log.error("<手动调用上报到积分中心任务> 报错！", e);
            } finally {
                RedisLockUtil.releaseDistributedLock(stringRedisTemplate, lock, uuid);
            }
        }else{
            re = "<手动调用上报到积分中心任务> 任务开关未打开！取消执行！";
        }
        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/test/find/user/pbScore")
    @ApiOperation("测试查询人员党建积分和业务积分")
    public ResponseEntity<Result<?>> testFind(@RequestParam(value = "region_id") Long regionId, @RequestParam(value = "user_list") List<Long> userIdList,
                                              @RequestParam(value = "start_time")  String startTime, @RequestParam(value = "end_time")  String endTime) {
        Map<Long, TbcPartyAndBusinessScoreVo> re = tbcScoreService.findUserPartyAndBusiness(regionId,userIdList,startTime,endTime);
        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/test/find/org/pbScore")
    @ApiOperation("测试查询单位党建积分和业务积分")
    public ResponseEntity<Result<?>> testFindOrg(@RequestParam(value = "region_id") Long regionId, @RequestParam(value = "unitId_List") List<Long> unitIdList,
                                              @RequestParam(value = "start_time")  String startTime, @RequestParam(value = "end_time")  String endTime) {
        Map<Long, TbcPartyAndBusinessScoreVo> re = tbcScoreService.findUnitPartyAndBusiness(regionId,unitIdList,startTime,endTime);
        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }

    @GetMapping("/test/exam-score")
    @ApiOperation("测试添加学习积分")
    @HttpMonitorLogger
    public ResponseEntity<Result<String>> test1(){
        log.debug("开始");
        learnService.run();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 每日一练-针对某日加积分：由于sas服务可能挂掉，存在没有加分的情况。此接口用于手动对每日一练加积分。
     * @Param date: yyyy-MM-dd
     */
    @GetMapping("/test/exam-score-by-date")
    @ApiOperation("测试添加学习积分")
    @HttpMonitorLogger
    public ResponseEntity<Result<String>> trainingByDate(@Param("date") String date){
      learnService.trainingByDate(date);
        return new ResponseEntity<>(new Result<>("结束", errors), HttpStatus.OK);
    }


    @GetMapping("/test/exam-score-by-month")
    @ApiOperation("测试添加学习积分")
    @HttpMonitorLogger
    public ResponseEntity<Result<String>> trainingByMonth(@Param("date") String date){
        learnService.examMonthByMonth(date);
        return new ResponseEntity<>(new Result<>("结束", errors), HttpStatus.OK);
    }

    @GetMapping("/test/exam-score-by-quart")
    @ApiOperation("测试添加学习积分")
    @HttpMonitorLogger
    public ResponseEntity<Result<String>> trainingByQuart(@Param("date") String date){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDate = LocalDate.parse(date,formatter);
        learnService.examQuartByQuart(localDate);
        return new ResponseEntity<>(new Result<>("结束", errors), HttpStatus.OK);
    }

    @GetMapping("/test/finish-by-date")
    @ApiOperation("查询学习教育完成状态")
    @HttpMonitorLogger
    public ResponseEntity<Result<String>> finishStatus(@RequestParam(value="date",required = false) String dateStr){
        if(dateStr == null){
            learnService.statusFinish(LocalDate.now());
        }else{
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate date = LocalDate.parse(dateStr,formatter);
            learnService.statusFinish(date);
        }
        return new ResponseEntity<>(new Result<>("结束", errors), HttpStatus.OK);

    }


}
