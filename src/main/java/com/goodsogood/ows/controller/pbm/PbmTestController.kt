package com.goodsogood.ows.controller.pbm

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.pbm.PbmWorkDataService
import com.goodsogood.ows.service.pbm.PbmWorkKitService
import com.goodsogood.ows.service.pbm.fusion.FusionDataService
import com.goodsogood.ows.service.user.UserMongoService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 *
 * <AUTHOR>
 * @createTime 2022年06月22日 16:41:00
 */
@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "党业融合测试接口", tags = ["党业融合测试接口"])
@RequestMapping("/pbm/test")
class PbmTestController(
    val errors: Errors,
    val userMongoService: UserMongoService,
    val pbmWorkDataService: PbmWorkDataService,
    val pbmWorkKitService: PbmWorkKitService,
    val fusionDataService: FusionDataService
) {

    @HttpMonitorLogger
    @GetMapping("/getUserList")
    @ApiOperation("获取用户列表")
    fun getUserList(
        @RequestParam("org_id", required = false) orgId: Long? = null,
        @RequestParam("is_party", required = false) isParty: Int? = 1,
        @RequestParam("sequence", required = false) sequence: Int? = 0,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.userMongoService.getUserIdList(header.regionId, orgId, isParty, sequence), errors
            ), HttpStatus.OK
        )
    }


    @HttpMonitorLogger
    @GetMapping("/getSequenceUserList")
    @ApiOperation("获取序列用户列表")
    fun getSequenceUserList(
        @RequestParam("org_id", required = false) orgId: Long? = null,
        @RequestParam("is_party", required = false) isParty: Int? = 1,
        @RequestParam("sequence", required = false) sequence: Int? = 0,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.userMongoService.getSequenceUserIdList(header.regionId, orgId, isParty, sequence), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/calculatePbmUserWork")
    @ApiOperation("计算用户工作数据")
    fun calculatePbmUserWork(
        @RequestParam("year") year: Int,
        @RequestParam("month") month: Int,
        @RequestParam("unit_id", required = false) unitId: Long? = null,
        @RequestParam("week", required = false) week: Int? = null,
        @RequestParam("work_item_id", required = false) workItemId: Long? = null,
        @RequestParam("type") type: Int,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.pbmWorkDataService.calculatePbmUserWork(
                    header.regionId,
                    unitId,
                    year,
                    month,
                    week,
                    workItemId,
                    type
                ), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/calculatePbmOrgWork")
    @ApiOperation("计算组织工作数据")
    fun calculatePbmOrgWork(
        @RequestParam("unit_id", required = false) unitId: Long? = null,
        @RequestParam("year") year: Int,
        @RequestParam("month") month: Int,
        @RequestParam("week", required = false) week: Int? = null,
        @RequestParam("work_item_id", required = false) workItemId: Long? = null,
        @RequestParam("type") type: Int,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.pbmWorkDataService.calculatePbmOrgWork(
                    header.regionId,
                    unitId,
                    year,
                    month,
                    week,
                    workItemId,
                    type
                ), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/calculatePbmOrgUserKit")
    @ApiOperation("计算组织用户工作数据")
    fun calculatePbmOrgUserKit(
        @RequestParam("unit_id", required = false) unitId: Long? = null,
        @RequestParam("year") year: Int,
        @RequestParam("month") month: Int,
        @RequestParam("is_calculate_user", required = false) isCalculateUser: Boolean? = true,
        @RequestParam("type", required = false) type: Int? = 0,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.pbmWorkKitService.calculatePbmKit(
                    header.regionId,
                    unitId,
                    year,
                    month,
                    isCalculateUser ?: true,
                    type ?: 0
                ), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/fusion/collect")
    @ApiOperation("收集党业融合基础数据-按月")
    fun collectFusion(
        @RequestParam("unit_id", required = false) unitId: Long? = null,
        @RequestParam("item_id", required = false) itemId: Long? = null,
        @RequestParam("year") year: Int,
        @RequestParam("month", required = false) month: Int? = null,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        fusionDataService.collect(unitId, itemId, year, month, header.regionId)
        return ResponseEntity<Result<Any>>(Result<Any>("success", errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/fusion/collect-all")
    @ApiOperation("收集党业融合基础数据")
    fun collectFusion(
        @RequestParam("unit_id", required = false) unitId: Long? = null,
        @RequestParam("item_id", required = false) itemId: Long? = null,
        @RequestParam("start_month") startMonth: String,
        @RequestParam("end_month") endMonth: String,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        fusionDataService.collectAll(unitId, itemId, startMonth, endMonth, header.regionId)
        return ResponseEntity<Result<Any>>(Result<Any>("success", errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/fusion/calculate")
    @ApiOperation("计算党业融合基础数据-指定月份")
    fun calculateFusion(
        @RequestParam("unit_id", required = false) unitId: Long? = null,
        @RequestParam("item_id", required = false) itemId: Long? = null,
        @RequestParam("year") year: Int,
        @RequestParam("month", required = false) month: Int? = null,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        fusionDataService.calculate(unitId, itemId, year, month, header.regionId)
        return ResponseEntity<Result<Any>>(Result<Any>("success", errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/fusion/calculate-all")
    @ApiOperation("计算党业融合基础数据")
    fun calculateFusion(
        @RequestParam("unit_id", required = false) unitId: Long? = null,
        @RequestParam("item_id", required = false) itemId: Long? = null,
        @RequestParam("start_month") startMonth: String,
        @RequestParam("end_month") endMonth: String,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        fusionDataService.calculateAll(unitId, itemId, startMonth, endMonth, header.regionId)
        return ResponseEntity<Result<Any>>(Result<Any>("success", errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/fusion/summary")
    @ApiOperation("计算党业融合度")
    fun summaryFusion(
        @RequestParam("unit_id", required = false) unitId: Long? = null,
        @RequestParam("year") year: Int,
        @RequestParam("month") month: Int,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        fusionDataService.summary(unitId, year, month, header.regionId)
        return ResponseEntity<Result<Any>>(Result<Any>("success", errors), HttpStatus.OK)
    }

    @GetMapping("/get-user-corp-info")
    @ApiOperation("查询人员单位信息")
    @HttpMonitorLogger
    fun getUserCorpInfo(@RequestParam("user_id") userId: Long,
                        @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val result = this.fusionDataService.getUserCorpInfo(userId, header)
        return ResponseEntity(Result(result, errors), HttpStatus.OK)
    }
}