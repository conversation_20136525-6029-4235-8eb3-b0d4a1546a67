package com.goodsogood.ows.controller.pbm

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.mongodb.pbm.*
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.user.PbmService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import javax.servlet.http.HttpServletResponse
import javax.validation.constraints.NotNull

/**
 *
 * <AUTHOR>
 * @Date 2022-06-23 16:17:47
 * @Description PbmBusinessController
 *
 */
@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "党业融合业务工作接口", tags = ["党业融合业务工作接口"])
@RequestMapping("/pbm/detail")
class PbmBusinessController(
    val errors: Errors,
    val pdmService: PbmService
) {
    private val log: Logger = LoggerFactory.getLogger(PbmBusinessController::class.java)

    /**
     * 根据月份和用户ID查询用户业务工作列表
     * @param queryId 查询ID（userId或者orgId）
     * @param queryType 1-用户 2-组织
     * @param type 1-党务 2-业务
     * @param calDate 查询月份 yyyy-MM
     */
    @HttpMonitorLogger
    @GetMapping("/find-details")
    @ApiOperation("根据月份和用户ID查询用户业务工作列表")
    fun findDetails(
        @RequestParam("queryId") queryId: Long?,
        @RequestParam("calDate") calDate: String?,
        @RequestParam("queryType") queryType: Int?,
        @RequestParam("type") type: Int?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.pdmService.findDetails(queryId, calDate, queryType, type, header), errors
            ), HttpStatus.OK
        )
    }

    /**
     * 党员-散点图
     * @param type 0-全部 1-卷烟营销
     * @param calDate 查询月份 yyyy-MM
     */
    @HttpMonitorLogger
    @GetMapping("/find-scatter-plot")
    @ApiOperation("散点图")
    fun findScatterPlot(
        @RequestParam("type") type: Int?,
        @RequestParam("calDate") calDate: String?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val data = try {
            this.pdmService.findScatterPlot(type, calDate, header) ?: ScatterPlotForm()
        } catch (e: Exception) {
            log.error(e.localizedMessage, e)
            ScatterPlotForm()
        }
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                data, errors
            ), HttpStatus.OK
        )
    }

    /**
     * 党员-拟合度散点图
     * @param type 0-本单位 1-卷烟营销
     * @param calDate 查询月份 yyyy-MM
     */
    @HttpMonitorLogger
    @GetMapping("/find-fit-scatter-plot")
    @ApiOperation("党员-拟合度散点图")
    fun findFitScatterPlot(
        @RequestParam("type") type: Int?,
        @RequestParam("calDate") calDate: String?,
        @RequestParam("unitId") unitId: Long?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.pdmService.findFitScatterPlot(type, calDate, unitId, header), errors
            ), HttpStatus.OK
        )
    }

    /**
     * 单位-拟合度散点图
     * @param calDate 查询月份 yyyy-MM
     */
    @HttpMonitorLogger
    @GetMapping("/find-unit-scatter-plot")
    @ApiOperation("单位-拟合度散点图")
    fun findUnitScatterPlot(
        @RequestParam("calDate") calDate: String?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.pdmService.findUnitScatterPlot(calDate, header), errors
            ), HttpStatus.OK
        )
    }

    /**
     * 单位-拟合度列表
     * @param calDate 查询月份 yyyy-MM
     */
    @HttpMonitorLogger
    @GetMapping("/find-unit-scatter-list")
    @ApiOperation("单位-拟合度列表")
    fun findUnitScatterList(
        @RequestParam("calDate") calDate: String?,
        @RequestParam("unitId") unitId: Long?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.pdmService.findUnitScatterList(calDate, unitId, header), errors
            ), HttpStatus.OK
        )
    }

    /**
     * 单位-拟合度柱状图
     * @param calDate 查询月份 yyyy-MM
     */
    @HttpMonitorLogger
    @GetMapping("/find-unit-columnar-chart")
    @ApiOperation("单位-拟合度柱状图")
    fun findUnitColumnarChart(
        @RequestParam("calDate") calDate: String?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.pdmService.findUnitColumnarChart(calDate, header), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/find-unit-mix-scatter")
    @ApiOperation("各单位党务-业务工作情况对比")
    fun findUnitMixScatter(
        @RequestParam @NotNull unitId: Long,
        @RequestParam @NotNull calDate: String,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<List<FindUnitMixScatterVO>>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity(Result(this.pdmService.findUnitMixScatter(calDate, header), errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/find-unit-mix-line-bar")
    @ApiOperation("各单位党务-业务工作情况对比")
    fun findUnitMixLineBar(
        @RequestParam @NotNull unitId: Long,
        @RequestParam(required = false) calDate: String?,
        @RequestParam(required = false) num: Int?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<FindUnitMixLineBarVO>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val date = calDate ?: DateTime.now().toString("yyyy-MM")
        val n = num ?: 6
        val list = mutableListOf<String>(date)
        val dateDecoder = DateTimeFormat.forPattern("yyyy-MM")
        // 计算出月份
        var temp = date
        repeat(n - 1) {
            temp = DateTime.parse(temp, dateDecoder).minusMonths(1).toString("yyyy-MM")
            list.add(temp) // 当前月的上一月
        }
        return ResponseEntity(
            Result(this.pdmService.findUnitMixLineBar(unitId, list.reversed(), header), errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/find-scatter-list")
    @ApiOperation("散点图了解详情")
    fun findScatterList(
        @RequestParam @NotNull type: Int,
        @RequestParam @NotNull calDate: String,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<List<ScatterListForm>>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity(
            Result(this.pdmService.findScatterList(type, calDate, header), errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/get-kit-trend")
    @ApiOperation("获取拟合度趋势图")
    fun getUserKitTrend(
        @RequestParam("user_id") userId: Long,
        @RequestParam("year") year: Int,
        @RequestParam("month") month: Int,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<GetUserKitResponse>> {
        return ResponseEntity(
            Result(this.pdmService.getUserKitTrend(userId, year, month, headers), errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/detail")
    @ApiOperation("获取指标详情")
    fun queryDetail(
        @RequestParam("item_id") itemId: Long,
        @RequestParam("unit_id") unitId: Long,
        @RequestParam("year") year: Int,
        @RequestParam(value = "month", required = false) month: Int?,
        @RequestParam(value = "is_party", required = false) isParty: Int?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(
            Result(this.pdmService.queryDetail(unitId, itemId, year, month, isParty, headers), errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/totalDetail")
    @ApiOperation("融合指标大表格")
    fun queryTotalDetail(
        @RequestParam("unit_id") unitId: Long,
        @RequestParam("year_month") yearMonth: String,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(
            Result(this.pdmService.queryTotalDetail(unitId, yearMonth, headers), errors),
            HttpStatus.OK
        )
    }


    @HttpMonitorLogger
    @GetMapping("/target-fusion")
    @ApiOperation("目标融合-市局")
    fun targetFusion(
        @RequestParam(value = "year") year: Int?,
        @RequestParam(value = "month") month: Int?,
        @RequestHeader headers: HttpHeaders,
    ): ResponseEntity<Result<Any>> {
        val newMonth = if (month == null) LocalDate.now().monthValue else if (month == 12) 1 else month + 1
        val newYear = if (year == null) LocalDate.now().year else if (month == 12) year + 1 else year
        return ResponseEntity(
            Result(
                this.pdmService.targetFusion(
                    newYear,
                    newMonth, headers
                ), errors
            ),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/target-detail-fusion")
    @ApiOperation("目标融合各单位详情-市局")
    fun targetFusion(
        @RequestParam(value = "year") year: Int,
        @RequestParam(value = "month") month: Int?,
        @RequestParam(value = "item_id") itemId: Long,
        @RequestParam(value = "unit_name") unitName: String?,

        @RequestHeader headers: HttpHeaders,
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(
            Result(this.pdmService.targetFusionDetail(year, month, itemId, unitName, headers), errors),
            HttpStatus.OK
        )
    }


    @HttpMonitorLogger
    @GetMapping("/target-detail-fusion-export")
    @ApiOperation("目标融合各单位详情-市局-导出")
    fun targetFusionExport(
        @RequestParam(value = "year") year: Int,
        @RequestParam(value = "month") month: Int?,
        @RequestParam(value = "item_id") itemId: Long,
        @RequestParam(value = "unit_name") unitName: String?,
        @RequestHeader headers: HttpHeaders,
        response: HttpServletResponse
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(
            Result(this.pdmService.targetFusionExport(year, month, itemId, unitName, headers, response), errors),
            HttpStatus.OK
        )
    }


    @HttpMonitorLogger
    @GetMapping("/all-fusion")
    @ApiOperation("市局12个月融合度")
    fun allFusion(
        @RequestParam(value = "year") year: Int?,
        @RequestParam(value = "month") month: Int?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(
            Result(
                this.pdmService.allFusion(
                    year ?: if (LocalDate.now().monthValue == 1) LocalDate.now().year - 1 else LocalDate.now().year,
                    month ?: if (LocalDate.now().monthValue == 1) 12 else LocalDate.now().monthValue - 1, headers
                ), errors
            ),
            HttpStatus.OK
        )
    }
}