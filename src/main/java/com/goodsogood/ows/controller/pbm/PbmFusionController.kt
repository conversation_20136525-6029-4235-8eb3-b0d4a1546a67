package com.goodsogood.ows.controller.pbm

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.db.sas.PbmFusionItemVO
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.model.vo.fusion.FusionIndexVO
import com.goodsogood.ows.model.vo.fusion.FusionMapDetail
import com.goodsogood.ows.service.pbm.fusion.FusionDataService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 党业融合融合度
 * <AUTHOR>
 * @createTime 2023年03月17日 10:47:00
 */
@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "党业融合融合度", tags = ["党业融合融合度"])
@RequestMapping("/fusion")
class PbmFusionController @Autowired constructor(
    private val errors: Errors,
    private val fusionDataService: FusionDataService
) {

    @HttpMonitorLogger
    @GetMapping("/unit/index")
    @ApiOperation(value = "单位融合概况")
    fun fusionIndex(
        @RequestParam("unit_id") unitId: Long,
        @RequestParam(value = "year", required = false) year: Int? = null,
        @RequestParam(value = "month", required = false) month: Int? = null
    ): ResponseEntity<Result<FusionIndexVO>> {
        return ResponseEntity<Result<FusionIndexVO>>(
            Result<FusionIndexVO>(
                this.fusionDataService.findFusion(unitId, year, month), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/map")
    @ApiOperation(value = "融合概况地图")
    fun map(
        @RequestParam(value = "year", required = false) year: Int? = null,
        @RequestParam(value = "month", required = false) month: Int? = null,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<List<FusionMapDetail>>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<List<FusionMapDetail>>>(
            Result<List<FusionMapDetail>>(
                this.fusionDataService.getFusionMapDetail(year, month, header.regionId), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/item")
    @ApiOperation(value = "融合规则")
    fun item(): ResponseEntity<Result<List<PbmFusionItemVO>>> {
        return ResponseEntity<Result<List<PbmFusionItemVO>>>(
            Result<List<PbmFusionItemVO>>(
                this.fusionDataService.item(), errors
            ), HttpStatus.OK
        )
    }
}