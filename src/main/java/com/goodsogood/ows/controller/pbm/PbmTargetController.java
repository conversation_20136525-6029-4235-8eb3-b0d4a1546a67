package com.goodsogood.ows.controller.pbm;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.sas.PbmTargetEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.pbm.*;
import com.goodsogood.ows.service.ExcelServices;
import com.goodsogood.ows.service.pbm.fusion.PbmOrgTargetService;
import com.goodsogood.ows.service.pbm.fusion.PbmTargetService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/pbm/target")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class PbmTargetController {

    private final Errors errors;

    private final PbmTargetService pbmTargetService;

    private final PbmOrgTargetService pbmOrgTargetService;

    private final ExcelServices excelServices;

    public PbmTargetController(Errors errors, PbmTargetService pbmTargetService, PbmOrgTargetService pbmOrgTargetService, ExcelServices excelServices) {
        this.errors = errors;
        this.pbmTargetService = pbmTargetService;
        this.pbmOrgTargetService = pbmOrgTargetService;
        this.excelServices = excelServices;
    }

    @HttpMonitorLogger
    @GetMapping(value = "/list")
    @ApiOperation(value = "人员列表")
    public ResponseEntity<Result<?>> pbmTargetList(@RequestHeader HttpHeaders headers,
                                                    @RequestParam(name = "page_no",defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "page_size",defaultValue = "10") Integer pageSize,
                                                    @RequestParam(name = "org_id") Long orgId,
                                                    @RequestParam(name = "time") String time,
                                                   @RequestParam(name = "name",required = false) String name,
                                                    @RequestParam(name = "is_party_member",required = false) Integer isPartyMember) {
        Page<PbmTargetEntity> entity = pbmTargetService.pbmTargetList(pageNo, pageSize, orgId, time, name,isPartyMember);
        return new ResponseEntity<>(new Result<>(entity, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping(value = "/import/user")
    @ApiOperation(value = "导入用户")
    public ResponseEntity<Result<?>> pbmTargetImport(@RequestHeader HttpHeaders headers,
                                                      @RequestParam(name = "org_id") Long orgId,
                                                      @RequestParam(name = "up_file") MultipartFile upFile) throws Exception {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long regionId = header.getRegionId();
        String token = UUID.randomUUID().toString();
        pbmTargetService.pbmTargetImport(regionId,token,headers,orgId, upFile,header);
        Thread.sleep(1000);
        return new ResponseEntity<>(new Result<>(token, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping(value = "/export")
    @ApiOperation(value = "导出")
    public ResponseEntity<Result<?>> pbmTargetExport(@RequestHeader HttpHeaders headers,
                                                     @RequestParam(name = "org_id",required = false) Long orgId,
                                                     @RequestParam(name = "time") String time,
                                                     @RequestParam(name = "name",required = false) String name,
                                                     @RequestParam(name = "org_name",required = false) String orgName,
                                                     @RequestParam(name = "is_party_member",required = false) Integer isPartyMember,
                                                     @RequestParam(name = "type") Integer type) {
        String token = UUID.randomUUID().toString();
        if (type == 1) {
            List<PbmTargetExportVO> list = pbmTargetService.getList(orgId,time,name,isPartyMember);
            String sheetName = "员工业务绩效_" + time.substring(0,3) + "年" + time.substring(4,5) + "月";
            excelServices.exportFixExcel(headers,token,"",sheetName,list, PbmTargetExportVO.class);
        }else if (type == 2) {
            List<PbmOrgTargetUnitVo> list = pbmOrgTargetService.getUnitList(time,orgName);
            String sheetName = "单位业务绩效_" + time.substring(0,3) + "年";
            excelServices.exportFixExcel(headers,token,"",sheetName,list, PbmOrgTargetUnitVo.class);
        }else if (type == 3) {
            List<PbmOrgTargetPersonVo> list = pbmOrgTargetService.getPersonList(time,orgName);
            String sheetName = "干群满意度_" + time.substring(0,3) + "年";
            excelServices.exportFixExcel(headers,token,"",sheetName,list, PbmOrgTargetPersonVo.class);
        }else if (type == 4) {
            List<PbmOrgTargetBuildVo> list = pbmOrgTargetService.getBuildList(time,orgName);
            String sheetName = "党建年度考核_" + time.substring(0,3) + "年";
            excelServices.exportFixExcel(headers,token,"",sheetName,list, PbmOrgTargetBuildVo.class);
        }
        return new ResponseEntity<>(new Result<>(token, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping(value = "/query/import-result")
    @ApiOperation(value = "查询导入结果")
    public ResponseEntity<Result<?>> queryImportResult(@RequestHeader HttpHeaders headers,
                                                       HttpServletRequest request,
                                                       @RequestParam(name = "token") String token) throws Exception {
        return pbmTargetService.queryImportResult(token);
    }
}
