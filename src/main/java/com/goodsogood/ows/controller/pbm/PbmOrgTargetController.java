package com.goodsogood.ows.controller.pbm;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.sas.PbmOrgTargetEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.pbm.fusion.PbmOrgTargetService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/pbm/org/target")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class PbmOrgTargetController {

    private final Errors errors;

    private final PbmOrgTargetService pbmOrgTargetService;

    public PbmOrgTargetController(Errors errors, PbmOrgTargetService pbmOrgTargetService) {
        this.errors = errors;
        this.pbmOrgTargetService = pbmOrgTargetService;
    }

    @HttpMonitorLogger
    @GetMapping(value = "/orgList")
    @ApiOperation(value = "单位列表")
    public ResponseEntity<Result<?>> pbmOrgTargetList(@RequestHeader HttpHeaders headers,
                                                      @RequestParam(name = "page_no",defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "page_size",defaultValue = "10") Integer pageSize,
                                                      @RequestParam(name = "org_name",required = false) String orgName,
                                                      @RequestParam(name = "time") String time) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Page<PbmOrgTargetEntity> entity = pbmOrgTargetService.pbmOrgTargetList(pageNo, pageSize, time, orgName);
        return new ResponseEntity<>(new Result<>(entity, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping(value = "/update/orgList")
    @ApiOperation(value = "单位列表")
    public ResponseEntity<Result<?>> updatePbmOrgTarget(@RequestHeader HttpHeaders headers,
                                                        @RequestParam(name = "type") Integer type,
                                                        @RequestBody PbmOrgTargetEntity pbm) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        pbmOrgTargetService.updatePbmOrgTarget(pbm,header.getUserId(),type);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }
}
