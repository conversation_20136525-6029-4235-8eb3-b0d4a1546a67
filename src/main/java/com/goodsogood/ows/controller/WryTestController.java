package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.StaMeetingScheduler;
import com.goodsogood.ows.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/sas/wry")
@Log4j2
public class WryTestController  {

    private final StaMeetingScheduler staMeetingScheduler;
    private final Errors errors;

    @Autowired
    public WryTestController(StaMeetingScheduler staMeetingScheduler
            , Errors errors) {
        this.staMeetingScheduler = staMeetingScheduler;
        this.errors = errors;
    }

    @HttpMonitorLogger
    @GetMapping("/test01")
    @ApiOperation("测试调用用户中心组织接口")
    public ResponseEntity<Result<?>> testMeeting() {
        staMeetingScheduler.resetPeriodCreateTime(1L);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }
}
