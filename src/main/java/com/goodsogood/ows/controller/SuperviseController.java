package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.aidangqun.log4j2cm.aop.HttpLogAspect;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SuperviseConfig;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.supervise.*;
import com.goodsogood.ows.service.supervise.SuperviseExtendService;
import com.goodsogood.ows.service.supervise.SuperviseRecordService;
import com.goodsogood.ows.service.supervise.SuperviseService;
import com.goodsogood.ows.service.user.OrgService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @create 2021-10-13 17:24
 **/
@RestController
@RequestMapping("/supervise")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class SuperviseController {

    private final Errors errors;
    private final SuperviseService superviseService;
    private final SuperviseExtendService superviseExtendService;
    private final OrgService orgService;
    private final OrgTypeConfig orgTypeConfig;
    private final SuperviseConfig superviseConfig;
    private final SuperviseRecordService superviseRecordService;

    @Autowired
    public SuperviseController(Errors errors, SuperviseService superviseService,
                               SuperviseExtendService superviseExtendService,
                               OrgService orgService, OrgTypeConfig orgTypeConfig,
                               SuperviseConfig superviseConfig,
                               SuperviseRecordService superviseRecordService) {
        this.errors = errors;
        this.superviseService = superviseService;
        this.superviseExtendService = superviseExtendService;
        this.orgService = orgService;
        this.orgTypeConfig = orgTypeConfig;
        this.superviseConfig = superviseConfig;
        this.superviseRecordService = superviseRecordService;
    }


    @HttpMonitorLogger
    @GetMapping("/pullAll")
    @ApiOperation("拉取党支部统计")
    public ResponseEntity<Result<?>> pullAll(@RequestHeader HttpHeaders headers,
                                             @RequestParam(value = "org_id", required = false) Long orgId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = getOrgOidByHeader(sysHeader);
        //删除之前的数据
        superviseService.delSuperviseData();
        //获取所有组织信息
        List<OrganizationEntity> organizationEntities = orgService.selectAllChildOrgBYOrgAndType(sysHeader.getRegionId()
                , orgId, orgTypeConfig.getBranch());
        CountDownLatch latch = new CountDownLatch(organizationEntities.size());
        organizationEntities.forEach(item -> superviseService.pullSupervise(item, superviseConfig.getBranch(), latch, HttpLogAspect.getSSLog()));
        // 等待所有线程与完毕
        try {
            log.debug("等待前面执行完毕!");
            latch.await();
            log.debug("拉取党支部执行完成!");
            //拉取所有党委
            List<OrganizationEntity> organizationEntitiesCommittee = orgService.selectAllChildOrgBYOrgAndType(
                    sysHeader.getRegionId(), orgId, orgTypeConfig.getCommittee());
            //处理顶级组织是不是包含在内
            orgService.handlerTopOrgInfo(organizationEntitiesCommittee, sysHeader.getRegionId());
            CountDownLatch latchCommittee = new CountDownLatch(organizationEntitiesCommittee.size());
            organizationEntitiesCommittee.forEach(item -> superviseService.
                    pullSupervise(item, superviseConfig.getCommittee(), latchCommittee, HttpLogAspect.getSSLog()));
            latchCommittee.await();
            log.debug("拉取党委执行执行完成!");
            //执行细项数据
        } catch (InterruptedException e) {
            log.error("程序出现异常: ", e);
            Thread.currentThread().interrupt();
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/pullBranch")
    @ApiOperation("拉取党支部统计")
    public ResponseEntity<Result<?>> pullSuperviseBranchInfo(@RequestHeader HttpHeaders headers,
                                                             @RequestParam(value = "org_id", required = false) Long orgId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = getOrgOidByHeader(sysHeader);
        //获取所有组织信息
        List<OrganizationEntity> organizationEntities = orgService.selectAllChildOrgBYOrgAndType(sysHeader.getRegionId()
                , orgId, orgTypeConfig.getBranch());
        CountDownLatch latch = new CountDownLatch(organizationEntities.size());
        organizationEntities.forEach(item -> superviseService.pullSupervise(item, superviseConfig.getBranch(), latch, HttpLogAspect.getSSLog()));
        // 等待所有线程与完毕
        try {
            log.debug("等待前面执行完毕!");
            latch.await();
            log.debug("前面已经执行完毕!");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("程序出现异常: ", e);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/pullBranchTest")
    @ApiOperation("拉取党支部统计")
    public ResponseEntity<Result<?>> pullSuperviseBranchInfoTest(@RequestHeader HttpHeaders headers,
                                                                 @RequestParam(value = "org_id") Long orgId,
                                                                 @RequestParam(value = "impl_class_name") String implClassName) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //获取所有组织信息
        List<OrganizationEntity> organizationEntities = orgService.selectAllChildOrgBYOrgAndType(sysHeader.getRegionId()
                , 3L, orgTypeConfig.getBranch());
        List<OrganizationEntity> collect =
                organizationEntities.stream().filter(item -> item.getOrganizationId().equals(orgId)).collect(Collectors.toList());
        superviseExtendService.pullSuperviseBranchInfoTest(collect.get(0), superviseConfig.getBranch(), implClassName);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/pullCommittee")
    @ApiOperation("拉取党委统计")
    public ResponseEntity<Result<?>> pullSuperviseCommittee(@RequestHeader HttpHeaders headers,
                                                            @RequestParam(value = "org_id", required = false) Long orgId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = getOrgOidByHeader(sysHeader);
        //获取所有组织信息
//        List<OrganizationEntity> organizationEntitiesCommittee = orgService.selectAllChildOrgBYOrgAndType(
//                sysHeader.getRegionId(), orgId, orgTypeConfig.getCommittee());
        List<OrganizationEntity> organizationEntitiesCommittee = new ArrayList<>();
        //处理顶级组织是不是包含在内
        orgService.handlerTopOrgInfo(organizationEntitiesCommittee, sysHeader.getRegionId());
        CountDownLatch latchCommittee = new CountDownLatch(organizationEntitiesCommittee.size());
        organizationEntitiesCommittee.forEach(item -> superviseService.
                pullSupervise(item, superviseConfig.getCommittee(), latchCommittee, HttpLogAspect.getSSLog()));
        CountDownLatch latch = new CountDownLatch(organizationEntitiesCommittee.size());
        organizationEntitiesCommittee.forEach(item -> superviseService.pullSupervise(item, superviseConfig.getCommittee(), latch, HttpLogAspect.getSSLog()));
        // 等待所有线程与完毕
        try {
            log.debug("等待前面执行完毕!");
            latch.await();
            log.debug("前面已经执行完毕!");
        } catch (InterruptedException e) {
            log.error("程序出现异常: ", e);
            Thread.currentThread().interrupt();
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/pullCommitteeTest")
    @ApiOperation("拉取党委统计")
    public ResponseEntity<Result<?>> pullSuperviseCommitteeTest(@RequestHeader HttpHeaders headers,
                                                                @RequestParam(value = "org_id", required = false) Long orgId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //获取所有组织信息
        List<OrganizationEntity> organizationEntities = orgService.selectAllChildOrgBYOrgAndType(
                sysHeader.getRegionId(), orgId, orgTypeConfig.getCommittee());
        List<OrganizationEntity> collect =
                organizationEntities.stream().filter(item -> item.getOrganizationId().equals(orgId)).collect(Collectors.toList());
        CountDownLatch latch = new CountDownLatch(1);
        superviseService.pullSupervise(collect.get(0), superviseConfig.getCommittee(), latch, HttpLogAspect.getSSLog());
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/list")
    @ApiOperation("监督预警列表")
    public ResponseEntity<Result<?>> superviseList(@RequestHeader HttpHeaders headers,
                                                   @RequestParam(value = "org_id", required = false) Long orgId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = getOrgOidByHeader(sysHeader);
        //获取列表信息
        SuperviseListForm superviseListForm = superviseService.list(sysHeader.getRegionId(), orgId);
        return new ResponseEntity<>(new Result<>(superviseListForm, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/sendMsg")
    @ApiOperation("监督预警列表")
    public ResponseEntity<Result<?>> sendMsg(@RequestHeader HttpHeaders headers,
                                             @RequestParam(value = "org_ids", required = false) List<Long> orgIds) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Long orgId = getOrgOidByHeader(sysHeader);
        //调用发送消息
        //superviseService.sendMsgSummary(sysHeader, Collections.singletonList(orgId),-1L,null);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/one_click")
    @ApiOperation("一健督办")
    @RepeatedCheck
    public ResponseEntity<Result<?>> onClick(@RequestHeader HttpHeaders headers,
                                             @RequestBody OneClickForm oneClickForm) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Long orgId = getOrgOidByHeader(sysHeader);
        int type = oneClickForm.getType();
        if (type == 1) {
            if (CollectionUtils.isEmpty(oneClickForm.getOptionKeys())) {
                return new ResponseEntity<>(new Result<>(errors, 9404, HttpStatus.OK.value(),
                        "选项key不能为空"), HttpStatus.OK);
            }
        } else if (type == 2) {
            if (CollectionUtils.isEmpty(oneClickForm.getChooseOrgIds())) {
                return new ResponseEntity<>(new Result<>(errors, 9404, HttpStatus.OK.value(),
                        "组织不能为空"), HttpStatus.OK);
            }
        } else if (type == 3) {
            if (CollectionUtils.isEmpty(oneClickForm.getUserIds())) {
                return new ResponseEntity<>(new Result<>(errors, 9404, HttpStatus.OK.value(),
                        "用户不能为空"), HttpStatus.OK);
            }
        }
        //记录发送消息记录
        Long recordId = superviseRecordService.insertRecord(sysHeader, orgId);
        //调用发送消息
        superviseService.sendMsgSummary(sysHeader, Collections.singletonList(orgId), recordId, oneClickForm.getOptionKeys(),
                oneClickForm.getUserIds(), oneClickForm.getChooseOrgIds(), type);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/branch/detail")
    @ApiOperation("列表首页-支部-查看用户")
    public ResponseEntity<Result<?>> superviseBranchDetail(@RequestHeader HttpHeaders headers,
                                                           @RequestParam(required = false, defaultValue = "1") Integer page,
                                                           @RequestParam(required = false,
                                                                   name = "page_size", defaultValue = "10") Integer pageSize,
                                                           @RequestParam(value = "org_id", required = false) Long orgId,
                                                           @RequestParam(value = "option_key", required = false) String optionKey) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = getOrgOidByHeader(sysHeader);
        String[] col = {"userId", "userName", "userPhone", "detailContent", "orgName", "orgSecretary"};
        //获取列表信息
        List<SuperviseUserForm> superviseList = superviseService.superviseBranchDetail(orgId, optionKey, col,
                new PageNumber(page, pageSize), sysHeader);
        return new ResponseEntity<>(new Result<>(superviseList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/committee/detail")
    @ApiOperation("监督预警列表-用户统计")
    public ResponseEntity<Result<?>> superviseCommitteeDetail(@RequestHeader HttpHeaders headers,
                                                              @RequestParam(required = false, defaultValue = "1") Integer page,
                                                              @RequestParam(required = false,
                                                                      name = "page_size", defaultValue = "10") Integer pageSize,
                                                              @RequestParam(value = "org_id", required = false) Long orgId,
                                                              @RequestParam(value = "option_key", required = false)
                                                              String optionKey) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = getOrgOidByHeader(sysHeader);
        String[] col = {"orgId", "orgName", "orgSecretary", "subOrgId", "detailContent"};
        //获取列表信息
        List<SuperviseUserForm> superviseList = superviseService.superviseCommitteeDetail(orgId, optionKey, col,
                new PageNumber(page, pageSize), sysHeader);
        return new ResponseEntity<>(new Result<>(superviseList, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/org/list")
    @ApiOperation("监督党委-按组织统计")
    public ResponseEntity<Result<?>> superviseOrgList(@RequestHeader HttpHeaders headers,
                                                      @RequestParam(required = false, defaultValue = "1") Integer page,
                                                      @RequestParam(required = false,
                                                              name = "page_size", defaultValue = "10") Integer pageSize,
                                                      @RequestParam(value = "org_id", required = false) Long orgId,
                                                      @RequestParam(value = "org_name", required = false) String orgName) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = getOrgOidByHeader(sysHeader);
        //获取列表信息-按组织统计
        List<SuperviseOrgForm> superviseList = superviseService.listByOrgSta(orgId, orgName,
                new PageNumber(page, pageSize), sysHeader);
        return new ResponseEntity<>(new Result<>(superviseList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org/list_sub")
    @ApiOperation("监督党委-按组织统计-查询下级")
    public ResponseEntity<Result<?>> superviseOrgListSub(@RequestHeader HttpHeaders headers,
                                                         @RequestParam(required = false, defaultValue = "1") Integer page,
                                                         @RequestParam(required = false,
                                                                 name = "page_size", defaultValue = "100") Integer pageSize,
                                                         @RequestParam(value = "org_id") Long orgId,
                                                         @RequestParam(value = "org_name", required = false) String orgName) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //获取列表信息-按组织统计
        List<SuperviseOrgForm> superviseList = superviseService.listByOrgSta(orgId, orgName,
                new PageNumber(page, pageSize), sysHeader);
        return new ResponseEntity<>(new Result<>(superviseList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org/list/detail")
    @ApiOperation("监督党委-按组织统计-详情")
    public ResponseEntity<Result<?>> superviseOrgListDetail(@RequestHeader HttpHeaders headers,
                                                            @RequestParam(value = "org_id", required = false) Long subOrgId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //获取列表信息-按组织统计-详情
        List<OptionForm> superviseList = superviseService.listByOrgStaDetail(sysHeader.getOid(), subOrgId);
        return new ResponseEntity<>(new Result<>(superviseList, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/user/list")
    @ApiOperation("监督党委-按党员统计")
    public ResponseEntity<Result<?>> superviseUserList(@RequestHeader HttpHeaders headers,
                                                       @RequestParam(defaultValue = "1") Integer page,
                                                       @RequestParam(required = false,
                                                               name = "page_size", defaultValue = "10") Integer pageSize,
                                                       @RequestParam(value = "user_name", required = false) String userName,
                                                       @RequestParam(value = "mobile", required = false) String mobile) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Long orgId = getOrgOidByHeader(sysHeader);
        //获取列表信息-按组织统计
        List<SuperviseUserForm> superviseList = superviseService.listByUserSta(orgId, userName, mobile,
                new PageNumber(page, pageSize), sysHeader);
        return new ResponseEntity<>(new Result<>(superviseList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/user/list/detail")
    @ApiOperation("监督党委-按党员统计")
    public ResponseEntity<Result<?>> superviseUserDetailList(@RequestHeader HttpHeaders headers,
                                                             @RequestParam(required = false, defaultValue = "1") Integer page,
                                                             @RequestParam(required = false,
                                                                     name = "page_size", defaultValue = "10") Integer pageSize,
                                                             @RequestParam(value = "org_id", required = false) Long orgId,
                                                             @RequestParam(value = "user_id") Long userId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = getOrgOidByHeader(sysHeader);
        //获取列表信息-按组织统计
        List<OptionForm> superviseList = superviseService.listByUserStaDetail(userId, orgId,
                new PageNumber(page, pageSize), sysHeader);
        return new ResponseEntity<>(new Result<>(superviseList, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/tips")
    @ApiOperation("监督党委-按组织统计-详情")
    public ResponseEntity<Result<?>> tips(@RequestHeader HttpHeaders headers,
                                          @RequestParam(value = "org_id", required = false) Long orgId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgId = getOrgOidByHeader(sysHeader);
        //获取列表信息-按组织统计-详情
        SuperviseTipsForm tips = superviseService.tips(orgId);
        tips.setIsClick(superviseRecordService.valTodayIsSend(sysHeader, orgId));
        return new ResponseEntity<>(new Result<>(tips, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/click_detail")
    @ApiOperation("点击查看详情")
    public ResponseEntity<Result<?>> clickDetail(@RequestHeader HttpHeaders headers,
                                                 @RequestParam(value = "option_key") String optionKey,
                                                 @RequestParam(value = "detail_id", required = false) Long detailOrgId,
                                                 @RequestParam(value = "user_id", required = false) Long userId) {
        // 2.组织统计  3.按党员统计
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return superviseService.clickDetail(sysHeader, optionKey, detailOrgId, userId);
    }

    /**
     * 换取组织id 试配手机端与pc端
     *
     * @param sysHeader
     * @return
     */
    private Long getOrgOidByHeader(HeaderHelper.SysHeader sysHeader) {
        Long uoid = sysHeader.getUoid();
        if (null != uoid) {
            return uoid;
        }
        return sysHeader.getOid();
    }
}
