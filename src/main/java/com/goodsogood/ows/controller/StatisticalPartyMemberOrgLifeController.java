package com.goodsogood.ows.controller;


import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.sas.SasOrgLifeConditionForm;
import com.goodsogood.ows.model.vo.sas.StaLeaderOrgLife;
import com.goodsogood.ows.service.sas.StatisticalUserOrgLifeService;
import com.goodsogood.ows.utils.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 党员组织生活统计
 *
 * <AUTHOR>
 * @create 2019年7月23日 15:14:05
 **/
@RestController
@RequestMapping("/sas/party-member-org-life")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "/sas/party-member-org-life", tags = {"党员组织生活统计"})
@Validated
public class StatisticalPartyMemberOrgLifeController {

    private final Errors errors;
    private final StatisticalUserOrgLifeService statisticalUserOrgLifeService;
    @Autowired
    public StatisticalPartyMemberOrgLifeController(
            Errors errors,
            StatisticalUserOrgLifeService statisticalUserOrgLifeService) {
        this.errors = errors;
        this.statisticalUserOrgLifeService = statisticalUserOrgLifeService;
    }

    /**
     * 查询党员组织生活统计
     *
     * <AUTHOR>
     * @date 2019/4/23 14:43
     */
    @HttpMonitorLogger
    @PostMapping("/list")
    @ApiOperation("查询党员组织生活统计")
    public ResponseEntity<Result<Page<StaLeaderOrgLife>>> list(@RequestHeader HttpHeaders headers,
                                                               @RequestBody @Valid SasOrgLifeConditionForm form,
                                                               BindingResult bindingResult) {
        log.info("分页查询党党员组织生活统计. StatisticalPartyMemberOrgLifeController.list SasOrgLifeConditionForm->\n{}", JsonUtils.toJson(form));
        //得到区县id
        Long regionId =  HeaderHelper.buildMyHeader(headers).getRegionId();
        form.setRegionId(regionId);
        return new ResponseEntity<>(new Result<>(statisticalUserOrgLifeService.
                partyMemberOrgLife(form), errors), HttpStatus.OK);
    }


    /**
     * 党员组织生活统计导出
     *
     * <AUTHOR>
     * @date 2019/4/23 14:43
     */
    @HttpMonitorLogger
    @GetMapping("/export")
    @ApiOperation("党员组织生活统计导出")
    @RepeatedCheck
    public void export(HttpServletResponse response, HttpServletRequest request,@RequestHeader HttpHeaders headers,
                                            @RequestParam(name = "org_id")
                                            @Range(min = 1, max = 999999L, message = "orgId参数出错") Long orgId,
                                            @RequestParam(name = "org_name")
                                            @NotNull(message = "组织信息不能为空") String orgName,
                                            @RequestParam(name = "activity_type_ids", required = false) String activityTypeIds,
                                            @RequestParam(name = "time_type")
                                            @Range(min = 1, max = 4, message = "时间类型输入出错") Integer timeType,
                                            @RequestParam(name = "year")
                                            @Range(min = 2018, max = 2024, message = "年份输出参数错误") Integer year,
                                            @Range(min = 0, max = 6, message = "查询时间参数错误") Integer time,
                                            @RequestParam(name = "start_month", required = false) Integer startMonth,
                                            @RequestParam(name = "end_month", required = false) Integer endMonth,
                                            @RequestParam(name = "is_retire") Integer isRetire) {
        //得到区县id
        Long regionId =  HeaderHelper.buildMyHeader(headers).getRegionId();
        log.info("党员组织生活统计导出. StatisticalPartyMemberOrgLifeController.export " +
                        "orgId:{},regionId:{}, orgName:{},activityTypeIds:{},timeType:{}, year:{},time:{},startMonth:{}, endMonth:{}, isRetire:{}}",
                orgId,
                regionId,
                orgName,
                activityTypeIds,
                timeType,
                year,
                time,
                startMonth,
                endMonth,
                isRetire
        );
            log.info("导出党员组织生活统计统计列表...开始。。。。");
            //条件组装
            SasOrgLifeConditionForm form = new SasOrgLifeConditionForm();
            form.setOrgId(orgId);
            form.setActivityTypeIds(activityTypeIds);
            form.setTimeType(timeType);
            form.setYear(year);

            form.setTime(time);
            form.setIsRetire(isRetire);
            form.setStartMonth(startMonth);
            form.setEndMonth(endMonth);
            form.setOrgName(orgName);
            if (StringUtils.isEmpty(form.getOrgName())) {
                throw new ApiException("组织信息不能为空！");
            }
            boolean flag = statisticalUserOrgLifeService.export(response, request, form,regionId);
            log.info("导出结果：" + flag);
            log.info("导出党员组织生活统计统计列表...结束。。。。");
    }
}
