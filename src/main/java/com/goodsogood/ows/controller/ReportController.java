package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.report.OrgReportForm;
import com.goodsogood.ows.service.electronicreport.OrgReportService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @description 电子党务报告控制类
 * @date 2019/11/26
 */
@Controller
@RequestMapping("/sas")
@Log4j2
public class ReportController {

    private final Errors errors;
    private final OrgReportService orgReportService;

    @Autowired
    public ReportController(Errors errors, OrgReportService orgReportService) {
        this.errors = errors;
        this.orgReportService = orgReportService;
    }

    @HttpMonitorLogger
    @GetMapping("/org/report")
    public ResponseEntity<Result<?>> OrgReport(@RequestParam(value = "org_id") Long orgId,
                                               @RequestParam(value = "start_date") String startDate,
                                               @RequestParam(value = "end_date") String endDate){
        OrgReportForm orgReportForm = this.orgReportService.getOrgReportInfo(orgId, startDate, endDate);
        return new ResponseEntity<>(new Result<>(orgReportForm, errors), HttpStatus.OK);
    }
}
