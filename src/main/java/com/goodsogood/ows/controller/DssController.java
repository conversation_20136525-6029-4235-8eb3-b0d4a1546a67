package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.dss.*;
import com.goodsogood.ows.service.sas.DssHistoryService;
import com.goodsogood.ows.utils.RateUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;

/**
 * 辅助决策控制层
 * <AUTHOR>
 */
@Controller
@Log4j2
@RequestMapping("/dss")
public class DssController {

    private static final String STATIC_UUID = "DSS_BUILD_";
    private static final String STATIC_UUID_FILE = "DSS_BUILD_FILE";

    private final Errors errors;
    private final DssService dssService;
    private final DssFileService dssFileService;
    private final DssHistoryService dssHistoryService;
    private final DssBatchService dssBatchService;
    private final DssCompensateService compensateService;
    private final DssMakeUpService dssMakeUpService;

    @Autowired
    public DssController(Errors errors, DssService dssService,
                         DssFileService dssFileService,
                         DssHistoryService dssHistoryService,
                         DssBatchService dssBatchService,
                         DssCompensateService compensateService,
                         DssMakeUpService dssMakeUpService) {
        this.errors = errors;
        this.dssService = dssService;
        this.dssFileService = dssFileService;
        this.dssHistoryService = dssHistoryService;
        this.dssBatchService = dssBatchService;
        this.compensateService = compensateService;
        this.dssMakeUpService = dssMakeUpService;
    }

    @HttpMonitorLogger
    @GetMapping("/build")
    @ApiOperation("构建辅助决策数据")
    public ResponseEntity<Result<?>> build(@RequestHeader HttpHeaders headers,
                                           HttpServletRequest request,
                                           @RequestParam(value = "year") Integer year) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        String result = "0";
        String redisKey = STATIC_UUID + year;
        if (RateUtils.hasKey(redisKey)) {
            result = RateUtils.getRate(redisKey, 2);
        } else {
            this.dssService.build(header.getRegionId(), year, redisKey);
            // 记录操作历史
            this.dssHistoryService.insert(header.getUserId(), new Date(), Utils.getRemoteHost(request), header.getRegionId(), 1, "/dss/build");
        }
        return new ResponseEntity<>(new Result<>(result + "%", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/buildIndex")
    @ApiOperation("构建首页数据")
    public ResponseEntity<Result<?>> buildIndex(@RequestHeader HttpHeaders headers,
                                                HttpServletRequest request,
                                                @RequestParam(value = "year") Integer year) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.dssService.buildIndex(header.getRegionId(), year);
        // 记录操作历史
        this.dssHistoryService.insert(header.getUserId(), new Date(), Utils.getRemoteHost(request), header.getRegionId(), 1, "/dss/buildIndex");
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/builderCommittee")
    @ApiOperation("构建党委数据")
    public ResponseEntity<Result<?>> buildCommittee(@RequestHeader HttpHeaders headers,
                                                    HttpServletRequest request,
                                                    @RequestParam(value = "year") Integer year) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        String redisKey = this.dssService.buildCommittee(header.getRegionId(), year);
        // 记录操作历史
        this.dssHistoryService.insert(header.getUserId(), new Date(), Utils.getRemoteHost(request), header.getRegionId(), 1, "/dss/builderCommittee");
        return new ResponseEntity<>(new Result<>(redisKey, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/buildBranch")
    @ApiOperation("构建党支部数据")
    public ResponseEntity<Result<?>> buildBranch(@RequestHeader HttpHeaders headers,
                                                 HttpServletRequest request,
                                                 @RequestParam(value = "year") Integer year) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        String redisKey = this.dssService.buildBranch(header.getRegionId(), year);
        // 记录操作历史
        this.dssHistoryService.insert(header.getUserId(), new Date(), Utils.getRemoteHost(request), header.getRegionId(), 1, "/dss/buildBranch");
        return new ResponseEntity<>(new Result<>(redisKey, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/buildUser")
    @ApiOperation("构建人员数据")
    public ResponseEntity<Result<?>> buildUserInfo(@RequestHeader HttpHeaders headers,
                                                   HttpServletRequest request,
                                                   @RequestParam(value = "year") Integer year) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        String redisKey = this.dssService.buildUserInfo(header.getRegionId(), year);
        // 记录操作历史
        this.dssHistoryService.insert(header.getUserId(), new Date(), Utils.getRemoteHost(request), header.getRegionId(), 1, "/dss/buildUser");
        return new ResponseEntity<>(new Result<>(redisKey, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/buildDssFile")
    @ApiOperation("构建文件")
    public ResponseEntity<Result<?>> buildDssFile(@RequestHeader HttpHeaders headers,
                                                  HttpServletRequest request,
                                                  @RequestParam(value = "year", required = false) Integer[] year) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        String result = "0";
        if (RateUtils.hasKey(STATIC_UUID_FILE)) {
            result = RateUtils.getRate(STATIC_UUID_FILE, 2);
        } else {
            this.dssFileService.buildFile(header.getRegionId(), year, header.getUserId(), STATIC_UUID_FILE);
            // 记录操作历史
            this.dssHistoryService.insert(header.getUserId(), new Date(), Utils.getRemoteHost(request), header.getRegionId(), 2, "/dss/buildDssFile");
        }
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/downloadDssFile")
    @ApiOperation("下载党费统计列表")
    public void downDssFil(@RequestHeader HttpHeaders headers,
                           HttpServletRequest request,
                           HttpServletResponse response) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        try {
            this.dssFileService.downloadFile(response, STATIC_UUID_FILE);
        } catch (IOException e) {
            log.error("文件下载失败 -> ", e);
            throw new ApiException("文件下载失败", new Result<>(errors, 5001, HttpStatus.OK.value()));
        }
        // 记录操作历史
        this.dssHistoryService.insert(header.getUserId(), new Date(), Utils.getRemoteHost(request), header.getRegionId(), 2, "/dss/downDssFile");

    }

    @HttpMonitorLogger
    @GetMapping("/testDssOrgData")
    @ApiOperation("指定组织拉取辅助决策数据")
    public ResponseEntity<Result<?>> testDssOrgData(@RequestParam(value = "region_id") Long regionId,
                                                 @RequestParam(value = "org_ids",required = false) long[] orgIds,
                                                 @RequestParam(value = "years",required = false) int[] years) {
        this.dssService.buildDssDataByOrg(regionId, orgIds, years);
        return new ResponseEntity<>(new Result<>("ok", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testDssOneOrgData")
    @ApiOperation("指定组织拉取辅助决策数据")
    public ResponseEntity<Result<?>> testDssOneOrgData(@RequestParam(value = "region_id") Long regionId,
                                                    @RequestParam(value = "org_ids",required = false) long[] orgIds,
                                                    @RequestParam(value = "years",required = false) int[] years) {
        this.dssService.buildDssOneOrgInfo(orgIds, regionId, years);
        return new ResponseEntity<>(new Result<>("ok", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/testDssUserData")
    @ApiOperation("指定组织拉取辅助决策数据")
    public ResponseEntity<Result<?>> testDssUserData(@RequestParam(value = "region_id") Long regionId,
                                                 @RequestParam(value = "user_ids",required = false) long[] userIds,
                                                 @RequestParam(value = "years",required = false) int[] years) {
        this.dssService.buildDssOneUserInfo(userIds, regionId, years);
        return new ResponseEntity<>(new Result<>("ok", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/buildCommitteeList")
    @ApiOperation("批量拉取党委的辅助决策数据")
    public ResponseEntity<Result<?>> buildCommitteeList(@RequestParam(value = "years",required = false) int[] years,
                                                        @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        for (int year : years) {
            this.dssBatchService.buildCommitteeList(header.getRegionId(), year);
        }
        return new ResponseEntity<>(new Result<>("ok", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/buildBranchList")
    @ApiOperation("批量拉取党支部的辅助决策数据")
    public ResponseEntity<Result<?>> buildBranchList(@RequestParam(value = "years",required = false) int[] years,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        for (int year : years) {
            this.dssBatchService.buildBranchList(header.getRegionId(), year);
        }
        return new ResponseEntity<>(new Result<>("ok", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/buildUserInfoList")
    @ApiOperation("批量拉取人员的辅助决策数据")
    public ResponseEntity<Result<?>> buildUserInfoList(@RequestParam(value = "years",required = false) int[] years,
                                                       @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        for (int year : years) {
            this.dssBatchService.buildUserInfoList(header.getRegionId(), year);
        }
        return new ResponseEntity<>(new Result<>("ok", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/setDssOneModule")
    @ApiOperation("单模块数据补偿")
    public ResponseEntity<Result<?>> setDssOneModule(@RequestParam(value = "module_type",required = false) int moduleType,
                                                     @RequestParam(value = "data_type",required = false) int dataType,
                                                     @RequestParam(value = "years",required = false) int[] years,
                                                     @RequestParam(value = "ids", required = false) long[] ids,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (ids == null ||ids.length == 0) {
            for (int year : years) {
                this.compensateService.setDssOneModule(moduleType, dataType, header.getRegionId(), year, null);
            }
        } else {
            for (int year : years) {
                for (long id : ids) {
                    this.compensateService.setDssOneModule(moduleType, dataType, header.getRegionId(), year, id);
                }
            }
        }
        return new ResponseEntity<>(new Result<>("ok", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/makeUpDssData")
    @ApiOperation("补录某类型数据")
    public ResponseEntity<Result<?>> makeUpDssData(@RequestParam(value = "years",required = false) int[] years,
                                                   @RequestParam(value = "data_type",required = false) int dataType,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        for (int year : years) {
            this.dssMakeUpService.makeUpDssData(year, header.getRegionId(), dataType);
        }
        return new ResponseEntity<>(new Result<>("ok", errors), HttpStatus.OK);
    }
}
