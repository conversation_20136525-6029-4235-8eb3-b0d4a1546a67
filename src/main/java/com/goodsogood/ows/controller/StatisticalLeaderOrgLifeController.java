package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.config.ApplicationConfigHelper;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.sas.EvalLeaderMeeting;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.sas.SasOrgLifeConditionForm;
import com.goodsogood.ows.model.vo.sas.StaLeaderOrgLife;
import com.goodsogood.ows.service.sas.StasticConfigInfoService;
import com.goodsogood.ows.service.sas.StatisticalLeaderOrgLifeService;
import com.goodsogood.ows.service.sas.StatisticalOrgLifeService;

import com.goodsogood.ows.utils.ExclExportUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 *
 * <AUTHOR>
 * @create 2019-04-22 16:01
 **/
@RestController
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
@RequestMapping("/sas/leader-org-life")
@Api(value = "领导组织生活统计", tags = {"领导组织生活统计"})
public class StatisticalLeaderOrgLifeController {

     private final Errors errors;
     private final StatisticalLeaderOrgLifeService statisticalLeaderOrgLifeService;
     private final StatisticalOrgLifeService statisticalOrgLifeService;
     private final StasticConfigInfoService stasticConfigInfoService;
    private final ApplicationConfigHelper applicationConfigHelper;

     @Autowired
     public StatisticalLeaderOrgLifeController(Errors errors,
                                               StatisticalLeaderOrgLifeService statisticalLeaderOrgLifeService,
                                               StatisticalOrgLifeService statisticalOrgLifeService,
                                               StasticConfigInfoService stasticConfigInfoService,
                                               ApplicationConfigHelper applicationConfigHelper) {
          this.errors = errors;
          this.statisticalLeaderOrgLifeService = statisticalLeaderOrgLifeService;
          this.statisticalOrgLifeService=statisticalOrgLifeService;
          this.stasticConfigInfoService=stasticConfigInfoService;
          this.applicationConfigHelper = applicationConfigHelper;
     }

     /**
      * 查询党组织生活统计列表
      * <AUTHOR>
      * @date 2019/4/23 14:43
      */
     @HttpMonitorLogger
     @PostMapping("/list")
     @ApiOperation("查询领导党组织生活统计列表")
     @Valid
     public ResponseEntity<Result<Page<List<StaLeaderOrgLife>>>> list(@RequestHeader HttpHeaders headers,
                                                                      @RequestBody
                                                                      @Valid SasOrgLifeConditionForm form,
                                                                      BindingResult bindingResult) {
          try {
              //Region regions = applicationConfigHelper.getRegions("V3.0-DEV");
              //System.out.println(regions);
              HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
              form.setRegionId(sysHeader.getRegionId());
              SasOrgLifeConditionForm conditionForm = statisticalOrgLifeService.buildParam(form);
              //查询组织类型选择范围
              conditionForm.setShowOrgType(stasticConfigInfoService.
                      getConfig(Constants.LEADER_ORG_MEETING_TYPE,
                              sysHeader.getRegionId()).getShowOrganizationTypes());
              //先统计那些组织 领导 这里限制查询那些区县组织
               Page<List<StaLeaderOrgLife>> leadOrgLife = statisticalLeaderOrgLifeService.
                       getLeadOrgLife(conditionForm);
               if(CollectionUtils.isEmpty(leadOrgLife.getResult())){
                   return new ResponseEntity<>(new Result<>(leadOrgLife, errors), HttpStatus.OK);
               }
               //然后再统计 参加年月信息
               statisticalLeaderOrgLifeService.getLeadOrgLifeDetailInfo(leadOrgLife,conditionForm);
               return new ResponseEntity<>(new Result<>(leadOrgLife, errors), HttpStatus.OK);
          }catch (Exception ex){
               log.error("ex-msg",ex);
              throw new ApiException("查询领导党组织生活统计出错", new Result<>(errors, 4713,
                      HttpStatus.OK.value()));
          }
     }

     /**
      * 查询党组织生活统计列表
      * <AUTHOR>
      * @date 2019/4/23 14:43
      */
     @HttpMonitorLogger
     @GetMapping("/exportExcel")
     @ApiOperation("导出领导党组织生活统计列表")
     @Valid
     public ResponseEntity<Result<?>> export(HttpServletResponse response, HttpServletRequest request,
                                             @RequestHeader HttpHeaders headers,
                                             @Range(min = 1, max = 999999L, message = "orgId参数出错") Long orgId,
                                             @NotNull(message = "组织信息不能为空") String orgName,
                                             @NotNull(message = "类型Ids不能为空")String activityTypeIds,
                                             @Range(min = 1, max = 4, message = "时间类型输入出错")Integer timeType,
                                             @Range(min = 2018, max = 2023, message = "年份输出参数错误")Integer year,
                                             @Range(min = 0, max = 6, message = "查询时间参数错误")Integer time,
                                             @RequestParam( required = false) Integer startMonth,
                                             @RequestParam( required = false) Integer endMonth,
                                             @RequestParam(required = false) Integer isRetire,
                                             @RequestParam(required = false) Integer queryLimit) {
          try {
              log.info("导出领导党组织生活统计列表...开始。。。。");
              HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
              //条件组装
              SasOrgLifeConditionForm form = new SasOrgLifeConditionForm();
              form.setOrgId(orgId);
              form.setRegionId(sysHeader.getRegionId());
              form.setActivityTypeIds(activityTypeIds);
              form.setTimeType(timeType);
              form.setYear(year);

              form.setActivityTypeId(0);
              form.setTime(time);
              form.setIsRetire(isRetire);
              form.setOrgName(orgName);
              form.setStartMonth(startMonth);
              form.setEndMonth(endMonth);
              form.setQueryLimit(queryLimit);
              if(StringUtils.isEmpty(form.getOrgName())){
                  return new ResponseEntity<>(new Result<>("组织信息不能为空！", errors), HttpStatus.OK);
              }
              SasOrgLifeConditionForm conditionForm = statisticalOrgLifeService.buildParam(form);
              //查询组织类型选择范围
              conditionForm.setShowOrgType(stasticConfigInfoService.
                      getConfig(Constants.LEADER_ORG_MEETING_TYPE,sysHeader.getRegionId()).getShowOrganizationTypes());
               //先统计那些组织生活
               List<StaLeaderOrgLife> leadOrgLife = statisticalLeaderOrgLifeService.exportLeadOrgLife(conditionForm);
               if(CollectionUtils.isEmpty(leadOrgLife)){
                    return new ResponseEntity<>(new Result<>("查询信息不存在，无法导出！", errors), HttpStatus.OK);
               }
               //然后再统计 参加年月信息
               List<StaLeaderOrgLife> staLeaderOrgLives = statisticalLeaderOrgLifeService.exportLeadOrgLifeDetailInfo(leadOrgLife, conditionForm);
               //得到返回excelName
               String excelName = statisticalLeaderOrgLifeService.bulidSheetName(1, conditionForm);
               HSSFWorkbook workbook = statisticalLeaderOrgLifeService.exportPortExcel(staLeaderOrgLives, conditionForm);
               boolean flag = ExclExportUtils.export03(request, response, workbook, excelName);
               log.info("导出结果：" + flag);
               log.info("导出领导党组织生活统计列表...结束。。。。");
               return  null;

          }catch (Exception ex){
               log.error("ex-exportMsg-{},ex-{}",ex.getMessage(),ex);
              throw new ApiException(
                      "导出领导组织生活失败！", new Result<>(errors, 4712, HttpStatus.OK.value()));
          }
     }

    @HttpMonitorLogger
    @GetMapping("/getLeaderMeetingByDate")
    @ApiOperation("考核任务根据组织查询领导干部组织生活情况")
     public ResponseEntity<Result<?>> getLeaderMeetingByOrg(@RequestHeader HttpHeaders headers,
                                                            @RequestParam(value = "query_date", required = false) String queryDate){
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Long regionId = sysHeader.getRegionId();
        List<EvalLeaderMeeting> leaderMeeting = this.statisticalLeaderOrgLifeService.getLeaderMeeting(queryDate,regionId);
        return new ResponseEntity<>(new Result<>(leaderMeeting, errors), HttpStatus.OK);
     }
}
