package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.CenterGroupVo;
import com.goodsogood.ows.model.vo.PartyWillVo;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.PartyWillService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/party-will")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class PartyWillController {
    private final Errors errors;
    private final PartyWillService partyWillService;
    public PartyWillController(Errors errors, PartyWillService partyWillService) {
        this.errors = errors;
        this.partyWillService = partyWillService;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "党组会议统计")
    @GetMapping("/statistics")
    public ResponseEntity<Result<List<PartyWillVo>>> statistics(@RequestHeader HttpHeaders headers,
                                                                @RequestParam(name = "name", required = false) String name,
                                                                @RequestParam(name = "year", required = true) Integer year) {
        Result<List<PartyWillVo>> result = new Result<>(partyWillService.statistics(headers,name,year), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
}
