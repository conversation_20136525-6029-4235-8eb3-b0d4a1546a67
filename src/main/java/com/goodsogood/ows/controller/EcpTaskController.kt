package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.ecp.EcpStatisticsTaskService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*


@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "党业融合", tags = ["云区任务参与情况"])
@RequestMapping
class EcpTaskController(
    @Autowired val errors: Errors,
    @Autowired val ecpStatisticsTaskService: EcpStatisticsTaskService,
) {
    /**
     * 全市系统: 云上任务参与情况
     */
    @HttpMonitorLogger
    @GetMapping("/findCityTaskRate")
    @ApiOperation("全市系统: 云上任务参与情况")
    fun findCityTaskRate(
        @RequestHeader headers: HttpHeaders,
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.ecpStatisticsTaskService.findCityTaskRate(header), errors
            ), HttpStatus.OK
        )
    }


    /**
     * 全市系统: 云上任务参与情况列表
     * @param type 1:员工参与率
     *             2:党员参与率
     *             3:发起任务数
     *             4:参与总人次
     * @param sort -1:倒序 1:正序
     */
    @HttpMonitorLogger
    @GetMapping("/findCityTaskList")
    @ApiOperation("全市系统: 云上任务参与情况列表")
    fun findCityTaskList(
        @RequestHeader headers: HttpHeaders,
        @RequestParam(name = "type",  required = false) type: Int?,
        @RequestParam(name = "sort", required = false) sort: Int?,
        @RequestParam(name = "page", defaultValue = "1", required = false) page: Int,
        @RequestParam(name = "page_size", defaultValue = "10",required = false) pageSize: Int,
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.ecpStatisticsTaskService.findCityTaskList(header,page,pageSize,type,sort), errors
            ), HttpStatus.OK
        )
    }

    /**
     * 本单位: 云上任务参与情况
     * @param unitId 单位id
     */
    @HttpMonitorLogger
    @GetMapping("/findUnitTaskRate")
    @ApiOperation("本单位: 云上任务参与情况")
    fun findUnitTaskRate(
        @RequestHeader headers: HttpHeaders,
        @RequestParam(name = "unit_id") unitId: Long,
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.ecpStatisticsTaskService.findUnitTaskRate(header, unitId), errors
            ), HttpStatus.OK
        )
    }

    /**
     * 本单位: 云上任务参与情况人员列表
     * @param unitId 单位id
     */
    @HttpMonitorLogger
    @GetMapping("/findUnitTaskUserList")
    @ApiOperation("本单位: 云上任务参与情况人员列表")
    fun findUnitTaskUserList(
        @RequestHeader headers: HttpHeaders,
        @RequestParam(name = "unit_id") unitId: Long,
        @RequestParam(name = "type", required = false) type: Int?,
        @RequestParam(name = "page", defaultValue = "1", required = false) page: Int,
        @RequestParam(name = "page_size", defaultValue = "10",required = false) pageSize: Int,
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.ecpStatisticsTaskService.findUnitTaskUserList(header, unitId,type,page,pageSize), errors
            ), HttpStatus.OK
        )
    }
}