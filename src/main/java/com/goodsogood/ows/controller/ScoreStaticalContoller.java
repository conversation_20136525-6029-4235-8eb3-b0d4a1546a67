package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.score.LineChartVo;
import com.goodsogood.ows.model.vo.score.ReportVo;
import com.goodsogood.ows.model.vo.score.UnitScoreVo;
import com.goodsogood.ows.service.score.ScoreService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@Controller
@Log4j2
@RequestMapping("/score-statical")
public class ScoreStaticalContoller {
    private final ScoreService scoreService;
    private final Errors errors;

    @Autowired
    public ScoreStaticalContoller(ScoreService scoreService, Errors errors) {
        this.scoreService = scoreService;
        this.errors = errors;
    }

    @HttpMonitorLogger
    @GetMapping("/unit")
    @ApiOperation("查询各行政单位党员积分平均值")
    public ResponseEntity<Result<List<UnitScoreVo>>>  queryUnitScore(@RequestHeader HttpHeaders headers){
        return new ResponseEntity<>(new Result<>( scoreService.queryUnitYearScore(headers), errors), HttpStatus.OK);

    }


    @HttpMonitorLogger
    @GetMapping("/max-avg")
    @ApiOperation("查询最大分和平均值")
    public ResponseEntity<Result<ReportVo>>  queryMaxScore(@RequestHeader HttpHeaders headers){
        return new ResponseEntity<>(new Result<>(scoreService.queryAvgAndMax(), errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/month-score")
    @ApiOperation("查询最近12月月度平均积分")
    public ResponseEntity<Result<LineChartVo>>  queryMonthScore(@RequestHeader HttpHeaders headers){
        return new ResponseEntity<>(new Result<>( scoreService.queryMonthScore(headers), errors), HttpStatus.OK);
    }

}
