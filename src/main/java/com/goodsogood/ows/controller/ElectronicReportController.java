package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.report.PersonElectronicReport;
import com.goodsogood.ows.model.vo.sas.ElectronicConditionForm;
import com.goodsogood.ows.model.vo.sas.ElectronicOrgLifeData;
import com.goodsogood.ows.model.vo.sas.ElectronicPartyFeeData;
import com.goodsogood.ows.model.vo.sas.ElectronicReportForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.electronicreport.ElectronicReportServices;
import com.goodsogood.ows.service.sas.StatisticalOrgLifeService;
import com.goodsogood.ows.service.sas.StatisticalPartyFeeService;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 * 电子党务报告
 *
 * <AUTHOR>
 * @date 2019/6/12 14:48
 */
@RestController
@RequestMapping("/sas/electronic")
@Api(value = "/sas/electronic", tags = {"电子党务报告"})
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class ElectronicReportController {

    private final Errors errors;
    private final StatisticalOrgLifeService service;
    private final StatisticalPartyFeeService partyFeeService;
    private final ElectronicReportServices electronicReportServices;
    public ElectronicReportController(Errors errors, StatisticalOrgLifeService service,
                                      StatisticalPartyFeeService partyFeeService,
                                      ElectronicReportServices electronicReportServices) {
        this.errors = errors;
        this.service = service;
        this.partyFeeService = partyFeeService;
        this.electronicReportServices = electronicReportServices;
    }

    /**
     * 电子党务报表统计
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/6/12 17:44
     */
    @HttpMonitorLogger
    @PostMapping("/list")
    @ApiOperation("电子报表统计")
    public ResponseEntity<Result<ElectronicReportForm>> list(@RequestHeader HttpHeaders headers,
                                                             @Valid @RequestBody ElectronicConditionForm form,
                                                             BindingResult bindingResult) {
        ElectronicReportForm electronicReportForm = new ElectronicReportForm();
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        form.setRegionId(sysHeader.getRegionId());
        //查询统计信息
        List<ElectronicOrgLifeData> electronicOrgLifeDataList = service.electronicSastic(form);
        electronicReportForm.setOrgLifeData(electronicOrgLifeDataList);
        //查询数量以及名字
        Integer activityAllCount=0;
        if (!CollectionUtils.isEmpty(electronicOrgLifeDataList)){
            for (ElectronicOrgLifeData orgLifeData:electronicOrgLifeDataList) {
                activityAllCount+=orgLifeData.getParticipateNum();
            }
        }
        form.setRegionId(sysHeader.getRegionId());
        ElectronicOrgLifeData electronicBasicData = service.electronicBasicData(form);
        electronicReportForm.setOrgName(electronicBasicData.getOrgName());
        electronicReportForm.setSubOrgCount(electronicBasicData.getSubOrgCount());
        electronicReportForm.setActivityAllCount(activityAllCount);
        //查询党费信息
        ElectronicPartyFeeData partyFeeData = partyFeeService.electronicPartyfFeeStastic(form);
        electronicReportForm.setPartyfFeeData(partyFeeData);
        return new ResponseEntity<>(new Result<>(electronicReportForm, errors), HttpStatus.OK);
    }


    /**
     * 得到个人电子党务报统计--微信端-个人电子党务报告
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/11/26 16:00
     */
    @HttpMonitorLogger
    @GetMapping("/getPerson")
    @ApiOperation("电子报表统计")
    public ResponseEntity<Result<?>> getPersonElectronicReport(
            @RequestHeader HttpHeaders headers,
             @RequestParam(value = "user_id") Long userId,
             @RequestParam(value = "query_time") String queryTime) {
        try {
            HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
            Long regionId = sysHeader.getRegionId();
            PersonElectronicReport personElectronicReport =
                    electronicReportServices.getPersonElectronicReport(regionId,userId, queryTime);
            Result<PersonElectronicReport> result = new Result<>(personElectronicReport, errors);
            return new ResponseEntity<>(result, HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ApiException("没有查询信息", new Result(errors, 9404, HttpStatus.OK.value(),"电子党务信息"));
        }
    }


}
