package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.overview.PpmdStaDetailsVo;
import com.goodsogood.ows.service.overview.OverviewService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserService;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

@RestController
@RequestMapping("/overview")
@Log4j2
public class OverviewController {

    private final OrgService orgService;
    private final UserService userService;
    private final OverviewService overviewService;
    private final Errors errors;


    public OverviewController(OrgService orgService, UserService userService,
                              OverviewService overviewService, Errors errors) {
        this.orgService = orgService;
        this.userService = userService;
        this.overviewService = overviewService;
        this.errors = errors;
    }


    /**
     * 组织生活概况-组织生活统计
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/org_life_overview/org_life")
    public ResponseEntity<Result<?>> orgLifeOverviewOrgLife(@RequestHeader HttpHeaders headers) {
        return overviewService.orgLifeOverviewOrgLife(headers);
    }


    /**
     * 组织生活概况-党费交纳统计
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/org_life_overview/ppmd")
    public ResponseEntity<Result<?>> orgLifeOverviewPpmd(@RequestHeader HttpHeaders headers) {
        return overviewService.orgLifeOverviewPpmd(headers);
    }

    /**
     * 组织生活概况-党费交纳统计 钻取详情
     * isPay 是否已交  0 未交  1已交
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/org_life_overview/ppmd/details")
    public ResponseEntity<Result<?>> orgLifeOverviewPpmdDetails(@RequestHeader HttpHeaders headers,
                                                                @RequestParam(value = "is_pay", required = false) Integer isPay,
                                                                @RequestParam(value = "page_no", required = false) Integer pageNo,
                                                                @RequestParam(value = "page_size", required = false) Integer pageSize) {
        if (pageNo == null) {
            pageNo = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        Page<PpmdStaDetailsVo> list = overviewService.orgLifeOverviewPpmdDetails(headers, isPay, pageNo, pageSize);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }


    /**
     * 组织生活概况-开展活动完成情况
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/org_life_overview/activity")
    public ResponseEntity<Result<?>> orgLifeOverviewActivity(@RequestHeader HttpHeaders headers) {
        return overviewService.orgLifeOverviewActivity(headers);
    }

    /**
     * 组织生活概况-开展活动完成情况-新
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/org_life_overview/activity-new1")
    public ResponseEntity<Result<?>> orgLifeOverviewActivityNew1(@RequestHeader HttpHeaders headers,
                                                                 @RequestParam(value = "org_id", required = false) Long orgId) {
        return overviewService.orgLifeOverviewActivityNew1(headers, orgId);
    }


    /**
     * 组织生活概况-开展活动完成细项
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/org_life_overview/activity-detail")
    public ResponseEntity<Result<?>> orgLifeOverviewActivityDetail(@RequestHeader HttpHeaders headers, Integer type,
                                                                   @RequestParam(value = "org_id", required = false) Long orgId,
                                                                   @RequestParam(value = "page_num", required = false, defaultValue = "1") Integer pageNum,
                                                                   @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize) {
        return overviewService.orgLifeOverviewActivityDetail(headers, type, orgId, pageNum, pageSize);
    }


    /**
     * 组织生活概况-开展活动完成细项
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/org_life_overview/activity-detail-all")
    public ResponseEntity<Result<?>> orgLifeOverviewActivityDetailAll(@RequestHeader HttpHeaders headers, Integer type,
                                                                      @RequestParam(value = "org_id", required = false) Long orgId,
                                                                      @RequestParam(value = "page_num", required = false, defaultValue = "1") Integer pageNum,
                                                                      @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize) {
        return overviewService.orgLifeOverviewActivityDetailAll(headers, type, orgId, pageNum, pageSize);
    }


    /**
     * 组织生活概况-民主评议统计
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/org_life_overview/democratic_review")
    public ResponseEntity<Result<?>> orgLifeOverviewDemocraticReview(@RequestHeader HttpHeaders headers) {
        return overviewService.orgLifeOverviewDemocraticReview(headers);
    }


    /**
     * 组织生活概况-民主评议统计细项
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/org_life_overview/democratic_review_type")
    public ResponseEntity<Result<?>> orgLifeOverviewDemocraticReviewType(@RequestHeader HttpHeaders headers,
                                                                         @RequestParam("type") Integer type,
                                                                         @RequestParam("year") Integer year,
                                                                         @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
                                                                         @RequestParam(value = "page_num", required = false, defaultValue = "1") Integer pageNum) {
        return overviewService.orgLifeOverviewDemocraticReviewType(headers, type, year, pageSize, pageNum);
    }


    /**
     * 组织概况-组织信息
     * 全市 、（党委与党总支）、（党小组与党支部）显示不一样数据结构
     * 一共有三种格式
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/org")
    public ResponseEntity<Result<?>> orgOverviewOrg(@RequestHeader HttpHeaders headers) {
        return overviewService.orgOverviewOrg(headers);
    }

    /**
     * 组织概况-组织信息-详情列表
     * 全市 、（党委与党总支）、（党小组与党支部）显示不一样数据结构
     * 一共有三种格式
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/org_detail")
    public ResponseEntity<Result<?>> orgOverviewOrgDetail(@RequestHeader HttpHeaders headers,
                                                          @RequestParam(value = "alias_name") String aliasName,
                                                          @ApiParam(value = "页码")
                                                          @RequestParam(value = "page_no", required = false, defaultValue = "1")
                                                          Integer pageNo,
                                                          @ApiParam(value = "每页条数")
                                                          @RequestParam(value = "page_size", required = false, defaultValue = "10")
                                                          Integer pageSize) throws UnsupportedEncodingException {
        String decodeName = URLDecoder.decode(aliasName, "UTF-8");
        return overviewService.orgOverviewOrgDetail(headers, decodeName, pageNo, pageSize);
    }


    /**
     * 组织概况-换届情况
     * 全市 与（党委与党总支）显示一校准、（党小组与党支部）显示一样
     * 一共有两种格式
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/period")
    public ResponseEntity<Result<?>> orgOverviewPeriod(@RequestHeader HttpHeaders headers) {
        return overviewService.orgOverviewPeriod(headers);
    }

    /**
     * 组织概况-换届情况详情
     * 全市 与（党委与党总支）显示一校准、（党小组与党支部）显示一样
     * 一共有两种格式
     * 统计类型 1.本月应换届 2.6月内应换届 3.超期未换届
     * 组织类型 1.党委 2.党总支 3.党支部
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/period_detail")
    public ResponseEntity<Result<?>> orgOverviewPeriodDetail(@RequestHeader HttpHeaders headers,
                                                             @RequestParam(value = "type") Integer type,
                                                             @RequestParam(value = "org_type") Integer orgType,
                                                             @RequestParam(value = "page_no", required = false, defaultValue = "1")
                                                             Integer pageNo,
                                                             @ApiParam(value = "每页条数")
                                                             @RequestParam(value = "page_size", required = false, defaultValue = "10")
                                                             Integer pageSize) {
        return overviewService.orgOverviewPeriodDetail(headers, type, orgType, pageNo, pageSize);
    }

    /**
     * 组织概况-人员统计
     * 显示一样
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/user_sta")
    public ResponseEntity<Result<?>> orgOverviewUserSta(@RequestHeader HttpHeaders headers) {
        return overviewService.orgOverviewUserSta(headers);
    }

    /**
     * 组织概况-人员统计_详情
     * 显示一样
     * type:1 正式党员 2.预备党员 3.男党员 4.女党员 5.党员 6 非党员 7.未知性别党员
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/user_sta_detail")
    public ResponseEntity<Result<?>> orgOverviewUserSta(@RequestHeader HttpHeaders headers,
                                                        @RequestParam(value = "type") Integer type,
                                                        @RequestParam(value = "page_no", required = false, defaultValue = "1")
                                                        Integer pageNo,
                                                        @ApiParam(value = "每页条数")
                                                        @RequestParam(value = "page_size", required = false, defaultValue = "10")
                                                        Integer pageSize) {
        return overviewService.orgOverviewUserStaDetail(headers, type, pageNo, pageSize);
    }

    /**
     * 组织概况-学历分布
     * 显示一样
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/education")
    public ResponseEntity<Result<?>> orgOverviewEducation(@RequestHeader HttpHeaders headers) {
        return overviewService.orgOverviewEducation(headers);
    }


    /**
     * 组织概况-学历分布
     * 显示一样
     * type:1 研究生及以上 2.本科 3.专科 4高中生及以下 5.未知
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/education_detail")
    public ResponseEntity<Result<?>> orgOverviewEducationDetail(@RequestHeader HttpHeaders headers,
                                                                @RequestParam(value = "alias_name") String aliasName,
                                                                @RequestParam(value = "page_no", required = false, defaultValue = "1")
                                                                Integer pageNo,
                                                                @ApiParam(value = "每页条数")
                                                                @RequestParam(value = "page_size",
                                                                        required = false, defaultValue = "10")
                                                                Integer pageSize) throws UnsupportedEncodingException {
        String decodeName = URLDecoder.decode(aliasName, "UTF-8");
        return overviewService.orgOverviewEducationDetail(headers, decodeName, pageNo, pageSize);
    }


    /**
     * 组织概况-党务公开
     * 显示一样
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/party_affairs")
    public ResponseEntity<Result<?>> orgOverviewPartyAffairs(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return overviewService.orgOverviewPartyAffairs(headers, sysHeader);
    }


    /**
     * 组织概况-组织关系转接情况
     * 显示一样
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/org_relationship")
    public ResponseEntity<Result<?>> orgOverviewOrgRelationship(@RequestHeader HttpHeaders headers) {
        return overviewService.orgOverviewOrgRelationship(headers);
    }

    /**
     * 组织概况-组织关系转接情况详情
     * 显示一样
     * sta_type=1 本月
     * sta_type=2 本年
     * <p>
     * type:1 转入 2，转出
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/org_relationship_detail")
    public ResponseEntity<Result<?>> orgOverviewOrgRelationshipDetail(@RequestHeader HttpHeaders headers,
                                                                      @RequestParam(value = "sta_type") Integer staType,
                                                                      @RequestParam(value = "type") Integer type,
                                                                      @RequestParam(value = "page_no", required = false,
                                                                              defaultValue = "1") Integer pageNo,
                                                                      @ApiParam(value = "每页条数")
                                                                      @RequestParam(value = "page_size", required = false,
                                                                              defaultValue = "10") Integer pageSize) {
        return overviewService.orgOverviewOrgRelationshipDetail(headers, staType, type, pageNo, pageSize);
    }

    /**
     * 组织概况-年龄分布
     * 显示一样
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/age_distributed")
    public ResponseEntity<Result<?>> orgOverviewAgeDistributed(@RequestHeader HttpHeaders headers) {
        return overviewService.orgOverviewAgeDistributed(headers);
    }

    /**
     * 组织概况-年龄分布详情
     * 显示一样
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/age_distributed_detail")
    public ResponseEntity<Result<?>> orgOverviewAgeDistributedDetail(@RequestHeader HttpHeaders headers,
                                                                     @RequestParam(value = "alias_name") String aliasName,
                                                                     @RequestParam(value = "page_no", required = false,
                                                                             defaultValue = "1") Integer pageNo,
                                                                     @ApiParam(value = "每页条数")
                                                                     @RequestParam(value = "page_size", required = false,
                                                                             defaultValue = "10") Integer pageSize)
            throws UnsupportedEncodingException {
        String decodeName = URLDecoder.decode(aliasName, "UTF-8");
        return overviewService.orgOverviewAgeDistributedDetail(headers, decodeName, pageNo, pageSize);
    }


    /**
     * 组织概况-党龄分布
     * 显示一样
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/party_age_distributed")
    public ResponseEntity<Result<?>> orgOverviewPartyAgeDistributed(@RequestHeader HttpHeaders headers) {
        return overviewService.orgOverviewPartyAgeDistributed(headers);
    }


    /**
     * 组织概况-党龄分布
     * 显示一样
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/party_age_distributed_detail")
    public ResponseEntity<Result<?>> orgOverviewPartyAgeDistributedDetail(@RequestHeader HttpHeaders headers,
                                                                          @RequestParam(value = "alias_name") String aliasName,
                                                                          @RequestParam(value = "page_no", required = false,
                                                                                  defaultValue = "1") Integer pageNo,
                                                                          @ApiParam(value = "每页条数")
                                                                          @RequestParam(value = "page_size", required = false,
                                                                                  defaultValue = "10") Integer pageSize)
            throws UnsupportedEncodingException {
        String decodeName = URLDecoder.decode(aliasName, "UTF-8");
        return overviewService.orgOverviewPartyAgeDistributedDetail(headers, decodeName, pageNo, pageSize);
    }

    /**
     * 组织概况-发展党员情况
     * 显示一样
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/develop_party_members")
    public ResponseEntity<Result<?>> orgOverviewDevelopPartyMembers(@RequestHeader HttpHeaders headers) {
        return overviewService.orgOverviewDevelopPartyMembers(headers);
    }

    /**
     * 组织概况-重温入党誓词概况与志愿书
     * 显示一样
     * type=1 入党誓词
     * type=2 入党志愿书
     * <p>
     * 1.入党誓词浏览 2.入党志愿书浏览 3.入党誓词填写 4.入党志愿书填写
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/revisit_block")
    public ResponseEntity<Result<?>> orgOverviewRevisit(@RequestHeader HttpHeaders headers,
                                                        @RequestParam(value = "type") Integer type) {
        return overviewService.orgOverviewRevisit(headers, type);
    }


    /**
     * 我的支部- 志愿书与入党誓词统计
     * type=1 入党誓词
     * type=2 入党志愿书
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/my_branch_revisit")
    public ResponseEntity<Result<?>> myBranchRevisit(@RequestHeader HttpHeaders headers,
                                                     @RequestParam(value = "type") Integer type,
                                                     @RequestParam(value = "org_id") Long orgId) {
        return overviewService.myBranchRevisit(headers, type, orgId);
    }

    /**
     * 组织概况-重温入党誓词概况与志愿书 按天拆线图
     * 显示一样
     * type=1 入党誓词
     * type=2 入党志愿书
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/revisit_stitching")
    public ResponseEntity<Result<?>> orgOverviewStitching(@RequestHeader HttpHeaders headers,
                                                          @RequestParam(value = "type") Integer type) {
        return overviewService.orgOverviewStitching(headers, type);
    }


    /**
     * 组织概况-重温入党誓词概况与志愿书 按年拆线图
     * 显示一样
     * type=1 入党誓词
     * type=2 入党志愿书
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/revisit_year_stitching")
    public ResponseEntity<Result<?>> orgOverviewYearStitching(@RequestHeader HttpHeaders headers,
                                                              @RequestParam(value = "type") Integer type) {
        return overviewService.orgOverviewYearStitching(headers, type);
    }


    /**
     * 党员重温排行
     * 显示一样
     * type=1 入党誓词 type=2 入党志愿书
     * sta_data 1.月度 2.季度 3.年度
     * sta_type 1.支部 2.单位 3.全市
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/revisit_user_rank")
    public ResponseEntity<Result<?>> revisitUserRank(@RequestHeader HttpHeaders headers,
                                                     @RequestParam(value = "type") Integer type,
                                                     @RequestParam(value = "sta_data") Integer staData,
                                                     @RequestParam(value = "sta_type") Integer staType) {
        return overviewService.revisitUserRank(headers, type, staData, staType);
    }


    /**
     * 单个党员重温排行
     * 显示一样
     * type=1 入党誓词 type=2 入党志愿书
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/single_user_sta")
    public ResponseEntity<Result<?>> singleUserSta(@RequestHeader HttpHeaders headers,
                                                   @RequestParam(value = "type") Integer type) {
        return overviewService.singleUserSta(headers, type);
    }

    /**
     * 党员重温排行 导出接口
     * 显示一样
     * type=1 入党誓词 type=2 入党志愿书
     * sta_data 1.月度 2.季度 3.年度
     * sta_type 1.支部 2.单位 3.全市
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/revisit_user_rank/report")
    public void revisitUserRankReport(@RequestHeader HttpHeaders headers,
                                      HttpServletResponse httpServletResponse,
                                      @RequestParam(value = "type") Integer type,
                                      @RequestParam(value = "sta_data") Integer staData,
                                      @RequestParam(value = "sta_type") Integer staType) {
        overviewService.revisitUserRankReport(headers, httpServletResponse, type, staData, staType);
    }


    /**
     * 组织重温排行
     * 显示一样
     * type=1 入党誓词 type=2 入党志愿书
     * sta_data 1.月度 2.季度 3.年度
     * sta_type 1.支部 2.单位
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/revisit_org_rank")
    public ResponseEntity<Result<?>> revisitOrgRank(@RequestHeader HttpHeaders headers,
                                                    @RequestParam(value = "type") Integer type,
                                                    @RequestParam(value = "sta_data") Integer staData,
                                                    @RequestParam(value = "sta_type") Integer staType) {
        return overviewService.revisitOrgRank(headers, type, staData, staType);
    }

    /**
     * 组织重温排行导出
     * 显示一样
     * type=1 入党誓词 type=2 入党志愿书
     * sta_data 1.月度 2.季度 3.年度
     * sta_type 1.支部 2.单位
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/revisit_org_rank/report")
    public void revisitOrgRankReport(@RequestHeader HttpHeaders headers,
                                     HttpServletResponse httpServletResponse,
                                     @RequestParam(value = "type") Integer type,
                                     @RequestParam(value = "sta_data") Integer staData,
                                     @RequestParam(value = "sta_type") Integer staType) {
        overviewService.revisitOrgRankReport(headers, httpServletResponse, type, staData, staType);
    }


    /**
     * * 补录数据
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/supplementary_record")
    public ResponseEntity<Result<?>> supplementaryRecord(@RequestHeader HttpHeaders headers) {
        return overviewService.supplementaryRecord(headers);
    }

    /**
     * * 党建品牌
     */
    @HttpMonitorLogger
    @GetMapping("/org_overview/brand")
    public ResponseEntity<Result<?>> brand(@RequestHeader HttpHeaders headers) {
        return overviewService.brand(headers);
    }

    @HttpMonitorLogger
    @GetMapping("/sequence_distributed")
    public ResponseEntity<Result<?>> sequenceDistributed(@RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(this.overviewService.sequenceDistributed(headers), errors), HttpStatus.OK);
    }

}
