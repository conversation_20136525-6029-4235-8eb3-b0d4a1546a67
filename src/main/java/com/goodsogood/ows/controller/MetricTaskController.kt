package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.aidangqun.log4j2cm.aop.HttpLogAspect
import com.goodsogood.ows.common.Constants
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.model.vo.eval.v2.MetricTaskBean
import com.goodsogood.ows.service.eval.MetricService
import com.goodsogood.ows.service.eval.v2.MetricTaskService
import com.goodsogood.ows.service.eval.v2.Strategy
import com.goodsogood.ows.service.sas.OpenService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.time.LocalDateTime
import java.util.*

/**
 * <AUTHOR>
 * @date 2023/11/29
 * @description class 考核2.0 指标计算任务调度
 */
@RestController
@CrossOrigin(originPatterns = ["*"], maxAge = 3600)
@Api(value = "考核v2.0消息控制层", tags = ["考核2.0"])
@RequestMapping("/eval/v2/metric")
class MetricTaskController(
    val errors: Errors,
    val metricTaskService: MetricTaskService,
    val metricService: MetricService,
    val openService: OpenService,
    val redisTemplate: StringRedisTemplate
) {
    @HttpMonitorLogger
    @GetMapping("/task")
    @ApiOperation("考核v2.0任务调用")
    fun task(
        @RequestParam(required = false) year: Int?,
        @RequestParam(required = false, name = "task_id") taskId: String?,
        @RequestParam(name = "unit_id", required = false) unitId: Long?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val y = year ?: LocalDateTime.now().year
        val task = taskId ?: UUID.randomUUID().toString().replace("-", "")
        metricTaskService.computeMetric(year = y, headers = headers, id = task, unitId = unitId)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                "考核2.0指标计算任务调度成功:task => $task",
                errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/task_status")
    @ApiOperation("考核v2.0任务状态")
    fun taskStatus(
        @RequestParam(name = "task_id") taskId: String?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val msg: String
        val status =
            redisTemplate.opsForHash<String, String>().entries("${Constants.METRIC_TASK_STATUS_CACHE_KEY}$taskId")
        msg = if (status.isNullOrEmpty()) {
            "没有这个任务id"
        } else {
            val inProgress = status.filter { it.value == MetricTaskBean.TaskStatus.InProgress.value.toString() }
            val canceled = status.filter { it.value == MetricTaskBean.TaskStatus.Canceled.value.toString() }
            val notStarted = status.filter { it.value == MetricTaskBean.TaskStatus.NotStarted.value.toString() }
            val completed = status.filter { it.value == MetricTaskBean.TaskStatus.Completed.value.toString() }
            "任务状态->${status.size},未开始->${notStarted.size},进行中->${inProgress.size},已完成->${completed.size},已取消->${canceled.size}"
        }
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                "考核2.0指标计算任务调度状态:status => $msg",
                errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/call/{year}/{metricId}/{unitId}/{orgId}")
    @ApiOperation("考核v2.0任务调用")
    fun callStrategy(
        @PathVariable year: Int,
        @PathVariable metricId: Long,
        @PathVariable unitId: Long,
        @PathVariable orgId: Long,
        @RequestParam(required = false, name = "all") all: Boolean? = false,
        @RequestHeader headers: HttpHeaders
    ): Any {
        // -1 代表策略没找到
        val metric = metricService.selectByPrimaryKey(metricId) ?: return -1.0
        // -2 代表测没找到
        val strategy = Strategy.createStrategy(metric.clzName ?: "metric_${year}_${metric.metricClassId}_${metric.id}")
            ?: return -2.0
        // -3 代表单位没找到
        val unit = openService.findOrgByIds(listOf(unitId), headers)?.get(0) ?: return -3.0
        // -4 代表组织没找到
        val org = openService.findOrgByIds(listOf(orgId), headers)?.get(0) ?: return -4.0
        if (all != true) {
            return strategy.compute(
                org,
                unit,
                metric,
                year,
                HttpLogAspect.getSSLog()?.trackerId ?: "",
                mapOf()
            )
        } else {
            // 获取当前单位的所有下级组织 含 党小组
            val allOrg = metricTaskService.getAllOrgs(unit.organizationId, org, headers)
            val data: MutableList<Pair<String, Double>> = mutableListOf()
            val total = allOrg.sumOf { o ->
                val s = strategy.compute(
                    o,
                    unit,
                    metric,
                    year,
                    HttpLogAspect.getSSLog()?.trackerId ?: "",
                    mapOf()
                )
                data.add(o.organizationId.toString() to s)
                s
            }
            data.add("总分" to total)
            return data
        }
    }
}