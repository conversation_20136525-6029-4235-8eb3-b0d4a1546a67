package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.configuration.Global
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.model.antd.AntDesignBase
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.model.vo.ql.Column
import com.goodsogood.ows.model.vo.ql.QueryForm
import com.goodsogood.ows.model.vo.ql.QueryForm.Companion.checkValues
import com.goodsogood.ows.model.vo.ql.QueryForm.Companion.createCondition
import com.goodsogood.ows.service.adb.QLService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.*
import java.util.*

/**
 * <AUTHOR>
 * @date 2023/9/26
 * @description class QLController
 */
@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "轻流数据获取接口", tags = ["轻流数据获取接口"])
@RequestMapping("/ql")
class QLController(val qlService: QLService, val errors: Errors) {
    // logger
    private var log = LoggerFactory.getLogger(this.javaClass)

    @HttpMonitorLogger
    @PostMapping("/list")
    @ApiOperation("获取轻流表数据")
    fun listQLQuery(
        @RequestParam(name = "query") query: String,
        @RequestParam(name = "page", required = false, defaultValue = "1") page: Int?,
        @RequestParam(name = "page_size", required = false, defaultValue = "30") pageSize: Int?,
        @RequestBody params: List<QueryForm>,
        bandingResult: BindingResult,
    ): ResponseEntity<Result<List<SortedMap<String, Any?>>>> {
        // 对params进行验证，并处理成map
        val listParam = mutableListOf<Map<String, Any>>()
        params.filter { it.param != null && it.values != null }.forEach {
            if (!it.checkValues()) {
                throw ApiException(
                    "params: $params 格式错误",
                    Result<Any>(
                        errors,
                        4760,
                        HttpStatus.OK.value(),
                        it.type ?: "未知",
                        it.values?.joinToString() ?: "未知"
                    )
                )
            }
            it.createCondition().let { map ->
                if (map.isNotEmpty()) {
                    listParam.add(map)
                }
            }
        }
//        log.debug("params->{} ,listParam->{}", params, listParam)
        val data: Page<SortedMap<String, Any?>> = qlService.findAll(
            query, listParam, PageRequest.of(
                if (page == null) 0 else page - 1,
                pageSize ?: 30
            )
        )
        val result = Result<List<SortedMap<String, Any?>>>(data.content, errors)
        result.pageSize = data.size
        result.pageNum = data.number + 1
        result.total = data.totalElements
        return ResponseEntity(result, HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/column")
    @ApiOperation("获取轻流表头")
    fun listQLColumn(
        @RequestParam(name = "query") query: String,
    ): ResponseEntity<Result<List<Column>>> {
        return ResponseEntity(Result(qlService.getColumn(query), errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/query-column")
    @ApiOperation("获取轻流查询头")
    fun listQLQueryColumn(
        @RequestParam(name = "query") query: String,
    ): ResponseEntity<Result<List<AntDesignBase>>> {
        return ResponseEntity(Result(qlService.getQueryColumn(query), errors), HttpStatus.OK)
    }
}