package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.service.MessageService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import com.goodsogood.ows.model.vo.Result

@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "移动端首页消息控制层", tags = ["移动端首页消息"])
@RequestMapping("/message")
@Validated class MessageController(@Autowired val errors: Errors,
                        @Autowired val messageService: MessageService) {

    @HttpMonitorLogger
    @GetMapping("/index")
    @ApiOperation("移动端首页消息")
    fun message(
        @RequestParam(defaultValue = "1") page: Int,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.messageService.message(page, header), errors
            ), HttpStatus.OK
        )
    }
}