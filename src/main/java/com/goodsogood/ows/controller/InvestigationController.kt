package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.investigation.InvestigationService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "调查研究")
@RequestMapping("/investigation")
class InvestigationController (@Autowired val errors: Errors,
    @Autowired val investigationService: InvestigationService){
    @HttpMonitorLogger
    @GetMapping("/survey-statistics")
    @ApiOperation("调研统计")
    fun surveyStatistics(
        @RequestHeader headers: HttpHeaders,
        @RequestParam(value = "leader_name", required = false) leader: String?,
        @RequestParam(value = "department", required = false) department: String?,
        @RequestParam(value = "interview", required = false, ) interview: Int?,
        @RequestParam(value = "year", required = false)year:Int?
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                investigationService.surveyStatistics(year, leader, department, interview), errors
            ), HttpStatus.OK
        )
    }


}