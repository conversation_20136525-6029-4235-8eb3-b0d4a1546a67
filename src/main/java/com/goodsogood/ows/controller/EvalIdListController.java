package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.sas.EvalIdForm;
import com.goodsogood.ows.model.vo.sas.EvalIdRequestForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.sas.EvalIdListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 考核系统 查询组织信息或领导班子信息
 *
 * <AUTHOR>
 * @create 2019-04-22 16:04
 **/
@RestController
@RequestMapping("/sas/eval-id-list")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "查询组织信息或领导班子信息", tags = {"考核系统 查询组织信息或领导班子信息"})
@Validated
public class EvalIdListController {

    private final Errors errors;
    private final EvalIdListService evalIdListService;


    @Autowired
    public EvalIdListController(
            Errors errors,
            EvalIdListService evalIdListService) {
        this.errors = errors;
        this.evalIdListService = evalIdListService;
    }

    /**
     * 查询考核列表
     *
     * @param type  类型 1 领导班子，7 党费，其它 组织生活
     */

    @HttpMonitorLogger
    @PostMapping("/{type}")
    @ApiOperation("查询组织")
    public ResponseEntity<Result<List<EvalIdForm>>> listByType(
            @Valid @RequestBody EvalIdRequestForm form,
            BindingResult bindingResult, @PathVariable("type") Integer type) {
        return new ResponseEntity<>(new Result<>(evalIdListService.findList(form, type), errors), HttpStatus.OK);
    }
}
