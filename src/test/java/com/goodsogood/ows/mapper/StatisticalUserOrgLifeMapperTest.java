package com.goodsogood.ows.mapper;

import com.goodsogood.ows.mapper.sas.StatisticalUserOrgLifeMapper;
import com.goodsogood.ows.service.sas.StasticConfigInfoService;
import lombok.extern.log4j.Log4j2;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class StatisticalUserOrgLifeMapperTest {
    @Autowired
    StatisticalUserOrgLifeMapper statisticalUserOrgLifeMapper;
    @Autowired
    StasticConfigInfoService stasticConfigInfoService;

    @Test
    public void findList() {
//        List<String> months = new ArrayList<>();
//        months.add("1");
//        months.add("2");
//        months.add("3");
//        // 党支部组织生活统计配置
//        StasticConfigInfoEntity stasticConfigInfoEntity =
//                stasticConfigInfoService.getConfig(Constants.ORG_LIFE_TYPE,3L);
//        statisticalUserOrgLifeMapper.findListAll(3L,
//                stasticConfigInfoEntity.getShowOrganizationTypes(),
//                1,
//                2,
//                DateTime.now().toDate(),
//                DateTime.now().toDate(),
//                2019,
//                months,
//                stasticConfigInfoEntity.getShowActivityTypes(),
//                3L
//        );
    }
}