package com.goodsogood.ows.mapper;

import com.goodsogood.ows.configuration.ExperienceConfig;
import com.goodsogood.ows.mapper.experience.UserPartyEvalDetailMapper;
import com.goodsogood.ows.mapper.experience.UserPartyEvalPlanMapper;
import com.goodsogood.ows.mapper.user.UserLoginLogMapper;
import com.goodsogood.ows.model.vo.operation.VisitDaysVo;
import com.goodsogood.ows.service.operation.DataSourceService;
import com.goodsogood.ows.service.user.UserLoginService;
import com.goodsogood.ows.utils.NumberUtils;
import lombok.extern.log4j.Log4j2;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022-03-16 18:15:27
 * @Description LoginMapperTest
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class LoginMapperTest {

    @Autowired
    UserLoginLogMapper userLoginLogMapper;
    @Autowired
    ExperienceConfig experienceConfig;
    @Autowired
    UserPartyEvalPlanMapper userPartyEvalPlanMapper;
    @Autowired
    UserPartyEvalDetailMapper userPartyEvalDetailMapper;
    @Autowired
    UserLoginService userLoginService;

    @Autowired
    DataSourceService dataSourceService;

    @Test
    public void testLogin() {

/*        UserPartyEvalDetailEntity w = new UserPartyEvalDetailEntity();
        w.setDetailId(1116040L);
        w.setRegionId(19L);
        w.setRuleId(29);
        w.setUserId(6964L);
        w.setDateMonth(202111);
        w.setCreateTime(LocalDateTime.now());
        w.setTaskId("57a215eb03ea45d7b4c8c326441a3897");
        String queryMonth = "2021-11";*/

        /*Integer totalTimes = this.userLoginLogMapper.findTotalByLoginId(w.getUserId(), queryMonth);
        // 设置星级
        List<ExperienceConfig.StarLevel> starList = this.experienceConfig.getStarLevels();
        // 默认1星
        AtomicReference<Integer> starLevel = new AtomicReference<>(1);
        starList.stream().filter(s -> totalTimes >= s.getMin() && totalTimes <= s.getMax()).forEach(s -> {
            starLevel.set(s.getStar());
        });
        w.setStar(starLevel.get());
        // 如果没有达到5星，则设置锤炼计划
        if (!this.experienceConfig.getMaxStar().equals(starLevel.get())) {
            Example example = new Example(UserPartyEvalPlanEntity.class);
            example.createCriteria().andEqualTo("regionId", 19)
                    .andEqualTo("ruleId", 27);
            UserPartyEvalPlanEntity plan = this.userPartyEvalPlanMapper.selectOneByExample(example);
            if (Objects.nonNull(plan)) {
                w.setPlanId(plan.getPlanId());
            }
        }
        // 设置建议
        w.setParamType(Constants.PARAM_TYPE_SEVEN);
        // 设置建议参数
        w.setParamValue(String.valueOf(totalTimes));
        // 设置状态为完成
        w.setCalStatus(Constants.CAL_STATUS_DONE);
        w.setUpdateTime(LocalDateTime.now());
        this.userPartyEvalDetailMapper.updateByPrimaryKey(w);*/

        // 查询当月连续登录次数
        /*Integer maxDays = this.userLoginLogMapper.findMaxByLoginId(w.getUserId(), queryMonth);
        // 默认1星
        AtomicReference<Integer> star = new AtomicReference<>(1);
        if (Objects.isNull(maxDays)) {
            maxDays = 0;
        } else if (maxDays >= this.experienceConfig.getMaxDay()) {
            star.set(this.experienceConfig.getMaxStar());
        }
        List<ExperienceConfig.StarLevel> starLevalList = this.experienceConfig.getStarLevels();
        Integer finalMaxDays = maxDays;
        starLevalList.forEach(days -> {
            if (days.getDays().contains(finalMaxDays)) {
                star.set(days.getStar());
            }
        });
        // 设置星级
        w.setStar(star.get());
        // 如果没有达到5星，则设置锤炼计划
        if (!this.experienceConfig.getMaxStar().equals(star.get())) {
            Example example = new Example(UserPartyEvalPlanEntity.class);
            example.createCriteria().andEqualTo("regionId", 19)
                    .andEqualTo("ruleId", 26);
            UserPartyEvalPlanEntity plan = this.userPartyEvalPlanMapper.selectOneByExample(example);
            if (Objects.nonNull(plan)) {
                w.setPlanId(plan.getPlanId());
            }
        }
        // 设置建议
        w.setParamType(Constants.PARAM_TYPE_EIGHT);
        // 查询建议参数
        w.setParamValue(this.userLoginLogMapper.findParamValue(w.getUserId(), queryMonth, this.experienceConfig.getStartTime(), this.experienceConfig.getEndTime()));
        // 设置状态为完成
        w.setCalStatus(Constants.CAL_STATUS_DONE);
        w.setUpdateTime(LocalDateTime.now());
        this.userPartyEvalDetailMapper.updateByPrimaryKey(w);*/

        // 计算季度
        /*// 年
        Integer year = Integer.valueOf(w.getDateMonth().toString().substring(0, 4));
        // 月
        Integer month = Integer.valueOf(w.getDateMonth().toString().substring(4, 6));
        // 属于那个季度
        List<Integer> months = new ArrayList<>();
        ExperienceConfig.Quarter quarter = this.experienceConfig.getQuarters();
        if (quarter.getFirst().contains(month)) {
            months = quarter.getFirst();
        } else if (quarter.getSecond().contains(month)) {
            months = quarter.getSecond();
        } else if (quarter.getThird().contains(month)) {
            months = quarter.getThird();
        } else if (quarter.getFour().contains(month)) {
            months = quarter.getFour();
        }
        // 查询当季度登录天数
        Integer quearTerDays = this.userLoginLogMapper.findQuarterAndYearTotalByLoginId(w.getUserId(), year, months);
        // 设置星级
        List<ExperienceConfig.StarLevel> starList = this.experienceConfig.getStarLevels();
        // 默认1星
        AtomicReference<Integer> starLevel = new AtomicReference<>(1);
        starList.stream().filter(s -> quearTerDays >= s.getQMin() && quearTerDays <= s.getQMax()).forEach(s -> {
            starLevel.set(s.getStar());
        });
        w.setStar(starLevel.get());
        // 如果没有达到5星，则设置锤炼计划
        if (!this.experienceConfig.getMaxStar().equals(starLevel.get())) {
            Example example = new Example(UserPartyEvalPlanEntity.class);
            example.createCriteria().andEqualTo("regionId", 19)
                    .andEqualTo("ruleId", w.getRuleId());
            UserPartyEvalPlanEntity plan = this.userPartyEvalPlanMapper.selectOneByExample(example);
            if (Objects.nonNull(plan)) {
                w.setPlanId(plan.getPlanId());
            }
        }
        // 设置状态为完成
        w.setCalStatus(Constants.CAL_STATUS_DONE);
        w.setUpdateTime(LocalDateTime.now());
        this.userPartyEvalDetailMapper.updateByPrimaryKey(w);*/

        // 计算年度
        // 年
        /*Integer year = Integer.valueOf(w.getDateMonth().toString().substring(0, 4));
        // 查询当前年度登录天数
        Integer yearDays = this.userLoginLogMapper.findQuarterAndYearTotalByLoginId(w.getUserId(), year, null);
        // 设置星级
        List<ExperienceConfig.StarLevel> starList = this.experienceConfig.getStarLevels();
        // 默认1星
        AtomicReference<Integer> starLevel = new AtomicReference<>(1);
        starList.stream().filter(s -> yearDays >= s.getYMin() && yearDays <= s.getYMax()).forEach(s -> {
            starLevel.set(s.getStar());
        });
        w.setStar(starLevel.get());
        // 如果没有达到5星，则设置锤炼计划
        if (!this.experienceConfig.getMaxStar().equals(starLevel.get())) {
            Example example = new Example(UserPartyEvalPlanEntity.class);
            example.createCriteria().andEqualTo("regionId", 19)
                    .andEqualTo("ruleId", w.getRuleId());
            UserPartyEvalPlanEntity plan = this.userPartyEvalPlanMapper.selectOneByExample(example);
            if (Objects.nonNull(plan)) {
                w.setPlanId(plan.getPlanId());
            }
        }
        // 设置状态为完成
        w.setCalStatus(Constants.CAL_STATUS_DONE);
        w.setUpdateTime(LocalDateTime.now());
        this.userPartyEvalDetailMapper.updateByPrimaryKey(w);*/

        List<VisitDaysVo> visit = this.dataSourceService.findVisitDays(19L, "2022-01");
        System.out.println(visit);

//        this.dataSourceService.findVisitNumByInterval(3L,"2021-08-11",3);

    }

    public static void main(String[] args) {
        System.out.println(NumberUtils.divide(25, 36));
    }
}
