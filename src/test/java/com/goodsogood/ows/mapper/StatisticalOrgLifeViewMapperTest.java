package com.goodsogood.ows.mapper;

import com.goodsogood.ows.common.bean.TimeBean;
import com.goodsogood.ows.model.vo.sas.MeetingTypeForm;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class StatisticalOrgLifeViewMapperTest {

    @Test
    public void timeBean() {
        TimeBean timeBean = new TimeBean(null, null, null);
        log.debug(JsonUtils.toJson(timeBean));
//        Assert.assertEquals(2019, (int) timeBean.getYear());
//        Assert.assertEquals(4, (int) timeBean.getTimeType());
//        Assert.assertEquals(7, (int) timeBean.getTime());
    }

    @Test
    public void findList() {
        List<MeetingTypeForm> list = new ArrayList<>();
//        MeetingTypeForm meetingTypeForm1 = new MeetingTypeForm();
//        meetingTypeForm1.setTypeId(1L);
//        list.add(meetingTypeForm1);
//
//
//        MeetingTypeForm meetingTypeForm2 = new MeetingTypeForm();
//        meetingTypeForm2.setTypeId(2L);
//        list.add(meetingTypeForm2);
//
//
//        MeetingTypeForm meetingTypeForm3 = new MeetingTypeForm();
//        meetingTypeForm3.setTypeId(3L);
//        list.add(meetingTypeForm3);
//
//        MeetingTypeForm meetingTypeForm4 = new MeetingTypeForm();
//        meetingTypeForm4.setTypeId(4L);
//        list.add(meetingTypeForm4);

//        List<Map<String, Object>> result = statisticalOrgLifeViewMapper.findList(list, 2019, 1, 12);
//
//        log.debug("===============>{}", JsonUtils.toJson(result));
    }
}