package com.goodsogood.ows.service;

import com.goodsogood.ows.mapper.user.OrgGroupMapper;
import com.goodsogood.ows.model.mongodb.dss.DssUserChild;
import com.goodsogood.ows.utils.RateUtils;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

//@ExtendWith(SpringExtension.class)
//@SpringBootTest
//@ActiveProfiles("dev")
//@Log4j2
public class DoTest {

    @Autowired
    private OrgGroupMapper orgGroupMapper;

    @Test
    public void test() {
        /*int a = 1218;
        int b = 2100;
        int c = 1200;
        int d = 2200;
        Stream.of(a, b, c, d).forEach();*/
//        Stream.of("hello", "world").map(s -> s.split("")).forEach(System.out::println);
//        System.out.println("--------------");
//        Stream.of("hello", "world").flatMap(s -> Stream.of(s.split(""))).forEach(System.out::println);
        List<Long> list = new ArrayList<>();
        list.add(1L);
        System.out.println(list instanceof Collection<?>);

    }

    @Test
    public void test01() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", java.util.Locale.US);
       String date = "Fri Dec 11 19:59:35 CST 2020";
        Date parse1 = sdf.parse(date);
        System.out.println(parse1);
    }

    @Test
    public void test02() throws ParseException {
        String time = "2009-06-10";
        final SimpleDateFormat FORMAT = new SimpleDateFormat("yyyy-MM-dd");
        final SimpleDateFormat FORMAT_D = new SimpleDateFormat("yyyy年MM月dd日");
        String format = FORMAT_D.format(FORMAT.parse(time));
        System.out.println(format);
    }

    @Test
    public void test03() {
        List<DssUserChild> userChildes = new ArrayList<>(5);
        DssUserChild user1 = new DssUserChild();
        user1.setUserId(1L);
        user1.setName("哈哈");
        user1.setGrand(1.0);
        userChildes.add(user1);
        DssUserChild user2 = new DssUserChild();
        user2.setUserId(2L);
        user2.setName("哈哈2");
        user2.setGrand(2.0);
        userChildes.add(user2);
        DssUserChild user3 = new DssUserChild();
        user3.setUserId(3L);
        user3.setName("哈哈3");
        user3.setGrand(3.0);
        userChildes.add(user3);
        DssUserChild user4 = new DssUserChild();
        user4.setUserId(4L);
        user4.setName("哈哈3");
        user4.setGrand(4.0);
        userChildes.add(user4);
        DssUserChild user5 = null;
        /*user5.setUserId(5L);
        user5.setName("哈哈3");
        user5.setGrand(null);*/
        userChildes.add(user5);
        userChildes = userChildes.stream().filter(Objects::nonNull).sorted(Comparator.comparing(DssUserChild::getGrand, Comparator.nullsFirst(Double::compareTo).reversed())).collect(Collectors.toList());
        userChildes.forEach(System.out::println);
    }

}
