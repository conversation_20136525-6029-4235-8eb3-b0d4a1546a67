package com.goodsogood.ows.service;

import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.service.pbm.impl.PbmUserPartyOathServiceImpl;
import com.goodsogood.ows.service.user.DssPeriodAndLeaderService;
import com.goodsogood.ows.service.volunteer.VolunteerReportService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
//@ActiveProfiles("test")
public class VolunteerServiceTest {

    @Autowired
    private VolunteerReportService volunteerReportService;

    @Autowired
    private DssPeriodAndLeaderService dssPeriodAndLeaderService;

    @Autowired
    private PbmUserPartyOathServiceImpl pbmUserPartyOathService;


    @Test
    public void test01() {
//        assert info != null && info.getYear() != null && info.getRootId() != null && info.getRegionId() != null;
        Long regionId = 3L;
        IndexInfo indexInfo = new IndexInfo();
        indexInfo.setYear(2020);
        indexInfo.setRootId(regionId);
        indexInfo.setRegionId(3L);
        volunteerReportService.buildIndex(indexInfo);

        PartyCommitteeInfo partyCommitteeInfo = new PartyCommitteeInfo();
//        assert info != null && info.getRegionId() != null && info.getOrganizationId() != null;
        partyCommitteeInfo.setRegionId(regionId);
        partyCommitteeInfo.setOrganizationId(4685L);
        partyCommitteeInfo.setYear(2020);
//        volunteerReportService.buildPartyCommittee(partyCommitteeInfo);
    }

    @Test
    public void test2() {
//        assert info != null && info.getRegionId() != null && info.getOrganizationId() != null;
        PartyBranchInfo partyBranchInfo = new PartyBranchInfo();
        partyBranchInfo.setRegionId(3L);
        partyBranchInfo.setOrganizationId(3L);
        partyBranchInfo.setYear(2020);
//        dssPeriodAndLeaderService.buildPartyBranch(partyBranchInfo);
        PartyCommitteeInfo partyCommitteeInfo = new PartyCommitteeInfo();
        partyCommitteeInfo.setRegionId(3L);
//        partyCommitteeInfo.setOrganizationId(4685L);
        partyCommitteeInfo.setOrganizationId(3L);
        partyCommitteeInfo.setYear(2020);
        dssPeriodAndLeaderService.buildPartyCommittee(partyCommitteeInfo);
//        71735
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(71735L);
        userInfo.setRegionId(3L);
        userInfo.setYear(2020);
//        dssPeriodAndLeaderService.buildUser(userInfo);
    }

//    @Test
//    public void test03(){
//        long userId=7L;
//        String startTime="2022-05-01";
//        String endTime="2022-06-01";
//        long regionId=19L;
//        Object userWorkData = pbmUserPartyOathService.getUserWorkData(userId, startTime, endTime, regionId);
//        System.out.println(userWorkData);
//    }
}
