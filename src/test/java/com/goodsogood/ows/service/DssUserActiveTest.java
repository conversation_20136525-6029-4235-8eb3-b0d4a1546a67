package com.goodsogood.ows.service;

import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.model.db.user.UserEntity;
import com.goodsogood.ows.model.db.user.UserSnapshotEntity;
import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.dss.DssOrgChild;
import com.goodsogood.ows.model.mongodb.dss.PieObject;
import com.goodsogood.ows.model.vo.GroupValue;
import com.goodsogood.ows.service.cilckSpy.ClickSpyService;
import com.goodsogood.ows.service.dss.DssAsyncService;
import com.goodsogood.ows.service.dss.DssFileService;
import com.goodsogood.ows.service.dss.DssService;
import com.goodsogood.ows.service.user.DssUserService;
import com.goodsogood.ows.service.user.OrgService;
import com.goodsogood.ows.service.user.UserLoginService;
import com.goodsogood.ows.service.user.UserService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.RateUtils;
import lombok.extern.log4j.Log4j2;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class DssUserActiveTest {

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Autowired
    private UserLoginService userLoginService;

    @Autowired
    private DssService dssService;

    @Autowired
    private ClickSpyService clickSpyService;

    @Autowired
    private DssFileService dssFileService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private DssAsyncService dssAsyncService;

    @Autowired
    private MyMongoTemplate mongoTemplate;

    @Autowired
    private DssUserService dssUserService;

    @Autowired
    private UserService userService;

    @Test
    public void test() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date first = DateUtils.firstDayOfYear(2019);
        Date first1 = DateUtils.firstDayOfYear(2020);
        System.out.println(format.format(first));
        System.out.println(format.format(first1));
        Date end = DateUtils.endDayOfYear(2019);
        Date end2 = DateUtils.endDayOfYear(2020);
        System.out.println(format.format(end));
        System.out.println(format.format(end2));
    }

    @Test
    public void testDssIndex() {
        Long regionId = 3L;
        Integer year = 2020;
        this.dssService.buildIndex(regionId, year);
    }

    @Test
    public void testDssCommittee() {
        Long regionId = 3L;
        Integer year = 2020;
        this.dssService.buildCommittee(regionId, year);
    }

    @Test
    public void testDssBranch() {
        Long regionId = 3L;
        Integer year = 2020;
        this.dssService.buildBranch(regionId, year);
    }

    @Test
    public void testDssUser() {
        Long regionId = 3L;
        Integer year = 2020;
        this.dssService.buildUserInfo(regionId, year);
    }

    @Test
    public void testClickSpy() {
        List<GroupValue> userClickSpy = this.clickSpyService.getUserClickSpy(13201L, 2020);
        System.out.println(userClickSpy);
    }

    @Test
    public void testIndexActive() {
        IndexInfo info = new IndexInfo();
        info.setRegionId(3L);
        info.setRootId(3L);
        info.setYear(2019);
        IndexInfo indexInfo = this.userLoginService.buildIndex(info);
        System.out.println(indexInfo);
    }

    @Test
    public void testOrgActive() {
        PartyCommitteeInfo committeeInfo = new PartyCommitteeInfo();
        committeeInfo.setRegionId(3L);
        committeeInfo.setOrganizationId(4L);
        committeeInfo.setYear(2020);
        committeeInfo = this.userLoginService.buildPartyCommittee(committeeInfo);
        System.out.println(committeeInfo);
    }

    @Test
    public void testUserVisit() {
        List<PieObject> visitModule = this.userLoginService.getUserVisitModule(13201L, 2020);
        System.out.println(visitModule);
    }

    @Test
    public void testOrgVisit() {
        List<PieObject> visitModule = this.userLoginService.getOrgVisitModule(3L, 2020);
        System.out.println(visitModule);
    }

    @Test
    public void testRedis() {

        String uuid = RateUtils.build(3);
        String s = RateUtils.getRate(uuid, 2);
        System.out.println("第0次 -> " + s);
        RateUtils.auto(uuid);
        System.out.println("第1次 -> " + RateUtils.complete(uuid));
        RateUtils.auto(uuid);
        System.out.println("第2次 -> " + RateUtils.complete(uuid));
        RateUtils.auto(uuid);
        System.out.println("第3次 -> " + RateUtils.complete(uuid));
    }

    @Test
    public void testFile() {
        Integer[] year = {2020};
        this.dssFileService.buildFile(3L, year, 63511L, UUID.randomUUID().toString());
    }

    @Test
    public void testPartyCommitteeInfo() {

    }

    @Test
    public void testPartyBranchInfo() {

    }

    @Test
    public void testUserScore() {
        DssOrgChild dssOrgChild = this.orgService.buildOrgList(4L, 2020, true);
        System.out.println(dssOrgChild);
    }

    @Test
    public void testDssUserSer() {
        PartyBranchInfo info = new PartyBranchInfo();
        info.setOrganizationId(4686L);
        info.setYear(2020);
        info = this.dssUserService.buildPartyBranch(info);
        System.out.println(info);
    }

    @Test
    public void testDssOrgSer() {
        PartyCommitteeInfo info = new PartyCommitteeInfo();
        info.setOrganizationId(4L);
        info.setYear(2020);
        info = this.dssUserService.buildPartyCommittee(info);
        System.out.println(info);
    }

    @Test
    public void testDssUserOne() {
/*        LocalDateTime dateTime = LocalDateTime.now();
        String today = DATE_FORMAT.format(dateTime);
        int year = 2020;
        Long userId = 19L;
        Long regionId = 3L;
        if (LocalDateTime.now().getYear() > year) {
            UserSnapshotEntity userInfo = this.userService.getHistoryUserInfo(userId, year, null);
            this.dssAsyncService.saveUserInfo(userInfo.getUserId(), userInfo.getUserName(), userInfo.getPoliticalType(),
                    userInfo.getGender(), userInfo.getAge(), userInfo.getPosition(), userInfo.getProPartyTime(),
                    userInfo.getEducation(), userInfo.getBirthplace(), year, dateTime, regionId, today, null);
        } else {
            UserEntity info = this.userService.getUserInfo(userId);
            this.dssAsyncService.saveUserInfo(info.getUserId(), info.getName(), info.getPoliticalType(), info.getGender(),
                    info.getAge(), info.getPosition(), info.getJoiningTime(), info.getEducation(), info.getBirthPlace(),
                    year, dateTime, regionId, today, null);
        }*/
    }
}
