package com.goodsogood.ows.service.user; 
 
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import com.goodsogood.ows.model.vo.sas.PartyOrgVo;




import lombok.extern.log4j.Log4j2;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Before;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
 
/** 
* OrgService Tester. 
* 
* <AUTHOR> 
* @date 12/11/2020
* @version 1.0 
*/ 
@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class OrgServiceTest { 
    
    @Autowired
    OrgService orgService;

 
    /** 
    * 
    * Method: findAllChildOrg(Long orgId, Integer isInclude, Long regionId, boolean excludeRetire) 
    * 
    */ 
    @Test
    public void testFindAllChildOrg() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getById(Long orgId) 
    * 
    */ 
    @Test
    public void testGetById() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: findPartyId(long orgId) 
    * 
    */ 
    @Test
    public void testFindPartyId() throws Exception {
        PartyOrgVo partyId = orgService.findPartyId(667);
        System.out.println(partyId);
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: findAllChildOrgCount(Long orgId, Integer orgType, Integer orgTypeChild, Integer isInclude) 
    * 
    */ 
    @Test
    public void testFindAllChildOrgCount() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getOrgType(List<MeetingInfo> meetingInfoList) 
    * 
    */ 
    @Test
    public void testGetOrgTypeMeetingInfoList() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getChildPartyOrg(Long orgId, Integer isInclude) 
    * 
    */ 
    @Test
    public void testGetChildPartyOrg() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: findOrgByType(Long orgType, Long orgTypeChild, Long regionId) 
    * 
    */ 
    @Test
    public void testFindOrgByType() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: findUserCountByList(OrgUserCountQueryForm form) 
    * 
    */ 
    @Test
    public void testFindUserCountByList() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: findEvalOrgByOrgId(Long orgId, int type) 
    * 
    */ 
    @Test
    public void testFindEvalOrgByOrgId() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: findAllOrg(long orgId) 
    * 
    */ 
    @Test
    public void testFindAllOrg() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getOrgScore(Long orgId, String staDate) 
    * 
    */ 
    @Test
    public void testGetOrgScoreForOrgIdStaDate() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getOrgScoreList(List<Long> orgIds, Integer year) 
    * 
    */ 
    @Test
    public void testGetOrgScoreList() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getOfficeCommittee(Long rootId) 
    * 
    */ 
    @Test
    public void testGetOfficeCommitteeRootId() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getHistoryOfficeCommittee(Long rootId, String dateMonth) 
    * 
    */ 
    @Test
    public void testGetHistoryOfficeCommittee() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getPartyBranch(Long orgId, List<Long> orgIds, boolean excludeRetire) 
    * 
    */ 
    @Test
    public void testGetPartyBranchForOrgIdOrgIdsExcludeRetire() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getHistoryPartyBranch(Long orgId, Integer year, boolean excludeRetire, List<Long> orgIds) 
    * 
    */ 
    @Test
    public void testGetHistoryPartyBranch() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getCommunistAndGeneral(Long regionId, List<Long> orgIds) 
    * 
    */ 
    @Test
    public void testGetCommunistAndGeneral() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getHistoryCommunistAndGeneral(Long regionId, Integer year, List<Long> orgIds) 
    * 
    */ 
    @Test
    public void testGetHistoryCommunistAndGeneral() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getPartyBranch(Long orgId, Integer year, Integer month) 
    * 
    */ 
    @Test
    public void testGetPartyBranchForOrgIdYearMonth() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getOfficeCommittee(Long rootId, Integer year, Integer month) 
    * 
    */ 
    @Test
    public void testGetOfficeCommitteeForRootIdYearMonth() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getHistoryOrgInfo(Long orgId, Integer year, Integer month) 
    * 
    */ 
    @Test
    public void testGetHistoryOrgInfo() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getHistoryOrgParty(Long orgId, Integer year, Integer month, Long rootId) 
    * 
    */ 
    @Test
    public void testGetHistoryOrgParty() throws Exception {
        OrgSnapshotEntity historyOrgParty = this.orgService.getHistoryOrgParty(6L, 2019, null, 3L);
        System.out.println(historyOrgParty);
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getHistoryOrgChildInfo(Long orgId, Integer year, Integer month) 
    * 
    */ 
    @Test
    public void testGetHistoryOrgChildInfo() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getOrgType(Integer orgTypeChild) 
    * 
    */ 
    @Test
    public void testGetOrgTypeOrgTypeChild() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: buildOrgList(Long orgId, Integer year, boolean flag) 
    * 
    */ 
    @Test
    public void testBuildOrgList() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: buildIndexOfficeList(Long orgId, Integer year) 
    * 
    */ 
    @Test
    public void testBuildIndexOfficeList() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getOrgScore(Long orgId, Integer year) 
    * 
    */ 
    @Test
    public void testGetOrgScoreForOrgIdYear() throws Exception { 
        //TODO: Test goes here... 
    } 
    
           /** 
    * 
    * Method: getParentIdByOrgId(long orgId, Long rootOrgId) 
    * 
    */ 
    @Test
    public void testGetParentIdByOrgId() throws Exception { 
        //TODO: Test goes here... 
        /* 
        try { 
           Method method = OrgService.getClass().getMethod("getParentIdByOrgId", long.class, Long.class); 
           method.setAccessible(true); 
           method.invoke(<Object>, <Parameters>); 
        } catch(NoSuchMethodException e) { 
        } catch(IllegalAccessException e) { 
        } catch(InvocationTargetException e) { 
        } 
        */ 
    } 
 
   /** 
    * 
    * Method: getFieldValueByFieldName(String fieldName, Object object) 
    * 
    */ 
    @Test
    public void testGetFieldValueByFieldName() throws Exception { 
        //TODO: Test goes here... 
        /* 
        try { 
           Method method = OrgService.getClass().getMethod("getFieldValueByFieldName", String.class, Object.class); 
           method.setAccessible(true); 
           method.invoke(<Object>, <Parameters>); 
        } catch(NoSuchMethodException e) { 
        } catch(IllegalAccessException e) { 
        } catch(InvocationTargetException e) { 
        } 
        */ 
    } 
 
} 
