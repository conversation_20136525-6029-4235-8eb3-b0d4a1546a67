package com.goodsogood.ows.service.user

import com.goodsogood.ows.model.mongodb.pbm.ColumnarChartForm
import com.goodsogood.ows.model.mongodb.pbm.PbmUnitKitInfo
import lombok.extern.log4j.Log4j2
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension

/**
 *
 * <AUTHOR>
 * @Date 2022-07-04 17:11:27
 * @Description OrgBusinessTest
 *
 */
@ExtendWith(SpringExtension::class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
class OrgBusinessTest {

    @Test
    fun test1() {
        val result = mutableListOf<ColumnarChartForm>()
        val allFit = mutableListOf<PbmUnitKitInfo>()
        val a = PbmUnitKitInfo()
        a.unitName = "aaa（分公司）"
        allFit.add(a)
        val b = PbmUnitKitInfo()
        b.unitName = "市局（公司）机关"
        allFit.add(b)
        allFit.forEach {
            val chart = ColumnarChartForm()
            chart.areaName = if (it.unitName?.endsWith("（分公司）") == true) {
                it.unitName?.replace("（分公司）", "")
            } else if (it.unitName?.endsWith("（公司）机关") == true) {
                it.unitName?.replace("（公司）机关", "")
            } else {
                it.unitName
            }
            result.add(chart)
        }
        println(result)
    }
}