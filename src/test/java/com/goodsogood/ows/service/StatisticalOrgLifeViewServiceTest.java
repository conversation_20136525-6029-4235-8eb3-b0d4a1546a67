package com.goodsogood.ows.service;

import com.goodsogood.ows.common.bean.TimeBean;
import com.goodsogood.ows.model.vo.sas.SasOrgLifeForm;
import com.goodsogood.ows.service.sas.StatisticalOrgLifeViewService;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.PageUtils;
import lombok.extern.log4j.Log4j2;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class StatisticalOrgLifeViewServiceTest {
    @Autowired
    StatisticalOrgLifeViewService statisticalOrgLifeViewService;

    @Test
    public void sasOrgLifeViewCount() {
        //        statisticalOrgLifeViewService.sasOrgLifeViewCount(new Date());
    }
}
