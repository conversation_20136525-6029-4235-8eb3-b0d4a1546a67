package com.goodsogood.ows.service; 
 
import com.goodsogood.ows.common.DingDingMessage;


import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Before;


import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.UUID;

/** 
* PushDingDingService Tester. 
* 
* <AUTHOR> 
* @date 12/01/2020
* @version 1.0 
*/ 
@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class PushDingDingServiceTest { 
    
    @Autowired
    PushDingDingService pushDingDingService;

    /** 
    * 
    * Method: addMessage(DingDingMessage dingDingMessage) 
    * 
    */ 
    @Test
    public void testAddMessage() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: push(String server) 
    * 
    */ 
    @Test
    public void testPush() throws Exception {
        DingDingMessage message = DingDingMessage.builder()
                .content("大家好，我是SAS!第一次与大家见面，请多多指教。")
                .time("2020-12-01 15:08:00")
                .trackId(UUID.randomUUID().toString())
                .build();
        this.pushDingDingService.push(message);
    } 
    
           /** 
    * 
    * Method: push(String source, String message) 
    * 
    */ 
    @Test
    public void testPushForSourceMessage() throws Exception { 
        //TODO: Test goes here... 
        /* 
        try { 
           Method method = PushDingDingService.getClass().getMethod("push", String.class, String.class); 
           method.setAccessible(true); 
           method.invoke(<Object>, <Parameters>); 
        } catch(NoSuchMethodException e) { 
        } catch(IllegalAccessException e) { 
        } catch(InvocationTargetException e) { 
        } 
        */ 
    } 
 
} 
