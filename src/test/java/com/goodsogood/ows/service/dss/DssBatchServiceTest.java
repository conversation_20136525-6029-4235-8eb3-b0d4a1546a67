package com.goodsogood.ows.service.dss; 
 
 
import org.aspectj.lang.annotation.Before;


import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
 
/** 
* DssBatchService Tester. 
* 
* <AUTHOR> 
* @date 12/03/2020
* @version 1.0 
*/ 
@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class DssBatchServiceTest { 
    
    @Autowired
    DssBatchService dssBatchService;

 
    /** 
    * 
    * Method: buildCommitteeList(Long regionId, Integer year) 
    * 
    */ 
    @Test
    public void testBuildCommitteeList() throws Exception { 
        //this.dssBatchService.buildCommitteeList(3L, 2020);
    } 
    
    /** 
    * 
    * Method: buildBranchList(Long regionId, Integer year) 
    * 
    */ 
    @Test
    public void testBuildBranchList() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: buildUserInfo(Long regionId, Integer year) 
    * 
    */ 
    @Test
    public void testBuildUserInfo() throws Exception { 
        //TODO: Test goes here... 
    } 
    
        } 
