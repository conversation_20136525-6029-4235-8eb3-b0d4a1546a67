package com.goodsogood.ows.service.meeting;

import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import lombok.extern.log4j.Log4j2;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("test")
@Log4j2
public class BasicWorkScoreFromEvalMeetingServiceTest {
    @Autowired
    MeetingScoreService service;
    @Test
    public void sasOrgLifeViewCount(){
//        System.out.println("组织得分："+service.orgBasicWorkScore(3L, 3L, "2020-03"));
        ScoreResultVo scoreResultVo = service.excellentPartyMemberScore(
                3L, 2019, Arrays.asList(14695L, 14730L, 14751L));
        System.out.println("用户得分："+scoreResultVo);
    }
}