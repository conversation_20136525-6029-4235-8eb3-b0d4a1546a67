package com.goodsogood.ows.service;

import com.goodsogood.ows.model.mongodb.IndexInfo;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.PartyCommitteeInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.model.mongodb.dss.DssOrg;
import com.goodsogood.ows.service.autoScore.RealAutoScoreService;
import com.goodsogood.ows.service.rank.DecisionSupportService;
import com.goodsogood.ows.service.rank.PpmdAutomaticGradeService;
import lombok.extern.log4j.Log4j2;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by FuXiao on 2020/11/19
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class RealAutoScoreServiceTest {
    @Autowired
    private RealAutoScoreService realAutoScoreService;
    @Autowired
    private DecisionSupportService decisionSupportService;
    @Autowired
    private PpmdAutomaticGradeService ppmdAutomaticGradeService;

    @Test
    public void test() {
        PartyCommitteeInfo info = new PartyCommitteeInfo();
        info.setYear(2019);
        info.setOrganizationId(3035L);
        info.setRegionId(3L);
        /*DssOrg dssOrg = new DssOrg();
        dssOrg.setOrgId(2145L);
        info.setBranch(dssOrg);*/
        realAutoScoreService.buildPartyCommittee(info);
    }
}
