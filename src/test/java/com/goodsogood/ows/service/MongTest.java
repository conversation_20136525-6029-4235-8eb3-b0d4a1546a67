package com.goodsogood.ows.service;

import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.service.dss.DssBaseService;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class MongTest {

	@Autowired
	DssBaseService dssBaseService;

	@Autowired
	MongoTemplate mongoTemplate;

	@Autowired
	MyMongoTemplate myMongoTemplate;

	@Test
	public void MoTest() {
		List<Long> idCollect = new ArrayList<>();
		idCollect.add(13201L);
		Criteria criteria = Criteria.where("year").is(2020);
		criteria.and("userId").in(idCollect);
		Query query = new Query(criteria);
		List<UserInfo> userInfos = this.myMongoTemplate.find(query, UserInfo.class);
		//List<ObjectId> idList = new ArrayList<>();
		//idList.add(userInfos.get(0).getId());

		/*Criteria criteria2 = Criteria.where("_id").in(idList);
		Query query2 = new Query(criteria2);
		List<UserInfo> userInfos2 = this.myMongoTemplate.find(query2, UserInfo.class);
		System.out.println(userInfos2);*/
		Map<String, Object> queryMap = new HashMap<>();
		List<Long> collect = userInfos.stream().map(UserInfo::getUserId).collect(Collectors.toList());
		queryMap.put("userId", new ArrayList<>(collect));
		queryMap.put("year", 2020);
		this.dssBaseService.insertOrUpdateData(UserInfo.class, userInfos, queryMap);
	}


	@Test
	public void test() {
		long page = 1;
		int pageSize = 10;
		Criteria criteria = new Criteria();
		AggregationOptions aggregationOptions = AggregationOptions.builder().allowDiskUse(true).build();
		Aggregation aggregation = Aggregation.newAggregation(
				Aggregation.match(criteria),
				this.buildGroup(UserInfo.class, "userId"),
				Aggregation.sort(Sort.by(Sort.Direction.DESC, "updateTime")),
				Aggregation.skip((page - 1) * pageSize),
				Aggregation.limit(pageSize),
				Aggregation.sort(Sort.by(Sort.Direction.ASC, "userId"))
		).withOptions(aggregationOptions);
		AggregationResults<HashMap> aggregate = this.mongoTemplate.aggregate(aggregation, "DSS-UserInfo_12-17", HashMap.class);
		List<HashMap> results = aggregate.getMappedResults();
		System.out.println("查询出 -> " + results.size());
		results.forEach(map -> {
			for (Object key: map.keySet()) {
				Object o = map.get(key);

			}



			UserInfo user = new UserInfo();
			String json = JsonUtils.toJson(map);
			System.out.println("json -> " + json);
			user = JsonUtils.fromJson(json, UserInfo.class);
			//BeanUtils.populate(user, map);
			System.out.println("对象 -> " + user);
		});

	}

	public UserInfo getUserInfo(HashMap map) {
		UserInfo user = new UserInfo();
		Field[] fields = UserInfo.class.getDeclaredFields();

		for (Object keyO : map.keySet()) {
			String key = String.valueOf(keyO);
			String value = String.valueOf(map.get(keyO));
			switch (key) {
				case "updateTime" :
					user.setUpdateTime(String.valueOf(value));
					break;
				case "regionId" :
					user.setRegionId(Long.parseLong(value));
					break;
				case "year" :
					user.setYear(Integer.parseInt(value));
					break;
				case "createTime" :
					user.setCreateTime(this.strToDate(value));
					break;
				case "userId" :
					user.setUserId(Long.parseLong(value));
					break;
				case "name" :
					user.setName(value);
					break;
				case "compositeScore" :
					user.setCompositeScore(Double.parseDouble(value));
					break;
				case "star"	:
					user.setStar(Integer.parseInt(value));
					break;
				case "annualScoreMap" :

			}

			if ("userId".equals(key)) {

			}

		}

		return null;
	}



	private <T> GroupOperation buildGroup(Class<T> entityClass, String groupBy) {
		Field[] fields = entityClass.getDeclaredFields();
		GroupOperation groupOperation = Aggregation.group(groupBy);
		for (Field field: fields) {
			groupOperation = groupOperation.first(field.getName()).as(field.getName());
		}
		return groupOperation;
	}

	private Date strToDate(String str) {
		SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", java.util.Locale.US);
		try {
			return sdf.parse(str);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}
}
