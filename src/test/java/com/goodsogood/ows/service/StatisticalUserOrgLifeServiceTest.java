package com.goodsogood.ows.service;

import com.goodsogood.ows.service.sas.StatisticalUserOrgLifeService;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("")
@Log4j2
public class StatisticalUserOrgLifeServiceTest {
    @Autowired
    StatisticalUserOrgLifeService statisticalUserOrgLifeService;

    @Test
    public void sasUserOrgLifeCount() {
        statisticalUserOrgLifeService.sasUserOrgLifeCount(3L,DateTime.now().toDate());
    }
}