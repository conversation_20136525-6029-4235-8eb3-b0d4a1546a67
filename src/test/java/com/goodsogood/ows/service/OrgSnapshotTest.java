package com.goodsogood.ows.service;

import com.goodsogood.ows.mapper.user.OrgSnapshotMapper;
import com.goodsogood.ows.model.db.user.OrgSnapshotEntity;
import lombok.extern.log4j.Log4j2;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class OrgSnapshotTest {

	@Autowired
	OrgSnapshotMapper orgSnapshotMapper;

	@Test
	public void test01() {
		//int[] years = {2019,2020};
		//int[] months = {1,2,3,4,5,6,7,8,9,10,11,12};
		int[] years = {2019};
		int[] months = {2};
		List<Long> errorList = new ArrayList<>();
		for (int year : years) {
			for (int month : months) {
				Example example = new Example(OrgSnapshotEntity.class);
				Example.Criteria criteria = example.createCriteria();
				criteria.andEqualTo("year", year);
				criteria.andEqualTo("month", month);
				criteria.andEqualTo("orgType", 102803);
				List<OrgSnapshotEntity> entityList = this.orgSnapshotMapper.selectByExample(example);
				for (OrgSnapshotEntity org : entityList) {
					String levelNow = this.getOrgLevelNow(org, "");
					if (!levelNow.equals(org.getOrgLevel())) {
						errorList.add(org.getOrgId());
					}
				}
			}
		}
		System.out.println(errorList);
	}

	@Test
	public void test02() {
		OrgSnapshotEntity partyOrgHis = this.getPartyOrgHis(199L, 2020, 1);
		String orgLevelNow = this.getOrgLevelNow(partyOrgHis, "");
		System.out.println(orgLevelNow);
		System.out.println(partyOrgHis.getOrgLevel());
	}

	private OrgSnapshotEntity getPartyOrgHis(Long orgId, int year, int month) {
		Example example = new Example(OrgSnapshotEntity.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("year", year);
		criteria.andEqualTo("month", month);
		criteria.andEqualTo("orgId", orgId);
		return this.orgSnapshotMapper.selectOneByExample(example);
	}

	private String getOrgLevelNow(OrgSnapshotEntity org, String curLevel) {
		if (!org.getOrgId().equals(3L)) {
			if (null == org.getOrgPid()) {
				return "-";
			}
			if (!org.getOrgPid().equals(3L)) {
				curLevel = org.getOrgPid() + "-" + curLevel;
				OrgSnapshotEntity partyOrgHis = this.getPartyOrgHis(org.getOrgPid(), org.getYear(), org.getMonth());
				if (null == partyOrgHis.getOrgPid()) {
					return "-";
				}
				if (!partyOrgHis.getOrgPid().equals(3L)) {
					return this.getOrgLevelNow(partyOrgHis, curLevel);
				} else {
					return "-0-1-3-" + curLevel;
				}
			} else {
				return "-0-1-3-";
			}
		} else {
			return "-0-1-";
		}
	}
}
