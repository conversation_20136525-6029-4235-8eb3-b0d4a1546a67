package com.goodsogood.ows.service;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.mapper.ppmd.OrgPayMapper;
import com.goodsogood.ows.model.db.ppmd.PayLogEntity;
import com.goodsogood.ows.model.db.user.OrganizationEntity;
import com.goodsogood.ows.model.mongodb.MeetingInfo;
import com.goodsogood.ows.model.mongodb.PpmdPayInfo;
import com.goodsogood.ows.model.mongodb.report.MeetingReportInfo;
import com.goodsogood.ows.model.mongodb.report.PpmdReportInfo;
import com.goodsogood.ows.service.meeting.MeetingReportService;
import com.goodsogood.ows.service.ppmd.OrgPayService;
import com.goodsogood.ows.service.ppmd.PpmdReportService;
import com.goodsogood.ows.service.user.OrgService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 电子党务报告测试类
 * @date 2019/11/20
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class ReportTest {

    @Autowired
    PpmdReportService ppmdReportService;

    @Autowired
    MeetingReportService meetingReportService;

    @Autowired
    OrgPayService orgPayService;

    @Autowired
    MyMongoTemplate mongoTemplate;

    @Autowired
    OrgService orgService;

    @Autowired
    OrgPayMapper orgPayMapper;


//    @Test
//    public void testOrgPpmdInfo(){
//        this.orgPayService.getPpmdUserList();
//    }

    @Test
    public void testMongo(){
        Criteria criteria = new Criteria();
        criteria.andOperator(
                Criteria.where("orgId").is(4686),
                Criteria.where("statsDate").is("2019-08")
        );
        Query query = new Query(criteria);
        List<PpmdPayInfo> ppmdPayInfos =
                this.mongoTemplate.find(query, PpmdPayInfo.class);
        log.debug("查询结果 -> [{}]", ppmdPayInfos);
    }

    @Test
    public void testList(){
        List<MeetingInfo.MeetingType> typeList = new ArrayList<>();
        MeetingInfo.MeetingType type = new MeetingInfo().new MeetingType();
        type.setId(1L);
        type.setName("一");
        MeetingInfo.MeetingType type1 = new MeetingInfo().new MeetingType();
        type1.setId(1L);
        type1.setName("二");
        MeetingInfo.MeetingType type2 = new MeetingInfo().new MeetingType();
        type2.setId(1L);
        type2.setName("三");

        typeList.add(type);
        typeList.add(type1);
        typeList.add(type2);

        MeetingInfo.MeetingType type3 = new MeetingInfo().new MeetingType();
        type3.setId(2L);
        type3.setName("四");
        MeetingInfo.MeetingType type4 = new MeetingInfo().new MeetingType();
        type4.setId(2L);
        type4.setName("五");
        MeetingInfo.MeetingType type5 = new MeetingInfo().new MeetingType();
        type5.setId(2L);
        type5.setName("六");

        typeList.add(type3);
        typeList.add(type4);
        typeList.add(type5);

        MeetingInfo.MeetingType type6 = new MeetingInfo().new MeetingType();
        type6.setId(3L);
        type6.setName("七");
        MeetingInfo.MeetingType type7 = new MeetingInfo().new MeetingType();
        type7.setId(3L);
        type7.setName("八");

        typeList.add(type6);
        typeList.add(type7);

        System.out.println("原本数据 -> " + typeList);
        List<List<MeetingInfo.MeetingType>> list = new ArrayList<>();
        List<Long> idList = new ArrayList<>();
        for (MeetingInfo.MeetingType t: typeList) {
            List<MeetingInfo.MeetingType> tt = new ArrayList<>();
            if (!idList.contains(t.getId())) {
                idList.add(t.getId());
                for (MeetingInfo.MeetingType t1 : typeList) {
                    if (t.getId().equals(t1.getId())) {
                        tt.add(t1);
                    }
                }
                list.add(tt);
            }
        }
        System.out.println("处理后的数据 -> " + list);
    }

    @Test
    public void testUserMeeting(){
        List<MeetingInfo> userMeetingReport = this.meetingReportService.getUserMeetingList(25958L, "2019-11");
        userMeetingReport.forEach(meetingInfo -> {
            System.out.println("查询结果 -> " + meetingInfo);
        });
        System.out.println("查询会议次数 -> " + userMeetingReport.size());
    }

    @Test
    public void testStr(){
        String s1 = null;
        s1 = StringUtils.isBlank(s1) ? "" : s1;
        String s2 = "123";
        s2 = StringUtils.isBlank(s2) ? "" : s2;
        if (s1.equals(s2)) {
            System.out.println("查询结果 -> 两个相同");
        } else {
            System.out.println("查询结果 -> 两个不相同");
        }
    }

    @Test
    public void testOrgPpmdInfo(){
        PpmdReportInfo orgPpmdReport = this.ppmdReportService.getOrgPpmdReport(108L, "2019-05");
        System.out.println("查询结果 -> " + orgPpmdReport);
    }

    @Test
    public void testPpmdInfo() {
        String queryTime = "2020-08";
         //List<PayLogEntity> payLogList = this.orgPayMapper.getOrgPayByOrgId(3L, queryTime);
        List<PayLogEntity> payLogList = this.orgPayMapper.getOrgPayByDate(queryTime);
        List<PpmdPayInfo> ppmdOrgList = this.orgPayService.getPpmdOrgList(payLogList, queryTime);
        if (!CollectionUtils.isEmpty(ppmdOrgList)) {
            ppmdOrgList.forEach(payInfo -> {
                System.out.println("查询结果 -> " + payInfo);
                List<PpmdPayInfo.User> userList = payInfo.getUserList();
                if (!CollectionUtils.isEmpty(userList)) {
                    userList.forEach(user -> {
                        System.out.println("查询人员 -> " + user);
                    });
                }
            });
        }
    }

    @Test
    public void testBag(){
        // 查询字段
        Criteria criteria = new Criteria();
        // 匹配
        Pattern pattern= Pattern.compile("^.*-"+3+"-.*$", Pattern.CASE_INSENSITIVE);
        criteria.andOperator(
                Criteria.where("orgId").is(3L),
                Criteria.where("orgLevel").regex(pattern),
                Criteria.where("statsDate").is("2019-11")
        );
        Query query = new Query(criteria);
        List<MeetingInfo> infoList = this.mongoTemplate.find(query, MeetingInfo.class);

        MeetingReportInfo.MeetingNum meetingNum = this.meetingReportService.getMeetingNum(infoList);

        System.out.println("查询结果 -> " + meetingNum);
    }

    @Test
    public void testDate() throws Exception{
        /*String month = "2019-01";
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        Date date = format.parse(month);*/

        LocalDateTime today = LocalDateTime.now();
        today = today.minusMonths(1);
        LocalDateTime beforeDate = today.minusDays(1);
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyy-MM");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String queryTime = formatters.format(today);
        String before = formatter.format(beforeDate);

        System.out.println(queryTime);
        System.out.println(before);


        /*String[] last12Months = new String[12];
        Calendar cal = Calendar.getInstance();
        //如果当前日期大于二月份的天数28天或者29天会导致计算月份错误，会多出一个三月份，故设置一个靠前日期解决此问题
        cal.set(Calendar.DAY_OF_MONTH, 1);
        for (int i = 0; i < 12; i++) {
            last12Months[11 - i] = cal.get(Calendar.YEAR) + "-" + (cal.get(Calendar.MONTH) + 1);
            cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) - 1); //逐次往前推1个月
        }
        for (int i = 0; i < last12Months.length; i++) {
            System.out.println(last12Months[i]);
        }*/

    }

    @Test
    public void getRangeSet(){
        /*Date1.after(Date2),当Date1大于Date2时，返回TRUE，当小于等于时，返回false；
          Date1.before(Date2)，当Date1小于Date2时，返回TRUE，当大于等于时，返回false；
          如果业务数据存在相等的时候，而且相等时也需要做相应的业务判断或处理时，你需要使用：！Date1.after(Date2);*/
        String beginDate = "2019-01";
        List<String> rangeSet =null;
        SimpleDateFormat sdf = null;
        Date begin_date = null;
        Date end_date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(end_date); // 设置为当前时间
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1); // 设置为上一个月
        end_date = calendar.getTime();
        rangeSet = new java.util.ArrayList<String>();
        sdf = new SimpleDateFormat("yyyy-MM");
        try {
            begin_date = sdf.parse(beginDate);//定义起始日期
        } catch (ParseException e) {
            System.out.println("时间转化异常，请检查你的时间格式是否为yyyy-MM或yyyy-MM-dd");
        }
        Calendar dd = Calendar.getInstance();//定义日期实例
        dd.setTime(begin_date);//设置日期起始时间
        while(!dd.getTime().after(end_date)){//判断是否到结束日期
            rangeSet.add(sdf.format(dd.getTime()));
            System.out.println(sdf.format(dd.getTime()));
            dd.add(Calendar.MONTH, 1);//进行当前日期月份加1
        }
    }

    @Test
    public void testOrg(){
        List<OrganizationEntity> allChildOrg = this.orgService.getChildPartyOrg(Constants.ROOT_ORG_ID, Constants.INCLUDE);
        System.out.println("长度 -> " + allChildOrg.size());
        for (OrganizationEntity org: allChildOrg) {
            System.out.println(org);
        }
    }

    @Test
    public void testStr1() {
        String str = "2020-05-12";
        String substring = str.substring(0, 7);
        System.out.println(substring.replace("-","年"));
    }

    @Test
    public void testActivePay(){
        String orgLevel = "-0-1-3-";
        String[] str = orgLevel.split("-");
        String s = str[str.length - 1];
        System.out.println(s);
        String s1 = str.length > 4 ? str[4] : str[3];
        System.out.println(s1);
//        int i = orgLevel.indexOf("108");
//        String substring = orgLevel.substring(0, i);
//        System.out.println(i);
//        System.out.println(substring);
        //this.orgPayService.getActivePayOrg(4L, "2019-11", new PpmdPayInfo());
    }

    @Test
    public void testSet() throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        List<Student> set = new ArrayList<>();
        // Set<Student> set1 = new HashSet<>();
        Student student1 = new Student(10,"剑圣", format.parse("2019-9-10"));
        Student student2 = new Student(11,"提莫", format.parse("2019-10-10"));
        Student student3 = new Student(9,null, format.parse("2019-12-20"));
        Student student4 = new Student(5,null, format.parse("2019-10-1"));
        Student student5 = new Student(5,null, format.parse("2019-10-1"));
        set.add(student1);
        set.add(student2);
        set.add(student3);
        set.add(student4);

//        set1.add(student1);
//        set1.add(student2);
//        set1.add(student3);
//        set1.add(student4);
//        set1.add(student5);
        // List<Student> list = set1.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparingInt(Student::getAge))), ArrayList::new));
        // list.forEach(student -> {System.out.println("第一次打印 -> " + student);});

        List<Person> personList = set.stream().filter(student -> student.getAge() == 5).map(student -> (Person) student).collect(Collectors.toList());
        personList.forEach(person -> {System.out.println("第一次打印 -> " + person);});



        /*set.forEach(student -> {System.out.println("第一次打印 -> " + student);});
        System.out.println();
        Collections.sort(set, (s1,s2)->(s1.getAge()-s2.getAge()));

        set.forEach(student -> {System.out.println("第二次打印 -> " + student);});
        System.out.println();
        Collections.sort(set);

        set.forEach(student -> {System.out.println("第三次打印 -> " + student);});

        Set<String> collect = set.stream().map(Student::getName).collect(Collectors.toSet());
        String s1 = collect.iterator().next();
        System.out.println(s1);
        collect.forEach(s -> {
            if (StringUtils.isNotBlank(s)) {
                System.out.println(s);
            }
        });*/

    }

    public class Student extends Person {

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        private Date date;

        public Student(int age, String name, Date date) {
            this.age = age;
            this.name = name;
            this.date = date;
        }

        public Student() {

        }

        public Date getDate() {
            return date;
        }

        public void setDate(Date date) {
            this.date = date;
        }

    }

    public class Person {

        public int age;

        public String name;

        public int getAge() {
            return age;
        }

        public void setAge(int age) {
            this.age = age;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return "Student{" +
                    "age=" + age +
                    ", name='" + name + '\'' +
                    '}';
        }
    }

}
