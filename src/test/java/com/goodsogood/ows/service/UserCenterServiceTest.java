package com.goodsogood.ows.service;

import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.service.sas.UserCenterService;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class UserCenterServiceTest {
    @Autowired
    UserCenterService userCenterService;

    @Test
    public void findPartyUser() {
        List<OrganizationBase> list = userCenterService.findAllOrg(3L);
        log.debug(JsonUtils.toJson(list));
    }

    @Test
    public void findAllOrg() {
    }
}