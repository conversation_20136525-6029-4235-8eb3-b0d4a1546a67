package com.goodsogood.ows.service

import org.junit.jupiter.api.Test
import java.math.RoundingMode
import java.text.DecimalFormat
import kotlin.math.ln

/**
 *
 * <AUTHOR>
 * @createTime 2022年07月22日 15:48:00
 */
class KotlinTest {


    @Test
    fun test01(){
        val l = mathLog(value = 182)
        println(l)
        println(dealNumbers(l))
    }

    /**
     * 保留小数点后一位
     */
    fun dealNumbers(data: Double?): Double {
        val d = if (data == null || data.isNaN()) {
            0.0
        } else {
            data
        }
        val df = DecimalFormat("#.#")
        df.roundingMode = RoundingMode.HALF_UP
        return df.format(d).toDouble()
    }

    /**
     * @title 求对数
     * <AUTHOR>
     * @param   value   求对数的值
     * @param   base    对数的基数
     * @updateTime 2022/6/26 17:23
     * @return
     * @throws
     */
    fun mathLog(value: Long, base: Long = 100): Double {
        return if (value <= 0L) {
            0.0
        } else {
            ln(value.toDouble()) / ln(base.toDouble())
        }
    }
}