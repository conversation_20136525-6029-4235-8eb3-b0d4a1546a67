package com.goodsogood.ows.service;

import com.goodsogood.ows.configuration.ScoreConfig;
import com.goodsogood.ows.mapper.meeting.MeetingMapper;
import com.goodsogood.ows.mapper.user.LeaderMapper;
import com.goodsogood.ows.mapper.user.OrganizationMapper;
import com.goodsogood.ows.model.mongodb.PartyBranchInfo;
import com.goodsogood.ows.model.mongodb.UserInfo;
import com.goodsogood.ows.model.vo.rank.ScoreResultVo;
import com.goodsogood.ows.service.meeting.dss.DssUserFromMeeting;
import com.goodsogood.ows.service.rank.RemoteModService;
import com.goodsogood.ows.service.rank.RemoteRuleWryService;
import com.goodsogood.ows.service.user.DssPeriodAndLeaderService;
import lombok.extern.log4j.Log4j2;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("test")
@Log4j2
public class RemoteModWRYTest {

    @Autowired
    private RemoteModService remoteModService;
    @Autowired
    private MeetingMapper meetingMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private DssPeriodAndLeaderService dssPeriodAndLeaderService;


    @Test
    public void test01() {
        Long topOrgId = 4685L;
        Long baseOrgId = 123L;
        String queryDate1 = "2020-12";
        String queryDate2 = "2019-12";
        Double aDouble = 0.0;
//        组织 基础工作 民主评议结果 所有基层党组织 是否已完成上一年度民主评议结果录入
//        aDouble += remoteModService.lastYearOrgCommentResult(4685L, queryDate2);
//        组织 基础工作 领导班子成员支部联系点情况 机关党委 是否设置支部联系点（有/无？）
//        remoteModService.isCreateLeaderContact(topOrgId,queryDate2);
//        组织 基础工作 领导班子成员支部联系点情况 机关党委 支部联系点设置是否合理（联系点与所在支部是否一致）
//        remoteModService.checkLeaderContactEqual(topOrgId,queryDate2);
//        aDouble += remoteModService.orgCheckLeaderBaseInfo(7928L, "2020-01");
//        组织建设-届次换届
        aDouble += remoteModService.orgCheckPeriodInfo(31L, "2020-01");
//        党员 基础工作 民主评议 非离退休党员 是否有有评议结果（按年统计）
//        aDouble += remoteModService.lastYearUserCommentResult(4685L, queryDate2);
//        党员 领导干部示范带头 讲党课 机关单位领导干部 讲党课次数是否满足规定，每年大于1次
//        aDouble += remoteModService.leaderLectures(123L, queryDate2);

        System.out.println(aDouble);

    }

    @Test
    public void test02() {
        RemoteRuleWryService.RemoteRuleWryQuery build = RemoteRuleWryService.RemoteRuleWryQuery.builder().build();
        Method method = ReflectionUtils.findMethod(MeetingMapper.class, "leaderLectures", null);
//        Object bean = SpringContextUtil.getBean(MeetingMapper.class);
        Object o = ReflectionUtils.invokeMethod(method, meetingMapper, 123L, "2020");
        System.out.println(o.toString());
    }

    @Autowired
    private RemoteRuleWryService remoteRuleWryService;

    @Autowired
    private LeaderMapper leaderMapper;

    @Autowired
    private ScoreConfig scoreConfig;

    @Test
    public void test04() {
        List<Long> allOrgan = organizationMapper.getAllOrgan();
//        List<Long> longs = Arrays.asList(24L,27L, 28L, 229L, 232L, 304L, 1200L, 7697L);
        List<Long> longs = Arrays.asList(24L, 27L, 28L);
//        组织 基础工作 民主评议结果 所有基层党组织 是否已完成上一年度民主评议结果录入
//        ScoreResultVo scoreResultVo1 = remoteModService.lastYearOrgCommentResult(allOrgan, 2019);
//        System.out.println(scoreResultVo1);
        //组织 基础工作 领导班子成员支部联系点情况 机关党委 是否设置支部联系点（有/无？）
        //组织 基础工作 领导班子成员支部联系点情况 机关党委 支部联系点设置是否合理（联系点与所在支部是否一致）
//        ScoreResultVo scoreResultVo2 = remoteModService.orgCheckLeaderBaseInfo(Arrays.asList(2557L), 2019);
//        ScoreResultVo scoreResultVo2 = remoteModService.orgCheckLeaderBaseInfo(Arrays.asList(440L, 478L, 687L, 827L, 856L, 1012L, 1019L, 1290L, 1323L, 1386L, 1503L), 2019);
//        System.out.println(scoreResultVo2);
        //组织建设-届次换届
        ScoreResultVo scoreResultVo3 = remoteModService.orgCheckPeriodInfo(Arrays.asList(1177L, 1178L, 1179L, 1180L), 2020);
        System.out.println(scoreResultVo3);
        //党员 基础工作 民主评议 非离退休党员 是否有有评议结果（按年统计）
        ScoreResultVo scoreResultVo4 = remoteModService.lastYearUserCommentResult(longs, 2019);
//        System.out.println(scoreResultVo4);
        //党员 领导干部示范带头 讲党课 机关单位领导干部 讲党课次数是否满足规定，每年大于1次
//        ScoreResultVo scoreResultVo5=remoteModService.leaderLectures(longs, 2019);
//        System.out.println(scoreResultVo5);
    }

    @Test
    public void test05() {
        List<Long> allOrgan = organizationMapper.getAllOrgan();
        List<PartyBranchInfo> list = new ArrayList<>();
//        List<Long> longs = Arrays.asList(440L, 478L, 687L, 827L, 856L, 1012L, 1019L, 1290L, 1323L, 1386L, 1503L);
        List<Long> longs = Arrays.asList(735L,375L,1960L,614L,608L,615L,611L);
        longs.forEach(x -> {
            PartyBranchInfo partyBranchInfo = new PartyBranchInfo();
            partyBranchInfo.setOrganizationId(x);
            partyBranchInfo.setYear(2020);
            list.add(partyBranchInfo);
        });

        List<PartyBranchInfo> partyBranchInfos = dssPeriodAndLeaderService.buildPartyBranchList(list);
        System.out.println(partyBranchInfos.size());

//        PartyCommitteeInfo partyCommitteeInfo = new PartyCommitteeInfo();
//        partyCommitteeInfo.setYear(2020);
//        partyCommitteeInfo.setOrganizationId(114L);
//        partyCommitteeInfo.setRegionId(3L);
//        PartyCommitteeInfo partyCommitteeInfo2 = dssPeriodAndLeaderService.buildPartyCommittee(partyCommitteeInfo);

    }

    @Autowired
    private DssUserFromMeeting dssUserFromMeeting;

    /**
     * 顺 组织生活考核相关
     * DssUserFromMeeting
     */
    @Test
    public void test06() {
        UserInfo userInfo=new UserInfo();
        userInfo.setYear(2019);
        userInfo.setRegionId(3L);
        userInfo.setUserId(2L);
        UserInfo userInfo1 = dssUserFromMeeting.buildUser(userInfo);
    }
}
