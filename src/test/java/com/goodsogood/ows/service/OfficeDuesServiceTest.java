package com.goodsogood.ows.service;

import com.goodsogood.ows.OwsSasApplication;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.service.tbc.TbcPpmdService;
import lombok.extern.log4j.Log4j2;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = OwsSasApplication.class)
@ActiveProfiles("test")
@Log4j2
public class OfficeDuesServiceTest {

    @Autowired
    private TbcPpmdService tbcPpmdService;
    @Autowired
    private RedisTemplate redisTemplate;

    @Test
    public void getTest(){

        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new StringRedisSerializer());

        String key="dues_form_2021-01_4647_3";
        if (this.redisTemplate.hasKey(key)){
            this.redisTemplate.delete(key);
        }
        key="status_4647_3_2021-01";
        if (this.redisTemplate.hasKey(key)){
            this.redisTemplate.delete(key);
        }
        key="month_trend_2021-01_4647";
        if (this.redisTemplate.hasKey(key)){
            this.redisTemplate.delete(key);
        }
        key="month_trend_2020-12_4647";
        if (this.redisTemplate.hasKey(key)){
            this.redisTemplate.delete(key);
        }
        key="branch_4647_2021-01";
        if (this.redisTemplate.hasKey(key)){
            this.redisTemplate.delete(key);
        }
        key="rank_list_2021-01_4647";
        if (this.redisTemplate.hasKey(key)){
            this.redisTemplate.delete(key);
        }
        System.out.println(
                Utils.toJson(
                        tbcPpmdService.getDuesForm(4647L,3L)
                )
        );
    }

    @Test
    public void testDetails(){
        System.out.println(
                Utils.toJson(
                        tbcPpmdService.getDetails(4647L,true,1,10)
                )
        );
        System.out.println(
                Utils.toJson(
                        tbcPpmdService.getDetails(4647L,false,1,10)
                )
        );
    }

    @Test
    public void testUnpaidMember(){
        System.out.println(
                Utils.toJson(
                        tbcPpmdService.getUnpaidMember(4647L,4667L,1,10)
                )
        );
    }
}

