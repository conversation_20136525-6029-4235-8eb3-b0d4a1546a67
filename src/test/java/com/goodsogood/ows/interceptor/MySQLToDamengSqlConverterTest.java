package com.goodsogood.ows.interceptor;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * MySQL到达梦SQL转换器测试类
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public class MySQLToDamengSqlConverterTest {

    @Mock
    private SqlInterceptorConfig config;

    @InjectMocks
    private MySQLToDamengSqlConverter converter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 设置默认配置
        when(config.isEnabled()).thenReturn(true);
        when(config.isFunctionConversionEnabled()).thenReturn(true);
        when(config.isKeywordConversionEnabled()).thenReturn(true);
        when(config.isSyntaxConversionEnabled()).thenReturn(true);
        when(config.isLimitConversionEnabled()).thenReturn(true);
        when(config.isDateFunctionConversionEnabled()).thenReturn(true);
        when(config.isStringFunctionConversionEnabled()).thenReturn(true);
        when(config.isMathFunctionConversionEnabled()).thenReturn(true);
        when(config.isAggregateFunctionConversionEnabled()).thenReturn(true);
        when(config.isConditionalFunctionConversionEnabled()).thenReturn(true);
        when(config.isLogEnabled()).thenReturn(true);
        when(config.getLogLevel()).thenReturn("INFO");
        when(config.isBlacklisted(anyString())).thenReturn(false);
        when(config.isWhitelisted(anyString())).thenReturn(true);
    }

    @Test
    void testBasicFunctionConversion() {
        // 测试基本函数转换
        String mysqlSql = "SELECT NOW(), CURDATE(), LENGTH('test') FROM users";
        String result = converter.convertSql(mysqlSql);
        
        assertTrue(result.contains("SYSDATE"));
        assertTrue(result.contains("TRUNC(SYSDATE)"));
        assertTrue(result.contains("LEN"));
    }

    @Test
    void testDateFunctionConversion() {
        // 测试日期函数转换
        String mysqlSql = "SELECT DATE_ADD(created_at, INTERVAL 1 DAY) FROM orders";
        String result = converter.convertSql(mysqlSql);
        
        assertTrue(result.contains("DATEADD"));
    }

    @Test
    void testStringFunctionConversion() {
        // 测试字符串函数转换
        String mysqlSql = "SELECT CONCAT(first_name, ' ', last_name), SUBSTRING(email, 1, 10) FROM users";
        String result = converter.convertSql(mysqlSql);
        
        assertTrue(result.contains("CONCAT"));
        assertTrue(result.contains("SUBSTR"));
    }

    @Test
    void testLimitConversion() {
        // 测试LIMIT转换
        String mysqlSql = "SELECT * FROM users ORDER BY id LIMIT 10";
        String result = converter.convertSql(mysqlSql);
        
        assertTrue(result.contains("ROWNUM"));
    }

    @Test
    void testLimitWithOffsetConversion() {
        // 测试带偏移的LIMIT转换
        String mysqlSql = "SELECT * FROM users ORDER BY id LIMIT 10, 20";
        String result = converter.convertSql(mysqlSql);
        
        assertTrue(result.contains("ROWNUM"));
        assertTrue(result.contains("BETWEEN"));
    }

    @Test
    void testIfFunctionConversion() {
        // 测试IF函数转换
        String mysqlSql = "SELECT IF(status = 1, 'Active', 'Inactive') FROM users";
        String result = converter.convertSql(mysqlSql);
        
        assertTrue(result.contains("CASE WHEN"));
        assertTrue(result.contains("THEN"));
        assertTrue(result.contains("ELSE"));
        assertTrue(result.contains("END"));
    }

    @Test
    void testGroupConcatConversion() {
        // 测试GROUP_CONCAT转换
        String mysqlSql = "SELECT GROUP_CONCAT(name) FROM users GROUP BY department";
        String result = converter.convertSql(mysqlSql);
        
        assertTrue(result.contains("LISTAGG"));
        assertTrue(result.contains("WITHIN GROUP"));
    }

    @Test
    void testBacktickConversion() {
        // 测试反引号转换
        String mysqlSql = "SELECT `user_id`, `user_name` FROM `users`";
        String result = converter.convertSql(mysqlSql);
        
        assertTrue(result.contains("\"user_id\""));
        assertTrue(result.contains("\"user_name\""));
        assertTrue(result.contains("\"users\""));
    }

    @Test
    void testInsertIgnoreConversion() {
        // 测试INSERT IGNORE转换
        String mysqlSql = "INSERT IGNORE INTO users (name, email) VALUES ('test', '<EMAIL>')";
        String result = converter.convertSql(mysqlSql);
        
        assertTrue(result.startsWith("INSERT INTO"));
        assertFalse(result.contains("IGNORE"));
    }

    @Test
    void testComplexQuery() {
        // 测试复杂查询
        String mysqlSql = "SELECT u.`user_id`, CONCAT(u.first_name, ' ', u.last_name) as full_name, " +
                         "DATE_FORMAT(u.created_at, '%Y-%m-%d') as created_date, " +
                         "IF(u.status = 1, 'Active', 'Inactive') as status_text " +
                         "FROM `users` u WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) " +
                         "ORDER BY u.created_at DESC LIMIT 0, 50";
        
        String result = converter.convertSql(mysqlSql);
        
        // 验证各种转换都生效了
        assertTrue(result.contains("\"user_id\""));
        assertTrue(result.contains("CONCAT"));
        assertTrue(result.contains("TO_CHAR"));
        assertTrue(result.contains("CASE WHEN"));
        assertTrue(result.contains("\"users\""));
        assertTrue(result.contains("DATEADD"));
        assertTrue(result.contains("SYSDATE"));
        assertTrue(result.contains("ROWNUM"));
    }

    @Test
    void testDisabledConversion() {
        // 测试禁用转换
        when(config.isEnabled()).thenReturn(false);
        
        String mysqlSql = "SELECT NOW() FROM users LIMIT 10";
        String result = converter.convertSql(mysqlSql);
        
        assertEquals(mysqlSql, result); // 应该返回原始SQL
    }

    @Test
    void testBlacklistedSql() {
        // 测试黑名单SQL
        when(config.isBlacklisted(anyString())).thenReturn(true);
        
        String mysqlSql = "SELECT NOW() FROM users";
        String result = converter.convertSql(mysqlSql);
        
        assertEquals(mysqlSql, result); // 应该返回原始SQL
    }

    @Test
    void testWhitelistedSql() {
        // 测试白名单SQL
        when(config.isWhitelisted(anyString())).thenReturn(false);
        
        String mysqlSql = "SELECT NOW() FROM users";
        String result = converter.convertSql(mysqlSql);
        
        assertEquals(mysqlSql, result); // 应该返回原始SQL
    }

    @Test
    void testSelectiveFunctionConversion() {
        // 测试选择性函数转换
        when(config.isDateFunctionConversionEnabled()).thenReturn(false);
        when(config.isStringFunctionConversionEnabled()).thenReturn(true);
        
        String mysqlSql = "SELECT NOW(), LENGTH('test') FROM users";
        String result = converter.convertSql(mysqlSql);
        
        assertTrue(result.contains("NOW()")); // 日期函数不应该被转换
        assertTrue(result.contains("LEN")); // 字符串函数应该被转换
    }

    @Test
    void testNullAndEmptySql() {
        // 测试空SQL
        assertNull(converter.convertSql(null));
        assertEquals("", converter.convertSql(""));
        assertEquals("   ", converter.convertSql("   "));
    }

    @Test
    void testErrorHandling() {
        // 测试错误处理
        String invalidSql = "INVALID SQL SYNTAX";
        String result = converter.convertSql(invalidSql);
        
        // 即使SQL无效，也应该返回原始SQL而不是抛出异常
        assertNotNull(result);
    }

    @Test
    void testDataSourceSpecificConversion() {
        // 测试数据源特定转换
        String mysqlSql = "SELECT NOW() FROM users";
        String result1 = converter.convertSql(mysqlSql, "sas");
        String result2 = converter.convertSql(mysqlSql, "user");
        
        // 两个结果应该相同（因为使用相同的配置）
        assertEquals(result1, result2);
    }
}
