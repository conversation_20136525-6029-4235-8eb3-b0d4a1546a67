package com.goodsogood.ows.helper;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.goodsogood.ows.helper.entity.JsonFile;
import com.goodsogood.ows.helper.entity.Org;
import com.goodsogood.ows.helper.entity.User;
import org.junit.jupiter.api.Test;


import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/11/4
 * @description class PADFileHelperTest
 */
public class PADFileHelperTest {

    @Test
    public void testBuild() {
        File file = new PADFileHelper("/Users/<USER>/Downloads/temp", UUID.randomUUID().toString(), "V1.0.0")
                .clear() // 清理目录
                .setDebug(false)
                .addUser(new User( //添加用户 支持数组
                        1, "2020", "张三", "186****2345", "党支部1", "党委1", null
                ))
                .addUser(new User(
                        2, "2020", "李四", "186****7890", "党支部2", "党委1", null
                ))
                .addOrg(new Org(// 添加组织 支持数组
                        1, "2020", "党委1", "党委1", 2, "党委"
                ))
                .addOrg(new Org(
                        2, "2020", "党支部1", "党支部1", 3, "党支部"
                ))
                .addOrg(new Org(
                        3, "2020", "党支部2", "党支部2", 3, "党支部"
                ))
                .addOrgFile("2020", "{org:12345}") // 工委数据 org.json
                .addMapFile("2020", "{map:12345}") // 地图或者首页第一屏数据 map.json
                .addFile(2, "2020", "{committee:123}", 1)  //党委数据  2.党委，3.支部，4.个人
                .addFile(3, "2020", "{branch:123}", 2) // 党支部数据 2.党委，3.支部，4.个人
                .addFile(4, "2020", "{user:123}", 1) // 用户数据 2.党委，3.支部，4.个人
                .addFiles(new JsonFile( // 批量添加数据文件 支持数组
                                3, "2020", "{branch:345}", 3L
                        ), new JsonFile( // 批量添加数据文件 支持数组 1.工委，2.党委，3.支部，4.个人，5.地图
                                1, "2019", "{org:12345}", 0L
                        )
                ).build("186____2345", "周_去", false);
        System.out.println(file.getAbsolutePath());
        System.out.println(file.exists());
    }

    @Test
    public void checkFile() throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        
        List<JsonFile> jsonFiles = objectMapper.readValue(new File("/Users/<USER>/Downloads/files.json"), new TypeReference<List<JsonFile>>() {
        });

        Set<JsonFile> uniqueSet = new HashSet<>(jsonFiles);
        for (JsonFile temp : uniqueSet) {
            if (Collections.frequency(jsonFiles, temp) > 1) {
                System.out.println(temp.getId() + "|" + temp.getType() + "|" + temp.getData());
            }
        }

    }
}
