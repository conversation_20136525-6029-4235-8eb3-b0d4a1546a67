package com.goodsogood.ows.helper;

import net.lingala.zip4j.exception.ZipException;
import org.junit.jupiter.api.Test;

import java.nio.file.FileAlreadyExistsException;

import static org.junit.jupiter.api.Assertions.*;

class ZipHelperTest {
    String sourceFolder = "/Users/<USER>/Downloads/ows";
    String targetFolder = "/Users/<USER>/Downloads/";
    String fileName = "186____8132-许_多-20201028-123456.ows";

    @Test
    void zipFolderEncrypt() throws ZipException, FileAlreadyExistsException {
        ZipHelper.zipFolderEncrypt(sourceFolder, ZipHelper.createPassword(fileName), targetFolder + fileName);
    }

    @Test
    void createPassword() {
        System.out.println(ZipHelper.createPassword(fileName));
    }

    @Test
    void unZipEncrypt() throws ZipException {
        ZipHelper.unZipEncrypt(targetFolder + fileName, ZipHelper.createPassword(fileName), targetFolder);
    }
}