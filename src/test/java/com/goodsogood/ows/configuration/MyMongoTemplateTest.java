package com.goodsogood.ows.configuration;

import com.mongodb.BasicDBObject;
import com.mongodb.BasicDBObjectBuilder;
import com.mongodb.DBObject;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.bson.Document;
import org.joda.time.DateTime;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.query.BasicQuery;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.DateFormat;
import java.util.Date;
import java.util.List;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class MyMongoTemplateTest {

    @Autowired
    private MyMongoTemplate mongoTemplate;

    @Test
    public void testISODate() {
        DateTime time1 = DateTime.parse("2019-12-31");
        DateTime time2 = DateTime.parse("2021-01-01");
        Query query = new Query(new Criteria("transferTime").lte(time2).gte(time1));
        List<PageView> pvs = mongoTemplate.find(query, PageView.class, "PageView");
        log.debug("size->{}", pvs.size());
    }

    @Data
    static class PageView {
        /**
         * 用户id
         */
        private String userId;
        /**
         * 操作类型 A点击 B点赞 C取消赞
         */
        private String type;
        /**
         * 操作对象（暂指消息id）
         */
        private String object;
        /**
         * 数量（默认为1，用于购买或金额等的累加统计）
         */
        private Double amount;
        /**
         * 调用时间
         */
        private Date transferTime;
        /**
         * 接收时间
         */
        private Date receiveTime;
        /**
         * 渠道名称
         */
        private String channal;
        /**
         * uuid
         */
        private String uuid;
        /**
         * 操作对象前缀
         */
        private String prefix;
        /**
         * 分享者id
         */
        private String sharerId;
        /**
         * 区县id
         */
        private Long regionId;
    }

}