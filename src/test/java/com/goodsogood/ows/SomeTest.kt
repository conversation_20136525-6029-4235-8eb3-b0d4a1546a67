package com.goodsogood.ows

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.model.db.eval.v2.MetricEntity
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.model.vo.eval.v2.TaskInfo
import com.goodsogood.ows.model.vo.ql.QueryForm
import com.goodsogood.ows.model.vo.ql.QueryForm.Companion.checkValues
import com.goodsogood.ows.model.vo.ql.QueryForm.Companion.createCondition
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import java.time.LocalDate
import kotlin.random.Random

/**
 * <AUTHOR>
 * @date 2023/4/4
 * @description class SomeTest
 */
class SomeTest {
    @Test
    fun test1() {
        val numQuestions = 150
        val maxDividend = 99
        val maxDivisor = 9

        var count = 0
        while (count < numQuestions) {
            var dividend = Random.nextInt(9, maxDividend + 1)
            var divisor = Random.nextInt(1, maxDivisor + 1)
            // Ensure dividend is greater than or equal to divisor
            if (dividend < divisor) {
                val temp = dividend
                dividend = divisor
                divisor = temp
            }

            val quotient = dividend / divisor
            val remainder = dividend % divisor

            if (remainder > 0 && quotient < 100 && remainder < 10) {
                println("$dividend ÷ $divisor = ")
                count++
            }
        }
    }

    @Test
    fun test2() {
        val p1 = QueryForm(param = "col18,col17", type = "Input", values = listOf("冉幕寿"))
        val p2 = QueryForm(param = "col14", type = "RangePicker", values = listOf("2023-07-01", "2023-07-31"))
        val p3 = QueryForm(param = "col15", type = "Select", values = listOf("主题党日", "支部集中学习"))
        val params = listOf(p1, p2, p3)
        val listParam = mutableListOf<Map<String, Any>>()
        params.forEach {
            if (!it.checkValues()) {
                println("error")
            }
            it.createCondition().let { map ->
                if (map.isNotEmpty()) {
                    listParam.add(map)
                }
            }
        }
        listParam.forEach {
            println(it)
        }
    }

    @Test
    fun test3() {
        val t1 = T1(1, "a", "b")
        val t2 = T1(1, "a1", "b1")
        val t3 = T1(2, "a2", "b2")
        val t4 = T1(3, "a3", "b3")
        val list = listOf(t1, t2, t3, t4)
        list.distinctBy { it.id }.forEach(::println)
    }

    data class T1(
        var id: Long? = null,
        var a: String? = null,
        var b: String? = null,
    )

    @Test
    fun test4() {
        val taskStr =
            "{\"taskId\":\"d857ce30c6f84be595a68601ac775334\",\"metricId\":15,\"metricClassId\":3,\"metric\":{\"id\":15,\"metricClassId\":3,\"level\":15,\"name\":\"领导成员每年至少深入基层联系点1次\",\"summary\":\"按未开展人次扣分\",\"type\":1,\"year\":2023,\"enabled\":1,\"scoreLimit\":null,\"score\":0.3,\"scoreType\":1,\"clzName\":null,\"includeSubOrg\":1,\"createUser\":-999,\"createTime\":\"2023-11-27T11:16:23\",\"lastChangeUser\":-999,\"updateTime\":\"2023-11-27T11:16:31\"},\"unit\":630,\"org\":{\"organization_id\":137,\"region_id\":19,\"parent_id\":3,\"name\":\"中国共产党中国烟草总公司重庆市公司长寿分公司机关支部委员会\",\"short_name\":\"长寿分公司机关党支部\",\"org_type\":102803,\"org_type_child\":10280304,\"owner_tree\":2,\"owner_id\":630,\"owner_name\":\"长寿区局（分公司）\",\"owner_short_name\":\"长寿\",\"org_level\":\"-0-1-3-\",\"is_retire\":2,\"org_create_time\":\"2018-05-21T00:00:00.000+08:00\",\"status\":1},\"time\":1701808160956,\"ssLog\":null}"
        val objectMapper = ObjectMapper()
        val taskInfo = objectMapper.readValue(taskStr, TaskInfo::class.java)
        println(taskInfo)
        println(taskInfo.metric)
    }

    @Test
    fun test5(){
        val str = "-1"
        println(str.toLong())
        for(i in 1..1){
            println(i)
        }

        val year = 2023

        val startMonth = if (year == 2023) {
            // 2023是特殊情况，11月开始
            11
        } else {
            1
        }

        var countNumber = 0
        // 获取当前月 的上一个月
        val endTime = if (year == LocalDate.now().year) {
            LocalDate.now().minusMonths(1)
        } else {
            LocalDate.of(year, 12, 1)
        }
        val endMonth = endTime.month.value
//        val calYear = year
//        if (endMonth == 1) {
//            endMonth = 12
//            calYear = year - 1
//        }
        for (i in startMonth..endMonth) {
            println(i)
        }
    }
}