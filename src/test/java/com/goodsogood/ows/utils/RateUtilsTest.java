package com.goodsogood.ows.utils; 
 
 



import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/** 
* RateUtils Tester. 
* 
* <AUTHOR> 
* @date 12/17/2020
* @version 1.0 
*/ 
@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class RateUtilsTest { 
    
    @Autowired
    RateUtils rateUtils;
    
   
 
 
 
    /** 
    * 
    * Method: RateUtils(StringRedisTemplate redisTemplate) 
    * 
    */ 
    @Test
    public void testRateUtils() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: build(Integer total) 
    * 
    */ 
    @Test
    public void testBuildTotal() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: build(Integer total, String uuid) 
    * 
    */ 
    @Test
    public void testBuildForTotalUuid() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: auto(String uuid) 
    * 
    */ 
    @Test
    public void testAuto() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: update(String uuid, long delta) 
    * 
    */ 
    @Test
    public void testUpdate() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: complete(String uuid) 
    * 
    */ 
    @Test
    public void testComplete() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getRate(String uuid, int type) 
    * 
    */ 
    @Test
    public void testGetRateForUuidType() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: getRate(String uuid, int type, int fullMarks) 
    * 
    */ 
    @Test
    public void testGetRateForUuidTypeFullMarks() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: delete(String uuid) 
    * 
    */ 
    @Test
    public void testDelete() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: hasKey(String uuid) 
    * 
    */ 
    @Test
    public void testHasKey() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: get(String redisKey) 
    * 
    */ 
    @Test
    public void testGet() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: incr(String uuid, Integer divisor, String parentUuid) 
    * 
    */ 
    @Test
    public void testIncrForUuidDivisorParentUuid() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
    * 
    * Method: incr(String uuid, Integer after, Integer fullMarks, String parentUuid) 
    * 
    */ 
    @Test
    public void testIncrForUuidAfterFullMarksParentUuid() throws Exception { 
        //TODO: Test goes here...
        //for (int i = 0; i < 100; i++) {
        String uuid = RateUtils.build(100);
        AtomicReference<Integer> after = new AtomicReference<>(0);
        RateUtils.update(uuid, 10L);
        String childUuid = RateUtils.build(1000);
        CountDownLatch latch = new CountDownLatch(1000);

        MyThread mt1 = new MyThread(childUuid, after, uuid, latch);
        MyThread mt2 = new MyThread(childUuid, after, uuid, latch) ;
        Thread t1 = new Thread(mt1);
        Thread t2 = new Thread(mt2);
        t1.start();
        t2.start();
        latch.await();
        log.debug("当前的进度 -> [{}]", RateUtils.getRate(uuid, 2));
        //}



    }

    class MyThread implements Runnable {

        private final String  childUuid;
        private final AtomicReference<Integer> after;
        private final String  uuid;
        private final CountDownLatch latch;

        public MyThread(String childUuid, AtomicReference<Integer> after, String uuid, CountDownLatch latch) {
            this.childUuid = childUuid;
            this.after = after;
            this.uuid = uuid;
            this.latch = latch;
        }

        @Override
        public void run() {
            for (int j = 0; j < 500; j++) {
                RateUtils.auto(childUuid);
                RateUtils.incr(childUuid, after, 90, uuid);
                latch.countDown();
            }
        }
    }
    
}
