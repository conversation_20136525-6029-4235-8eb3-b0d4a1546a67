# MySQL到达梦数据库SQL拦截器

## 概述

本项目实现了一个完整的SQL拦截器系统，用于将MySQL语法自动转换为达梦数据库语法。该拦截器集成到MyBatis中，能够在SQL执行前自动进行语法转换，支持多数据源配置和复杂SQL场景。

## 核心功能

### 1. 函数转换
- **日期时间函数**: NOW() → SYSDATE, CURDATE() → TRUNC(SYSDATE), DATE_FORMAT → TO_CHAR等
- **字符串函数**: LENGTH → LEN, SUBSTRING → SUBSTR, LOCATE → INSTR等
- **数学函数**: CEIL → CEIL, FLOOR → FLOOR, MOD → MOD等
- **聚合函数**: GROUP_CONCAT → LISTAGG等
- **条件函数**: IF → CASE WHEN, IFNULL → NVL等

### 2. 关键字转换
- **数据类型**: AUTO_INCREMENT → IDENTITY(1,1), TEXT → CLOB, DATETIME → TIMESTAMP等
- **语法元素**: 反引号(`) → 双引号("), LIMIT → ROWNUM等

### 3. 特殊语法处理
- **LIMIT语法**: 转换为ROWNUM分页
- **INSERT IGNORE**: 转换为普通INSERT
- **DATE_ADD/DATE_SUB**: 转换为DATEADD函数
- **复杂分页**: 支持LIMIT offset, count语法

## 架构设计

### 核心组件

1. **MySQLToDamengSqlConverter**: SQL转换器核心类
2. **DamengSqlInterceptor**: MyBatis拦截器
3. **SqlInterceptorConfig**: 配置管理类
4. **SqlSessionFactoryConfigurer**: 数据源配置工具类
5. **SqlInterceptorController**: 管理接口

### 工作流程

```
SQL执行请求 → MyBatis拦截器 → SQL转换器 → 达梦数据库
```

## 配置说明

### application.yml配置

```yaml
sql:
  interceptor:
    enabled: true                              # 是否启用拦截器
    convert-all-data-sources: true             # 是否对所有数据源进行转换
    cache-enabled: true                        # 是否启用SQL缓存
    max-cache-size: 1000                       # 缓存最大大小
    log-enabled: true                          # 是否记录转换日志
    log-level: INFO                            # 日志级别
    performance-monitor-enabled: false         # 是否启用性能监控
    performance-threshold: 100                 # 性能监控阈值(ms)
    
    # 功能开关
    function-conversion-enabled: true          # 函数转换
    keyword-conversion-enabled: true           # 关键字转换
    syntax-conversion-enabled: true            # 语法转换
    limit-conversion-enabled: true             # LIMIT转换
    date-function-conversion-enabled: true     # 日期函数转换
    string-function-conversion-enabled: true   # 字符串函数转换
    math-function-conversion-enabled: true     # 数学函数转换
    aggregate-function-conversion-enabled: true # 聚合函数转换
    conditional-function-conversion-enabled: true # 条件函数转换
    
    # 启用的数据源列表
    enabled-data-sources:
      - sas
      - user
      - activity
      - score
      - eval
```

### 数据源配置

每个数据源配置类都已集成SQL拦截器：

```java
@Configuration
@MapperScan(basePackages = "com.goodsogood.ows.mapper.sas", 
           sqlSessionFactoryRef = "sasSqlSessionFactory")
public class SasDataSourceConfig {
    
    @Autowired
    private SqlSessionFactoryConfigurer sqlSessionFactoryConfigurer;
    
    @Bean
    public SqlSessionFactory sasSqlSessionFactory(DataSource dataSource) throws Exception {
        return sqlSessionFactoryConfigurer.createSqlSessionFactory(dataSource);
    }
}
```

## 使用示例

### 1. 基本查询转换

**MySQL原始SQL:**
```sql
SELECT `user_id`, `user_name`, NOW() as current_time 
FROM `users` 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
ORDER BY created_at DESC 
LIMIT 10;
```

**转换后的达梦SQL:**
```sql
SELECT "user_id", "user_name", SYSDATE as current_time 
FROM "users" 
WHERE created_at >= DATEADD(DAY, -30, SYSDATE)
ORDER BY created_at DESC 
AND ROWNUM <= 10;
```

### 2. 复杂查询转换

**MySQL原始SQL:**
```sql
SELECT u.user_id, 
       CONCAT(u.first_name, ' ', u.last_name) as full_name,
       IF(u.status = 1, 'Active', 'Inactive') as status_text,
       DATE_FORMAT(u.created_at, '%Y-%m-%d') as created_date
FROM users u 
WHERE u.email LIKE '%@example.com'
ORDER BY u.created_at DESC 
LIMIT 20, 10;
```

**转换后的达梦SQL:**
```sql
SELECT * FROM (
    SELECT ROWNUM rn, t.* FROM (
        SELECT u.user_id, 
               CONCAT(u.first_name, ' ', u.last_name) as full_name,
               CASE WHEN u.status = 1 THEN 'Active' ELSE 'Inactive' END as status_text,
               TO_CHAR(u.created_at, 'YYYY-MM-DD') as created_date
        FROM users u 
        WHERE u.email LIKE '%@example.com'
        ORDER BY u.created_at DESC
    ) t WHERE ROWNUM <= 30
) WHERE rn > 20;
```

## 管理接口

### 1. 获取拦截器状态
```
GET /api/sql-interceptor/status
```

### 2. 启用/禁用拦截器
```
POST /api/sql-interceptor/enable
POST /api/sql-interceptor/disable
```

### 3. 管理数据源
```
POST /api/sql-interceptor/datasource/{dataSourceName}/enable
POST /api/sql-interceptor/datasource/{dataSourceName}/disable
GET /api/sql-interceptor/datasources
```

### 4. 测试SQL转换
```
POST /api/sql-interceptor/test-conversion
Content-Type: application/json

{
  "sql": "SELECT NOW() FROM users LIMIT 10"
}
```

### 5. 清除缓存
```
POST /api/sql-interceptor/clear-cache
```

### 6. 配置管理
```
GET /api/sql-interceptor/config
POST /api/sql-interceptor/config
```

## 支持的转换规则

### 函数映射表

| MySQL函数 | 达梦函数 | 说明 |
|-----------|----------|------|
| NOW() | SYSDATE | 当前时间 |
| CURDATE() | TRUNC(SYSDATE) | 当前日期 |
| LENGTH() | LEN() | 字符串长度 |
| SUBSTRING() | SUBSTR() | 子字符串 |
| CONCAT() | CONCAT() | 字符串连接 |
| IF() | CASE WHEN | 条件表达式 |
| IFNULL() | NVL() | 空值处理 |
| GROUP_CONCAT() | LISTAGG() | 聚合连接 |

### 关键字映射表

| MySQL关键字 | 达梦关键字 | 说明 |
|-------------|------------|------|
| AUTO_INCREMENT | IDENTITY(1,1) | 自增列 |
| TEXT | CLOB | 大文本类型 |
| DATETIME | TIMESTAMP | 日期时间类型 |
| \`column\` | "column" | 标识符引用 |
| LIMIT n | ROWNUM <= n | 分页限制 |

## 性能优化

1. **SQL缓存**: 已转换的SQL会被缓存，避免重复转换
2. **选择性转换**: 可以针对特定数据源或功能模块启用转换
3. **异常处理**: 转换失败时返回原始SQL，确保系统稳定性
4. **性能监控**: 可选的性能监控功能，记录转换耗时

## 注意事项

1. **测试验证**: 在生产环境使用前，请充分测试所有SQL语句的转换结果
2. **备份策略**: 建议保留原始MySQL语句的备份
3. **监控日志**: 启用日志记录，监控转换过程中的异常情况
4. **渐进式迁移**: 可以先对部分数据源启用转换，逐步扩展到全部数据源

## 扩展开发

### 添加自定义转换规则

1. **自定义函数映射**:
```yaml
sql:
  interceptor:
    custom-function-mapping:
      MY_FUNC: DM_FUNC
```

2. **自定义关键字映射**:
```yaml
sql:
  interceptor:
    custom-keyword-mapping:
      MY_KEYWORD: DM_KEYWORD
```

### 添加新的转换逻辑

继承`MySQLToDamengSqlConverter`类，重写相关方法：

```java
@Component
public class CustomSqlConverter extends MySQLToDamengSqlConverter {
    
    @Override
    protected String handleSpecialSyntax(String sql) {
        // 添加自定义转换逻辑
        sql = super.handleSpecialSyntax(sql);
        // 自定义处理
        return sql;
    }
}
```

## 故障排除

### 常见问题

1. **转换失败**: 检查日志，确认SQL语法是否正确
2. **性能问题**: 调整缓存大小，启用性能监控
3. **配置不生效**: 检查配置文件格式，重启应用
4. **部分SQL未转换**: 检查数据源配置和白名单设置

### 调试模式

启用DEBUG日志级别查看详细转换过程：

```yaml
sql:
  interceptor:
    log-level: DEBUG
```

## 版本历史

- v1.0.0: 初始版本，支持基本的MySQL到达梦语法转换
- 支持多数据源配置
- 支持复杂SQL场景
- 提供管理接口和监控功能
