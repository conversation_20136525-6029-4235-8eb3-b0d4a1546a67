-- 建表
CREATE TABLE t_rank_column (column_id int(10) NOT NULL AUTO_INCREMENT comment '项id', top_id int(10) NOT NULL comment '外键', column_name varchar(100) NOT NULL comment '项名称', upper_limit int(10) comment '分值上限', single_choice int(10) NOT NULL comment '是否单项选择 1是0否', parent_id int(10) comment '父级项id', level tinyint(1) NOT NULL comment '层级 只有1、2、3层', PRIMARY KEY (column_id)) comment='评分栏目表';
CREATE TABLE t_rank_org_score (org_score_id int(10) NOT NULL AUTO_INCREMENT comment '主键', score_rule_id int(10) NOT NULL comment '规则id 外键', score_rule_name varchar(100) NOT NULL comment '对应规则名称', org_id int(10) NOT NULL comment '组织id', org_name varchar(100) NOT NULL comment '组织名称', time varchar(50) NOT NULL comment '时间', score int(10) NOT NULL comment '得分', percentage double NOT NULL comment '计算系数 例如30%用0.3表示', top_id int(10) NOT NULL comment '顶级类别id', PRIMARY KEY (org_score_id)) comment='组织评分表';
CREATE TABLE t_rank_org_score_detail (org_score_detail_id int(10) NOT NULL AUTO_INCREMENT comment '主键', org_id int(10) NOT NULL comment '组织id', org_name varchar(100) NOT NULL comment '组织名称', org_level varchar(100) NOT NULL comment '组织架构', score int(10) NOT NULL comment '得分', score_rule_id int(10) NOT NULL comment '对应的规则id', year int(10) NOT NULL comment '年', create_time datetime NOT NULL comment '创建时间', create_user_id int(10) NOT NULL comment '创建人id', create_user_name varchar(50) NOT NULL comment '创建人名称', top_id int(10) NOT NULL comment '顶级类别id', PRIMARY KEY (org_score_detail_id)) comment='组织评分流水表';
CREATE TABLE t_rank_rate_details (rate_details_id int(10) NOT NULL AUTO_INCREMENT comment '主键', rate_rule_id int(10) NOT NULL comment '外键', sort int(10) NOT NULL comment '排序', rate_name varchar(100) NOT NULL comment '等级名称', compare_type tinyint comment '比较类型 1大于 2小于 3大于等于 4小于等于 5等于', compare_content tinyint NOT NULL comment '比较内容 1平均分 2排名 3第一名 4一票否决', coefficient double comment '系数 例如0.7', PRIMARY KEY (rate_details_id)) comment='评级详情表';
CREATE TABLE t_rank_rate_rule (rate_rule_id int(10) NOT NULL AUTO_INCREMENT comment '评级规则id', rule_name varchar(100) NOT NULL comment '评级规则名称', year int(10) NOT NULL comment '评级年度', deadline date NOT NULL comment '填报截止时间', status tinyint NOT NULL comment '1正常 2已截止', type tinyint(1) NOT NULL comment '1人员 2组织', PRIMARY KEY (rate_rule_id)) comment='评级规则表';
CREATE TABLE t_rank_score_rule (score_rule_id int(10) NOT NULL AUTO_INCREMENT comment '评分规则id', column_id int(10) NOT NULL comment '外键', rule_description varchar(255) NOT NULL comment '规则描述', score_rule tinyint NOT NULL comment '打分逻辑 1加分 2减分 3清零', score varchar(50) NOT NULL comment '分值 如果是区间则用1-100表示', is_range tinyint(3) NOT NULL comment '是否范围打分 1是 0否', default_rule tinyint NOT NULL comment '是否是默认规则 1是 0否', PRIMARY KEY (score_rule_id)) comment='评分规则表';
CREATE TABLE t_rank_top (top_id int(10) NOT NULL AUTO_INCREMENT comment '主键', top_name varchar(100) NOT NULL comment '类型名称', type tinyint(1) NOT NULL comment '1人员 2组织', percentage double NOT NULL comment '计算系数 例如30%用0.3表示', PRIMARY KEY (top_id));
CREATE TABLE t_rank_user_score (user_score_id int(10) NOT NULL AUTO_INCREMENT comment '主键', score_rule_id int(10) NOT NULL comment '外键', score_rule_name varchar(100) NOT NULL comment '对应规则名称', user_id int(10) NOT NULL comment '用户id', user_name varchar(50) NOT NULL comment '姓名', org_id int(10) NOT NULL comment '所在组织', time varchar(50) NOT NULL comment '时间', score int(10) NOT NULL comment '得分', percentage double NOT NULL comment '计算系数 例如30%用0.3表示', top_id int(10) NOT NULL comment '顶级类别id', PRIMARY KEY (user_score_id)) comment='人员评分表';
CREATE TABLE t_rank_user_score_detail (user_score_detail_id int(10) NOT NULL AUTO_INCREMENT comment '人员评分流水主键', user_id int(10) NOT NULL comment '用户id', user_name varchar(100) NOT NULL comment '用户名称', org_id int(10) NOT NULL comment '所属组织id', org_level varchar(100) NOT NULL comment '组织结构', score int(10) NOT NULL comment '得分', score_rule_id int(10) NOT NULL comment '对应的规则id', year int(10) NOT NULL comment '年', create_time datetime NOT NULL comment '录入时间', create_user_id int(10) NOT NULL comment '录入人id', create_user_name varchar(50) NOT NULL comment '录入人名称', top_id int(10) NOT NULL comment '顶级类别id', PRIMARY KEY (user_score_detail_id)) comment='人员评分流水表';
ALTER TABLE t_rank_column ADD CONSTRAINT FKt_rank_col675230 FOREIGN KEY (top_id) REFERENCES t_rank_top (top_id);
ALTER TABLE t_rank_org_score ADD CONSTRAINT FKt_rank_org391280 FOREIGN KEY (score_rule_id) REFERENCES t_rank_score_rule (score_rule_id);
ALTER TABLE t_rank_rate_details ADD CONSTRAINT FKt_rank_rat766218 FOREIGN KEY (rate_rule_id) REFERENCES t_rank_rate_rule (rate_rule_id);
ALTER TABLE t_rank_user_score ADD CONSTRAINT FKt_rank_use839752 FOREIGN KEY (score_rule_id) REFERENCES t_rank_score_rule (score_rule_id);
ALTER TABLE t_rank_score_rule ADD CONSTRAINT FKt_rank_sco162234 FOREIGN KEY (column_id) REFERENCES t_rank_column (column_id);

-- 索引
ALTER TABLE `t_rank_org_score`
ADD INDEX `user_id_index`(`user_id`) USING BTREE;
ADD INDEX `time_index`(`time`(10)) USING BTREE,
ADD INDEX `score_rule_name_index`(`score_rule_name`(100)) USING BTREE;

ALTER TABLE `t_rank_user_score`
ADD INDEX `org_id_index`(`org_id`) USING BTREE;
ADD INDEX `time_index`(`time`(10)) USING BTREE,
ADD INDEX `score_rule_name_index`(`score_rule_name`(100)) USING BTREE;

-- 自动打分数据库初始化
-- t_rank_top
INSERT INTO `ows-saas-test`.`t_rank_top`(`top_id`, `top_name`, `type`, `percentage`) VALUES (1, '组织自动打分', 2, 1);
INSERT INTO `ows-saas-test`.`t_rank_top`(`top_id`, `top_name`, `type`, `percentage`) VALUES (2, '党员自动打分', 1, 1);

-- t_rank_column
INSERT INTO `ows-saas-test`.`t_rank_column`(`column_id`, `top_id`, `column_name`, `upper_limit`, `single_choice`, `parent_id`, `level`) VALUES (1, 1, '基础信息完整度', 17, 0, NULL, 1);
INSERT INTO `ows-saas-test`.`t_rank_column`(`column_id`, `top_id`, `column_name`, `upper_limit`, `single_choice`, `parent_id`, `level`) VALUES (2, 1, '基础工作', 63, 0, NULL, 1);
INSERT INTO `ows-saas-test`.`t_rank_column`(`column_id`, `top_id`, `column_name`, `upper_limit`, `single_choice`, `parent_id`, `level`) VALUES (3, 1, '组织建设', 35, 0, NULL, 1);
INSERT INTO `ows-saas-test`.`t_rank_column`(`column_id`, `top_id`, `column_name`, `upper_limit`, `single_choice`, `parent_id`, `level`) VALUES (4, 1, '活跃程度', 20, 0, NULL, 1);
INSERT INTO `ows-saas-test`.`t_rank_column`(`column_id`, `top_id`, `column_name`, `upper_limit`, `single_choice`, `parent_id`, `level`) VALUES (5, 1, '优秀组织', 5, 0, NULL, 1);
INSERT INTO `ows-saas-test`.`t_rank_column`(`column_id`, `top_id`, `column_name`, `upper_limit`, `single_choice`, `parent_id`, `level`) VALUES (6, 2, '基础工作', 60, 0, NULL, 1);
INSERT INTO `ows-saas-test`.`t_rank_column`(`column_id`, `top_id`, `column_name`, `upper_limit`, `single_choice`, `parent_id`, `level`) VALUES (7, 2, '活跃程度', 30, 0, NULL, 1);
INSERT INTO `ows-saas-test`.`t_rank_column`(`column_id`, `top_id`, `column_name`, `upper_limit`, `single_choice`, `parent_id`, `level`) VALUES (8, 2, '优秀党员', 3, 0, NULL, 1);
INSERT INTO `ows-saas-test`.`t_rank_column`(`column_id`, `top_id`, `column_name`, `upper_limit`, `single_choice`, `parent_id`, `level`) VALUES (9, 2, '领导干部示范带头', 10, 0, NULL, 1);

-- t_rank_score_rule
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (1, 1, '组织信息完整性', 1, '0-8', 1, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (2, 1, '党员信息完整度', 1, '0-9', 1, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (3, 2, '党支部组织生活开展情况', 1, '20', 0, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (4, 2, '党费收缴情况', 1, '0-24', 1, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (5, 2, '民主评议结果', 1, '5', 0, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (6, 2, '述职评议结果', 1, '0-4', 1, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (7, 2, '领导班子成员支部联系点情况', 1, '0-10', 1, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (8, 3, '届次换届', 1, '0-35', 1, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (9, 4, '消费扶贫', 1, '0-4', 1, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (10, 4, '在线学习', 1, '0-4', 1, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (11, 4, '积分换书', 1, '0-4', 1, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (12, 4, '志愿活动', 1, '0-4', 1, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (13, 4, '捐款', 1, '0-4', 1, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (14, 5, '奖惩登记', 1, '5', 0, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (15, 6, '组织生活', 1, '0-35', 1, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (16, 6, '党费', 1, '0-20', 1, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (17, 6, '民主评议', 1, '5', 0, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (18, 7, '消费扶贫', 1, '5', 0, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (19, 7, '积分换书', 1, '5', 0, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (20, 7, '志愿者', 1, '5', 0, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (21, 7, '学习', 1, '5', 0, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (22, 7, '一元捐', 1, '5', 0, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (23, 7, '互动', 1, '5', 0, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (24, 8, '奖惩登记', 1, '3', 0, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (25, 9, '双重组织生活', 1, '5', 0, 0);
INSERT INTO `ows-saas-test`.`t_rank_score_rule`(`score_rule_id`, `column_id`, `rule_description`, `score_rule`, `score`, `is_range`, `default_rule`) VALUES (26, 9, '讲党课', 1, '5', 0, 0);
