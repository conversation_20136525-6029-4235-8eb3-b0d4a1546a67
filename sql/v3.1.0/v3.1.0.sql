-- 同步2020年数据 t_statistical_user_org_life到t_statistical_leader_org_life
UPDATE  t_statistical_leader_org_life a,t_statistical_user_org_life b
SET a.participate_num=b.participate_num
WHERE
a.leader_user_id=b.user_id
AND a.activity_type_id=b.type_id
AND a.statistical_year = b.statistical_year
AND a.statistical_month=b.statistical_month
AND a.statistical_year=2020;

-- 同步2020年数据 t_statistical_leader_org_life到t_eval_statistical_leader_org_life
UPDATE t_eval_statistical_leader_org_life a,(
       SELECT leader_user_id leaderUserId,
              leader_name leaderName,
              org_id orgId,
              org_name orgName,
              org_type_id orgTypeId,
              parent_org_id parentOrgId,
              org_level orgLevel,
              is_retire isRetire,
              SUM(participate_num) participateNum,
              statistical_date statisticalDate
         FROM t_statistical_leader_org_life where 1=1 and region_id=3
        GROUP BY leader_user_id, statistical_date ) b
SET a.participate_num = b.participateNum
	WHERE a.leader_user_id=b.leaderUserId
	AND a.org_id=b.orgId AND a.statistical_date=b.statisticalDate
	AND a.statistical_year=2020;