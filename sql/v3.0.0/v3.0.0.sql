ALTER TABLE `t_statistical_config_info` add `region_id` bigint(20) COMMENT '区县ID' AFTER `update_time`;
ALTER TABLE `t_statistical_config_info` ADD INDEX `region_id`(`region_id`);

alter table t_statistical_user_temp add `region_id` bigint(20) comment '区县Id' after org_id;
alter table t_statistical_user_temp add  index region_id(region_id);

alter table t_statistical_org_life add `region_id` bigint(20) comment '区县Id' after org_id;
alter table t_statistical_org_life add  index region_id(region_id);

alter table t_statistical_leader_org_life add `region_id` bigint(20) comment '区县Id' after org_id;
alter table t_statistical_leader_org_life add  index region_id(region_id);

alter table t_statistical_party_fee add `region_id` bigint(20) comment '区县Id' after org_id;
alter table t_statistical_party_fee add  index region_id(region_id);

alter table t_statistical_org_life_view add `region_id` bigint(20) comment '区县Id' after org_id;
alter table t_statistical_org_life_view add  index region_id(region_id);

ALTER TABLE `t_statistical_user_org_life` add `region_id` bigint(20) COMMENT '区县ID' AFTER `update_time`;
ALTER TABLE `t_statistical_user_org_life` ADD INDEX `region_id`(`region_id`);

ALTER TABLE `t_statistical_user_temp`
MODIFY COLUMN `org_level` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL
COMMENT '组织父级路径' AFTER `parent_id`;

ALTER TABLE `t_statistical_user_temp`
DROP INDEX `org_level`,
ADD INDEX `org_level`(`org_level`(100)) USING BTREE;

UPDATE t_statistical_org_life set region_id=3;
UPDATE t_statistical_user_temp set region_id=3;
UPDATE t_statistical_leader_org_life set region_id=3;
UPDATE t_statistical_party_fee set region_id=3;
UPDATE t_statistical_org_life_view set region_id=3;
UPDATE t_statistical_user_org_life set region_id=3;
UPDATE t_statistical_config_info set region_id=3;


UPDATE t_file SET file_id=file_id+1000000;
UPDATE t_file SET create_user=create_user+100000;
UPDATE t_statistical_org_life SET org_life_id=org_life_id+1000000;
UPDATE t_statistical_org_life SET org_id=org_id+100000;
UPDATE t_statistical_leader_org_life SET leader_org_life_id=leader_org_life_id+1000000;
UPDATE t_statistical_leader_org_life SET org_id=org_id+100000;
UPDATE t_statistical_storage_variable SET storage_val_id=storage_val_id+1000000;
UPDATE t_statistical_storage_variable SET org_id=org_id+100000;
UPDATE t_statistical_party_fee SET party_fee_id=party_fee_id+1000000;
UPDATE t_statistical_party_fee SET org_id=org_id+100000;
UPDATE t_statistical_user_org_life SET user_org_life_id=user_org_life_id+200000000;
UPDATE t_statistical_user_org_life SET user_id=user_id+100000;
下列表不用操作 相当于临时存储表
t_statistical_org_life_view
t_statistical_user_temp
t_statistical_temp_activity
t_statistical_config_info
t_statistical_data_record
