drop PROCEDURE IF EXISTS sp_split;
DROP TEMPORARY TABLE IF EXISTS temp_split;
CREATE  PROCEDURE sp_split(in c1 varchar(2000),in split1 varchar(20),in addValues BIGINT(20))
BEGIN
CREATE TEMPORARY TABLE IF NOT EXISTS temp_split
(
   col BIGINT(20)
);
DELETE FROM temp_split;
while(instr(c1,split1)<>0) DO

IF  c1 !='' and c1 >= 3 then
	insert temp_split(col) values (CONVERT(substring(c1,1,instr(c1,split1)-1),SIGNED)+addValues);
ELSE
	insert temp_split(col) values (substring(c1,1,instr(c1,split1)-1));
end if;
set c1 = INSERT(c1,1,instr(c1,split1),'');
END WHILE;
IF   c1 !='' then
 insert temp_split(col) values (c1);
end if;

END;

-- 创建存储过程之前需判断该存储过程是否已存在，若存在则删除
DROP PROCEDURE IF EXISTS generate_sta_leader_org_life;
-- 创建存储过程
CREATE PROCEDURE generate_sta_leader_org_life(in regionId BIGINT(20),in addValues BIGINT(20))
BEGIN
-- 定义变量
DECLARE done INT DEFAULT 0;
DECLARE orgLevel varchar(2000);
DECLARE pId BIGINT(20);
-- 定义游标，并将sql结果集赋值到游标中
DECLARE curLevel CURSOR FOR select org_level orgLevel,leader_org_life_id pId from t_statistical_leader_org_life where region_id=regionId;
-- 打开游标
declare continue handler for not FOUND set done = 1;
open curLevel;

 /* 循环开始 */
 REPEAT
-- 将游标中的值赋值给变量，注意：变量名不要和返回的列名同名，变量顺序要和sql结果列的顺序一致
fetch curLevel into orgLevel,pId;
-- 执行业务逻辑
call sp_split(SUBSTR(orgLevel,2),'-',addValues);
UPDATE t_statistical_leader_org_life set org_level = (select CONCAT('-',GROUP_CONCAT(col SEPARATOR '-'),'-') from temp_split ) where leader_org_life_id = pId;
-- 将游标中的值再赋值给变量，供下次循环使用

-- 当s等于1时表明遍历以完成，退出循环
until done end repeat;
-- 关闭游标
close curLevel;
END;


call generate_sta_leader_org_life(6,100000);




