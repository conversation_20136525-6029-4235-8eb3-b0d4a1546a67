CREATE TABLE `t_menu_route`  (
  `menu_route_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `menu_id` varchar(20) NULL,
  `name` varchar(20) NULL,
  `menu_route` varchar(200) NULL,
  PRIMARY KEY (`menu_route_id`)
);

CREATE TABLE `t_dss_history`  (
  `dss_history_id` bigint(20) NOT NULL AUTO_INCREMENT comment '辅助决策历史表',
  `user_id` bigint(20) NULL comment '操作人',
  `time` datetime NULL comment '操作时间',
  `ip`   varchar(150) NULL comment '操作人IP地址',
  `region_id`   bigint(20) NULL comment '区县ID',
  `type` int(2) NULL comment '操作类型 1-生成数据，2-下载文件',
  `url` varchar(200) NULL comment '操作接口名称',
  PRIMARY KEY (`dss_history_id`)
);

INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0101','通知消息','notification-message');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0102','审批管理','secondaryDirectory/0102');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('010201','发起审批','new-process');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('010202','我的审批','my-process');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('010203','审批类型管理','type-process');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0105','人员管理','secondaryDirectory/0105');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('010501','人员管理','personnel-manage');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('010502','权限角色管理','root-manage');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0106','部门管理','department-manage');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0107','标签管理','tag-manage');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0108','系统管理员管理','system-manage');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0109','账户设置','secondaryDirectory/0109');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('010901','密码修改','user-info-password');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('010902','手机号修改','user-info-phone');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0110','组织信息','unit-information');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0113','新闻管理','news-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('02','互动中心','activity');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0201','互动发布','secondaryDirectory/0201');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('020101','投票','new-vote');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('020102','问卷调查','questionnaire');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('020103','有奖竞答','contest');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('020104','线下互动','physical');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('020105','积分捐赠','contribute');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0202','互动管理','all-activity');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0203','互动分类','activity-list');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0204','互动奖励','secondaryDirectory/0204');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('020401','奖品发布','create-article');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('020402','奖品管理','article-manage');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('020403','中奖记录','winning-history');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('020404','奖品发货','prize-delivery');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('04','组织及党员管理','organize');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0401','组织大数据','organize-framework');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0403','单位领导班子','leader-group');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0404','党组织委员会','party-organization');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0405','党小组','party-group');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('06','上传资源','upload');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0601','上传资源','upload-resource');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0703','权限管理','authority-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0801','通知消息','notification-message');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0802','消息管理','message-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0803','模板设置','template-setting');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0901','发布资源','release-resources');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0902','管理资源','resources-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1001','统计看板','secondaryDirectory/1001');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('100101','积分兑换统计','integral-exchange-statisitics');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1002','智能查询','secondaryDirectory/1002');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('100201','积分兑换明细','integral-exchange');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('100202','分级平台公众号列表','weixin-accounts');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('100203','销售清单明细查询','order-statistics');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2002','积分兑换设置','integral-rule');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2603','扶贫主页','baweiyuzhen');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2604','扶贫专题页','poverty-alleviation-theme');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1003','统计设置','statistical-setting');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1101','新闻发布','create-news');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1102','新闻管理','news-list');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1103','新闻栏目','news-column');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1201','派发任务','meeting-topic-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1202','活动类型','assess-plan-manage');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1203','活动回顾','check-plan');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1204','基础设置','docu-manage-base-setting');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1301','任务查看','task-manage-base-setting');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1302','活动录入','meeting-manage');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1401','设置','weixin-bind');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1402','新闻矩阵','news-matrix');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1403','互动矩阵','activitys-matrix');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1501','党费设置','party-setting');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1502','收交统计','party-pay-statistics');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1503','党费标准','party-normal');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1505','党费返还','party-return');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1504','党费返还','dues-return');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1506','党费返还记录','party-history');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1507','党费对账报表','party-fee-reconciliation');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1508','党费交纳流水','party-pay-flow');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('18','七一书院','library-islogin');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2001','考核设置','score-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2004','党费账户设置','party-return-accounts');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2101','考核任务管理','examination-tasks-list');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2102','任务执行','execute-task');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2103','任务审核','audit-task');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2104','问题查处','question-search');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2106','考核统计','evaluation-statistics');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2107','考核评价','assessment-list');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('3602','基层组织述职评议','project-review');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('3601','述职评议统计','project-statistical');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('3502','民主评议党员','democracy-member');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('3501','民主评议统计','democracy-statistical');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0704','升级日志管理','version-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2201','党支部组织生活统计','party-branch-report');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2202','领导干部双重组织生活统计','leading-cadres-report');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2203','党费交纳完成情况统计','payment-count');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2206','考核报表','evaluation-report');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2204','党员组织生活统计','party-member-org-life');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2205','组织生活一览表','org-life-view');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2207','综合报表库','comprehensive-report-library');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2209','人员查询','report-iframe/2209');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2210','组织基本信息','report-iframe/2210');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2003','统计报表设置','report-setting');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('23','发送记录','send-message-record');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('24','消息发送','send-message');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('25','账户系统','accounts-stylem');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('27','党员画像','member-photo');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('28','组织画象','org-photo');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('29','一元捐活动分析','member-oneMoney');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('32','大数据','static/charts');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0406','成员管理','member-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2601','供应渠道管理','channel-registration');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('2602','商城链接工具','channel-generator-url');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('0407','组织管理','org-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('34','主题推送策略','push-strategy');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('3701','组织奖惩','org-report-comment');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('3702','党员奖惩','party-report-comment');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('38','日程管理','schedule-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('39','志愿团体','voluntary-group-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('40','志愿者','volunteer');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('4101','本团体项目','volunteer-project');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('4102','下级团体项目','subordinate-volunteer-project');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('42','求助信息','volunteer-seek-help');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('43','送服务','deliver-service');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1601','任务查看','task-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1602','纪实管理','meeting-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1603','请假审批','leave-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1604','党费管理','party-fee-statistical');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1605','监督预警','supervision-warning');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1704','活动回顾','meeting-history');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1606','活动录入','meeting-entering');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1607','电子党务报告','party-affairs-report');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1608','组织管理','org-manager');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1609','组织报告','org-report');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1610','捐款统计','juankuantongji');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1701','我的任务','my-meeting');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1702','党费交纳','party-fee-index');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1703','线上互动','ac');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1705','共产党员一元捐','sign-up-activity');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1707','消费扶贫','baweiyuzhen');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1708','主题推送','theme-propelling');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1709','个人报告','person-report');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1710','我要捐款','woyaojuankuan');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1713','日程管理','schedule-management');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1715','志愿服务','volunteer-home');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('1716','政策文件','resource-lib/7');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('3301','会议指南','meeting-agenda');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('3302','通讯录','chongqing-symposium-member-list');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('3303','重庆介绍','chongqing-info-list');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('3304','天气预报','weather-table');
INSERT INTO t_menu_route(menu_id, `name`, menu_route) VALUES('3305','活动报道','first-column-for-meeting');

-- 登录日志表加入索引
ALTER TABLE `t_user_login_log`
ADD INDEX `login_id_idnex`(`login_id`) USING BTREE,
ADD INDEX `org_id_index`(`org_id`) USING BTREE,
ADD INDEX `region_id_index`(`region_id`) USING BTREE,
ADD INDEX `login_date_index`(`login_date`) USING BTREE;

-- mongoDB
db.getCollection("PageView").createIndex({
    userId: "hashed"
}, {
    name: "user_id"
})

db.getCollection("PageView").createIndex({
    transferTime: NumberInt("-1")
}, {
    name: "transfer_time"
})

db.getCollection("PageView").createIndex({
    object: "hashed"
}, {
    name: "object"
})