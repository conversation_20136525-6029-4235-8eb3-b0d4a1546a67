CREATE TABLE t_sas_score_manager_flow
(
    flow_id            bigint(19) NOT NULL AUTO_INCREMENT,
    region_id          bigint(19) NOT NULL,
    data_id            bigint(20) NOT NULL comment '根据类型区分 可能是组织id,用户id,党组id....',
    data_type          tinyint(3) NOT NULL comment '1:组织id 2:人员id 3:党组id',
    source_data_id     bigint(20) NOT NULL comment '业务数据id',
    source_data_id_str varchar(100) NOT NULL comment '业务数据id str',
    type               tinyint(3) NOT NULL comment '业务类型',
    type_str           varchar(255) NOT NULL comment '类型说明',
    status             tinyint(3) DEFAULT 1 NOT NULL comment '0:不需要发送 1:新增待发送 2:发送成功 3:发送失败',
    is_del             tinyint(3) NOT NULL comment '1:有效 0:无效',
    handle_time        varchar(100) NOT NULL comment '该数据应该所处的时间',
    token              varchar(255) comment '调用积分中心提交的token',
    score              bigint(20) NOT NULL comment '积分',
    score_type         tinyint(3) NOT NULL comment '积分类型',
    send_type          tinyint(3) NOT NULL comment '0:加分 1:减分',
    send_time          datetime NULL comment '发送时间',
    create_user        bigint(19) NOT NULL,
    create_time        datetime     NOT NULL,
    update_user        bigint(20),
    change_time        datetime NULL,
    var1               varchar(500) comment '冗余字段 业务审查',
    var2               varchar(500) comment '冗余字段 业务审查',
    var3               varchar(500) comment '冗余字段 业务审查',
    var4               varchar(500) comment '冗余字段 业务审查',
    var5               varchar(500) comment '冗余字段 业务审查',
    PRIMARY KEY (flow_id),
    CONSTRAINT unique_source_data_id UNIQUE (region_id, is_del, handle_time, data_type, data_id, type, source_data_id),
    CONSTRAINT unique_source_data_str UNIQUE (region_id, is_del, handle_time, data_type, data_id, type,
                                              source_data_id_str),
    INDEX (region_id),
    INDEX (data_id),
    INDEX (source_data_id),
    INDEX (source_data_id_str),
    INDEX ( type)
) comment='组织积分管理';
CREATE TABLE t_sas_score_manager_ratio
(
    ratio_id    bigint(20) NOT NULL AUTO_INCREMENT,
    region_id   bigint(20) NOT NULL,
    org_id      bigint(20) NOT NULL,
    type        tinyint(3) NOT NULL comment '1:组织平均登录率 2:组织浏览新闻完成率',
    total       int(10) NOT NULL,
    find        int(10) NOT NULL,
    `date`      date     NOT NULL,
    ratio       float    NOT NULL comment '比例 如果为-1 说明没有人员数据,在计算时不选取当前天',
    create_user bigint(20) NOT NULL,
    create_time datetime NOT NULL,
    update_user bigint(20),
    change_time datetime NULL comment '变更时间',
    PRIMARY KEY (ratio_id),
    CONSTRAINT unique_queryAndInsert UNIQUE (region_id, type, org_id, `date`),
    INDEX (org_id),
    INDEX (`date`)
) comment='日比率数据';




alter table t_overview_option add column alias_name varchar(255) comment '新别名';
alter table t_overview_option add column period_str varchar(255) comment '周期';
update t_overview_option set alias_name='党员大会',period_str='本季度' where overview_option_id=39 and option_type=1 and project_name='overview';
update t_overview_option set alias_name='党课',period_str='本季度' where overview_option_id=40 and option_type=2 and project_name='overview';
update t_overview_option set alias_name='支委会',period_str='本月' where overview_option_id=41 and option_type=3 and project_name='overview';
update t_overview_option set alias_name='党小组会',period_str='本月' where overview_option_id=42 and option_type=4 and project_name='overview';
update t_overview_option set alias_name='主题党日',period_str='本月' where overview_option_id=43 and option_type=5 and project_name='overview';
update t_overview_option set alias_name='组织生活会',period_str='本年' where overview_option_id=44 and option_type=6 and project_name='overview';
update t_overview_option set alias_name='民主生活会',period_str='本年' where overview_option_id=45 and option_type=7 and project_name='overview';
