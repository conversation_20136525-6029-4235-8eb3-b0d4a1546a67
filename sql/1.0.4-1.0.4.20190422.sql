DROP TABLE IF EXISTS t_statistical_data_record;
CREATE TABLE `t_statistical_data_record` (
  `static_data_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` bigint(20) NOT NULL COMMENT '组织id',
  `org_name` varchar(255) NOT NULL COMMENT '组织名称',
  `org_type_id` int(11) NOT NULL COMMENT '组织类型id',
  `parent_org_id` bigint(20) NOT NULL COMMENT '父级组织Id',
  `org_level` varchar(50) DEFAULT NULL COMMENT '组织父级路径',
  `is_retire` tinyint(3) NOT NULL COMMENT '是否离退休党组织 1-是 2-否',
  `activity_type_id` int(11) DEFAULT NULL COMMENT '活动类型id',
  `activity_type_name` varchar(255) DEFAULT NULL COMMENT '活动类型名称',
  `participate_num` int(11) DEFAULT '0' COMMENT '参与次数',
  `statistical_year` int(4) DEFAULT NULL,
  `statistical_month` int(11) DEFAULT NULL COMMENT '统计的月（1-12）',
  `statistical_date` varchar(50) DEFAULT NULL COMMENT '统计日期',
  `data_type` tinyint(3) NOT NULL COMMENT '当前数据类型（以后按照统计类型添加），1-组织生活统计，2-干部双重组织生活，3-党费缴纳情况统计',
  `status` tinyint(1) DEFAULT NULL COMMENT '逻辑状态:(1-有效, 0-无效)',
  `user_id` bigint(20) DEFAULT NULL COMMENT '领导人ID',
  `user_name` varchar(20) DEFAULT NULL COMMENT '领导人名称',
  `payable_person_num` int(11) DEFAULT NULL COMMENT '应交人数（默认为0）',
  `unpaid_person_num` int(11) DEFAULT NULL COMMENT '党费未缴纳人数',
  `create_time` datetime NOT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`static_data_id`) USING BTREE,
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `org_type_id` (`org_type_id`) USING BTREE,
  KEY `parent_org_id` (`parent_org_id`) USING BTREE,
  KEY `is_retire` (`is_retire`) USING BTREE,
  KEY `activity_type_id` (`activity_type_id`) USING BTREE,
  KEY `statistical_year` (`statistical_year`) USING BTREE,
  KEY `statistical_month` (`statistical_month`) USING BTREE,
  KEY `statistical_date` (`statistical_date`) USING BTREE,
  KEY `data_type` (`data_type`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='统计流水表';


DROP TABLE IF EXISTS t_statistical_org_life;
CREATE TABLE `t_statistical_org_life` (
  `org_life_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` bigint(20) NOT NULL COMMENT '组织id',
  `org_name` varchar(255) NOT NULL COMMENT '组织名称',
  `org_type_id` int(11) NOT NULL COMMENT '组织类型id',
  `parent_org_id` bigint(20) NOT NULL COMMENT '父级组织Id',
  `org_level` varchar(50) DEFAULT NULL COMMENT '组织父级路径',
  `is_retire` tinyint(3) NOT NULL COMMENT '是否离退休党组织 1-是 2-否',
  `activity_type_id` int(11) NOT NULL COMMENT '活动类型id',
  `activity_type_name` varchar(255) NOT NULL COMMENT '活动类型名称',
  `participate_num` int(11) NOT NULL DEFAULT '0' COMMENT '参与次数',
  `statistical_year` int(4) NOT NULL COMMENT '统计年份',
  `statistical_month` int(11) NOT NULL COMMENT '统计的月（1-12）',
  `statistical_date` varchar(50) DEFAULT NULL COMMENT '统计时间(yyyy-MM-dd)',
  `status` tinyint(1) DEFAULT NULL COMMENT '逻辑状态:(1-有效, 0-无效)',
  `create_time` datetime NOT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`org_life_id`) USING BTREE,
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `org_type_id` (`org_type_id`) USING BTREE,
  KEY `parent_org_id` (`parent_org_id`) USING BTREE,
  KEY `is_retire` (`is_retire`) USING BTREE,
  KEY `activity_type_id` (`activity_type_id`) USING BTREE,
  KEY `statistical_year` (`statistical_year`) USING BTREE,
  KEY `statistical_month` (`statistical_month`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='党支部组织生活';


DROP TABLE IF EXISTS t_statistical_leader_org_life;
CREATE TABLE `t_statistical_leader_org_life` (
  `leader_org_life_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `leader_user_id` bigint(20) DEFAULT NULL COMMENT '领导人ID',
  `leader_name` varchar(20) NOT NULL COMMENT '领导人名字',
  `org_id` bigint(20) NOT NULL COMMENT '组织id',
  `org_name` varchar(255) NOT NULL COMMENT '组织名称',
  `org_type_id` int(11) NOT NULL COMMENT '组织类型id',
  `parent_org_id` bigint(20) NOT NULL COMMENT '父级组织Id',
  `org_level` varchar(50) DEFAULT NULL COMMENT '组织父级路径',
  `is_retire` tinyint(3) NOT NULL COMMENT '是否离退休党组织 1-是 2-否',
  `activity_type_id` varchar(50) NOT NULL COMMENT '活动类型id',
  `activity_type_name` varchar(50) NOT NULL COMMENT '活动类型名称',
  `participate_num` int(11) NOT NULL DEFAULT '0' COMMENT '参与次数',
  `statistical_year` int(4) NOT NULL COMMENT '统计年份',
  `statistical_month` int(11) NOT NULL COMMENT '统计的月（1-12）',
  `statistical_date` varchar(50) DEFAULT NULL COMMENT '统计时间yyyy-MM)',
  `status` tinyint(1) DEFAULT NULL COMMENT '逻辑状态:(1-有效, 0-无效)',
  `create_time` datetime NOT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`leader_org_life_id`) USING BTREE,
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `org_type_id` (`org_type_id`) USING BTREE,
  KEY `parent_org_id` (`parent_org_id`) USING BTREE,
  KEY `is_retire` (`is_retire`) USING BTREE,
  KEY `activity_type_id` (`activity_type_id`) USING BTREE,
  KEY `statistical_year` (`statistical_year`) USING BTREE,
  KEY `statistical_month` (`statistical_month`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COMMENT='领导干部双重组织生活';


DROP TABLE IF EXISTS t_statistical_party_fee;
CREATE TABLE `t_statistical_party_fee` (
  `party_fee_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` bigint(20) NOT NULL COMMENT '组织id',
  `org_name` varchar(255) NOT NULL COMMENT '组织名称',
  `org_type_id` int(11) NOT NULL COMMENT '组织类型',
  `parent_org_id` bigint(20) NOT NULL COMMENT '父级组织Id',
  `org_level` varchar(50) DEFAULT NULL COMMENT '组织父级路径',
  `is_retire` tinyint(3) NOT NULL COMMENT '是否离退休党组织 1-是 2-否',
  `payable_person_num` int(11) NOT NULL DEFAULT '0' COMMENT '应交人数（默认为0）',
  `unpaid_person_num` int(11) NOT NULL DEFAULT '0' COMMENT '未交纳人数',
  `statistical_year` int(4) NOT NULL COMMENT '统计年份',
  `statistical_month` int(11) NOT NULL COMMENT '统计的月（1-12）',
  `statistical_date` varchar(50) DEFAULT NULL COMMENT '统计时间(yyyy-MM)',
  `status` tinyint(1) DEFAULT NULL COMMENT '逻辑状态:(1-有效, 0-无效)',
  `create_time` datetime NOT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`party_fee_id`) USING BTREE,
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `org_type_id` (`org_type_id`) USING BTREE,
  KEY `parent_org_id` (`parent_org_id`) USING BTREE,
  KEY `is_retire` (`is_retire`) USING BTREE,
  KEY `statistical_year` (`statistical_year`) USING BTREE,
  KEY `statistical_month` (`statistical_month`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=24505 DEFAULT CHARSET=utf8mb4 COMMENT='党费交纳完成情况统计';



DROP TABLE IF EXISTS t_statistical_config_info;
CREATE TABLE `t_statistical_config_info` (
  `config_info_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `statistical_type` tinyint(3) NOT NULL COMMENT '统计类型1.党支部组织生活统计 2.领导干部双重组织生活统计 3.党费交纳完成情况统计',
  `show_organization_types` varchar(255) NOT NULL COMMENT '显示组织类型(存入的组织类型类型id,多个以逗号分隔）',
  `all_organization_types` text COMMENT '全部组织类型(存入的组织类型类型id,多个以逗号分隔）',
  `show_activity_types` varchar(512) DEFAULT NULL COMMENT '显示活动类型(存入的活动类型类型id,多个以逗号分隔）',
  `all_activity_types` varchar(255) DEFAULT NULL COMMENT '全部活动类型(存入的活动类型类型id,多个以逗号分隔）',
  `create_time` datetime NOT NULL,
  `update_time` datetime DEFAULT NULL,
  `last_change_user` bigint(20) NOT NULL COMMENT '最后修改人',
  PRIMARY KEY (`config_info_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COMMENT='统计配置信息';

DROP TABLE IF EXISTS `t_statistical_date_temp`;
CREATE TABLE `t_statistical_date_temp`  (
                                          `id` int(11) NULL DEFAULT NULL,
                                          `month` int(11) NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
INSERT INTO `t_statistical_date_temp` VALUES (1, 1);
INSERT INTO `t_statistical_date_temp` VALUES (2, 2);
INSERT INTO `t_statistical_date_temp` VALUES (3, 3);
INSERT INTO `t_statistical_date_temp` VALUES (4, 4);
INSERT INTO `t_statistical_date_temp` VALUES (5, 5);
INSERT INTO `t_statistical_date_temp` VALUES (6, 6);
INSERT INTO `t_statistical_date_temp` VALUES (7, 7);
INSERT INTO `t_statistical_date_temp` VALUES (8, 8);
INSERT INTO `t_statistical_date_temp` VALUES (9, 9);
INSERT INTO `t_statistical_date_temp` VALUES (10, 10);
INSERT INTO `t_statistical_date_temp` VALUES (11, 11);
INSERT INTO `t_statistical_date_temp` VALUES (12, 12);


-- ----------------------------
-- Table structure for t_statistical_temp_activity
-- ----------------------------
DROP TABLE IF EXISTS `t_statistical_temp_activity`;
CREATE TABLE `t_statistical_temp_activity`  (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `activity_id` int(11) NULL DEFAULT NULL,
                                              `type_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_statistical_temp_activity
-- ----------------------------
INSERT INTO `t_statistical_temp_activity` VALUES (2, 64, '主题党日');
INSERT INTO `t_statistical_temp_activity` VALUES (3, 60, '党支部党员大会');
INSERT INTO `t_statistical_temp_activity` VALUES (4, 63, '党课');
INSERT INTO `t_statistical_temp_activity` VALUES (5, 61, '党支部委员会会议');
INSERT INTO `t_statistical_temp_activity` VALUES (6, 62, '党小组会');

-- ----------------------------
-- 党务看板菜单脚本
-- ----------------------------

INSERT INTO `t_menu`(`menu_id`, `parent_id`, `name`, `status`, `last_change_user`, `create_time`, `update_time`, `seq`, `belong`) VALUES ('22', 'pc01', '党务看板', 1, NULL, sysdate(), sysdate(), 100, 1);
INSERT INTO `t_menu`(`menu_id`, `parent_id`, `name`, `status`, `last_change_user`, `create_time`, `update_time`, `seq`, `belong`) VALUES ('2201', '22', '党支部组织生活统计', 1, NULL, sysdate(), sysdate(), 100, 1);
INSERT INTO `t_menu`(`menu_id`, `parent_id`, `name`, `status`, `last_change_user`, `create_time`, `update_time`, `seq`, `belong`) VALUES ('2202', '22', '领导干部双重组织生活统计', 1, NULL, sysdate(), sysdate(), 100, 1);
INSERT INTO `t_menu`(`menu_id`, `parent_id`, `name`, `status`, `last_change_user`, `create_time`, `update_time`, `seq`, `belong`) VALUES ('2203', '22', '党费交纳完成情况统计', 1, NULL, sysdate(), sysdate(), 100, 1);
INSERT INTO `t_menu`(`menu_id`, `parent_id`, `name`, `status`, `last_change_user`, `create_time`, `update_time`, `seq`, `belong`) VALUES ('2003', '20', '统计报表设置', 1, NULL, sysdate(), sysdate(), 100, 1);
