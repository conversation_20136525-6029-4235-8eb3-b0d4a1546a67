/*
 Navicat Premium Data Transfer

 Source Server         : 重庆烟草-灰度测试
 Source Server Type    : MySQL
 Source Server Version : 50726 (5.7.26-log)
 Source Host           : rm-9bq67y35u2ql9gbh1.mysql.rds.ops.cqyccloud.com:3306
 Source Schema         : gs_ows_cqyc_grey

 Target Server Type    : MySQL
 Target Server Version : 50726 (5.7.26-log)
 File Encoding         : 65001

 Date: 25/08/2023 14:53:37
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_cem_doris_index
-- ----------------------------
DROP TABLE IF EXISTS `t_cem_doris_index`;
CREATE TABLE `t_cem_doris_index`  (
  `rule_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `detail` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '积分规则',
  `score_type` int(2) NULL DEFAULT NULL,
  `score_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `parent_score_type` int(2) NULL DEFAULT NULL,
  `parent_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明',
  `object` tinyint(1) NULL DEFAULT NULL COMMENT '1-人员  2-党组织  3-党组',
  `status` tinyint(1) NULL DEFAULT NULL COMMENT '0-启用  1-未启用',
  PRIMARY KEY (`rule_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 466 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of t_cem_doris_index
-- ----------------------------
INSERT INTO `t_cem_doris_index` VALUES (1, '登录“行动者.先锋”数智党建平台', '每天登录+1；每日上限1；', 34, '系统活跃', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (2, '在“行动者.先锋”数智党建平台上学习习近平新时代中国特色社会主义思想和党的创新理念', '浏览学习首页推送的新闻（PC端首页或移动端首页外部抓取新闻），每条20S以上+1；\r\n每日上限5；', 17, '理论武装', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (3, '新增个人成长轨迹', '个人成长轨迹每新增1条加1积分，每月上限5分。', 17, '理论武装', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (4, '重温入党誓词', '每日完完成重温加1分；', 35, '党性修养', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (5, '重温入党志愿书', '每日完完成重温加1分；', 35, '党性修养', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (6, '重温入党誓词感悟', '重温后填写思想感悟的（不少于15字），再加1分。', 35, '党性修养', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (7, '重温入党志愿书感悟', '重温后填写思想感悟的，再加1分（不少于15字）。每季度加1次', 35, '党性修养', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (8, '党员每月主动、按期、足额缴纳党费', '按时足额缴纳党费的，加5分', 18, '缴纳党费', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (9, '连续6个月未缴党费的', '连续6个月未缴党费的，-10分', 18, '缴纳党费', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (10, '按时参加支部党员大会', '按时参加（规定期限内补学），加2分', 12, '组织生活', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (11, '担任党小组组长', '担任党小组组长', 12, '组织生活', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (12, '按时参加党小组会', '按时参加（规定期限内补学），加5分', 12, '组织生活', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (13, '按时参加党课', '按时参加（规定期限内补学），加5分', 12, '组织生活', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (14, '未按时组织开展党小组会', '未开展，党小组组长-2分', 12, '组织生活', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (15, '按时参加主题党日', '按时参加（规定期限内补学），加5分', 12, '组织生活', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (16, '积极参加讲党课', '讲课人+10，每年上限20分', 12, '组织生活', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (17, '支委班子成员', '支委会成员+20', 12, '组织生活', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (18, '未按时参加支部委员会', '每季度1次，每缺1次，扣2分', 12, '组织生活', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (19, '党员奖励', '【奖惩模块】—党员奖励\r\n获得国家级奖励加20分\r\n国家级部门/省部级奖励加10分\r\n省部级部门/地市级（含系统）奖励加5分\r\n地市级部门加2分；', 16, '创先争优', 3, '创新指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (20, '受到党内/政务处分', '【奖惩模块】-党内处分：\r\n党内警告：积分-10\r\n严重警告：积分-20\r\n撤销党内职务：积分-30\r\n留党察看：积分-50；\r\n【奖惩模块】-政务处分：\r\n警告：积分-5\r\n记过：积分-10\r\n记大过：积分-20\r\n撤职：积分-40', 35, '党性修养', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (22, '基层党组织或本单位获得地市级部门以上级别表彰奖励', '国家级20分、省部级10分、地市级（含系统）5分；其所属部门表彰的，按10、5、2分加分', 16, '创先争优', 3, '创新指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (23, '每日一练', '全部答对加3分', 17, '理论武装', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (24, '每月一测', '答对一题加0.5分', 17, '理论武装', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (25, '每季一考', '答对一题加0.5分', 17, '理论武装', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (26, '支部党员获得地市级部门以上级别表彰奖励', '国家级表彰加10分、省部级含国家局表彰加5分、区县和系统表彰加3分；其所属部门表彰的，按5、3、1加分', 16, '创先争优', 3, '创新指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (27, '主动参与完成任务系统派发的“党建任务”', '承担A级活动或任务，1次加20分；B级任务加10分；C级任务加5分；D级任务加2分', 40, '担当作为', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (28, '主动参与完成任务系统派发的“党建任务”后获得评价', '完成A级活动或任务，总体评价获五至一星评价的，分别加10、8、5、3分；\r\nB级任务，分别加8、5、3、1分；\r\nC级任务，分别加5、3、2、1分；\r\nD级任务不加分。', 40, '担当作为', 1, '党建指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (29, '完成任务系统派发的“业务任务”', '承担A级活动或任务，1次加20分；B级任务加10分；C级任务加5分；D级任务加2分', 40, '担当作为', 2, '业务指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (30, '主动参与完成任务系统派发的“党建任务”后获得评价', '完成A级活动或任务，总体评价获五至一星评价的，分别加10、8、5、3分；\r\nB级任务，分别加8、5、3、1分；\r\nC级任务，分别加5、3、2、1分；\r\nD级任务不加分。', 40, '担当作为', 2, '业务指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (31, '完成任务系统派发的“创新任务”', '承担A级活动或任务，1次加20分；B级任务加10分；C级任务加5分；D级任务加2分', 40, '担当作为', 3, '创新指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (32, '主动参与完成任务系统派发的“创新任务”后获得评价', '完成A级活动或任务，总体评价获五至一星评价的，分别加10、8、5、3分；\r\nB级任务，分别加8、5、3、1分；\r\nC级任务，分别加5、3、2、1分；\r\nD级任务不加分。', 40, '担当作为', 3, '创新指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (33, '主动报名参与云上党支部任务', '报名参加A级活动或任务，1次加20分；B级任务加10分；C级任务加5分；D级任务加2分', 40, '担当作为', 3, '创新指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (35, '系统访问率', '每月统计1次，日平均登录率90%以上，加5分；\r\n70%以上，加3分；\r\n50%以上，加1分；\r\n50%以下，不加分', 42, '系统运用', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (36, '按期换届', '组织届次到期未换届，-5', 24, '组织建设', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (37, '配齐组织书记', '届次管理中设置书记，加5分', 24, '组织建设', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (38, '支部内党员受处罚', '支部内有受党纪处分、政务处分、组织处理的，每1人次积分-5；', 20, '党性教育', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (39, '组织发展史', '每新增5条组织发展史加1积分，每5条数据删除1条减1积分。', 24, '组织建设', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (40, '组织风采', '每新增5条组织发展史加1积分，每5条数据删除1条减1积分。', 24, '组织建设', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (41, '按时组织召开党员大会', '每季度按时组织召开，加2分', 12, '组织生活', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (42, '按时组织召开支委会', '每月按时组织召开，加2分', 12, '组织生活', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (43, '下属党小组全部按时组织开展党小组会', '下属党小组全部已开展，党支部加2分；', 12, '组织生活', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (44, '按时组织开展党课', '每季度按时组织开展，加2分', 12, '组织生活', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (45, '按时组织开展主题党日活动', '每月按时组织开展，加2分', 12, '组织生活', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (47, '学习习近平新时代中国特色社会主义思想和新闻报道', '每月统计1次，日平均参与率90%以上，加5分；\r\n70%以上，加3分；\r\n50%以上，加1分\r\n50%以下，不加分', 17, '理论武装', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (48, '党员主动、按期、足额缴纳党费', '100%按时足额缴纳，加5分', 25, '党费收缴', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (49, '完成A级党建任务', '承担A级活动或任务，1次加20分；B级任务加10分；C级任务加5分；D级任务加2分', 40, '担当作为', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (50, '健全组织机构，配强组织力量', '配齐配强书记（副书记），加5个积分', 24, '组织建设', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (51, '未督促提醒所属党支部按期开展换届', '下属党支部未按期换届，一次扣2分，可累加', 24, '组织建设', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (52, '获得A级党建任务5星评价', '完成A级活动或任务，总体评价获五至一星评价的，分别加10、8、5、3分；\r\nB级任务，分别加8、5、3、1分；\r\nC级任务，分别加5、3、2、1分；\r\nD级任务不加分。', 40, '担当作为', 1, '党建指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (53, '完成A级业务任务', '承担A级活动或任务，1次加20分；B级任务加10分；C级任务加5分；D级任务加2分', 40, '担当作为', 2, '业务指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (54, '获得A级业务任务5星评价', '完成A级活动或任务，总体评价获五至一星评价的，分别加10、8、5、3分；\r\nB级任务，分别加8、5、3、1分；\r\nC级任务，分别加5、3、2、1分；\r\nD级任务不加分。', 40, '担当作为', 2, '业务指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (55, '完成A级创新任务', '承担A级活动或任务，1次加20分；B级任务加10分；C级任务加5分；D级任务加2分', 40, '担当作为', 3, '创新指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (56, '获得A级业务任务5星评价', '完成A级活动或任务，总体评价获五至一星评价的，分别加10、8、5、3分；\r\nB级任务，分别加8、5、3、1分；\r\nC级任务，分别加5、3、2、1分；\r\nD级任务不加分。', 40, '担当作为', 3, '创新指标', NULL, 2, 0);
INSERT INTO `t_cem_doris_index` VALUES (70, '成立党的建设工作领导小组', '成立党的建设工作领导小组，加2分', 19, '主体责任', 1, '党建指标', NULL, 3, 0);
INSERT INTO `t_cem_doris_index` VALUES (71, '本单位有受处分', '本单位有受党纪处分、政务处分、组织处理的，每1人次扣5分，应累加', 20, '党性教育', 1, '党建指标', NULL, 3, 0);
INSERT INTO `t_cem_doris_index` VALUES (72, '建立党支部工作联系点制度', '每个党组成员均设置了支部联系点，加2分', 21, '服务基层', 1, '党建指标', NULL, 3, 0);
INSERT INTO `t_cem_doris_index` VALUES (73, '建立干部基层联系点制度', '每个党组成员均设置了基层联系点，加2分', 21, '服务基层', 1, '党建指标', NULL, 3, 0);
INSERT INTO `t_cem_doris_index` VALUES (74, '建设本单位特色品牌', '有党建品牌，积分+2', 22, '党建品牌建设', 1, '党建指标', NULL, 3, 0);
INSERT INTO `t_cem_doris_index` VALUES (75, '因地制宜建设党员活动室、支部园地和党建廊', '有党建阵地，积分+2', 23, '党建阵地建设', 1, '党建指标', NULL, 3, 0);
INSERT INTO `t_cem_doris_index` VALUES (76, '完成A级党建任务', '承担A级活动或任务，1次加20分；B级任务加10分；C级任务加5分；D级任务加2分（任务主要以“揭榜挂帅”“第二党支部”等方式在智慧党建平台安排部署）', 40, '担当作为', 1, '党建指标', NULL, 3, 0);
INSERT INTO `t_cem_doris_index` VALUES (77, '获得A级党建任务5星评价', '完成A级活动或任务，总体评价获五至一星评价的，分别加10、8、5、3分；\r\nB级任务，分别加8、5、3、1分；\r\nC级任务，分别加5、3、2、1分；\r\nD级任务不加分。', 40, '担当作为', 1, '党建指标', NULL, 3, 0);
INSERT INTO `t_cem_doris_index` VALUES (78, '完成A级业务任务', '承担A级活动或任务，1次加20分；B级任务加10分；C级任务加5分；D级任务加2分（任务主要以“揭榜挂帅”“第二党支部”等方式在智慧党建平台安排部署）', 40, '担当作为', 2, '业务指标', NULL, 3, 0);
INSERT INTO `t_cem_doris_index` VALUES (79, '获得A级业务任务5星评价', '完成A级活动或任务，总体评价获五至一星评价的，分别加10、8、5、3分；\r\nB级任务，分别加8、5、3、1分；\r\nC级任务，分别加5、3、2、1分；\r\nD级任务不加分。', 40, '担当作为', 2, '业务指标', NULL, 3, 0);
INSERT INTO `t_cem_doris_index` VALUES (80, '完成创新任务', '承担A级活动或任务，1次加20分；B级任务加10分；C级任务加5分；D级任务加2分', 40, '担当作为', 3, '创新指标', NULL, 3, 0);
INSERT INTO `t_cem_doris_index` VALUES (81, '获得创新任务5星评价', '完成A级活动或任务，总体评价获五至一星评价的，分别加10、8、5、3分；\r\nB级任务，分别加8、5、3、1分；\r\nC级任务，分别加5、3、2、1分；\r\nD级任务不加分。', 40, '担当作为', 3, '创新指标', NULL, 3, 0);
INSERT INTO `t_cem_doris_index` VALUES (100, '党小组设置', '支部党员人数20人以上未设置党小组，-5', 24, '组织建设', 1, '党建指标', NULL, 2, 1);
INSERT INTO `t_cem_doris_index` VALUES (101, '支委会规范设置', '支部正式党员人数7人以上未设置支委，-5', 24, '组织建设', 1, '党建指标', NULL, 2, 1);
INSERT INTO `t_cem_doris_index` VALUES (102, '学习强国积分', '所有党员月度积分平均值排名加分：\r\n前20%的，加20；在前50%的，加10；在前80%的，加5分。80%以下的不加分', 17, '理论武装', 1, '党建指标', NULL, 2, 1);
INSERT INTO `t_cem_doris_index` VALUES (103, '网络学员时长', '所有党员月度时长平均值排名加分：\r\n前20%的，加20；在前50%的，加10；在前80%的，加5分。80%以下的不加分', 17, '理论武装', 1, '党建指标', NULL, 2, 1);
INSERT INTO `t_cem_doris_index` VALUES (104, '三会一课（主题党日）出勤率', '月度支部的三会一课（主题党日）出勤率：\r\n95%以上：+5\r\n90%-95%：+3\r\n80%-90%：+2\r\n70%-80%：+1\r\n70%以下无分', 12, '组织生活', 1, '党建指标', NULL, 2, 1);
INSERT INTO `t_cem_doris_index` VALUES (105, '领导讲党课', '党组成员及行政领导讲党课次数一年1次以上：\r\n完成指标的领导数/领导总数*5', 21, '服务基层', 1, '党建指标', NULL, 3, 1);
INSERT INTO `t_cem_doris_index` VALUES (106, '双重组织生活', '党组成员及行政领导各领导在一个季度内，参加所在支部的三会一课+主题党日的次数2次以上：\r\n完成指标的领导数/领导总数*5', 21, '服务基层', 1, '党建指标', NULL, 3, 1);
INSERT INTO `t_cem_doris_index` VALUES (107, '党建工作年度考核', '单位党建工作年度考核得分排名加分：\r\n前20%的，加20；在前50%的，加10；在前80%的，加5分。80%以下的不加分', 43, '党建工作考核', 1, '党建指标', NULL, 3, 1);
INSERT INTO `t_cem_doris_index` VALUES (108, '第一议题执行率', '各级党组织会议议题标签落实第一题题情况：\r\n95%以上：+5\r\n90%-95%：+3\r\n80%-90%：+2\r\n70%-80%：+1\r\n70%以下无分', 44, '第一议题制度履行', 1, '党建指标', NULL, 3, 1);
INSERT INTO `t_cem_doris_index` VALUES (109, '云区组织建设', '根据单位业务线（专卖、营销、综合，两烟产区还包括烟叶）建立云区组织情况，每条业务线1各以上，达标则得5分，否则不得分', 45, '云区建设', 3, '创新指标', NULL, 3, 1);
INSERT INTO `t_cem_doris_index` VALUES (110, '云区活动开展', '根据该单位与业务线相关的云区组织发布云区任务平均数量排名进行指标分值计算（满分5分）', 45, '云区建设', 3, '创新指标', NULL, 3, 1);
INSERT INTO `t_cem_doris_index` VALUES (111, '云区活跃情况', '党员参与次数/员工（党员+非党员）参与总次数*5分', 45, '云区建设', 3, '创新指标', NULL, 3, 1);
INSERT INTO `t_cem_doris_index` VALUES (464, '日常业务指标', '第一步：各项业务指标先排名，排名之后赋分；排名（1%），得分=满分（100）*（1-排名）；例如排名10%，则得分为100*90%=90分；\r\n\r\n第二步：每个月的所有业务指标得分求和；\r\n\r\n第三步：月度业务指标得分排名，排名之后奖励积分；排名（1%），积分=满分（300）*（1-排名）；', 41, '日常业务指标', 2, '业务指标', NULL, 1, 0);
INSERT INTO `t_cem_doris_index` VALUES (465, '日常业务指标', '第一步：各项业务指标先排名，排名之后赋分；排名（1%），得分=满分（100）*（1-排名）；例如排名10%，则得分为100*90%=90分；\r\n\r\n第二步：每个月的所有业务指标得分求和；\r\n\r\n第三步：月度业务指标得分排名，排名之后奖励积分；排名（1%），积分=满分（300）*（1-排名）；', 41, '日常业务指标', 2, '业务指标', NULL, 2, 0);

SET FOREIGN_KEY_CHECKS = 1;



/*
 Navicat Premium Data Transfer

 Source Server         : 重庆烟草-灰度测试
 Source Server Type    : MySQL
 Source Server Version : 50726 (5.7.26-log)
 Source Host           : rm-9bq67y35u2ql9gbh1.mysql.rds.ops.cqyccloud.com:3306
 Source Schema         : gs_ows_cqyc_grey

 Target Server Type    : MySQL
 Target Server Version : 50726 (5.7.26-log)
 File Encoding         : 65001

 Date: 25/08/2023 14:56:48
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_cem_unique_list
-- ----------------------------
DROP TABLE IF EXISTS `t_cem_unique_list`;
CREATE TABLE `t_cem_unique_list`  (
  `list_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `rule_id` int(4) NULL DEFAULT NULL COMMENT 't_cem_doris_index中的rule_id',
  `unique_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '唯一性标志',
  PRIMARY KEY (`list_id`) USING BTREE,
  UNIQUE INDEX `rulecode`(`rule_id`, `unique_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 33 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '指数-有上限的，已入库数据清单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_cem_unique_list
-- ----------------------------
INSERT INTO `t_cem_unique_list` VALUES (11, 72, '13');
INSERT INTO `t_cem_unique_list` VALUES (13, 72, '15');
INSERT INTO `t_cem_unique_list` VALUES (15, 72, '17');
INSERT INTO `t_cem_unique_list` VALUES (17, 72, '19');
INSERT INTO `t_cem_unique_list` VALUES (18, 72, '21');
INSERT INTO `t_cem_unique_list` VALUES (20, 72, '24');
INSERT INTO `t_cem_unique_list` VALUES (22, 72, '25');
INSERT INTO `t_cem_unique_list` VALUES (24, 72, '26');
INSERT INTO `t_cem_unique_list` VALUES (26, 72, '31');
INSERT INTO `t_cem_unique_list` VALUES (28, 72, '32');
INSERT INTO `t_cem_unique_list` VALUES (29, 72, '35');
INSERT INTO `t_cem_unique_list` VALUES (32, 72, '39');
INSERT INTO `t_cem_unique_list` VALUES (1, 72, '4');
INSERT INTO `t_cem_unique_list` VALUES (30, 72, '40');
INSERT INTO `t_cem_unique_list` VALUES (3, 72, '5');
INSERT INTO `t_cem_unique_list` VALUES (4, 72, '6');
INSERT INTO `t_cem_unique_list` VALUES (7, 72, '7');
INSERT INTO `t_cem_unique_list` VALUES (9, 72, '8');
INSERT INTO `t_cem_unique_list` VALUES (10, 73, '15');
INSERT INTO `t_cem_unique_list` VALUES (12, 73, '19');
INSERT INTO `t_cem_unique_list` VALUES (14, 73, '21');
INSERT INTO `t_cem_unique_list` VALUES (16, 73, '24');
INSERT INTO `t_cem_unique_list` VALUES (19, 73, '25');
INSERT INTO `t_cem_unique_list` VALUES (21, 73, '31');
INSERT INTO `t_cem_unique_list` VALUES (23, 73, '32');
INSERT INTO `t_cem_unique_list` VALUES (25, 73, '35');
INSERT INTO `t_cem_unique_list` VALUES (31, 73, '39');
INSERT INTO `t_cem_unique_list` VALUES (27, 73, '40');
INSERT INTO `t_cem_unique_list` VALUES (2, 73, '5');
INSERT INTO `t_cem_unique_list` VALUES (5, 73, '6');
INSERT INTO `t_cem_unique_list` VALUES (6, 73, '7');
INSERT INTO `t_cem_unique_list` VALUES (8, 73, '9');

SET FOREIGN_KEY_CHECKS = 1;
