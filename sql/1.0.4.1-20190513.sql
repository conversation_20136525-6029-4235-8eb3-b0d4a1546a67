ALTER TABLE t_statistical_org_life ADD COLUMN party_group_num  int(11)  comment '党小组数量'   	AFTER  participate_num;
ALTER TABLE t_statistical_org_life ADD COLUMN period_sum  int(11)       comment '支委会数量'   	AFTER  party_group_num;
ALTER TABLE t_statistical_org_life ADD COLUMN org_create_time  datetime comment '组织创建时间'  AFTER  org_id;
-- 添加索引
ALTER TABLE t_statistical_date_temp ADD INDEX month (month);

-- 操作党费
ALTER TABLE t_statistical_party_fee ADD COLUMN check_num  int(11) comment '考核未缴纳人数'  AFTER  org_id;
ALTER TABLE `t_statistical_party_fee`
ADD COLUMN `org_create_time` datetime(0) NULL COMMENT '组织建立时间' AFTER `is_retire`,
ADD COLUMN `eval_unpaid_person_num` int(11) NULL COMMENT '考核未缴纳人数' AFTER `unpaid_person_num`;

-- 操作临时表
CREATE TABLE t_num (i INT);
INSERT INTO t_num (i) VALUES (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);

-- 创建存储统计党小组变量的表
CREATE TABLE `t_statistical_storage_variable` (
  `storage_val_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` bigint(20) DEFAULT NULL,
  `statistical_date` varchar(50) DEFAULT NULL COMMENT '统计日期（yyyy-MM)',
  `storage_val` bigint(20) DEFAULT NULL COMMENT '统计的值',
  `storage_type` int(3) DEFAULT NULL COMMENT '1.党小组变量',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`storage_val_id`) USING BTREE,
  KEY `org_id` (`org_id`),
  KEY `sta_data` (`statistical_date`),
  KEY `sto_type` (`storage_type`)
) ENGINE=InnoDB AUTO_INCREMENT=5642 DEFAULT CHARSET=utf8mb4;