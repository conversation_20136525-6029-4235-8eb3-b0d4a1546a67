-- 电子党务报告-组织党费统计
CREATE TABLE t_sas_org_ppmd_report (
    org_ppmd_report_id int(10) NOT NULL AUTO_INCREMENT comment '主键',
    org_id             bigint(20) comment '组织ID',
    stats_date         datetime NULL comment '统计时间（年-月）',
    pay_num            int(10) DEFAULT 0 comment '缴纳人数',
    should_pay_num     int(10) DEFAULT 0 comment '应缴纳人数',
    pay_amount         int(10) DEFAULT 0 comment '党费总金额',
    should_pay_amount  int(10) DEFAULT 0 comment '应缴纳金额',
    first_org          varchar(200),
    first_user         varchar(50),
    create_time        datetime NULL comment '创建时间',
    update_time        datetime NOT NULL comment '更新时间',
    PRIMARY KEY (org_ppmd_report_id)) comment='电子党务报告-组织党费统计';

-- 电子党务报告-组织生活统计
CREATE TABLE t_sas_org_meeting_report (
    org_meeting_report_id bigint(20) NOT NULL AUTO_INCREMENT comment '主键',
    org_id                bigint(20) comment '组织ID',
    stats_date            datetime NULL comment '统计时间',
    org_num               int(10) DEFAULT 0 comment '总共开展组织生活的组织数量',
    meeting_num           int(10) comment '开展了活动次数',
    create_time           datetime NULL comment '创建时间',
    update_time           datetime NOT NULL comment '更新时间',
    PRIMARY KEY (org_meeting_report_id)) comment='电子党务报告-组织生活统计';

-- 电子党务报告-组织生活-统计维度
CREATE TABLE t_sas_org_meeting_report_dimension (
    id                    int(10) NOT NULL AUTO_INCREMENT comment '主键',
    org_meeting_report_id bigint(20) comment '主表ID',
    type_id               bigint(20) comment '类型ID',
    type_name             varchar(100) comment '类型名称',
    num                   int(10) DEFAULT 0 comment '数量',
    dimension             int(2) comment '维度 1-组织，2-活动',
    create_time           datetime NULL comment '创建时间',
    update_time           datetime NOT NULL comment '更新时间',
    PRIMARY KEY (id)) comment='电子党务报告-组织生活-统计维度';

-- 电子党务报告-组织党群活动统计
CREATE TABLE t_sas_org_activity_report (
    org_activity_report_id bigint(20) NOT NULL AUTO_INCREMENT comment '主键',
    org_id                 bigint(20) comment '组织ID',
    stats_date             datetime NULL comment '统计时间',
    num                    int(10) DEFAULT 0 comment '参加党群活动人数',
    create_time            datetime NULL comment '创建时间',
    update_time            datetime NOT NULL comment '更新时间',
    PRIMARY KEY (org_activity_report_id)) comment='电子党务报告-组织党群活动统计';

-- 电子党务报告-党群活动-统计维度
CREATE TABLE t_sas_org_activity_report_dimension (
    id                     int(10) NOT NULL AUTO_INCREMENT comment '主键',
    org_activity_report_id bigint(20) comment '主表ID',
    type_id                bigint(20) comment '类型ID',
    type_name              varchar(100) comment '类型名称',
    num                    int(10) DEFAULT 0 comment '数量',
    create_time            datetime NULL comment '创建时间',
    update_time            datetime NOT NULL comment '更新时间',
    PRIMARY KEY (id)) comment='电子党务报告-党群活动-统计维度';

-- 电子党务报告-党员学习统计
CREATE TABLE t_sas_org_study_report (
    org_study_report_id bigint(20) NOT NULL AUTO_INCREMENT,
    org_id              bigint(20) comment '组织ID',
    stats_date          datetime NULL comment '统计时间',
    person_num          int(10) DEFAULT 0 comment '学习人数',
    score               int(10) DEFAULT 0 comment '总积分',
    book_num            int(10) DEFAULT 0 comment '兑换书数量',
    first_user_id       bigint(20) comment '最高学习积分党员ID',
    first_user_name     varchar(50) comment '最高学习积分党员名称',
    create_time         datetime NULL comment '创建时间',
    update_time         datetime NOT NULL comment '更新时间',
    PRIMARY KEY (org_study_report_id)) comment='电子党务报告-党员学习统计';

-- 电子党务报告-统计公益、扶贫
CREATE TABLE t_sas_org_poverty_report (
    org_poverty_report_id int(10) NOT NULL AUTO_INCREMENT comment '主键',
    org_id                bigint(20) comment '组织ID',
    stats_date            datetime NULL comment '统计时间',
    party_person_num      int(10) DEFAULT 0 comment '参与公益项目的人数',
    score                 int(10) DEFAULT 0 comment '捐助总积分',
    buy_person_num        int(10) DEFAULT 0 comment '购买扶贫产品人数',
    order_num             int(10) DEFAULT 0 comment '扶贫产品订单数量',
    amount                int(10) DEFAULT 0 comment '交易金额',
    good_baby_num         int(10) DEFAULT 0 comment '公益宝贝数量',
    good_baby_amount      int(10) DEFAULT 0 comment '公益宝贝捐献金额',
    create_time           datetime NULL comment '创建时间',
    update_time           datetime NOT NULL comment '更新时间',
    PRIMARY KEY (org_poverty_report_id)) comment='电子党务报告-统计公益、扶贫';





