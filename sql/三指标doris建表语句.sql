CREATE TABLE
IF
	NOT EXISTS demo.t_index_user_score (
		rule_id INT COMMENT '积分id',
		data_month INT COMMENT '数据月',
		user_id BIGINT COMMENT '用户ID',
    score_type INT COMMENT '积分类型id',
		parent_score_type INT COMMENT '积分父类id 1.党建指标 2.业务指标 3.创新指标 ',
		org_id BIGINT COMMENT '用户所在组织ID',
		user_name VARCHAR ( 50 ) COMMENT '用户名',
		score BIGINT SUM DEFAULT '0' COMMENT '分值',-- 合计
		create_time DATETIME REPLACE DEFAULT '1970-01-01 00:00' COMMENT '创建时间',
		--  因为不支持该索引（like匹配），使用倒排索引
    -- INDEX k1_idx (user_name) USING  NGRAM_BF PROPERTIES("gram_size"="6", "bf_size"="260") COMMENT 'username ngram_bf index'
		INDEX user_name ( user_name ) USING INVERTED PROPERTIES ( "parser" = "chinese" ) -- 倒排索引
		)
		AGGREGATE KEY ( rule_id,`data_month`, `user_id`,`score_type`, `parent_score_type`,  `org_id`, `user_name` )
		DISTRIBUTED BY HASH ( `score_type` ) BUCKETS AUTO
		-- 物化视图user_parent_score，统计每个机构每月每大类的分值，查询命中可提高某些查询的查询效率
		 ROLLUP ( user_parent_score ( org_id, data_month, parent_score_type, score )
		)
		PROPERTIES (
		'replication_allocation' = 'tag.location.default:1', -- 分片数量
		 "bloom_filter_columns" = "score_type" -- 索引,适用于in查询
	);



CREATE TABLE
IF
	NOT EXISTS demo.t_index_org_score (
		rule_id INT COMMENT '积分id',
		data_month INT COMMENT '数据月',-- 	region_id INT COMMENT '区域ID',
		org_id BIGINT COMMENT '组织ID',
    score_type INT COMMENT '积分类型id',
		parent_score_type INT COMMENT '积分父类id 1.党建指标 2.业务指标 3.创新指标 ',
		score_org_type INT COMMENT '组织类型 1.党树组织  2.党组',
		org_name VARCHAR ( 255 ) COMMENT '机构名',
		score BIGINT SUM DEFAULT '0' COMMENT '分值',-- 合计
		create_time DATETIME REPLACE DEFAULT '1970-01-01 00:00' COMMENT '创建时间',--  因为不支持该索引
    -- INDEX k1_idx (user_name) USING  NGRAM_BF PROPERTIES("gram_size"="6", "bf_size"="260") COMMENT 'username ngram_bf index'
		INDEX org_name ( org_name ) USING INVERTED PROPERTIES ( "parser" = "chinese" ) -- 倒排索引
		)
		AGGREGATE KEY ( rule_id,`data_month`, `org_id`, `score_type`, `parent_score_type`,  `score_org_type`,`org_name` )
		DISTRIBUTED BY HASH ( `score_type` ) BUCKETS AUTO
		ROLLUP ( org_parent_score ( org_id, data_month, parent_score_type, score ) -- 物化视图，统计每个机构每月每大类的分值，查询命中可提高某些查询的查询效率
	) PROPERTIES (
	 'replication_allocation' = 'tag.location.default:1', -- 分片数量
	 "bloom_filter_columns" = "org_id,score_type" -- 索引，适用于in查询
	);