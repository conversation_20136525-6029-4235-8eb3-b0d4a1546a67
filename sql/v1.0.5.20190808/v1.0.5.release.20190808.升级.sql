CREATE TABLE t_statistical_org_life_view (
  org_life_view_id  bigint(20) NOT NULL AUTO_INCREMENT comment 'id',
  org_id            bigint(20) NOT NULL comment '组织id',
  org_name          varchar(255) NOT NULL comment '组织名称',
  org_seq           int(10) NOT NULL comment '组织排序权重',
  type_id           bigint(20) NOT NULL comment '活动类型id。当该组织当月没有其它类型的统计数据时为 -1 双重组织生活',
  type_name         varchar(255) NOT NULL comment '类型名称',
  participate_num   int(10) DEFAULT 0 NOT NULL comment '举办次数',
  statistical_year  year NOT NULL comment '统计的年',
  statistical_month int(11) NOT NULL comment '统计的月（1-12）',
  create_time       datetime NOT NULL comment '创建时间',
  update_time       datetime NULL comment '更新时间',
  PRIMARY KEY (org_life_view_id),
  INDEX (org_id),
  INDEX (org_seq),
  INDEX (type_id),
  INDEX (participate_num),
  INDEX (statistical_year),
  INDEX (statistical_month)) comment='组织生活一浏览表';


-- 创建用户双重组织生活统计表
CREATE TABLE t_statistical_user_org_life (
  user_org_life_id  bigint(20) NOT NULL AUTO_INCREMENT,
  user_id           bigint(20) NOT NULL comment '用户ID',
  type_id           bigint(20) NOT NULL comment '活动类型id',
  type_name         varchar(50) NOT NULL comment '活动类型名称',
  participate_num   int(11) DEFAULT 0 NOT NULL comment '参与次数',
  statistical_year  year NOT NULL,
  statistical_month int(11) NOT NULL comment '统计的月（1-12）',
  create_time       datetime NOT NULL,
  update_time       datetime NULL comment '更新时间',
  PRIMARY KEY (user_org_life_id),
  INDEX (user_id),
  INDEX (type_id),
  INDEX (statistical_year),
  INDEX (statistical_month)) comment='用户双重组织生活';

CREATE TABLE t_statistical_user_temp (
  user_org_life_id bigint(20) NOT NULL AUTO_INCREMENT,
  user_id          bigint(20) NOT NULL comment '用户ID',
  user_name        varchar(20) NOT NULL comment '用户名字',
  user_status      tinyint(3) comment '用户状态  1-正常 2-删除',
  is_party_member  int(10) NOT NULL comment '是否是党员。1 是 2否',
  org_id           bigint(20) NOT NULL comment '组织id',
  org_name         varchar(255) NOT NULL comment '组织名称',
  org_status       tinyint(3) comment '组织状态 1.正常 2.删除',
  org_type         int(11) NOT NULL comment '组织类型 eg:党组织',
  org_type_child   int(11) NOT NULL comment '组织具体类型 eg:党支部 10280302',
  parent_id        bigint(20) NOT NULL comment '父级组织Id',
  org_level        varchar(50) comment '组织父级路径',
  is_retire        tinyint(3) NOT NULL comment '是否离退休党组织 1-是 2-否',
  create_time      datetime NOT NULL,
  PRIMARY KEY (user_org_life_id),
  INDEX (user_id),
  INDEX (user_status),
  INDEX (is_party_member),
  INDEX (org_id),
  INDEX (org_status),
  INDEX (org_type),
  INDEX (org_type_child),
  INDEX (parent_id),
  INDEX (org_level),
  INDEX (is_retire)) comment='双重组织生活用户临时表';
