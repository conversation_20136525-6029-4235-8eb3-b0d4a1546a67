-- ----------------------------
-- Table structure for t_pbm_work_item
-- ----------------------------
DROP TABLE IF EXISTS `t_pbm_work_item`;
CREATE TABLE `t_pbm_work_item`  (
                                    `work_item_id` bigint(20) NOT NULL AUTO_INCREMENT,
                                    `rule_id` bigint(20) NOT NULL COMMENT '烟草营销指标ruleId',
                                    `region_id` bigint(20) NOT NULL COMMENT '区县ID',
                                    `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                    `type` tinyint(2) NOT NULL COMMENT '类型 1-党务，2-业务',
                                    `against` int(1) NOT NULL COMMENT '针对对象 1-人员 2-组织',
                                    `cycle` tinyint(3) NOT NULL COMMENT '考核周期 1-周，2-月度，3-季度，4-年度，5-累计',
                                    `criterion` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '考核标准',
                                    `strategy_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '策略名称，方便工厂类调用',
                                    `data_type` tinyint(1) NOT NULL COMMENT '数据类型 1-Integer,2-double,3-time,4-boolean',
                                    `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作结果描述',
                                    `compare_type` tinyint(3) NOT NULL COMMENT '比较类型 0-不比较 1-百分比，2-超过数量，3-判断选项',
                                    `compare_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结果比较描述',
                                    `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注说明',
                                    PRIMARY KEY (`work_item_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 39 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工作详情维度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_pbm_work_item
-- ----------------------------
INSERT INTO `t_pbm_work_item` VALUES (1, 1, 19, '店铺管理系统推广到位率', 2, 2, 2, '店铺管理系统推广到位率', 'ORG_BUSINESS', 2, '店铺管理系统推广到位率：%s', 0, '超过了%s家单位', NULL);
INSERT INTO `t_pbm_work_item` VALUES (2, 2, 19, '聚合支付开通比例', 2, 2, 2, '聚合支付开通比例', 'ORG_BUSINESS', 2, '聚合支付开通比例：%s', 0, '超过了%s家单位', NULL);
INSERT INTO `t_pbm_work_item` VALUES (3, 3, 19, '聚合支付使用比例', 2, 2, 2, '聚合支付使用比例', 'ORG_BUSINESS', 2, '聚合支付使用比例：%s', 0, '超过了%s家单位', NULL);
INSERT INTO `t_pbm_work_item` VALUES (4, 4, 19, '客户毛利率', 2, 2, 1, '客户毛利率与全市周均毛利率差值', 'ORG_BUSINESS', 2, '客户毛利率：%s', 0, '与全市周均毛利率左右相差', NULL);
INSERT INTO `t_pbm_work_item` VALUES (5, 5, 19, '客户拜访时长', 2, 1, 2, '\"周拜访时长\"12小时以上，得分100；否则不得分', 'USER_BUSINESS', 2, '周均拜访时长：%s', 0, NULL, NULL);
INSERT INTO `t_pbm_work_item` VALUES (6, 6, 19, '实地拜访覆盖率', 2, 1, 2, '实地拜访覆盖率', 'USER_BUSINESS', 2, '实地拜访覆盖率：%s', 0, '超过了%s的员工', NULL);
INSERT INTO `t_pbm_work_item` VALUES (7, 7, 19, '拜访完成率', 2, 1, 2, '拜访完成率', 'USER_BUSINESS', 2, '拜访完成率：%s', 0, '超过了%s的员工', NULL);
INSERT INTO `t_pbm_work_item` VALUES (8, 8, 19, '拜访到位率', 2, 1, 2, '拜访到位率', 'USER_BUSINESS', 2, '拜访到位率：%s', 0, '超过了%s的员工', NULL);
INSERT INTO `t_pbm_work_item` VALUES (9, 9, 19, '标准策略完成率', 2, 1, 2, '标准策略完成率', 'USER_BUSINESS', 2, '标准策略完成率：%s', 0, '超过了%s的员工', NULL);
INSERT INTO `t_pbm_work_item` VALUES (10, 10, 19, '临时策略完成率', 2, 1, 2, '临时策略完成率', 'USER_BUSINESS', 2, '临时策略完成率：%s', 0, '超过了%s的员工', NULL);
INSERT INTO `t_pbm_work_item` VALUES (11, 11, 19, '预警策略完成率', 2, 1, 2, '预警策略完成率', 'USER_BUSINESS', 2, '预警策略完成率：%s', 0, '超过了%s的员工', NULL);
INSERT INTO `t_pbm_work_item` VALUES (12, 12, 19, '预约策略完成率', 2, 1, 2, '预约策略完成率', 'USER_BUSINESS', 2, '预约策略完成率：%s', 0, '超过了%s的员工', NULL);
INSERT INTO `t_pbm_work_item` VALUES (13, 0, 19, '登录“行动者.先锋”数智党建平台', 1, 1, 2, '登录天数', 'USER_LOGIN', 1, '登录数智党建平台天数：%s天', 1, '超过了系统%s的党员；', '计算一个月的登录天数；登录PC端/移动端都计算，不重复计算；');
INSERT INTO `t_pbm_work_item` VALUES (14, 0, 19, '新增个人成长轨迹', 1, 1, 5, '成长轨迹记录数量', 'USER_HIGHLIGHT', 1, '累计成长轨迹记录条数：%s条', 1, '超过了系统%s的党员；', '计算所有成长轨迹的记录条数');
INSERT INTO `t_pbm_work_item` VALUES (15, 0, 19, '在“行动者.先锋”平台上看新闻资讯进行学习', 1, 1, 2, '看新闻学习次数', 'NEWS_STUDY', 1, '看新闻学习次数：%s次', 1, '超过了系统%s的党员；', '计算一个月阅读的新闻条数；');
INSERT INTO `t_pbm_work_item` VALUES (16, 0, 19, '重温入党誓词', 1, 1, 2, '重温天数', 'USER_PARTY_OATH', 1, '重温入党誓词天数：%s天', 1, '超过了系统%s的党员；', '计算一个月重温入党誓词天数；');
INSERT INTO `t_pbm_work_item` VALUES (17, 0, 19, '重温入党志愿书', 1, 1, 2, '重温天数', 'USER_VOLUNTEER', 1, '重温入党志愿书天数：%s天', 1, '超过了系统%s的党员；', '计算一个月重温入党志愿书天数；');
INSERT INTO `t_pbm_work_item` VALUES (18, 0, 19, '按时参加党员大会', 1, 1, 5, '出勤率', 'MEETING_PARTY_MEMBER', 2, '党员大会按时出勤率：%s', 1, '超过了系统%s的党员；', '分母是党员所在的支部在平台开展支部党员大会的次数');
INSERT INTO `t_pbm_work_item` VALUES (19, 0, 19, '按时参加党小组会', 1, 1, 5, '出勤率', 'MEETING_PARTY_GROUP', 2, '党小组会按时出勤率：%s', 1, '超过了系统%s的党员；', '分母是党员所在的党小组在平台开展党小组会的次数');
INSERT INTO `t_pbm_work_item` VALUES (20, 0, 19, '按时参加党课', 1, 1, 5, '出勤率', 'MEETING_PARTY_CLASS', 2, '党课按时出勤率：%s', 1, '超过了系统%s的党员；', '分母是党员所在的党支部在平台开展党课的次数');
INSERT INTO `t_pbm_work_item` VALUES (21, 0, 19, '按时参加主题党日活动', 1, 1, 5, '出勤率', 'MEETING_PARTY_DAY', 2, '主题党日按时出勤率：%s', 1, '超过了系统%s的党员；', '分母是党员所在的党支部在平台开展主题党日的次数');
INSERT INTO `t_pbm_work_item` VALUES (22, 0, 19, '党员主动讲党课', 1, 1, 4, '讲课次数', 'MEETING_PARTY_CLASS_COUNT', 1, '本年度党员主动讲党课次数：%s次', 1, '超过了系统%s的党员；', '计算党员本年度作为党课讲课人的次数');
INSERT INTO `t_pbm_work_item` VALUES (23, 0, 19, '党员每月主动、按期、足额缴纳党费', 1, 1, 2, '上月党费交纳时间', 'PPMD_PAY_DATE', 3, '党费交纳时间：%s', 1, '超过了系统%s的党员；', '根据党费交纳时间先后排序');
INSERT INTO `t_pbm_work_item` VALUES (24, 0, 19, '每日一练', 1, 1, 2, '参与答题天数', NULL, 1, '参与答题天数：%s', 1, '超过了系统%s的党员；', '计算一个月在“每日一课”首次答题正确率');
INSERT INTO `t_pbm_work_item` VALUES (25, 0, 19, '每月一测', 1, 1, 2, '答题正确率', NULL, 2, '测试正确率：%s', 1, '超过了系统%s的党员；', '计算在“每月一测”首次答题正确率');
INSERT INTO `t_pbm_work_item` VALUES (26, 0, 19, '每季一考', 1, 1, 2, '答题正确率', NULL, 2, '本季度测试正确率：%s', 1, '超过了系统%s的党员；', '计算上一个季度“每季一考”首次答题正确率');
INSERT INTO `t_pbm_work_item` VALUES (27, 0, 19, '成立党的建设工作领导小组', 1, 2, 4, '成立党的建设工作领导小组', 'PARTY_LEADER_GROUP', 4, '成立党的建设工作领导小组：%s', 0, NULL, '本单位是否设置了党建工作领导小组');
INSERT INTO `t_pbm_work_item` VALUES (28, 0, 19, '建立党支部工作联系点制度', 1, 2, 4, '建立党支部工作联系点制度', 'PARTY_GROUP_CONTACT', 4, '建立党支部工作联系点制度：%s', 0, NULL, '本单位的党组成员是否均设置了联系支部');
INSERT INTO `t_pbm_work_item` VALUES (29, 0, 19, '建立干部基层联系点制度', 1, 2, 4, '建立干部基层联系点制度', 'PARTY_LEADER_GROUP_CONTACT', 4, '建立干部基层联系点制度：%s', 0, NULL, '本单位的党组成员是否均设置了基层联系点');
INSERT INTO `t_pbm_work_item` VALUES (30, 0, 19, '建设本单位特色品牌', 1, 2, 4, '建设本单位特色品牌', 'USER_BRAND', 4, '建设本单位特色品牌：%s', 0, NULL, '本单位是否维护了党建品牌');
INSERT INTO `t_pbm_work_item` VALUES (31, 0, 19, '因地制宜建设党员活动室、支部园地和党建廊', 1, 2, 4, '因地制宜建设党员活动室、支部园地和党建廊', 'USER_POSITION', 4, '因地制宜建设党员活动室、支部园地和党建廊：%s', 0, NULL, '本单位是否维护了党建阵地');
INSERT INTO `t_pbm_work_item` VALUES (32, 0, 19, '健全组织机构，配强组织力量', 1, 2, 4, '健全组织机构，配强组织力量', 'ORG_PERIOD_DETAIL', 4, '健全组织机构，配强组织力量：%s', 0, NULL, '本单位的所有党组织是否设置了组织书记');
INSERT INTO `t_pbm_work_item` VALUES (33, 0, 19, '党员按时足额交纳党费', 1, 2, 2, '党员按时交纳比例', 'PPMD_PAY_RATE', 2, '党员按时交纳党费比例：%s', 2, '超过了%s家单位', '党员按时交纳党费的比例在46个区县公司中的排名');
INSERT INTO `t_pbm_work_item` VALUES (34, 0, 19, '党支部党员大会', 1, 2, 3, '按时组织开展支部党员大会', 'MEETING_PARTY_MEMBER_ORG', 4, '本季度党支部党员大会：%s', 0, NULL, '本单位下的所有党支部是否组织开展了该季度的党员大会');
INSERT INTO `t_pbm_work_item` VALUES (35, 0, 19, '党支部支委会', 1, 2, 2, '按时组织开展支部支委会', 'MEETING_PARTY_COMMITTEE_ORG', 1, '党支部支委会：%s个支部未开展', 0, NULL, '设置有支委的党支部，是否按时组织开展支委会会议');
INSERT INTO `t_pbm_work_item` VALUES (36, 0, 19, '党小组会', 1, 2, 2, '党小组按时组织开展党小组会', 'MEETING_PARTY_GROUP_ORG', 1, '党小组会：%s个党小组未开展', 0, NULL, '各党小组是否按时组织开展党小组会');
INSERT INTO `t_pbm_work_item` VALUES (37, 0, 19, '党课', 1, 2, 3, '按时组织开展党课', 'MEETING_PARTY_CLASS_ORG', 4, '本季度党课：%s', 0, NULL, '各党支部是否按时组织开展党课学习');
INSERT INTO `t_pbm_work_item` VALUES (38, 0, 19, '主题党日', 1, 2, 2, '按时组织主题党日活动', 'MEETING_PARTY_DAY_ORG', 4, '主题党日：%s', 0, NULL, '各党支部是否按时组织开展主题党日活动');

SET FOREIGN_KEY_CHECKS = 1;

-- mongo
db.createCollection("PBM-USER-WORK-DETAIL")
db.getCollection("PBM-USER-WORK-DETAIL").createIndex({"userId":1})
db.getCollection("PBM-USER-WORK-DETAIL").createIndex({"workItemId":1})
db.getCollection("PBM-USER-WORK-DETAIL").createIndex({"year":1})
db.getCollection("PBM-USER-WORK-DETAIL").createIndex({"month":1})
db.getCollection("PBM-USER-WORK-DETAIL").createIndex({"type":1})

db.createCollection("PBM-ORG-WORK-DETAIL")
db.getCollection("PBM-ORG-WORK-DETAIL").createIndex({"unitId":1})
db.getCollection("PBM-ORG-WORK-DETAIL").createIndex({"workItemId":1})
db.getCollection("PBM-ORG-WORK-DETAIL").createIndex({"year":1})
db.getCollection("PBM-ORG-WORK-DETAIL").createIndex({"month":1})
db.getCollection("PBM-ORG-WORK-DETAIL").createIndex({"type":1})

db.createCollection("PBM-USER-KIT-INFO")
db.getCollection("PBM-USER-KIT-INFO").createIndex({"userId":1})
db.getCollection("PBM-USER-KIT-INFO").createIndex({"date":1})

db.createCollection("PBM-UNIT-KIT-INFO")
db.getCollection("PBM-UNIT-KIT-INFO").createIndex({"unitId":1})
db.getCollection("PBM-UNIT-KIT-INFO").createIndex({"date":1})

db.createCollection("PBM-ORG-USER-KIT-INFO")
db.getCollection("PBM-ORG-USER-KIT-INFO").createIndex({"unitId":1})
db.getCollection("PBM-ORG-USER-KIT-INFO").createIndex({"date":1})
db.getCollection("PBM-ORG-USER-KIT-INFO").createIndex({"type":1})