-- 组织表新增 单位列表 字段
ALTER TABLE `t_organization`
    ADD COLUMN `unit_category` int(10) NULL COMMENT '单位分类code, 数据字典1051' AFTER `party_leader`,
ADD INDEX `unit_cate_idx`(`unit_category`) USING BTREE;

-- 新增字典表 1051-单位类别
INSERT INTO `t_option` (`code`, `op_key`, `op_value`, `seq`, `has_child`)
VALUES ('1051', 105101, '机关', 1, 1),
       ('1051', 105102, '专业分公司', 2, 1),
       ('1051', 105103, '两烟产区', 3, 1),
       ('1051', 105104, '纯销区', 4, 1);
-- 更新数据
UPDATE t_organization SET unit_category = 105101,update_time = NOW() WHERE organization_id = 217;
UPDATE t_organization SET unit_category = 105102,update_time = NOW() WHERE organization_id IN (243,244,255,272,346);
UPDATE t_organization SET unit_category = 105103,update_time = NOW() WHERE organization_id IN (388,445,1196,920,869,999,
                                                                                               1026,1068,1090,1142,1168);
UPDATE t_organization SET unit_category = 105104,update_time = NOW() WHERE organization_id IN (474,490,502,516,534,552,566,
                                                                                               582,592,613,630,641,665,685,705,726,746,769,787,801,814,830,851,911,946,962,980,1119);
-- 新增云区标签关联表
CREATE TABLE `t_ecp_org_tag` (
    `ecp_org_tag_id` bigint(20) NOT NULL AUTO_INCREMENT,
    `ecp_org_id` bigint(20) NOT NULL COMMENT '云区组织ID',
    `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`ecp_org_tag_id`) USING BTREE,
    KEY `org_id_idx` (`ecp_org_id`) USING BTREE,
    KEY `tag_id_idx` (`tag_id`) USING BTREE
    ) COMMENT='云区组织标签关联表';

CREATE TABLE `t_pbm_fusion_item`  (
    `fusion_item_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id\n',
    `region_id` bigint(20) NOT NULL COMMENT 'region_id\n',
    `category` tinyint(1) NOT NULL COMMENT '类型 1-目标融合，2-组织融合，3-工作融合，4-数据融合\n',
    `name` varchar(100) NOT NULL COMMENT '指标名称\n',
    `description` varchar(255) NULL COMMENT '说明\n',
    `base_score` double NOT NULL COMMENT '基础值\n',
    `cycle` tinyint(1) NOT NULL COMMENT '考核周期 1-周，2-月度，3-季度，4-年度，5-累计, 6-半年\n',
    `strategy_name` varchar(255) NULL COMMENT '工厂类-策略名\n',
    `rule` text NULL COMMENT '指标计算规则\n',
    `remark` text NULL COMMENT '备注\n',
    PRIMARY KEY (`fusion_item_id`)
);

CREATE TABLE `t_pbm_target` (
`pbm_target_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
`unit_id` varchar(100) DEFAULT NULL,
`org_id` bigint(20) NOT NULL COMMENT '组织id',
`user_name` varchar(100) NOT NULL COMMENT '人员姓名',
`phone` varchar(100) NOT NULL COMMENT '手机号',
`unit` text COMMENT '所在单位',
`department_id` varchar(100) DEFAULT NULL,
`department` varchar(225) DEFAULT NULL COMMENT '所在部门',
`branch_id` bigint(20) DEFAULT NULL,
`is_party_member` tinyint(2) DEFAULT NULL COMMENT '是否党员 1-是 2-否',
`branch` varchar(225) DEFAULT NULL COMMENT '所在支部',
`score` double NOT NULL COMMENT '月度绩效得分',
`remark` varchar(225) DEFAULT NULL COMMENT '备注',
`time` varchar(20) NOT NULL COMMENT '时间',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
`update_time` datetime DEFAULT NULL COMMENT '修改时间',
`last_change_user` bigint(20) DEFAULT NULL COMMENT '最后修改人',
`user_id` bigint(20) NOT NULL,
PRIMARY KEY (`pbm_target_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='员工指标';


CREATE TABLE `t_pbm_org_target` (
`pbm_target_org_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
`org_id` bigint(20) NOT NULL COMMENT '单位id',
`org_name` varchar(20) NOT NULL COMMENT '单位名称',
`target_score` double DEFAULT NULL COMMENT '业务绩效得分',
`target_remark` varchar(225) DEFAULT NULL COMMENT '业务绩效备注',
`satisfied_score` double DEFAULT NULL COMMENT '干群满意度得分',
`satisfied_remark` varchar(225) DEFAULT NULL COMMENT '干群满意度备注',
`examine_score` double DEFAULT NULL COMMENT '党建年度考核得分',
`examine_remark` varchar(225) DEFAULT NULL COMMENT '党建年度考核备注',
`time` varchar(20) DEFAULT NULL COMMENT '时间',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
`update_time` datetime DEFAULT NULL COMMENT '修改时间',
`last_change_user` bigint(20) DEFAULT NULL COMMENT '最后修改人',
PRIMARY KEY (`pbm_target_org_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='单位指标';











